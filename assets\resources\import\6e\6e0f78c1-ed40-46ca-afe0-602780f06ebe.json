[1, ["7aWQAGmEZNeIXElrV0mlyn", "a5aMXyXdZBeqXlN1ExgZ3Z", "2a6rg1ecpIA5X7Djqyxtgy"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "animation_olympus", "\nanimation_olympus.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nComp 1/1 (10)\n  rotate: false\n  xy: 2, 27\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (11)\n  rotate: false\n  xy: 804, 722\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (12)\n  rotate: false\n  xy: 307, 433\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (13)\n  rotate: false\n  xy: 509, 437\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (14)\n  rotate: false\n  xy: 204, 27\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (15)\n  rotate: true\n  xy: 711, 520\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (16)\n  rotate: true\n  xy: 711, 318\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (17)\n  rotate: false\n  xy: 509, 135\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (18)\n  rotate: true\n  xy: 711, 116\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nEF/elip\n  rotate: false\n  xy: 406, 282\n  size: 50, 50\n  orig: 50, 50\n  offset: 0, 0\n  index: -1\nKhieng tay\n  rotate: true\n  xy: 406, 2\n  size: 112, 122\n  orig: 112, 122\n  offset: 0, 0\n  index: -1\nbody\n  rotate: true\n  xy: 486, 739\n  size: 283, 316\n  orig: 283, 316\n  offset: 0, 0\n  index: -1\nmay3\n  rotate: false\n  xy: 2, 735\n  size: 482, 287\n  orig: 482, 287\n  offset: 0, 0\n  index: -1\nmui\n  rotate: false\n  xy: 2, 4\n  size: 18, 21\n  orig: 18, 21\n  offset: 0, 0\n  index: -1\nrau\n  rotate: false\n  xy: 307, 330\n  size: 81, 101\n  orig: 81, 101\n  offset: 0, 0\n  index: -1\nria\n  rotate: true\n  xy: 406, 221\n  size: 59, 27\n  orig: 59, 27\n  offset: 0, 0\n  index: -1\ntoc1\n  rotate: false\n  xy: 390, 334\n  size: 56, 42\n  orig: 56, 42\n  offset: 0, 0\n  index: -1\ntoc2\n  rotate: false\n  xy: 390, 378\n  size: 75, 53\n  orig: 75, 53\n  offset: 0, 0\n  index: -1\nzeus\n  rotate: false\n  xy: 2, 329\n  size: 303, 404\n  orig: 303, 404\n  offset: 0, 0\n  index: -1\n\nanimation_olympus2.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nComp 1/1 (19)\n  rotate: false\n  xy: 2, 708\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (2)\n  rotate: false\n  xy: 2, 406\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (20)\n  rotate: false\n  xy: 2, 104\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (21)\n  rotate: true\n  xy: 204, 808\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (22)\n  rotate: true\n  xy: 506, 808\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (23)\n  rotate: false\n  xy: 808, 708\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (24)\n  rotate: true\n  xy: 204, 606\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (25)\n  rotate: true\n  xy: 506, 606\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (26)\n  rotate: false\n  xy: 808, 406\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (27)\n  rotate: false\n  xy: 204, 104\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (28)\n  rotate: true\n  xy: 406, 253\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (29)\n  rotate: true\n  xy: 708, 204\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (3)\n  rotate: true\n  xy: 406, 51\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (30)\n  rotate: true\n  xy: 708, 2\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nmay2\n  rotate: false\n  xy: 481, 460\n  size: 196, 144\n  orig: 196, 144\n  offset: 0, 0\n  index: -1\nmay4\n  rotate: false\n  xy: 204, 455\n  size: 275, 149\n  orig: 275, 149\n  offset: 0, 0\n  index: -1\n\nanimation_olympus3.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nComp 1/1 (31)\n  rotate: false\n  xy: 2, 606\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (32)\n  rotate: false\n  xy: 2, 304\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (4)\n  rotate: false\n  xy: 204, 606\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (5)\n  rotate: false\n  xy: 2, 2\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (6)\n  rotate: false\n  xy: 204, 304\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (7)\n  rotate: false\n  xy: 406, 606\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (8)\n  rotate: false\n  xy: 204, 2\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nComp 1/1 (9)\n  rotate: false\n  xy: 406, 304\n  size: 200, 300\n  orig: 200, 300\n  offset: 0, 0\n  index: -1\nmay1\n  rotate: false\n  xy: 608, 756\n  size: 250, 150\n  orig: 250, 150\n  offset: 0, 0\n  index: -1\n", ["animation_olympus.png", "animation_olympus2.png", "animation_olympus3.png"], {"skins": {"default": {"may8": {"may2": {"x": 16.23, "width": 196, "y": 4.97, "height": 144}}, "mui": {"mui": {"width": 18, "type": "mesh", "hull": 9, "height": 21, "triangles": [7, 11, 6, 6, 10, 5, 10, 11, 9, 10, 6, 11, 11, 8, 0, 11, 7, 8, 5, 10, 4, 10, 3, 4, 10, 9, 3, 11, 0, 9, 9, 12, 3, 9, 0, 12, 12, 2, 3, 0, 1, 12, 12, 1, 2], "uvs": [0.19794, 0.34802, 0.22584, 0.10512, 0.77059, 0.10673, 0.7743, 0.38513, 0.96938, 0.5087, 0.87148, 0.75901, 0.48799, 1, 0.14557, 0.79304, 0, 0.4089, 0.50153, 0.51089, 0.63722, 0.55883, 0.38449, 0.54838, 0.5062, 0.20599], "vertices": [2, 37, 2.91, -5.45, 0.74769, 38, -3.57, -5.53, 0.25231, 2, 37, -2.18, -4.89, 0.999, 38, -8.67, -5.09, 0.001, 1, 37, -2.04, 4.91, 1, 2, 37, 3.81, 4.91, 0.8078, 38, -2.9, 4.85, 0.1922, 2, 37, 6.44, 8.39, 0.39703, 38, -0.34, 8.39, 0.60297, 2, 37, 11.68, 6.57, 0.09127, 38, 4.93, 6.68, 0.90873, 1, 38, 10.07, -0.17, 1, 2, 37, 12.25, -6.5, 0.0278, 38, 5.79, -6.38, 0.9722, 2, 37, 4.15, -9.03, 0.48126, 38, -2.25, -9.08, 0.51874, 2, 37, 6.39, -0.03, 0.92107, 38, -0.21, -0.03, 0.07893, 2, 37, 7.43, 2.4, 0.29352, 38, 0.78, 2.42, 0.70648, 2, 37, 7.16, -2.14, 0.24805, 38, 0.6, -2.13, 0.75195, 1, 37, -0.01, 0.13, 1], "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 0, 0, 2]}}, "may7": {"may1": {"x": -0.07, "width": 250, "y": -0.06, "height": 150}}, "Comp 1/1 (1)2": {"Comp 1/1 (16)": {"width": 200, "height": 300}, "Comp 1/1 (26)": {"width": 200, "height": 300}, "Comp 1/1 (3)": {"width": 200, "height": 300}, "Comp 1/1 (18)": {"width": 200, "height": 300}, "Comp 1/1 (28)": {"width": 200, "height": 300}, "Comp 1/1 (12)": {"width": 200, "height": 300}, "Comp 1/1 (22)": {"width": 200, "height": 300}, "Comp 1/1 (14)": {"width": 200, "height": 300}, "Comp 1/1 (24)": {"width": 200, "height": 300}, "Comp 1/1 (21)": {"width": 200, "height": 300}, "Comp 1/1 (8)": {"width": 200, "height": 300}, "Comp 1/1 (31)": {"width": 200, "height": 300}, "Comp 1/1 (6)": {"width": 200, "height": 300}, "Comp 1/1 (4)": {"width": 200, "height": 300}, "Comp 1/1 (19)": {"width": 200, "height": 300}, "Comp 1/1 (2)": {"width": 200, "height": 300}, "Comp 1/1 (27)": {"width": 200, "height": 300}, "Comp 1/1 (15)": {"width": 200, "height": 300}, "Comp 1/1 (29)": {"width": 200, "height": 300}, "Comp 1/1 (17)": {"width": 200, "height": 300}, "Comp 1/1 (23)": {"width": 200, "height": 300}, "Comp 1/1 (11)": {"width": 200, "height": 300}, "Comp 1/1 (25)": {"width": 200, "height": 300}, "Comp 1/1 (13)": {"width": 200, "height": 300}, "Comp 1/1 (30)": {"width": 200, "height": 300}, "Comp 1/1 (10)": {"width": 200, "height": 300}, "Comp 1/1 (32)": {"width": 200, "height": 300}, "Comp 1/1 (20)": {"width": 200, "height": 300}, "Comp 1/1 (9)": {"width": 200, "height": 300}, "Comp 1/1 (7)": {"width": 200, "height": 300}, "Comp 1/1 (5)": {"width": 200, "height": 300}}, "cliping": {"cliping": {"color": "ce3a3aff", "end": "cliping", "type": "clipping", "vertexCount": 4, "vertices": [148.72, 441.35, 146.89, -273.8, -522.25, -278.74, -601.05, 476.77]}}, "may6": {"may3": {"x": 96.4, "width": 482, "y": 30.29, "height": 287}}, "may5": {"may1": {"x": -0.07, "width": 250, "y": -0.06, "height": 150}}, "rau": {"rau": {"width": 81, "type": "mesh", "hull": 21, "height": 101, "triangles": [15, 16, 25, 14, 15, 25, 12, 14, 25, 14, 12, 13, 9, 24, 8, 10, 24, 9, 11, 25, 24, 16, 17, 24, 10, 11, 24, 25, 16, 24, 12, 25, 11, 4, 22, 21, 4, 21, 3, 7, 23, 6, 4, 5, 6, 23, 7, 8, 1, 22, 0, 21, 1, 2, 22, 1, 21, 4, 6, 22, 23, 8, 24, 18, 24, 17, 24, 18, 23, 22, 19, 20, 6, 23, 22, 19, 23, 18, 0, 22, 20, 22, 23, 19, 21, 2, 3], "uvs": [0.09821, 0.18401, 0.25217, 0.09482, 0.45285, 0.10025, 0.52211, 0.10513, 0.61001, 0.11353, 0.76827, 0.05608, 0.91577, 0.1203, 1, 0.25811, 0.97092, 0.40177, 0.96952, 0.56888, 0.88832, 0.66907, 0.77947, 0.80165, 0.73496, 0.92242, 0.78606, 1, 0.53742, 1, 0.36197, 0.95147, 0.22441, 0.84396, 0.12925, 0.74049, 0.03942, 0.59953, 0, 0.45427, 0, 0.32411, 0.47864, 0.15666, 0.48575, 0.2999, 0.50349, 0.45849, 0.51353, 0.62139, 0.52421, 0.76925], "vertices": [4, 40, 1.88, -30.8, 0.02916, 41, -13.84, -30.43, 0.35554, 42, -30.12, -30.66, 0.08061, 39, -3.02, 30.67, 0.53468, 4, 40, -6.51, -17.91, 0.0067, 41, -21.86, -17.3, 0.16009, 42, -38.24, -17.6, 0.0142, 39, 5.99, 18.2, 0.81901, 3, 40, -5.17, -1.7, 0.59288, 41, -20.07, -1.14, 0.0062, 39, 5.44, 1.95, 0.40092, 2, 40, -4.4, 3.88, 0.90695, 39, 4.95, -3.66, 0.09305, 4, 40, -3.21, 10.95, 0.06138, 42, -34.36, 11.18, 0.00188, 39, 4.1, -10.78, 0.93656, 43, -50.19, 10.99, 0.00017, 4, 41, -22.56, 24.68, 0.00492, 42, -39.27, 24.37, 0.01562, 39, 9.9, -23.6, 0.97586, 43, -55.15, 24.16, 0.0036, 4, 41, -15.17, 36.1, 0.0425, 42, -31.98, 35.84, 0.04744, 39, 3.42, -35.55, 0.8915, 43, -47.9, 35.66, 0.01856, 5, 40, 12.91, 41.79, 0.00585, 41, -0.77, 41.83, 0.10884, 42, -17.62, 41.69, 0.11884, 39, -10.5, -42.37, 0.70532, 43, -33.56, 41.57, 0.06115, 5, 40, 27.29, 38.73, 0.01018, 41, 13.51, 38.37, 0.15411, 42, -3.31, 38.34, 0.23656, 39, -25.01, -40.02, 0.43379, 43, -19.24, 38.27, 0.16536, 6, 40, 44.14, 37.8, 0.00065, 41, 30.33, 36.96, 0.10039, 42, 13.52, 37.07, 0.32063, 39, -41.89, -39.9, 0.19089, 43, -2.4, 37.06, 0.38405, 44, -8.1, 40.89, 0.00338, 5, 41, 39.92, 29.63, 0.04589, 42, 23.16, 29.81, 0.26024, 39, -52.01, -33.33, 0.09501, 43, 7.27, 29.84, 0.54715, 44, -0.81, 31.28, 0.0517, 5, 41, 52.59, 19.81, 0.00167, 42, 35.91, 20.09, 0.04965, 39, -65.4, -24.51, 0.01462, 43, 20.06, 20.17, 0.47258, 44, 8.81, 18.45, 0.46148, 4, 42, 47.84, 15.66, 0.00037, 39, -77.6, -20.9, 0.00043, 43, 31.99, 15.78, 0.05197, 44, 19.07, 10.93, 0.94723, 1, 44, 27.84, 12.18, 1, 2, 42, 54.55, -0.84, 0.0002, 44, 21.03, -6.77, 0.9998, 4, 41, 65.09, -15.07, 0.00123, 42, 48.68, -14.68, 0.03743, 43, 32.95, -14.56, 0.05819, 44, 11.61, -18.49, 0.90315, 5, 41, 53.4, -25.35, 0.03584, 42, 37.08, -25.05, 0.2169, 39, -69.67, 20.45, 0.00275, 43, 21.39, -24.97, 0.29731, 44, -2.38, -25.3, 0.4472, 5, 41, 42.39, -32.23, 0.12866, 42, 26.13, -32.02, 0.42043, 39, -59.22, 28.16, 0.01876, 43, 10.46, -31.99, 0.27814, 44, -14.82, -29.02, 0.15402, 5, 41, 27.64, -38.39, 0.30904, 42, 11.42, -38.3, 0.48072, 39, -44.98, 35.44, 0.07308, 43, -4.22, -38.32, 0.11377, 44, -30.68, -31.05, 0.02339, 6, 40, 28.76, -40.08, 0.00071, 41, 12.77, -40.45, 0.44861, 42, -3.43, -40.48, 0.34782, 39, -30.31, 38.63, 0.17601, 43, -19.06, -40.55, 0.02618, 44, -45.57, -29.09, 0.00067, 5, 40, 15.63, -39.44, 0.00998, 41, -0.34, -39.44, 0.47012, 42, -16.55, -39.57, 0.21162, 39, -17.17, 38.63, 0.3056, 43, -32.18, -39.7, 0.00268, 1, 39, -0.25, -0.14, 1, 4, 40, 15.1, -0.02, 0.40485, 41, 0.24, -0.02, 0.59499, 42, -16.28, -0.15, 1e-05, 39, -14.72, -0.72, 0.00015, 5, 40, 31.17, 0.64, 3e-05, 41, 16.32, 0.18, 0.59389, 42, -0.2, 0.18, 0.40576, 39, -30.74, -2.15, 0.0002, 43, -15.98, 0.12, 0.00012, 3, 41, 32.79, -0.27, 0.00012, 42, 16.27, -0.14, 0.29232, 43, 0.49, -0.14, 0.70756, 4, 41, 47.74, -0.55, 5e-05, 42, 31.23, -0.31, 0.00082, 43, 15.44, -0.25, 0.95431, 44, -1.26, 0.1, 0.04483], "edges": [4, 2, 2, 0, 0, 40, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 38, 40, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14]}}, "body2": {"body": {"width": 283, "type": "mesh", "hull": 9, "height": 316, "triangles": [1, 3, 4, 3, 1, 2, 1, 4, 9, 6, 9, 5, 9, 4, 5, 1, 9, 0, 6, 7, 9, 0, 9, 8, 9, 7, 8], "uvs": [0.21293, 0.57018, 0.13201, 0.69731, 0.13676, 0.79331, 0.06213, 0.82308, 0, 0.77933, 0, 0.70703, 0.01329, 0.67908, 0, 0.64826, 0.06761, 0.48988, 0.06905, 0.66129], "vertices": [5, 4, 27.64, 88.03, 0.00775, 5, -24.2, 87.42, 0.00172, 3, 77.12, 88.94, 0.00076, 9, 88.22, 17.39, 0.75947, 14, 0.39, 18.92, 0.2303, 3, 9, 134.42, 15.37, 0.00078, 14, 46.56, 21.5, 0.26774, 15, 12.47, 17.51, 0.73148, 1, 15, 42.84, 17.15, 1, 1, 15, 51.04, -4.47, 1, 1, 15, 36.26, -21.25, 1, 2, 14, 69.43, -8.21, 0.01693, 15, 13.44, -19.96, 0.98307, 2, 14, 59.97, -9.85, 0.1654, 15, 4.84, -15.71, 0.8346, 2, 14, 53.83, -18.29, 0.4619, 15, -5.1, -18.92, 0.5381, 2, 9, 84.43, -30.79, 0.04391, 14, 1.41, -29.4, 0.95609, 3, 9, 132.43, -5.68, 1e-05, 14, 46.68, 0.35, 0.50146, 15, 0.11, 0.36, 0.49853], "edges": [12, 10, 8, 10, 8, 6, 6, 4, 4, 2, 2, 0, 12, 14, 14, 16, 16, 0]}}, "Comp 1/1 (1)": {"Comp 1/1 (16)": {"width": 200, "height": 300}, "Comp 1/1 (26)": {"width": 200, "height": 300}, "Comp 1/1 (3)": {"width": 200, "height": 300}, "Comp 1/1 (18)": {"width": 200, "height": 300}, "Comp 1/1 (28)": {"width": 200, "height": 300}, "Comp 1/1 (12)": {"width": 200, "height": 300}, "Comp 1/1 (22)": {"width": 200, "height": 300}, "Comp 1/1 (14)": {"width": 200, "height": 300}, "Comp 1/1 (24)": {"width": 200, "height": 300}, "Comp 1/1 (21)": {"width": 200, "height": 300}, "Comp 1/1 (8)": {"width": 200, "height": 300}, "Comp 1/1 (31)": {"width": 200, "height": 300}, "Comp 1/1 (6)": {"width": 200, "height": 300}, "Comp 1/1 (4)": {"width": 200, "height": 300}, "Comp 1/1 (19)": {"width": 200, "height": 300}, "Comp 1/1 (2)": {"width": 200, "height": 300}, "Comp 1/1 (27)": {"width": 200, "height": 300}, "Comp 1/1 (15)": {"width": 200, "height": 300}, "Comp 1/1 (29)": {"width": 200, "height": 300}, "Comp 1/1 (17)": {"width": 200, "height": 300}, "Comp 1/1 (23)": {"width": 200, "height": 300}, "Comp 1/1 (11)": {"width": 200, "height": 300}, "Comp 1/1 (25)": {"width": 200, "height": 300}, "Comp 1/1 (13)": {"width": 200, "height": 300}, "Comp 1/1 (30)": {"width": 200, "height": 300}, "Comp 1/1 (10)": {"width": 200, "height": 300}, "Comp 1/1 (32)": {"width": 200, "height": 300}, "Comp 1/1 (20)": {"width": 200, "height": 300}, "Comp 1/1 (9)": {"width": 200, "height": 300}, "Comp 1/1 (7)": {"width": 200, "height": 300}, "Comp 1/1 (5)": {"width": 200, "height": 300}}, "ria": {"ria": {"width": 59, "type": "mesh", "hull": 28, "height": 27, "triangles": [29, 10, 11, 20, 21, 29, 19, 29, 28, 19, 28, 18, 20, 29, 19, 15, 13, 14, 16, 13, 15, 28, 11, 12, 29, 11, 28, 12, 13, 16, 16, 28, 12, 17, 28, 16, 18, 28, 17, 8, 30, 7, 30, 8, 9, 30, 21, 32, 29, 9, 10, 30, 9, 29, 21, 30, 29, 32, 6, 7, 30, 32, 7, 5, 6, 32, 31, 4, 5, 32, 31, 5, 21, 22, 31, 32, 21, 31, 31, 33, 3, 31, 3, 4, 33, 31, 22, 23, 33, 22, 2, 3, 33, 33, 34, 2, 24, 33, 23, 24, 34, 33, 25, 34, 24, 35, 0, 1, 27, 0, 35, 26, 27, 35, 34, 1, 2, 35, 1, 34, 26, 35, 34, 25, 26, 34], "uvs": [0.07334, 0.11615, 0.17359, 0.35623, 0.21331, 0.41087, 0.29924, 0.31933, 0.3635, 0.20772, 0.37204, 0.14741, 0.47826, 0.05551, 0.51201, 0.06058, 0.6308, 0.13951, 0.65057, 0.22777, 0.71991, 0.33446, 0.77815, 0.39415, 0.79777, 0.38267, 0.87422, 0.24796, 0.9105, 0.03271, 1, 0.08093, 0.97217, 0.4177, 0.94801, 0.77788, 0.82369, 0.94785, 0.71541, 0.95465, 0.58799, 0.72105, 0.50167, 0.50969, 0.47631, 0.50968, 0.41074, 0.70614, 0.27954, 0.9384, 0.17444, 0.90295, 0.02613, 0.54666, 0, 0.24171, 0.79408, 0.67604, 0.67662, 0.56581, 0.57058, 0.3179, 0.4373, 0.35609, 0.49143, 0.24374, 0.34773, 0.51939, 0.19378, 0.63732, 0.07625, 0.4175], "vertices": [1, 48, 14.42, -6.55, 1, 3, 48, 5.69, -5.41, 0.83446, 47, 8.41, -7.64, 0.16517, 46, 11.78, -10.3, 0.00037, 3, 48, 2.94, -5.8, 0.41837, 47, 6.65, -5.49, 0.55587, 46, 10.99, -7.64, 0.02576, 4, 48, 0.65, -10.97, 0.01254, 47, 1.04, -6.23, 0.42738, 46, 5.53, -6.14, 0.55394, 45, -0.59, 10.99, 0.00614, 3, 47, -3.53, -7.88, 0.02479, 46, 0.68, -5.9, 0.74545, 45, 2.43, 7.18, 0.22976, 3, 47, -4.53, -9.27, 0.00451, 46, -0.78, -6.8, 0.63979, 45, 4.07, 6.68, 0.3557, 3, 46, -7.15, -4.52, 0.08618, 45, 6.55, 0.39, 0.7807, 49, -8.51, 1.5, 0.13312, 3, 46, -8.56, -3.09, 0.02463, 45, 6.42, -1.61, 0.72981, 49, -7.01, 2.82, 0.24556, 3, 45, 4.28, -8.64, 0.13031, 49, -0.54, 6.31, 0.86967, 51, 1.42, 17.34, 2e-05, 4, 45, 1.89, -9.81, 0.03184, 49, 1.98, 5.46, 0.9535, 51, 0.86, 14.74, 0.00541, 50, -4.97, 7.26, 0.00926, 3, 49, 6.92, 6.34, 0.61127, 51, 2.31, 9.93, 0.13945, 50, 0.01, 6.63, 0.24928, 3, 49, 10.5, 7.65, 0.13379, 51, 4.03, 6.53, 0.58445, 50, 3.82, 6.82, 0.28176, 3, 49, 11.1, 8.69, 0.05649, 51, 5.13, 6.06, 0.77971, 50, 4.7, 7.64, 0.16379, 2, 51, 10.95, 6.14, 0.99825, 50, 6.98, 12.99, 0.00175, 1, 51, 16.23, 9.41, 1, 1, 51, 19.6, 5.11, 1, 1, 51, 12.68, -1.05, 1, 2, 51, 5.53, -7.85, 0.93582, 50, 17.57, 2.35, 0.06418, 2, 51, -3.1, -6.93, 0.22532, 50, 13.23, -5.16, 0.77468, 2, 49, 18.67, -5.68, 0.00105, 50, 7.66, -8.33, 0.99895, 3, 46, -0.12, 13.31, 0.00465, 49, 8.86, -6.58, 0.61641, 50, -1.97, -6.28, 0.37894, 3, 46, -0.07, 5.64, 0.35561, 45, -5.75, -1, 0.12277, 49, 1.2, -6.18, 0.52161, 4, 47, -7.26, 1.99, 0.00118, 46, 1.06, 4.65, 0.7045, 45, -5.75, 0.51, 0.08556, 49, 0.14, -7.24, 0.20876, 3, 47, -1.89, 5.81, 0.49098, 46, 7.49, 6.08, 0.509, 49, 1.19, -13.75, 3e-05, 2, 48, -9.27, 2.6, 0.08141, 47, 7.47, 9.3, 0.91859, 2, 48, -3.9, 5.87, 0.4633, 47, 13.07, 6.42, 0.5367, 1, 48, 9.04, 4.16, 1, 1, 48, 15.55, -1.15, 1, 1, 51, 0.06, -0.06, 1, 2, 49, 9.57, 0.11, 0.02535, 50, 0.69, -0.1, 0.97465, 1, 49, 0.38, 0.38, 1, 1, 46, 0.05, 0, 1, 3, 46, -4.37, -0.17, 0.01366, 45, 1.46, -0.39, 0.87309, 49, -4.34, -1.53, 0.11325, 3, 48, -5.03, -8.68, 1e-05, 47, 0.04, -0.18, 0.50708, 46, 6.95, -0.18, 0.49291, 2, 48, -0.13, -0.36, 0.25341, 47, 9.69, -0.04, 0.74659, 2, 48, 9.03, -0.43, 0.99945, 47, 14.4, -7.9, 0.00055], "edges": [12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32]}}, "Khieng tay": {"Khieng tay": {"width": 112, "type": "mesh", "hull": 19, "height": 122, "triangles": [4, 1, 3, 1, 4, 23, 23, 4, 22, 1, 2, 3, 1, 23, 27, 7, 22, 4, 4, 5, 6, 7, 4, 6, 9, 19, 8, 19, 20, 8, 8, 20, 7, 9, 21, 19, 9, 10, 21, 19, 21, 20, 25, 1, 27, 20, 22, 7, 20, 21, 22, 25, 27, 26, 1, 25, 17, 21, 11, 22, 21, 10, 11, 0, 1, 17, 23, 22, 12, 22, 11, 12, 12, 13, 23, 0, 17, 18, 23, 24, 27, 23, 13, 24, 24, 13, 14, 24, 15, 27, 27, 15, 26, 24, 14, 15, 25, 26, 16, 26, 15, 16, 25, 16, 17], "uvs": [0.85256, 0.26109, 0.8444, 0.43108, 1, 0.37773, 1, 0.4548, 0.80423, 0.744, 1, 0.82682, 1, 0.92623, 0.41712, 0.92507, 0.106, 1, 0, 1, 0, 0.8258, 0.09044, 0.65871, 0.07079, 0.46504, 0.15798, 0.22553, 0.40044, 0.0786, 0.44867, 0.1187, 0.47155, 0.06851, 0.60819, 0, 0.72328, 0, 0.06751, 0.9704, 0.09294, 0.90274, 0.06024, 0.85609, 0.1677, 0.68918, 0.31184, 0.35667, 0.38857, 0.14141, 0.56286, 0.13277, 0.50907, 0.13988, 0.48008, 0.20041], "vertices": [2, 10, -32.78, 43.4, 0.32, 11, 40.75, 66.88, 0.68, 2, 10, -13.95, 52.14, 0.464, 11, 50.25, 48.42, 0.536, 2, 10, -27.75, 64.61, 0.592, 11, 62.15, 62.72, 0.408, 2, 10, -19.4, 68.94, 0.424, 11, 66.81, 54.55, 0.576, 2, 10, 22.01, 65.73, 0.344, 11, 65.28, 13.04, 0.656, 2, 10, 20.88, 89.84, 0.232, 11, 89.33, 15.15, 0.768, 2, 10, 31.65, 95.43, 0.08, 11, 95.35, 4.62, 0.92, 2, 10, 61.59, 37.42, 0.448, 11, 38.6, -27.65, 0.552, 2, 10, 85.75, 10.7, 0.736, 11, 12.88, -52.87, 0.264, 1, 10, 91.22, 0.16, 1, 1, 10, 72.36, -9.62, 1, 1, 10, 49.6, -10.02, 1, 1, 10, 29.64, -22.86, 1, 1, 10, -0.79, -27.65, 1, 1, 10, -29.21, -11.8, 1, 2, 10, -27.36, -4.75, 0.6, 11, -7.14, 59.52, 0.4, 1, 10, -33.97, -5.3, 1, 1, 10, -48.44, 4.43, 1, 2, 10, -54.38, 15.87, 0.528, 11, 12.37, 87.35, 0.472, 2, 10, 84.53, 5.21, 0.656, 11, 7.34, -51.88, 0.344, 2, 10, 75.89, 3.94, 0.64, 11, 5.72, -43.3, 0.36, 2, 10, 72.53, -1.93, 0.704, 11, -0.28, -40.17, 0.296, 2, 10, 48.91, -0.63, 0.48, 11, 0.06, -16.52, 0.52, 2, 10, 5.47, -4.99, 0.44, 11, -6.05, 26.71, 0.56, 2, 10, -21.8, -9.45, 0.624, 11, -11.61, 53.77, 0.376, 2, 10, -31.73, 7.39, 0.624, 11, 4.81, 64.37, 0.376, 2, 10, -28.18, 2.44, 0.76, 11, 0.01, 60.63, 0.24, 2, 10, -20.13, 2.96, 0.584, 11, 0.85, 52.61, 0.416], "edges": [38, 40, 42, 44, 44, 46, 46, 48, 50, 34, 52, 54, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 18, 20, 22, 20, 16, 18, 16, 14, 14, 12, 6, 8, 10, 12, 8, 10, 6, 4, 4, 2, 2, 0, 34, 36, 0, 36]}}, "body": {"body": {"width": 283, "type": "mesh", "hull": 82, "height": 316, "triangles": [116, 85, 90, 91, 85, 116, 145, 144, 80, 146, 77, 78, 147, 77, 146, 83, 68, 82, 63, 82, 62, 67, 68, 83, 63, 66, 67, 63, 67, 82, 82, 67, 83, 64, 66, 63, 64, 65, 66, 70, 71, 84, 69, 70, 84, 82, 68, 69, 84, 82, 69, 82, 84, 62, 85, 91, 92, 86, 74, 75, 98, 86, 75, 76, 150, 91, 91, 75, 76, 91, 98, 75, 104, 98, 91, 104, 91, 116, 98, 72, 86, 99, 98, 97, 73, 74, 86, 72, 73, 86, 99, 72, 98, 100, 99, 97, 100, 97, 103, 99, 71, 72, 57, 99, 100, 99, 58, 71, 57, 58, 99, 59, 84, 71, 58, 59, 71, 101, 57, 100, 56, 57, 101, 55, 56, 101, 60, 84, 59, 61, 84, 60, 62, 84, 61, 102, 55, 101, 54, 55, 102, 124, 52, 125, 50, 51, 52, 50, 52, 124, 121, 125, 117, 124, 125, 121, 129, 127, 109, 122, 129, 109, 37, 122, 109, 39, 122, 37, 39, 37, 38, 49, 50, 124, 128, 121, 117, 123, 129, 122, 123, 122, 39, 123, 39, 40, 120, 121, 128, 117, 129, 128, 129, 117, 127, 118, 129, 123, 118, 128, 129, 120, 128, 118, 47, 48, 49, 42, 123, 40, 42, 40, 41, 119, 120, 118, 120, 119, 47, 49, 124, 121, 120, 49, 121, 47, 49, 120, 119, 46, 47, 45, 46, 119, 119, 123, 42, 123, 119, 118, 119, 42, 43, 43, 45, 119, 44, 45, 43, 116, 114, 115, 104, 116, 115, 97, 98, 104, 105, 104, 115, 103, 97, 104, 103, 104, 105, 110, 115, 114, 105, 115, 110, 96, 103, 105, 106, 105, 110, 96, 105, 106, 95, 103, 96, 100, 103, 95, 94, 101, 100, 95, 94, 100, 35, 112, 34, 126, 96, 106, 95, 96, 126, 108, 110, 111, 106, 110, 108, 107, 126, 106, 107, 106, 108, 93, 101, 94, 102, 101, 93, 36, 111, 112, 36, 112, 35, 108, 111, 36, 53, 54, 102, 52, 53, 102, 93, 52, 102, 109, 107, 108, 126, 125, 94, 126, 94, 95, 93, 94, 125, 127, 107, 109, 117, 126, 107, 117, 107, 127, 125, 126, 117, 37, 108, 36, 109, 108, 37, 125, 52, 93, 15, 88, 14, 16, 88, 15, 17, 88, 16, 17, 19, 88, 88, 20, 21, 88, 19, 20, 18, 19, 17, 26, 27, 87, 25, 26, 87, 11, 12, 13, 87, 11, 13, 87, 24, 25, 13, 24, 87, 13, 23, 24, 88, 22, 23, 13, 88, 23, 21, 22, 88, 14, 88, 13, 113, 89, 8, 6, 89, 5, 7, 89, 6, 8, 89, 7, 114, 89, 113, 110, 114, 113, 113, 9, 31, 32, 110, 113, 31, 32, 113, 31, 9, 30, 32, 111, 110, 33, 111, 32, 112, 111, 33, 34, 112, 33, 113, 8, 9, 29, 30, 28, 30, 9, 28, 28, 87, 27, 28, 9, 87, 10, 87, 9, 87, 10, 11, 143, 80, 81, 142, 143, 81, 142, 81, 0, 144, 79, 80, 143, 145, 80, 142, 145, 143, 141, 142, 0, 140, 145, 142, 78, 79, 144, 141, 140, 142, 139, 140, 141, 130, 145, 140, 151, 140, 139, 152, 151, 139, 131, 144, 145, 131, 145, 130, 146, 78, 144, 146, 144, 131, 167, 130, 140, 167, 140, 151, 160, 167, 151, 131, 130, 167, 152, 160, 151, 153, 167, 160, 159, 141, 0, 139, 141, 159, 152, 139, 159, 160, 152, 159, 161, 167, 153, 161, 153, 160, 158, 146, 131, 158, 131, 167, 146, 158, 135, 163, 132, 158, 132, 135, 158, 161, 166, 167, 166, 163, 158, 158, 167, 166, 161, 154, 166, 159, 0, 1, 157, 163, 166, 132, 163, 157, 147, 146, 135, 134, 135, 132, 134, 132, 157, 136, 147, 135, 136, 135, 134, 133, 136, 134, 156, 157, 166, 159, 161, 160, 159, 154, 161, 162, 154, 159, 154, 156, 166, 154, 162, 156, 155, 156, 162, 156, 137, 133, 164, 156, 155, 2, 159, 1, 165, 162, 159, 2, 165, 159, 155, 162, 165, 164, 155, 165, 148, 77, 147, 147, 137, 149, 133, 147, 136, 137, 147, 133, 148, 147, 149, 133, 134, 156, 156, 134, 157, 164, 137, 156, 164, 138, 137, 149, 137, 138, 150, 138, 164, 76, 77, 148, 165, 150, 164, 92, 150, 165, 92, 91, 150, 76, 148, 149, 76, 149, 138, 76, 138, 150, 92, 165, 2, 92, 2, 3, 3, 90, 92, 85, 92, 90, 89, 4, 5, 114, 4, 89, 116, 90, 4, 3, 4, 90, 114, 116, 4], "uvs": [0.63523, 0.0367, 0.65007, 0.1183, 0.63455, 0.15577, 0.67125, 0.20786, 0.67714, 0.21846, 0.80566, 0.24911, 0.8301, 0.26216, 0.84373, 0.27478, 0.88959, 0.32663, 0.9278, 0.55181, 0.92644, 0.56093, 0.92949, 0.56792, 1, 0.5424, 1, 0.76446, 0.9443, 0.85893, 0.93921, 0.86745, 0.93322, 0.87419, 0.89184, 0.9705, 0.78159, 0.97926, 0.76222, 0.91492, 0.77486, 0.85523, 0.82399, 0.82489, 0.82829, 0.81592, 0.82686, 0.80695, 0.79293, 0.68456, 0.81212, 0.60461, 0.81212, 0.59427, 0.80997, 0.58609, 0.78472, 0.55002, 0.76754, 0.51659, 0.7678, 0.50288, 0.76673, 0.49687, 0.76297, 0.50023, 0.75572, 0.51298, 0.77774, 0.54401, 0.76963, 0.57512, 0.72731, 0.60531, 0.7065, 0.65671, 0.723, 0.65863, 0.69861, 0.71003, 0.76389, 0.77876, 0.78469, 0.81602, 0.7438, 0.85328, 0.75743, 0.91624, 0.76102, 1, 0.50921, 1, 0.21946, 1, 0.28151, 0.83275, 0.25527, 0.81776, 0.31985, 0.71624, 0.30221, 0.67816, 0.29588, 0.63441, 0.31394, 0.62007, 0.29362, 0.61827, 0.26126, 0.58322, 0.26226, 0.5475, 0.27882, 0.51964, 0.28108, 0.51447, 0.2758, 0.5162, 0.24331, 0.54259, 0.22512, 0.54962, 0.21809, 0.56147, 0.21293, 0.57018, 0.13201, 0.69731, 0.13676, 0.79331, 0.06213, 0.82308, 0, 0.77933, 0, 0.70703, 0, 0.64826, 0.06761, 0.48988, 0.12635, 0.45942, 0.14406, 0.44893, 0.21888, 0.30661, 0.23125, 0.26341, 0.27082, 0.24957, 0.39883, 0.2324, 0.40479, 0.22194, 0.40711, 0.1322, 0.41926, 0.06027, 0.41318, 0, 0.49311, 0, 0.53467, 0, 0.06905, 0.66129, 0.01329, 0.67908, 0.15851, 0.53529, 0.52065, 0.26507, 0.30174, 0.29609, 0.87415, 0.58994, 0.8655, 0.84284, 0.74269, 0.31645, 0.54175, 0.26517, 0.50698, 0.26517, 0.53575, 0.23181, 0.35541, 0.59393, 0.4052, 0.55738, 0.42887, 0.53618, 0.44683, 0.5084, 0.38153, 0.41264, 0.33827, 0.36366, 0.33663, 0.41483, 0.34725, 0.49378, 0.3301, 0.54422, 0.31296, 0.57346, 0.41744, 0.46527, 0.47132, 0.40825, 0.51131, 0.46454, 0.55865, 0.52083, 0.55294, 0.58808, 0.6615, 0.58296, 0.62232, 0.63706, 0.64844, 0.47843, 0.7064, 0.52156, 0.75211, 0.54056, 0.76353, 0.42214, 0.71538, 0.36731, 0.54233, 0.39217, 0.53417, 0.30445, 0.52899, 0.65051, 0.52903, 0.81512, 0.51737, 0.88717, 0.41709, 0.79423, 0.41359, 0.69398, 0.6503, 0.70233, 0.64797, 0.79318, 0.38316, 0.67612, 0.40733, 0.6395, 0.50119, 0.58041, 0.58669, 0.64699, 0.47145, 0.72273, 0.61736, 0.69526, 0.50148, 0.07909, 0.48502, 0.09126, 0.47786, 0.11005, 0.46352, 0.13138, 0.47279, 0.12202, 0.46059, 0.11118, 0.45825, 0.12434, 0.4656, 0.14963, 0.47435, 0.17854, 0.53502, 0.07872, 0.51921, 0.07384, 0.53702, 0.05812, 0.53027, 0.04139, 0.51537, 0.01229, 0.46636, 0.04772, 0.49098, 0.05563, 0.45024, 0.09186, 0.43896, 0.12182, 0.42004, 0.15872, 0.44973, 0.16383, 0.48494, 0.20224, 0.526, 0.08031, 0.53323, 0.08799, 0.51918, 0.09719, 0.51588, 0.11346, 0.52127, 0.14153, 0.50217, 0.13494, 0.48694, 0.1186, 0.48463, 0.10144, 0.54521, 0.09822, 0.52253, 0.09392, 0.51823, 0.10101, 0.5274, 0.1357, 0.48419, 0.10576, 0.51524, 0.15194, 0.54462, 0.15644, 0.50313, 0.11668, 0.50964, 0.09378], "vertices": [2, 13, 6.66, 76.58, 0.00511, 12, 61.89, -28.66, 0.99489, 2, 13, 17.71, 52.91, 0.09009, 12, 36.11, -32.86, 0.90991, 2, 13, 16.71, 40.32, 0.24164, 12, 24.26, -28.47, 0.75836, 2, 13, 31.18, 27.31, 0.7112, 12, 7.8, -38.85, 0.2888, 3, 13, 33.7, 24.54, 0.79856, 12, 4.46, -40.52, 0.19941, 16, -40.2, -14.53, 0.00203, 3, 13, 71.33, 25.12, 0.26492, 12, -5.23, -76.89, 0.00039, 16, -19.06, 16.61, 0.73469, 2, 13, 79.11, 23.03, 0.11417, 16, -12.88, 21.78, 0.88583, 2, 13, 83.91, 20.24, 0.04833, 16, -7.85, 24.1, 0.95167, 1, 16, 11.9, 30.95, 1, 2, 16, 82.64, 17.68, 0.96794, 17, -13.85, 14.32, 0.03206, 2, 16, 85.24, 16.37, 0.91051, 17, -10.95, 14.08, 0.08949, 2, 16, 87.61, 16.45, 0.79487, 17, -8.79, 15.06, 0.20513, 2, 16, 86.58, 37.95, 0.60191, 17, -17.84, 34.58, 0.39809, 1, 17, 52.24, 38.1, 1, 2, 17, 82.85, 23.85, 0.5585, 18, -3.87, 22.95, 0.4415, 2, 17, 85.61, 22.55, 0.4502, 18, -0.83, 22.68, 0.5498, 2, 17, 87.82, 20.96, 0.34827, 18, 1.8, 21.96, 0.65173, 1, 18, 34.39, 23.15, 1, 1, 18, 49.2, -4.45, 1, 1, 18, 32.66, -17.48, 1, 2, 17, 84.08, -24.1, 0.06952, 18, 13.9, -21.61, 0.93048, 2, 17, 73.81, -10.69, 0.63527, 18, -0.37, -12.59, 0.36473, 2, 17, 70.92, -9.62, 0.8682, 18, -3.46, -12.58, 0.1318, 2, 17, 68.11, -10.17, 0.95732, 18, -5.91, -14.07, 0.04268, 1, 17, 29.96, -21.7, 1, 3, 16, 87.59, -18.73, 0.22257, 17, 4.46, -17.54, 0.777, 4, 12.26, -81.19, 0.00044, 4, 16, 84.51, -17.65, 0.35209, 17, 1.19, -17.7, 0.64651, 4, 15.53, -81.28, 0.00125, 5, -31.81, -82.15, 0.00015, 4, 16, 81.87, -17.37, 0.47235, 17, -1.36, -18.44, 0.52488, 4, 18.13, -80.74, 0.00237, 5, -29.23, -81.54, 0.0004, 6, 13, 91.5, -68, 0.00168, 16, 68.75, -20.36, 0.81437, 17, -12.38, -26.15, 0.15602, 4, 29.71, -73.9, 0.02103, 5, -17.83, -74.4, 0.00639, 3, 84.83, -72.82, 0.0005, 6, 13, 83.94, -59.16, 0.01255, 16, 57.17, -21.46, 0.83027, 17, -22.69, -31.53, 0.04843, 4, 40.4, -69.32, 0.0771, 5, -7.27, -69.53, 0.02783, 3, 95.36, -67.87, 0.00383, 6, 13, 82.84, -54.97, 0.0212, 16, 53.11, -19.96, 0.78863, 17, -27.02, -31.67, 0.02523, 4, 44.73, -69.51, 0.11524, 5, -2.93, -69.61, 0.04312, 3, 99.69, -67.91, 0.00658, 6, 13, 82.03, -53.22, 0.02754, 16, 51.21, -19.62, 0.75304, 17, -28.9, -32.07, 0.01534, 4, 46.64, -69.25, 0.14154, 5, -1.03, -69.31, 0.05385, 3, 101.59, -67.59, 0.00869, 6, 13, 81.29, -54.53, 0.03181, 16, 51.87, -20.98, 0.68585, 17, -27.79, -33.08, 0.01028, 4, 45.6, -68.16, 0.1915, 5, -2.1, -68.24, 0.06685, 3, 100.51, -66.53, 0.0137, 6, 13, 80.42, -58.97, 0.03277, 16, 54.99, -24.24, 0.54435, 17, -23.66, -34.93, 0.00399, 4, 41.63, -66, 0.30608, 5, -6.13, -66.19, 0.08433, 3, 96.47, -64.51, 0.02849, 6, 13, 89.08, -66.71, 0.02084, 16, 66.3, -21.6, 0.40708, 17, -14.18, -28.21, 0.00021, 4, 31.66, -71.97, 0.43556, 5, -15.93, -72.42, 0.07729, 3, 86.72, -70.83, 0.05901, 5, 13, 89.55, -76.79, 0.0159, 16, 74.83, -27.01, 0.35446, 4, 21.9, -69.42, 0.47702, 5, -25.76, -70.13, 0.06909, 3, 76.87, -68.61, 0.08353, 5, 13, 80.62, -89.23, 0.0082, 16, 79.88, -41.46, 0.2324, 4, 12.68, -57.19, 0.53059, 5, -35.3, -58.15, 0.04607, 3, 67.23, -56.71, 0.18274, 5, 13, 79.37, -106.46, 0.00054, 16, 93.27, -52.38, 0.09464, 4, -3.4, -50.88, 0.40859, 5, -51.54, -52.26, 0.0093, 3, 50.94, -50.96, 0.48694, 5, 13, 84.03, -105.78, 0.00034, 16, 95.39, -48.17, 0.0904, 4, -4.13, -55.53, 0.39581, 5, -52.15, -56.93, 0.00805, 3, 50.37, -55.64, 0.50541, 4, 16, 108.44, -60.04, 0.03561, 4, -20.18, -48.2, 0.1854, 5, -68.39, -50.03, 0.00058, 3, 34.07, -48.87, 0.77842, 3, 16, 135.04, -49.77, 0.00344, 4, -42.39, -66.09, 0.01528, 3, 12.5, -67.52, 0.98128, 3, 16, 148.1, -48.1, 0.00141, 4, -54.31, -71.66, 0.00475, 3, 0.78, -73.51, 0.99383, 3, 16, 155.39, -62.91, 0.00039, 4, -65.78, -59.78, 0.00075, 3, -11.09, -62.04, 0.99886, 1, 3, -30.95, -66.06, 1, 1, 3, -57.41, -67.29, 1, 1, 3, -58.01, 3.97, 1, 1, 3, -58.69, 85.96, 1, 3, 4, -55.82, 70.83, 0.02078, 3, -5.69, 68.84, 0.96746, 9, 153.18, 72.55, 0.01176, 3, 4, -50.89, 78.13, 0.02868, 3, -1.02, 76.31, 0.95617, 9, 152.35, 63.78, 0.01515, 4, 4, -19.3, 59.01, 0.19706, 5, -70.35, 57.16, 0.0002, 3, 31.21, 58.3, 0.72459, 9, 115.46, 65.39, 0.07815, 4, 4, -7.14, 63.68, 0.32642, 5, -58.32, 62.15, 0.00401, 3, 43.2, 63.39, 0.52261, 9, 107.04, 55.45, 0.14696, 4, 4, 6.73, 65.1, 0.40012, 5, -44.5, 63.95, 0.01446, 3, 57.01, 65.3, 0.36522, 9, 95.56, 47.54, 0.22021, 4, 4, 11.12, 59.87, 0.4262, 5, -39.97, 58.83, 0.02405, 3, 61.59, 60.22, 0.2823, 9, 89.19, 50.02, 0.26746, 4, 4, 11.84, 65.61, 0.42505, 5, -39.4, 64.58, 0.03309, 3, 62.11, 65.98, 0.21344, 9, 91.32, 44.65, 0.32842, 4, 4, 23.16, 74.47, 0.3954, 5, -28.32, 73.74, 0.04693, 3, 73.11, 75.23, 0.13391, 9, 85.64, 31.44, 0.42376, 5, 4, 34.43, 73.89, 0.35188, 5, -17.03, 73.46, 0.05717, 3, 84.4, 75.04, 0.09624, 9, 75.47, 26.54, 0.49439, 8, 83.93, 75.97, 0.00032, 5, 4, 43.11, 68.97, 0.24164, 5, -8.23, 68.77, 0.06561, 3, 93.24, 70.43, 0.0487, 9, 65.5, 26.68, 0.64151, 8, 77.8, 68.11, 0.00254, 5, 4, 44.73, 68.28, 0.2032, 5, -6.6, 68.13, 0.06496, 3, 94.88, 69.8, 0.03592, 9, 63.76, 26.51, 0.6927, 8, 76.89, 66.61, 0.00322, 5, 4, 44.22, 69.79, 0.17048, 5, -7.14, 69.63, 0.05549, 3, 94.32, 71.29, 0.0289, 9, 64.92, 25.43, 0.7427, 8, 78.46, 66.89, 0.00243, 5, 4, 36.13, 79.2, 0.05483, 5, -15.48, 78.82, 0.01669, 3, 85.91, 80.42, 0.00782, 9, 76.54, 21.06, 0.92057, 8, 88.95, 73.52, 8e-05, 5, 4, 34.04, 84.41, 0.02595, 5, -17.7, 83.97, 0.00739, 3, 83.64, 85.55, 0.00329, 9, 80.87, 17.49, 0.95826, 14, -6.93, 18.29, 0.00511, 5, 4, 30.35, 86.5, 0.01321, 5, -21.45, 85.96, 0.00338, 3, 79.88, 87.5, 0.00149, 9, 85.11, 17.43, 0.87441, 14, -2.71, 18.65, 0.1075, 5, 4, 27.64, 88.03, 0.00775, 5, -24.2, 87.42, 0.00172, 3, 77.12, 88.94, 0.00076, 9, 88.22, 17.39, 0.75947, 14, 0.39, 18.92, 0.2303, 3, 9, 134.42, 15.37, 0.00078, 14, 46.56, 21.5, 0.26774, 15, 12.47, 17.51, 0.73148, 1, 15, 42.84, 17.15, 1, 1, 15, 51.04, -4.47, 1, 1, 15, 36.26, -21.25, 1, 2, 14, 69.43, -8.21, 0.01693, 15, 13.44, -19.96, 0.98307, 2, 14, 53.83, -18.29, 0.4619, 15, -5.1, -18.92, 0.5381, 2, 9, 84.43, -30.79, 0.04391, 14, 1.41, -29.4, 0.95609, 2, 9, 68.28, -20.39, 0.52669, 14, -15.7, -20.66, 0.47331, 2, 9, 63.04, -17.45, 0.744, 14, -21.2, -18.25, 0.256, 1, 9, 13.36, -19.15, 1, 2, 9, -0.38, -22.27, 0.79244, 8, 77.16, -13.97, 0.20756, 2, 9, -9.39, -14.3, 0.47212, 8, 65.38, -16.36, 0.52788, 2, 12, 0.05, 38.25, 0.14297, 8, 28.76, -15.48, 0.85703, 2, 12, 3.35, 36.56, 0.25575, 8, 26.53, -18.45, 0.74425, 3, 12, 31.71, 35.9, 0.38985, 8, 21.01, -46.27, 0.14615, 20, 17.78, -9.39, 0.464, 3, 12, 54.44, 32.46, 0.61762, 8, 13.72, -68.07, 0.04638, 20, -5, -12.41, 0.336, 3, 12, 73.49, 34.18, 0.72974, 8, 12.14, -87.13, 0.01426, 19, 2.89, -28.2, 0.256, 3, 12, 73.49, 11.56, 0.43712, 8, -10.14, -83.25, 0.00289, 19, -13.46, -12.57, 0.56, 1, 12, 73.49, -0.2, 1, 3, 9, 132.43, -5.68, 1e-05, 14, 46.68, 0.35, 0.50146, 15, 0.11, 0.36, 0.49853, 2, 14, 59.97, -9.85, 0.1654, 15, 4.84, -15.71, 0.8346, 2, 9, 85.45, -1.35, 0.14183, 14, -0.5, 0, 0.85817, 4, 13, -4.91, -1.68, 0.06987, 12, -10.28, 3.77, 0.03074, 8, -3.43, 0.61, 0.12863, 6, 21.84, -0.12, 0.77076, 3, 12, -20.08, 65.72, 2e-05, 9, -0.31, 0.19, 0.44476, 8, 59.28, -0.37, 0.55523, 2, 16, 89.01, -0.63, 0.27076, 17, -1.05, -0.24, 0.72924, 1, 18, 0.22, 0.44, 1, 2, 13, 59.97, -0.21, 0.59662, 16, -4.85, -7.24, 0.40338, 1, 13, 0.84, -0.09, 1, 2, 12, -10.3, 7.64, 0.00124, 8, 0.38, -0.03, 0.99876, 2, 13, -3.66, 9.59, 0.01013, 12, 0.23, -0.5, 0.98987, 5, 4, 19.07, 47.92, 0.49334, 5, -31.71, 47.1, 0.03995, 3, 69.95, 48.56, 0.20017, 9, 76.49, 56.69, 0.26642, 8, 60.48, 94.95, 0.00012, 5, 4, 30.24, 33.53, 0.61313, 5, -20.16, 33.01, 0.09061, 3, 81.61, 34.56, 0.07958, 9, 59.78, 63.95, 0.21479, 8, 44.62, 86, 0.0019, 5, 4, 36.76, 26.66, 0.64443, 5, -13.46, 26.31, 0.14632, 3, 88.37, 27.92, 0.03109, 9, 50.76, 66.85, 0.17415, 8, 36.87, 80.55, 0.00401, 5, 4, 45.4, 21.34, 0.54896, 5, -4.68, 21.23, 0.29442, 3, 97.19, 22.91, 0.00718, 9, 40.63, 67.36, 0.14081, 8, 30.35, 72.77, 0.00862, 6, 4, 76.14, 39.01, 0.08093, 5, 25.58, 39.71, 0.37522, 3, 127.29, 41.64, 0.00031, 9, 22.15, 37.1, 0.37506, 8, 43.36, 39.79, 0.16414, 6, -23.95, 40.21, 0.00433, 5, 4, 91.94, 50.84, 0.0173, 5, 41.06, 51.95, 0.14187, 9, 13.97, 19.14, 0.54476, 8, 52.76, 22.44, 0.2922, 6, -8.23, 52.13, 0.00387, 6, 4, 75.78, 51.73, 0.07495, 5, 24.89, 52.41, 0.20628, 3, 126.5, 54.34, 0.00097, 9, 28.56, 26.11, 0.60562, 8, 56, 38.29, 0.11189, 6, -24.38, 52.93, 0.00029, 5, 4, 50.76, 49.39, 0.28753, 5, -0.06, 49.41, 0.18051, 3, 101.57, 51.13, 0.02489, 9, 49.39, 40.18, 0.48617, 8, 57.32, 63.38, 0.02089, 5, 4, 34.96, 54.67, 0.39529, 5, -16, 54.26, 0.08621, 3, 85.59, 55.85, 0.08507, 9, 65.78, 43.14, 0.43059, 8, 64.84, 78.25, 0.00284, 5, 4, 25.85, 59.76, 0.4162, 5, -25.24, 59.11, 0.05297, 3, 76.31, 60.62, 0.13986, 9, 76.22, 43.05, 0.39066, 8, 71.2, 86.52, 0.00031, 5, 4, 59.24, 29.29, 0.24754, 5, 8.95, 29.54, 0.45691, 3, 110.75, 31.34, 0.00261, 9, 32.3, 53.74, 0.24615, 8, 36.2, 57.92, 0.0468, 5, 4, 76.85, 13.58, 0.01325, 5, 26.97, 14.3, 0.84196, 9, 9.31, 59.07, 0.06746, 8, 18.09, 42.78, 0.07135, 6, -23.1, 14.78, 0.00598, 4, 4, 58.77, 2.73, 0.00589, 5, 9.18, 2.98, 0.98207, 9, 19.96, 77.27, 0.00981, 8, 9.99, 62.25, 0.00224, 4, 13, 27.42, -76.53, 0.00914, 16, 38.93, -77.71, 0.03103, 4, 40.63, -10.19, 0.81535, 5, -8.61, -10.42, 0.14449, 5, 13, 31.65, -97.42, 0.00068, 16, 58.46, -86.25, 0.01564, 4, 19.43, -8.01, 0.96761, 5, -29.86, -8.8, 0.00392, 3, 72.26, -7.33, 0.01216, 5, 13, 60.77, -87.5, 0.01075, 16, 67.07, -56.71, 0.16744, 4, 20.23, -38.76, 0.62193, 5, -28.24, -39.53, 0.05646, 3, 74.13, -38.03, 0.14342, 5, 13, 54.76, -106.97, 0.0006, 16, 79.55, -72.82, 0.06143, 4, 3.44, -27.23, 0.54373, 5, -45.33, -28.44, 0.00683, 3, 56.95, -27.09, 0.38741, 5, 13, 48.23, -56.72, 0.10511, 16, 34.67, -49.3, 0.23433, 4, 53.35, -35.94, 0.33976, 5, 4.79, -35.83, 0.31686, 3, 107.13, -34.06, 0.00394, 6, 13, 67.72, -65.37, 0.04343, 16, 52.95, -38.32, 0.3753, 17, -20.26, -48.73, 0.0001, 4, 39.29, -51.98, 0.42033, 5, -8.84, -52.23, 0.12512, 3, 93.64, -50.58, 0.03572, 6, 13, 81.8, -67.63, 0.02379, 16, 62.88, -28.09, 0.41151, 17, -14.91, -35.51, 0.00038, 4, 32.94, -64.75, 0.4267, 5, -14.84, -65.17, 0.08214, 3, 87.74, -63.56, 0.05548, 5, 13, 74.73, -30.74, 0.0825, 16, 28.62, -12.69, 0.82087, 4, 70.27, -68.98, 0.0424, 5, 22.58, -68.4, 0.05403, 3, 125.19, -66.48, 0.0002, 4, 13, 56.91, -17.78, 0.50196, 16, 7.77, -19.84, 0.38104, 4, 87.95, -55.81, 0.02185, 5, 39.9, -54.77, 0.09515, 4, 13, 11.92, -38.66, 0.04287, 16, -0.97, -68.66, 0.00584, 4, 81.4, -6.65, 0.00286, 5, 32.05, -5.8, 0.94844, 3, 13, 2.15, -12.61, 0.15343, 5, 59.77, -3.49, 0.00211, 6, 9.33, -3.69, 0.84446, 3, 16, 74.85, -99.15, 0.00044, 4, -0.11, -0.71, 0.49356, 3, 52.48, -0.71, 0.506, 2, 16, 123.96, -116.3, 1e-05, 3, 0.46, -1.16, 0.99999, 1, 3, -22.33, 1.95, 1, 3, 4, -44.67, 32.15, 0.02316, 3, 6.8, 30.58, 0.96409, 9, 124.82, 101.12, 0.01275, 3, 4, -12.97, 32.3, 0.22777, 3, 38.47, 31.83, 0.7124, 9, 97.09, 85.77, 0.05983, 4, 16, 101.64, -72.15, 0.03239, 4, -17.39, -34.59, 0.19513, 5, -65.96, -36.36, 0.00029, 3, 36.39, -35.18, 0.77219, 3, 16, 128.52, -82.24, 0.00364, 4, -46.07, -33.17, 0.0147, 3, 7.67, -34.76, 0.98166, 4, 4, -7.11, 40.76, 0.31872, 5, -57.68, 39.24, 0.00129, 3, 44.04, 40.49, 0.58024, 9, 96, 75.54, 0.09975, 4, 4, 4.28, 33.62, 0.4898, 5, -46.11, 32.41, 0.00544, 3, 55.67, 33.75, 0.39127, 9, 82.59, 76.34, 0.11349, 3, 4, 22.24, 6.57, 0.97416, 3, 74.56, 7.34, 0.00837, 9, 53.84, 91.44, 0.01746, 5, 13, 45.9, -112.73, 2e-05, 16, 79.19, -83.37, 0.02797, 4, 0.57, -17.06, 0.50907, 5, -48.47, -18.35, 0.00113, 3, 53.72, -17.03, 0.46182, 3, 4, -22.49, 16.17, 0.03394, 3, 29.52, 15.38, 0.95237, 9, 97.7, 104.49, 0.01369, 3, 16, 96.45, -80.21, 0.02356, 4, -14.91, -25.33, 0.18452, 3, 38.54, -25.84, 0.79192, 4, 13, -26.12, 53.4, 0.0643, 12, 48.5, 9.2, 0.79761, 8, -8.18, -58.22, 0.03408, 21, -2.6, -3.31, 0.104, 4, 13, -29.56, 48.43, 0.0513, 12, 44.65, 13.85, 0.76589, 8, -2.93, -55.23, 0.07082, 21, 2.19, -6.99, 0.112, 3, 13, -29.89, 42.16, 0.04479, 12, 38.71, 15.88, 0.85359, 8, 0.09, -49.73, 0.10161, 3, 13, -31.96, 34.57, 0.03599, 12, 31.97, 19.94, 0.73957, 8, 5.24, -43.79, 0.22444, 3, 13, -30.24, 38.13, 0.04019, 12, 34.93, 17.31, 0.8064, 8, 2.15, -46.25, 0.15341, 3, 13, -34.5, 40.49, 0.03338, 12, 38.35, 20.77, 0.83414, 8, 4.96, -50.22, 0.13248, 3, 13, -34.01, 36.31, 0.03237, 12, 34.2, 21.43, 0.77193, 8, 6.33, -46.23, 0.19571, 3, 13, -29.83, 29.18, 0.03016, 12, 26.21, 19.35, 0.7218, 8, 5.65, -38, 0.24804, 3, 13, -24.96, 21.07, 0.02149, 12, 17.07, 16.87, 0.72489, 8, 4.78, -28.58, 0.25363, 3, 13, -17.02, 56.09, 0.06604, 12, 48.61, -0.3, 0.90496, 8, -17.55, -56.7, 0.029, 4, 13, -21.75, 56.36, 0.04592, 12, 50.15, 4.18, 0.64373, 8, -13.41, -58.99, 0.02235, 21, -5.34, 1.21, 0.288, 3, 13, -18.24, 62.51, 0.04822, 12, 55.12, -0.86, 0.93065, 8, -19.23, -63.02, 0.02113, 3, 13, -21.52, 67.08, 0.03537, 12, 60.41, 1.05, 0.94804, 8, -18.25, -68.56, 0.0166, 4, 13, -28.08, 74.78, 0.01033, 12, 69.6, 5.26, 0.96602, 8, -15.68, -78.34, 0.00764, 19, -15.33, -5.41, 0.016, 5, 13, -38.38, 60.23, 0.00626, 12, 58.41, 19.13, 0.27107, 8, -0.09, -69.69, 0.0119, 20, -12.52, -0.71, 0.10668, 19, 2.43, -6.9, 0.60409, 5, 13, -31, 59.72, 0.01718, 12, 55.91, 12.17, 0.40202, 8, -6.53, -66.03, 0.0151, 20, -12.06, 6.68, 0.376, 19, -0.88, -0.28, 0.1897, 5, 13, -38.98, 45.57, 0.00642, 12, 44.46, 23.69, 0.21783, 8, 6.8, -56.73, 0.02631, 20, 2.15, -1.21, 0.64, 19, 15.37, 0.03, 0.10944, 4, 13, -39.47, 35.59, 0.01309, 12, 34.99, 26.89, 0.51225, 8, 11.57, -47.95, 0.13066, 20, 12.13, -1.64, 0.344, 3, 13, -41.45, 22.91, 0.00428, 12, 23.33, 32.24, 0.63594, 8, 18.85, -37.39, 0.35978, 3, 13, -32.93, 23.64, 0.01623, 12, 21.72, 23.84, 0.68707, 8, 10.84, -34.35, 0.2967, 3, 13, -20.04, 14.67, 0.01568, 12, 9.58, 13.87, 0.55546, 8, 3.11, -20.68, 0.42886, 4, 13, -19.34, 54.91, 0.05148, 12, 48.11, 2.26, 0.69259, 8, -14.95, -56.65, 0.02393, 21, -3.78, 3.54, 0.232, 3, 13, -16.71, 53.13, 0.06538, 12, 45.68, 0.21, 0.87981, 8, -16.55, -53.9, 0.05481, 5, 13, -19.75, 49.25, 0.04539, 12, 42.77, 4.19, 0.66394, 8, -12.13, -51.72, 0.02181, 21, 1.85, 2.85, 0.184, 22, -3.77, 6.55, 0.08486, 5, 13, -19.25, 44.05, 0.03105, 12, 37.64, 5.12, 0.5064, 8, -10.33, -46.82, 0.02805, 21, 7.07, 3.09, 0.248, 22, 0.48, 3.52, 0.1865, 4, 13, -15.36, 35.93, 0.01863, 12, 28.76, 3.59, 0.34272, 8, -10.31, -37.82, 0.01465, 22, 9.16, 1.12, 0.624, 4, 13, -21.13, 36.47, 0.03828, 12, 30.85, 9, 0.73671, 8, -5.34, -40.8, 0.08102, 22, 4.97, -2.88, 0.144, 4, 13, -26.69, 40.26, 0.0349, 12, 36.01, 13.31, 0.66493, 8, -1.98, -46.63, 0.07616, 22, -1.54, -4.59, 0.224, 4, 13, -28.79, 45.3, 0.03819, 12, 41.43, 13.96, 0.62589, 8, -2.27, -52.08, 0.06392, 21, 5.35, -6.38, 0.272, 3, 13, -12.57, 50.94, 0.08636, 12, 42.45, -3.18, 0.86616, 8, -19.33, -50.14, 0.04748, 4, 13, -19.11, 50.5, 0.04951, 12, 43.81, 3.24, 0.70717, 8, -13.24, -52.58, 0.02732, 21, 0.63, 3.54, 0.216, 4, 13, -19.68, 48.02, 0.042, 12, 41.57, 4.45, 0.62981, 8, -11.66, -50.58, 0.02418, 21, 3.09, 2.86, 0.304, 4, 13, -14.2, 38.18, 0.04505, 12, 30.61, 1.86, 0.69369, 8, -12.34, -39.34, 0.02925, 22, 8.23, 3.47, 0.232, 5, 13, -28.54, 43.95, 0.03941, 12, 40.07, 14.09, 0.67553, 8, -1.91, -50.76, 0.07201, 21, 6.7, -6.2, 0.14105, 22, -5.54, -3.56, 0.072, 4, 13, -16.11, 32.3, 0.03884, 12, 25.47, 5.3, 0.77792, 8, -8.06, -34.87, 0.10324, 22, 11.41, -1.83, 0.08, 4, 13, -7.72, 33.2, 0.07987, 12, 24.05, -3.02, 0.75144, 8, -16.01, -32.04, 0.07269, 22, 16.23, 5.09, 0.096, 5, 13, -22.44, 42.09, 0.01079, 12, 36.62, 8.73, 0.18839, 8, -6.6, -46.44, 0.01548, 21, 8.87, -0.2, 0.312, 22, -0.13, -0.18, 0.47334, 5, 13, -22.64, 49.56, 0.02134, 12, 43.85, 6.89, 0.30714, 8, -9.66, -53.25, 0.0143, 21, 1.4, -0.02, 0.48122, 22, -5.9, 4.57, 0.176], "edges": [166, 134, 132, 134, 132, 130, 130, 128, 128, 126, 126, 124, 134, 136, 166, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 116, 118, 118, 120, 120, 122, 122, 124, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 150, 152, 152, 154, 154, 156, 156, 158, 162, 0, 0, 2, 2, 4, 4, 6, 88, 90, 90, 92, 268, 270, 270, 272, 272, 266, 158, 160, 160, 162, 262, 316, 262, 260, 260, 280, 280, 302, 302, 304, 304, 318, 318, 320, 320, 322, 322, 308, 308, 324, 326, 314, 314, 312, 312, 328, 328, 330, 330, 324]}}, "EF/elip2": {"EF/elip": {"scaleX": 0.366, "scaleY": 0.366, "x": 1.08, "width": 50, "y": 0.22, "height": 50}}, "zeus": {"zeus": {"width": 303, "type": "mesh", "hull": 11, "height": 404, "triangles": [1, 11, 0, 9, 10, 0, 6, 7, 8, 5, 6, 8, 4, 5, 8, 2, 3, 4, 8, 2, 4, 9, 1, 2, 9, 11, 1, 9, 0, 11, 9, 2, 8], "uvs": [0.13746, 0.73033, 0.44422, 0.72489, 0.6094, 0.73305, 0.71105, 0.74939, 0.76006, 0.77526, 0.80363, 0.79432, 0.87623, 0.7807, 1, 0.80793, 1, 1, 0, 1, 0, 0.75756, 0.29175, 0.72761], "vertices": [-105.75, 26.35, -12.8, 28.55, 37.25, 25.25, 68.05, 18.65, 82.9, 8.2, 96.1, 0.5, 118.1, 6, 155.6, -5, 155.6, -82.59, -147.4, -82.59, -147.4, 15.35, -59, 27.45], "edges": [16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 22, 22, 0, 18, 20, 0, 20, 0, 2]}}, "EF/elip": {"EF/elip": {"scaleX": 0.366, "scaleY": 0.366, "x": 1.08, "width": 50, "y": 0.22, "height": 50}}, "may4": {"may4": {"x": -14.95, "width": 275, "y": -0.92, "height": 149}}, "toc2": {"toc2": {"width": 75, "type": "mesh", "hull": 24, "height": 53, "triangles": [7, 9, 10, 8, 9, 7, 34, 7, 10, 34, 10, 11, 6, 7, 34, 32, 12, 13, 31, 4, 5, 31, 5, 32, 14, 32, 13, 31, 32, 14, 15, 31, 14, 30, 3, 4, 30, 4, 31, 16, 29, 30, 30, 31, 15, 16, 30, 15, 33, 34, 11, 6, 34, 33, 5, 6, 33, 33, 11, 12, 32, 5, 33, 32, 33, 12, 26, 0, 1, 27, 26, 1, 27, 1, 2, 19, 20, 26, 19, 26, 27, 18, 19, 27, 18, 27, 28, 24, 22, 23, 25, 24, 23, 25, 23, 0, 21, 22, 24, 21, 24, 25, 26, 25, 0, 20, 21, 25, 20, 25, 26, 28, 27, 2, 29, 2, 3, 28, 2, 29, 29, 3, 30, 17, 18, 28, 17, 28, 29, 17, 29, 16], "uvs": [0.39103, 0.19101, 0.4873, 0.33848, 0.56693, 0.44529, 0.66229, 0.5329, 0.77035, 0.5796, 0.82871, 0.59401, 0.8333, 0.54938, 0.83454, 0.4481, 0.76999, 0.34866, 0.82067, 0.29127, 0.92238, 0.40743, 0.96453, 0.51079, 1, 0.64612, 1, 0.75193, 0.9355, 0.8915, 0.84138, 1, 0.63479, 1, 0.43708, 0.96738, 0.29671, 0.84771, 0.17725, 0.73734, 0.06581, 0.58583, 0, 0.35097, 0, 0, 0.20861, 0, 0.1, 0.0874, 0.18747, 0.25937, 0.27667, 0.40817, 0.39421, 0.55001, 0.46474, 0.64475, 0.57857, 0.74488, 0.71361, 0.79286, 0.82631, 0.79326, 0.8931, 0.70009, 0.90654, 0.57946, 0.873, 0.4599], "vertices": [3, 62, -15.29, 18.1, 0.00175, 60, 18.97, 13.93, 0.59715, 61, -1.3, 14.09, 0.4011, 5, 62, -5.11, 14.99, 0.12991, 60, 29.61, 14.15, 0.08581, 61, 9.32, 13.41, 0.7841, 65, 17.75, 30.52, 1e-05, 63, -12.67, 22.37, 0.00016, 6, 62, 2.9, 13.12, 0.52919, 60, 37.81, 14.87, 0.00132, 61, 17.55, 13.43, 0.43264, 65, 12.19, 24.45, 0.00372, 63, -6.19, 17.31, 0.03055, 64, 0.14, 25.66, 0.00258, 5, 62, 11.42, 12.71, 0.58354, 61, 25.93, 15, 0.05818, 65, 7.67, 17.22, 0.0414, 63, 1.38, 13.37, 0.26286, 64, 2.29, 17.41, 0.05402, 6, 62, 19.66, 14.67, 0.13721, 61, 33.51, 18.8, 0.00037, 65, 5.34, 9.07, 0.24102, 63, 9.68, 11.68, 0.34866, 64, 6.61, 10.11, 0.27265, 66, 1.94, 9.49, 9e-05, 5, 62, 23.83, 16.22, 0.02155, 65, 4.65, 4.68, 0.62037, 63, 14.11, 11.34, 0.092, 64, 9.32, 6.6, 0.21459, 66, -0.97, 6.13, 0.05149, 5, 62, 22.93, 18.43, 0.00373, 65, 7.02, 4.38, 0.57579, 63, 14.23, 13.73, 0.01785, 64, 11.17, 8.11, 0.04552, 66, 0.88, 4.62, 0.35711, 2, 65, 12.39, 4.38, 0.00488, 66, 5.43, 1.77, 0.99512, 1, 66, 12.44, 3.21, 1, 1, 66, 13.1, -1.61, 1, 2, 65, 14.66, -2.17, 0.01208, 66, 3.89, -4.98, 0.98792, 2, 65, 9.24, -5.42, 0.73405, 66, -2.43, -4.88, 0.26595, 2, 65, 2.12, -8.21, 0.8478, 64, 16.95, -4.1, 0.1522, 2, 65, -3.49, -8.31, 0.44448, 64, 13.17, -8.24, 0.55552, 3, 65, -10.97, -3.6, 0.02292, 63, 23.6, -3.58, 0.08405, 64, 4.61, -10.44, 0.89302, 2, 63, 17.12, -9.98, 0.579, 64, -4.48, -9.93, 0.421, 2, 62, 22.12, -9.7, 0.23482, 63, 1.7, -11.47, 0.76518, 3, 62, 8.45, -15.69, 0.88989, 61, 29.58, -13.32, 0.07352, 63, -13.23, -11.17, 0.03659, 3, 62, -3.84, -15.52, 0.40592, 60, 40.34, -14.44, 0.01265, 61, 17.58, -15.99, 0.58143, 3, 62, -14.53, -14.99, 0.05172, 60, 30.02, -17.27, 0.19294, 61, 7.06, -17.93, 0.75534, 2, 60, 18.46, -18.2, 0.644, 61, -4.53, -17.88, 0.356, 2, 60, 5.87, -13.64, 0.97793, 61, -16.69, -12.27, 0.02207, 1, 60, -8.05, -1.31, 1, 1, 60, 2.32, 10.4, 1, 1, 60, 0.39, 1.23, 1, 1, 60, 11.56, 0.1, 1, 2, 60, 21.9, -0.12, 0.28057, 61, 0.43, -0.16, 0.71943, 2, 62, -5.49, 1.79, 0.00315, 61, 12, 0.48, 0.99685, 1, 62, 1.61, 0.12, 1, 1, 62, 11.66, -0.15, 1, 4, 62, 21.69, 2.76, 0.00026, 65, -6.04, 13.13, 0.0002, 63, 6.53, 0.02, 0.99872, 64, -4.16, 4.64, 0.00081, 1, 64, 2.07, -1.08, 1, 2, 65, -0.88, -0.24, 0.19679, 64, 9.1, -0.81, 0.80321, 1, 65, 5.53, -1.14, 1, 1, 66, 3.41, -0.38, 1], "edges": [44, 46, 46, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 42, 44, 40, 42]}}, "may3": {"may3": {"x": 96.4, "width": 482, "y": 30.29, "height": 287}}, "may2": {"may2": {"x": 16.23, "width": 196, "y": 4.97, "height": 144}}, "may1": {"may1": {"x": -0.07, "width": 250, "y": -0.06, "height": 150}}, "toc1": {"toc1": {"width": 56, "type": "mesh", "hull": 23, "height": 42, "triangles": [16, 18, 23, 17, 18, 16, 18, 19, 23, 19, 20, 21, 12, 27, 11, 27, 28, 11, 28, 10, 11, 27, 7, 28, 9, 7, 8, 7, 9, 28, 10, 28, 9, 12, 13, 27, 13, 26, 27, 26, 7, 27, 13, 14, 26, 26, 6, 7, 26, 25, 6, 6, 4, 5, 6, 3, 4, 14, 25, 26, 14, 15, 25, 15, 24, 25, 25, 3, 6, 25, 24, 3, 0, 1, 3, 3, 1, 2, 15, 16, 24, 24, 16, 23, 24, 0, 3, 0, 24, 22, 19, 21, 23, 24, 23, 22, 23, 21, 22], "uvs": [0.49414, 0.08697, 0.57481, 0.04257, 0.70553, 0.03645, 0.60428, 0.24201, 0.80629, 0.32514, 0.86293, 0.39973, 0.75168, 0.46677, 0.8143, 0.60166, 0.82168, 0.48793, 0.92388, 0.48252, 1, 0.73608, 1, 0.90785, 0.82423, 1, 0.64298, 0.96616, 0.46882, 0.85026, 0.3755, 0.74201, 0.22052, 0.63898, 0.11459, 0.6142, 0.12818, 0.43271, 0.08864, 0.33488, 0, 0.10469, 0.10763, 0, 0.29408, 0, 0.24059, 0.37973, 0.41858, 0.40375, 0.54238, 0.49337, 0.66828, 0.6191, 0.82695, 0.75431, 0.93785, 0.70127], "vertices": [4, 53, 14.9, 20.1, 0.51353, 54, -4.39, 19.7, 0.34572, 55, -19.31, 18.95, 0.13624, 58, 36.01, 3.68, 0.0045, 4, 53, 18.99, 22.77, 0.39441, 54, -1.31, 23.49, 0.41696, 55, -16.37, 22.85, 0.18254, 58, 34.67, -1.02, 0.00609, 4, 53, 26.14, 24.4, 0.36201, 54, 5, 27.21, 0.43873, 55, -10.19, 26.79, 0.1938, 58, 30.34, -6.92, 0.00546, 4, 53, 22.18, 14.85, 0.25757, 54, 4.14, 16.92, 0.42881, 55, -10.69, 16.48, 0.28807, 58, 27.08, 2.88, 0.02555, 4, 53, 33.95, 13.54, 0.00431, 54, 15.75, 19.25, 0.20883, 55, 0.83, 19.22, 0.68308, 58, 17.33, -3.84, 0.10378, 3, 54, 20.03, 18.01, 0.19166, 55, 5.15, 18.13, 0.70059, 58, 12.9, -4.39, 0.10775, 4, 53, 32.06, 7.13, 0.00414, 54, 15.9, 12.56, 0.17197, 55, 1.22, 12.54, 0.6836, 58, 14.55, 2.25, 0.14029, 4, 54, 21.69, 9.26, 0.00381, 55, 7.12, 9.45, 0.34511, 58, 7.93, 3.01, 0.64756, 57, 8.15, 8.12, 0.00352, 1, 58, 11.43, -0.28, 1, 1, 58, 8.06, -4.91, 1, 2, 58, -2.94, -1.66, 0.09202, 57, 5.19, -3.34, 0.90798, 1, 57, -1.81, -5.09, 1, 3, 54, 30.16, -5.18, 0.00306, 55, 16.1, -4.68, 0.73164, 57, -7.95, 3.52, 0.2653, 2, 54, 20.57, -8.77, 0.33198, 55, 6.64, -8.61, 0.66802, 3, 53, 19.51, -11.66, 0.00613, 54, 9.67, -9.15, 0.99084, 55, -4.24, -9.38, 0.00303, 3, 53, 13.52, -8.17, 0.26546, 54, 2.91, -7.65, 0.73437, 52, -9.79, -13.13, 0.00017, 3, 53, 4.19, -5.54, 0.43673, 54, -6.78, -7.99, 0.00694, 52, -5.47, -4.46, 0.55633, 1, 52, -4.42, 1.48, 1, 1, 52, 3.2, 0.72, 1, 1, 52, 7.31, 2.93, 1, 1, 52, 16.97, 7.89, 1, 4, 53, -7.04, 19.64, 0.53206, 54, -25.15, 12.58, 0.00366, 55, -39.81, 11.1, 0.00028, 52, 21.37, 1.87, 0.464, 4, 53, 3.21, 21.59, 0.66064, 54, -15.97, 17.56, 0.06074, 55, -30.81, 16.4, 0.01462, 52, 21.37, -8.57, 0.264, 3, 53, 3.26, 5.36, 0.9894, 54, -10.99, 2.12, 0.00828, 55, -25.29, 1.14, 0.00232, 4, 53, 13.24, 6.24, 0.56388, 54, -1.75, 5.99, 0.38189, 55, -16.19, 5.34, 0.05172, 58, 28.19, 15.25, 0.00251, 4, 53, 20.75, 3.84, 0.04728, 54, 6.14, 5.99, 0.73967, 55, -8.31, 5.62, 0.19491, 58, 20.94, 12.14, 0.01815, 3, 54, 14.85, 4.71, 0.07068, 55, 0.45, 4.66, 0.84848, 58, 12.43, 9.88, 0.08085, 3, 55, 10.99, 4.28, 0.5602, 58, 2.46, 6.42, 0.24374, 57, 2.1, 5.87, 0.19605, 3, 55, 15.2, 9.36, 0.00082, 58, 0.36, 0.17, 0.68486, 57, 5.77, 0.39, 0.31432], "edges": [40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 44, 42, 44, 42, 40]}}}}, "skeleton": {"images": "./oplymplis_play_bg/", "width": 303, "spine": "3.6.53", "hash": "Oj4oPyIXT8odAkyesO87OfM6myk", "height": 398.49}, "slots": [{"attachment": "cliping", "name": "cliping", "bone": "root"}, {"attachment": "toc2", "name": "toc2", "bone": "toc9"}, {"attachment": "body", "name": "body", "bone": "bone"}, {"attachment": "zeus", "name": "zeus", "bone": "<PERSON>"}, {"attachment": "toc1", "name": "toc1", "bone": "toc1"}, {"attachment": "rau", "name": "rau", "bone": "rau"}, {"attachment": "ria", "name": "ria", "bone": "ria"}, {"attachment": "mui", "name": "mui", "bone": "mui"}, {"attachment": "<PERSON><PERSON><PERSON> tay", "name": "<PERSON><PERSON><PERSON> tay", "bone": "<PERSON><PERSON><PERSON> tay"}, {"color": "0fb1b8ff", "blend": "additive", "name": "EF/elip", "bone": "eye"}, {"color": "0fb1b8ff", "blend": "additive", "name": "EF/elip2", "bone": "eye2"}, {"name": "may1", "bone": "may1"}, {"name": "may7", "bone": "may7"}, {"name": "may5", "bone": "may5"}, {"name": "may2", "bone": "may2"}, {"name": "may8", "bone": "may8"}, {"name": "may3", "bone": "may3"}, {"name": "may6", "bone": "may6"}, {"name": "may4", "bone": "may4"}, {"blend": "additive", "name": "Comp 1/1 (1)", "bone": "bone20"}, {"blend": "additive", "name": "Comp 1/1 (1)2", "bone": "bone21"}, {"attachment": "body", "name": "body2", "bone": "bone"}], "bones": [{"name": "root"}, {"parent": "root", "name": "<PERSON>", "y": 1.29}, {"parent": "<PERSON>", "name": "bone", "x": 3.96, "y": 57.77}, {"parent": "bone", "rotation": 89.52, "name": "bone2", "length": 52.56}, {"parent": "bone2", "rotation": 2, "name": "bone3", "length": 49.51, "x": 52.56}, {"parent": "bone3", "rotation": -1.52, "name": "bone4", "length": 50.37, "x": 49.51}, {"parent": "bone4", "rotation": 1.19, "name": "bone5", "length": 21.03, "x": 50.37}, {"parent": "bone5", "rotation": 1.93, "name": "bone6", "x": 21.6, "y": 0.27}, {"parent": "bone6", "rotation": 98.69, "name": "bone8", "length": 59.97, "x": 0.33, "y": 3.1}, {"parent": "bone8", "rotation": 52.94, "name": "bone10", "length": 83.45, "x": 59.62, "y": -0.24}, {"parent": "bone10", "rotation": -0.25, "name": "<PERSON><PERSON><PERSON> tay", "length": 65.24, "x": -25.83, "y": -25.86}, {"parent": "<PERSON><PERSON><PERSON> tay", "rotation": 87.68, "name": "Khieng tay2", "length": 18, "x": 32.4, "y": -0.02}, {"parent": "bone6", "rotation": -1.19, "name": "bone7", "length": 45.15, "x": 10.44, "y": -4.37}, {"parent": "bone6", "rotation": -106.98, "name": "bone9", "length": 53.79, "x": 0.42, "y": -5.58}, {"parent": "bone10", "rotation": -5.71, "name": "bone11", "length": 46.8, "x": 85.95, "y": -1.39}, {"parent": "bone11", "rotation": 36.1, "name": "bone12", "length": 35.39, "x": 46.8}, {"parent": "bone9", "rotation": -54.95, "name": "bone13", "length": 86.94, "x": 68.68, "y": -0.02}, {"parent": "bone13", "rotation": -22.14, "name": "bone14", "length": 73.65, "x": 90.07, "y": -0.81}, {"parent": "bone14", "rotation": -20.27, "name": "bone15", "length": 33.17, "x": 78.52, "y": 0.98}, {"parent": "bone7", "rotation": 133.71, "name": "bone16", "length": 12.9, "x": 55.1, "y": 12.61}, {"parent": "bone16", "rotation": 30.14, "name": "bone17", "length": 15.86, "x": 12.9}, {"parent": "<PERSON>", "rotation": -100.98, "name": "bone18", "length": 8.74, "x": -1.91, "y": 287.42}, {"parent": "bone18", "rotation": 38.12, "name": "bone19", "length": 11.51, "x": 8.86, "y": 0.02}, {"parent": "root", "scaleY": 1.541, "name": "bone20", "x": -227.39, "y": 22.75}, {"parent": "root", "scaleY": 1.541, "name": "bone21", "x": -227.39, "y": 22.75}, {"parent": "bone10", "rotation": -5.71, "name": "bone22", "length": 46.8, "x": 85.95, "y": -1.39}, {"parent": "bone22", "rotation": 36.1, "name": "bone23", "length": 35.39, "x": 46.8}, {"parent": "bone7", "scaleY": 0.705, "rotation": -91.93, "name": "eye", "x": 24.8, "y": 11.38}, {"parent": "bone7", "scaleY": 0.705, "rotation": -91.93, "name": "eye2", "x": 24.11, "y": -8.98}, {"parent": "root", "name": "may1", "x": 12.27, "y": -82.73}, {"parent": "root", "name": "may2", "x": -98.09, "y": -17.37}, {"parent": "root", "name": "may3", "x": -106.51, "y": -79.53}, {"parent": "root", "name": "may4", "x": 17.13, "y": -41.91}, {"parent": "root", "name": "may5", "x": 12.27, "y": -82.73}, {"parent": "root", "rotation": -9.43, "name": "may6", "x": -118.1, "y": -28.51}, {"scaleX": 1.354, "parent": "root", "scaleY": 1.354, "name": "may7", "x": -0.42, "y": -48.89}, {"scaleX": 1.24, "parent": "root", "scaleY": 1.24, "name": "may8", "x": -98.09, "y": -17.37}, {"parent": "bone7", "rotation": -179.35, "name": "mui", "length": 6.6, "x": 23.15, "y": -0.87}, {"parent": "mui", "rotation": -1.27, "name": "mui2", "length": 6.98, "x": 6.6}, {"parent": "bone7", "name": "rau", "x": -3.08, "y": -8.52}, {"parent": "rau", "rotation": -177.21, "name": "rau2", "length": 14.86, "x": 0.36}, {"parent": "rau2", "rotation": 1.61, "name": "rau3", "length": 16.52, "x": 14.86}, {"parent": "rau3", "rotation": -0.45, "name": "rau4", "length": 15.78, "x": 16.52}, {"parent": "rau4", "rotation": -0.21, "name": "rau5", "length": 16.69, "x": 15.78}, {"parent": "rau5", "rotation": 16.04, "name": "rau6", "length": 17.12, "x": 16.69}, {"parent": "bone7", "name": "ria", "x": 12.47, "y": -0.49}, {"parent": "ria", "rotation": 131.31, "name": "ria2", "length": 6.99, "x": -1.55, "y": 2.78}, {"parent": "ria2", "rotation": -22.78, "name": "ria3", "length": 10.17, "x": 6.99}, {"parent": "ria3", "rotation": -58.69, "name": "ria4", "length": 9.78, "x": 10.07, "y": 0.03}, {"parent": "ria", "rotation": -135.24, "name": "ria5", "length": 8.88, "x": -0.55, "y": -4.54}, {"parent": "ria5", "rotation": 17.28, "name": "ria6", "length": 7.8, "x": 8.88}, {"parent": "ria6", "rotation": 66.04, "name": "ria7", "length": 9.02, "x": 8.15, "y": 0.49}, {"parent": "bone7", "name": "toc1", "x": 47.12, "y": 0.22}, {"parent": "toc1", "rotation": -100.78, "name": "toc2", "length": 13.08, "x": 0.77, "y": -1.38}, {"parent": "toc2", "rotation": -17.73, "name": "toc3", "length": 14.1, "x": 13.08}, {"parent": "toc3", "rotation": -2.04, "name": "toc4", "length": 10.84, "x": 14.24, "y": 0.07}, {"parent": "toc4", "rotation": 31.72, "name": "toc5", "length": 7.5, "x": 10.84}, {"parent": "toc5", "rotation": 74.79, "name": "toc6", "length": 5.68, "x": 7.5}, {"parent": "toc6", "rotation": 52.33, "name": "toc7", "length": 7.41, "x": 5.68}, {"parent": "toc1", "name": "toc9", "x": 2.75, "y": -7.04}, {"parent": "toc9", "rotation": -138.47, "name": "toc10", "length": 21.46, "x": 0.46, "y": 0.61}, {"parent": "toc10", "rotation": 4.87, "name": "toc11", "length": 17.75, "x": 21.46}, {"parent": "toc11", "rotation": 13.31, "name": "toc12", "length": 15.77, "x": 17.75}, {"parent": "toc12", "rotation": 24.79, "name": "toc13", "length": 12.76, "x": 15.77}, {"parent": "toc13", "rotation": 47.9, "name": "toc14", "length": 9.53, "x": 12.76}, {"parent": "toc14", "rotation": 46.6, "name": "toc15", "length": 8.72, "x": 9.53}, {"parent": "toc15", "rotation": 31.97, "name": "toc16", "length": 9.81, "x": 8.72}], "animations": {"animation": {"slots": {"may8": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"color": "a7a7a700", "time": 1.0333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "curve": "stepped", "time": 1.6667}, {"color": "ffffffff", "time": 2}], "attachment": [{"name": "may2", "time": 0}, {"name": "may2", "time": 2}]}, "may7": {"color": [{"color": "ffffff00", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "curve": "stepped", "time": 0.5}, {"color": "ffffffff", "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "may1", "time": 0}, {"name": "may1", "time": 2}]}, "Comp 1/1 (1)2": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff00", "time": 1.6667}, {"color": "ffffffff", "time": 2}], "attachment": [{"name": "Comp 1/1 (28)", "time": 0}, {"name": "Comp 1/1 (29)", "time": 0.0667}, {"name": "Comp 1/1 (30)", "time": 0.1333}, {"name": "Comp 1/1 (31)", "time": 0.2}, {"name": "Comp 1/1 (32)", "time": 0.2667}, {"name": "Comp 1/1 (2)", "time": 0.3333}, {"name": "Comp 1/1 (3)", "time": 0.4}, {"name": "Comp 1/1 (4)", "time": 0.4667}, {"name": "Comp 1/1 (5)", "time": 0.5333}, {"name": "Comp 1/1 (6)", "time": 0.6}, {"name": "Comp 1/1 (7)", "time": 0.6667}, {"name": "Comp 1/1 (8)", "time": 0.7333}, {"name": "Comp 1/1 (9)", "time": 0.8}, {"name": "Comp 1/1 (10)", "time": 0.8667}, {"name": "Comp 1/1 (11)", "time": 0.9333}, {"name": "Comp 1/1 (12)", "time": 1}, {"name": "Comp 1/1 (13)", "time": 1.0667}, {"name": "Comp 1/1 (14)", "time": 1.1333}, {"name": "Comp 1/1 (15)", "time": 1.2}, {"name": "Comp 1/1 (16)", "time": 1.2667}, {"name": "Comp 1/1 (17)", "time": 1.3333}, {"name": "Comp 1/1 (18)", "time": 1.4}, {"name": "Comp 1/1 (19)", "time": 1.4667}, {"name": "Comp 1/1 (20)", "time": 1.5333}, {"name": "Comp 1/1 (21)", "time": 1.6}, {"name": "Comp 1/1 (22)", "time": 1.6667}, {"name": "Comp 1/1 (23)", "time": 1.7333}, {"name": "Comp 1/1 (24)", "time": 1.8}, {"name": "Comp 1/1 (25)", "time": 1.8667}, {"name": "Comp 1/1 (26)", "time": 1.9333}, {"name": "Comp 1/1 (27)", "time": 2}]}, "may6": {"color": [{"color": "ffffff00", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "a7a7a700", "time": 0.0333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "curve": "stepped", "time": 0.5333}, {"color": "ffffffff", "time": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "may3", "time": 0}, {"name": "may3", "time": 2}]}, "may5": {"color": [{"color": "ffffffff", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"color": "a7a7a700", "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "time": 2}], "attachment": [{"name": "may1", "time": 0}, {"name": "may1", "time": 2}]}, "Comp 1/1 (1)": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.3333}, {"color": "ffffffff", "time": 1.6667}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "Comp 1/1 (2)", "time": 0}, {"name": "Comp 1/1 (3)", "time": 0.0667}, {"name": "Comp 1/1 (4)", "time": 0.1333}, {"name": "Comp 1/1 (5)", "time": 0.2}, {"name": "Comp 1/1 (6)", "time": 0.2667}, {"name": "Comp 1/1 (7)", "time": 0.3333}, {"name": "Comp 1/1 (8)", "time": 0.4}, {"name": "Comp 1/1 (9)", "time": 0.4667}, {"name": "Comp 1/1 (10)", "time": 0.5333}, {"name": "Comp 1/1 (11)", "time": 0.6}, {"name": "Comp 1/1 (12)", "time": 0.6667}, {"name": "Comp 1/1 (13)", "time": 0.7333}, {"name": "Comp 1/1 (14)", "time": 0.8}, {"name": "Comp 1/1 (15)", "time": 0.8667}, {"name": "Comp 1/1 (16)", "time": 0.9333}, {"name": "Comp 1/1 (17)", "time": 1}, {"name": "Comp 1/1 (18)", "time": 1.0667}, {"name": "Comp 1/1 (19)", "time": 1.1333}, {"name": "Comp 1/1 (20)", "time": 1.2}, {"name": "Comp 1/1 (21)", "time": 1.2667}, {"name": "Comp 1/1 (22)", "time": 1.3333}, {"name": "Comp 1/1 (23)", "time": 1.4}, {"name": "Comp 1/1 (24)", "time": 1.4667}, {"name": "Comp 1/1 (25)", "time": 1.5333}, {"name": "Comp 1/1 (26)", "time": 1.6}, {"name": "Comp 1/1 (27)", "time": 1.6667}, {"name": "Comp 1/1 (28)", "time": 1.7333}, {"name": "Comp 1/1 (29)", "time": 1.8}, {"name": "Comp 1/1 (30)", "time": 1.8667}, {"name": "Comp 1/1 (31)", "time": 1.9333}, {"name": "Comp 1/1 (32)", "time": 2}]}, "EF/elip": {"color": [{"color": "0fb1b800", "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"color": "0fb1b8ff", "time": 0.8333, "curve": [0.276, 0, 0.621, 0.4]}, {"color": "0fb1b82e", "time": 0.9, "curve": [0.314, 0.27, 0.657, 0.64]}, {"color": "0fb1b8f1", "time": 0.9667, "curve": [0.343, 0.36, 0.686, 0.73]}, {"color": "0fb1b82f", "time": 1.0333, "curve": [0.379, 0.6, 0.724, 1]}, {"color": "0fb1b8ff", "time": 1.1, "curve": [0.276, 0, 0.621, 0.4]}, {"color": "0fb1b82e", "time": 1.1667, "curve": [0.314, 0.27, 0.657, 0.64]}, {"color": "0fb1b8f1", "time": 1.2333, "curve": [0.343, 0.36, 0.686, 0.73]}, {"color": "0fb1b82f", "time": 1.3, "curve": [0.379, 0.6, 0.724, 1]}, {"color": "0fb1b8ff", "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "0fb1b800", "time": 2}], "attachment": [{"name": "EF/elip", "time": 0.1667}]}, "may4": {"color": [{"color": "a7a7a700", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "curve": "stepped", "time": 0.8333}, {"color": "ffffffff", "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.9667, "curve": [0.25, 0, 0.75, 1]}, {"color": "a7a7a700", "time": 2}], "attachment": [{"name": "may4", "time": 0}, {"name": "may4", "time": 2}]}, "EF/elip2": {"color": [{"color": "0fb1b800", "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"color": "0fb1b8ff", "time": 0.8333, "curve": [0.276, 0, 0.621, 0.4]}, {"color": "0fb1b82e", "time": 0.9, "curve": [0.314, 0.27, 0.657, 0.64]}, {"color": "0fb1b8f1", "time": 0.9667, "curve": [0.343, 0.36, 0.686, 0.73]}, {"color": "0fb1b82f", "time": 1.0333, "curve": [0.379, 0.6, 0.724, 1]}, {"color": "0fb1b8ff", "time": 1.1, "curve": [0.276, 0, 0.621, 0.4]}, {"color": "0fb1b82e", "time": 1.1667, "curve": [0.314, 0.27, 0.657, 0.64]}, {"color": "0fb1b8f1", "time": 1.2333, "curve": [0.343, 0.36, 0.686, 0.73]}, {"color": "0fb1b82f", "time": 1.3, "curve": [0.379, 0.6, 0.724, 1]}, {"color": "0fb1b8ff", "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "0fb1b800", "time": 2}], "attachment": [{"name": "EF/elip", "time": 0.1667}]}, "may3": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"color": "a7a7a700", "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "time": 2}], "attachment": [{"name": "may3", "time": 0}, {"name": "may3", "time": 2}]}, "may2": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"color": "a7a7a700", "time": 1.0333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "curve": "stepped", "time": 1.6667}, {"color": "ffffffff", "time": 2}], "attachment": [{"name": "may2", "time": 0}, {"name": "may2", "time": 2}]}, "may1": {"color": [{"color": "ffffffff", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"color": "a7a7a700", "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "curve": "stepped", "time": 0.8333}, {"color": "ffffffff", "time": 2}], "attachment": [{"name": "may1", "time": 0}, {"name": "may1", "time": 2}]}}, "bones": {"bone20": {"rotate": [{"angle": -41.66, "time": 0}], "scale": [{"x": 0.854, "y": 1.349, "time": 0}], "translate": [{"x": 160.09, "y": 105.12, "time": 0}]}, "bone21": {"rotate": [{"angle": -41.66, "time": 0}], "scale": [{"x": 0.854, "y": 1.349, "time": 0}], "translate": [{"x": 160.09, "y": 105.12, "time": 0}]}, "bone22": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.55, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "bone23": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.55, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "Khieng tay": {"rotate": [{"angle": -4.73, "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"angle": -5.82, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -4.61, "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"angle": -4.73, "time": 2}], "translate": [{"x": 1.21, "y": 10.42, "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"x": 1.32, "y": 8.87, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.19, "y": 10.58, "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"x": 1.21, "y": 10.42, "time": 2}]}, "may8": {"rotate": [{"angle": -11.54, "time": 0, "curve": [0.374, 0.5, 0.751, 1]}, {"angle": 6.62, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.95, "time": 1.0333, "curve": [0.251, 0, 0.624, 0.5]}, {"angle": -11.54, "time": 2}], "scale": [{"x": 1.049, "y": 1.049, "time": 0, "curve": [0.374, 0.5, 0.751, 1]}, {"x": 1.663, "y": 1.663, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.0333, "curve": [0.251, 0, 0.624, 0.5]}, {"x": 1.049, "y": 1.049, "time": 2}], "translate": [{"x": 37.41, "y": 0, "time": 0, "curve": [0.374, 0.5, 0.751, 1]}, {"x": 76.54, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.0333, "curve": [0.25, 0, 0.75, 1]}, {"x": 37.41, "y": 0, "time": 2}]}, "may7": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.148, "y": 1.148, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 10.57, "time": 0.5}]}, "may6": {"rotate": [{"angle": -15.39, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.0333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.91, "time": 0.5333, "curve": [0.334, 0.34, 0.758, 1]}, {"angle": -15.39, "time": 2}], "scale": [{"x": 1.1, "y": 1.1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.0333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.019, "y": 1.019, "time": 0.5333, "curve": [0.334, 0.34, 0.758, 1]}, {"x": 1.1, "y": 1.1, "time": 2}], "translate": [{"x": 76.54, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.0333}, {"x": 14.47, "y": 0, "time": 0.5333, "curve": [0.334, 0.34, 0.758, 1]}, {"x": 76.54, "y": 0, "time": 2}]}, "may5": {"rotate": [{"angle": 0.32, "time": 0, "curve": [0.378, 0.6, 0.722, 1]}, {"angle": 0.38, "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0.32, "time": 2}], "scale": [{"x": 2.072, "y": 2.072, "time": 0, "curve": [0.378, 0.6, 0.722, 1]}, {"x": 2.199, "y": 2.199, "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.455, "y": 1.455, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 2.072, "y": 2.072, "time": 2}], "translate": [{"x": 63.48, "y": 0, "time": 0, "curve": [0.378, 0.6, 0.722, 1]}, {"x": 76.54, "y": 0, "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 63.48, "y": 0, "time": 2}]}, "ria7": {"rotate": [{"angle": 5.51, "time": 0, "curve": [0.367, 0.46, 0.754, 1]}, {"angle": 0, "time": 0.5667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 9.36, "time": 1.5667, "curve": [0.255, 0, 0.62, 0.47]}, {"angle": 5.51, "time": 2}]}, "toc6": {"rotate": [{"angle": -23.25, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": -29.78, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -41.2, "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -16.72, "time": 1.6667, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -23.25, "time": 2}]}, "ria6": {"rotate": [{"angle": 1.56, "time": 0, "curve": [0.378, 0.61, 0.722, 1]}, {"angle": 0, "time": 0.2333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 9.36, "time": 1.2333, "curve": [0.242, 0, 0.671, 0.68]}, {"angle": 1.56, "time": 2}]}, "toc7": {"rotate": [{"angle": -23.21, "time": 0, "curve": [0.333, 0.33, 0.758, 1]}, {"angle": -11.79, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -46.79, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -25.79, "time": 1.8333, "curve": [0.276, 0, 0.621, 0.4]}, {"angle": -23.21, "time": 2}]}, "toc4": {"rotate": [{"angle": -0.32, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -13.49, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 27.59, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.32, "time": 2}]}, "toc5": {"rotate": [{"angle": -8.11, "time": 0, "curve": [0.379, 0.6, 0.724, 1]}, {"angle": -18.28, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 33.6, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 36.85, "time": 1.5, "curve": [0.242, 0, 0.667, 0.67]}, {"angle": -8.11, "time": 2}]}, "ria3": {"rotate": [{"angle": -5.18, "time": 0, "curve": [0.38, 0.53, 0.745, 1]}, {"angle": 0, "time": 0.4333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -12.57, "time": 1.4333, "curve": [0.246, 0, 0.633, 0.54]}, {"angle": -5.18, "time": 2}]}, "toc2": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 4.12, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.67, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.96, "time": 1.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "may4": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -15.39, "time": 1.9667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.1, "y": 1.1, "time": 1.9667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 76.54, "y": 0, "time": 1.9667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "ria2": {"rotate": [{"angle": -1.64, "time": 0, "curve": [0.375, 0.62, 0.716, 1]}, {"angle": 0, "time": 0.2, "curve": [0.25, 0, 0.75, 1]}, {"angle": -12.57, "time": 1.2, "curve": [0.243, 0, 0.68, 0.71]}, {"angle": -1.64, "time": 2}]}, "toc3": {"rotate": [{"angle": -3.59, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -16.83, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -4.33, "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -7.18, "time": 1.8333, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -3.59, "time": 2}]}, "may3": {"rotate": [{"angle": -2.91, "time": 0, "curve": [0.334, 0.34, 0.758, 1]}, {"angle": -15.39, "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.91, "time": 2}], "scale": [{"x": 1.019, "y": 1.019, "time": 0, "curve": [0.334, 0.34, 0.758, 1]}, {"x": 1.1, "y": 1.1, "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.019, "y": 1.019, "time": 2}], "translate": [{"x": 14.47, "y": 0, "time": 0, "curve": [0.334, 0.34, 0.758, 1]}, {"x": 76.54, "y": 0, "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.5}, {"x": 14.47, "y": 0, "time": 2}]}, "ria5": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 9.36, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "may2": {"rotate": [{"angle": -11.54, "time": 0, "curve": [0.374, 0.5, 0.751, 1]}, {"angle": -11.11, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.95, "time": 1.0333, "curve": [0.251, 0, 0.624, 0.5]}, {"angle": -11.54, "time": 2}], "scale": [{"x": 1.049, "y": 1.049, "time": 0, "curve": [0.374, 0.5, 0.751, 1]}, {"x": 1.1, "y": 1.1, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.0333, "curve": [0.251, 0, 0.624, 0.5]}, {"x": 1.049, "y": 1.049, "time": 2}], "translate": [{"x": 37.41, "y": 0, "time": 0, "curve": [0.374, 0.5, 0.751, 1]}, {"x": 76.54, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.0333, "curve": [0.25, 0, 0.75, 1]}, {"x": 37.41, "y": 0, "time": 2}]}, "ria4": {"rotate": [{"angle": -9.53, "time": 0, "curve": [0.345, 0.37, 0.757, 1]}, {"angle": 0, "time": 0.7, "curve": [0.25, 0, 0.75, 1]}, {"angle": -12.57, "time": 1.7, "curve": [0.269, 0, 0.618, 0.42]}, {"angle": -9.53, "time": 2}]}, "may1": {"rotate": [{"angle": -12.76, "time": 0, "curve": [0.378, 0.6, 0.722, 1]}, {"angle": -15.39, "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.242, 0, 0.67, 0.68]}, {"angle": -12.76, "time": 2}], "scale": [{"x": 1.083, "y": 1.083, "time": 0, "curve": [0.378, 0.6, 0.722, 1]}, {"x": 1.1, "y": 1.1, "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.5, "curve": [0.242, 0, 0.67, 0.68]}, {"x": 1.083, "y": 1.083, "time": 2}], "translate": [{"x": 63.48, "y": 0, "time": 0, "curve": [0.378, 0.6, 0.722, 1]}, {"x": 76.54, "y": 0, "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.5, "curve": [0.242, 0, 0.67, 0.68]}, {"x": 63.48, "y": 0, "time": 2}]}, "bone17": {"rotate": [{"angle": -4.99, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.93, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -13.73, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -4.99, "time": 2}]}, "bone18": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -22.96, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "bone15": {"rotate": [{"angle": -3.73, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.73, "time": 2}]}, "bone16": {"rotate": [{"angle": 5.19, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.93, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -13.73, "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 18.3, "time": 1.6667, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 5.19, "time": 2}]}, "rau6": {"rotate": [{"angle": -14.65, "time": 0, "curve": [0.347, 0.38, 0.757, 1]}, {"angle": -22.51, "time": 0.3667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.01, "time": 0.8667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -22.15, "time": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.92, "time": 1.8333, "curve": [0.267, 0, 0.618, 0.42]}, {"angle": -14.65, "time": 2}]}, "bone5": {"translate": [{"x": 0.19, "y": 0, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0.38, "y": 0.01, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"x": 0.19, "y": 0, "time": 2}]}, "bone4": {"scale": [{"x": 1.002, "y": 1.002, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"x": 1, "y": 1, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.016, "y": 1.016, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"x": 1.002, "y": 1.002, "time": 2}], "translate": [{"x": 0.21, "y": -0.01, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"x": 0, "y": 0, "time": 0.3333}, {"x": 0.73, "y": -0.02, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"x": 0.21, "y": -0.01, "time": 2}]}, "bone10": {"rotate": [{"angle": 2.4, "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"angle": 0, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.66, "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"angle": 2.4, "time": 2}]}, "bone7": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.41, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "bone9": {"rotate": [{"angle": 4.51, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.29, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": 4.51, "time": 2}]}, "bone13": {"rotate": [{"angle": -2.67, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -4.83, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -2.67, "time": 2}]}, "bone8": {"rotate": [{"angle": -2.41, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.36, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -2.41, "time": 2}]}, "bone14": {"rotate": [{"angle": -3.37, "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"angle": 0, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.73, "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"angle": -3.37, "time": 2}]}, "bone11": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.55, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "bone12": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.55, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "toc10": {"rotate": [{"angle": -6.26, "time": 0, "curve": [0.375, 0.62, 0.716, 1]}, {"angle": -6.81, "time": 0.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.2, "time": 0.6, "curve": [0.25, 0, 0.75, 1]}, {"angle": -7.46, "time": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.56, "time": 1.6, "curve": [0.243, 0, 0.68, 0.71]}, {"angle": -6.26, "time": 2}]}, "bone3": {"translate": [{"x": 0.21, "y": 0.01, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"x": 0, "y": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": 2.2, "y": 0.08, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"x": 0.21, "y": 0.01, "time": 2}]}, "bone2": {"translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0.01, "y": 1.11, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "rau3": {"rotate": [{"angle": 1, "time": 0, "curve": [0.374, 0.62, 0.714, 1]}, {"angle": -1.46, "time": 0.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.04, "time": 0.6, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.19, "time": 0.9333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 19.63, "time": 1.5667, "curve": [0.243, 0, 0.683, 0.73]}, {"angle": 1, "time": 2}]}, "rau2": {"rotate": [{"angle": 2.21, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 13.71, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 9.86, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 18.01, "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.21, "time": 2}]}, "toc15": {"rotate": [{"angle": 7.78, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -21.35, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.2, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -14.72, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 7.78, "time": 2}]}, "rau5": {"rotate": [{"angle": 2.57, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": -4.5, "time": 0.2667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.99, "time": 0.7667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.07, "time": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 9.64, "time": 1.7333, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": 2.57, "time": 2}]}, "toc16": {"rotate": [{"angle": -7.3, "time": 0, "curve": [0.375, 0.62, 0.716, 1]}, {"angle": -6.19, "time": 0.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.81, "time": 0.6, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.2, "time": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -14.72, "time": 1.6, "curve": [0.243, 0, 0.68, 0.71]}, {"angle": -7.3, "time": 2}]}, "rau4": {"rotate": [{"angle": 0.89, "time": 0, "curve": [0.382, 0.56, 0.74, 1]}, {"angle": -4.1, "time": 0.2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 7.39, "time": 0.7, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0.39, "time": 1.0333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.78, "time": 1.6667, "curve": [0.244, 0, 0.641, 0.57]}, {"angle": 0.89, "time": 2}]}, "Khieng tay2": {"translate": [{"x": -0.44, "y": -10.95, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": -0.44, "y": -10.95, "time": 2}]}, "toc13": {"rotate": [{"angle": -3.23, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": -6.81, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.2, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -5.96, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.81, "time": 1.8333, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -3.23, "time": 2}]}, "toc14": {"rotate": [{"angle": -6.27, "time": 0, "curve": [0.32, 0.29, 0.757, 1]}, {"angle": -6.81, "time": 0.4, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.2, "time": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"angle": -14.72, "time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.19, "time": 1.9, "curve": [0.284, 0, 0.625, 0.38]}, {"angle": -6.27, "time": 2}]}, "toc11": {"rotate": [{"angle": -5.39, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": -6.81, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.2, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -5.96, "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.81, "time": 1.6667, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": -5.39, "time": 2}]}, "toc12": {"rotate": [{"angle": 3.11, "time": 0, "curve": [0.372, 0.48, 0.752, 1]}, {"angle": -6.81, "time": 0.2667}, {"angle": -0.2, "time": 0.7667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -15.05, "time": 1.2667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 11.43, "time": 1.7667, "curve": [0.252, 0, 0.622, 0.48]}, {"angle": 3.11, "time": 2}], "translate": [{"x": -3.27, "y": -0.99, "time": 0, "curve": [0.372, 0.48, 0.752, 1]}, {"x": 0, "y": 0, "time": 0.2667, "curve": [0.25, 0, 0.75, 1]}, {"x": -4.39, "y": -1.02, "time": 0.7667, "curve": [0.25, 0, 0.75, 1]}, {"x": -2.58, "y": -0.79, "time": 1.2667, "curve": [0.25, 0, 0.75, 1]}, {"x": -6.01, "y": -1.82, "time": 1.7667, "curve": [0.252, 0, 0.622, 0.48]}, {"x": -3.27, "y": -0.99, "time": 2}]}}, "deform": {"default": {"body2": {"body": [{"curve": "stepped", "time": 0}, {"curve": "stepped", "time": 1}, {"time": 2}]}, "body": {"body": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"offset": 308, "time": 1, "vertices": [1.09988, 0.2829, 1.07983, 0.30849, 1.08929, 0.32109, -0.836, -0.74973, 3.40326, -3.39453, 3.45273, -3.26582, 3.51918, -3.27379, -4.53339, 1.42645, -0.90198, -9.57648, -0.64066, -9.489, -0.56854, -9.60209, -3.57462, 8.81294, -1.12851, -5.32694, -0.97589, -5.29473, -0.94273, -5.36304, -1.43854, 5.18794, -5.01584, 1.95612, -0.84344, -1.70723, -0.78903, -1.70953, -0.78371, -1.73558, -0.03827, 1.88225, -1.53017, 1.09692, -0.15402, -0.79419, -0.1315, -0.78901, -0.12642, -0.79907, -0.2269, 0.76694, -0.75008, 0.27777, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.90863, 1.3538, 1.85104, 1.38819, 1.86015, 1.41954, -1.05728, -2.05804, 1.01429, -2.0799, 1.58014, 0.73832, 1.54257, 0.77123, 1.55334, 0.79298, -1.04962, -1.36826, 0.46657, -1.66055, 0.80045, 0.75902, 0.77124, 0.77122, 0.77345, 0.78647, -0.3562, -1.03088, 0.61197, -0.90292, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.67319, -1.85479, -0.61682, -1.85091, -0.60858, -1.87712, -0.25488, 1.93413, -1.70146, 0.95438, -0.65521, -1.83765, -2.45706, -4.41237, -2.31345, -4.42562, -2.30342, -4.49535, 0.14423, 4.99112, -3.91025, 3.10492, -2.772, -4.13192, -2.63177, -4.15668, -2.62753, -4.22604, 0.54834, 4.88866, -3.58606, 3.36725, -0.80692, -2.38047, -0.73541, -2.37399, -0.72455, -2.40714, -0.3772, 2.45612, -2.19287, 1.16907], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}}}, [0, 1, 2]]], 0, 0, [0, 0, 0], [-1, -2, -3], [0, 1, 2]]