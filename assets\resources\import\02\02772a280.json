[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "7a/QZLET9IDreTiBfRn2PD", "77+3uXMlZJLIPxF3qoS2Lt", "fcXT5X4edMYKqmH8hZAmmr", "f0BIwQ8D5Ml7nTNQbh1YlS", "17+vwqpoFIR5QvFXONnsih", "bfRNqozmBKmKOTphbduI8j", "e42gjd3K5JhZuT64imhjNm", "a2MjXRFdtLlYQ5ouAFv/+R", "2afnwSFjtA5rL6jzxp80aR", "e97GVMl6JHh5Ml5qEDdSGa", "29FYIk+N1GYaeWH/q1NxQO", "a0uq4lmyxHtaGNT5B3nuEx", "3aml7mf39Bspjr1qMFz/+p", "c3KAXiY9NFMJ2pw3tDVVbj", "35DbLlkYdORYKEoYWMPnNx", "d2nBcOJbhLxYkhIFYwBc9D", "c5GR5x0vRH8amsssxYnZLB", "e6Rt0IcNlE7p3iOLbn3z1X", "1a0mqlC8hD+aECrSIX8UjP", "4bFf6lAndHj60Rd6/s3Rbg", "ffl/VX6llC6bVXDrM8u+Qe", "73nuLshiFDmoSlSl+eIRyT", "15hw04s/xJlZd7o1LOcInD", "78tCVH/ThBU4nz+50SYftM", "17PnXZLixHXJi92hEB4Amp", "a0YXI/eN5H7aIV1MZA4Ptk", "eeWiyBNkpJZ4Yt5144a6MK", "e12zRoL21Bh439fGHEkJzZ", "679UX79apEI5U98LROfLCp", "75ucHrXvRBob6KHdVlmI7c", "21nxQMmApEC5qAoyW1rEH5", "10FveEjXFE/ay8kC/A82fx", "91b/n9P1dAYK1/4XL7QRB7", "8boOqP1WNCkJsCZAOqF0Lr", "91LIR127ZLAaDHbyTxd1P8", "0aqpgBtjxEvqQuKw7pFz5Z", "1fmQ9+jSZGhbLarb2g7ORF", "7d6ppPdURP0IbP9MHsYbZ8", "e5w+DnLENJhJ9WZCQqyxLH", "d2GVv54U5N5bw0fW98Lwwz", "5dfIKQ/l5PnJt40NZH3qPC", "67ualx69NDdKhnZkkoHj8e", "8aehD4qpVNGreP6PGp55kL", "0ehXGz8FFJ0oCZ+VdyGQ1E", "850FUPLxtO3LKAqRmQtQLK", "63QjqLtHlAwbXth9k+ApAr", "81OhpPiXpPoYORljE4+9O2", "10JSDjfY9HgZKFLLKFIcu6", "14gSy8pM9NsJLtmfJzjH4s", "73cKx7ohBNLZmaI/zrPEiO", "0faJqGvv5LN7U7z73KMNtK", "ebW8GWxTtH8YanG9roIDRk", "f7P+xpoIJFI58DJ8oDfNL0", "a0zkGiUuBD9YuwI1AmYOSC", "89Wdx7Ow5Kc529ybLYecOU", "0afwNGwTNLApqjiipyiF9W", "84XabNi/VLHYW6MyGQ/mgA", "feGDySj0dLionwIf5utftz", "65hRHDrhlFTb8du76zNCB4", "aaD6uAnixAMpafzr9oTfsu", "deM9EOvlVBy7Ds6/X5kj9F", "d19RJMe1xLN7CEgOfqwNoM", "7dZVDJ6lVM3q9RwNr1sk6t", "15ltdRoKVDA7npB1w2YCia", "b74BOTIXtLWpEkwNWUxvBX", "99rFZmmutJX5x0/K6cwoAF", "c5KuF4b1ZONbvvbkaFAJMx", "581xKt1hNNZI1leTjUkc5y", "55rUR1z8pI8IaPGsduueRL", "985kzCZ61LRZQplTOJ3NHO", "45Ft5U9I1MW4xVR7josoNg", "02xHrB42pE6YhTnGScqkVo", "46DrhZXhdH/4BFVKuRUW3A", "60uO6krhdLPoLM2MaOYrRn", "73OLSFwktLeqhfP92A4PgW", "9f8lLufDJLn6P35lCJX+Nw", "9ftOt/eGJNvLLPhE+KPMNn", "bdC/HTmFRDqKBfCM3B6KBA", "94nFtpwipAO45TpvFB3Slb", "0fbvW7Bu9ImIvQRPfRLd4z", "a5ZUH2bsdCmITiLZAdPb9h", "84xJcOjZJDgIJG/b3LGEIW", "2dhe/mrzFEC7La8f1/ftkY", "276mgPAxVB46tk5TyDJiW2", "86ISNQhG1O6rc79viQ+8oR", "c8lFdz5B5Oy4Xw5SMXkxkq", "ccfPqyTfJCkYRG4ef8QdB6", "e8CiSwicBNyLplwEyaIs/o", "f0H1QOPHNBCbkZOSv5fnLZ", "8bt8rrZuxFyqvk50qlSnYr", "82k41LZXBIpLZubDxjhKi0", "a5WgMkpf1CRqnFW/O7gXiL", "e69cqvl0pAurUpmxe3f/4c", "f0iGa1E7lOCbybN4s8/mK8", "e2MwsNtstExqCOXNlv0bHI", "4f4zQINFVLD6qVdPfJMj1I", "faV5U8wQlJ0pWM8ls1jqMs", "ceWdsdvNVI5o494cheWAlJ", "25Hld9VfxLFoYHtjQudU5e", "2dSrpG8oFLaaZNDbzuk9gF", "f4/pckRTRDR6BHJyLJ3rhq", "b8DzTC2mBAILqST9gdMyJr", "fb9LsAJfdCgqQ0dnzE5uH8", "d2/5zR5P5EjJGzSU3Dk26i", "d2Cb4+2v5EhoyZCYZ1f6IW", "27uBfda1NH9o/ocuf4VDy/", "ccoTZUeixMI6owOFJ7V0vJ", "8cMA5LMzRBB6LIhgQ7KBJQ", "c8QLDCiT1NObFdEcFwsChK", "9dsMtC2LFKPawy9mHgKmY6", "dbUhEgNelEXY1H4X7aYon1", "e8NwRWXMJAcaPnnZ0GOddW", "3f2l3DfB1Nd7wyYXWutfpM", "c6+LuySHZP7qFJ+YWwmbwF", "a2mc2NubpBMqYm1fMs4vic", "6atLBSPb9P+Lerg1a1UBxL", "3b1pVcg2BIEozmIi/wOeIS", "1c2IGtxNVBNYnkQ3c3xyU/", "20WKyiql5HOKIxgiCIa7kK", "11BMDtnjZFWZxUcI1AWF6N", "63lW44Xh9IHajTKHs61HAc", "e2piGgXq5HGrc5sIZAWccO", "b117tkPaJLK7Vgz3+Eid77", "3681s6HZBGyL/FuJm+3/rT", "7eUj2WuV1NiI+GniePHIVx", "0d7CrCWEFFfoVo/UaecXCa", "69a5p1gSRM0JxWzo/HUMjn", "45Q1VImoRHCoMzq6hWKuXQ", "23MSO9RzRP47x/eKudKkZ4", "f0H6ZRJ41CZYqHIdr55e0y", "91RCZHXWhOL4g7DoR3ZcDp", "c2UTpR0HdFKqba10HAXIzA", "c4KumOXt1A05Y2CW47rqaW", "e9fRxQdyRNdohHuaF7fNc/", "5dKN47sElHmpcj+OVuw8kS", "d2YWj13mxOiKUcSAkzUM4g", "63YfJh2hhHLbYn1cAsA59/", "df/0GUsjRDgpriUh+90Ngb", "adw94Z+hpN57wutNivq8Q5", "06KjV/+zdO6LACcRXvbAUI", "06xLKbVk1L1L82gIfLc5sG", "a8gJq6BOFL2KALbCW0PQf6", "5bjrvt2EhNPq1C9dcc+XC9", "a9hr/K7EpJFL2IHoSwzqhI", "cb3HrZVpxEgKsDLaGvZIvg", "6dCT0B8ntDUrWrHgwBxpfL", "afdr+B4ktOMYraC5VK213Y", "e2HRO6ondGeZgEqmQ0KlXy", "cc+mkWovJDHafpDlVqOAfj", "8aCRjlWJBDNYnyvmZZ/Ogu", "e3kQScUVtMN4ZWY40mcAQd", "deMl43On1NCZO9it2BRhDu", "7bLpX+Wi5G7ol+Mj959hLz", "c9TDzIbwlHDbW1QLI8b9PD", "4cgNR3/+hLwpam1Jqhb5JD", "a1vyg9fyVGHIQCL9ha4eY6", "32rcdlsXtL14G5eOdIzU/c", "41GJbPZvJDHqxOKJ0O804Q", "bc7mLPdM1HfKADvudRoNnO", "84QTfBXYhMFr/WKKQm2czF", "13Szu4EgVDTKY9hM9YIuSa", "08Eq+eqZZFwoQp5CFs33b+", "c30PkdbrhIjpUjrMGrft89", "8aF7w6Ep5FzaV4LqaPf9WB", "4f3toalutMzo3NnTIdhQeC", "22TJEQm6xNpIDpayRxU+Li", "1bxiW0xZZKBb3qmkW/8+B6", "80aAJgnFhC8ZW850IJeXXI", "d1zqY0mtJCd6aFMtLlP4eN", "feATCZ3PBBbb/PBoKpQLSa", "1eL5M7wdZGCqJpJF5FMrBp", "d8wj8wrxhGS6IikcIfnr6k", "394vBOc4VPbJ92IOFIN63b", "9cgg1sEztG+LICseLnf+zr", "b8zSNeKCFN6o64gddRb9iO", "dbB9CnbmVEKqUjzzae7AEN", "29/iI4b+1KP5c6iZiWvK8B", "calj5El+1MAoJ1zHScTKno", "0cKGYrKj5LOawYZJTDWbqu", "adJML3X+tMR5KOMdEVTQCB", "34ETBUAZBD1pHZcE53EUGM", "20D271gHBNm7WwpPG00DeD", "60ao803qNKyLgTz6EKUZvV", "83TuYzopRPA5jxFwS/pUJd"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_defaultClip", "_N$skeletonData", "_parent", "_N$target", "lblCoin", "lblServiceName", "lblNickname", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "checkMark", "skeleton", "clipClick", "root", "lblServerTime", "lblPing", "popupGuide", "panelMenu", "effectMegaWin", "effectBigWin", "effectJackpot", "lblTargetFishTime", "progressTargetFish", "btnTargetFish", "lblFastShootTime", "progressFastShoot", "btnFastShoot", "waveState", "target", "toggleAuto", "lblJackpot", "coinEffectTemplate", "fishTemplate", "fishsNode", "bulletTemplate", "touchPad", "loading", "lobby", "avatar", "lblUserName", "popupCoinTransfer", "lblBalance", "itemTemplate", "grid", "coin2", "coin1", "coin0", "coinExplore", "lblId", "anim", "fishNet", "bullet", "data", "sprGunBar", "lblBet", "btnSoundMain", "btnMusic", "btnSound", "clipHit", "clipCoin", "clipJackpot", "clipBgm", "sfSoundOn", "sfSoundOff", "sfMusicOn", "sfMusicOff", "sfSoundOnMain", "sfSoundOffMain", "sfMusicOnMain", "sfMusicOffMain"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_opacity", "_prefab", "_parent", "_components", "_children", "_contentSize", "_trs", "_color", "_eulerAngles", "_anchorPoint"], -1, 4, 1, 9, 2, 5, 7, 5, 5, 5], ["cc.Label", ["_N$verticalAlign", "_string", "_N$horizontalAlign", "_fontSize", "_isSystemFontUsed", "_N$overflow", "_lineHeight", "_enableWrapText", "_spacingX", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalHeight", "_left", "_originalWidth", "_top", "_right", "_bottom", "alignMode", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_dstBlendFactor", "_fillType", "_fillStart", "_fillRange", "node", "_spriteFrame", "_materials", "_fillCenter"], -5, 1, 6, 3, 5], ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_color", "_children", "_eulerAngles"], 0, 2, 4, 1, 5, 7, 5, 2, 5], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "_animationName", "defaultAnimation", "loop", "_playTimes", "node", "_materials", "_N$skeletonData"], -3, 1, 3, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 1, 12, 4, 5, 1, 7, 2, 5, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "_enabled", "zoomScale", "_N$enableAutoGrayEffect", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], -1, 1, 9, 1, 5, 5, 5, 6, 6, 6, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "_N$spacingY", "node", "_layoutSize", "_N$cellSize"], -1, 1, 5, 5], ["cc.Toggle", ["_N$transition", "_N$isChecked", "node", "checkMark"], 1, 1, 1], ["cbf1dlYiXRG35Ei6K+I4cFm", ["node", "designResolution"], 3, 1, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["720d1dsYhpMV5P3msI2r43M", ["localPos", "node", "gunRotate", "sprFramesGunBar", "guns", "lblNickname", "lblServiceName", "lblCoin", "lblBet", "sprGunBar"], 2, 1, 1, 3, 2, 1, 1, 1, 1, 1], ["cc.AudioClip", ["_name", "_native", "duration"], 0], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["5fa1efbhuJLl5N2iupithus", ["node", "lobby", "loading", "touchPad", "sprFramesBullet", "bulletTemplate", "players", "fishsAnim", "fishsNode", "fishTemplate", "coinEffectTemplate", "lblJackpot", "toggleAuto", "target", "waveState", "btnFastShoot", "progressFastShoot", "lblFastShootTime", "btnTargetFish", "progressTargetFish", "lblTargetFishTime", "effectJackpot", "effectBigWin", "effectMegaWin", "panelMenu", "popupGuide", "lblPing", "lblServerTime", "clipClick", "clipHit", "clipCoin", "clipJackpot"], 3, 1, 1, 1, 1, 3, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6], ["4378bty4U5E8KWt2uwkMkhd", ["node", "playNode", "lblBalance", "popupCoinTransfer", "lblUserName", "avatar", "clipBgm", "clipClick"], 3, 1, 1, 1, 1, 1, 1, 6, 6], ["7d13cnQbVxCL6YsYEEUmyp0", ["node", "spriteChips", "sfChips"], 3, 1, 2, 3], ["cc.BlockInputEvents", ["node"], 3, 1], ["bab5aIcJEpPB53KA3RPsstw", ["node", "grid", "itemTemplate"], 3, 1, 1, 1], ["f3de4Savp5BHqfZYOvwMNmn", ["node", "lblCoin", "coinExplore", "coin0", "coin1", "coin2"], 3, 1, 1, 1, 1, 1, 1], ["8dcf1d04CtOE75piBWi/t9b", ["node", "anim", "lblId"], 3, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["36d2bcC+d9JWLYn26kehzX6", ["node", "bullet", "fishNet"], 3, 1, 1, 1], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1], ["ce53aga3XZMIaymrxUZy+Ja", ["node", "skeletons"], 3, 1, 2], ["3e4a4YbN9FGnbtYmPvTrs9e", ["node"], 3, 1], ["3ebbaXYFItGA4MRPRT0exe9", ["node", "arrow", "btnMusicMain"], 3, 1, 1, 1], ["cc.ToggleContainer", ["node"], 3, 1], ["cc.EditBox", ["_N$backgroundImage", "_N$returnType", "_N$fontSize", "_N$lineHeight", "_N$placeholder", "_N$placeholderFontSize", "_N$maxLength", "_N$inputMode", "node", "_N$fontColor", "_N$placeholderFontColor"], -5, 1, 8, 8], ["bcd88coqOBPF7ZC2zQEsAhZ", ["node", "tabs", "tabContents", "tabCashIn", "tabCashOut"], 3, 1, 1, 1, 4, 4], ["PopupCoinTransfer.TabCashIn", ["lblBalance", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quickButtons"], 3, 1, 1, 1], ["PopupCoinTransfer.TabCashOut", ["lblBalance", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quickButtons"], 3, 1, 1, 1], ["cc.TTFFont", ["_name", "_native"], 1]], [[18, 0, 1, 2], [1, 0, 5, 6, 4, 8, 9, 2], [12, 0, 1, 2, 3, 2], [1, 0, 5, 6, 4, 8, 2], [1, 0, 5, 7, 6, 4, 8, 9, 2], [26, 0, 1, 2, 3], [1, 0, 5, 7, 4, 9, 11, 2], [4, 8, 9, 1], [4, 0, 2, 8, 10, 9, 3], [5, 0, 5, 3, 4, 6, 2], [4, 8, 10, 9, 1], [8, 0, 4, 2], [2, 1, 3, 6, 4, 2, 0, 5, 9, 11, 8], [5, 0, 5, 3, 4, 6, 7, 2], [6, 3, 0, 1, 2, 6, 7, 5], [5, 0, 5, 3, 4, 8, 6, 7, 2], [4, 4, 0, 8, 10, 9, 3], [8, 0, 4, 5, 2], [4, 1, 0, 8, 9, 3], [4, 0, 8, 10, 9, 2], [1, 0, 7, 6, 4, 8, 2], [3, 7, 0, 3, 1, 8, 5], [9, 1, 0, 2, 4, 5, 4], [3, 0, 8, 2], [8, 0, 4, 5, 6, 2], [2, 1, 3, 4, 2, 0, 9, 6], [15, 0, 1, 2, 4], [1, 0, 5, 7, 6, 4, 8, 2], [1, 0, 5, 6, 4, 10, 8, 9, 2], [7, 0, 5, 2, 3, 4, 6, 2], [4, 0, 2, 8, 9, 3], [2, 1, 3, 4, 2, 0, 9, 10, 6], [1, 0, 1, 5, 7, 6, 4, 8, 3], [1, 0, 7, 4, 9, 11, 2], [1, 0, 5, 6, 4, 8, 9, 11, 2], [1, 0, 5, 6, 4, 10, 8, 2], [5, 0, 9, 3, 4, 7, 2], [5, 0, 1, 5, 3, 4, 8, 6, 7, 3], [3, 7, 0, 2, 5, 3, 1, 8, 7], [4, 0, 8, 9, 2], [4, 1, 0, 8, 3], [4, 8, 10, 1], [4, 8, 1], [8, 0, 4, 5, 7, 8, 9, 6, 10, 11, 12, 13, 2], [6, 0, 1, 6, 7, 8, 3], [2, 1, 3, 7, 2, 0, 5, 9, 7], [2, 1, 3, 4, 2, 0, 5, 9, 10, 7], [2, 1, 3, 6, 7, 4, 2, 0, 5, 9, 10, 9], [2, 1, 4, 2, 0, 9, 5], [1, 0, 5, 7, 4, 2], [1, 0, 5, 6, 4, 2], [5, 0, 1, 5, 9, 3, 4, 3], [7, 0, 5, 7, 2, 3, 4, 6, 2], [8, 2, 0, 4, 5, 3], [13, 1, 2, 1], [13, 0, 1, 2, 2], [6, 3, 0, 1, 2, 6, 8, 5], [6, 3, 0, 1, 2, 6, 7, 8, 5], [2, 1, 3, 4, 2, 0, 9, 11, 6], [2, 1, 3, 6, 7, 4, 2, 0, 5, 9, 11, 9], [2, 1, 3, 6, 4, 2, 0, 9, 7], [14, 0, 1, 5, 6, 7, 8, 2, 9, 3, 4, 2], [1, 0, 1, 7, 4, 3], [1, 0, 5, 7, 6, 4, 9, 2], [1, 0, 5, 4, 2], [1, 0, 5, 7, 4, 8, 9, 2], [1, 0, 1, 5, 7, 6, 4, 8, 9, 3], [1, 0, 3, 5, 6, 4, 10, 8, 3], [1, 0, 1, 5, 6, 4, 8, 12, 9, 3], [1, 0, 5, 6, 4, 10, 8, 12, 9, 2], [5, 0, 5, 9, 3, 4, 6, 7, 2], [5, 0, 2, 5, 3, 4, 8, 6, 3], [5, 0, 5, 3, 4, 6, 7, 10, 2], [5, 0, 5, 3, 4, 2], [5, 0, 1, 5, 3, 4, 6, 3], [7, 0, 5, 2, 3, 4, 2], [7, 0, 5, 2, 3, 8, 4, 9, 6, 2], [3, 0, 2, 5, 4, 6, 3, 1, 8, 8], [3, 7, 0, 2, 5, 4, 6, 3, 1, 8, 9], [9, 1, 0, 4, 5, 6, 3], [9, 0, 2, 3, 4, 5, 4], [22, 0, 1], [4, 1, 5, 6, 7, 8, 10, 11, 5], [11, 0, 1, 1], [6, 3, 0, 1, 2, 6, 5], [6, 3, 0, 4, 2, 5, 6, 6], [2, 3, 7, 4, 2, 0, 5, 9, 11, 7], [2, 1, 3, 2, 0, 9, 10, 5], [2, 1, 3, 6, 7, 4, 2, 0, 5, 9, 9], [28, 0, 1, 2, 2], [30, 0, 1], [33, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [16, 0, 2], [1, 0, 7, 6, 4, 8, 9, 2], [1, 0, 5, 7, 4, 8, 2], [1, 0, 2, 5, 7, 6, 4, 8, 3], [1, 0, 1, 7, 6, 4, 3], [1, 0, 7, 4, 2], [1, 0, 2, 1, 5, 7, 6, 4, 8, 4], [1, 0, 7, 6, 4, 2], [1, 0, 2, 5, 7, 4, 8, 3], [1, 0, 1, 5, 7, 4, 9, 3], [1, 0, 1, 5, 6, 4, 8, 9, 3], [1, 0, 5, 7, 4, 9, 2], [1, 0, 1, 5, 6, 4, 8, 3], [1, 0, 5, 6, 4, 8, 12, 9, 2], [1, 0, 2, 5, 7, 4, 3], [5, 0, 5, 9, 3, 4, 7, 2], [5, 0, 2, 5, 3, 4, 8, 6, 7, 3], [5, 0, 1, 5, 3, 4, 8, 6, 3], [7, 0, 1, 5, 7, 2, 3, 4, 3], [7, 0, 7, 2, 3, 4, 2], [7, 0, 1, 5, 2, 3, 4, 6, 3], [17, 0, 1, 2, 3, 4, 5, 2], [3, 0, 3, 1, 8, 4], [3, 0, 4, 8, 3], [3, 0, 2, 4, 8, 4], [3, 0, 2, 5, 4, 8, 5], [3, 0, 5, 8, 3], [3, 0, 6, 1, 8, 4], [3, 0, 4, 6, 1, 8, 5], [3, 0, 2, 6, 1, 8, 5], [3, 0, 2, 5, 6, 1, 8, 6], [3, 7, 0, 2, 3, 1, 8, 6], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 1], [20, 0, 1, 2, 3, 4, 5, 6, 7, 1], [21, 0, 1, 2, 1], [9, 0, 3, 4, 5, 3], [4, 3, 0, 8, 9, 3], [4, 0, 8, 10, 2], [4, 0, 8, 2], [23, 0, 1, 2, 1], [24, 0, 1, 2, 3, 4, 5, 1], [25, 0, 1, 2, 1], [8, 1, 4, 2], [8, 3, 0, 4, 5, 3], [10, 0, 2, 3, 2], [10, 0, 1, 2, 3, 3], [10, 0, 1, 2, 3], [11, 0, 1], [27, 0, 1, 2, 1], [12, 1, 2, 1], [6, 0, 1, 2, 6, 7, 8, 4], [6, 0, 1, 2, 6, 7, 4], [6, 3, 0, 4, 1, 2, 5, 6, 7], [2, 1, 3, 6, 0, 5, 9, 6], [2, 1, 3, 6, 4, 2, 0, 5, 9, 10, 8], [2, 1, 4, 2, 0, 5, 9, 11, 6], [2, 1, 2, 0, 9, 4], [2, 1, 3, 6, 4, 8, 2, 0, 9, 8], [2, 1, 3, 4, 2, 0, 5, 10, 7], [2, 1, 3, 7, 4, 2, 0, 5, 9, 10, 8], [14, 1, 2, 3, 4, 1], [29, 0, 1, 1], [31, 0, 1, 2, 1], [32, 0, 1], [34, 0, 1, 2, 3, 4, 1], [35, 0, 1, 2, 1], [36, 0, 1, 2, 1], [37, 0, 1, 3]], [[[{"name": "toggleMusicOn", "rect": [1, 1, 68, 68], "offset": [-0.5, 0.5], "originalSize": [71, 71], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [65]], [[{"name": "bg", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [66]], [[{"name": "btnBack2", "rect": [1, 1, 68, 69], "offset": [-0.5, 0], "originalSize": [71, 71], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [67]], [[{"name": "coin_2", "rect": [0, 0, 41, 45], "offset": [0, 0], "originalSize": [41, 45], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [68]], [[{"name": "music-icon", "rect": [0, 0, 76, 78], "offset": [0, 0], "originalSize": [76, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [69]], [[{"name": "btnMenu", "rect": [1, 4, 42, 59], "offset": [-1.5, 0.5], "originalSize": [47, 68], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [70]], [[{"name": "btnBack", "rect": [2, 2, 74, 75], "offset": [-0.5, 0], "originalSize": [79, 79], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [71]], [[{"name": "6_00000", "rect": [3, 0, 108, 252], "offset": [1, 0], "originalSize": [112, 252], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [72]], [[{"name": "dan2", "rect": [0, 0, 48, 136], "offset": [0, 0], "originalSize": [48, 136], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [73]], [[{"name": "coin_1", "rect": [0, 0, 39, 38], "offset": [0, 0], "originalSize": [39, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [74]], [[{"name": "popupBg", "rect": [0, 0, 992, 622], "offset": [0, 0], "originalSize": [992, 622], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [75]], [[{"name": "ui_sung3", "rect": [1, 0, 180, 78], "offset": [-0.5, 0], "originalSize": [183, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [76]], [[[26, "bomb_explosion", ".mp3", 2.847347], -1], 0, 0, [], [], []], [[{"name": "info", "rect": [0, 0, 153, 115], "offset": [-1, 0], "originalSize": [155, 115], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [77]], [[{"name": "bg_01", "rect": [0, 0, 929, 545], "offset": [0, 0], "originalSize": [929, 545], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [78]], [[{"name": "1", "rect": [4, 2, 202, 202], "offset": [1, 1], "originalSize": [208, 208], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [79]], [[{"name": "btnHelp2", "rect": [1, 1, 68, 69], "offset": [-0.5, 0], "originalSize": [71, 71], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [80]], [[[26, "button_click", ".mp3", 0.20898], -1], 0, 0, [], [], []], [[[92, "ShootFish"], [93, "ShootFish", [-3, -4], [[23, 45, -2]], [0, "c1SMxYRtZOaKZZEMnBN5Mh", -1], [5, 1561, 732], [780.5, 366, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "Play", false, 1, [-60, -61, -62, -63, -64, -65, -66, -67, -68, -69, -70], [[23, 45, -5], [124, -59, -58, -57, -56, [38, 39, 40, 41], -55, [-51, -52, -53, -54], [-26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50], -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, 247, 248, 249, 250]], [0, "29jVY8H8dF3aJSIYf0igcX", 1], [5, 1561, 732]], [62, "fishsTemplate", false, [-71, -72, -73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92, -93, -94, -95], [0, "67naz4ZU5Jbr7EQNzg7gG1", 1]], [27, "Lobby", 1, [-106, -107, -108, -109, -110, -111, -112, -113, -114, -115, -116], [[23, 45, -96], [125, -101, 2, -100, -99, -98, -97, 384, 385], [126, -105, [-102, -103, -104], [386, 387, 388]]], [0, "0cFm+i/m5EpKRUOpeL0CtN", 1], [5, 1561, 732]], [94, "container", 2, [3, -117, -118, -119, -120, -121, -122, -123, -124, -125, -126, -127, -128, -129, -130], [0, "3dRYJWIGFJSIC31DBweD/m", 1], [5, 1561, 732]], [95, "wave", 512, 5, [-132, -133, -134, -135, -136, -137, -138, -139, -140, -141, -142, -143], [[79, 1, 3, -131, [5, 2060, 1536], [5, 256, 256]]], [0, "d9JiDU7JhBRYEEOmigUIWL", 1], [5, 2060, 1536]], [20, "quickButtons", [-145, -146, -147, -148, -149, -150, -151, -152, -153], [[80, 3, 94.6, 16.1, -144, [5, 620, 200]]], [0, "1fKCsBhFhBG4HJvariAD+a", 1], [5, 620, 200]], [20, "quickButtons", [-155, -156, -157, -158, -159, -160, -161, -162, -163], [[80, 3, 94.6, 16.1, -154, [5, 620, 200]]], [0, "57kXH3Iz5Js74BDIji7yd1", 1], [5, 620, 200]], [36, "player", [-165, -166, -167, -168, -169, -170, -171, -172, -173], [-164], [0, "18zat7Kz5Kho4bKGbW4x70", 1], [-323.5, -353.8, 0, 0, 0, 0, 1, 1, 1, 1]], [110, "PopupCoinTransfer", false, 4, [-176, -177], [[[81, -174], -175], 4, 1], [0, "04IeRMOLVJ/a5qaVmeMyHi", 1], [5, 1398, 786]], [111, "panelMenu", [-180, -181], [[[10, -178, [307], 308], -179], 4, 1], [0, "feDKcUh8BEL6v6JaxbdAY5", 1], [5, 150, 407]], [32, "PopupGuide", false, 2, [-187, -188], [[114, 45, 1280, 720, -182], [81, -183], [131, -186, -185, -184]], [0, "25yyjEATtEb5JI///QWRl8", 1], [5, 1398, 786]], [36, "player1", [-190, -191, -192, -193, -194, -195, -196], [-189], [0, "52n0zW9S1Io6rq5HnLYeJR", 1], [323.5, -353.8, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "player2", [-198, -199, -200, -201, -202, -203, -204], [-197], [0, "5dzGYJzpZDwIB898ENgSFL", 1], [323.5, 353.8, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "player3", [-206, -207, -208, -209, -210, -211, -212], [-205], [0, "38WZZFNMpBkIo5f2WM/Aoc", 1], [-323.5, 353.8, 0, 0, 0, 0, 1, 1, 1, 1]], [96, "coinEffect", false, [-219, -220, -221, -222, -223], [[132, -218, -217, -216, -215, -214, -213]], [0, "23TIH0OzdLrJqbBBliMPv2", 1]], [20, "fish", [-229, -230], [[133, -226, -225, -224], [128, false, 0, -227, 213], [134, false, -228]], [0, "7eZ0uetKRHpJf6BmhaMqCf", 1], [5, 100, 50]], [51, "effectJackpot", false, 5, [-232, -233, -234, -235], [-231], [0, "887BbTW5dG3qQDsUe9SwrO", 1]], [4, "buttons", 11, [-237, -238, -239, -240], [[127, 3, 20, -236, [5, 72.8, 343.1]]], [0, "74H4NrelhFN7pAj+c+Yn5v", 1], [5, 72.8, 343.1], [22.7, -2.9, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnAddCoin", 4, [-242, -243, -244, -245], [[17, 3, -241, [[5, "bcd88coqOBPF7ZC2zQEsAhZ", "show", 10]]]], [0, "d5/Sy0XtVN65xMMzm7MAAv", 1], [5, 288, 50], [0, 296.6, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "Container", 10, [-247, -248, -249, -250], [[18, 1, 2, -246, 383]], [0, "7bdyOggfFL57JnIowSNPYe", 1], [5, 929, 545]], [107, "tabs", 21, [-252, -253, -254, -255], [-251], [0, "eehJAA0W1ARI/yEMVb4NeT", 1], [0, 137, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "tabCashIn", 22, [-259, -260, -261], [[7, -256, 329], [136, 3, -258, -257]], [0, "3f5Qq7nHRMyLFDVYd2V1kG", 1], [5, 122, 58], [-80, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "tabCashOut", 22, [-265, -266, -267], [[7, -262, 331], [137, 3, false, -264, -263]], [0, "d0HRu3BgdANroWZGzyx3jX", 1], [5, 122, 58], [80, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [97, "cashIn", [-268, -269, -270, -271, 7], [0, "c3VBtHJcFBN7WF/gM5URlO", 1]], [70, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 25, [-273, -274, -275, -276], [-272], [0, "a1nib95+9GIY3YEmJdSX5y", 1], [5, 210, 46], [-73.5, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "cashOut", false, [-277, -278, 8, -279, -280], [0, "f3QXSAE9RHYbP9YVRuh+Yi", 1]], [70, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 27, [-282, -283, -284, -285], [-281], [0, "2drXOrIrBFa5AJH/FSjj7g", 1], [5, 210, 46], [-73.5, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "Background", [-288], [[18, 1, 0, -286, 10], [21, 0, 45, 100, 40, -287]], [0, "44hBO6qZRORY/UJYvP8IcR", 1], [5, 100, 40]], [20, "Background", [-291], [[18, 1, 0, -289, 15], [21, 0, 45, 100, 40, -290]], [0, "f1MHlCRahCipO+Fja2ftYZ", 1], [5, 100, 40]], [20, "Background", [-294], [[18, 1, 0, -292, 20], [21, 0, 45, 100, 40, -293]], [0, "7aEQiVv+lOHZEsndyIe5hb", 1], [5, 100, 40]], [20, "Background", [-297], [[18, 1, 0, -295, 25], [21, 0, 45, 100, 40, -296]], [0, "d2BUClPRdMi6YGH2yhU2vK", 1], [5, 100, 40]], [98, "loading", 512, false, 2, [-300], [[7, -298, 32], [83, -299, [5, 1280, 720]]], [0, "67TJjIuNlP54n5l2h3Uc/r", 1], [5, 1280, 720]], [27, "Container", 12, [-302, -303, -304], [[7, -301, 37]], [0, "835B6R651KwZiUXgx1o87N", 1], [5, 992, 622]], [99, "bullet", [-308, -309], [[140, -307, -306, -305]], [0, "30ii4RartJRqmCYaGKRbZ2", 1]], [49, "players", 5, [9, 13, 14, 15], [0, "84dXr4VwpG9ZxVmZlTyVqK", 1]], [33, "-90", [-310, -311, -312, -313], [0, "00iYmlxoJCW4rsodNwNhxZ", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [33, "-90", [-314, -315, -316, -317], [0, "f8qqWqNMZJRb5CJjW+oyHK", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [33, "-90", [-318, -319, -320, -321], [0, "2fEawKTQNBGI/rFjx3idII", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [33, "-90", [-322, -323, -324, -325], [0, "24J/IL8EZFNKept+aRhK68", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [63, "rightPanel", 5, [-327, -328, -329], [[23, 8, -326]], [0, "905V/eSgJOaJHZ9OjbpRqg", 1], [-780.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "btnFastShoot", 41, [-332, -333], [[[10, -330, [222], 223], -331], 4, 1], [0, "86VZrpQPtBNJLsfEK0Bbic", 1], [5, 79, 80], [61.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "btnTargetFish", 41, [-336, -337], [[[10, -334, [226], 227], -335], 4, 1], [0, "d0qHHa/vBJe793pxZeGPZY", 1], [5, 80, 80], [61.6, -98, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "effectBigWin", false, 5, [-339, -340, -341], [-338], [0, "a6qDNxLGhNVaKhX87SwsaC", 1]], [51, "effectMegaWin", false, 5, [-343, -344, -345], [-342], [0, "d0nxOLtkNAeIEifbTggZZ6", 1]], [100, "border", 512, 5, [-346, -347, -348, -349], [0, "34oSZf0RFA35zzC25Ve53R", 1], [5, 1398, 786]], [101, "profile", false, 4, [-350, -351, -352], [0, "f7Dw9I9eJJOLGxWrhr92YD", 1], [-182, 277, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BtnClose", 34, [[7, -353, 35], [24, 3, -355, [[5, "bab5aIcJEpPB53KA3RPsstw", "dismiss", 12]], -354]], [0, "769AFyjM5C94rpDY2jc8HQ", 1], [5, 70, 71], [468, 278, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "Grid", 34, [-357], [[79, 2, 3, -356, [5, 957, 523], [5, 191.4, 104.6]]], [0, "91zjXUPqRCoai+V98AuUza", 1], [5, 957, 523]], [113, "itemTemp", 49, [[[64, "fishParent", -358, [0, "23hBrCW/tAl4mxhSxZSp71", 1]], -359], 4, 1], [0, "373mQUuVZI1IkX7cT8Dvf4", 1], [5, 191.4, 104.6], [-382.8, 209.2, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Layout copy", 13, [-361, -362], [[22, 1, 1, 2, -360, [5, 57.6, 50]]], [0, "376Nxjnk1HZ6/AJE49+DFa", 1], [5, 57.6, 50], [234.3, 59.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Layout copy", 14, [-364, -365], [[22, 1, 1, 2, -363, [5, 66.6, 50]]], [0, "4fPZvGbA5JLrGMaYn9WPhK", 1], [5, 66.6, 50], [234, -72.3, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Layout copy", 15, [-367, -368], [[22, 1, 1, 2, -366, [5, 82.8, 50]]], [0, "0fDXUKeFVJXJ6tirgMpBiS", 1], [5, 82.8, 50], [-204, -72.3, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Layout", 9, [-370, -371], [[22, 1, 1, 2, -369, [5, 82.8, 50]]], [0, "c4cu9G+z5LQJeZPlRUc9H1", 1], [5, 82.8, 50], [-203.5, 59.5, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "toggleAuto", 41, [-374], [[[10, -372, [228], 229], -373], 4, 1], [0, "5b6+sU9gFEU450uSabX6In", 1], [5, 80, 80], [61.6, 98, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "waveState", 5, [[19, 0, -375, [232], 233], [141, -376, [234]]], [0, "f4P9MB3u9J7LP7KY7eIMKh", 1], [5, 475, 732]], [4, "New Layout", 18, [-378, -379], [[22, 1, 1, 10, -377, [5, 460, 80]]], [0, "3cQX6EJy5FeY+ND9JATtTb", 1], [5, 460, 80], [0, -51, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Layout copy", 44, [-381, -382], [[22, 1, 1, 10, -380, [5, 423.31, 80]]], [0, "03IbUQEy9OVb1KutnfJ0Tt", 1], [5, 423.31, 80], [0, 79, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "New Layout copy", 45, [-384, -385], [[22, 1, 1, 10, -383, [5, 399.27, 80]]], [0, "31OosN4T1FW4ag1KLzSzgR", 1], [5, 399.27, 80], [0, 130, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnMenu", 11, [[10, -386, [235], 236], [17, 3, -387, [[5, "3ebbaXYFItGA4MRPRT0exe9", "toggleShow", 11], [5, "5fa1efbhuJLl5N2iupithus", "playClickSound", 2]]]], [0, "31jeHGwgJDup8AgDIAkbJb", 1], [5, 42, 59], [-54.1, 0, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, -180]], [1, "btnBack", 19, [[10, -388, [237], 238], [23, 9, -389], [17, 3, -390, [[5, "5fa1efbhuJLl5N2iupithus", "actBack", 2]]]], [0, "8cE+wOhjdD/r8KQXpoNPWd", 1], [5, 68, 69], [-2.3999999999999986, 137.05, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 19, [[10, -391, [239], 240], [115, 9, 89, -392], [17, 3, -393, [[5, "bab5aIcJEpPB53KA3RPsstw", "show", 12], [5, "5fa1efbhuJLl5N2iupithus", "playClickSound", 2]]]], [0, "58JFhGwk1LqpteA3nqjUCX", 1], [5, 68, 69], [-2.3999999999999986, 48.05000000000001, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBack", 4, [[10, -394, [315], 316], [116, 9, 38.52999999999997, 38.22000000000003, -395], [17, 3, -396, [[5, "4378bty4U5E8KWt2uwkMkhd", "actBack", 4]]]], [0, "efmLkYYOpOXL9dyGP/C3fA", 1], [5, 74, 75], [-704.97, 290.28, 0, 0, 0, 0, 1, 1, 1, 1]], [102, "btnHonors", false, 4, [[7, -397, 317], [117, 33, 1034.533, 45.863, 37.72000000000003, -398], [11, 3, -399]], [0, "dd2qVoC/lNw5rYyl+Q3RbT", 1], [5, 76, 76], [556.137, 284.28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BtnClose", 21, [[30, 2, false, -400, 326], [24, 3, -402, [[5, "bcd88coqOBPF7ZC2zQEsAhZ", "dismiss", 10]], -401]], [0, "02PKMw+TZFjIucBrnW6kLZ", 1], [5, 61, 61], [426.5, 235, 0, 0, 0, 0, 1, 1, 1, 1]], [65, "EdbBalance", 22, [-403, -404, -405], [0, "7feYX9wGdKfohoNRIF0FOT", 1], [5, 299, 46], [-185, -77, 0, 0, 0, 0, 1, 1, 1, 1]], [65, "EdbBalance", 22, [-406, -407, -408], [0, "c2T4uZxWdD9LHjtGsyDjhn", 1], [5, 299, 46], [183, -77, 0, 0, 0, 0, 1, 1, 1, 1]], [103, "contents", 21, [25, 27], [0, "edxXvJJV9OFZR+TMIWJbQd", 1], [0, -83, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BtnSubmit", 25, [[30, 2, false, -409, 339], [24, 3, -411, [[5, "bcd88coqOBPF7ZC2zQEsAhZ", "actSubmitCashIn", 10]], -410]], [0, "c4BO3GwvBAJL/XH3dgUEpp", 1], [5, 208, 69], [232, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClear", 25, [[30, 2, false, -412, 340], [24, 3, -414, [[5, "bcd88coqOBPF7ZC2zQEsAhZ", "actClearCashIn", 10]], -413]], [0, "f3/4Jk2sZBC4EZDS/OCAui", 1], [5, 47, 47], [55, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn", 7, [-417], [[7, -415, 342], [11, 3, -416]], [0, "0bJH9bBpZJUKSXb/eY1UCO", 1], [5, 141, 48], [-239.50000000000003, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-420], [[7, -418, 344], [11, 3, -419]], [0, "f1dsMGnbtAt6aVgX27v3xw", 1], [5, 141, 48], [-3.900000000000034, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-423], [[7, -421, 346], [11, 3, -422]], [0, "74chmRF8JGBrP35tL27elz", 1], [5, 141, 48], [231.69999999999996, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-426], [[7, -424, 348], [11, 3, -425]], [0, "e4VWz/C6ZNx6zuMIGGv4NE", 1], [5, 141, 48], [-239.5, 11.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-429], [[7, -427, 350], [11, 3, -428]], [0, "fcZQsL5elASbVFUGyj5WTk", 1], [5, 141, 48], [-3.9000000000000057, 11.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-432], [[7, -430, 352], [11, 3, -431]], [0, "09TbEL7NRFeonm+i58j23o", 1], [5, 141, 48], [231.7, 11.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-435], [[7, -433, 354], [11, 3, -434]], [0, "942E/T+B5BrpFs8sUavfgl", 1], [5, 141, 48], [-239.5, -52.19999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-438], [[7, -436, 356], [11, 3, -437]], [0, "4en43vX3FEsJRntMvAHp99", 1], [5, 141, 48], [-3.9000000000000057, -52.19999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 7, [-441], [[7, -439, 358], [11, 3, -440]], [0, "6dBOY+jt9C842nuPkflA31", 1], [5, 141, 48], [231.7, -52.19999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn", 8, [-444], [[7, -442, 363], [11, 3, -443]], [0, "d2Oa+BenVM6r5cCKzFcOmU", 1], [5, 141, 48], [-239.50000000000003, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-447], [[7, -445, 365], [11, 3, -446]], [0, "14ZXMmxBFD16jlpzSe6/3G", 1], [5, 141, 48], [-3.900000000000034, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-450], [[7, -448, 367], [11, 3, -449]], [0, "19n/lwFiVGTqjnaVeVc28G", 1], [5, 141, 48], [231.69999999999996, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-453], [[7, -451, 369], [11, 3, -452]], [0, "038SPePJNN/ax9H5HKrLko", 1], [5, 141, 48], [-239.5, 11.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-456], [[7, -454, 371], [11, 3, -455]], [0, "5a3bAWFy5MPJVkqBcPM7g3", 1], [5, 141, 48], [-3.9000000000000057, 11.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-459], [[7, -457, 373], [11, 3, -458]], [0, "4dogjWwl9DzpIGLdqiZcQZ", 1], [5, 141, 48], [231.7, 11.900000000000006, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-462], [[7, -460, 375], [11, 3, -461]], [0, "320BesrIVKoqeBakbiXSaU", 1], [5, 141, 48], [-239.5, -52.19999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-465], [[7, -463, 377], [11, 3, -464]], [0, "89t2pIrh9F2qIZ0LNTiSv8", 1], [5, 141, 48], [-3.9000000000000057, -52.19999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn copy", 8, [-468], [[7, -466, 379], [11, 3, -467]], [0, "77c7ZSZxBPtZ2H28NZZCaB", 1], [5, 141, 48], [231.7, -52.19999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BtnSubmit", 27, [[7, -469, 380], [24, 3, -471, [[5, "bcd88coqOBPF7ZC2zQEsAhZ", "actSubmitCashOut", 10]], -470]], [0, "d6pXjbfURN86HSMypHIr6z", 1], [5, 208, 69], [232, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClear", 27, [[30, 2, false, -472, 381], [24, 3, -474, [[5, "bcd88coqOBPF7ZC2zQEsAhZ", "actClearCashOut", 10]], -473]], [0, "28tJGw5opAlqWga8dNKFEb", 1], [5, 47, 47], [55, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg", 4, [[19, 0, -475, [0], 1], [139, -476]], [0, "1fI43dM0BLUI40qzjLQqo2", 1], [5, 1561, 732]], [29, "nickname", 47, [[-477, [54, -478, [4, 4283979864]]], 1, 4], [0, "15Un630lpEwr1yMVT6wtj1", 1], [5, 61.69, 52.4], [102.4, 18.9, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "toggleMusicMain", 4, [[[10, -479, [6], 7], -480], 4, 1], [0, "dfzD4ox2dOG4NKQB+/0KFy", 1], [5, 76, 78], [268, 296, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [63, "leftPanel", 5, [11], [[118, 32, -38, -481]], [0, "d2mL9YJn1NYIF2l7Rj8F6+", 1], [818.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg", 2, [[19, 0, -482, [8], 9], [83, -483, [5, 1561, 732]]], [0, "eaGsSNOWRC0r9hjW4f4bQK", 1], [5, 1561, 732]], [3, "touchPad", 2, [[23, 45, -484]], [0, "eduuI0EKxNepHr7Vrtz6xS", 1], [5, 1561, 732]], [32, "getstate", false, 2, [29], [[43, 2, -485, [[5, "5fa1efbhuJLl5N2iupithus", "actGetState", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 29, 11, 12, 13, 14]], [0, "d6vVcAGDFHXbd0KTHYZqxm", 1], [5, 100, 40]], [66, "btnEffectJackpotTest", false, 2, [30], [[43, 2, -486, [[5, "5fa1efbhuJLl5N2iupithus", "actEffectJackpotTest", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 30, 16, 17, 18, 19]], [0, "7cQFmAiz1Fa6jm0oDhqi8d", 1], [5, 100, 40], [-142, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "btnEffectMegaWinTest", false, 2, [31], [[43, 2, -487, [[5, "5fa1efbhuJLl5N2iupithus", "actEffectMegaWinTest", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 31, 21, 22, 23, 24]], [0, "6cA0oNsahEg7Sl6EqlIK8i", 1], [5, 100, 40]], [66, "btnEffectBigWinTest", false, 2, [32], [[43, 2, -488, [[5, "5fa1efbhuJLl5N2iupithus", "actEffectBigWinTest", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 32, 26, 27, 28, 29]], [0, "06vc6iqXpA8Y1f+mrzl+vd", 1], [5, 100, 40], [142, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [112, "lblServerTime", false, 2, [[-489, [55, 3, -490, [4, 4278190080]]], 1, 4], [0, "9eQxo9XPFB1IHL2z7B8DAX", 1], [5, 390, 50], [0, 333, 0, 0, 0, 0, 1, 1, 1, 1]], [67, "Bg", 128, 12, [[39, 0, -491, 33], [77, 45, -860, -860, -1140, -1140, 40, 36, -492]], [0, "48vWCApnVGcYfX4AyW78Km", 1], [4, 4278190080], [5, 3000, 3000]], [34, "bullet", 35, [[10, -493, [42], 43]], [0, "afqC1TYmVAuY26d2t0isFt", 1], [5, 61, 129], [0, 2, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [104, "fishNet", false, 35, [[7, -494, 44]], [0, "f4QSfARgdEupzppZNndiCg", 1], [5, 181, 181]], [6, "gunRotate", 13, [37], [0, "9boAhAhxxHWqkXTOytSrYi", 1], [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [6, "gunRotate", 14, [38], [0, "b0KD4YAOtK46dgSs1Ao6QU", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [6, "gunRotate", 15, [39], [0, "64Y1G3ThdAiI3Y43YJn6Rw", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [6, "gunRotate", 9, [40], [0, "9dUxyugExMx7RkdyKBU40X", 1], [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [1, "btnMinus", 9, [[10, -495, [98], 99], [17, 3, -496, [[5, "5fa1efbhuJLl5N2iupithus", "actBetDown", 2]]]], [0, "8dSD34AfNACZxJSP6mV297", 1], [5, 77, 56], [-93.8, 16.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnAdd", 9, [[10, -497, [100], 101], [17, 3, -498, [[5, "5fa1efbhuJLl5N2iupithus", "actBetUp", 2]]]], [0, "09V88l8bJBmrQOj8c4ChFA", 1], [5, 71, 52], [95.7, 16.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "fish0", 3, [-499], [0, "3cWpH4raJDKoF17fHkDpH7", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [6, "fish1", 3, [-500], [0, "d1g6gGv7BJy4xIG37Mvvpv", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish01", 112, [[8, 2, false, -501, [113], 114], [2, true, -502, [116], 115]], [0, "144VuXy2lANa0nNEHqKzEk", 1], [5, 34, 58]], [6, "fish2", 3, [-503], [0, "541qSRRWpD5JRlzP1WbFUx", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish02", 114, [[8, 2, false, -504, [117], 118], [2, true, -505, [120], 119]], [0, "59O9Y96UZMuq7BTTS5Sv7H", 1], [5, 38, 52]], [6, "fish3", 3, [-506], [0, "cdV7wpzNxNNpXMEjDLLhP6", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish03", 116, [[8, 2, false, -507, [121], 122], [2, true, -508, [124], 123]], [0, "13hfEl0x1OW7EAUg9YwLmb", 1], [5, 42, 51]], [6, "fish4", 3, [-509], [0, "e4oH1d7aZF2ZFL+R+L/cPg", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish04", 118, [[8, 2, false, -510, [125], 126], [2, true, -511, [128], 127]], [0, "25/30AzAJPGYhxBVT+THIs", 1], [5, 43, 79]], [6, "fish5", 3, [-512], [0, "51oZK6n/pM36qaJZXF3FuS", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish05", 120, [[8, 2, false, -513, [129], 130], [2, true, -514, [132], 131]], [0, "c5BzEmIERKm51MarsUAEw9", 1], [5, 55, 54]], [6, "fish6", 3, [-515], [0, "d0TYC59qxI16apYvwufZtr", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish06", 122, [[8, 2, false, -516, [133], 134], [2, true, -517, [136], 135]], [0, "6dTivyomBJnpoxrCtGnBVh", 1], [5, 54, 103]], [6, "fish7", 3, [-518], [0, "c9SKkdmhZBu4b1PhKRaAGC", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish07", 124, [[8, 2, false, -519, [137], 138], [2, true, -520, [140], 139]], [0, "7duGrzHltCDrbubKyB4uoQ", 1], [5, 57, 76]], [6, "fish8", 3, [-521], [0, "075jJH2xRNAaKuMy76eQsp", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish08", 126, [[8, 2, false, -522, [141], 142], [2, true, -523, [144], 143]], [0, "e9qgnQVw9POLGzQF3p0rAk", 1], [5, 61, 100]], [6, "fish9", 3, [-524], [0, "8fKghuH6ZJJI6Fd1tqRHB3", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish09", 128, [[8, 2, false, -525, [145], 146], [2, true, -526, [148], 147]], [0, "8bYgWlsrJAA5lpNIKChL0q", 1], [5, 61, 102]], [6, "fish10", 3, [-527], [0, "205ldESEFLWJZA/OJtQYDe", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish10", 130, [[8, 2, false, -528, [149], 150], [2, true, -529, [152], 151]], [0, "61uyHNH9hP07Kk1M9pX1t7", 1], [5, 67, 104]], [6, "fish11", 3, [-530], [0, "00k98sEu5OhI4+qSS5D8G1", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish11", 132, [[8, 2, false, -531, [153], 154], [2, true, -532, [156], 155]], [0, "a0wmvbbo1MwadN5J5IpXLa", 1], [5, 69, 97]], [6, "fish12", 3, [-533], [0, "762ZLBFm1EdpwTJOUT8mEz", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish12", 134, [[8, 2, false, -534, [157], 158], [2, true, -535, [160], 159]], [0, "23lL1IC8ZMDqdi/DeWU73d", 1], [5, 190, 720]], [6, "fish13", 3, [-536], [0, "45HIKoYJhFKbJMJ/LhvTAU", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish13", 136, [[8, 2, false, -537, [161], 162], [2, true, -538, [164], 163]], [0, "d2C7mzTKBIKZCpZR8XRgU8", 1], [5, 79, 49]], [6, "fish14", 3, [-539], [0, "e98NcERuZPAbYEmcDINHhe", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish14", 138, [[8, 2, false, -540, [165], 166], [2, true, -541, [168], 167]], [0, "9cS4uMjOJE/4ywnVcyJOFy", 1], [5, 85, 85]], [6, "fish15", 3, [-542], [0, "3dgSaomelG6ptgVEUL10YC", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish15", 140, [[8, 2, false, -543, [169], 170], [2, true, -544, [172], 171]], [0, "1e0LSi5wVMmLFPJI4TDwGf", 1], [5, 125, 129]], [6, "fish16", 3, [-545], [0, "f9vtuJotJKWpszmq9If0F/", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish16", 142, [[8, 2, false, -546, [173], 174], [2, true, -547, [176], 175]], [0, "ef4gKw9HFFB5va5Yox0J9v", 1], [5, 211, 198]], [6, "fish17", 3, [-548], [0, "81jbqx02VJLqYcDyDTftK3", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish17", 144, [[8, 2, false, -549, [177], 178], [2, true, -550, [180], 179]], [0, "3dkw1wGgNA4J863/z73Djq", 1], [5, 112, 252]], [6, "fish18", 3, [-551], [0, "baDTNcfMVNzZvtdCN737c3", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish18", 146, [[8, 2, false, -552, [181], 182], [2, true, -553, [184], 183]], [0, "d0ssuGkORC+bqpvik9xVIB", 1], [5, 144, 298]], [6, "fish19", 3, [-554], [0, "15XTwotH1Bc6ICLhNcYnXV", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish19", 148, [[8, 2, false, -555, [185], 186], [2, true, -556, [188], 187]], [0, "71one080lPOJLRArY0pHU7", 1], [5, 145, 249]], [6, "fish20", 3, [-557], [0, "9184C0vLNNzILMciEURMq6", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish20", 150, [[8, 2, false, -558, [189], 190], [2, true, -559, [192], 191]], [0, "a29I/hz51OsbeL7rEQVdf4", 1], [5, 119, 228]], [6, "fish21", 3, [-560], [0, "48e+lntElOGokiD0XAEWfO", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish21", 152, [[8, 2, false, -561, [193], 194], [2, true, -562, [196], 195]], [0, "14c6iAritDzIOPRlzkVgGg", 1], [5, 157, 282]], [6, "fish22", 3, [-563], [0, "a7iHTJjERP1I/EG6xul4NM", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish22", 154, [[8, 2, false, -564, [197], 198], [2, true, -565, [200], 199]], [0, "0d/yFg1xpEDY+FlcXdG+2I", 1], [5, 162, 356]], [6, "fish23", 3, [-566], [0, "a5Iahe6kpMG4rn72SIXv7w", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish23", 156, [[8, 2, false, -567, [201], 202], [2, true, -568, [204], 203]], [0, "e55I7GSghF86ultllYH9AH", 1], [5, 215, 430]], [6, "fish24", 3, [-569], [0, "a2WC5h34dO2rlhJ9zuD5Cn", 1], [0, 0, 0, 0, 0, -0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, -90]], [3, "fish24", 158, [[8, 2, false, -570, [205], 206], [2, true, -571, [208], 207]], [0, "bedtgasWpGpaqU9BmLm1q0", 1], [5, 402, 687]], [3, "fish00", 111, [[8, 2, false, -572, [209], 210], [2, true, -573, [212], 211]], [0, "46+4dCxNRDvaXqU980YwYg", 1], [5, 29, 48]], [49, "fishs", 5, [17], [0, "56QkbAXsFFsb1Kk/W4HUVf", 1]], [50, "coin0", 16, [[56, "Idle", 0, false, "Idle", -574, 214]], [0, "374Pk9DgpNq4hvBpxN62N8", 1]], [50, "coin1", 16, [[56, "Idle", 0, false, "Idle", -575, 215]], [0, "43D3/XycxPpKGZA8phX3hb", 1]], [50, "coin2", 16, [[56, "Idle", 0, false, "Idle", -576, 216]], [0, "51uLREVeRNRbT+2zzRTiXe", 1]], [4, "bgJackpot", 5, [-578], [[10, -577, [217], 218]], [0, "3ccFVCF3xGeIDhBNZWg2S3", 1], [5, 299, 105], [0, 275.4, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "lblFastShootTime", 42, [[-579, [55, 3, -580, [4, 4278190080]]], 1, 4], [0, "33hNaoxmNHz7A2ZhUhdrHl", 1], [5, 41.03, 56.4]], [71, "progressFastShoot", 108, 42, [-581, -582], [0, "1caWFCCB1FoZcXPtqdDaYj", 1], [4, 4278190080], [5, 79, 80]], [75, "lblFastTargetFish", 43, [[-583, [55, 3, -584, [4, 4278190080]]], 1, 4], [0, "d6/LAOxw1DwblFy7T/XLyL", 1], [5, 41.03, 56.4]], [71, "progressTargetFish", 108, 43, [-585, -586], [0, "6dFJFhijZKnafXL0IJm6bD", 1], [4, 4278190080], [5, 80, 80]], [3, "target", 5, [[142, 0, false, "Idle", -587, [230], 231]], [0, "570nQ/eCBOaq32N+twiuYp", 1], [5, 115.25, 115.53]], [29, "toggleSound", 19, [[[10, -588, [243], 244], -589], 4, 1], [0, "f10pLUL39EKZx+1S6YBAt8", 1], [5, 70, 70], [-1.3999999999999986, -41.44999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "toggleMusic", 19, [[[10, -590, [241], 242], -591], 4, 1], [0, "7a0zDsHS9KFLKxmQietpzn", 1], [5, 68, 68], [-2.3999999999999986, -130.45, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "toggleSoundMain", 4, [[[10, -592, [245], 246], -593], 4, 1], [0, "7b+wFXh4hI24wG4MTe+QL5", 1], [5, 76, 78], [376, 296, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "wave", 6, [[16, 772, 0, -594, [251], 252], [2, true, -595, [254], 253]], [0, "11PWC+A65J67FGJhm4MRQh", 1], [5, 512, 512], [-774, 512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -596, [255], 256], [2, true, -597, [258], 257]], [0, "88+/IXyDdN3bCN4Uu07MSt", 1], [5, 512, 512], [-262, 512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -598, [259], 260], [2, true, -599, [262], 261]], [0, "3b3j8O1zpIm5Mzva6Ynrwn", 1], [5, 512, 512], [250, 512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -600, [263], 264], [2, true, -601, [266], 265]], [0, "36cvr1HZpENJclgthmXD/A", 1], [5, 512, 512], [762, 512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -602, [267], 268], [2, true, -603, [270], 269]], [0, "d6MzzR0NlHK6sJaMqEzSpE", 1], [5, 512, 512], [-774, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -604, [271], 272], [2, true, -605, [274], 273]], [0, "19Ay/aGZZAaIwT4OOO8Gys", 1], [5, 512, 512], [-262, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -606, [275], 276], [2, true, -607, [278], 277]], [0, "b9hxfO9vxNzYnl5ohw7hO9", 1], [5, 512, 512], [250, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -608, [279], 280], [2, true, -609, [282], 281]], [0, "86a5qk3NNLuq77FFSmCN5+", 1], [5, 512, 512], [762, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -610, [283], 284], [2, true, -611, [286], 285]], [0, "d56hUivhVDZ6hAcrbIWqwE", 1], [5, 512, 512], [-774, -512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -612, [287], 288], [2, true, -613, [290], 289]], [0, "9fmGtSpHVOvb0k1iZeNNdO", 1], [5, 512, 512], [-262, -512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -614, [291], 292], [2, true, -615, [294], 293]], [0, "80SjunCFRLLZigjgiwWJXS", 1], [5, 512, 512], [250, -512, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "wave copy", 6, [[16, 772, 0, -616, [295], 296], [2, true, -617, [298], 297]], [0, "d1wh1FJAlBJotcwU3EG27v", 1], [5, 512, 512], [762, -512, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "top", 46, [[19, 0, -618, [299], 300], [119, 4, 759, 100, -619]], [0, "62H+WJBTpPMY69s1Y1HfPw", 1], [4, 4278190080], [5, 1561, 1500], [0, 1116, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "bottom", 46, [[19, 0, -620, [301], 302], [120, 1, 762, 720.5, 100, -621]], [0, "69HSgkfAtOwp/uldQHO6in", 1], [4, 4278190080], [5, 1561, 1500], [0, -1119, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "right", 46, [[19, 0, -622, [303], 304], [121, 8, 1482.5, 720.5, 100, -623]], [0, "02MVUuY7xD7KLE8MbeZtu2", 1], [4, 4278190080], [5, 629, 2000], [1098, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "left", 46, [[19, 0, -624, [305], 306], [122, 32, 1279.5, 1482.5, 720.5, 100, -625]], [0, "50l9DDEatKWL4kyES0nMkL", 1], [4, 4278190080], [5, 629, 2000], [-1098, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Room1", 4, [-627], [[53, 0.92, 3, -626, [[5, "4378bty4U5E8KWt2uwkMkhd", "actRoom1", 4]]]], [0, "938dL45I1A6KT1PwrEI5VU", 1], [5, 263, 349], [-416, -145, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Room2", 4, [-629], [[53, 0.92, 3, -628, [[5, "4378bty4U5E8KWt2uwkMkhd", "actRoom2", 4]]]], [0, "377bQ70qJA+YMCoz23E13X", 1], [5, 263, 349], [0, -109, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Room3", 4, [-631], [[53, 0.92, 3, -630, [[5, "4378bty4U5E8KWt2uwkMkhd", "actRoom3", 4]]]], [0, "2dgBq0+cxCW5YiaO2KV70a", 1], [5, 263, 349], [416, -145, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "bgJackpot", 20, [-633], [[10, -632, [319], 320]], [0, "18fdCVmkpPvpZUzYpReSIE", 1], [5, 299, 105]], [67, "Bg", 128, 10, [[39, 0, -634, 325], [77, 45, -860, -860, -1140, -1140, 40, 36, -635]], [0, "da3IwxwF1NwJdyQt4ndaEi", 1], [4, 4278190080], [5, 3118, 3066]], [1, "lbl", 23, [[58, "ĐỔI", 26, false, 1, 1, -636, 327], [54, -637, [4, 4278214570]]], [0, "54Yrd2jiBLN51ATcYJ7xdy", 1], [5, 42.9, 26], [-22, 3.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 24, [[58, "ĐỔI", 26, false, 1, 1, -638, 330], [54, -639, [4, 4294145862]]], [0, "52Wx3YoF5JxJEMP57luksY", 1], [5, 42.9, 26], [-22, 3.1, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "background", 66, [[18, 1, 0, -640, 332], [21, 0, 45, 160, 40, -641]], [0, "acqJDAOLZG049nVqHvFsWB", 1], [5, 299, 46]], [76, "lblBalance", 66, [[-642, [78, 0, 45, 51.099999999999994, 20, -8.399999999999999, 8.399999999999999, 158, 40, -643]], 1, 4], [0, "d5Jpy6XI5HH5g2ZG7UWw5g", 1], [4, 4278250495], [5, 227.9, 46], [0, 0, 1], [-98.4, 31.4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "background", 67, [[18, 1, 0, -644, 333], [21, 0, 45, 160, 40, -645]], [0, "57scAgdo9BvJa1uH4zRVa0", 1], [5, 299, 46]], [76, "lblBanlance", 67, [[-646, [78, 0, 45, 51.1, 20, -7.699999999999999, 7.699999999999999, 158, 40, -647]], 1, 4], [0, "94ToyQ/OZIdKeTv02CZXoM", 1], [4, 4278250495], [5, 227.9, 46], [0, 0, 1], [-98.4, 30.7, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "BACKGROUND_SPRITE", 26, [[40, 1, 0, -648], [38, 0, 45, -104, -104, 160, 40, -649]], [0, "f6ZwSJnl5Di5SM+pQNU4ge", 1], [5, 418, 46]], [68, "TEXT_LABEL", false, 26, [[86, 20, false, false, 1, 1, 1, -650, 336], [38, 0, 45, 48, 48, 158, 40, -651]], [0, "90kNyI1dVFtZpNYwawx8A/", 1], [5, 208, 46], [0, 0, 1], [-103, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [69, "PLACEHOLDER_LABEL", 26, [[59, "<PERSON><PERSON><PERSON><PERSON> số tiền...", 20, 46, false, false, 1, 1, 1, -652, 337], [38, 0, 45, -104, -104, 158, 40, -653]], [0, "dd2Us6TZdHVZQp6LTnX+Mt", 1], [4, 4286545791], [5, 418, 46], [0, 0, 1], [-209, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "BACKGROUND_SPRITE", 28, [[40, 1, 0, -654], [21, 0, 45, 160, 40, -655]], [0, "6b02IL7c5GlbJmz2zy62YZ", 1], [5, 210, 46]], [68, "TEXT_LABEL", false, 28, [[86, 20, false, false, 1, 1, 1, -656, 360], [38, 0, 45, 48, 48, 158, 40, -657]], [0, "86S2Ia1EZDypDb37/oPTTw", 1], [5, 208, 46], [0, 0, 1], [-103, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [69, "PLACEHOLDER_LABEL", 28, [[59, "<PERSON><PERSON><PERSON><PERSON> số tiền...", 20, 46, false, false, 1, 1, 1, -658, 361], [123, 0, 45, 2, 158, 40, -659]], [0, "40oEtTQ/xEMYsP7/Z7apQ4", 1], [4, 4286545791], [5, 208, 46], [0, 0, 1], [-103, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgProfile", 47, [[10, -660, [2], 3]], [0, "35FXUu0UVEYosI2FJkV5DY", 1], [5, 354, 140], [45, 22, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [13, "avatar", 47, [-661], [0, "67N7nqWe1HcY/NIFSP7CTO", 1], [5, 66, 66], [-35, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [129, 0, 208, [4]], [31, "B88live", 18, false, 1, 1, 92, [5]], [17, 3, 93, [[5, "3ebbaXYFItGA4MRPRT0exe9", "toggleMusic", 11], [5, "5fa1efbhuJLl5N2iupithus", "playClickSound", 2]]], [35, "Label", 29, [[45, "State", 20, false, 1, 1, 1, -662]], [0, "9cuzhrNkxNmL9l0mekZyL8", 1], [4, 4278190080], [5, 100, 40]], [35, "Label", 30, [[45, "<PERSON><PERSON>", 20, false, 1, 1, 1, -663]], [0, "96HOnsftRKfZysUY/25F/z", 1], [4, 4278190080], [5, 100, 40]], [35, "Label", 31, [[45, "Ef <PERSON>", 20, false, 1, 1, 1, -664]], [0, "1aMgVl4SNOm63isyVhMetd", 1], [4, 4278190080], [5, 100, 40]], [35, "Label", 32, [[45, "<PERSON><PERSON>", 20, false, 1, 1, 1, -665]], [0, "f3ndFBvcBHkoK115iFpc4g", 1], [4, 4278190080], [5, 100, 40]], [145, "Label", 20, 18, 1, 1, 101], [108, "lblPing", 175, 2, [-666], [0, "1a5TfG7KxO4I66BqRK+38E", 1], [4, 4294506744], [5, 115, 50], [-579, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [146, "0ms", 20, 18, false, 1, 1, 1, 217, [30]], [3, "loading", 33, [[147, "<PERSON><PERSON> tả<PERSON>...", false, 1, 1, 1, -667, 31]], [0, "e4Uio2fKlLOascHeeVchnE", 1], [5, 541, 148]], [105, "Title", 34, [[59, "HỆ SỐ CÁ", 28, 50, false, false, 1, 1, 1, -668, 34]], [0, "71/9FlrIhNUIrI3wTBy3Xr", 1], [5, 500, 50], [0, 0.5, 0.5094752742529568], [0, 257, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "lblFactor", 50, [[58, "x0", 18, false, 1, 1, -669, 36]], [0, "e80CCDmfVDKYMn2W5JJCIZ", 1], [4, 4278255595], [5, 20.7, 18], [0, -35, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "bullets", 5, [35], [0, "d2pc2PkmlPFL+cq5LgYN0O", 1]], [152, 9, 108, [109, 110, 111, 112], [-670, -671, -672, -673]], [13, "gunBar", 13, [-674], [0, "affMrkaLJDTYtKcVSzg2KC", 1], [5, 164, 73], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [41, 224, [45]], [9, "gun", 37, [-675], [0, "fbCP6YWtdKOY+vszvPjmK3", 1], [5, 99, 145]], [14, 1, 0, false, 1, 226, [46]], [9, "gun1", 37, [-676], [0, "92mP9O2JBNW5CdvUNGU8a7", 1], [5, 90, 144]], [14, 1, 0, false, 1, 228, [47]], [9, "gun2", 37, [-677], [0, "daoXkvpQNOuZ5bRUmuX6Ti", 1], [5, 102, 149]], [14, 1, 0, false, 1, 230, [48]], [9, "gun3", 37, [-678], [0, "c7DuiNtxFP6ZDbSGttOoOX", 1], [5, 79, 147]], [14, 1, 0, false, 1, 232, [49]], [1, "bar", 13, [[44, 0, false, -679, [50], 51]], [0, "42Rar4GtlNPLDLP5BfK/Gi", 1], [5, 157, 93], [0, -10.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "info", 13, [[10, -680, [52], 53]], [0, "13ALJ9TcNHybulzxokpQwo", 1], [5, 153, 115], [219.5, 65, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "lbServiceId", false, 51, [-681], [0, "27hzB1eE5Cuo/kqYEjMARP", 1], [4, 4282969994], [5, 22.2, 12], [-29.799999999999997, -0.8, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "[TQ]", 12, false, 1, 1, 236], [9, "lblNickname", 51, [-682], [0, "ectVWGr3JNfpT2sn3GDCOs", 1], [5, 57.6, 18]], [31, "<PERSON><PERSON>", 18, false, 1, 1, 238, [54]], [15, "lblCoin", 13, [-683], [0, "19Jslaz55ABaK1FrunbNfH", 1], [4, 4278245119], [5, 227, 50], [233.1, 31.2, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "0", 18, false, 1, 1, 1, 240, [55]], [15, "lblBet", 13, [-684], [0, "19o7rah/FFi6lV9GMmruSP", 1], [4, 4278245119], [5, 100, 30], [0, 15.1, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "0", 14, 50, false, false, 1, 1, 2, 242, [56]], [61, 1, 13, 239, 237, 241, 243, 105, 225, [57, 58, 59, 60], [227, 229, 231, 233]], [72, "gunBar", 14, [-685], [0, "f1VpMah/9BEZRGXAtgjU7P", 1], [5, 164, 73], [0, -46, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, -180]], [41, 245, [61]], [9, "gun", 38, [-686], [0, "f6NGoQOvRIsJeiGyuWwfPa", 1], [5, 99, 145]], [143, 0, false, 1, 247, [62]], [9, "gun1", 38, [-687], [0, "d29AGRgplAoqTZP24kvLLK", 1], [5, 90, 144]], [14, 1, 0, false, 1, 249, [63]], [9, "gun2", 38, [-688], [0, "d54/yL89ZOH7WDef1RmIU9", 1], [5, 102, 149]], [14, 1, 0, false, 1, 251, [64]], [9, "gun3", 38, [-689], [0, "9cqFDqLchAzLwbtqfXnX/e", 1], [5, 79, 147]], [14, 1, 0, false, 1, 253, [65]], [34, "bar", 14, [[44, 0, false, -690, [66], 67]], [0, "3ekT+tk0hPA54rxcEz404v", 1], [5, 157, 93], [0, 9.8, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, -180]], [1, "info", 14, [[10, -691, [68], 69]], [0, "02U6MNRZBJXpTSc6liaS3X", 1], [5, 153, 115], [219.5, -66.4, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "lbServiceId", false, 52, [-692], [0, "93nqpID+BPE4M3ohNmUpcZ", 1], [4, 4282969994], [5, 22.2, 12], [-34.3, -0.8, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "[TQ]", 12, false, 1, 1, 257], [9, "lblNickname", 52, [-693], [0, "7870DOWUFIrZCdz4CVvSLf", 1], [5, 66.6, 18]], [31, "Nickna..", 18, false, 1, 1, 259, [70]], [15, "lblCoin", 14, [-694], [0, "a05QlOHO5FYKnUEzNRpLHt", 1], [4, 4278245119], [5, 227, 50], [233.1, -100.3, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "0", 18, false, 1, 1, 1, 261, [71]], [15, "lblBet", 14, [-695], [0, "37AkAiRwFDM4YUmio9iccv", 1], [4, 4278245119], [5, 100, 30], [0, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "0", 14, 50, false, false, 1, 1, 2, 263, [72]], [61, 2, 14, 260, 258, 262, 264, 106, 246, [73, 74, 75, 76], [248, 250, 252, 254]], [72, "gunBar", 15, [-696], [0, "dezNtP2jRHrbQw3gh+6rf7", 1], [5, 164, 73], [0, -46, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, -180]], [41, 266, [77]], [9, "gun", 39, [-697], [0, "ecjlQSX6FIprAgV6hEdA4m", 1], [5, 99, 145]], [14, 1, 0, false, 1, 268, [78]], [9, "gun1", 39, [-698], [0, "27iWj7mgRGWoutvvkTWKf+", 1], [5, 90, 144]], [14, 1, 0, false, 1, 270, [79]], [9, "gun2", 39, [-699], [0, "a5aPE2SgRF1KmQ2at061Fx", 1], [5, 102, 149]], [14, 1, 0, false, 1, 272, [80]], [9, "gun3", 39, [-700], [0, "7dATT1+TVKmqCOG6w1R6e/", 1], [5, 79, 147]], [14, 1, 0, false, 1, 274, [81]], [34, "bar", 15, [[44, 0, false, -701, [82], 83]], [0, "17PJpacP9G+K+/Kq8E4HOc", 1], [5, 157, 93], [0, 9.8, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, -180]], [1, "info", 15, [[10, -702, [84], 85]], [0, "0b3n5zC/lFWr545tC59B6W", 1], [5, 153, 115], [-219.5, -66.4, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "lbServiceId", false, 53, [-703], [0, "c78IeZAcRHZLwlEYMorkj5", 1], [4, 4282969994], [5, 22.2, 12], [-42.4, -0.8, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "[TQ]", 12, false, 1, 1, 278], [9, "lblNickname", 53, [-704], [0, "7eAGLA+nNDhb7zicPSZ0uI", 1], [5, 82.8, 18]], [31, "Nickname", 18, false, 1, 1, 280, [86]], [15, "lblCoin", 15, [-705], [0, "f0+wpcB99BmIvcphNjfSn0", 1], [4, 4278245119], [5, 227, 50], [-205.4, -100.3, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "0", 18, false, 1, 1, 1, 282, [87]], [15, "lblBet", 15, [-706], [0, "35LB9CeLVHGI+HPZ5+oEHu", 1], [4, 4278245119], [5, 100, 30], [0, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "0", 14, 50, false, false, 1, 1, 2, 284, [88]], [61, 3, 15, 281, 279, 283, 285, 107, 267, [89, 90, 91, 92], [269, 271, 273, 275]], [13, "gunBar", 9, [-707], [0, "491IIQHWlKTqPB6NctvHNB", 1], [5, 164, 73], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [41, 287, [93]], [9, "gun", 40, [-708], [0, "c3doo3kdBGh7s0YE7jxcKz", 1], [5, 99, 145]], [14, 1, 0, false, 1, 289, [94]], [9, "gun1", 40, [-709], [0, "6c3PMun5NGFqkH7pJ0QMKt", 1], [5, 90, 144]], [14, 1, 0, false, 1, 291, [95]], [9, "gun2", 40, [-710], [0, "a2PwdFYPtP0rl9ebTqKDk2", 1], [5, 102, 149]], [14, 1, 0, false, 1, 293, [96]], [9, "gun3", 40, [-711], [0, "f2idszJkdIEKsIronN53RB", 1], [5, 79, 147]], [14, 1, 0, false, 1, 295, [97]], [1, "bar", 9, [[44, 0, false, -712, [102], 103]], [0, "0f0g9pLElM8LJhl/E2SKK0", 1], [5, 157, 93], [0, -10.6, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lblBet", 9, [-713], [0, "0d+e29yrpH+rj/YxmmGLH9", 1], [4, 4278245119], [5, 100, 30], [0, 15.1, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "0", 14, 50, false, false, 1, 1, 2, 298, [104]], [1, "info", 9, [[10, -714, [105], 106]], [0, "00YyLUX11McbbNBXLYHDYC", 1], [5, 153, 115], [-219.5, 65, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "lbServiceId", false, 54, [-715], [0, "0ffkodUKlERYN65FaemSLc", 1], [4, 4282969994], [5, 22.2, 12], [-42.4, -0.8, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "[TQ]", 12, false, 1, 1, 301], [9, "lblNickname", 54, [-716], [0, "4aYT55RwRBbIllD4UbMk/A", 1], [5, 82.8, 18]], [31, "Nickname", 18, false, 1, 1, 303, [107]], [15, "lblCoin", 9, [-717], [0, "0f3SUc4fdMf7whYMf/17Zv", 1], [4, 4278245119], [5, 227, 50], [-202.3, 31.2, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "0", 18, false, 1, 1, 1, 305, [108]], [64, "anim", 17, [0, "94GeHe/3pHxKQWnFJseGW2", 1]], [109, "lblId", false, 17, [-718], [0, "78L6dgsGNIJZLWa38IKlXs", 1], [4, 4278190328], [5, 40, 40]], [148, "ID", 1, 1, 308], [106, "coinEffects", 512, 5, [16], [0, "a6IskXJQFJGZbOk04q6TIR", 1]], [73, "explore", 16, [-719], [0, "11d62r8IhBBadXm5v76QXW", 1]], [144, "Idle", 0, false, false, "Idle", 1, 311], [9, "lblCoin", 16, [-720], [0, "13l//pOBFF5IpZOdcpRSsH", 1], [5, 135.03, 0]], [149, "200", 28, 0, false, -10.799999999999999, 1, 1, 313], [150, "0", 28, false, 1, 1, 1, [219]], [15, "lblJackpot", 165, [315], [0, "40P18vLb5N4IuXbm+GsOHQ", 1], [4, 4278247679], [5, 425, 107], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [138, 3, false, 55], [87, "00:00", 14, 1, 1, 166, [220]], [82, 3, 2, 0.25, 1, 167, [221], [0, 0.5, 0.5]], [89, 2, 167, 319], [17, 3, 42, [[5, "5fa1efbhuJLl5N2iupithus", "actFastShoot", 2]]], [87, "00:00", 14, 1, 1, 168, [224]], [82, 3, 2, 0.25, 1, 169, [225], [0, 0.5, 0.5]], [89, 2, 169, 323], [135, true, 3, 43, [[5, "5fa1efbhuJLl5N2iupithus", "actTargetFish", 2]]], [74, "checked", false, 55, [-721], [0, "0fS8x6yaJJ/IsOfJ7coTLS", 1], [5, 80, 80]], [42, 326], [153, 18, [-722, -723]], [9, "NoHu-JackPot-1", 18, [-724], [0, "b9C5dQL4NDRprMX7jw5Pb3", 1], [5, 872.01, 881.65]], [84, "Idle", 0, false, "Idle", 329], [73, "NoHu-JackPot-2", 18, [-725], [0, "d50HfVReJKzakrMbU9PhVm", 1]], [84, "Idle", 0, false, "Idle", 331], [13, "lblCoin", 18, [-726], [0, "f7FTsHWUVALaAIwx9wOQ+S", 1], [5, 80.63, 150], [0, -187.4, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "0", 60, 80, false, 1, 1, 333], [13, "lbSName", 57, [-727], [0, "62lst1ZN9IIpog7JSFDyFq", 1], [5, 81.25, 50], [-189.375, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "[TQ]", false, 1, 1, 335], [13, "lblNickname", 57, [-728], [0, "d3Fe9K/ZBCp4+JAS8wkIEh", 1], [5, 277.5, 50], [91.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "Nickname", false, 1, 1, 337], [90, 44], [13, "thanglon", 44, [-729], [0, "a8Fmj2bp9Gu5BPsVyPkxCy", 1], [5, 457, 465], [24, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [85, "animation", 0, false, "animation", 1, 340], [15, "lblCoin", 44, [-730], [0, "627vQffr1ACYAteL5rwl3D", 1], [4, 4278250495], [5, 80.63, 150], [0, -187.4, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "0", 60, 80, false, 1, 1, 342], [13, "lbSName copy", 58, [-731], [0, "40OZZouDVBT6gsSEPvmfOz", 1], [5, 115, 50], [-154.155, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "[TQ]", false, 1, 1, 344], [13, "lblNickname", 58, [-732], [0, "e8SnmrnoZEaLsxq+Ag3xCd", 1], [5, 298.31, 55.5], [62.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "Nickname", 44.39999999999989, false, 1, 1, 346], [90, 45], [13, "thanglon", 45, [-733], [0, "08YdEUKFRJaYxxpuHmXx/d", 1], [5, 457, 465], [24, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [85, "animation", 0, false, "animation", 1, 349], [15, "lblCoin", 45, [-734], [0, "ablGbQ1FdH6oAo97EXh8l8", 1], [4, 4278250495], [5, 80.63, 150], [0, -187.4, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "0", 60, 80, false, 1, 1, 351], [13, "lbSName copy", 59, [-735], [0, "52PO1ylxtEY4b7k6td9n/g", 1], [5, 81.25, 50], [-159.01, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "[TQ]", false, 1, 1, 353], [13, "lblNickname", 59, [-736], [0, "3bWKx4CQJOBY7LYTZrWXtZ", 1], [5, 308.02, 55.5], [45.625, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "Nickname", 44.39999999999989, false, 1, 1, 355], [154, 11, 60, 211], [17, 3, 171, [[5, "3ebbaXYFItGA4MRPRT0exe9", "toggleSound", 11], [5, "5fa1efbhuJLl5N2iupithus", "playClickSound", 2]]], [17, 3, 172, [[5, "3ebbaXYFItGA4MRPRT0exe9", "toggleMusic", 11], [5, "5fa1efbhuJLl5N2iupithus", "playClickSound", 2]]], [17, 3, 173, [[5, "3ebbaXYFItGA4MRPRT0exe9", "toggleSound", 11], [5, "5fa1efbhuJLl5N2iupithus", "playClickSound", 2]]], [1, "phong1", 190, [[57, "animation", 0, false, "animation", -737, [309], 310]], [0, "99YCqSv8FKkqh20dIS5Hy5", 1], [5, 359, 399], [0, -88, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "phong2", 191, [[57, "animation", 0, false, "animation", -738, [311], 312]], [0, "feec1K3mhD/ZSC/vDmaBlA", 1], [5, 391, 372.16], [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "phong3", 192, [[57, "animation", 0, false, "animation", -739, [313], 314]], [0, "e7/+SKxWFG8auG7k+NlWFq", 1], [5, 414.57, 396], [0, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lblBalance", 193, [-740], [0, "a2gCasCu1HsI31qnDXBoAY", 1], [4, 4278247679], [5, 200, 50.4], [-13.7, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [151, "0", 28, false, false, 1, 1, 2, 364, [318]], [1, "iconCoin", 20, [[42, -741]], [0, "9bdsbv8dpGJaW7CuRcJ+eg", 1], [5, 35, 35], [113.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnAddCoin", 20, [[19, 0, -742, [321], 322]], [0, "6efFCBjghPzrcFTjbyZ0//", 1], [5, 55.4, 51.9], [113.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "coin_2", 20, [[19, 0, -743, [323], 324]], [0, "94L5JG8QlM9olhrlW8Rss2", 1], [5, 45, 46], [-138, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "tabActive", 23, [-744], [0, "7cKZN9KY5BHLW8B5ClPwYQ", 1], [5, 123, 58]], [42, 369], [1, "coin_2", 23, [[30, 0, false, -745, 328]], [0, "afT48xjZlE87rKQvjU36my", 1], [5, 40, 40], [31, 2.3, 0, 0, 0, 0, 1, 1, 1, 1]], [74, "tabActive", false, 24, [-746], [0, "78OB+f4GhL6YJg2DJ9lEY9", 1], [5, 123, 58]], [42, 372], [13, "coin_1", 24, [-747], [0, "32+7ZaM0BK/4CTnMpC3FT0", 1], [5, 40, 40], [31, 2.3, 0, 0, 0, 0, 1, 1, 1, 1]], [40, 1, 0, 374], [88, "0", 28, 25, false, false, 1, 1, 1, 198], [13, "coin_1", 66, [-748], [0, "70ufbhKIJKEbebNXoUVuI9", 1], [5, 40, 40], [-128.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, 1, 0, 377], [88, "0", 28, 25, false, false, 1, 1, 1, 200], [1, "coin_2", 67, [[39, 0, -749, 334]], [0, "74z0TbX+pDyYCX3t48xMze", 1], [5, 40, 40], [-128.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [155, 22], [1, "background copy", 25, [[18, 1, 0, -750, 335]], [0, "c3jAHiHl9DQ5XXiZNi87co", 1], [5, 299, 46], [-72.3, -122.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "coin_2", 26, [[39, 0, -751, 338]], [0, "84IzG4XCVEm74cKdB/5CjQ", 1], [5, 40, 40], [-130.4, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [91, null, 0, 20, 40, "<PERSON><PERSON><PERSON><PERSON> số tiền...", 20, 20, 6, 26, [4, 4294967295], [4, 4286545791]], [1, "lbl", 71, [[12, "0", 22, 18, false, 1, 1, 1, -752, 341]], [0, "02HwRKXHVD+7hoUm6aWrdf", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 72, [[12, "0", 22, 18, false, 1, 1, 1, -753, 343]], [0, "12GbLZyNFCr7J5CCl0bJiZ", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 73, [[12, "0", 22, 18, false, 1, 1, 1, -754, 345]], [0, "1bvE/jB49Mc7DCJdIjm96p", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 74, [[12, "0", 22, 18, false, 1, 1, 1, -755, 347]], [0, "c92md/wwNDSofH+0k8wqQA", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 75, [[12, "0", 22, 18, false, 1, 1, 1, -756, 349]], [0, "79lr/s9ORO8o7P6kzg3OIg", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 76, [[12, "0", 22, 18, false, 1, 1, 1, -757, 351]], [0, "3d4czPmpBO5bj7BU5qBmwr", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 77, [[12, "0", 22, 18, false, 1, 1, 1, -758, 353]], [0, "843cXJDRdMeq+zbK2OoAVJ", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 78, [[12, "0", 22, 18, false, 1, 1, 1, -759, 355]], [0, "21m4UvTapLO7nORJBOV5kC", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 79, [[12, "0", 22, 18, false, 1, 1, 1, -760, 357]], [0, "90IOvovm1FDZDayAz3AqUj", 1], [5, 138.4, 47.9], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "background copy", 27, [[18, 1, 0, -761, 359]], [0, "2cfNgwT5dFlpm9xpLfbYWo", 1], [5, 299, 46], [-72.3, -122.3, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "coin_1", 28, [-762], [0, "d21Nt3LhVIi587xMMxwEos", 1], [5, 40, 40], [-130.4, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [130, 0, 395], [91, null, 0, 20, 40, "<PERSON><PERSON><PERSON><PERSON> số tiền...", 20, 20, 6, 28, [4, 4294967295], [4, 4286545791]], [1, "lbl", 80, [[12, "0", 22, 18, false, 1, 1, 1, -763, 362]], [0, "e1cgt+AUNJXZCIAUCHZHKl", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 81, [[12, "0", 22, 18, false, 1, 1, 1, -764, 364]], [0, "773QApCGFIaLIu3g+WE0Hu", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 82, [[12, "0", 22, 18, false, 1, 1, 1, -765, 366]], [0, "d8dWqzEs5Ms4M5jUk3EJ/F", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 83, [[12, "0", 22, 18, false, 1, 1, 1, -766, 368]], [0, "a8CIbfKMRAOIRaj/4KADIE", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 84, [[12, "0", 22, 18, false, 1, 1, 1, -767, 370]], [0, "d9hA7E+JJHvq8IGnBKxMC0", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 85, [[12, "0", 22, 18, false, 1, 1, 1, -768, 372]], [0, "0ccwBB5ulPnoXQ06dXX7x9", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 86, [[12, "0", 22, 18, false, 1, 1, 1, -769, 374]], [0, "76DOzIi3VBwZJs7urS04zJ", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 87, [[12, "0", 22, 18, false, 1, 1, 1, -770, 376]], [0, "62aKJpavJAw61OgSsTmzes", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbl", 88, [[12, "0", 22, 18, false, 1, 1, 1, -771, 378]], [0, "24sT0/osZEoZ9BsGHgWW70", 1], [5, 138.4, 47.9], [0, 7.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 21, [[7, -772, 382]], [0, "736ibtQlVNequMUcIDGdgK", 1], [5, 146, 41], [0, 218, 0, 0, 0, 0, 1, 1, 1, 1]], [156, 10, 381, 68, [157, 376, 384, 7], [158, 379, 397, 8]]], 0, [0, 18, 1, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, 19, 216, 0, 20, 218, 0, 21, 12, 0, 22, 357, 0, 23, 348, 0, 24, 339, 0, 25, 328, 0, 26, 322, 0, 27, 324, 0, 28, 325, 0, 29, 318, 0, 30, 320, 0, 31, 321, 0, 32, 56, 0, 33, 170, 0, 34, 317, 0, 35, 315, 0, 36, 16, 0, 37, 17, 0, 38, 161, 0, -1, 111, 0, -2, 112, 0, -3, 114, 0, -4, 116, 0, -5, 118, 0, -6, 120, 0, -7, 122, 0, -8, 124, 0, -9, 126, 0, -10, 128, 0, -11, 130, 0, -12, 132, 0, -13, 134, 0, -14, 136, 0, -15, 138, 0, -16, 140, 0, -17, 142, 0, -18, 144, 0, -19, 146, 0, -20, 148, 0, -21, 150, 0, -22, 152, 0, -23, 154, 0, -24, 156, 0, -25, 158, 0, -1, 223, 0, -2, 244, 0, -3, 265, 0, -4, 286, 0, 39, 35, 0, 40, 96, 0, 41, 33, 0, 42, 4, 0, 0, 2, 0, -1, 95, 0, -2, 96, 0, -3, 5, 0, -4, 97, 0, -5, 98, 0, -6, 99, 0, -7, 100, 0, -8, 101, 0, -9, 217, 0, -10, 33, 0, -11, 12, 0, -1, 111, 0, -2, 112, 0, -3, 114, 0, -4, 116, 0, -5, 118, 0, -6, 120, 0, -7, 122, 0, -8, 124, 0, -9, 126, 0, -10, 128, 0, -11, 130, 0, -12, 132, 0, -13, 134, 0, -14, 136, 0, -15, 138, 0, -16, 140, 0, -17, 142, 0, -18, 144, 0, -19, 146, 0, -20, 148, 0, -21, 150, 0, -22, 152, 0, -23, 154, 0, -24, 156, 0, -25, 158, 0, 0, 4, 0, 43, 209, 0, 44, 210, 0, 45, 408, 0, 46, 365, 0, 0, 4, 0, -1, 396, 0, -2, 378, 0, -3, 375, 0, 0, 4, 0, -1, 91, 0, -2, 47, 0, -3, 93, 0, -4, 173, 0, -5, 190, 0, -6, 191, 0, -7, 192, 0, -8, 63, 0, -9, 64, 0, -10, 20, 0, -11, 10, 0, -2, 161, 0, -3, 222, 0, -4, 6, 0, -5, 56, 0, -6, 170, 0, -7, 36, 0, -8, 310, 0, -9, 165, 0, -10, 18, 0, -11, 44, 0, -12, 45, 0, -13, 94, 0, -14, 41, 0, -15, 46, 0, 0, 6, 0, -1, 174, 0, -2, 175, 0, -3, 176, 0, -4, 177, 0, -5, 178, 0, -6, 179, 0, -7, 180, 0, -8, 181, 0, -9, 182, 0, -10, 183, 0, -11, 184, 0, -12, 185, 0, 0, 7, 0, -1, 71, 0, -2, 72, 0, -3, 73, 0, -4, 74, 0, -5, 75, 0, -6, 76, 0, -7, 77, 0, -8, 78, 0, -9, 79, 0, 0, 8, 0, -1, 80, 0, -2, 81, 0, -3, 82, 0, -4, 83, 0, -5, 84, 0, -6, 85, 0, -7, 86, 0, -8, 87, 0, -9, 88, 0, -1, 223, 0, -1, 287, 0, -2, 108, 0, -3, 109, 0, -4, 110, 0, -5, 297, 0, -6, 298, 0, -7, 300, 0, -8, 54, 0, -9, 305, 0, 0, 10, 0, -2, 408, 0, -1, 194, 0, -2, 21, 0, 0, 11, 0, -2, 357, 0, -1, 60, 0, -2, 19, 0, 0, 12, 0, 0, 12, 0, 47, 50, 0, 48, 49, 0, 0, 12, 0, -1, 102, 0, -2, 34, 0, -1, 244, 0, -1, 224, 0, -2, 105, 0, -3, 234, 0, -4, 235, 0, -5, 51, 0, -6, 240, 0, -7, 242, 0, -1, 265, 0, -1, 245, 0, -2, 106, 0, -3, 255, 0, -4, 256, 0, -5, 52, 0, -6, 261, 0, -7, 263, 0, -1, 286, 0, -1, 266, 0, -2, 107, 0, -3, 276, 0, -4, 277, 0, -5, 53, 0, -6, 282, 0, -7, 284, 0, 49, 164, 0, 50, 163, 0, 51, 162, 0, 52, 312, 0, 8, 314, 0, 0, 16, 0, -1, 311, 0, -2, 162, 0, -3, 163, 0, -4, 164, 0, -5, 313, 0, 53, 309, 0, 54, 307, 0, 0, 17, 0, 0, 17, 0, 0, 17, 0, -1, 307, 0, -2, 308, 0, -1, 328, 0, -1, 329, 0, -2, 331, 0, -3, 333, 0, -4, 57, 0, 0, 19, 0, -1, 61, 0, -2, 62, 0, -3, 171, 0, -4, 172, 0, 0, 20, 0, -1, 193, 0, -2, 366, 0, -3, 367, 0, -4, 368, 0, 0, 21, 0, -1, 65, 0, -2, 22, 0, -3, 68, 0, -4, 407, 0, -1, 381, 0, -1, 23, 0, -2, 24, 0, -3, 66, 0, -4, 67, 0, 0, 23, 0, 15, 370, 0, 0, 23, 0, -1, 369, 0, -2, 195, 0, -3, 371, 0, 0, 24, 0, 15, 373, 0, 0, 24, 0, -1, 372, 0, -2, 196, 0, -3, 374, 0, -1, 382, 0, -2, 26, 0, -3, 69, 0, -4, 70, 0, -1, 384, 0, -1, 201, 0, -2, 202, 0, -3, 203, 0, -4, 383, 0, -1, 394, 0, -2, 28, 0, -4, 89, 0, -5, 90, 0, -1, 397, 0, -1, 204, 0, -2, 205, 0, -3, 206, 0, -4, 395, 0, 0, 29, 0, 0, 29, 0, -1, 212, 0, 0, 30, 0, 0, 30, 0, -1, 213, 0, 0, 31, 0, 0, 31, 0, -1, 214, 0, 0, 32, 0, 0, 32, 0, -1, 215, 0, 0, 33, 0, 0, 33, 0, -1, 219, 0, 0, 34, 0, -1, 220, 0, -2, 48, 0, -3, 49, 0, 55, 104, 0, 56, 103, 0, 0, 35, 0, -1, 103, 0, -2, 104, 0, -1, 226, 0, -2, 228, 0, -3, 230, 0, -4, 232, 0, -1, 247, 0, -2, 249, 0, -3, 251, 0, -4, 253, 0, -1, 268, 0, -2, 270, 0, -3, 272, 0, -4, 274, 0, -1, 289, 0, -2, 291, 0, -3, 293, 0, -4, 295, 0, 0, 41, 0, -1, 55, 0, -2, 42, 0, -3, 43, 0, 0, 42, 0, -2, 321, 0, -1, 166, 0, -2, 167, 0, 0, 43, 0, -2, 325, 0, -1, 168, 0, -2, 169, 0, -1, 339, 0, -1, 340, 0, -2, 342, 0, -3, 58, 0, -1, 348, 0, -1, 349, 0, -2, 351, 0, -3, 59, 0, -1, 186, 0, -2, 187, 0, -3, 188, 0, -4, 189, 0, -1, 207, 0, -2, 208, 0, -3, 92, 0, 0, 48, 0, 7, 48, 0, 0, 48, 0, 0, 49, 0, -1, 50, 0, 6, 50, 0, -2, 221, 0, 0, 51, 0, -1, 236, 0, -2, 238, 0, 0, 52, 0, -1, 257, 0, -2, 259, 0, 0, 53, 0, -1, 278, 0, -2, 280, 0, 0, 54, 0, -1, 301, 0, -2, 303, 0, 0, 55, 0, -2, 317, 0, -1, 326, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, -1, 335, 0, -2, 337, 0, 0, 58, 0, -1, 344, 0, -2, 346, 0, 0, 59, 0, -1, 353, 0, -2, 355, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 7, 65, 0, 0, 65, 0, -1, 197, 0, -2, 198, 0, -3, 377, 0, -1, 199, 0, -2, 200, 0, -3, 380, 0, 0, 69, 0, 7, 69, 0, 0, 69, 0, 0, 70, 0, 7, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, -1, 385, 0, 0, 72, 0, 0, 72, 0, -1, 386, 0, 0, 73, 0, 0, 73, 0, -1, 387, 0, 0, 74, 0, 0, 74, 0, -1, 388, 0, 0, 75, 0, 0, 75, 0, -1, 389, 0, 0, 76, 0, 0, 76, 0, -1, 390, 0, 0, 77, 0, 0, 77, 0, -1, 391, 0, 0, 78, 0, 0, 78, 0, -1, 392, 0, 0, 79, 0, 0, 79, 0, -1, 393, 0, 0, 80, 0, 0, 80, 0, -1, 398, 0, 0, 81, 0, 0, 81, 0, -1, 399, 0, 0, 82, 0, 0, 82, 0, -1, 400, 0, 0, 83, 0, 0, 83, 0, -1, 401, 0, 0, 84, 0, 0, 84, 0, -1, 402, 0, 0, 85, 0, 0, 85, 0, -1, 403, 0, 0, 86, 0, 0, 86, 0, -1, 404, 0, 0, 87, 0, 0, 87, 0, -1, 405, 0, 0, 88, 0, 0, 88, 0, -1, 406, 0, 0, 89, 0, 7, 89, 0, 0, 89, 0, 0, 90, 0, 7, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, -1, 210, 0, 0, 92, 0, 0, 93, 0, -2, 211, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, -1, 216, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 109, 0, 0, 109, 0, 0, 110, 0, 0, 110, 0, -1, 160, 0, -1, 113, 0, 0, 113, 0, 0, 113, 0, -1, 115, 0, 0, 115, 0, 0, 115, 0, -1, 117, 0, 0, 117, 0, 0, 117, 0, -1, 119, 0, 0, 119, 0, 0, 119, 0, -1, 121, 0, 0, 121, 0, 0, 121, 0, -1, 123, 0, 0, 123, 0, 0, 123, 0, -1, 125, 0, 0, 125, 0, 0, 125, 0, -1, 127, 0, 0, 127, 0, 0, 127, 0, -1, 129, 0, 0, 129, 0, 0, 129, 0, -1, 131, 0, 0, 131, 0, 0, 131, 0, -1, 133, 0, 0, 133, 0, 0, 133, 0, -1, 135, 0, 0, 135, 0, 0, 135, 0, -1, 137, 0, 0, 137, 0, 0, 137, 0, -1, 139, 0, 0, 139, 0, 0, 139, 0, -1, 141, 0, 0, 141, 0, 0, 141, 0, -1, 143, 0, 0, 143, 0, 0, 143, 0, -1, 145, 0, 0, 145, 0, 0, 145, 0, -1, 147, 0, 0, 147, 0, 0, 147, 0, -1, 149, 0, 0, 149, 0, 0, 149, 0, -1, 151, 0, 0, 151, 0, 0, 151, 0, -1, 153, 0, 0, 153, 0, 0, 153, 0, -1, 155, 0, 0, 155, 0, 0, 155, 0, -1, 157, 0, 0, 157, 0, 0, 157, 0, -1, 159, 0, 0, 159, 0, 0, 159, 0, 0, 160, 0, 0, 160, 0, 0, 162, 0, 0, 163, 0, 0, 164, 0, 0, 165, 0, -1, 316, 0, -1, 318, 0, 0, 166, 0, -1, 319, 0, -2, 320, 0, -1, 322, 0, 0, 168, 0, -1, 323, 0, -2, 324, 0, 0, 170, 0, 0, 171, 0, -2, 358, 0, 0, 172, 0, -2, 359, 0, 0, 173, 0, -2, 360, 0, 0, 174, 0, 0, 174, 0, 0, 175, 0, 0, 175, 0, 0, 176, 0, 0, 176, 0, 0, 177, 0, 0, 177, 0, 0, 178, 0, 0, 178, 0, 0, 179, 0, 0, 179, 0, 0, 180, 0, 0, 180, 0, 0, 181, 0, 0, 181, 0, 0, 182, 0, 0, 182, 0, 0, 183, 0, 0, 183, 0, 0, 184, 0, 0, 184, 0, 0, 185, 0, 0, 185, 0, 0, 186, 0, 0, 186, 0, 0, 187, 0, 0, 187, 0, 0, 188, 0, 0, 188, 0, 0, 189, 0, 0, 189, 0, 0, 190, 0, -1, 361, 0, 0, 191, 0, -1, 362, 0, 0, 192, 0, -1, 363, 0, 0, 193, 0, -1, 364, 0, 0, 194, 0, 0, 194, 0, 0, 195, 0, 0, 195, 0, 0, 196, 0, 0, 196, 0, 0, 197, 0, 0, 197, 0, -1, 376, 0, 0, 198, 0, 0, 199, 0, 0, 199, 0, -1, 379, 0, 0, 200, 0, 0, 201, 0, 0, 201, 0, 0, 202, 0, 0, 202, 0, 0, 203, 0, 0, 203, 0, 0, 204, 0, 0, 204, 0, 0, 205, 0, 0, 205, 0, 0, 206, 0, 0, 206, 0, 0, 207, 0, -1, 209, 0, 0, 212, 0, 0, 213, 0, 0, 214, 0, 0, 215, 0, -1, 218, 0, 0, 219, 0, 0, 220, 0, 0, 221, 0, -1, 290, 0, -2, 292, 0, -3, 294, 0, -4, 296, 0, -1, 225, 0, -1, 227, 0, -1, 229, 0, -1, 231, 0, -1, 233, 0, 0, 234, 0, 0, 235, 0, -1, 237, 0, -1, 239, 0, -1, 241, 0, -1, 243, 0, -1, 246, 0, -1, 248, 0, -1, 250, 0, -1, 252, 0, -1, 254, 0, 0, 255, 0, 0, 256, 0, -1, 258, 0, -1, 260, 0, -1, 262, 0, -1, 264, 0, -1, 267, 0, -1, 269, 0, -1, 271, 0, -1, 273, 0, -1, 275, 0, 0, 276, 0, 0, 277, 0, -1, 279, 0, -1, 281, 0, -1, 283, 0, -1, 285, 0, -1, 288, 0, -1, 290, 0, -1, 292, 0, -1, 294, 0, -1, 296, 0, 0, 297, 0, -1, 299, 0, 0, 300, 0, -1, 302, 0, -1, 304, 0, -1, 306, 0, -1, 309, 0, -1, 312, 0, -1, 314, 0, -1, 327, 0, -1, 330, 0, -2, 332, 0, -1, 330, 0, -1, 332, 0, -1, 334, 0, -1, 336, 0, -1, 338, 0, -1, 341, 0, -1, 343, 0, -1, 345, 0, -1, 347, 0, -1, 350, 0, -1, 352, 0, -1, 354, 0, -1, 356, 0, 0, 361, 0, 0, 362, 0, 0, 363, 0, -1, 365, 0, 0, 366, 0, 0, 367, 0, 0, 368, 0, -1, 370, 0, 0, 371, 0, -1, 373, 0, -1, 375, 0, -1, 378, 0, 0, 380, 0, 0, 382, 0, 0, 383, 0, 0, 385, 0, 0, 386, 0, 0, 387, 0, 0, 388, 0, 0, 389, 0, 0, 390, 0, 0, 391, 0, 0, 392, 0, 0, 393, 0, 0, 394, 0, -1, 396, 0, 0, 398, 0, 0, 399, 0, 0, 400, 0, 0, 401, 0, 0, 402, 0, 0, 403, 0, 0, 404, 0, 0, 405, 0, 0, 406, 0, 0, 407, 0, 57, 1, 3, 6, 5, 7, 6, 25, 8, 6, 27, 9, 6, 36, 11, 6, 94, 13, 6, 36, 14, 6, 36, 15, 6, 36, 16, 6, 310, 17, 6, 161, 25, 6, 68, 27, 6, 68, 29, 6, 97, 30, 6, 98, 31, 6, 99, 32, 6, 100, 35, 6, 222, 37, 6, 105, 38, 6, 106, 39, 6, 107, 40, 6, 108, 223, 58, 288, 223, 59, 299, 223, 8, 306, 223, 9, 302, 223, 10, 304, 315, 0, 316, 317, 15, 327, 328, 8, 334, 328, 9, 336, 328, 10, 338, 339, 8, 343, 339, 9, 345, 339, 10, 347, 339, 16, 341, 348, 8, 352, 348, 9, 354, 348, 10, 356, 348, 16, 350, 357, 60, 360, 357, 61, 359, 357, 62, 358, 772], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 209, 210, 218, 225, 227, 229, 231, 233, 237, 239, 241, 243, 246, 248, 250, 252, 254, 258, 260, 262, 264, 267, 269, 271, 273, 275, 279, 281, 283, 285, 288, 290, 292, 294, 296, 299, 302, 304, 306, 312, 314, 315, 319, 323, 327, 330, 332, 334, 336, 338, 341, 343, 345, 347, 350, 352, 354, 356, 357, 357, 357, 357, 357, 357, 357, 357, 365, 370, 373, 375, 376, 378, 379, 396], [-1, 1, -1, 1, -1, -1, -1, 1, -1, 1, 1, 11, 12, 13, 14, 1, 11, 12, 13, 14, 1, 11, 12, 13, 14, 1, 11, 12, 13, 14, -1, 2, 1, 1, 2, 1, 2, 1, -1, -2, -3, -4, -1, 1, 1, -1, -1, -1, -1, -1, -1, 5, -1, 1, -1, -1, -1, -1, -2, -3, -4, -1, -1, -1, -1, -1, -1, 5, -1, 1, -1, -1, -1, -1, -2, -3, -4, -1, -1, -1, -1, -1, -1, 5, -1, 1, -1, -1, -1, -1, -2, -3, -4, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 5, -1, -1, 1, -1, -1, -1, -2, -3, -4, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, 1, 5, 5, 5, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 5, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 17, 63, 64, 65, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, 4, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 5, -1, 5, -1, 5, -1, 1, 1, -1, -1, 1, -1, 1, -1, 1, 1, 1, 2, 1, 1, 2, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 1, 2, 2, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 1, 1, 1, 1, 66, 17, -1, -2, -3, 1, 2, 2, 1, 5, 5, 5, 5, 2, 2, 2, 2, 1, 5, 5, 5, 5, 2, 2, 2, 2, 1, 5, 5, 5, 5, 2, 2, 2, 2, 1, 5, 5, 5, 5, 2, 2, 2, 2, 5, 2, 2, 1, 1, 1, 5, 5, 2, 2, 2, 5, 2, 2, 2, 5, 2, 2, 2, 67, 68, 69, 70, 71, 72, 73, 74, 2, 1, 1, 1, 2, 1, 2, 1], [0, 81, 0, 82, 0, 0, 0, 26, 0, 27, 5, 5, 11, 5, 12, 5, 5, 11, 5, 12, 5, 5, 11, 5, 12, 5, 5, 11, 5, 12, 0, 1, 27, 9, 1, 83, 1, 84, 28, 85, 86, 87, 0, 28, 88, 0, 2, 2, 2, 2, 2, 13, 0, 14, 0, 0, 0, 8, 15, 16, 17, 0, 2, 2, 2, 2, 2, 13, 0, 14, 0, 0, 0, 8, 15, 16, 17, 0, 2, 2, 2, 2, 2, 13, 0, 14, 0, 0, 0, 8, 15, 16, 17, 0, 2, 2, 2, 2, 0, 89, 0, 90, 2, 13, 0, 0, 14, 0, 0, 8, 15, 16, 17, 0, 91, 29, 29, 0, 92, 30, 30, 0, 93, 31, 31, 0, 94, 32, 32, 0, 95, 33, 33, 0, 96, 34, 34, 0, 97, 35, 35, 0, 98, 36, 36, 0, 99, 37, 37, 0, 100, 38, 38, 0, 101, 39, 39, 0, 102, 40, 40, 0, 103, 41, 41, 0, 104, 42, 42, 0, 105, 43, 43, 0, 106, 44, 44, 0, 107, 45, 45, 0, 108, 46, 46, 0, 109, 47, 47, 0, 110, 48, 48, 0, 111, 49, 49, 0, 112, 50, 50, 0, 113, 51, 51, 0, 114, 52, 52, 0, 115, 53, 53, 9, 23, 23, 23, 0, 54, 0, 0, 0, 0, 55, 0, 0, 0, 56, 0, 116, 2, 117, 0, 118, 119, 0, 120, 0, 121, 0, 122, 0, 57, 0, 58, 0, 59, 60, 123, 124, 125, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 6, 3, 3, 0, 9, 0, 9, 0, 9, 0, 9, 0, 126, 2, 127, 2, 128, 2, 129, 0, 130, 131, 0, 0, 54, 0, 132, 0, 133, 9, 134, 1, 24, 61, 1, 61, 18, 18, 24, 18, 1, 1, 24, 62, 63, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 18, 1, 1, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 62, 63, 135, 136, 137, 60, 10, 10, 10, 138, 139, 140, 8, 19, 20, 21, 22, 1, 1, 1, 7, 8, 19, 20, 21, 22, 1, 1, 1, 7, 8, 19, 20, 21, 22, 1, 1, 1, 7, 8, 19, 20, 21, 22, 7, 1, 1, 1, 141, 142, 1, 55, 56, 143, 144, 145, 25, 7, 7, 146, 25, 7, 7, 147, 25, 7, 7, 58, 148, 57, 149, 59, 150, 26, 151, 1, 64, 64, 10, 1, 10, 1, 10]], [[{"name": "btn_1_active", "rect": [0, 0, 123, 58], "offset": [0, 0], "originalSize": [123, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [152]], [[{"name": "panelMenu", "rect": [2, 2, 150, 407], "offset": [0, 0], "originalSize": [154, 411], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [153]], [[{"name": "toggleSoundOn", "rect": [0, 0, 70, 70], "offset": [-0.5, 0.5], "originalSize": [71, 71], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [154]], [[{"name": "btnClose", "rect": [2, 2, 70, 71], "offset": [0, -0.5], "originalSize": [74, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [155]], [[{"name": "dan3", "rect": [0, 0, 57, 150], "offset": [0, 0], "originalSize": [57, 150], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [156]], [[{"name": "btnTarget", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [157]], [[{"name": "music-off-icon", "rect": [0, 0, 76, 78], "offset": [0, 0], "originalSize": [76, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [158]], [[{"name": "btnAddCoin", "rect": [0, 0, 74, 75], "offset": [0, 0], "originalSize": [74, 75], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [159]], [[{"name": "bg", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [160]], [[{"name": "btnFast", "rect": [1, 0, 79, 80], "offset": [0.5, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [161]], [[{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [162]], [[{"name": "rank-bg", "rect": [0, 0, 354, 140], "offset": [0, 0], "originalSize": [354, 140], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [163]], [[{"name": "toggleAutoOn", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [164]], [[{"name": "btn_1_inactive", "rect": [0, 0, 122, 58], "offset": [0, 0], "originalSize": [122, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [165]], [[{"name": "toggleSoundOff", "rect": [1, 1, 68, 68], "offset": [-0.5, 0.5], "originalSize": [71, 71], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [166]], [[[26, "coin_eat", ".mp3", 2.194286], -1], 0, 0, [], [], []], [[{"name": "ui_sung2", "rect": [10, 3, 162, 75], "offset": [-0.5, -1.5], "originalSize": [183, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [167]], [[{"name": "btn_close", "rect": [0, 0, 61, 61], "offset": [0, 0], "originalSize": [61, 61], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [168]], [[{"name": "round_01", "rect": [0, 0, 299, 46], "offset": [0, 0], "originalSize": [299, 46], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [169]], [[{"name": "dan4", "rect": [0, 0, 37, 131], "offset": [0, 0], "originalSize": [37, 131], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [170]], [[{"name": "fishNet", "rect": [0, 0, 181, 181], "offset": [0, 0], "originalSize": [181, 181], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [171]], [[{"name": "sound-off-icon", "rect": [0, 0, 76, 78], "offset": [0, 0], "originalSize": [76, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [172]], [[{"name": "close", "rect": [0, 0, 47, 47], "offset": [0, 0], "originalSize": [47, 47], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [173]], [[[26, "ingame_music", ".mp3", 64.116], -1], 0, 0, [], [], []], [[{"name": "ui_sung4", "rect": [0, 4, 183, 74], "offset": [0, -2], "originalSize": [183, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [174]], [[{"name": "btn_xacnhan", "rect": [0, 0, 208, 69], "offset": [0, 0], "originalSize": [208, 69], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [175]], [[[159, "Roboto-Medium", "Roboto-Medium.ttf"], -1], 0, 0, [], [], []], [[{"name": "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "rect": [1, 1, 68, 68], "offset": [-0.5, 0.5], "originalSize": [71, 71], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [176]], [[[26, "hit", ".mp3", 0.288], -1], 0, 0, [], [], []], [[{"name": "ui_sung1", "rect": [9, 5, 164, 73], "offset": [-0.5, -2.5], "originalSize": [183, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [177]], [[{"name": "btnMinus", "rect": [0, 0, 77, 56], "offset": [0, 0], "originalSize": [77, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [178]], [[{"name": "text", "rect": [0, 0, 146, 41], "offset": [0, 0], "originalSize": [146, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [179]], [[{"name": "dan1", "rect": [0, 0, 61, 129], "offset": [0, 0], "originalSize": [61, 129], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [180]], [[{"name": "btnAdd", "rect": [0, 0, 71, 52], "offset": [0, 0.5], "originalSize": [71, 53], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [181]], [[{"name": "btnHonors", "rect": [1, 1, 76, 76], "offset": [-0.5, 0.5], "originalSize": [79, 79], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [182]], [[{"name": "bgJackpot", "rect": [0, 0, 299, 105], "offset": [0, 0], "originalSize": [299, 105], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [183]], [[{"name": "round_02", "rect": [0, 0, 141, 48], "offset": [0, 0], "originalSize": [141, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [184]], [[{"name": "sound-icon", "rect": [0, 0, 76, 78], "offset": [0, 0], "originalSize": [76, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [185]]]]