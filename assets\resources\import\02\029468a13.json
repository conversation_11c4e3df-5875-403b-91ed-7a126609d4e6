[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "bcbgFB+epIlKdGw1JroyQo", "26XBIiR41EpJSfFK/OQnr/", "dajDVAI7xFELGIq5QJOZI5", "c1y3UL3AVHoqWPxPdQzt/K", "2cWB/vWPRHja3uQTinHH30", "a9r3CcUc5GmZKsUU0DHpzz"], ["node", "_spriteFrame", "root", "_N$target", "_N$content", "data", "_N$file", "_defaultClip", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_active", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint"], 0, 9, 4, 1, 5, 7, 2, 5], ["cc.Sprite", ["_type", "_isTrimmedMode", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 9, 5, 5, 5, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["bc40dQyQXtKzpVzgCIsT6zv", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -2, 1, 3, 6]], [[7, 0, 1, 2], [0, 0, 5, 3, 4, 6, 7, 2], [1, 2, 3, 4, 5, 2], [2, 2, 4, 1], [8, 0, 1, 2, 3], [4, 0, 2], [0, 0, 8, 3, 4, 2], [0, 0, 5, 8, 3, 4, 6, 2], [0, 0, 1, 5, 3, 4, 6, 7, 3], [0, 0, 5, 8, 3, 4, 6, 9, 7, 2], [0, 0, 5, 8, 3, 4, 6, 9, 2], [0, 0, 5, 3, 4, 6, 9, 7, 2], [0, 0, 2, 5, 3, 4, 6, 7, 3], [5, 0, 1, 2, 1], [6, 0, 1], [1, 0, 1, 3, 4, 5, 3], [1, 3, 4, 5, 1], [2, 0, 2, 3, 5, 6, 7, 2], [2, 1, 0, 2, 3, 3], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 6]], [[[[5, "SicboHelpView"], [6, "taiXiuHelpView", [-4, -5, -6, -7], [[13, -2, [12, 13], 11], [14, -3]], [0, "482uhYoORHP6+1RUzk1glq", -1]], [7, "nen popup", 1, [-10, -11], [[15, 1, false, -8, [4], 5], [3, -9, [4, 4292269782]]], [0, "241vm6yaND3ruq4O7+83Ie", 1], [5, 1197, 634]], [1, "btnClose", 1, [[2, 0, -12, [9], 10], [17, 3, -14, [[4, "bc40dQyQXtKzpVzgCIsT6zv", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -13]], [0, "13yjKDFnVNZ6AAvSO91mEo", 1], [5, 90, 50], [553.7, 288.649, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "black", 100, 1, [[2, 0, -15, [0], 1], [3, -16, [4, 4292269782]]], [0, "9082MxgbxHNIBcevNUN1fe", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "scrollview", 1, [-19], [[19, false, 0.75, 0.23, null, null, -18, -17]], [0, "cafBi9cEdFdLzXmmoC3Kup", 1], [5, 1000, 529], [0, 0.5, 1], [0, 252, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "view", 5, [-21], [[20, -20, [8]]], [0, "99WhUSkfJBTazBqOuKi6GR", 1], [5, 1000, 538], [0, 0.5, 1]], [11, "hd_pic", 6, [[16, -22, [6], 7]], [0, "75OFmMkLdI0K6hWJBCYuHP", 1], [5, 1606, 3112], [0, 0.5, 1], [0, -24.559, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [12, "btnRule", false, 2, [[18, 0.9, 3, -23, [[4, "c1258OEgGhNlanTanxfRHzr", "showRuleClicked", 1]]]], [0, "a40wahOqRPdIdCgFBqySOi", 1], [5, 779, 30], [0, -91, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 2, [[21, "LUẬT CHƠI", 24, false, 1, 1, -24, [2], 3]], [0, "b6wNXpqi5FDYjNYHC3f1E0", 1], [5, 215.25, 30], [0, 314.841, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 5, 0, -4, 3, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 4, 7, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 5, 1, 24], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 6, -1, 1, -1, 1, -1, -1, 1, 7, -1, -2], [0, 2, 0, 3, 0, 4, 0, 5, 0, 0, 6, 1, 1, 7]], [[{"name": "Info_Sicbo", "rect": [0, 0, 1606, 3112], "offset": [0, 0], "originalSize": [1606, 3112], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [8], [8]]]]