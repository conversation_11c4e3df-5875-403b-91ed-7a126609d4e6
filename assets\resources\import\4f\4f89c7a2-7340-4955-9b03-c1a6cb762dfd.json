[1, ["6dxfFcu2hIsqgbvesvI09o"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "Quaynhanh", "\r\nQuaynhanh.png\r\nsize: 570,335\r\nformat: RGBA8888\r\nfilter: Linear,Linear\r\nrepeat: none\r\nquay nhanh Polygon\r\n  rotate: false\r\n  xy: 379, 227\r\n  size: 99, 106\r\n  orig: 99, 106\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light A\r\n  rotate: true\r\n  xy: 482, 157\r\n  size: 32, 82\r\n  orig: 32, 82\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light B\r\n  rotate: false\r\n  xy: 349, 53\r\n  size: 47, 63\r\n  orig: 47, 63\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light C\r\n  rotate: false\r\n  xy: 252, 25\r\n  size: 59, 60\r\n  orig: 59, 60\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light D\r\n  rotate: false\r\n  xy: 398, 56\r\n  size: 46, 60\r\n  orig: 46, 60\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light E\r\n  rotate: true\r\n  xy: 524, 57\r\n  size: 58, 44\r\n  orig: 58, 44\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light F\r\n  rotate: false\r\n  xy: 516, 25\r\n  size: 25, 30\r\n  orig: 25, 30\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light G\r\n  rotate: false\r\n  xy: 112, 55\r\n  size: 94, 64\r\n  orig: 94, 64\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light H\r\n  rotate: false\r\n  xy: 2, 55\r\n  size: 108, 64\r\n  orig: 108, 64\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light I\r\n  rotate: false\r\n  xy: 480, 231\r\n  size: 69, 65\r\n  orig: 69, 65\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light K\r\n  rotate: false\r\n  xy: 2, 162\r\n  size: 375, 171\r\n  orig: 375, 171\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text A\r\n  rotate: false\r\n  xy: 349, 12\r\n  size: 59, 39\r\n  orig: 59, 39\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text B\r\n  rotate: false\r\n  xy: 482, 191\r\n  size: 69, 38\r\n  orig: 69, 38\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text C\r\n  rotate: true\r\n  xy: 212, 2\r\n  size: 83, 38\r\n  orig: 83, 38\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text D\r\n  rotate: false\r\n  xy: 475, 117\r\n  size: 89, 38\r\n  orig: 89, 38\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text E\r\n  rotate: false\r\n  xy: 2, 16\r\n  size: 102, 37\r\n  orig: 102, 37\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text F\r\n  rotate: false\r\n  xy: 215, 124\r\n  size: 104, 36\r\n  orig: 104, 36\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text G\r\n  rotate: false\r\n  xy: 106, 18\r\n  size: 104, 35\r\n  orig: 104, 35\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text H\r\n  rotate: false\r\n  xy: 379, 190\r\n  size: 101, 35\r\n  orig: 101, 35\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text I\r\n  rotate: false\r\n  xy: 379, 154\r\n  size: 94, 34\r\n  orig: 94, 34\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text K\r\n  rotate: false\r\n  xy: 446, 81\r\n  size: 76, 34\r\n  orig: 76, 34\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text M\r\n  rotate: false\r\n  xy: 377, 118\r\n  size: 90, 34\r\n  orig: 90, 34\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text N\r\n  rotate: true\r\n  xy: 313, 32\r\n  size: 90, 34\r\n  orig: 90, 34\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text O\r\n  rotate: true\r\n  xy: 446, 2\r\n  size: 77, 33\r\n  orig: 77, 33\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text P\r\n  rotate: true\r\n  xy: 481, 13\r\n  size: 66, 33\r\n  orig: 66, 33\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text R\r\n  rotate: false\r\n  xy: 215, 87\r\n  size: 93, 35\r\n  orig: 93, 35\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text X\r\n  rotate: true\r\n  xy: 410, 13\r\n  size: 41, 32\r\n  orig: 41, 32\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text Y\r\n  rotate: false\r\n  xy: 480, 298\r\n  size: 88, 35\r\n  orig: 88, 35\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh light text Z\r\n  rotate: false\r\n  xy: 321, 128\r\n  size: 54, 32\r\n  orig: 54, 32\r\n  offset: 0, 0\r\n  index: -1\r\nquay nhanh text\r\n  rotate: false\r\n  xy: 2, 121\r\n  size: 211, 39\r\n  orig: 211, 39\r\n  offset: 0, 0\r\n  index: -1\r\n", ["Quaynhanh.png"], {"skeleton": {"hash": "jJSakuXk1lZVfgsD4vCQOnNNcU8", "spine": "3.6.53", "width": 399, "height": 172, "images": "../images/"}, "bones": [{"name": "root"}, {"name": "quay nhanh light K", "parent": "root", "color": "002dffff"}, {"name": "quay nhanh Polygonn", "parent": "quay nhanh light K", "x": 6.39, "y": 85.99, "color": "002dffff"}, {"name": "quay nhanh Polygon", "parent": "quay nhanh Polygonn", "x": -0.64, "y": -1.68}, {"name": "quay nhanh light A", "parent": "quay nhanh light K", "x": -71.76, "y": 80.18}, {"name": "quay nhanh light B", "parent": "quay nhanh light K", "x": -55.26, "y": 87.68}, {"name": "quay nhanh light C", "parent": "quay nhanh light K", "x": -49.26, "y": 101.18}, {"name": "quay nhanh light D", "parent": "quay nhanh light K", "x": 1.24, "y": 106.18}, {"name": "quay nhanh light E", "parent": "quay nhanh light K", "x": 37.24, "y": 120.18}, {"name": "quay nhanh light F", "parent": "quay nhanh light K", "x": 30.74, "y": 99.18}, {"name": "quay nhanh light G", "parent": "quay nhanh light K", "x": -15.76, "y": 61.18}, {"name": "quay nhanh light H", "parent": "quay nhanh light K", "x": 5.24, "y": 59.18}, {"name": "quay nhanh light I", "parent": "quay nhanh light K", "x": 71.74, "y": 79.68}, {"name": "quay nhanh text", "parent": "quay nhanh light K", "x": -6.26, "y": 12.68}, {"name": "quay nhanh light text A", "parent": "quay nhanh text", "x": -76}, {"name": "quay nhanh light text B", "parent": "quay nhanh text", "x": -71, "y": 0.5}, {"name": "quay nhanh light text C", "parent": "quay nhanh text", "x": -64, "y": 0.5}, {"name": "quay nhanh light text D", "parent": "quay nhanh text", "x": -59, "y": 0.5}, {"name": "quay nhanh light text E", "parent": "quay nhanh text", "x": -43.5, "y": 1}, {"name": "quay nhanh light text F", "parent": "quay nhanh text", "x": -29.5, "y": 1.5}, {"name": "quay nhanh light text G", "parent": "quay nhanh text", "x": -15.5, "y": 2}, {"name": "quay nhanh light text H", "parent": "quay nhanh text", "x": 1, "y": 2}, {"name": "quay nhanh light text I", "parent": "quay nhanh text", "x": 7.5, "y": 2.5}, {"name": "quay nhanh light text K", "parent": "quay nhanh text", "x": 28.5, "y": 2.5}, {"name": "quay nhanh light text M", "parent": "quay nhanh text", "x": 35.5, "y": 2.5}, {"name": "quay nhanh light text N", "parent": "quay nhanh text", "x": 46.5, "y": 2.5}, {"name": "quay nhanh light text O", "parent": "quay nhanh text", "x": 67, "y": 2}, {"name": "quay nhanh light text P", "parent": "quay nhanh text", "x": 72.5, "y": 2}, {"name": "quay nhanh light text R", "parent": "quay nhanh text", "x": 54, "y": 2}, {"name": "quay nhanh light text X", "parent": "quay nhanh text", "x": 84, "y": 2.5}, {"name": "quay nhanh light text Y", "parent": "quay nhanh text", "x": 61.5, "y": 2}, {"name": "quay nhanh light text Z", "parent": "quay nhanh text", "x": 78.5, "y": 2.5}], "slots": [{"name": "quay nhanh light K", "bone": "quay nhanh light K", "attachment": "quay nhanh light K"}, {"name": "quay nhanh light I", "bone": "quay nhanh light I", "attachment": "quay nhanh light I", "blend": "additive"}, {"name": "quay nhanh light H", "bone": "quay nhanh light H", "attachment": "quay nhanh light H", "blend": "additive"}, {"name": "quay nhanh light G", "bone": "quay nhanh light G", "attachment": "quay nhanh light G", "blend": "additive"}, {"name": "quay nhanh light F", "bone": "quay nhanh light F", "attachment": "quay nhanh light F", "blend": "additive"}, {"name": "quay nhanh light E", "bone": "quay nhanh light E", "attachment": "quay nhanh light E", "blend": "additive"}, {"name": "quay nhanh light D", "bone": "quay nhanh light D", "attachment": "quay nhanh light D", "blend": "additive"}, {"name": "quay nhanh light C", "bone": "quay nhanh light C", "attachment": "quay nhanh light C", "blend": "additive"}, {"name": "quay nhanh light B", "bone": "quay nhanh light B", "attachment": "quay nhanh light B", "blend": "additive"}, {"name": "quay nhanh light A", "bone": "quay nhanh light A", "attachment": "quay nhanh light A", "blend": "additive"}, {"name": "quay nhanh text", "bone": "quay nhanh text", "attachment": "quay nhanh text"}, {"name": "quay nhanh light text X", "bone": "quay nhanh light text X", "attachment": "quay nhanh light text X", "blend": "additive"}, {"name": "quay nhanh light text Z", "bone": "quay nhanh light text Z", "attachment": "quay nhanh light text Z", "blend": "additive"}, {"name": "quay nhanh light text P", "bone": "quay nhanh light text P", "attachment": "quay nhanh light text P", "blend": "additive"}, {"name": "quay nhanh light text O", "bone": "quay nhanh light text O", "attachment": "quay nhanh light text O", "blend": "additive"}, {"name": "quay nhanh light text Y", "bone": "quay nhanh light text Y", "attachment": "quay nhanh light text Y", "blend": "additive"}, {"name": "quay nhanh light text R", "bone": "quay nhanh light text R", "attachment": "quay nhanh light text R", "blend": "additive"}, {"name": "quay nhanh light text N", "bone": "quay nhanh light text N", "attachment": "quay nhanh light text N", "blend": "additive"}, {"name": "quay nhanh light text M", "bone": "quay nhanh light text M", "attachment": "quay nhanh light text M", "blend": "additive"}, {"name": "quay nhanh light text K", "bone": "quay nhanh light text K", "attachment": "quay nhanh light text K", "blend": "additive"}, {"name": "quay nhanh light text I", "bone": "quay nhanh light text I", "attachment": "quay nhanh light text I", "blend": "additive"}, {"name": "quay nhanh light text H", "bone": "quay nhanh light text H", "attachment": "quay nhanh light text H", "blend": "additive"}, {"name": "quay nhanh light text G", "bone": "quay nhanh light text G", "attachment": "quay nhanh light text G", "blend": "additive"}, {"name": "quay nhanh light text F", "bone": "quay nhanh light text F", "attachment": "quay nhanh light text F", "blend": "additive"}, {"name": "quay nhanh light text E", "bone": "quay nhanh light text E", "attachment": "quay nhanh light text E", "blend": "additive"}, {"name": "quay nhanh light text D", "bone": "quay nhanh light text D", "attachment": "quay nhanh light text D", "blend": "additive"}, {"name": "quay nhanh light text C", "bone": "quay nhanh light text C", "attachment": "quay nhanh light text C", "blend": "additive"}, {"name": "quay nhanh light text B", "bone": "quay nhanh light text B", "attachment": "quay nhanh light text B", "blend": "additive"}, {"name": "quay nhanh light text A", "bone": "quay nhanh light text A", "attachment": "quay nhanh light text A", "blend": "additive"}, {"name": "quay nhanh Polygon", "bone": "quay nhanh Polygon", "attachment": "quay nhanh Polygon"}], "skins": {"default": {"quay nhanh Polygon": {"quay nhanh Polygon": {"y": 5.86, "width": 99, "height": 106}}, "quay nhanh light A": {"quay nhanh light A": {"width": 32, "height": 82}}, "quay nhanh light B": {"quay nhanh light B": {"width": 47, "height": 63}}, "quay nhanh light C": {"quay nhanh light C": {"width": 59, "height": 60}}, "quay nhanh light D": {"quay nhanh light D": {"width": 46, "height": 60}}, "quay nhanh light E": {"quay nhanh light E": {"width": 58, "height": 44}}, "quay nhanh light F": {"quay nhanh light F": {"width": 25, "height": 30}}, "quay nhanh light G": {"quay nhanh light G": {"width": 94, "height": 64}}, "quay nhanh light H": {"quay nhanh light H": {"width": 108, "height": 64}}, "quay nhanh light I": {"quay nhanh light I": {"width": 69, "height": 65}}, "quay nhanh light K": {"quay nhanh light K": {"x": 99.74, "y": 56.68, "width": 375, "height": 171}}, "quay nhanh light text A": {"quay nhanh light text A": {"width": 59, "height": 39}}, "quay nhanh light text B": {"quay nhanh light text B": {"width": 69, "height": 38}}, "quay nhanh light text C": {"quay nhanh light text C": {"width": 83, "height": 38}}, "quay nhanh light text D": {"quay nhanh light text D": {"width": 89, "height": 38}}, "quay nhanh light text E": {"quay nhanh light text E": {"width": 102, "height": 37}}, "quay nhanh light text F": {"quay nhanh light text F": {"width": 104, "height": 36}}, "quay nhanh light text G": {"quay nhanh light text G": {"width": 104, "height": 35}}, "quay nhanh light text H": {"quay nhanh light text H": {"width": 101, "height": 35}}, "quay nhanh light text I": {"quay nhanh light text I": {"width": 94, "height": 34}}, "quay nhanh light text K": {"quay nhanh light text K": {"width": 76, "height": 34}}, "quay nhanh light text M": {"quay nhanh light text M": {"width": 90, "height": 34}}, "quay nhanh light text N": {"quay nhanh light text N": {"width": 90, "height": 34}}, "quay nhanh light text O": {"quay nhanh light text O": {"width": 77, "height": 33}}, "quay nhanh light text P": {"quay nhanh light text P": {"width": 66, "height": 33}}, "quay nhanh light text R": {"quay nhanh light text R": {"width": 93, "height": 35}}, "quay nhanh light text X": {"quay nhanh light text X": {"width": 41, "height": 32}}, "quay nhanh light text Y": {"quay nhanh light text Y": {"width": 88, "height": 35}}, "quay nhanh light text Z": {"quay nhanh light text Z": {"width": 54, "height": 32}}, "quay nhanh text": {"quay nhanh text": {"width": 211, "height": 39}}}}, "animations": {"Idle": {"slots": {"quay nhanh light A": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffc7"}, {"time": 0.5, "color": "ffffff00"}]}, "quay nhanh light B": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffc7"}, {"time": 0.6667, "color": "ffffff00"}]}, "quay nhanh light C": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffc7"}, {"time": 0.8333, "color": "ffffff00"}]}, "quay nhanh light D": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffc7"}, {"time": 1, "color": "ffffff00"}]}, "quay nhanh light E": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffc7"}, {"time": 1.5, "color": "ffffff00"}]}, "quay nhanh light F": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffc7"}, {"time": 1.1667, "color": "ffffff00"}]}, "quay nhanh light G": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffc7"}, {"time": 1, "color": "ffffff00"}]}, "quay nhanh light H": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffc7"}, {"time": 1.1667, "color": "ffffff00"}]}, "quay nhanh light I": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffc7"}, {"time": 1.5, "color": "ffffff00"}]}, "quay nhanh light text A": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00"}]}, "quay nhanh light text B": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2671, "color": "ffffff00"}]}, "quay nhanh light text C": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.3338, "color": "ffffff00"}]}, "quay nhanh light text D": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2671, "color": "ffffffff"}, {"time": 0.4006, "color": "ffffff00"}]}, "quay nhanh light text E": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2671, "color": "ffffff00"}, {"time": 0.3338, "color": "ffffffff"}, {"time": 0.4674, "color": "ffffff00"}]}, "quay nhanh light text F": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3338, "color": "ffffff00"}, {"time": 0.4006, "color": "ffffffff"}, {"time": 0.5341, "color": "ffffff00"}]}, "quay nhanh light text G": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4006, "color": "ffffff00"}, {"time": 0.4674, "color": "ffffffff"}, {"time": 0.6009, "color": "ffffff00"}]}, "quay nhanh light text H": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4674, "color": "ffffff00"}, {"time": 0.5341, "color": "ffffffff"}, {"time": 0.6677, "color": "ffffff00"}]}, "quay nhanh light text I": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5341, "color": "ffffff00"}, {"time": 0.6009, "color": "ffffffff"}, {"time": 0.7344, "color": "ffffff00"}]}, "quay nhanh light text K": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6009, "color": "ffffff00"}, {"time": 0.6677, "color": "ffffffff"}, {"time": 0.8012, "color": "ffffff00"}]}, "quay nhanh light text M": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6677, "color": "ffffff00"}, {"time": 0.7344, "color": "ffffffff"}, {"time": 0.868, "color": "ffffff00"}]}, "quay nhanh light text N": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7344, "color": "ffffff00"}, {"time": 0.8012, "color": "ffffffff"}, {"time": 0.9347, "color": "ffffff00"}]}, "quay nhanh light text O": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9347, "color": "ffffff00"}, {"time": 1.0015, "color": "ffffffff"}, {"time": 1.1351, "color": "ffffff00"}]}, "quay nhanh light text P": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0015, "color": "ffffff00"}, {"time": 1.0683, "color": "ffffffff"}, {"time": 1.2018, "color": "ffffff00"}]}, "quay nhanh light text R": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8012, "color": "ffffff00"}, {"time": 0.868, "color": "ffffffff"}, {"time": 1.0015, "color": "ffffff00"}]}, "quay nhanh light text X": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1351, "color": "ffffff00"}, {"time": 1.2018, "color": "ffffffff"}, {"time": 1.3354, "color": "ffffff00"}]}, "quay nhanh light text Y": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.868, "color": "ffffff00"}, {"time": 0.9347, "color": "ffffffff"}, {"time": 1.0683, "color": "ffffff00"}]}, "quay nhanh light text Z": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0683, "color": "ffffff00"}, {"time": 1.1351, "color": "ffffffff"}, {"time": 1.2686, "color": "ffffff00"}]}}, "bones": {"quay nhanh Polygon": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8, "angle": 179}, {"time": 0.8667, "angle": -173.05}, {"time": 1.6667, "angle": 0}]}, "quay nhanh Polygonn": {"translate": [{"time": 0, "x": -4.67, "y": 0.92}], "scale": [{"time": 0, "x": 1.12, "y": 1.018}], "shear": [{"time": 0, "x": 14.77, "y": 20.14}]}, "quay nhanh text": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.419, "x": 1.048, "y": 1.048, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8379, "x": 1, "y": 1}, {"time": 1.2569, "x": 1.048, "y": 1.048, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]