[1, ["2eRH5kkv1CebHbXnzgzgLx", "791CbVKiJMjJXZKWT0rZyK", "df35oAfp5CIIi1LEqA48Qv", "7c2bAmReRMn7dFdT9qw4S+", "8cMA5LMzRBB6LIhgQ7KBJQ", "47+6LMOoBDEqbBT7zaErlr", "6eiI0D+jVHbo/+LbE2UpUs", "3cuhjzPnVHwaZSw0USkahB", "43TXmfBttBg6EtUVO2Cy0V", "64Oa3DXmpKZIFQBrhVPE8e", "3dIdZFGhtBE6E/cDGG4YSz", "b3KIstUyVB27JwhFIZgvvd", "a0P7EBWnVGoJ+HRaeeFhiS", "71nkR/LFNAeLLemdSux5tg", "a1Ry8Gl4lM1ZweJzZ8DCMK", "92TVz1xNdBfprnGyihl88Q", "e6zPGPmQJLsJF5Pk4jML8z", "f5CMGNjypBg7EEaZwD+mli", "cctZ0k/udGSJFAOGJDf0S4", "96XJ3fkCRCF7kvadTtmEEX", "95zE2j4V9Gjq4kwCYRGD/O", "c7wgWOVHhLHqVOTibEVvVN", "a1wwn3pzRNtptAZhp8xcwg", "22VtJaXllHv4T1jzSwySa3"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "23_00003", "rect": [4, 0, 130, 249], "offset": [-3.5, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "23_00006", "rect": [15, 0, 121, 249], "offset": [3, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [1]], [[{"name": "23_00004", "rect": [8, 0, 125, 249], "offset": [-2, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [2]], [[{"name": "23_00001", "rect": [0, 0, 143, 248], "offset": [-1, 0.5], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [3]], [[[0, "fish19", 0.8, 15, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.06666666666666667}, "value", 6, 1], [{"frame": 0.13333333333333333}, "value", 6, 2], [{"frame": 0.2}, "value", 6, 3], [{"frame": 0.26666666666666666}, "value", 6, 4], [{"frame": 0.3333333333333333}, "value", 6, 5], [{"frame": 0.4}, "value", 6, 6], [{"frame": 0.4666666666666667}, "value", 6, 7], [{"frame": 0.5333333333333333}, "value", 6, 8], [{"frame": 0.6}, "value", 6, 9], [{"frame": 0.6666666666666666}, "value", 6, 10], [{"frame": 0.7333333333333333}, "value", 6, 11]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]], [[{"name": "23_00005", "rect": [12, 0, 122, 249], "offset": [0.5, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [16]], [[{"name": "23_00002", "rect": [1, 0, 135, 248], "offset": [-4, 0.5], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [17]], [[{"name": "23_00009", "rect": [13, 0, 131, 249], "offset": [6, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [18]], [[{"name": "23_00000", "rect": [2, 0, 139, 248], "offset": [-1, 0.5], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [19]], [[{"name": "23_00011", "rect": [5, 0, 138, 249], "offset": [1.5, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [20]], [[{"name": "23_00008", "rect": [17, 0, 125, 249], "offset": [7, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [21]], [[{"name": "23_00010", "rect": [9, 0, 135, 249], "offset": [4, 0], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [22]], [[{"name": "23_00007", "rect": [17, 0, 123, 248], "offset": [6, 0.5], "originalSize": [145, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [23]]]]