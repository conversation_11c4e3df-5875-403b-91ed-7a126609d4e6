[1, ["ecpdLyjvZBwrvm+cedCcQy", "24xd2Xl+xHVZeWwPN10Wzf", "fdHIe7q1BD+o3g3g+0ROLn", "d82n49/IVAvIEqsa0xvvk0", "7a/QZLET9IDreTiBfRn2PD", "9bYDnu5NdJH7u79ZpCFpEO", "fdNoodJKVLj4dF1TLppv2g", "9aruc+aM1K7Z7JgaauoqQo", "a9VpD0DP5LJYQPXITZq+uj", "45FxAZBZxO3Lyn049ErJ1j", "26XBIiR41EpJSfFK/OQnr/", "97hXOIiwdEKpNpE4oPTXe8", "2cWB/vWPRHja3uQTinHH30", "79kwd+hddCpZ7QvD32rA6n", "89y8OQyq9L95fqHTfMrnxo"], ["node", "_spriteFrame", "_N$file", "_N$skeletonData", "root", "TaiXiuMd5TopListView", "rankSprite3", "rankSprite2", "rankSprite1", "lbTotalWin", "lbNickName", "lbRank", "_N$target", "data", "_parent", "scrollView", "fontRegurlar", "fontBold", "fontName", "_defaultClip", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint", "_eulerAngles", "_color"], 0, 4, 9, 1, 5, 7, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$verticalAlign", "_spacingX", "_N$overflow", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["e546fhM459ObqNd4ssUCCsL", ["node", "TaiXiuMd5TopListView"], 3, 1, 1], ["5a520LZ8StG5ovcuQfM5jmr", ["node", "lbRank", "lbNickName", "lbTotalWin", "rankSprite1", "rankSprite2", "rankSprite3", "fontRegurlar", "fontBold", "fontName"], 3, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials"], -1, 1, 3], ["60787IDPThN5Zw8ZNMQj9dN", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate"], 0, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1]], [[3, 0, 1, 2, 2], [0, 0, 5, 4, 3, 6, 7, 2], [9, 0, 1, 2, 3, 4, 5, 2], [1, 0, 2, 3, 4, 2], [2, 0, 1, 2, 7, 3, 9, 10, 11, 6], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 7, 10, 2], [2, 0, 1, 4, 5, 2, 3, 6, 8, 9, 10, 9], [16, 0, 1, 2, 3, 4, 5, 5], [7, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 6, 7, 2], [0, 0, 5, 8, 4, 3, 6, 7, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 4, 3, 6, 9, 7, 2], [0, 0, 5, 8, 3, 2], [0, 0, 2, 5, 4, 3, 11, 6, 7, 3], [8, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 1], [11, 0, 1, 1], [3, 1, 2, 1], [1, 2, 3, 4, 1], [1, 1, 0, 2, 3, 4, 3], [1, 2, 3, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [13, 0, 1], [4, 0, 1, 2, 3, 4, 5, 2], [4, 1, 6, 1], [14, 0, 1, 2, 3], [5, 0, 1, 2, 4, 5, 4], [5, 0, 1, 3, 4, 5, 4], [15, 0, 1, 1], [2, 0, 1, 4, 5, 2, 3, 6, 9, 8], [2, 0, 1, 4, 5, 2, 3, 6, 9, 10, 8], [17, 0, 1, 2, 3, 4, 4], [18, 0, 1, 2, 3, 4, 5, 6, 6]], [[[[9, "taiXiuMd5TopView"], [10, "taiXiuTopView", [-5, -6, -7, -8, -9], [[18, -2, [33, 34], 32], [19, -4, -3]], [20, -1, 0]], [5, "bg", 1, [-11, -12, -13, -14, -15, -16, -17, -18], [[3, 0, -10, [18], 19]], [0, "d2D3B9k8VBh5vWXCP4MZki", 1, 0], [5, 1200, 634]], [11, "<PERSON><PERSON>", [-26, -27, -28, -29, -30, -31], [[24, -25, -24, -23, -22, -21, -20, -19, 28, 29, 30]], [0, "daXgaQxB1DM6FT02EdpSjD", 1, 0], [5, 1004, 50], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "scrollview", 1, [-35], [[-32, -33, [25, -34]], 1, 1, 4], [0, "7dYoD2m1hFaLqhkdEJOU4Z", 1, 0], [5, 1150, 370], [0, -61, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 1, [[21, -36, [20], 21], [26, 3, -38, [[28, "e546fhM459ObqNd4ssUCCsL", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -37]], [0, "2519Sre1pBIJ+oZiZFW85f", 1, 0], [5, 96, 50], [553.308, 290.425, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "layout-nick<PERSON><PERSON>", 3, [-40, -41], [[29, 1, 1, 5, -39, [5, 120.49, 50]]], [0, "29UNpnGb9M76WgMZyr3Cn1", 1, 0], [5, 120.49, 50], [-195, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[3, 0, -42, [0], 1], [27, -43, [4, 4292269782]]], [0, "ddF0lWsbpDlpgT+vu/YpWY", 1, 0], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "view", 4, [-45], [[31, -44, [31]]], [0, "670MfFlZxBQaYf+46HO4u9", 1, 0], [5, 1150, 370]], [14, "content", 8, [[30, 1, 2, 10, -46, [5, 1150, 0]]], [0, "af7qEwzvJK6oj7fcUr2qNe", 1, 0], [5, 1150, 0], [0, 0.5, 1], [0, 188, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "borderPass", 2, [[22, 1, 0, -47, [2], 3]], [0, "a2drTFWXBKnpNLPOKs6xz+", 1, 0], [5, 1160, 450], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Title", 2, [[4, "XẾP HẠNG TÀI XỈU MD5", 22, false, -1, 1, -48, [4], 5]], [0, "46vOlk1K9BipUrL3M8NWeg", 1, 0], [5, 412.06, 40], [0, 306, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Title", 2, [[4, "Hạng", 17, false, -1, 1, -49, [6], 7]], [0, "adSd9BuNhFVKVAkEzVRyv1", 1, 0], [5, 66.59, 40], [-488.001, 173, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Title", 2, [[4, "<PERSON><PERSON><PERSON>", 17, false, -1, 1, -50, [8], 9]], [0, "40+UcvYitISb6dgRs6clxx", 1, 0], [5, 164.31, 40], [-190.791, 173, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Title", 2, [[4, "Tổng <PERSON>", 17, false, -1, 1, -51, [10], 11]], [0, "0fokOFgwVNT4Fa66Mkj0U0", 1, 0], [5, 157.81, 40], [287.29, 173, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[3, 0, -52, [12], 13]], [0, "e3LSLx2u5MELZN+TVAbnIq", 1, 0], [5, 1400, 24], [0, 116, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "line", 2, [[3, 0, -53, [14], 15]], [0, "1a+IlfXx9L4IuzRGmJVjvL", 1, 0], [5, 600, 24], [-402.401, -51.019999999999996, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [6, "line", 2, [[3, 0, -54, [16], 17]], [0, "4dPinLJSJMjLuX7Ne0tQt+", 1, 0], [5, 600, 24], [25, -51.019999999999996, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [15, "temp", 1, [3], [0, "ba0lKMgeFCmalStzPvOTzQ", 1, 0]], [2, "lbRank", 3, [-55], [0, "604uLuuUBP9aMhe6PNbpJy", 1, 0], [5, 150, 30], [-493, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "1", 26, 48, false, false, 1, 1, 1, 19, [22]], [2, "rankSprite1", 3, [-56], [0, "b45IrD4j1IO7q3ajK2MTBX", 1, 0], [5, 122.39, 130.51], [-490.8, -0.3999999999999999, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [8, "default", "Rank1", 0, "Rank1", 21, [23]], [2, "rankSprite2", 3, [-57], [0, "5bo0n4Gj5L6Kd7W2P8rjdd", 1, 0], [5, 122.39, 130.51], [-490.8, -0.3999999999999999, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [8, "default", "Rank2", 0, "Rank2", 23, [24]], [2, "rankSprite3", 3, [-58], [0, "47sKa9fOhCdbomYg2nt+4E", 1, 0], [5, 36, 40], [-490.8, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [23, 25, [25]], [16, "lbSID", false, 6, [[32, "[TQ]", 24, 48, false, false, 1, 1, -59]], [0, "05IcMsiERAwYJqXW8oxoh/", 1, 0], [4, 4279026733], [5, 44.4, 28.8], [-59.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbNickName", 6, [-60], [0, "72eIaB3kBFS7AvMvLpnSgG", 1, 0], [5, 120.49, 60.48], [-7.105427357601002e-15, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "<PERSON><PERSON><PERSON>", 24, 48, false, false, 1, 1, 28, [26]], [2, "lbTotalWin", 3, [-61], [0, "861IwfPDtKV6x7wMhVeLf8", 1, 0], [5, 200, 0], [284, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "0", 25, 0, false, false, 1, 1, 1, 30, [27]], [34, 20, 10, 400, 4, 3], [35, false, 0.75, 0.23, null, null, 4, 9]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 32, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, -3, 5, 0, -4, 18, 0, -5, 4, 0, 0, 2, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, -5, 14, 0, -6, 15, 0, -7, 16, 0, -8, 17, 0, 6, 26, 0, 7, 24, 0, 8, 22, 0, 9, 31, 0, 10, 29, 0, 11, 20, 0, 0, 3, 0, -1, 19, 0, -2, 21, 0, -3, 23, 0, -4, 25, 0, -5, 6, 0, -6, 30, 0, -1, 32, 0, -2, 33, 0, 0, 4, 0, -1, 8, 0, 0, 5, 0, 12, 5, 0, 0, 5, 0, 0, 6, 0, -1, 27, 0, -2, 28, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, -1, 20, 0, -1, 22, 0, -1, 24, 0, -1, 26, 0, 0, 27, 0, -1, 29, 0, -1, 31, 0, 13, 1, 3, 14, 18, 32, 15, 33, 61], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 24, 26, 29, 31], [-1, 1, -1, 1, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 16, 17, 18, -1, 19, -1, -2, 3, 3, 1, 2, 2], [0, 8, 0, 9, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2, 0, 10, 0, 11, 0, 4, 4, 0, 0, 0, 3, 3, 5, 0, 6, 6, 12, 7, 7, 13, 5, 3]], [[{"name": "icTopexit", "rect": [0, 0, 96, 50], "offset": [0, 0], "originalSize": [96, 50], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [20], [14]]]]