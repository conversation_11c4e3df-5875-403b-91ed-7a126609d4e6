[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "62TTC6G+hHCamgiYceJ0w6", "e8zRISlSpJaY0/oMNgi0gY", "7a/QZLET9IDreTiBfRn2PD", "e1NJ8c5BZHI5JcP4RUdG0s", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "lbBet", "lbTime", "_defaultClip", "root", "dragonTigerHistoryListView", "lbSessionID", "lbWin", "lbRefund", "lbResult", "lbSide", "lbSession", "_N$target", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_color"], 2, 1, 2, 4, 5, 7, 2, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_N$horizontalAlign", "_N$overflow", "_spacingX", "_enableWrapText", "_N$verticalAlign", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 5, 5], ["a2a1brfUOREaKNBKPd1nw5T", ["node", "lbSession", "lbTime", "lbSide", "lbResult", "lbBet", "lbRefund", "lbWin"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["7db6by5MyFPbputnQXLGklv", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["a9398OWmM5LiKsVZpumOhGe", ["node", "dragonTigerHistoryListView"], 3, 1, 1]], [[5, 0, 1, 2], [0, 0, 4, 3, 2, 5, 7, 2], [3, 0, 1, 3, 7, 2, 6, 4, 5, 9, 10, 9], [3, 0, 1, 3, 7, 2, 4, 8, 5, 9, 10, 9], [2, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 7, 4, 5, 2], [0, 0, 4, 6, 3, 2, 5, 7, 2], [4, 1, 2, 1], [6, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 1, 4, 3, 2, 5, 7, 3], [0, 0, 4, 3, 2, 5, 2], [0, 0, 4, 6, 2, 7, 2], [0, 0, 6, 3, 2, 5, 2], [0, 0, 4, 6, 3, 2, 5, 2], [0, 0, 4, 2, 5, 8, 7, 2], [2, 0, 1, 6, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 2], [1, 0, 3, 4, 5, 2], [1, 2, 0, 1, 3, 4, 5, 4], [1, 0, 3, 4, 2], [1, 0, 1, 3, 4, 5, 3], [1, 2, 0, 1, 3, 4, 4], [4, 0, 1, 3, 4, 5, 6, 2], [5, 1, 1], [3, 0, 1, 2, 6, 9, 10, 11, 5], [3, 0, 1, 3, 2, 4, 8, 5, 9, 10, 8], [7, 0, 1, 2, 3, 4, 5, 6, 6], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 4, 5, 1], [10, 0, 1, 2, 3, 4, 5, 6, 7, 1], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 3, 4, 5, 4], [14, 0, 1, 2, 1], [15, 0, 1, 1]], [[8, "dragonTigerHistoryView"], [9, "dragonTigerHistoryView", [-5, -6, -7, -8, -9, -10, -11], [[34, -2, [27, 28], 26], [35, -4, -3]], [24, -1]], [6, "title", 1, [-17, -18, -19, -20, -21, -22, -23], [[22, 1, 0, false, -12, [17]], [29, -16, -15, -14, -13, [4, 4278246399], [4, 4294829568]]], [0, "48aoerzs9J0KQDy24aOvh7", 1], [5, 994, 50], [0, 170.048, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "<PERSON><PERSON>", [-32, -33, -34, -35, -36, -37, -38], [[30, -31, -30, -29, -28, -27, -26, -25, -24]], [0, "e1RO1bFy9GXqoEREZGp73Q", 1], [5, 994, 62]], [16, "scrollview", 1, [-41, -42], [-39, -40], [0, "1epo55TkJFH7Il1JiIxOXI", 1], [5, 1050, 440], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-45], [[23, 3, -44, [[28, "a9398OWmM5LiKsVZpumOhGe", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -43]], [0, "f0tBL3UKhJqbndztf9vB5r", 1], [5, 80, 80], [512.847, 276.289, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "black", 100, 1, [[18, 0, -46, [0], 1], [7, -47, [4, 4292269782]]], [0, "14IYFa3aZNFaHKTHE1Pvh+", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "nen popup", 1, [[19, 1, 0, false, -48, [2], 3], [7, -49, [4, 4292269782]]], [0, "f75IaZcAlFCqkYqZpdAkDw", 1], [5, 1084, 618]], [6, "title_lschoi", 1, [-51], [[27, "default", "animation", 0, false, "animation", -50, [7]]], [0, "69TH0akRBANa5Cepp7lhrw", 1], [5, 270, 89.04], [0, 274.763, 0, 0, 0, 0, 1, 1.5, 1, 1]], [14, "view", 4, [-53], [[31, 0, -52, [25]]], [0, "67khkH9nZLVqtbYtjnBTzu", 1], [5, 1050, 440]], [1, "bg_content", 1, [[20, 0, -54, [4]]], [0, "0fLQgnsr9KepMPTFIlOZBD", 1], [5, 1020, 520], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Node", 8, [[25, "LỊCH SỬ CHƠI", 25, false, 2, -55, [5], 6]], [0, "a5wTosmUZDFJlUsVe0NvBF", 1], [5, 302.47, 31.25], [0, 39.371, 0, 0, 0, 0, 1, 0.7, 1, 1]], [1, "sprite", 5, [[21, 0, false, -56, [8], 9]], [0, "50Y7/rgHxDpq+ta7dV1ec+", 1], [5, 70, 70], [-3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbSession", 2, [-57], [0, "0davUFmzBNNLPV7iitEVAR", 1], [5, 150, 30], [-436, 25.952, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHIÊN", 15, 48, false, false, 1, 1, 1, 13, [10]], [4, "lbTime", 2, [-58], [0, "e5JKnF7IVF5JYxIn0tsR5L", 1], [5, 200, 38], [-277, 25.952, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THỜI GIAN", 15, 48, false, false, 1, 1, 1, 15, [11]], [4, "lbSide", 2, [-59], [0, "45ToP7i25PBZYby/bN2SIo", 1], [5, 200, 38], [-118, 25.952, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ĐẶT CỬA", 15, 48, false, false, 1, 1, 1, 17, [12]], [1, "lbResult", 2, [[2, "KẾT QUẢ", 15, 48, false, false, 1, 1, 1, -60, [13]]], [0, "74AGuu95xPP71rnpdgzLLA", 1], [5, 200, 38], [0, 25.952, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 2, [[2, "TỔNG ĐẶT", 15, 48, false, false, 1, 1, 1, -61, [14]]], [0, "0fCmPKVC1I6IrCLJdPWkcL", 1], [5, 200, 38], [136, 25.952, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRefund", 2, [[2, "HOÀN TRẢ", 15, 48, false, false, 1, 1, 1, -62, [15]]], [0, "c93iH9Q6lLjaMNcJP9bN2S", 1], [5, 200, 38], [271, 25.952, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTotalWin", 2, [[2, "THỰC NHẬN", 15, 48, false, false, 1, 1, 1, -63, [16]]], [0, "0fejNDZCNDIYe8TwEXl+0A", 1], [5, 200, 38], [412, 25.952, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "temp", 4, [3], [0, "bcQyI1jVxLOpTCbyPzYoO/", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSession", 3, [-64], [0, "e2rYZFKCRDZJjYTbBxRT7t", 1], [4, 4284344318], [5, 150, 30], [-436, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "PHIÊN", 22, 48, false, false, 1, 1, 1, 24, [18]], [4, "lbTime", 3, [-65], [0, "7fQqU7LdNCMZIKfRAEsp1k", 1], [5, 200, 38], [-277, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "THỜI GIAN", 22, 48, false, false, 1, 1, 1, 26, [19]], [4, "lbSide", 3, [-66], [0, "65gzmPm0JOCaOkxzmB1KwZ", 1], [5, 200, 38], [-118, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ĐẶT CỬA", 22, 48, false, false, 1, 1, 1, 28, [20]], [17, "lbResult", 3, [-67], [0, "f8QmVZqhtEz7UAi9CNtj7e", 1], [5, 200, 38]], [26, "KET QUA", 22, 48, false, 1, 1, 2, 30, [21]], [5, "lbBet", 3, [-68], [0, "4e3MJ6YOxMTIKfxd2vILNz", 1], [4, 4282969994], [5, 200, 38], [136, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "TIỀN ĐẶT", 22, 48, false, false, 1, 1, 1, 32, [22]], [5, "lbRefund", 3, [-69], [0, "e38izChKlPOZA/jAYCqsKH", 1], [4, 4284344318], [5, 200, 38], [271, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "HOÀN TRẢ", 22, 48, false, false, 1, 1, 1, 34, [23]], [5, "lbWin", 3, [-70], [0, "83YYKEvDdFj6rCkmWojtNP", 1], [4, 4284344318], [5, 200, 38], [412, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "HOÀN TRẢ", 22, 48, false, false, 1, 1, 1, 36, [24]], [15, "content", 9, [0, "6fu3jDAHBLkJ6j5RKi4DF1", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [32, false, 0.75, 0.23, null, null, 4, 38], [33, 20, 10, 400, 4, 3, 39]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 40, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 10, 0, -4, 8, 0, -5, 5, 0, -6, 2, 0, -7, 4, 0, 0, 2, 0, 3, 18, 0, 4, 16, 0, 8, 14, 0, 0, 2, 0, -1, 13, 0, -2, 15, 0, -3, 17, 0, -4, 19, 0, -5, 20, 0, -6, 21, 0, -7, 22, 0, 9, 37, 0, 10, 35, 0, 3, 33, 0, 11, 31, 0, 12, 29, 0, 4, 27, 0, 13, 25, 0, 0, 3, 0, -1, 24, 0, -2, 26, 0, -3, 28, 0, -4, 30, 0, -5, 32, 0, -6, 34, 0, -7, 36, 0, -1, 39, 0, -2, 40, 0, -1, 23, 0, -2, 9, 0, 14, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 11, 0, 0, 9, 0, -1, 38, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, -1, 14, 0, -1, 16, 0, -1, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, -1, 25, 0, -1, 27, 0, -1, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, -1, 37, 0, 15, 1, 3, 16, 23, 70], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 27, 29, 31, 33, 35, 37], [-1, 2, -1, 2, -1, -1, 1, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 5, -1, -2, 1, 1, 1, 1, 1, 1, 1], [0, 3, 0, 4, 0, 0, 5, 6, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 8, 1, 1, 1, 1, 1, 1, 1]]