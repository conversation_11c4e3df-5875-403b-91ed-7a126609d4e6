[1, ["c1LAFCXY5BD5ekKEHJsO2J"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "STTT-UI-anim", "\nSTTT-UI-anim.png\nsize: 292,1046\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nsp-Long2\n  rotate: true\n  xy: 194, 679\n  size: 44, 96\n  orig: 44, 96\n  offset: 0, 0\n  index: -1\nsp-Long3\n  rotate: false\n  xy: 242, 112\n  size: 43, 109\n  orig: 43, 109\n  offset: 0, 0\n  index: -1\nsp-bird\n  rotate: false\n  xy: 194, 569\n  size: 95, 106\n  orig: 95, 106\n  offset: 0, 0\n  index: -1\nsp-circleBright\n  rotate: false\n  xy: 175, 24\n  size: 58, 62\n  orig: 58, 62\n  offset: 0, 0\n  index: -1\nsp-giudequay\n  rotate: false\n  xy: 151, 412\n  size: 120, 61\n  orig: 120, 61\n  offset: 0, 0\n  index: -1\nsp-long1\n  rotate: true\n  xy: 154, 477\n  size: 88, 123\n  orig: 88, 123\n  offset: 0, 0\n  index: -1\nsp-long4\n  rotate: false\n  xy: 2, 433\n  size: 145, 121\n  orig: 145, 121\n  offset: 0, 0\n  index: -1\nsp-long5\n  rotate: false\n  xy: 2, 558\n  size: 148, 100\n  orig: 148, 100\n  offset: 0, 0\n  index: -1\nsp-long6\n  rotate: false\n  xy: 197, 875\n  size: 89, 169\n  orig: 89, 169\n  offset: 0, 0\n  index: -1\nsp-may1\n  rotate: true\n  xy: 242, 225\n  size: 105, 48\n  orig: 105, 48\n  offset: 0, 0\n  index: -1\nsp-may2\n  rotate: true\n  xy: 120, 2\n  size: 84, 51\n  orig: 84, 51\n  offset: 0, 0\n  index: -1\nsp-may3\n  rotate: true\n  xy: 2, 8\n  size: 331, 114\n  orig: 331, 114\n  offset: 0, 0\n  index: -1\nsp-quay\n  rotate: false\n  xy: 2, 343\n  size: 144, 86\n  orig: 144, 86\n  offset: 0, 0\n  index: -1\nsp-sangtron\n  rotate: false\n  xy: 150, 334\n  size: 119, 74\n  orig: 119, 74\n  offset: 0, 0\n  index: -1\nsp-stop\n  rotate: true\n  xy: 197, 727\n  size: 144, 88\n  orig: 144, 88\n  offset: 0, 0\n  index: -1\nsp-tron\n  rotate: false\n  xy: 2, 854\n  size: 191, 190\n  orig: 191, 190\n  offset: 0, 0\n  index: -1\nsp-tronDo\n  rotate: false\n  xy: 120, 212\n  size: 118, 118\n  orig: 118, 118\n  offset: 0, 0\n  index: -1\nsp-tronTim\n  rotate: false\n  xy: 120, 90\n  size: 118, 118\n  orig: 118, 118\n  offset: 0, 0\n  index: -1\nsp-wave\n  rotate: false\n  xy: 2, 662\n  size: 188, 188\n  orig: 188, 188\n  offset: 0, 0\n  index: -1\n", ["STTT-UI-anim.png"], {"skeleton": {"hash": "i2ytbMckhKjIlAFw+3BtnhLJUK4", "spine": "3.7.94", "width": 0, "height": 0, "images": "./images/", "audio": "/Volumes/Lulu/SonTinhThuyTinh/Spine/UI"}, "bones": [{"name": "root"}, {"name": "Spin", "parent": "root", "color": "ecfd0cff"}, {"name": "khungNgoai", "parent": "Spin", "color": "ecfd0cff"}, {"name": "khungTrong", "parent": "Spin", "color": "ecfd0cff"}, {"name": "quay-text", "parent": "Spin", "color": "ecfd0cff"}, {"name": "long", "parent": "Spin", "color": "ecfd0cff"}, {"name": "long1", "parent": "long", "x": -9.66, "y": 67.62, "color": "ecfd0cff"}, {"name": "long2", "parent": "long", "x": 8.69, "y": 73.42, "color": "ecfd0cff"}, {"name": "long3", "parent": "long", "x": 15.46, "y": 71.97, "color": "ecfd0cff"}, {"name": "long4", "parent": "long", "x": 25.6, "y": 62.79, "color": "ecfd0cff"}, {"name": "long5", "parent": "long", "x": 35.74, "y": 59.41, "color": "ecfd0cff"}, {"name": "long6", "parent": "long", "x": -17.39, "y": 60.37, "color": "ecfd0cff"}, {"name": "may1", "parent": "Spin", "x": 124.13, "y": 24.63, "color": "ecfd0cff"}, {"name": "may2", "parent": "Spin", "x": 94.67, "y": -52.16, "color": "ecfd0cff"}, {"name": "may3", "parent": "Spin", "x": -11.59, "y": -35.74, "color": "ecfd0cff"}, {"name": "bird", "parent": "Spin", "color": "ecfd0cff"}, {"name": "circle-bright", "parent": "Spin", "color": "ecfd0cff"}, {"name": "giu de quay", "parent": "Spin", "y": -38, "color": "ecfd0cff"}, {"name": "sang<PERSON>", "parent": "Spin", "x": -0.59, "y": 30.78, "color": "ecfd0cff"}, {"name": "wave", "parent": "Spin", "color": "ecfd0cff"}, {"name": "bone", "parent": "sang<PERSON>", "x": 4.16, "y": -6.86, "color": "ecfd0cff"}, {"name": "bone5", "parent": "wave", "color": "ecfd0cff"}], "slots": [{"name": "sp-long6", "bone": "long6"}, {"name": "sp-long5", "bone": "long5"}, {"name": "sp-long4", "bone": "long4"}, {"name": "sp-Long3", "bone": "long3"}, {"name": "sp-Long2", "bone": "long2"}, {"name": "sp-long1", "bone": "long1"}, {"name": "sp-may3", "bone": "may3"}, {"name": "sp-may2", "bone": "may2"}, {"name": "sp-may1", "bone": "may1"}, {"name": "sp-tron", "bone": "khungNgoai"}, {"name": "sp-circle<PERSON>right", "bone": "circle-bright"}, {"name": "sp-tronTim", "bone": "khungTrong"}, {"name": "sp-sangtron", "bone": "sang<PERSON>"}, {"name": "sp-giudequay", "bone": "giu de quay"}, {"name": "sp-quay", "bone": "quay-text"}, {"name": "sp-bird", "bone": "bird"}, {"name": "mask-wave", "bone": "wave"}, {"name": "sp-wave", "bone": "wave"}], "skins": {"default": {"mask-wave": {"mask-wave": {"type": "clipping", "end": "mask-wave", "vertexCount": 110, "color": "ce3a3aff", "vertices": [-93.06, 13.52, -93.85, 1.95, -93.07, -6.66, -92.55, -15.54, -90.2, -24.67, -86.02, -34.33, -80.28, -47.64, -74.54, -57.3, -68.01, -64.34, -59.4, -71.65, -52.88, -76.35, -44.52, -82.88, -34.87, -86.53, -24.43, -90.18, -10.59, -92.79, 3.5, -94.36, 13.42, -93.58, 28.03, -90.71, 40.04, -85.75, 48.91, -80.53, 58.31, -74, 68.49, -64.87, 75.54, -56.78, 80.76, -46.86, 86.5, -36.42, 90.15, -26.24, 92.76, -15.54, 94.33, -1.18, 94.07, 10.82, 91.72, 22.31, 89.11, 29.35, 85.19, 38.23, 81.02, 47.1, 74.75, 56.24, 69.01, 64.07, 63.53, 69.03, 56.22, 75.29, 48.39, 81.29, 39.78, 85.73, 28.82, 89.91, 19.94, 91.47, 7.68, 93.56, -0.68, 93.82, -12.68, 93.04, -22.08, 91.47, -31.47, 88.6, -40.61, 84.69, -48.96, 80.51, -56.79, 75.03, -63.32, 68.5, -69.84, 61.46, -74.54, 55.19, -79.76, 48.67, -84.72, 39.53, -87.27, 33.23, -89.36, 26.63, -92.85, 13.56, -57.57, 10.54, -54.91, 19.87, -50.24, 30.34, -45.73, 35.49, -41.22, 40.8, -35.91, 45.79, -29.63, 50.46, -23.19, 53.68, -17.07, 55.94, -8.22, 58.03, 0.32, 58.67, 9.82, 58.03, 16.74, 56.1, 25.59, 52.72, 32.68, 48.85, 40.24, 42.73, 45.4, 37.1, 48.62, 32.59, 51.84, 26.47, 55.06, 19.87, 56.99, 13.75, 58.28, 7.47, 58.44, 0.07, 58.12, -8.46, 56.67, -15.71, 53.77, -23.28, 50.55, -28.11, 47.49, -32.61, 43.79, -37.44, 39.28, -42.6, 35.41, -46.3, 31.39, -49.68, 24.95, -52.74, 19.48, -54.51, 13.36, -56.44, 6.92, -57.25, 0.64, -57.57, -5.96, -57.09, -11.6, -56.12, -17.39, -54.19, -23.19, -52.58, -29.31, -49.68, -35.75, -45.17, -40.74, -41.63, -44.12, -36.96, -48.79, -31.33, -52.17, -25.85, -54.42, -20.54, -56.36, -14.58, -57.8, -8.46, -58.29, -3.15, -58.13, 2.32, -57.44, 10.33]}}, "sp-Long2": {"sp-Long2": {"x": 7.24, "y": 37.67, "width": 44, "height": 96}}, "sp-Long3": {"sp-Long3": {"x": 12.07, "y": 59.89, "width": 43, "height": 109}}, "sp-bird": {"sp-bird": {"width": 95, "height": 106}}, "sp-circleBright": {"sp-circleBright": {"x": -44.81, "y": 57.24, "width": 58, "height": 62}}, "sp-giudequay": {"sp-giudequay": {"width": 120, "height": 61}}, "sp-long1": {"sp-long1": {"x": -35.26, "y": 13.52, "width": 88, "height": 123}}, "sp-long4": {"sp-long4": {"x": 36.71, "y": 36.22, "width": 145, "height": 121}}, "sp-long5": {"sp-long5": {"x": 32.36, "y": -14.97, "width": 148, "height": 100}}, "sp-long6": {"sp-long6": {"x": -2.9, "y": 11.11, "width": 89, "height": 169}}, "sp-may1": {"sp-may1": {"width": 105, "height": 48}}, "sp-may2": {"sp-may2": {"width": 84, "height": 51}}, "sp-may3": {"sp-may3": {"width": 331, "height": 114}}, "sp-quay": {"sp-quay": {"width": 144, "height": 86}, "sp-stop": {"width": 144, "height": 88}}, "sp-sangtron": {"sp-sangtron": {"type": "mesh", "hull": 6, "width": 119, "height": 74, "uvs": [1, 1, 0.52916, 1, 0, 1, 0, 0, 0.52916, 0, 1, 0, 0.17414, 0.30451, 0.17414, 0.74472, 0.52488, 0.24948, 0.52488, 0.77912, 0.8243, 0.29763, 0.82002, 0.77224], "triangles": [11, 10, 0, 8, 4, 10, 8, 6, 4, 10, 9, 8, 9, 6, 8, 6, 7, 2, 11, 9, 10, 7, 6, 9, 1, 9, 11, 7, 9, 1, 4, 6, 3, 10, 4, 5, 10, 5, 0, 6, 2, 3, 1, 11, 0, 2, 7, 1], "vertices": [2, 18, 59.83, -52.21, 0.59, 20, 59.24, -21.43, 0.41, 2, 18, 3.8, -52.21, 0.59, 20, 3.21, -21.43, 0.41, 2, 18, -59.31, -53.17, 0.59, 20, -59.91, -22.39, 0.41, 2, 18, -59.31, 20.83, 0.57, 20, -59.91, 51.61, 0.43, 2, 18, 3.87, 22.26, 0.57, 20, 3.28, 53.05, 0.43, 2, 18, 59.9, 22.26, 0.57, 20, 59.31, 53.05, 0.43, 1, 20, -41.75, 11.86, 1, 1, 20, -41.75, -20.72, 1, 1, 20, -0.01, 15.93, 1, 1, 20, -0.01, -23.27, 1, 1, 20, 35.62, 12.36, 1, 1, 20, 35.11, -22.76, 1], "edges": [4, 6, 0, 10, 6, 8, 8, 10, 0, 2, 2, 4]}}, "sp-tron": {"sp-tron": {"width": 191, "height": 190}}, "sp-tronTim": {"sp-tronDo": {"width": 118, "height": 118}, "sp-tronTim": {"width": 118, "height": 118}}, "sp-wave": {"sp-wave": {"type": "mesh", "hull": 4, "width": 188, "height": 188, "uvs": [1, 1, 0, 1, 0, 0, 1, 0, 0.11866, 0.26936, 0.5498, 0.27233, 0.89175, 0.2753, 0.90067, 0.57562, 0.53494, 0.56967, 0.12163, 0.58156, 0.10082, 0.89377, 0.54386, 0.88782, 0.89472, 0.88188, 0.3268, 0.48344, 0.71334, 0.45965, 0.72523, 0.75997, 0.32085, 0.77781, 0.3149, 0.60535, 0.72523, 0.59346], "triangles": [14, 5, 6, 13, 4, 5, 8, 13, 5, 14, 8, 5, 14, 6, 7, 9, 4, 13, 18, 14, 7, 8, 14, 18, 17, 9, 13, 17, 13, 8, 15, 18, 7, 8, 18, 15, 16, 17, 8, 12, 15, 7, 11, 8, 15, 16, 8, 11, 16, 10, 9, 16, 9, 17, 11, 15, 12, 10, 16, 11, 5, 4, 2, 3, 5, 2, 6, 5, 3, 7, 6, 3, 0, 7, 3, 9, 2, 4, 2, 9, 1, 10, 1, 9, 12, 7, 0, 0, 11, 12, 1, 10, 11, 1, 11, 0], "vertices": [1, 19, 94, -94, 1, 1, 19, -94, -94, 1, 1, 19, -94, 94, 1, 1, 19, 94, 94, 1, 2, 19, -71.69, 43.36, 0.48, 21, -71.69, 43.36, 0.52, 2, 19, 9.36, 42.8, 0.48, 21, 9.36, 42.8, 0.52, 2, 19, 73.65, 42.24, 0.48, 21, 73.65, 42.24, 0.52, 2, 19, 75.33, -14.22, 0.48, 21, 75.33, -14.22, 0.52, 1, 21, 6.57, -13.1, 1, 2, 19, -71.13, -15.33, 0.48, 21, -71.13, -15.33, 0.52, 2, 19, -75.05, -74.03, 0.48, 21, -75.05, -74.03, 0.52, 2, 19, 8.25, -72.91, 0.48, 21, 8.25, -72.91, 0.52, 2, 19, 74.21, -71.79, 0.48, 21, 74.21, -71.79, 0.52, 1, 21, -32.56, 3.11, 1, 1, 21, 40.11, 7.59, 1, 1, 21, 42.34, -48.87, 1, 1, 21, -33.68, -52.23, 1, 1, 21, -34.8, -19.81, 1, 1, 21, 42.34, -17.57, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}, "animations": {"Spin_quay": {"slots": {"mask-wave": {"attachment": [{"time": 0, "name": "mask-wave"}]}, "sp-Long2": {"attachment": [{"time": 0, "name": "sp-Long2"}]}, "sp-Long3": {"attachment": [{"time": 0, "name": "sp-Long3"}]}, "sp-bird": {"attachment": [{"time": 0, "name": "sp-bird"}]}, "sp-circleBright": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 7.5, "color": "ffffffff"}, {"time": 10, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "sp-circle<PERSON>right"}]}, "sp-giudequay": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "color": "ffffff7c", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "color": "ffffff7c", "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 8.2667, "color": "ffffff7c", "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "sp-giudequay"}]}, "sp-long1": {"attachment": [{"time": 0, "name": "sp-long1"}]}, "sp-long4": {"attachment": [{"time": 0, "name": "sp-long4"}]}, "sp-long5": {"attachment": [{"time": 0, "name": "sp-long5"}]}, "sp-long6": {"attachment": [{"time": 0, "name": "sp-long6"}]}, "sp-may1": {"attachment": [{"time": 0, "name": "sp-may1"}]}, "sp-may2": {"attachment": [{"time": 0, "name": "sp-may2"}]}, "sp-may3": {"attachment": [{"time": 0, "name": "sp-may3"}]}, "sp-quay": {"attachment": [{"time": 0, "name": "sp-quay"}]}, "sp-sangtron": {"attachment": [{"time": 0, "name": "sp-sangtron"}]}, "sp-tron": {"attachment": [{"time": 0, "name": "sp-tron"}]}, "sp-tronTim": {"attachment": [{"time": 0, "name": "sp-tronTim"}]}, "sp-wave": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff78"}, {"time": 4.2667, "color": "ffffffee"}, {"time": 5, "color": "ffffff78"}, {"time": 5.6667, "color": "ffffffee"}, {"time": 6.2667, "color": "ffffff78"}, {"time": 7.0333, "color": "ffffffee"}, {"time": 7.5, "color": "ffffffff"}, {"time": 8.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "sp-wave"}]}}, "bones": {"khungNgoai": {"rotate": [{"time": 0, "angle": 0}, {"time": 3.3333, "angle": -120}, {"time": 6.6667, "angle": 120}, {"time": 10, "angle": 0}]}, "long6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -4.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -3.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "angle": -4.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "angle": 0, "curve": "stepped"}, {"time": 4.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "angle": -4.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "angle": -1.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "angle": -1.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "angle": 0}]}, "long2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -1.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "angle": 0, "curve": "stepped"}, {"time": 3.7667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.4333, "angle": -5.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "angle": 0, "curve": "stepped"}, {"time": 5.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4, "angle": -1.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "angle": 0}]}, "long3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": -3.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "angle": 0, "curve": "stepped"}, {"time": 5.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3, "angle": -3.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1, "angle": 0}]}, "bird": {"rotate": [{"time": 0, "angle": 0}, {"time": 3.3333, "angle": -120}, {"time": 6.6667, "angle": 120}, {"time": 10, "angle": 0}]}, "circle-bright": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8333, "angle": 120}, {"time": 1.6667, "angle": -120}, {"time": 2.5, "angle": 0}, {"time": 3.3333, "angle": 120}, {"time": 4.1667, "angle": -120}, {"time": 5, "angle": 0}, {"time": 5.8333, "angle": 120}, {"time": 6.6667, "angle": -120}, {"time": 7.5, "angle": 0}, {"time": 8.3333, "angle": 120}, {"time": 9.1667, "angle": -120}, {"time": 10, "angle": 0}]}, "long1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -10.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -10.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "angle": -10.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5, "angle": 0}]}, "long4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 1.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "angle": -4.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "angle": -2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6, "angle": 0, "curve": "stepped"}, {"time": 5.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4, "angle": -2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4667, "angle": 0, "curve": "stepped"}, {"time": 7.9, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9333, "angle": -2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "angle": 0}]}, "long5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -1.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3, "angle": -4.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": -2.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8667, "angle": 2.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": 0, "curve": "stepped"}, {"time": 8.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5, "angle": 0}]}, "may1": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": -11, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0}]}, "may2": {"translate": [{"time": 0, "x": -2.93, "y": 0, "curve": [0.368, 0.47, 0.753, 1]}, {"time": 2.8, "x": -9, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8, "x": 0, "y": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 9.3333, "x": -1.75, "y": 0, "curve": [0.324, 0.3, 0.66, 0.65]}, {"time": 10, "x": -2.93, "y": 0}]}, "may3": {"translate": [{"time": 0, "x": -6.5, "y": 0, "curve": [0.38, 0.6, 0.725, 1]}, {"time": 1.2667, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.2667, "x": 0, "y": 0, "curve": [0.242, 0, 0.666, 0.66]}, {"time": 10, "x": -6.5, "y": 0}]}, "khungTrong": {"rotate": [{"time": 0, "angle": 0}, {"time": 3.3333, "angle": 120}, {"time": 6.6667, "angle": -120}, {"time": 10, "angle": 0}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 13.74, "y": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 13.87, "y": -44.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -21.8, "y": -1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": -28.72, "y": -38.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 13.74, "y": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 13.87, "y": -44.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": -21.8, "y": -1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": -28.72, "y": -38.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 13.74, "y": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 13.87, "y": -44.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": -21.8, "y": -1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": -28.72, "y": -38.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0}]}, "bone5": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 57.02, "y": -11.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 6.48, "y": -50.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "x": -29.67, "y": -5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.2667, "x": 57.02, "y": -11.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1, "x": 6.48, "y": -50.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "x": -29.67, "y": -5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6, "x": 57.02, "y": -11.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "x": 6.48, "y": -50.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2, "x": -29.67, "y": -5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0}]}}}, "Spin_stop": {"slots": {"mask-wave": {"attachment": [{"time": 0, "name": "mask-wave"}]}, "sp-Long2": {"attachment": [{"time": 0, "name": "sp-Long2"}]}, "sp-Long3": {"attachment": [{"time": 0, "name": "sp-Long3"}]}, "sp-bird": {"attachment": [{"time": 0, "name": "sp-bird"}]}, "sp-circleBright": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 2.5, "color": "ffffffff", "curve": "stepped"}, {"time": 7.5, "color": "ffffffff"}, {"time": 10, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "sp-circle<PERSON>right"}]}, "sp-long1": {"attachment": [{"time": 0, "name": "sp-long1"}]}, "sp-long4": {"attachment": [{"time": 0, "name": "sp-long4"}]}, "sp-long5": {"attachment": [{"time": 0, "name": "sp-long5"}]}, "sp-long6": {"attachment": [{"time": 0, "name": "sp-long6"}]}, "sp-may1": {"attachment": [{"time": 0, "name": "sp-may1"}]}, "sp-may2": {"attachment": [{"time": 0, "name": "sp-may2"}]}, "sp-may3": {"attachment": [{"time": 0, "name": "sp-may3"}]}, "sp-quay": {"attachment": [{"time": 0, "name": "sp-stop"}]}, "sp-sangtron": {"color": [{"time": 0, "color": "ffe800ff"}], "attachment": [{"time": 0, "name": "sp-sangtron"}]}, "sp-tron": {"attachment": [{"time": 0, "name": "sp-tron"}]}, "sp-tronTim": {"attachment": [{"time": 0, "name": "sp-tronDo"}]}, "sp-wave": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 3.5, "color": "ffffff78"}, {"time": 4.2667, "color": "ffffffee"}, {"time": 5, "color": "ffffff78"}, {"time": 5.6667, "color": "ffffffee"}, {"time": 6.2667, "color": "ffffff78"}, {"time": 7.0333, "color": "ffffffee"}, {"time": 7.5, "color": "ffffffff"}, {"time": 8.4, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "sp-wave"}]}}, "bones": {"khungNgoai": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": -120}, {"time": 3.3, "angle": 120}, {"time": 4.9667, "angle": 0}, {"time": 6.6667, "angle": -120}, {"time": 8.3, "angle": 120}, {"time": 10, "angle": 0}]}, "long6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -4.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -3.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "angle": -4.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.7333, "angle": 0, "curve": "stepped"}, {"time": 4.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.9333, "angle": -4.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "angle": -1.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.5667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "angle": -1.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "angle": 0}]}, "long2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -1.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "angle": 0, "curve": "stepped"}, {"time": 3.7667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.4333, "angle": -5.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1333, "angle": 0, "curve": "stepped"}, {"time": 5.8333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4, "angle": -1.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 7, "angle": 0}]}, "long3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": -3.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "angle": 0, "curve": "stepped"}, {"time": 5.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.3, "angle": -3.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.1, "angle": 0}]}, "bird": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": -120}, {"time": 3.3333, "angle": 120}, {"time": 5, "angle": 0}, {"time": 6.6667, "angle": -120}, {"time": 8.3333, "angle": 120}, {"time": 10, "angle": 0}]}, "circle-bright": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4333, "angle": 120}, {"time": 0.8333, "angle": -120}, {"time": 1.2667, "angle": 0}, {"time": 1.6667, "angle": 120}, {"time": 2.1, "angle": -120}, {"time": 2.5, "angle": 0}, {"time": 2.9333, "angle": 120}, {"time": 3.3667, "angle": -120}, {"time": 3.7667, "angle": 0}, {"time": 4.2, "angle": 120}, {"time": 4.6, "angle": -120}, {"time": 5.0333, "angle": 0}, {"time": 5.4667, "angle": 120}, {"time": 5.8667, "angle": -120}, {"time": 6.3, "angle": 0}, {"time": 6.7, "angle": 120}, {"time": 7.1333, "angle": -120}, {"time": 7.5333, "angle": 0}, {"time": 7.9667, "angle": 120}, {"time": 8.4, "angle": -120}, {"time": 8.8, "angle": 0}, {"time": 9.2333, "angle": 120}, {"time": 9.6333, "angle": -120}, {"time": 10.0667, "angle": 0}]}, "long1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -10.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -10.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "angle": -10.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.5, "angle": 0}]}, "long4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 1.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "angle": -4.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.9333, "angle": -2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6, "angle": 0, "curve": "stepped"}, {"time": 5.3667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.4, "angle": -2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.4667, "angle": 0, "curve": "stepped"}, {"time": 7.9, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.9333, "angle": -2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "angle": 0}]}, "long5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -1.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3, "angle": -4.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 4, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "angle": -2.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "angle": 0, "curve": "stepped"}, {"time": 6, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.8667, "angle": 2.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6667, "angle": 0, "curve": "stepped"}, {"time": 8.3, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.8667, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.5, "angle": 0}]}, "may1": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 5, "x": -11, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0}]}, "may2": {"translate": [{"time": 0, "x": -2.93, "y": 0, "curve": [0.368, 0.47, 0.753, 1]}, {"time": 2.8, "x": -9, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.8, "x": 0, "y": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 9.3333, "x": -1.75, "y": 0, "curve": [0.324, 0.3, 0.66, 0.65]}, {"time": 10, "x": -2.93, "y": 0}]}, "may3": {"translate": [{"time": 0, "x": -6.5, "y": 0, "curve": [0.38, 0.6, 0.725, 1]}, {"time": 1.2667, "x": -10, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.2667, "x": 0, "y": 0, "curve": [0.242, 0, 0.666, 0.66]}, {"time": 10, "x": -6.5, "y": 0}]}, "khungTrong": {"rotate": [{"time": 0, "angle": 0}, {"time": 3.3333, "angle": 120}, {"time": 6.6667, "angle": -120}, {"time": 10, "angle": 0}]}, "bone": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 13.74, "y": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 13.87, "y": -44.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -21.8, "y": -1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": -28.72, "y": -38.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4, "x": 13.74, "y": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.6667, "x": 13.87, "y": -44.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.3333, "x": -21.8, "y": -1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 6, "x": -28.72, "y": -38.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.3333, "x": 13.74, "y": -1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 8, "x": 13.87, "y": -44.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.6667, "x": -21.8, "y": -1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.3333, "x": -28.72, "y": -38.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0}]}, "bone5": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 57.02, "y": -11.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 6.48, "y": -50.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "x": -29.67, "y": -5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 4.2667, "x": 57.02, "y": -11.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.1, "x": 6.48, "y": -50.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 5.8667, "x": -29.67, "y": -5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 6.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 7.6, "x": 57.02, "y": -11.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 8.4333, "x": 6.48, "y": -50.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 9.2, "x": -29.67, "y": -5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 10, "x": 0, "y": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]