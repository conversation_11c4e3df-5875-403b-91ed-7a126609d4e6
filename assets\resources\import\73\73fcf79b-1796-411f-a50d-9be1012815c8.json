[1, ["54ygJPpBxMBK2Mv7yW7Znn"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "Tai<PERSON><PERSON>", "\nTaiXiu.png\nsize: 1889,1380\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nban\n  rotate: true\n  xy: 2, 217\n  size: 1161, 537\n  orig: 1161, 537\n  offset: 0, 0\n  index: -1\ncoin1\n  rotate: true\n  xy: 544, 148\n  size: 71, 52\n  orig: 71, 52\n  offset: 0, 0\n  index: -1\ncoin2\n  rotate: false\n  xy: 2, 4\n  size: 46, 33\n  orig: 46, 33\n  offset: 0, 0\n  index: -1\ncoin3\n  rotate: true\n  xy: 598, 164\n  size: 55, 51\n  orig: 55, 51\n  offset: 0, 0\n  index: -1\nelect/elec1_00000\n  rotate: false\n  xy: 345, 41\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nelect/elec1_00002\n  rotate: false\n  xy: 447, 41\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nelect/elec1_00004\n  rotate: false\n  xy: 541, 221\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nelect/elec1_00006\n  rotate: false\n  xy: 549, 46\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nelect/elec1_00009\n  rotate: false\n  xy: 643, 221\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nelect/elec1_00011\n  rotate: false\n  xy: 651, 119\n  size: 100, 100\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nke\n  rotate: true\n  xy: 541, 323\n  size: 1055, 475\n  orig: 1055, 475\n  offset: 0, 0\n  index: -1\nrong\n  rotate: true\n  xy: 1441, 459\n  size: 380, 389\n  orig: 380, 389\n  offset: 0, 0\n  index: -1\nset1\n  rotate: true\n  xy: 1441, 841\n  size: 537, 444\n  orig: 537, 444\n  offset: 0, 0\n  index: -1\nsieutoc\n  rotate: false\n  xy: 2, 124\n  size: 334, 91\n  orig: 334, 91\n  offset: 0, 0\n  index: -1\nsieutocblur\n  rotate: false\n  xy: 2, 39\n  size: 341, 83\n  orig: 341, 83\n  offset: 0, 0\n  index: -1\ntaixiu\n  rotate: false\n  xy: 338, 143\n  size: 204, 72\n  orig: 204, 72\n  offset: 0, 0\n  index: -1\ntron1\n  rotate: false\n  xy: 1536, 106\n  size: 351, 351\n  orig: 351, 351\n  offset: 0, 0\n  index: -1\ntron2\n  rotate: false\n  xy: 753, 2\n  size: 319, 319\n  orig: 319, 319\n  offset: 0, 0\n  index: -1\nupban\n  rotate: true\n  xy: 1018, 365\n  size: 1013, 421\n  orig: 1013, 421\n  offset: 0, 0\n  index: -1\nvuong\n  rotate: false\n  xy: 1074, 29\n  size: 460, 334\n  orig: 460, 334\n  offset: 0, 0\n  index: -1\n", ["TaiXiu.png"], {"skins": {"default": {"rong": {"rong": {"x": 5, "width": 380, "y": 4, "height": 389}}, "coin3": {"coin3": {"x": 2.52, "width": 55, "y": 4.58, "height": 51}}, "set2": {"set1": {"x": -8.55, "width": 537, "y": 25.97, "height": 444}}, "coin2": {"coin2": {"x": 1.85, "width": 46, "y": 2.05, "height": 33}}, "sieutoc2": {"sieutoc": {"x": -19.71, "width": 334, "y": 13.44, "height": 91}}, "coin1": {"coin1": {"x": 13.56, "width": 71, "y": 1, "height": 52}}, "vuong2": {"vuong": {"x": 18.36, "width": 460, "y": 16.01, "height": 334}}, "sieutocblur": {"sieutocblur": {"x": 33.18, "width": 341, "y": 15.05, "height": 83}}, "tron1": {"tron1": {"x": -6.58, "width": 351, "y": 17.33, "height": 351}}, "tron2": {"tron2": {"x": 0.02, "width": 319, "y": 0.03, "height": 319}}, "ban": {"ban": {"x": 0.01, "width": 1161, "y": -0.01, "height": 537}}, "tron3": {"tron2": {"x": 0.02, "width": 319, "y": 0.03, "height": 319}}, "elect/elect_01": {"elect/elec1_00006": {"width": 100, "height": 100}, "elect/elec1_00004": {"width": 100, "height": 100}, "elect/elec1_00009": {"width": 100, "height": 100}, "elect/elec1_00002": {"width": 100, "height": 100}, "elect/elec1_00000": {"width": 100, "height": 100}, "elect/elec1_00011": {"width": 100, "height": 100}}, "upban": {"upban": {"x": 0.01, "width": 1013, "y": -0.01, "height": 421}}, "taixiu": {"taixiu": {"x": 4.72, "width": 204, "y": 9.72, "height": 72}}, "taixiu2": {"taixiu": {"x": 4.72, "width": 204, "y": 9.72, "height": 72}}, "sieutocblur3": {"sieutocblur": {"x": 33.18, "width": 341, "y": 15.05, "height": 83}}, "sieutocblur2": {"sieutocblur": {"x": 33.18, "width": 341, "y": 15.05, "height": 83}}, "ke2": {"ke": {"x": 31.47, "width": 1055, "y": -16.86, "height": 475}}, "ke": {"ke": {"x": 31.47, "width": 1055, "y": -16.86, "height": 475}}, "set1": {"set1": {"x": 160.3, "width": 537, "y": -100.45, "height": 444}}, "vuong": {"vuong": {"x": 18.36, "width": 460, "y": 16.01, "height": 334}}, "sieutoc": {"sieutoc": {"x": -19.71, "width": 334, "y": 13.44, "height": 91}}}}, "skeleton": {"images": "./images/", "width": 1170.95, "spine": "3.6.53", "hash": "puhwSEJt89Y213G1bLcFOrG6Mm0", "height": 841.72}, "slots": [{"color": "ffffff00", "attachment": "set1", "blend": "additive", "name": "set2", "bone": "set2"}, {"color": "ffffff00", "attachment": "set1", "blend": "additive", "name": "set1", "bone": "set1"}, {"attachment": "ban", "name": "ban", "bone": "root"}, {"attachment": "vuong", "name": "vuong", "bone": "vuong"}, {"attachment": "vuong", "name": "vuong2", "bone": "vuong2"}, {"attachment": "ke", "blend": "additive", "name": "ke", "bone": "ke"}, {"attachment": "ke", "blend": "additive", "name": "ke2", "bone": "ke2"}, {"attachment": "upban", "name": "upban", "bone": "root"}, {"attachment": "tron1", "name": "tron1", "bone": "root"}, {"attachment": "tron2", "name": "tron2", "bone": "tron2"}, {"color": "ffffff00", "attachment": "tron2", "blend": "additive", "name": "tron3", "bone": "tron3"}, {"attachment": "coin1", "name": "coin1", "bone": "coin1"}, {"attachment": "coin2", "name": "coin2", "bone": "coin2"}, {"attachment": "coin3", "name": "coin3", "bone": "coin3"}, {"attachment": "sieutocblur", "name": "sieutocblur", "bone": "sieutocblur"}, {"attachment": "sieutocblur", "name": "sieutocblur2", "bone": "sieutocblur2"}, {"attachment": "sieutocblur", "name": "sieutocblur3", "bone": "sieutocblur3"}, {"attachment": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>"}, {"color": "ffffff00", "attachment": "<PERSON><PERSON><PERSON>", "blend": "additive", "name": "taixiu2", "bone": "<PERSON><PERSON><PERSON>"}, {"attachment": "sieutoc", "name": "sieutoc", "bone": "sieutoc"}, {"color": "ffffff00", "attachment": "sieutoc", "blend": "additive", "name": "sieutoc2", "bone": "sieutoc"}, {"attachment": "elect/elec1_00000", "blend": "additive", "name": "elect/elect_01", "bone": "elect"}, {"attachment": "rong", "name": "rong", "bone": "rong"}], "bones": [{"name": "root"}, {"parent": "root", "name": "coin1", "x": -82.56, "y": 299.4}, {"parent": "root", "name": "coin2", "x": -87.35, "y": 344.85}, {"parent": "root", "name": "coin3", "x": -155.52, "y": 323.32}, {"parent": "root", "name": "<PERSON><PERSON><PERSON>", "x": -184.22, "y": 276.68}, {"scaleX": 2, "parent": "<PERSON><PERSON><PERSON>", "scaleY": 2, "name": "elect", "x": -4.55, "y": 5.72}, {"parent": "root", "name": "ke", "x": -31.46, "y": 16.85}, {"scaleX": -1, "parent": "root", "name": "ke2", "x": -31.46, "y": 16.85}, {"parent": "root", "name": "rong", "x": -6.6, "y": 18.3}, {"parent": "root", "name": "set1", "x": -3.3, "y": 375.85}, {"scaleX": -1, "parent": "root", "scaleY": -1, "rotation": 13.65, "name": "set2", "x": -39.67, "y": 312.5}, {"parent": "root", "name": "sieutoc", "x": 105.21, "y": 281.46}, {"parent": "sieutoc", "name": "sieutocblur", "x": -63.39, "y": 2.39}, {"parent": "sieutoc", "name": "sieutocblur2", "x": -63.39, "y": 2.39}, {"parent": "sieutoc", "name": "sieutocblur3", "x": -63.39, "y": 2.39}, {"parent": "root", "name": "tron2", "x": -6.6, "y": 18.3}, {"parent": "root", "name": "tron3", "x": -6.6, "y": 18.3}, {"parent": "root", "name": "vuong", "x": -303.74, "y": -122.34}, {"parent": "root", "name": "vuong2", "x": 280.92, "y": 104.61}], "animations": {"animation_dragon_only": {"slots": {"coin3": {"color": [{"color": "ffffff00", "time": 0}]}, "set2": {"color": [{"color": "ffffff00", "time": 0}]}, "coin2": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutoc2": {"color": [{"color": "ffffff00", "time": 0}]}, "coin1": {"color": [{"color": "ffffff00", "time": 0}]}, "vuong2": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutocblur": {"color": [{"color": "ffffff00", "time": 0}]}, "tron1": {"color": [{"color": "ffffff00", "time": 0}]}, "tron2": {"color": [{"color": "ffffff00", "time": 0}]}, "ban": {"color": [{"color": "ffffff00", "time": 0}]}, "tron3": {"color": [{"color": "ffffff00", "time": 0}]}, "elect/elect_01": {"color": [{"color": "ffffff00", "time": 0}], "attachment": [{"name": null, "time": 0}]}, "upban": {"color": [{"color": "ffffff00", "time": 0}]}, "taixiu": {"color": [{"color": "ffffff00", "time": 0}]}, "taixiu2": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutocblur3": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutocblur2": {"color": [{"color": "ffffff00", "time": 0}]}, "ke2": {"color": [{"color": "ffffff00", "time": 0}]}, "ke": {"color": [{"color": "ffffff00", "time": 0}]}, "set1": {"color": [{"color": "ffffff00", "time": 0}]}, "vuong": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutoc": {"color": [{"color": "ffffff00", "time": 0}]}}, "bones": {"rong": {"rotate": [{"angle": 0, "time": 0}, {"angle": -120, "time": 0.6667}, {"angle": 120, "time": 1.3333}, {"angle": 0, "time": 2}], "scale": [{"x": -1, "y": 1, "time": 0}]}, "set2": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 0, "y": 0, "time": 0}], "translate": [{"x": -72.69, "y": -30.14, "time": 0}]}, "coin3": {"rotate": [{"angle": 3.52, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": -3.56, "time": 0}]}, "coin2": {"rotate": [{"angle": 23.08, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "coin1": {"rotate": [{"angle": 47.1, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": -4.69, "time": 0}]}, "vuong2": {"scale": [{"x": 1.078, "y": 1.078, "time": 0}], "translate": [{"x": 4.25, "y": 0, "time": 0}]}, "sieutocblur": {"translate": [{"x": -12.02, "y": 0, "time": 0}]}, "tron3": {"rotate": [{"angle": 0, "time": 0}]}, "taixiu": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "sieutocblur3": {"translate": [{"x": -30.49, "y": 0, "time": 0}]}, "sieutocblur2": {"translate": [{"x": -72.6, "y": 0, "time": 0}]}, "ke2": {"rotate": [{"angle": 2.34, "time": 0}], "scale": [{"x": 1.01, "y": 1.01, "time": 0}]}, "set1": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 0, "y": 0, "time": 0}], "translate": [{"x": -72.69, "y": -30.14, "time": 0}]}, "ke": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 0.904, "y": 0.904, "time": 0}]}, "vuong": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "sieutoc": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}}}, "animation_table_dragon": {"slots": {"elect/elect_01": {"attachment": [{"name": null, "time": 0}, {"name": "elect/elec1_00000", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "elect/elec1_00002", "time": 0.1333}, {"name": null, "time": 0.1667}, {"name": null, "time": 0.8333}, {"name": "elect/elec1_00000", "time": 0.8667}, {"name": null, "time": 0.9}, {"name": "elect/elec1_00002", "time": 0.9667}, {"name": null, "time": 1}, {"name": "elect/elec1_00004", "time": 1.0667}, {"name": null, "time": 1.1}, {"name": "elect/elec1_00006", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "elect/elec1_00009", "time": 1.2333}, {"name": null, "time": 1.2667}, {"name": "elect/elec1_00011", "time": 1.3}, {"name": null, "time": 1.3333}, {"name": null, "time": 1.6667}, {"name": "elect/elec1_00000", "time": 1.7}, {"name": null, "time": 1.7333}, {"name": "elect/elec1_00002", "time": 1.8}, {"name": null, "time": 1.8333}]}, "taixiu2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff89", "time": 0.0667}, {"color": "ffffff00", "curve": "stepped", "time": 0.1667}, {"color": "ffffff00", "time": 0.8}, {"color": "ffffff89", "time": 0.8667}, {"color": "ffffff00", "time": 0.9333}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.1}, {"color": "ffffffff", "time": 1.1667}, {"color": "ffffff00", "curve": "stepped", "time": 1.2667}, {"color": "ffffff00", "time": 1.6667}, {"color": "ffffffff", "time": 1.7333}, {"color": "ffffff00", "time": 1.8333}]}, "sieutocblur3": {"color": [{"color": "ffffffdf", "time": 0}, {"color": "ffffffff", "time": 0.0333}, {"color": "ffffff00", "curve": "stepped", "time": 0.2333}, {"color": "ffffff00", "time": 0.2667}, {"color": "ffffffff", "time": 0.5333}, {"color": "ffffff00", "curve": "stepped", "time": 0.7333}, {"color": "ffffff00", "time": 0.7667}, {"color": "ffffffff", "time": 1.0333}, {"color": "ffffff00", "curve": "stepped", "time": 1.2333}, {"color": "ffffff00", "time": 1.2667}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "curve": "stepped", "time": 1.7333}, {"color": "ffffff00", "time": 1.7667}, {"color": "ffffffdf", "time": 2}]}, "set2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.0667}, {"color": "ffffffb2", "curve": "stepped", "time": 0.1333}, {"color": "ffffff3a", "curve": "stepped", "time": 0.2}, {"color": "ffffffff", "curve": "stepped", "time": 0.2667}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff3a", "curve": "stepped", "time": 0.6}, {"color": "ffffff00", "curve": "stepped", "time": 0.7}, {"color": "ffffffff", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "curve": "stepped", "time": 0.9}, {"color": "ffffff3a", "curve": "stepped", "time": 1.2667}, {"color": "ffffffff", "curve": "stepped", "time": 1.3667}, {"color": "ffffff00", "time": 1.4667}]}, "sieutoc2": {"color": [{"color": "ffffff00", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff56", "time": 0.0667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.1333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 0.2, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "curve": "stepped", "time": 0.2667}, {"color": "ffffff00", "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff56", "time": 0.5667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.6333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 0.7, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.7667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "curve": "stepped", "time": 0.9}, {"color": "ffffff00", "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff56", "time": 1.5333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.6, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 1.6667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.7333}]}, "sieutocblur2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.2667}, {"color": "ffffff00", "curve": "stepped", "time": 0.4667}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.7667}, {"color": "ffffff00", "curve": "stepped", "time": 0.9667}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.4667}, {"color": "ffffff00", "time": 1.5}, {"color": "ffffffff", "time": 1.7667}, {"color": "ffffff00", "curve": "stepped", "time": 1.9667}, {"color": "ffffff00", "time": 2}]}, "ke2": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.9667}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 2}]}, "vuong2": {"color": [{"color": "ffffff7f", "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"color": "ffffffff", "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff71", "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"color": "ffffff7f", "time": 2}]}, "ke": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "curve": "stepped", "time": 1.9667}, {"color": "ffffff00", "time": 2}]}, "set1": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffffb2", "curve": "stepped", "time": 0.0667}, {"color": "ffffff3a", "curve": "stepped", "time": 0.1333}, {"color": "ffffffff", "curve": "stepped", "time": 0.2}, {"color": "ffffff00", "curve": "stepped", "time": 0.2667}, {"color": "ffffff3a", "curve": "stepped", "time": 0.5}, {"color": "ffffff00", "curve": "stepped", "time": 0.6}, {"color": "ffffffff", "curve": "stepped", "time": 0.7}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff3a", "curve": "stepped", "time": 1.1667}, {"color": "ffffffff", "curve": "stepped", "time": 1.2667}, {"color": "ffffff00", "time": 1.3667}]}, "vuong": {"color": [{"color": "ffffffff", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff71", "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "time": 2}]}, "tron3": {"color": [{"color": "ffffff00", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff72", "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 2}]}}, "bones": {"rong": {"rotate": [{"angle": 0, "time": 0}, {"angle": -120, "time": 0.6667}, {"angle": 120, "time": 1.3333}, {"angle": 0, "time": 2}], "scale": [{"x": -1, "y": 1, "time": 0}]}, "set2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.0667}, {"curve": "stepped", "angle": 1.9, "time": 0.1333}, {"curve": "stepped", "angle": 5.68, "time": 0.2}, {"curve": "stepped", "angle": -166.84, "time": 0.2667}, {"curve": "stepped", "angle": -149.68, "time": 0.3333}, {"curve": "stepped", "angle": 5.68, "time": 0.6}, {"curve": "stepped", "angle": 0, "time": 0.7}, {"curve": "stepped", "angle": 9.39, "time": 0.8}, {"curve": "stepped", "angle": 13.12, "time": 0.9}, {"curve": "stepped", "angle": 5.68, "time": 1.2667}, {"curve": "stepped", "angle": -160.78, "time": 1.3667}, {"angle": -149.68, "time": 1.4667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.0667}, {"curve": "stepped", "x": 0.927, "y": 0.927, "time": 0.1333}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.2}, {"curve": "stepped", "x": 1.305, "y": 1.305, "time": 0.2667}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.6}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.7}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.8}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.9}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 1.2667}, {"curve": "stepped", "x": 1.305, "y": 1.305, "time": 1.3667}, {"x": 0, "y": 0, "time": 1.4667}], "translate": [{"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.0667}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.2}, {"curve": "stepped", "x": 156.85, "y": -68.37, "time": 0.2667}, {"curve": "stepped", "x": 81.34, "y": -42.25, "time": 0.3333}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.6}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.7}, {"curve": "stepped", "x": -169.14, "y": -15.89, "time": 0.8}, {"curve": "stepped", "x": -123.92, "y": -11.13, "time": 0.9}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 1.2667}, {"curve": "stepped", "x": 158.74, "y": -124.24, "time": 1.3667}, {"x": 81.34, "y": -42.25, "time": 1.4667}]}, "coin3": {"rotate": [{"angle": 3.52, "time": 0, "curve": [0.3, 0.21, 0.756, 1]}, {"angle": 50.62, "time": 0.4333, "curve": [0.245, 0, 0.637, 0.56]}, {"angle": 18.62, "time": 0.7333, "curve": [0.381, 0.55, 0.742, 1]}, {"angle": 0, "time": 0.9333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 1.4333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.9333, "curve": [0.297, 0, 0.634, 0.37]}, {"angle": 3.52, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": -3.56, "time": 0, "curve": [0.378, 0.52, 0.748, 1]}, {"x": 0, "y": -6.55, "time": 0.2333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.7333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 1.2333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.7333, "curve": [0.248, 0, 0.628, 0.52]}, {"x": 0, "y": -3.56, "time": 2}]}, "coin2": {"rotate": [{"angle": 23.08, "time": 0, "curve": [0.378, 0.52, 0.748, 1]}, {"angle": 0, "time": 0.2333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 0.7333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.2333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 1.7333, "curve": [0.248, 0, 0.628, 0.52]}, {"angle": 23.08, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "coin1": {"rotate": [{"angle": 47.1, "time": 0, "curve": [0.366, 0.63, 0.703, 1]}, {"angle": 50.62, "time": 0.0667, "curve": [0.248, 0, 0.628, 0.52]}, {"angle": 23.08, "time": 0.3333, "curve": [0.378, 0.52, 0.748, 1]}, {"angle": 0, "time": 0.5667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 1.0667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.5667, "curve": [0.244, 0, 0.7, 0.79]}, {"angle": 47.1, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": -4.69, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"x": 0, "y": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 1.8333, "curve": [0.265, 0, 0.618, 0.43]}, {"x": 0, "y": -4.69, "time": 2}]}, "vuong2": {"scale": [{"x": 1.078, "y": 1.078, "time": 0, "curve": [0.359, 0.43, 0.756, 1]}, {"x": 1, "y": 1, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.118, "y": 1.118, "time": 1.5, "curve": [0.26, 0, 0.618, 0.44]}, {"x": 1.078, "y": 1.078, "time": 2}], "translate": [{"x": 4.25, "y": 0, "time": 0, "curve": [0.359, 0.43, 0.756, 1]}, {"x": 0, "y": 0, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 6.4, "y": 0, "time": 1.5, "curve": [0.26, 0, 0.618, 0.44]}, {"x": 4.25, "y": 0, "time": 2}]}, "sieutocblur": {"translate": [{"x": -12.02, "y": 0, "time": 0, "curve": [0.38, 0.59, 0.727, 1]}, {"x": -14.76, "y": 0, "time": 0.1333, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.23, "y": 0, "time": 0.6333, "curve": [0.25, 0, 0.75, 1]}, {"x": -14.76, "y": 0, "time": 1.1333, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.23, "y": 0, "time": 1.6333, "curve": [0.242, 0, 0.663, 0.65]}, {"x": -12.02, "y": 0, "time": 2}]}, "tron3": {"rotate": [{"angle": 0, "time": 0}, {"angle": -120, "time": 0.6667}, {"angle": 120, "time": 1.3333}, {"angle": 0, "time": 2}]}, "taixiu": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.088, "y": 1.088, "time": 0.4, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.059, "y": 1.059, "time": 0.8667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.9333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.059, "y": 1.059, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.0667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.059, "y": 1.059, "time": 1.1333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.088, "y": 1.088, "time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 10.41, "y": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 10.41, "y": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "sieutocblur3": {"translate": [{"x": -30.49, "y": 0, "time": 0}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.2333}, {"x": -72.6, "y": 0, "time": 0.2667}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.7333}, {"x": -72.6, "y": 0, "time": 0.7667}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.2333}, {"x": -72.6, "y": 0, "time": 1.2667}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.7333}, {"x": -72.6, "y": 0, "time": 1.7667}, {"x": -30.49, "y": 0, "time": 2}]}, "sieutocblur2": {"translate": [{"x": -72.6, "y": 0, "time": 0}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.4667}, {"x": -72.6, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.9667}, {"x": -72.6, "y": 0, "time": 1}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.4667}, {"x": -72.6, "y": 0, "time": 1.5}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.9667}, {"x": 11.62, "y": 0, "time": 2}]}, "ke2": {"rotate": [{"angle": 2.34, "time": 0}, {"curve": "stepped", "angle": 4.61, "time": 0.9667}, {"angle": 0, "time": 1}, {"angle": 2.34, "time": 2}], "scale": [{"x": 1.01, "y": 1.01, "time": 0}, {"curve": "stepped", "x": 1.112, "y": 1.112, "time": 0.9667}, {"x": 0.904, "y": 0.904, "time": 1}, {"x": 1.01, "y": 1.01, "time": 2}]}, "set1": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 1.9, "time": 0.0667}, {"curve": "stepped", "angle": 5.68, "time": 0.1333}, {"curve": "stepped", "angle": -149.48, "time": 0.2}, {"curve": "stepped", "angle": -172.28, "time": 0.2667}, {"curve": "stepped", "angle": 5.68, "time": 0.5}, {"curve": "stepped", "angle": 0, "time": 0.6}, {"curve": "stepped", "angle": 3.78, "time": 0.7}, {"curve": "stepped", "angle": 13.12, "time": 0.8}, {"curve": "stepped", "angle": 5.68, "time": 1.1667}, {"curve": "stepped", "angle": -139.36, "time": 1.2667}, {"angle": -149.68, "time": 1.3667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0.927, "y": 0.927, "time": 0.0667}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.1333}, {"curve": "stepped", "x": 0.957, "y": 0.957, "time": 0.2}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.2667}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.7}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.8}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 1.1667}, {"curve": "stepped", "x": 1.305, "y": 1.305, "time": 1.2667}, {"x": 0, "y": 0, "time": 1.3667}], "translate": [{"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.1333}, {"curve": "stepped", "x": 12.57, "y": -57.85, "time": 0.2}, {"curve": "stepped", "x": 81.34, "y": -42.25, "time": 0.2667}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.5}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.6}, {"curve": "stepped", "x": -374.85, "y": -32.63, "time": 0.7}, {"curve": "stepped", "x": -123.92, "y": -11.13, "time": 0.8}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 1.1667}, {"curve": "stepped", "x": 187.65, "y": -24.43, "time": 1.2667}, {"x": 81.34, "y": -42.25, "time": 1.3667}]}, "ke": {"rotate": [{"angle": 0, "time": 0}, {"curve": "stepped", "angle": 4.61, "time": 1.9667}, {"angle": 0, "time": 2}], "scale": [{"x": 0.904, "y": 0.904, "time": 0}, {"curve": "stepped", "x": 1.112, "y": 1.112, "time": 1.9667}, {"x": 0.904, "y": 0.904, "time": 2}]}, "vuong": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.118, "y": 1.118, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 6.4, "y": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "sieutoc": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 30.03, "y": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 30.03, "y": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}}}, "animation_table_nodragon": {"slots": {"rong": {"color": [{"color": "ffffff00", "time": 0}]}, "set2": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.0667}, {"color": "ffffffb2", "curve": "stepped", "time": 0.1333}, {"color": "ffffff3a", "curve": "stepped", "time": 0.2}, {"color": "ffffffff", "curve": "stepped", "time": 0.2667}, {"color": "ffffff00", "curve": "stepped", "time": 0.3333}, {"color": "ffffff3a", "curve": "stepped", "time": 0.6}, {"color": "ffffff00", "curve": "stepped", "time": 0.7}, {"color": "ffffffff", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "curve": "stepped", "time": 0.9}, {"color": "ffffff3a", "curve": "stepped", "time": 1.2667}, {"color": "ffffffff", "curve": "stepped", "time": 1.3667}, {"color": "ffffff00", "time": 1.4667}]}, "sieutoc2": {"color": [{"color": "ffffff00", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff56", "time": 0.0667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.1333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 0.2, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "curve": "stepped", "time": 0.2667}, {"color": "ffffff00", "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff56", "time": 0.5667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.6333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 0.7, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 0.7667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "curve": "stepped", "time": 0.9}, {"color": "ffffff00", "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff56", "time": 1.5333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.6, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff9e", "time": 1.6667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.7333}]}, "vuong2": {"color": [{"color": "ffffff7f", "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"color": "ffffffff", "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff71", "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"color": "ffffff7f", "time": 2}]}, "tron3": {"color": [{"color": "ffffff00", "time": 0}]}, "elect/elect_01": {"attachment": [{"name": null, "time": 0}, {"name": "elect/elec1_00000", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "elect/elec1_00002", "time": 0.1333}, {"name": null, "time": 0.1667}, {"name": null, "time": 0.8333}, {"name": "elect/elec1_00000", "time": 0.8667}, {"name": null, "time": 0.9}, {"name": "elect/elec1_00002", "time": 0.9667}, {"name": null, "time": 1}, {"name": "elect/elec1_00004", "time": 1.0667}, {"name": null, "time": 1.1}, {"name": "elect/elec1_00006", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "elect/elec1_00009", "time": 1.2333}, {"name": null, "time": 1.2667}, {"name": "elect/elec1_00011", "time": 1.3}, {"name": null, "time": 1.3333}, {"name": null, "time": 1.6667}, {"name": "elect/elec1_00000", "time": 1.7}, {"name": null, "time": 1.7333}, {"name": "elect/elec1_00002", "time": 1.8}, {"name": null, "time": 1.8333}]}, "taixiu2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffff89", "time": 0.0667}, {"color": "ffffff00", "curve": "stepped", "time": 0.1667}, {"color": "ffffff00", "time": 0.8}, {"color": "ffffff89", "time": 0.8667}, {"color": "ffffff00", "time": 0.9333}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.1}, {"color": "ffffffff", "time": 1.1667}, {"color": "ffffff00", "curve": "stepped", "time": 1.2667}, {"color": "ffffff00", "time": 1.6667}, {"color": "ffffffff", "time": 1.7333}, {"color": "ffffff00", "time": 1.8333}]}, "sieutocblur3": {"color": [{"color": "ffffffdf", "time": 0}, {"color": "ffffffff", "time": 0.0333}, {"color": "ffffff00", "curve": "stepped", "time": 0.2333}, {"color": "ffffff00", "time": 0.2667}, {"color": "ffffffff", "time": 0.5333}, {"color": "ffffff00", "curve": "stepped", "time": 0.7333}, {"color": "ffffff00", "time": 0.7667}, {"color": "ffffffff", "time": 1.0333}, {"color": "ffffff00", "curve": "stepped", "time": 1.2333}, {"color": "ffffff00", "time": 1.2667}, {"color": "ffffffff", "time": 1.5333}, {"color": "ffffff00", "curve": "stepped", "time": 1.7333}, {"color": "ffffff00", "time": 1.7667}, {"color": "ffffffdf", "time": 2}]}, "sieutocblur2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.2667}, {"color": "ffffff00", "curve": "stepped", "time": 0.4667}, {"color": "ffffff00", "time": 0.5}, {"color": "ffffffff", "time": 0.7667}, {"color": "ffffff00", "curve": "stepped", "time": 0.9667}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 1.2667}, {"color": "ffffff00", "curve": "stepped", "time": 1.4667}, {"color": "ffffff00", "time": 1.5}, {"color": "ffffffff", "time": 1.7667}, {"color": "ffffff00", "curve": "stepped", "time": 1.9667}, {"color": "ffffff00", "time": 2}]}, "ke2": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.9667}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 2}]}, "ke": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "curve": "stepped", "time": 1.9667}, {"color": "ffffff00", "time": 2}]}, "set1": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffffb2", "curve": "stepped", "time": 0.0667}, {"color": "ffffff3a", "curve": "stepped", "time": 0.1333}, {"color": "ffffffff", "curve": "stepped", "time": 0.2}, {"color": "ffffff00", "curve": "stepped", "time": 0.2667}, {"color": "ffffff3a", "curve": "stepped", "time": 0.5}, {"color": "ffffff00", "curve": "stepped", "time": 0.6}, {"color": "ffffffff", "curve": "stepped", "time": 0.7}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff3a", "curve": "stepped", "time": 1.1667}, {"color": "ffffffff", "curve": "stepped", "time": 1.2667}, {"color": "ffffff00", "time": 1.3667}]}, "vuong": {"color": [{"color": "ffffffff", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff71", "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "time": 2}]}}, "bones": {"set2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.0667}, {"curve": "stepped", "angle": 1.9, "time": 0.1333}, {"curve": "stepped", "angle": 5.68, "time": 0.2}, {"curve": "stepped", "angle": -166.84, "time": 0.2667}, {"curve": "stepped", "angle": -149.68, "time": 0.3333}, {"curve": "stepped", "angle": 5.68, "time": 0.6}, {"curve": "stepped", "angle": 0, "time": 0.7}, {"curve": "stepped", "angle": 9.39, "time": 0.8}, {"curve": "stepped", "angle": 13.12, "time": 0.9}, {"curve": "stepped", "angle": 5.68, "time": 1.2667}, {"curve": "stepped", "angle": -160.78, "time": 1.3667}, {"angle": -149.68, "time": 1.4667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.0667}, {"curve": "stepped", "x": 0.927, "y": 0.927, "time": 0.1333}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.2}, {"curve": "stepped", "x": 1.305, "y": 1.305, "time": 0.2667}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.6}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.7}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.8}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.9}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 1.2667}, {"curve": "stepped", "x": 1.305, "y": 1.305, "time": 1.3667}, {"x": 0, "y": 0, "time": 1.4667}], "translate": [{"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.0667}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.2}, {"curve": "stepped", "x": 156.85, "y": -68.37, "time": 0.2667}, {"curve": "stepped", "x": 81.34, "y": -42.25, "time": 0.3333}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.6}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.7}, {"curve": "stepped", "x": -169.14, "y": -15.89, "time": 0.8}, {"curve": "stepped", "x": -123.92, "y": -11.13, "time": 0.9}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 1.2667}, {"curve": "stepped", "x": 158.74, "y": -124.24, "time": 1.3667}, {"x": 81.34, "y": -42.25, "time": 1.4667}]}, "coin3": {"rotate": [{"angle": 3.52, "time": 0, "curve": [0.3, 0.21, 0.756, 1]}, {"angle": 50.62, "time": 0.4333, "curve": [0.245, 0, 0.637, 0.56]}, {"angle": 18.62, "time": 0.7333, "curve": [0.381, 0.55, 0.742, 1]}, {"angle": 0, "time": 0.9333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 1.4333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.9333, "curve": [0.297, 0, 0.634, 0.37]}, {"angle": 3.52, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": -3.56, "time": 0, "curve": [0.378, 0.52, 0.748, 1]}, {"x": 0, "y": -6.55, "time": 0.2333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.7333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 1.2333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.7333, "curve": [0.248, 0, 0.628, 0.52]}, {"x": 0, "y": -3.56, "time": 2}]}, "coin2": {"rotate": [{"angle": 23.08, "time": 0, "curve": [0.378, 0.52, 0.748, 1]}, {"angle": 0, "time": 0.2333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 0.7333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.2333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 1.7333, "curve": [0.248, 0, 0.628, 0.52]}, {"angle": 23.08, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "coin1": {"rotate": [{"angle": 47.1, "time": 0, "curve": [0.366, 0.63, 0.703, 1]}, {"angle": 50.62, "time": 0.0667, "curve": [0.248, 0, 0.628, 0.52]}, {"angle": 23.08, "time": 0.3333, "curve": [0.378, 0.52, 0.748, 1]}, {"angle": 0, "time": 0.5667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 50.62, "time": 1.0667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.5667, "curve": [0.244, 0, 0.7, 0.79]}, {"angle": 47.1, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": -4.69, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"x": 0, "y": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -6.55, "time": 1.8333, "curve": [0.265, 0, 0.618, 0.43]}, {"x": 0, "y": -4.69, "time": 2}]}, "vuong2": {"scale": [{"x": 1.078, "y": 1.078, "time": 0, "curve": [0.359, 0.43, 0.756, 1]}, {"x": 1, "y": 1, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.118, "y": 1.118, "time": 1.5, "curve": [0.26, 0, 0.618, 0.44]}, {"x": 1.078, "y": 1.078, "time": 2}], "translate": [{"x": 4.25, "y": 0, "time": 0, "curve": [0.359, 0.43, 0.756, 1]}, {"x": 0, "y": 0, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 6.4, "y": 0, "time": 1.5, "curve": [0.26, 0, 0.618, 0.44]}, {"x": 4.25, "y": 0, "time": 2}]}, "sieutocblur": {"translate": [{"x": -12.02, "y": 0, "time": 0, "curve": [0.38, 0.59, 0.727, 1]}, {"x": -14.76, "y": 0, "time": 0.1333, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.23, "y": 0, "time": 0.6333, "curve": [0.25, 0, 0.75, 1]}, {"x": -14.76, "y": 0, "time": 1.1333, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.23, "y": 0, "time": 1.6333, "curve": [0.242, 0, 0.663, 0.65]}, {"x": -12.02, "y": 0, "time": 2}]}, "tron3": {"rotate": [{"angle": 0, "time": 0}]}, "taixiu": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.088, "y": 1.088, "time": 0.4, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.059, "y": 1.059, "time": 0.8667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.9333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.059, "y": 1.059, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.0667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.059, "y": 1.059, "time": 1.1333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.088, "y": 1.088, "time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 10.41, "y": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 10.41, "y": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "sieutocblur3": {"translate": [{"x": -30.49, "y": 0, "time": 0}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.2333}, {"x": -72.6, "y": 0, "time": 0.2667}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.7333}, {"x": -72.6, "y": 0, "time": 0.7667}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.2333}, {"x": -72.6, "y": 0, "time": 1.2667}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.7333}, {"x": -72.6, "y": 0, "time": 1.7667}, {"x": -30.49, "y": 0, "time": 2}]}, "sieutocblur2": {"translate": [{"x": -72.6, "y": 0, "time": 0}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.4667}, {"x": -72.6, "y": 0, "time": 0.5}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 0.9667}, {"x": -72.6, "y": 0, "time": 1}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.4667}, {"x": -72.6, "y": 0, "time": 1.5}, {"curve": "stepped", "x": 11.62, "y": 0, "time": 1.9667}, {"x": 11.62, "y": 0, "time": 2}]}, "ke2": {"rotate": [{"angle": 2.34, "time": 0}, {"curve": "stepped", "angle": 4.61, "time": 0.9667}, {"angle": 0, "time": 1}, {"angle": 2.34, "time": 2}], "scale": [{"x": 1.01, "y": 1.01, "time": 0}, {"curve": "stepped", "x": 1.112, "y": 1.112, "time": 0.9667}, {"x": 0.904, "y": 0.904, "time": 1}, {"x": 1.01, "y": 1.01, "time": 2}]}, "set1": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 1.9, "time": 0.0667}, {"curve": "stepped", "angle": 5.68, "time": 0.1333}, {"curve": "stepped", "angle": -149.48, "time": 0.2}, {"curve": "stepped", "angle": -172.28, "time": 0.2667}, {"curve": "stepped", "angle": 5.68, "time": 0.5}, {"curve": "stepped", "angle": 0, "time": 0.6}, {"curve": "stepped", "angle": 3.78, "time": 0.7}, {"curve": "stepped", "angle": 13.12, "time": 0.8}, {"curve": "stepped", "angle": 5.68, "time": 1.1667}, {"curve": "stepped", "angle": -139.36, "time": 1.2667}, {"angle": -149.68, "time": 1.3667}], "scale": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0.927, "y": 0.927, "time": 0.0667}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.1333}, {"curve": "stepped", "x": 0.957, "y": 0.957, "time": 0.2}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.2667}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 0.5}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.7}, {"curve": "stepped", "x": 1.021, "y": 1.021, "time": 0.8}, {"curve": "stepped", "x": 0.68, "y": 0.774, "time": 1.1667}, {"curve": "stepped", "x": 1.305, "y": 1.305, "time": 1.2667}, {"x": 0, "y": 0, "time": 1.3667}], "translate": [{"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.1333}, {"curve": "stepped", "x": 12.57, "y": -57.85, "time": 0.2}, {"curve": "stepped", "x": 81.34, "y": -42.25, "time": 0.2667}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.5}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 0.6}, {"curve": "stepped", "x": -374.85, "y": -32.63, "time": 0.7}, {"curve": "stepped", "x": -123.92, "y": -11.13, "time": 0.8}, {"curve": "stepped", "x": -72.69, "y": -30.14, "time": 1.1667}, {"curve": "stepped", "x": 187.65, "y": -24.43, "time": 1.2667}, {"x": 81.34, "y": -42.25, "time": 1.3667}]}, "ke": {"rotate": [{"angle": 0, "time": 0}, {"curve": "stepped", "angle": 4.61, "time": 1.9667}, {"angle": 0, "time": 2}], "scale": [{"x": 0.904, "y": 0.904, "time": 0}, {"curve": "stepped", "x": 1.112, "y": 1.112, "time": 1.9667}, {"x": 0.904, "y": 0.904, "time": 2}]}, "vuong": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.118, "y": 1.118, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 6.4, "y": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "sieutoc": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 30.03, "y": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 30.03, "y": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}}}, "animation_dragon_only_x2speed": {"slots": {"coin3": {"color": [{"color": "ffffff00", "time": 0}]}, "set2": {"color": [{"color": "ffffff00", "time": 0}]}, "coin2": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutoc2": {"color": [{"color": "ffffff00", "time": 0}]}, "coin1": {"color": [{"color": "ffffff00", "time": 0}]}, "vuong2": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutocblur": {"color": [{"color": "ffffff00", "time": 0}]}, "tron1": {"color": [{"color": "ffffff00", "time": 0}]}, "tron2": {"color": [{"color": "ffffff00", "time": 0}]}, "ban": {"color": [{"color": "ffffff00", "time": 0}]}, "tron3": {"color": [{"color": "ffffff00", "time": 0}]}, "elect/elect_01": {"color": [{"color": "ffffff00", "time": 0}], "attachment": [{"name": null, "time": 0}]}, "upban": {"color": [{"color": "ffffff00", "time": 0}]}, "taixiu": {"color": [{"color": "ffffff00", "time": 0}]}, "taixiu2": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutocblur3": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutocblur2": {"color": [{"color": "ffffff00", "time": 0}]}, "ke2": {"color": [{"color": "ffffff00", "time": 0}]}, "ke": {"color": [{"color": "ffffff00", "time": 0}]}, "set1": {"color": [{"color": "ffffff00", "time": 0}]}, "vuong": {"color": [{"color": "ffffff00", "time": 0}]}, "sieutoc": {"color": [{"color": "ffffff00", "time": 0}]}}, "bones": {"rong": {"rotate": [{"angle": 0, "time": 0}, {"angle": -120, "time": 0.2333}, {"angle": 120, "time": 0.4333}, {"angle": 0, "time": 0.6667}], "scale": [{"x": -1, "y": 1, "time": 0}]}, "set2": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 0, "y": 0, "time": 0}], "translate": [{"x": -72.69, "y": -30.14, "time": 0}]}, "coin3": {"rotate": [{"angle": 3.52, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": -3.56, "time": 0}]}, "coin2": {"rotate": [{"angle": 23.08, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "coin1": {"rotate": [{"angle": 47.1, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": -4.69, "time": 0}]}, "vuong2": {"scale": [{"x": 1.078, "y": 1.078, "time": 0}], "translate": [{"x": 4.25, "y": 0, "time": 0}]}, "sieutocblur": {"translate": [{"x": -12.02, "y": 0, "time": 0}]}, "tron3": {"rotate": [{"angle": 0, "time": 0}]}, "taixiu": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "sieutocblur3": {"translate": [{"x": -30.49, "y": 0, "time": 0}]}, "sieutocblur2": {"translate": [{"x": -72.6, "y": 0, "time": 0}]}, "ke2": {"rotate": [{"angle": 2.34, "time": 0}], "scale": [{"x": 1.01, "y": 1.01, "time": 0}]}, "set1": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 0, "y": 0, "time": 0}], "translate": [{"x": -72.69, "y": -30.14, "time": 0}]}, "ke": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 0.904, "y": 0.904, "time": 0}]}, "vuong": {"scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "sieutoc": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]