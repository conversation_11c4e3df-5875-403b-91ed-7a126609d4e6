[1, ["ecpdLyjvZBwrvm+cedCcQy", "8dBdviayBJHLH0Sjugg3TC", "2cCtbdyyJB+qdz5oG5pm9Z", "afg7RRgyZP77/ZmrmjWi11", "16MoCcHttPWK6DqmKYJBjO", "52FOh2/J9C+Z6rMAahvA5W", "351xrxKgVJXZsnC6iAYL2E", "96OETKS8tJC66H+C8qjvNt", "26oXcstdFLWI12mkoBa80A", "8e0O+B2sVPoapru+3GQz1d", "c1/bTKqSlKcZ/S0P4czV61", "33bO/gpx5Oo7uzwGg/PZv6", "39B2kKrg1LnoqpFMbJ3h4b", "75iSVU8X5HC4iwjqUmmYnK", "624nhmWNBD44oFvUajAP0P", "b8wKMeO9NLDaxT/JGBc+PF", "d1/iUQnDJNZqP7ouQxGeMc", "fcEXd67VhOt4ysewVASO1R", "bfPUnOh25NDpa93sK/kOhO", "41s8jyTNpIppo9WEZb8Ga2", "aaqBBCE2ZMiYCmaXbHm7LF", "46CvMO39BHJYMlf09jbOGZ", "3c+3dxSDdDIYRnPPlrMRU9", "9cVppNY4VOo5Mbg6brhPes", "feWgZn2EJDv5HF6nYqe8cM", "7bHGcFP2hMYrbSn0cFShYB", "beT/vnL1ZPdamF6ukb/UWu", "7eKO/aMf1BUaMErzUfhkYb", "dee+m0JedEpLPVX0NLExcu", "38FKzS+61EcaN9ULTqSvWB", "c10uCGom9IoalmiggX30+4", "e61ZJ2wydGZ5MbGfh5kQO9", "99AVkW2A9Jr5vdFjOzvd6V", "fdNoodJKVLj4dF1TLppv2g", "47ZaHQWAZOMIP9IPzsA445", "1d9szKqmBP6Y9/McDtBYFg", "dad/yF6OdH6qAiDdR7QfLF", "ecB3AzISlM86c6qMXo8LKa", "cdZNucEQVBooHZfBhkKszd", "d8gcVAsAdNu7oFDLOcJKp9", "c1UIxlffhMMp0A9nMJLfoA", "1dMOo/RKJEi61gvYQzjuZI", "f3wu8X1/VCPZ7k+X501Nu7", "34nfp8KHlG05Fcf+9o/B8C", "dcX01BIiFODJ/Kp+Ctu0b/", "69cEGKz9tHVqCOlzI0mkBr", "3ew4CdG3BEfZ7AmM68b3y6", "c9Ap0s4UlBwpxkHJCTeEoP", "17/UZLo7ZKbLI7UlnYLqkj", "e9SqmTBPNCwbYlO3mHQoIS", "aazDksP+xC06woRdNkFO9s", "76OBC9nuhB8IstrrEHUlog", "a9VpD0DP5LJYQPXITZq+uj", "4bi2cg2UtHZ4jy1BRfAjtg", "b7b4O2NFFH+owmOo7FiZh+", "3a71+O4mxAt5wLxXoqL2RD", "80rZRwjlVG77B2zYPgb1Jz", "eeNSyu6fVFPZ/iyi+0e1dz", "ceptgRDyJENaA/WL2VSAc3", "444eemB7JKRJuU0Ixkr+Y3", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30", "6c//50z1tLA7x+K5BT0p6S", "83QBezrBNCypVDRFZcAqaD", "10Ih72ZWVFqpKiG+iG0fP4", "53VJKfdmRNdbrlTuYy2bYU", "e6L+JFxqJKhYE8rn2duUFE", "7ez1nmwYFHBL20/rh8lkAp", "edavFWv2lCB47zcpNwpeqf", "08VifNS+dHfJtYZ5Q+qJsV", "30ivtutTlIso8C4K7B2ybi", "c4S4mp9L5PZL+D770mT6Z2", "774ZSMQ1pPFo500JDtQJFd", "ecQepIJMFC9KyGM/ssvucU", "f3HRxAJ7hFg7HK90aAoYfn", "3dT66l2iVCLolRDFRxBmXx", "29JEpYqiBEi6USX1KpDTMC", "d2fSpEIW9D4Ys4gr8XV6rc", "f96d5pVLJNsrTdkpCxjlaH", "50gcBvrV9IsJO10HDCEUgn"], ["node", "_spriteFrame", "_textureSetter", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "data", "_defaultClip"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children"], 1, 9, 4, 1, 5, 7, 2], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 6, 6, 6, 6, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Layout", ["_enabled", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_resize", "node", "_layoutSize"], -2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["a4c84JoE69IlpundA0+AU+n", ["node", "nodeLines"], 3, 1, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1]], [[9, 0, 1, 2], [3, 0, 1, 2, 3, 4, 3], [1, 0, 4, 2, 3, 5, 6, 2], [1, 0, 4, 7, 2, 3, 5, 6, 2], [2, 0, 1, 2, 3, 4, 5, 6, 3], [5, 0, 1, 2, 3, 4], [1, 0, 4, 2, 3, 5, 2], [3, 2, 3, 4, 1], [5, 0, 1, 3, 3], [2, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 3], [4, 0, 1, 2, 3, 5, 6, 5], [6, 0, 2], [1, 0, 7, 2, 3, 2], [1, 0, 1, 4, 7, 2, 3, 5, 6, 3], [1, 0, 1, 4, 2, 3, 5, 6, 3], [7, 0, 1, 2, 1], [8, 0, 1, 1], [4, 0, 4, 1, 2, 5, 6, 5], [3, 0, 2, 3, 4, 2], [2, 0, 1, 2, 3, 4, 5, 6, 7, 3], [2, 2, 11, 1]], [[[{"name": "L2", "rect": [0, 0, 114, 66], "offset": [0, 0], "originalSize": [114, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [34]], [[{"name": "L6", "rect": [0, 0, 114, 66], "offset": [0, 0], "originalSize": [114, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [35]], [[{"name": "L9", "rect": [0, 0, 115, 66], "offset": [0, 0], "originalSize": [115, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [36]], [[{"name": "L4", "rect": [0, 0, 112, 66], "offset": [0, 0], "originalSize": [112, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [37]], [[{"name": "L27", "rect": [0, 0, 129, 66], "offset": [0, 0], "originalSize": [129, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [38]], [[{"name": "L10", "rect": [0, 0, 127, 66], "offset": [0, 0], "originalSize": [127, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [39]], [[{"name": "text_dongChan", "rect": [0, 0, 112, 36], "offset": [0, 0], "originalSize": [112, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [40]], [[{"name": "L20", "rect": [0, 0, 131, 66], "offset": [0, 0], "originalSize": [131, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [41]], [[{"name": "L17", "rect": [0, 0, 129, 66], "offset": [0, 0], "originalSize": [129, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [42]], [[{"name": "btn_close", "rect": [0, 0, 59, 61], "offset": [0, 0], "originalSize": [59, 61], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [43]], [[{"name": "L19", "rect": [0, 0, 129, 66], "offset": [0, 0], "originalSize": [129, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [44]], [[{"name": "L3", "rect": [0, 0, 115, 66], "offset": [0, 0], "originalSize": [115, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [45]], [[{"name": "L12", "rect": [0, 0, 130, 66], "offset": [0, 0], "originalSize": [130, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [46]], [[{"name": "L11", "rect": [0, 0, 129, 66], "offset": [0, 0], "originalSize": [129, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [47]], [[{"name": "L23", "rect": [0, 0, 129, 66], "offset": [0, 0], "originalSize": [129, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [48]], [[{"name": "L25", "rect": [0, 0, 129, 65], "offset": [0, 0], "originalSize": [129, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [49]], [[{"name": "text_dongLe", "rect": [0, 0, 95, 34], "offset": [0, 0], "originalSize": [95, 34], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [50]], [[{"name": "L7", "rect": [1, 0, 115, 66], "offset": [0.5, 0], "originalSize": [116, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [51]], [[[11, "aquariumBetLinesView"], [12, "aquariumBetLinesView", [-34, -35, -36, -37, -38, -39, -40], [[15, -2, [162, 163], 161], [16, -33, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32]]], [0, "2e8TD6c0BCTLcip8Ytpwha", -1]], [13, "layout-lines", 100, 1, [-42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61, -62, -63, -64, -65, -66, -67, -68, -69, -70, -71], [[10, false, 3, 5, -3, -41, [5, 850, 350]]], [0, "b3h/TJN5tPE6RHKFCIcbE8", 1], [5, 850, 350], [0, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layout-lines", 1, [-73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92, -93, -94, -95, -96, -97, -98, -99, -100, -101, -102], [[10, false, 3, 5, -3, -72, [5, 850, 350]]], [0, "50ujMlVolFD5tfqg0CduEQ", 1], [5, 850, 350], [0, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layout-button", 1, [-104, -105, -106, -107], [[17, false, 1, 1, 75, -103, [5, 785, 60]]], [0, "27NIMJGC9FDoEzwvI9HABF", 1], [5, 785, 60], [0, -198, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnEven", 4, [-111], [[7, -108, [128], 129], [9, 1.05, 2, -110, [[8, "a4c84JoE69IlpundA0+AU+n", "selectEvenClicked", 1]], [4, 4294967295], [4, 4294967295], -109, 130, 131, 132, 133]], [0, "94JVux0XpChLsZC8kpbHZI", 1], [5, 140, 64], [-322.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnOdd", 4, [-115], [[7, -112, [136], 137], [9, 1.05, 2, -114, [[8, "a4c84JoE69IlpundA0+AU+n", "selectOddClicked", 1]], [4, 4294967295], [4, 4294967295], -113, 138, 139, 140, 141]], [0, "cewr1xgflMAovKNjIwMjkW", 1], [5, 140, 64], [-107.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnNone", 4, [-119], [[1, 2, false, -116, [144], 145], [9, 1.05, 2, -118, [[8, "a4c84JoE69IlpundA0+AU+n", "selectNoneClicked", 1]], [4, 4294967295], [4, 4294967295], -117, 146, 147, 148, 149]], [0, "249VfhWNZOeZoLupbR+3OF", 1], [5, 140, 64], [107.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnAll", 4, [-123], [[7, -120, [152], 153], [9, 1.05, 2, -122, [[8, "a4c84JoE69IlpundA0+AU+n", "selectAllClicked", 1]], [4, 4294967295], [4, 4294967295], -121, 154, 155, 156, 157]], [0, "57COVkUv1D36s78WoKtD2w", 1], [5, 140, 64], [322.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-126], [[4, 1.05, 3, -125, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "0", 1]], [4, 4294967295], [4, 4294967295], -124]], [0, "a3OdQqFxtISY+tDBOuWAIC", 1], [5, 132, 80], [-359, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-129], [[4, 1.05, 3, -128, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "1", 1]], [4, 4294967295], [4, 4294967295], -127]], [0, "8egIwqDMNI+YiICEQabKgt", 1], [5, 132, 80], [-222, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-132], [[4, 1.05, 3, -131, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "2", 1]], [4, 4294967295], [4, 4294967295], -130]], [0, "e9NZ0F2XlOf6hfU2FMNYJU", 1], [5, 132, 80], [-85, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-135], [[4, 1.05, 3, -134, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "3", 1]], [4, 4294967295], [4, 4294967295], -133]], [0, "e7S6Gn8FtDPalTaOZtANw2", 1], [5, 132, 80], [52, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-138], [[4, 1.05, 3, -137, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "4", 1]], [4, 4294967295], [4, 4294967295], -136]], [0, "596CNRcJJDaKx5l35f8U/9", 1], [5, 132, 80], [189, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-141], [[4, 1.05, 3, -140, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "5", 1]], [4, 4294967295], [4, 4294967295], -139]], [0, "e7Wx/744ZKJ48VbhDe2JTX", 1], [5, 132, 80], [326, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-144], [[4, 1.05, 3, -143, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "6", 1]], [4, 4294967295], [4, 4294967295], -142]], [0, "7fG8pcL6hHP4fyLGjt78QC", 1], [5, 132, 80], [-359, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-147], [[4, 1.05, 3, -146, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "7", 1]], [4, 4294967295], [4, 4294967295], -145]], [0, "a2Dq0/zH5Gy4xjUdLfoqCm", 1], [5, 132, 80], [-222, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-150], [[4, 1.05, 3, -149, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "8", 1]], [4, 4294967295], [4, 4294967295], -148]], [0, "19RyIjNlhKsbPcd6n0DhCG", 1], [5, 132, 80], [-85, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-153], [[4, 1.05, 3, -152, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "9", 1]], [4, 4294967295], [4, 4294967295], -151]], [0, "21QQgs/M5BGpwcinrw1R+8", 1], [5, 132, 80], [52, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-156], [[4, 1.05, 3, -155, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "10", 1]], [4, 4294967295], [4, 4294967295], -154]], [0, "3fntKR8m9K8Z0i/PopflVY", 1], [5, 132, 80], [189, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-159], [[4, 1.05, 3, -158, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "11", 1]], [4, 4294967295], [4, 4294967295], -157]], [0, "d4vrhHmORCA61JYU/Bms9K", 1], [5, 132, 80], [326, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-162], [[4, 1.05, 3, -161, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "12", 1]], [4, 4294967295], [4, 4294967295], -160]], [0, "2bJzg0ivdKX5WFCDq3xGFU", 1], [5, 132, 80], [-359, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-165], [[4, 1.05, 3, -164, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "13", 1]], [4, 4294967295], [4, 4294967295], -163]], [0, "bfKHK9qhZMMr4ySTRgOkk0", 1], [5, 132, 80], [-222, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-168], [[4, 1.05, 3, -167, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "14", 1]], [4, 4294967295], [4, 4294967295], -166]], [0, "d125eiu3VNwJ1BL5Kbz/C7", 1], [5, 132, 80], [-85, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-171], [[4, 1.05, 3, -170, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "15", 1]], [4, 4294967295], [4, 4294967295], -169]], [0, "51cbenpjdL4aRai25NvZYX", 1], [5, 132, 80], [52, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-174], [[4, 1.05, 3, -173, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "16", 1]], [4, 4294967295], [4, 4294967295], -172]], [0, "8dqQ06QQNJxKcJ1oSgaGRf", 1], [5, 132, 80], [189, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-177], [[4, 1.05, 3, -176, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "17", 1]], [4, 4294967295], [4, 4294967295], -175]], [0, "09E7Ya9ZhIyITyCJ5kEfH8", 1], [5, 132, 80], [326, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-180], [[4, 1.05, 3, -179, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "18", 1]], [4, 4294967295], [4, 4294967295], -178]], [0, "7927zE0hlDi5/WBBSbSslF", 1], [5, 132, 80], [-359, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-183], [[4, 1.05, 3, -182, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "19", 1]], [4, 4294967295], [4, 4294967295], -181]], [0, "ecWPjQl2ZK15QImC+094RW", 1], [5, 132, 80], [-222, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-186], [[4, 1.05, 3, -185, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "20", 1]], [4, 4294967295], [4, 4294967295], -184]], [0, "6fAykWH/lI7Jkk9DnIfzYL", 1], [5, 132, 80], [-85, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-189], [[4, 1.05, 3, -188, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "21", 1]], [4, 4294967295], [4, 4294967295], -187]], [0, "2cD6J0YS9Ctou2kMiwLB4z", 1], [5, 132, 80], [52, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-192], [[4, 1.05, 3, -191, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "22", 1]], [4, 4294967295], [4, 4294967295], -190]], [0, "4eIKbIgG5IIIjbK0RBJ7mz", 1], [5, 132, 80], [189, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-195], [[4, 1.05, 3, -194, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "23", 1]], [4, 4294967295], [4, 4294967295], -193]], [0, "c1FpyXDmVB6oCC9GxcuByT", 1], [5, 132, 80], [326, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-198], [[4, 1.05, 3, -197, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "24", 1]], [4, 4294967295], [4, 4294967295], -196]], [0, "f8QnMMI8FEHZuiyX0650nL", 1], [5, 132, 80], [-359, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-201], [[4, 1.05, 3, -200, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "25", 1]], [4, 4294967295], [4, 4294967295], -199]], [0, "2e8+tPX8RH84iGk90/nCV+", 1], [5, 132, 80], [-222, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-204], [[4, 1.05, 3, -203, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "26", 1]], [4, 4294967295], [4, 4294967295], -202]], [0, "d5DYU/OPZNOJmZYXPcznGW", 1], [5, 132, 80], [-85, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-207], [[4, 1.05, 3, -206, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "27", 1]], [4, 4294967295], [4, 4294967295], -205]], [0, "f5dqBV+GlGWq7su+qwAT0A", 1], [5, 132, 80], [52, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-210], [[4, 1.05, 3, -209, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "28", 1]], [4, 4294967295], [4, 4294967295], -208]], [0, "67iFaQ1q1I0pp43A3m8DXH", 1], [5, 132, 80], [189, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-213], [[4, 1.05, 3, -212, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "29", 1]], [4, 4294967295], [4, 4294967295], -211]], [0, "25Omxa3MxHeosRzoHjxysB", 1], [5, 132, 80], [326, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnClose", 1, [-216], [[19, 1.1, 3, -215, [[8, "a4c84JoE69IlpundA0+AU+n", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -214, 160]], [0, "1ca/ew6XhJorPG8VlDti28", 1], [5, 80, 80], [452, 277, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "black", 100, 1, [[18, 0, -217, [0], 1], [20, -218, [4, 4292269782]]], [0, "ddJUDEjcRAN5AbouvPo2Lc", 1], [5, 3000, 3000], [0, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "sprite", 9, [[1, 2, false, -219, [66], 67]], [0, "2czaOAjDBO5aUeyreS3Wqd", 1], [5, 116, 66]], [2, "sprite", 10, [[1, 2, false, -220, [68], 69]], [0, "17nytqXlJMp7mQ+S5iMRAa", 1], [5, 114, 66], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 11, [[1, 2, false, -221, [70], 71]], [0, "f1AA7lGKVCi6VdnFc+udqh", 1], [5, 115, 66], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 12, [[1, 2, false, -222, [72], 73]], [0, "e7dfnk7ZJDTpkq+DFgI4YZ", 1], [5, 112, 66], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 13, [[1, 2, false, -223, [74], 75]], [0, "47zGSkqWxMNK/ymhPye6Xl", 1], [5, 115, 66], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 14, [[1, 2, false, -224, [76], 77]], [0, "a63iRanp9MpKkzc+xSR/u8", 1], [5, 114, 66], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "sprite", 15, [[1, 2, false, -225, [78], 79]], [0, "029U/L3rdA07WD8VWLpLRx", 1], [5, 116, 66]], [2, "sprite", 16, [[1, 2, false, -226, [80], 81]], [0, "16MyROJoNII6BG27HWzZHa", 1], [5, 114, 66], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 17, [[1, 2, false, -227, [82], 83]], [0, "31JRoeS59G7aTVcmKWIuWn", 1], [5, 115, 66], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "sprite", 18, [[1, 2, false, -228, [84], 85]], [0, "04gplHgC5J3721FIN85Hyp", 1], [5, 127, 66]], [6, "sprite", 19, [[1, 2, false, -229, [86], 87]], [0, "a4EPYgUeBG3IxDtt2ghCfb", 1], [5, 129, 66]], [6, "sprite", 20, [[1, 2, false, -230, [88], 89]], [0, "74hiNEgflPRaexc9bHJuao", 1], [5, 130, 66]], [6, "sprite", 21, [[1, 2, false, -231, [90], 91]], [0, "beE3sRSitFNKtL25tzyZsz", 1], [5, 130, 66]], [6, "sprite", 22, [[1, 2, false, -232, [92], 93]], [0, "55mJiT48ZGt4AZqBN1Gjx3", 1], [5, 130, 66]], [6, "sprite", 23, [[1, 2, false, -233, [94], 95]], [0, "d4KWZ6NgNL+6fMZeBJPirz", 1], [5, 130, 66]], [6, "sprite", 24, [[1, 2, false, -234, [96], 97]], [0, "e0nNpmBCJOfr/S2CUCmQoT", 1], [5, 128, 66]], [6, "sprite", 25, [[1, 2, false, -235, [98], 99]], [0, "caiRvIG1JEj5KF3ixgXPv0", 1], [5, 129, 66]], [6, "sprite", 26, [[1, 2, false, -236, [100], 101]], [0, "40VOleSutOPLjHht1z98wX", 1], [5, 130, 66]], [6, "sprite", 27, [[1, 2, false, -237, [102], 103]], [0, "a4g49NOxxMs78bGAh8ZK1q", 1], [5, 129, 66]], [6, "sprite", 28, [[1, 2, false, -238, [104], 105]], [0, "57atTXTdtOVpUA+aIfonPD", 1], [5, 131, 66]], [6, "sprite", 29, [[1, 2, false, -239, [106], 107]], [0, "1bAPI5GyxCmoFVeewwmq6t", 1], [5, 131, 65]], [6, "sprite", 30, [[1, 2, false, -240, [108], 109]], [0, "67k2Ca42hJUqJNIwljZ2tX", 1], [5, 129, 66]], [6, "sprite", 31, [[1, 2, false, -241, [110], 111]], [0, "b142NzP8RMFI1j6d3EElEj", 1], [5, 129, 66]], [6, "sprite", 32, [[1, 2, false, -242, [112], 113]], [0, "d82S4QqGdJ04V/YlOT4Tcp", 1], [5, 130, 66]], [6, "sprite", 33, [[1, 2, false, -243, [114], 115]], [0, "fcMnBmmMZKFaoQ4ae7xoA5", 1], [5, 129, 65]], [6, "sprite", 34, [[1, 2, false, -244, [116], 117]], [0, "e5JP3jFEZL0Ldyae09qPvy", 1], [5, 129, 63]], [6, "sprite", 35, [[1, 2, false, -245, [118], 119]], [0, "dd2oqUQRVFNqtAt3QzCDVA", 1], [5, 129, 66]], [6, "sprite", 36, [[1, 2, false, -246, [120], 121]], [0, "31XhX854pHdZnNagM6laXW", 1], [5, 128, 63]], [6, "sprite", 37, [[1, 2, false, -247, [122], 123]], [0, "b4e06RvJhLqL+zfHoOgwak", 1], [5, 128, 63]], [6, "sprite", 38, [[1, 2, false, -248, [124], 125]], [0, "14AHNwivZAsYz69el+/J5P", 1], [5, 127, 63]], [2, "nen popup", 1, [[1, 2, false, -249, [2], 3]], [0, "32LqZIIXFBKbTsv/Kr/t/j", 1], [5, 955, 594], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "title_chonDong", 1, [[7, -250, [4], 5]], [0, "f9SAoXF79C975Nv4qXpN1R", 1], [5, 197, 44], [0, 293, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -251, [6], 7]], [0, "3eNfmUNeZFJ79EC1QUNr8/", 1], [5, 116, 66], [-359, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -252, [8], 9]], [0, "c7c1fkPLJCqIVzNiTD2SSi", 1], [5, 114, 66], [-214, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -253, [10], 11]], [0, "01Kf79fz1EFYuUn5ZysZNO", 1], [5, 115, 66], [-77, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -254, [12], 13]], [0, "3bIMNbNc5DNISFhoWvnulJ", 1], [5, 112, 66], [60, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -255, [14], 15]], [0, "c9nWCN2T9ECq7CRw41YsHJ", 1], [5, 115, 66], [197, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -256, [16], 17]], [0, "a3BIWlLlFH161U+RpA0jol", 1], [5, 114, 66], [334, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -257, [18], 19]], [0, "59Mjpb7V1GV5flghEhDEqB", 1], [5, 116, 66], [-359, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -258, [20], 21]], [0, "e8SSwfb+9BYrVHBgpXk6Nu", 1], [5, 114, 66], [-214, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -259, [22], 23]], [0, "b2ADppbNRF6o6ctHkUso+O", 1], [5, 115, 66], [-77, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -260, [24], 25]], [0, "e8d6KCBOBDjqsPvBoH8Kpo", 1], [5, 127, 66], [52, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -261, [26], 27]], [0, "b92hgAXBVLeoO6RSDR/lqC", 1], [5, 129, 66], [189, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -262, [28], 29]], [0, "53AuT8R+RHGInDNTT3EIvk", 1], [5, 130, 66], [326, 58, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -263, [30], 31]], [0, "9afgSWThdHQrdCBAt+LqfR", 1], [5, 130, 66], [-359, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -264, [32], 33]], [0, "74+IboCJlNY5nV/OQxtbFb", 1], [5, 130, 66], [-222, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -265, [34], 35]], [0, "6bd1zdqxVPbLZEqXPByvYy", 1], [5, 130, 66], [-85, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -266, [36], 37]], [0, "0aEvB77J9G+5BNfsGUIznu", 1], [5, 128, 66], [52, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -267, [38], 39]], [0, "38JG4/Lv9K2aASUhtbngeK", 1], [5, 129, 66], [189, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -268, [40], 41]], [0, "92h+pZM7JJlYyqKTqUi7Qr", 1], [5, 130, 66], [326, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -269, [42], 43]], [0, "65lE5KiKdFuq8Hs4AJNBWA", 1], [5, 129, 66], [-359, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -270, [44], 45]], [0, "24e3NOOd1HBpATRNdjKH40", 1], [5, 131, 66], [-222, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -271, [46], 47]], [0, "9cVVFeR7lElbsGQ8o7ftRM", 1], [5, 131, 65], [-85, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -272, [48], 49]], [0, "3cGhfr/ORCrYc+w1p00non", 1], [5, 129, 66], [52, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -273, [50], 51]], [0, "38WNByO8JHxYpuQYBB9z3v", 1], [5, 129, 66], [189, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -274, [52], 53]], [0, "94v/VRVWZKApa0iFW3vjuS", 1], [5, 130, 66], [326, -96, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -275, [54], 55]], [0, "bamRW3tydOXb19skzsML8W", 1], [5, 129, 65], [-359, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -276, [56], 57]], [0, "9bCYABiftLxodRB5Nhq6wC", 1], [5, 129, 63], [-222, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -277, [58], 59]], [0, "cfzyqQ5etOq5RDwZ4relJE", 1], [5, 129, 66], [-85, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -278, [60], 61]], [0, "01aE/+gjROi5HUNSuot03k", 1], [5, 128, 63], [52, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -279, [62], 63]], [0, "a479nqUFhDd4sZ+8zRxWkX", 1], [5, 128, 63], [189, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[1, 2, false, -280, [64], 65]], [0, "cfn8SgWlZO7LaP0x93qf7+", 1], [5, 127, 63], [326, -173, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "text_dongChan", 5, [[7, -281, [126], 127]], [0, "21evwijhNC9oauZsi3J5rw", 1], [5, 112, 36]], [6, "text_dongLe", 6, [[7, -282, [134], 135]], [0, "f9gwL2j55LQIiufWDCoXt/", 1], [5, 95, 34]], [6, "text_boChon", 7, [[7, -283, [142], 143]], [0, "d0VJ4OwflPoLt2/n5pWi6P", 1], [5, 107, 35]], [6, "text_tatca", 8, [[7, -284, [150], 151]], [0, "e7mMDyq4tFs6wt+4VgfZh7", 1], [5, 96, 33]], [6, "sprite", 39, [[1, 2, false, -285, [158], 159]], [0, "f1HUEz115NybP+erMR9j2T", 1], [5, 59, 61]]], 0, [0, 8, 1, 0, 0, 1, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, -4, 44, 0, -5, 45, 0, -6, 46, 0, -7, 47, 0, -8, 48, 0, -9, 49, 0, -10, 50, 0, -11, 51, 0, -12, 52, 0, -13, 53, 0, -14, 54, 0, -15, 55, 0, -16, 56, 0, -17, 57, 0, -18, 58, 0, -19, 59, 0, -20, 60, 0, -21, 61, 0, -22, 62, 0, -23, 63, 0, -24, 64, 0, -25, 65, 0, -26, 66, 0, -27, 67, 0, -28, 68, 0, -29, 69, 0, -30, 70, 0, 0, 1, 0, -1, 40, 0, -2, 71, 0, -3, 72, 0, -4, 2, 0, -5, 3, 0, -6, 4, 0, -7, 39, 0, 0, 2, 0, -1, 73, 0, -2, 74, 0, -3, 75, 0, -4, 76, 0, -5, 77, 0, -6, 78, 0, -7, 79, 0, -8, 80, 0, -9, 81, 0, -10, 82, 0, -11, 83, 0, -12, 84, 0, -13, 85, 0, -14, 86, 0, -15, 87, 0, -16, 88, 0, -17, 89, 0, -18, 90, 0, -19, 91, 0, -20, 92, 0, -21, 93, 0, -22, 94, 0, -23, 95, 0, -24, 96, 0, -25, 97, 0, -26, 98, 0, -27, 99, 0, -28, 100, 0, -29, 101, 0, -30, 102, 0, 0, 3, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, -6, 14, 0, -7, 15, 0, -8, 16, 0, -9, 17, 0, -10, 18, 0, -11, 19, 0, -12, 20, 0, -13, 21, 0, -14, 22, 0, -15, 23, 0, -16, 24, 0, -17, 25, 0, -18, 26, 0, -19, 27, 0, -20, 28, 0, -21, 29, 0, -22, 30, 0, -23, 31, 0, -24, 32, 0, -25, 33, 0, -26, 34, 0, -27, 35, 0, -28, 36, 0, -29, 37, 0, -30, 38, 0, 0, 4, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, 0, 5, 0, 3, 5, 0, 0, 5, 0, -1, 103, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, -1, 104, 0, 0, 7, 0, 3, 7, 0, 0, 7, 0, -1, 105, 0, 0, 8, 0, 3, 8, 0, 0, 8, 0, -1, 106, 0, 3, 9, 0, 0, 9, 0, -1, 41, 0, 3, 10, 0, 0, 10, 0, -1, 42, 0, 3, 11, 0, 0, 11, 0, -1, 43, 0, 3, 12, 0, 0, 12, 0, -1, 44, 0, 3, 13, 0, 0, 13, 0, -1, 45, 0, 3, 14, 0, 0, 14, 0, -1, 46, 0, 3, 15, 0, 0, 15, 0, -1, 47, 0, 3, 16, 0, 0, 16, 0, -1, 48, 0, 3, 17, 0, 0, 17, 0, -1, 49, 0, 3, 18, 0, 0, 18, 0, -1, 50, 0, 3, 19, 0, 0, 19, 0, -1, 51, 0, 3, 20, 0, 0, 20, 0, -1, 52, 0, 3, 21, 0, 0, 21, 0, -1, 53, 0, 3, 22, 0, 0, 22, 0, -1, 54, 0, 3, 23, 0, 0, 23, 0, -1, 55, 0, 3, 24, 0, 0, 24, 0, -1, 56, 0, 3, 25, 0, 0, 25, 0, -1, 57, 0, 3, 26, 0, 0, 26, 0, -1, 58, 0, 3, 27, 0, 0, 27, 0, -1, 59, 0, 3, 28, 0, 0, 28, 0, -1, 60, 0, 3, 29, 0, 0, 29, 0, -1, 61, 0, 3, 30, 0, 0, 30, 0, -1, 62, 0, 3, 31, 0, 0, 31, 0, -1, 63, 0, 3, 32, 0, 0, 32, 0, -1, 64, 0, 3, 33, 0, 0, 33, 0, -1, 65, 0, 3, 34, 0, 0, 34, 0, -1, 66, 0, 3, 35, 0, 0, 35, 0, -1, 67, 0, 3, 36, 0, 0, 36, 0, -1, 68, 0, 3, 37, 0, 0, 37, 0, -1, 69, 0, 3, 38, 0, 0, 38, 0, -1, 70, 0, 3, 39, 0, 0, 39, 0, -1, 107, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 90, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 9, 1, 285], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 4, 5, 6, 7, -1, 1, -1, 1, 4, 5, 6, 7, -1, 1, -1, 1, 4, 5, 6, 7, -1, 1, -1, 1, 4, 5, 6, 7, -1, 1, 4, 10, -1, -2], [0, 52, 0, 53, 0, 54, 0, 3, 0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 12, 0, 13, 0, 14, 0, 15, 0, 16, 0, 17, 0, 18, 0, 19, 0, 20, 0, 21, 0, 22, 0, 23, 0, 24, 0, 25, 0, 26, 0, 27, 0, 28, 0, 29, 0, 30, 0, 31, 0, 32, 0, 3, 0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 12, 0, 13, 0, 14, 0, 15, 0, 16, 0, 17, 0, 18, 0, 19, 0, 20, 0, 21, 0, 22, 0, 23, 0, 24, 0, 25, 0, 26, 0, 27, 0, 28, 0, 29, 0, 30, 0, 31, 0, 32, 0, 55, 0, 1, 1, 2, 1, 1, 0, 56, 0, 1, 1, 2, 1, 1, 0, 57, 0, 1, 1, 2, 1, 1, 0, 58, 0, 1, 1, 2, 1, 1, 0, 59, 60, 33, 33, 61]], [[{"name": "L5", "rect": [0, 0, 115, 66], "offset": [0, 0], "originalSize": [115, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [62]], [[{"name": "L30", "rect": [0, 0, 127, 63], "offset": [0, 0], "originalSize": [127, 63], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [63]], [[{"name": "L21", "rect": [0, 0, 131, 65], "offset": [0, 0], "originalSize": [131, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [64]], [[{"name": "L18", "rect": [0, 0, 130, 66], "offset": [0, 0], "originalSize": [130, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [65]], [[{"name": "L1", "rect": [0, 0, 116, 66], "offset": [0, 0], "originalSize": [116, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [66]], [[{"name": "title_chonDong", "rect": [0, 0, 197, 44], "offset": [0, 0], "originalSize": [197, 44], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [67]], [[{"name": "L13", "rect": [0, 0, 130, 66], "offset": [0, 0], "originalSize": [130, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [68]], [[{"name": "L24", "rect": [0, 0, 130, 66], "offset": [0, 0], "originalSize": [130, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [69]], [[{"name": "L16", "rect": [0, 0, 128, 66], "offset": [0, 0], "originalSize": [128, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [70]], [[{"name": "L28", "rect": [0, 0, 128, 63], "offset": [0, 0], "originalSize": [128, 63], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [71]], [[{"name": "L8", "rect": [0, 0, 114, 66], "offset": [0, 0], "originalSize": [114, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [72]], [[{"name": "text_tatca", "rect": [0, 0, 96, 33], "offset": [0, 0], "originalSize": [96, 33], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [73]], [[{"name": "L14", "rect": [0, 0, 130, 66], "offset": [0, 0], "originalSize": [130, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [74]], [[{"name": "L26", "rect": [0, 0, 129, 63], "offset": [0, 0], "originalSize": [129, 63], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [75]], [[{"name": "L29", "rect": [0, 0, 128, 63], "offset": [0, 0], "originalSize": [128, 63], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [76]], [[{"name": "text_boChon", "rect": [0, 0, 107, 35], "offset": [0, 0], "originalSize": [107, 35], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [77]], [[{"name": "L15", "rect": [0, 0, 130, 66], "offset": [0, 0], "originalSize": [130, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [78]], [[{"name": "L22", "rect": [0, 0, 129, 66], "offset": [0, 0], "originalSize": [129, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [79]]]]