[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "ff1r5jKANAQ4ZMDv4p23IF", "40Au7leqpI6KKSfsb14Ksf", "c1y3UL3AVHoqWPxPdQzt/K", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_parent", "_defaultClip", "root", "_N$target", "_N$content", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_anchorPoint", "_children"], 0, 4, 9, 1, 5, 7, 5, 2], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint"], 2, 12, 9, 4, 5, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["_enabled", "horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["469fftM9vNEEqmL93QfozrK", ["node"], 3, 1]], [[5, 0, 1, 2], [1, 0, 3, 4, 5, 2], [2, 1, 2, 1], [3, 0, 2], [0, 0, 9, 4, 3, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 4, 3, 6, 2], [0, 0, 2, 5, 9, 4, 3, 6, 8, 7, 3], [0, 0, 5, 4, 3, 6, 8, 2], [0, 0, 5, 3, 6, 8, 2], [0, 0, 5, 4, 3, 6, 7, 2], [4, 0, 1, 2, 3, 4, 5, 2], [1, 1, 2, 3, 4, 5, 3], [2, 0, 1, 3, 4, 5, 6, 2], [6, 0, 1, 1], [7, 0, 1, 2, 3, 4, 5, 6, 7, 7], [8, 0, 1, 2, 3], [9, 0, 1, 2, 1], [10, 0, 1]], [[3, "taiXiuSieuTocHelpView"], [4, "taiXiuHelpView", [-4, -5, -6, -7], [[17, -2, [10, 11], 9], [18, -3]], [0, "482uhYoORHP6+1RUzk1glq", -1]], [11, "view", [[-9, [9, "content", -10, [0, "c8imFkEyFAa7y4BOT8FjJy", 1], [5, 1000, 560], [0, 0.5, 1]]], 1, 4], [[14, -8, [6]]], [0, "99WhUSkfJBTazBqOuKi6GR", 1], [5, 1000, 560], [0, 0.5, 1]], [10, "btnClose", 1, [[1, 0, -11, [7], 8], [13, 3, -13, [[16, "469fftM9vNEEqmL93QfozrK", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -12]], [0, "13yjKDFnVNZ6AAvSO91mEo", 1], [5, 90, 50], [553.7, 307.7, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "black", 100, 1, [[1, 0, -14, [0], 1], [2, -15, [4, 4292269782]]], [0, "9082MxgbxHNIBcevNUN1fe", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "nen popup", 1, [[12, 1, false, -16, [2], 3], [2, -17, [4, 4292269782]]], [0, "241vm6yaND3ruq4O7+83Ie", 1], [5, 1195, 671]], [7, "scrollview", false, 1, [2], [[15, false, false, 0.75, 0.23, null, null, -19, -18]], [0, "cafBi9cEdFdLzXmmoC3Kup", 1], [5, 1000, 560], [0, 0.5, 1], [0, 252, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "hd_pic", 2, [[1, 0, -20, [4], 5]], [0, "75OFmMkLdI0K6hWJBCYuHP", 1], [5, 890, 640], [0, 0.5, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 3, 0, 0, 2, 0, -1, 7, 0, 2, 2, 0, 0, 3, 0, 5, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 6, 7, 0, 0, 6, 0, 0, 7, 0, 7, 1, 2, 2, 6, 20], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, -1, 1, 3, -1, -2], [0, 2, 0, 3, 0, 4, 0, 0, 5, 1, 1, 6]]