[1, ["be1iInt3hNxI0IRxU0CsRG"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "Thang-Coin-1", "\nThang-Coin-1.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nThang/Coin-1/1\n  rotate: false\n  xy: 472, 574\n  size: 195, 178\n  orig: 270, 308\n  offset: 34, 56\n  index: -1\nThang/Coin-1/10\n  rotate: true\n  xy: 2, 440\n  size: 244, 234\n  orig: 270, 308\n  offset: 13, 46\n  index: -1\nThang/Coin-1/11\n  rotate: false\n  xy: 2, 231\n  size: 233, 207\n  orig: 270, 308\n  offset: 8, 53\n  index: -1\nThang/Coin-1/12\n  rotate: false\n  xy: 472, 754\n  size: 224, 213\n  orig: 270, 308\n  offset: 20, 55\n  index: -1\nThang/Coin-1/2\n  rotate: false\n  xy: 247, 535\n  size: 183, 178\n  orig: 270, 308\n  offset: 38, 55\n  index: -1\nThang/Coin-1/3\n  rotate: true\n  xy: 238, 365\n  size: 168, 178\n  orig: 270, 308\n  offset: 49, 56\n  index: -1\nThang/Coin-1/4\n  rotate: false\n  xy: 669, 583\n  size: 153, 169\n  orig: 270, 308\n  offset: 55, 60\n  index: -1\nThang/Coin-1/5\n  rotate: false\n  xy: 206, 38\n  size: 163, 191\n  orig: 270, 308\n  offset: 56, 47\n  index: -1\nThang/Coin-1/6\n  rotate: false\n  xy: 698, 756\n  size: 193, 211\n  orig: 270, 308\n  offset: 48, 37\n  index: -1\nThang/Coin-1/7\n  rotate: false\n  xy: 2, 2\n  size: 202, 227\n  orig: 270, 308\n  offset: 37, 27\n  index: -1\nThang/Coin-1/8\n  rotate: false\n  xy: 247, 715\n  size: 223, 252\n  orig: 270, 308\n  offset: 26, 14\n  index: -1\nThang/Coin-1/9\n  rotate: false\n  xy: 2, 686\n  size: 243, 281\n  orig: 270, 308\n  offset: 18, 17\n  index: -1\n", ["Thang-Coin-1.png"], {"skeleton": {"hash": "umWEEkcS2PfG3rl/YsN5ys5Uheg", "spine": "3.6.53", "width": 0, "height": 0, "images": "../"}, "bones": [{"name": "root"}], "slots": [{"name": "Coin-1", "bone": "root"}], "skins": {"default": {"Coin-1": {"Thang/Coin-1/1": {"width": 270, "height": 308}, "Thang/Coin-1/2": {"width": 270, "height": 308}, "Thang/Coin-1/3": {"width": 270, "height": 308}, "Thang/Coin-1/4": {"width": 270, "height": 308}, "Thang/Coin-1/5": {"width": 270, "height": 308}, "Thang/Coin-1/6": {"width": 270, "height": 308}, "Thang/Coin-1/7": {"width": 270, "height": 308}, "Thang/Coin-1/8": {"width": 270, "height": 308}, "Thang/Coin-1/9": {"width": 270, "height": 308}, "Thang/Coin-1/10": {"width": 270, "height": 308}, "Thang/Coin-1/11": {"width": 270, "height": 308}, "Thang/Coin-1/12": {"width": 270, "height": 308}}}}, "animations": {"Idle": {"slots": {"Coin-1": {"attachment": [{"time": 0, "name": "Thang/Coin-1/1"}, {"time": 0.0333, "name": "Thang/Coin-1/2"}, {"time": 0.0667, "name": "Thang/Coin-1/3"}, {"time": 0.1, "name": "Thang/Coin-1/4"}, {"time": 0.1333, "name": "Thang/Coin-1/5"}, {"time": 0.1667, "name": "Thang/Coin-1/6"}, {"time": 0.2, "name": "Thang/Coin-1/7"}, {"time": 0.2333, "name": "Thang/Coin-1/8"}, {"time": 0.2667, "name": "Thang/Coin-1/9"}, {"time": 0.3, "name": "Thang/Coin-1/10"}, {"time": 0.3333, "name": "Thang/Coin-1/11"}, {"time": 0.3667, "name": "Thang/Coin-1/12"}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]