[1, ["ecpdLyjvZBwrvm+cedCcQy", "43FgFyEmhMwp0gdhTuTevT", "7a/QZLET9IDreTiBfRn2PD", "84cbWRHshDY6cM42UYQNGn", "94/Z4szM5GnqVf6Ola0ppg", "15TqJIGghPRbUeUWSV2ffV", "fa0JEQ585BFoq9kN30BSTe", "9dSTZRFWdKu7auOUZj5fqr", "97VdwFZ6dLdLWR9bX/Uo9i", "defNyxihBA9piIDeOs/t38", "caMgThy1VMjaOc0JGk7H66", "a9VpD0DP5LJYQPXITZq+uj", "80NC2FR4dIM6qbuBKEpntK", "a43LM9mpZAdL2OL/CDGr8d", "acjnvAr1FBWIMBNfdm5yM9", "c8b+IZbOBLK4quBdDEfTbC", "43Z8r7dvZE6IKwsyeogy9T", "eaqnIplqNErbdgYnwQx8GG", "8d0FjO4hFKgab4glGQWm5B", "f7xeAOTcpMcZ5eECSIKiKC", "5dlO7Gl8NFKopo5eoKF/D1", "d0P70OG4lOlbXxIrsuKYr9", "fdNoodJKVLj4dF1TLppv2g", "9bYDnu5NdJH7u79ZpCFpEO", "bcbgFB+epIlKdGw1JroyQo", "49mwvGKDlEqbNnn0IdTrDT", "f1Jc+qU+pDb6CZ0+aIivDT", "e9xc8DzvtB3ayXEE8uFAJ5", "f0yzsdE0dDu6rCuciuz+62", "609g536fFNb6jR5xklAsrJ", "f5jGhOZwFO9IqgzlyWKlvX", "c0UOLPDLtL8I2f/+fm9bYQ", "41H+DHwUhO+r0ypnRy8D4a", "d7QVuhll1DbZvZVkT6/1Ms", "cfHLYKddRElKGsZsDL8Zqv", "00cRqXMK9Ha67jXsYRskio", "11YWs7KRhEva+SLC22a8iQ", "11NCkUKbhNS7Gw9gKCnOq/", "2273ZNHspLSK60fe4HYsP/", "a3JTCykGNPBbVHrJGDdMpn", "8azDlpsXNK7az+dC0w4Agc", "c5Yd3YgtxBZ4S0iAmI7T8y", "04EmkqXXJH6pHmn58tpsGo", "b009OmTtBMqpoWkXkT6aLZ", "c3ekK8CrdNxoTvOIyiwNe6", "ccN9OW04lN8aPPFVxRJ2lZ", "1eV+M4bc1Ps5ip4r2UaoZr", "e4+mF4Uf1LxrBudcx4fJkz", "840Jl5rg1M9a6NAk0zwPHW", "007NPzH9FDZ5h5ydDvbJgO", "cfjRWbW7ZCjqCE//viWGM+", "99FFKKpT5L0YgFrOMk7U1L", "7dWUHT5TJENLUw4CyBgz0h", "1bZexmyMNAbISFLdk+FtEi", "a4gZnE1bZKGbBiv0z3g3yu", "1b8xr2lRZBQ4n5tVAIsnnO", "b9sAPrSq9Bhq4NL0/rodI/", "6c5R0sAflFwbnYfosxA68Q", "0bqSDyYyVCEovDWnR1jQwP", "cbHfQego5EmJEXx+s2+dgJ", "97AF8IfjFNJqsIABSL6uR1", "75/x9rgPpGhrACFuvtrcpy", "23vDYhkPxPt4SS21sPCZVs", "0aLgiltBdMk7EYPGA14agE", "27AHLJq+NAmrXyvotrlwk7", "97BlrVJNJH0ZxryHV1zovl", "75KR+WTuRPmbFyY0Hxq7t+", "0fdlXb4m1KtaqEAO3HYLt6", "d1MzziBA9I4bENTciYWb6n", "33MnE1Y21CgrnigkJQMfaA", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "8cJkMt10JCtovg68b+Tojs", "5b0LLm6bFBEa89UYnku39d", "acL7rSCBdICLx8UARLCSZY", "07Eh+5ORpAz5y9YvJooGFK", "10paj1R+RLIZxdDGbCK2kj", "cbmJ50bIhGead/YYUqNPmO", "05/k69cl1FpKZs3PPmGgqu", "5bfKNoiDVPwaHBuMQ8Q5nQ", "51CpLw1YpIHqHgtoRITLRy", "a4SCThNRVJmKEMx5NZJoAw", "f9nBVbB69E7Jhl0LsbbfWP", "e8yPidfkFJ/Kr6rcBteued", "c2lQuR9lhISLow1ducYF1E", "f71XqdbAFBIraWoiCR/PYW", "4482gLDRBB/qaawwh4zld6", "762zLkBgJKQYMSVAMqDp1k", "e6kpNRkItHg4ZnqZc+Ess2", "2cWB/vWPRHja3uQTinHH30", "ebPlCsMqBLD6uK3L1aAGGA", "9bsuZEGVpLPZnUbzjUaPaV", "83az2JGh1Fq78RMXojGwix", "a1dJDmTIFGHpfXrcb7e0Ew", "faaielRSFGE4/vdznekOcI", "32rojxejFOKZkwAbzaehnb", "d7evT1WfFCk6V3zuJz9cYW", "d7UfaWJOVIRJE77kcYU9XG", "c0JzyHUexHBYz0GbAR5UQ0", "e4Ay7XgMVL+4end6+f7K00", "aeY5rQWbpKLqinwNskoJCm", "17AVOJWK1DF4sfIarOsL+y", "bfCSL/EGxNdac4DdARYnr3", "28K80YK2tNZ7rAdLZTYfOM", "eciFMHlpZD9pDJVpTnx9q/", "74IEvD3SBKkKzqOqcpNikb", "cdkfaszJVGXpHmfGfrqEbJ", "f4PFVjOdZHcL5f5Y8DjyQe", "2dJ+MBiilA7opM4JMbTouk", "53fGWzaLtJQpRAX+AV/mGY", "ae9YcHFdtBHo0ijpazqBQ9", "3cxUluH1lJ87hHIwcrRBxH", "75hgcQQC5GJ4meCdWsk48W", "77SlosDulLpqipNUvJgjVe", "e4J4qZ+f1Gqb2mYYzd/cdf"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_N$skeletonData", "_parent", "_defaultClip", "_clip", "lblTextNotiNewGame", "_N$target", "_normalMaterial", "Showketqua", "winSound", "nodebaton", "nodeXiu", "nodeTai", "nodeketqua", "nodeBowl", "nodeBGTimer", "root", "xnEffect", "rollDice", "nodeXiuWins", "nodeTaiWins", "nodekeymd5", "nodeMain", "lbUserBetXiu", "lbUserBetTai", "lbTotalBetXiu", "lbTotalBetTai", "lbTimer", "lbBigTimer", "lbSessionID", "lbBetXiu", "lbBetTai", "nodeLBBetResult", "animationBetResult", "nodeLBBetXiu", "nodeLBBetTai", "lbTotalDice", "spriteDice3", "spriteDice2", "spriteDice1", "md5hash", "md5key", "editBoxDatXiu", "editBoxDatTai", "audioChonSo", "lbBetXiuTemp", "lbBetTaiTemp", "nodeinputxiu", "nodeinputtai", "nodeInputFree", "nodeInputValue", "nodeInput", "spriteChat", "spriteNan", "nodeChat", "spriteLightButton", "btnSendChat", "editBoxChat", "chatListView", "touchParent", "rtAdmin", "spriteVIP", "lbMessage", "lbName", "nodeUser", "lbXiuWin", "lbTaiWin", "data", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail", "prefabGraph", "prefabJackpotHistory", "prefabRule", "_N$font", "spriteFrame"], [["cc.Label", ["_fontSize", "_N$verticalAlign", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_string", "_enableWrapText", "_N$overflow", "_spacingX", "_materials", "node", "_N$file"], -6, 3, 1, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint", "_color"], 0, 9, 4, 5, 7, 1, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_children"], 0, 1, 2, 4, 5, 7, 5, 5, 2], ["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_parent", "_children", "_trs", "_components", "_contentSize", "_color"], 0, 4, 1, 2, 7, 12, 5, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "_enabled", "_N$interactable", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_normalMaterial", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], -1, 1, 9, 5, 5, 1, 5, 6, 6, 6, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "_N$spacingY", "_enabled", "node", "_layoutSize"], -2, 1, 5], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "_animationName", "defaultSkin", "premultipliedAlpha", "_materials", "node", "_N$skeletonData"], -2, 3, 1, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.AudioSource", ["node", "_clip"], 3, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.EditBox", ["returnType", "max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "editingReturn", "_N$textLabel", "_N$placeholderLabel", "_N$background", "textChanged"], 0, 1, 9, 1, 1, 1, 9], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -3], ["cc.Prefab", ["_name"], 2], ["5e6fdzwzFBBOpmhY4UBi5k7", ["node", "nodeBGTimer", "lbSessionID", "lbBigTimer", "lbTimer", "lbTotalBetTai", "lbTotalBetXiu", "lbUserBetTai", "lbUserBetXiu", "nodeMain", "nodeBowl", "nodeketqua", "nodekeymd5", "nodeTaiWins", "nodeXiuWins", "lblTextNotiNewGame", "nodeTai", "nodeXiu", "nodebaton", "rollDice", "winSound", "xnAnimation", "Showketqua", "xnEffect"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1], ["d5c4eIeR0RFI569FkC8CimL", ["node", "lbBetTai", "lbBetXiu"], 3, 1, 1, 1], ["31233FAsMVA85fIiyXwP3IM", ["node", "nodeBowl", "nodebaton", "md5key", "md5hash", "nodeketqua", "nodeBGTimer", "xnAnimation", "Showketqua", "spriteDice1", "spriteDice2", "spriteDice3", "lbTotalDice", "lblTextNotiNewGame", "nodeTaiWins", "nodeXiuWins", "winSound", "nodeTai", "nodeXiu", "nodeLBBetTai", "nodeLBBetXiu", "animationBetResult", "nodeLBBetResult"], 3, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1], ["1e5f11NUVtHqa2yU6wShGpf", ["node", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail", "prefabGraph", "prefabJackpotHistory", "prefabRule"], 3, 1, 6, 6, 6, 6, 6, 6, 6], ["f6418avtdxOd7WVNYqrWf5y", ["node"], 3, 1], ["f74d33qIsFLnqhF+m+kFzXV", ["node"], 3, 1], ["faac23uBfBBJ4eRyib6vBGW", ["node", "nodeInput", "nodeInputValue", "nodeInputFree", "nodeinputtai", "nodeinputxiu", "lbBetTaiTemp", "lbBetXiuTemp", "lblTextNotiNewGame", "audioChonSo", "editBoxDatTai", "editBoxDatXiu"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["0d21e55ZJdLvrAD/qzGrwsv", ["node", "spriteSides", "sfSides"], 3, 1, 2, 3], ["3a9ccPvVxFB/4d66plykjoi", ["node", "spriteLightButton", "nodeChat", "spriteNan", "sfNans", "spriteChat", "sfChats"], 3, 1, 1, 1, 1, 3, 1, 3], ["a00000pMpdE2aX7FTfFS08F", ["node", "chatListView", "editBoxChat", "btnSendChat", "lblTextNotiNewGame"], 3, 1, 1, 1, 1, 1], ["ce4f0IQOXpH36Z//2C++UVH", ["node", "touchParent"], 3, 1, 1], ["ce4f0IQOXpH36Z//2C++UVH", ["touchParent", "node"], 2, 1], ["8be9fmDgjRGuJDpCIp+Bx7T", ["node", "nodeUser", "lbName", "lbMessage", "spriteVIP", "rtAdmin"], 3, 1, 1, 1, 1, 1, 1], ["27a30R0q0lCWaTA8e8BagIb", ["node", "lbTaiWin", "lbXiuWin"], 3, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["9d1bbBbrYlCd6GxBqWZU2YQ", ["node"], 3, 1], ["cc.Widget", ["alignMode", "_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -5, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$handleTouchEvent", "node"], -3, 1], ["<PERSON><PERSON>", ["horizontal", "node", "_N$content"], 2, 1, 1], ["87b0b6j4kBLKKRiRXRQjf/0", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[12, 0, 1, 2], [2, 0, 7, 3, 4, 5, 6, 2], [6, 0, 1, 4, 5, 6, 7, 3], [3, 4, 5, 6, 1], [4, 0, 3, 4, 5, 6, 7, 2], [11, 0, 1, 3, 3], [11, 0, 1, 2, 3, 4], [3, 0, 4, 5, 2], [3, 0, 1, 4, 5, 3], [2, 0, 7, 8, 3, 4, 5, 6, 2], [2, 0, 1, 7, 3, 4, 5, 6, 3], [3, 1, 4, 5, 6, 2], [4, 0, 3, 4, 5, 6, 2], [8, 3, 0, 1, 2, 6, 5, 7, 5], [5, 0, 2, 4, 5, 3, 3], [4, 0, 3, 10, 4, 5, 6, 7, 2], [4, 0, 1, 3, 4, 5, 6, 7, 3], [9, 0, 1, 2, 3, 2], [3, 0, 4, 5, 6, 2], [0, 5, 0, 2, 6, 3, 4, 1, 10, 9, 8], [14, 0, 1, 2, 3, 4, 5], [4, 0, 2, 3, 4, 5, 8, 6, 9, 7, 3], [4, 0, 3, 4, 5, 8, 6, 9, 7, 2], [4, 0, 3, 4, 5, 2], [9, 1, 2, 1], [7, 1, 0, 2, 5, 6, 4], [3, 0, 1, 4, 5, 6, 3], [6, 0, 1, 4, 5, 10, 3], [8, 3, 0, 1, 4, 2, 5, 6], [5, 0, 4, 5, 3, 8, 2], [5, 0, 4, 5, 3, 2], [5, 0, 4, 5, 3, 6, 2], [5, 0, 4, 7, 3, 8, 6, 2], [5, 0, 1, 4, 7, 3, 6, 3], [2, 0, 7, 8, 3, 4, 2], [2, 0, 8, 3, 4, 5, 2], [2, 0, 3, 4, 5, 6, 2], [2, 0, 1, 7, 8, 3, 4, 5, 6, 3], [2, 0, 1, 7, 3, 4, 6, 3], [2, 0, 1, 7, 3, 4, 5, 3], [4, 0, 1, 2, 3, 10, 4, 5, 6, 7, 4], [4, 0, 1, 2, 3, 4, 5, 8, 6, 9, 7, 4], [9, 1, 2, 3, 1], [10, 0, 1, 1], [10, 0, 1], [3, 3, 0, 4, 5, 3], [3, 0, 4, 2], [3, 1, 4, 5, 2], [3, 2, 0, 4, 3], [6, 4, 5, 6, 7, 8, 1], [31, 0, 1], [8, 0, 1, 2, 6, 5, 7, 4], [0, 0, 2, 6, 3, 4, 1, 9, 7], [0, 0, 6, 1, 7, 10, 5], [0, 5, 0, 2, 3, 4, 1, 10, 9, 7], [0, 0, 2, 3, 8, 4, 1, 10, 9, 7], [13, 0, 1, 2, 3, 8, 4, 5, 6, 7, 4], [15, 0, 1, 2, 3, 4, 5, 7], [16, 0, 2], [5, 0, 5, 7, 3, 8, 2], [5, 0, 4, 5, 3, 8, 6, 2], [5, 0, 1, 4, 5, 7, 3, 8, 6, 3], [5, 0, 4, 7, 3, 9, 8, 6, 2], [5, 0, 4, 7, 3, 6, 2], [2, 0, 8, 3, 4, 5, 9, 6, 2], [2, 0, 1, 8, 3, 4, 5, 6, 3], [2, 0, 8, 3, 4, 5, 6, 2], [2, 0, 1, 7, 8, 3, 4, 5, 9, 6, 3], [2, 0, 2, 7, 3, 4, 5, 6, 3], [2, 0, 1, 7, 3, 4, 10, 5, 6, 3], [2, 0, 2, 7, 8, 3, 4, 5, 6, 3], [2, 0, 7, 3, 4, 5, 2], [2, 0, 7, 3, 4, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 4], [4, 0, 1, 3, 4, 5, 6, 9, 7, 3], [4, 0, 1, 3, 4, 5, 8, 6, 7, 3], [4, 0, 3, 4, 5, 8, 6, 7, 2], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 1], [18, 0, 1, 2, 1], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 1], [20, 0, 1, 2, 3, 4, 5, 6, 7, 1], [21, 0, 1], [22, 0, 1], [12, 1, 1], [23, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [10, 1], [7, 4, 1, 0, 2, 3, 5, 6, 6], [7, 1, 0, 2, 3, 5, 6, 5], [7, 0, 2, 5, 6, 3], [7, 1, 0, 5, 6, 3], [24, 0, 1, 2, 1], [25, 0, 1, 2, 3, 4, 5, 6, 1], [26, 0, 1, 2, 3, 4, 1], [3, 2, 0, 1, 4, 5, 6, 4], [3, 2, 4, 5, 6, 2], [3, 2, 0, 4, 5, 3], [3, 3, 4, 2], [6, 4, 9, 8, 10, 1], [6, 4, 1], [6, 1, 4, 5, 9, 6, 7, 8, 11, 12, 13, 2], [6, 2, 0, 1, 4, 5, 6, 7, 4], [6, 0, 4, 5, 2], [6, 0, 3, 1, 4, 5, 6, 7, 8, 4], [6, 0, 1, 4, 5, 3], [27, 0, 1, 1], [28, 0, 1, 2], [29, 0, 1, 2, 3, 4, 5, 1], [30, 0, 1, 2, 1], [11, 0, 1, 3], [32, 0, 1], [8, 3, 0, 1, 4, 2, 6, 5, 6], [33, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [0, 5, 0, 2, 3, 8, 4, 1, 9, 8], [0, 5, 0, 3, 8, 4, 1, 10, 9, 11, 7], [0, 0, 2, 6, 3, 1, 7, 10, 7], [0, 5, 0, 2, 6, 3, 1, 7, 10, 9, 8], [0, 0, 2, 6, 3, 1, 10, 9, 6], [0, 0, 2, 6, 3, 1, 7, 10, 9, 7], [0, 4, 1, 9, 3], [0, 5, 0, 6, 1, 7, 10, 9, 6], [0, 5, 0, 2, 6, 1, 7, 10, 9, 7], [0, 5, 0, 2, 6, 3, 4, 7, 10, 9, 8], [0, 5, 0, 2, 4, 1, 10, 9, 6], [0, 0, 2, 4, 1, 10, 9, 5], [0, 0, 2, 3, 4, 10, 9, 5], [0, 0, 3, 4, 1, 10, 9, 5], [0, 0, 2, 3, 4, 1, 10, 9, 6], [0, 5, 0, 2, 3, 8, 4, 1, 10, 9, 8], [34, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 4], [35, 0, 1, 2, 3, 4, 5, 6, 7], [36, 0, 1, 2, 2], [37, 0, 1, 2, 3, 4, 3], [38, 0, 1, 2, 4]], [[[[20, "dragon", "\ndragon.png\nsize: 1024,256\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nImage 1\n  rotate: true\n  xy: 346, 159\n  size: 46, 45\n  orig: 66, 57\n  offset: 10, 1\n  index: -1\nImage 11\n  rotate: false\n  xy: 347, 111\n  size: 45, 46\n  orig: 66, 57\n  offset: 11, 1\n  index: -1\nImage 13\n  rotate: false\n  xy: 300, 207\n  size: 45, 47\n  orig: 66, 57\n  offset: 11, 1\n  index: -1\nImage 3\n  rotate: true\n  xy: 347, 208\n  size: 46, 45\n  orig: 66, 57\n  offset: 10, 1\n  index: -1\nImage 5\n  rotate: false\n  xy: 299, 109\n  size: 46, 46\n  orig: 66, 57\n  offset: 10, 1\n  index: -1\nImage 7\n  rotate: false\n  xy: 252, 143\n  size: 45, 48\n  orig: 66, 57\n  offset: 11, 1\n  index: -1\nImage 9\n  rotate: false\n  xy: 299, 157\n  size: 45, 47\n  orig: 66, 57\n  offset: 11, 1\n  index: -1\nbody\n  rotate: true\n  xy: 2, 13\n  size: 241, 64\n  orig: 243, 67\n  offset: 1, 1\n  index: -1\nfirebird_000\n  rotate: true\n  xy: 394, 209\n  size: 45, 43\n  orig: 45, 44\n  offset: 0, 1\n  index: -1\nfirebird_001\n  rotate: true\n  xy: 484, 210\n  size: 44, 43\n  orig: 45, 44\n  offset: 1, 1\n  index: -1\nfirebird_002\n  rotate: true\n  xy: 393, 161\n  size: 45, 44\n  orig: 45, 44\n  offset: 0, 0\n  index: -1\nfirebird_003\n  rotate: true\n  xy: 484, 164\n  size: 44, 43\n  orig: 45, 44\n  offset: 1, 1\n  index: -1\nfirebird_004\n  rotate: true\n  xy: 394, 114\n  size: 45, 43\n  orig: 45, 44\n  offset: 0, 1\n  index: -1\nfirebird_005\n  rotate: true\n  xy: 439, 209\n  size: 45, 43\n  orig: 45, 44\n  offset: 0, 1\n  index: -1\nfirebird_006\n  rotate: true\n  xy: 529, 210\n  size: 44, 43\n  orig: 45, 44\n  offset: 1, 1\n  index: -1\nfirebird_007\n  rotate: true\n  xy: 529, 164\n  size: 44, 43\n  orig: 45, 44\n  offset: 1, 1\n  index: -1\nfirebird_008\n  rotate: false\n  xy: 483, 116\n  size: 44, 44\n  orig: 45, 44\n  offset: 1, 0\n  index: -1\nfirebird_009\n  rotate: true\n  xy: 529, 118\n  size: 44, 43\n  orig: 45, 44\n  offset: 1, 1\n  index: -1\nfirebird_010\n  rotate: true\n  xy: 439, 162\n  size: 45, 43\n  orig: 45, 44\n  offset: 0, 1\n  index: -1\nfirebird_011\n  rotate: true\n  xy: 574, 210\n  size: 44, 43\n  orig: 45, 44\n  offset: 1, 1\n  index: -1\ngold\n  rotate: false\n  xy: 107, 41\n  size: 577, 66\n  orig: 579, 68\n  offset: 1, 1\n  index: -1\nhead\n  rotate: false\n  xy: 68, 109\n  size: 140, 145\n  orig: 143, 147\n  offset: 1, 1\n  index: -1\nlhand\n  rotate: false\n  xy: 380, 14\n  size: 59, 25\n  orig: 61, 27\n  offset: 1, 1\n  index: -1\nlight1\n  rotate: false\n  xy: 2, 2\n  size: 9, 9\n  orig: 11, 11\n  offset: 1, 1\n  index: -1\nlxu1\n  rotate: true\n  xy: 68, 23\n  size: 84, 37\n  orig: 84, 37\n  offset: 0, 0\n  index: -1\nlxu2\n  rotate: false\n  xy: 483, 15\n  size: 44, 24\n  orig: 44, 24\n  offset: 0, 0\n  index: -1\nlxu3\n  rotate: true\n  xy: 439, 115\n  size: 45, 42\n  orig: 45, 42\n  offset: 0, 0\n  index: -1\nlxu4\n  rotate: true\n  xy: 574, 165\n  size: 43, 39\n  orig: 43, 39\n  offset: 0, 0\n  index: -1\nlxu5\n  rotate: true\n  xy: 299, 6\n  size: 33, 32\n  orig: 33, 32\n  offset: 0, 0\n  index: -1\nlxu6\n  rotate: false\n  xy: 333, 8\n  size: 45, 31\n  orig: 45, 31\n  offset: 0, 0\n  index: -1\nlxu7\n  rotate: false\n  xy: 233, 5\n  size: 21, 34\n  orig: 21, 34\n  offset: 0, 0\n  index: -1\nlxu8\n  rotate: false\n  xy: 258, 206\n  size: 40, 48\n  orig: 40, 48\n  offset: 0, 0\n  index: -1\nrhand\n  rotate: false\n  xy: 210, 193\n  size: 46, 61\n  orig: 48, 64\n  offset: 1, 1\n  index: -1\nrxu1\n  rotate: false\n  xy: 107, 2\n  size: 63, 37\n  orig: 63, 37\n  offset: 0, 0\n  index: -1\nrxu2\n  rotate: true\n  xy: 172, 2\n  size: 37, 25\n  orig: 37, 25\n  offset: 0, 0\n  index: -1\nrxu3\n  rotate: true\n  xy: 574, 121\n  size: 42, 41\n  orig: 42, 41\n  offset: 0, 0\n  index: -1\nrxu4\n  rotate: false\n  xy: 256, 6\n  size: 41, 33\n  orig: 41, 33\n  offset: 0, 0\n  index: -1\nrxu5\n  rotate: false\n  xy: 199, 5\n  size: 32, 34\n  orig: 32, 34\n  offset: 0, 0\n  index: -1\nrxu6\n  rotate: false\n  xy: 441, 14\n  size: 40, 25\n  orig: 40, 25\n  offset: 0, 0\n  index: -1\nrxu7\n  rotate: true\n  xy: 68, 5\n  size: 16, 31\n  orig: 18, 33\n  offset: 1, 1\n  index: -1\nrxu8\n  rotate: false\n  xy: 210, 142\n  size: 40, 49\n  orig: 40, 49\n  offset: 0, 0\n  index: -1\n", ["dragon.png"], {"skins": [{"name": "default", "attachments": {"body": {"body": {"x": 6.81, "width": 243, "y": 29.27, "height": 67}}, "fireball2": {"firebird_000": {"x": 0.5, "width": 45, "height": 44}, "firebird_011": {"x": 0.5, "width": 45, "height": 44}, "firebird_001": {"x": 0.5, "width": 45, "height": 44}, "firebird_002": {"x": 0.5, "width": 45, "height": 44}, "firebird_003": {"x": 0.5, "width": 45, "height": 44}, "firebird_004": {"x": 0.5, "width": 45, "height": 44}, "firebird_005": {"x": 0.5, "width": 45, "height": 44}, "firebird_006": {"x": 0.5, "width": 45, "height": 44}, "firebird_007": {"x": 0.5, "width": 45, "height": 44}, "firebird_008": {"x": 0.5, "width": 45, "height": 44}, "firebird_009": {"x": 0.5, "width": 45, "height": 44}, "firebird_010": {"x": 0.5, "width": 45, "height": 44}}, "rxu4": {"rxu4": {"width": 41, "type": "mesh", "hull": 9, "height": 33, "triangles": [14, 13, 1, 13, 0, 1, 0, 13, 8, 13, 7, 8, 14, 7, 13, 14, 12, 7, 12, 6, 7, 15, 14, 1, 10, 11, 15, 15, 11, 14, 10, 6, 11, 11, 12, 14, 11, 6, 12, 17, 16, 1, 16, 15, 1, 17, 9, 16, 9, 10, 16, 16, 10, 15, 2, 17, 1, 17, 2, 9, 9, 2, 3, 9, 3, 5, 3, 4, 5, 9, 5, 10, 10, 5, 6], "uvs": [1, 0.17629, 1, 1, 0.26819, 1, 0.10272, 1, 0, 1, 0, 0.65726, 0, 0, 0.77082, 0, 1, 0, 0.21824, 0.59908, 0.31814, 0.40902, 0.44614, 0.24611, 0.60536, 0.11423, 0.87072, 0.18405, 0.67716, 0.30817, 0.56165, 0.49435, 0.53355, 0.75811, 0.41804, 0.93653], "vertices": [4, 64, 29.19, -14.06, 0.02543, 65, 20.24, -13.17, 0.05755, 66, 14.02, -8.07, 0.03263, 67, 8.25, -3.82, 0.8844, 3, 64, 5.18, -26.8, 0.6298, 65, -3.18, -26.97, 0.25239, 67, 9.61, -30.97, 0.11781, 3, 63, 6.68, -6.98, 0.97268, 64, -8.88, -0.29, 0.02722, 67, -20.36, -32.46, 0.0001, 1, 63, 0.05, -5.56, 1, 1, 63, -4.07, -4.67, 1, 3, 63, -1.69, 6.39, 0.84535, 64, -4.04, 14.72, 0.08211, 65, -14.25, 14.1, 0.07255, 4, 63, 2.87, 27.59, 0.21041, 64, 15.12, 24.88, 0.11875, 65, 4.44, 25.11, 0.62712, 66, -16.26, 20.17, 0.04373, 2, 66, 9.65, 2.08, 0.84535, 67, -1.42, 1.52, 0.15465, 1, 67, 7.96, 1.99, 1, 3, 63, 7.46, 6.38, 0.58606, 64, 1.85, 7.72, 0.32419, 65, -8.05, 7.36, 0.08975, 4, 63, 12.78, 11.65, 0.15063, 64, 9.31, 7.04, 0.35809, 65, -0.57, 7.02, 0.48043, 66, -13.3, 1.64, 0.01084, 4, 63, 19.04, 15.81, 0.02611, 64, 16.52, 4.92, 0.02027, 65, 6.73, 5.23, 0.84553, 66, -5.92, 3.04, 0.10809, 3, 63, 26.34, 18.69, 0.00078, 65, 13.79, 1.81, 0.03345, 66, 1.93, 2.87, 0.96577, 4, 64, 26.48, -9.5, 0.0223, 65, 17.33, -8.73, 0.07456, 66, 9.53, -5.25, 0.18561, 67, 2.97, -4.34, 0.71753, 4, 64, 19.14, -4.41, 0.02705, 65, 9.77, -3.98, 0.56269, 66, 0.68, -4.06, 0.34955, 67, -4.75, -8.82, 0.06071, 4, 64, 11.5, -3.1, 0.14993, 65, 2.07, -3.01, 0.82731, 66, -6.73, -6.39, 0.00653, 67, -9.17, -15.2, 0.01623, 4, 63, 19, -1.47, 0.02699, 64, 3.27, -6.16, 0.88301, 65, -6.01, -6.44, 0.07002, 67, -9.89, -23.95, 0.01999, 4, 63, 13.13, -6.23, 0.5712, 64, -4.15, -4.74, 0.41792, 65, -13.49, -5.35, 0.00554, 67, -14.32, -30.06, 0.00534], "edges": [8, 10, 10, 12, 12, 14, 14, 16, 2, 0, 0, 16, 2, 4, 4, 6, 6, 8]}}, "light15": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "rxu3": {"rxu3": {"width": 42, "type": "mesh", "hull": 7, "height": 41, "triangles": [5, 6, 0, 11, 5, 0, 10, 5, 11, 12, 11, 0, 0, 13, 12, 10, 4, 5, 9, 4, 10, 12, 10, 11, 22, 9, 10, 12, 22, 10, 9, 8, 4, 8, 7, 4, 13, 22, 12, 22, 23, 8, 22, 8, 9, 23, 22, 13, 1, 13, 0, 14, 13, 1, 4, 7, 21, 20, 7, 8, 20, 8, 23, 19, 20, 23, 20, 18, 21, 20, 21, 7, 18, 20, 19, 24, 19, 23, 18, 19, 24, 14, 23, 13, 15, 24, 23, 16, 18, 24, 14, 15, 23, 16, 24, 15, 3, 4, 21, 3, 21, 18, 17, 3, 18, 16, 17, 18, 2, 3, 17, 15, 14, 1, 17, 16, 15, 1, 17, 15, 2, 17, 1], "uvs": [1, 0.11321, 1, 1, 0, 1, 0, 0.90306, 0, 0, 0.83913, 0, 1, 0, 0.21133, 0.3536, 0.39113, 0.3536, 0.55875, 0.24433, 0.65018, 0.10696, 0.84523, 0.16628, 0.77209, 0.3255, 0.74771, 0.53467, 0.69894, 0.76569, 0.46428, 0.89994, 0.29361, 0.88121, 0.17476, 0.94677, 0.17171, 0.74072, 0.27837, 0.62208, 0.23875, 0.48472, 0.12294, 0.33799, 0.65018, 0.34735, 0.45209, 0.54716, 0.33018, 0.74696], "vertices": [3, 60, 27.75, -5.65, 0.00476, 61, 16.32, -9.14, 0.0662, 62, 10.13, -4.62, 0.92904, 3, 59, 24.79, -24.82, 0.36596, 60, -0.41, -28.64, 0.59922, 61, -15.92, -25.96, 0.03481, 1, 59, -14.68, -10.47, 1, 1, 59, -13.32, -6.73, 1, 3, 59, -0.67, 28.06, 0.31712, 60, 4.78, 29.82, 0.15619, 61, 1.01, 30.24, 0.52669, 1, 62, 6.83, 2.88, 1, 2, 61, 20.43, -6.99, 3e-05, 62, 12.57, -0.68, 0.99997, 3, 59, 2.72, 11.41, 0.52043, 60, -0.84, 13.78, 0.19506, 61, -7.74, 15.67, 0.2845, 3, 59, 9.81, 8.83, 0.21683, 60, 3.94, 7.93, 0.47036, 61, -4.25, 8.97, 0.31281, 3, 59, 17.96, 10.63, 0.01186, 60, 11.86, 5.31, 0.10424, 61, 2.98, 4.8, 0.8839, 4, 59, 23.5, 14.61, 0.00199, 60, 18.65, 5.9, 0.00248, 61, 9.75, 4, 0.77388, 62, -2.22, 3.34, 0.22165, 3, 60, 21.95, -1.99, 0.00609, 61, 11.38, -4.38, 0.1787, 62, 3.46, -3.05, 0.81521, 4, 59, 25.25, 4.44, 0.00031, 60, 14.95, -3.74, 0.09996, 61, 4.17, -4.68, 0.77357, 62, -2.59, -6.98, 0.12617, 4, 59, 21.35, -3.27, 0.07853, 60, 7.66, -8.37, 0.70943, 61, -3.9, -7.74, 0.18921, 62, -7.98, -13.73, 0.02284, 4, 59, 16.19, -11.47, 0.45605, 60, -0.97, -12.77, 0.51803, 61, -13.25, -10.3, 0.02548, 62, -14.71, -20.7, 0.00045, 2, 59, 5.05, -13.28, 0.89843, 60, -11.47, -8.62, 0.10157, 2, 59, -1.43, -10.1, 0.99595, 60, -15.41, -2.58, 0.00405, 1, 59, -7.04, -10.92, 1, 2, 59, -4.27, -2.94, 0.99975, 61, -22.58, 9.8, 0.00025, 3, 59, 1.6, 0.1, 0.99913, 60, -7.58, 4.63, 0.00047, 61, -16.2, 8.08, 0.0004, 3, 59, 1.96, 5.96, 0.78014, 60, -4.27, 9.48, 0.11461, 61, -11.97, 12.16, 0.10525, 3, 59, -0.55, 13.28, 0.55667, 60, -2.69, 17.06, 0.14744, 61, -8.89, 19.26, 0.29589, 2, 61, 1.01, -0.56, 0.99807, 62, -7.42, -5.04, 0.00193, 3, 59, 9.51, 0.49, 0.89837, 60, -0.59, 0.92, 0.09953, 61, -10.1, 3.03, 0.0021, 1, 59, 1.9, -5.46, 1], "edges": [2, 4, 8, 10, 10, 12, 2, 0, 0, 12, 4, 6, 6, 8]}}, "light16": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "rxu6": {"rxu6": {"width": 40, "type": "mesh", "hull": 8, "height": 25, "triangles": [1, 13, 11, 11, 12, 10, 11, 0, 1, 0, 10, 6, 0, 11, 10, 10, 9, 6, 6, 7, 0, 1, 2, 13, 13, 12, 11, 12, 9, 10, 13, 8, 12, 12, 8, 9, 8, 5, 9, 6, 9, 5, 13, 2, 4, 2, 3, 4, 4, 8, 13, 4, 5, 8], "uvs": [1, 0.19654, 1, 1, 0.26529, 1, 0, 1, 0, 0.73414, 0, 0, 0.88609, 0, 1, 0, 0.28449, 0.3143, 0.55329, 0.20166, 0.74849, 0.19654, 0.79649, 0.43718, 0.55329, 0.47814, 0.41569, 0.8007], "vertices": [2, 71, 30.2, -4.31, 0.00909, 72, 14.22, -2.72, 0.99091, 2, 71, 21.72, -22.52, 0.38536, 72, 7.87, -21.77, 0.61464, 3, 70, 7.13, -8.26, 0.84041, 71, -4.92, -10.12, 0.15846, 72, -20.01, -12.48, 0.00112, 1, 70, -0.78, -1.18, 1, 1, 70, 3.65, 3.77, 1, 2, 70, 15.9, 17.45, 0.81668, 71, -3.99, 17.02, 0.18332, 1, 72, 11.45, 3.39, 1, 1, 72, 15.77, 1.95, 1, 2, 70, 19.13, 4, 0.33138, 71, 3.01, 5.09, 0.66862, 3, 70, 29.02, -1.07, 0.00556, 71, 13.94, 3.11, 0.92916, 72, -2.77, 2.81, 0.06527, 1, 72, 4.68, 0.47, 1, 2, 71, 20.28, -6.33, 0.20777, 72, 4.59, -5.85, 0.79223, 3, 70, 24.41, -6.22, 0.00249, 71, 11.03, -3.16, 0.96395, 72, -4.96, -3.74, 0.03356, 3, 70, 14.93, -8.56, 0.33162, 71, 2.63, -8.14, 0.64619, 72, -12.73, -9.65, 0.02219], "edges": [6, 8, 8, 10, 10, 12, 12, 14, 2, 0, 0, 14, 2, 4, 4, 6]}}, "light13": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "rxu5": {"rxu5": {"width": 32, "type": "mesh", "hull": 8, "height": 34, "triangles": [9, 5, 6, 6, 7, 0, 9, 6, 0, 8, 5, 9, 10, 8, 9, 10, 9, 0, 4, 5, 8, 4, 8, 10, 2, 3, 4, 10, 2, 4, 10, 0, 1, 2, 10, 1], "uvs": [1, 0.3231, 1, 1, 0.28362, 1, 0, 1, 0, 0.82757, 0, 0, 0.91961, 0, 1, 0, 0.47961, 0.44734, 0.78761, 0.21393, 0.55961, 0.67322], "vertices": [2, 68, 34.15, -4.69, 0.03357, 69, 16.62, -6.12, 0.96643, 2, 68, 19.32, -22.28, 0.83944, 69, 0.34, -22.39, 0.16056, 1, 68, 1.79, -7.51, 1, 1, 68, -5.15, -1.65, 1, 2, 68, -1.37, 2.83, 0.99785, 69, -18.14, 4.38, 0.00215, 2, 68, 16.77, 24.34, 0.24022, 69, 1.76, 24.28, 0.75978, 1, 69, 22.57, 3.47, 1, 1, 69, 24.39, 1.65, 1, 2, 68, 18.7, 2.82, 0.11111, 69, 1.86, 2.67, 0.88889, 1, 69, 14.44, 1.31, 1, 2, 68, 15.71, -4.7, 0.92014, 69, -1.76, -4.57, 0.07986], "edges": [6, 8, 8, 10, 10, 12, 12, 14, 2, 0, 0, 14, 2, 4, 4, 6]}}, "light14": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light19": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "rxu2": {"rxu2": {"width": 37, "type": "mesh", "hull": 8, "height": 25, "triangles": [9, 5, 6, 6, 7, 0, 10, 6, 0, 10, 0, 1, 10, 9, 6, 8, 9, 10, 8, 5, 9, 11, 8, 10, 4, 5, 8, 4, 2, 3, 8, 2, 4, 8, 11, 2, 11, 10, 1, 2, 11, 1], "uvs": [1, 0.22182, 1, 1, 0.18302, 1, 0, 1, 0, 0.63142, 0, 0, 0.7815, 0, 1, 0, 0.28334, 0.48806, 0.50821, 0.15526, 0.58431, 0.43174, 0.39059, 0.7799], "vertices": [4, 55, 36.49, -5.85, 0.00361, 56, 23.49, -12.39, 0.01905, 57, 16.7, -8.82, 0.08473, 58, 10.01, -4, 0.89262, 4, 55, 26.13, -22.32, 0.10727, 56, 9.2, -25.6, 0.28054, 57, 5.8, -24.94, 0.26662, 58, 9.7, -23.45, 0.34558, 2, 55, 0.54, -6.23, 0.99966, 58, -20.53, -22.97, 0.00034, 1, 55, -5.19, -2.63, 1, 2, 55, -0.29, 5.17, 0.89831, 56, -9.15, 7.82, 0.10169, 2, 55, 8.11, 18.53, 0.25243, 56, 2.44, 18.54, 0.74757, 1, 58, 2.01, 1.67, 1, 1, 58, 10.09, 1.55, 1, 2, 55, 10.49, 2.63, 0.36181, 56, 0.6, 2.56, 0.63819, 2, 56, 12.36, 2.1, 0.01254, 57, 2.56, 2.75, 0.98746, 4, 55, 20.67, -2.11, 0.0283, 56, 9.19, -4.66, 0.39944, 57, 1.02, -4.55, 0.50928, 58, -5.46, -9, 0.06299, 4, 55, 9.97, -5.66, 0.78323, 56, -2.07, -5.31, 0.19537, 57, -9.8, -7.74, 0.00535, 58, -12.76, -17.59, 0.01605], "edges": [6, 8, 8, 10, 10, 12, 12, 14, 2, 0, 0, 14, 2, 4, 4, 6]}}, "light17": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "rhand": {"rhand": {"x": 1.38, "width": 48, "y": 17.2, "height": 64}}, "rxu1": {"rxu1": {"width": 63, "type": "mesh", "hull": 9, "height": 37, "triangles": [16, 8, 0, 17, 16, 0, 17, 0, 1, 17, 1, 2, 2, 20, 17, 16, 7, 8, 18, 7, 16, 17, 18, 16, 15, 6, 7, 18, 15, 7, 19, 15, 18, 19, 18, 17, 14, 6, 15, 19, 14, 15, 20, 14, 19, 13, 6, 14, 20, 13, 14, 12, 6, 13, 12, 13, 20, 21, 12, 20, 17, 20, 19, 21, 20, 2, 11, 5, 12, 22, 11, 12, 22, 12, 21, 23, 11, 22, 22, 21, 2, 23, 22, 2, 10, 11, 23, 3, 9, 10, 3, 10, 23, 3, 23, 2, 12, 5, 6, 9, 5, 11, 10, 9, 11, 4, 5, 9, 4, 9, 3], "uvs": [1, 0.22598, 1, 0.44393, 1, 1, 0.15765, 1, 0, 1, 0, 0.69993, 0, 0, 0.80577, 0, 1, 0, 0.09466, 0.79679, 0.1678, 0.82101, 0.24704, 0.69647, 0.32628, 0.50274, 0.45225, 0.2848, 0.5518, 0.1395, 0.66761, 0.05301, 0.91549, 0.09799, 0.8972, 0.29517, 0.77123, 0.18447, 0.65745, 0.22253, 0.50914, 0.41625, 0.40958, 0.59961, 0.32222, 0.78296, 0.25517, 0.92825], "vertices": [2, 53, 13.12, 2.44, 0.01985, 54, 4.44, 3.54, 0.98015, 6, 49, 41.03, -28.67, 0.0083, 50, 30.26, -25.91, 0.03362, 51, 20.79, -22.28, 0.00143, 52, 20.7, -13.9, 0.00099, 53, 17.38, -4.41, 0.03582, 54, 10.15, -2.16, 0.91983, 4, 49, 25.08, -41.67, 0.07021, 50, 15.61, -40.36, 0.20314, 53, 28.26, -21.87, 0.05859, 54, 24.69, -16.71, 0.66806, 3, 47, 10.76, -2.17, 0.17309, 48, -0.62, -3.58, 0.82522, 49, -8.45, -0.52, 0.00169, 1, 47, 2.49, -7.68, 1, 4, 47, -3.67, 1.56, 0.91363, 48, -3.83, 10.97, 0.01639, 49, -6.11, 14.19, 0.04162, 50, -20.75, 12.27, 0.02837, 3, 47, -18.03, 23.1, 0.30905, 49, 13.96, 30.55, 0.15971, 50, -2.32, 30.46, 0.53125, 2, 52, 9.85, 3.47, 0.53684, 53, -1.69, 3.07, 0.46316, 2, 53, 8.7, 9.54, 0.52696, 54, -1.47, 9.45, 0.47304, 4, 47, 3.28, 1.88, 0.89214, 48, -0.39, 4.92, 0.08139, 49, -5.12, 7.3, 0.02348, 50, -19.11, 5.51, 0.00298, 4, 47, 7.61, 3.69, 0.14472, 48, 3.19, 1.88, 0.81139, 49, -2.91, 3.16, 0.04297, 50, -16.51, 1.6, 0.00093, 4, 47, 9.21, 10.3, 0.05026, 48, 9.8, 3.45, 0.10333, 49, 3.82, 2.2, 0.84231, 50, -9.72, 1.28, 0.00411, 4, 47, 9.39, 19.03, 0.02559, 48, 17.66, 7.25, 0.0057, 49, 12.53, 2.86, 0.63425, 50, -1.11, 2.77, 0.33446, 3, 47, 11.52, 30.14, 0.00586, 50, 10.2, 2.79, 0.97697, 51, -4.48, 1.95, 0.01717, 3, 47, 13.76, 38.09, 0.0009, 50, 18.43, 2.1, 0.02343, 51, 3.73, 2.88, 0.97567, 2, 51, 11.57, 1.44, 0.11831, 52, 1.02, 2.23, 0.88169, 2, 53, 6.1, 3.65, 0.83047, 54, -2.67, 3.12, 0.16953, 6, 49, 41.2, -20.17, 0.00174, 50, 29.62, -17.43, 0.0124, 51, 18.52, -14.09, 0.00663, 52, 14.7, -7.89, 0.02354, 53, 8.98, -3.16, 0.38931, 54, 1.67, -2.85, 0.56637, 6, 49, 39.36, -11.43, 0.00045, 50, 26.96, -8.91, 0.01153, 51, 14.24, -6.25, 0.03317, 52, 7.13, -3.15, 0.61843, 53, 0.07, -3.87, 0.32979, 54, -6.83, -5.57, 0.00662, 6, 49, 33.75, -6.76, 0.00071, 50, 20.92, -4.79, 0.03493, 51, 7.52, -3.39, 0.52881, 52, -0.13, -3.97, 0.38033, 53, -5.27, -8.86, 0.04584, 54, -10.91, -11.63, 0.00938, 6, 49, 22.29, -4.05, 0.01189, 50, 9.26, -3.18, 0.91288, 51, -4.24, -4.09, 0.04205, 52, -10.03, -10.35, 0.01147, 53, -9.41, -19.88, 0.00995, 54, -12.45, -23.31, 0.01174, 5, 49, 13.07, -3.47, 0.43547, 50, 0.02, -3.48, 0.55097, 52, -16.84, -16.6, 0.0009, 53, -11.15, -28.96, 0.00347, 54, -12.08, -32.54, 0.00918, 5, 48, 12.36, -1.66, 0.01374, 49, 4.33, -3.49, 0.95195, 50, -8.67, -4.33, 0.02782, 53, -12.23, -37.62, 0.00128, 54, -11.18, -41.23, 0.0052, 5, 48, 6.04, -4.28, 0.67624, 49, -2.51, -3.61, 0.31679, 50, -15.46, -5.11, 0.00487, 53, -12.98, -44.42, 0.00031, 54, -10.36, -48.02, 0.00179], "edges": [8, 10, 10, 12, 12, 14, 14, 16, 0, 16, 0, 2, 2, 4, 4, 6, 6, 8]}}, "light1": {"light1": {"scaleX": 20, "scaleY": 20, "x": 0.5, "width": 11, "y": 0.5, "height": 11}}, "rxu8": {"rxu8": {"width": 40, "type": "mesh", "hull": 8, "height": 49, "triangles": [6, 7, 0, 11, 6, 0, 10, 6, 11, 12, 11, 0, 10, 11, 12, 13, 10, 12, 12, 0, 1, 13, 12, 1, 14, 13, 1, 6, 9, 5, 10, 9, 6, 13, 9, 10, 14, 9, 13, 8, 5, 9, 8, 9, 14, 15, 14, 1, 15, 8, 14, 4, 5, 8, 4, 8, 15, 16, 4, 15, 3, 4, 16, 2, 3, 16, 16, 15, 1, 2, 16, 1], "uvs": [1, 0.13064, 1, 1, 0.23089, 1, 0, 1, 0, 0.78109, 0, 0, 0.85809, 0, 1, 0, 0.17969, 0.52509, 0.46769, 0.32917, 0.73329, 0.26387, 0.88049, 0.14371, 0.92529, 0.28215, 0.70769, 0.41277, 0.51249, 0.52248, 0.32369, 0.72885, 0.23409, 0.88036], "vertices": [1, 77, 3.52, -2.54, 1, 3, 74, 10.54, -35.9, 0.20702, 75, 11.06, -35.17, 0.20051, 76, -6.75, -34.98, 0.59247, 3, 74, -2.55, -8.06, 0.92501, 75, -14.39, -17.89, 0.03591, 76, -32.85, -18.71, 0.03908, 2, 74, -6.48, 0.3, 0.99984, 76, -40.69, -13.82, 0.00016, 1, 74, 3.23, 4.86, 1, 2, 75, 5.49, 27.83, 0.9997, 77, 5.27, 37.93, 0.0003, 3, 75, 33.89, 8.55, 0.11176, 76, 14.36, 9.6, 0.03914, 77, 9.22, 3.84, 0.84911, 1, 77, 9.87, -1.8, 1, 2, 74, 17.64, 3.7, 0.71101, 75, -3.01, 2.51, 0.28899, 2, 75, 11.91, 3.98, 0.99649, 77, -8.6, 17.49, 0.00351, 3, 75, 22.5, 0.66, 0.38774, 76, 3.28, 1.27, 0.60128, 77, -4.2, 7.31, 0.01099, 3, 75, 30.68, 2.22, 0.04327, 76, 11.39, 3.15, 0.22398, 77, 2.33, 2.14, 0.73275, 1, 76, 9.33, -3.55, 1, 3, 74, 31.61, -13.07, 0.00086, 75, 17.55, -4.8, 0.18782, 76, -1.45, -4.38, 0.81133, 3, 74, 23.42, -8.3, 0.02664, 75, 8.07, -4.86, 0.84507, 76, -10.92, -4.81, 0.12828, 3, 74, 11.06, -5.76, 0.78655, 75, -3.86, -8.98, 0.16239, 76, -22.67, -9.4, 0.05106, 3, 74, 2.81, -5.68, 0.92986, 75, -10.99, -13.11, 0.03749, 76, -29.64, -13.8, 0.03266], "edges": [6, 8, 8, 10, 10, 12, 12, 14, 2, 0, 0, 14, 2, 4, 4, 6]}}, "light11": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light5": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "rxu7": {"rxu7": {"rotation": -57.49, "x": 14.13, "width": 18, "y": 2.65, "height": 33}}, "light4": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light10": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light9": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light8": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light7": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light6": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "xiaoguo8": {"Image 3": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 1": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 11": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 9": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 13": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 7": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 5": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}}, "xiaoguo9": {"Image 3": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 1": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 11": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 9": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 13": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 7": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 5": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}}, "xiaoguo7": {"Image 3": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 1": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 11": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 9": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 13": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 7": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 5": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}}, "xiaoguo4": {"Image 3": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 1": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 11": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 9": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 13": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 7": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 5": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}}, "xiaoguo5": {"Image 3": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 1": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 11": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 9": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 13": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 7": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 5": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}}, "xiaoguo2": {"Image 3": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 1": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 11": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 9": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 13": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 7": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 5": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}}, "xiaoguo3": {"Image 3": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 1": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 11": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 9": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 13": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 7": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}, "Image 5": {"x": 0.5, "width": 66, "y": 0.5, "height": 57}}, "cut": {"cut": {"color": "ce3a3a00", "end": "light1", "type": "clipping", "vertexCount": 115, "vertices": [-279.91, 10.09, -279.14, 15.25, -277.42, 19.64, -270.93, 24.61, -263.29, 25.18, -257.56, 21.93, -255.27, 24.23, -255.23, 26.4, -251.31, 29.52, -247.63, 30.56, -239.71, 30.64, -238.35, 33.92, -234.83, 36.72, -232.35, 39.44, -227.71, 41.36, -217.87, 41.52, -213.95, 40.16, -210.51, 37.28, -206.35, 36.56, -202.27, 34.56, -197.15, 38.32, -192.91, 41.6, -186.67, 44.16, -176.67, 44.24, -171.63, 42.8, -166.43, 39.44, -161.39, 39.6, -156.43, 38.4, -153.47, 37.12, -151.47, 34.32, -150.27, 47.2, -146.75, 50.08, -140.03, 51.68, -130.83, 51.76, -123.55, 51.28, -118.11, 49.76, -114.03, 46.8, -112.27, 49.68, -106.67, 51.68, -95.36, 51.52, 48.8, 52.4, 49.2, 54.64, 51.52, 57.68, 54.08, 58.96, 56.08, 61.44, 60.4, 64.16, 65.76, 65.68, 74.16, 65.84, 78.88, 64.48, 82.8, 62.24, 85.28, 59.68, 85.12, 52.16, 82.96, 50.16, 82.86, 48.76, 84.28, 48.22, 84.3, 45.56, 85.38, 45.42, 88.22, 47.26, 93.27, 48.05, 98.76, 47.94, 101.94, 47.17, 104.66, 44.99, 107.73, 47.35, 111.8, 50.18, 117.58, 51.89, 123.6, 52.6, 131.33, 51.3, 136.52, 48.94, 139.59, 45.69, 142.12, 41.45, 142.54, 36.25, 147.43, 37.26, 153.1, 36.43, 158.29, 34.54, 161, 32.77, 164.48, 36.55, 169.44, 40.21, 175.04, 42.57, 181.18, 43.39, 188.02, 42.98, 192.63, 41.33, 195.99, 38.91, 196.28, 41.86, 199.59, 44.57, 203.84, 46.7, 208.73, 47.64, 215.81, 47.76, 219.65, 46.87, 223.42, 45.34, 226.14, 43.28, 227.67, 40.92, 229.21, 40.62, 230.92, 39.26, 232.16, 37.43, 232.45, 34.6, 238.88, 34.96, 244.96, 33.95, 249.21, 31.24, 251.86, 28.76, 252.04, 25.52, 253.34, 24.87, 253.45, 23.33, 254.52, 23.39, 256.05, 25.87, 259.53, 27.23, 264.31, 27.82, 269.92, 27.58, 273.87, 25.87, 275.93, 23.98, 276.29, 22.45, 278.35, 21.5, 279.47, 20.44, 279.71, 16.96, 281.3, 16.02, 281.36, 8.11]}}, "head": {"head": {"x": -3.57, "width": 143, "y": 37.71, "height": 147}}, "gold": {"gold": {"x": 1.81, "width": 579, "y": 32.77, "height": 68}}, "lxu3": {"lxu3": {"width": 45, "type": "mesh", "hull": 7, "height": 42, "triangles": [3, 4, 5, 12, 3, 13, 2, 3, 12, 15, 2, 12, 11, 5, 6, 13, 5, 11, 3, 5, 13, 10, 11, 6, 13, 11, 10, 14, 13, 10, 9, 10, 6, 14, 10, 9, 12, 13, 14, 15, 14, 9, 16, 15, 9, 15, 1, 2, 1, 15, 16, 15, 12, 14, 16, 9, 8, 17, 16, 8, 1, 17, 18, 17, 1, 16, 8, 9, 6, 7, 8, 6, 19, 20, 7, 20, 18, 8, 20, 8, 7, 17, 8, 18, 6, 19, 7, 18, 20, 19, 19, 6, 0, 18, 19, 0, 0, 1, 18], "uvs": [1, 1, 0, 1, 0, 0.30499, 0, 0.0947, 0, 0, 0.20275, 0, 1, 0, 0.76311, 0.55794, 0.61804, 0.50613, 0.49288, 0.3507, 0.39902, 0.16785, 0.32506, 0.06118, 0.09466, 0.23794, 0.20844, 0.1648, 0.27671, 0.26842, 0.33644, 0.44518, 0.40186, 0.58537, 0.47582, 0.75299, 0.67208, 0.78956, 0.8712, 0.7987, 0.7688, 0.6768], "vertices": [1, 20, -10.53, 10.64, 1, 3, 21, 11.46, 25.39, 0.41687, 22, -11.32, 22.77, 0.56319, 25, 26.89, 27.72, 0.01994, 5, 21, 30.15, 2.96, 0.0001, 22, 16.03, 12.57, 0.2416, 23, 4.53, 13.59, 0.10422, 24, 8.76, 10.94, 0.00709, 25, 10.54, 3.54, 0.64699, 1, 25, 5.6, -3.78, 1, 2, 24, 14.58, -0.47, 0.00046, 25, 3.37, -7.08, 0.99954, 3, 20, 28.39, -28.56, 0.00048, 24, 6.45, -4.62, 0.86937, 25, -4.19, -1.97, 0.13015, 4, 20, -7.38, -31.24, 0.55795, 22, 12.3, -34.07, 0.06697, 23, 9.15, -32.97, 0.17748, 24, -25.5, -20.93, 0.1976, 5, 20, 1.49, -7.08, 0.86236, 21, -3.03, -10.86, 0.04787, 22, -5.93, -15.89, 0.03802, 23, -12.02, -18.32, 0.02943, 24, -26.66, 4.78, 0.02232, 5, 20, 8.17, -8.76, 0.50718, 21, 3.38, -8.35, 0.29713, 22, -1.61, -10.53, 0.13287, 23, -8.72, -12.28, 0.04123, 24, -19.86, 5.81, 0.0216, 5, 20, 14.27, -14.85, 0.14258, 21, 11.88, -9.76, 0.13704, 22, 6.48, -7.54, 0.44501, 23, -1.29, -7.9, 0.22506, 24, -11.87, 2.56, 0.05031, 5, 20, 19.06, -22.19, 0.04891, 21, 20.04, -12.96, 0.01022, 22, 15.15, -6.27, 0.03346, 23, 7.01, -5.1, 0.54257, 24, -4.62, -2.36, 0.36484, 4, 20, 22.71, -26.41, 0.01643, 21, 25.47, -14.27, 0.00033, 23, 12.01, -2.62, 0.01772, 24, 0.38, -4.83, 0.96552, 4, 22, 17.18, 7.59, 0.08812, 23, 6.55, 8.9, 0.13006, 24, 6.24, 6.49, 0.09328, 25, 5.44, 3.59, 0.68854, 4, 22, 18.27, 1.72, 0.00426, 23, 8.66, 3.32, 0.05173, 24, 3.08, 1.43, 0.83629, 25, -0.53, 3.91, 0.10772, 4, 22, 13.12, 0.37, 0.00342, 23, 3.83, 1.06, 0.94111, 24, -1.64, 3.91, 0.03104, 25, -0.63, 9.23, 0.02444, 2, 22, 5.22, 0.44, 0.998, 25, 1.29, 16.89, 0.002, 2, 21, 8.72, 0.43, 0.99957, 25, 2.15, 23.42, 0.00043, 4, 20, 13.77, 2.06, 0.12188, 21, 1.66, 3.71, 0.83227, 22, -9.08, -0.91, 0.04578, 25, 3.34, 31.11, 7e-05, 2, 20, 4.85, 2.93, 0.99698, 21, -6.11, -0.76, 0.00302, 3, 20, -4.11, 2.64, 0.99944, 23, -22.83, -21.31, 0.00025, 24, -35.59, 11.58, 0.00031, 5, 20, 0.86, -2.12, 0.98356, 21, -6.42, -7.19, 0.00415, 22, -10.7, -14.38, 0.00479, 23, -16.98, -17.68, 0.00417, 24, -29.16, 9.11, 0.00333], "edges": [0, 2, 0, 12, 8, 10, 10, 12, 6, 8, 2, 4, 4, 6]}}, "lxu4": {"lxu4": {"width": 43, "type": "mesh", "hull": 9, "height": 39, "triangles": [17, 14, 15, 3, 4, 14, 14, 4, 15, 15, 7, 16, 4, 6, 15, 15, 6, 7, 4, 5, 6, 17, 15, 16, 17, 13, 12, 17, 16, 13, 16, 7, 13, 13, 7, 8, 18, 14, 17, 18, 3, 14, 19, 18, 11, 8, 9, 11, 18, 12, 11, 18, 17, 12, 11, 12, 8, 12, 13, 8, 3, 20, 21, 20, 3, 19, 3, 18, 19, 10, 19, 11, 10, 20, 19, 1, 22, 0, 22, 23, 0, 1, 2, 22, 3, 21, 2, 2, 21, 22, 21, 20, 22, 20, 10, 22, 23, 10, 9, 23, 22, 10, 0, 23, 9, 0, 9, 8, 9, 10, 11], "uvs": [1, 1, 0.80753, 1, 0.62297, 1, 0, 1, 0, 0.1426, 0, 0, 0.05739, 0, 0.23897, 0, 1, 0, 0.94446, 0.57912, 0.76883, 0.53973, 0.68846, 0.42158, 0.57237, 0.1984, 0.39079, 0.09337, 0.0693, 0.20824, 0.15265, 0.16558, 0.27469, 0.18855, 0.35507, 0.29358, 0.42353, 0.44127, 0.45925, 0.5824, 0.47116, 0.77604, 0.51581, 0.92373, 0.86111, 0.8384, 0.95041, 0.73994], "vertices": [1, 26, -9.5, 3.11, 1, 2, 26, -2.26, 7.12, 0.99837, 27, -14.88, 0.89, 0.00163, 3, 26, 4.68, 10.97, 0.8399, 27, -10.23, 7.32, 0.15171, 28, -21.75, 0.75, 0.00839, 5, 26, 28.11, 23.95, 0.01181, 27, 5.47, 29.02, 0.60029, 28, -13.39, 26.2, 0.32049, 29, -10.17, 33.22, 0.04448, 30, 7.72, 37.74, 0.02293, 4, 27, 32.57, 9.43, 0.00515, 28, 18.38, 15.76, 0.04516, 29, 14.59, 10.75, 0.09644, 30, 11.1, 4.47, 0.85324, 3, 28, 23.66, 14.02, 2e-05, 29, 18.71, 7.01, 1e-05, 30, 11.66, -1.06, 0.99996, 1, 30, 9.2, -1.31, 1, 2, 29, 11.8, -0.6, 0.00466, 30, 1.44, -2.1, 0.99534, 4, 26, 9.4, -31, 0.20525, 27, 11.88, -28.67, 0.01645, 28, 10.24, -26.83, 0.73401, 29, -10.19, -24.83, 0.04429, 3, 26, 0.54, -10.09, 0.77274, 27, -5.03, -13.5, 0.0518, 28, -10.47, -17.51, 0.17545, 3, 26, 7.89, -7.77, 0.57252, 27, 0.64, -8.28, 0.23471, 28, -6.65, -10.82, 0.19277, 4, 26, 13.15, -10.13, 0.21956, 27, 6.4, -8.18, 0.33322, 28, -1.2, -8.97, 0.44666, 29, -13.36, -3.87, 0.00056, 4, 26, 21.73, -15.32, 0.04351, 27, 16.38, -9.24, 0.00919, 28, 8.63, -6.95, 0.74383, 29, -3.56, -6.02, 0.20347, 3, 26, 30.55, -15.12, 0.00159, 28, 14.96, -0.81, 0.00798, 29, 4.72, -2.99, 0.99043, 4, 27, 28.75, 8.52, 0.01404, 28, 15.02, 13.73, 0.09847, 29, 10.69, 10.26, 0.20881, 30, 7.87, 6.72, 0.67869, 4, 27, 27.99, 4.64, 0.00345, 28, 15.48, 9.8, 0.0501, 29, 9.52, 6.49, 0.27614, 30, 4.48, 4.7, 0.67031, 3, 28, 12.99, 5.1, 0.03215, 29, 5.33, 3.2, 0.81031, 30, -0.84, 5.06, 0.15754, 3, 28, 8.02, 3.09, 0.59927, 29, -0.03, 3.4, 0.3708, 30, -4.69, 8.79, 0.02993, 4, 27, 12.46, 1.5, 0.04279, 28, 1.63, 2.09, 0.92917, 29, -6.27, 5.09, 0.02114, 30, -8.2, 14.22, 0.0069, 4, 27, 7.1, 3.48, 0.85246, 28, -4.08, 2.35, 0.13636, 29, -11.38, 7.65, 0.00743, 30, -10.28, 19.54, 0.00375, 5, 26, 14.62, 6.49, 0.25255, 27, 0.68, 7.49, 0.67074, 28, -11.41, 4.22, 0.07029, 29, -17.32, 12.34, 0.00454, 30, -11.55, 27, 0.00187, 5, 26, 10.15, 10.6, 0.56081, 27, -5.12, 9.31, 0.39584, 28, -17.48, 4.2, 0.04206, 29, -22.87, 14.79, 0.00116, 30, -14.04, 32.54, 0.00013, 1, 26, -1.22, 0.49, 1, 3, 26, -2.72, -4.73, 0.96198, 27, -10.26, -10.04, 0.00389, 28, -16.51, -15.8, 0.03414], "edges": [0, 16, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 4, 6, 0, 2, 2, 4]}}, "lxu1": {"lxu1": {"width": 84, "type": "mesh", "hull": 9, "height": 37, "triangles": [17, 19, 18, 3, 4, 17, 2, 17, 18, 3, 17, 2, 24, 18, 21, 2, 18, 26, 19, 5, 20, 4, 5, 19, 19, 20, 18, 17, 4, 19, 20, 16, 21, 5, 6, 16, 5, 16, 20, 15, 6, 7, 7, 21, 15, 16, 6, 15, 16, 15, 21, 18, 20, 21, 14, 7, 8, 23, 22, 7, 22, 21, 7, 21, 22, 23, 13, 14, 8, 14, 23, 7, 24, 23, 14, 23, 24, 21, 31, 13, 8, 13, 24, 14, 12, 31, 8, 31, 24, 13, 25, 31, 12, 25, 24, 31, 26, 25, 12, 18, 24, 25, 25, 26, 18, 11, 12, 8, 27, 12, 11, 26, 12, 27, 2, 26, 27, 28, 11, 10, 27, 11, 28, 2, 28, 29, 2, 27, 28, 8, 10, 11, 9, 10, 8, 9, 8, 0, 30, 9, 0, 30, 29, 10, 30, 10, 9, 28, 10, 29, 30, 0, 1, 29, 30, 1, 1, 2, 29], "uvs": [1, 0.66815, 1, 1, 0.11995, 1, 0, 1, 0, 0.7062, 0, 0.50209, 0, 0, 0.32871, 0, 1, 0, 0.94433, 0.61971, 0.84986, 0.65777, 0.74776, 0.58512, 0.65785, 0.42253, 0.54509, 0.2115, 0.43538, 0.07658, 0.16871, 0.06274, 0.06509, 0.22188, 0.0529, 0.92761, 0.12909, 0.8515, 0.07881, 0.67161, 0.11233, 0.38793, 0.21138, 0.22188, 0.31043, 0.18728, 0.40947, 0.23226, 0.49024, 0.33604, 0.55728, 0.46404, 0.58776, 0.56436, 0.67309, 0.74425, 0.7569, 0.8342, 0.859, 0.89647, 0.96871, 0.8169, 0.59995, 0.30144], "vertices": [4, 5, -1.41, -0.74, 0.99523, 7, -17.97, -17.61, 0.00277, 8, -30.22, -15.09, 0.002, 14, 43.84, 66.62, 0, 1, 5, 1.24, 11.25, 1, 8, 7, 25.62, 43.35, 0.00266, 8, 18.39, 41.94, 0.00808, 9, 15.52, 39.53, 0.00137, 10, 17.56, 35.66, 0.00112, 11, 25.2, 25.73, 0.00373, 12, 23.53, 15.6, 0.00205, 13, 19.93, 4.76, 0.00108, 14, 8.8, 0.37, 0.9799, 2, 13, 18.69, -5.24, 0.03912, 14, 2.7, -7.64, 0.96088, 2, 13, 7.9, -3.91, 0.99727, 14, -5.95, -1.05, 0.00273, 2, 12, 12.34, -2.17, 0.15357, 13, 0.41, -2.98, 0.84643, 2, 11, 16.25, -11.56, 0.51553, 12, -3.89, -11.21, 0.48447, 1, 10, 5.1, -3.34, 1, 4, 5, -6.75, -24.87, 0.71085, 7, -0.49, -35.09, 0.14468, 8, -14.29, -34, 0.14447, 14, 24.17, 81.6, 0, 4, 5, 2.77, -3.5, 0.97307, 7, -13.4, -15.57, 0.01661, 8, -25.49, -13.45, 0.01031, 14, 39.58, 63.98, 0, 4, 5, 10.82, -3.83, 0.96508, 7, -8.78, -8.96, 0.02708, 8, -20.33, -7.26, 0.00784, 14, 35.89, 56.82, 0, 5, 5, 18.61, -8.31, 0.0948, 6, 8.88, -4.11, 0.58965, 7, -0.81, -4.8, 0.29897, 8, -12.04, -3.79, 0.01658, 14, 28.55, 51.63, 0, 5, 5, 24.69, -15.82, 0.03943, 6, 18.15, -6.81, 0.00714, 7, 8.78, -3.71, 0.68443, 8, -2.39, -3.52, 0.26901, 14, 19.19, 49.26, 0, 3, 5, 32.25, -25.49, 0.00795, 8, 9.89, -3.39, 0.90272, 9, -2.52, -2.91, 0.08933, 3, 8, 20.15, -1.26, 0.00367, 9, 7.96, -3.04, 0.99147, 10, -4.16, -1.72, 0.00486, 1, 11, 4.94, -2.71, 1, 2, 11, 15.4, -1.73, 0.13837, 12, 0.62, -2.44, 0.86163, 1, 14, 3.26, -2.48, 1, 8, 7, 28.96, 38.92, 0.006, 8, 21.34, 37.24, 0.02113, 9, 17.4, 34.31, 0.00459, 10, 17.53, 30.12, 0.00485, 11, 21.89, 21.28, 0.01933, 12, 18.36, 13.6, 0.02161, 13, 14.57, 6.19, 0.11266, 14, 4.9, 4.32, 0.80983, 8, 7, 36.66, 37.2, 0.00092, 8, 28.86, 34.87, 0.00524, 9, 24.23, 30.38, 0.00153, 10, 22.6, 24.08, 0.00216, 11, 22.39, 13.41, 0.01617, 12, 14.6, 6.67, 0.05924, 13, 7.44, 2.82, 0.81446, 14, -2.96, 4.99, 0.10026, 8, 7, 42.09, 27.79, 0.00053, 8, 33.47, 25.03, 0.00637, 9, 26.62, 19.78, 0.00266, 10, 21.2, 13.3, 0.00633, 11, 14.88, 5.56, 0.20175, 12, 4.06, 4.02, 0.67684, 13, -2.63, 6.9, 0.08005, 14, -9.6, 13.59, 0.02547, 8, 7, 40.55, 17.56, 0.00044, 8, 31.07, 14.97, 0.01026, 9, 22.12, 10.47, 0.00866, 10, 13.77, 6.11, 0.10845, 11, 4.63, 4.18, 0.81077, 12, -5.36, 8.3, 0.03515, 13, -7.7, 15.91, 0.00826, 14, -9.44, 23.93, 0.018, 8, 7, 35.57, 10.77, 0.00051, 8, 25.53, 8.63, 0.0183, 9, 15.35, 5.46, 0.05846, 10, 5.7, 3.73, 0.79125, 11, -3.28, 7.06, 0.10715, 12, -10.52, 14.94, 0.00697, 13, -7.95, 24.33, 0.00226, 14, -5.42, 31.33, 0.01508, 8, 7, 28.51, 6.06, 0.00093, 8, 18.1, 4.54, 0.05571, 9, 7.21, 3.07, 0.81817, 10, -2.77, 4.28, 0.0954, 11, -9.78, 12.52, 0.01551, 12, -13.12, 23.02, 0.00191, 13, -5.28, 32.38, 0.00044, 14, 0.95, 36.93, 0.01193, 7, 7, 21, 3.98, 0.00365, 8, 10.44, 3.11, 0.77316, 9, -0.58, 3.31, 0.1992, 10, -10, 7.18, 0.00694, 11, -13.88, 19.14, 0.00507, 12, -13.07, 30.82, 0.00062, 14, 8.11, 40, 0.01136, 7, 7, 13.67, 3.35, 0.0897, 8, 3.08, 3.1, 0.8904, 9, -7.77, 4.88, 0.00543, 10, -16.21, 11.13, 0.0008, 11, -16.54, 26.01, 0.00221, 12, -11.67, 38.04, 0.00013, 14, 15.29, 41.61, 0.01133, 8, 6, 21.6, 0.27, 0.00415, 7, 9.23, 4.16, 0.60487, 8, -1.27, 4.29, 0.37309, 9, -11.76, 6.98, 0.00066, 10, -19.24, 14.47, 0.00019, 11, -17, 30.49, 0.00194, 12, -9.67, 42.08, 3e-05, 14, 19.8, 41.4, 0.01508, 5, 6, 12.45, 3.72, 0.48272, 7, -0.54, 3.8, 0.48464, 8, -11.04, 4.76, 0.02507, 11, -20.09, 39.77, 0.00026, 14, 29.43, 43.07, 0.00732, 5, 5, 19.85, 0.85, 0.00424, 6, 4.67, 4.13, 0.98224, 7, -7.87, 1.18, 0.01174, 8, -18.57, 2.76, 0.00077, 14, 36.35, 46.65, 0.00101, 2, 5, 11.98, 4.95, 0.79961, 6, -4.14, 3.01, 0.20039, 1, 5, 2.34, 4.07, 1, 4, 5, 28.47, -21.24, 0.02141, 7, 15.39, -3.44, 0.02609, 8, 4.22, -3.81, 0.9525, 14, 12.68, 48.11, 0], "edges": [12, 14, 14, 16, 10, 12, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 16]}}, "lxu2": {"lxu2": {"width": 44, "type": "mesh", "hull": 10, "height": 24, "triangles": [18, 3, 14, 18, 14, 15, 17, 18, 15, 15, 16, 17, 3, 4, 14, 15, 14, 5, 15, 5, 13, 5, 14, 4, 5, 6, 13, 15, 7, 16, 15, 13, 7, 16, 7, 8, 13, 6, 7, 17, 8, 12, 17, 16, 8, 19, 3, 18, 19, 18, 11, 18, 12, 11, 18, 17, 12, 11, 12, 9, 12, 8, 9, 1, 10, 0, 3, 19, 2, 1, 2, 10, 19, 11, 2, 2, 11, 10, 10, 9, 0, 10, 11, 9], "uvs": [1, 0.67373, 1, 1, 0.82881, 1, 0, 1, 0, 0.46573, 0, 0.27906, 0, 0, 0.25863, 0, 0.49136, 0, 1, 0, 0.951, 0.67373, 0.77645, 0.51906, 0.65136, 0.22573, 0.08409, 0.10306, 0.11027, 0.48706, 0.19463, 0.26839, 0.31681, 0.23639, 0.45354, 0.31106, 0.58154, 0.56706, 0.68045, 0.80173], "vertices": [2, 15, -1.99, -4.79, 0.92969, 16, -7.67, -10.96, 0.07031, 1, 15, -5.15, 2.37, 1, 3, 15, 1.75, 5.41, 0.99938, 16, -10.27, -0.41, 4e-05, 19, 0.85, 35.66, 0.00058, 3, 16, 9.18, 30.44, 0.09538, 17, 15.05, 26.47, 0.02118, 19, 20.43, 4.9, 0.88343, 4, 16, 20.03, 23.6, 0.00574, 17, 20.85, 15.03, 0.00042, 18, 17.66, 6.34, 0.01214, 19, 9.61, -1.99, 0.9817, 2, 18, 17.15, 1.89, 0.3211, 19, 5.83, -4.39, 0.6789, 2, 18, 16.38, -4.77, 0.99064, 19, 0.18, -7.99, 0.00936, 1, 18, 5.08, -3.46, 1, 4, 15, 25, -10.58, 0.00014, 16, 17.95, -0.65, 0.01001, 17, 6.63, -4.72, 0.98975, 18, -5.1, -2.29, 0.00011, 3, 15, 4.52, -19.59, 0.40545, 16, 6.01, -19.58, 0.5945, 17, -13.33, -14.85, 5e-05, 2, 15, -0.02, -3.92, 0.93334, 16, -6.52, -9.13, 0.06666, 2, 15, 8.5, -4.23, 0.57966, 16, 0.72, -4.62, 0.42034, 3, 15, 16.38, -8.46, 0.04405, 16, 9.61, -3.72, 0.7143, 17, -2.1, -3.08, 0.24165, 1, 18, 12.99, -1.89, 1, 3, 16, 17.01, 19.77, 0.02645, 17, 16.29, 13.29, 0.01872, 19, 7.44, 2.38, 0.95484, 4, 16, 19.47, 13.83, 0.01033, 17, 15.36, 6.93, 0.02487, 18, 8.61, 2.61, 0.00946, 19, 1.02, 2.69, 0.95534, 4, 16, 17.25, 8.87, 0.02429, 17, 10.91, 3.81, 0.1824, 18, 3.18, 2.47, 0.6246, 19, -2.52, 6.82, 0.16871, 4, 16, 12.52, 4.74, 0.10254, 17, 4.74, 2.69, 0.82695, 18, -2.59, 4.94, 0.00816, 19, -4.24, 12.86, 0.06235, 4, 15, 15.89, 0.28, 0.00659, 16, 4.32, 3.25, 0.8924, 17, -3.07, 5.62, 0.05641, 19, -2.08, 20.91, 0.0446, 4, 15, 9.64, 3.68, 0.77063, 16, -2.76, 2.58, 0.20372, 17, -9.5, 8.67, 0.00308, 19, 0.34, 27.6, 0.02257], "edges": [16, 18, 12, 14, 14, 16, 10, 12, 6, 8, 8, 10, 2, 4, 4, 6, 2, 0, 0, 18]}}, "lxu7": {"lxu7": {"width": 21, "type": "mesh", "hull": 9, "height": 34, "triangles": [4, 5, 6, 11, 7, 8, 12, 4, 6, 12, 6, 7, 11, 12, 7, 10, 11, 8, 12, 11, 10, 13, 12, 10, 8, 0, 10, 13, 10, 9, 13, 3, 4, 13, 4, 12, 0, 9, 10, 14, 13, 9, 3, 13, 14, 2, 14, 9, 2, 9, 0, 3, 14, 2, 2, 0, 1], "uvs": [1, 0.71134, 1, 1, 0.69618, 1, 0, 1, 0, 0.12404, 0, 0, 0.26951, 0, 0.52551, 0, 1, 0, 0.71447, 0.62475, 0.69008, 0.42146, 0.65351, 0.2031, 0.23294, 0.26334, 0.23904, 0.5231, 0.37313, 0.80546], "vertices": [2, 40, 2.77, -6.28, 0.88531, 41, -3.97, -11.03, 0.11469, 1, 40, -4.17, 0.66, 1, 1, 40, 0.35, 5.17, 1, 2, 40, 10.68, 15.51, 0.9698, 41, -11.08, 11.04, 0.0302, 1, 41, 18.47, 7.31, 1, 1, 41, 22.65, 6.78, 1, 1, 41, 21.94, 1.17, 1, 1, 41, 21.27, -4.16, 1, 2, 40, 19.88, -23.38, 1e-05, 41, 20.03, -14.05, 0.99999, 2, 40, 9.1, -4.12, 0.70358, 41, -0.3, -5.45, 0.29642, 2, 40, 14.35, -8.64, 0.13783, 41, 6.62, -5.8, 0.86217, 2, 40, 20.14, -13.35, 0.00968, 41, 14.08, -5.97, 0.99032, 1, 41, 13.16, 3.05, 1, 2, 40, 18.6, 0.5, 0.05141, 41, 4.38, 4.03, 0.94859, 2, 40, 9.82, 5.29, 0.96258, 41, -5.5, 2.43, 0.03742], "edges": [2, 0, 0, 16, 14, 16, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6]}}, "lxu8": {"lxu8": {"width": 40, "type": "mesh", "hull": 8, "height": 48, "triangles": [4, 13, 12, 13, 5, 6, 13, 4, 5, 11, 13, 6, 15, 12, 11, 14, 4, 12, 6, 7, 11, 11, 12, 13, 3, 15, 16, 3, 14, 15, 14, 3, 4, 15, 11, 16, 16, 11, 10, 14, 12, 15, 10, 11, 7, 3, 16, 17, 17, 9, 8, 8, 9, 7, 16, 10, 17, 17, 10, 9, 9, 10, 7, 2, 0, 1, 3, 18, 2, 2, 18, 0, 3, 17, 18, 18, 8, 0, 18, 17, 8, 0, 8, 7], "uvs": [1, 0.87786, 1, 1, 0.82049, 1, 0, 1, 0, 0.14187, 0, 0, 0.1197, 0, 1, 0, 0.93569, 0.67786, 0.81729, 0.48586, 0.65089, 0.3792, 0.42689, 0.2512, 0.1901, 0.1792, 0.1293, 0.0512, 0.0781, 0.31787, 0.28929, 0.4032, 0.45569, 0.4912, 0.66369, 0.63253, 0.75329, 0.8272], "vertices": [1, 42, -0.36, -4.94, 1, 1, 42, -5.95, -3.16, 1, 3, 42, -3.77, 3.68, 0.97612, 43, -14.72, 10.56, 0.01488, 44, -19.7, 20.88, 0.009, 3, 42, 6.15, 34.96, 0.22542, 43, 7.04, 35.13, 0.2898, 44, 10.42, 33.91, 0.48478, 3, 44, 26.78, -3.9, 0.00228, 45, 12.93, 0.99, 0.04153, 46, 0.91, 1.93, 0.95618, 2, 45, 17.75, -3.83, 0.00129, 46, 7.66, 1, 0.99871, 2, 45, 14.36, -7.21, 0.17014, 46, 7, -3.74, 0.82986, 4, 43, 16.45, -26.64, 0.41032, 44, -7.23, -26.03, 0.24856, 45, -10.53, -32.11, 0.33196, 46, 2.19, -38.63, 0.00916, 3, 42, 9.57, -5.39, 0.96867, 43, -6.2, -3.14, 0.03113, 45, -31.72, -7.29, 0.00019, 4, 42, 19.79, -3.66, 0.11374, 43, 3.84, -5.71, 0.85571, 44, -9.78, -1.72, 0.00526, 45, -21.86, -10.45, 0.02529, 4, 43, 12.08, -4.12, 0.56266, 44, -1.64, -3.78, 0.36241, 45, -13.53, -9.37, 0.07264, 46, -13.93, -22.31, 0.00228, 4, 43, 22.62, -1.49, 0.00549, 44, 9.02, -5.86, 0.40075, 45, -2.85, -7.38, 0.554, 46, -6.62, -14.27, 0.03976, 2, 45, 6.29, -3.12, 0.83722, 46, -1.9, -5.36, 0.16278, 2, 45, 12.35, -5.75, 0.23812, 46, 4.52, -3.79, 0.76188, 4, 42, 36.42, 22.08, 0.00184, 43, 29.48, 11.08, 0.00841, 44, 20.55, 2.62, 0.54095, 45, 4.75, 4.75, 0.44879, 3, 42, 29.96, 15.27, 0.006, 43, 20.81, 7.47, 0.0341, 44, 11.17, 3.02, 0.9599, 3, 42, 23.92, 10.2, 0.01678, 43, 13.24, 5.29, 0.32728, 44, 3.39, 4.26, 0.65593, 3, 42, 14.94, 4.33, 0.15746, 43, 2.64, 3.56, 0.81946, 44, -6.94, 7.18, 0.02308, 3, 42, 4.95, 3.74, 0.93341, 43, -6.73, 7.07, 0.04988, 44, -13.94, 14.33, 0.01671], "edges": [2, 0, 0, 14, 10, 12, 12, 14, 6, 8, 8, 10, 2, 4, 4, 6]}}, "fireball": {"firebird_000": {"x": 0.5, "width": 45, "height": 44}, "firebird_011": {"x": 0.5, "width": 45, "height": 44}, "firebird_001": {"x": 0.5, "width": 45, "height": 44}, "firebird_002": {"x": 0.5, "width": 45, "height": 44}, "firebird_003": {"x": 0.5, "width": 45, "height": 44}, "firebird_004": {"x": 0.5, "width": 45, "height": 44}, "firebird_005": {"x": 0.5, "width": 45, "height": 44}, "firebird_006": {"x": 0.5, "width": 45, "height": 44}, "firebird_007": {"x": 0.5, "width": 45, "height": 44}, "firebird_008": {"x": 0.5, "width": 45, "height": 44}, "firebird_009": {"x": 0.5, "width": 45, "height": 44}, "firebird_010": {"x": 0.5, "width": 45, "height": 44}}, "lxu5": {"lxu5": {"width": 33, "type": "mesh", "hull": 7, "height": 32, "triangles": [9, 10, 5, 3, 4, 5, 3, 5, 10, 11, 3, 10, 11, 10, 9, 2, 3, 11, 6, 9, 5, 8, 9, 6, 12, 9, 8, 11, 9, 12, 7, 8, 6, 13, 12, 8, 2, 11, 12, 0, 7, 6, 13, 8, 7, 1, 13, 7, 2, 12, 13, 2, 13, 1, 1, 7, 0], "uvs": [1, 1, 0.81127, 1, 0, 1, 0, 0.2368, 0, 0, 0.11696, 0, 1, 0, 0.90436, 0.7368, 0.67163, 0.4648, 0.41175, 0.2568, 0.18678, 0.1488, 0.1829, 0.4168, 0.41951, 0.6448, 0.60957, 0.8768], "vertices": [2, 31, -4.63, -2.76, 0.98742, 32, -15.88, -5.98, 0.01258, 1, 31, -0.06, 1.48, 1, 3, 31, 19.56, 19.7, 0.69991, 32, 3.3, 20.87, 0.21184, 33, -4.03, 22.29, 0.08825, 4, 31, 36.17, 1.8, 0.00907, 32, 23.17, 6.67, 0.01607, 33, 12.25, 4.09, 0.25586, 34, 1.29, 4.6, 0.719, 1, 34, 7.66, 0.49, 1, 3, 32, 27.09, -0.87, 0.00454, 33, 14.43, -4.13, 0.09528, 34, 5.56, -2.76, 0.90018, 3, 32, 10.16, -24.58, 0.96558, 33, -7.29, -23.56, 0.03442, 34, -10.24, -27.23, 0, 2, 31, 3.42, -6.78, 0.69102, 32, -7.19, -8.31, 0.30898, 3, 31, 14.97, -7.94, 0.00871, 32, 4.35, -7.12, 0.99061, 33, -9.13, -5.25, 0.00067, 3, 32, 14.75, -4.01, 0.43566, 33, 1.7, -4.49, 0.53891, 34, -6.62, -6.47, 0.02543, 3, 32, 21.88, 0.02, 0.00755, 33, 9.54, -2.12, 0.52721, 34, 0.31, -2.11, 0.46524, 4, 31, 27.83, 1.91, 0.05584, 32, 14.98, 5.11, 0.20284, 33, 3.91, 4.36, 0.73393, 34, -6.82, 2.65, 0.00739, 3, 31, 17.14, 1.95, 0.2694, 32, 4.5, 3, 0.72107, 33, -6.77, 4.59, 0.00954, 1, 31, 7.5, 3.12, 1], "edges": [0, 12, 8, 10, 10, 12, 4, 6, 6, 8, 0, 2, 2, 4]}}, "lxu6": {"lxu6": {"width": 45, "type": "mesh", "hull": 6, "height": 31, "triangles": [2, 3, 11, 3, 10, 11, 3, 4, 10, 10, 4, 5, 11, 10, 9, 9, 10, 5, 11, 12, 13, 7, 12, 8, 11, 9, 12, 12, 9, 8, 8, 9, 5, 2, 11, 13, 13, 7, 6, 6, 7, 5, 7, 13, 12, 7, 8, 5, 1, 6, 0, 6, 5, 0, 2, 13, 1, 1, 13, 6], "uvs": [1, 1, 0.77502, 1, 0, 1, 0, 0.26586, 0, 0, 1, 0, 0.87742, 0.80676, 0.76364, 0.51773, 0.63564, 0.26173, 0.43084, 0.17089, 0.21182, 0.10895, 0.24311, 0.40211, 0.46497, 0.43102, 0.62426, 0.73244], "vertices": [1, 35, -4.41, 2.45, 1, 5, 35, 4.95, 6.3, 0.96473, 36, -7.87, 2.07, 0.02634, 37, -16.84, 8.7, 0.00712, 38, -17.34, 23.19, 0.00022, 39, -35.45, -0.55, 0.00159, 4, 36, 10.43, 31.76, 0.11605, 37, 10.91, 29.83, 0.28225, 38, 17.53, 24.14, 0.07959, 39, -10.07, 23.37, 0.52212, 3, 36, 29.81, 19.81, 0.00337, 37, 24.69, 11.72, 0.02195, 39, 5.54, 6.8, 0.97467, 1, 39, 11.19, 0.81, 1, 3, 35, 7.37, -26.22, 0.1694, 36, 13.21, -22.82, 0.63568, 37, -6.12, -22.09, 0.19492, 2, 35, 2.97, -0.99, 0.98169, 36, -5.19, -5, 0.01831, 3, 35, 11.11, -7.34, 0.24455, 36, 5.13, -5.34, 0.755, 37, -7.38, -2.88, 0.00045, 4, 35, 19.45, -12.49, 0.02929, 36, 14.91, -4.6, 0.31778, 37, 2.01, -5.71, 0.65006, 38, -10.45, 0.49, 0.00287, 3, 36, 22.14, 1.76, 0.0054, 37, 11.05, -2.37, 0.54692, 38, -1.16, -2.08, 0.44768, 2, 38, 8.74, -3.73, 0.70912, 39, 1.94, -3.27, 0.29088, 4, 36, 20.47, 12.72, 0.01764, 37, 13.43, 8.45, 0.17756, 38, 7.09, 5.31, 0.54602, 39, -5.32, 2.38, 0.25878, 4, 36, 14.47, 4.69, 0.02137, 37, 4.94, 3.12, 0.89119, 38, -2.91, 5.94, 0.07298, 39, -13.2, -3.82, 0.01446, 5, 35, 14.38, 1.2, 0.04171, 36, 2.75, 3.49, 0.85065, 37, -6.42, 6.21, 0.08689, 38, -10.33, 15.09, 0.00759, 39, -24.82, -1.93, 0.01316], "edges": [8, 10, 0, 10, 4, 6, 6, 8, 0, 2, 2, 4]}}, "light26": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light27": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light25": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light28": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "lhand": {"lhand": {"x": 3.68, "width": 61, "y": 9, "height": 27}}, "light22": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light23": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light20": {"light1": {"width": 11, "y": -0.07, "height": 11}}, "light21": {"light1": {"width": 11, "y": -0.07, "height": 11}}}}], "skeleton": {"images": "./images/", "x": -287.69, "width": 579, "y": -12.47, "spine": "3.8.75", "audio": "C:/Users/<USER>/Desktop/go88/MD5 太秀/龙头", "hash": "GyqwZ70bYg+fBiI1MS8EcdGseZI", "height": 158.24}, "slots": [{"attachment": "Image 9", "blend": "additive", "name": "xiaoguo2", "bone": "<PERSON><PERSON><PERSON><PERSON>"}, {"attachment": "Image 9", "blend": "additive", "name": "xiaoguo3", "bone": "xiaoguo2"}, {"attachment": "Image 9", "blend": "additive", "name": "xiaoguo4", "bone": "xiaoguo3"}, {"attachment": "Image 9", "blend": "additive", "name": "xiaoguo5", "bone": "xiaoguo4"}, {"attachment": "Image 9", "blend": "additive", "name": "xiaoguo7", "bone": "xiaoguo6"}, {"attachment": "Image 9", "blend": "additive", "name": "xiaoguo8", "bone": "xiaoguo7"}, {"attachment": "gold", "name": "gold", "bone": "all"}, {"attachment": "cut", "name": "cut", "bone": "root"}, {"attachment": "light1", "blend": "additive", "name": "light1", "bone": "light1"}, {"attachment": "Image 9", "blend": "additive", "name": "xiaoguo9", "bone": "xiaoguo8"}, {"attachment": "body", "name": "body", "bone": "all"}, {"attachment": "lhand", "name": "lhand", "bone": "lhand"}, {"attachment": "head", "name": "head", "bone": "all2"}, {"attachment": "lxu1", "name": "lxu1", "bone": "lxu1"}, {"attachment": "lxu2", "name": "lxu2", "bone": "lxu2"}, {"attachment": "lxu3", "name": "lxu3", "bone": "lxu3"}, {"attachment": "lxu4", "name": "lxu4", "bone": "lxu4"}, {"attachment": "lxu5", "name": "lxu5", "bone": "all3"}, {"attachment": "lxu6", "name": "lxu6", "bone": "lxu6"}, {"attachment": "lxu7", "name": "lxu7", "bone": "lxu7"}, {"attachment": "lxu8", "name": "lxu8", "bone": "lxu8"}, {"attachment": "rhand", "name": "rhand", "bone": "rhand"}, {"attachment": "rxu1", "name": "rxu1", "bone": "rxu1"}, {"attachment": "rxu2", "name": "rxu2", "bone": "rxu2"}, {"attachment": "rxu3", "name": "rxu3", "bone": "rxu3"}, {"attachment": "rxu4", "name": "rxu4", "bone": "rxu4"}, {"attachment": "rxu5", "name": "rxu5", "bone": "rxu5"}, {"attachment": "rxu6", "name": "rxu6", "bone": "rxu6"}, {"attachment": "rxu7", "name": "rxu7", "bone": "rxu7"}, {"attachment": "rxu8", "name": "rxu8", "bone": "rxu8"}, {"attachment": "firebird_011", "blend": "additive", "name": "fireball", "bone": "fireball"}, {"attachment": "firebird_011", "blend": "additive", "name": "fireball2", "bone": "fireball2"}, {"attachment": "light1", "name": "light15", "bone": "light13"}, {"attachment": "light1", "name": "light22", "bone": "light20"}, {"attachment": "light1", "name": "light16", "bone": "light14"}, {"color": "ff9191ff", "attachment": "light1", "blend": "additive", "name": "light28", "bone": "light26"}, {"attachment": "light1", "name": "light25", "bone": "light23"}, {"color": "73ff87ff", "attachment": "light1", "name": "light27", "bone": "light25"}, {"attachment": "light1", "name": "light9", "bone": "light7"}, {"attachment": "light1", "name": "light13", "bone": "light11"}, {"attachment": "light1", "blend": "additive", "name": "light4", "bone": "light2"}, {"attachment": "light1", "name": "light17", "bone": "light15"}, {"color": "ff82c7ff", "attachment": "light1", "blend": "additive", "name": "light23", "bone": "light21"}, {"attachment": "light1", "name": "light8", "bone": "light6"}, {"attachment": "light1", "name": "light26", "bone": "light24"}, {"attachment": "light1", "name": "light14", "bone": "light12"}, {"attachment": "light1", "name": "light5", "bone": "light3"}, {"color": "ff7e7eff", "attachment": "light1", "blend": "additive", "name": "light11", "bone": "light9"}, {"attachment": "light1", "name": "light6", "bone": "light4"}, {"color": "fbf77bff", "attachment": "light1", "name": "light20", "bone": "light18"}, {"attachment": "light1", "name": "light21", "bone": "light19"}, {"attachment": "light1", "blend": "additive", "name": "light10", "bone": "light8"}, {"attachment": "light1", "name": "light19", "bone": "light17"}, {"attachment": "light1", "name": "light7", "bone": "light5"}], "bones": [{"name": "root"}, {"parent": "root", "name": "all"}, {"parent": "all", "name": "all2", "x": -1.62, "y": 34.55}, {"parent": "all", "name": "lhand", "x": -75.87, "y": -1.34}, {"parent": "all", "name": "rhand", "x": 62.92, "y": 13.57}, {"parent": "all2", "rotation": -167.53, "name": "lxu1", "length": 13.63, "x": -9.29, "y": -22.53}, {"parent": "lxu1", "rotation": -34.75, "name": "all4", "length": 11.48, "x": 13.66, "y": 0.12}, {"parent": "all4", "rotation": -22.72, "name": "all5", "length": 10.86, "x": 11.48}, {"parent": "all5", "rotation": 4.87, "name": "all6", "length": 11.72, "x": 10.86}, {"parent": "all6", "rotation": 12.4, "name": "all7", "length": 11.28, "x": 11.72}, {"parent": "all7", "rotation": 20.09, "name": "all8", "length": 12.53, "x": 11.28}, {"parent": "all8", "rotation": 36.37, "name": "all9", "length": 13.58, "x": 12.53}, {"parent": "all9", "rotation": 32.14, "name": "all10", "length": 10.26, "x": 13.58}, {"parent": "all10", "rotation": 36.18, "name": "all11", "length": 12.51, "x": 10.26}, {"parent": "all11", "rotation": 30.25, "name": "all12", "length": 6.76, "x": 12.51}, {"parent": "all2", "rotation": 156.25, "name": "lxu2", "length": 10.49, "x": -26.82, "y": -17.54}, {"parent": "lxu2", "rotation": -34.01, "name": "all14", "length": 9.84, "x": 10.49}, {"parent": "all14", "rotation": 30.85, "name": "all15", "length": 9.62, "x": 9.84}, {"parent": "all15", "rotation": 33.49, "name": "all16", "length": 10.05, "x": 9.62}, {"parent": "all16", "rotation": 50.95, "name": "all17", "length": 6.68, "x": 10.06, "y": 0.13}, {"parent": "all2", "rotation": 175.71, "name": "lxu3", "length": 10.27, "x": -29.77, "y": -0.39}, {"parent": "lxu3", "rotation": -35.52, "name": "all19", "length": 10, "x": 10.27}, {"parent": "all19", "rotation": -29.73, "name": "all20", "length": 9.15, "x": 10}, {"parent": "all20", "rotation": -10.24, "name": "all21", "length": 7.93, "x": 9.15}, {"parent": "all21", "rotation": 52.73, "name": "all22", "length": 6.75, "x": 7.93}, {"parent": "all22", "rotation": 61.1, "name": "all23", "length": 5.72, "x": 6.75}, {"parent": "all2", "rotation": 151.01, "name": "lxu4", "length": 10.83, "x": -28.87, "y": 12.54}, {"parent": "lxu4", "rotation": -25.14, "name": "all13", "length": 10.27, "x": 10.83}, {"parent": "all13", "rotation": -17.68, "name": "all18", "length": 9.43, "x": 10.27}, {"parent": "all18", "rotation": 24.03, "name": "all24", "length": 9.33, "x": 9.43}, {"parent": "all24", "rotation": 41.99, "name": "all25", "length": 8.88, "x": 9.33}, {"parent": "all2", "rotation": 137.12, "name": "all3", "length": 12.23, "x": -27.34, "y": 25.34}, {"parent": "all3", "rotation": -11.58, "name": "all26", "length": 12.11, "x": 12.13, "y": -0.09}, {"parent": "all26", "rotation": 12.64, "name": "all27", "length": 9.79, "x": 12.11}, {"parent": "all27", "rotation": -15.32, "name": "all28", "length": 7.31, "x": 9.79}, {"parent": "all2", "rotation": 157.66, "name": "lxu6", "length": 10.1, "x": -22.22, "y": 28.16}, {"parent": "lxu6", "rotation": -36.01, "name": "all30", "length": 10.98, "x": 10.1}, {"parent": "all30", "rotation": 21.07, "name": "all31", "length": 10.78, "x": 10.98}, {"parent": "all31", "rotation": 35.73, "name": "all32", "length": 9.48, "x": 10.78}, {"parent": "all32", "rotation": -41.75, "name": "all33", "length": 9.15, "x": 9.48}, {"parent": "all2", "rotation": 135, "name": "lxu7", "length": 12.67, "x": -25.54, "y": 21.63}, {"parent": "lxu7", "rotation": -37.82, "name": "all34", "length": 15.35, "x": 12.67}, {"parent": "all2", "rotation": 107.6, "name": "lxu8", "length": 13.97, "x": -23.88, "y": 28.93}, {"parent": "lxu8", "rotation": 23.93, "name": "all35", "length": 11.97, "x": 13.97}, {"parent": "all35", "rotation": 25.07, "name": "all36", "length": 14.5, "x": 11.97}, {"parent": "all36", "rotation": -21.6, "name": "all37", "length": 11.04, "x": 14.39, "y": -0.05}, {"parent": "all37", "rotation": -37.15, "name": "all38", "length": 7.49, "x": 11.04}, {"parent": "all2", "rotation": -33.69, "name": "rxu1", "length": 7.85, "x": 24.12, "y": -27.01}, {"parent": "rxu1", "rotation": 63.05, "name": "all39", "length": 7.05, "x": 7.85}, {"parent": "all39", "rotation": 21.47, "name": "all40", "length": 13.37, "x": 7.05}, {"parent": "all40", "rotation": -5.46, "name": "all41", "length": 14.21, "x": 13.37}, {"parent": "all41", "rotation": -11.25, "name": "all42", "length": 9.59, "x": 14.21}, {"parent": "all42", "rotation": -29.42, "name": "all43", "length": 9.38, "x": 9.59}, {"parent": "all43", "rotation": -36.61, "name": "all44", "length": 7.99, "x": 9.38}, {"parent": "all44", "rotation": -13.09, "name": "all45", "length": 6.52, "x": 7.99}, {"parent": "all2", "rotation": 32.15, "name": "rxu2", "length": 10.58, "x": 31.93, "y": -17.79}, {"parent": "rxu2", "rotation": 15.09, "name": "all46", "length": 9.24, "x": 10.58}, {"parent": "all46", "rotation": -13.17, "name": "all47", "length": 10.51, "x": 9.24}, {"parent": "all47", "rotation": -33.17, "name": "all48", "length": 8.06, "x": 10.51}, {"parent": "all2", "rotation": 19.98, "name": "rxu3", "length": 10.49, "x": 36.15, "y": 3.07}, {"parent": "rxu3", "rotation": 30.78, "name": "all49", "length": 9.92, "x": 10.49}, {"parent": "all49", "rotation": 11.68, "name": "all50", "length": 9.96, "x": 9.92}, {"parent": "all50", "rotation": -30.65, "name": "all51", "length": 7.53, "x": 9.96}, {"parent": "all2", "rotation": 12.14, "name": "rxu4", "length": 12.18, "x": 31.93, "y": 16.64}, {"parent": "rxu4", "rotation": 49.93, "name": "all52", "length": 9.56, "x": 12.18}, {"parent": "all52", "rotation": -2.56, "name": "all53", "length": 10.84, "x": 9.56}, {"parent": "all53", "rotation": -24.57, "name": "all54", "length": 9.84, "x": 10.84}, {"parent": "all54", "rotation": -37.79, "name": "all55", "length": 7.69, "x": 9.84}, {"parent": "all2", "rotation": 40.14, "name": "rxu5", "length": 17.08, "x": 31.8, "y": 28.8}, {"parent": "rxu5", "rotation": 4.86, "name": "all56", "length": 19.01, "x": 17.08}, {"parent": "all2", "rotation": 41.84, "name": "rxu6", "length": 14.78, "x": 28.73, "y": 31.61}, {"parent": "rxu6", "rotation": -16.88, "name": "all57", "length": 16.38, "x": 14.78}, {"parent": "all57", "rotation": -6.53, "name": "all58", "length": 13.76, "x": 16.38}, {"parent": "all2", "rotation": 57.49, "name": "rxu7", "length": 13.81, "x": 32.57, "y": 21.37}, {"parent": "all2", "rotation": 64.82, "name": "rxu8", "length": 18.95, "x": 27.96, "y": 29.95}, {"parent": "rxu8", "rotation": -30.64, "name": "all60", "length": 20.73, "x": 18.95}, {"parent": "all60", "rotation": -2.24, "name": "all61", "length": 11.61, "x": 19.17, "y": -0.48}, {"parent": "all61", "rotation": 51.45, "name": "all62", "length": 8.89, "x": 11.61}, {"scaleX": 0.9717, "parent": "all", "scaleY": 0.8441, "rotation": -20.33, "name": "<PERSON><PERSON><PERSON><PERSON>", "x": 6.67, "y": 95.48}, {"scaleX": 0.896, "parent": "all", "scaleY": 0.896, "name": "xiaoguo2", "x": -13.46, "y": 105.5}, {"scaleX": 1.0805, "parent": "all", "scaleY": 1.0805, "rotation": 2.21, "name": "xiaoguo3", "x": -34.52, "y": 106.88}, {"scaleX": 1.1513, "parent": "all", "scaleY": 1.1513, "rotation": 19.63, "name": "xiaoguo4", "x": -50.7, "y": 96.04}, {"scaleX": 0.8823, "parent": "all", "scaleY": 0.8823, "rotation": 36.6, "name": "xiaoguo6", "x": -65.07, "y": 79.1}, {"scaleX": 1.0927, "parent": "all", "scaleY": 1.0927, "rotation": 47.52, "name": "xiaoguo7", "x": -72.9, "y": 62.83}, {"scaleX": 0.6005, "parent": "all2", "scaleY": 0.7425, "name": "fireball", "x": -14.48, "y": 9.34}, {"scaleX": 0.6005, "parent": "all2", "scaleY": 0.7425, "name": "fireball2", "x": 17.42, "y": 7.97}, {"parent": "root", "name": "light"}, {"scaleX": 1.8533, "parent": "light", "scaleY": 1.8533, "name": "light2", "x": -96.68, "y": 39.96}, {"parent": "light", "name": "light3", "x": 215.42, "y": 2.86}, {"scaleX": 0.8434, "parent": "light", "scaleY": 0.8434, "name": "light4", "x": 268.57, "y": 3.96}, {"parent": "light", "name": "light5", "x": -265.79, "y": -6.9}, {"scaleX": 1.5283, "parent": "light", "scaleY": 1.5283, "name": "light6", "x": 4.34, "y": 70.69}, {"scaleX": 0.5607, "parent": "light", "scaleY": 0.5607, "name": "light7", "x": -63.17, "y": 55.88}, {"parent": "light", "name": "light8", "x": 174.83, "y": 15.83}, {"scaleX": 1.2583, "parent": "light", "scaleY": 1.2583, "name": "light9", "x": -271.76, "y": 5.88}, {"scaleX": 0.677, "parent": "light", "scaleY": 0.677, "name": "light11", "x": -181.63, "y": 18.2}, {"scaleX": 1.5283, "parent": "light", "scaleY": 1.5283, "name": "light12", "x": -142.45, "y": 23.29}, {"parent": "light", "name": "light13", "x": 163.43, "y": 19.23}, {"parent": "light", "name": "light14", "x": 223.09, "y": 26.47}, {"scaleX": 1.8309, "parent": "light", "scaleY": 1.8309, "name": "light15", "x": 273.38, "y": 4.81}, {"scaleX": 1.2478, "parent": "light", "scaleY": 1.2478, "name": "light17", "x": 131.33, "y": 26.36}, {"scaleX": 1.8196, "parent": "light", "scaleY": 1.8196, "name": "light18", "x": -52.5, "y": 33.73}, {"scaleX": 1.4427, "parent": "light", "scaleY": 1.4427, "name": "light19", "x": 189.81, "y": 22.27}, {"parent": "light", "name": "light20", "x": -158.85, "y": 17.74}, {"scaleX": 1.8309, "parent": "light", "scaleY": 1.8309, "name": "light21", "x": 122.14, "y": 47.95}, {"parent": "light", "name": "light23", "x": 263.28, "y": 9.88}, {"scaleX": 1.5283, "parent": "light", "scaleY": 1.5283, "name": "light24", "x": 35.6, "y": 76.19}, {"parent": "light", "name": "light25", "x": 58.88, "y": 54.99}, {"parent": "light", "name": "light26", "x": -130.76, "y": 43.56}, {"scaleX": 1.0927, "parent": "all", "scaleY": 1.0927, "rotation": 11.25, "name": "xiaoguo8", "x": -86.66, "y": 59.29}, {"scaleX": 2.3926, "parent": "root", "scaleY": 2.3926, "name": "light1", "x": -13.11, "y": 67.25}], "animations": {"animation": {"slots": {"fireball2": {"attachment": [{"name": "firebird_000"}, {"name": "firebird_001", "time": 0.0333}, {"name": "firebird_002", "time": 0.0667}, {"name": "firebird_003", "time": 0.1}, {"name": "firebird_004", "time": 0.1333}, {"name": "firebird_005", "time": 0.1667}, {"name": "firebird_006", "time": 0.2}, {"name": "firebird_007", "time": 0.2333}, {"name": "firebird_008", "time": 0.2667}, {"name": "firebird_009", "time": 0.3}, {"name": "firebird_010", "time": 0.3333}, {"name": "firebird_011", "time": 0.3667}, {"name": "firebird_000", "time": 0.4}, {"name": "firebird_001", "time": 0.4333}, {"name": "firebird_002", "time": 0.4667}, {"name": "firebird_003", "time": 0.5}, {"name": "firebird_004", "time": 0.5333}, {"name": "firebird_005", "time": 0.5667}, {"name": "firebird_006", "time": 0.6}, {"name": "firebird_007", "time": 0.6333}, {"name": "firebird_008", "time": 0.6667}, {"name": "firebird_009", "time": 0.7}, {"name": "firebird_010", "time": 0.7333}, {"name": "firebird_011", "time": 0.7667}, {"name": "firebird_000", "time": 0.8}, {"name": "firebird_001", "time": 0.8333}, {"name": "firebird_002", "time": 0.8667}, {"name": "firebird_003", "time": 0.9}, {"name": "firebird_004", "time": 0.9333}, {"name": "firebird_005", "time": 0.9667}, {"name": "firebird_006", "time": 1}, {"name": "firebird_007", "time": 1.0333}, {"name": "firebird_008", "time": 1.0667}, {"name": "firebird_009", "time": 1.1}, {"name": "firebird_010", "time": 1.1333}, {"name": "firebird_011", "time": 1.1667}, {"name": "firebird_000", "time": 1.2}, {"name": "firebird_001", "time": 1.2333}, {"name": "firebird_002", "time": 1.2667}, {"name": "firebird_003", "time": 1.3}, {"name": "firebird_004", "time": 1.3333}, {"name": "firebird_005", "time": 1.3667}, {"name": "firebird_006", "time": 1.4}, {"name": "firebird_007", "time": 1.4333}, {"name": "firebird_008", "time": 1.4667}, {"name": "firebird_009", "time": 1.5}, {"name": "firebird_010", "time": 1.5333}, {"name": "firebird_011", "time": 1.5667}, {"name": "firebird_000", "time": 1.6}, {"name": "firebird_001", "time": 1.6333}, {"name": "firebird_002", "time": 1.6667}, {"name": "firebird_003", "time": 1.7}, {"name": "firebird_004", "time": 1.7333}, {"name": "firebird_005", "time": 1.7667}, {"name": "firebird_006", "time": 1.8}, {"name": "firebird_007", "time": 1.8333}, {"name": "firebird_008", "time": 1.8667}, {"name": "firebird_009", "time": 1.9}, {"name": "firebird_010", "time": 1.9333}, {"name": "firebird_011", "time": 1.9667}, {"name": "firebird_000", "time": 2}, {"name": "firebird_001", "time": 2.0333}, {"name": "firebird_002", "time": 2.0667}, {"name": "firebird_003", "time": 2.1}, {"name": "firebird_004", "time": 2.1333}, {"name": "firebird_005", "time": 2.1667}, {"name": "firebird_006", "time": 2.2}, {"name": "firebird_007", "time": 2.2333}, {"name": "firebird_008", "time": 2.2667}, {"name": "firebird_009", "time": 2.3}, {"name": "firebird_010", "time": 2.3333}, {"name": "firebird_011", "time": 2.3667}, {"name": "firebird_000", "time": 2.4}, {"name": "firebird_001", "time": 2.4333}, {"name": "firebird_002", "time": 2.4667}, {"name": "firebird_003", "time": 2.5}, {"name": "firebird_004", "time": 2.5333}, {"name": "firebird_005", "time": 2.5667}, {"name": "firebird_006", "time": 2.6}, {"name": "firebird_007", "time": 2.6333}, {"name": "firebird_008", "time": 2.6667}, {"name": "firebird_009", "time": 2.7}, {"name": "firebird_010", "time": 2.7333}, {"name": "firebird_011", "time": 2.7667}, {"name": "firebird_000", "time": 2.8}, {"name": "firebird_001", "time": 2.8333}, {"name": "firebird_002", "time": 2.8667}, {"name": "firebird_003", "time": 2.9}, {"name": "firebird_004", "time": 2.9333}, {"name": "firebird_005", "time": 2.9667}, {"name": "firebird_006", "time": 3}, {"name": "firebird_007", "time": 3.0333}, {"name": "firebird_008", "time": 3.0667}, {"name": "firebird_009", "time": 3.1}, {"name": "firebird_010", "time": 3.1333}, {"name": "firebird_011", "time": 3.1667}, {"name": "firebird_000", "time": 3.2}, {"name": "firebird_001", "time": 3.2333}, {"name": "firebird_002", "time": 3.2667}, {"name": "firebird_003", "time": 3.3}, {"name": "firebird_004", "time": 3.3333}, {"name": "firebird_005", "time": 3.3667}, {"name": "firebird_006", "time": 3.4}, {"name": "firebird_007", "time": 3.4333}, {"name": "firebird_008", "time": 3.4667}, {"name": "firebird_009", "time": 3.5}, {"name": "firebird_010", "time": 3.5333}, {"name": "firebird_011", "time": 3.5667}, {"name": "firebird_000", "time": 3.6}, {"name": "firebird_001", "time": 3.6333}, {"name": "firebird_002", "time": 3.6667}, {"name": "firebird_003", "time": 3.7}, {"name": "firebird_004", "time": 3.7333}, {"name": "firebird_005", "time": 3.7667}, {"name": "firebird_006", "time": 3.8}, {"name": "firebird_007", "time": 3.8333}, {"name": "firebird_008", "time": 3.8667}, {"name": "firebird_009", "time": 3.9}, {"name": "firebird_010", "time": 3.9333}, {"name": "firebird_011", "time": 3.9667}, {"name": "firebird_000", "time": 4}, {"name": "firebird_001", "time": 4.0333}, {"name": "firebird_002", "time": 4.0667}, {"name": "firebird_003", "time": 4.1}, {"name": "firebird_004", "time": 4.1333}, {"name": "firebird_005", "time": 4.1667}, {"name": "firebird_006", "time": 4.2}, {"name": "firebird_007", "time": 4.2333}, {"name": "firebird_008", "time": 4.2667}, {"name": "firebird_009", "time": 4.3}, {"name": "firebird_010", "time": 4.3333}, {"name": "firebird_011", "time": 4.3667}, {"name": "firebird_000", "time": 4.4}, {"name": "firebird_001", "time": 4.4333}, {"name": "firebird_002", "time": 4.4667}, {"name": "firebird_003", "time": 4.5}, {"name": "firebird_004", "time": 4.5333}, {"name": "firebird_005", "time": 4.5667}, {"name": "firebird_006", "time": 4.6}, {"name": "firebird_007", "time": 4.6333}, {"name": "firebird_008", "time": 4.6667}, {"name": "firebird_009", "time": 4.7}, {"name": "firebird_010", "time": 4.7333}, {"name": "firebird_011", "time": 4.7667}, {"name": "firebird_000", "time": 4.8}, {"name": "firebird_001", "time": 4.8333}, {"name": "firebird_002", "time": 4.8667}, {"name": "firebird_003", "time": 4.9}, {"name": "firebird_004", "time": 4.9333}, {"name": "firebird_005", "time": 4.9667}, {"name": "firebird_006", "time": 5}, {"name": "firebird_007", "time": 5.0333}, {"name": "firebird_008", "time": 5.0667}, {"name": "firebird_009", "time": 5.1}, {"name": "firebird_010", "time": 5.1333}, {"name": "firebird_011", "time": 5.1667}, {"name": "firebird_000", "time": 5.2}, {"name": "firebird_001", "time": 5.2333}, {"name": "firebird_002", "time": 5.2667}, {"name": "firebird_003", "time": 5.3}, {"name": "firebird_004", "time": 5.3333}, {"name": "firebird_005", "time": 5.3667}, {"name": "firebird_006", "time": 5.4}, {"name": "firebird_007", "time": 5.4333}, {"name": "firebird_008", "time": 5.4667}, {"name": "firebird_009", "time": 5.5}, {"name": "firebird_010", "time": 5.5333}, {"name": "firebird_011", "time": 5.5667}, {"name": "firebird_000", "time": 5.6}, {"name": "firebird_001", "time": 5.6333}, {"name": "firebird_002", "time": 5.6667}, {"name": "firebird_003", "time": 5.7}, {"name": "firebird_004", "time": 5.7333}, {"name": "firebird_005", "time": 5.7667}, {"name": "firebird_006", "time": 5.8}, {"name": "firebird_007", "time": 5.8333}, {"name": "firebird_008", "time": 5.8667}, {"name": "firebird_009", "time": 5.9}, {"name": "firebird_010", "time": 5.9333}, {"name": "firebird_011", "time": 5.9667}, {"name": "firebird_000", "time": 6}, {"name": "firebird_001", "time": 6.0333}, {"name": "firebird_002", "time": 6.0667}, {"name": "firebird_003", "time": 6.1}, {"name": "firebird_004", "time": 6.1333}, {"name": "firebird_005", "time": 6.1667}, {"name": "firebird_006", "time": 6.2}, {"name": "firebird_007", "time": 6.2333}, {"name": "firebird_008", "time": 6.2667}, {"name": "firebird_009", "time": 6.3}, {"name": "firebird_010", "time": 6.3333}, {"name": "firebird_011", "time": 6.3667}, {"name": "firebird_000", "time": 6.4}, {"name": "firebird_001", "time": 6.4333}, {"name": "firebird_002", "time": 6.4667}, {"name": "firebird_003", "time": 6.5}, {"name": "firebird_004", "time": 6.5333}, {"name": "firebird_005", "time": 6.5667}, {"name": "firebird_006", "time": 6.6}, {"name": "firebird_007", "time": 6.6333}, {"name": "firebird_008", "time": 6.6667}, {"name": "firebird_009", "time": 6.7}, {"name": "firebird_010", "time": 6.7333}, {"name": "firebird_011", "time": 6.7667}, {"name": "firebird_000", "time": 6.8}, {"name": "firebird_001", "time": 6.8333}, {"name": "firebird_002", "time": 6.8667}, {"name": "firebird_003", "time": 6.9}, {"name": "firebird_004", "time": 6.9333}, {"name": "firebird_005", "time": 6.9667}, {"name": "firebird_006", "time": 7}, {"name": "firebird_007", "time": 7.0333}, {"name": "firebird_008", "time": 7.0667}, {"name": "firebird_009", "time": 7.1}, {"name": "firebird_010", "time": 7.1333}, {"name": "firebird_011", "time": 7.1667}, {"name": "firebird_000", "time": 7.2}, {"name": "firebird_001", "time": 7.2333}, {"name": "firebird_002", "time": 7.2667}, {"name": "firebird_003", "time": 7.3}, {"name": "firebird_004", "time": 7.3333}, {"name": "firebird_005", "time": 7.3667}, {"name": "firebird_006", "time": 7.4}, {"name": "firebird_007", "time": 7.4333}, {"name": "firebird_008", "time": 7.4667}, {"name": "firebird_009", "time": 7.5}, {"name": "firebird_010", "time": 7.5333}, {"name": "firebird_011", "time": 7.5667}, {"name": "firebird_000", "time": 7.6}, {"name": "firebird_001", "time": 7.6333}, {"name": "firebird_002", "time": 7.6667}, {"name": "firebird_003", "time": 7.7}, {"name": "firebird_004", "time": 7.7333}, {"name": "firebird_005", "time": 7.7667}, {"name": "firebird_006", "time": 7.8}, {"name": "firebird_007", "time": 7.8333}, {"name": "firebird_008", "time": 7.8667}, {"name": "firebird_009", "time": 7.9}, {"name": "firebird_010", "time": 7.9333}, {"name": "firebird_011", "time": 7.9667}, {"name": "firebird_000", "time": 8}]}, "gold": {"color": [{"color": "cfceceff"}, {"color": "ffffffff", "time": 2}, {"color": "cfceceff", "time": 4}, {"color": "ffffffff", "time": 6}, {"color": "cfceceff", "time": 8}]}, "light15": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 5.2}, {"color": "ffffffba", "time": 5.6333}, {"color": "ffffffff", "time": 6.9667}, {"color": "ffffff00", "time": 7.6333}]}, "light16": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 4.8333}, {"color": "ffffffba", "time": 5.2667}, {"color": "ffffffff", "time": 6.6}, {"color": "ffffff00", "time": 7.2667}]}, "light13": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 1.9667}, {"color": "ffffffba", "time": 2.4}, {"color": "ffffffff", "time": 3.7333}, {"color": "ffffff00", "time": 4.4}]}, "light14": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 4.7333}, {"color": "ffffffba", "time": 5.2}, {"color": "ffffffff", "time": 6.5333}, {"color": "ffffff00", "time": 7.2}]}, "light19": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 1.4333}, {"color": "ffffffba", "time": 2}, {"color": "ffffffff", "time": 3.7}, {"color": "ffffff00", "time": 4.5333}]}, "light17": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 3.7333}, {"color": "ffffffba", "time": 4.1667}, {"color": "ffffffff", "time": 5.5}, {"color": "ffffff00", "time": 6.1667}]}, "light1": {"color": [{"color": "ffffff1d"}, {"color": "ffffffb2", "time": 2}, {"color": "ffffff1d", "time": 4}, {"color": "ffffffb2", "time": 6}, {"color": "ffffff1d", "time": 8}]}, "light11": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 2.4}, {"color": "ffffffba", "time": 3.1}, {"color": "ffffffff", "time": 5.2667}, {"color": "ffffff00", "time": 6.3333}]}, "light5": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 2.2333}, {"color": "ffffffba", "time": 2.8}, {"color": "ffffffff", "time": 4.4667}, {"color": "ffffff00", "time": 5.3333}]}, "light4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 0.9}, {"color": "ffffffba", "time": 1.3333}, {"color": "ffffffff", "time": 2.6667}, {"color": "ffffff00", "time": 3.3333}]}, "fireball": {"attachment": [{"name": "firebird_000"}, {"name": "firebird_001", "time": 0.0333}, {"name": "firebird_002", "time": 0.0667}, {"name": "firebird_003", "time": 0.1}, {"name": "firebird_004", "time": 0.1333}, {"name": "firebird_005", "time": 0.1667}, {"name": "firebird_006", "time": 0.2}, {"name": "firebird_007", "time": 0.2333}, {"name": "firebird_008", "time": 0.2667}, {"name": "firebird_009", "time": 0.3}, {"name": "firebird_010", "time": 0.3333}, {"name": "firebird_011", "time": 0.3667}, {"name": "firebird_000", "time": 0.4}, {"name": "firebird_001", "time": 0.4333}, {"name": "firebird_002", "time": 0.4667}, {"name": "firebird_003", "time": 0.5}, {"name": "firebird_004", "time": 0.5333}, {"name": "firebird_005", "time": 0.5667}, {"name": "firebird_006", "time": 0.6}, {"name": "firebird_007", "time": 0.6333}, {"name": "firebird_008", "time": 0.6667}, {"name": "firebird_009", "time": 0.7}, {"name": "firebird_010", "time": 0.7333}, {"name": "firebird_011", "time": 0.7667}, {"name": "firebird_000", "time": 0.8}, {"name": "firebird_001", "time": 0.8333}, {"name": "firebird_002", "time": 0.8667}, {"name": "firebird_003", "time": 0.9}, {"name": "firebird_004", "time": 0.9333}, {"name": "firebird_005", "time": 0.9667}, {"name": "firebird_006", "time": 1}, {"name": "firebird_007", "time": 1.0333}, {"name": "firebird_008", "time": 1.0667}, {"name": "firebird_009", "time": 1.1}, {"name": "firebird_010", "time": 1.1333}, {"name": "firebird_011", "time": 1.1667}, {"name": "firebird_000", "time": 1.2}, {"name": "firebird_001", "time": 1.2333}, {"name": "firebird_002", "time": 1.2667}, {"name": "firebird_003", "time": 1.3}, {"name": "firebird_004", "time": 1.3333}, {"name": "firebird_005", "time": 1.3667}, {"name": "firebird_006", "time": 1.4}, {"name": "firebird_007", "time": 1.4333}, {"name": "firebird_008", "time": 1.4667}, {"name": "firebird_009", "time": 1.5}, {"name": "firebird_010", "time": 1.5333}, {"name": "firebird_011", "time": 1.5667}, {"name": "firebird_000", "time": 1.6}, {"name": "firebird_001", "time": 1.6333}, {"name": "firebird_002", "time": 1.6667}, {"name": "firebird_003", "time": 1.7}, {"name": "firebird_004", "time": 1.7333}, {"name": "firebird_005", "time": 1.7667}, {"name": "firebird_006", "time": 1.8}, {"name": "firebird_007", "time": 1.8333}, {"name": "firebird_008", "time": 1.8667}, {"name": "firebird_009", "time": 1.9}, {"name": "firebird_010", "time": 1.9333}, {"name": "firebird_011", "time": 1.9667}, {"name": "firebird_000", "time": 2}, {"name": "firebird_001", "time": 2.0333}, {"name": "firebird_002", "time": 2.0667}, {"name": "firebird_003", "time": 2.1}, {"name": "firebird_004", "time": 2.1333}, {"name": "firebird_005", "time": 2.1667}, {"name": "firebird_006", "time": 2.2}, {"name": "firebird_007", "time": 2.2333}, {"name": "firebird_008", "time": 2.2667}, {"name": "firebird_009", "time": 2.3}, {"name": "firebird_010", "time": 2.3333}, {"name": "firebird_011", "time": 2.3667}, {"name": "firebird_000", "time": 2.4}, {"name": "firebird_001", "time": 2.4333}, {"name": "firebird_002", "time": 2.4667}, {"name": "firebird_003", "time": 2.5}, {"name": "firebird_004", "time": 2.5333}, {"name": "firebird_005", "time": 2.5667}, {"name": "firebird_006", "time": 2.6}, {"name": "firebird_007", "time": 2.6333}, {"name": "firebird_008", "time": 2.6667}, {"name": "firebird_009", "time": 2.7}, {"name": "firebird_010", "time": 2.7333}, {"name": "firebird_011", "time": 2.7667}, {"name": "firebird_000", "time": 2.8}, {"name": "firebird_001", "time": 2.8333}, {"name": "firebird_002", "time": 2.8667}, {"name": "firebird_003", "time": 2.9}, {"name": "firebird_004", "time": 2.9333}, {"name": "firebird_005", "time": 2.9667}, {"name": "firebird_006", "time": 3}, {"name": "firebird_007", "time": 3.0333}, {"name": "firebird_008", "time": 3.0667}, {"name": "firebird_009", "time": 3.1}, {"name": "firebird_010", "time": 3.1333}, {"name": "firebird_011", "time": 3.1667}, {"name": "firebird_000", "time": 3.2}, {"name": "firebird_001", "time": 3.2333}, {"name": "firebird_002", "time": 3.2667}, {"name": "firebird_003", "time": 3.3}, {"name": "firebird_004", "time": 3.3333}, {"name": "firebird_005", "time": 3.3667}, {"name": "firebird_006", "time": 3.4}, {"name": "firebird_007", "time": 3.4333}, {"name": "firebird_008", "time": 3.4667}, {"name": "firebird_009", "time": 3.5}, {"name": "firebird_010", "time": 3.5333}, {"name": "firebird_011", "time": 3.5667}, {"name": "firebird_000", "time": 3.6}, {"name": "firebird_001", "time": 3.6333}, {"name": "firebird_002", "time": 3.6667}, {"name": "firebird_003", "time": 3.7}, {"name": "firebird_004", "time": 3.7333}, {"name": "firebird_005", "time": 3.7667}, {"name": "firebird_006", "time": 3.8}, {"name": "firebird_007", "time": 3.8333}, {"name": "firebird_008", "time": 3.8667}, {"name": "firebird_009", "time": 3.9}, {"name": "firebird_010", "time": 3.9333}, {"name": "firebird_011", "time": 3.9667}, {"name": "firebird_000", "time": 4}, {"name": "firebird_001", "time": 4.0333}, {"name": "firebird_002", "time": 4.0667}, {"name": "firebird_003", "time": 4.1}, {"name": "firebird_004", "time": 4.1333}, {"name": "firebird_005", "time": 4.1667}, {"name": "firebird_006", "time": 4.2}, {"name": "firebird_007", "time": 4.2333}, {"name": "firebird_008", "time": 4.2667}, {"name": "firebird_009", "time": 4.3}, {"name": "firebird_010", "time": 4.3333}, {"name": "firebird_011", "time": 4.3667}, {"name": "firebird_000", "time": 4.4}, {"name": "firebird_001", "time": 4.4333}, {"name": "firebird_002", "time": 4.4667}, {"name": "firebird_003", "time": 4.5}, {"name": "firebird_004", "time": 4.5333}, {"name": "firebird_005", "time": 4.5667}, {"name": "firebird_006", "time": 4.6}, {"name": "firebird_007", "time": 4.6333}, {"name": "firebird_008", "time": 4.6667}, {"name": "firebird_009", "time": 4.7}, {"name": "firebird_010", "time": 4.7333}, {"name": "firebird_011", "time": 4.7667}, {"name": "firebird_000", "time": 4.8}, {"name": "firebird_001", "time": 4.8333}, {"name": "firebird_002", "time": 4.8667}, {"name": "firebird_003", "time": 4.9}, {"name": "firebird_004", "time": 4.9333}, {"name": "firebird_005", "time": 4.9667}, {"name": "firebird_006", "time": 5}, {"name": "firebird_007", "time": 5.0333}, {"name": "firebird_008", "time": 5.0667}, {"name": "firebird_009", "time": 5.1}, {"name": "firebird_010", "time": 5.1333}, {"name": "firebird_011", "time": 5.1667}, {"name": "firebird_000", "time": 5.2}, {"name": "firebird_001", "time": 5.2333}, {"name": "firebird_002", "time": 5.2667}, {"name": "firebird_003", "time": 5.3}, {"name": "firebird_004", "time": 5.3333}, {"name": "firebird_005", "time": 5.3667}, {"name": "firebird_006", "time": 5.4}, {"name": "firebird_007", "time": 5.4333}, {"name": "firebird_008", "time": 5.4667}, {"name": "firebird_009", "time": 5.5}, {"name": "firebird_010", "time": 5.5333}, {"name": "firebird_011", "time": 5.5667}, {"name": "firebird_000", "time": 5.6}, {"name": "firebird_001", "time": 5.6333}, {"name": "firebird_002", "time": 5.6667}, {"name": "firebird_003", "time": 5.7}, {"name": "firebird_004", "time": 5.7333}, {"name": "firebird_005", "time": 5.7667}, {"name": "firebird_006", "time": 5.8}, {"name": "firebird_007", "time": 5.8333}, {"name": "firebird_008", "time": 5.8667}, {"name": "firebird_009", "time": 5.9}, {"name": "firebird_010", "time": 5.9333}, {"name": "firebird_011", "time": 5.9667}, {"name": "firebird_000", "time": 6}, {"name": "firebird_001", "time": 6.0333}, {"name": "firebird_002", "time": 6.0667}, {"name": "firebird_003", "time": 6.1}, {"name": "firebird_004", "time": 6.1333}, {"name": "firebird_005", "time": 6.1667}, {"name": "firebird_006", "time": 6.2}, {"name": "firebird_007", "time": 6.2333}, {"name": "firebird_008", "time": 6.2667}, {"name": "firebird_009", "time": 6.3}, {"name": "firebird_010", "time": 6.3333}, {"name": "firebird_011", "time": 6.3667}, {"name": "firebird_000", "time": 6.4}, {"name": "firebird_001", "time": 6.4333}, {"name": "firebird_002", "time": 6.4667}, {"name": "firebird_003", "time": 6.5}, {"name": "firebird_004", "time": 6.5333}, {"name": "firebird_005", "time": 6.5667}, {"name": "firebird_006", "time": 6.6}, {"name": "firebird_007", "time": 6.6333}, {"name": "firebird_008", "time": 6.6667}, {"name": "firebird_009", "time": 6.7}, {"name": "firebird_010", "time": 6.7333}, {"name": "firebird_011", "time": 6.7667}, {"name": "firebird_000", "time": 6.8}, {"name": "firebird_001", "time": 6.8333}, {"name": "firebird_002", "time": 6.8667}, {"name": "firebird_003", "time": 6.9}, {"name": "firebird_004", "time": 6.9333}, {"name": "firebird_005", "time": 6.9667}, {"name": "firebird_006", "time": 7}, {"name": "firebird_007", "time": 7.0333}, {"name": "firebird_008", "time": 7.0667}, {"name": "firebird_009", "time": 7.1}, {"name": "firebird_010", "time": 7.1333}, {"name": "firebird_011", "time": 7.1667}, {"name": "firebird_000", "time": 7.2}, {"name": "firebird_001", "time": 7.2333}, {"name": "firebird_002", "time": 7.2667}, {"name": "firebird_003", "time": 7.3}, {"name": "firebird_004", "time": 7.3333}, {"name": "firebird_005", "time": 7.3667}, {"name": "firebird_006", "time": 7.4}, {"name": "firebird_007", "time": 7.4333}, {"name": "firebird_008", "time": 7.4667}, {"name": "firebird_009", "time": 7.5}, {"name": "firebird_010", "time": 7.5333}, {"name": "firebird_011", "time": 7.5667}, {"name": "firebird_000", "time": 7.6}, {"name": "firebird_001", "time": 7.6333}, {"name": "firebird_002", "time": 7.6667}, {"name": "firebird_003", "time": 7.7}, {"name": "firebird_004", "time": 7.7333}, {"name": "firebird_005", "time": 7.7667}, {"name": "firebird_006", "time": 7.8}, {"name": "firebird_007", "time": 7.8333}, {"name": "firebird_008", "time": 7.8667}, {"name": "firebird_009", "time": 7.9}, {"name": "firebird_010", "time": 7.9333}, {"name": "firebird_011", "time": 7.9667}, {"name": "firebird_000", "time": 8}]}, "light10": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffba", "time": 1.2333}, {"color": "ffffffff", "time": 2.9333}, {"color": "ffffff00", "time": 3.7667}]}, "light9": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 1.1667}, {"color": "ffffffba", "time": 1.6}, {"color": "ffffffff", "time": 2.9333}, {"color": "ffffff00", "time": 3.6}]}, "light8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 0.9}, {"color": "ffffffba", "time": 1.3333}, {"color": "ffffffff", "time": 2.6667}, {"color": "ffffff00", "time": 3.3333}]}, "light7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 4}, {"color": "ffffffba", "time": 4.5667}, {"color": "ffffffff", "time": 6.2667}, {"color": "ffffff00", "time": 7.1}]}, "light6": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 2.2333}, {"color": "ffffffba", "time": 2.9333}, {"color": "ffffffff", "time": 5}, {"color": "ffffff00", "time": 6.0667}]}, "light26": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 0.1}, {"color": "ffffffba", "time": 0.5333}, {"color": "ffffffff", "time": 1.8667}, {"color": "ffffff00", "time": 2.5333}]}, "light27": {"color": [{"color": "ffffffde"}, {"color": "ffffffff", "time": 0.6333}, {"color": "ffffff00", "curve": "stepped", "time": 1.3}, {"color": "ffffff00", "time": 6.8667}, {"color": "ffffffba", "time": 7.3333}, {"color": "ffffffdc", "time": 8}]}, "light25": {"color": [{"color": "ffffffde"}, {"color": "ffffffff", "time": 0.6333}, {"color": "ffffff00", "curve": "stepped", "time": 1.3}, {"color": "ffffff00", "time": 6.8667}, {"color": "ffffffba", "time": 7.3333}, {"color": "ffffffdc", "time": 8}]}, "light28": {"color": [{"color": "ffffffde"}, {"color": "ffffffff", "time": 0.6333}, {"color": "ffffff00", "curve": "stepped", "time": 1.3}, {"color": "ffffff00", "time": 6.8667}, {"color": "ffffffba", "time": 7.3333}, {"color": "ffffffdc", "time": 8}]}, "xiaoguo8": {"attachment": [{"name": "Image 1"}, {"name": "Image 3", "time": 0.0333}, {"name": "Image 5", "time": 0.0667}, {"name": "Image 7", "time": 0.1}, {"name": "Image 9", "time": 0.1333}, {"name": "Image 11", "time": 0.1667}, {"name": "Image 13", "time": 0.2}, {"name": "Image 1", "time": 0.2333}, {"name": "Image 3", "time": 0.2667}, {"name": "Image 5", "time": 0.3}, {"name": "Image 7", "time": 0.3333}, {"name": "Image 9", "time": 0.3667}, {"name": "Image 11", "time": 0.4}, {"name": "Image 13", "time": 0.4333}, {"name": "Image 1", "time": 0.4667}, {"name": "Image 3", "time": 0.5}, {"name": "Image 5", "time": 0.5333}, {"name": "Image 7", "time": 0.5667}, {"name": "Image 9", "time": 0.6}, {"name": "Image 11", "time": 0.6333}, {"name": "Image 13", "time": 0.6667}, {"name": "Image 1", "time": 0.7}, {"name": "Image 3", "time": 0.7333}, {"name": "Image 5", "time": 0.7667}, {"name": "Image 7", "time": 0.8}, {"name": "Image 9", "time": 0.8333}, {"name": "Image 11", "time": 0.8667}, {"name": "Image 13", "time": 0.9}, {"name": "Image 1", "time": 0.9333}, {"name": "Image 3", "time": 0.9667}, {"name": "Image 5", "time": 1}, {"name": "Image 7", "time": 1.0333}, {"name": "Image 9", "time": 1.0667}, {"name": "Image 11", "time": 1.1}, {"name": "Image 13", "time": 1.1667}, {"name": "Image 1", "time": 1.2}, {"name": "Image 3", "time": 1.2333}, {"name": "Image 5", "time": 1.2667}, {"name": "Image 7", "time": 1.3}, {"name": "Image 9", "time": 1.3333}, {"name": "Image 11", "time": 1.3667}, {"name": "Image 13", "time": 1.4}, {"name": "Image 1", "time": 1.4333}, {"name": "Image 3", "time": 1.4667}, {"name": "Image 5", "time": 1.5}, {"name": "Image 7", "time": 1.5333}, {"name": "Image 9", "time": 1.5667}, {"name": "Image 11", "time": 1.6}, {"name": "Image 13", "time": 1.6333}, {"name": "Image 1", "time": 1.6667}, {"name": "Image 3", "time": 1.7}, {"name": "Image 5", "time": 1.7333}, {"name": "Image 7", "time": 1.7667}, {"name": "Image 9", "time": 1.8}, {"name": "Image 11", "time": 1.8333}, {"name": "Image 13", "time": 1.8667}, {"name": "Image 1", "time": 1.9}, {"name": "Image 3", "time": 1.9333}, {"name": "Image 5", "time": 1.9667}, {"name": "Image 7", "time": 2}, {"name": "Image 9", "time": 2.0333}, {"name": "Image 11", "time": 2.0667}, {"name": "Image 13", "time": 2.1}, {"name": "Image 1", "time": 2.1333}, {"name": "Image 3", "time": 2.1667}, {"name": "Image 5", "time": 2.2}, {"name": "Image 7", "time": 2.2333}, {"name": "Image 9", "time": 2.2667}, {"name": "Image 11", "time": 2.3}, {"name": "Image 13", "time": 2.3333}, {"name": "Image 1", "time": 2.3667}, {"name": "Image 3", "time": 2.4}, {"name": "Image 5", "time": 2.4333}, {"name": "Image 7", "time": 2.4667}, {"name": "Image 9", "time": 2.5}, {"name": "Image 11", "time": 2.5333}, {"name": "Image 13", "time": 2.5667}, {"name": "Image 1", "time": 2.6}, {"name": "Image 3", "time": 2.6333}, {"name": "Image 5", "time": 2.6667}, {"name": "Image 7", "time": 2.7}, {"name": "Image 9", "time": 2.7333}, {"name": "Image 11", "time": 2.7667}, {"name": "Image 13", "time": 2.8}, {"name": "Image 1", "time": 2.8333}, {"name": "Image 3", "time": 2.8667}, {"name": "Image 5", "time": 2.9}, {"name": "Image 7", "time": 2.9333}, {"name": "Image 9", "time": 2.9667}, {"name": "Image 11", "time": 3}, {"name": "Image 13", "time": 3.0333}, {"name": "Image 1", "time": 3.0667}, {"name": "Image 3", "time": 3.1}, {"name": "Image 5", "time": 3.1333}, {"name": "Image 7", "time": 3.1667}, {"name": "Image 9", "time": 3.2}, {"name": "Image 11", "time": 3.2333}, {"name": "Image 13", "time": 3.2667}, {"name": "Image 1", "time": 3.3}, {"name": "Image 3", "time": 3.3333}, {"name": "Image 5", "time": 3.4}, {"name": "Image 7", "time": 3.4333}, {"name": "Image 9", "time": 3.4667}, {"name": "Image 11", "time": 3.5}, {"name": "Image 13", "time": 3.5333}, {"name": "Image 1", "time": 3.5667}, {"name": "Image 3", "time": 3.6}, {"name": "Image 5", "time": 3.6333}, {"name": "Image 7", "time": 3.6667}, {"name": "Image 9", "time": 3.7}, {"name": "Image 11", "time": 3.7333}, {"name": "Image 13", "time": 3.7667}, {"name": "Image 1", "time": 3.8}, {"name": "Image 3", "time": 3.8333}, {"name": "Image 5", "time": 3.8667}, {"name": "Image 7", "time": 3.9}, {"name": "Image 9", "time": 3.9333}, {"name": "Image 11", "time": 3.9667}, {"name": "Image 1", "time": 4}, {"name": "Image 3", "time": 4.0333}, {"name": "Image 5", "time": 4.0667}, {"name": "Image 7", "time": 4.1}, {"name": "Image 9", "time": 4.1333}, {"name": "Image 11", "time": 4.1667}, {"name": "Image 13", "time": 4.2}, {"name": "Image 1", "time": 4.2333}, {"name": "Image 3", "time": 4.2667}, {"name": "Image 5", "time": 4.3}, {"name": "Image 7", "time": 4.3333}, {"name": "Image 9", "time": 4.3667}, {"name": "Image 11", "time": 4.4}, {"name": "Image 13", "time": 4.4333}, {"name": "Image 1", "time": 4.4667}, {"name": "Image 3", "time": 4.5}, {"name": "Image 5", "time": 4.5333}, {"name": "Image 7", "time": 4.5667}, {"name": "Image 9", "time": 4.6}, {"name": "Image 11", "time": 4.6333}, {"name": "Image 13", "time": 4.6667}, {"name": "Image 1", "time": 4.7}, {"name": "Image 3", "time": 4.7333}, {"name": "Image 5", "time": 4.7667}, {"name": "Image 7", "time": 4.8}, {"name": "Image 9", "time": 4.8333}, {"name": "Image 11", "time": 4.8667}, {"name": "Image 13", "time": 4.9}, {"name": "Image 1", "time": 4.9333}, {"name": "Image 3", "time": 4.9667}, {"name": "Image 5", "time": 5}, {"name": "Image 7", "time": 5.0333}, {"name": "Image 9", "time": 5.0667}, {"name": "Image 11", "time": 5.1}, {"name": "Image 13", "time": 5.1667}, {"name": "Image 1", "time": 5.2}, {"name": "Image 3", "time": 5.2333}, {"name": "Image 5", "time": 5.2667}, {"name": "Image 7", "time": 5.3}, {"name": "Image 9", "time": 5.3333}, {"name": "Image 11", "time": 5.3667}, {"name": "Image 13", "time": 5.4}, {"name": "Image 1", "time": 5.4333}, {"name": "Image 3", "time": 5.4667}, {"name": "Image 5", "time": 5.5}, {"name": "Image 7", "time": 5.5333}, {"name": "Image 9", "time": 5.5667}, {"name": "Image 11", "time": 5.6}, {"name": "Image 13", "time": 5.6333}, {"name": "Image 1", "time": 5.6667}, {"name": "Image 3", "time": 5.7}, {"name": "Image 5", "time": 5.7333}, {"name": "Image 7", "time": 5.7667}, {"name": "Image 9", "time": 5.8}, {"name": "Image 11", "time": 5.8333}, {"name": "Image 13", "time": 5.8667}, {"name": "Image 1", "time": 5.9}, {"name": "Image 3", "time": 5.9333}, {"name": "Image 5", "time": 5.9667}, {"name": "Image 7", "time": 6}, {"name": "Image 9", "time": 6.0333}, {"name": "Image 11", "time": 6.0667}, {"name": "Image 13", "time": 6.1}, {"name": "Image 1", "time": 6.1333}, {"name": "Image 3", "time": 6.1667}, {"name": "Image 5", "time": 6.2}, {"name": "Image 7", "time": 6.2333}, {"name": "Image 9", "time": 6.2667}, {"name": "Image 11", "time": 6.3}, {"name": "Image 13", "time": 6.3333}, {"name": "Image 1", "time": 6.3667}, {"name": "Image 3", "time": 6.4}, {"name": "Image 5", "time": 6.4333}, {"name": "Image 7", "time": 6.4667}, {"name": "Image 9", "time": 6.5}, {"name": "Image 11", "time": 6.5333}, {"name": "Image 13", "time": 6.5667}, {"name": "Image 1", "time": 6.6}, {"name": "Image 3", "time": 6.6333}, {"name": "Image 5", "time": 6.6667}, {"name": "Image 7", "time": 6.7}, {"name": "Image 9", "time": 6.7333}, {"name": "Image 11", "time": 6.7667}, {"name": "Image 13", "time": 6.8}, {"name": "Image 1", "time": 6.8333}, {"name": "Image 3", "time": 6.8667}, {"name": "Image 5", "time": 6.9}, {"name": "Image 7", "time": 6.9333}, {"name": "Image 9", "time": 6.9667}, {"name": "Image 11", "time": 7}, {"name": "Image 13", "time": 7.0333}, {"name": "Image 1", "time": 7.0667}, {"name": "Image 3", "time": 7.1}, {"name": "Image 5", "time": 7.1333}, {"name": "Image 7", "time": 7.1667}, {"name": "Image 9", "time": 7.2}, {"name": "Image 11", "time": 7.2333}, {"name": "Image 13", "time": 7.2667}, {"name": "Image 1", "time": 7.3}, {"name": "Image 3", "time": 7.3333}, {"name": "Image 5", "time": 7.4}, {"name": "Image 7", "time": 7.4333}, {"name": "Image 9", "time": 7.4667}, {"name": "Image 11", "time": 7.5}, {"name": "Image 13", "time": 7.5333}, {"name": "Image 1", "time": 7.5667}, {"name": "Image 3", "time": 7.6}, {"name": "Image 5", "time": 7.6333}, {"name": "Image 7", "time": 7.6667}, {"name": "Image 9", "time": 7.7}, {"name": "Image 11", "time": 7.7333}, {"name": "Image 13", "time": 7.7667}, {"name": "Image 1", "time": 7.8}, {"name": "Image 3", "time": 7.8333}, {"name": "Image 5", "time": 7.8667}, {"name": "Image 7", "time": 7.9}, {"name": "Image 9", "time": 7.9333}, {"name": "Image 11", "time": 7.9667}, {"name": "Image 13", "time": 8}]}, "xiaoguo9": {"attachment": [{"name": "Image 1"}, {"name": "Image 3", "time": 0.0333}, {"name": "Image 5", "time": 0.0667}, {"name": "Image 7", "time": 0.1}, {"name": "Image 9", "time": 0.1333}, {"name": "Image 11", "time": 0.1667}, {"name": "Image 13", "time": 0.2}, {"name": "Image 1", "time": 0.2333}, {"name": "Image 3", "time": 0.2667}, {"name": "Image 5", "time": 0.3}, {"name": "Image 7", "time": 0.3333}, {"name": "Image 9", "time": 0.3667}, {"name": "Image 11", "time": 0.4}, {"name": "Image 13", "time": 0.4333}, {"name": "Image 1", "time": 0.4667}, {"name": "Image 3", "time": 0.5}, {"name": "Image 5", "time": 0.5333}, {"name": "Image 7", "time": 0.5667}, {"name": "Image 9", "time": 0.6}, {"name": "Image 11", "time": 0.6333}, {"name": "Image 13", "time": 0.6667}, {"name": "Image 1", "time": 0.7}, {"name": "Image 3", "time": 0.7333}, {"name": "Image 5", "time": 0.7667}, {"name": "Image 7", "time": 0.8}, {"name": "Image 9", "time": 0.8333}, {"name": "Image 11", "time": 0.8667}, {"name": "Image 13", "time": 0.9}, {"name": "Image 1", "time": 0.9333}, {"name": "Image 3", "time": 0.9667}, {"name": "Image 5", "time": 1}, {"name": "Image 7", "time": 1.0333}, {"name": "Image 9", "time": 1.0667}, {"name": "Image 11", "time": 1.1}, {"name": "Image 13", "time": 1.1667}, {"name": "Image 1", "time": 1.2}, {"name": "Image 3", "time": 1.2333}, {"name": "Image 5", "time": 1.2667}, {"name": "Image 7", "time": 1.3}, {"name": "Image 9", "time": 1.3333}, {"name": "Image 11", "time": 1.3667}, {"name": "Image 13", "time": 1.4}, {"name": "Image 1", "time": 1.4333}, {"name": "Image 3", "time": 1.4667}, {"name": "Image 5", "time": 1.5}, {"name": "Image 7", "time": 1.5333}, {"name": "Image 9", "time": 1.5667}, {"name": "Image 11", "time": 1.6}, {"name": "Image 13", "time": 1.6333}, {"name": "Image 1", "time": 1.6667}, {"name": "Image 3", "time": 1.7}, {"name": "Image 5", "time": 1.7333}, {"name": "Image 7", "time": 1.7667}, {"name": "Image 9", "time": 1.8}, {"name": "Image 11", "time": 1.8333}, {"name": "Image 13", "time": 1.8667}, {"name": "Image 1", "time": 1.9}, {"name": "Image 3", "time": 1.9333}, {"name": "Image 5", "time": 1.9667}, {"name": "Image 7", "time": 2}, {"name": "Image 9", "time": 2.0333}, {"name": "Image 11", "time": 2.0667}, {"name": "Image 13", "time": 2.1}, {"name": "Image 1", "time": 2.1333}, {"name": "Image 3", "time": 2.1667}, {"name": "Image 5", "time": 2.2}, {"name": "Image 7", "time": 2.2333}, {"name": "Image 9", "time": 2.2667}, {"name": "Image 11", "time": 2.3}, {"name": "Image 13", "time": 2.3333}, {"name": "Image 1", "time": 2.3667}, {"name": "Image 3", "time": 2.4}, {"name": "Image 5", "time": 2.4333}, {"name": "Image 7", "time": 2.4667}, {"name": "Image 9", "time": 2.5}, {"name": "Image 11", "time": 2.5333}, {"name": "Image 13", "time": 2.5667}, {"name": "Image 1", "time": 2.6}, {"name": "Image 3", "time": 2.6333}, {"name": "Image 5", "time": 2.6667}, {"name": "Image 7", "time": 2.7}, {"name": "Image 9", "time": 2.7333}, {"name": "Image 11", "time": 2.7667}, {"name": "Image 13", "time": 2.8}, {"name": "Image 1", "time": 2.8333}, {"name": "Image 3", "time": 2.8667}, {"name": "Image 5", "time": 2.9}, {"name": "Image 7", "time": 2.9333}, {"name": "Image 9", "time": 2.9667}, {"name": "Image 11", "time": 3}, {"name": "Image 13", "time": 3.0333}, {"name": "Image 1", "time": 3.0667}, {"name": "Image 3", "time": 3.1}, {"name": "Image 5", "time": 3.1333}, {"name": "Image 7", "time": 3.1667}, {"name": "Image 9", "time": 3.2}, {"name": "Image 11", "time": 3.2333}, {"name": "Image 13", "time": 3.2667}, {"name": "Image 1", "time": 3.3}, {"name": "Image 3", "time": 3.3333}, {"name": "Image 5", "time": 3.4}, {"name": "Image 7", "time": 3.4333}, {"name": "Image 9", "time": 3.4667}, {"name": "Image 11", "time": 3.5}, {"name": "Image 13", "time": 3.5333}, {"name": "Image 1", "time": 3.5667}, {"name": "Image 3", "time": 3.6}, {"name": "Image 5", "time": 3.6333}, {"name": "Image 7", "time": 3.6667}, {"name": "Image 9", "time": 3.7}, {"name": "Image 11", "time": 3.7333}, {"name": "Image 13", "time": 3.7667}, {"name": "Image 1", "time": 3.8}, {"name": "Image 3", "time": 3.8333}, {"name": "Image 5", "time": 3.8667}, {"name": "Image 7", "time": 3.9}, {"name": "Image 9", "time": 3.9333}, {"name": "Image 11", "time": 3.9667}, {"name": "Image 1", "time": 4}, {"name": "Image 3", "time": 4.0333}, {"name": "Image 5", "time": 4.0667}, {"name": "Image 7", "time": 4.1}, {"name": "Image 9", "time": 4.1333}, {"name": "Image 11", "time": 4.1667}, {"name": "Image 13", "time": 4.2}, {"name": "Image 1", "time": 4.2333}, {"name": "Image 3", "time": 4.2667}, {"name": "Image 5", "time": 4.3}, {"name": "Image 7", "time": 4.3333}, {"name": "Image 9", "time": 4.3667}, {"name": "Image 11", "time": 4.4}, {"name": "Image 13", "time": 4.4333}, {"name": "Image 1", "time": 4.4667}, {"name": "Image 3", "time": 4.5}, {"name": "Image 5", "time": 4.5333}, {"name": "Image 7", "time": 4.5667}, {"name": "Image 9", "time": 4.6}, {"name": "Image 11", "time": 4.6333}, {"name": "Image 13", "time": 4.6667}, {"name": "Image 1", "time": 4.7}, {"name": "Image 3", "time": 4.7333}, {"name": "Image 5", "time": 4.7667}, {"name": "Image 7", "time": 4.8}, {"name": "Image 9", "time": 4.8333}, {"name": "Image 11", "time": 4.8667}, {"name": "Image 13", "time": 4.9}, {"name": "Image 1", "time": 4.9333}, {"name": "Image 3", "time": 4.9667}, {"name": "Image 5", "time": 5}, {"name": "Image 7", "time": 5.0333}, {"name": "Image 9", "time": 5.0667}, {"name": "Image 11", "time": 5.1}, {"name": "Image 13", "time": 5.1667}, {"name": "Image 1", "time": 5.2}, {"name": "Image 3", "time": 5.2333}, {"name": "Image 5", "time": 5.2667}, {"name": "Image 7", "time": 5.3}, {"name": "Image 9", "time": 5.3333}, {"name": "Image 11", "time": 5.3667}, {"name": "Image 13", "time": 5.4}, {"name": "Image 1", "time": 5.4333}, {"name": "Image 3", "time": 5.4667}, {"name": "Image 5", "time": 5.5}, {"name": "Image 7", "time": 5.5333}, {"name": "Image 9", "time": 5.5667}, {"name": "Image 11", "time": 5.6}, {"name": "Image 13", "time": 5.6333}, {"name": "Image 1", "time": 5.6667}, {"name": "Image 3", "time": 5.7}, {"name": "Image 5", "time": 5.7333}, {"name": "Image 7", "time": 5.7667}, {"name": "Image 9", "time": 5.8}, {"name": "Image 11", "time": 5.8333}, {"name": "Image 13", "time": 5.8667}, {"name": "Image 1", "time": 5.9}, {"name": "Image 3", "time": 5.9333}, {"name": "Image 5", "time": 5.9667}, {"name": "Image 7", "time": 6}, {"name": "Image 9", "time": 6.0333}, {"name": "Image 11", "time": 6.0667}, {"name": "Image 13", "time": 6.1}, {"name": "Image 1", "time": 6.1333}, {"name": "Image 3", "time": 6.1667}, {"name": "Image 5", "time": 6.2}, {"name": "Image 7", "time": 6.2333}, {"name": "Image 9", "time": 6.2667}, {"name": "Image 11", "time": 6.3}, {"name": "Image 13", "time": 6.3333}, {"name": "Image 1", "time": 6.3667}, {"name": "Image 3", "time": 6.4}, {"name": "Image 5", "time": 6.4333}, {"name": "Image 7", "time": 6.4667}, {"name": "Image 9", "time": 6.5}, {"name": "Image 11", "time": 6.5333}, {"name": "Image 13", "time": 6.5667}, {"name": "Image 1", "time": 6.6}, {"name": "Image 3", "time": 6.6333}, {"name": "Image 5", "time": 6.6667}, {"name": "Image 7", "time": 6.7}, {"name": "Image 9", "time": 6.7333}, {"name": "Image 11", "time": 6.7667}, {"name": "Image 13", "time": 6.8}, {"name": "Image 1", "time": 6.8333}, {"name": "Image 3", "time": 6.8667}, {"name": "Image 5", "time": 6.9}, {"name": "Image 7", "time": 6.9333}, {"name": "Image 9", "time": 6.9667}, {"name": "Image 11", "time": 7}, {"name": "Image 13", "time": 7.0333}, {"name": "Image 1", "time": 7.0667}, {"name": "Image 3", "time": 7.1}, {"name": "Image 5", "time": 7.1333}, {"name": "Image 7", "time": 7.1667}, {"name": "Image 9", "time": 7.2}, {"name": "Image 11", "time": 7.2333}, {"name": "Image 13", "time": 7.2667}, {"name": "Image 1", "time": 7.3}, {"name": "Image 3", "time": 7.3333}, {"name": "Image 5", "time": 7.4}, {"name": "Image 7", "time": 7.4333}, {"name": "Image 9", "time": 7.4667}, {"name": "Image 11", "time": 7.5}, {"name": "Image 13", "time": 7.5333}, {"name": "Image 1", "time": 7.5667}, {"name": "Image 3", "time": 7.6}, {"name": "Image 5", "time": 7.6333}, {"name": "Image 7", "time": 7.6667}, {"name": "Image 9", "time": 7.7}, {"name": "Image 11", "time": 7.7333}, {"name": "Image 13", "time": 7.7667}, {"name": "Image 1", "time": 7.8}, {"name": "Image 3", "time": 7.8333}, {"name": "Image 5", "time": 7.8667}, {"name": "Image 7", "time": 7.9}, {"name": "Image 9", "time": 7.9333}, {"name": "Image 11", "time": 7.9667}, {"name": "Image 13", "time": 8}]}, "xiaoguo7": {"attachment": [{"name": "Image 1"}, {"name": "Image 3", "time": 0.0333}, {"name": "Image 5", "time": 0.0667}, {"name": "Image 7", "time": 0.1}, {"name": "Image 9", "time": 0.1333}, {"name": "Image 11", "time": 0.1667}, {"name": "Image 13", "time": 0.2}, {"name": "Image 1", "time": 0.2333}, {"name": "Image 3", "time": 0.2667}, {"name": "Image 5", "time": 0.3}, {"name": "Image 7", "time": 0.3333}, {"name": "Image 9", "time": 0.3667}, {"name": "Image 11", "time": 0.4}, {"name": "Image 13", "time": 0.4333}, {"name": "Image 1", "time": 0.4667}, {"name": "Image 3", "time": 0.5}, {"name": "Image 5", "time": 0.5333}, {"name": "Image 7", "time": 0.5667}, {"name": "Image 9", "time": 0.6}, {"name": "Image 11", "time": 0.6333}, {"name": "Image 13", "time": 0.6667}, {"name": "Image 1", "time": 0.7}, {"name": "Image 3", "time": 0.7333}, {"name": "Image 5", "time": 0.7667}, {"name": "Image 7", "time": 0.8}, {"name": "Image 9", "time": 0.8333}, {"name": "Image 11", "time": 0.8667}, {"name": "Image 13", "time": 0.9}, {"name": "Image 1", "time": 0.9333}, {"name": "Image 3", "time": 0.9667}, {"name": "Image 5", "time": 1}, {"name": "Image 7", "time": 1.0333}, {"name": "Image 9", "time": 1.0667}, {"name": "Image 11", "time": 1.1}, {"name": "Image 13", "time": 1.1667}, {"name": "Image 1", "time": 1.2}, {"name": "Image 3", "time": 1.2333}, {"name": "Image 5", "time": 1.2667}, {"name": "Image 7", "time": 1.3}, {"name": "Image 9", "time": 1.3333}, {"name": "Image 11", "time": 1.3667}, {"name": "Image 13", "time": 1.4}, {"name": "Image 1", "time": 1.4333}, {"name": "Image 3", "time": 1.4667}, {"name": "Image 5", "time": 1.5}, {"name": "Image 7", "time": 1.5333}, {"name": "Image 9", "time": 1.5667}, {"name": "Image 11", "time": 1.6}, {"name": "Image 13", "time": 1.6333}, {"name": "Image 1", "time": 1.6667}, {"name": "Image 3", "time": 1.7}, {"name": "Image 5", "time": 1.7333}, {"name": "Image 7", "time": 1.7667}, {"name": "Image 9", "time": 1.8}, {"name": "Image 11", "time": 1.8333}, {"name": "Image 13", "time": 1.8667}, {"name": "Image 1", "time": 1.9}, {"name": "Image 3", "time": 1.9333}, {"name": "Image 5", "time": 1.9667}, {"name": "Image 7", "time": 2}, {"name": "Image 9", "time": 2.0333}, {"name": "Image 11", "time": 2.0667}, {"name": "Image 13", "time": 2.1}, {"name": "Image 1", "time": 2.1333}, {"name": "Image 3", "time": 2.1667}, {"name": "Image 5", "time": 2.2}, {"name": "Image 7", "time": 2.2333}, {"name": "Image 9", "time": 2.2667}, {"name": "Image 11", "time": 2.3}, {"name": "Image 13", "time": 2.3333}, {"name": "Image 1", "time": 2.3667}, {"name": "Image 3", "time": 2.4}, {"name": "Image 5", "time": 2.4333}, {"name": "Image 7", "time": 2.4667}, {"name": "Image 9", "time": 2.5}, {"name": "Image 11", "time": 2.5333}, {"name": "Image 13", "time": 2.5667}, {"name": "Image 1", "time": 2.6}, {"name": "Image 3", "time": 2.6333}, {"name": "Image 5", "time": 2.6667}, {"name": "Image 7", "time": 2.7}, {"name": "Image 9", "time": 2.7333}, {"name": "Image 11", "time": 2.7667}, {"name": "Image 13", "time": 2.8}, {"name": "Image 1", "time": 2.8333}, {"name": "Image 3", "time": 2.8667}, {"name": "Image 5", "time": 2.9}, {"name": "Image 7", "time": 2.9333}, {"name": "Image 9", "time": 2.9667}, {"name": "Image 11", "time": 3}, {"name": "Image 13", "time": 3.0333}, {"name": "Image 1", "time": 3.0667}, {"name": "Image 3", "time": 3.1}, {"name": "Image 5", "time": 3.1333}, {"name": "Image 7", "time": 3.1667}, {"name": "Image 9", "time": 3.2}, {"name": "Image 11", "time": 3.2333}, {"name": "Image 13", "time": 3.2667}, {"name": "Image 1", "time": 3.3}, {"name": "Image 3", "time": 3.3333}, {"name": "Image 5", "time": 3.4}, {"name": "Image 7", "time": 3.4333}, {"name": "Image 9", "time": 3.4667}, {"name": "Image 11", "time": 3.5}, {"name": "Image 13", "time": 3.5333}, {"name": "Image 1", "time": 3.5667}, {"name": "Image 3", "time": 3.6}, {"name": "Image 5", "time": 3.6333}, {"name": "Image 7", "time": 3.6667}, {"name": "Image 9", "time": 3.7}, {"name": "Image 11", "time": 3.7333}, {"name": "Image 13", "time": 3.7667}, {"name": "Image 1", "time": 3.8}, {"name": "Image 3", "time": 3.8333}, {"name": "Image 5", "time": 3.8667}, {"name": "Image 7", "time": 3.9}, {"name": "Image 9", "time": 3.9333}, {"name": "Image 11", "time": 3.9667}, {"name": "Image 1", "time": 4}, {"name": "Image 3", "time": 4.0333}, {"name": "Image 5", "time": 4.0667}, {"name": "Image 7", "time": 4.1}, {"name": "Image 9", "time": 4.1333}, {"name": "Image 11", "time": 4.1667}, {"name": "Image 13", "time": 4.2}, {"name": "Image 1", "time": 4.2333}, {"name": "Image 3", "time": 4.2667}, {"name": "Image 5", "time": 4.3}, {"name": "Image 7", "time": 4.3333}, {"name": "Image 9", "time": 4.3667}, {"name": "Image 11", "time": 4.4}, {"name": "Image 13", "time": 4.4333}, {"name": "Image 1", "time": 4.4667}, {"name": "Image 3", "time": 4.5}, {"name": "Image 5", "time": 4.5333}, {"name": "Image 7", "time": 4.5667}, {"name": "Image 9", "time": 4.6}, {"name": "Image 11", "time": 4.6333}, {"name": "Image 13", "time": 4.6667}, {"name": "Image 1", "time": 4.7}, {"name": "Image 3", "time": 4.7333}, {"name": "Image 5", "time": 4.7667}, {"name": "Image 7", "time": 4.8}, {"name": "Image 9", "time": 4.8333}, {"name": "Image 11", "time": 4.8667}, {"name": "Image 13", "time": 4.9}, {"name": "Image 1", "time": 4.9333}, {"name": "Image 3", "time": 4.9667}, {"name": "Image 5", "time": 5}, {"name": "Image 7", "time": 5.0333}, {"name": "Image 9", "time": 5.0667}, {"name": "Image 11", "time": 5.1}, {"name": "Image 13", "time": 5.1667}, {"name": "Image 1", "time": 5.2}, {"name": "Image 3", "time": 5.2333}, {"name": "Image 5", "time": 5.2667}, {"name": "Image 7", "time": 5.3}, {"name": "Image 9", "time": 5.3333}, {"name": "Image 11", "time": 5.3667}, {"name": "Image 13", "time": 5.4}, {"name": "Image 1", "time": 5.4333}, {"name": "Image 3", "time": 5.4667}, {"name": "Image 5", "time": 5.5}, {"name": "Image 7", "time": 5.5333}, {"name": "Image 9", "time": 5.5667}, {"name": "Image 11", "time": 5.6}, {"name": "Image 13", "time": 5.6333}, {"name": "Image 1", "time": 5.6667}, {"name": "Image 3", "time": 5.7}, {"name": "Image 5", "time": 5.7333}, {"name": "Image 7", "time": 5.7667}, {"name": "Image 9", "time": 5.8}, {"name": "Image 11", "time": 5.8333}, {"name": "Image 13", "time": 5.8667}, {"name": "Image 1", "time": 5.9}, {"name": "Image 3", "time": 5.9333}, {"name": "Image 5", "time": 5.9667}, {"name": "Image 7", "time": 6}, {"name": "Image 9", "time": 6.0333}, {"name": "Image 11", "time": 6.0667}, {"name": "Image 13", "time": 6.1}, {"name": "Image 1", "time": 6.1333}, {"name": "Image 3", "time": 6.1667}, {"name": "Image 5", "time": 6.2}, {"name": "Image 7", "time": 6.2333}, {"name": "Image 9", "time": 6.2667}, {"name": "Image 11", "time": 6.3}, {"name": "Image 13", "time": 6.3333}, {"name": "Image 1", "time": 6.3667}, {"name": "Image 3", "time": 6.4}, {"name": "Image 5", "time": 6.4333}, {"name": "Image 7", "time": 6.4667}, {"name": "Image 9", "time": 6.5}, {"name": "Image 11", "time": 6.5333}, {"name": "Image 13", "time": 6.5667}, {"name": "Image 1", "time": 6.6}, {"name": "Image 3", "time": 6.6333}, {"name": "Image 5", "time": 6.6667}, {"name": "Image 7", "time": 6.7}, {"name": "Image 9", "time": 6.7333}, {"name": "Image 11", "time": 6.7667}, {"name": "Image 13", "time": 6.8}, {"name": "Image 1", "time": 6.8333}, {"name": "Image 3", "time": 6.8667}, {"name": "Image 5", "time": 6.9}, {"name": "Image 7", "time": 6.9333}, {"name": "Image 9", "time": 6.9667}, {"name": "Image 11", "time": 7}, {"name": "Image 13", "time": 7.0333}, {"name": "Image 1", "time": 7.0667}, {"name": "Image 3", "time": 7.1}, {"name": "Image 5", "time": 7.1333}, {"name": "Image 7", "time": 7.1667}, {"name": "Image 9", "time": 7.2}, {"name": "Image 11", "time": 7.2333}, {"name": "Image 13", "time": 7.2667}, {"name": "Image 1", "time": 7.3}, {"name": "Image 3", "time": 7.3333}, {"name": "Image 5", "time": 7.4}, {"name": "Image 7", "time": 7.4333}, {"name": "Image 9", "time": 7.4667}, {"name": "Image 11", "time": 7.5}, {"name": "Image 13", "time": 7.5333}, {"name": "Image 1", "time": 7.5667}, {"name": "Image 3", "time": 7.6}, {"name": "Image 5", "time": 7.6333}, {"name": "Image 7", "time": 7.6667}, {"name": "Image 9", "time": 7.7}, {"name": "Image 11", "time": 7.7333}, {"name": "Image 13", "time": 7.7667}, {"name": "Image 1", "time": 7.8}, {"name": "Image 3", "time": 7.8333}, {"name": "Image 5", "time": 7.8667}, {"name": "Image 7", "time": 7.9}, {"name": "Image 9", "time": 7.9333}, {"name": "Image 11", "time": 7.9667}, {"name": "Image 13", "time": 8}]}, "xiaoguo4": {"attachment": [{"name": "Image 1"}, {"name": "Image 3", "time": 0.0333}, {"name": "Image 5", "time": 0.0667}, {"name": "Image 7", "time": 0.1}, {"name": "Image 9", "time": 0.1333}, {"name": "Image 11", "time": 0.1667}, {"name": "Image 13", "time": 0.2}, {"name": "Image 1", "time": 0.2333}, {"name": "Image 3", "time": 0.2667}, {"name": "Image 5", "time": 0.3}, {"name": "Image 7", "time": 0.3333}, {"name": "Image 9", "time": 0.3667}, {"name": "Image 11", "time": 0.4}, {"name": "Image 13", "time": 0.4333}, {"name": "Image 1", "time": 0.4667}, {"name": "Image 3", "time": 0.5}, {"name": "Image 5", "time": 0.5333}, {"name": "Image 7", "time": 0.5667}, {"name": "Image 9", "time": 0.6}, {"name": "Image 11", "time": 0.6333}, {"name": "Image 13", "time": 0.6667}, {"name": "Image 1", "time": 0.7}, {"name": "Image 3", "time": 0.7333}, {"name": "Image 5", "time": 0.7667}, {"name": "Image 7", "time": 0.8}, {"name": "Image 9", "time": 0.8333}, {"name": "Image 11", "time": 0.8667}, {"name": "Image 13", "time": 0.9}, {"name": "Image 1", "time": 0.9333}, {"name": "Image 3", "time": 0.9667}, {"name": "Image 5", "time": 1}, {"name": "Image 7", "time": 1.0333}, {"name": "Image 9", "time": 1.0667}, {"name": "Image 11", "time": 1.1}, {"name": "Image 13", "time": 1.1667}, {"name": "Image 1", "time": 1.2}, {"name": "Image 3", "time": 1.2333}, {"name": "Image 5", "time": 1.2667}, {"name": "Image 7", "time": 1.3}, {"name": "Image 9", "time": 1.3333}, {"name": "Image 11", "time": 1.3667}, {"name": "Image 13", "time": 1.4}, {"name": "Image 1", "time": 1.4333}, {"name": "Image 3", "time": 1.4667}, {"name": "Image 5", "time": 1.5}, {"name": "Image 7", "time": 1.5333}, {"name": "Image 9", "time": 1.5667}, {"name": "Image 11", "time": 1.6}, {"name": "Image 13", "time": 1.6333}, {"name": "Image 1", "time": 1.6667}, {"name": "Image 3", "time": 1.7}, {"name": "Image 5", "time": 1.7333}, {"name": "Image 7", "time": 1.7667}, {"name": "Image 9", "time": 1.8}, {"name": "Image 11", "time": 1.8333}, {"name": "Image 13", "time": 1.8667}, {"name": "Image 1", "time": 1.9}, {"name": "Image 3", "time": 1.9333}, {"name": "Image 5", "time": 1.9667}, {"name": "Image 7", "time": 2}, {"name": "Image 9", "time": 2.0333}, {"name": "Image 11", "time": 2.0667}, {"name": "Image 13", "time": 2.1}, {"name": "Image 1", "time": 2.1333}, {"name": "Image 3", "time": 2.1667}, {"name": "Image 5", "time": 2.2}, {"name": "Image 7", "time": 2.2333}, {"name": "Image 9", "time": 2.2667}, {"name": "Image 11", "time": 2.3}, {"name": "Image 13", "time": 2.3333}, {"name": "Image 1", "time": 2.3667}, {"name": "Image 3", "time": 2.4}, {"name": "Image 5", "time": 2.4333}, {"name": "Image 7", "time": 2.4667}, {"name": "Image 9", "time": 2.5}, {"name": "Image 11", "time": 2.5333}, {"name": "Image 13", "time": 2.5667}, {"name": "Image 1", "time": 2.6}, {"name": "Image 3", "time": 2.6333}, {"name": "Image 5", "time": 2.6667}, {"name": "Image 7", "time": 2.7}, {"name": "Image 9", "time": 2.7333}, {"name": "Image 11", "time": 2.7667}, {"name": "Image 13", "time": 2.8}, {"name": "Image 1", "time": 2.8333}, {"name": "Image 3", "time": 2.8667}, {"name": "Image 5", "time": 2.9}, {"name": "Image 7", "time": 2.9333}, {"name": "Image 9", "time": 2.9667}, {"name": "Image 11", "time": 3}, {"name": "Image 13", "time": 3.0333}, {"name": "Image 1", "time": 3.0667}, {"name": "Image 3", "time": 3.1}, {"name": "Image 5", "time": 3.1333}, {"name": "Image 7", "time": 3.1667}, {"name": "Image 9", "time": 3.2}, {"name": "Image 11", "time": 3.2333}, {"name": "Image 13", "time": 3.2667}, {"name": "Image 1", "time": 3.3}, {"name": "Image 3", "time": 3.3333}, {"name": "Image 5", "time": 3.4}, {"name": "Image 7", "time": 3.4333}, {"name": "Image 9", "time": 3.4667}, {"name": "Image 11", "time": 3.5}, {"name": "Image 13", "time": 3.5333}, {"name": "Image 1", "time": 3.5667}, {"name": "Image 3", "time": 3.6}, {"name": "Image 5", "time": 3.6333}, {"name": "Image 7", "time": 3.6667}, {"name": "Image 9", "time": 3.7}, {"name": "Image 11", "time": 3.7333}, {"name": "Image 13", "time": 3.7667}, {"name": "Image 1", "time": 3.8}, {"name": "Image 3", "time": 3.8333}, {"name": "Image 5", "time": 3.8667}, {"name": "Image 7", "time": 3.9}, {"name": "Image 9", "time": 3.9333}, {"name": "Image 11", "time": 3.9667}, {"name": "Image 1", "time": 4}, {"name": "Image 3", "time": 4.0333}, {"name": "Image 5", "time": 4.0667}, {"name": "Image 7", "time": 4.1}, {"name": "Image 9", "time": 4.1333}, {"name": "Image 11", "time": 4.1667}, {"name": "Image 13", "time": 4.2}, {"name": "Image 1", "time": 4.2333}, {"name": "Image 3", "time": 4.2667}, {"name": "Image 5", "time": 4.3}, {"name": "Image 7", "time": 4.3333}, {"name": "Image 9", "time": 4.3667}, {"name": "Image 11", "time": 4.4}, {"name": "Image 13", "time": 4.4333}, {"name": "Image 1", "time": 4.4667}, {"name": "Image 3", "time": 4.5}, {"name": "Image 5", "time": 4.5333}, {"name": "Image 7", "time": 4.5667}, {"name": "Image 9", "time": 4.6}, {"name": "Image 11", "time": 4.6333}, {"name": "Image 13", "time": 4.6667}, {"name": "Image 1", "time": 4.7}, {"name": "Image 3", "time": 4.7333}, {"name": "Image 5", "time": 4.7667}, {"name": "Image 7", "time": 4.8}, {"name": "Image 9", "time": 4.8333}, {"name": "Image 11", "time": 4.8667}, {"name": "Image 13", "time": 4.9}, {"name": "Image 1", "time": 4.9333}, {"name": "Image 3", "time": 4.9667}, {"name": "Image 5", "time": 5}, {"name": "Image 7", "time": 5.0333}, {"name": "Image 9", "time": 5.0667}, {"name": "Image 11", "time": 5.1}, {"name": "Image 13", "time": 5.1667}, {"name": "Image 1", "time": 5.2}, {"name": "Image 3", "time": 5.2333}, {"name": "Image 5", "time": 5.2667}, {"name": "Image 7", "time": 5.3}, {"name": "Image 9", "time": 5.3333}, {"name": "Image 11", "time": 5.3667}, {"name": "Image 13", "time": 5.4}, {"name": "Image 1", "time": 5.4333}, {"name": "Image 3", "time": 5.4667}, {"name": "Image 5", "time": 5.5}, {"name": "Image 7", "time": 5.5333}, {"name": "Image 9", "time": 5.5667}, {"name": "Image 11", "time": 5.6}, {"name": "Image 13", "time": 5.6333}, {"name": "Image 1", "time": 5.6667}, {"name": "Image 3", "time": 5.7}, {"name": "Image 5", "time": 5.7333}, {"name": "Image 7", "time": 5.7667}, {"name": "Image 9", "time": 5.8}, {"name": "Image 11", "time": 5.8333}, {"name": "Image 13", "time": 5.8667}, {"name": "Image 1", "time": 5.9}, {"name": "Image 3", "time": 5.9333}, {"name": "Image 5", "time": 5.9667}, {"name": "Image 7", "time": 6}, {"name": "Image 9", "time": 6.0333}, {"name": "Image 11", "time": 6.0667}, {"name": "Image 13", "time": 6.1}, {"name": "Image 1", "time": 6.1333}, {"name": "Image 3", "time": 6.1667}, {"name": "Image 5", "time": 6.2}, {"name": "Image 7", "time": 6.2333}, {"name": "Image 9", "time": 6.2667}, {"name": "Image 11", "time": 6.3}, {"name": "Image 13", "time": 6.3333}, {"name": "Image 1", "time": 6.3667}, {"name": "Image 3", "time": 6.4}, {"name": "Image 5", "time": 6.4333}, {"name": "Image 7", "time": 6.4667}, {"name": "Image 9", "time": 6.5}, {"name": "Image 11", "time": 6.5333}, {"name": "Image 13", "time": 6.5667}, {"name": "Image 1", "time": 6.6}, {"name": "Image 3", "time": 6.6333}, {"name": "Image 5", "time": 6.6667}, {"name": "Image 7", "time": 6.7}, {"name": "Image 9", "time": 6.7333}, {"name": "Image 11", "time": 6.7667}, {"name": "Image 13", "time": 6.8}, {"name": "Image 1", "time": 6.8333}, {"name": "Image 3", "time": 6.8667}, {"name": "Image 5", "time": 6.9}, {"name": "Image 7", "time": 6.9333}, {"name": "Image 9", "time": 6.9667}, {"name": "Image 11", "time": 7}, {"name": "Image 13", "time": 7.0333}, {"name": "Image 1", "time": 7.0667}, {"name": "Image 3", "time": 7.1}, {"name": "Image 5", "time": 7.1333}, {"name": "Image 7", "time": 7.1667}, {"name": "Image 9", "time": 7.2}, {"name": "Image 11", "time": 7.2333}, {"name": "Image 13", "time": 7.2667}, {"name": "Image 1", "time": 7.3}, {"name": "Image 3", "time": 7.3333}, {"name": "Image 5", "time": 7.4}, {"name": "Image 7", "time": 7.4333}, {"name": "Image 9", "time": 7.4667}, {"name": "Image 11", "time": 7.5}, {"name": "Image 13", "time": 7.5333}, {"name": "Image 1", "time": 7.5667}, {"name": "Image 3", "time": 7.6}, {"name": "Image 5", "time": 7.6333}, {"name": "Image 7", "time": 7.6667}, {"name": "Image 9", "time": 7.7}, {"name": "Image 11", "time": 7.7333}, {"name": "Image 13", "time": 7.7667}, {"name": "Image 1", "time": 7.8}, {"name": "Image 3", "time": 7.8333}, {"name": "Image 5", "time": 7.8667}, {"name": "Image 7", "time": 7.9}, {"name": "Image 9", "time": 7.9333}, {"name": "Image 11", "time": 7.9667}, {"name": "Image 13", "time": 8}]}, "light22": {"color": [{"color": "ffffffde"}, {"color": "ffffffff", "time": 0.6333}, {"color": "ffffff00", "curve": "stepped", "time": 1.3}, {"color": "ffffff00", "time": 6.8667}, {"color": "ffffffba", "time": 7.3333}, {"color": "ffffffdc", "time": 8}]}, "light23": {"color": [{"color": "fffffff8"}, {"color": "ffffffff", "time": 0.1333}, {"color": "ffffff00", "curve": "stepped", "time": 0.8}, {"color": "ffffff00", "time": 6.4}, {"color": "ffffffba", "time": 6.8333}, {"color": "fffffff5", "time": 8}]}, "xiaoguo5": {"attachment": [{"name": "Image 1"}, {"name": "Image 3", "time": 0.0333}, {"name": "Image 5", "time": 0.0667}, {"name": "Image 7", "time": 0.1}, {"name": "Image 9", "time": 0.1333}, {"name": "Image 11", "time": 0.1667}, {"name": "Image 13", "time": 0.2}, {"name": "Image 1", "time": 0.2333}, {"name": "Image 3", "time": 0.2667}, {"name": "Image 5", "time": 0.3}, {"name": "Image 7", "time": 0.3333}, {"name": "Image 9", "time": 0.3667}, {"name": "Image 11", "time": 0.4}, {"name": "Image 13", "time": 0.4333}, {"name": "Image 1", "time": 0.4667}, {"name": "Image 3", "time": 0.5}, {"name": "Image 5", "time": 0.5333}, {"name": "Image 7", "time": 0.5667}, {"name": "Image 9", "time": 0.6}, {"name": "Image 11", "time": 0.6333}, {"name": "Image 13", "time": 0.6667}, {"name": "Image 1", "time": 0.7}, {"name": "Image 3", "time": 0.7333}, {"name": "Image 5", "time": 0.7667}, {"name": "Image 7", "time": 0.8}, {"name": "Image 9", "time": 0.8333}, {"name": "Image 11", "time": 0.8667}, {"name": "Image 13", "time": 0.9}, {"name": "Image 1", "time": 0.9333}, {"name": "Image 3", "time": 0.9667}, {"name": "Image 5", "time": 1}, {"name": "Image 7", "time": 1.0333}, {"name": "Image 9", "time": 1.0667}, {"name": "Image 11", "time": 1.1}, {"name": "Image 13", "time": 1.1667}, {"name": "Image 1", "time": 1.2}, {"name": "Image 3", "time": 1.2333}, {"name": "Image 5", "time": 1.2667}, {"name": "Image 7", "time": 1.3}, {"name": "Image 9", "time": 1.3333}, {"name": "Image 11", "time": 1.3667}, {"name": "Image 13", "time": 1.4}, {"name": "Image 1", "time": 1.4333}, {"name": "Image 3", "time": 1.4667}, {"name": "Image 5", "time": 1.5}, {"name": "Image 7", "time": 1.5333}, {"name": "Image 9", "time": 1.5667}, {"name": "Image 11", "time": 1.6}, {"name": "Image 13", "time": 1.6333}, {"name": "Image 1", "time": 1.6667}, {"name": "Image 3", "time": 1.7}, {"name": "Image 5", "time": 1.7333}, {"name": "Image 7", "time": 1.7667}, {"name": "Image 9", "time": 1.8}, {"name": "Image 11", "time": 1.8333}, {"name": "Image 13", "time": 1.8667}, {"name": "Image 1", "time": 1.9}, {"name": "Image 3", "time": 1.9333}, {"name": "Image 5", "time": 1.9667}, {"name": "Image 7", "time": 2}, {"name": "Image 9", "time": 2.0333}, {"name": "Image 11", "time": 2.0667}, {"name": "Image 13", "time": 2.1}, {"name": "Image 1", "time": 2.1333}, {"name": "Image 3", "time": 2.1667}, {"name": "Image 5", "time": 2.2}, {"name": "Image 7", "time": 2.2333}, {"name": "Image 9", "time": 2.2667}, {"name": "Image 11", "time": 2.3}, {"name": "Image 13", "time": 2.3333}, {"name": "Image 1", "time": 2.3667}, {"name": "Image 3", "time": 2.4}, {"name": "Image 5", "time": 2.4333}, {"name": "Image 7", "time": 2.4667}, {"name": "Image 9", "time": 2.5}, {"name": "Image 11", "time": 2.5333}, {"name": "Image 13", "time": 2.5667}, {"name": "Image 1", "time": 2.6}, {"name": "Image 3", "time": 2.6333}, {"name": "Image 5", "time": 2.6667}, {"name": "Image 7", "time": 2.7}, {"name": "Image 9", "time": 2.7333}, {"name": "Image 11", "time": 2.7667}, {"name": "Image 13", "time": 2.8}, {"name": "Image 1", "time": 2.8333}, {"name": "Image 3", "time": 2.8667}, {"name": "Image 5", "time": 2.9}, {"name": "Image 7", "time": 2.9333}, {"name": "Image 9", "time": 2.9667}, {"name": "Image 11", "time": 3}, {"name": "Image 13", "time": 3.0333}, {"name": "Image 1", "time": 3.0667}, {"name": "Image 3", "time": 3.1}, {"name": "Image 5", "time": 3.1333}, {"name": "Image 7", "time": 3.1667}, {"name": "Image 9", "time": 3.2}, {"name": "Image 11", "time": 3.2333}, {"name": "Image 13", "time": 3.2667}, {"name": "Image 1", "time": 3.3}, {"name": "Image 3", "time": 3.3333}, {"name": "Image 5", "time": 3.4}, {"name": "Image 7", "time": 3.4333}, {"name": "Image 9", "time": 3.4667}, {"name": "Image 11", "time": 3.5}, {"name": "Image 13", "time": 3.5333}, {"name": "Image 1", "time": 3.5667}, {"name": "Image 3", "time": 3.6}, {"name": "Image 5", "time": 3.6333}, {"name": "Image 7", "time": 3.6667}, {"name": "Image 9", "time": 3.7}, {"name": "Image 11", "time": 3.7333}, {"name": "Image 13", "time": 3.7667}, {"name": "Image 1", "time": 3.8}, {"name": "Image 3", "time": 3.8333}, {"name": "Image 5", "time": 3.8667}, {"name": "Image 7", "time": 3.9}, {"name": "Image 9", "time": 3.9333}, {"name": "Image 11", "time": 3.9667}, {"name": "Image 1", "time": 4}, {"name": "Image 3", "time": 4.0333}, {"name": "Image 5", "time": 4.0667}, {"name": "Image 7", "time": 4.1}, {"name": "Image 9", "time": 4.1333}, {"name": "Image 11", "time": 4.1667}, {"name": "Image 13", "time": 4.2}, {"name": "Image 1", "time": 4.2333}, {"name": "Image 3", "time": 4.2667}, {"name": "Image 5", "time": 4.3}, {"name": "Image 7", "time": 4.3333}, {"name": "Image 9", "time": 4.3667}, {"name": "Image 11", "time": 4.4}, {"name": "Image 13", "time": 4.4333}, {"name": "Image 1", "time": 4.4667}, {"name": "Image 3", "time": 4.5}, {"name": "Image 5", "time": 4.5333}, {"name": "Image 7", "time": 4.5667}, {"name": "Image 9", "time": 4.6}, {"name": "Image 11", "time": 4.6333}, {"name": "Image 13", "time": 4.6667}, {"name": "Image 1", "time": 4.7}, {"name": "Image 3", "time": 4.7333}, {"name": "Image 5", "time": 4.7667}, {"name": "Image 7", "time": 4.8}, {"name": "Image 9", "time": 4.8333}, {"name": "Image 11", "time": 4.8667}, {"name": "Image 13", "time": 4.9}, {"name": "Image 1", "time": 4.9333}, {"name": "Image 3", "time": 4.9667}, {"name": "Image 5", "time": 5}, {"name": "Image 7", "time": 5.0333}, {"name": "Image 9", "time": 5.0667}, {"name": "Image 11", "time": 5.1}, {"name": "Image 13", "time": 5.1667}, {"name": "Image 1", "time": 5.2}, {"name": "Image 3", "time": 5.2333}, {"name": "Image 5", "time": 5.2667}, {"name": "Image 7", "time": 5.3}, {"name": "Image 9", "time": 5.3333}, {"name": "Image 11", "time": 5.3667}, {"name": "Image 13", "time": 5.4}, {"name": "Image 1", "time": 5.4333}, {"name": "Image 3", "time": 5.4667}, {"name": "Image 5", "time": 5.5}, {"name": "Image 7", "time": 5.5333}, {"name": "Image 9", "time": 5.5667}, {"name": "Image 11", "time": 5.6}, {"name": "Image 13", "time": 5.6333}, {"name": "Image 1", "time": 5.6667}, {"name": "Image 3", "time": 5.7}, {"name": "Image 5", "time": 5.7333}, {"name": "Image 7", "time": 5.7667}, {"name": "Image 9", "time": 5.8}, {"name": "Image 11", "time": 5.8333}, {"name": "Image 13", "time": 5.8667}, {"name": "Image 1", "time": 5.9}, {"name": "Image 3", "time": 5.9333}, {"name": "Image 5", "time": 5.9667}, {"name": "Image 7", "time": 6}, {"name": "Image 9", "time": 6.0333}, {"name": "Image 11", "time": 6.0667}, {"name": "Image 13", "time": 6.1}, {"name": "Image 1", "time": 6.1333}, {"name": "Image 3", "time": 6.1667}, {"name": "Image 5", "time": 6.2}, {"name": "Image 7", "time": 6.2333}, {"name": "Image 9", "time": 6.2667}, {"name": "Image 11", "time": 6.3}, {"name": "Image 13", "time": 6.3333}, {"name": "Image 1", "time": 6.3667}, {"name": "Image 3", "time": 6.4}, {"name": "Image 5", "time": 6.4333}, {"name": "Image 7", "time": 6.4667}, {"name": "Image 9", "time": 6.5}, {"name": "Image 11", "time": 6.5333}, {"name": "Image 13", "time": 6.5667}, {"name": "Image 1", "time": 6.6}, {"name": "Image 3", "time": 6.6333}, {"name": "Image 5", "time": 6.6667}, {"name": "Image 7", "time": 6.7}, {"name": "Image 9", "time": 6.7333}, {"name": "Image 11", "time": 6.7667}, {"name": "Image 13", "time": 6.8}, {"name": "Image 1", "time": 6.8333}, {"name": "Image 3", "time": 6.8667}, {"name": "Image 5", "time": 6.9}, {"name": "Image 7", "time": 6.9333}, {"name": "Image 9", "time": 6.9667}, {"name": "Image 11", "time": 7}, {"name": "Image 13", "time": 7.0333}, {"name": "Image 1", "time": 7.0667}, {"name": "Image 3", "time": 7.1}, {"name": "Image 5", "time": 7.1333}, {"name": "Image 7", "time": 7.1667}, {"name": "Image 9", "time": 7.2}, {"name": "Image 11", "time": 7.2333}, {"name": "Image 13", "time": 7.2667}, {"name": "Image 1", "time": 7.3}, {"name": "Image 3", "time": 7.3333}, {"name": "Image 5", "time": 7.4}, {"name": "Image 7", "time": 7.4333}, {"name": "Image 9", "time": 7.4667}, {"name": "Image 11", "time": 7.5}, {"name": "Image 13", "time": 7.5333}, {"name": "Image 1", "time": 7.5667}, {"name": "Image 3", "time": 7.6}, {"name": "Image 5", "time": 7.6333}, {"name": "Image 7", "time": 7.6667}, {"name": "Image 9", "time": 7.7}, {"name": "Image 11", "time": 7.7333}, {"name": "Image 13", "time": 7.7667}, {"name": "Image 1", "time": 7.8}, {"name": "Image 3", "time": 7.8333}, {"name": "Image 5", "time": 7.8667}, {"name": "Image 7", "time": 7.9}, {"name": "Image 9", "time": 7.9333}, {"name": "Image 11", "time": 7.9667}, {"name": "Image 13", "time": 8}]}, "light20": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 4.7}, {"color": "ffffffba", "time": 5.2667}, {"color": "ffffffff", "time": 6.9667}, {"color": "ffffff00", "time": 7.8}]}, "xiaoguo2": {"attachment": [{"name": "Image 1"}, {"name": "Image 3", "time": 0.0333}, {"name": "Image 5", "time": 0.0667}, {"name": "Image 7", "time": 0.1}, {"name": "Image 9", "time": 0.1333}, {"name": "Image 11", "time": 0.1667}, {"name": "Image 13", "time": 0.2}, {"name": "Image 1", "time": 0.2333}, {"name": "Image 3", "time": 0.2667}, {"name": "Image 5", "time": 0.3}, {"name": "Image 7", "time": 0.3333}, {"name": "Image 9", "time": 0.3667}, {"name": "Image 11", "time": 0.4}, {"name": "Image 13", "time": 0.4333}, {"name": "Image 1", "time": 0.4667}, {"name": "Image 3", "time": 0.5}, {"name": "Image 5", "time": 0.5333}, {"name": "Image 7", "time": 0.5667}, {"name": "Image 9", "time": 0.6}, {"name": "Image 11", "time": 0.6333}, {"name": "Image 13", "time": 0.6667}, {"name": "Image 1", "time": 0.7}, {"name": "Image 3", "time": 0.7333}, {"name": "Image 5", "time": 0.7667}, {"name": "Image 7", "time": 0.8}, {"name": "Image 9", "time": 0.8333}, {"name": "Image 11", "time": 0.8667}, {"name": "Image 13", "time": 0.9}, {"name": "Image 1", "time": 0.9333}, {"name": "Image 3", "time": 0.9667}, {"name": "Image 5", "time": 1}, {"name": "Image 7", "time": 1.0333}, {"name": "Image 9", "time": 1.0667}, {"name": "Image 11", "time": 1.1}, {"name": "Image 13", "time": 1.1667}, {"name": "Image 1", "time": 1.2}, {"name": "Image 3", "time": 1.2333}, {"name": "Image 5", "time": 1.2667}, {"name": "Image 7", "time": 1.3}, {"name": "Image 9", "time": 1.3333}, {"name": "Image 11", "time": 1.3667}, {"name": "Image 13", "time": 1.4}, {"name": "Image 1", "time": 1.4333}, {"name": "Image 3", "time": 1.4667}, {"name": "Image 5", "time": 1.5}, {"name": "Image 7", "time": 1.5333}, {"name": "Image 9", "time": 1.5667}, {"name": "Image 11", "time": 1.6}, {"name": "Image 13", "time": 1.6333}, {"name": "Image 1", "time": 1.6667}, {"name": "Image 3", "time": 1.7}, {"name": "Image 5", "time": 1.7333}, {"name": "Image 7", "time": 1.7667}, {"name": "Image 9", "time": 1.8}, {"name": "Image 11", "time": 1.8333}, {"name": "Image 13", "time": 1.8667}, {"name": "Image 1", "time": 1.9}, {"name": "Image 3", "time": 1.9333}, {"name": "Image 5", "time": 1.9667}, {"name": "Image 7", "time": 2}, {"name": "Image 9", "time": 2.0333}, {"name": "Image 11", "time": 2.0667}, {"name": "Image 13", "time": 2.1}, {"name": "Image 1", "time": 2.1333}, {"name": "Image 3", "time": 2.1667}, {"name": "Image 5", "time": 2.2}, {"name": "Image 7", "time": 2.2333}, {"name": "Image 9", "time": 2.2667}, {"name": "Image 11", "time": 2.3}, {"name": "Image 13", "time": 2.3333}, {"name": "Image 1", "time": 2.3667}, {"name": "Image 3", "time": 2.4}, {"name": "Image 5", "time": 2.4333}, {"name": "Image 7", "time": 2.4667}, {"name": "Image 9", "time": 2.5}, {"name": "Image 11", "time": 2.5333}, {"name": "Image 13", "time": 2.5667}, {"name": "Image 1", "time": 2.6}, {"name": "Image 3", "time": 2.6333}, {"name": "Image 5", "time": 2.6667}, {"name": "Image 7", "time": 2.7}, {"name": "Image 9", "time": 2.7333}, {"name": "Image 11", "time": 2.7667}, {"name": "Image 13", "time": 2.8}, {"name": "Image 1", "time": 2.8333}, {"name": "Image 3", "time": 2.8667}, {"name": "Image 5", "time": 2.9}, {"name": "Image 7", "time": 2.9333}, {"name": "Image 9", "time": 2.9667}, {"name": "Image 11", "time": 3}, {"name": "Image 13", "time": 3.0333}, {"name": "Image 1", "time": 3.0667}, {"name": "Image 3", "time": 3.1}, {"name": "Image 5", "time": 3.1333}, {"name": "Image 7", "time": 3.1667}, {"name": "Image 9", "time": 3.2}, {"name": "Image 11", "time": 3.2333}, {"name": "Image 13", "time": 3.2667}, {"name": "Image 1", "time": 3.3}, {"name": "Image 3", "time": 3.3333}, {"name": "Image 5", "time": 3.4}, {"name": "Image 7", "time": 3.4333}, {"name": "Image 9", "time": 3.4667}, {"name": "Image 11", "time": 3.5}, {"name": "Image 13", "time": 3.5333}, {"name": "Image 1", "time": 3.5667}, {"name": "Image 3", "time": 3.6}, {"name": "Image 5", "time": 3.6333}, {"name": "Image 7", "time": 3.6667}, {"name": "Image 9", "time": 3.7}, {"name": "Image 11", "time": 3.7333}, {"name": "Image 13", "time": 3.7667}, {"name": "Image 1", "time": 3.8}, {"name": "Image 3", "time": 3.8333}, {"name": "Image 5", "time": 3.8667}, {"name": "Image 7", "time": 3.9}, {"name": "Image 9", "time": 3.9333}, {"name": "Image 11", "time": 3.9667}, {"name": "Image 1", "time": 4}, {"name": "Image 3", "time": 4.0333}, {"name": "Image 5", "time": 4.0667}, {"name": "Image 7", "time": 4.1}, {"name": "Image 9", "time": 4.1333}, {"name": "Image 11", "time": 4.1667}, {"name": "Image 13", "time": 4.2}, {"name": "Image 1", "time": 4.2333}, {"name": "Image 3", "time": 4.2667}, {"name": "Image 5", "time": 4.3}, {"name": "Image 7", "time": 4.3333}, {"name": "Image 9", "time": 4.3667}, {"name": "Image 11", "time": 4.4}, {"name": "Image 13", "time": 4.4333}, {"name": "Image 1", "time": 4.4667}, {"name": "Image 3", "time": 4.5}, {"name": "Image 5", "time": 4.5333}, {"name": "Image 7", "time": 4.5667}, {"name": "Image 9", "time": 4.6}, {"name": "Image 11", "time": 4.6333}, {"name": "Image 13", "time": 4.6667}, {"name": "Image 1", "time": 4.7}, {"name": "Image 3", "time": 4.7333}, {"name": "Image 5", "time": 4.7667}, {"name": "Image 7", "time": 4.8}, {"name": "Image 9", "time": 4.8333}, {"name": "Image 11", "time": 4.8667}, {"name": "Image 13", "time": 4.9}, {"name": "Image 1", "time": 4.9333}, {"name": "Image 3", "time": 4.9667}, {"name": "Image 5", "time": 5}, {"name": "Image 7", "time": 5.0333}, {"name": "Image 9", "time": 5.0667}, {"name": "Image 11", "time": 5.1}, {"name": "Image 13", "time": 5.1667}, {"name": "Image 1", "time": 5.2}, {"name": "Image 3", "time": 5.2333}, {"name": "Image 5", "time": 5.2667}, {"name": "Image 7", "time": 5.3}, {"name": "Image 9", "time": 5.3333}, {"name": "Image 11", "time": 5.3667}, {"name": "Image 13", "time": 5.4}, {"name": "Image 1", "time": 5.4333}, {"name": "Image 3", "time": 5.4667}, {"name": "Image 5", "time": 5.5}, {"name": "Image 7", "time": 5.5333}, {"name": "Image 9", "time": 5.5667}, {"name": "Image 11", "time": 5.6}, {"name": "Image 13", "time": 5.6333}, {"name": "Image 1", "time": 5.6667}, {"name": "Image 3", "time": 5.7}, {"name": "Image 5", "time": 5.7333}, {"name": "Image 7", "time": 5.7667}, {"name": "Image 9", "time": 5.8}, {"name": "Image 11", "time": 5.8333}, {"name": "Image 13", "time": 5.8667}, {"name": "Image 1", "time": 5.9}, {"name": "Image 3", "time": 5.9333}, {"name": "Image 5", "time": 5.9667}, {"name": "Image 7", "time": 6}, {"name": "Image 9", "time": 6.0333}, {"name": "Image 11", "time": 6.0667}, {"name": "Image 13", "time": 6.1}, {"name": "Image 1", "time": 6.1333}, {"name": "Image 3", "time": 6.1667}, {"name": "Image 5", "time": 6.2}, {"name": "Image 7", "time": 6.2333}, {"name": "Image 9", "time": 6.2667}, {"name": "Image 11", "time": 6.3}, {"name": "Image 13", "time": 6.3333}, {"name": "Image 1", "time": 6.3667}, {"name": "Image 3", "time": 6.4}, {"name": "Image 5", "time": 6.4333}, {"name": "Image 7", "time": 6.4667}, {"name": "Image 9", "time": 6.5}, {"name": "Image 11", "time": 6.5333}, {"name": "Image 13", "time": 6.5667}, {"name": "Image 1", "time": 6.6}, {"name": "Image 3", "time": 6.6333}, {"name": "Image 5", "time": 6.6667}, {"name": "Image 7", "time": 6.7}, {"name": "Image 9", "time": 6.7333}, {"name": "Image 11", "time": 6.7667}, {"name": "Image 13", "time": 6.8}, {"name": "Image 1", "time": 6.8333}, {"name": "Image 3", "time": 6.8667}, {"name": "Image 5", "time": 6.9}, {"name": "Image 7", "time": 6.9333}, {"name": "Image 9", "time": 6.9667}, {"name": "Image 11", "time": 7}, {"name": "Image 13", "time": 7.0333}, {"name": "Image 1", "time": 7.0667}, {"name": "Image 3", "time": 7.1}, {"name": "Image 5", "time": 7.1333}, {"name": "Image 7", "time": 7.1667}, {"name": "Image 9", "time": 7.2}, {"name": "Image 11", "time": 7.2333}, {"name": "Image 13", "time": 7.2667}, {"name": "Image 1", "time": 7.3}, {"name": "Image 3", "time": 7.3333}, {"name": "Image 5", "time": 7.4}, {"name": "Image 7", "time": 7.4333}, {"name": "Image 9", "time": 7.4667}, {"name": "Image 11", "time": 7.5}, {"name": "Image 13", "time": 7.5333}, {"name": "Image 1", "time": 7.5667}, {"name": "Image 3", "time": 7.6}, {"name": "Image 5", "time": 7.6333}, {"name": "Image 7", "time": 7.6667}, {"name": "Image 9", "time": 7.7}, {"name": "Image 11", "time": 7.7333}, {"name": "Image 13", "time": 7.7667}, {"name": "Image 1", "time": 7.8}, {"name": "Image 3", "time": 7.8333}, {"name": "Image 5", "time": 7.8667}, {"name": "Image 7", "time": 7.9}, {"name": "Image 9", "time": 7.9333}, {"name": "Image 11", "time": 7.9667}, {"name": "Image 13", "time": 8}]}, "xiaoguo3": {"attachment": [{"name": "Image 1"}, {"name": "Image 3", "time": 0.0333}, {"name": "Image 5", "time": 0.0667}, {"name": "Image 7", "time": 0.1}, {"name": "Image 9", "time": 0.1333}, {"name": "Image 11", "time": 0.1667}, {"name": "Image 13", "time": 0.2}, {"name": "Image 1", "time": 0.2333}, {"name": "Image 3", "time": 0.2667}, {"name": "Image 5", "time": 0.3}, {"name": "Image 7", "time": 0.3333}, {"name": "Image 9", "time": 0.3667}, {"name": "Image 11", "time": 0.4}, {"name": "Image 13", "time": 0.4333}, {"name": "Image 1", "time": 0.4667}, {"name": "Image 3", "time": 0.5}, {"name": "Image 5", "time": 0.5333}, {"name": "Image 7", "time": 0.5667}, {"name": "Image 9", "time": 0.6}, {"name": "Image 11", "time": 0.6333}, {"name": "Image 13", "time": 0.6667}, {"name": "Image 1", "time": 0.7}, {"name": "Image 3", "time": 0.7333}, {"name": "Image 5", "time": 0.7667}, {"name": "Image 7", "time": 0.8}, {"name": "Image 9", "time": 0.8333}, {"name": "Image 11", "time": 0.8667}, {"name": "Image 13", "time": 0.9}, {"name": "Image 1", "time": 0.9333}, {"name": "Image 3", "time": 0.9667}, {"name": "Image 5", "time": 1}, {"name": "Image 7", "time": 1.0333}, {"name": "Image 9", "time": 1.0667}, {"name": "Image 11", "time": 1.1}, {"name": "Image 13", "time": 1.1667}, {"name": "Image 1", "time": 1.2}, {"name": "Image 3", "time": 1.2333}, {"name": "Image 5", "time": 1.2667}, {"name": "Image 7", "time": 1.3}, {"name": "Image 9", "time": 1.3333}, {"name": "Image 11", "time": 1.3667}, {"name": "Image 13", "time": 1.4}, {"name": "Image 1", "time": 1.4333}, {"name": "Image 3", "time": 1.4667}, {"name": "Image 5", "time": 1.5}, {"name": "Image 7", "time": 1.5333}, {"name": "Image 9", "time": 1.5667}, {"name": "Image 11", "time": 1.6}, {"name": "Image 13", "time": 1.6333}, {"name": "Image 1", "time": 1.6667}, {"name": "Image 3", "time": 1.7}, {"name": "Image 5", "time": 1.7333}, {"name": "Image 7", "time": 1.7667}, {"name": "Image 9", "time": 1.8}, {"name": "Image 11", "time": 1.8333}, {"name": "Image 13", "time": 1.8667}, {"name": "Image 1", "time": 1.9}, {"name": "Image 3", "time": 1.9333}, {"name": "Image 5", "time": 1.9667}, {"name": "Image 7", "time": 2}, {"name": "Image 9", "time": 2.0333}, {"name": "Image 11", "time": 2.0667}, {"name": "Image 13", "time": 2.1}, {"name": "Image 1", "time": 2.1333}, {"name": "Image 3", "time": 2.1667}, {"name": "Image 5", "time": 2.2}, {"name": "Image 7", "time": 2.2333}, {"name": "Image 9", "time": 2.2667}, {"name": "Image 11", "time": 2.3}, {"name": "Image 13", "time": 2.3333}, {"name": "Image 1", "time": 2.3667}, {"name": "Image 3", "time": 2.4}, {"name": "Image 5", "time": 2.4333}, {"name": "Image 7", "time": 2.4667}, {"name": "Image 9", "time": 2.5}, {"name": "Image 11", "time": 2.5333}, {"name": "Image 13", "time": 2.5667}, {"name": "Image 1", "time": 2.6}, {"name": "Image 3", "time": 2.6333}, {"name": "Image 5", "time": 2.6667}, {"name": "Image 7", "time": 2.7}, {"name": "Image 9", "time": 2.7333}, {"name": "Image 11", "time": 2.7667}, {"name": "Image 13", "time": 2.8}, {"name": "Image 1", "time": 2.8333}, {"name": "Image 3", "time": 2.8667}, {"name": "Image 5", "time": 2.9}, {"name": "Image 7", "time": 2.9333}, {"name": "Image 9", "time": 2.9667}, {"name": "Image 11", "time": 3}, {"name": "Image 13", "time": 3.0333}, {"name": "Image 1", "time": 3.0667}, {"name": "Image 3", "time": 3.1}, {"name": "Image 5", "time": 3.1333}, {"name": "Image 7", "time": 3.1667}, {"name": "Image 9", "time": 3.2}, {"name": "Image 11", "time": 3.2333}, {"name": "Image 13", "time": 3.2667}, {"name": "Image 1", "time": 3.3}, {"name": "Image 3", "time": 3.3333}, {"name": "Image 5", "time": 3.4}, {"name": "Image 7", "time": 3.4333}, {"name": "Image 9", "time": 3.4667}, {"name": "Image 11", "time": 3.5}, {"name": "Image 13", "time": 3.5333}, {"name": "Image 1", "time": 3.5667}, {"name": "Image 3", "time": 3.6}, {"name": "Image 5", "time": 3.6333}, {"name": "Image 7", "time": 3.6667}, {"name": "Image 9", "time": 3.7}, {"name": "Image 11", "time": 3.7333}, {"name": "Image 13", "time": 3.7667}, {"name": "Image 1", "time": 3.8}, {"name": "Image 3", "time": 3.8333}, {"name": "Image 5", "time": 3.8667}, {"name": "Image 7", "time": 3.9}, {"name": "Image 9", "time": 3.9333}, {"name": "Image 11", "time": 3.9667}, {"name": "Image 1", "time": 4}, {"name": "Image 3", "time": 4.0333}, {"name": "Image 5", "time": 4.0667}, {"name": "Image 7", "time": 4.1}, {"name": "Image 9", "time": 4.1333}, {"name": "Image 11", "time": 4.1667}, {"name": "Image 13", "time": 4.2}, {"name": "Image 1", "time": 4.2333}, {"name": "Image 3", "time": 4.2667}, {"name": "Image 5", "time": 4.3}, {"name": "Image 7", "time": 4.3333}, {"name": "Image 9", "time": 4.3667}, {"name": "Image 11", "time": 4.4}, {"name": "Image 13", "time": 4.4333}, {"name": "Image 1", "time": 4.4667}, {"name": "Image 3", "time": 4.5}, {"name": "Image 5", "time": 4.5333}, {"name": "Image 7", "time": 4.5667}, {"name": "Image 9", "time": 4.6}, {"name": "Image 11", "time": 4.6333}, {"name": "Image 13", "time": 4.6667}, {"name": "Image 1", "time": 4.7}, {"name": "Image 3", "time": 4.7333}, {"name": "Image 5", "time": 4.7667}, {"name": "Image 7", "time": 4.8}, {"name": "Image 9", "time": 4.8333}, {"name": "Image 11", "time": 4.8667}, {"name": "Image 13", "time": 4.9}, {"name": "Image 1", "time": 4.9333}, {"name": "Image 3", "time": 4.9667}, {"name": "Image 5", "time": 5}, {"name": "Image 7", "time": 5.0333}, {"name": "Image 9", "time": 5.0667}, {"name": "Image 11", "time": 5.1}, {"name": "Image 13", "time": 5.1667}, {"name": "Image 1", "time": 5.2}, {"name": "Image 3", "time": 5.2333}, {"name": "Image 5", "time": 5.2667}, {"name": "Image 7", "time": 5.3}, {"name": "Image 9", "time": 5.3333}, {"name": "Image 11", "time": 5.3667}, {"name": "Image 13", "time": 5.4}, {"name": "Image 1", "time": 5.4333}, {"name": "Image 3", "time": 5.4667}, {"name": "Image 5", "time": 5.5}, {"name": "Image 7", "time": 5.5333}, {"name": "Image 9", "time": 5.5667}, {"name": "Image 11", "time": 5.6}, {"name": "Image 13", "time": 5.6333}, {"name": "Image 1", "time": 5.6667}, {"name": "Image 3", "time": 5.7}, {"name": "Image 5", "time": 5.7333}, {"name": "Image 7", "time": 5.7667}, {"name": "Image 9", "time": 5.8}, {"name": "Image 11", "time": 5.8333}, {"name": "Image 13", "time": 5.8667}, {"name": "Image 1", "time": 5.9}, {"name": "Image 3", "time": 5.9333}, {"name": "Image 5", "time": 5.9667}, {"name": "Image 7", "time": 6}, {"name": "Image 9", "time": 6.0333}, {"name": "Image 11", "time": 6.0667}, {"name": "Image 13", "time": 6.1}, {"name": "Image 1", "time": 6.1333}, {"name": "Image 3", "time": 6.1667}, {"name": "Image 5", "time": 6.2}, {"name": "Image 7", "time": 6.2333}, {"name": "Image 9", "time": 6.2667}, {"name": "Image 11", "time": 6.3}, {"name": "Image 13", "time": 6.3333}, {"name": "Image 1", "time": 6.3667}, {"name": "Image 3", "time": 6.4}, {"name": "Image 5", "time": 6.4333}, {"name": "Image 7", "time": 6.4667}, {"name": "Image 9", "time": 6.5}, {"name": "Image 11", "time": 6.5333}, {"name": "Image 13", "time": 6.5667}, {"name": "Image 1", "time": 6.6}, {"name": "Image 3", "time": 6.6333}, {"name": "Image 5", "time": 6.6667}, {"name": "Image 7", "time": 6.7}, {"name": "Image 9", "time": 6.7333}, {"name": "Image 11", "time": 6.7667}, {"name": "Image 13", "time": 6.8}, {"name": "Image 1", "time": 6.8333}, {"name": "Image 3", "time": 6.8667}, {"name": "Image 5", "time": 6.9}, {"name": "Image 7", "time": 6.9333}, {"name": "Image 9", "time": 6.9667}, {"name": "Image 11", "time": 7}, {"name": "Image 13", "time": 7.0333}, {"name": "Image 1", "time": 7.0667}, {"name": "Image 3", "time": 7.1}, {"name": "Image 5", "time": 7.1333}, {"name": "Image 7", "time": 7.1667}, {"name": "Image 9", "time": 7.2}, {"name": "Image 11", "time": 7.2333}, {"name": "Image 13", "time": 7.2667}, {"name": "Image 1", "time": 7.3}, {"name": "Image 3", "time": 7.3333}, {"name": "Image 5", "time": 7.4}, {"name": "Image 7", "time": 7.4333}, {"name": "Image 9", "time": 7.4667}, {"name": "Image 11", "time": 7.5}, {"name": "Image 13", "time": 7.5333}, {"name": "Image 1", "time": 7.5667}, {"name": "Image 3", "time": 7.6}, {"name": "Image 5", "time": 7.6333}, {"name": "Image 7", "time": 7.6667}, {"name": "Image 9", "time": 7.7}, {"name": "Image 11", "time": 7.7333}, {"name": "Image 13", "time": 7.7667}, {"name": "Image 1", "time": 7.8}, {"name": "Image 3", "time": 7.8333}, {"name": "Image 5", "time": 7.8667}, {"name": "Image 7", "time": 7.9}, {"name": "Image 9", "time": 7.9333}, {"name": "Image 11", "time": 7.9667}, {"name": "Image 13", "time": 8}]}, "light21": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"color": "ffffff00", "time": 0.1333}, {"color": "ffffffba", "time": 0.7}, {"color": "ffffffff", "time": 2.4}, {"color": "ffffff00", "time": 3.2333}]}}, "bones": {"all19": {"rotate": [{}, {"angle": -3.4, "time": 0.7667}, {"angle": 3.24, "time": 1.6333}, {"time": 2.6667}, {"angle": -3.4, "time": 3.4667}, {"angle": 3.24, "time": 4.3333}, {"time": 5.3333}, {"angle": -3.4, "time": 6.1333}, {"angle": 3.24, "time": 7}, {"time": 8}]}, "all18": {"rotate": [{}, {"angle": 7.53, "time": 0.5667}, {"angle": -7.6, "time": 1.4667}, {"time": 2.6667}, {"angle": 7.53, "time": 3.2667}, {"angle": -7.6, "time": 4.1333}, {"time": 5.3333}, {"angle": 7.53, "time": 5.9333}, {"angle": -7.6, "time": 6.8}, {"time": 8}]}, "all11": {"rotate": [{}, {"angle": 5.52, "time": 0.9}, {"angle": -7.7, "time": 1.9}, {"time": 2.6667}, {"angle": 5.52, "time": 3.6}, {"angle": -7.7, "time": 4.6}, {"time": 5.3333}, {"angle": 5.52, "time": 6.2667}, {"angle": -7.7, "time": 7.2667}, {"time": 8}]}, "all10": {"rotate": [{}, {"angle": 4.56, "time": 0.8667}, {"angle": -6.46, "time": 1.8667}, {"time": 2.6667}, {"angle": 4.56, "time": 3.5333}, {"angle": -6.46, "time": 4.5333}, {"time": 5.3333}, {"angle": 4.56, "time": 6.2}, {"angle": -6.46, "time": 7.2}, {"time": 8}]}, "all13": {"rotate": [{}, {"angle": 6.16, "time": 0.5333}, {"angle": -6.32, "time": 1.4333}, {"time": 2.6667}, {"angle": 6.16, "time": 3.2}, {"angle": -6.32, "time": 4.1}, {"time": 5.3333}, {"angle": 6.16, "time": 5.8667}, {"angle": -6.32, "time": 6.7667}, {"time": 8}]}, "all12": {"rotate": [{}, {"angle": 12.35, "time": 0.9667}, {"angle": -10.33, "time": 1.9667}, {"time": 2.6667}, {"angle": 12.35, "time": 3.6333}, {"angle": -10.33, "time": 4.6333}, {"time": 5.3333}, {"angle": 12.35, "time": 6.3}, {"angle": -10.33, "time": 7.3}, {"time": 8}]}, "all15": {"rotate": [{}, {"angle": -4.59, "time": 1.1667}, {"angle": 3.93, "time": 2.2}, {"time": 2.6667}, {"angle": -4.59, "time": 3.8667}, {"angle": 3.93, "time": 4.8667}, {"time": 5.3333}, {"angle": -4.59, "time": 6.5333}, {"angle": 3.93, "time": 7.5333}, {"time": 8}]}, "all14": {"rotate": [{}, {"angle": -4.88, "time": 1.1333}, {"angle": 5.96, "time": 2.1667}, {"time": 2.6667}, {"angle": -4.88, "time": 3.8}, {"angle": 5.96, "time": 4.8333}, {"time": 5.3333}, {"angle": -4.88, "time": 6.4667}, {"angle": 5.96, "time": 7.5}, {"time": 8}]}, "all17": {"rotate": [{}, {"angle": -14.17, "time": 1.2667}, {"angle": 16.29, "time": 2.3}, {"time": 2.6667}, {"angle": -14.17, "time": 3.9333}, {"angle": 16.29, "time": 4.9667}, {"time": 5.3333}, {"angle": -14.17, "time": 6.6}, {"angle": 16.29, "time": 7.6333}, {"time": 8}]}, "all16": {"rotate": [{}, {"angle": -7.78, "time": 1.2333}, {"angle": 6.96, "time": 2.2333}, {"time": 2.6667}, {"angle": -7.78, "time": 3.9}, {"angle": 6.96, "time": 4.9333}, {"time": 5.3333}, {"angle": -7.78, "time": 6.5667}, {"angle": 6.96, "time": 7.6}, {"time": 8}]}, "rxu4": {"rotate": [{}, {"angle": 5.25, "time": 0.6667}, {"angle": -6.06, "time": 1.8}, {"time": 2.6667}, {"angle": 5.25, "time": 3.3333}, {"angle": -6.06, "time": 4.4667}, {"time": 5.3333}, {"angle": 5.25, "time": 6}, {"angle": -6.06, "time": 7.1333}, {"time": 8}]}, "rxu3": {"rotate": [{}, {"angle": -4.14, "time": 0.5667}, {"angle": 5.28, "time": 1.8333}, {"time": 2.6667}, {"angle": -4.14, "time": 3.2667}, {"angle": 5.28, "time": 4.5}, {"time": 5.3333}, {"angle": -4.14, "time": 5.9333}, {"angle": 5.28, "time": 7.1667}, {"time": 8}]}, "rxu6": {"rotate": [{}, {"angle": -3.03, "time": 0.6667}, {"angle": 4.99, "time": 1.9333}, {"time": 2.6667}, {"angle": -3.03, "time": 3.3333}, {"angle": 4.99, "time": 4.6}, {"time": 5.3333}, {"angle": -3.03, "time": 6}, {"angle": 4.99, "time": 7.2667}, {"time": 8}]}, "rxu5": {"rotate": [{}, {"angle": -4.87, "time": 0.5667}, {"angle": 3.89, "time": 2}, {"time": 2.6667}, {"angle": -4.87, "time": 3.2333}, {"angle": 3.89, "time": 4.6667}, {"time": 5.3333}, {"angle": -4.87, "time": 5.9}, {"angle": 3.89, "time": 7.3333}, {"time": 8}]}, "all20": {"rotate": [{}, {"angle": -4.45, "time": 0.8333}, {"angle": 5.8, "time": 1.7}, {"time": 2.6667}, {"angle": -4.45, "time": 3.5}, {"angle": 5.8, "time": 4.3667}, {"time": 5.3333}, {"angle": -4.45, "time": 6.1667}, {"angle": 5.8, "time": 7.0333}, {"time": 8}]}, "rxu2": {"rotate": [{}, {"angle": 7.86, "time": 0.5667}, {"angle": -5.81, "time": 1.5}, {"time": 2.6667}, {"angle": 7.86, "time": 3.2667}, {"angle": -5.81, "time": 4.2}, {"time": 5.3333}, {"angle": 7.86, "time": 5.9333}, {"angle": -5.81, "time": 6.8667}, {"time": 8}]}, "rxu1": {"rotate": [{}, {"angle": -6.54, "time": 0.7667}, {"angle": 5.97, "time": 1.6333}, {"time": 2.6667}, {"angle": -6.54, "time": 3.4333}, {"angle": 5.97, "time": 4.3333}, {"time": 5.3333}, {"angle": -6.54, "time": 6.1}, {"angle": 5.97, "time": 7}, {"time": 8}]}, "rxu8": {"rotate": [{}, {"angle": 10.1, "time": 0.6333}, {"angle": -7.59, "time": 1.6667}, {"time": 2.6667}, {"angle": 10.1, "time": 3.3}, {"angle": -7.59, "time": 4.3333}, {"time": 5.3333}, {"angle": 10.1, "time": 5.9667}, {"angle": -7.59, "time": 7}, {"time": 8}]}, "rxu7": {"rotate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "angle": -14.4, "time": 1.1667}, {"c3": 0.75, "curve": 0.25, "time": 2.6667}, {"c3": 0.75, "curve": 0.25, "angle": -14.4, "time": 3.8667}, {"c3": 0.75, "curve": 0.25, "time": 5.3333}, {"c3": 0.75, "curve": 0.25, "angle": -14.4, "time": 6.5333}, {"time": 8}]}, "all22": {"rotate": [{}, {"angle": -12.75, "time": 0.9}, {"angle": 18.33, "time": 1.7667}, {"time": 2.6667}, {"angle": -12.75, "time": 3.6}, {"angle": 18.33, "time": 4.4667}, {"time": 5.3333}, {"angle": -12.75, "time": 6.2667}, {"angle": 18.33, "time": 7.1333}, {"time": 8}]}, "all21": {"rotate": [{}, {"angle": -7.13, "time": 0.8667}, {"angle": 15.05, "time": 1.7333}, {"time": 2.6667}, {"angle": -7.13, "time": 3.5333}, {"angle": 15.05, "time": 4.4}, {"time": 5.3333}, {"angle": -7.13, "time": 6.2}, {"angle": 15.05, "time": 7.0667}, {"time": 8}]}, "all24": {"rotate": [{}, {"angle": 12.45, "time": 0.6333}, {"angle": -16.79, "time": 1.5}, {"time": 2.6667}, {"angle": 12.45, "time": 3.3}, {"angle": -16.79, "time": 4.2}, {"time": 5.3333}, {"angle": 12.45, "time": 5.9667}, {"angle": -16.79, "time": 6.8667}, {"time": 8}]}, "all23": {"rotate": [{}, {"angle": -22.15, "time": 0.9667}, {"angle": 25.59, "time": 1.8333}, {"time": 2.6667}, {"angle": -22.15, "time": 3.6333}, {"angle": 25.59, "time": 4.5}, {"time": 5.3333}, {"angle": -22.15, "time": 6.3}, {"angle": 25.59, "time": 7.1667}, {"time": 8}]}, "all26": {"rotate": [{}, {"angle": 3.14, "time": 1.4667}, {"angle": -1.63, "time": 2.7667}, {"time": 4}, {"angle": 3.14, "time": 5.4667}, {"angle": -1.63, "time": 6.7667}, {"time": 8}]}, "all25": {"rotate": [{}, {"angle": 19.03, "time": 0.6667}, {"angle": -27.48, "time": 1.5667}, {"time": 2.6667}, {"angle": 19.03, "time": 3.3333}, {"angle": -27.48, "time": 4.2333}, {"time": 5.3333}, {"angle": 19.03, "time": 6}, {"angle": -27.48, "time": 6.9}, {"time": 8}]}, "all28": {"rotate": [{}, {"angle": 23.8, "time": 1.6}, {"angle": -32, "time": 2.9}, {"time": 4}, {"angle": 23.8, "time": 5.6}, {"angle": -32, "time": 6.9}, {"time": 8}]}, "all27": {"rotate": [{}, {"angle": 7.36, "time": 1.5333}, {"angle": -9.73, "time": 2.8333}, {"time": 4}, {"angle": 7.36, "time": 5.5333}, {"angle": -9.73, "time": 6.8333}, {"time": 8}]}, "all31": {"rotate": [{}, {"angle": -7.1, "time": 0.9}, {"angle": 6.27, "time": 1.8333}, {"time": 2.6667}, {"angle": -7.1, "time": 3.6}, {"angle": 6.27, "time": 4.5}, {"time": 5.3333}, {"angle": -7.1, "time": 6.2667}, {"angle": 6.27, "time": 7.1667}, {"time": 8}]}, "all30": {"rotate": [{}, {"angle": -4.81, "time": 0.8667}, {"angle": 4.62, "time": 1.7667}, {"time": 2.6667}, {"angle": -4.81, "time": 3.5333}, {"angle": 4.62, "time": 4.4667}, {"time": 5.3333}, {"angle": -4.81, "time": 6.2}, {"angle": 4.62, "time": 7.1333}, {"time": 8}]}, "all33": {"rotate": [{}, {"angle": -25, "time": 1}, {"angle": 19.18, "time": 1.9}, {"time": 2.6667}, {"angle": -25, "time": 3.6667}, {"angle": 19.18, "time": 4.6}, {"time": 5.3333}, {"angle": -25, "time": 6.3333}, {"angle": 19.18, "time": 7.2667}, {"time": 8}]}, "all32": {"rotate": [{}, {"angle": -11.79, "time": 0.9667}, {"angle": 12.83, "time": 1.8667}, {"time": 2.6667}, {"angle": -11.79, "time": 3.6333}, {"angle": 12.83, "time": 4.5333}, {"time": 5.3333}, {"angle": -11.79, "time": 6.3}, {"angle": 12.83, "time": 7.2}, {"time": 8}]}, "all35": {"rotate": [{}, {"angle": 7.05, "time": 0.7667}, {"angle": -6.71, "time": 1.8667}, {"time": 2.6667}, {"angle": 7.05, "time": 3.4333}, {"angle": -6.71, "time": 4.5333}, {"time": 5.3333}, {"angle": 7.05, "time": 6.1}, {"angle": -6.71, "time": 7.2}, {"time": 8}]}, "all34": {"rotate": [{}, {"angle": -5.86, "time": 0.7667}, {"angle": 8.67, "time": 1.9}, {"time": 2.6667}, {"angle": -5.86, "time": 3.4667}, {"angle": 8.67, "time": 4.5667}, {"time": 5.3333}, {"angle": -5.86, "time": 6.1333}, {"angle": 8.67, "time": 7.2333}, {"time": 8}]}, "all37": {"rotate": [{}, {"angle": 13.66, "time": 0.8333}, {"angle": -14.86, "time": 1.9667}, {"time": 2.6667}, {"angle": 13.66, "time": 3.5333}, {"angle": -14.86, "time": 4.6333}, {"time": 5.3333}, {"angle": 13.66, "time": 6.2}, {"angle": -14.86, "time": 7.3}, {"time": 8}]}, "all36": {"rotate": [{}, {"angle": 11.89, "time": 0.8}, {"angle": -7.25, "time": 1.9}, {"time": 2.6667}, {"angle": 11.89, "time": 3.4667}, {"angle": -7.25, "time": 4.6}, {"time": 5.3333}, {"angle": 11.89, "time": 6.1333}, {"angle": -7.25, "time": 7.2667}, {"time": 8}]}, "all39": {"rotate": [{}, {"angle": -5.78, "time": 0.8}, {"angle": 3.29, "time": 1.7}, {"time": 2.6667}, {"angle": -5.78, "time": 3.4667}, {"angle": 3.29, "time": 4.3667}, {"time": 5.3333}, {"angle": -5.78, "time": 6.1333}, {"angle": 3.29, "time": 7.0333}, {"time": 8}]}, "all38": {"rotate": [{}, {"angle": 34.6, "time": 0.9}, {"angle": -27.09, "time": 2}, {"time": 2.6667}, {"angle": 34.6, "time": 3.5667}, {"angle": -27.09, "time": 4.6667}, {"time": 5.3333}, {"angle": 34.6, "time": 6.2333}, {"angle": -27.09, "time": 7.3333}, {"time": 8}]}, "all40": {"rotate": [{}, {"angle": -4.63, "time": 0.8333}, {"angle": 3.13, "time": 1.7333}, {"time": 2.6667}, {"angle": -4.63, "time": 3.5333}, {"angle": 3.13, "time": 4.4}, {"time": 5.3333}, {"angle": -4.63, "time": 6.2}, {"angle": 3.13, "time": 7.0667}, {"time": 8}]}, "all42": {"rotate": [{}, {"angle": -6.44, "time": 0.9333}, {"angle": 6.12, "time": 1.8333}, {"time": 2.6667}, {"angle": -6.44, "time": 3.6}, {"angle": 6.12, "time": 4.5}, {"time": 5.3333}, {"angle": -6.44, "time": 6.2667}, {"angle": 6.12, "time": 7.1667}, {"time": 8}]}, "all41": {"rotate": [{}, {"angle": -5.5, "time": 0.9}, {"angle": 4.29, "time": 1.7667}, {"time": 2.6667}, {"angle": -5.5, "time": 3.5667}, {"angle": 4.29, "time": 4.4667}, {"time": 5.3333}, {"angle": -5.5, "time": 6.2333}, {"angle": 4.29, "time": 7.1333}, {"time": 8}]}, "all44": {"rotate": [{}, {"angle": -16.54, "time": 1.0333}, {"angle": 10.67, "time": 1.9}, {"time": 2.6667}, {"angle": -16.54, "time": 3.7}, {"angle": 10.67, "time": 4.6}, {"time": 5.3333}, {"angle": -16.54, "time": 6.3667}, {"angle": 10.67, "time": 7.2667}, {"time": 8}]}, "all43": {"rotate": [{}, {"angle": -13.41, "time": 0.9667}, {"angle": 6.97, "time": 1.8667}, {"time": 2.6667}, {"angle": -13.41, "time": 3.6667}, {"angle": 6.97, "time": 4.5333}, {"time": 5.3333}, {"angle": -13.41, "time": 6.3333}, {"angle": 6.97, "time": 7.2}, {"time": 8}]}, "all46": {"rotate": [{}, {"angle": 7.31, "time": 0.6333}, {"angle": -16.46, "time": 1.5667}, {"time": 2.6667}, {"angle": 7.31, "time": 3.3}, {"angle": -16.46, "time": 4.2333}, {"time": 5.3333}, {"angle": 7.31, "time": 5.9667}, {"angle": -16.46, "time": 6.9}, {"time": 8}]}, "all45": {"rotate": [{}, {"angle": -34.29, "time": 1.0667}, {"angle": 39.22, "time": 1.9667}, {"time": 2.6667}, {"angle": -34.29, "time": 3.7333}, {"angle": 39.22, "time": 4.6333}, {"time": 5.3333}, {"angle": -34.29, "time": 6.4}, {"angle": 39.22, "time": 7.3}, {"time": 8}]}, "all48": {"rotate": [{}, {"angle": 17.5, "time": 0.7}, {"angle": -27.78, "time": 1.6333}, {"time": 2.6667}, {"angle": 17.5, "time": 3.4}, {"angle": -27.78, "time": 4.3333}, {"time": 5.3333}, {"angle": 17.5, "time": 6.0667}, {"angle": -27.78, "time": 7}, {"time": 8}]}, "all47": {"rotate": [{}, {"angle": 13.61, "time": 0.6667}, {"angle": -16.98, "time": 1.6}, {"time": 2.6667}, {"angle": 13.61, "time": 3.3333}, {"angle": -16.98, "time": 4.2667}, {"time": 5.3333}, {"angle": 13.61, "time": 6}, {"angle": -16.98, "time": 6.9333}, {"time": 8}]}, "all49": {"rotate": [{}, {"angle": -7.8, "time": 0.6333}, {"angle": 7.49, "time": 1.8667}, {"time": 2.6667}, {"angle": -7.8, "time": 3.3}, {"angle": 7.49, "time": 4.5333}, {"time": 5.3333}, {"angle": -7.8, "time": 5.9667}, {"angle": 7.49, "time": 7.2}, {"time": 8}]}, "all51": {"rotate": [{}, {"angle": -23.73, "time": 0.7}, {"angle": 27.86, "time": 1.9667}, {"time": 2.6667}, {"angle": -23.73, "time": 3.4}, {"angle": 27.86, "time": 4.6333}, {"time": 5.3333}, {"angle": -23.73, "time": 6.0667}, {"angle": 27.86, "time": 7.3}, {"time": 8}]}, "all50": {"rotate": [{}, {"angle": -13.62, "time": 0.6667}, {"angle": 9.99, "time": 1.9}, {"time": 2.6667}, {"angle": -13.62, "time": 3.3333}, {"angle": 9.99, "time": 4.6}, {"time": 5.3333}, {"angle": -13.62, "time": 6}, {"angle": 9.99, "time": 7.2667}, {"time": 8}]}, "all53": {"rotate": [{}, {"angle": 5.59, "time": 0.7667}, {"angle": -6.2, "time": 1.9}, {"time": 2.6667}, {"angle": 5.59, "time": 3.4333}, {"angle": -6.2, "time": 4.5667}, {"time": 5.3333}, {"angle": 5.59, "time": 6.1}, {"angle": -6.2, "time": 7.2333}, {"time": 8}]}, "all52": {"rotate": [{}, {"angle": 6.26, "time": 0.7}, {"angle": -4.75, "time": 1.8333}, {"time": 2.6667}, {"angle": 6.26, "time": 3.4}, {"angle": -4.75, "time": 4.5333}, {"time": 5.3333}, {"angle": 6.26, "time": 6.0667}, {"angle": -4.75, "time": 7.2}, {"time": 8}]}, "all55": {"rotate": [{}, {"angle": 41.45, "time": 0.8333}, {"angle": -17.18, "time": 1.9667}, {"time": 2.6667}, {"angle": 41.45, "time": 3.5333}, {"angle": -17.18, "time": 4.6667}, {"time": 5.3333}, {"angle": 41.45, "time": 6.2}, {"angle": -17.18, "time": 7.3333}, {"time": 8}]}, "all54": {"rotate": [{}, {"angle": 7.56, "time": 0.8}, {"angle": -7.52, "time": 1.9333}, {"time": 2.6667}, {"angle": 7.56, "time": 3.4667}, {"angle": -7.52, "time": 4.6}, {"time": 5.3333}, {"angle": 7.56, "time": 6.1333}, {"angle": -7.52, "time": 7.2667}, {"time": 8}]}, "all57": {"rotate": [{}, {"angle": -4.97, "time": 0.7}, {"angle": 4.56, "time": 1.9667}, {"time": 2.6667}, {"angle": -4.97, "time": 3.4}, {"angle": 4.56, "time": 4.6667}, {"time": 5.3333}, {"angle": -4.97, "time": 6.0667}, {"angle": 4.56, "time": 7.3333}, {"time": 8}]}, "all56": {"rotate": [{}, {"angle": -3.16, "time": 0.6333}, {"angle": 4.72, "time": 2.0667}, {"time": 2.6667}, {"angle": -3.16, "time": 3.3}, {"angle": 4.72, "time": 4.7333}, {"time": 5.3333}, {"angle": -3.16, "time": 5.9667}, {"angle": 4.72, "time": 7.4}, {"time": 8}]}, "all58": {"rotate": [{}, {"angle": -10.63, "time": 0.7667}, {"angle": 14.74, "time": 2.0333}, {"time": 2.6667}, {"angle": -10.63, "time": 3.4333}, {"angle": 14.74, "time": 4.7}, {"time": 5.3333}, {"angle": -10.63, "time": 6.1}, {"angle": 14.74, "time": 7.3667}, {"time": 8}]}, "light15": {"scale": [{"time": 3.7333}, {"x": 1.128, "y": 1.128, "time": 6.1667}], "translate": [{"time": 3.7333}, {"x": 22.49, "y": 23.15, "time": 6.1667}]}, "all60": {"rotate": [{}, {"angle": 9.11, "time": 0.6667}, {"angle": -5.78, "time": 1.7}, {"time": 2.6667}, {"angle": 9.11, "time": 3.3333}, {"angle": -5.78, "time": 4.4}, {"time": 5.3333}, {"angle": 9.11, "time": 6}, {"angle": -5.78, "time": 7.0667}, {"time": 8}]}, "light13": {"scale": [{"time": 5.2}, {"x": 1.128, "y": 1.128, "time": 7.6333}], "translate": [{"time": 5.2}, {"x": 16.79, "y": 38.39, "time": 6.3}, {"x": 36.95, "y": 67.86, "time": 7.6333}]}, "light14": {"scale": [{"time": 4.8333}, {"x": 1.128, "y": 1.128, "time": 7.2667}], "translate": [{"time": 4.8333}, {"x": 16.79, "y": 38.39, "time": 5.9333}, {"x": 22.77, "y": 42.08, "time": 7.2667}]}, "all62": {"rotate": [{}, {"angle": 22.45, "time": 0.7667}, {"angle": -24.22, "time": 1.8}, {"time": 2.6667}, {"angle": 22.45, "time": 3.4333}, {"angle": -24.22, "time": 4.4667}, {"time": 5.3333}, {"angle": 22.45, "time": 6.1}, {"angle": -24.22, "time": 7.1333}, {"time": 8}]}, "light19": {"scale": [{"time": 0.1333}, {"x": 1.128, "y": 1.128, "time": 3.2333}], "translate": [{"time": 0.1333}, {"x": -11.57, "y": 25.95, "time": 1.0333}, {"x": -16.12, "y": 40.46, "time": 1.7}, {"x": -7.99, "y": 57.24, "time": 2.4}, {"x": -8.37, "y": 65.12, "time": 3.2333}]}, "all9": {"rotate": [{}, {"angle": 4.51, "time": 0.8333}, {"angle": -3.46, "time": 1.8333}, {"time": 2.6667}, {"angle": 4.51, "time": 3.5}, {"angle": -3.46, "time": 4.5}, {"time": 5.3333}, {"angle": 4.51, "time": 6.1667}, {"angle": -3.46, "time": 7.1667}, {"time": 8}]}, "all61": {"rotate": [{}, {"angle": 10.33, "time": 0.7}, {"angle": -6.68, "time": 1.7667}, {"time": 2.6667}, {"angle": 10.33, "time": 3.4}, {"angle": -6.68, "time": 4.4333}, {"time": 5.3333}, {"angle": 10.33, "time": 6.0667}, {"angle": -6.68, "time": 7.1}, {"time": 8}]}, "rhand": {"translate": [{}, {"y": -1.79, "time": 1.2}, {"time": 4}, {"y": -1.79, "time": 5.2}, {"time": 8}]}, "light17": {"scale": [{"time": 1.4333}, {"x": 1.128, "y": 1.128, "time": 4.5333}], "translate": [{"time": 1.4333}, {"x": -11.57, "y": 25.95, "time": 2.3}, {"x": -16.12, "y": 40.46, "time": 2.9667}, {"x": -7.99, "y": 57.24, "time": 3.7}, {"x": -8.37, "y": 65.12, "time": 4.5333}]}, "light18": {"scale": [{"time": 4.7}, {"x": 1.128, "y": 1.128, "time": 7.8}], "translate": [{"time": 4.7}, {"x": -11.57, "y": 25.95, "time": 5.6}, {"x": -16.12, "y": 40.46, "time": 6.2667}, {"x": -7.99, "y": 57.24, "time": 6.9667}, {"x": -8.37, "y": 65.12, "time": 7.8}]}, "all6": {"rotate": [{}, {"angle": 2.16, "time": 0.7}, {"angle": -1.75, "time": 1.7}, {"time": 2.6667}, {"angle": 2.16, "time": 3.3667}, {"angle": -1.75, "time": 4.3667}, {"time": 5.3333}, {"angle": 2.16, "time": 6.0333}, {"angle": -1.75, "time": 7.0333}, {"time": 8}]}, "all5": {"rotate": [{}, {"angle": 1.36, "time": 0.6333}, {"angle": -1.54, "time": 1.6333}, {"time": 2.6667}, {"angle": 1.36, "time": 3.3333}, {"angle": -1.54, "time": 4.3333}, {"time": 5.3333}, {"angle": 1.36, "time": 6}, {"angle": -1.54, "time": 7}, {"time": 8}]}, "all8": {"rotate": [{}, {"angle": 2.53, "time": 0.7667}, {"angle": -2.56, "time": 1.7667}, {"time": 2.6667}, {"angle": 2.53, "time": 3.4667}, {"angle": -2.56, "time": 4.4667}, {"time": 5.3333}, {"angle": 2.53, "time": 6.1333}, {"angle": -2.56, "time": 7.1333}, {"time": 8}]}, "all7": {"rotate": [{}, {"angle": 2.76, "time": 0.7333}, {"angle": -2.88, "time": 1.7333}, {"time": 2.6667}, {"angle": 2.76, "time": 3.4}, {"angle": -2.88, "time": 4.4}, {"time": 5.3333}, {"angle": 2.76, "time": 6.0667}, {"angle": -2.88, "time": 7.0667}, {"time": 8}]}, "all2": {"translate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "y": -1.54, "time": 2}, {"c3": 0.75, "curve": 0.25, "time": 4}, {"c3": 0.75, "curve": 0.25, "y": -1.54, "time": 6}, {"time": 8}]}, "light5": {"scale": [{"time": 4}, {"x": 1.128, "y": 1.128, "time": 7.1}], "translate": [{"time": 4}, {"x": -11.57, "y": 25.95, "time": 4.8667}, {"x": -16.12, "y": 40.46, "time": 5.5333}, {"x": -7.99, "y": 57.24, "time": 6.2667}, {"x": -8.37, "y": 65.12, "time": 7.1}]}, "light11": {"scale": [{"time": 1.9667}, {"x": 1.128, "y": 1.128, "time": 4.4}], "translate": [{"time": 1.9667}, {"x": 16.79, "y": 38.39, "time": 3.0667}, {"x": 36.95, "y": 67.86, "time": 4.4}]}, "light4": {"scale": [{"time": 2.2333}, {"x": 1.128, "y": 1.128, "time": 6.0667}], "translate": [{"time": 2.2333}, {"x": -11.57, "y": 25.95, "time": 3.3333}, {"x": -16.12, "y": 40.46, "time": 4.1667}, {"x": -7.99, "y": 57.24, "time": 5}, {"x": -8.37, "y": 65.12, "time": 6.0667}]}, "light12": {"scale": [{"time": 4.7333}, {"x": 1.329, "y": 1.329, "time": 7.2}], "translate": [{"time": 4.7333}, {"x": -4.74, "y": 68.76, "time": 7.2}]}, "all4": {"rotate": [{}, {"angle": 2.04, "time": 0.6}, {"angle": -1, "time": 1.6}, {"time": 2.6667}, {"angle": 2.04, "time": 3.2667}, {"angle": -1, "time": 4.2667}, {"time": 5.3333}, {"angle": 2.04, "time": 5.9333}, {"angle": -1, "time": 6.9333}, {"time": 8}]}, "light3": {"scale": [{"time": 2.2333}, {"x": 1.128, "y": 1.128, "time": 5.3333}], "translate": [{"time": 2.2333}, {"x": 9.99, "y": 70.39, "time": 5.3333}]}, "all3": {"rotate": [{}, {"angle": 3.01, "time": 1.4}, {"angle": -3.57, "time": 2.7}, {"time": 4}, {"angle": 3.01, "time": 5.4}, {"angle": -3.57, "time": 6.7}, {"time": 8}]}, "light2": {"scale": [{"time": 0.9}, {"x": 1.128, "y": 1.128, "time": 3.3333}], "translate": [{"time": 0.9}, {"x": 14.54, "y": 39.52, "time": 3.3333}]}, "light9": {"scale": [{"time": 2.4}, {"x": 1.128, "y": 1.128, "time": 6.3333}], "translate": [{"time": 2.4}, {"x": 8.84, "y": 39.53, "time": 4.2333}, {"x": -0.63, "y": 56.53, "time": 6.3333}]}, "light8": {"scale": [{"time": 0.6667}, {"x": 1.128, "y": 1.128, "time": 3.7667}], "translate": [{"time": 0.6667}, {"x": -13.54, "y": 45.03, "time": 3.7667}]}, "light7": {"scale": [{"time": 1.1667}, {"x": 1.128, "y": 1.128, "time": 3.6}], "translate": [{"time": 1.1667}, {"x": 16.79, "y": 38.39, "time": 2.2667}, {"x": 36.95, "y": 67.86, "time": 3.6}]}, "light6": {"scale": [{"time": 0.9}, {"x": 1.329, "y": 1.329, "time": 3.3333}], "translate": [{"time": 0.9}, {"x": -3.01, "y": 44.65, "time": 3.3333}]}, "lxu3": {"rotate": [{}, {"angle": -2.45, "time": 0.7333}, {"angle": 3.64, "time": 1.6}, {"time": 2.6667}, {"angle": -2.45, "time": 3.4}, {"angle": 3.64, "time": 4.2667}, {"time": 5.3333}, {"angle": -2.45, "time": 6.0667}, {"angle": 3.64, "time": 6.9333}, {"time": 8}]}, "lxu4": {"rotate": [{}, {"angle": 5.45, "time": 0.5}, {"angle": -7.31, "time": 1.3667}, {"time": 2.6667}, {"angle": 5.45, "time": 3.1667}, {"angle": -7.31, "time": 4.0667}, {"time": 5.3333}, {"angle": 5.45, "time": 5.8333}, {"angle": -7.31, "time": 6.7333}, {"time": 8}]}, "lxu1": {"rotate": [{}, {"angle": 1.77, "time": 0.5667}, {"angle": -3.2, "time": 1.5667}, {"time": 2.6667}, {"angle": 1.77, "time": 3.2333}, {"angle": -3.2, "time": 4.2333}, {"time": 5.3333}, {"angle": 1.77, "time": 5.9}, {"angle": -3.2, "time": 6.9}, {"time": 8}]}, "lxu2": {"rotate": [{}, {"angle": -3.74, "time": 1.1}, {"angle": 3.78, "time": 2.1}, {"time": 2.6667}, {"angle": -3.74, "time": 3.7667}, {"angle": 3.78, "time": 4.8}, {"time": 5.3333}, {"angle": -3.74, "time": 6.4333}, {"angle": 3.78, "time": 7.4667}, {"time": 8}]}, "lxu7": {"rotate": [{}, {"angle": -5.08, "time": 0.7}, {"angle": 5.08, "time": 1.8333}, {"time": 2.6667}, {"angle": -5.08, "time": 3.4}, {"angle": 5.08, "time": 4.5}, {"time": 5.3333}, {"angle": -5.08, "time": 6.0667}, {"angle": 5.08, "time": 7.1667}, {"time": 8}]}, "lxu8": {"rotate": [{}, {"angle": 7.98, "time": 0.7}, {"angle": -5.78, "time": 1.8333}, {"time": 2.6667}, {"angle": 7.98, "time": 3.4}, {"angle": -5.78, "time": 4.5}, {"time": 5.3333}, {"angle": 7.98, "time": 6.0667}, {"angle": -5.78, "time": 7.1667}, {"time": 8}]}, "lxu6": {"rotate": [{}, {"angle": -4.32, "time": 0.8333}, {"angle": 5.75, "time": 1.7333}, {"time": 2.6667}, {"angle": -4.32, "time": 3.5}, {"angle": 5.75, "time": 4.4}, {"time": 5.3333}, {"angle": -4.32, "time": 6.1667}, {"angle": 5.75, "time": 7.0667}, {"time": 8}]}, "light26": {"scale": [{"x": 1.06, "y": 1.06}, {"x": 1.128, "y": 1.128, "time": 1.3}, {"time": 6.8667}, {"x": 1.058, "y": 1.058, "time": 8}], "translate": [{"x": -2.15, "y": 18.87}, {"x": -8.97, "y": 26.53, "time": 1.3}, {"time": 6.8667}, {"x": -2.15, "y": 18.87, "time": 8}]}, "light24": {"scale": [{"time": 0.1}, {"x": 1.329, "y": 1.329, "time": 2.5333}], "translate": [{"time": 0.1}, {"x": -15.49, "y": 33.29, "time": 2.5333}]}, "light25": {"scale": [{"x": 1.06, "y": 1.06}, {"x": 1.128, "y": 1.128, "time": 1.3}, {"time": 6.8667}, {"x": 1.058, "y": 1.058, "time": 8}], "translate": [{"x": 12.2, "y": 20.59}, {"x": 24.32, "y": 36.29, "time": 1.3}, {"time": 6.8667}, {"x": 12.2, "y": 20.59, "time": 8}]}, "lhand": {"translate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "y": 2.05, "time": 2.0333}, {"c3": 0.75, "curve": 0.25, "time": 4}, {"c3": 0.75, "curve": 0.25, "y": 2.05, "time": 6.0333}, {"time": 8}]}, "light23": {"scale": [{"x": 1.06, "y": 1.06}, {"x": 1.128, "y": 1.128, "time": 1.3}, {"time": 6.8667}, {"x": 1.058, "y": 1.058, "time": 8}], "translate": [{"x": 17.47, "y": 39.37}, {"x": 36.95, "y": 67.86, "time": 1.3}, {"time": 6.8667}, {"x": 16.79, "y": 38.39, "time": 8}]}, "light20": {"scale": [{"x": 1.06, "y": 1.06}, {"x": 1.128, "y": 1.128, "time": 1.3}, {"time": 6.8667}, {"x": 1.058, "y": 1.058, "time": 8}], "translate": [{"x": 17.47, "y": 39.37}, {"x": 36.95, "y": 67.86, "time": 1.3}, {"time": 6.8667}, {"x": 16.79, "y": 38.39, "time": 8}]}, "light21": {"scale": [{"x": 1.086, "y": 1.086}, {"x": 1.128, "y": 1.128, "time": 0.8}, {"time": 6.4}, {"x": 1.083, "y": 1.083, "time": 8}], "translate": [{"x": 2.76, "y": 14.69}, {"x": 15.57, "y": 22.1, "time": 0.8}, {"time": 6.4}, {"x": 2.76, "y": 14.69, "time": 8}]}}}}}, [0]]], 0, 0, [0], [-1], [26]], [[{"name": "tTI", "rect": [0, 0, 187, 65], "offset": [0, 0], "originalSize": [187, 65], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [27]], [[{"name": "btn232", "rect": [0, 0, 175, 55], "offset": [0, 0], "originalSize": [175, 55], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [28]], [[{"name": "xiu", "rect": [0, 0, 156, 97], "offset": [0, 0], "originalSize": [156, 97], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [29]], [[{"name": "tai", "rect": [0, 0, 132, 88], "offset": [0, 0.5], "originalSize": [132, 89], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [30]], [[[57, "openshowmd5", 0.4444444444444444, 45, 0.5, 22, {"props": {"opacity": [{"frame": 0, "value": 250}, {"frame": 0.2222222222222222, "value": 100}, {"frame": 0.4444444444444444, "value": 255}]}}]], 0, 0, [], [], []], [[{"name": "boxchat1", "rect": [0, 0, 413, 596], "offset": [0, 0], "originalSize": [413, 596], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [31]], [[{"name": "SVN-franko", "rect": [2, 2, 507, 256], "offset": [-0.5, 126], "originalSize": [512, 512], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [32]], [[{"name": "bgchen", "rect": [0, 0, 252, 252], "offset": [0, 0], "originalSize": [252, 252], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [33]], [[{"name": "thong<PERSON>e", "rect": [0, 0, 67, 66], "offset": [0, 0], "originalSize": [67, 66], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [34]], [[{"name": "bantxmd52", "rect": [0, 0, 761, 355], "offset": [0, 0], "originalSize": [761, 355], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [35]], [[{"name": "x", "rect": [0, 0, 67, 66], "offset": [0, 0], "originalSize": [67, 66], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [36]], [[{"name": "bgSoiCau", "rect": [0, 0, 515, 29], "offset": [0, 0], "originalSize": [515, 29], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [37]], [[{"name": "nanDisableBlack", "rect": [0, 0, 65, 65], "offset": [0, 0], "originalSize": [65, 65], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [38]], [[[58, "taixiuMd5View"], [59, "taixiuView", [-61, -62, -63], [[[77, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, [-4, -5, -6], -3, -2], [42, -28, [215, 216], 214], [78, -31, -30, -29], [79, -56, -55, -54, -53, -52, -51, -50, [-47, -48, -49], -46, -45, -44, -43, -42, -41, [-40], [-39], -38, -37, -36, -35, -34, -33, -32], -57, [80, -58, 217, 218, 219, 220, 221, 222, 223], [81, -59], [82, -60]], 4, 4, 4, 4, 1, 4, 4, 4], [83, -1], [5, 800, 500]], [64, "betInputView", [-78, -79], [[84, -75, -74, -73, -72, -71, -70, -69, -68, -67, -66, -65, -64], [24, -76, [144, 145]], [43, -77, 146]], [0, "f1/Ciwkr9DrYMuBCRoQX+r", 1], [5, 900, 170], [0, 0.5, 1], [0, -202, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "taiXiuMain", 1, [-80, -81, -82, -83, -84, -85, 2, -86, -87, -88, -89, -90, -91, -92, -93, -94, -95, -96, -97, -98, -99, -100, -101, -102, -103, -104], [0, "f2PA3AISdOeICO/OiRMTCK", 1], [5, 900, 420]], [9, "session<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", 3, [-123, -124, -125, -126, -127, -128, -129, -130, -131, -132, -133, -134, -135, -136, -137, -138], [[25, 1, 1, 18.5, -105, [5, 709, 40]], [90, -122, [-106, -107, -108, -109, -110, -111, -112, -113, -114, -115, -116, -117, -118, -119, -120, -121], [53, 54]]], [0, "84zLbYIDROxZQyE4m7nBO6", 1], [5, 709, 40], [-0.093, -165.441, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [34, "buttonView", 3, [-144, -145, -146, -147, -148, -149, -150, -151, -152], [[91, -143, -142, -141, -140, [70, 71], -139, [72, 73]]], [0, "96LGW41hVLrYfxg1hiEyaV", 1]], [65, "layout-free", false, [-154, -155, -156, -157, -158, -159, -160, -161, -162, -163, -164, -165], [[86, false, 1, 3, 25.9, 15, -153, [5, 596, 107]]], [0, "86cL5cJoFDLYbbHeMalb7J", 1], [5, 596, 107], [2, -74, 0, 0, 0, 0, 1, 1, 1, 1]], [66, "layout-value", [-167, -168, -169, -170, -171, -172, -173, -174, -175, -176], [[87, 1, 1, 18, 15, -166, [5, 886, 86]]], [0, "d1JnSaiIJGoaZceGkpwCXM", 1], [5, 886, 86], [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "chatView", [-182, -183, -184, -185], [[92, -181, -180, -179, -178, -177]], [0, "59rGikyehL15ZugGeK2QZt", 1], [5, 360, 420]], [30, "result-sprite", 3, [-186, -187, -188, -189, -190, -191, -192], [0, "69OILfZQJMUIpeEueV6mFO", 1]], [9, "bg-main", 3, [-194, -195, -196, -197, -198], [[11, false, -193, [78], 79]], [0, "515YzdXMNISIVz9dU7CcI4", 1], [5, 761, 355], [3.613, -6.022, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [60, "layout-infoBet", 3, [-199, -200, -201, -202, -203, -204], [0, "5d3NAB5TxG6LVDHjiwA47D", 1], [5, 200, 150], [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "vn1102-dice", false, 9, [-205, -206, -207, -208], [0, "af5doVS6RO/ZQg3DelYMlu", 1]], [1, "bowl", 9, [[26, 0, false, -209, [190], 191], [97, -211, [4, 4292269782], -210, 192], [104, -213, -212]], [0, "1eoeJVUctL5bEH5s8XGDrp", 1], [5, 247, 247], [1, 13, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [35, "item", [-221, -222], [[25, 1, 2, 5, -214, [5, 320, 64.5]], [106, -220, -219, -218, -217, -216, -215]], [0, "f87v/ZvNdK6JU2FRvPLhmN", 1], [5, 320, 64.5]], [9, "item-horizontal", 14, [-224, -225, -226], [[88, 1, 5, -223, [5, 315, 33]]], [0, "e09E6Vr0BFpY3SDMXL/QxO", 1], [5, 315, 33], [4.484, -15.75, 0, 0, 0, 0, 1, 1.04, 1.04, 1.04]], [67, "bg-input", false, 2, [7, 6, -228], [[98, -227]], [0, "66VWM5sqRKZYPtSGsZZWdM", 1], [5, 900, 170], [0, 0.5, 1], [0, -4, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "layout-button", 16, [-230, -231, -232, -233], [[25, 1, 1, -45, -229, [5, 760, 100]]], [0, "dfYO7d9A5CMouMLksv74zK", 1], [5, 760, 100], [0, -123, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "layout-info", 3, [-234, -235, -236, -237, -238], [0, "f7utxE0mtBeY7DdIIgeNtM", 1], [5, 200, 150]], [9, "md5View", 3, [-240, -241, -242, -243], [[3, -239, [178], 179]], [0, "c0hkHZ3HtF87bgVxu5ZQ5a", 1], [5, 605, 41], [0, -205.089, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "nodeketqua", false, 19, [-244, -245, -246], [0, "3cAtq/CDBOn60zt1kmem4w", 1]], [34, "resultEffectView", 3, [-250, -251, -252, -253], [[107, -249, -248, -247]], [0, "e1VOoT8LdIVqhSNlGMfrsT", 1]], [9, "bg_popup_chat", 8, [-256, -257], [[93, 1, 0, false, -254, [60], 61], [50, -255]], [0, "70kDG7KvpFJIwiIR89/vwM", 1], [5, 374, 468], [0, -15, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [15, "editbox-chat", 8, [-259, -260, -261], [-258], [0, "d3G70mk45McYI1QLXe0l84", 1], [5, 200, 47.5], [-55.3, -176.3, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", 8, [-264, -265], [-262, -263], [0, "feJr3qVLpBQ5ldigMpP7n0", 1], [5, 322, 300], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "btnOpenInputTai", [[3, -266, [136], 137], [49, -268, [[5, "faac23uBfBBJ4eRyib6vBGW", "openInputBetTaiClicked", 2]], [4, 4294967295], [4, 4294967295], -267]], [0, "c1I9UG3gtOcq2Qs+YIx167", 1], [5, 187, 65], [1, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "editboxDatXiu", false, 0, 11, [-270, -271, -272], [-269], [0, "f4fCNOM+dM/bdIaVDF/hyn", 1], [5, 160, 40], [251, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "editboxDatTai", false, 0, 11, [-274, -275, -276], [-273], [0, "bfmT2f9jJI8LNedES0L1FN", 1], [5, 174, 64], [-251, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "btnOpenInputXiu", [[3, -277, [127], 128], [49, -279, [[5, "faac23uBfBBJ4eRyib6vBGW", "openInputBetXiuClicked", 2]], [4, 4294967295], [4, 4294967295], -278]], [0, "1bt77GtRRCxoVVDf3xqaVR", 1], [5, 187, 65], [2, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "bg-timer", false, 3, [-281], [[11, false, -280, [196], 197]], [0, "0f6Nbu03pAqKC0bjfSTv3Y", 1], [5, 51, 51], [0, 134, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [68, "light_off", 120, 1, [[18, 0, -282, [0], 1], [24, -283, [2, 3]], [109, -284]], [0, "ffU/DJmmJLVYXV76BG1uXo", 1], [5, 4000, 4000], [-1.1368683772161603e-13, 3.410605131648481e-13, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "taixiumd5", false, 3, [-286, -287], [[13, "default", "animation", 0, "animation", -285, [8], 9]], [0, "15KCwW13NLXIyS+tAlnyqZ", 1], [5, 647, 157.37], [-3.96, 202.593, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [9, "btnTop", 5, [-290], [[11, false, -288, [22], 23], [2, 1.1, 3, -289, [[5, "3a9ccPvVxFB/4d66plykjoi", "topClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "46+Yc5MblJ86YaTNxGAdIs", 1], [5, 67, 66], [401, 119, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "btnSend", 8, [-292], [-291], [0, "177h3AgtBNyZrjOAqB5i8a", 1], [5, 80, 44], [108.4, -182.7, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btnAllin", 17, [-296], [[3, -293, [115], 116], [99, 3, -295, [[5, "faac23uBfBBJ4eRyib6vBGW", "allInClick", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -294, 117, 118, 119]], [0, "90ZgdbrpBMirFThZHyolZS", 1], [5, 256, 61], [-252, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [69, "Background", false, 34, [[94, 1, -297, [113], 114], [111, 0, 45, -53, -53, -0.5, -0.5, 100, 40, -298]], [0, "f3FRrARztHGqOXh9GOEQcW", 1], [4, 4293322470], [5, 256, 61], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "bgLbTai", 11, [-300, 25], [[3, -299, [134], 135]], [0, "48Y7nvAqhM4qV0GTiUYMK+", 1], [5, 175, 55], [-243.2, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "bgLbXiu", 11, [-302, 28], [[3, -301, [129], 130]], [0, "efxjyd7pxOGpP55bS/WtNF", 1], [5, 175, 55], [246, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [112, "<PERSON>n mời nặn", 18, 0, false, -1, 1, 1, [143]], [70, "bgNotify", 0, 3, [-305], [[18, 0, -303, [138], 139], [42, -304, [141, 142], 140]], [0, "0aaFvRSIlD0o93ssBa1m1S", 1], [5, 560, 50], [0, 44, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tai", 3, [[26, 2, false, -306, [147], 148]], [0, "0aZUoukDlEo7mReWGR3Ryk", 1], [5, 132, 89], [-244.7, 87.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "xiu", 3, [[26, 2, false, -307, [149], 150]], [0, "08HEJaNexPCa/mbvum/mUp", 1], [5, 156, 97], [254.2, 93.9, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "taiWin", false, 3, [[13, "default", "tai", 0, "tai", -308, [151], 152]], [0, "c3gFqZcRVHrKfLpm8Bv2Kj", 1], [-229.193, 72, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [38, "x<PERSON><PERSON><PERSON>", false, 3, [[13, "default", "xiu", 0, "xiu", -309, [153], 154]], [0, "abgsXJJ0JLLbdIAtRNcNRB", 1], [258, 75, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [31, "result-<PERSON><PERSON>", 9, [-310, -311, -312], [0, "692uQjG7tDlrb9DKjUf2ko", 1], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dia", 9, [[18, 0, -313, [188], 189]], [0, "7cmg6rPTtHf5wsz2X++KJY", 1], [5, 247, 247], [1, 13, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [61, "bg result", 0, 3, [-316], [[[18, 0, -314, [210], 211], -315], 4, 1], [0, "50fMAtgQpOlrZ9o18qYl84", 1], [5, 560, 50], [0, 44, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 5, [[11, false, -317, [18], 19], [2, 1.1, 3, -318, [[5, "3a9ccPvVxFB/4d66plykjoi", "helpClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "30/RiPZ2RAXJY+GyzdbF4q", 1], [5, 67, 66], [-431, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHistory", 5, [[11, false, -319, [24], 25], [2, 1.1, 3, -320, [[5, "3a9ccPvVxFB/4d66plykjoi", "historyClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "5cEqbomeZC2LKYwCJOzqiA", 1], [5, 67, 66], [-307.515, 188, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "btnNan", 5, [[-321, [2, 1.1, 3, -322, [[5, "3a9ccPvVxFB/4d66plykjoi", "nanClicked", 5]], [4, 4294967295], [4, 4294967295]]], 1, 4], [0, "75qOvftIFOW4kS5SL8dbj1", 1], [5, 65, 65], [394, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 5, [[11, false, -323, [27], 28], [2, 1.1, 3, -324, [[5, "f6418avtdxOd7WVNYqrWf5y", "closeClicked", 1]], [4, 4294967295], [4, 4294967295]]], [0, "60ZBR65J1Gwp2EFOIOuc+p", 1], [5, 67, 66], [315, 188, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "btnChat", 5, [[-325, [2, 1.1, 3, -326, [[5, "3a9ccPvVxFB/4d66plykjoi", "chatClicked", 5]], [4, 4294967295], [4, 4294967295]]], 1, 4], [0, "8bkmx9EdZPZbtY/qQZFREU", 1], [5, 67, 66], [433, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnGraph", 5, [[11, false, -327, [30], 31], [2, 1.1, 3, -328, [[5, "3a9ccPvVxFB/4d66plykjoi", "graphClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "55dZGZPxxMAZGh2sNO4TtR", 1], [5, 67, 66], [-408.43, 119, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnEvent", 5, [[11, false, -329, [32], 33], [2, 1.1, 3, -330, [[5, "0d21e55ZJdLvrAD/qzGrwsv", "sessionDetailClicked", 4]], [4, 4294967295], [4, 4294967295]]], [0, "90wpAWK3BNfZsBllWpFoqg", 1], [5, 67, 66], [-392, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "dot", 4, [-332], [-331], [0, "a1X8s7Z7xMyq3IXDO9qK39", 1], [5, 30, 30], [339.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [71, "dot-outline", 54, [[7, 0, -333, [49]], [17, true, -334, [51], 50]], [0, "55luKeYlpDlp2x5srtni7u", 1], [5, 36, 44]], [1, "close", 22, [[3, -335, [57], 58], [27, 1.1, 3, -336, [[5, "3a9ccPvVxFB/4d66plykjoi", "chatClicked", 5]], 59]], [0, "5bWaRypTJB97nj51DuP77i", 1], [5, 96, 50], [156, 212, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [9, "view", 24, [-338], [[128, -337, [68]]], [0, "ec/6ZA3WFGJ766Dm4h76bM", 1], [5, 325, 300], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content", 57, [[89, 1, 2, -339, [5, 300, 0]]], [0, "2aRhuuAc5GTZZjgTJQiRVc", 1], [5, 300, 0], [0, 141, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[3, -340, [82], 83], [2, 1.1, 3, -341, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "1000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "78bkHfEhRIGK1LxcEEEkQ2", 1], [5, 95, 58], [-395.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [39, "btnValue", false, 7, [[7, 0, -342, [84]], [2, 1.1, 3, -343, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "5000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "bbqrDXsAFEPIcYHdzcn4fT", 1], [5, 118, 54]], [1, "btnValue", 7, [[3, -344, [85], 86], [2, 1.1, 3, -345, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "10000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "1b4w/c4oFME6Db63LqkXr0", 1], [5, 95, 58], [-282.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnValue", 7, [[3, -346, [87], 88], [2, 1.1, 3, -347, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "50000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "39Bq5L749I/rqCKwljCB3M", 1], [5, 95, 57], [-169.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnValue", 7, [[3, -348, [89], 90], [2, 1.1, 3, -349, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "100000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "c3/hrjI2xFMaFCUiGow2Z9", 1], [5, 95, 56], [-56.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [39, "btnValue", false, 7, [[45, false, 0, -350, [91]], [2, 1.1, 3, -351, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "200000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "d55TPF3ChAY4HV8HosJ6lk", 1], [5, 118, 54]], [1, "btnValue", 7, [[3, -352, [92], 93], [2, 1.1, 3, -353, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "500000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "e5tAnMvflDQ6Zokl451Q7L", 1], [5, 95, 58], [56.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnValue", 7, [[3, -354, [94], 95], [2, 1.1, 3, -355, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "1000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "d36DLZwWJLBbEOWDAjjtGw", 1], [5, 95, 58], [169.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnValue", 7, [[3, -356, [96], 97], [2, 1.1, 3, -357, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "10000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "b5pUIbFQlDgKOjH4A4xEBJ", 1], [5, 95, 58], [282.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnValue", 7, [[3, -358, [98], 99], [2, 1.1, 3, -359, [[6, "faac23uBfBBJ4eRyib6vBGW", "betValueClicked", "50000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "dakYzp9blBErbuGOp461U2", 1], [5, 95, 58], [395.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnValue", 6, [[7, 0, -360, [100]], [2, 1.1, 3, -361, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "1", 2]], [4, 4294967295], [4, 4294967295]]], [0, "eeiBwe0RhPUrfHJoIuKOXp", 1], [5, 78, 47], [-259.5, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -362, [101]], [2, 1.1, 3, -363, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "2", 2]], [4, 4294967295], [4, 4294967295]]], [0, "9cudEVK5pNY5k7Ymr8cKxe", 1], [5, 78, 47], [-156.6, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -364, [102]], [2, 1.1, 3, -365, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "3", 2]], [4, 4294967295], [4, 4294967295]]], [0, "60piKM4pJAkZVJRczHb46G", 1], [5, 78, 47], [-53.699999999999996, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -366, [103]], [2, 1.1, 3, -367, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "4", 2]], [4, 4294967295], [4, 4294967295]]], [0, "78IzivSFVDdatAgURbUzpj", 1], [5, 78, 47], [49.2, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -368, [104]], [2, 1.1, 3, -369, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "5", 2]], [4, 4294967295], [4, 4294967295]]], [0, "41SCJX7+9Oy7lgsNsyzJ8q", 1], [5, 78, 47], [152.1, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue000", 6, [[7, 0, -370, [105]], [2, 1.1, 3, -371, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "30FsS6HuxI142OfsKY2zi6", 1], [5, 78, 47], [255, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -372, [106]], [2, 1.1, 3, -373, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "6", 2]], [4, 4294967295], [4, 4294967295]]], [0, "4avwFYr4hJVYYWuaxGnmFa", 1], [5, 78, 47], [-259.5, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -374, [107]], [2, 1.1, 3, -375, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "7", 2]], [4, 4294967295], [4, 4294967295]]], [0, "30Q5t8+iNBZ68WnUOjdyuJ", 1], [5, 78, 47], [-156.6, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -376, [108]], [2, 1.1, 3, -377, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "8", 2]], [4, 4294967295], [4, 4294967295]]], [0, "6fXg09XO5PIb/m6i3MYk4e", 1], [5, 78, 47], [-53.699999999999996, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -378, [109]], [2, 1.1, 3, -379, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "9", 2]], [4, 4294967295], [4, 4294967295]]], [0, "a88b8I7FdN7J37O8MtoVrB", 1], [5, 78, 47], [49.2, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[7, 0, -380, [110]], [2, 1.1, 3, -381, [[6, "faac23uBfBBJ4eRyib6vBGW", "addValueClicked", "0", 2]], [4, 4294967295], [4, 4294967295]]], [0, "a2trlw+IBNlK1tiwFhfr21", 1], [5, 78, 47], [152.1, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnDelete", 6, [[7, 0, -382, [111]], [2, 1.1, 3, -383, [[5, "faac23uBfBBJ4eRyib6vBGW", "deleteClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "90xqiS7H5C6o43XYGl93sP", 1], [5, 78, 47], [255, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnOther", false, 17, [[45, false, 0, -384, [112]], [100, false, 1.1, 3, -385, [[5, "faac23uBfBBJ4eRyib6vBGW", "otherClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "4dy4pAjzBHoLEKyn8rWyPs", 1], [5, 150, 60], [-240, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnConfirm", 17, [[3, -386, [120], 121], [2, 1.1, 3, -387, [[5, "faac23uBfBBJ4eRyib6vBGW", "confirmClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "ecOle4eUxO363N/tVHUaPN", 1], [5, 347, 72], [4.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnCancel", 17, [[3, -388, [122], 123], [2, 1.1, 3, -389, [[5, "faac23uBfBBJ4eRyib6vBGW", "cancelClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "93Itmh0PNAu7XVEy69Ofev", 1], [5, 247, 60], [256.5, 5, 0, 0, 0, 0, 1, 1, 1, 0]], [52, 23, 0, false, false, 1, 1, [131]], [52, 23, 0, false, false, 1, 1, [132]], [9, "sprite", 18, [-391], [[3, -390, [160], 161]], [0, "5cs0mruTpLVqyl9sGCh+MJ", 1], [5, 74, 28], [-150.922, 140.349, 0, 0, 0, 0, 1, -1, 1, 1]], [9, "sprite", 18, [-393], [[3, -392, [163], 164]], [0, "cb7hSH+PRPkZBNPoox6rEh", 1], [5, 74, 29], [154.316, 143.589, 0, 0, 0, 0, 1, -1, 1, 1]], [1, "btnCopyHash", 19, [[3, -394, [166], 167], [27, 1.1, 3, -395, [[5, "31233FAsMVA85fIiyXwP3IM", "copyHashClicked", 1]], 168]], [0, "5brkXEfD9IwaVcsmij1aiy", 1], [5, 67, 38], [268, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "lblResult", 20, [[-396, [17, true, -397, [173], 172]], 1, 4], [0, "55+o7nm0JEmbUIXlW2iqQ1", 1], [4, 4290176247], [5, 315.02, 37.8], [23, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCopyResult", 20, [[3, -398, [174], 175], [27, 1.1, 3, -399, [[5, "31233FAsMVA85fIiyXwP3IM", "copyResultClicked", 1]], 176]], [0, "c9pxIFmAJPIKDUuLUrnvfo", 1], [5, 67, 38], [268, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "Nodekeymd5", false, 19, [-400], [0, "adqbReAr1DAo5iv44KmU3x", 1]], [28, "default", 1, 0, false, 1, [181]], [28, "default", 2, 0, false, 2, [182]], [28, "default", 3, 0, false, 3, [183]], [85], [63, "lbBigTimer", 3, [[-401, [50, -402]], 1, 4], [0, "e8cwbTAJ9HCISqGxNSGkos", 1], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "lbTaiWin", 0, 21, [[-403, [17, true, -404, [202], 201]], 1, 4], [0, "be5kRNiA9Pp4rj60rKQK8D", 1], [0, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "lbXiuWin", 0, 21, [[-405, [17, true, -406, [205], 204]], 1, 4], [0, "64equav59L8q+SS/YxUzG1", 1], [0, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "light_off copy", false, 150, 1, [-407], [0, "fd8UAxPlBHWbpF4M8BG7Db", 1], [5, 4000, 4000], [-1.1368683772161603e-13, 3.410605131648481e-13, 0, 0, 0, 0, 1, 1, 1, 1]], [46, 0, 99], [10, "taixiuMD5", false, 31, [[13, "default", "animation", 0, "animation", -408, [4], 5]], [0, "d46pf+HFhPTKz+lOdS4A70", 1], [5, 368.11, 176.4], [-61.298, 55.734, 0, 0, 0, 0, 1, 0.81818, 0.81818, 0.81818]], [10, "dragon", false, 31, [[13, "default", "animation", 0, "animation", -409, [6], 7]], [0, "4czg8KK55NwbLEOlxk7kDK", 1], [5, 579, 158.24], [0, 0, 0, 0, 0, 0, 1, 0.63636, 0.63636, 0.63636]], [10, "title", false, 3, [[3, -410, [10], 11]], [0, "e4fmf/ZZBHa4VYrPk+60dJ", 1], [5, 378, 140], [-12.897, 237.019, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "rongmd5", 3, [[13, "default", "animation", 0, "animation", -411, [12], 13]], [0, "78IDF5ZtJGvKzQ7PjYGlmf", 1], [5, 1205.22, 1149.55], [68.874, 188.146, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [10, "Jackpot", false, 10, [[51, "JP idle", 0, "JP idle", -412, [14], 15]], [0, "84jrNfhYlB8aXqkCfSTB3W", 1], [5, 1808, 1730.99], [0, -210, 0, 0, 0, 0, 1, 0.5, 0.4, 1]], [10, "lblJackpot", false, 10, [[113, "1.500.000", 10, false, 2, 1, 1, -413, [16], 17]], [0, "eeethtnIhAw57mxffMyz13", 1], [5, 136.94, 12.5], [0, 172, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnJackpotHis", false, 10, [[101, 1.1, -414, [[5, "3a9ccPvVxFB/4d66plykjoi", "jackpot<PERSON><PERSON><PERSON>Clicked", 5]]]], [0, "68S7CcEmFE85RMdeHfQk2E", 1], [5, 220, 50], [0, 184, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "Gift", false, 32, [[3, -415, [20], 21]], [0, "ffmijmlq9JBZS3PfDoVlUs", 1], [5, 49, 49], [35, 20, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [47, false, 49, [26]], [47, false, 51, [29]], [16, "dot", false, 4, [-416], [0, "b7dQZYwm9HYoqpwFp86Xk1", 1], [5, 30, 30], [-337.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 111, [34]], [4, "dot", 4, [-417], [0, "3bvI7x+YRJH5DcDv/3AVs7", 1], [5, 30, 30], [-339.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 113, [35]], [4, "dot", 4, [-418], [0, "28hSBOqF5MhqqFe8gDY3vM", 1], [5, 30, 30], [-291, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 115, [36]], [4, "dot", 4, [-419], [0, "93vmqdFt9FEIuCMd9Sr0s/", 1], [5, 30, 30], [-242.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 117, [37]], [4, "dot", 4, [-420], [0, "b8KABM67hD45uhCjuGDPP8", 1], [5, 30, 30], [-194, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 119, [38]], [4, "dot", 4, [-421], [0, "9b3eXFoUlAv6y1/FPJSoJt", 1], [5, 30, 30], [-145.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 121, [39]], [4, "dot", 4, [-422], [0, "ddu25zBJlAdYqgKvASDRqL", 1], [5, 30, 30], [-97, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 123, [40]], [4, "dot", 4, [-423], [0, "3cYkRedvNC4JNOfJSey88c", 1], [5, 30, 30], [-48.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 125, [41]], [4, "dot", 4, [-424], [0, "c3dJ6j8y1DWKadY+vEf9U0", 1], [5, 30, 30], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 127, [42]], [4, "dot", 4, [-425], [0, "bf2IH0oI5MAYHmOgxtSF8l", 1], [5, 30, 30], [48.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 129, [43]], [4, "dot", 4, [-426], [0, "98EfLMCU1ACauJTPPOE8Hf", 1], [5, 30, 30], [97, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 131, [44]], [4, "dot", 4, [-427], [0, "e7ISrWTwtBnoXTIDjhGfv0", 1], [5, 30, 30], [145.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 133, [45]], [4, "dot", 4, [-428], [0, "60lAZWl+RKbr3nA8v+0KV/", 1], [5, 30, 30], [194, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 135, [46]], [4, "dot", 4, [-429], [0, "20U2Ra1ElP5baMw/yCvaWP", 1], [5, 30, 30], [242.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 137, [47]], [4, "dot", 4, [-430], [0, "7cxq3f2iJBBb1CK0TJnxD0", 1], [5, 30, 30], [291, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, 139, [48]], [8, 0, false, 54, [52]], [10, "bt_big", false, 5, [[46, 0, -431]], [0, "d3ysmLEMVLOq3lx4aKH0dJ", 1], [5, 75, 75], [300, 195, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "offset-chat", 3, [8], [0, "d1Thc7nc1OL6ve2EbJVZ1G", 1], [707, 5, 0, 0, 0, 0, 1, 1.2, 1.35, 1]], [1, "boder", 22, [[3, -432, [55], 56]], [0, "82g7MaEBdI24AYTaSIv8bk", 1], [5, 404, 494], [0, 24, 0, 0, 0, 0, 1, 0.91, 0.68, 1]], [1, "sprite", 33, [[3, -433, [62], 63]], [0, "f5QxZPRJdC/JlXuLIQfFp7", 1], [5, 93, 53], [4, 5.5, 0, 0, 0, 0, 1, 1, 1, 1]], [102, 1.1, false, 3, 33, [[5, "a00000pMpdE2aX7FTfFS08F", "sendChatClicked", 8]], [4, 4294967295], [4, 4294967295], 33], [12, "BACKGROUND_SPRITE", 23, [-434], [0, "2dsk/T0mhFTKNbhUUz6/AO", 1], [5, 220, 47.5]], [95, 1, 0, 147, [64]], [74, "TEXT_LABEL", false, 23, [-435], [0, "e01KBllTZHTYQdkrqZ9I5U", 1], [5, 197.3, 47.5], [0, 0, 1], [-97.65, 23.75, 0, 0, 0, 0, 1, 1, 1, 1]], [114, 18, 42, false, false, 1, 1, 149], [21, "PLACEHOLDER_LABEL", 124.185, 23, [-436], [0, "64paPc+ZhOdqychdYt0s3x", 1], [4, 4290493371], [5, 197.3, 47.5], [0, 0, 1], [-97.65, 23.75, 0, 0, 0, 0, 1, 1.104, 1, 1]], [115, "<PERSON><PERSON><PERSON><PERSON> nội dung chat...", 18, 47.5, false, false, 1, 1, 151, [65]], [129, 2, 250, 6, 23, [[108, "a00000pMpdE2aX7FTfFS08F", "editingReturn"]], 150, 152, 148], [30, "temp", 24, [14], [0, "85WIWEROlOp7NmX1iBHQRc", 1]], [22, "rtChat", 14, [-437], [0, "10qEssX8VOCLfN0JCbuHk5", 1], [4, 4292072403], [5, 320, 31.5], [0, 0, 0.5], [-160, 16.5, 0, 0, 0, 0, 1, 1, 1, 1]], [130, false, "", 20, 320, 25, false, 155], [22, "lbNickName", 15, [-438], [0, "0c+S06bqNPzZcyGrLqJOtU", 1], [4, 4278224383], [5, 0, 20], [0, 0, 0.5], [-157.5, 0, 0, 0, 0, 0, 1, 0.997, 1, 1]], [116, 20.1, 20, false, false, 1, 157, [66]], [22, "lbMessage", 15, [-439], [0, "5aW9StTIxP6ZkyDmBzNdTd", 1], [4, 4294440951], [5, 226, 23], [0, 0, 0.5], [-152.5, 0, 0, 0, 0, 0, 1, 0.997, 1, 1]], [117, 20.1, 20, false, false, 1, 1, 159, [67]], [16, "V10", false, 15, [-440], [0, "e4EP7d+rVDFpGGbOP0uj0Y", 1], [5, 42, 28], [-54.61999999999999, 1, 0, 0, 0, 0, 1, 0.9000000000000001, 0.9, 0.9]], [96, false, 161], [131, false, 24, 58], [132, 15, 400, 24, 14, 163], [118, 1, 1, [69]], [16, "New Label", false, 3, [165], [0, "easVY6f8JKRK2lXQQ4ltjU", 1], [5, 97.87, 50.4], [254.2, 93.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "rectangle5", 10, [[3, -441, [74], 75]], [0, "41tucPkG5EcILC9WU2SHtE", 1], [5, 326, 39], [-233, 17, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "rectangle5", 10, [[3, -442, [76], 77]], [0, "64FoW3r/lG+JRBjVGcHMDK", 1], [5, 326, 39], [233, 17, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "time_bg", false, 3, [[3, -443, [80], 81]], [0, "239dvsbx5ODZrSlwPIiUVI", 1], [5, 252, 252], [0, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "audioChonSo", 2, [-444], [0, "1cUvVpNzBO+aOpgd7T9Yd2", 1]], [44, 170], [12, "BACKGROUND_SPRITE", 26, [-445], [0, "15iWADZV5NcL5xtXczQumv", 1], [5, 160, 40]], [48, 1, 0, 172], [41, "TEXT_LABEL", false, 0, 26, [-446], [0, "83BdZnRe1EnpJu4zMZqsnH", 1], [4, 16777215], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [53, 20, false, 1, 1, 174], [21, "PLACEHOLDER_LABEL", 0, 26, [-447], [0, "baP0ryPclNartQqiXPDp7z", 1], [4, 12303291], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [119, "Enter text here...", 20, false, 1, 1, 176, [124]], [56, 1, 9, 5, 26, [[5, "347b1xdyqpC24YIVRrVdPlw", "onTextXiuChange", 2]], [[5, "347b1xdyqpC24YIVRrVdPlw", "confirmClicked", 2]], 175, 177, 173], [12, "BACKGROUND_SPRITE", 27, [-448], [0, "8bnrzkqqdJGJY+3RAcbpi4", 1], [5, 174, 64]], [48, 1, 0, 179], [41, "TEXT_LABEL", false, 0, 27, [-449], [0, "bfiUqwD21DwaNDuCveWnhb", 1], [4, 16777215], [5, 172, 64], [0, 0, 1], [-85, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [53, 20, false, 1, 1, 181], [21, "PLACEHOLDER_LABEL", 0, 27, [-450], [0, "11F9nilixI0L6T1H0Ksgum", 1], [4, 12303291], [5, 172, 64], [0, 0, 1], [-85, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [120, "Enter text here...", 20, 64, false, 1, 1, 183, [125]], [56, 1, 9, 5, 27, [[5, "347b1xdyqpC24YIVRrVdPlw", "onTextTaiChange", 2]], [[5, "347b1xdyqpC24YIVRrVdPlw", "confirmClicked", 2]], 182, 184, 180], [4, "lbInputBetXiu", 37, [-451], [0, "73MwQMGcRITqO7Cc8xv2/b", 1], [5, 14.63, 30], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 26, 30, false, false, 1, 1, 186, [126]], [4, "lbBetTai", 11, [84], [0, "00VLJ6PTRN563H1yM8HKn/", 1], [5, 0, 28.98], [-244, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBetXiu", 11, [85], [0, "75ku7w8N5KEJ44oQg/IIgz", 1], [5, 0, 28.98], [248, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbInputBetTai", 36, [-452], [0, "66nbeSICxKmaqvq3gvoLaY", 1], [5, 14.63, 30], [-2.8, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 26, 30, false, false, 1, 1, 190, [133]], [4, "txtNotify", 39, [38], [0, "462X9XnjVPTaAqpSTlv2I/", 1], [5, 164.38, 0], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_soicau", 3, [[3, -453, [155], 156]], [0, "d8gM0bCopGt7Bv0Vcelw1E", 1], [5, 515, 29], [-0.668, -170.25, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbTotalBetTai", 18, [-454], [0, "aafQ7FPg5C0KnRPaCPXtbL", 1], [5, 20.63, 0], [-245, 11.600000000000001, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 12, 0, false, false, 1, 1, 194, [157]], [4, "lbTotalBetXiu", 18, [-455], [0, "92n0z7S3ZEsb4k749KcWSf", 1], [5, 20.63, 0], [245, 11.6, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 12, 0, false, false, 1, 1, 196, [158]], [4, "lbUserTai", 86, [-456], [0, "195M+dO7NDmpurJft960TY", 1], [5, 10.13, 0], [0, 3.832, 0, 0, 0, 0, 1, -1, 1, -1]], [54, "0", 18, 0, false, 1, 1, 198, [159]], [4, "lbUserXiu", 87, [-457], [0, "e9I4OMutVLI7NTLLjcaD8j", 1], [5, 10.13, 0], [0, 3.798, 0, 0, 0, 0, 1, -1, 1, -1]], [54, "0", 18, 0, false, 1, 1, 200, [162]], [4, "lbSessionID", 18, [-458], [0, "48ttkS3jJLJZwcwl7CO7E/", 1], [5, 150, 0], [0, 200, 0, 0, 0, 0, 1, 1, 1, 1]], [121, "00000", 22, 0, false, false, 1, 1, 202, [165]], [1, "Result", 20, [[3, -459, [169], 170]], [0, "427aT+SUFF2p59tKg8gR1S", 1], [5, 112, 41], [-246.654, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [122, "gfhthtrhtrhtrutryrtyrtrtytytytytytyttyrtyrryrt", 18, 30, 1, 1, 89, [171]], [75, "lblMd5String", false, 91, [-460], [0, "ccvxSW3r9GOpnVYn15MmL/", 1], [4, 4290176247], [5, 0, 37.8], [24, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [123, 22.5, 30, 1, 1, 206, [177]], [1, "btnShowRule", 19, [[103, 0.9, 3, -461, [[5, "5e6fdzwzFBBOpmhY4UBi5k7", "showRuleClick", 1]]]], [0, "4eHYpo++ZIWY0f77b5BJvd", 1], [5, 539, 39], [-26, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_timew", false, 9, [[7, 0, -462, [180]]], [0, "226d3UbMRMy46bsEli4ix9", 1], [5, 48, 48], [116, 99, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Dice1v", 12, [92], [0, "4b3vmC+dZNo5d4/guONiEm", 1], [5, 322.37, 372.36], [-54.973, -43.302, 0, 0, 0, 0, 1, 0.32, 0.32, 0.32]], [4, "Dice1v", 12, [93], [0, "07WNwhNBdOFbSTDVYjUKX9", 1], [5, 322.37, 372.36], [52.687, -45.469, 0, 0, 0, 0, 1, 0.32, 0.32, 0.32]], [4, "Dice1v", 12, [94], [0, "7a7vLLd49LiK1csQO+XKkS", 1], [5, 322.37, 372.36], [0, -109.497, 0, 0, 0, 0, 1, 0.32, 0.32, 0.32]], [16, "effect", false, 12, [-463], [0, "205sLSDjRBdYOxh1/sN5B5", 1], [5, 1347.04, 1545.94], [-5, -83, 0, 0, 0, 0, 1, 0.26, 0.3, 0.26]], [110, "default", "effect", 0, false, "effect", 213, [184]], [12, "New Sprite(Splash)", 44, [-464], [0, "baegfsZY9LuKrR7AoqadrT", 1], [5, 100, 100]], [7, 0, 215, [185]], [12, "New Sprite(Splash)", 44, [-465], [0, "eegeUpLKREp7azsT/be8n7", 1], [5, 100, 100]], [7, 0, 217, [186]], [12, "New Sprite(Splash)", 44, [-466], [0, "dajUOM0QtFl7Q0DzXuujXF", 1], [5, 100, 100]], [7, 0, 219, [187]], [105, null, 1], [23, "rollDice", 9, [-467], [0, "edFQYe3spBboDvXVA3lhWx", 1]], [44, 222], [23, "winSound", 9, [95], [0, "77znS6BGBImKz+a5Ni5GJP", 1]], [124, 36, 0, false, 1, 96, [193]], [14, "result-label", false, 3, [-468], [0, "d8ZAAuaadHK4V5IEuqFD9n", 1]], [4, "lbTotalDice", 226, [-469], [0, "741Pr92TBKt7OXr1HLV33b", 1], [5, 0, 50.4], [115.8, 99.4, 0, 0, 0, 0, 1, 1, 1, 1]], [125, 28, false, 1, 1, 227, [194]], [76, "lbTimer", 29, [-470], [0, "b0QOgdpgRCTYViVi21Yemz", 1], [4, 4294769916], [5, 34.38, 0], [-2.5, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [126, 12.5, 0, false, 1, 1, 229, [195]], [10, "Popup", false, 21, [[3, -471, [198], 199]], [0, "1aUyKUqMZD9bQgUnZtKN3L", 1], [5, 860, 50], [-1.636, -99.815, 0, 0, 0, 0, 1, 1, 1, 1]], [55, 26, 0, false, -1, 1, 1, 97, [200]], [55, 26, 0, false, -1, 1, 1, 98, [203]], [72, "Win", 21, [[43, -472, 206]], [0, "cfcXXFPLVOQJ2bQ+Sw/rEk", 1]], [14, "jackpot", false, 3, [-473], [0, "ddPJxSlPNEypnLPV4/P39q", 1]], [1, "dragon", 235, [[51, "sanhrongdonghoa", 0, "sanhrongdonghoa", -474, [207], 208]], [0, "1bhuBvbohCao7AjqOOppEb", 1], [5, 1281.86, 1171.98], [0, -359, 0, 0, 0, 0, 1, 0.35, 0.5, 1]], [4, "txt result", 46, [-475], [0, "e8iHLdFvNGR7jw3ApYwWPy", 1], [5, 164.38, 0], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [127, "<PERSON>n mời nặn", 18, 0, false, -1, 1, 1, 237, [209]], [24, 46, [212, 213]]], 0, [0, 19, 1, 0, 20, 214, 0, 11, 12, 0, -1, 92, 0, -2, 93, 0, -3, 94, 0, 12, 95, 0, 21, 223, 0, 13, 45, 0, 14, 41, 0, 15, 40, 0, 8, 38, 0, 22, 43, 0, 23, 42, 0, 24, 91, 0, 16, 20, 0, 17, 13, 0, 25, 3, 0, 26, 201, 0, 27, 199, 0, 28, 197, 0, 29, 195, 0, 30, 230, 0, 31, 225, 0, 32, 203, 0, 18, 29, 0, 0, 1, 0, 0, 1, 0, 33, 85, 0, 34, 84, 0, 0, 1, 0, 35, 238, 0, 36, 239, 0, 37, 85, 0, 38, 84, 0, 14, 41, 0, 15, 40, 0, 12, 95, 0, -1, 43, 0, -1, 42, 0, 8, 38, 0, 39, 228, 0, 40, 220, 0, 41, 218, 0, 42, 216, 0, 11, 12, 0, -1, 92, 0, -2, 93, 0, -3, 94, 0, 18, 29, 0, 16, 20, 0, 43, 205, 0, 44, 207, 0, 13, 45, 0, 17, 13, 0, 0, 1, 0, -5, 221, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 30, 0, -2, 99, 0, -3, 3, 0, 45, 178, 0, 46, 185, 0, 47, 171, 0, 8, 38, 0, 48, 187, 0, 49, 191, 0, 50, 28, 0, 51, 25, 0, 52, 6, 0, 53, 7, 0, 54, 16, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 170, 0, -2, 16, 0, -1, 31, 0, -2, 103, 0, -3, 104, 0, -4, 10, 0, -5, 169, 0, -6, 143, 0, -8, 40, 0, -9, 166, 0, -10, 41, 0, -11, 42, 0, -12, 43, 0, -13, 193, 0, -14, 4, 0, -15, 5, 0, -16, 18, 0, -17, 11, 0, -18, 19, 0, -19, 9, 0, -20, 96, 0, -21, 226, 0, -22, 29, 0, -23, 21, 0, -24, 235, 0, -25, 39, 0, -26, 46, 0, 0, 4, 0, -1, 141, 0, -2, 140, 0, -3, 138, 0, -4, 136, 0, -5, 134, 0, -6, 132, 0, -7, 130, 0, -8, 128, 0, -9, 126, 0, -10, 124, 0, -11, 122, 0, -12, 120, 0, -13, 118, 0, -14, 116, 0, -15, 114, 0, -16, 112, 0, 0, 4, 0, -1, 111, 0, -2, 113, 0, -3, 115, 0, -4, 117, 0, -5, 119, 0, -6, 121, 0, -7, 123, 0, -8, 125, 0, -9, 127, 0, -10, 129, 0, -11, 131, 0, -12, 133, 0, -13, 135, 0, -14, 137, 0, -15, 139, 0, -16, 54, 0, 55, 110, 0, 56, 109, 0, 57, 8, 0, 58, 100, 0, 0, 5, 0, -1, 47, 0, -2, 32, 0, -3, 48, 0, -4, 49, 0, -5, 50, 0, -6, 51, 0, -7, 52, 0, -8, 53, 0, -9, 142, 0, 0, 6, 0, -1, 69, 0, -2, 70, 0, -3, 71, 0, -4, 72, 0, -5, 73, 0, -6, 74, 0, -7, 75, 0, -8, 76, 0, -9, 77, 0, -10, 78, 0, -11, 79, 0, -12, 80, 0, 0, 7, 0, -1, 59, 0, -2, 60, 0, -3, 61, 0, -4, 62, 0, -5, 63, 0, -6, 64, 0, -7, 65, 0, -8, 66, 0, -9, 67, 0, -10, 68, 0, 8, 165, 0, 59, 146, 0, 60, 153, 0, 61, 164, 0, 0, 8, 0, -1, 22, 0, -2, 33, 0, -3, 23, 0, -4, 24, 0, -1, 209, 0, -2, 12, 0, -3, 44, 0, -4, 45, 0, -5, 13, 0, -6, 222, 0, -7, 224, 0, 0, 10, 0, -1, 105, 0, -2, 106, 0, -3, 107, 0, -4, 167, 0, -5, 168, 0, -1, 26, 0, -2, 27, 0, -3, 36, 0, -4, 37, 0, -5, 188, 0, -6, 189, 0, -1, 210, 0, -2, 211, 0, -3, 212, 0, -4, 213, 0, 0, 13, 0, 9, 13, 0, 0, 13, 0, 62, 221, 0, 0, 13, 0, 0, 14, 0, 63, 156, 0, 64, 162, 0, 65, 160, 0, 66, 158, 0, 67, 15, 0, 0, 14, 0, -1, 155, 0, -2, 15, 0, 0, 15, 0, -1, 157, 0, -2, 159, 0, -3, 161, 0, 0, 16, 0, -3, 17, 0, 0, 17, 0, -1, 81, 0, -2, 34, 0, -3, 82, 0, -4, 83, 0, -1, 194, 0, -2, 196, 0, -3, 86, 0, -4, 87, 0, -5, 202, 0, 0, 19, 0, -1, 88, 0, -2, 20, 0, -3, 91, 0, -4, 208, 0, -1, 204, 0, -2, 89, 0, -3, 90, 0, 68, 233, 0, 69, 232, 0, 0, 21, 0, -1, 231, 0, -2, 97, 0, -3, 98, 0, -4, 234, 0, 0, 22, 0, 0, 22, 0, -1, 144, 0, -2, 56, 0, -1, 153, 0, -1, 147, 0, -2, 149, 0, -3, 151, 0, -1, 163, 0, -2, 164, 0, -1, 154, 0, -2, 57, 0, 0, 25, 0, 9, 25, 0, 0, 25, 0, -1, 178, 0, -1, 172, 0, -2, 174, 0, -3, 176, 0, -1, 185, 0, -1, 179, 0, -2, 181, 0, -3, 183, 0, 0, 28, 0, 9, 28, 0, 0, 28, 0, 0, 29, 0, -1, 229, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, -1, 101, 0, -2, 102, 0, 0, 32, 0, 0, 32, 0, -1, 108, 0, -1, 146, 0, -1, 145, 0, 0, 34, 0, 9, 35, 0, 0, 34, 0, -1, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, -1, 190, 0, 0, 37, 0, -1, 186, 0, 0, 39, 0, 0, 39, 0, -1, 192, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, -1, 215, 0, -2, 217, 0, -3, 219, 0, 0, 45, 0, 0, 46, 0, -2, 239, 0, -1, 237, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, -1, 109, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, -1, 110, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, -1, 141, 0, -1, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, -1, 58, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 86, 0, -1, 198, 0, 0, 87, 0, -1, 200, 0, 0, 88, 0, 0, 88, 0, -1, 205, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, -1, 206, 0, -1, 225, 0, 0, 96, 0, -1, 232, 0, 0, 97, 0, -1, 233, 0, 0, 98, 0, -1, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, -1, 112, 0, -1, 114, 0, -1, 116, 0, -1, 118, 0, -1, 120, 0, -1, 122, 0, -1, 124, 0, -1, 126, 0, -1, 128, 0, -1, 130, 0, -1, 132, 0, -1, 134, 0, -1, 136, 0, -1, 138, 0, -1, 140, 0, 0, 142, 0, 0, 144, 0, 0, 145, 0, -1, 148, 0, -1, 150, 0, -1, 152, 0, -1, 156, 0, -1, 158, 0, -1, 160, 0, -1, 162, 0, 0, 167, 0, 0, 168, 0, 0, 169, 0, -1, 171, 0, -1, 173, 0, -1, 175, 0, -1, 177, 0, -1, 180, 0, -1, 182, 0, -1, 184, 0, -1, 187, 0, -1, 191, 0, 0, 193, 0, -1, 195, 0, -1, 197, 0, -1, 199, 0, -1, 201, 0, -1, 203, 0, 0, 204, 0, -1, 207, 0, 0, 208, 0, 0, 209, 0, -1, 214, 0, -1, 216, 0, -1, 218, 0, -1, 220, 0, -1, 223, 0, -1, 227, 0, -1, 228, 0, -1, 230, 0, 0, 231, 0, 0, 234, 0, -1, 236, 0, 0, 236, 0, -1, 238, 0, 70, 1, 2, 5, 3, 6, 5, 16, 7, 5, 16, 8, 5, 143, 14, 5, 154, 25, 5, 36, 28, 5, 37, 38, 0, 192, 84, 0, 188, 85, 0, 189, 92, 0, 210, 93, 0, 211, 94, 0, 212, 95, 0, 224, 165, 0, 166, 475], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 100, 109, 110, 112, 114, 116, 118, 120, 122, 124, 126, 128, 130, 132, 134, 136, 138, 140, 141, 148, 152, 156, 158, 160, 162, 171, 187, 84, 85, 191, 38, 195, 197, 199, 201, 203, 92, 93, 94, 214, 223, 95, 225, 228, 230, 232, 233, 238, 239], [-1, 1, -1, -2, -1, 4, -1, 4, -1, 4, -1, 1, -1, 4, -1, 4, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, -1, -1, -1, -2, -1, 1, -1, 1, 10, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -2, -1, -2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, 71, 72, 73, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 6, -1, -2, -1, -1, -2, 7, -1, 1, -1, 1, -1, 4, -1, 4, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, 10, -1, 1, -1, 6, -1, -1, 1, 10, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, 10, -1, -1, -1, -1, 1, -1, 1, -1, 6, -1, -1, 6, -1, 7, -1, 4, -1, -1, 1, -1, -2, 6, -1, -2, 74, 75, 76, 77, 78, 79, 80, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 81, 2, 2, 1, 7, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 4, 4, 4, 4, 7, 7, 2, 2, 2, 2, 2, 2, 6], [0, 11, 39, 40, 2, 41, 2, 42, 2, 43, 0, 44, 2, 45, 2, 46, 0, 3, 0, 47, 0, 48, 0, 49, 0, 50, 0, 0, 51, 0, 0, 52, 0, 53, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 12, 0, 1, 54, 0, 55, 0, 56, 0, 0, 57, 0, 58, 0, 0, 0, 0, 0, 0, 59, 13, 7, 7, 0, 14, 0, 14, 0, 60, 0, 61, 0, 62, 0, 0, 63, 0, 64, 0, 65, 0, 0, 66, 0, 67, 0, 68, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 0, 15, 70, 71, 72, 0, 73, 0, 74, 0, 0, 0, 0, 75, 0, 16, 0, 0, 0, 0, 16, 0, 76, 0, 8, 5, 5, 17, 0, 77, 78, 79, 0, 80, 0, 81, 2, 18, 2, 18, 0, 82, 0, 0, 0, 0, 83, 0, 0, 84, 0, 0, 19, 0, 0, 85, 0, 20, 20, 0, 19, 0, 0, 0, 86, 0, 2, 2, 2, 2, 0, 0, 0, 0, 21, 0, 21, 0, 0, 0, 0, 0, 87, 0, 8, 0, 6, 6, 0, 6, 6, 88, 2, 89, 0, 0, 8, 5, 17, 22, 22, 90, 91, 92, 93, 94, 95, 96, 97, 11, 13, 7, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 98, 9, 99, 9, 9, 100, 101, 4, 23, 23, 4, 24, 3, 3, 4, 4, 4, 10, 10, 10, 102, 103, 104, 3, 105, 3, 25, 25, 24, 5]], [[{"name": "tXU", "rect": [0, 0, 187, 65], "offset": [0, 0], "originalSize": [187, 65], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [106]], [[{"name": "rectangle5", "rect": [0, 0, 326, 39], "offset": [0, 0], "originalSize": [326, 39], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [107]], [[[20, "taixiumd5", "\ntaixiumd5.png\nsize: 702,162\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nXX2\n  rotate: true\n  xy: 651, 108\n  size: 52, 49\n  orig: 53, 55\n  offset: 0, 4\n  index: -1\nXX3\n  rotate: true\n  xy: 651, 55\n  size: 51, 49\n  orig: 51, 51\n  offset: 0, 0\n  index: -1\nbg_logo\n  rotate: false\n  xy: 2, 2\n  size: 647, 158\n  orig: 647, 161\n  offset: 0, 0\n  index: -1\n", ["taixiumd5.png"], {"skeleton": {"hash": "wdmXHB5JIRpbx9XEc+dyCevvb8M", "spine": "3.8.75", "x": -313.77, "y": -2.28, "width": 647, "height": 157.37, "images": "../UI GAME/out/xoc.vin/spines/88_logo/PNG/", "audio": ""}, "bones": [{"name": "root"}, {"name": "bg_logo", "parent": "root", "x": 4.71, "y": 32.56}, {"name": "bg_logo3", "parent": "bg_logo", "length": 78.39, "rotation": -178.64, "x": -34.52, "y": 2.8}, {"name": "bg_logo2", "parent": "bg_logo3", "length": 76.51, "rotation": -1.36, "x": 82.1, "y": -1.02}, {"name": "bg_logo5", "parent": "bg_logo2", "length": 66.56, "rotation": -5.63, "x": 77.44, "y": -2.8}, {"name": "bg_logo4", "parent": "bg_logo5", "length": 52.28, "rotation": 7.68, "x": 65.73, "y": -1.02}, {"name": "bg_logo7", "parent": "bg_logo", "length": 61.81, "x": 11.78, "y": -6.07}, {"name": "bg_logo6", "parent": "bg_logo7", "length": 80.21, "rotation": 1.75, "x": 61.81, "y": -1.22}, {"name": "bg_logo9", "parent": "bg_logo6", "length": 83.88, "rotation": -0.08, "x": 79.6, "y": 0.02}, {"name": "bg_logo8", "parent": "bg_logo9", "length": 57.62, "rotation": 10.59, "x": 90.66, "y": 1.64}, {"name": "XX2", "parent": "root", "x": -201.96, "y": 38.91}, {"name": "XX3", "parent": "root", "x": 219.31, "y": 36}], "slots": [{"name": "bg_logo", "bone": "bg_logo8", "attachment": "bg_logo"}, {"name": "XX2", "bone": "XX2", "attachment": "XX2"}, {"name": "XX3", "bone": "XX3", "attachment": "XX3"}], "skins": [{"name": "default", "attachments": {"bg_logo": {"bg_logo": {"type": "mesh", "hull": 46, "width": 647, "height": 161, "uvs": [0.33604, 0.62365, 0.2766, 0.51218, 0.23499, 0.50819, 0.21616, 0.6356, 0.21814, 0.75106, 0.18644, 0.56792, 0.13492, 0.52014, 0.08043, 0.56792, 7e-05, 0.59977, 0, 0.86254, 0.01988, 0.86652, 0.05852, 0.80282, 0.10806, 0.84661, 0.15066, 0.93818, 0.21604, 0.98198, 0.28837, 0.9979, 0.31016, 0.95411, 0.31611, 0.87448, 0.35177, 0.95013, 0.39041, 1, 0.62224, 1, 0.65196, 0.93818, 0.68763, 0.84661, 0.70348, 0.88244, 0.69159, 0.94216, 0.70943, 0.98994, 0.73915, 0.98198, 0.76392, 0.92226, 0.82534, 0.93022, 0.86497, 0.86652, 0.93234, 0.85059, 0.98782, 0.87846, 1, 0.8267, 1, 0.5918, 0.93531, 0.5918, 0.8719, 0.54005, 0.79661, 0.61171, 0.78571, 0.69134, 0.77481, 0.50421, 0.69655, 0.51616, 0.70447, 0.38875, 0.63413, 0.23746, 0.46075, 0.02247, 0.29728, 0.21357, 0.31115, 0.3569, 0.31214, 0.48033, 0.0187, 0.7431, 0.05634, 0.6993, 0.12272, 0.75902, 0.20495, 0.8267, 0.27034, 0.84263, 0.30997, 0.71921, 0.39815, 0.75902, 0.49127, 0.78689, 0.58044, 0.79883, 0.66663, 0.75106, 0.74094, 0.73115, 0.79443, 0.8267, 0.85982, 0.73513, 0.94007, 0.71124, 0.49226, 0.54801, 0.47146, 0.2932, 0.39121, 0.3569, 0.58935, 0.39274], "triangles": [59, 34, 33, 36, 35, 34, 58, 36, 34, 58, 34, 59, 59, 33, 32, 30, 58, 59, 30, 59, 32, 30, 29, 58, 31, 30, 32, 38, 37, 39, 56, 39, 37, 37, 36, 58, 58, 56, 37, 57, 56, 58, 22, 56, 57, 29, 57, 58, 27, 23, 57, 28, 27, 57, 29, 28, 57, 26, 24, 27, 55, 60, 39, 55, 39, 56, 22, 55, 56, 54, 55, 22, 23, 22, 57, 21, 54, 22, 24, 23, 27, 25, 24, 26, 20, 54, 21, 53, 54, 20, 63, 61, 41, 63, 41, 40, 39, 63, 40, 60, 63, 39, 55, 54, 60, 47, 8, 7, 46, 8, 47, 5, 47, 7, 46, 47, 48, 11, 46, 48, 9, 8, 46, 9, 46, 11, 10, 9, 11, 7, 6, 5, 48, 5, 4, 48, 47, 5, 12, 11, 48, 12, 48, 49, 2, 1, 3, 3, 1, 0, 51, 3, 0, 4, 3, 51, 49, 48, 4, 50, 4, 51, 50, 51, 52, 49, 4, 50, 13, 12, 49, 16, 50, 17, 50, 13, 49, 14, 50, 16, 14, 13, 50, 15, 14, 16, 61, 43, 42, 62, 44, 43, 61, 62, 43, 45, 44, 62, 63, 62, 61, 45, 62, 60, 0, 45, 60, 52, 0, 60, 51, 0, 52, 17, 50, 52, 53, 18, 17, 53, 17, 52, 19, 18, 53, 19, 53, 20, 41, 61, 42, 60, 62, 63, 53, 52, 60, 60, 54, 53], "vertices": [3, 1, -101.07, 25.75, 0.0362, 2, 65.98, -24.53, 0.78982, 3, -15.56, -23.88, 0.17397, 2, 2, 104, -43.39, 0.00133, 3, 22.9, -41.83, 0.99867, 1, 3, 49.82, -42.47, 1, 1, 3, 62, -21.96, 1, 2, 3, 60.72, -3.37, 0.99031, 4, -16.58, -2.21, 0.00969, 2, 4, 6.72, -29.54, 0.94254, 5, -62.28, -20.38, 0.05746, 2, 4, 40.65, -33.92, 0.57569, 5, -29.25, -29.26, 0.42431, 1, 5, 6.26, -22.83, 1, 1, 5, 58.41, -19.56, 1, 1, 5, 59.96, 22.71, 1, 2, 4, 109.25, 28.88, 0.00705, 5, 47.13, 23.81, 0.99295, 2, 4, 85.38, 16.22, 0.36715, 5, 21.78, 14.46, 0.63284, 2, 3, 131.95, 12.01, 0.03093, 4, 52.79, 20.09, 0.96907, 2, 3, 104.38, 26.76, 0.47519, 4, 23.91, 32.06, 0.52481, 2, 2, 144.97, 31.3, 0.00028, 3, 62.08, 33.81, 0.99972, 2, 2, 98.25, 34.98, 0.155, 3, 15.28, 36.37, 0.845, 2, 2, 83.98, 28.26, 0.31045, 3, 1.18, 29.32, 0.68955, 2, 2, 79.83, 15.54, 0.63492, 3, -2.66, 16.5, 0.36508, 1, 2, 57.05, 28.26, 1, 3, 1, -65.89, -34.84, 0.00055, 2, 32.25, 36.88, 0.99902, 6, -77.67, -28.77, 0.00043, 1, 7, 9.67, -27.85, 1, 1, 7, 29.19, -18.49, 1, 1, 7, 52.71, -4.46, 1, 2, 7, 62.78, -10.54, 0.97112, 8, -16.8, -10.58, 0.02888, 2, 7, 54.8, -19.91, 0.96774, 8, -24.77, -19.97, 0.03226, 2, 7, 66.1, -27.95, 0.8728, 8, -13.46, -27.99, 0.1272, 2, 7, 85.36, -27.26, 0.45862, 8, 5.8, -27.27, 0.54138, 2, 7, 101.67, -18.14, 0.05764, 8, 22.1, -18.13, 0.94236, 1, 8, 61.78, -20.57, 1, 2, 8, 87.71, -11.07, 0.59324, 9, -5.23, -11.95, 0.40676, 1, 9, 37.9, -18.7, 1, 1, 9, 72.03, -30.71, 1, 1, 9, 81.5, -24.24, 1, 1, 9, 89.53, 12.71, 1, 1, 9, 48.63, 21.61, 1, 2, 8, 93.73, 41.34, 0.20877, 9, 10.32, 38.46, 0.79123, 3, 7, 124.34, 31.19, 8e-05, 8, 44.7, 31.23, 0.96005, 9, -39.74, 37.54, 0.03988, 4, 6, 178.09, 20.93, 0.00199, 7, 116.9, 18.59, 0.01633, 8, 37.28, 18.62, 0.97891, 9, -49.35, 26.51, 0.00277, 4, 1, 182.82, 44.98, 0.00031, 6, 171.04, 51.05, 0.0225, 7, 110.77, 48.92, 0.13378, 8, 31.11, 48.94, 0.84342, 4, 1, 132.18, 43.06, 0.05301, 6, 120.4, 49.13, 0.38825, 7, 60.1, 48.54, 0.42781, 8, -19.56, 48.5, 0.13092, 5, 1, 137.31, 63.57, 0.09328, 2, -173.23, -56.66, 2e-05, 6, 125.53, 69.64, 0.5669, 7, 65.85, 68.89, 0.30509, 8, -13.84, 68.85, 0.03471, 4, 1, 91.8, 87.93, 0.22251, 2, -128.31, -82.1, 0.02501, 6, 80.02, 94, 0.67977, 7, 21.1, 94.62, 0.07272, 3, 1, -20.38, 122.54, 0.4391, 2, -16.99, -119.37, 0.43105, 6, -32.16, 128.61, 0.12985, 2, 1, -126.14, 91.77, 0.17313, 2, 89.48, -91.13, 0.82687, 2, 1, -117.17, 68.7, 0.15585, 2, 81.05, -67.85, 0.84415, 3, 1, -116.53, 48.82, 0.12023, 2, 80.89, -47.96, 0.87374, 3, -0.1, -46.96, 0.00603, 2, 4, 111.96, 9.18, 0.00167, 5, 47.18, 3.93, 0.99833, 1, 5, 22.59, -2.25, 1, 2, 3, 122.46, -2.09, 0.01289, 4, 44.73, 5.12, 0.98711, 1, 3, 69.25, 8.81, 1, 2, 2, 109.31, 9.71, 0.0303, 3, 26.95, 11.37, 0.9697, 3, 1, -117.93, 10.36, 0.00126, 2, 83.21, -9.55, 0.15358, 3, 1.31, -8.5, 0.84517, 2, 1, -60.88, 3.95, 0.00257, 2, 26.33, -1.78, 0.99743, 2, 1, -0.63, -0.53, 0.9901, 2, -33.8, 4.14, 0.0099, 2, 1, 57.06, -2.46, 0.00806, 6, 45.28, 3.62, 0.99194, 4, 1, 112.83, 5.24, 0.00847, 6, 101.05, 11.31, 0.13344, 7, 39.6, 11.33, 0.85486, 8, -40.02, 11.26, 0.00323, 4, 1, 160.9, 8.44, 0.00076, 6, 149.12, 14.52, 0.0143, 7, 87.75, 13.07, 0.2153, 8, 8.13, 13.06, 0.76964, 1, 8, 42.28, -3.33, 1, 2, 8, 85, 10.18, 0.4893, 9, -4, 9.43, 0.5107, 1, 9, 47.56, 2.16, 1, 3, 1, 0.01, 37.93, 0.57982, 2, -35.36, -34.3, 0.2483, 6, -11.77, 44, 0.17188, 3, 1, -13.45, 78.95, 0.45887, 2, -22.88, -75.63, 0.39486, 6, -25.23, 85.03, 0.14627, 3, 1, -65.37, 68.7, 0.25109, 2, 29.27, -66.61, 0.73551, 6, -77.15, 74.77, 0.0134, 4, 1, 62.83, 62.93, 0.28126, 2, -98.75, -57.79, 0.04698, 6, 51.05, 69, 0.65592, 7, -8.62, 70.52, 0.01584], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 0, 90]}}, "XX2": {"XX2": {"x": -1.39, "y": 1.31, "width": 53, "height": 55}}, "XX3": {"XX3": {"x": -1.52, "y": 1.21, "width": 51, "height": 51}}}}], "animations": {"animation": {"bones": {"bg_logo9": {"rotate": [{"angle": -7.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": -14.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": -7.26, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": -14.51, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": -7.26}]}, "bg_logo8": {"rotate": [{"angle": -22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": -31.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": -22.69, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": -31.68, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": -22.69}]}, "bg_logo7": {"rotate": [{"angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -3, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": -0.29, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -3, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": -0.29}]}, "bg_logo6": {"rotate": [{"angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": -3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": -0.85, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": -0.85}]}, "bg_logo5": {"rotate": [{"angle": 11.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 1.5, "angle": 23.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2, "angle": 11.65, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 2.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "angle": 23.29, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 4, "angle": 11.65}]}, "XX3": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": 23.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 0.8333, "y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 1.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "y": 23.95, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2.8333, "y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 3.6667, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "y": 23.95}]}, "XX2": {"rotate": [{}, {"time": 1, "angle": 180}, {"time": 2}, {"time": 3, "angle": 180}, {"time": 4}], "translate": [{"y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 0.8333, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 2, "y": 76.25, "curve": 0.311, "c2": 0.25, "c3": 0.757}, {"time": 2.8333, "curve": 0.25, "c3": 0.75}, {"time": 3.8333, "y": 84.39, "curve": 0.29, "c3": 0.629, "c4": 0.38}, {"time": 4, "y": 76.25}]}, "bg_logo4": {"rotate": [{"angle": 17.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 24.91, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 17.84, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 2.6667, "curve": 0.25, "c3": 0.75}, {"time": 3.6667, "angle": 24.91, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 4, "angle": 17.84}]}, "bg_logo3": {"rotate": [{"angle": 0.56, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": 5.82, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 2, "angle": 0.56, "curve": 0.371, "c2": 0.62, "c3": 0.71}, {"time": 2.1667, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 5.82, "curve": 0.243, "c3": 0.689, "c4": 0.75}, {"time": 4, "angle": 0.56}]}, "bg_logo2": {"rotate": [{"angle": 2.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 7.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 2.3333, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": 7.96, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 4, "angle": 2.26}]}}}}}, [0]]], 0, 0, [0], [-1], [108]], [[{"name": "KETQUA_TITLE", "rect": [0, 0, 112, 41], "offset": [0, 0], "originalSize": [112, 41], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [109]], [[[20, "taixiuMD5", "\ntaixiuMD5.png\nsize: 191,208\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n3_MG5\n  rotate: true\n  xy: 2, 2\n  size: 204, 98\n  orig: 211, 103\n  offset: 3, 3\n  index: -1\n3_taixiu\n  rotate: true\n  xy: 102, 12\n  size: 194, 87\n  orig: 200, 92\n  offset: 3, 2\n  index: -1\n", ["taixiuMD5.png"], {"skeleton": {"hash": "qyNp8cQznDhdMJY7FwUVS5GDzbQ", "spine": "3.8.75", "x": -179, "y": -84.34, "width": 368.11, "height": 176.4, "images": "C:/Users/<USER>/UI GAME/out/play.titan.vin/spines/105_taixiuMD5/png", "audio": "E:/My Drive/Outsource/GiangHoang/Fullslot2/title"}, "bones": [{"name": "root"}, {"name": "3", "parent": "root"}, {"name": "3_taixiu", "parent": "3", "x": -77.39, "y": -3.85}, {"name": "3_MG5", "parent": "3", "x": 202.34, "y": 0.73, "scaleX": 0.9, "scaleY": 0.9}], "slots": [{"name": "3_taixiu", "bone": "3_taixiu", "attachment": "3_taixiu"}, {"name": "3_taixiu2", "bone": "3_taixiu", "color": "ffffff00", "attachment": "3_taixiu", "blend": "additive"}, {"name": "3_MG5", "bone": "3_MG5", "attachment": "3_MG5"}, {"name": "3_MG6", "bone": "3_MG5", "color": "ffffff00", "attachment": "3_MG5", "blend": "additive"}], "skins": [{"name": "default", "attachments": {"3_MG5": {"3_MG5": {"x": 24.98, "y": -0.81, "width": 211, "height": 103}}, "3_MG6": {"3_MG5": {"x": -1.43, "y": 2.33, "width": 211, "height": 103}}, "3_taixiu": {"3_taixiu": {"x": -1.61, "y": 3.85, "width": 200, "height": 92}}, "3_taixiu2": {"3_taixiu": {"x": -1.61, "y": 3.85, "width": 200, "height": 92}}}}], "animations": {"animation": {"slots": {"3_MG6": {"color": [{"color": "ffffff39", "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 1, "color": "ffffff73", "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.3333, "color": "ffffff73", "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 2.6667, "color": "ffffff39"}]}, "3_taixiu2": {"color": [{"color": "ffffff15", "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.1667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff73", "curve": 0.25, "c3": 0.75}, {"time": 1.5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 2.1667, "color": "ffffff73", "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 2.6667, "color": "ffffff15"}]}}, "bones": {"3_taixiu": {"translate": [{"x": -5, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": 10, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -5, "curve": 0.25, "c3": 0.75}, {"time": 2, "x": 10, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -5}]}, "3_MG5": {"translate": [{"x": 1.24, "curve": 0.369, "c2": 0.63, "c3": 0.706}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 15, "curve": 0.25, "c3": 0.75}, {"time": 1.4333, "curve": 0.25, "c3": 0.75}, {"time": 2.1, "x": 15, "curve": 0.244, "c3": 0.694, "c4": 0.77}, {"time": 2.6667, "x": 1.24}]}}}}}, [0]]], 0, 0, [0], [-1], [110]], [[{"name": "nan", "rect": [0, 0, 67, 66], "offset": [0, 0], "originalSize": [67, 66], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [111]], [[{"name": "to", "rect": [0, 0, 247, 247], "offset": [0, 0], "originalSize": [247, 247], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [112]], [[[133, "SVN-franko", 32, {"commonHeight": 42, "fontSize": 32, "atlasName": "SVN-franko.png", "fontDefDictionary": {"32": {"xOffset": 0, "yOffset": 36, "xAdvance": 9, "rect": {"x": 458, "y": 241, "width": 0, "height": 0}}, "33": {"xOffset": 3, "yOffset": 13, "xAdvance": 12, "rect": {"x": 468, "y": 191, "width": 6, "height": 23}}, "34": {"xOffset": 3, "yOffset": 13, "xAdvance": 17, "rect": {"x": 251, "y": 241, "width": 12, "height": 11}}, "35": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 327, "y": 191, "width": 16, "height": 23}}, "36": {"xOffset": 0, "yOffset": 10, "xAdvance": 17, "rect": {"x": 183, "y": 41, "width": 17, "height": 30}}, "37": {"xOffset": 1, "yOffset": 12, "xAdvance": 22, "rect": {"x": 2, "y": 138, "width": 21, "height": 25}}, "38": {"xOffset": 0, "yOffset": 13, "xAdvance": 20, "rect": {"x": 395, "y": 138, "width": 20, "height": 24}}, "39": {"xOffset": 2, "yOffset": 13, "xAdvance": 9, "rect": {"x": 230, "y": 241, "width": 5, "height": 11}}, "40": {"xOffset": 2, "yOffset": 13, "xAdvance": 11, "rect": {"x": 350, "y": 108, "width": 7, "height": 26}}, "41": {"xOffset": 1, "yOffset": 13, "xAdvance": 11, "rect": {"x": 492, "y": 108, "width": 7, "height": 26}}, "42": {"xOffset": 1, "yOffset": 13, "xAdvance": 12, "rect": {"x": 237, "y": 241, "width": 12, "height": 11}}, "43": {"xOffset": 2, "yOffset": 20, "xAdvance": 19, "rect": {"x": 440, "y": 216, "width": 17, "height": 17}}, "44": {"xOffset": 1, "yOffset": 30, "xAdvance": 9, "rect": {"x": 265, "y": 241, "width": 7, "height": 11}}, "45": {"xOffset": 1, "yOffset": 26, "xAdvance": 9, "rect": {"x": 382, "y": 241, "width": 8, "height": 5}}, "46": {"xOffset": 2, "yOffset": 30, "xAdvance": 9, "rect": {"x": 374, "y": 241, "width": 6, "height": 6}}, "47": {"xOffset": -2, "yOffset": 13, "xAdvance": 15, "rect": {"x": 75, "y": 138, "width": 14, "height": 24}}, "48": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 286, "y": 138, "width": 16, "height": 24}}, "49": {"xOffset": 3, "yOffset": 13, "xAdvance": 17, "rect": {"x": 199, "y": 191, "width": 13, "height": 23}}, "50": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 437, "y": 138, "width": 16, "height": 24}}, "51": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 125, "y": 138, "width": 17, "height": 24}}, "52": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 38, "y": 191, "width": 17, "height": 23}}, "53": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 57, "y": 138, "width": 16, "height": 24}}, "54": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 38, "y": 138, "width": 17, "height": 24}}, "55": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 2, "y": 191, "width": 15, "height": 23}}, "56": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 321, "y": 138, "width": 17, "height": 24}}, "57": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 471, "y": 138, "width": 17, "height": 24}}, "58": {"xOffset": 2, "yOffset": 20, "xAdvance": 9, "rect": {"x": 432, "y": 216, "width": 6, "height": 17}}, "59": {"xOffset": 1, "yOffset": 20, "xAdvance": 9, "rect": {"x": 90, "y": 216, "width": 7, "height": 22}}, "60": {"xOffset": 2, "yOffset": 20, "xAdvance": 19, "rect": {"x": 396, "y": 216, "width": 17, "height": 17}}, "61": {"xOffset": 2, "yOffset": 22, "xAdvance": 19, "rect": {"x": 287, "y": 241, "width": 17, "height": 11}}, "62": {"xOffset": 2, "yOffset": 20, "xAdvance": 19, "rect": {"x": 345, "y": 216, "width": 17, "height": 17}}, "63": {"xOffset": 0, "yOffset": 13, "xAdvance": 14, "rect": {"x": 164, "y": 138, "width": 14, "height": 24}}, "64": {"xOffset": 1, "yOffset": 13, "xAdvance": 26, "rect": {"x": 2, "y": 165, "width": 24, "height": 24}}, "65": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 291, "y": 191, "width": 18, "height": 23}}, "66": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 366, "y": 191, "width": 17, "height": 23}}, "67": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 376, "y": 138, "width": 17, "height": 24}}, "68": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 111, "y": 165, "width": 17, "height": 23}}, "69": {"xOffset": 1, "yOffset": 13, "xAdvance": 18, "rect": {"x": 94, "y": 165, "width": 15, "height": 23}}, "70": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 147, "y": 165, "width": 14, "height": 23}}, "71": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 144, "y": 138, "width": 18, "height": 24}}, "72": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 19, "y": 191, "width": 17, "height": 23}}, "73": {"xOffset": 2, "yOffset": 13, "xAdvance": 9, "rect": {"x": 480, "y": 165, "width": 6, "height": 23}}, "74": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 490, "y": 138, "width": 10, "height": 24}}, "75": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 2, "y": 216, "width": 18, "height": 23}}, "76": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 163, "y": 165, "width": 14, "height": 23}}, "77": {"xOffset": 1, "yOffset": 13, "xAdvance": 24, "rect": {"x": 70, "y": 165, "width": 22, "height": 23}}, "78": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 196, "y": 165, "width": 17, "height": 23}}, "79": {"xOffset": 0, "yOffset": 13, "xAdvance": 19, "rect": {"x": 231, "y": 138, "width": 18, "height": 24}}, "80": {"xOffset": 1, "yOffset": 13, "xAdvance": 18, "rect": {"x": 412, "y": 165, "width": 17, "height": 23}}, "81": {"xOffset": 0, "yOffset": 13, "xAdvance": 19, "rect": {"x": 245, "y": 76, "width": 18, "height": 29}}, "82": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 215, "y": 165, "width": 17, "height": 23}}, "83": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 180, "y": 138, "width": 17, "height": 24}}, "84": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 326, "y": 165, "width": 17, "height": 23}}, "85": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 107, "y": 138, "width": 16, "height": 24}}, "86": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 234, "y": 165, "width": 18, "height": 23}}, "87": {"xOffset": 0, "yOffset": 13, "xAdvance": 26, "rect": {"x": 401, "y": 191, "width": 26, "height": 23}}, "88": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 429, "y": 191, "width": 18, "height": 23}}, "89": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 162, "y": 191, "width": 18, "height": 23}}, "90": {"xOffset": 0, "yOffset": 13, "xAdvance": 16, "rect": {"x": 253, "y": 191, "width": 16, "height": 23}}, "91": {"xOffset": 2, "yOffset": 13, "xAdvance": 12, "rect": {"x": 359, "y": 108, "width": 8, "height": 26}}, "92": {"xOffset": -2, "yOffset": 13, "xAdvance": 15, "rect": {"x": 455, "y": 138, "width": 14, "height": 24}}, "93": {"xOffset": 1, "yOffset": 13, "xAdvance": 12, "rect": {"x": 369, "y": 108, "width": 8, "height": 26}}, "94": {"xOffset": 2, "yOffset": 13, "xAdvance": 19, "rect": {"x": 213, "y": 241, "width": 15, "height": 12}}, "95": {"xOffset": 0, "yOffset": 38, "xAdvance": 16, "rect": {"x": 430, "y": 241, "width": 16, "height": 2}}, "96": {"xOffset": -1, "yOffset": 14, "xAdvance": 11, "rect": {"x": 402, "y": 241, "width": 8, "height": 5}}, "97": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 415, "y": 216, "width": 15, "height": 17}}, "98": {"xOffset": 2, "yOffset": 13, "xAdvance": 17, "rect": {"x": 385, "y": 191, "width": 14, "height": 23}}, "99": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 329, "y": 216, "width": 14, "height": 17}}, "100": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 75, "y": 191, "width": 14, "height": 23}}, "101": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 380, "y": 216, "width": 14, "height": 17}}, "102": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 25, "y": 138, "width": 11, "height": 24}}, "103": {"xOffset": 0, "yOffset": 19, "xAdvance": 17, "rect": {"x": 91, "y": 191, "width": 17, "height": 23}}, "104": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 311, "y": 191, "width": 14, "height": 23}}, "105": {"xOffset": 1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 22, "y": 216, "width": 6, "height": 23}}, "106": {"xOffset": -1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 445, "y": 76, "width": 8, "height": 28}}, "107": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 182, "y": 191, "width": 15, "height": 23}}, "108": {"xOffset": 1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 110, "y": 191, "width": 6, "height": 23}}, "109": {"xOffset": 1, "yOffset": 20, "xAdvance": 24, "rect": {"x": 459, "y": 216, "width": 22, "height": 17}}, "110": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 87, "y": 241, "width": 14, "height": 17}}, "111": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 70, "y": 241, "width": 15, "height": 17}}, "112": {"xOffset": 2, "yOffset": 20, "xAdvance": 17, "rect": {"x": 240, "y": 216, "width": 14, "height": 22}}, "113": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 207, "y": 216, "width": 14, "height": 22}}, "114": {"xOffset": 1, "yOffset": 20, "xAdvance": 13, "rect": {"x": 57, "y": 241, "width": 11, "height": 17}}, "115": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 364, "y": 216, "width": 14, "height": 17}}, "116": {"xOffset": 0, "yOffset": 14, "xAdvance": 11, "rect": {"x": 134, "y": 191, "width": 11, "height": 23}}, "117": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 2, "y": 241, "width": 14, "height": 17}}, "118": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 103, "y": 241, "width": 14, "height": 17}}, "119": {"xOffset": 0, "yOffset": 20, "xAdvance": 20, "rect": {"x": 35, "y": 241, "width": 20, "height": 17}}, "120": {"xOffset": 0, "yOffset": 20, "xAdvance": 15, "rect": {"x": 18, "y": 241, "width": 15, "height": 17}}, "121": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 276, "y": 216, "width": 14, "height": 21}}, "122": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 483, "y": 216, "width": 13, "height": 17}}, "123": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 456, "y": 108, "width": 11, "height": 26}}, "124": {"xOffset": 2, "yOffset": 13, "xAdvance": 7, "rect": {"x": 225, "y": 138, "width": 4, "height": 24}}, "125": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 420, "y": 108, "width": 11, "height": 26}}, "126": {"xOffset": 2, "yOffset": 25, "xAdvance": 19, "rect": {"x": 357, "y": 241, "width": 15, "height": 6}}, "161": {"xOffset": 3, "yOffset": 21, "xAdvance": 11, "rect": {"x": 49, "y": 216, "width": 4, "height": 22}}, "162": {"xOffset": 2, "yOffset": 17, "xAdvance": 16, "rect": {"x": 147, "y": 191, "width": 13, "height": 23}}, "163": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 251, "y": 138, "width": 17, "height": 24}}, "164": {"xOffset": 1, "yOffset": 19, "xAdvance": 16, "rect": {"x": 166, "y": 241, "width": 13, "height": 13}}, "165": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 234, "y": 191, "width": 17, "height": 23}}, "166": {"xOffset": 2, "yOffset": 14, "xAdvance": 6, "rect": {"x": 55, "y": 216, "width": 3, "height": 22}}, "167": {"xOffset": 2, "yOffset": 14, "xAdvance": 16, "rect": {"x": 336, "y": 108, "width": 12, "height": 27}}, "168": {"xOffset": 0, "yOffset": 36, "xAdvance": 16, "rect": {"x": 454, "y": 241, "width": 0, "height": 0}}, "169": {"xOffset": 1, "yOffset": 13, "xAdvance": 26, "rect": {"x": 28, "y": 165, "width": 24, "height": 24}}, "170": {"xOffset": 0, "yOffset": 14, "xAdvance": 9, "rect": {"x": 318, "y": 241, "width": 9, "height": 10}}, "171": {"xOffset": 1, "yOffset": 23, "xAdvance": 16, "rect": {"x": 181, "y": 241, "width": 14, "height": 13}}, "172": {"xOffset": 1, "yOffset": 24, "xAdvance": 18, "rect": {"x": 329, "y": 241, "width": 17, "height": 9}}, "173": {"xOffset": 1, "yOffset": 28, "xAdvance": 11, "rect": {"x": 450, "y": 241, "width": 0, "height": 0}}, "174": {"xOffset": 1, "yOffset": 13, "xAdvance": 26, "rect": {"x": 199, "y": 138, "width": 24, "height": 24}}, "175": {"xOffset": -1, "yOffset": 17, "xAdvance": 11, "rect": {"x": 418, "y": 241, "width": 10, "height": 2}}, "176": {"xOffset": 1, "yOffset": 14, "xAdvance": 13, "rect": {"x": 274, "y": 241, "width": 11, "height": 11}}, "177": {"xOffset": 1, "yOffset": 17, "xAdvance": 18, "rect": {"x": 292, "y": 216, "width": 16, "height": 20}}, "178": {"xOffset": 0, "yOffset": 15, "xAdvance": 10, "rect": {"x": 119, "y": 241, "width": 10, "height": 14}}, "179": {"xOffset": 0, "yOffset": 15, "xAdvance": 10, "rect": {"x": 131, "y": 241, "width": 9, "height": 14}}, "180": {"xOffset": 1, "yOffset": 14, "xAdvance": 11, "rect": {"x": 392, "y": 241, "width": 8, "height": 5}}, "181": {"xOffset": -2, "yOffset": 22, "xAdvance": 18, "rect": {"x": 256, "y": 216, "width": 18, "height": 21}}, "182": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 142, "y": 108, "width": 16, "height": 27}}, "183": {"xOffset": 2, "yOffset": 24, "xAdvance": 8, "rect": {"x": 412, "y": 241, "width": 4, "height": 4}}, "184": {"xOffset": 1, "yOffset": 36, "xAdvance": 11, "rect": {"x": 348, "y": 241, "width": 7, "height": 7}}, "185": {"xOffset": 2, "yOffset": 15, "xAdvance": 10, "rect": {"x": 142, "y": 241, "width": 6, "height": 14}}, "186": {"xOffset": 1, "yOffset": 14, "xAdvance": 10, "rect": {"x": 306, "y": 241, "width": 10, "height": 10}}, "187": {"xOffset": 1, "yOffset": 23, "xAdvance": 16, "rect": {"x": 197, "y": 241, "width": 14, "height": 13}}, "188": {"xOffset": 0, "yOffset": 36, "xAdvance": 28, "rect": {"x": 456, "y": 241, "width": 0, "height": 0}}, "189": {"xOffset": 0, "yOffset": 36, "xAdvance": 28, "rect": {"x": 448, "y": 241, "width": 0, "height": 0}}, "190": {"xOffset": 0, "yOffset": 36, "xAdvance": 28, "rect": {"x": 452, "y": 241, "width": 0, "height": 0}}, "191": {"xOffset": 1, "yOffset": 21, "xAdvance": 14, "rect": {"x": 60, "y": 216, "width": 12, "height": 22}}, "192": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 443, "y": 41, "width": 18, "height": 30}}, "193": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 42, "y": 76, "width": 18, "height": 30}}, "194": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 2, "y": 76, "width": 18, "height": 30}}, "195": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 463, "y": 41, "width": 18, "height": 30}}, "200": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 483, "y": 41, "width": 15, "height": 30}}, "201": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 371, "y": 41, "width": 15, "height": 30}}, "202": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 228, "y": 76, "width": 15, "height": 30}}, "204": {"xOffset": -1, "yOffset": 7, "xAdvance": 9, "rect": {"x": 282, "y": 41, "width": 9, "height": 30}}, "205": {"xOffset": 2, "yOffset": 7, "xAdvance": 9, "rect": {"x": 186, "y": 76, "width": 9, "height": 30}}, "210": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 333, "y": 41, "width": 18, "height": 30}}, "211": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 406, "y": 41, "width": 18, "height": 30}}, "212": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 262, "y": 41, "width": 18, "height": 30}}, "213": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 293, "y": 41, "width": 18, "height": 30}}, "217": {"xOffset": 1, "yOffset": 7, "xAdvance": 19, "rect": {"x": 388, "y": 41, "width": 16, "height": 30}}, "218": {"xOffset": 1, "yOffset": 7, "xAdvance": 19, "rect": {"x": 353, "y": 41, "width": 16, "height": 30}}, "221": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 313, "y": 41, "width": 18, "height": 30}}, "224": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 270, "y": 165, "width": 15, "height": 23}}, "225": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 179, "y": 165, "width": 15, "height": 23}}, "226": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 130, "y": 165, "width": 15, "height": 23}}, "227": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 304, "y": 138, "width": 15, "height": 24}}, "232": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 254, "y": 165, "width": 14, "height": 23}}, "233": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 448, "y": 165, "width": 14, "height": 23}}, "234": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 54, "y": 165, "width": 14, "height": 23}}, "236": {"xOffset": -2, "yOffset": 14, "xAdvance": 8, "rect": {"x": 298, "y": 165, "width": 9, "height": 23}}, "237": {"xOffset": 1, "yOffset": 14, "xAdvance": 8, "rect": {"x": 287, "y": 165, "width": 9, "height": 23}}, "242": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 345, "y": 165, "width": 15, "height": 23}}, "243": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 379, "y": 165, "width": 15, "height": 23}}, "244": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 431, "y": 165, "width": 15, "height": 23}}, "245": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 340, "y": 138, "width": 15, "height": 24}}, "249": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 396, "y": 165, "width": 14, "height": 23}}, "250": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 464, "y": 165, "width": 14, "height": 23}}, "253": {"xOffset": 0, "yOffset": 14, "xAdvance": 14, "rect": {"x": 286, "y": 108, "width": 14, "height": 27}}, "258": {"xOffset": 0, "yOffset": 8, "xAdvance": 18, "rect": {"x": 472, "y": 76, "width": 18, "height": 28}}, "259": {"xOffset": 1, "yOffset": 15, "xAdvance": 15, "rect": {"x": 223, "y": 216, "width": 15, "height": 22}}, "272": {"xOffset": 0, "yOffset": 13, "xAdvance": 20, "rect": {"x": 345, "y": 191, "width": 19, "height": 23}}, "273": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 57, "y": 191, "width": 16, "height": 23}}, "296": {"xOffset": -1, "yOffset": 7, "xAdvance": 9, "rect": {"x": 214, "y": 76, "width": 12, "height": 30}}, "297": {"xOffset": -2, "yOffset": 13, "xAdvance": 8, "rect": {"x": 488, "y": 165, "width": 12, "height": 23}}, "360": {"xOffset": 1, "yOffset": 7, "xAdvance": 19, "rect": {"x": 222, "y": 41, "width": 16, "height": 30}}, "361": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 270, "y": 138, "width": 14, "height": 24}}, "416": {"xOffset": 0, "yOffset": 9, "xAdvance": 19, "rect": {"x": 18, "y": 108, "width": 20, "height": 28}}, "417": {"xOffset": 1, "yOffset": 17, "xAdvance": 15, "rect": {"x": 310, "y": 216, "width": 17, "height": 20}}, "431": {"xOffset": 1, "yOffset": 8, "xAdvance": 20, "rect": {"x": 282, "y": 76, "width": 20, "height": 29}}, "432": {"xOffset": 1, "yOffset": 15, "xAdvance": 16, "rect": {"x": 99, "y": 216, "width": 18, "height": 22}}, "7840": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 330, "y": 76, "width": 18, "height": 29}}, "7841": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 309, "y": 165, "width": 15, "height": 23}}, "7842": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 459, "y": 2, "width": 18, "height": 33}}, "7843": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 215, "y": 108, "width": 15, "height": 27}}, "7844": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 77, "y": 41, "width": 22, "height": 33}}, "7845": {"xOffset": 1, "yOffset": 11, "xAdvance": 15, "rect": {"x": 433, "y": 108, "width": 21, "height": 26}}, "7846": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 422, "y": 2, "width": 18, "height": 34}}, "7847": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 318, "y": 108, "width": 16, "height": 27}}, "7848": {"xOffset": 0, "yOffset": 0, "xAdvance": 18, "rect": {"x": 41, "y": 2, "width": 20, "height": 36}}, "7849": {"xOffset": 1, "yOffset": 7, "xAdvance": 15, "rect": {"x": 22, "y": 76, "width": 18, "height": 30}}, "7850": {"xOffset": 0, "yOffset": 1, "xAdvance": 18, "rect": {"x": 180, "y": 2, "width": 18, "height": 35}}, "7851": {"xOffset": 1, "yOffset": 8, "xAdvance": 15, "rect": {"x": 455, "y": 76, "width": 15, "height": 28}}, "7852": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 143, "y": 2, "width": 18, "height": 36}}, "7853": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 411, "y": 76, "width": 15, "height": 29}}, "7854": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 280, "y": 2, "width": 18, "height": 34}}, "7855": {"xOffset": 1, "yOffset": 9, "xAdvance": 15, "rect": {"x": 40, "y": 108, "width": 15, "height": 28}}, "7856": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 220, "y": 2, "width": 18, "height": 34}}, "7857": {"xOffset": 1, "yOffset": 9, "xAdvance": 15, "rect": {"x": 492, "y": 76, "width": 15, "height": 28}}, "7858": {"xOffset": 0, "yOffset": 0, "xAdvance": 18, "rect": {"x": 123, "y": 2, "width": 18, "height": 36}}, "7859": {"xOffset": 1, "yOffset": 7, "xAdvance": 15, "rect": {"x": 197, "y": 76, "width": 15, "height": 30}}, "7860": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 300, "y": 2, "width": 18, "height": 34}}, "7861": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 232, "y": 108, "width": 15, "height": 27}}, "7862": {"xOffset": 0, "yOffset": 8, "xAdvance": 18, "rect": {"x": 200, "y": 2, "width": 18, "height": 34}}, "7863": {"xOffset": 1, "yOffset": 15, "xAdvance": 15, "rect": {"x": 57, "y": 108, "width": 15, "height": 28}}, "7864": {"xOffset": 1, "yOffset": 13, "xAdvance": 18, "rect": {"x": 265, "y": 76, "width": 15, "height": 29}}, "7865": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 118, "y": 191, "width": 14, "height": 23}}, "7866": {"xOffset": 1, "yOffset": 3, "xAdvance": 18, "rect": {"x": 442, "y": 2, "width": 15, "height": 33}}, "7867": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 302, "y": 108, "width": 14, "height": 27}}, "7868": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 426, "y": 41, "width": 15, "height": 30}}, "7869": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 91, "y": 138, "width": 14, "height": 24}}, "7870": {"xOffset": 1, "yOffset": 3, "xAdvance": 18, "rect": {"x": 55, "y": 41, "width": 20, "height": 33}}, "7871": {"xOffset": 1, "yOffset": 11, "xAdvance": 15, "rect": {"x": 398, "y": 108, "width": 20, "height": 26}}, "7872": {"xOffset": 1, "yOffset": 2, "xAdvance": 18, "rect": {"x": 342, "y": 2, "width": 16, "height": 34}}, "7873": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 106, "y": 108, "width": 16, "height": 27}}, "7874": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 103, "y": 2, "width": 18, "height": 36}}, "7875": {"xOffset": 1, "yOffset": 7, "xAdvance": 15, "rect": {"x": 62, "y": 76, "width": 18, "height": 30}}, "7876": {"xOffset": 1, "yOffset": 1, "xAdvance": 18, "rect": {"x": 163, "y": 2, "width": 15, "height": 35}}, "7877": {"xOffset": 1, "yOffset": 8, "xAdvance": 15, "rect": {"x": 2, "y": 108, "width": 14, "height": 28}}, "7878": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 24, "y": 2, "width": 15, "height": 36}}, "7879": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 378, "y": 76, "width": 14, "height": 29}}, "7880": {"xOffset": 1, "yOffset": 3, "xAdvance": 9, "rect": {"x": 44, "y": 41, "width": 9, "height": 33}}, "7881": {"xOffset": 0, "yOffset": 10, "xAdvance": 8, "rect": {"x": 501, "y": 108, "width": 8, "height": 26}}, "7882": {"xOffset": 2, "yOffset": 13, "xAdvance": 9, "rect": {"x": 322, "y": 76, "width": 6, "height": 29}}, "7883": {"xOffset": 1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 370, "y": 76, "width": 6, "height": 29}}, "7884": {"xOffset": 0, "yOffset": 13, "xAdvance": 19, "rect": {"x": 104, "y": 76, "width": 18, "height": 30}}, "7885": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 362, "y": 165, "width": 15, "height": 23}}, "7886": {"xOffset": 0, "yOffset": 3, "xAdvance": 19, "rect": {"x": 402, "y": 2, "width": 18, "height": 34}}, "7887": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 269, "y": 108, "width": 15, "height": 27}}, "7888": {"xOffset": 0, "yOffset": 3, "xAdvance": 19, "rect": {"x": 479, "y": 2, "width": 22, "height": 33}}, "7889": {"xOffset": 1, "yOffset": 11, "xAdvance": 16, "rect": {"x": 469, "y": 108, "width": 21, "height": 26}}, "7890": {"xOffset": 0, "yOffset": 2, "xAdvance": 19, "rect": {"x": 382, "y": 2, "width": 18, "height": 34}}, "7891": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 124, "y": 108, "width": 16, "height": 27}}, "7892": {"xOffset": 0, "yOffset": 0, "xAdvance": 19, "rect": {"x": 2, "y": 2, "width": 20, "height": 37}}, "7893": {"xOffset": 1, "yOffset": 7, "xAdvance": 16, "rect": {"x": 202, "y": 41, "width": 18, "height": 30}}, "7894": {"xOffset": 0, "yOffset": 1, "xAdvance": 19, "rect": {"x": 83, "y": 2, "width": 18, "height": 36}}, "7895": {"xOffset": 1, "yOffset": 8, "xAdvance": 16, "rect": {"x": 428, "y": 76, "width": 15, "height": 28}}, "7896": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 63, "y": 2, "width": 18, "height": 36}}, "7897": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 394, "y": 76, "width": 15, "height": 29}}, "7898": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 139, "y": 41, "width": 20, "height": 30}}, "7899": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 30, "y": 216, "width": 17, "height": 23}}, "7900": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 117, "y": 41, "width": 20, "height": 30}}, "7901": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 449, "y": 191, "width": 17, "height": 23}}, "7902": {"xOffset": 0, "yOffset": 3, "xAdvance": 19, "rect": {"x": 258, "y": 2, "width": 20, "height": 34}}, "7903": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 196, "y": 108, "width": 17, "height": 27}}, "7904": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 240, "y": 41, "width": 20, "height": 30}}, "7905": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 357, "y": 138, "width": 17, "height": 24}}, "7906": {"xOffset": 0, "yOffset": 9, "xAdvance": 19, "rect": {"x": 2, "y": 41, "width": 20, "height": 33}}, "7907": {"xOffset": 1, "yOffset": 17, "xAdvance": 15, "rect": {"x": 379, "y": 108, "width": 17, "height": 26}}, "7908": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 304, "y": 76, "width": 16, "height": 29}}, "7909": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 119, "y": 216, "width": 14, "height": 22}}, "7910": {"xOffset": 1, "yOffset": 3, "xAdvance": 19, "rect": {"x": 240, "y": 2, "width": 16, "height": 34}}, "7911": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 90, "y": 108, "width": 14, "height": 27}}, "7912": {"xOffset": 1, "yOffset": 7, "xAdvance": 20, "rect": {"x": 124, "y": 76, "width": 20, "height": 30}}, "7913": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 214, "y": 191, "width": 18, "height": 23}}, "7914": {"xOffset": 1, "yOffset": 7, "xAdvance": 20, "rect": {"x": 82, "y": 76, "width": 20, "height": 30}}, "7915": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 271, "y": 191, "width": 18, "height": 23}}, "7916": {"xOffset": 1, "yOffset": 3, "xAdvance": 20, "rect": {"x": 320, "y": 2, "width": 20, "height": 34}}, "7917": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 249, "y": 108, "width": 18, "height": 27}}, "7918": {"xOffset": 1, "yOffset": 7, "xAdvance": 20, "rect": {"x": 161, "y": 41, "width": 20, "height": 30}}, "7919": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 417, "y": 138, "width": 18, "height": 24}}, "7920": {"xOffset": 1, "yOffset": 8, "xAdvance": 20, "rect": {"x": 360, "y": 2, "width": 20, "height": 34}}, "7921": {"xOffset": 1, "yOffset": 15, "xAdvance": 16, "rect": {"x": 160, "y": 108, "width": 18, "height": 27}}, "7922": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 166, "y": 76, "width": 18, "height": 30}}, "7923": {"xOffset": 0, "yOffset": 14, "xAdvance": 14, "rect": {"x": 180, "y": 108, "width": 14, "height": 27}}, "7924": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 350, "y": 76, "width": 18, "height": 29}}, "7925": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 74, "y": 216, "width": 14, "height": 22}}, "7926": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 24, "y": 41, "width": 18, "height": 33}}, "7927": {"xOffset": 0, "yOffset": 10, "xAdvance": 14, "rect": {"x": 101, "y": 41, "width": 14, "height": 31}}, "7928": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 146, "y": 76, "width": 18, "height": 30}}, "7929": {"xOffset": 0, "yOffset": 13, "xAdvance": 14, "rect": {"x": 74, "y": 108, "width": 14, "height": 28}}, "9679": {"xOffset": 3, "yOffset": 20, "xAdvance": 19, "rect": {"x": 150, "y": 241, "width": 14, "height": 14}}, "9824": {"xOffset": 2, "yOffset": 14, "xAdvance": 24, "rect": {"x": 161, "y": 216, "width": 21, "height": 22}}, "9827": {"xOffset": 1, "yOffset": 15, "xAdvance": 24, "rect": {"x": 135, "y": 216, "width": 24, "height": 22}}, "9829": {"xOffset": 2, "yOffset": 14, "xAdvance": 24, "rect": {"x": 184, "y": 216, "width": 21, "height": 22}}, "9830": {"xOffset": 3, "yOffset": 14, "xAdvance": 24, "rect": {"x": 476, "y": 191, "width": 18, "height": 23}}}, "kerningDict": {}}]], 0, 0, [0], [82], [113]], [[{"name": "btn_copy", "rect": [0, 0, 67, 38], "offset": [0, 0.5], "originalSize": [67, 39], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [114]], [[{"name": "md5 box", "rect": [0, 0, 605, 41], "offset": [0, 0], "originalSize": [605, 41], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [115]]]]