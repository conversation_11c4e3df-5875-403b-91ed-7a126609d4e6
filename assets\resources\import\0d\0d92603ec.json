[1, ["4eUTsrZLRDwrjB0SbgkatS", "58GVf2ZPtL7LD5fc6mPvjF", "d2rzA7Ir9PFbDzJ1vTv3ti", "7dhsRuVVRLKJtE3kSs+fC6", "7eQzJAh4dCT4TI0mGJF1US", "98y14mBU5PW48DyjjP2vGe", "f5/xCTrcFO+5uwl3JEn8fF", "05kGfmSiZHLa5gxEgmr1z6", "ac5bjSDA5BiqYcRCSLnDrQ", "759fd68MJJ9oVYWGXG0H/Y", "6dUp9eq7FLcJbNPtAJWFlp", "d3cCp6oF9AoJyujG/vWxAs", "f96MzFqAVFXLFrsm6ilc8z", "8f48PfS65DxISG8NEGvLrT", "0biPqydftGEYLxFdTBAt5w", "96bmlVZIFF+YdABudMY2/Q", "17XE2s+DBDv47WJQdMeEB2", "960CR2bMJHrJI38MrVPZA7", "77lJwHqPZCxrC6I5hI8VDX", "61PbV/LNxJeboBKSyRwNDz", "93BdnD4RhJsrSJ2YqhVu7m", "abbjSQukNEKo+ZjcpfNsxy", "efm6JmkndAbrIFRPSF+cJo", "310cDvIj5CLIc6ahMmoBwy", "66VkgutsNIup5lLWQ87T+y", "2eEfzFc3ZDJLF1pFGJ751Y", "1dRtN0ZnBG/agrOiY/rb5W", "cdX8jE2FpHGaerSRi/vE+O", "e0VhCk8MRIZ6BwmJTSpa1W", "89Chy7YTRF+IZbywIsjhXt", "e7RUVxoChPdaFtR6DcMoU8", "c2j22EfOdIXJPovMRNqvLe", "a2gzt58gJGqIQRIHCwH2yG", "6fGvOAWUNBxaP4SuOcufvO", "9eOzzPgC1HSrKJR4FAUb9f"], ["_textureSetter", "mntx_dice_anim_0", "mntx_dice_anim_1", "mntx_dice_anim_10", "mntx_dice_anim_11", "mntx_dice_anim_12", "mntx_dice_anim_13", "mntx_dice_anim_14", "mntx_dice_anim_15", "mntx_dice_anim_2", "mntx_dice_anim_3", "mntx_dice_anim_4", "mntx_dice_anim_5", "mntx_dice_anim_6", "mntx_dice_anim_7", "mntx_dice_anim_8", "mntx_dice_anim_9", "mntx_dice_result_1_1", "mntx_dice_result_1_2", "mntx_dice_result_1_3", "mntx_dice_result_1_4", "mntx_dice_result_1_5", "mntx_dice_result_1_6", "mntx_dice_result_2_1", "mntx_dice_result_2_2", "mntx_dice_result_2_3", "mntx_dice_result_2_4", "mntx_dice_result_2_5", "mntx_dice_result_2_6", "mntx_dice_result_3_1", "mntx_dice_result_3_2", "mntx_dice_result_3_3", "mntx_dice_result_3_4", "mntx_dice_result_3_5", "mntx_dice_result_3_6"], ["cc.SpriteFrame", ["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[1, 0, 1, 2]], [[[{"name": "mntx_dice_result_2_4", "rect": [239, 215, 92, 94], "offset": [-2, 43], "originalSize": [240, 220], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_2_3", "rect": [395, 1597, 92, 94], "offset": [-2, 43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_2_1", "rect": [1, 1667, 92, 94], "offset": [-2, 43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_1_3", "rect": [407, 1121, 90, 92], "offset": [-46, -44], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_2_2", "rect": [95, 1667, 92, 94], "offset": [-2, 43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_3_5", "rect": [407, 835, 92, 94], "offset": [46, -43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_1_2", "rect": [407, 1027, 90, 92], "offset": [-46, -44], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_3_1", "rect": [331, 309, 92, 94], "offset": [46, -43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_1_4", "rect": [407, 1215, 90, 92], "offset": [-46, -44], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_1_1", "rect": [405, 1505, 90, 92], "offset": [-46, -44], "originalSize": [240, 220], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_3_6", "rect": [407, 931, 92, 94], "offset": [46, -43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_3_4", "rect": [405, 739, 92, 94], "offset": [46, -43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_1_5", "rect": [407, 1309, 90, 92], "offset": [-46, -44], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_3_3", "rect": [405, 643, 92, 94], "offset": [46, -43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_2_5", "rect": [335, 215, 92, 94], "offset": [-2, 43], "originalSize": [240, 220], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "MiniTaiXiu_Dice.plist", [{}, "mntx_dice_anim_0", 6, 0, "mntx_dice_anim_1", 6, 1, "mntx_dice_anim_10", 6, 2, "mntx_dice_anim_11", 6, 3, "mntx_dice_anim_12", 6, 4, "mntx_dice_anim_13", 6, 5, "mntx_dice_anim_14", 6, 6, "mntx_dice_anim_15", 6, 7, "mntx_dice_anim_2", 6, 8, "mntx_dice_anim_3", 6, 9, "mntx_dice_anim_4", 6, 10, "mntx_dice_anim_5", 6, 11, "mntx_dice_anim_6", 6, 12, "mntx_dice_anim_7", 6, 13, "mntx_dice_anim_8", 6, 14, "mntx_dice_anim_9", 6, 15, "mntx_dice_result_1_1", 6, 16, "mntx_dice_result_1_2", 6, 17, "mntx_dice_result_1_3", 6, 18, "mntx_dice_result_1_4", 6, 19, "mntx_dice_result_1_5", 6, 20, "mntx_dice_result_1_6", 6, 21, "mntx_dice_result_2_1", 6, 22, "mntx_dice_result_2_2", 6, 23, "mntx_dice_result_2_3", 6, 24, "mntx_dice_result_2_4", 6, 25, "mntx_dice_result_2_5", 6, 26, "mntx_dice_result_2_6", 6, 27, "mntx_dice_result_3_1", 6, 28, "mntx_dice_result_3_2", 6, 29, "mntx_dice_result_3_3", 6, 30, "mntx_dice_result_3_4", 6, 31, "mntx_dice_result_3_5", 6, 32, "mntx_dice_result_3_6", 6, 33]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34]], [[{"name": "mntx_dice_result_2_6", "rect": [237, 309, 92, 94], "offset": [-2, 43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_3_2", "rect": [405, 1409, 92, 94], "offset": [46, -43], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "mntx_dice_result_1_6", "rect": [407, 405, 90, 92], "offset": [-46, -44], "originalSize": [240, 220], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]