[1, ["dfCzKgllVD1KtMHedhQVLX", "c1NWKyAo1BXKm8Ny8g2H/k"], ["spriteFrame", "_textureSetter"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[[0, "so_v_T@export", 32, {"commonHeight": 120, "fontSize": 32, "atlasName": "<EMAIL>", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 80, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 10, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "46": {"xOffset": 0, "yOffset": 89, "xAdvance": 25, "rect": {"x": 279, "y": 0, "width": 26, "height": 31}}, "48": {"xOffset": 0, "yOffset": 17, "xAdvance": 77, "rect": {"x": 0, "y": 102, "width": 78, "height": 105}}, "49": {"xOffset": 0, "yOffset": 19, "xAdvance": 33, "rect": {"x": 244, "y": 0, "width": 34, "height": 101}}, "50": {"xOffset": 0, "yOffset": 18, "xAdvance": 62, "rect": {"x": 216, "y": 208, "width": 63, "height": 102}}, "51": {"xOffset": 0, "yOffset": 18, "xAdvance": 58, "rect": {"x": 231, "y": 102, "width": 59, "height": 103}}, "52": {"xOffset": 0, "yOffset": 19, "xAdvance": 75, "rect": {"x": 103, "y": 0, "width": 76, "height": 101}}, "53": {"xOffset": 0, "yOffset": 19, "xAdvance": 63, "rect": {"x": 151, "y": 208, "width": 64, "height": 103}}, "54": {"xOffset": 0, "yOffset": 17, "xAdvance": 75, "rect": {"x": 79, "y": 102, "width": 76, "height": 105}}, "55": {"xOffset": 0, "yOffset": 19, "xAdvance": 62, "rect": {"x": 180, "y": 0, "width": 63, "height": 101}}, "56": {"xOffset": 0, "yOffset": 17, "xAdvance": 73, "rect": {"x": 156, "y": 102, "width": 74, "height": 105}}, "57": {"xOffset": 0, "yOffset": 17, "xAdvance": 77, "rect": {"x": 0, "y": 208, "width": 78, "height": 104}}, "75": {"xOffset": 0, "yOffset": 19, "xAdvance": 70, "rect": {"x": 79, "y": 208, "width": 71, "height": 101}}, "77": {"xOffset": 0, "yOffset": 19, "xAdvance": 101, "rect": {"x": 0, "y": 0, "width": 102, "height": 101}}}, "kerningDict": {}}]], 0, 0, [0], [0], [0]], [[{"name": "so_v_T@export", "rect": [0, 0, 305, 312], "offset": [-0.5, 0.5], "originalSize": [306, 313], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]]]]