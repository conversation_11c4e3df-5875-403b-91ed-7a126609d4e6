[1, ["ecpdLyjvZBwrvm+cedCcQy", "5b4hxm6C5Ac4ld/9Eul9gs", "a2tBXzjmRHWIetS1zkxuiC", "e3eE2CcDxHYI2nHPG62F41", "d6QPCnXE9Mj5f7/j85J0xF", "7fkPcDrdtJsJicV1F3ZEhQ", "adw94Z+hpN57wutNivq8Q5", "a9Iv+mV1tFI7CjOUl5vK95", "f0Kzatav9N1Lw1tSxhEUVM", "a9VpD0DP5LJYQPXITZq+uj", "395rNa1wpDEqwvYtu0QmcS", "d9JvehW55O0onMoSmNc9RE", "6a5h44kzdEZLpsX5Jh8O+o", "16ovWCGYdEvYHKA0xmFfYj", "88Rwo8yjhPuLbVLSt1HafE", "a8LSkPX+RIhopqVeWEDuDg", "70EOHW1PdNoL5uO8vl69H8", "7a/QZLET9IDreTiBfRn2PD", "c9zSnab4NK9acPEVYE6ysg", "54k9NdSdRC044Gsy4V11EG", "aeC/4keUxPPIwO9cJU2TEl", "e1c3yU3axJjIoCZ/PRoOEs", "caKgpNYY1BlbkdsS0v0DvV", "75Sdk6WeROJagKHCXa43YZ", "347d6Ht0pAUYwGEaGgXjYq", "5csM8iV5pIq4c7p7EbGcgE", "2dQ2T56DlBvobuPhVS6mtT", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "ecLkTXkTNJEJHkmCc8NVhB", "a6cmKQRV9Mp5CutMWLTKxy", "65G5sH8GhMNZi7/x9UDoca", "5eyIL2OJtMjYpsaNs1L6zm", "06io7pee5N1qn/RNMm1jg8", "77QM5yEDVHjZCuIxU4hoxe", "3a3Sy3arlMIIrrO11ZFeC2", "9f8jW/kgtEMpBZNoH8/mbT", "18B8561jlPLJe1jpcKbORo", "ecwUD0lgJJD7PUp/utvMOS", "6aelqP12BCVbeSKmKNzC5w", "921Y5I891J/JaSxbtk5e93", "f9jwpnHPVCB6puw09ly8io", "9eM0NwMVlEna048q2ZIBsw", "06+VRi67pFWqOgW81bJvFN", "f93Knzl7BBfJB87Wmm/e8m", "a7WrxhHHBNkZ08YuSkOTiH", "2bc1nAlv9BNKXzFyfBSxAG"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_N$normalSprite", "_N$skeletonData", "_parent", "root", "btnQuickPlay", "lbiWin", "lbTime", "nodeResult", "nodeMulti", "nodePick", "nodeBonus", "nodeStart", "target", "lbResult", "_N$target", "data", "sfPicked", "sfMiss", "sfDoorOpen", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], ["cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_enabled", "_type", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_eulerAngles"], 0, 4, 5, 9, 1, 2, 7, 5], ["cc.Label", ["_lineHeight", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_string", "_fontSize", "_enableWrapText", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 5, 6, 6, 6, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 2, 5, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 2, 4, 5, 7, 2], ["fa35bHjksRPqKrWumsBm7ik", ["node", "btnPicks", "spriteDoors", "spriteResults", "sfMultipliers", "sfDoorOpen"], 3, 1, 2, 2, 2, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["sp.Skeleton", ["defaultAnimation", "defaultSkin", "_preCacheMode", "_animationName", "loop", "premultipliedAlpha", "node", "_materials", "_N$skeletonData"], -3, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["3583bXj5HBNNrBb4UiPP0yG", ["node", "nodeStart", "nodeBonus", "nodePick", "nodeMulti", "nodeResult", "lbTime", "lbiWin", "btnQuickPlay"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["ef6a1RSJzxDyavLJFcERFeM", ["node", "btnPicks", "lbiPrizes", "cardSkeletons", "sfPicked", "sfMiss"], 3, 1, 2, 2, 2, 6, 6], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["09052Zq0oVJl59gzr1ZolUq", ["node"], 3, 1], ["7269ege3OpEV4lKd6fET93f", ["node", "lbResult"], 3, 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[12, 0, 1, 2], [6, 0, 1, 2, 3, 4, 5, 6, 3], [8, 0, 1, 2, 3, 4], [19, 0, 1], [4, 1, 0, 2, 3, 4, 5, 6, 3], [2, 0, 1, 6, 7, 5, 3, 4, 3], [2, 0, 6, 5, 3, 4, 8, 2], [5, 0, 1, 6, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 7, 4, 2], [1, 3, 0, 4, 5, 6, 3], [9, 0, 4, 5, 6, 4], [1, 4, 6, 1], [3, 0, 6, 1, 3, 2, 7, 9, 6], [1, 0, 4, 5, 6, 2], [1, 4, 5, 6, 1], [2, 0, 6, 7, 5, 3, 4, 8, 2], [6, 0, 2, 3, 4, 5, 6, 2], [8, 0, 1, 3, 3], [2, 0, 2, 6, 5, 3, 4, 3], [1, 0, 1, 4, 5, 6, 3], [1, 0, 1, 4, 5, 3], [4, 2, 3, 7, 1], [4, 2, 3, 4, 5, 6, 1], [2, 0, 7, 5, 3, 4, 2], [2, 0, 1, 7, 5, 3, 4, 3], [2, 0, 6, 5, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 2], [6, 0, 2, 7, 3, 4, 5, 6, 2], [1, 4, 1], [14, 0, 1], [4, 2, 7, 1], [3, 4, 5, 0, 1, 3, 2, 7, 8, 9, 7], [18, 0, 1, 2, 2], [10, 0, 2], [2, 0, 6, 7, 5, 3, 4, 2], [2, 0, 6, 7, 3, 4, 2], [2, 0, 6, 5, 3, 4, 8, 9, 2], [5, 0, 1, 2, 3, 4, 8, 5, 2], [6, 0, 2, 7, 3, 4, 5, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [1, 2, 0, 4, 5, 6, 3], [1, 2, 0, 1, 4, 5, 6, 4], [1, 2, 4, 5, 6, 2], [1, 4, 5, 1], [13, 0, 1, 2, 3, 4, 5, 1], [7, 0, 1, 2, 3, 4, 5, 1], [7, 0, 1], [4, 0, 2, 4, 5, 6, 8, 9, 10, 11, 2], [15, 0, 1, 2, 3, 4, 5, 5], [16, 0, 1], [17, 0, 1, 1], [3, 0, 6, 1, 3, 2, 7, 8, 9, 6], [3, 4, 5, 0, 6, 1, 3, 2, 7, 8, 9, 8], [3, 4, 5, 0, 6, 1, 2, 7, 8, 7], [3, 4, 5, 0, 1, 3, 2, 7, 8, 7], [9, 1, 0, 2, 3, 6, 7, 8, 5], [20, 0, 1, 2, 3, 4, 5]], [[[[33, "bonusGameView"], [23, "bonusGameView", [-11, -12, -13], [[39, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [0, "bcrrzLeUpDkJEBYOAKGREZ", -1], [5, 1280, 720]], [23, "bonusGamePickView", [-47, -48, -49, -50, -51], [[19, 2, false, -14, [62], 63], [44, -45, [-35, -36, -37, -38, -39, -40, -41, -42, -43, -44], [-25, -26, -27, -28, -29, -30, -31, -32, -33, -34], [-15, -16, -17, -18, -19, -20, -21, -22, -23, -24], 64, 65], [29, -46]], [0, "1d0Db7f9VCQ4qL/H37vD5G", 1], [5, 2024, 1200]], [24, "bonusGameMultiView", false, [-64, -65, -66, -67, -68, -69], [[45, -61, [-58, -59, -60], [-55, -56, -57], [-52, -53, -54], [80, 81, 82], 83], [29, -62], [30, -63, [4, 4292269782]]], [0, "53GzVa0jZJQpxxcr+hXPBu", 1], [5, 1280, 720]], [15, "layout", 2, [-71, -72, -73, -74, -75, -76, -77, -78, -79, -80], [[48, 1, 3, 38, 20, -70, [5, 854, 392]]], [0, "afoLOmqQJPWaUvf6uQarIA", 1], [5, 854, 392], [0, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "bonusGameStartView", 1, [-85, -86, -87], [[40, false, 2, -81, [10], 11], [49, -82], [21, -84, [[17, "09052Zq0oVJl59gzr1ZolUq", "startClicked", -83]], [4, 4292269782]]], [0, "37SV/MHJBItK4WKTsqseMc", 1], [5, 489, 297]], [24, "bonusGameResultView", false, [-91, -92, -93, -94], [[41, false, 2, false, -88, [106], 107], [50, -90, -89]], [0, "862Asd/UpOv5WPrEBNZBCn", 1], [5, 489, 297]], [35, "bonusGame", 1, [2, 3, -95, -96, -97, 6], [0, "500o+npENJyKYRgN0WhyV+", 1], [5, 1280, 720]], [7, "button", 4, [-100, -101], [[[9, 1, 0, -98, [22], 23], -99], 4, 1], [0, "8bUU7MWvJCPaxs2zzSos9W", 1], [5, 140, 186], [-357, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-104, -105], [[[9, 1, 0, -102, [26], 27], -103], 4, 1], [0, "39e78gF25EZblT9D4TrsF/", 1], [5, 140, 186], [-179, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-108, -109], [[[9, 1, 0, -106, [30], 31], -107], 4, 1], [0, "aezV+hqnRPJqSfM9LN9eW6", 1], [5, 140, 186], [-1, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-112, -113], [[[9, 1, 0, -110, [34], 35], -111], 4, 1], [0, "d2fG/CkBhN7YxR6vOPdTGJ", 1], [5, 140, 186], [177, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-116, -117], [[[9, 1, 0, -114, [40], 41], -115], 4, 1], [0, "d3gFlR3mJKE4qOvT6R5CiW", 1], [5, 140, 186], [355, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-120, -121], [[[9, 1, 0, -118, [44], 45], -119], 4, 1], [0, "78YTRwbrdOz5dQ4ePRXG2z", 1], [5, 140, 186], [-357, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-124, -125], [[[9, 1, 0, -122, [48], 49], -123], 4, 1], [0, "2dJkhuh5tLm7AuWajP3A6Z", 1], [5, 140, 186], [-179, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-128, -129], [[[9, 1, 0, -126, [52], 53], -127], 4, 1], [0, "58jf4z219ODq4+dPVTXOTq", 1], [5, 140, 186], [-1, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-132, -133], [[[9, 1, 0, -130, [56], 57], -131], 4, 1], [0, "d4LB8hAklHAoOEdHOV6LhH", 1], [5, 140, 186], [177, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "button", 4, [-136, -137], [[[9, 1, 0, -134, [60], 61], -135], 4, 1], [0, "54jvyq/WJElLl+OE53hrv9", 1], [5, 140, 186], [355, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "layout", 3, [-139, -140, -141], [[46, -138]], [0, "fbo3JAklJBbLPwabR0JFld", 1], [5, 790, 440], [0, 68, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "button", 18, [-143, -144], [-142], [0, "409j+Pi6RA3bf+/3IgsRVl", 1], [5, 250, 550], [-280, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "button", 18, [-146, -147], [-145], [0, "9fE02tNgtGGL9LNm18JGw6", 1], [5, 250, 550]], [27, "button", 18, [-149, -150], [-148], [0, "dcAS7x9ZFHZ561/8pJpCWS", 1], [5, 250, 550], [262, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "sprite", 7, [-154], [[13, 2, -151, [89], 90], [47, 2, -153, [4, 4294967295], [4, 4294967295], -152, 91, 92, 93, 94]], [0, "01yAugVy1PrJ8PsDfs4004", 1], [5, 239, 104], [-372, -292, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btnQuickPlay", 7, [[[19, 2, false, -155, [95], 96], -156], 4, 1], [0, "611BDIjahMd7W5LeQfMWHc", 1], [5, 163, 115], [403.3, -286.3, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "black", 0, 1, [[13, 0, -157, [0], 1], [30, -158, [4, 4292269782]]], [0, "08BOhSLxhLXJuh+a/E+i9M", 1], [5, 3000, 3000]], [15, "BG_PanelS_half", 5, [-160], [[13, 0, -159, [4], 5]], [0, "a7OsJlzfNKS4xWc5Qgp13j", 1], [5, 381, 416], [-153.065, 15.793, 0, 0, 0, 0, 1, 0.888, 0.888, 0.888]], [18, "black", 0, 5, [[13, 0, -161, [6], 7], [21, -162, [[17, "09052Zq0oVJl59gzr1ZolUq", "startClicked", 5]], [4, 4292269782]]], [0, "53lxsVKc5FWpI/OT/oMgFt", 1], [5, 3000, 3000]], [25, "label", 5, [[31, "<PERSON><PERSON><PERSON> mừng bạn đã trúng\nBONUS GAME", 32, 50, false, 1, 1, -163, [8], 9], [32, 1.2, -164, [4, 4286228947]]], [0, "13wjIgf7xBKqm09q4okoHb", 1], [5, 359.2, 100]], [5, "bgValue", false, 8, [-166], [[11, -165, 21]], [0, "10osUbMqxI1ocLWx3dAI9N", 1], [5, 140, 55]], [8, "Label", 28, [[[12, 50, false, false, 1, 1, -167, 20], -168], 4, 1], [0, "7fr25aVA1PFpavxQjgZh73", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 9, [-170], [[11, -169, 25]], [0, "b2jtXbXnFNaK8a6tByiPho", 1], [5, 140, 55]], [8, "Label", 30, [[[12, 50, false, false, 1, 1, -171, 24], -172], 4, 1], [0, "d8l78cIfxCZLVCnYVpvgrR", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 10, [-174], [[11, -173, 29]], [0, "970Tofsn1NtpR7rZevDX/N", 1], [5, 140, 55]], [8, "Label", 32, [[[12, 50, false, false, 1, 1, -175, 28], -176], 4, 1], [0, "9dwoaJAFhLGLKi9UtUq0iw", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 11, [-178], [[11, -177, 33]], [0, "cfKSB8+MVPdZ+3P+Hzs/WJ", 1], [5, 140, 55]], [8, "Label", 34, [[[12, 50, false, false, 1, 1, -179, 32], -180], 4, 1], [0, "abGhWdTLZNVI3KKwCBDgMX", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 12, [-182], [[14, -181, [38], 39]], [0, "8705d7wndDXqpqKzJMuSvv", 1], [5, 140, 55]], [8, "Label", 36, [[[51, 50, false, false, 1, 1, -183, [36], 37], -184], 4, 1], [0, "3dEXyKVlZN3bnjAgm2vKjb", 1], [4, 4287823615], [5, 0, 63]], [5, "bgValue", false, 13, [-186], [[11, -185, 43]], [0, "fdW3y8dIlCaI7+f5p4auM/", 1], [5, 140, 55]], [8, "Label", 38, [[[12, 50, false, false, 1, 1, -187, 42], -188], 4, 1], [0, "72Yq05hm1PbqvXt8aWID4N", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 14, [-190], [[11, -189, 47]], [0, "f4LkSZZHpPp4GZ4Wcko9mK", 1], [5, 140, 55]], [8, "Label", 40, [[[12, 50, false, false, 1, 1, -191, 46], -192], 4, 1], [0, "6cLRWaR/NPKLOpBGw3pJFM", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 15, [-194], [[11, -193, 51]], [0, "e7XWEWsU9ICaiz4tMRU1Ru", 1], [5, 140, 55]], [8, "Label", 42, [[[12, 50, false, false, 1, 1, -195, 50], -196], 4, 1], [0, "1bwhLUKLdBXq9NayOD7B0D", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 16, [-198], [[11, -197, 55]], [0, "bewz0dibhFirPO/ASJNPR+", 1], [5, 140, 55]], [8, "Label", 44, [[[12, 50, false, false, 1, 1, -199, 54], -200], 4, 1], [0, "14QAnBRclPH5DELbMgoaLK", 1], [4, 4287823615], [5, 0, 50]], [5, "bgValue", false, 17, [-202], [[11, -201, 59]], [0, "332mavP6xAhL1wVWQiJBbh", 1], [5, 140, 55]], [8, "Label", 46, [[[12, 50, false, false, 1, 1, -203, 58], -204], 4, 1], [0, "6fTFkpK1hNarluMAQSKmXA", 1], [4, 4287823615], [5, 0, 50]], [15, "Rectangle", 7, [-206], [[14, -205, [85], 86]], [0, "56HlaWkE5EwrTTerNPdCrI", 1], [5, 513, 104], [76, -292, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "lbTime", 48, [[-207, [3, -208]], 1, 4], [0, "c2gLra5axHso5k7exXa987", 1], [5, 443.8, 50], [0, 0, 0.5], [-218, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "lbWin", 22, [[[52, "0", 32, 50, false, false, 1, 1, -209, [87], 88], -210], 4, 1], [0, "8fkCqaEidLKLcFQIciXzVj", 1], [5, 19.2, 50], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "BG_PanelS_half", 6, [-212], [[13, 0, -211, [99], 100]], [0, "48gXniaZNKMqZyNPWT/+La", 1], [5, 381, 416], [-168.321, 48.212, 0, 0, 0, 0, 1, 0.877, 0.877, 0.877]], [18, "black", 0, 6, [[13, 0, -213, [101], 102], [21, -214, [[17, "7269ege3OpEV4lKd6fET93f", "closeClicked", 6]], [4, 4292269782]]], [0, "eaXLAobbZDq4P8eu+XPTa3", 1], [5, 3000, 3000]], [6, "label", 6, [[31, "<PERSON><PERSON><PERSON> mừng bạn đã được", 34, 50, false, 1, 1, -215, [103], 104], [32, 1.2, -216, [4, 4286228947]]], [0, "0eRtZuSZ9LZq4Lbcsfbg7Q", 1], [5, 377.4, 50], [0, 76, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "BG_PanelS_half", 25, [[13, 0, -217, [2], 3]], [0, "04MBuFiZ9DZpL/aVZ1oLxj", 1], [5, -397, 416], [388, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "backgrund loby ", 2, [[19, 2, false, -218, [12], 13]], [0, "ddXzg5Q65LJ4V0ZBZqUOdV", 1], [5, 872, 478], [0, 22, 0, 0, 0, 0, 1, 1.05, 1, 1.05]], [6, "khung1", 2, [[42, false, -219, [14], 15]], [0, "bfD0CEoKdBDIo2fw9L3ReX", 1], [5, 1010, 589], [0, 65, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "bonus", 2, [[14, -220, [16], 17]], [0, "8ayJLJCwVLMaEnmv475ED9", 1], [5, 191, 52], [0, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "chumvang", 2, [[55, "default", "animation60", 0, "animation60", -221, [18], 19]], [0, "21yQGP+xBJRY53mSMgFSd+", 1], [5, 189.17, 167.22], [0, 337.369, 0, 0, 0, 0, 1, 1, 1, 1]], [3, 29], [1, "skeleton", false, 8, [-222], [0, "96451XiHxHgbCJnjxkGTlR", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 1]], [10, "lat bai", false, false, 60], [4, 1.05, 3, 8, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "1", 2]], [4, 4294967295], [4, 4294967295], 8], [3, 31], [1, "skeleton", false, 9, [-223], [0, "52l/klZ3NDJ61tp2MaiF3g", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 64], [4, 1.05, 3, 9, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "2", 2]], [4, 4294967295], [4, 4294967295], 9], [3, 33], [1, "skeleton", false, 10, [-224], [0, "8cTkP3QLVGxLoGBss9tZxh", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 68], [4, 1.05, 3, 10, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "3", 2]], [4, 4294967295], [4, 4294967295], 10], [3, 35], [1, "skeleton", false, 11, [-225], [0, "99Fonruh1KYqrygTef9eyS", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 72], [4, 1.05, 3, 11, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "4", 2]], [4, 4294967295], [4, 4294967295], 11], [3, 37], [1, "skeleton", false, 12, [-226], [0, "62yaB/bD1FnoDQtMpUgT7L", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 76], [4, 1.05, 3, 12, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "5", 2]], [4, 4294967295], [4, 4294967295], 12], [3, 39], [1, "skeleton", false, 13, [-227], [0, "5aPskdxyxNMYCLFnubexQf", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 80], [4, 1.05, 3, 13, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "6", 2]], [4, 4294967295], [4, 4294967295], 13], [3, 41], [1, "skeleton", false, 14, [-228], [0, "95YwPgebtG/LnXdYLu0sBf", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 84], [4, 1.05, 3, 14, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "7", 2]], [4, 4294967295], [4, 4294967295], 14], [3, 43], [1, "skeleton", false, 15, [-229], [0, "12PUS+2pxG25aYRdArPG5B", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 88], [4, 1.05, 3, 15, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "8", 2]], [4, 4294967295], [4, 4294967295], 15], [3, 45], [1, "skeleton", false, 16, [-230], [0, "73jVdP9/pOOZUewngSBa/o", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 92], [4, 1.05, 3, 16, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "9", 2]], [4, 4294967295], [4, 4294967295], 16], [3, 47], [1, "skeleton", false, 17, [-231], [0, "992xae2t5GF6lCk7XOzRg/", 1], [5, 175, 232], [0, 0, 0, 0, 0, 0, 1, 0.802, 0.802, 0.802]], [10, "lat bai", false, false, 96], [4, 1.05, 3, 17, [[2, "ef6a1RSJzxDyavLJFcERFeM", "pickClicked", "10", 2]], [4, 4294967295], [4, 4294967295], 17], [6, "bg-in", 3, [[14, -232, [66], 67]], [0, "c5+O2ZMM9Bh7/yCYuz5rO1", 1], [5, 2004, 1127], [0, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "bg-on", 3, [[14, -233, [68], 69]], [0, "df/+F+FeFJ/ItQ+aqWrtW/", 1], [5, 1280, 720]], [1, "x1", false, 19, [-234], [0, "55IhIQgQ1I9IWDif5CSvgx", 1], [5, 92, 96], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [28, 101], [16, "close", 19, [-235], [0, "e4XRUa8h9NEatlZYQrPIBd", 1], [5, 291, 600], [0, -1.2, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 2, false, 103, [70]], [22, 19, [[2, "fa35bHjksRPqKrWumsBm7ik", "pickClicked", "1", 3]], [4, 4294967295], [4, 4294967295], 19], [1, "x1", false, 20, [-236], [0, "16i1uSyoFOUJht0/+7kU3J", 1], [5, 92, 96], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [28, 106], [16, "close", 20, [-237], [0, "8ei0m/m15Mup8D7cSDS+Xz", 1], [5, 291, 600], [0, -1.2, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 2, false, 108, [71]], [22, 20, [[2, "fa35bHjksRPqKrWumsBm7ik", "pickClicked", "2", 3]], [4, 4294967295], [4, 4294967295], 20], [1, "x1", false, 21, [-238], [0, "4371hZQ6FNPaMlmDTPvd8M", 1], [5, 91, 96], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 111, [72]], [16, "close", 21, [-239], [0, "36yxT97A9AIbvl5X1CN16t", 1], [5, 291, 600], [0, -1.2, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 2, false, 113, [73]], [22, 21, [[2, "fa35bHjksRPqKrWumsBm7ik", "pickClicked", "3", 3]], [4, 4294967295], [4, 4294967295], 21], [36, "bg-on", 3, [[14, -240, [74], 75]], [0, "f8xPcl+oxH653WQhIHxTf8", 1], [5, 1280, 720], [0, 719, 0, 0, 0, 6.123233995736766e-17, 1, -1, 1, 1], [1, 180, 180, 7.016709298534876e-15]], [6, "bg-on", 3, [[14, -241, [76], 77]], [0, "a9F/dnFuZJqbp986BHGipG", 1], [5, 1280, 720], [-1280, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [6, "bg-on", 3, [[14, -242, [78], 79]], [0, "4bfqqOn9dObIsiL8rl1BXO", 1], [5, 1280, 720], [1280, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [53, "T<PERSON> động chơi Bonus Game sau 10s", 28, 50, false, false, 1, 49, [84]], [3, 50], [4, 1.05, 3, 23, [[17, "3583bXj5HBNNrBb4UiPP0yG", "quickPlayClicked", 1]], [4, 4294967295], [4, 4294967295], 23], [6, "BG_PanelS_half", 51, [[13, 0, -243, [97], 98]], [0, "87TaF6TDNL8YUw5vU2EZO2", 1], [5, -397, 416], [388, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbWin", 6, [-244], [0, "c0RhW7G2hAxIEAuWwNGXUM", 1], [5, 38, 50], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "0", 32, 50, false, 1, 1, 123, [105]]], 0, [0, 7, 1, 0, 8, 121, 0, 9, 120, 0, 10, 119, 0, 11, 6, 0, 12, 3, 0, 13, 2, 0, 14, 7, 0, 15, 5, 0, 0, 1, 0, -1, 24, 0, -2, 5, 0, -3, 7, 0, 0, 2, 0, -1, 61, 0, -2, 65, 0, -3, 69, 0, -4, 73, 0, -5, 77, 0, -6, 81, 0, -7, 85, 0, -8, 89, 0, -9, 93, 0, -10, 97, 0, -1, 59, 0, -2, 63, 0, -3, 67, 0, -4, 71, 0, -5, 75, 0, -6, 79, 0, -7, 83, 0, -8, 87, 0, -9, 91, 0, -10, 95, 0, -1, 62, 0, -2, 66, 0, -3, 70, 0, -4, 74, 0, -5, 78, 0, -6, 82, 0, -7, 86, 0, -8, 90, 0, -9, 94, 0, -10, 98, 0, 0, 2, 0, 0, 2, 0, -1, 55, 0, -2, 56, 0, -3, 57, 0, -4, 58, 0, -5, 4, 0, -1, 102, 0, -2, 107, 0, -3, 112, 0, -1, 104, 0, -2, 109, 0, -3, 114, 0, -1, 105, 0, -2, 110, 0, -3, 115, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 99, 0, -2, 100, 0, -3, 18, 0, -4, 116, 0, -5, 117, 0, -6, 118, 0, 0, 4, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, -6, 13, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, -10, 17, 0, 0, 5, 0, 0, 5, 0, 16, 5, 0, 0, 5, 0, -1, 25, 0, -2, 26, 0, -3, 27, 0, 0, 6, 0, 17, 124, 0, 0, 6, 0, -1, 51, 0, -2, 52, 0, -3, 53, 0, -4, 123, 0, -3, 48, 0, -4, 22, 0, -5, 23, 0, 0, 8, 0, -2, 62, 0, -1, 28, 0, -2, 60, 0, 0, 9, 0, -2, 66, 0, -1, 30, 0, -2, 64, 0, 0, 10, 0, -2, 70, 0, -1, 32, 0, -2, 68, 0, 0, 11, 0, -2, 74, 0, -1, 34, 0, -2, 72, 0, 0, 12, 0, -2, 78, 0, -1, 36, 0, -2, 76, 0, 0, 13, 0, -2, 82, 0, -1, 38, 0, -2, 80, 0, 0, 14, 0, -2, 86, 0, -1, 40, 0, -2, 84, 0, 0, 15, 0, -2, 90, 0, -1, 42, 0, -2, 88, 0, 0, 16, 0, -2, 94, 0, -1, 44, 0, -2, 92, 0, 0, 17, 0, -2, 98, 0, -1, 46, 0, -2, 96, 0, 0, 18, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, -1, 105, 0, -1, 101, 0, -2, 103, 0, -1, 110, 0, -1, 106, 0, -2, 108, 0, -1, 115, 0, -1, 111, 0, -2, 113, 0, 0, 22, 0, 18, 22, 0, 0, 22, 0, -1, 50, 0, 0, 23, 0, -2, 121, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, -1, 54, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, -1, 29, 0, 0, 29, 0, -2, 59, 0, 0, 30, 0, -1, 31, 0, 0, 31, 0, -2, 63, 0, 0, 32, 0, -1, 33, 0, 0, 33, 0, -2, 67, 0, 0, 34, 0, -1, 35, 0, 0, 35, 0, -2, 71, 0, 0, 36, 0, -1, 37, 0, 0, 37, 0, -2, 75, 0, 0, 38, 0, -1, 39, 0, 0, 39, 0, -2, 79, 0, 0, 40, 0, -1, 41, 0, 0, 41, 0, -2, 83, 0, 0, 42, 0, -1, 43, 0, 0, 43, 0, -2, 87, 0, 0, 44, 0, -1, 45, 0, 0, 45, 0, -2, 91, 0, 0, 46, 0, -1, 47, 0, 0, 47, 0, -2, 95, 0, 0, 48, 0, -1, 49, 0, -1, 119, 0, 0, 49, 0, 0, 50, 0, -2, 120, 0, 0, 51, 0, -1, 122, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, -1, 61, 0, -1, 65, 0, -1, 69, 0, -1, 73, 0, -1, 77, 0, -1, 81, 0, -1, 85, 0, -1, 89, 0, -1, 93, 0, -1, 97, 0, 0, 99, 0, 0, 100, 0, -1, 102, 0, -1, 104, 0, -1, 107, 0, -1, 109, 0, -1, 112, 0, -1, 114, 0, 0, 116, 0, 0, 117, 0, 0, 118, 0, 0, 122, 0, -1, 124, 0, 19, 1, 2, 6, 7, 3, 6, 7, 6, 6, 7, 244], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 62, 65, 66, 69, 70, 73, 74, 77, 78, 81, 82, 85, 86, 89, 90, 93, 94, 97, 98, 102, 104, 107, 109, 112, 114, 119, 121, 124], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 5, 2, 1, -1, 1, 2, 1, -1, 1, 2, 1, -1, 1, 2, 1, -1, 1, -1, 2, -1, 1, -1, 1, 2, 1, -1, 1, 2, 1, -1, 1, 2, 1, -1, 1, 2, 1, -1, 1, 2, 1, -1, 1, -1, 1, 20, 21, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -2, -3, 22, -1, -1, 1, -1, 2, -1, 1, 4, 23, 24, 25, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, 1, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 1, 1, 1, 1, 1, 1, 2, 4, 2], [0, 9, 0, 5, 0, 5, 0, 9, 0, 6, 0, 13, 0, 14, 0, 15, 0, 16, 17, 18, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 2, 0, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 2, 3, 0, 1, 0, 19, 20, 21, 0, 22, 0, 7, 0, 0, 0, 0, 0, 7, 0, 7, 0, 7, 8, 23, 24, 25, 0, 0, 26, 0, 6, 0, 11, 11, 27, 28, 29, 0, 12, 0, 5, 0, 5, 0, 9, 0, 6, 0, 0, 30, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 8, 10, 8, 10, 8, 10, 6, 12, 31]], [[{"name": "popup_bg2", "rect": [0, 0, 489, 297], "offset": [0, 0], "originalSize": [489, 297], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [32]], [[{"name": "Rounded Rectangle 2", "rect": [0, 0, 513, 104], "offset": [0, 0], "originalSize": [513, 104], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [33]], [[{"name": "x3", "rect": [1, 0, 91, 94], "offset": [0.5, 0], "originalSize": [92, 94], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [34]], [[{"name": "close", "rect": [0, 11, 284, 585], "offset": [-3.5, -3.5], "originalSize": [291, 600], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [35]], [[{"name": "open", "rect": [2, 1, 287, 595], "offset": [0, 1.5], "originalSize": [291, 600], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [36]], [[{"name": "bonus", "rect": [0, 0, 191, 52], "offset": [0, 0], "originalSize": [191, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [37]], [[{"name": "x2 ", "rect": [1, 0, 88, 91], "offset": [0.5, 0], "originalSize": [89, 91], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [38]], [[{"name": "bg-on", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [39]], [[{"name": "trung copy", "rect": [0, 0, 142, 188], "offset": [0, 0], "originalSize": [142, 188], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [40]], [[[56, "chumvang", "\nchumvang.png\nsize: 485,195\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nchum\n  rotate: false\n  xy: 209, 63\n  size: 180, 130\n  orig: 180, 130\n  offset: 0, 0\n  index: -1\nchum_mieng\n  rotate: false\n  xy: 2, 4\n  size: 121, 18\n  orig: 121, 18\n  offset: 0, 0\n  index: -1\nlight\n  rotate: false\n  xy: 2, 24\n  size: 205, 169\n  orig: 205, 169\n  offset: 0, 0\n  index: -1\nnuivang\n  rotate: true\n  xy: 391, 26\n  size: 167, 92\n  orig: 167, 92\n  offset: 0, 0\n  index: -1\nstar\n  rotate: true\n  xy: 209, 2\n  size: 59, 71\n  orig: 59, 71\n  offset: 0, 0\n  index: -1\n", ["chumvang.png"], {"skeleton": {"hash": "Kci/DbXzwdRPg5N2djPqx5TH7Cg", "spine": "3.7.94", "width": 189.17, "height": 167.22}, "bones": [{"name": "root", "y": -19.08}, {"name": "chum", "parent": "root", "y": -36.43, "scaleX": 0.758, "scaleY": 0.758}, {"name": "star", "parent": "chum", "x": 84.49, "y": 102.01}, {"name": "star2", "parent": "chum", "x": 37.79, "y": 137.85}, {"name": "star3", "parent": "chum", "x": -49.63, "y": 135.13}, {"name": "star4", "parent": "chum", "x": -15.96, "y": 155.22}, {"name": "light", "parent": "chum", "x": 2.8, "y": 112.02}, {"name": "nuivang", "parent": "chum", "x": 2.74, "y": 111.32}, {"name": "star6", "parent": "chum", "x": 10.36, "y": 160.84}, {"name": "star7", "parent": "chum", "rotation": -40.94, "x": 52.78, "y": 8.52}, {"name": "star5", "parent": "chum", "x": -50.01, "y": 114.61, "scaleX": 0.766, "scaleY": 0.656}, {"name": "star8", "parent": "chum", "x": -65.52, "y": 73.16, "scaleX": 0.917, "scaleY": 0.917}], "slots": [{"name": "chum_mieng", "bone": "chum", "attachment": "chum_mieng"}, {"name": "chum", "bone": "light", "color": "ffffff00", "attachment": "light"}, {"name": "nuivang", "bone": "nuivang", "attachment": "nuivang"}, {"name": "chum 2", "bone": "chum", "attachment": "chum"}, {"name": "star", "bone": "star"}, {"name": "star5", "bone": "star5"}, {"name": "star8", "bone": "star8"}, {"name": "star2", "bone": "star2"}, {"name": "star3", "bone": "star3"}, {"name": "star4", "bone": "star4"}, {"name": "star6", "bone": "star6"}, {"name": "star7", "bone": "star7"}], "skins": {"default": {"chum": {"light": {"x": -5.87, "y": 1.17, "scaleX": 1.218, "scaleY": 1.218, "width": 205, "height": 169}}, "chum 2": {"chum": {"y": 60.4, "width": 180, "height": 130}}, "chum_mieng": {"chum_mieng": {"x": 0.72, "y": 120.07, "width": 121, "height": 18}}, "nuivang": {"nuivang": {"type": "mesh", "hull": 4, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [64.11, -4.47, -64.33, -4.47, -64.33, 59.65, 64.11, 59.65]}}, "star": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}, "star2": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}, "star3": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}, "star4": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}, "star5": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}, "star6": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}, "star7": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}, "star8": {"star": {"x": -4.26, "y": 1.83, "width": 59, "height": 71}}}}, "animations": {"animation20": {"slots": {"chum": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1, "color": "ffffff7c"}, {"time": 2, "color": "ffffff00"}]}, "chum 2": {"color": [{"time": 1.3333, "color": "ffffffff"}]}, "nuivang": {"attachment": [{"time": 0, "name": "nuivang"}]}, "star": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star2": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "star"}]}, "star3": {"color": [{"time": 0.4667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "star"}]}, "star4": {"color": [{"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "star"}]}, "star5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star6": {"color": [{"time": 1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "color": "ffffff00"}], "attachment": [{"time": 1, "name": "star"}]}, "star7": {"color": [{"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}], "attachment": [{"time": 1.2, "name": "star"}]}, "star8": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "star"}]}}, "bones": {"chum": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.963, "y": 0.964, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "nuivang": {"translate": [{"time": 0, "x": 0, "y": -38.01}]}, "star": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7, "angle": -54.53}]}, "star4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.9333, "angle": -56.67}], "translate": [{"time": 0.1667, "x": -8.01, "y": -43.18}]}, "star3": {"rotate": [{"time": 0.4667, "angle": 0}, {"time": 1.3, "angle": -24.05}], "translate": [{"time": 0.4667, "x": 36.35, "y": -5.64}]}, "star2": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1.4333, "angle": -43.3}], "translate": [{"time": 0.6667, "x": -3.88, "y": -16.15}]}, "star6": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.7667, "angle": -56.67}], "translate": [{"time": 1, "x": -0.72, "y": -32.75}]}, "star7": {"rotate": [{"time": 1.2, "angle": 0}, {"time": 1.9667, "angle": -56.67}]}, "light": {"translate": [{"time": 0, "x": 0, "y": -14.59}], "scale": [{"time": 0, "x": 0.552, "y": 0.552}, {"time": 1, "x": 0.774, "y": 0.774}, {"time": 2, "x": 0.844, "y": 0.844}]}, "star5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -81.73}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7, "x": 45.36, "y": -3.26}, {"time": 1.0333, "x": 68.95, "y": -3.51}, {"time": 1.3667, "x": 98.57, "y": 0}]}, "star8": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -81.73}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.6, "y": -8.47}, {"time": 1.0333, "x": 45.36, "y": -13.74}, {"time": 1.3667, "x": 68.95, "y": -15.6}, {"time": 1.7, "x": 100.7, "y": -12.82}, {"time": 1.8667, "x": 121.83, "y": -6.95}]}}, "deform": {"default": {"nuivang": {"nuivang": [{"time": 0, "vertices": [-6.63698, 6.76, -0.84362, 6.76, -2.83964, 6.76, -3.03292, 6.76]}]}}}}, "animation40": {"slots": {"chum": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1, "color": "ffffff7c"}, {"time": 2, "color": "ffffff00"}]}, "nuivang": {"attachment": [{"time": 0, "name": "nuivang"}]}, "star": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star2": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "star"}]}, "star3": {"color": [{"time": 0.4667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "star"}]}, "star4": {"color": [{"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "star"}]}, "star5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star6": {"color": [{"time": 1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "color": "ffffff00"}], "attachment": [{"time": 1, "name": "star"}]}, "star7": {"color": [{"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}], "attachment": [{"time": 1.2, "name": "star"}]}, "star8": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "star"}]}}, "bones": {"chum": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.963, "y": 0.964, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "nuivang": {"translate": [{"time": 0, "x": 0, "y": -34.48}]}, "star": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7, "angle": -54.53}]}, "star4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.9333, "angle": -56.67}], "translate": [{"time": 0.1667, "x": -8.01, "y": -43.18}]}, "star3": {"rotate": [{"time": 0.4667, "angle": 0}, {"time": 1.3, "angle": -24.05}], "translate": [{"time": 0.4667, "x": 36.35, "y": -5.64}]}, "star2": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1.4333, "angle": -43.3}], "translate": [{"time": 0.6667, "x": -3.88, "y": -16.15}]}, "star6": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.7667, "angle": -56.67}], "translate": [{"time": 1, "x": -0.72, "y": -32.75}]}, "star7": {"rotate": [{"time": 1.2, "angle": 0}, {"time": 1.9667, "angle": -56.67}]}, "light": {"translate": [{"time": 0, "x": 0, "y": -5.11}], "scale": [{"time": 0, "x": 0.552, "y": 0.552}, {"time": 1, "x": 0.774, "y": 0.774}, {"time": 2, "x": 0.844, "y": 0.844}]}, "star5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -81.73}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7, "x": 45.36, "y": -3.26}, {"time": 1.0333, "x": 68.95, "y": -3.51}, {"time": 1.3667, "x": 98.57, "y": 0}]}, "star8": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -81.73}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.6, "y": -8.47}, {"time": 1.0333, "x": 45.36, "y": -13.74}, {"time": 1.3667, "x": 68.95, "y": -15.6}, {"time": 1.7, "x": 100.7, "y": -12.82}, {"time": 1.8667, "x": 121.83, "y": -6.95}]}}, "deform": {"default": {"nuivang": {"nuivang": [{"time": 0, "vertices": [-7.36008, 14.19595, 0.98485, 14.19594, -17.86198, 14.19597, 7.78597, 14.19597]}]}}}}, "animation60": {"slots": {"chum": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1, "color": "ffffff7c"}, {"time": 2, "color": "ffffff00"}]}, "nuivang": {"attachment": [{"time": 0, "name": "nuivang"}]}, "star": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star2": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "star"}]}, "star3": {"color": [{"time": 0.4667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "star"}]}, "star4": {"color": [{"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "star"}]}, "star5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star6": {"color": [{"time": 1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "color": "ffffff00"}], "attachment": [{"time": 1, "name": "star"}]}, "star7": {"color": [{"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}], "attachment": [{"time": 1.2, "name": "star"}]}, "star8": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "star"}]}}, "bones": {"chum": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.963, "y": 0.964, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "nuivang": {"translate": [{"time": 0, "x": 0, "y": -28.92}]}, "star": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7, "angle": -54.53}]}, "star4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.9333, "angle": -56.67}], "translate": [{"time": 0.1667, "x": -8.01, "y": -43.18}]}, "star3": {"rotate": [{"time": 0.4667, "angle": 0}, {"time": 1.3, "angle": -24.05}], "translate": [{"time": 0.4667, "x": 36.35, "y": -5.64}]}, "star2": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1.4333, "angle": -43.3}], "translate": [{"time": 0.6667, "x": -3.88, "y": -16.15}]}, "star6": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.7667, "angle": -56.67}], "translate": [{"time": 1, "x": -0.72, "y": -32.75}]}, "star7": {"rotate": [{"time": 1.2, "angle": 0}, {"time": 1.9667, "angle": -56.67}]}, "light": {"translate": [{"time": 0, "x": 0, "y": 5.04}], "scale": [{"time": 0, "x": 0.552, "y": 0.552}, {"time": 1, "x": 0.774, "y": 0.774}, {"time": 2, "x": 0.844, "y": 0.844}]}, "star5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -81.73}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7, "x": 45.36, "y": -3.26}, {"time": 1.0333, "x": 68.95, "y": -3.51}, {"time": 1.3667, "x": 98.57, "y": 0}]}, "star8": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -81.73}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.6, "y": -8.47}, {"time": 1.0333, "x": 45.36, "y": -13.74}, {"time": 1.3667, "x": 68.95, "y": -15.6}, {"time": 1.7, "x": 100.7, "y": -12.82}, {"time": 1.8667, "x": 121.83, "y": -6.95}]}}, "deform": {"default": {"nuivang": {"nuivang": [{"time": 0, "vertices": [-1.82795, 17.57599, -1.66597, 17.57599, -10.99198, 17.57599, 0, 17.57599]}]}}}}, "animation80": {"slots": {"chum": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1, "color": "ffffff7c"}, {"time": 2, "color": "ffffff00"}]}, "nuivang": {"attachment": [{"time": 0, "name": "nuivang"}]}, "star": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star2": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "star"}]}, "star3": {"color": [{"time": 0.4667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "star"}]}, "star4": {"color": [{"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "star"}]}, "star5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star6": {"color": [{"time": 1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "color": "ffffff00"}], "attachment": [{"time": 1, "name": "star"}]}, "star7": {"color": [{"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}], "attachment": [{"time": 1.2, "name": "star"}]}, "star8": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "star"}]}}, "bones": {"chum": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.963, "y": 0.964, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "nuivang": {"translate": [{"time": 0, "x": -2.12, "y": -14.09}]}, "star": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7, "angle": -54.53}]}, "star4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.9333, "angle": -56.67}], "translate": [{"time": 0.1667, "x": -12.19, "y": -39.35}]}, "star3": {"rotate": [{"time": 0.4667, "angle": 0}, {"time": 1.3, "angle": -24.05}], "translate": [{"time": 0.4667, "x": 36.35, "y": -5.64}]}, "star2": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1.4333, "angle": -43.3}], "translate": [{"time": 0.6667, "x": -3.88, "y": -16.15}]}, "star6": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.7667, "angle": -56.67}], "translate": [{"time": 0.1667, "x": -25.77, "y": -11.84}, {"time": 1, "x": -0.72, "y": -12.6}]}, "star7": {"rotate": [{"time": 1.2, "angle": 0}, {"time": 1.9667, "angle": -56.67}]}, "light": {"translate": [{"time": 0, "x": 0, "y": 12.49}], "scale": [{"time": 0, "x": 0.552, "y": 0.552}, {"time": 1, "x": 0.774, "y": 0.774}, {"time": 2, "x": 0.844, "y": 0.844}]}, "star5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -81.73}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7, "x": 45.36, "y": -3.26}, {"time": 1.0333, "x": 68.95, "y": -3.51}, {"time": 1.3667, "x": 98.57, "y": 0}]}, "star8": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -81.73}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.6, "y": -8.47}, {"time": 1.0333, "x": 45.36, "y": -13.74}, {"time": 1.3667, "x": 68.95, "y": -15.6}, {"time": 1.7, "x": 100.7, "y": -12.82}, {"time": 1.8667, "x": 121.83, "y": -6.95}]}}, "deform": {"default": {"nuivang": {"nuivang": [{"time": 0, "vertices": [-2.02893, 6.08402, 3.98401, 7.84504, -7.78599, 10.14001, 4.57997, 10.14001]}]}}}}, "animation100": {"slots": {"chum": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1, "color": "ffffff7c"}, {"time": 2, "color": "ffffff00"}]}, "nuivang": {"attachment": [{"time": 0, "name": "nuivang"}]}, "star": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star2": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "star"}]}, "star3": {"color": [{"time": 0.4667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "star"}]}, "star4": {"color": [{"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "star"}]}, "star5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "star"}]}, "star6": {"color": [{"time": 1, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "color": "ffffff00"}], "attachment": [{"time": 1, "name": "star"}]}, "star7": {"color": [{"time": 1.2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "color": "ffffffba", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "color": "ffffff00"}], "attachment": [{"time": 1.2, "name": "star"}]}, "star8": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "star"}]}}, "bones": {"chum": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.963, "y": 0.964, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "star": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7, "angle": -54.53}]}, "star2": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 1.4333, "angle": -43.3}], "translate": [{"time": 0.6667, "x": 0.04, "y": 2.37}]}, "star3": {"rotate": [{"time": 0.4667, "angle": 0}, {"time": 1.3, "angle": -24.05}]}, "star4": {"rotate": [{"time": 0.1667, "angle": 0}, {"time": 0.9333, "angle": -56.67}]}, "nuivang": {"translate": [{"time": 0, "x": -2.12, "y": 1.98}]}, "star6": {"rotate": [{"time": 1, "angle": 0}, {"time": 1.7667, "angle": -56.67}]}, "star7": {"rotate": [{"time": 1.2, "angle": 0}, {"time": 1.9667, "angle": -56.67}]}, "light": {"translate": [{"time": 0, "x": 0, "y": 20.61}], "scale": [{"time": 0, "x": 0.608, "y": 0.608}, {"time": 1, "x": 0.835, "y": 0.835}, {"time": 2, "x": 1.085, "y": 1.085}]}, "star5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -81.73}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7, "x": 45.36, "y": -3.26}, {"time": 1.0333, "x": 68.95, "y": -3.51}, {"time": 1.3667, "x": 98.57, "y": 0}]}, "star8": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -81.73}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.6, "y": -8.47}, {"time": 1.0333, "x": 45.36, "y": -13.74}, {"time": 1.3667, "x": 68.95, "y": -15.6}, {"time": 1.7, "x": 100.7, "y": -12.82}, {"time": 1.8667, "x": 121.83, "y": -6.95}]}}, "deform": {"default": {"nuivang": {"nuivang": [{"time": 0, "vertices": [-1.56399, -2.532, 1.415, -0.97299, -9.17101, 11.44899, 2.19001, 13.466]}]}}}}}}, [0]]], 0, 0, [0], [-1], [41]], [[{"name": "bg-in", "rect": [0, 0, 2004, 1127], "offset": [0, 0], "originalSize": [2004, 1127], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [42]], [[{"name": "tien", "rect": [0, 0, 239, 104], "offset": [0, 0], "originalSize": [239, 104], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [43]], [[{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rect": [0, 0, 142, 188], "offset": [0, 0], "originalSize": [142, 188], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [44]], [[{"name": "bgValue", "rect": [0, 0, 140, 55], "offset": [0, 0], "originalSize": [140, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [45]], [[{"name": "popup_bg", "rect": [0, 0, 489, 297], "offset": [0, 0], "originalSize": [489, 297], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [46]], [[{"name": "x1", "rect": [1, 0, 91, 96], "offset": [0.5, 0], "originalSize": [92, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [47]]]]