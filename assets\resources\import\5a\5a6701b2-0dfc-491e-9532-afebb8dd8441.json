[1, ["43mUkWSytNgKGFukzadhII"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "avenger_quay_btn", "\r\nBUTTON SPIN.png\r\nsize: 684,389\r\nformat: RGBA8888\r\nfilter: Linear,Linear\r\nrepeat: none\r\nBACK\r\n  rotate: true\r\n  xy: 275, 16\r\n  size: 255, 77\r\n  orig: 255, 77\r\n  offset: 0, 0\r\n  index: -1\r\nBUTTON BLUE\r\n  rotate: false\r\n  xy: 2, 137\r\n  size: 271, 134\r\n  orig: 271, 134\r\n  offset: 0, 0\r\n  index: -1\r\nBUTTON RED\r\n  rotate: false\r\n  xy: 411, 253\r\n  size: 271, 134\r\n  orig: 271, 134\r\n  offset: 0, 0\r\n  index: -1\r\nD DOWN\r\n  rotate: false\r\n  xy: 2, 273\r\n  size: 378, 55\r\n  orig: 378, 55\r\n  offset: 0, 0\r\n  index: -1\r\nDECK UP\r\n  rotate: false\r\n  xy: 2, 330\r\n  size: 407, 57\r\n  orig: 407, 57\r\n  offset: 0, 0\r\n  index: -1\r\nGRAY BUTTON\r\n  rotate: false\r\n  xy: 2, 2\r\n  size: 271, 133\r\n  orig: 271, 133\r\n  offset: 0, 0\r\n  index: -1\r\nHOLD TO AUTO\r\n  rotate: true\r\n  xy: 657, 99\r\n  size: 152, 18\r\n  orig: 152, 18\r\n  offset: 0, 0\r\n  index: -1\r\nLIGHT\r\n  rotate: false\r\n  xy: 354, 94\r\n  size: 225, 157\r\n  orig: 225, 157\r\n  offset: 0, 0\r\n  index: -1\r\nLIGHT EFFECT\r\n  rotate: false\r\n  xy: 354, 17\r\n  size: 289, 75\r\n  orig: 289, 75\r\n  offset: 0, 0\r\n  index: -1\r\nSPIN\r\n  rotate: true\r\n  xy: 619, 125\r\n  size: 126, 36\r\n  orig: 126, 36\r\n  offset: 0, 0\r\n  index: -1\r\nSTOP\r\n  rotate: true\r\n  xy: 581, 117\r\n  size: 134, 36\r\n  orig: 134, 36\r\n  offset: 0, 0\r\n  index: -1\r\n", ["BUTTON SPIN.png"], {"skins": {"default": {"BUTTON BLUE": {"BUTTON BLUE": {"width": 271, "type": "mesh", "hull": 4, "height": 134, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 2, 135.52, -83.77, 1, 1, 2, -135.48, -83.77, 1, 1, 2, -135.48, 50.23, 1, 1, 2, 135.52, 50.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "D DOWN": {"D DOWN": {"width": 378, "type": "mesh", "hull": 4, "height": 55, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 1, 188.52, 54, 1, 1, 1, -189.48, 54, 1, 1, 1, -189.48, 109, 1, 1, 1, 188.52, 109, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "HOLD TO AUTO": {"HOLD TO AUTO": {"width": 152, "type": "mesh", "hull": 4, "height": 18, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [76, -37.97, -76, -37.97, -76, -19.97, 76, -19.97], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "STOP": {"STOP": {"width": 134, "type": "mesh", "hull": 4, "height": 36, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [65.52, -18.77, -68.48, -18.77, -68.48, 17.23, 65.52, 17.23], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "GRAY BUTTON": {"GRAY BUTTON": {"width": 271, "type": "mesh", "hull": 4, "height": 133, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [135.52, 0.5, -135.48, 0.5, -135.48, 133.5, 135.52, 133.5], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "LIGHT": {"LIGHT": {"width": 225, "type": "mesh", "hull": 4, "height": 157, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 3, 106.46, -79.77, 1, 1, 3, -118.54, -79.77, 1, 1, 3, -118.54, 77.23, 1, 1, 3, 106.46, 77.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "SPIN2": {"SPIN": {"width": 126, "type": "mesh", "hull": 4, "height": 36, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [63, -18.77, -63, -18.77, -63, 17.23, 63, 17.23], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BACK": {"BACK": {"width": 255, "type": "mesh", "hull": 4, "height": 77, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 1, 127.52, 14, 1, 1, 1, -127.48, 14, 1, 1, 1, -127.48, 91, 1, 1, 1, 127.52, 91, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "DECK UP": {"DECK UP": {"width": 407, "type": "mesh", "hull": 4, "height": 57, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 1, 203.52, 0, 1, 1, 1, -203.48, 0, 1, 1, 1, -203.48, 57, 1, 1, 1, 203.52, 57, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "BUTTON RED": {"BUTTON RED": {"width": 271, "type": "mesh", "hull": 4, "height": 134, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 2, 135.52, -83.77, 1, 1, 2, -135.48, -83.77, 1, 1, 2, -135.48, 50.23, 1, 1, 2, 135.52, 50.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "LIGHT EFFECT": {"LIGHT EFFECT": {"width": 289, "type": "mesh", "hull": 4, "height": 75, "triangles": [1, 2, 3, 1, 3, 0], "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "vertices": [1, 1, 144.52, 0, 1, 1, 1, -144.48, 0, 1, 1, 1, -144.48, 75, 1, 1, 1, 144.52, 75, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "MASK": {"MASK": {"color": "ce3a3aff", "end": "LIGHT", "type": "clipping", "vertexCount": 6, "vertices": [-116.66, 80.17, -88.38, 44.55, 87.95, 44.42, 116.89, 79.68, 85.71, 113.68, -85.73, 113.68]}}}}, "skeleton": {"images": "./BUTTON SPIN FIX/", "width": 407, "spine": "3.6.53", "hash": "74X544vQ75/DRWydZQJ3ZhptaNg", "height": 134}, "slots": [{"attachment": "BACK", "name": "BACK", "bone": "root"}, {"attachment": "D DOWN", "name": "D DOWN", "bone": "root"}, {"attachment": "BUTTON BLUE", "name": "BUTTON BLUE", "bone": "root"}, {"color": "ffffff00", "attachment": "BUTTON RED", "name": "BUTTON RED", "bone": "root"}, {"color": "ffffff00", "attachment": "GRAY BUTTON", "name": "GRAY BUTTON", "bone": "root"}, {"color": "ffffff00", "attachment": "LIGHT EFFECT", "blend": "additive", "name": "LIGHT EFFECT", "bone": "root"}, {"attachment": "DECK UP", "name": "DECK UP", "bone": "root"}, {"attachment": "SPIN", "name": "SPIN2", "bone": "BUTTON"}, {"name": "STOP", "bone": "BUTTON"}, {"name": "HOLD TO AUTO", "bone": "BUTTON"}, {"attachment": "MASK", "name": "MASK", "bone": "root"}, {"blend": "additive", "name": "LIGHT", "bone": "root"}], "bones": [{"name": "root"}, {"parent": "root", "name": "CENTER"}, {"parent": "CENTER", "name": "BUTTON", "y": 83.77}, {"parent": "BUTTON", "name": "LIGHT", "x": -178.86}], "animations": {"stop": {"slots": {"BUTTON BLUE": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 1.3333}]}, "STOP": {"attachment": [{"name": "STOP", "time": 0}, {"name": "STOP", "time": 1.3333}]}, "SPIN2": {"attachment": [{"name": null, "time": 0}, {"name": null, "time": 1.3333}]}, "BUTTON RED": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 1.3333}]}}, "bones": {"LIGHT": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 368.48, "y": 0, "time": 0.5}, {"x": 368.48, "y": 0, "time": 1.3333}]}}}, "spin": {"slots": {"HOLD TO AUTO": {"attachment": [{"name": "HOLD TO AUTO", "time": 0}, {"name": "HOLD TO AUTO", "time": 1.3333}]}}, "bones": {"LIGHT": {"translate": [{"x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 368.48, "y": 0, "time": 0.5}, {"x": 368.48, "y": 0, "time": 1.3333}]}}}, "stop_pressed": {"slots": {"BUTTON BLUE": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1}]}, "STOP": {"attachment": [{"name": "STOP", "time": 0}]}, "SPIN2": {"attachment": [{"name": null, "time": 0}]}, "BUTTON RED": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.1}]}, "LIGHT EFFECT": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.1}]}}, "bones": {"BUTTON": {"translate": [{"curve": "stepped", "x": 0, "y": -19.04, "time": 0}, {"x": 0, "y": -19.04, "time": 0.1}]}}}, "stop_disable": {"slots": {"BUTTON BLUE": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1667}]}, "STOP": {"attachment": [{"name": "STOP", "time": 0}, {"name": "STOP", "time": 0.1667}]}, "GRAY BUTTON": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 0.1667}]}, "SPIN2": {"attachment": [{"name": null, "time": 0}, {"name": null, "time": 0.1667}]}, "BUTTON RED": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.1667}]}}, "bones": {"LIGHT": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.1667}]}}}, "hold": {"slots": {"BUTTON BLUE": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 2.1667}]}, "HOLD TO AUTO": {"attachment": [{"name": "HOLD TO AUTO", "time": 0}, {"name": "HOLD TO AUTO", "time": 2.1667}]}, "BUTTON RED": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 1}, {"color": "ffffffff", "time": 2.1667}]}, "LIGHT EFFECT": {"color": [{"color": "ffffffff", "curve": "stepped", "time": 0}, {"color": "ffffffff", "time": 2.1667}]}}, "bones": {"BUTTON": {"translate": [{"curve": "stepped", "x": 0, "y": -19.04, "time": 0}, {"x": 0, "y": -19.04, "time": 2.1667}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]