[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdHIe7q1BD+o3g3g+0ROLn", "4258i28H5If7PWTT+RDkRA", "d82n49/IVAvIEqsa0xvvk0", "7a/QZLET9IDreTiBfRn2PD", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "9aruc+aM1K7Z7JgaauoqQo", "dduOXAqPtEOZlLjBszo4Aq", "a9VpD0DP5LJYQPXITZq+uj", "24xd2Xl+xHVZeWwPN10Wzf", "b2TsuZH79L9qDB8oMJcx5J", "7aUsxJWKtMLLAJwLGB4DOs", "26XBIiR41EpJSfFK/OQnr/", "c9jagUNyBP8aHy/wXfW/Py", "b9sAPrSq9Bhq4NL0/rodI/", "60lCKF0BFLRJpfGf89TIRK", "2cWB/vWPRHja3uQTinHH30", "b3HZR9f5VGhqOkF4XSu0ru", "9bYDnu5NdJH7u79ZpCFpEO", "62bPu8HstAhL0n7yha7ILT", "cdeKSodFZBn4j136Mgh1Y5"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$skeletonData", "_textureSetter", "spriteFrame", "root", "TaiXiuSicboTopListView", "rankSprite3", "rankSprite2", "rankSprite1", "lbTotalWin", "lbNickName", "lbRank", "_N$target", "data", "fontRegurlar", "fontBold", "fontName", "_defaultClip"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_eulerAngles", "_anchorPoint", "_color"], 0, 4, 9, 5, 1, 7, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "_enabled", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 5, 9, 5, 5, 1], "cc.SpriteFrame", ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["d2da9AhrHpDwZBi0tOduJFd", ["node", "TaiXiuSicboTopListView"], 3, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["a278dFFRqxCj4+WcisO+WrS", ["node", "lbRank", "lbNickName", "lbTotalWin", "rankSprite1", "rankSprite2", "rankSprite3", "fontRegurlar", "fontBold", "fontName"], 3, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials"], 0, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["8d5a90MWmhF0LPOMEvJ/6jV", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[12, 0, 1, 2], [0, 0, 6, 4, 3, 5, 7, 2], [9, 0, 1, 2, 3, 4, 5, 2], [1, 0, 4, 5, 6, 2], [2, 0, 1, 2, 3, 4, 8, 9, 10, 6], [0, 0, 6, 8, 4, 3, 5, 7, 2], [0, 0, 6, 4, 3, 5, 7, 9, 2], [1, 1, 0, 4, 5, 6, 3], [1, 4, 5, 6, 1], [2, 0, 1, 5, 6, 2, 3, 4, 7, 8, 9, 9], [17, 0, 1, 2, 3, 4, 4], [6, 0, 1, 2, 4], [7, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 5, 7, 2], [0, 0, 8, 4, 3, 5, 2], [0, 0, 6, 8, 4, 3, 5, 2], [0, 0, 2, 6, 4, 3, 5, 7, 3], [0, 0, 1, 6, 4, 3, 5, 3], [0, 0, 6, 4, 3, 5, 10, 7, 2], [0, 0, 1, 6, 4, 3, 5, 7, 9, 3], [0, 0, 6, 8, 3, 7, 2], [0, 0, 1, 6, 4, 3, 11, 5, 7, 3], [8, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 1], [11, 0, 1, 1], [1, 2, 1, 0, 3, 4, 5, 6, 5], [1, 4, 5, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [14, 0, 1], [3, 0, 2, 4, 5, 6, 7, 2], [3, 2, 3, 1], [3, 1, 2, 3, 2], [15, 0, 1, 2, 3], [5, 0, 1, 2, 4, 5, 4], [5, 0, 1, 3, 4, 5, 4], [16, 0, 1, 1], [2, 0, 1, 2, 3, 8, 9, 10, 5], [2, 0, 1, 5, 6, 2, 3, 4, 8, 10, 8], [2, 0, 1, 5, 6, 2, 3, 4, 8, 9, 8], [18, 0, 1, 2, 3, 4, 5, 6, 6], [19, 0, 1, 2, 3, 4, 5, 4]], [[[[11, "Font_HelveticaNeue_Effect-export", 32, {"commonHeight": 39, "fontSize": 32, "atlasName": "Font_HelveticaNeue_Effect-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 160, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 12, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 410, "y": 84, "width": 11, "height": 27}}, "35": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 234, "y": 364, "width": 23, "height": 27}}, "36": {"xOffset": 0, "yOffset": 9, "xAdvance": 25, "rect": {"x": 209, "y": 0, "width": 24, "height": 33}}, "37": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 28, "width": 34, "height": 29}}, "38": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 154, "y": 274, "width": 27, "height": 27}}, "39": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 318, "y": 404, "width": 11, "height": 13}}, "40": {"xOffset": 0, "yOffset": 11, "xAdvance": 15, "rect": {"x": 394, "y": 52, "width": 14, "height": 34}}, "41": {"xOffset": 0, "yOffset": 11, "xAdvance": 15, "rect": {"x": 395, "y": 87, "width": 14, "height": 34}}, "42": {"xOffset": 0, "yOffset": 12, "xAdvance": 18, "rect": {"x": 183, "y": 247, "width": 17, "height": 14}}, "43": {"xOffset": 0, "yOffset": 18, "xAdvance": 25, "rect": {"x": 208, "y": 397, "width": 24, "height": 21}}, "44": {"xOffset": 0, "yOffset": 30, "xAdvance": 12, "rect": {"x": 53, "y": 240, "width": 11, "height": 13}}, "45": {"xOffset": 0, "yOffset": 24, "xAdvance": 18, "rect": {"x": 35, "y": 249, "width": 17, "height": 8}}, "46": {"xOffset": 0, "yOffset": 30, "xAdvance": 12, "rect": {"x": 306, "y": 413, "width": 11, "height": 9}}, "47": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 377, "y": 292, "width": 19, "height": 28}}, "48": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 259, "y": 232, "width": 23, "height": 27}}, "49": {"xOffset": 0, "yOffset": 12, "xAdvance": 17, "rect": {"x": 377, "y": 29, "width": 16, "height": 27}}, "50": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 171, "width": 23, "height": 27}}, "51": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 265, "width": 22, "height": 27}}, "52": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 98, "width": 23, "height": 27}}, "53": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 321, "width": 22, "height": 27}}, "54": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 362, "width": 23, "height": 27}}, "55": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 349, "width": 22, "height": 27}}, "56": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 300, "width": 23, "height": 27}}, "57": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 293, "width": 22, "height": 27}}, "58": {"xOffset": 0, "yOffset": 18, "xAdvance": 12, "rect": {"x": 397, "y": 327, "width": 11, "height": 21}}, "59": {"xOffset": 0, "yOffset": 18, "xAdvance": 12, "rect": {"x": 410, "y": 112, "width": 11, "height": 25}}, "63": {"xOffset": 0, "yOffset": 11, "xAdvance": 23, "rect": {"x": 354, "y": 57, "width": 22, "height": 28}}, "64": {"xOffset": 0, "yOffset": 11, "xAdvance": 31, "rect": {"x": 33, "y": 356, "width": 30, "height": 29}}, "65": {"xOffset": 0, "yOffset": 12, "xAdvance": 30, "rect": {"x": 65, "y": 114, "width": 29, "height": 27}}, "66": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 391, "width": 26, "height": 27}}, "67": {"xOffset": 0, "yOffset": 11, "xAdvance": 29, "rect": {"x": 125, "y": 100, "width": 28, "height": 28}}, "68": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 154, "y": 61, "width": 27, "height": 27}}, "69": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 271, "width": 23, "height": 27}}, "70": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 331, "y": 0, "width": 23, "height": 27}}, "71": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 95, "y": 324, "width": 29, "height": 28}}, "72": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 190, "width": 26, "height": 27}}, "73": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 387, "width": 10, "height": 27}}, "74": {"xOffset": 0, "yOffset": 12, "xAdvance": 22, "rect": {"x": 377, "y": 213, "width": 21, "height": 27}}, "75": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 155, "y": 236, "width": 27, "height": 27}}, "76": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 126, "width": 23, "height": 27}}, "77": {"xOffset": 0, "yOffset": 12, "xAdvance": 32, "rect": {"x": 33, "y": 328, "width": 31, "height": 27}}, "78": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 36, "width": 26, "height": 27}}, "79": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 94, "y": 393, "width": 29, "height": 28}}, "80": {"xOffset": 0, "yOffset": 12, "xAdvance": 26, "rect": {"x": 209, "y": 275, "width": 25, "height": 27}}, "81": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 64, "y": 356, "width": 29, "height": 30}}, "82": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 363, "width": 26, "height": 27}}, "83": {"xOffset": 0, "yOffset": 11, "xAdvance": 27, "rect": {"x": 183, "y": 218, "width": 26, "height": 28}}, "84": {"xOffset": 0, "yOffset": 12, "xAdvance": 26, "rect": {"x": 209, "y": 247, "width": 25, "height": 27}}, "85": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 162, "width": 26, "height": 27}}, "86": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 126, "y": 274, "width": 27, "height": 27}}, "87": {"xOffset": 0, "yOffset": 12, "xAdvance": 38, "rect": {"x": 0, "y": 0, "width": 37, "height": 27}}, "88": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 153, "y": 302, "width": 27, "height": 27}}, "89": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 125, "y": 72, "width": 28, "height": 27}}, "90": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 64, "width": 26, "height": 27}}, "91": {"xOffset": 0, "yOffset": 11, "xAdvance": 14, "rect": {"x": 399, "y": 152, "width": 13, "height": 34}}, "92": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 377, "y": 321, "width": 19, "height": 28}}, "93": {"xOffset": 0, "yOffset": 11, "xAdvance": 14, "rect": {"x": 396, "y": 384, "width": 13, "height": 34}}, "94": {"xOffset": 0, "yOffset": 12, "xAdvance": 25, "rect": {"x": 126, "y": 302, "width": 24, "height": 19}}, "95": {"xOffset": 0, "yOffset": 35, "xAdvance": 24, "rect": {"x": 33, "y": 387, "width": 23, "height": 8}}, "97": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 209, "y": 303, "width": 23, "height": 22}}, "98": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 390, "width": 23, "height": 27}}, "99": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 307, "y": 63, "width": 22, "height": 22}}, "100": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 307, "y": 0, "width": 23, "height": 27}}, "101": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 354, "y": 115, "width": 22, "height": 22}}, "102": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 377, "y": 92, "width": 16, "height": 28}}, "103": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 354, "y": 86, "width": 22, "height": 28}}, "104": {"xOffset": 0, "yOffset": 12, "xAdvance": 22, "rect": {"x": 377, "y": 241, "width": 21, "height": 27}}, "105": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 399, "y": 0, "width": 10, "height": 27}}, "106": {"xOffset": 0, "yOffset": 12, "xAdvance": 14, "rect": {"x": 396, "y": 350, "width": 13, "height": 33}}, "107": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 377, "width": 22, "height": 27}}, "108": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 359, "width": 10, "height": 27}}, "109": {"xOffset": 0, "yOffset": 17, "xAdvance": 32, "rect": {"x": 0, "y": 396, "width": 31, "height": 22}}, "110": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 241, "width": 21, "height": 22}}, "111": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 282, "y": 394, "width": 23, "height": 22}}, "112": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 307, "y": 213, "width": 23, "height": 28}}, "113": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 307, "y": 242, "width": 23, "height": 28}}, "114": {"xOffset": 0, "yOffset": 17, "xAdvance": 16, "rect": {"x": 394, "y": 29, "width": 15, "height": 22}}, "115": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 377, "y": 269, "width": 21, "height": 22}}, "116": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 378, "y": 121, "width": 16, "height": 26}}, "117": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 30, "width": 21, "height": 22}}, "118": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 355, "y": 138, "width": 22, "height": 22}}, "119": {"xOffset": 0, "yOffset": 17, "xAdvance": 32, "rect": {"x": 32, "y": 396, "width": 31, "height": 22}}, "120": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 283, "y": 35, "width": 23, "height": 22}}, "121": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 283, "y": 58, "width": 23, "height": 28}}, "122": {"xOffset": 0, "yOffset": 18, "xAdvance": 23, "rect": {"x": 355, "y": 161, "width": 22, "height": 21}}, "123": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 377, "y": 57, "width": 16, "height": 34}}, "124": {"xOffset": 0, "yOffset": 9, "xAdvance": 10, "rect": {"x": 411, "y": 284, "width": 9, "height": 36}}, "125": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 378, "y": 148, "width": 16, "height": 34}}, "192": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 35, "y": 135, "width": 29, "height": 34}}, "193": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 35, "y": 100, "width": 29, "height": 34}}, "194": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 0, "width": 29, "height": 34}}, "195": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 35, "y": 206, "width": 29, "height": 33}}, "200": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 0, "width": 24, "height": 34}}, "201": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 35, "width": 24, "height": 34}}, "202": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 209, "y": 169, "width": 24, "height": 34}}, "204": {"xOffset": 0, "yOffset": 5, "xAdvance": 14, "rect": {"x": 397, "y": 292, "width": 13, "height": 34}}, "205": {"xOffset": 0, "yOffset": 4, "xAdvance": 14, "rect": {"x": 399, "y": 217, "width": 13, "height": 35}}, "210": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 65, "y": 142, "width": 29, "height": 34}}, "211": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 96, "y": 287, "width": 29, "height": 34}}, "212": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 214, "width": 29, "height": 34}}, "213": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 95, "y": 147, "width": 29, "height": 33}}, "217": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 182, "y": 127, "width": 26, "height": 34}}, "218": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 182, "y": 92, "width": 26, "height": 34}}, "221": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 125, "y": 165, "width": 28, "height": 34}}, "224": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 173, "width": 23, "height": 29}}, "225": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 334, "width": 23, "height": 29}}, "226": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 392, "width": 23, "height": 29}}, "227": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 235, "y": 271, "width": 23, "height": 28}}, "232": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 259, "y": 260, "width": 23, "height": 29}}, "233": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 259, "y": 0, "width": 23, "height": 29}}, "234": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 282, "y": 117, "width": 23, "height": 29}}, "236": {"xOffset": 0, "yOffset": 10, "xAdvance": 14, "rect": {"x": 399, "y": 187, "width": 13, "height": 29}}, "237": {"xOffset": 0, "yOffset": 10, "xAdvance": 15, "rect": {"x": 395, "y": 122, "width": 14, "height": 29}}, "242": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 283, "y": 210, "width": 23, "height": 29}}, "243": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 283, "y": 180, "width": 23, "height": 29}}, "244": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 306, "y": 271, "width": 23, "height": 29}}, "245": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 306, "y": 87, "width": 23, "height": 28}}, "249": {"xOffset": 0, "yOffset": 10, "xAdvance": 22, "rect": {"x": 377, "y": 183, "width": 21, "height": 29}}, "250": {"xOffset": 0, "yOffset": 10, "xAdvance": 22, "rect": {"x": 355, "y": 0, "width": 21, "height": 29}}, "253": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 306, "y": 143, "width": 23, "height": 35}}, "258": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 64, "y": 387, "width": 29, "height": 33}}, "259": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 234, "y": 144, "width": 23, "height": 28}}, "272": {"xOffset": 0, "yOffset": 12, "xAdvance": 30, "rect": {"x": 65, "y": 328, "width": 29, "height": 27}}, "273": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 38, "y": 0, "width": 26, "height": 27}}, "296": {"xOffset": 0, "yOffset": 6, "xAdvance": 19, "rect": {"x": 377, "y": 350, "width": 18, "height": 33}}, "297": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 377, "y": 384, "width": 18, "height": 28}}, "360": {"xOffset": 0, "yOffset": 6, "xAdvance": 27, "rect": {"x": 182, "y": 264, "width": 26, "height": 33}}, "361": {"xOffset": 0, "yOffset": 11, "xAdvance": 22, "rect": {"x": 377, "y": 0, "width": 21, "height": 28}}, "416": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 58, "width": 34, "height": 28}}, "417": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 125, "y": 322, "width": 27, "height": 23}}, "431": {"xOffset": 0, "yOffset": 10, "xAdvance": 33, "rect": {"x": 0, "y": 260, "width": 32, "height": 29}}, "432": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 153, "y": 389, "width": 27, "height": 23}}, "7840": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 124, "y": 353, "width": 28, "height": 31}}, "7841": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 258, "y": 144, "width": 23, "height": 26}}, "7842": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 170, "width": 29, "height": 35}}, "7843": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 234, "y": 303, "width": 23, "height": 30}}, "7844": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 28, "width": 29, "height": 35}}, "7845": {"xOffset": 0, "yOffset": 7, "xAdvance": 25, "rect": {"x": 209, "y": 34, "width": 24, "height": 32}}, "7846": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 64, "width": 29, "height": 35}}, "7847": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 235, "y": 203, "width": 23, "height": 32}}, "7848": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 96, "y": 249, "width": 29, "height": 37}}, "7849": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 235, "y": 236, "width": 23, "height": 34}}, "7850": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 94, "y": 356, "width": 29, "height": 36}}, "7851": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 283, "y": 0, "width": 23, "height": 34}}, "7852": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 125, "y": 200, "width": 28, "height": 38}}, "7853": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 258, "y": 328, "width": 23, "height": 33}}, "7854": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 0, "width": 29, "height": 37}}, "7855": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 234, "y": 109, "width": 23, "height": 34}}, "7856": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 38, "width": 29, "height": 37}}, "7857": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 258, "y": 109, "width": 23, "height": 34}}, "7858": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 76, "width": 29, "height": 37}}, "7859": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 330, "y": 63, "width": 23, "height": 34}}, "7860": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 95, "y": 74, "width": 29, "height": 36}}, "7861": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 307, "y": 28, "width": 23, "height": 34}}, "7862": {"xOffset": 0, "yOffset": 6, "xAdvance": 29, "rect": {"x": 125, "y": 34, "width": 28, "height": 37}}, "7863": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 259, "y": 199, "width": 23, "height": 32}}, "7864": {"xOffset": 0, "yOffset": 12, "xAdvance": 25, "rect": {"x": 209, "y": 137, "width": 24, "height": 31}}, "7865": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 282, "y": 90, "width": 23, "height": 26}}, "7866": {"xOffset": 0, "yOffset": 4, "xAdvance": 25, "rect": {"x": 209, "y": 67, "width": 24, "height": 35}}, "7867": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 259, "y": 30, "width": 23, "height": 30}}, "7868": {"xOffset": 0, "yOffset": 6, "xAdvance": 25, "rect": {"x": 209, "y": 103, "width": 24, "height": 33}}, "7869": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 259, "y": 61, "width": 23, "height": 28}}, "7870": {"xOffset": 0, "yOffset": 4, "xAdvance": 25, "rect": {"x": 210, "y": 204, "width": 24, "height": 35}}, "7871": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 208, "y": 331, "width": 25, "height": 32}}, "7872": {"xOffset": 0, "yOffset": 4, "xAdvance": 24, "rect": {"x": 331, "y": 191, "width": 23, "height": 35}}, "7873": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 282, "y": 147, "width": 23, "height": 32}}, "7874": {"xOffset": 0, "yOffset": 2, "xAdvance": 24, "rect": {"x": 331, "y": 227, "width": 23, "height": 37}}, "7875": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 282, "y": 290, "width": 23, "height": 34}}, "7876": {"xOffset": 0, "yOffset": 3, "xAdvance": 24, "rect": {"x": 331, "y": 154, "width": 23, "height": 36}}, "7877": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 282, "y": 325, "width": 23, "height": 34}}, "7878": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 70, "width": 24, "height": 38}}, "7879": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 282, "y": 360, "width": 23, "height": 33}}, "7880": {"xOffset": 0, "yOffset": 4, "xAdvance": 13, "rect": {"x": 410, "y": 0, "width": 12, "height": 35}}, "7881": {"xOffset": 0, "yOffset": 9, "xAdvance": 13, "rect": {"x": 399, "y": 253, "width": 12, "height": 30}}, "7882": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 409, "y": 52, "width": 11, "height": 31}}, "7883": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 327, "width": 10, "height": 31}}, "7884": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 95, "y": 181, "width": 29, "height": 32}}, "7885": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 306, "y": 116, "width": 23, "height": 26}}, "7886": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 95, "y": 111, "width": 29, "height": 35}}, "7887": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 283, "y": 240, "width": 23, "height": 30}}, "7888": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 65, "y": 177, "width": 29, "height": 35}}, "7889": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 208, "y": 364, "width": 25, "height": 32}}, "7890": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 65, "y": 213, "width": 29, "height": 35}}, "7891": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 306, "y": 301, "width": 23, "height": 32}}, "7892": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 66, "y": 249, "width": 29, "height": 37}}, "7893": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 306, "y": 334, "width": 23, "height": 34}}, "7894": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 66, "y": 287, "width": 29, "height": 36}}, "7895": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 306, "y": 369, "width": 23, "height": 34}}, "7896": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 35, "width": 29, "height": 38}}, "7897": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 307, "y": 179, "width": 23, "height": 33}}, "7898": {"xOffset": 0, "yOffset": 5, "xAdvance": 35, "rect": {"x": 0, "y": 122, "width": 34, "height": 34}}, "7899": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 146, "width": 27, "height": 29}}, "7900": {"xOffset": 0, "yOffset": 5, "xAdvance": 35, "rect": {"x": 0, "y": 87, "width": 34, "height": 34}}, "7901": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 0, "width": 27, "height": 29}}, "7902": {"xOffset": 0, "yOffset": 4, "xAdvance": 35, "rect": {"x": 0, "y": 224, "width": 34, "height": 35}}, "7903": {"xOffset": 0, "yOffset": 9, "xAdvance": 28, "rect": {"x": 154, "y": 30, "width": 27, "height": 30}}, "7904": {"xOffset": 0, "yOffset": 6, "xAdvance": 35, "rect": {"x": 0, "y": 157, "width": 34, "height": 33}}, "7905": {"xOffset": 0, "yOffset": 11, "xAdvance": 28, "rect": {"x": 154, "y": 89, "width": 27, "height": 28}}, "7906": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 191, "width": 34, "height": 32}}, "7907": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 154, "y": 118, "width": 27, "height": 27}}, "7908": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 331, "width": 26, "height": 31}}, "7909": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 214, "width": 21, "height": 26}}, "7910": {"xOffset": 0, "yOffset": 4, "xAdvance": 27, "rect": {"x": 182, "y": 0, "width": 26, "height": 35}}, "7911": {"xOffset": 0, "yOffset": 9, "xAdvance": 22, "rect": {"x": 355, "y": 183, "width": 21, "height": 30}}, "7912": {"xOffset": 0, "yOffset": 5, "xAdvance": 33, "rect": {"x": 0, "y": 290, "width": 32, "height": 34}}, "7913": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 176, "width": 27, "height": 29}}, "7914": {"xOffset": 0, "yOffset": 5, "xAdvance": 33, "rect": {"x": 0, "y": 325, "width": 32, "height": 34}}, "7915": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 206, "width": 27, "height": 29}}, "7916": {"xOffset": 0, "yOffset": 4, "xAdvance": 33, "rect": {"x": 0, "y": 360, "width": 32, "height": 35}}, "7917": {"xOffset": 0, "yOffset": 9, "xAdvance": 28, "rect": {"x": 153, "y": 330, "width": 27, "height": 30}}, "7918": {"xOffset": 0, "yOffset": 6, "xAdvance": 33, "rect": {"x": 33, "y": 260, "width": 32, "height": 33}}, "7919": {"xOffset": 0, "yOffset": 11, "xAdvance": 28, "rect": {"x": 181, "y": 302, "width": 27, "height": 28}}, "7920": {"xOffset": 0, "yOffset": 10, "xAdvance": 33, "rect": {"x": 33, "y": 294, "width": 32, "height": 33}}, "7921": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 153, "y": 361, "width": 27, "height": 27}}, "7922": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 126, "y": 239, "width": 28, "height": 34}}, "7923": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 330, "y": 299, "width": 23, "height": 35}}, "7924": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 124, "y": 385, "width": 28, "height": 31}}, "7925": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 331, "y": 28, "width": 23, "height": 28}}, "7926": {"xOffset": 0, "yOffset": 4, "xAdvance": 29, "rect": {"x": 125, "y": 129, "width": 28, "height": 35}}, "7927": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 330, "y": 335, "width": 23, "height": 36}}, "7928": {"xOffset": 0, "yOffset": 6, "xAdvance": 29, "rect": {"x": 125, "y": 0, "width": 28, "height": 33}}, "7929": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 330, "y": 372, "width": 23, "height": 34}}, "8221": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 259, "y": 90, "width": 16, "height": 14}}}, "kerningDict": {}}]], 0, 0, [0], [6], [8]], [[[12, "SicboTopView"], [13, "taiXiuTopView", [-5, -6, -7, -8, -9, -10], [[24, -2, [38, 39], 37], [25, -4, -3]], [0, "a1NUL9h8tK6788yo3J5Jf1", -1]], [14, "boxinside2", [-12, -13, -14, -15, -16, -17, -18], [[7, 1, 0, -11, [18], 19]], [0, "a1ZegMIpVOsZVnSbT7Xlq4", 1], [5, 1060, 504], [-3, -23, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "<PERSON><PERSON>", [-26, -27, -28, -29, -30, -31], [[28, -25, -24, -23, -22, -21, -20, -19, 33, 34, 35]], [0, "daXgaQxB1DM6FT02EdpSjD", 1], [5, 1004, 50]], [23, "scrollview", 1, [-35], [[-32, [29, -33], -34], 1, 4, 1], [0, "7dYoD2m1hFaLqhkdEJOU4Z", 1], [5, 1030, 440], [-18, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "bg", 1, [-37, 2], [[8, -36, [20], 21]], [0, "d2D3B9k8VBh5vWXCP4MZki", 1], [5, 1197, 634]], [1, "btnClose", 1, [[8, -38, [24], 25], [30, 3, -40, [[33, "d2da9AhrHpDwZBi0tOduJFd", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -39]], [0, "2519Sre1pBIJ+oZiZFW85f", 1], [5, 96, 50], [550.321, 288.538, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 3, [-42, -43], [[34, 1, 1, 5, -41, [5, 120.49, 50]]], [0, "29UNpnGb9M76WgMZyr3Cn1", 1], [5, 120.49, 50], [-63, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "black", 100, 1, [[3, 0, -44, [0], 1], [31, -45, [4, 4292269782]]], [0, "ddF0lWsbpDlpgT+vu/YpWY", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "nen popup", false, 1, [[26, false, 1, 0, false, -46, [22], 23], [32, false, -47, [4, 4292269782]]], [0, "58LW3iMwJFArftapbeg+IJ", 1], [5, 1084, 618]], [5, "view", 4, [-49], [[36, -48, [36]]], [0, "670MfFlZxBQaYf+46HO4u9", 1], [5, 1030, 429], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "content", 10, [[35, 1, 2, 10, -50, [5, 1020, 0]]], [0, "af7qEwzvJK6oj7fcUr2qNe", 1], [5, 1020, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Title", 5, [[37, "BẢNG XẾP HẠNG", 22, false, 1, -51, [2], 3]], [0, "46vOlk1K9BipUrL3M8NWeg", 1], [5, 299.06, 27.5], [0, 307.309, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "boderNhoCopy", 2, [[7, 1, 0, -52, [4], 5]], [0, "aehGpjP99AY5Ela86HLRpx", 1], [5, 1060, 54.7], [0.1, 224.6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lineNgang", 2, [[3, 0, -53, [6], 7]], [0, "7cMFQLSSJOMbtVuHBmS8KZ", 1], [5, 552, 29], [-314.863, 10.16, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [6, "lineNgang", 2, [[3, 0, -54, [8], 9]], [0, "2e0sInJSNFMIT+PCN3Sygf", 1], [5, 552, 29], [206.142, 10.16, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [20, "lineNgang", false, 2, [[3, 0, -55, [10], 11]], [0, "5eVmVyJ5RB3KUHKz0Q/Ehl", 1], [5, 552, 29], [-332.381, 10.16, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [1, "lblRank", 2, [[4, "XẾP HẠNG", 20, false, 1, 1, -56, [12], 13]], [0, "58ybO+e5pP0pJzmuf31Uke", 1], [5, 126.88, 25], [-436.193, 224.792, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lblNickname", 2, [[4, "TÊN NHÂN VẬT", 20, false, 1, 1, -57, [14], 15]], [0, "8f83gktGxPGIP6/Xlgjs49", 1], [5, 184.38, 25], [-69.273, 224.792, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lblWin", 2, [[4, "SỐ TIỀN THẮNG", 20, false, 1, 1, -58, [16], 17]], [0, "0efBozGdJL/JPpNdR4WaT8", 1], [5, 192.5, 25], [366.777, 224.792, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "temp", 1, [3], [0, "ba0lKMgeFCmalStzPvOTzQ", 1], [0, 1943, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbRank", 3, [-59], [0, "604uLuuUBP9aMhe6PNbpJy", 1], [5, 150, 30], [-430, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "1", 24, 48, false, false, 1, 1, 1, 21, [26]], [2, "rankSprite1", 3, [-60], [0, "b45IrD4j1IO7q3ajK2MTBX", 1], [5, 122.39, 130.51], [-427.8, -0.3999999999999999, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [10, "Rank1", 0, "Rank1", 23, [27]], [2, "rankSprite2", 3, [-61], [0, "5bo0n4Gj5L6Kd7W2P8rjdd", 1], [5, 122.39, 130.51], [-427.8, -0.3999999999999999, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [10, "Rank2", 0, "Rank2", 25, [28]], [2, "rankSprite3", 3, [-62], [0, "47sKa9fOhCdbomYg2nt+4E", 1], [5, 46, 49], [-427.8, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [27, 27, [29]], [22, "lbSID", false, 7, [[38, "[TQ]", 24, 48, false, false, 1, 1, -63, 30]], [0, "05IcMsiERAwYJqXW8oxoh/", 1], [4, 4279026733], [5, 44.4, 28.8], [-59.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbNickName", 7, [-64], [0, "72eIaB3kBFS7AvMvLpnSgG", 1], [5, 120.49, 60.48], [-7.105427357601002e-15, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "<PERSON><PERSON><PERSON>", 24, 48, false, false, 1, 1, 30, [31]], [2, "lbTotalWin", 3, [-65], [0, "861IwfPDtKV6x7wMhVeLf8", 1], [5, 200, 30], [382.703, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "0", 20, 48, false, false, 1, 1, 1, 32, [32]], [40, false, 0.75, 0.23, null, null, 4, 11], [41, 20, 10, 400, 4, 3, 34]], 0, [0, 7, 1, 0, 0, 1, 0, 8, 35, 0, 0, 1, 0, -1, 8, 0, -2, 5, 0, -3, 9, 0, -4, 6, 0, -5, 20, 0, -6, 4, 0, 0, 2, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 16, 0, -5, 17, 0, -6, 18, 0, -7, 19, 0, 9, 28, 0, 10, 26, 0, 11, 24, 0, 12, 33, 0, 13, 31, 0, 14, 22, 0, 0, 3, 0, -1, 21, 0, -2, 23, 0, -3, 25, 0, -4, 27, 0, -5, 7, 0, -6, 32, 0, -1, 34, 0, 0, 4, 0, -3, 35, 0, -1, 10, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 15, 6, 0, 0, 6, 0, 0, 7, 0, -1, 29, 0, -2, 30, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, -1, 22, 0, -1, 24, 0, -1, 26, 0, -1, 28, 0, 0, 29, 0, -1, 31, 0, -1, 33, 0, 16, 1, 2, 3, 5, 3, 3, 20, 65], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 24, 26, 28, 31, 33], [-1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, -1, 17, 18, 19, -1, 20, -1, -2, 2, 4, 4, 1, 2, 2], [0, 9, 0, 10, 0, 11, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 2, 0, 12, 0, 13, 0, 14, 0, 15, 0, 4, 4, 0, 5, 0, 0, 3, 3, 16, 0, 6, 6, 17, 5, 7, 7, 18, 19, 3]], [[{"name": "Icon_Rank_No3", "rect": [0, 0, 46, 49], "offset": [0, 0], "originalSize": [46, 49], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [5], [20]], [[{"name": "bxh", "rect": [0, 0, 1274, 709], "offset": [0, 0], "originalSize": [1274, 709], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [5], [21]]]]