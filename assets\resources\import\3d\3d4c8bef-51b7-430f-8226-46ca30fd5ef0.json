[1, ["ecpdLyjvZBwrvm+cedCcQy", "d82n49/IVAvIEqsa0xvvk0", "24xd2Xl+xHVZeWwPN10Wzf", "90Ss2Yf1lLHYPvA9KDgBaE", "7a/QZLET9IDreTiBfRn2PD", "21tx2pDSpElYCwzUj6L+8q", "97be04EipF04Aia2h48z4C", "41obftpCRMW6380/g5lNfM", "ffn1eiLvZFdK9Ped0tFFLf", "9116UMnDZOzp1+wrqjuJly", "c4E6vT74FAMY2sFh5Bh9cH", "b5qw/Tc11MyrVtdNkz9Fim", "15TqJIGghPRbUeUWSV2ffV", "fdNoodJKVLj4dF1TLppv2g", "d0eRki7tdFCa9wS+t6obx8", "f0puneJcNGfY+3VzwuGjGx", "4d4OkqjRJEeZGzHA0GW7zL", "06RCegptdIBYzb8YE5jwlN", "73dYUDQRFCEJymCU8UE4Bv", "45wrrBM+VO/IPs52mxUvQ6", "9bYDnu5NdJH7u79ZpCFpEO", "adw94Z+hpN57wutNivq8Q5", "a9VpD0DP5LJYQPXITZq+uj", "1ewsTTeZRBbL9DYfWsJfc7", "83G0v9bH5EBJH+BvUuy/Le", "c1y3UL3AVHoqWPxPdQzt/K", "c25Leu0BdNDphgb/Hp9jw/", "42R3O6ZfNA87w5le2Iepqg", "eaqnIplqNErbdgYnwQx8GG", "2cWB/vWPRHja3uQTinHH30", "1efjUgjgRNroUUKxfQSfVE", "24Jdc6Aj5CiJf1WtGQrr1q", "bfBjN8MAhH37ZondOTDPHj"], ["node", "_N$file", "_spriteFrame", "_N$skeletonData", "_normalMaterial", "_defaultClip", "_N$disabledSprite", "_N$pressedSprite", "_N$hoverSprite", "root", "btnBack", "btnNext", "lbTotalRefundXiu", "lbTotalRefundTai", "lbTotalBetXiu", "lbTotalBetTai", "spriteDice3", "spriteDice2", "spriteDice1", "lbXiu", "lbTai", "lblTotalUserBetXiu", "lblTotalUserBetTai", "lblTextNotiNewGame", "lblResult", "lblSicboHash", "lblTotalDice", "nodeXiu", "nodeTai", "lbSessionID", "xiuSessionDetailListView", "taiSessionDetailListView", "sicboBetSideName", "lbRefund", "lbBet", "lbNickName", "lbTime", "_N$target", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 1, 5, 7, 2, 5], ["cc.Label", ["_isSystemFontUsed", "_N$verticalAlign", "_string", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_normalMaterial"], 1, 1, 9, 5, 5, 1, 5, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_eulerAngles", "_children"], 2, 1, 12, 4, 5, 7, 5, 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], 0, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["f411d7pNJVCjJbZfhaMcuw+", ["node"], 3, 1], ["e90d9uHynVHW6YRkL03I0xc", ["node", "lbTime", "lbNickName", "lbBet", "lbRefund", "sicboBetSideName"], 3, 1, 1, 1, 1, 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["0a2085lsQtDbbaN4ccHSe7h", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["46e31tOA01JQKYAG1BXCOWH", ["node", "taiSessionDetailListView", "xiuSessionDetailListView", "lbSessionID", "nodeTai", "nodeXiu", "lblTotalDice", "lblSicboHash", "lblResult", "lblTextNotiNewGame", "lblTotalUserBetTai", "lblTotalUserBetXiu", "nodeEffectTais", "nodeEffectXius", "lbTai", "lbXiu", "spriteDice1", "spriteDice2", "spriteDice3", "lbTotalBetTai", "lbTotalBetXiu", "lbTotalRefundTai", "lbTotalRefundXiu", "btnNext", "btnBack", "sfDices"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3]], [[9, 0, 1, 2], [0, 0, 5, 4, 3, 6, 7, 2], [0, 0, 2, 5, 4, 3, 6, 7, 3], [3, 0, 4, 5, 6, 2], [3, 4, 5, 6, 1], [0, 0, 5, 8, 4, 3, 6, 7, 2], [2, 0, 2, 3, 4, 5, 6, 2], [1, 2, 3, 5, 6, 0, 4, 1, 8, 9, 8], [1, 2, 3, 5, 6, 0, 4, 1, 8, 9, 10, 8], [2, 0, 1, 2, 3, 4, 5, 6, 3], [11, 0, 1, 2, 3], [0, 0, 5, 8, 3, 6, 7, 2], [2, 0, 2, 3, 4, 5, 7, 6, 2], [1, 2, 3, 5, 0, 1, 8, 9, 6], [1, 2, 3, 5, 6, 0, 4, 1, 7, 8, 9, 10, 9], [10, 0, 1, 2, 3, 4, 5, 4], [3, 1, 4, 5, 6, 2], [3, 0, 4, 5, 2], [8, 0, 1], [1, 2, 3, 0, 4, 1, 8, 9, 10, 6], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 9, 7, 2], [5, 0, 1, 7, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 5, 7, 6, 3], [2, 0, 2, 3, 4, 5, 2], [3, 3, 2, 0, 1, 4, 6, 5], [4, 1, 0, 2, 3, 4, 5, 6, 3], [4, 1, 0, 2, 3, 8, 3], [1, 3, 5, 0, 4, 1, 8, 9, 6], [1, 2, 0, 4, 1, 8, 9, 10, 5], [1, 2, 3, 5, 6, 0, 4, 1, 7, 8, 9, 9], [6, 3, 0, 1, 4, 5, 6, 5], [14, 0, 1, 1], [15, 0, 1, 2, 3, 4, 5, 6, 6], [16, 0, 1, 2, 3, 4, 5, 4], [17, 0, 1, 2, 1], [7, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 4, 3, 6, 2], [0, 0, 5, 8, 3, 7, 2], [0, 0, 8, 4, 3, 6, 2], [0, 0, 1, 5, 8, 4, 3, 6, 7, 3], [5, 0, 1, 2, 3, 4, 5, 6, 2], [5, 0, 1, 2, 3, 4, 5, 2], [2, 0, 2, 3, 4, 2], [3, 2, 0, 1, 4, 5, 6, 4], [4, 2, 7, 1], [4, 0, 2, 3, 4, 5, 6, 2], [1, 2, 0, 4, 1, 8, 9, 5], [1, 2, 3, 5, 6, 0, 1, 8, 9, 7], [1, 2, 3, 5, 0, 4, 1, 8, 9, 7], [12, 0, 1], [6, 0, 1, 2, 5, 6, 4], [13, 0, 1, 2, 3, 4, 5, 1], [18, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 1]], [[36, "SicboSessionDetailView"], [37, "taiXiuSessionDetailView", [-28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60], [[35, -2, [112, 113], 111], [55, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, [-15], [-14], -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, [114, 115, 116, 117, 118, 119]]], [0, "42vuDtDYpOKZ0ZabvGknVh", -1]], [41, "<PERSON><PERSON>", [-67, -68, -69, -70, -71], [[54, -66, -65, -64, -63, -62, -61]], [0, "71ryvLiWRHNZ72pUdSb1nZ", 1], [5, 487, 50]], [11, "title-left", 1, [-72, -73, -74, -75, -76, -77, -78], [0, "b6JEenaVFBS7z+g+IyhepT", 1], [5, 500, 30], [-270, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "title-right", 1, [-79, -80, -81, -82, -83, -84, -85], [0, "cfDhx82ktIvKIR4V47Dz9F", 1], [5, 500, 30], [256, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "total-tai", 1, [-87, -88, -89, -90], [[25, false, 1, 0, false, -86, 89]], [0, "68AEbHKg9Nkrqq9xnVN5pA", 1], [5, 487, 50], [-263, -251, 0, 0, 0, 0, 1, 1, 1.1, 1]], [5, "total-xiu", 1, [-92, -93, -94, -95], [[25, false, 1, 0, false, -91, 96]], [0, "edcF8gL3xC7LxuSKn58Byp", 1], [5, 487, 50], [255, -251, 0, 0, 0, 0, 1, 1, 1.1, 1]], [22, "scrollview-tai", 1, [-99], [[-96, -97, [18, -98]], 1, 1, 4], [0, "88JCrALKFAUpWp5WoQimue", 1], [5, 500, 290], [-271, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "scrollview-xiu", 1, [-103], [[-100, -101, [18, -102]], 1, 1, 4], [0, "2ddmR339lKipLmv+IS5GO8", 1], [5, 500, 290], [268, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "black", 100, 1, [[3, 0, -104, [0], 1], [47, -105, [4, 4292269782]], [18, -106]], [0, "73lxVvwE9MRa8dsxQFhD7s", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "btnBackSession", 1, [[[16, false, -107, [14], 15], -108], 4, 1], [0, "80sQzMN5ZJ4Yrc9xLcaevd", 1], [5, 23, 35], [-264, 175, 0, 0, 0, 1, 6.123233995736766e-17, 1.5, 1.5, 1], [1, 0, 0, 180]], [44, "btnNextSession", 1, [[[16, false, -109, [16], 17], -110], 4, 1], [0, "d9rR0Pv91AI4W03v3rkkU1", 1], [5, 23, 35], [285, 175, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [11, "md5Hash", 1, [-111, -112, -113], [0, "eceP3RjFBKk4bE0dveZcZE", 1], [5, 500, 50], [-272, 113, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "nodeResult", 1, [-114, -115, -116], [0, "dbvrYbKcVKx49jnHshmFOB", 1], [5, 500, 50], [272, 113, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnClose", 1, [-119], [[48, 3, -118, [[10, "46e31tOA01JQKYAG1BXCOWH", "closeClicked", 1]], [4, **********], [4, **********], -117]], [0, "3fm/KsSmRIhZb4QfbTz1Z7", 1], [5, 80, 80], [506, 314, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "bgNotify", 0, 1, [-122], [[3, 0, -120, [106], 107], [35, -121, [109, 110], 108]], [0, "eeCoosAF5P+qsav6+59i+P", 1], [5, 686, 50], [0, 239, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nen popup", 1, [-124], [[46, 1, 0, false, -123, [4], 5]], [0, "2fr4Dq6y5KzZ+Yak0PO0Lv", 1], [5, 1084, 643], [0, 13, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tai", false, 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -125, [6], 7]], [0, "6e8L4KFxhDLqvKZGBZ5rBz", 1], [5, 311, 301], [-425, 250, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "xiu", false, 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -126, [8], 9]], [0, "3bFmADV8xAOIuKUBJLqn2y", 1], [5, 311, 301], [425, 250, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "anim<PERSON>iu", false, 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -127, [10], 11]], [0, "0daFa7RzhH2KpgtmIQuzho", 1], [5, 311, 301], [426, 250.9, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "<PERSON>im<PERSON><PERSON>", false, 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -128, [12], 13]], [0, "720d4EvSVB44o04gMRq1Da", 1], [5, 311, 301], [-425, 250.9, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "userTai", 1, [-130], [[4, -129, [29], 30]], [0, "9bGii+ILdD+6DxKUCVcuWA", 1], [5, 13, 22], [-309, 227, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "userXiu", 1, [-132], [[4, -131, [32], 33]], [0, "fa1PcNYehGVp/HrWaYaSfA", 1], [5, 13, 22], [272, 227, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCopy", 12, [[4, -133, [37], 38], [27, 1.1, 3, -134, [[10, "be00fiONJlIcKw5Eti8jYLm", "copyHashClicked", 1]], 39]], [0, "0cVLxCTvxGZ6DRqfXnK/5U", 1], [5, 46, 30], [230, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCopy", 13, [[4, -135, [43], 44], [27, 1.1, 3, -136, [[10, "be00fiONJlIcKw5Eti8jYLm", "copyResultClicked", 1]], 45]], [0, "aajt1nSkdAPq9OpgHNAGdz", 1], [5, 46, 30], [230, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "temp", 1, [-137, 2], [0, "62A/+E4ExPdaFKSkp9wUQi", 1], [-263, 1990, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 2, [-139], [[53, 1, 1, 5, -138, [5, 106.31, 50]]], [0, "de4BopV8pFSYo7bDIInaMf", 1], [5, 106.31, 50], [23.346, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "view", 7, [-141], [[32, -140, [103]]], [0, "71Q05LiVxPPJIKjeAM9XAP", 1], [5, 500, 290]], [21, "content", 27, [[31, false, 1, 2, 10, -142, [5, 500, 0]]], [0, "b7dmvL2HVHX7fIuRA/Ag5M", 1], [5, 500, 0], [0, 0.5, 1], [0, 143, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "view", 8, [-144], [[32, -143, [104]]], [0, "67q2jbDQpMBIvP3fdPPK+E", 1], [5, 500, 290]], [21, "content", 29, [[31, false, 1, 2, 10, -145, [5, 500, 0]]], [0, "f5tn1TjUZLo56Ux4l6QVTZ", 1], [5, 500, 0], [0, 0.5, 1], [0, 143, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 16, [[19, "LỊCH SỬ CƯỢC", 26, false, 1, 1, -146, [2], 3]], [0, "58GgUEANJHmJ0nYofrVaDR", 1], [5, 305.5, 32.5], [0, 307, 0, 0, 0, 0, 1, 1, 1, 1]], [26, 1.1, 3, 10, [[10, "46e31tOA01JQKYAG1BXCOWH", "backSessionClicked", 1]], [4, **********], [4, 2533359615], 10], [26, 1.1, 3, 11, [[10, "46e31tOA01JQKYAG1BXCOWH", "nextSessionClicked", 1]], [4, **********], [4, 2533359615], 11], [6, "dice_1", 1, [-147], [0, "beCIT6/4xIfaYmsw/KuqcW", 1], [5, 68, 68], [-174, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 0, 34, [18]], [1, "plus", 1, [[4, -148, [19], 20]], [0, "60OfZtwotP26HIVEEBoIsd", 1], [5, 63, 62], [10, 174, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [6, "dice_2", 1, [-149], [0, "30g9bUqh9Ps7G37Z4mtI1j", 1], [5, 68, 68], [-51, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 0, 37, [21]], [1, "plus", 1, [[4, -150, [22], 23]], [0, "8fgouryOBKmqNpDkeD/bVz", 1], [5, 63, 62], [-111, 174, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [6, "dice_3", 1, [-151], [0, "d5LCgzgMVHFYOhxizPCkt6", 1], [5, 68, 68], [69, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 0, 40, [24]], [1, "blance", 1, [[4, -152, [25], 26]], [0, "bdoz2xj4xPPp5jG9H7Xkhz", 1], [5, 26, 20], [136, 174, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbTotal", 1, [-153], [0, "25bKNfQOdOIqTV2I3/Xrkp", 1], [5, 78.75, 50], [204, 242, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "16", false, 1, 1, 43, [27]], [12, "lbTotalUserTai", 21, [-154], [0, "03p2Nl7cxA6qEOHCQ9sXi1", 1], [5, 37.88, 11.25], [0, 0, 0.5], [13, 19, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "500", 12, 30, false, 1, 45, [28]], [12, "lbTotalUserXiu", 22, [-155], [0, "0cBKlpxItBbI4F8kecPUFU", 1], [5, 37.88, 11.25], [0, 0, 0.5], [13, 19, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "500", 12, 30, false, 1, 47, [31]], [1, "title", 12, [[19, "Chuỗi MD5:", 12, false, 1, 1, -156, [34], 35]], [0, "3dn/vFcp9L0oFjiJYu1K5d", 1], [5, 110.63, 15], [-195, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lblMd5", 12, [-157], [0, "32T34g6J5Me6kpnaIVYk2A", 1], [5, 282, 37.8], [0, 0, 0.5], [-136, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "49c34db8361fb6e5a549a99d476e376c", 16, 30, false, 1, 50, [36]], [1, "title", 13, [[19, "Chuỗi KQ:", 12, false, 1, 1, -158, [40], 41]], [0, "28M1+gwntCKJZY4N21hV17", 1], [5, 96, 15], [-195, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lblResult", 13, [-159], [0, "b0t6ZVMYhPCqNqmgNHAVj2", 1], [5, 356, 37.8], [0, 0, 0.5], [-143, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "#650231_4s74HBSv{4-4-3}oGzkpK7zUmkvmixQ", 16, 30, false, 1, 53, [42]], [39, "sprite", 14, [[16, false, -160, [46], 47]], [0, "5etFymNvRBXa2EMi7WLiog", 1], [5, 69, 36]], [6, "lbSession", 1, [-161], [0, "86uT48h3lJGI0sxEL84nAJ", 1], [5, 313, 63], [0, 230, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "PHIEN xxxxxx - Ngay xx-xx-xxxx", 20, 50, false, false, 1, 1, 56, [48]], [9, "lbTai", false, 1, [-162], [0, "0ej0TJSFNByoIi5It8ttNw", 1], [5, 0, 62.5], [-183, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [28, 50, 50, false, 1, 1, 58, [49]], [9, "lbXiu", false, 1, [-163], [0, "45tCrKGHhCs7WcYPrFcX02", 1], [5, 0, 62.5], [161, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [28, 50, 50, false, 1, 1, 60, [50]], [1, "bgContentLeft", 1, [[4, -164, [51], 52]], [0, "84f4y50/dHer6EMc/sMAGX", 1], [5, 678, 375], [-270, -80, 0, 0, 0, 0, 1, 0.76, 0.9, 1]], [1, "bgContentRight", 1, [[4, -165, [53], 54]], [0, "f7N4ItcUhKWYDYLKExNlzc", 1], [5, 678, 375], [267, -80, 0, 0, 0, 0, 1, 0.76, 0.9, 1]], [1, "lbTime", 3, [[14, "TỤ CƯỢC", 14, 48, false, false, 1, 1, 1, -166, [55], 56]], [0, "a7P3r6o6tJd5nqybfqadK1", 1], [5, 91, 30], [-166.929, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1", 3, [[3, 0, -167, [57], 58]], [0, "e43MAY8+tJLIiEGR8X2azY", 1], [5, 25, 379], [-59.141, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 3, [[8, "ĐẶT CƯỢC", 14, 48, false, false, 1, 1, -168, [59], 60]], [0, "72foDeQ7FHqInZnuc7gvGQ", 1], [5, 97.13, 21], [13.185, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 3, [[3, 0, -169, [61], 62]], [0, "1dh0g9XFxH6apNQMeogUPK", 1], [5, 25, 379], [105, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 3, [[8, "THẮNG", 14, 48, false, false, 1, 1, -170, [63], 64]], [0, "2eCQE+JktBoLKRaHUSqhCX", 1], [5, 60.81, 21], [177.779, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "3", false, 3, [[3, 0, -171, [65], 66]], [0, "f1JqTZE9ZHFqg7jg3gINs2", 1], [5, 25, 379], [166, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbRefund", false, 3, [[14, "TRẢ LẠI", 14, 48, false, false, 1, 1, 1, -172, [67], 68]], [0, "21XikddZxMV5l22B+I/YpR", 1], [5, 69.13, 30], [205, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 4, [[14, "TỤ CƯỢC", 14, 48, false, false, 1, 1, 1, -173, [69], 70]], [0, "5ewDJyQBdOrrwRUPuCrHdq", 1], [5, 91, 30], [-151.036, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1", 4, [[3, 0, -174, [71], 72]], [0, "3bPeIZHGhGW7jKaoXQan+b", 1], [5, 25, 379], [113.61, -158, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 4, [[8, "ĐẶT CƯỢC", 14, 48, false, false, 1, 1, -175, [73], 74]], [0, "a8kAIatBhOpb4Jq1CqbWhb", 1], [5, 97.13, 21], [22.995, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "2", false, 4, [[3, 0, -176, [75], 76]], [0, "2bann80A5HMYgOocNEGZh5", 1], [5, 25, 379], [163, -158, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 4, [[8, "THẮNG", 14, 48, false, false, 1, 1, -177, [77], 78]], [0, "b5CX5qvX1HgZKG2CaiSylT", 1], [5, 60.81, 21], [183.697, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "3", 4, [[3, 0, -178, [79], 80]], [0, "c6v5P8DTZIk4Q0qNMFPIww", 1], [5, 25, 379], [-53.091, -158, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbRefund", false, 4, [[14, "TRẢ LẠI", 14, 48, false, false, 1, 1, 1, -179, [81], 82]], [0, "26cmZwUaJLg7r3LYtVv8jx", 1], [5, 69.13, 30], [199.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTotal", false, 5, [[8, "Tổng:", 20, 48, false, false, 1, 1, -180, [83], 84]], [0, "7daJgWOppHAJsNUklY9moP", 1], [5, 66.25, 30], [-57, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbTotalBetTai", false, 5, [-181], [0, "e3Zbcr/sNDG7jpAHFYDabX", 1], [5, 111.25, 30], [-155, 392, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "200.000k", 20, 48, false, false, 1, 1, 79, [85]], [23, "lbTotalRefundTai", false, 5, [-182], [0, "cdLSka7hxNuLhsAdpSlEe1", 1], [5, 111.25, 30], [0, 0, 0.5], [134, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "200.000k", 20, 48, false, false, 1, 81, [86]], [2, "line", false, 5, [[29, "/", false, 1, 1, -183, [87], 88]], [0, "214LeS3aJG6KOxZf8yuEfN", 1], [5, 23.75, 50], [115, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTotal", false, 6, [[8, "Tổng:", 20, 48, false, false, 1, 1, -184, [90], 91]], [0, "0aOfqXMMFGeo1YQKOuXk/X", 1], [5, 66.25, 30], [-60, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbTotalBetXiu", false, 6, [-185], [0, "84o9B2+IdK7LOnQhRP7NJX", 1], [5, 111.25, 30], [179, 392, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "200.000k", 20, 48, false, false, 1, 1, 85, [92]], [2, "line", false, 6, [[29, "/", false, 1, 1, -186, [93], 94]], [0, "bcrDJH40ZJoprL3WlY2YO+", 1], [5, 23.75, 50], [117, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "lbTotalRefundXiu", false, 6, [-187], [0, "a7vc/UGLRMhI5I/zJxsuC8", 1], [5, 111.25, 30], [0, 0, 0.5], [135, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "200.000k", 20, 48, false, false, 1, 1, 88, [95]], [45, "TaiXiuSicboBetSideName", 25, [-188], [0, "2bHRxNwstCYanKKpRU42EE", 1]], [52, 90], [6, "lbTime", 2, [-189], [0, "b7qcJGKF1P14DeeP4yOyS8", 1], [5, 89, 24], [-158.705, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "<PERSON><PERSON><PERSON><PERSON> gian", 16, 48, false, false, 1, 1, 92, [97]], [6, "lbBet", 2, [-190], [0, "3dX1ux7n9HPabr89PWqxvw", 1], [5, 52.85, 30], [181.633, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "100.000", 14, 48, false, false, 1, 1, 1, 94, [98]], [9, "lbRefund", false, 2, [-191], [0, "e2lPHzPuVFQ6sm3W7oPgeH", 1], [5, 60.4, 30], [199.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "100.000", 16, 48, false, false, 1, 1, 1, 96, [99]], [24, "lbNickName", 26, [-192], [0, "ebJ1H3uwpH7q3xmRYROblX", 1], [5, 106.31, 27]], [7, "<PERSON><PERSON><PERSON>", 18, 48, false, false, 1, 1, 98, [100]], [1, "line", 2, [[3, 0, -193, [101], 102]], [0, "8ckGpUPIRAT7COPlYCzNW3", 1], [5, 486, 16], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [33, false, 0.75, 0.23, null, null, 7, 28], [34, 20, 10, 400, 7, 2, 101], [33, false, 0.75, 0.23, null, null, 8, 30], [34, 20, 10, 400, 8, 2, 103], [24, "lblNotify", 15, [-194], [0, "baNdKwGZhHzbEn7im8FSiu", 1], [5, 60, 37.8]], [51, "Label", 22, 30, false, 1, 1, 105, [105]]], 0, [0, 9, 1, 0, 0, 1, 0, 10, 32, 0, 11, 33, 0, 12, 89, 0, 13, 82, 0, 14, 86, 0, 15, 80, 0, 16, 41, 0, 17, 38, 0, 18, 35, 0, 19, 61, 0, 20, 59, 0, -1, 19, 0, -1, 20, 0, 21, 48, 0, 22, 46, 0, 23, 106, 0, 24, 54, 0, 25, 51, 0, 26, 44, 0, 27, 18, 0, 28, 17, 0, 29, 57, 0, 30, 104, 0, 31, 102, 0, 0, 1, 0, -1, 9, 0, -2, 16, 0, -3, 17, 0, -4, 18, 0, -5, 19, 0, -6, 20, 0, -7, 10, 0, -8, 11, 0, -9, 34, 0, -10, 36, 0, -11, 37, 0, -12, 39, 0, -13, 40, 0, -14, 42, 0, -15, 43, 0, -16, 21, 0, -17, 22, 0, -18, 12, 0, -19, 13, 0, -20, 14, 0, -21, 56, 0, -22, 58, 0, -23, 60, 0, -24, 62, 0, -25, 63, 0, -26, 3, 0, -27, 4, 0, -28, 5, 0, -29, 6, 0, -30, 25, 0, -31, 7, 0, -32, 8, 0, -33, 15, 0, 32, 91, 0, 33, 97, 0, 34, 95, 0, 35, 99, 0, 36, 93, 0, 0, 2, 0, -1, 92, 0, -2, 94, 0, -3, 96, 0, -4, 26, 0, -5, 100, 0, -1, 64, 0, -2, 65, 0, -3, 66, 0, -4, 67, 0, -5, 68, 0, -6, 69, 0, -7, 70, 0, -1, 71, 0, -2, 72, 0, -3, 73, 0, -4, 74, 0, -5, 75, 0, -6, 76, 0, -7, 77, 0, 0, 5, 0, -1, 78, 0, -2, 79, 0, -3, 81, 0, -4, 83, 0, 0, 6, 0, -1, 84, 0, -2, 85, 0, -3, 87, 0, -4, 88, 0, -1, 101, 0, -2, 102, 0, 0, 7, 0, -1, 27, 0, -1, 103, 0, -2, 104, 0, 0, 8, 0, -1, 29, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -2, 32, 0, 0, 11, 0, -2, 33, 0, -1, 49, 0, -2, 50, 0, -3, 23, 0, -1, 52, 0, -2, 53, 0, -3, 24, 0, 37, 14, 0, 0, 14, 0, -1, 55, 0, 0, 15, 0, 0, 15, 0, -1, 105, 0, 0, 16, 0, -1, 31, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, -1, 45, 0, 0, 22, 0, -1, 47, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, -1, 90, 0, 0, 26, 0, -1, 98, 0, 0, 27, 0, -1, 28, 0, 0, 28, 0, 0, 29, 0, -1, 30, 0, 0, 30, 0, 0, 31, 0, -1, 35, 0, 0, 36, 0, -1, 38, 0, 0, 39, 0, -1, 41, 0, 0, 42, 0, -1, 44, 0, -1, 46, 0, -1, 48, 0, 0, 49, 0, -1, 51, 0, 0, 52, 0, -1, 54, 0, 0, 55, 0, -1, 57, 0, -1, 59, 0, -1, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, -1, 80, 0, -1, 82, 0, 0, 83, 0, 0, 84, 0, -1, 86, 0, 0, 87, 0, -1, 89, 0, -1, 91, 0, -1, 93, 0, -1, 95, 0, -1, 97, 0, -1, 99, 0, 0, 100, 0, -1, 106, 0, 38, 1, 2, 39, 25, 194], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 32, 32, 33, 35, 38, 41, 44, 46, 48, 51, 54, 57, 59, 61, 80, 82, 86, 89, 93, 95, 97, 99, 106], [-1, 2, -1, 1, -1, 2, -1, 3, -1, 3, -1, 3, -1, 3, -1, 2, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, -1, 2, -1, -1, 2, -1, 1, -1, -1, 2, 4, -1, 1, -1, -1, 2, 4, -1, 2, -1, -1, -1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 1, 2, -1, 1, -1, -1, 1, -1, 2, -1, -1, -1, -1, -1, 2, -1, -1, -1, -1, 2, 5, -1, -2, 5, -1, -2, -1, -2, -3, -4, -5, -6, 7, 8, 6, 6, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 22, 0, 2, 0, 23, 4, 5, 4, 5, 4, 5, 4, 5, 0, 6, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 24, 0, 0, 0, 8, 0, 0, 8, 0, 2, 0, 0, 9, 0, 0, 2, 0, 0, 9, 0, 0, 25, 0, 0, 0, 0, 10, 0, 10, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 1, 0, 0, 0, 1, 11, 0, 1, 0, 0, 1, 0, 11, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 27, 12, 12, 28, 13, 13, 29, 14, 15, 16, 30, 31, 32, 17, 17, 18, 18, 14, 15, 16, 2, 2, 2, 19, 19, 20, 21, 21, 1, 1, 1, 1, 1, 1, 1, 1, 20]]