[1, ["7a/QZLET9IDreTiBfRn2PD", "23VffKmqdD7b+EmVvZHfjO", "43FROe/yRGTJmZAHBfB5Il"], ["node", "root", "data", "_N$skeletonData"], [["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs"], 2, 9, 4, 5, 7], ["sp.Skeleton", ["defaultSkin", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["cc.Animation", ["node", "_clips"], 3, 1, 3], ["cc.PrefabInfo", ["root", "asset"], 3, 1, 1]], [[0, 0, 1, 2, 3, 4, 5, 6], [1, 0, 2], [2, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 5, 6, 5], [4, 0, 1, 1], [5, 0, 1, 1]], [[[[0, "card-scale", 0.2, 50, 0.2, "54", [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]], [{"frame": 0.1}, "value", 8, [0, 1.2, 1.2]], [{"frame": 0.2}, "value", 8, [0, 1, 1]]], 11, 11, 11]]]]], 0, 0, [], [], []], [[[1, "cardPrefab"], [2, "cardPrefab", [[3, "default", 0, false, "Qr", -2, [0], 1], [4, -3, [2]]], [5, -1, 0], [5, 66, 90], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 3], [0, 0, 0], [-1, 3, -1], [0, 1, 2]]]]