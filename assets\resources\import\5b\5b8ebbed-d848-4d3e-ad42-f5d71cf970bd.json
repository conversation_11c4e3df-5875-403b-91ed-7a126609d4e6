[1, ["a2Ueu7fcFPWpy6gp5+dFIT"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "NoHu-JackPot-1", "\nNoHu-JackPot-1.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n1 1\n  rotate: false\n  xy: 665, 600\n  size: 246, 245\n  orig: 248, 247\n  offset: 1, 1\n  index: -1\n1 2\n  rotate: false\n  xy: 2, 2\n  size: 246, 253\n  orig: 248, 255\n  offset: 1, 1\n  index: -1\n1 3\n  rotate: false\n  xy: 2, 257\n  size: 301, 284\n  orig: 303, 286\n  offset: 1, 1\n  index: -1\n1 4\n  rotate: false\n  xy: 2, 543\n  size: 302, 302\n  orig: 304, 304\n  offset: 1, 1\n  index: -1\n2\n  rotate: false\n  xy: 306, 630\n  size: 357, 215\n  orig: 359, 217\n  offset: 1, 1\n  index: -1\nfx-1\n  rotate: true\n  xy: 306, 447\n  size: 181, 183\n  orig: 181, 183\n  offset: 0, 0\n  index: -1\n", ["NoHu-JackPot-1.png"], {"skeleton": {"hash": "iuiIoTPJ5KK8+vqpTZM4wSJQQdM", "spine": "3.6.53", "width": 872.01, "height": 881.65, "images": "./NoHu-JackPot/NoHu-JackPot-P/"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 63.42, "y": 147.74}, {"name": "bone2", "parent": "bone", "length": 49.62, "rotation": 86.51, "x": -22.35, "y": -30.8}, {"name": "fx-1", "parent": "root", "length": 43.47, "x": -17.97, "y": 9.35, "scaleX": 4.818, "scaleY": 4.818}], "slots": [{"name": "fx-1", "bone": "fx-1", "attachment": "fx-1"}, {"name": "1 1", "bone": "bone", "attachment": "1 1"}, {"name": "2", "bone": "bone2"}], "skins": {"default": {"2": {"2": {"x": 97.24, "y": -0.35, "rotation": -86.51, "width": 359, "height": 217}}, "1 1": {"1 1": {"x": -0.58, "y": -6.77, "width": 248, "height": 247}, "1 2": {"x": -0.58, "y": -2.77, "width": 248, "height": 255}, "1 3": {"x": 7.92, "y": 12.73, "width": 303, "height": 286}, "1 4": {"x": 8.42, "y": 18.73, "width": 304, "height": 304}}, "fx-1": {"fx-1": {"width": 181, "height": 183}}}}, "animations": {"Idle": {"slots": {"2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "2"}, {"time": 4.1, "name": null}]}, "1 1": {"attachment": [{"time": 0, "name": "1 1"}, {"time": 0.5667, "name": "1 1"}, {"time": 0.6, "name": "1 2"}, {"time": 0.6333, "name": "1 3"}, {"time": 0.6667, "name": "1 4"}, {"time": 2.2, "name": "1 1"}, {"time": 2.3333, "name": "1 1"}, {"time": 2.3667, "name": "1 2"}, {"time": 2.4, "name": "1 3"}, {"time": 2.4333, "name": "1 4"}, {"time": 4.1, "name": "1 4"}]}, "fx-1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1, "color": "ffffff00"}, {"time": 2.1, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.6333, "color": "ffffff00"}, {"time": 3.2333, "color": "ffffffff"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 4.1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -1.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": 0.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -0.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 3.34}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.6333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 4.1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -147.74, "curve": [0.607, 0, 0.713, 1]}, {"time": 0.1333, "x": 0, "y": 1.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 0, "y": -2.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0, "y": 1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.411, "y": 0.411, "curve": [0.578, 0.01, 0.75, 1]}, {"time": 0.1333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 0.959, "y": 0.959, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.008, "y": 1.008, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 0.4, "x": 0.976, "y": 0.976, "curve": [0.311, 0.26, 0.651, 0.61]}, {"time": 0.4333, "x": 1.008, "y": 1.008, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 0.4667, "x": 0.976, "y": 0.976, "curve": [0.311, 0.26, 0.651, 0.61]}, {"time": 0.5, "x": 1.008, "y": 1.008, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 0.5333, "x": 0.976, "y": 0.976, "curve": [0.311, 0.26, 0.651, 0.61]}, {"time": 0.5667, "x": 1.005, "y": 1.005, "curve": "stepped"}, {"time": 0.6333, "x": 1.005, "y": 1.005, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1.027, "y": 1.027, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0.951, "y": 0.951, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.016, "y": 1.016, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1.005, "y": 1.005}, {"time": 2, "x": 1.008, "y": 1.008, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2.0333, "x": 0.976, "y": 0.976, "curve": [0.311, 0.26, 0.651, 0.61]}, {"time": 2.0667, "x": 1.008, "y": 1.008, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2.1, "x": 0.976, "y": 0.976, "curve": [0.311, 0.26, 0.651, 0.61]}, {"time": 2.1333, "x": 1.008, "y": 1.008, "curve": [0.284, 0, 0.625, 0.38]}, {"time": 2.1667, "x": 0.976, "y": 0.976, "curve": [0.311, 0.26, 0.651, 0.61]}, {"time": 2.2, "x": 1.005, "y": 1.005, "curve": "stepped"}, {"time": 2.2667, "x": 1.005, "y": 1.005, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 1.027, "y": 1.027, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4667, "x": 0.951, "y": 0.951, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1.016, "y": 1.016, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9667, "x": 1.005, "y": 1.005, "curve": "stepped"}, {"time": 4.1, "x": 1.005, "y": 1.005}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 4.1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 1}, {"time": 0.7667, "x": 1.089, "y": 1}, {"time": 1.5333, "x": 1, "y": 1}, {"time": 1.5667, "x": 0, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 1}, {"time": 2.4333, "x": 1.089, "y": 1}, {"time": 2.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}]}, "fx-1": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.1, "angle": 180}, {"time": 2.1, "angle": 0}, {"time": 3.1, "angle": 180}, {"time": 4.1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 4.1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 4.1, "x": 0, "y": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]