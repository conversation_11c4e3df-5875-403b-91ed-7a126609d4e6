[1, ["ecpdLyjvZBwrvm+cedCcQy", "d82n49/IVAvIEqsa0xvvk0", "90Ss2Yf1lLHYPvA9KDgBaE", "c25Leu0BdNDphgb/Hp9jw/", "17VpiysZ9BQLhFb4u1/yDF", "d3FbQrbiNDiJ95m9Czb5Y+", "a2MjXRFdtLlYQ5ouAFv/+R", "9doER5O/5CR4paTK/c16Ii", "efpKbpVfpE0bP2j/FsGGDI", "fdNoodJKVLj4dF1TLppv2g", "76JRMjNThFBrf6i1fqTbml", "4csau8tGZJOp/qYF1rI9iV", "a9VpD0DP5LJYQPXITZq+uj", "24xd2Xl+xHVZeWwPN10Wzf", "1ewsTTeZRBbL9DYfWsJfc7", "calux5gptDb4t1UQ6Ssjsd", "97be04EipF04Aia2h48z4C", "ffmyHfSHRCZr6cq5VJN6LN", "b9sAPrSq9Bhq4NL0/rodI/", "2cWB/vWPRHja3uQTinHH30", "6dpqW883VHj5A+voQVzzib"], ["node", "_spriteFrame", "_N$file", "_parent", "btnMore", "vinhDanh", "lblUserCount", "lblJackpot", "lblResult", "lblTime", "lblSession", "line", "bg", "_defaultClip", "root", "btnPrev", "btnNext", "lblPercentXiu", "lblPercentTai", "lblPage", "listItems", "_N$target", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color"], 0, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "_lineHeight", "_enableWrapText", "_spacingX", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_spriteFrame", "_materials"], 0, 1, 6, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 5, 9, 5, 5, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_color"], 2, 1, 2, 4, 5, 7, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5], ["cc.Widget", ["_alignFlags", "_top", "node"], 1, 1], ["531930+ITFDtYlQZ70K/03n", ["node", "bg", "line", "lblSession", "lblTime", "lblResult", "lblJackpot", "lblUserCount", "vinhDanh", "btnMore"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["510fdujoBlFgIX7Sx8IqLp7", ["node", "listItems", "lblPage", "lblPercentTai", "lblPercentXiu", "btnNext", "btnPrev"], 3, 1, 1, 1, 1, 1, 1, 1]], [[7, 0, 1, 2], [0, 0, 6, 4, 3, 5, 7, 2], [2, 3, 5, 4, 1], [1, 0, 1, 6, 4, 3, 5, 9, 10, 7], [2, 0, 3, 5, 4, 2], [1, 0, 1, 2, 8, 9, 10, 11, 5], [9, 0, 1, 2, 3, 3], [0, 0, 6, 8, 4, 3, 5, 7, 2], [4, 0, 1, 2, 3, 4, 5, 2], [0, 0, 6, 8, 3, 5, 7, 2], [0, 0, 6, 4, 3, 10, 5, 7, 2], [4, 0, 1, 2, 3, 4, 6, 5, 2], [4, 0, 1, 2, 3, 7, 4, 5, 2], [0, 0, 6, 4, 3, 5, 2], [0, 0, 6, 8, 4, 3, 5, 9, 7, 2], [8, 0, 1, 2, 3], [0, 0, 8, 4, 3, 5, 7, 2], [0, 0, 6, 8, 3, 7, 2], [0, 0, 2, 6, 4, 3, 5, 9, 7, 3], [0, 0, 8, 3, 7, 2], [5, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 6, 2, 3, 4, 5, 2], [3, 2, 3, 1], [3, 2, 4, 3, 1], [3, 1, 0, 2, 3, 3], [1, 0, 1, 2, 9, 10, 11, 4], [1, 0, 1, 2, 9, 10, 4], [1, 0, 1, 6, 7, 4, 3, 5, 9, 10, 8], [1, 0, 1, 2, 4, 3, 9, 10, 6], [1, 0, 1, 2, 3, 5, 9, 10, 6], [10, 0, 1, 2, 3], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [6, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 1, 6, 4, 3, 5, 7, 3], [0, 0, 6, 8, 4, 3, 5, 2], [0, 0, 8, 4, 3, 5, 9, 2], [0, 0, 2, 1, 6, 4, 3, 10, 5, 9, 4], [0, 0, 1, 6, 4, 3, 10, 5, 9, 3], [2, 1, 0, 2, 3, 5, 4, 4], [2, 1, 0, 3, 5, 4, 3], [2, 0, 2, 3, 5, 4, 3], [2, 0, 3, 4, 2], [3, 0, 2, 4, 5, 6, 7, 2], [1, 0, 1, 2, 4, 3, 9, 10, 11, 6], [1, 0, 1, 6, 7, 2, 4, 3, 5, 9, 10, 9], [12, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 1], [15, 0, 1, 2, 3, 4, 5, 6, 1]], [[32, "taiXiuMd5JackpotHistoryView"], [33, "taiXiuJackpotHistoryView", [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29], [[48, -2, [104, 105], 103], [49, -9, -8, -7, -6, -5, -4, -3]], [0, "482uhYoORHP6+1RUzk1glq", -1]], [7, "boder", 1, [-31, -32, -33, -34, -35, -36, -37], [[40, 1, 0, -30, [20], 21]], [0, "b4IgOqP6tC1IvUaz0PdL7l", 1], [5, 1286, 430], [0, -61, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "list", [-38, -39, -40, -41, -42, -43, -44], [0, "da0twn/AFI/6ZVfdVgxxhE", 1], [0, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "list", [-45, -46, -47, -48, -49, -50, -51], [0, "e3DopbAixGIZuU2Kh5IuFk", 1], [0, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "item", [-63, 3, -64, -65], [[6, 1, 2, -52, [5, 1280, 99]], [31, -62, -61, -60, -59, -58, -57, -56, -55, -54, -53]], [0, "abaJw/aTBEa4NdgRSo4dUg", 1], [5, 1280, 99], [0, -49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "item", [-77, 4, -78, -79], [[6, 1, 2, -66, [5, 1280, 99]], [31, -76, -75, -74, -73, -72, -71, -70, -69, -68, -67]], [0, "68eG9vJa5NRZa8iNKeV8W1", 1], [5, 1280, 99], [0, -148.5, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "content", [5, 6], [[6, 1, 2, -80, [5, 1280, 198]]], [0, "81CF5KwPJJnbY8nOzeTMbW", 1], [5, 1280, 198], [0, 0.5, 1]], [7, "dicesTai", 1, [-82, -83, -84], [[6, 1, 1, -81, [5, 222, 78]]], [0, "49xITuoWdIp5+cejs1wwjm", 1], [5, 222, 78], [-250.3, 186, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "dicesXiu", 1, [-86, -87, -88], [[6, 1, 1, -85, [5, 225, 78]]], [0, "09NWujRyJJ1aXxF/KPHjMh", 1], [5, 225, 78], [252, 186, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "VinhDanh", 5, [-90, -91], [[6, 1, 2, -89, [5, 368, 83]]], [0, "0cuslUv01G6Ld7GKcMoQUW", 1], [5, 368, 83], [0, 0.5, 1], [180, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "VinhDanh", 6, [-93, -94], [[6, 1, 2, -92, [5, 368, 83]]], [0, "e8WJGOT+RPjq1h6Rf2qzM0", 1], [5, 368, 83], [0, 0.5, 1], [180, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "nen popup", 1, [-97], [[39, 1, 0, false, -95, [4], 5], [22, -96, [4, 4292269782]]], [0, "241vm6yaND3ruq4O7+83Ie", 1], [5, 1352.3, 631.2]], [7, "btnClose", 1, [-100], [[43, 3, -99, [[15, "510fdujoBlFgIX7Sx8IqLp7", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -98]], [0, "13yjKDFnVNZ6AAvSO91mEo", 1], [5, 96.8, 80], [628.5, 291.4, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "btnMore", 3, [-103], [[[2, -101, [71], 72], -102], 4, 1], [0, "17InY0RWlFzaiWxZ/DgeYX", 1], [5, 186, 67], [534, -89, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "0", 10, [-104, -105, -106], [0, "db32FHq3RHBal2Cpw1OH6D", 1], [5, 368, 50], [0, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "btnMore", 4, [-109], [[[2, -107, [93], 94], -108], 4, 1], [0, "99tLzyALhBQZV6IREf2KCX", 1], [5, 186, 67], [534, -89, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "0", 11, [-110, -111, -112], [0, "d3Mjp7Hv9Dx4KRbrNaTmb9", 1], [5, 368, 50], [0, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "black", 100, 1, [[4, 0, -113, [0], 1], [22, -114, [4, 4292269782]]], [0, "9082MxgbxHNIBcevNUN1fe", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "icoNext", 1, [[[2, -115, [40], 41], -116], 4, 1], [0, "14amoIEoJL2KEpdDoWALdG", 1], [5, 23, 35], [656, -57, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [20, "icoPre", 1, [[[2, -117, [42], 43], -118], 4, 1], [0, "d1CswiJidA+b2+XlS0To2h", 1], [5, 23, 35], [-655, -56, 0, 0, 0, 0, 1, 1.6, 1.6, 1]], [7, "ScrollView", 1, [-120], [[47, false, 0.75, 0.23, null, null, -119, 7]], [0, "85odLq6UhEAaBa1tuzxnTX", 1], [5, 1280, 377.5], [0, -70.8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "view", 21, [7], [[46, -121, [102]]], [0, "3bFJdmvstBKbnX6iTjvZ+P", 1], [5, 1280, 377.5], [0, 0.5, 1], [0, 187.1, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "bg", 5, [-122, -123], [0, "95JsGknMxPNqzV4gn2BqQk", 1], [0, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "bg", false, 41, 23, [[42, 0, -124, 59]], [0, "feZ9Xhzv9FiYRmXhKBc0lo", 1], [4, 4288410623], [5, 1280, 144.4], [0, 0.5, 1]], [18, "line", false, 23, [[4, 0, -125, [60], 61]], [0, "a8Pgwp1N5HHIgNG9Bre05T", 1], [5, 25, 85.8], [0, 0.5, 1], [234, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lineNgang", 5, [[4, 0, -126, [78], 79], [30, 4, 181, -127]], [0, "3dlNTZGwNPta9xFb1KRHq7", 1], [5, 1280, 16], [0, -41.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "bg", 6, [-128, -129], [0, "4fA7rt5zNH1LRoy7Xg1/CA", 1], [0, 49.5, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "bg", 41, 27, [[4, 0, -130, [80], 81]], [0, "99EMZG/41LiZbP36ZJHWJZ", 1], [4, 4288410623], [5, 1280, 99], [0, 0.5, 1]], [18, "line", false, 27, [[4, 0, -131, [82], 83]], [0, "a1SctAkC1BE5rXH77AnFpK", 1], [5, 25, 85.8], [0, 0.5, 1], [234, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lineNgang", 6, [[4, 0, -132, [100], 101], [30, 4, 181, -133]], [0, "01jHpgQHRPc4BQvkqWpD+K", 1], [5, 1280, 16], [0, -41.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 12, [[44, "LỊCH SỬ NỔ HŨ", 26, false, 1, 1, -134, [2], 3]], [0, "a6rfp4h9pIjbC34rQoajIx", 1], [5, 317.69, 32.5], [0, 302, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[2, -135, [6], 7]], [0, "340NdVWPBJsoqCXv89dgGc", 1], [5, 25, 410], [-539, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[2, -136, [8], 9]], [0, "c8iR+N6whGnaZ1ZPNaWujK", 1], [5, 25, 410], [-370, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[2, -137, [10], 11]], [0, "13qOtl0t9Dv5cmsFelWCMy", 1], [5, 25, 410], [-229, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[2, -138, [12], 13]], [0, "22esKiFgpO5qe/JbOy9Wdo", 1], [5, 25, 410], [-42, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[2, -139, [14], 15]], [0, "acXb8+p8VGBJqsnSwjuMNn", 1], [5, 25, 410], [430, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[2, -140, [16], 17]], [0, "f2E9DrsoxOjpTgEh+dWp6a", 1], [5, 25, 410], [430, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 2, [[2, -141, [18], 19]], [0, "9fZaw8P49PPaoD2lD2WPQj", 1], [5, 25, 410], [235, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tai", 1, [[25, "Nổ Tài:", 30, false, -142, [22], 23]], [0, "e8hIrrRSJIkbDZlFVhGB6+", 1], [5, 126.56, 37.5], [-425.1, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "<PERSON><PERSON>", 1, [[25, "Nổ Xỉu:", 30, false, -143, [24], 25]], [0, "fa9aGsDH5M1YK+0TmIhmX5", 1], [5, 128.44, 37.5], [75.6, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lblPercentTai", 1, [-144], [0, "6bXMEnSqVMNKTOiyPznTe8", 1], [5, 44.06, 37.5], [0, 0, 0.5], [-133.8, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "00", 30, false, 41, [26]], [11, "lblPercentXiu", 1, [-145], [0, "07D4BKb25J1bz0o/747oRd", 1], [5, 44.06, 37.5], [0, 0, 0.5], [369, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "00", 30, false, 43, [27]], [1, "sesion", 1, [[5, "<PERSON><PERSON><PERSON>", 22, false, -0.6, -146, [28], 29]], [0, "6cwQOWzoxAiZ15hkylhqJh", 1], [5, 69.1, 27.5], [-595.4, 143.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "time", 1, [[5, "Thời Gian", 22, false, -0.6, -147, [30], 31]], [0, "acTVk34bJHv4LBRND1PMwJ", 1], [5, 122.39, 27.5], [-468.2, 143.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "result", 1, [[5, "<PERSON><PERSON><PERSON>", 22, false, -0.6, -148, [32], 33]], [0, "59bBiLdKVDX56x5Cu7eYBP", 1], [5, 105.03, 27.5], [-306.9, 143.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "value", 1, [[5, "<PERSON><PERSON><PERSON><PERSON>", 22, false, -0.6, -149, [34], 35]], [0, "32xCpZsx9KU5N1DxNYxwtR", 1], [5, 136.22, 27.5], [-146, 143.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "top", 1, [[5, "<PERSON><PERSON>", 22, false, -0.6, -150, [36], 37]], [0, "dffxATXh1ElL7q+Eysy4r8", 1], [5, 125.83, 27.5], [105.9, 143.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "amount", 1, [[5, "<PERSON><PERSON><PERSON><PERSON>", 22, false, -0.6, -151, [38], 39]], [0, "b7AHsLX6ND2K1/HALK4U7t", 1], [5, 134.16, 27.5], [325.5, 143.8, 0, 0, 0, 0, 1, 1, 1, 1]], [23, 19, [[15, "510fdujoBlFgIX7Sx8IqLp7", "onClickNextPage", 1]], [4, 4292269782]], [23, 20, [[15, "510fdujoBlFgIX7Sx8IqLp7", "onClickPrevPage", 1]], [4, 4292269782]], [8, "lblPage", 1, [-152], [0, "9ewaz03F5P14Qo16DoZ61V", 1], [5, 186, 50], [566.6, 181.2, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "Trang 1", 22, 48, false, false, 1, 1, 1, 53, [44]], [13, "sprite", 13, [[41, 2, false, -153, [45], 46]], [0, "5fLv6TrQJIla6eMV12P+6g", 1], [5, 96, 50]], [1, "dice_1", 8, [[2, -154, [47], 48]], [0, "c7RX5draJJiJFiJKXCQEL/", 1], [5, 74, 78], [-74, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "dice_2", 8, [[2, -155, [49], 50]], [0, "e0zMH9kilFtpBKo1L64uLN", 1], [5, 74, 78]], [1, "dice_3", 8, [[2, -156, [51], 52]], [0, "a04HI3P4dEorP1k+iT/Vma", 1], [5, 74, 78], [74, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dice_1", 9, [[2, -157, [53], 54]], [0, "00iZTzycNFirblzjGF8udn", 1], [5, 75, 78], [-75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "dice_2", 9, [[2, -158, [55], 56]], [0, "f8+erMOcZFjKphqJOYbZtM", 1], [5, 75, 78]], [1, "dice_3", 9, [[2, -159, [57], 58]], [0, "99BVsJl0VMJYqhUvo8YsdJ", 1], [5, 75, 78], [75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lblSession", 3, [-160], [0, "75f0PTVCNJSZjiJaxbJMeS", 1], [4, 4279679739], [5, 130, 30], [-598, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "#123456", 22, 48, false, 1, 1, 1, 62, [62]], [8, "lblTime", 3, [-161], [0, "333yF9v/5Nx40gfcA7yoyx", 1], [5, 168.8, 70], [-463, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "2022-06-30\n08:10", 22, 48, 1, 1, 1, 64, [63]], [8, "lblCoin", 3, [-162], [0, "86kYoiDm1PorGo+PmKsOFU", 1], [5, 165.56, 50.4], [-145, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "1.050.000.000", 26, false, 1, 1, 66, [64]], [12, "lblResult", 3, [-163], [0, "c7wUR95t9PUKAfJROFbbhx", 1], [4, 4279679739], [5, 160, 30], [-307, -33, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Tài", 26.400000000000013, 48, 1, 1, 1, 68, [65]], [11, "lblUserCount", 3, [-164], [0, "32OV5Y25JD+4wahXxVf17j", 1], [5, 79, 30], [0, 0, 0.5], [-315, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "0", 20, false, 1, 1, 70, [66]], [1, "iconmember", 3, [[2, -165, [67], 68]], [0, "49GYemhRlMz5E4IIxG2gbE", 1], [5, 13, 22], [-336, -68, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 14, [[5, "<PERSON><PERSON>", 25, false, -3, -166, [69], 70]], [0, "b21wAPr2JDObA69dxmdRrR", 1], [5, 133.69, 31.25], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 1.1, 3, 14, [4, 4292269782]], [10, "lblNickname", 15, [[3, "leolentop", 26.400000000000013, 48, 1, 1, 1, -167, [73]]], [0, "fd6zGnQ9dIOboscFqts0It", 1], [4, 4279679739], [5, 200, 30], [-76.1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lblCoin", 15, [[3, "50.000.000", 26.400000000000013, 48, 1, 1, 1, -168, [74]]], [0, "8ckzroni1O16bnLusR9pHa", 1], [5, 150, 30], [137, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lineNgang", 15, [[4, 0, -169, [75], 76]], [0, "e5lC6m6cRLnYHdr87ch4uR", 1], [5, 368, 25], [0, -31.9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "1", 10, [-170], [0, "85djNbRclM37VVTADFp7wB", 1], [5, 368, 33], [0, -66.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lblPage", 78, [[3, "...", 26.400000000000013, 48, 1, 1, 1, -171, [77]]], [0, "ecq6NLQ+tEhpvz5G0pyvvq", 1], [4, 4279679739], [5, 100, 30], [-76.1, 8.8, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lblSession", 4, [-172], [0, "5a6OkMp7RDdoMfLIfhj+dZ", 1], [4, 4279679739], [5, 130, 30], [-598, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "#123456", 22, 48, false, 1, 1, 1, 80, [84]], [8, "lblTime", 4, [-173], [0, "ffHcUrt+pOtLCEzgTZkpPP", 1], [5, 168.8, 70], [-463, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "2022-06-30\n08:10", 22, 48, 1, 1, 1, 82, [85]], [8, "lblCoin", 4, [-174], [0, "e0EfLzflREMoSH+ytkIX72", 1], [5, 165.56, 50.4], [-145, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "1.050.000.000", 26, false, 1, 1, 84, [86]], [12, "lblResult", 4, [-175], [0, "f3gJh82Z9P0Kir2aQFkkz4", 1], [4, 4279679739], [5, 160, 30], [-307, -33, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "Tài", 26.400000000000013, 48, 1, 1, 1, 86, [87]], [11, "lblUserCount", 4, [-176], [0, "67nuKL5QdNYbfqVMC5H+d5", 1], [5, 79, 30], [0, 0, 0.5], [-315, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "0", 20, false, 1, 1, 88, [88]], [1, "iconmember", 4, [[2, -177, [89], 90]], [0, "c3BAD2k3JKVbgTKP6YTcJc", 1], [5, 13, 22], [-336, -68, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 16, [[5, "<PERSON><PERSON>", 25, false, -3, -178, [91], 92]], [0, "0fJa1U5hNKI4l6NlgrgVBd", 1], [5, 133.69, 31.25], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 1.1, 3, 16, [4, 4292269782]], [10, "lblNickname", 17, [[3, "leolentop", 26.400000000000013, 48, 1, 1, 1, -179, [95]]], [0, "faoN9W+jRE8ofgW9OyxDDK", 1], [4, 4279679739], [5, 200, 30], [-76.1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lblCoin", 17, [[3, "50.000.000", 26.400000000000013, 48, 1, 1, 1, -180, [96]]], [0, "f5H7S0IElILqXrZYrLkGdQ", 1], [5, 150, 30], [137, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lineNgang", 17, [[4, 0, -181, [97], 98]], [0, "73uw7T9ZVFupI+x4V0TzHx", 1], [5, 368, 25], [0, -31.9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "1", 11, [-182], [0, "6cjiH5vwtAdY3AXk5nWUOc", 1], [5, 368, 33], [0, -66.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lblPage", 96, [[3, "...", 26.400000000000013, 48, 1, 1, 1, -183, [99]]], [0, "56q62bMJhIHIqI+VHvDK76", 1], [4, 4279679739], [5, 100, 30], [-76.1, 8.8, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 14, 1, 0, 0, 1, 0, 15, 52, 0, 16, 51, 0, 17, 44, 0, 18, 42, 0, 19, 54, 0, 20, 7, 0, 0, 1, 0, -1, 18, 0, -2, 12, 0, -3, 2, 0, -4, 39, 0, -5, 40, 0, -6, 41, 0, -7, 43, 0, -8, 45, 0, -9, 46, 0, -10, 47, 0, -11, 48, 0, -12, 49, 0, -13, 50, 0, -14, 19, 0, -15, 20, 0, -16, 53, 0, -17, 13, 0, -18, 8, 0, -19, 9, 0, -20, 21, 0, 0, 2, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, -5, 36, 0, -6, 37, 0, -7, 38, 0, -1, 62, 0, -2, 64, 0, -3, 66, 0, -4, 68, 0, -5, 70, 0, -6, 72, 0, -7, 14, 0, -1, 80, 0, -2, 82, 0, -3, 84, 0, -4, 86, 0, -5, 88, 0, -6, 90, 0, -7, 16, 0, 0, 5, 0, 4, 74, 0, 5, 10, 0, 6, 71, 0, 7, 67, 0, 8, 69, 0, 9, 65, 0, 10, 63, 0, 11, 25, 0, 12, 24, 0, 0, 5, 0, -1, 23, 0, -3, 10, 0, -4, 26, 0, 0, 6, 0, 4, 92, 0, 5, 11, 0, 6, 89, 0, 7, 85, 0, 8, 87, 0, 9, 83, 0, 10, 81, 0, 11, 29, 0, 12, 28, 0, 0, 6, 0, -1, 27, 0, -3, 11, 0, -4, 30, 0, 0, 7, 0, 0, 8, 0, -1, 56, 0, -2, 57, 0, -3, 58, 0, 0, 9, 0, -1, 59, 0, -2, 60, 0, -3, 61, 0, 0, 10, 0, -1, 15, 0, -2, 78, 0, 0, 11, 0, -1, 17, 0, -2, 96, 0, 0, 12, 0, 0, 12, 0, -1, 31, 0, 21, 13, 0, 0, 13, 0, -1, 55, 0, 0, 14, 0, -2, 74, 0, -1, 73, 0, -1, 75, 0, -2, 76, 0, -3, 77, 0, 0, 16, 0, -2, 92, 0, -1, 91, 0, -1, 93, 0, -2, 94, 0, -3, 95, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -2, 51, 0, 0, 20, 0, -2, 52, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, -1, 24, 0, -2, 25, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, -1, 28, 0, -2, 29, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, -1, 42, 0, -1, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, -1, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, -1, 63, 0, -1, 65, 0, -1, 67, 0, -1, 69, 0, -1, 71, 0, 0, 72, 0, 0, 73, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, -1, 79, 0, 0, 79, 0, -1, 81, 0, -1, 83, 0, -1, 85, 0, -1, 87, 0, -1, 89, 0, 0, 90, 0, 0, 91, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, -1, 97, 0, 0, 97, 0, 22, 1, 3, 3, 5, 4, 3, 6, 5, 3, 7, 6, 3, 7, 7, 3, 22, 183], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 44, 54, 67, 71, 85, 89], [-1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, -1, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 1, -1, 2, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 1, -1, 2, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 13, -1, -2, 2, 2, 2, 2, 2, 2, 2], [0, 12, 0, 13, 0, 14, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 15, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 16, 0, 17, 0, 0, 18, 0, 4, 0, 4, 0, 4, 0, 5, 0, 5, 0, 5, 6, 0, 2, 0, 0, 0, 0, 0, 0, 7, 0, 1, 0, 8, 0, 0, 0, 3, 0, 0, 3, 0, 6, 0, 2, 0, 0, 0, 0, 0, 0, 7, 0, 1, 0, 8, 0, 0, 0, 3, 0, 0, 3, 0, 9, 9, 19, 1, 1, 20, 10, 11, 10, 11]]