[1, ["02vgCgHI5Nep/MJzOhsvOD"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "card", "\ncard.png\nsize: 1884,191\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ncardB\n  rotate: false\n  xy: 1817, 100\n  size: 66, 90\n  orig: 66, 90\n  offset: 0, 0\n  index: -1\ncard_01_0\n  rotate: false\n  xy: 1, 96\n  size: 70, 94\n  orig: 70, 94\n  offset: 0, 0\n  index: -1\ncard_01_1\n  rotate: false\n  xy: 72, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_01_2\n  rotate: false\n  xy: 71, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_01_3\n  rotate: false\n  xy: 142, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_02_0\n  rotate: false\n  xy: 141, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_02_1\n  rotate: false\n  xy: 212, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_02_2\n  rotate: false\n  xy: 211, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_02_3\n  rotate: false\n  xy: 282, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_03_0\n  rotate: false\n  xy: 281, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_03_1\n  rotate: false\n  xy: 352, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_03_2\n  rotate: false\n  xy: 351, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_03_3\n  rotate: false\n  xy: 422, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_04_0\n  rotate: false\n  xy: 421, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_04_1\n  rotate: false\n  xy: 492, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_04_2\n  rotate: false\n  xy: 491, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_04_3\n  rotate: false\n  xy: 562, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_05_0\n  rotate: false\n  xy: 561, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_05_1\n  rotate: false\n  xy: 632, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_05_2\n  rotate: false\n  xy: 631, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_05_3\n  rotate: false\n  xy: 702, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_06_0\n  rotate: false\n  xy: 701, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_06_1\n  rotate: false\n  xy: 772, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_06_2\n  rotate: false\n  xy: 771, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_06_3\n  rotate: false\n  xy: 842, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_07_0\n  rotate: false\n  xy: 841, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_07_1\n  rotate: false\n  xy: 912, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_07_2\n  rotate: false\n  xy: 911, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_07_3\n  rotate: false\n  xy: 982, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_08_0\n  rotate: false\n  xy: 981, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_08_1\n  rotate: false\n  xy: 1052, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_08_2\n  rotate: false\n  xy: 1051, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_08_3\n  rotate: false\n  xy: 1122, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_09_0\n  rotate: false\n  xy: 1121, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_09_1\n  rotate: false\n  xy: 1192, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_09_2\n  rotate: false\n  xy: 1191, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_09_3\n  rotate: false\n  xy: 1262, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_10_0\n  rotate: false\n  xy: 1261, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_10_1\n  rotate: false\n  xy: 1332, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_10_2\n  rotate: false\n  xy: 1331, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_10_3\n  rotate: false\n  xy: 1402, 96\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_11_0\n  rotate: false\n  xy: 1472, 96\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_11_1\n  rotate: false\n  xy: 1471, 1\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_11_2\n  rotate: false\n  xy: 1541, 96\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_11_3\n  rotate: false\n  xy: 1540, 1\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_12_0\n  rotate: false\n  xy: 1610, 96\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_12_1\n  rotate: false\n  xy: 1609, 1\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_12_2\n  rotate: false\n  xy: 1679, 96\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_12_3\n  rotate: false\n  xy: 1678, 1\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_13_0\n  rotate: false\n  xy: 1748, 96\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_13_1\n  rotate: false\n  xy: 1747, 1\n  size: 68, 94\n  orig: 68, 94\n  offset: 0, 0\n  index: -1\ncard_13_2\n  rotate: false\n  xy: 1401, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_13_3\n  rotate: false\n  xy: 1, 1\n  size: 69, 94\n  orig: 69, 94\n  offset: 0, 0\n  index: -1\ncard_w\n  rotate: false\n  xy: 1816, 5\n  size: 66, 90\n  orig: 66, 90\n  offset: 0, 0\n  index: -1\n", ["card.png"], {"skeleton": {"hash": "/1Er1iPItMP5tmjzD+7rTvOTcBY", "spine": "3.6.53", "width": 66, "height": 90}, "bones": [{"name": "root"}, {"name": "cardB", "parent": "root"}, {"name": "cardB2", "parent": "root"}], "slots": [{"name": "cardB", "bone": "cardB", "attachment": "cardB"}, {"name": "cardB2", "bone": "cardB2"}], "skins": {"default": {"cardB": {"cardB": {"width": 66, "height": 90}}, "cardB2": {"card_01_0": {"width": 70, "height": 94}, "card_01_1": {"width": 69, "height": 94}, "card_01_2": {"width": 69, "height": 94}, "card_01_3": {"width": 69, "height": 94}, "card_02_0": {"width": 69, "height": 94}, "card_02_1": {"width": 69, "height": 94}, "card_02_2": {"width": 69, "height": 94}, "card_02_3": {"width": 69, "height": 94}, "card_03_0": {"width": 69, "height": 94}, "card_03_1": {"width": 69, "height": 94}, "card_03_2": {"width": 69, "height": 94}, "card_03_3": {"width": 69, "height": 94}, "card_04_0": {"width": 69, "height": 94}, "card_04_1": {"width": 69, "height": 94}, "card_04_2": {"width": 69, "height": 94}, "card_04_3": {"width": 69, "height": 94}, "card_05_0": {"width": 69, "height": 94}, "card_05_1": {"width": 69, "height": 94}, "card_05_2": {"width": 69, "height": 94}, "card_05_3": {"width": 69, "height": 94}, "card_06_0": {"width": 69, "height": 94}, "card_06_1": {"width": 69, "height": 94}, "card_06_2": {"width": 69, "height": 94}, "card_06_3": {"width": 69, "height": 94}, "card_07_0": {"width": 69, "height": 94}, "card_07_1": {"width": 69, "height": 94}, "card_07_2": {"width": 69, "height": 94}, "card_07_3": {"width": 69, "height": 94}, "card_08_0": {"width": 69, "height": 94}, "card_08_1": {"width": 69, "height": 94}, "card_08_2": {"width": 69, "height": 94}, "card_08_3": {"width": 69, "height": 94}, "card_09_0": {"width": 69, "height": 94}, "card_09_1": {"width": 69, "height": 94}, "card_09_2": {"width": 69, "height": 94}, "card_09_3": {"width": 69, "height": 94}, "card_10_0": {"width": 69, "height": 94}, "card_10_1": {"width": 69, "height": 94}, "card_10_2": {"width": 69, "height": 94}, "card_10_3": {"width": 69, "height": 94}, "card_11_0": {"width": 68, "height": 94}, "card_11_1": {"width": 68, "height": 94}, "card_11_2": {"width": 68, "height": 94}, "card_11_3": {"width": 68, "height": 94}, "card_12_0": {"width": 68, "height": 94}, "card_12_1": {"width": 68, "height": 94}, "card_12_2": {"width": 68, "height": 94}, "card_12_3": {"width": 68, "height": 94}, "card_13_0": {"width": 68, "height": 94}, "card_13_1": {"width": 68, "height": 94}, "card_13_2": {"width": 69, "height": 94}, "card_13_3": {"width": 69, "height": 94}, "card_w": {"width": 66, "height": 90}}}}, "animations": {"00back": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}, "01b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_01_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "01c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_01_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "01r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_01_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "01t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_01_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "02b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_02_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "02c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_02_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "02r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_02_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "02t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_02_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "03b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_03_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "03c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_03_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "03r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_03_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "03t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_03_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "04b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_04_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "04c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_04_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "04r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_04_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "04t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_04_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "05b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_05_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "05c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_05_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "05r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_05_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "05t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_05_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "06b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_06_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "06c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_06_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "06r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_06_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "06t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_06_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "07b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_07_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "07c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_07_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "07r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_07_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "07t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_07_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "08b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_08_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "08c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_08_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "08r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_08_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "08t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_08_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "09b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_09_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "09c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_09_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "09r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_09_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "09t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_09_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "10b": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_10_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "10c": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_10_1"}, {"time": 1, "name": "card_10_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "10r": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_09_3"}, {"time": 1, "name": "card_10_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "10t": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_10_3"}, {"time": 1, "name": "card_10_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Jb": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_11_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Jc": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_11_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Jr": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_11_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Jt": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_11_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Kb": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_13_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Kc": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_13_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Kr": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_13_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Kt": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_13_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Qb": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_12_2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Qc": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_12_1"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Qr": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_12_0"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}, "Qt": {"slots": {"cardB2": {"attachment": [{"time": 0, "name": null}, {"time": 0.7333, "name": "card_12_3"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "cardB": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}, "cardB2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": -0.026, "y": 1}, {"time": 1, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]