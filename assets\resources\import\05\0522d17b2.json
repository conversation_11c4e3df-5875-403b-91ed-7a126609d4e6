[1, ["fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "deznjldl9OCqvTVWbhwSbm", "b995CFj8dNMpbHogwXFKIr", "53QF0OyZ1D55fgjwVxpBcs", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30", "4bqocBVd1A1Z1tk+CYaXMY"], ["node", "_spriteFrame", "root", "_N$target", "_N$content", "data", "_N$normalSprite", "_defaultClip", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 1, 9, 4, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_spriteFrame"], 0, 1, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 6, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["a06776DJApOU6C17/CrzIrp", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1]], [[7, 0, 1, 2], [0, 0, 4, 2, 3, 5, 2], [2, 1, 7, 1], [4, 0, 2], [0, 0, 6, 2, 3, 2], [0, 0, 4, 6, 2, 3, 5, 7, 2], [0, 0, 1, 4, 2, 3, 5, 7, 3], [0, 0, 4, 6, 2, 3, 5, 8, 7, 2], [0, 0, 4, 6, 2, 3, 5, 8, 2], [0, 0, 4, 2, 3, 5, 8, 2], [5, 0, 1, 2, 1], [6, 0, 1], [2, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3], [1, 0, 3, 4, 2], [1, 2, 0, 1, 3, 4, 4], [1, 3, 4, 1], [1, 0, 1, 3, 4, 3], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2]], [[[[3, "HelpView"], [4, "HelpView", [-4, -5, -6, -7], [[10, -2, [6, 7], 5], [11, -3]], [0, "838ha0DwdFJpGFSgldxZYW", -1]], [5, "btnClose", 1, [-10], [[12, 3, -9, [[13, "a06776DJApOU6C17/CrzIrp", "closeFinished", 1]], [4, 4294967295], [4, 4294967295], -8, 4]], [0, "fcVbN+sPJGL7mmjKcfmt1J", 1], [5, 80, 80], [533, 285, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "black", 100, 1, [[14, 0, -11, 0], [2, -12, [4, 4292269782]]], [0, "78pGZyvUJA5oHriyteaCEB", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[15, 1, 0, false, -13, 1], [2, -14, [4, 4292269782]]], [0, "ab5AbAERVAMKK/waZS8DYm", 1], [5, 1100, 618]], [7, "scrollview", 1, [-17], [[18, false, 0.75, 0.23, null, null, -16, -15]], [0, "a3zZQEX09MI6QGBjMsHBg4", 1], [5, 1020, 510], [0, 0.5, 1], [0, 225, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "view", 5, [-19], [[19, 0, -18]], [0, "98WM1Cc3pG+KnZR0ahS8Uc", 1], [5, 1000, 470], [0, 0.5, 1]], [9, "<PERSON><PERSON><PERSON><PERSON>", 6, [[16, -20, 2]], [0, "bcf0oVCDVFO7CDMlj+sNnq", 1], [5, 926, 1232], [0, 0.5, 1]], [1, "sprite", 2, [[17, 2, false, -21, 3]], [0, "3eKxP0yVlPs4sbVYH3+4m/", 1], [5, 74, 74]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 2, 0, 3, 2, 0, 0, 2, 0, -1, 8, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 4, 7, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 8, 0, 5, 1, 21], [0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 6, 7, -1, -2], [1, 2, 3, 4, 5, 0, 0, 6]], [[{"name": "HuongDan_TLMN", "rect": [0, 0, 926, 1232], "offset": [0, 0], "originalSize": [926, 1232], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [8], [7]]]]