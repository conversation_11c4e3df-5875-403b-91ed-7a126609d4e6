[1, ["ecpdLyjvZBwrvm+cedCcQy", "183xriN5hIEb2s3bCmguiW", "66ZHnhEd9FIoTk3M8WiIgu", "2bA6LuuohGOI7zwgNBJjqX", "c4/0h4tWpM2aaYrKVXpnqx", "442zOVaSBDJpzNOYjRL+Oj", "75GfQTmypMmogM4r+gp69Y", "c2fnAjgzxBw6m6E6ChlhXy", "4aqa+FdRJJjIVHGUQqzTKg", "93izQq57FPv4o1I31dxAqb", "aa2tU7LZ9CiJi4An7fpTYl", "79uTbaoGpFeoF0gV7Amo4I", "f0TpsVh71L9ovyr8N50AmZ", "05PVBohdxCDrOurv7JfDCL", "ffwD6gbEtCJ6eZ8bsn265m", "afC2RewL5I6ZJo0U7QDS94", "c1r0of5idKQ6fAgPAwJnnP", "e2bc5ZF6xP34IgaBTzZaoA", "3fHNsQnqJABrV4ymGOZwqO", "2bVIFoRuZEPZME1m4A6dY5", "68W7G7MXJGBKkX+dKnbv35", "541bAIRv5KzZGLFVwOl7FL", "64abm/kC9Nz6N0ShrueWUn", "ccwxLE1H1OUqcJ2ALXojX8", "8fC5DNXfVET60Li5fdlHQ2", "e2Bhjd6WxKo4p/P24oU5/j", "fdNoodJKVLj4dF1TLppv2g", "b5G5suVFJCT5tcwN6lI8Vw", "72cpOOA2JO458okxmDazWk", "a9VpD0DP5LJYQPXITZq+uj", "2a74N91XtPq7ulcLY9icfO", "4b4clu0YdGvKu9WhXWkE9e", "a2d2rCW/tGCKF3KQoCstEr", "c1kTxBH1VHgaNdx+dnsTRV", "c8wNKQSt9NXIYJ7kJ+S2Gz", "7fiDddcq1Po5vKssRRIuiM", "825TQ2kU9Ktq1Ncj5HdPmn", "94qTDfxx1Jdo+HoT1Ml9Vp", "2cWB/vWPRHja3uQTinHH30", "76nMXMgCVDqJfyhd9qITe2", "43K5x48O1FKqgMrxd4KxNJ", "88UiRsSGhEWZmeGdPWZq+J", "54q+k4gsdL65uuU8y2COF+", "bf/ctfOAtKdqyKGLP1mAfn", "dbU+1UzFpO8K83PKHgfjTf", "f8KW6XL0lKhKfKWvkj+mkw", "5cwRM3MS9B6r1gHRG6Re/H", "94Q7rzw6BFg5GRvCIEtdT0", "7f6tt9Vm5D+LxgEyofSdfE", "582IOib/VNRagbiFeMWaxi", "a8zpbDeVBDuqcdJeUmCATB", "feCqaJbuBPO7WWyhxEAM0D", "91+MwxBsRLQ51wXeEr+O7E", "f8aJBthKNCqYLDomQmKnxB", "69S5u360dItoB7F9OmU4jJ", "66DmDX12FPNKxmEbZFj/zH", "44ige9OG5Ni6X03yMLqkTc", "ddzCn6rqtEt72VjVRxxevB", "6dwriY9JpHI5xkrnGUrZxA", "c6IVNwRcFN24UkJhc9AQiL", "a2A1mxCYZAYKD49bQ1WDfx", "beGF53xrZP+pt17wMw0u0J", "9b53tXcEBLZ6AVTdl3Oepe", "32WMpePSJFS6p1mK67z4fV", "8eDvQ/FtdL8pVO/7m58SK+", "a8XKdAc5NM651VPoAlE3Qo", "11mzvP38pPgI7AXOjXUUWm"], ["node", "_spriteFrame", "_textureSetter", "_N$target", "root", "data", "_N$normalSprite", "_defaultClip"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children"], 1, 9, 4, 1, 5, 7, 2], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 6, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "_resize", "node", "_layoutSize"], -2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["371f5ZxOGNC/Lk66fr6zg0i", ["node", "nodeLines"], 3, 1, 2]], [[4, 0, 1, 2], [1, 0, 4, 2, 3, 5, 2], [3, 2, 3, 4, 1], [2, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 4, 7, 2, 3, 5, 6, 2], [3, 0, 1, 2, 3, 4, 3], [6, 0, 1, 2, 3, 4], [1, 0, 4, 2, 3, 5, 6, 2], [6, 0, 1, 3, 3], [7, 0, 2], [1, 0, 7, 2, 3, 2], [1, 0, 1, 4, 2, 3, 5, 6, 3], [8, 0, 1, 2, 1], [9, 0, 1, 1], [4, 1, 1], [5, 2, 0, 1, 3, 5, 6, 5], [5, 4, 0, 1, 5, 6, 4], [2, 0, 1, 2, 3, 4, 5, 6, 7, 3], [2, 2, 8, 1], [3, 0, 2, 3, 4, 2]], [[[{"name": "chon-dong_13", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [27]], [[{"name": "chon-dong_01", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [28]], [[[9, "cbBetLinesView"], [10, "cbBetLinesView", [-29, -30, -31, -32, -33], [[12, -2, [118, 119], 117], [13, -28, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27]]], [14, -1]], [4, "layout-lines", 1, [-35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59], [[15, false, 3, 27, 13, -34, [5, 663, 350]]], [0, "a03t6+HDpHv5IXmk/JjwRc", 1], [5, 663, 350], [5, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "layout-button", 1, [-61, -62, -63, -64], [[16, 1, 1, 68, -60, [5, 1020, 60]]], [0, "efIoU8tI1PS5odgPo9NoEn", 1], [5, 1020, 60], [0, -229, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "button", 2, [-67, -68], [[3, 1.05, 3, -66, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "0", 1]], [4, 4294967295], [4, 4294967295], -65]], [0, "fa/6W51N9INIIoqlm4F/f1", 1], [5, 110, 60], [-276.5, 145, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-71, -72], [[3, 1.05, 3, -70, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "1", 1]], [4, 4294967295], [4, 4294967295], -69]], [0, "bfdMKBmzxKxZfu4yWnlZka", 1], [5, 110, 60], [-139.5, 145, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-75, -76], [[3, 1.05, 3, -74, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "2", 1]], [4, 4294967295], [4, 4294967295], -73]], [0, "73PjanN71OXolvusx2whzC", 1], [5, 110, 60], [-2.5, 145, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-79, -80], [[3, 1.05, 3, -78, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "3", 1]], [4, 4294967295], [4, 4294967295], -77]], [0, "fagmTaEmtIgLYzjP+KTTbe", 1], [5, 110, 60], [134.5, 145, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-83, -84], [[3, 1.05, 3, -82, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "4", 1]], [4, 4294967295], [4, 4294967295], -81]], [0, "f3SlEwRIVItZTxKd8b2zCu", 1], [5, 110, 60], [271.5, 145, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-87, -88], [[3, 1.05, 3, -86, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "5", 1]], [4, 4294967295], [4, 4294967295], -85]], [0, "23vo6k10dAnL18HBjQBXxC", 1], [5, 110, 60], [-276.5, 72, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-91, -92], [[3, 1.05, 3, -90, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "6", 1]], [4, 4294967295], [4, 4294967295], -89]], [0, "5e9cMIV0lCGJZP/PJt2jYX", 1], [5, 110, 60], [-139.5, 72, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-95, -96], [[3, 1.05, 3, -94, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "7", 1]], [4, 4294967295], [4, 4294967295], -93]], [0, "2fnLGDVvVGjpNao1+hGagi", 1], [5, 110, 60], [-2.5, 72, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-99, -100], [[3, 1.05, 3, -98, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "8", 1]], [4, 4294967295], [4, 4294967295], -97]], [0, "a6IYQUrRJKv4WcctUonEq2", 1], [5, 110, 60], [134.5, 72, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-103, -104], [[3, 1.05, 3, -102, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "9", 1]], [4, 4294967295], [4, 4294967295], -101]], [0, "24hLfLzqtL1oDfKi6P1jCZ", 1], [5, 110, 60], [264.5, 72, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-107, -108], [[3, 1.05, 3, -106, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "10", 1]], [4, 4294967295], [4, 4294967295], -105]], [0, "46U8HrtdxB6LYwPSSHpiMQ", 1], [5, 110, 60], [-283.5, -1, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-111, -112], [[3, 1.05, 3, -110, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "11", 1]], [4, 4294967295], [4, 4294967295], -109]], [0, "42arRJZM1KibdSMcl8LKHW", 1], [5, 110, 60], [-146.5, -1, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-115, -116], [[3, 1.05, 3, -114, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "12", 1]], [4, 4294967295], [4, 4294967295], -113]], [0, "f54gAhpHRDgoHelislziL7", 1], [5, 110, 60], [-9.5, -1, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-119, -120], [[3, 1.05, 3, -118, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "13", 1]], [4, 4294967295], [4, 4294967295], -117]], [0, "ddKrfn6KhB55D5kuSftHn7", 1], [5, 110, 60], [127.5, -1, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-123, -124], [[3, 1.05, 3, -122, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "14", 1]], [4, 4294967295], [4, 4294967295], -121]], [0, "ddAAKoUW5JbJgNPdCb+9H+", 1], [5, 110, 60], [264.5, -1, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-127, -128], [[3, 1.05, 3, -126, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "15", 1]], [4, 4294967295], [4, 4294967295], -125]], [0, "f1wnFg++ZLH4iOH4CYKdzV", 1], [5, 110, 60], [-283.5, -74, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-131, -132], [[3, 1.05, 3, -130, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "16", 1]], [4, 4294967295], [4, 4294967295], -129]], [0, "81PdzHy+RKRaDiXArtKRbg", 1], [5, 110, 60], [-146.5, -74, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-135, -136], [[3, 1.05, 3, -134, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "17", 1]], [4, 4294967295], [4, 4294967295], -133]], [0, "f5eeh0P+pAubJqaANN/ogd", 1], [5, 110, 60], [-9.5, -74, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-139, -140], [[3, 1.05, 3, -138, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "18", 1]], [4, 4294967295], [4, 4294967295], -137]], [0, "e1LqtwodlBPa2/Nk0WteYL", 1], [5, 110, 60], [127.5, -74, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-143, -144], [[3, 1.05, 3, -142, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "19", 1]], [4, 4294967295], [4, 4294967295], -141]], [0, "3bVvVoXFBA95kzTjNVfme6", 1], [5, 110, 60], [264.5, -74, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-147, -148], [[3, 1.05, 3, -146, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "20", 1]], [4, 4294967295], [4, 4294967295], -145]], [0, "3eKUI/6tpHubUYXPuLZTha", 1], [5, 110, 60], [-283.5, -147, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-151, -152], [[3, 1.05, 3, -150, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "21", 1]], [4, 4294967295], [4, 4294967295], -149]], [0, "2ecZc/03tLJ7AuLixZ1B2O", 1], [5, 110, 60], [-146.5, -147, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-155, -156], [[3, 1.05, 3, -154, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "22", 1]], [4, 4294967295], [4, 4294967295], -153]], [0, "c3bhDA07lJZY/Z2vjrP/Rv", 1], [5, 110, 60], [-9.5, -147, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-159, -160], [[3, 1.05, 3, -158, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "23", 1]], [4, 4294967295], [4, 4294967295], -157]], [0, "2dr0XuRzNBA47A5sRT8E1L", 1], [5, 110, 60], [127.5, -147, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "button", 2, [-163, -164], [[3, 1.05, 3, -162, [[6, "371f5ZxOGNC/Lk66fr6zg0i", "selectLineClicked", "24", 1]], [4, 4294967295], [4, 4294967295], -161]], [0, "ef9t8g+ZZBaqW4SFZ5OMQB", 1], [5, 110, 60], [264.5, -147, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "btnEven", 3, [[2, -165, [106], 107], [3, 0.9, 3, -167, [[8, "371f5ZxOGNC/Lk66fr6zg0i", "selectEvenClicked", 1]], [4, 4294967295], [4, 4294967295], -166]], [0, "c3qwyHHhZC6ZTSTxn8VsCY", 1], [5, 204, 74], [-408, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "btnOdd", 3, [[2, -168, [108], 109], [3, 0.9, 3, -170, [[8, "371f5ZxOGNC/Lk66fr6zg0i", "selectOddClicked", 1]], [4, 4294967295], [4, 4294967295], -169]], [0, "7cME7wrHdERo647cpiYe54", 1], [5, 204, 74], [-136, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "btnNone", 3, [[2, -171, [110], 111], [3, 0.9, 3, -173, [[8, "371f5ZxOGNC/Lk66fr6zg0i", "selectNoneClicked", 1]], [4, 4294967295], [4, 4294967295], -172]], [0, "47sP5ks6BAuIFbYhPsC1Po", 1], [5, 204, 74], [136, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "btnAll", 3, [[2, -174, [112], 113], [3, 0.9, 3, -176, [[8, "371f5ZxOGNC/Lk66fr6zg0i", "selectAllClicked", 1]], [4, 4294967295], [4, 4294967295], -175]], [0, "9cwJnsbK9IBadv5C+YrYlF", 1], [5, 204, 74], [408, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "btnClose", 1, [[17, 0.9, 3, -178, [[8, "371f5ZxOGNC/Lk66fr6zg0i", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -177, 114], [5, 2, false, -179, [115], 116]], [0, "47s/c/ptdOK5mTawXikTS0", 1], [5, 83, 75], [481.926, 235.087, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "black", 100, 1, [[19, 0, -180, [0], 1], [18, -181, [4, 4292269782]]], [0, "5fd79+y/BAAqpeRcMCn7Ut", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nen popup", 1, [-183], [[2, -182, [4], 5]], [0, "1fwY/bPplBdIiIvdTWcC5a", 1], [5, 1091, 576], [-9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "off", 4, [[5, 2, false, -184, [8], 9]], [0, "7ehM4PmBhGj4tJTJTTfM0h", 1], [5, 114, 56]], [1, "off", 5, [[5, 2, false, -185, [12], 13]], [0, "b2ps92NeVE9YpnAiIQUgIl", 1], [5, 116, 56]], [1, "off", 6, [[5, 2, false, -186, [16], 17]], [0, "2dqcOph2tKBbjeqflNFu42", 1], [5, 114, 56]], [1, "off", 7, [[5, 2, false, -187, [20], 21]], [0, "d0cewHb3NKW4jREQ23YN16", 1], [5, 114, 56]], [1, "off", 8, [[5, 2, false, -188, [24], 25]], [0, "34pfp5AWJJTbSL9eNCZp23", 1], [5, 114, 57]], [1, "off", 9, [[5, 2, false, -189, [28], 29]], [0, "f0Wbum+UJAwZjyEHTioNAX", 1], [5, 114, 56]], [1, "off", 10, [[5, 2, false, -190, [32], 33]], [0, "10kd2Z329DCYuoIG6/VtpE", 1], [5, 114, 56]], [1, "off", 11, [[5, 2, false, -191, [36], 37]], [0, "6bdRwWtD9KGJytwyzzZUnb", 1], [5, 114, 56]], [1, "off", 12, [[5, 2, false, -192, [40], 41]], [0, "32/fHvh15IeqnGDHj/yci7", 1], [5, 114, 56]], [1, "off", 13, [[5, 2, false, -193, [44], 45]], [0, "beRRphqzFO9KXPYawDCGIo", 1], [5, 114, 56]], [1, "off", 14, [[5, 2, false, -194, [48], 49]], [0, "fdKFktLxtLiYIhMV9JqRTk", 1], [5, 114, 56]], [1, "off", 15, [[5, 2, false, -195, [52], 53]], [0, "97z+n0aDNGB6j9881YH4We", 1], [5, 114, 56]], [1, "off", 16, [[5, 2, false, -196, [56], 57]], [0, "b0UqIye29KdJP0/TN/R4K9", 1], [5, 114, 56]], [1, "off", 17, [[5, 2, false, -197, [60], 61]], [0, "7az8XsA+dKPIc4KnrntSN2", 1], [5, 114, 56]], [1, "off", 18, [[5, 2, false, -198, [64], 65]], [0, "cakkykMUVKe4aFdyxJ12Be", 1], [5, 114, 56]], [1, "off", 19, [[5, 2, false, -199, [68], 69]], [0, "c4ARPSOqdFsKVEbXpf+Mpl", 1], [5, 115, 56]], [1, "off", 20, [[5, 2, false, -200, [72], 73]], [0, "2fkIOt9mBD/bEsA0Hns/39", 1], [5, 114, 56]], [1, "off", 21, [[5, 2, false, -201, [76], 77]], [0, "deeGuO/CRAKYhDI17XT6YM", 1], [5, 114, 56]], [1, "off", 22, [[5, 2, false, -202, [80], 81]], [0, "15DPhUfyJHErisb3hQ71RK", 1], [5, 114, 56]], [1, "off", 23, [[5, 2, false, -203, [84], 85]], [0, "ecskSwCBdDM6Gjc3HS0kK+", 1], [5, 114, 56]], [1, "off", 24, [[5, 2, false, -204, [88], 89]], [0, "85jxNhLmlLbZtjcw9ubrNU", 1], [5, 115, 56]], [1, "off", 25, [[5, 2, false, -205, [92], 93]], [0, "7awrlHJu5Kt4tVuCBS5KFP", 1], [5, 115, 56]], [1, "off", 26, [[5, 2, false, -206, [96], 97]], [0, "1b48W3OHpOJJBP15PE/M73", 1], [5, 114, 56]], [1, "off", 27, [[5, 2, false, -207, [100], 101]], [0, "eaP2qRhBFMUIYi0r5L5DPB", 1], [5, 114, 56]], [1, "off", 28, [[5, 2, false, -208, [104], 105]], [0, "9aDilUg8VDLo+5ta/lz8to", 1], [5, 114, 56]], [7, "title_chonDong", 35, [[2, -209, [2], 3]], [0, "bcgT/wvc9HLIfRX3w9BJQ1", 1], [5, 211, 80], [3.8, 241, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "on", 4, [[2, -210, [6], 7]], [0, "12ZjWL67xMuLBJa72IONo4", 1], [5, 114, 56]], [1, "on", 5, [[2, -211, [10], 11]], [0, "27dwyvnTpC+agf1ipQN4bw", 1], [5, 116, 56]], [1, "on", 6, [[2, -212, [14], 15]], [0, "2emqZqjNxB6rcdn5YOdbid", 1], [5, 114, 56]], [1, "on", 7, [[2, -213, [18], 19]], [0, "88JXGOm/5Ap5WGsPpKtgfU", 1], [5, 114, 56]], [1, "on", 8, [[2, -214, [22], 23]], [0, "fat99RH1FDuII6Km3z2Ge5", 1], [5, 114, 57]], [1, "on", 9, [[2, -215, [26], 27]], [0, "a8dbWfTNdIL4V6Z9ygUaox", 1], [5, 114, 56]], [1, "on", 10, [[2, -216, [30], 31]], [0, "71yfJSvPxO35s9tB/mFpHh", 1], [5, 114, 56]], [1, "on", 11, [[2, -217, [34], 35]], [0, "90so/xzxpEAYlq5QkoCWy8", 1], [5, 114, 56]], [1, "on", 12, [[2, -218, [38], 39]], [0, "72+egPT3VJRZ6Bk4tEIASo", 1], [5, 114, 56]], [1, "on", 13, [[2, -219, [42], 43]], [0, "c1BB2ipcdH8JmQ4G8ztzOk", 1], [5, 114, 56]], [1, "on", 14, [[2, -220, [46], 47]], [0, "015OOZGKBLeq0bNjDH4sU0", 1], [5, 114, 56]], [1, "on", 15, [[2, -221, [50], 51]], [0, "754nueCilN7Yi/az+UVw0m", 1], [5, 114, 56]], [1, "on", 16, [[2, -222, [54], 55]], [0, "23uPW0JwhBKKGkLD3MzpB7", 1], [5, 114, 56]], [1, "on", 17, [[2, -223, [58], 59]], [0, "034X3AkfRPN63igz7we+Nf", 1], [5, 114, 56]], [1, "on", 18, [[2, -224, [62], 63]], [0, "37d/nLU5NGoJ7/5l21wj1h", 1], [5, 114, 56]], [1, "on", 19, [[2, -225, [66], 67]], [0, "5fUJGfArFK9osko/tUp3aN", 1], [5, 115, 56]], [1, "on", 20, [[2, -226, [70], 71]], [0, "adpfYXuZhMl7Y/EFNXuwWi", 1], [5, 114, 56]], [1, "on", 21, [[2, -227, [74], 75]], [0, "13sOu8Dm9AW6RwYeZSGnwv", 1], [5, 114, 56]], [1, "on", 22, [[2, -228, [78], 79]], [0, "e3wdbLRJpM9rKU3G88Ofx1", 1], [5, 114, 56]], [1, "on", 23, [[2, -229, [82], 83]], [0, "47lBTFxoJNOLQrgxNW7fxZ", 1], [5, 114, 56]], [1, "on", 24, [[2, -230, [86], 87]], [0, "b3SoS3eGtG3Ixe9L4PXxmh", 1], [5, 115, 56]], [1, "on", 25, [[2, -231, [90], 91]], [0, "edQp5L8nlGxpNxginBGS9c", 1], [5, 115, 56]], [1, "on", 26, [[2, -232, [94], 95]], [0, "92DmHJxjNKQKO0KmxxMAD2", 1], [5, 114, 56]], [1, "on", 27, [[2, -233, [98], 99]], [0, "d5QAF20EpIyKvDVWBoIvld", 1], [5, 114, 56]], [1, "on", 28, [[2, -234, [102], 103]], [0, "c2eUrJMEhHRrTtgWFL9IOl", 1], [5, 114, 56]]], 0, [0, 4, 1, 0, 0, 1, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -4, 39, 0, -5, 40, 0, -6, 41, 0, -7, 42, 0, -8, 43, 0, -9, 44, 0, -10, 45, 0, -11, 46, 0, -12, 47, 0, -13, 48, 0, -14, 49, 0, -15, 50, 0, -16, 51, 0, -17, 52, 0, -18, 53, 0, -19, 54, 0, -20, 55, 0, -21, 56, 0, -22, 57, 0, -23, 58, 0, -24, 59, 0, -25, 60, 0, 0, 1, 0, -1, 34, 0, -2, 35, 0, -3, 2, 0, -4, 3, 0, -5, 33, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, -8, 11, 0, -9, 12, 0, -10, 13, 0, -11, 14, 0, -12, 15, 0, -13, 16, 0, -14, 17, 0, -15, 18, 0, -16, 19, 0, -17, 20, 0, -18, 21, 0, -19, 22, 0, -20, 23, 0, -21, 24, 0, -22, 25, 0, -23, 26, 0, -24, 27, 0, -25, 28, 0, 0, 3, 0, -1, 29, 0, -2, 30, 0, -3, 31, 0, -4, 32, 0, 3, 4, 0, 0, 4, 0, -1, 62, 0, -2, 36, 0, 3, 5, 0, 0, 5, 0, -1, 63, 0, -2, 37, 0, 3, 6, 0, 0, 6, 0, -1, 64, 0, -2, 38, 0, 3, 7, 0, 0, 7, 0, -1, 65, 0, -2, 39, 0, 3, 8, 0, 0, 8, 0, -1, 66, 0, -2, 40, 0, 3, 9, 0, 0, 9, 0, -1, 67, 0, -2, 41, 0, 3, 10, 0, 0, 10, 0, -1, 68, 0, -2, 42, 0, 3, 11, 0, 0, 11, 0, -1, 69, 0, -2, 43, 0, 3, 12, 0, 0, 12, 0, -1, 70, 0, -2, 44, 0, 3, 13, 0, 0, 13, 0, -1, 71, 0, -2, 45, 0, 3, 14, 0, 0, 14, 0, -1, 72, 0, -2, 46, 0, 3, 15, 0, 0, 15, 0, -1, 73, 0, -2, 47, 0, 3, 16, 0, 0, 16, 0, -1, 74, 0, -2, 48, 0, 3, 17, 0, 0, 17, 0, -1, 75, 0, -2, 49, 0, 3, 18, 0, 0, 18, 0, -1, 76, 0, -2, 50, 0, 3, 19, 0, 0, 19, 0, -1, 77, 0, -2, 51, 0, 3, 20, 0, 0, 20, 0, -1, 78, 0, -2, 52, 0, 3, 21, 0, 0, 21, 0, -1, 79, 0, -2, 53, 0, 3, 22, 0, 0, 22, 0, -1, 80, 0, -2, 54, 0, 3, 23, 0, 0, 23, 0, -1, 81, 0, -2, 55, 0, 3, 24, 0, 0, 24, 0, -1, 82, 0, -2, 56, 0, 3, 25, 0, 0, 25, 0, -1, 83, 0, -2, 57, 0, 3, 26, 0, 0, 26, 0, -1, 84, 0, -2, 58, 0, 3, 27, 0, 0, 27, 0, -1, 85, 0, -2, 59, 0, 3, 28, 0, 0, 28, 0, -1, 86, 0, -2, 60, 0, 0, 29, 0, 3, 29, 0, 0, 29, 0, 0, 30, 0, 3, 30, 0, 0, 30, 0, 0, 31, 0, 3, 31, 0, 0, 31, 0, 0, 32, 0, 3, 32, 0, 0, 32, 0, 3, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, -1, 61, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 5, 1, 234], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 6, -1, 1, 7, -1, -2], [0, 29, 0, 30, 0, 31, 0, 1, 0, 1, 0, 2, 0, 2, 0, 3, 0, 3, 0, 4, 0, 4, 0, 5, 0, 5, 0, 6, 0, 6, 0, 7, 0, 7, 0, 8, 0, 8, 0, 9, 0, 9, 0, 10, 0, 10, 0, 11, 0, 11, 0, 12, 0, 12, 0, 13, 0, 13, 0, 14, 0, 14, 0, 15, 0, 15, 0, 16, 0, 16, 0, 17, 0, 17, 0, 18, 0, 18, 0, 19, 0, 19, 0, 20, 0, 20, 0, 21, 0, 21, 0, 22, 0, 22, 0, 23, 0, 23, 0, 24, 0, 24, 0, 25, 0, 25, 0, 32, 0, 33, 0, 34, 0, 35, 36, 0, 37, 26, 26, 38]], [[{"name": "TEXT", "rect": [0, 0, 211, 80], "offset": [0, 0], "originalSize": [211, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [39]], [[{"name": "chon-dong_03", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [40]], [[{"name": "chon-dong_19", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [41]], [[{"name": "chon-dong_18", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [42]], [[{"name": "chon-dong_05", "rect": [0, 0, 114, 57], "offset": [0, 0], "originalSize": [114, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [43]], [[{"name": "chon-dong_08", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [44]], [[{"name": "chon-dong_21", "rect": [0, 0, 115, 56], "offset": [0, 0], "originalSize": [115, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [45]], [[{"name": "chon-dong_22", "rect": [0, 0, 115, 56], "offset": [0, 0], "originalSize": [115, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [46]], [[{"name": "chon-dong_02", "rect": [0, 0, 116, 56], "offset": [0, 0], "originalSize": [116, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [47]], [[{"name": "chon-dong_20", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [48]], [[{"name": "chon-dong_06", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [49]], [[{"name": "chon-dong_11", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [50]], [[{"name": "tat_ca", "rect": [0, 0, 204, 74], "offset": [0, 0], "originalSize": [204, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [51]], [[{"name": "chon-dong_24", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [52]], [[{"name": "chon-dong_09", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [53]], [[{"name": "dong_chan", "rect": [0, 0, 204, 74], "offset": [0, 0], "originalSize": [204, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [54]], [[{"name": "chon-dong_10", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [55]], [[{"name": "chon-dong_15", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [56]], [[{"name": "dong_le", "rect": [0, 0, 204, 74], "offset": [0, 0], "originalSize": [204, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [57]], [[{"name": "chon-dong_16", "rect": [0, 0, 115, 56], "offset": [0, 0], "originalSize": [115, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [58]], [[{"name": "chon-dong_07", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [59]], [[{"name": "chon-dong_04", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [60]], [[{"name": "bo_chon", "rect": [0, 0, 204, 74], "offset": [0, 0], "originalSize": [204, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [61]], [[{"name": "chon-dong_23", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [62]], [[{"name": "chon-dong_25", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [63]], [[{"name": "chon-dong_17", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [64]], [[{"name": "chon-dong_12", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [65]], [[{"name": "chon-dong_14", "rect": [0, 0, 114, 56], "offset": [0, 0], "originalSize": [114, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [66]]]]