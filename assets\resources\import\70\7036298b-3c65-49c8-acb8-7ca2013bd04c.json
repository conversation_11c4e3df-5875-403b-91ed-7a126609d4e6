[1, ["3akwWxThBI6rPLHpgq8oa9"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "01_ani", "\n01_ani.png\nsize: 276,276\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbg\n  rotate: false\n  xy: 128, 14\n  size: 144, 129\n  orig: 144, 129\n  offset: 0, 0\n  index: -1\nboder\n  rotate: false\n  xy: 128, 143\n  size: 148, 133\n  orig: 148, 133\n  offset: 0, 0\n  index: -1\nitem\n  rotate: false\n  xy: 0, 109\n  size: 128, 167\n  orig: 128, 167\n  offset: 0, 0\n  index: -1\n", ["01_ani.png"], {"skeleton": {"hash": "6RbSVuYgU63uV5mVgTdKWdAJUCI", "spine": "3.6.53", "width": 148, "height": 167}, "bones": [{"name": "root"}, {"name": "bg", "parent": "root", "length": 49.12, "rotation": 90, "x": 48.33, "y": -2.9}, {"name": "item", "parent": "root", "length": 74.3, "rotation": 95.06, "x": -4.07, "y": -44.82}], "slots": [{"name": "bg", "bone": "bg", "attachment": "bg"}, {"name": "boder", "bone": "root", "attachment": "boder"}, {"name": "item", "bone": "item", "attachment": "item"}, {"name": "item2", "bone": "item", "color": "ffffff00", "attachment": "item", "blend": "additive"}], "skins": {"default": {"bg": {"bg": {"x": 2.9, "y": 48.33, "rotation": -90, "width": 144, "height": 129}}, "boder": {"boder": {"width": 148, "height": 133}}, "item": {"item": {"x": 46.81, "y": -9.58, "rotation": -95.06, "width": 128, "height": 167}}, "item2": {"item": {"x": 46.81, "y": -9.58, "rotation": -95.06, "width": 128, "height": 167}}}}, "animations": {"stay": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "bg": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4333, "x": 0, "y": 4.29}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}}}, "win": {"slots": {"item2": {"color": [{"time": 0.4333, "color": "ffffff55"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "bg": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4333, "x": -3.27, "y": 1.97}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4333, "x": 1.111, "y": 1.111}, {"time": 0.8333, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]