[1, ["ecpdLyjvZBwrvm+cedCcQy", "d82n49/IVAvIEqsa0xvvk0", "fdHIe7q1BD+o3g3g+0ROLn", "7a/QZLET9IDreTiBfRn2PD", "b3XTwJcjZPs5bYTy9/DACp", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "60lCKF0BFLRJpfGf89TIRK", "a9VpD0DP5LJYQPXITZq+uj", "bcA9x8VrVHIZuhsaeLDpBP", "b2TsuZH79L9qDB8oMJcx5J", "7aUsxJWKtMLLAJwLGB4DOs", "bcbgFB+epIlKdGw1JroyQo", "82MNbCjvRDvqBUPXUnVOWH", "19yfhhSdBBiaTY59/tZqp8", "bbgHJ4Wg1MBoGjUZFA0GJx", "d8+6zzU3ZI9oI9NLY9NIkb", "d8NgniSrNN+aN8H/8/G9LZ", "3byGsiA21OfIrG2sM4sfSL", "bfYE6RlXJBT5XAIMVTCp7r", "7fZXTGObxAqIPm0dyBARfV", "adw94Z+hpN57wutNivq8Q5", "2cWB/vWPRHja3uQTinHH30", "79kwd+hddCpZ7QvD32rA6n"], ["node", "_spriteFrame", "_N$file", "_N$skeletonData", "_parent", "fontRegurlar", "fontBold", "_defaultClip", "root", "taiXiuTopListView", "thuongtoplast", "thuongtop6", "thuongtop5", "thuongtop4", "thuongtop3", "thuongtop2", "thuongtop1", "rankSprite3", "rankSprite2", "rankSprite1", "lbTotalWin", "lbNickName", "lbRank", "_N$target", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_eulerAngles", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_fillType", "_fillStart", "_fillRange", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -3, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_enableWrapText", "_spacingX", "_N$overflow", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_color"], 2, 1, 2, 4, 5, 7, 2, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["2c87cPUVDFKYqiNAruqZ4qc", ["node", "lbRank", "lbNickName", "lbTotalWin", "rankSprite1", "rankSprite2", "rankSprite3", "thuongtop1", "thuongtop2", "thuongtop3", "thuongtop4", "thuongtop5", "thuongtop6", "thuongtoplast", "fontRegurlar", "fontBold"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["99d190+qxlEtotJQxNcxxFe", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["99a3aomNaJFV4qH/mx381pg", ["node", "taiXiuTopListView"], 3, 1, 1]], [[7, 0, 1, 2], [0, 0, 6, 4, 3, 5, 7, 2], [1, 0, 6, 7, 8, 2], [0, 0, 6, 8, 4, 3, 5, 7, 2], [2, 0, 1, 2, 7, 3, 4, 9, 10, 11, 7], [0, 0, 6, 4, 3, 9, 5, 7, 2], [2, 0, 1, 5, 6, 2, 7, 3, 4, 8, 9, 10, 11, 10], [0, 0, 6, 4, 3, 5, 7, 10, 2], [3, 0, 1, 6, 2, 3, 4, 5, 2], [0, 0, 6, 8, 4, 3, 5, 2], [3, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 7, 4, 5, 2], [1, 0, 6, 7, 2], [9, 0, 1, 2, 3, 4, 5, 6, 5], [6, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 1, 6, 4, 3, 5, 7, 3], [0, 0, 8, 3, 5, 7, 2], [0, 0, 6, 8, 3, 2], [0, 0, 8, 4, 3, 5, 7, 2], [0, 0, 2, 6, 4, 3, 9, 5, 7, 3], [0, 0, 6, 4, 3, 5, 11, 7, 2], [1, 6, 7, 8, 1], [1, 1, 0, 2, 3, 4, 6, 7, 8, 6], [1, 1, 0, 6, 7, 8, 3], [1, 1, 0, 5, 6, 7, 8, 4], [1, 6, 7, 1], [4, 1, 2, 1], [4, 0, 1, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3], [2, 0, 1, 5, 6, 2, 3, 4, 8, 9, 10, 9], [2, 0, 1, 5, 6, 2, 3, 4, 9, 11, 8], [2, 0, 1, 5, 6, 2, 3, 4, 9, 10, 8], [2, 0, 1, 5, 6, 2, 7, 3, 4, 8, 9, 10, 10], [5, 0, 1, 2, 4, 5, 4], [5, 0, 1, 3, 4, 5, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 1], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 3, 4, 5, 4], [14, 0, 1, 2, 1], [15, 0, 1, 1]], [[14, "taiXiuTopView"], [15, "taiXiuTopView", [-5, -6, -7, -8], [[40, -2, [71, 72], 70], [41, -4, -3]], [0, "a1NUL9h8tK6788yo3J5Jf1", -1]], [19, "<PERSON><PERSON>", [-23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36], [[36, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, 67, 68]], [0, "daXgaQxB1DM6FT02EdpSjD", 1], [5, 900, 50], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "title-left", [-37, -38, -39, -40, -41, -42, -43], [0, "7bDjzuSUNHQ7DaBP3nTGPe", 1], [5, 500, 50], [0, 259, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "nen popup", 1, [-45, -46, -47], [[25, 1, 0, false, -44, [24], 25]], [0, "3b0dsT6rBO3Lks3iKFQRao", 1], [5, 1150, 610]], [1, "btnClose", 4, [[22, -48, [2], 3], [28, 3, -50, [[29, "99a3aomNaJFV4qH/mx381pg", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -49]], [0, "71qoFOGThHbpk6a7dNAZMT", 1], [5, 131, 67], [511.7, 272.566, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "boxinside2", 4, [-52, 3], [[24, 1, 0, -51, [20], 21]], [0, "23hEbLuMlMIJwxfdJST4Km", 1], [5, 1100, 450], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layout-nick<PERSON><PERSON>", 2, [-54, -55], [[34, 1, 1, 5, -53, [5, 110.45, 50]]], [0, "29UNpnGb9M76WgMZyr3Cn1", 1], [5, 110.45, 50], [-187, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "top1", 2, [-57], [[2, 0, -56, [41], 42]], [0, "6bhPaeA9VBvoMuxi8UPzOb", 1], [5, 60, 50], [302, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "top2", 2, [-59], [[2, 0, -58, [45], 46]], [0, "00AF9gHORJlaQc6EBMZyHF", 1], [5, 60, 50], [302, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "top3", 2, [-61], [[2, 0, -60, [49], 50]], [0, "aaX+DqSxxDSoBAAqtEv8u3", 1], [5, 60, 50], [302, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "topother", 2, [-63], [[2, 0, -62, [53], 54]], [0, "0bh5vstMBMiZAYPcxiYM1p", 1], [5, 40, 35], [302, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "top4", 2, [-65], [[2, 0, -64, [57], 58]], [0, "c0KXxNagRNHrn8Sqay0b51", 1], [5, 60, 50], [302, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "top5", 2, [-67], [[2, 0, -66, [61], 62]], [0, "4f4mbOXddAlIuqSjn6MOUV", 1], [5, 50, 40], [302, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "top6", 2, [-69], [[2, 0, -68, [65], 66]], [0, "a3PoGdKG1CTayZBawv8wFx", 1], [5, 40, 40], [302, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scrollview", 1, [-72], [-70, -71], [0, "7dYoD2m1hFaLqhkdEJOU4Z", 1], [5, 1150, 400], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "black", 100, 1, [[2, 0, -73, [0], 1], [27, -74, [4, 4292269782]]], [0, "ddF0lWsbpDlpgT+vu/YpWY", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "rankSprite1", 2, [-76], [-75], [0, "b45IrD4j1IO7q3ajK2MTBX", 1], [5, 27, 20], [-431.8, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "rankSprite2", 2, [-78], [-77], [0, "5bo0n4Gj5L6Kd7W2P8rjdd", 1], [5, 27, 20], [-431.8, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 15, [-80], [[37, 0, -79, [69]]], [0, "670MfFlZxBQaYf+46HO4u9", 1], [5, 1150, 390]], [21, "content", 19, [[35, 1, 2, 10, -81, [5, 1150, 0]]], [0, "af7qEwzvJK6oj7fcUr2qNe", 1], [5, 1150, 0], [0, 0.5, 1], [0, 199, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "boderNhoCopy", 6, [[23, 1, 0, 1, 1, 1, -82, [4], 5]], [0, "edyDFhX6RLX68uR/oXY8+D", 1], [5, 1100, 55], [-0.125, 213.339, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbTime", 3, [[6, "XẾP HẠNG", 19, 48, false, false, -2, 1, 1, 1, -83, [6], 7]], [0, "78ujEqmGhBfLWEdHkfRPVZ", 1], [4, 4294704124], [5, 240, 50], [-434, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbNickName", 3, [[6, "VINH DANH", 19, 48, false, false, -2, 1, 1, 1, -84, [8], 9]], [0, "896YA1jAhGfqaNQVcEUmC8", 1], [4, 4294704124], [5, 300, 50], [-187, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbBet", 3, [[6, "TỔNG THẮNG", 19, 48, false, false, -2, 1, 1, 1, -85, [10], 11]], [0, "b4RyM9Jg1PkZDg+OhQdciu", 1], [4, 4294704124], [5, 300, 50], [79.89999999999998, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lineNgang", 3, [[2, 0, -86, [12], 13]], [0, "cb4OTk0CdLso+jGoy3Q0/y", 1], [5, 500, 24], [-48, -240, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [7, "lineNgang", 3, [[2, 0, -87, [14], 15]], [0, "09CnZZw2ZGxbAaZYCKduOn", 1], [5, 500, 24], [-310, -240, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [5, "lbBet", 3, [[6, "PHẦN THƯỞNG", 19, 48, false, false, -2, 1, 1, 1, -88, [16], 17]], [0, "33vKrl55JBjboLn5r3KdqG", 1], [4, 4294704124], [5, 300, 50], [382.9, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lineNgang", 3, [[2, 0, -89, [18], 19]], [0, "fa+8NX0X9CXL3r8SGBlkW0", 1], [5, 500, 24], [226, -240, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [1, "New Label", 4, [[4, "CAO THỦ TÀI XỈU", 21, false, -2, 1, 1, -90, [22], 23]], [0, "e8cMjMIzlHMLtgXhrkD1xI", 1], [5, 273.22, 26.25], [0, 292.852, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "temp", 1, [2], [0, "ba0lKMgeFCmalStzPvOTzQ", 1]], [10, "lbRank", 2, [-91], [0, "604uLuuUBP9aMhe6PNbpJy", 1], [5, 240, 30], [-434, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "1", 26, 48, false, false, 1, 1, 1, 31, [26]], [1, "Rank1_Mini", 17, [[13, "Rank1", 0, false, "Rank1", -92, [27], 28]], [0, "345+m0+eZM3rCEW2IXu1vy", 1], [5, 122.39, 130.51], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [12, 0, 17, [29]], [1, "Rank1_Mini", 18, [[13, "Rank2", 0, false, "Rank2", -93, [30], 31]], [0, "e5S52lfUxMcb2u1EXcaI9j", 1], [5, 122.39, 130.51], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [12, 0, 18, [32]], [10, "rankSprite3", 2, [-94], [0, "47sKa9fOhCdbomYg2nt+4E", 1], [5, 36, 40], [-431.8, -0.3999999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [26, 37, [33]], [20, "lbSID", false, 7, [[31, "[TQ]", 24, 48, false, false, 1, 1, -95, 34]], [0, "05IcMsiERAwYJqXW8oxoh/", 1], [4, 4278255615], [5, 44.4, 28.8], [-59.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbNickName", 7, [-96], [0, "72eIaB3kBFS7AvMvLpnSgG", 1], [4, 4294956544], [5, 110.45, 60.48], [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "<PERSON><PERSON><PERSON>", 22, 48, false, false, 1, 1, 40, [35]], [11, "lbTotalWin", 2, [-97], [0, "861IwfPDtKV6x7wMhVeLf8", 1], [4, 4292072403], [5, 240, 30], [80, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "0", 24, 33, false, false, -2, 1, 1, 1, 42, [36]], [1, "lineNgang", 2, [[2, 0, -98, [37], 38]], [0, "7a52G5MR9BG7jrrgGdUwUx", 1], [5, 1000, 24], [0, -39, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 8, [[4, "200,000,000", 26, false, -5, 1, 1, -99, [39], 40]], [0, "09wCq1u2BGUrwtsKIgeW9b", 1], [5, 144.19, 32.5], [109, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 9, [[4, "120,000,000", 26, false, -5, 1, 1, -100, [43], 44]], [0, "8cuTACdVVJoKqz3tiMaaTL", 1], [5, 138.5, 32.5], [109, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 10, [[4, "80,000,000", 26, false, -5, 1, 1, -101, [47], 48]], [0, "a3UVTAiyZHEqV71Dxykxsp", 1], [5, 129.69, 32.5], [109, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 11, [[4, "5,000,000", 26, false, -5, 1, 1, -102, [51], 52]], [0, "2awtJrTR5KdIq6QP9mz6Nr", 1], [5, 114.38, 32.5], [109, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 12, [[4, "50,000,000", 26, false, -5, 1, 1, -103, [55], 56]], [0, "c7gb+Ky6lKdaQWS9SFVpY/", 1], [5, 128.88, 32.5], [109, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 13, [[4, "30,000,000", 26, false, -5, 1, 1, -104, [59], 60]], [0, "adF6gJMt1LLbISUDZBMueE", 1], [5, 128.88, 32.5], [109, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 14, [[4, "20,000,000", 26, false, -5, 1, 1, -105, [63], 64]], [0, "85vz9XZANOjLxzuDVI/m0D", 1], [5, 129.69, 32.5], [109, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [38, false, 0.75, 0.23, null, null, 15, 20], [39, 20, 10, 400, 15, 2, 52]], 0, [0, 8, 1, 0, 0, 1, 0, 9, 53, 0, 0, 1, 0, -1, 16, 0, -2, 4, 0, -3, 30, 0, -4, 15, 0, 10, 11, 0, 11, 14, 0, 12, 13, 0, 13, 12, 0, 14, 10, 0, 15, 9, 0, 16, 8, 0, 17, 38, 0, 18, 36, 0, 19, 34, 0, 20, 43, 0, 21, 41, 0, 22, 32, 0, 0, 2, 0, -1, 31, 0, -2, 17, 0, -3, 18, 0, -4, 37, 0, -5, 7, 0, -6, 42, 0, -7, 44, 0, -8, 8, 0, -9, 9, 0, -10, 10, 0, -11, 11, 0, -12, 12, 0, -13, 13, 0, -14, 14, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, -4, 25, 0, -5, 26, 0, -6, 27, 0, -7, 28, 0, 0, 4, 0, -1, 5, 0, -2, 6, 0, -3, 29, 0, 0, 5, 0, 23, 5, 0, 0, 5, 0, 0, 6, 0, -1, 21, 0, 0, 7, 0, -1, 39, 0, -2, 40, 0, 0, 8, 0, -1, 45, 0, 0, 9, 0, -1, 46, 0, 0, 10, 0, -1, 47, 0, 0, 11, 0, -1, 48, 0, 0, 12, 0, -1, 49, 0, 0, 13, 0, -1, 50, 0, 0, 14, 0, -1, 51, 0, -1, 52, 0, -2, 53, 0, -1, 19, 0, 0, 16, 0, 0, 16, 0, -1, 34, 0, -1, 33, 0, -1, 36, 0, -1, 35, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, -1, 32, 0, 0, 33, 0, 0, 35, 0, -1, 38, 0, 0, 39, 0, -1, 41, 0, -1, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 24, 1, 2, 4, 30, 3, 4, 6, 105], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 38, 41, 43], [-1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, -1, 3, -1, -1, 3, -1, -1, 2, -1, -1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 5, 6, -1, 7, -1, -2, 2, 1, 2, 2], [0, 8, 0, 9, 0, 10, 0, 1, 0, 1, 0, 1, 0, 2, 0, 2, 0, 1, 0, 2, 0, 11, 0, 12, 0, 13, 0, 3, 4, 0, 3, 4, 0, 0, 5, 0, 0, 0, 2, 0, 1, 0, 14, 0, 1, 0, 15, 0, 1, 0, 16, 0, 1, 0, 17, 0, 1, 0, 18, 0, 1, 0, 19, 0, 1, 0, 20, 5, 21, 0, 6, 6, 22, 7, 23, 7, 1]]