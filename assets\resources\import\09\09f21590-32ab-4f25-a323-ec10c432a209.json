[1, ["b0YUK1DjlEVKEbEnVYdCHK"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "item_jackpot_ani", "\nitem_jackpot_ani.png\nsize: 498,178\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\njp_7n\n  rotate: true\n  xy: 402, 72\n  size: 104, 94\n  orig: 104, 94\n  offset: 0, 0\n  index: -1\njp_fx\n  rotate: false\n  xy: 2, 2\n  size: 257, 174\n  orig: 257, 174\n  offset: 0, 0\n  index: -1\njp_n\n  rotate: false\n  xy: 261, 35\n  size: 139, 141\n  orig: 139, 141\n  offset: 0, 0\n  index: -1\njp_text\n  rotate: false\n  xy: 261, 4\n  size: 139, 29\n  orig: 139, 29\n  offset: 0, 0\n  index: -1\n", ["item_jackpot_ani.png"], {"skeleton": {"hash": "9mTFfcv7knE6GQbCwbFWmC34dsI", "spine": "3.6.53", "width": 266, "height": 174}, "bones": [{"name": "root"}, {"name": "jp_n", "parent": "root", "length": 66.43, "rotation": 89.67, "x": -1.47, "y": -24.42}, {"name": "jp_fx", "parent": "jp_n", "length": 79.78, "rotation": 1.08, "x": 26.75, "y": -1.82}, {"name": "jp_text", "parent": "jp_n", "length": 62.98, "rotation": -88.97, "x": -17.81, "y": 25.63}], "slots": [{"name": "jp_n", "bone": "jp_n", "attachment": "jp_n"}, {"name": "jp_fx", "bone": "jp_fx", "color": "ffffff32", "attachment": "jp_fx", "blend": "additive"}, {"name": "jp_7n", "bone": "jp_n", "attachment": "jp_7n"}, {"name": "jp_text", "bone": "jp_text", "attachment": "jp_text"}], "skins": {"default": {"jp_7n": {"jp_7n": {"x": 24.43, "y": -1.33, "rotation": -89.67, "width": 104, "height": 94}}, "jp_fx": {"jp_fx": {"type": "mesh", "hull": 4, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-99.67, -133.54, -96.22, 132.43, 77.77, 130.17, 74.32, -135.8]}}, "jp_n": {"jp_n": {"x": 24.43, "y": -1.33, "rotation": -89.67, "width": 139, "height": 141}}, "jp_text": {"jp_text": {"x": 22.34, "y": 0.61, "rotation": -0.7, "width": 139, "height": 29}}}}, "animations": {"animation": {"slots": {"jp_fx": {"color": [{"time": 0, "color": "ffffff32"}, {"time": 0.5, "color": "ffffff6d"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 2, "color": "ffffff6d"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 3, "color": "ffffff32"}], "attachment": [{"time": 0, "name": "jp_fx"}, {"time": 1.5, "name": "jp_fx"}, {"time": 3, "name": "jp_fx"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "jp_n": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "jp_fx": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -51.76}, {"time": 1, "angle": -90.91, "curve": "stepped"}, {"time": 1.5, "angle": -90.91}, {"time": 2, "angle": -140.63}, {"time": 2.5, "angle": 163.8}, {"time": 2.8333, "angle": 23.13}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "jp_text": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 7, "y": 0.04}, {"time": 1, "x": 0, "y": 0}, {"time": 1.3333, "x": 7.7, "y": 0.04}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2, "x": 7.7, "y": 0.04}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.6667, "x": 7.7, "y": 0.04}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.076, "y": 1.076}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}, "deform": {"default": {"jp_fx": {"jp_fx": [{"time": 0}, {"time": 0.5, "vertices": [-4.15708, 34.61003, 0, 0, 10.55418, -16.82137]}, {"time": 1, "curve": "stepped", "vertices": [10.25602, -10.42008, 12.1466, -10.6969, 8.44335, -13.45709, -2.23769, 14.4718]}, {"time": 1.5, "vertices": [10.25602, -10.42008, 12.1466, -10.6969, 8.44335, -13.45709, -2.23769, 14.4718]}, {"time": 2, "curve": "stepped", "vertices": [-4.15708, 34.61003, 0, 0, 10.55418, -16.82137]}, {"time": 2.5, "vertices": [-4.15708, 34.61003, 0, 0, 10.55418, -16.82137]}, {"time": 3}]}}}}, "lose": {"slots": {"jp_7n": {"color": [{"time": 0, "color": "3c3c3cff"}]}, "jp_n": {"color": [{"time": 0, "color": "3c3c3cff"}]}, "jp_text": {"color": [{"time": 0, "color": "3c3c3cff"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "jp_n": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "jp_text": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "jp_fx": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}, "win": {"slots": {"jp_fx": {"color": [{"time": 0, "color": "ffffff32"}, {"time": 1.4667, "color": "ffffff6f"}, {"time": 3, "color": "ffffff32"}], "attachment": [{"time": 0, "name": "jp_fx"}, {"time": 3, "name": "jp_fx"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "jp_n": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7333, "x": 0, "y": 5.15}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2.2667, "x": 0, "y": 5.15}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "jp_fx": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -90.65}, {"time": 1.4667, "angle": 175.66}, {"time": 2.2333, "angle": 91.17}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "jp_text": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 7, "y": 0.04}, {"time": 1, "x": 0, "y": 0}, {"time": 1.3333, "x": 7.7, "y": 0.04}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2, "x": 7.7, "y": 0.04}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.6667, "x": 7.7, "y": 0.04}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.076, "y": 1.076}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}, "deform": {"default": {"jp_fx": {"jp_fx": [{"time": 0}, {"time": 1.4667, "vertices": [9.47302, -25.92359, -10.14878, -10.24704, 0, 0, 17.16991, -5.12612]}, {"time": 3}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]