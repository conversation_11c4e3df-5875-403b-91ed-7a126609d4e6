[1, 0, 0, [["cc.AnimationClip", ["_name", "_duration", "events", "curveData"], 0, 11], ["cc.AnimationClip", ["_name", "_duration", "events", "curveData"], -1]], [[1, 0, 1, 2, 3, 5], [0, 0, 1, 2, 3, 4]], [[[[1, "close-card", 0.5, [{"frame": 0.25, "func": "onCloseCard", "params": []}], [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0.5, 0.5]], [{"frame": 0.25}, "value", 8, [0, 0, 0.5]], [{"frame": 0.5}, "value", 8, [0, 0.5, 0.5]]], 11, 11, 11]]]]], 0, 0, [], [], []], [[[0, "show-card", 0.6666666666666666, [{"frame": 0.3333333333333333, "func": "onShowCard", "params": []}], {"props": {"scaleX": [{"frame": 0, "value": 0.85}, {"frame": 0.3333333333333333, "value": 0}, {"frame": 0.6666666666666666, "value": 0.85}]}}]], 0, 0, [], [], []], [[[0, "flip", 0.6666666666666666, [{"frame": 0, "func": "initCard", "params": []}, {"frame": 0.3333333333333333, "func": "onShowCard", "params": []}], {"props": {"scaleX": [{"frame": 0, "value": 0.8}, {"frame": 0.3333333333333333, "value": 0}, {"frame": 0.6666666666666666, "value": 0.8}]}}]], 0, 0, [], [], []]]]