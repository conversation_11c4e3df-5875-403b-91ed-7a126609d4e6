[1, ["ecpdLyjvZBwrvm+cedCcQy", "d82n49/IVAvIEqsa0xvvk0", "24xd2Xl+xHVZeWwPN10Wzf", "90Ss2Yf1lLHYPvA9KDgBaE", "7a/QZLET9IDreTiBfRn2PD", "21tx2pDSpElYCwzUj6L+8q", "9bYDnu5NdJH7u79ZpCFpEO", "97be04EipF04Aia2h48z4C", "41obftpCRMW6380/g5lNfM", "ffn1eiLvZFdK9Ped0tFFLf", "9116UMnDZOzp1+wrqjuJly", "c4E6vT74FAMY2sFh5Bh9cH", "b5qw/Tc11MyrVtdNkz9Fim", "15TqJIGghPRbUeUWSV2ffV", "fdNoodJKVLj4dF1TLppv2g", "d0eRki7tdFCa9wS+t6obx8", "f0puneJcNGfY+3VzwuGjGx", "4d4OkqjRJEeZGzHA0GW7zL", "06RCegptdIBYzb8YE5jwlN", "73dYUDQRFCEJymCU8UE4Bv", "45wrrBM+VO/IPs52mxUvQ6", "a9VpD0DP5LJYQPXITZq+uj", "c1jCp4q6JJXpaQlGU2YUGG", "26XBIiR41EpJSfFK/OQnr/", "83G0v9bH5EBJH+BvUuy/Le", "c1y3UL3AVHoqWPxPdQzt/K", "c25Leu0BdNDphgb/Hp9jw/", "42R3O6ZfNA87w5le2Iepqg", "eaqnIplqNErbdgYnwQx8GG", "2cWB/vWPRHja3uQTinHH30", "1efjUgjgRNroUUKxfQSfVE", "24Jdc6Aj5CiJf1WtGQrr1q", "bfBjN8MAhH37ZondOTDPHj", "90Cjkb9UZIqK6RLVwQiacN"], ["node", "_N$file", "_spriteFrame", "_N$skeletonData", "_normalMaterial", "_defaultClip", "_N$disabledSprite", "root", "btnBack", "btnNext", "lbTotalRefundXiu", "lbTotalRefundTai", "lbTotalBetXiu", "lbTotalBetTai", "spriteDice3", "spriteDice2", "spriteDice1", "lbXiu", "lbTai", "lblTotalUserBetXiu", "lblTotalUserBetTai", "lblTextNotiNewGame", "lblResult", "lblMd5Hash", "lblTotalDice", "nodeXiu", "nodeTai", "lbSessionID", "xiuSessionDetailListView", "taiSessionDetailListView", "lbRefund", "lbBet", "lbNickName", "lbTime", "_N$target", "data", "_parent", "_N$pressedSprite", "_N$hoverSprite", "_textureSetter"], [["cc.Label", ["_isSystemFontUsed", "_N$verticalAlign", "_string", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_normalMaterial"], 1, 1, 9, 5, 5, 1, 5, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_eulerAngles"], 2, 1, 12, 4, 5, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["be00fiONJlIcKw5Eti8jYLm", ["node", "taiSessionDetailListView", "xiuSessionDetailListView", "lbSessionID", "nodeTai", "nodeXiu", "lblTotalDice", "lblMd5Hash", "lblResult", "lblTextNotiNewGame", "lblTotalUserBetTai", "lblTotalUserBetXiu", "nodeEffectTais", "nodeEffectXius", "lbTai", "lbXiu", "spriteDice1", "spriteDice2", "spriteDice3", "lbTotalBetTai", "lbTotalBetXiu", "lbTotalRefundTai", "lbTotalRefundXiu", "btnNext", "btnBack", "sfDices"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], ["e856aq/zDBOQ55MIkBS60uC", ["node", "lbTime", "lbNickName", "lbBet", "lbRefund"], 3, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "_animationName", "node", "_materials", "_N$skeletonData"], 0, 1, 3, 6], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["dc779bW36lOwaMserMOmV0G", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[6, 0, 1, 2], [1, 0, 6, 4, 3, 5, 7, 2], [1, 0, 2, 6, 4, 3, 5, 7, 3], [2, 0, 4, 5, 6, 2], [3, 0, 2, 3, 4, 5, 6, 2], [1, 0, 6, 8, 4, 3, 5, 7, 2], [2, 4, 5, 6, 1], [0, 2, 3, 5, 6, 0, 4, 1, 8, 9, 10, 8], [0, 2, 3, 5, 6, 0, 4, 1, 8, 9, 8], [2, 0, 4, 5, 2], [14, 0, 1, 2, 3], [1, 0, 6, 8, 3, 5, 7, 2], [3, 0, 2, 3, 4, 5, 7, 6, 2], [0, 2, 3, 5, 0, 1, 8, 9, 6], [0, 2, 3, 5, 6, 0, 4, 1, 7, 8, 9, 10, 9], [15, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 4, 5, 6, 3], [2, 1, 4, 5, 6, 2], [13, 0, 1], [1, 0, 6, 8, 4, 3, 5, 2], [1, 0, 6, 4, 3, 5, 9, 7, 2], [5, 0, 1, 6, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 7, 6, 3], [10, 0, 1, 2, 1], [2, 3, 2, 0, 1, 4, 5, 6, 5], [4, 0, 1, 2, 3, 8, 3], [4, 0, 1, 2, 3, 4, 5, 6, 3], [0, 2, 3, 0, 4, 1, 8, 9, 10, 6], [0, 3, 5, 0, 4, 1, 8, 9, 6], [0, 2, 0, 4, 1, 8, 9, 10, 5], [0, 2, 3, 5, 6, 0, 4, 1, 7, 8, 9, 9], [7, 3, 0, 1, 4, 5, 6, 5], [16, 0, 1, 1], [17, 0, 1, 2, 3, 4, 5, 6, 6], [18, 0, 1, 2, 3, 4, 5, 4], [9, 0, 2], [1, 0, 8, 4, 3, 2], [1, 0, 8, 4, 3, 5, 2], [1, 0, 1, 6, 4, 3, 5, 7, 3], [1, 0, 1, 6, 8, 4, 3, 5, 7, 3], [1, 0, 6, 8, 3, 7, 2], [5, 0, 1, 2, 3, 4, 5, 7, 2], [5, 0, 1, 2, 3, 4, 5, 2], [3, 0, 2, 3, 4, 5, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 1], [6, 1, 1], [12, 0, 1, 2, 3, 4, 1], [2, 2, 0, 1, 4, 5, 6, 4], [4, 2, 7, 1], [4, 2, 3, 4, 5, 6, 1], [0, 2, 3, 5, 0, 4, 1, 8, 9, 10, 7], [0, 2, 0, 4, 1, 8, 9, 5], [0, 2, 3, 5, 6, 0, 1, 8, 9, 7], [0, 2, 3, 5, 0, 4, 1, 8, 9, 7], [7, 0, 1, 2, 5, 6, 4]], [[[[35, "taiXiuMd5SessionDetailView"], [36, "taiXiuSessionDetailView", [-28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61, -62], [[23, -2, [120, 121], 119], [44, -27, -26, -25, -24, -23, -22, -21, -20, -19, -18, -17, -16, [-15], [-14], -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, [122, 123, 124, 125, 126, 127]]], [45, -1]], [37, "<PERSON><PERSON>", [-68, -69, -70, -71, -72], [[46, -67, -66, -65, -64, -63]], [0, "71ryvLiWRHNZ72pUdSb1nZ", 1], [5, 570, 50]], [11, "title-left", 1, [-73, -74, -75, -76, -77, -78, -79], [0, "b6JEenaVFBS7z+g+IyhepT", 1], [5, 500, 30], [-270, 59, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "title-left", 1, [-80, -81, -82, -83, -84, -85, -86], [0, "3aLF/q+1pNfoi55dOVCKpR", 1], [5, 500, 30], [317.124, 59, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "total-tai", 1, [-88, -89, -90, -91], [[24, false, 1, 0, false, -87, [93], 94]], [0, "68AEbHKg9Nkrqq9xnVN5pA", 1], [5, 487, 50], [-263, -251, 0, 0, 0, 0, 1, 1, 1.1, 1]], [5, "total-xiu", 1, [-93, -94, -95, -96], [[24, false, 1, 0, false, -92, [101], 102]], [0, "edcF8gL3xC7LxuSKn58Byp", 1], [5, 487, 50], [255, -251, 0, 0, 0, 0, 1, 1, 1.1, 1]], [21, "scrollview-tai", 1, [-100], [[-97, -98, [18, -99]], 1, 1, 4], [0, "88JCrALKFAUpWp5WoQimue", 1], [5, 570, 290], [-300, -107, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "scrollview-xiu", 1, [-104], [[-101, -102, [18, -103]], 1, 1, 4], [0, "2ddmR339lKipLmv+IS5GO8", 1], [5, 570, 290], [300, -107, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "black", 100, 1, [[3, 0, -105, [0], 1], [48, -106, [4, 4292269782]], [18, -107]], [0, "73lxVvwE9MRa8dsxQFhD7s", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nen popup", 1, [-109, -110], [[47, 1, 0, false, -108, [8], 9]], [0, "2fr4Dq6y5KzZ+Yak0PO0Lv", 1], [5, 1200, 650], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "btnBackSession", 1, [[[17, false, -111, [18], 19], -112], 4, 1], [0, "a1/Xw4XI9Efqch1Y2QPfXA", 1], [5, 23, 35], [-264, 175, 0, 0, 0, 1, 6.123233995736766e-17, 1.5, 1.5, 1], [1, 0, 0, 180]], [42, "btnNextSession", 1, [[[17, false, -113, [20], 21], -114], 4, 1], [0, "7aGsIOsXtFmLjSOqwKajgi", 1], [5, 23, 35], [285, 175, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [11, "md5Hash", 1, [-115, -116, -117], [0, "eceP3RjFBKk4bE0dveZcZE", 1], [5, 500, 50], [-272, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "nodeResult", 1, [-118, -119, -120], [0, "dbvrYbKcVKx49jnHshmFOB", 1], [5, 500, 50], [272, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnClose", 1, [-123], [[49, -122, [[10, "be00fiONJlIcKw5Eti8jYLm", "closeClicked", 1]], [4, **********], [4, **********], -121]], [0, "3fm/KsSmRIhZb4QfbTz1Z7", 1], [5, 80, 80], [563.939, 341.099, 0, 0, 0, 0, 1, 1, 1, 0]], [39, "bgNotify", 0, 1, [-126], [[3, 0, -124, [114], 115], [23, -125, [117, 118], 116]], [0, "eeCoosAF5P+qsav6+59i+P", 1], [5, 686, 50], [0, 263, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSession", 10, [-128], [[7, "Hướng Dẫn <PERSON><PERSON><PERSON> Quả MD5", 20, 50, false, false, 1, 1, -127, [6], 7]], [0, "80pohWlpNHR689NBatFOc8", 1], [5, 339.97, 63], [-12, -284.796, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tai", 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -129, [10], 11]], [0, "6e8L4KFxhDLqvKZGBZ5rBz", 1], [5, 311, 301], [-425, 250, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "xiu", 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -130, [12], 13]], [0, "3bFmADV8xAOIuKUBJLqn2y", 1], [5, 311, 301], [425, 250, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "anim<PERSON>iu", false, 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -131, [14], 15]], [0, "0daFa7RzhH2KpgtmIQuzho", 1], [5, 311, 301], [426, 250.9, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [2, "<PERSON>im<PERSON><PERSON>", false, 1, [[15, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -132, [16], 17]], [0, "720d4EvSVB44o04gMRq1Da", 1], [5, 311, 301], [-425, 250.9, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "userTai", 1, [-134], [[6, -133, [33], 34]], [0, "9bGii+ILdD+6DxKUCVcuWA", 1], [5, 13, 22], [-309, 238, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "userXiu", 1, [-136], [[6, -135, [36], 37]], [0, "fa1PcNYehGVp/HrWaYaSfA", 1], [5, 13, 22], [272, 238, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCopy", 13, [[6, -137, [41], 42], [25, 1.1, 3, -138, [[10, "be00fiONJlIcKw5Eti8jYLm", "copyHashClicked", 1]], 43]], [0, "0cVLxCTvxGZ6DRqfXnK/5U", 1], [5, 46, 30], [237, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCopy", 14, [[6, -139, [47], 48], [25, 1.1, 3, -140, [[10, "be00fiONJlIcKw5Eti8jYLm", "copyResultClicked", 1]], 49]], [0, "aajt1nSkdAPq9OpgHNAGdz", 1], [5, 46, 30], [288, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 2, [-142], [[54, 1, 1, 5, -141, [5, 119.53, 50]]], [0, "de4BopV8pFSYo7bDIInaMf", 1], [5, 119.53, 50], [-12.200000000000003, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "view", 7, [-144], [[32, -143, [111]]], [0, "71Q05LiVxPPJIKjeAM9XAP", 1], [5, 570, 290]], [20, "content", 27, [[31, false, 1, 2, 10, -145, [5, 500, 0]]], [0, "b7dmvL2HVHX7fIuRA/Ag5M", 1], [5, 570, 0], [0, 0.5, 1], [0, 143, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "view", 8, [-147], [[32, -146, [112]]], [0, "67q2jbDQpMBIvP3fdPPK+E", 1], [5, 570, 290]], [20, "content", 29, [[31, false, 1, 2, 10, -148, [5, 500, 0]]], [0, "f5tn1TjUZLo56Ux4l6QVTZ", 1], [5, 570, 0], [0, 0.5, 1], [0, 143, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 10, [[50, "THỐNG KÊ PHIÊN", 21, 0, false, 1, 1, -149, [2], 3]], [0, "58GgUEANJHmJ0nYofrVaDR", 1], [5, 290.72, 0], [0, 317, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "txmd5_btnHelp", 17, [[3, 0, -150, [4], 5]], [0, "95Zkajj4FCyJcM3NvIuUTs", 1], [5, 29, 29], [198, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, 1.1, 3, 11, [[10, "be00fiONJlIcKw5Eti8jYLm", "backSessionClicked", 1]], [4, **********], [4, 2533359615], 11], [26, 1.1, 3, 12, [[10, "be00fiONJlIcKw5Eti8jYLm", "nextSessionClicked", 1]], [4, **********], [4, 2533359615], 12], [4, "dice_1", 1, [-151], [0, "beCIT6/4xIfaYmsw/KuqcW", 1], [5, 68, 68], [-174, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 0, 35, [22]], [1, "plus", 1, [[6, -152, [23], 24]], [0, "60OfZtwotP26HIVEEBoIsd", 1], [5, 63, 62], [10, 174, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [4, "dice_2", 1, [-153], [0, "30g9bUqh9Ps7G37Z4mtI1j", 1], [5, 68, 68], [-51, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 0, 38, [25]], [1, "plus", 1, [[6, -154, [26], 27]], [0, "8fgouryOBKmqNpDkeD/bVz", 1], [5, 63, 62], [-111, 174, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [4, "dice_3", 1, [-155], [0, "d5LCgzgMVHFYOhxizPCkt6", 1], [5, 68, 68], [69, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 0, 41, [28]], [1, "blance", 1, [[6, -156, [29], 30]], [0, "bdoz2xj4xPPp5jG9H7Xkhz", 1], [5, 26, 20], [136, 174, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbTotal", 1, [-157], [0, "25bKNfQOdOIqTV2I3/Xrkp", 1], [5, 78.75, 50], [204, 242, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "16", false, 1, 1, 44, [31]], [12, "lbTotalUserTai", 22, [-158], [0, "03p2Nl7cxA6qEOHCQ9sXi1", 1], [5, 37.88, 11.25], [0, 0, 0.5], [13, 19, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "500", 12, 30, false, 1, 46, [32]], [12, "lbTotalUserXiu", 23, [-159], [0, "0cBKlpxItBbI4F8kecPUFU", 1], [5, 37.88, 11.25], [0, 0, 0.5], [13, 19, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "500", 12, 30, false, 1, 48, [35]], [1, "title", 13, [[27, "Chuỗi MD5:", 13, false, 1, 1, -160, [38], 39]], [0, "3dn/vFcp9L0oFjiJYu1K5d", 1], [5, 119.84, 16.25], [-245, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lblMd5", 13, [-161], [0, "32T34g6J5Me6kpnaIVYk2A", 1], [5, 320.62, 37.8], [0, 0, 0.5], [-145, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "49c34db8361fb6e5a549a99d476e376c", 17.5, 30, false, 1, 51, [40]], [1, "title", 14, [[27, "Chuỗi KQ:", 13, false, 1, 1, -162, [44], 45]], [0, "28M1+gwntCKJZY4N21hV17", 1], [5, 104, 16.25], [-205, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lblResult", 14, [-163], [0, "b0t6ZVMYhPCqNqmgNHAVj2", 1], [5, 413.4, 37.8], [0, 0, 0.5], [-148, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "#650231_4s74HBSv{4-4-3}oGzkpK7zUooomkvmixk", 17, 30, false, 1, 54, [46]], [1, "sprite", 15, [[17, false, -164, [50], 51]], [0, "5etFymNvRBXa2EMi7WLiog", 1], [5, 69, 36], [2, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbSession", 1, [-165], [0, "86uT48h3lJGI0sxEL84nAJ", 1], [5, 319.94, 63], [0, 241, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "PHIEN xxxxxx - Ngay xx-xx-xxxx", 20, 50, false, false, 1, 1, 57, [52]], [16, "lbTai", false, 1, [-166], [0, "0ej0TJSFNByoIi5It8ttNw", 1], [5, 0, 62.5], [-183, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [28, 50, 50, false, 1, 1, 59, [53]], [16, "lbXiu", false, 1, [-167], [0, "45tCrKGHhCs7WcYPrFcX02", 1], [5, 0, 62.5], [161, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [28, 50, 50, false, 1, 1, 61, [54]], [1, "bgContentLeft", 1, [[3, 0, -168, [55], 56]], [0, "84f4y50/dHer6EMc/sMAGX", 1], [5, 730, 375], [-298, -92, 0, 0, 0, 0, 1, 0.79, 0.9, 1]], [1, "bgContentRight", 1, [[3, 0, -169, [57], 58]], [0, "f7N4ItcUhKWYDYLKExNlzc", 1], [5, 730, 375], [298, -92, 0, 0, 0, 0, 1, 0.79, 0.9, 1]], [1, "lbTime", 3, [[14, "G<PERSON>ờ <PERSON>", 14, 48, false, false, 1, 1, 1, -170, [59], 60]], [0, "a7P3r6o6tJd5nqybfqadK1", 1], [5, 91, 30], [-248, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1", 3, [[3, 0, -171, [61], 62]], [0, "e43MAY8+tJLIiEGR8X2azY", 1], [5, 25, 379], [-174, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 3, [[7, "<PERSON><PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -172, [63], 64]], [0, "72foDeQ7FHqInZnuc7gvGQ", 1], [5, 95.38, 21], [-43.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 3, [[3, 0, -173, [65], 66]], [0, "1dh0g9XFxH6apNQMeogUPK", 1], [5, 25, 379], [96, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 3, [[7, "<PERSON><PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -174, [67], 68]], [0, "2eCQE+JktBoLKRaHUSqhCX", 1], [5, 88.38, 21], [170.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "3", false, 3, [[3, 0, -175, [69], 70]], [0, "f1JqTZE9ZHFqg7jg3gINs2", 1], [5, 25, 379], [166, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbRefund", false, 3, [[14, "TRẢ LẠI", 14, 48, false, false, 1, 1, 1, -176, [71], 72]], [0, "21XikddZxMV5l22B+I/YpR", 1], [5, 69.13, 30], [205, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 4, [[14, "G<PERSON>ờ <PERSON>", 14, 48, false, false, 1, 1, 1, -177, [73], 74]], [0, "682zY7rDpJJ41bN8YRCnpc", 1], [5, 91, 30], [-243, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1", 4, [[3, 0, -178, [75], 76]], [0, "443yoxhQVJjo4gWmFDt8QE", 1], [5, 25, 379], [-174, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 4, [[7, "<PERSON><PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -179, [77], 78]], [0, "43T/9v8dVIIIcs6Dj+9xxa", 1], [5, 95.38, 21], [-43.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 4, [[3, 0, -180, [79], 80]], [0, "a9efuvLUpBgK5Y9fRxU9m0", 1], [5, 25, 379], [96, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 4, [[7, "<PERSON><PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -181, [81], 82]], [0, "07yT/gcTpGVK3uJRjdn8y6", 1], [5, 88.38, 21], [173.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "3", false, 4, [[3, 0, -182, [83], 84]], [0, "39gqIKDK5E6pLBoNQUcqU1", 1], [5, 25, 379], [166, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbRefund", false, 4, [[14, "TRẢ LẠI", 14, 48, false, false, 1, 1, 1, -183, [85], 86]], [0, "eaHFArXoZKd5z0XOAra4ux", 1], [5, 69.13, 30], [205, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTotal", false, 5, [[7, "Tổng:", 20, 48, false, false, 1, 1, -184, [87], 88]], [0, "7daJgWOppHAJsNUklY9moP", 1], [5, 66.25, 30], [-57, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbTotalBetTai", 5, [-185], [0, "e3Zbcr/sNDG7jpAHFYDabX", 1], [5, 157.5, 30], [-155, 408, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "200.000k", 20, 48, false, false, 1, 1, 80, [89]], [22, "lbTotalRefundTai", false, 5, [-186], [0, "cdLSka7hxNuLhsAdpSlEe1", 1], [5, 111.25, 30], [0, 0, 0.5], [134, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "200.000k", 20, 48, false, false, 1, 82, [90]], [2, "line", false, 5, [[29, "/", false, 1, 1, -187, [91], 92]], [0, "214LeS3aJG6KOxZf8yuEfN", 1], [5, 23.75, 50], [115, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTotal", false, 6, [[7, "Tổng:", 20, 48, false, false, 1, 1, -188, [95], 96]], [0, "0aOfqXMMFGeo1YQKOuXk/X", 1], [5, 66.25, 30], [-60, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbTotalBetXiu", 6, [-189], [0, "84o9B2+IdK7LOnQhRP7NJX", 1], [5, 157.5, 30], [179, 408, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "200.000k", 20, 48, false, false, 1, 1, 86, [97]], [2, "line", false, 6, [[29, "/", false, 1, 1, -190, [98], 99]], [0, "bcrDJH40ZJoprL3WlY2YO+", 1], [5, 23.75, 50], [117, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbTotalRefundXiu", false, 6, [-191], [0, "a7vc/UGLRMhI5I/zJxsuC8", 1], [5, 111.25, 30], [0, 0, 0.5], [135, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "200.000k", 20, 48, false, false, 1, 1, 89, [100]], [2, "duoi", false, 1, [[9, 0, -192, [103]]], [0, "91JO6t0rNBKaSwkY5ByBCV", 1], [5, 1000, 7], [-3, -216, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "tren", false, 1, [[9, 0, -193, [104]]], [0, "01picr/hhJZI+lhZJ/pVHi", 1], [5, 1000, 7], [-3, 80, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "temp", 1, [2], [0, "62A/+E4ExPdaFKSkp9wUQi", 1], [-298, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbTime", 2, [-194], [0, "b7qcJGKF1P14DeeP4yOyS8", 1], [5, 105.69, 28.5], [-220.7, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "<PERSON><PERSON><PERSON><PERSON> gian", 19, 48, false, false, 1, 1, 94, [105]], [4, "lbBet", 2, [-195], [0, "3dX1ux7n9HPabr89PWqxvw", 1], [5, 52.85, 30], [205.9, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "100.000", 19, 48, false, false, 1, 1, 1, 96, [106]], [16, "lbRefund", false, 2, [-196], [0, "e2lPHzPuVFQ6sm3W7oPgeH", 1], [5, 60.4, 30], [199.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "100.000", 16, 48, false, false, 1, 1, 1, 98, [107]], [4, "lbNickName", 26, [-197], [0, "ebJ1H3uwpH7q3xmRYROblX", 1], [5, 119.53, 22.5], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "<PERSON><PERSON><PERSON>", 15, 48, false, false, 1, 1, 100, [108]], [1, "line", 2, [[3, 0, -198, [109], 110]], [0, "8ckGpUPIRAT7COPlYCzNW3", 1], [5, 486, 16], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [33, false, 0.75, 0.23, null, null, 7, 28], [34, 20, 10, 400, 7, 2, 103], [33, false, 0.75, 0.23, null, null, 8, 30], [34, 20, 10, 400, 8, 2, 105], [43, "lblNotify", 16, [-199], [0, "baNdKwGZhHzbEn7im8FSiu", 1], [5, 59.94, 37.8]], [53, "Label", 22, 30, false, 1, 1, 107, [113]]], 0, [0, 7, 1, 0, 0, 1, 0, 8, 33, 0, 9, 34, 0, 10, 90, 0, 11, 83, 0, 12, 87, 0, 13, 81, 0, 14, 42, 0, 15, 39, 0, 16, 36, 0, 17, 62, 0, 18, 60, 0, -1, 20, 0, -1, 21, 0, 19, 49, 0, 20, 47, 0, 21, 108, 0, 22, 55, 0, 23, 52, 0, 24, 45, 0, 25, 19, 0, 26, 18, 0, 27, 58, 0, 28, 106, 0, 29, 104, 0, 0, 1, 0, -1, 9, 0, -2, 10, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, -6, 21, 0, -7, 11, 0, -8, 12, 0, -9, 35, 0, -10, 37, 0, -11, 38, 0, -12, 40, 0, -13, 41, 0, -14, 43, 0, -15, 44, 0, -16, 22, 0, -17, 23, 0, -18, 13, 0, -19, 14, 0, -20, 15, 0, -21, 57, 0, -22, 59, 0, -23, 61, 0, -24, 63, 0, -25, 64, 0, -26, 3, 0, -27, 4, 0, -28, 5, 0, -29, 6, 0, -30, 91, 0, -31, 92, 0, -32, 93, 0, -33, 7, 0, -34, 8, 0, -35, 16, 0, 30, 99, 0, 31, 97, 0, 32, 101, 0, 33, 95, 0, 0, 2, 0, -1, 94, 0, -2, 96, 0, -3, 98, 0, -4, 26, 0, -5, 102, 0, -1, 65, 0, -2, 66, 0, -3, 67, 0, -4, 68, 0, -5, 69, 0, -6, 70, 0, -7, 71, 0, -1, 72, 0, -2, 73, 0, -3, 74, 0, -4, 75, 0, -5, 76, 0, -6, 77, 0, -7, 78, 0, 0, 5, 0, -1, 79, 0, -2, 80, 0, -3, 82, 0, -4, 84, 0, 0, 6, 0, -1, 85, 0, -2, 86, 0, -3, 88, 0, -4, 89, 0, -1, 103, 0, -2, 104, 0, 0, 7, 0, -1, 27, 0, -1, 105, 0, -2, 106, 0, 0, 8, 0, -1, 29, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, -1, 31, 0, -2, 17, 0, 0, 11, 0, -2, 33, 0, 0, 12, 0, -2, 34, 0, -1, 50, 0, -2, 51, 0, -3, 24, 0, -1, 53, 0, -2, 54, 0, -3, 25, 0, 34, 15, 0, 0, 15, 0, -1, 56, 0, 0, 16, 0, 0, 16, 0, -1, 107, 0, 0, 17, 0, -1, 32, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, -1, 46, 0, 0, 23, 0, -1, 48, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, -1, 100, 0, 0, 27, 0, -1, 28, 0, 0, 28, 0, 0, 29, 0, -1, 30, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, -1, 36, 0, 0, 37, 0, -1, 39, 0, 0, 40, 0, -1, 42, 0, 0, 43, 0, -1, 45, 0, -1, 47, 0, -1, 49, 0, 0, 50, 0, -1, 52, 0, 0, 53, 0, -1, 55, 0, 0, 56, 0, -1, 58, 0, -1, 60, 0, -1, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 79, 0, -1, 81, 0, -1, 83, 0, 0, 84, 0, 0, 85, 0, -1, 87, 0, 0, 88, 0, -1, 90, 0, 0, 91, 0, 0, 92, 0, -1, 95, 0, -1, 97, 0, -1, 99, 0, -1, 101, 0, 0, 102, 0, -1, 108, 0, 35, 1, 2, 36, 93, 199], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 33, 33, 34, 36, 39, 42, 45, 47, 49, 52, 55, 58, 81, 83, 87, 90, 95, 97, 99, 101, 108], [-1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 3, -1, 3, -1, 3, -1, 3, -1, 2, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, -1, 2, -1, -1, 2, -1, 1, -1, -1, 2, 4, -1, 1, -1, -1, 2, 4, -1, 2, -1, -1, -1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 1, -1, 2, -1, 1, -1, -1, 1, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, -1, -1, 2, 5, -1, -2, 5, -1, -2, -1, -2, -3, -4, -5, -6, 37, 38, 6, 6, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 21, 0, 2, 0, 22, 0, 6, 0, 23, 4, 5, 4, 5, 4, 5, 4, 5, 0, 7, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 24, 0, 0, 0, 9, 0, 0, 9, 0, 2, 0, 0, 10, 0, 0, 2, 0, 0, 10, 0, 0, 25, 0, 0, 0, 0, 11, 0, 11, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 1, 0, 0, 0, 1, 0, 12, 0, 1, 0, 0, 1, 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 26, 0, 0, 0, 0, 27, 13, 13, 28, 14, 14, 29, 15, 16, 17, 30, 31, 32, 18, 18, 19, 19, 15, 16, 17, 2, 2, 2, 20, 20, 6, 2, 1, 2, 1, 1, 1, 1, 2, 6]], [[{"name": "txmd5_btnHelp", "rect": [0, 0, 34, 34], "offset": [0, 0], "originalSize": [34, 34], "capInsets": [0, 0, 0, 0]}], [8], 0, [0], [39], [33]]]]