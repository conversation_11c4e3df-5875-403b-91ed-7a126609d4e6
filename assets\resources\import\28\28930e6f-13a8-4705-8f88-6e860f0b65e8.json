[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "32QZqIYVxO64WRfop4Olmy", "7fUwLV8R9CO48KGYZCXaAQ", "18s14wsJ1Nh7EeEYJxncZC", "83esreDA5PPoUmAk6McrcF", "c1iNYlndJPkbHWUAVaO4IP", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_N$target", "_defaultClip", "root", "slotsHistoryListView", "lbWin", "lbBet", "lbTime", "lbSessionID", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_skewX", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 9, 1, 5, 7, 2, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_color"], 2, 1, 2, 4, 5, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "_N$normalColor", "clickEvents", "_N$target", "_N$pressedColor", "_N$disabledColor"], 1, 1, 5, 9, 1, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "lbWin", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 1, 5, 5], ["cc.Layout", ["_enabled", "_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["94f0dcGWSRC9ovLsmjATU3F", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["c890fFIrOZAObefUnoFLaiq", ["node", "slotsHistoryListView"], 3, 1, 1]], [[6, 0, 1, 2], [0, 0, 5, 4, 3, 6, 7, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [2, 2, 3, 4, 1], [0, 0, 5, 4, 3, 6, 2], [0, 0, 5, 8, 4, 3, 6, 7, 2], [1, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 7, 4, 5, 2], [2, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3], [5, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 8, 3, 7, 2], [0, 0, 8, 4, 3, 6, 2], [0, 0, 2, 5, 4, 3, 9, 6, 7, 3], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 10, 7, 2], [1, 0, 1, 6, 2, 3, 4, 5, 2], [2, 0, 2, 3, 4, 2], [3, 2, 3, 1], [3, 0, 2, 4, 6, 7, 5, 2], [3, 1, 0, 2, 4, 3, 5, 3], [8, 0, 1, 2, 3, 4, 5, 6, 1], [9, 0, 1, 2, 3, 4, 5, 5], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 3, 4, 5, 4], [13, 0, 1, 2, 1], [14, 0, 1, 1]], [[11, "tkHistoryView"], [12, "tkHistoryView", [-5, -6, -7, -8, -9, -10, -11], [[29, -2, [30, 31], 29], [30, -4, -3]], [0, "bfdrSda91GO5HWgz9Fl5o5", -1]], [15, "<PERSON><PERSON>", [-17, -18, -19, -20, -21], [[24, -16, -15, -14, -13, -12, [4, 4278246399], [4, 4294829568]]], [0, "76pD+3o71FNofZmLKSQyar", 1], [5, 799, 37]], [6, "spriteBGTitle", 1, [-23, -24, -25, -26, -27], [[4, -22, [20], 21]], [0, "3dtbCkmoNEU7VpaZmhnvtl", 1], [5, 803, 38], [0, 195, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "scrollview", 1, [-30, -31], [-28, -29], [0, "aeOWD42KJGY4aNcwWe1Oyf", 1], [5, 803, 410], [0, -41, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-34], [[22, 3, -33, [[10, "c890fFIrOZAObefUnoFLaiq", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -32]], [0, "1b2c+tZnxB35ChhYYlt+2/", 1], [5, 80, 80], [468, 266.52, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbDesc", 10, 2, [[2, "<PERSON>em chi tiết", 20, 50, false, false, 1, 1, 1, -35, [26], 27], [23, 1.1, 3, -37, [[10, "98b467lg1tGSIOSS7iaiI4s", "openDetailClicked", 2]], [4, 4292269782], -36]], [0, "6a88x5yoJM2oOyO1jCS2Vs", 1], [4, 4278246399], [5, 150, 30], [319, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[20, 0, -38, [0], 1], [21, -39, [4, 4292269782]]], [0, "58KkRgpthJ86PVkaCTRqXl", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "view", 4, [-41], [[26, 0, -40, [28]]], [0, "9bg1Ovux5K6ojFerPzKJPt", 1], [5, 803, 396]], [18, "content", 8, [[25, false, 1, 2, 10, -42, [5, 1190, 75]]], [0, "13WrlL8s9At4ukYwd/yqhA", 1], [5, 803, 75], [0, 0.5, 1], [0, 193, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nen popup", 1, [[4, -43, [2], 3]], [0, "4eNRuH5N9Pc6d9C+u5Mjz5", 1], [5, 959, 559]], [1, "title", 1, [[4, -44, [4], 5]], [0, "eaByYWRtNPuaTiNphP0VXK", 1], [5, 121, 31], [0, 252, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg<PERSON><PERSON>nt", 1, [[9, 0, false, -45, [6], 7]], [0, "4cE6y19FJNvLN9DGW3BvvQ", 1], [5, 803, 410], [0, -41, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "sprite", 5, [[9, 2, false, -46, [8], 9]], [0, "876RZX8g1Cd6RzM1xrXYOQ", 1], [5, 81, 81]], [1, "lbSessionID", 3, [[2, "PHIÊN", 20, 50, false, false, 1, 1, 1, -47, [10], 11]], [0, "57+N/VJaNIzZ+APndeQ9Tz", 1], [5, 100, 30], [-318, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 3, [[2, "THỜI GIAN", 20, 50, false, false, 1, 1, 1, -48, [12], 13]], [0, "7eDvVC4EtK1J7wAFRR28iX", 1], [5, 100, 30], [-148, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 3, [[2, "CƯỢC", 20, 50, false, false, 1, 1, 1, -49, [14], 15]], [0, "d9sx/uWOVISIpJqiJ7CUHj", 1], [5, 100, 30], [22, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 3, [[2, "THẮNG", 20, 50, false, false, 1, 1, 1, -50, [16], 17]], [0, "38UkcG51xAjrSebx6d5aW+", 1], [5, 100, 30], [165, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 3, [[2, "CHI TIẾT", 20, 50, false, false, 1, 1, 1, -51, [18], 19]], [0, "79MqO5gyFApJKsGWsTqMXB", 1], [5, 100, 30], [319, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "temp", 4, [2], [0, "871+0o3I1LbKItx+DS4DXc", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lbSessionID", 2, [-52], [0, "17ZpkWaNNO8ZbuMHy2m75t", 1], [5, 150, 30], [-318, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "#66553620", 20, 50, false, false, 1, 1, 1, 20, [22]], [7, "lbTime", 2, [-53], [0, "515mzvChFCOKAFZdP510ha", 1], [5, 200, 30], [-148, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "17:03 23-03-2019", 20, 50, false, false, 1, 1, 1, 22, [23]], [8, "lbBet", 2, [-54], [0, "1cOdkDUmlCYa7lbJHkxGOz", 1], [4, 4278255615], [5, 200, 30], [22, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "250.000", 20, 50, false, false, 1, 1, 1, 24, [24]], [8, "lbWin", 2, [-55], [0, "33YDvF+vtNupO5JXhzSCsi", 1], [4, 4278255615], [5, 200, 30], [165, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "50.000.000", 20, 50, false, false, 1, 1, 1, 26, [25]], [27, false, 0.75, 0.23, null, null, 4, 9], [28, 15, 10, 400, 4, 2, 28]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 29, 0, 0, 1, 0, -1, 7, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 5, 0, -6, 3, 0, -7, 4, 0, 7, 27, 0, 8, 25, 0, 9, 23, 0, 10, 21, 0, 0, 2, 0, -1, 20, 0, -2, 22, 0, -3, 24, 0, -4, 26, 0, -5, 6, 0, 0, 3, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, -5, 18, 0, -1, 28, 0, -2, 29, 0, -1, 19, 0, -2, 8, 0, 3, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, -1, 21, 0, -1, 23, 0, -1, 25, 0, -1, 27, 0, 11, 1, 2, 12, 19, 55], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 23, 25, 27], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, -1, -1, -1, 1, -1, 4, -1, -2, 1, 1, 1, 1], [0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 9, 0, 0, 0, 0, 0, 2, 0, 3, 3, 10, 2, 2, 2, 2]]