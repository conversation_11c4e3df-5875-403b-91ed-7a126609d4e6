[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2tBXzjmRHWIetS1zkxuiC", "7a/QZLET9IDreTiBfRn2PD", "b8HUDj6L9D2Yv7ct+XmEBs", "37NJ58soNG1o92bWSdVc8R", "44/Bx9BqhLGIm1AurAtWyF", "ee6B4NmmBFYLKRKk2CJ59y", "80bs4NHIlIgpTpybNUeCdN", "41cHi73qlJFq3Vl8NGLYlB", "39Gb8g9UxMeI7Bk9UiHqx7", "caQiZ7+X5DbJ33gYZSfopw", "96I1/hnT9G4YQrfzaqlrWx", "cdIYT0KR5LMLV69JgTUcY3", "23S18zM7tLrJCQcG3K0X6D", "beq11ThlxPXYrw1QMZ2gUQ", "9dIABYtxNGX7bIdVvi+P6f", "65Jh12aGdHIJe4IPo6fYjo", "2a3N3nlxlJ4JePv3EjBCWG", "96WjhgjUhBKKEwkJ5u+S5t", "b1KRr+snVI4ILgmkzw/fOX", "96NF2Gu/FBsYEqsfJEuyf0", "0ei89u+IhLvIN0g/FOLKqw", "daZDXxL+pKJLXY/BEY1m59", "4504KRwxVCw6eOhESO0Ora", "d3gGyDqD9KOLwHoxdM+IWa", "65zxsnJPlBRI8RL3wJhb51", "ebSqFZ/LhJ6rmFxYJA/WE5"], ["node", "_textureSetter", "_spriteFrame", "_N$file", "_N$target", "_N$skeletonData", "scene", "_parent", "loadingView", "manifestUrl"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_parent", "_components", "_contentSize", "_trs", "_color", "_children", "_anchorPoint"], 0, 1, 9, 5, 7, 5, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_trs", "_contentSize", "_anchorPoint", "_color"], 1, 1, 2, 7, 5, 5, 5], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_string", "_N$horizontalAlign", "_enableWrapText", "_lineHeight", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Node", ["_name", "_id", "_active", "_children", "_contentSize", "_components", "_trs", "_parent"], 0, 2, 5, 12, 7, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "defaultSkin", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.<PERSON>", ["_fitWidth", "node", "_designResolution"], 2, 1, 5], ["2a33dcxF+NBW6IzkrkZ/0RB", ["node"], 3, 1], ["cc.Widget", ["_alignFlags", "node"], 2, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 9, 5, 5, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["cc.ProgressBar", ["_N$mode", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["23d18uKhIlKJaRUKfhtzpsz", ["node"], 3, 1], ["ebf24dHA9NHQrvWJ9bbnRit", ["node", "ske<PERSON><PERSON>", "hotUpdate", "progressBar", "lbProgress", "lbMessage", "nodeButtonTry", "nodeButtonTryCheckVersion"], 3, 1, 1, 1, 1, 1, 1, 1, 1]], [[2, 3, 4, 5, 1], [4, 3, 0, 5, 1, 4, 2, 7, 8, 9, 7], [1, 0, 3, 4, 5, 2], [1, 0, 1, 3, 4, 5, 6, 3], [1, 0, 1, 3, 8, 4, 5, 6, 3], [1, 0, 2, 3, 4, 7, 5, 6, 3], [1, 0, 3, 4, 5, 6, 2], [2, 0, 1, 3, 4, 5, 3], [11, 0, 1, 2, 3, 4, 5, 6, 3], [12, 0, 1, 2, 3], [7, 0, 1, 3], [5, 0, 1, 3, 5, 4, 6, 3], [5, 0, 7, 3, 5, 4, 6, 2], [5, 0, 2, 7, 3, 4, 3], [1, 0, 3, 4, 6, 2], [1, 0, 3, 4, 7, 5, 9, 6, 2], [3, 0, 2, 3, 7, 5, 4, 2], [3, 0, 1, 2, 3, 5, 6, 4, 3], [3, 0, 2, 3, 5, 6, 4, 2], [3, 0, 2, 3, 4, 2], [8, 0, 1, 2, 2], [9, 0, 1], [10, 0, 1, 2], [2, 1, 3, 4, 5, 2], [2, 2, 0, 1, 3, 4, 5, 4], [2, 2, 0, 3, 4, 3], [13, 0, 1], [14, 0, 1, 2, 3, 4, 3], [15, 0, 1, 2, 3], [16, 0, 1, 2, 3, 3], [4, 3, 0, 1, 2, 7, 8, 9, 5], [4, 3, 0, 1, 2, 7, 8, 5], [4, 0, 6, 1, 4, 2, 7, 8, 6], [6, 4, 0, 1, 2, 3, 5, 6, 7, 6], [6, 0, 1, 2, 3, 5, 6, 5], [17, 0, 1], [18, 0, 1, 2, 3, 4, 5, 6, 7, 1]], [[[{"name": "vang-btn", "rect": [0, 0, 268, 78], "offset": [0, 0], "originalSize": [268, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [3]], [[{"name": "treasure", "rect": [183, 283, 2135, 2217], "offset": [0.5, -141.5], "originalSize": [2500, 2500], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [4]], [[{"name": "vien@2x", "rect": [0, 0, 811, 61], "offset": [0, 0], "originalSize": [811, 61], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [5]], [[{"name": "BGcasino", "rect": [0, 0, 735, 397], "offset": [0, 0], "originalSize": [735, 397], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [6]], [[{"name": "logo-loading", "rect": [0, 0, 300, 300], "offset": [0, 0], "originalSize": [300, 300], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [7]], [[[10, "loadingWeb", null], [11, "<PERSON><PERSON>", "f9zJkgZT5EU7EZN1zP7Lyo", [-6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17], [[[20, true, -1, [5, 1561, 732]], -2, -3, [21, -4], [22, 45, -5]], 4, 1, 1, 4, 4], [5, 1561, 732], [780.5, 366, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn-retry", false, 1, [-21, -22], [[7, 2, false, -18, [16], 17], [8, 1.1, 3, -20, [[9, "23d18uKhIlKJaRUKfhtzpsz", "retryClicked", 1]], [4, 4294967295], [4, 4294967295], -19]], [5, 268, 78], [0, -156, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn-retryCheckV", false, 1, [-26, -27], [[7, 2, false, -23, [22], 23], [8, 1.1, 3, -25, [[9, "23d18uKhIlKJaRUKfhtzpsz", "retryCheckVersionClicked", 1]], [4, 4294967295], [4, 4294967295], -24]], [5, 203, 60], [0, -156, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "progressBar", 1, [-30], [[[23, false, -28, [3], 4], -29], 4, 1], [5, 811, 61], [28.122, -213, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "Logo", false, 1, [-31, -32, -33], [5, 475.42, 447]], [2, "bg_lb", 1, [[24, 1, 0, false, -34, [0], 1], [26, -35]], [5, 1561, 732]], [27, "New Node", false, [1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Main Camera", 1, [[28, 7, -1, -36]], [0, 0, 369.91193648057487, 0, 0, 0, 1, 1, 1, 1]], [16, "bar", 4, [-37], [4, 4293850922], [5, 719, 18], [-30.2, 6.6, 0, 0, 0, 0, 1, 1, 1, 1]], [25, 3, 0, 9, [2]], [29, 2, 0, 4, 10], [15, "Loading", 1, [[30, "<PERSON><PERSON> kết nối đến máy chủ ...", 26, false, 1, -38, [5], 6]], [4, 4294950971], [5, 332.31, 40], [0, 0, 0.5], [-178.01, -257.279, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "lbProgress", false, 1, [-39], [5, 38.37, 50.4], [0, 0, 0.5], [44, -269, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "0%", 26, false, 1, 13, [7]], [3, "tag-h-tr@2x", false, 1, [[0, -40, [8], 9]], [5, 128, 146], [743.58, 125.504, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "web@2x", false, 1, [[0, -41, [10], 11]], [5, 128, 147], [743.871, -18.511, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Label", 100, 2, [[1, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -42, [12], 13]], [4, 4278190080], [5, 84.78, 50.4], [-1, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 2, [[1, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -43, [14], 15]], [5, 84.78, 50.4]], [5, "Label", 100, 3, [[1, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -44, [18], 19]], [4, 4278190080], [5, 84.78, 50.4], [-1, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Label", 3, [[1, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -45, [20], 21]], [5, 84.78, 50.4]], [18, "lbMessage", 1, [-46], [5, 0, 37.8], [0, 0.5, 1], [0, -315, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 24, 30, false, 1, 1, 21, [24]], [6, "loading 2_cut", 1, [[33, "default", "animation", 0, false, "animation", -47, [25], 26]], [5, 228.51, 145.37], [-17.307, 44.694, 0, 0, 0, 0, 1, 3, 3, 1]], [19, "789600", 5, [-48], [-39.224, -274.955, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [34, "effect phao hoa", 0, false, "effect phao hoa", 24, [27]], [3, "logo-loading", false, 5, [[0, -49, [28], 29]], [5, 300, 300], [0, 18.568, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [6, "logo", 5, [[0, -50, [30], 31]], [5, 2135, 2217], [0, 25.805, 0, 0, 0, 0, 1, 0.2, 0.2, 1]], [35, 1], [36, 1, 25, 28, 11, 14, 22, 2, 3]], 0, [0, 0, 1, 0, -2, 28, 0, -3, 29, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 6, 0, -3, 4, 0, -4, 12, 0, -5, 13, 0, -6, 15, 0, -7, 16, 0, -8, 2, 0, -9, 3, 0, -10, 21, 0, -11, 23, 0, -12, 5, 0, 0, 2, 0, 4, 2, 0, 0, 2, 0, -1, 17, 0, -2, 18, 0, 0, 3, 0, 4, 3, 0, 0, 3, 0, -1, 19, 0, -2, 20, 0, 0, 4, 0, -2, 11, 0, -1, 9, 0, -1, 24, 0, -2, 26, 0, -3, 27, 0, 0, 6, 0, 0, 6, 0, 0, 8, 0, -1, 10, 0, 0, 12, 0, -1, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, -1, 22, 0, 0, 23, 0, -1, 25, 0, 0, 26, 0, 0, 27, 0, 6, 7, 1, 7, 7, 28, 8, 29, 50], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 14, 22, 25, 28], [-1, 2, -1, -1, 2, -1, 3, -1, -1, 2, -1, 2, -1, 3, -1, 3, -1, 2, -1, 3, -1, 3, -1, 2, -1, -1, 5, -1, -1, 2, -1, 2, 2, 3, 3, 5, 9], [0, 8, 0, 0, 9, 0, 10, 0, 0, 11, 0, 12, 0, 1, 0, 1, 0, 13, 0, 1, 0, 1, 0, 14, 0, 2, 15, 2, 0, 16, 0, 17, 18, 19, 20, 21, 22]], [[{"name": "tag-h-tr@2x", "rect": [0, 0, 128, 146], "offset": [0, 0.5], "originalSize": [128, 147], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [23]], [[{"name": "loading", "rect": [2, 2, 723, 18], "offset": [0.5, -0.5], "originalSize": [726, 21], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [24]], [[{"name": "button2", "rect": [5, 2, 192, 58], "offset": [-0.5, -1], "originalSize": [203, 60], "capInsets": [89, 0, 88, 0]}], [0], 0, [0], [1], [25]], [[{"name": "tai-app@2x", "rect": [0, 0, 128, 147], "offset": [0, 0], "originalSize": [128, 147], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [26]]]]