[1, ["7a/QZLET9IDreTiBfRn2PD", "3egc9IKZhG6LhPK2W8CZj3", "ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "271R0s38tAlIenzZCkaPff", "6ckK0dwQ5D9Izf3Aay5wGD", "27prX/qmpC/K00V6HQEsdU", "2cWB/vWPRHja3uQTinHH30", "24ABGoimpEfbwqdwyzt/ai"], ["_N$skeletonData", "node", "_N$file", "_spriteFrame", "root", "lbTotalWin", "lbTotalBet", "lbSessionID", "_N$target", "data", "_parent", "_defaultClip", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint"], 0, 4, 9, 5, 7, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "node", "_materials", "_N$file"], -2, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "defaultSkin", "node", "_materials"], 0, 1, 3], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["bfff4nSfydEvoywHot7bNxR", ["node", "lbSessionID", "lbTotalBet", "lbTotalWin", "skeletonIcons"], 3, 1, 1, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[10, 0, 1, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3, 4, 3], [0, 0, 7, 4, 3, 5, 6, 2], [3, 0, 1, 2, 3, 4, 6, 5, 2], [2, 0, 1, 2, 3, 5, 6, 5], [0, 0, 7, 4, 3, 5, 9, 6, 2], [1, 0, 1, 2, 3, 4, 3], [2, 0, 1, 2, 3, 5, 6, 7, 5], [7, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 5, 6, 2], [0, 0, 7, 8, 4, 3, 5, 6, 2], [0, 0, 1, 7, 4, 3, 5, 6, 3], [0, 0, 2, 7, 4, 3, 5, 6, 3], [0, 0, 7, 8, 3, 5, 9, 6, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 1], [11, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [4, 2, 7, 1], [12, 0, 1, 2, 3], [1, 0, 2, 3, 4, 2], [1, 2, 3, 4, 1], [2, 0, 1, 2, 4, 3, 5, 6, 7, 6], [5, 2, 0, 1, 3, 4, 4]], [[[[9, "aquariumSessionDetailView"], [10, "aquariumSessionDetailView", [-22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32], [[16, -2, [34, 35], 33], [17, -21, -20, -19, -18, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17]]], [0, "041usxavRJ/bZjULEhQSem", -1]], [11, "spinView", [-34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48], [[18, 1, -33, [32]]], [0, "ed/tvt+qFDZ4fkHZLImp5W", 1], [5, 900, 494], [0, 55, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [12, "btnClose", 1, [-51], [[19, 1.1, 3, -50, [[21, "bfff4nSfydEvoywHot7bNxR", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -49]], [0, "cb31BcTLNL5b8CYUYUjA71", 1], [5, 80, 80], [452, 277, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[22, 0, -52, [0], 1], [20, -53, [4, 4292269782]]], [0, "30FLp1bAZN8Z7dOjXcpM9H", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "nen popup", 1, [[7, 2, false, -54, [2], 3]], [0, "13+KrxE41MOY9boehDxIQ+", 1], [5, 955, 594], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "title_ctp", false, 1, [[23, -55, [4], 5]], [0, "f89dhvf19CpbbJnyn+WhSn", 1], [5, 217, 38], [0, 296, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 3, [[7, 2, false, -56, [6], 7]], [0, "b8W0ZhkuFKjIgNRlh0GXM3", 1], [5, 180, 58], [-444.325, -526.325, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "label", 1, [[24, "TÂY DU KÝ - CHI TIẾT PHIÊN:", 22, false, 1, 1, -57, [8], 9]], [0, "a2T4/uN7NOM6RzQvmXym+E", 1], [5, 293.9, 40], [-59.516, 206.837, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbSessionID", 1, [-58], [0, "18r0oRAphCQYXqc2krkNbj", 1], [5, 114.95, 40], [0, 0, 0.5], [102.484, 206.837, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "#12345678", 22, false, 1, 9, [10]], [6, "label", 1, [[8, "Cược:", 22, false, 1, -59, [11], 12]], [0, "80eezcNbNCA48VJ0g+YwcD", 1], [5, 57.75, 40], [0, 0, 0.5], [-382, 197, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbTotalBet", 1, [-60], [0, "4ePEHnnsJMPLK2g/y/uA4W", 1], [5, 83.05, 40], [0, 0, 0.5], [-315, 197, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "250.000", 22, false, 1, 12, [13]], [6, "label", 1, [[8, "Thắng:", 22, false, 1, -61, [14], 15]], [0, "81y9PfPb5LXqcVP3tlNlns", 1], [5, 68.75, 40], [0, 0, 0.5], [-382, 169, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbTotalWin", 1, [-62], [0, "6bhXGfBy1CLJ/spz05AnKQ", 1], [5, 83.05, 40], [0, 0, 0.5], [-304, 169, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "250.000", 22, false, 1, 15, [16]], [15, "backgroundSlot", 1, [2], [0, "5aJ+lT/QRD6oAIzg/Ew0gl", 1], [5, 954, 512], [0, 0.5, 0.3], [0, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "skeleton1", 2, [-63], [0, "65nO36LuJOfZrkhLuz13va", 1], [5, 163, 191.08], [-336, 171, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "default", 0, false, 18, [17]], [1, "skeleton2", 2, [-64], [0, "23EMd7fCtCLp9VVZ0ghPD6", 1], [5, 163, 191.08], [-168, 171, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 20, [18]], [1, "skeleton3", 2, [-65], [0, "3eo359hURA8os7PztIxa4k", 1], [5, 163, 191.08], [0, 171, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 22, [19]], [1, "skeleton4", 2, [-66], [0, "aen5WEGVtMhpyAp71WOhHz", 1], [5, 163, 191.08], [168, 171, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 24, [20]], [1, "skeleton5", 2, [-67], [0, "a3QksOrPNBKKVCFTS2oh5e", 1], [5, 163, 191.08], [336, 171, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 26, [21]], [1, "skeleton6", 2, [-68], [0, "4fHii+SClP1KDI/ppVovJ/", 1], [5, 163, 191.08], [-336, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 28, [22]], [1, "skeleton7", 2, [-69], [0, "b9gk1p2yNIEZ6PbAvAGWE5", 1], [5, 163, 191.08], [-168, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 30, [23]], [1, "skeleton8", 2, [-70], [0, "c0V6EqMIZCraHuxcwc7l/t", 1], [5, 163, 191.08], [0, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 32, [24]], [1, "skeleton9", 2, [-71], [0, "0e0e0pwN1OeIh8UOesl1JK", 1], [5, 163, 191.08], [168, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 34, [25]], [1, "skeleton10", 2, [-72], [0, "f3GRc4MnZDFLYoNkvkZ4AZ", 1], [5, 163, 191.08], [336, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 36, [26]], [1, "skeleton11", 2, [-73], [0, "c2fN7fBaJMwJLMFExy3tMk", 1], [5, 163, 191.08], [-336, -159, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 38, [27]], [1, "skeleton12", 2, [-74], [0, "cdAnqqSRdGfahUTQhW0mQC", 1], [5, 163, 191.08], [-168, -159, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 40, [28]], [1, "skeleton13", 2, [-75], [0, "661ctlUABFOYlzD4f27anl", 1], [5, 163, 191.08], [0, -159, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 42, [29]], [1, "skeleton14", 2, [-76], [0, "2blAH7zsVImJFoPoFHClkD", 1], [5, 163, 191.08], [168, -159, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 44, [30]], [1, "skeleton15", 2, [-77], [0, "bbsv4gZypOMrdPKAMFvq1E", 1], [5, 163, 191.08], [336, -159, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 0, false, 46, [31]]], 0, [0, 4, 1, 0, 1, 1, 0, -1, 19, 0, -2, 21, 0, -3, 23, 0, -4, 25, 0, -5, 27, 0, -6, 29, 0, -7, 31, 0, -8, 33, 0, -9, 35, 0, -10, 37, 0, -11, 39, 0, -12, 41, 0, -13, 43, 0, -14, 45, 0, -15, 47, 0, 5, 16, 0, 6, 13, 0, 7, 10, 0, 1, 1, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 3, 0, -5, 8, 0, -6, 9, 0, -7, 11, 0, -8, 12, 0, -9, 14, 0, -10, 15, 0, -11, 17, 0, 1, 2, 0, -1, 18, 0, -2, 20, 0, -3, 22, 0, -4, 24, 0, -5, 26, 0, -6, 28, 0, -7, 30, 0, -8, 32, 0, -9, 34, 0, -10, 36, 0, -11, 38, 0, -12, 40, 0, -13, 42, 0, -14, 44, 0, -15, 46, 0, 8, 3, 0, 1, 3, 0, -1, 7, 0, 1, 4, 0, 1, 4, 0, 1, 5, 0, 1, 6, 0, 1, 7, 0, 1, 8, 0, -1, 10, 0, 1, 11, 0, -1, 13, 0, 1, 14, 0, -1, 16, 0, -1, 19, 0, -1, 21, 0, -1, 23, 0, -1, 25, 0, -1, 27, 0, -1, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, -1, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, -1, 45, 0, -1, 47, 0, 9, 1, 2, 10, 17, 77], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 13, 16, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47], [-1, 3, -1, 3, -1, 3, -1, 3, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 11, -1, -2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 5, 2, 6, 2, 7, 2, 8, 2, 3, 2, 2, 3, 2, 2, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 4, 4, 9, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[{"name": "title_ctp", "rect": [0, 0, 217, 38], "offset": [0, 0], "originalSize": [217, 38], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [12], [10]]]]