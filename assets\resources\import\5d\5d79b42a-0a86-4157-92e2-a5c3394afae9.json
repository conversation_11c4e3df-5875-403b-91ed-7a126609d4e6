[1, ["a2zQhheWZG34w3Q7AT9l5z"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "avenger_symbol_1_0", "\nALL SYMBOL AVENGERS.png\nsize: 2048,2048\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nCAPTAIN MARVEL WILD/1\n  rotate: false\n  xy: 504, 1926\n  size: 54, 54\n  orig: 169, 165\n  offset: 44, 78\n  index: -1\nCAPTAIN MARVEL WILD/10\n  rotate: false\n  xy: 1213, 1231\n  size: 129, 121\n  orig: 169, 165\n  offset: 6, 1\n  index: -1\nCAPTAIN MARVEL WILD/11\n  rotate: false\n  xy: 129, 1302\n  size: 131, 120\n  orig: 169, 165\n  offset: 16, 0\n  index: -1\nCAPTAIN MARVEL WILD/12\n  rotate: false\n  xy: 1470, 1221\n  size: 132, 121\n  orig: 169, 165\n  offset: 30, 0\n  index: -1\nCAPTAIN MARVEL WILD/13\n  rotate: true\n  xy: 1, 1180\n  size: 123, 125\n  orig: 169, 165\n  offset: 43, 5\n  index: -1\nCAPTAIN MARVEL WILD/14\n  rotate: true\n  xy: 538, 1267\n  size: 120, 130\n  orig: 169, 165\n  offset: 47, 10\n  index: -1\nCAPTAIN MARVEL WILD/15\n  rotate: true\n  xy: 261, 1294\n  size: 119, 132\n  orig: 169, 165\n  offset: 48, 18\n  index: -1\nCAPTAIN MARVEL WILD/16\n  rotate: true\n  xy: 1737, 1209\n  size: 122, 130\n  orig: 169, 165\n  offset: 47, 29\n  index: -1\nCAPTAIN MARVEL WILD/17\n  rotate: true\n  xy: 1061, 1118\n  size: 125, 127\n  orig: 169, 165\n  offset: 43, 35\n  index: -1\nCAPTAIN MARVEL WILD/18\n  rotate: false\n  xy: 259, 1170\n  size: 128, 123\n  orig: 169, 165\n  offset: 37, 41\n  index: -1\nCAPTAIN MARVEL WILD/19\n  rotate: false\n  xy: 669, 1265\n  size: 131, 120\n  orig: 169, 165\n  offset: 30, 45\n  index: -1\nCAPTAIN MARVEL WILD/2\n  rotate: false\n  xy: 103, 1762\n  size: 90, 90\n  orig: 169, 165\n  offset: 25, 60\n  index: -1\nCAPTAIN MARVEL WILD/20\n  rotate: false\n  xy: 664, 1622\n  size: 116, 109\n  orig: 169, 165\n  offset: 28, 52\n  index: -1\nCAPTAIN MARVEL WILD/3\n  rotate: false\n  xy: 1774, 1683\n  size: 106, 106\n  orig: 169, 165\n  offset: 15, 53\n  index: -1\nCAPTAIN MARVEL WILD/4\n  rotate: false\n  xy: 1910, 1568\n  size: 114, 114\n  orig: 169, 165\n  offset: 6, 46\n  index: -1\nCAPTAIN MARVEL WILD/5\n  rotate: true\n  xy: 1389, 1349\n  size: 117, 122\n  orig: 169, 165\n  offset: 3, 37\n  index: -1\nCAPTAIN MARVEL WILD/6\n  rotate: true\n  xy: 1763, 1332\n  size: 118, 128\n  orig: 169, 165\n  offset: 2, 26\n  index: -1\nCAPTAIN MARVEL WILD/7\n  rotate: true\n  xy: 931, 1260\n  size: 121, 133\n  orig: 169, 165\n  offset: 0, 15\n  index: -1\nCAPTAIN MARVEL WILD/8\n  rotate: true\n  xy: 127, 1178\n  size: 123, 131\n  orig: 169, 165\n  offset: 0, 6\n  index: -1\nCAPTAIN MARVEL WILD/9\n  rotate: true\n  xy: 786, 1138\n  size: 125, 126\n  orig: 169, 165\n  offset: 3, 2\n  index: -1\nCAPTAIN MARVEL WILD/BODY\n  rotate: false\n  xy: 1533, 1955\n  size: 119, 46\n  orig: 121, 48\n  offset: 1, 1\n  index: -1\nCAPTAIN MARVEL WILD/BORDER\n  rotate: true\n  xy: 152, 741\n  size: 151, 152\n  orig: 153, 154\n  offset: 1, 1\n  index: -1\nCAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD\n  rotate: true\n  xy: 1521, 1885\n  size: 69, 107\n  orig: 71, 109\n  offset: 1, 1\n  index: -1\nCAPTAIN MARVEL WILD/HAIR 1\n  rotate: false\n  xy: 314, 1757\n  size: 120, 90\n  orig: 130, 92\n  offset: 1, 1\n  index: -1\nCAPTAIN MARVEL WILD/HAIR 2\n  rotate: false\n  xy: 1837, 1951\n  size: 84, 50\n  orig: 86, 52\n  offset: 1, 1\n  index: -1\nCAPTAIN MARVEL WILD/HAIR BACK 1\n  rotate: true\n  xy: 99, 1929\n  size: 52, 109\n  orig: 55, 111\n  offset: 1, 1\n  index: -1\nCAPTAIN MARVEL WILD/HAIR BACK 2\n  rotate: false\n  xy: 1629, 1884\n  size: 87, 70\n  orig: 89, 72\n  offset: 1, 1\n  index: -1\nCAPTAIN MARVEL WILD/BACK\n  rotate: true\n  xy: 781, 1620\n  size: 109, 128\n  orig: 111, 132\n  offset: 1, 2\n  index: -1\nCAPTAIN MARVEL WILD/WILD\n  rotate: false\n  xy: 1203, 1907\n  size: 120, 59\n  orig: 122, 61\n  offset: 1, 1\n  index: -1", ["ALL SYMBOL AVENGERS.png"], {"skeleton": {"hash": "5s5m4oWTxBD5AZ2f7TOv8I/77Uw", "spine": "3.8.99", "x": -220.96, "y": -215.73, "width": 441.92, "height": 431.46, "images": "../", "audio": "/Users/<USER>/Documents/Slot ADVENTURE/ALL ICON AVENGERS/CAPTAIN MARVEL/CAPTAIN MARVEL ICON OUT/CAPTAIN MARVEL MINI"}, "bones": [{"name": "root"}, {"name": "CENTER", "parent": "root"}, {"name": "WILD", "parent": "root", "y": -44.46}, {"name": "bone", "parent": "CENTER", "length": 50.83, "rotation": 100.82, "x": -7.69, "y": -54.24}, {"name": "bone2", "parent": "bone", "length": 66.27, "rotation": 7.36, "x": 50.83}, {"name": "bone3", "parent": "bone2", "length": 22.4, "rotation": -114.69, "x": 82.5, "y": -1.98}, {"name": "bone4", "parent": "bone3", "length": 27.04, "rotation": -34.67, "x": 22.4}, {"name": "bone5", "parent": "bone4", "length": 25.87, "rotation": -2.32, "x": 27.04}, {"name": "bone6", "parent": "bone5", "length": 24.96, "rotation": 9.41, "x": 26.32, "y": -0.01}, {"name": "bone7", "parent": "bone6", "length": 28.29, "rotation": 25.69, "x": 25.22, "y": 0.18}, {"name": "bone8", "parent": "bone2", "length": 19.32, "rotation": -141.08, "x": 68.16, "y": -34.43}, {"name": "bone9", "parent": "bone8", "length": 21.92, "rotation": 8.94, "x": 19.32}, {"name": "bone10", "parent": "bone9", "length": 17.61, "rotation": 7.16, "x": 21.92}, {"name": "bone11", "parent": "bone10", "length": 20.32, "rotation": -3.34, "x": 17.61}, {"name": "bone12", "parent": "bone2", "length": 17.92, "rotation": -172.97, "x": 40.05, "y": -22.18}, {"name": "bone13", "parent": "bone12", "length": 18.13, "rotation": 12.67, "x": 17.92}, {"name": "bone14", "parent": "bone13", "length": 19.67, "rotation": 13.03, "x": 18.38, "y": -0.2}, {"name": "bone15", "parent": "bone14", "length": 18.91, "rotation": 22.49, "x": 19.67}, {"name": "bone16", "parent": "bone2", "length": 13.61, "rotation": 109.23, "x": 79.55, "y": 4.34}, {"name": "bone17", "parent": "bone16", "length": 15.08, "rotation": 34.16, "x": 13.61}, {"name": "bone18", "parent": "bone17", "length": 21.01, "rotation": 21.04, "x": 16.49, "y": -0.2}, {"name": "bone19", "parent": "bone18", "length": 24.64, "rotation": 14.66, "x": 21.63, "y": -0.35}, {"name": "bone20", "parent": "bone19", "length": 19.34, "rotation": 19.04, "x": 24.64}, {"name": "bone21", "parent": "bone20", "length": 12.79, "rotation": -1.43, "x": 19.34}, {"name": "BONE EFFECT", "parent": "root", "scaleX": 2.6149, "scaleY": 2.6149}], "slots": [{"name": "CAPTAIN MARVEL WILD/BACK", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/BACK"}, {"name": "CAPTAIN MARVEL WILD/BODY", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/BODY"}, {"name": "CAPTAIN MARVEL WILD/BORDER", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/BORDER"}, {"name": "CAPTAIN MARVEL WILD/EFFECT", "bone": "BONE EFFECT", "attachment": "CAPTAIN MARVEL WILD/14"}, {"name": "CAPTAIN MARVEL WILD/HAIR BACK 1", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/HAIR BACK 1"}, {"name": "CAPTAIN MARVEL WILD/HAIR BACK 2", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/HAIR BACK 2"}, {"name": "CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD"}, {"name": "CAPTAIN MARVEL WILD/HAIR 2", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/HAIR 2"}, {"name": "CAPTAIN MARVEL WILD/HAIR 1", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/HAIR 1"}, {"name": "CAPTAIN MARVEL WILD/WILD", "bone": "root", "attachment": "CAPTAIN MARVEL WILD/WILD"}], "skins": {"default": {"CAPTAIN MARVEL WILD/BACK": {"CAPTAIN MARVEL WILD/BACK": {"type": "mesh", "hull": 4, "width": 111, "height": 132, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 55.5, -66, 1, 1, 1, -55.5, -66, 1, 1, 1, -55.5, 66, 1, 1, 1, 55.5, 66, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "CAPTAIN MARVEL WILD/BODY": {"CAPTAIN MARVEL WILD/BODY": {"type": "mesh", "hull": 4, "width": 121, "height": 48, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -13.98, -63.79, 1, 1, 3, 8.73, 55.06, 1, 1, 3, 55.88, 46.05, 1, 1, 3, 33.17, -72.8, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "CAPTAIN MARVEL WILD/BORDER": {"CAPTAIN MARVEL WILD/BORDER": {"type": "mesh", "hull": 4, "width": 153, "height": 154, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 76.5, -77, 1, 1, 1, -76.5, -77, 1, 1, 1, -76.5, 77, 1, 1, 1, 76.5, 77, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD": {"CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD": {"type": "mesh", "hull": 31, "width": 71, "height": 109, "uvs": [0.17344, 0.05837, 0.05251, 0.14589, 0, 0.23195, 0, 0.3647, 0.01892, 0.43472, 0.0122, 0.5237, 0.03684, 0.62726, 0.11298, 0.73813, 0.19584, 0.80669, 0.28317, 0.81981, 0.29437, 0.86941, 0.27198, 0.89421, 0.26974, 0.96277, 0.27869, 1, 0.48248, 1, 0.69299, 0.97736, 0.9035, 0.89567, 1, 0.82565, 0.94604, 0.74396, 0.91917, 0.71187, 0.88334, 0.70166, 0.88782, 0.61414, 0.89006, 0.53391, 0.9662, 0.49015, 1, 0.40152, 1, 0.30574, 0.95082, 0.21657, 0.8849, 0.09437, 0.71251, 0.02996, 0.50969, 0, 0.32969, 0, 0.22235, 0.30299, 0.39348, 0.23016, 0.56917, 0.26583, 0.56663, 0.39293, 0.33998, 0.45932, 0.24922, 0.39183, 0.13976, 0.31755, 0.18032, 0.38889, 0.133, 0.47168, 0.05593, 0.46551, 0.02888, 0.39153, 0.02618, 0.33781, 0.05863, 0.32195, 0.15733, 0.15205, 0.17897, 0.31322, 0.16545, 0.52988, 0.13435, 0.49993, 0.18708, 0.54926, 0.21548, 0.5519, 0.18708, 0.52548, 0.1668, 0.49289, 0.1668, 0.46471, 0.20331, 0.38456, 0.11542, 0.17142, 0.21818, 0.15821, 0.30336, 0.54573, 0.32635, 0.52988, 0.32905, 0.5061, 0.30742, 0.4832, 0.27902, 0.46647, 0.15463, 0.54749, 0.13164, 0.53516, 0.11136, 0.51226, 0.1215, 0.49289, 0.3979, 0.09425, 0.24426, 0.07572, 0.16568, 0.09761, 0.12377, 0.11875, 0.06689, 0.18651, 0.4307, 0.05005, 0.59901, 0.12494, 0.6247, 0.058, 0.80858, 0.50639, 0.78965, 0.59446, 0.74503, 0.66844, 0.65579, 0.71336, 0.52464, 0.76268, 0.31101, 0.79351, 0.25151, 0.78646, 0.18796, 0.76796, 0.13929, 0.71688, 0.09602, 0.64554, 0.05951, 0.59182, 0.04058, 0.54162, 0.04599, 0.50463, 0.02808, 0.20957, 0.57674, 0.49246, 0.52772, 0.62457, 0.4804, 0.70934, 0.45843, 0.60145, 0.36209, 0.56842, 0.25054, 0.56072, 0.2235, 0.57173, 0.1897, 0.56732, 0.13561, 0.61796, 0.13223, 0.6576, 0.19646, 0.69173, 0.27758, 0.69943, 0.39758, 0.6576, 0.83913, 0.52034, 0.89851, 0.50287, 0.9464, 0.46294, 0.96556, 0.39931, 0.96556, 0.33693, 0.84297, 0.57773, 0.84488, 0.66008, 0.84871, 0.7312, 0.33728, 0.81854, 0.3526, 0.86096, 0.36409, 0.90214], "triangles": [70, 30, 29, 72, 29, 28, 66, 0, 30, 65, 30, 70, 66, 30, 65, 67, 0, 66, 67, 68, 0, 71, 29, 72, 68, 1, 0, 44, 68, 67, 55, 67, 66, 44, 67, 55, 54, 1, 68, 54, 68, 44, 69, 1, 54, 86, 1, 69, 65, 55, 66, 32, 55, 65, 70, 29, 71, 86, 2, 1, 71, 65, 70, 71, 32, 65, 33, 32, 71, 31, 55, 32, 45, 44, 55, 45, 55, 31, 54, 44, 45, 37, 54, 45, 69, 54, 37, 43, 86, 69, 37, 43, 69, 42, 2, 86, 104, 26, 25, 43, 42, 86, 3, 2, 42, 53, 45, 31, 38, 45, 53, 37, 45, 38, 41, 42, 43, 3, 42, 41, 36, 31, 32, 53, 31, 36, 34, 32, 33, 35, 36, 32, 24, 103, 104, 24, 104, 25, 4, 3, 41, 34, 35, 32, 71, 26, 104, 26, 71, 27, 103, 101, 104, 102, 103, 24, 37, 41, 43, 52, 38, 53, 38, 40, 41, 4, 41, 40, 41, 37, 38, 38, 39, 40, 60, 36, 35, 52, 39, 38, 59, 60, 35, 23, 102, 24, 33, 104, 34, 104, 87, 34, 35, 34, 87, 71, 72, 28, 27, 71, 28, 104, 33, 71, 64, 40, 39, 60, 50, 51, 39, 52, 51, 47, 64, 39, 51, 47, 39, 103, 102, 101, 101, 102, 23, 104, 73, 87, 85, 4, 40, 63, 85, 40, 58, 59, 35, 101, 73, 104, 64, 63, 40, 63, 64, 47, 100, 73, 101, 5, 4, 85, 60, 51, 52, 36, 52, 53, 60, 52, 36, 46, 47, 51, 46, 51, 50, 58, 56, 59, 22, 100, 101, 22, 101, 23, 62, 63, 47, 62, 47, 46, 84, 5, 85, 56, 60, 59, 57, 56, 58, 49, 50, 60, 61, 62, 46, 48, 46, 50, 49, 48, 50, 61, 46, 48, 49, 60, 92, 56, 92, 60, 94, 48, 49, 90, 91, 35, 58, 35, 91, 57, 58, 91, 56, 57, 91, 92, 56, 91, 93, 49, 92, 94, 49, 93, 105, 100, 22, 63, 84, 85, 83, 63, 62, 83, 84, 63, 74, 87, 73, 105, 74, 73, 105, 73, 100, 87, 90, 35, 21, 105, 22, 95, 62, 61, 83, 62, 95, 94, 95, 61, 94, 61, 48, 88, 90, 87, 87, 76, 88, 6, 5, 84, 6, 84, 83, 82, 83, 95, 6, 83, 82, 96, 82, 95, 99, 91, 90, 106, 105, 21, 74, 105, 106, 74, 75, 87, 75, 74, 106, 97, 94, 93, 95, 94, 97, 96, 95, 97, 98, 92, 91, 98, 91, 99, 93, 92, 98, 97, 93, 98, 20, 106, 21, 89, 90, 88, 99, 90, 89, 75, 76, 87, 77, 89, 88, 81, 96, 97, 82, 96, 81, 107, 106, 20, 75, 106, 107, 7, 82, 81, 6, 82, 7, 76, 77, 88, 80, 81, 97, 79, 97, 98, 80, 97, 79, 78, 98, 99, 79, 98, 78, 8, 80, 79, 7, 81, 80, 8, 7, 80, 108, 78, 99, 108, 99, 89, 9, 79, 78, 8, 79, 9, 77, 109, 108, 77, 108, 89, 108, 10, 9, 108, 9, 78, 10, 108, 109, 107, 19, 18, 19, 107, 20, 16, 18, 17, 16, 107, 18, 110, 109, 77, 10, 109, 110, 107, 15, 76, 107, 76, 75, 15, 107, 16, 77, 76, 15, 14, 110, 77, 11, 110, 12, 110, 11, 10, 13, 12, 110, 15, 14, 77, 13, 110, 14], "vertices": [1, 4, 81.15, 6.9, 1, 1, 4, 74.77, 18.03, 1, 1, 4, 67.02, 24.5, 1, 1, 4, 53.27, 29.02, 1, 1, 4, 45.6, 30.12, 1, 1, 4, 36.54, 33.6, 1, 1, 4, 25.26, 35.46, 1, 1, 4, 12.1, 34.09, 1, 1, 4, 3.16, 30.83, 1, 1, 4, -0.13, 25.39, 1, 1, 4, -5.52, 26.32, 1, 1, 4, -7.59, 28.67, 1, 1, 4, -14.64, 31.15, 1, 1, 4, -18.69, 31.81, 1, 1, 4, -23.21, 18.07, 1, 1, 4, -25.52, 3.1, 1, 1, 4, -21.73, -13.88, 1, 1, 4, -16.61, -22.77, 1, 1, 4, -6.96, -21.91, 1, 1, 4, -3.04, -21.19, 1, 1, 4, -1.19, -19.12, 1, 1, 4, 7.78, -22.39, 1, 1, 4, 16.04, -25.27, 1, 1, 4, 18.88, -31.9, 1, 1, 4, 27.31, -37.19, 1, 1, 4, 37.23, -40.45, 1, 1, 4, 47.56, -40.16, 1, 1, 4, 61.67, -39.87, 1, 1, 4, 72.16, -30.43, 1, 1, 4, 79.75, -17.77, 1, 1, 4, 83.74, -5.62, 1, 1, 4, 54.74, 11.92, 1, 1, 4, 58.49, -2.1, 1, 1, 4, 50.91, -12.74, 1, 1, 4, 37.8, -8.25, 1, 1, 4, 35.94, 9.3, 1, 1, 4, 44.94, 13.13, 1, 1, 4, 55.06, 17.98, 1, 1, 4, 46.77, 17.67, 1, 1, 4, 39.25, 23.68, 1, 1, 4, 41.59, 28.67, 1, 1, 4, 49.85, 27.98, 1, 1, 4, 55.48, 26.34, 1, 1, 4, 56.4, 23.61, 1, 1, 4, 71.81, 11.17, 1, 1, 4, 54.64, 15.19, 1, 1, 4, 32.5, 23.47, 1, 1, 4, 36.29, 24.55, 1, 1, 4, 30.02, 22.67, 1, 1, 4, 29.11, 20.84, 1, 1, 4, 32.48, 21.86, 1, 1, 4, 36.3, 22.12, 1, 1, 4, 39.22, 21.16, 1, 1, 4, 46.71, 15.98, 1, 1, 4, 70.73, 14.66, 1, 1, 4, 69.82, 7.28, 1, 1, 4, 27.81, 14.71, 1, 1, 4, 28.94, 12.62, 1, 1, 4, 31.34, 11.63, 1, 1, 4, 34.19, 12.31, 1, 1, 4, 36.55, 13.65, 1, 1, 4, 30.92, 24.8, 1, 1, 4, 32.7, 25.93, 1, 1, 4, 35.52, 26.52, 1, 1, 4, 37.31, 25.18, 1, 1, 4, 72.47, -7.02, 1, 1, 4, 77.79, 2.71, 1, 1, 4, 77.26, 8.76, 1, 1, 4, 76, 12.3, 1, 1, 4, 70.24, 18.44, 1, 1, 4, 76.32, -10.74, 1, 1, 4, 64.84, -19.54, 1, 1, 4, 71.2, -23.55, 1, 1, 4, 20.69, -20.71, 1, 1, 4, 11.99, -16.44, 1, 1, 4, 5.32, -10.92, 1, 1, 4, 2.64, -3.37, 1, 1, 4, 0.44, 7.15, 1, 1, 4, 1.98, 22.61, 1, 1, 4, 4.02, 26.39, 1, 1, 4, 7.35, 30.05, 1, 1, 4, 13.71, 31.59, 1, 1, 4, 22.06, 32.09, 1, 1, 4, 28.43, 32.72, 1, 1, 4, 34.05, 32.29, 1, 1, 4, 37.76, 30.67, 1, 1, 4, 68.72, 21.85, 1, 1, 4, 27.27, -5.55, 1, 1, 4, 14.67, 2.25, 1, 1, 4, 6.94, 8.33, 1, 1, 4, 18.6, 6.14, 1, 1, 4, 24.15, 11.52, 1, 1, 4, 27.42, 18.78, 1, 1, 4, 26.88, 20.98, 1, 1, 4, 28.09, 23.11, 1, 1, 4, 24.04, 28.48, 1, 1, 4, 20.01, 30.05, 1, 1, 4, 15.05, 26.88, 1, 1, 4, 12.46, 21.67, 1, 1, 4, 14.13, 12.15, 1, 1, 4, 18.57, -22.3, 1, 1, 4, 19.06, -26.9, 1, 1, 4, 22.14, -31.49, 1, 1, 4, 28.3, -34.94, 1, 1, 4, 34.77, -37.06, 1, 1, 4, 12.54, -20.61, 1, 1, 4, 3.97, -17.94, 1, 1, 4, -3.48, -15.78, 1, 1, 4, -1.2, 21.69, 1, 1, 4, -5.93, 22.1, 1, 1, 4, -10.45, 22.73, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 0, 60, 62, 72, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 74, 74, 76, 92, 94, 94, 78, 92, 96, 98, 100, 100, 102, 102, 104, 104, 106, 106, 90, 90, 88, 74, 108, 62, 110, 112, 114, 114, 116, 116, 118, 118, 120, 122, 124, 124, 126, 126, 128, 64, 130, 110, 132, 88, 134, 108, 136, 86, 138, 130, 140, 66, 142, 142, 144, 146, 148, 148, 150, 150, 152, 152, 154, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 80, 84, 172, 68, 174, 174, 176, 176, 178, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 180, 146, 200, 200, 202, 202, 204, 204, 206, 206, 208, 200, 210, 210, 212, 212, 214, 216, 218, 218, 220]}}, "CAPTAIN MARVEL WILD/EFFECT": {"CAPTAIN MARVEL WILD/1": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/2": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/3": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/4": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/5": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/6": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/7": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/8": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/9": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/10": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/11": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/12": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/13": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/14": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/15": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/16": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/17": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/18": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/19": {"width": 169, "height": 165}, "CAPTAIN MARVEL WILD/20": {"width": 169, "height": 165}}, "CAPTAIN MARVEL WILD/HAIR 1": {"CAPTAIN MARVEL WILD/HAIR 1": {"type": "mesh", "hull": 22, "width": 130, "height": 92, "uvs": [0.05023, 0.04584, 0, 0.14366, 0, 0.26106, 0.10561, 0.36214, 0.20374, 0.47016, 0.30408, 0.58062, 0.3742, 0.71782, 0.45407, 0.8741, 0.58792, 1, 0.72968, 1, 0.79792, 1, 0.91561, 0.88714, 0.98715, 0.58714, 0.92946, 0.38497, 0.88948, 0.3812, 0.791, 0.37192, 0.65023, 0.29693, 0.59147, 0.25355, 0.49561, 0.1828, 0.40345, 0.10077, 0.29023, 0, 0.14023, 0, 0.18408, 0.2154, 0.251, 0.10127, 0.37331, 0.42736, 0.44946, 0.29366, 0.53023, 0.66866, 0.61331, 0.45671, 0.65715, 0.80236, 0.74023, 0.5904, 0.27534, 0.32421, 0.34965, 0.21021, 0.44943, 0.55221, 0.53648, 0.38421, 0.79337, 0.82821, 0.86556, 0.59121], "triangles": [29, 27, 15, 35, 15, 14, 29, 15, 35, 35, 14, 13, 35, 13, 12, 34, 28, 29, 35, 34, 29, 11, 35, 12, 34, 35, 11, 9, 28, 34, 10, 9, 34, 11, 10, 34, 26, 27, 29, 6, 32, 26, 28, 26, 29, 7, 6, 26, 7, 26, 28, 8, 7, 28, 8, 28, 9, 25, 18, 17, 33, 25, 17, 33, 17, 16, 24, 25, 33, 27, 33, 16, 27, 16, 15, 32, 24, 33, 32, 33, 27, 5, 4, 24, 5, 24, 32, 26, 32, 27, 6, 5, 32, 23, 20, 19, 31, 23, 19, 31, 19, 18, 22, 23, 31, 25, 31, 18, 30, 22, 31, 30, 31, 25, 24, 30, 25, 4, 3, 30, 4, 30, 24, 23, 21, 20, 23, 0, 21, 22, 0, 23, 1, 0, 22, 2, 1, 22, 3, 2, 22, 3, 22, 30], "vertices": [1, 5, -2.71, 7.84, 1, 1, 5, -8.18, -1.84, 1, 2, 5, -6.95, -12.57, 0.9802, 6, -16.99, -27.04, 0.0198, 3, 5, 7.75, -20.25, 0.69779, 6, -0.54, -24.99, 0.2961, 7, -26.55, -26.09, 0.00611, 4, 5, 21.55, -28.68, 0.18576, 6, 15.61, -24.07, 0.65833, 7, -10.45, -24.52, 0.15078, 8, -40.28, -18.16, 0.00514, 4, 5, 35.66, -37.29, 0.01029, 6, 32.11, -23.13, 0.2801, 7, 6, -22.91, 0.5772, 8, -23.78, -19.27, 0.13241, 3, 6, 47.29, -26.63, 0.02213, 7, 21.3, -25.79, 0.41254, 8, -9.16, -24.61, 0.56533, 3, 7, 38.73, -29.07, 0.06328, 8, 7.5, -30.69, 0.9345, 9, -29.36, -20.14, 0.00222, 2, 8, 28.4, -30.53, 0.8154, 9, -10.45, -29.05, 0.1846, 2, 8, 43.66, -20.2, 0.313, 9, 7.78, -26.36, 0.687, 2, 8, 51.01, -15.23, 0.12883, 9, 16.56, -25.06, 0.87117, 2, 8, 57.86, 1.95, 0.00053, 9, 30.17, -12.55, 0.99947, 2, 7, 70.82, 37.79, 0.02316, 9, 35.34, 16.11, 0.97684, 2, 7, 52.58, 46.12, 0.08821, 9, 25.2, 33.41, 0.91179, 2, 7, 48.57, 42.79, 0.10669, 9, 20.01, 33, 0.89331, 3, 7, 38.7, 34.6, 0.26384, 8, 17.87, 32.12, 0.01336, 9, 7.22, 31.97, 0.72279, 4, 6, 48.79, 26.14, 0.01278, 7, 20.67, 27, 0.74608, 8, -1.15, 27.57, 0.01591, 9, -11.89, 36.12, 0.22523, 4, 6, 40.42, 24.12, 0.07834, 7, 12.39, 24.64, 0.82978, 8, -9.71, 26.6, 0.00026, 9, -20.03, 38.95, 0.09162, 3, 6, 26.75, 20.81, 0.47331, 7, -1.13, 20.78, 0.51573, 9, -33.31, 43.57, 0.01095, 4, 5, 43.48, 8.04, 0.0217, 6, 12.77, 18.6, 0.90876, 7, -15.02, 18.01, 0.06954, 9, -46.26, 49.28, 1e-05, 2, 5, 27.81, 15.58, 0.53394, 6, -4.42, 15.88, 0.46606, 1, 5, 8.43, 13.36, 1, 2, 5, 16.35, -5.68, 0.78241, 6, -1.75, -8.12, 0.21759, 2, 5, 23.8, 5.74, 0.64114, 6, -2.12, 5.51, 0.35886, 4, 5, 43, -22.26, 0.00179, 6, 29.6, -6.59, 0.26235, 7, 2.82, -6.48, 0.72154, 8, -24.24, -2.54, 0.01432, 3, 6, 28.95, 9.18, 0.3885, 7, 1.54, 9.25, 0.60691, 9, -37.75, 32.6, 0.00459, 2, 7, 32.9, -8.54, 0.06516, 8, 5.1, -9.49, 0.93484, 4, 6, 54.86, 11.92, 0.00024, 7, 27.31, 13.04, 0.67557, 8, 3.12, 12.71, 0.16138, 9, -14.49, 20.88, 0.16281, 2, 8, 25.66, -10.43, 0.78105, 9, -4.2, -9.75, 0.21895, 3, 7, 47.75, 15.47, 0.08636, 8, 23.67, 11.77, 0.06736, 9, 3.63, 11.12, 0.84628, 4, 5, 29.27, -14.28, 0.0736, 6, 13.77, -7.84, 0.89482, 7, -12.95, -8.37, 0.03157, 8, -40.1, -1.83, 0, 3, 5, 37.68, -2.76, 0.00078, 6, 14.13, 6.42, 0.98686, 7, -13.16, 5.89, 0.01236, 3, 6, 44.61, -8.72, 0.00817, 7, 17.91, -8, 0.77416, 8, -9.6, -6.51, 0.21767, 3, 6, 42.95, 10.36, 0.02325, 7, 15.48, 11, 0.93309, 9, -25.35, 26.02, 0.04367, 2, 8, 41.66, -2.47, 0.03478, 9, 13.66, -9.51, 0.96522, 2, 7, 59.62, 26.64, 0.04098, 9, 19.76, 13.43, 0.95902], "edges": [0, 2, 2, 4, 4, 6, 14, 16, 20, 22, 22, 24, 24, 26, 30, 32, 40, 42, 0, 42, 6, 8, 8, 10, 36, 38, 38, 40, 10, 12, 12, 14, 32, 34, 34, 36, 16, 18, 18, 20, 26, 28, 28, 30]}}, "CAPTAIN MARVEL WILD/HAIR 2": {"CAPTAIN MARVEL WILD/HAIR 2": {"type": "mesh", "hull": 17, "width": 86, "height": 52, "uvs": [0.03058, 0, 0, 0.13687, 0.0236, 0.28687, 0.14569, 0.51187, 0.27476, 0.73687, 0.42825, 0.94456, 0.61662, 1, 0.8329, 1, 0.94453, 1, 1, 0.77148, 1, 0.51187, 0.92011, 0.36764, 0.89918, 0.18302, 0.76662, 0.10225, 0.59221, 0.07341, 0.39686, 0.00418, 0.18407, 0, 0.09337, 0.15995, 0.21546, 0.36187, 0.30965, 0.18302, 0.13872, 0.09648, 0.3829, 0.54648, 0.50151, 0.30418, 0.56081, 0.74264, 0.6829, 0.39071, 0.84686, 0.54071, 0.7736, 0.78302], "triangles": [9, 8, 26, 6, 26, 7, 8, 7, 26, 9, 26, 25, 26, 23, 25, 25, 10, 9, 25, 11, 10, 25, 24, 11, 24, 12, 11, 5, 23, 6, 6, 23, 26, 23, 24, 25, 24, 13, 12, 24, 14, 13, 5, 4, 23, 4, 21, 23, 23, 21, 24, 24, 21, 22, 4, 3, 21, 21, 18, 22, 22, 14, 24, 22, 19, 14, 3, 18, 21, 3, 2, 18, 2, 17, 18, 18, 17, 19, 18, 19, 22, 19, 17, 20, 14, 19, 15, 2, 1, 17, 20, 16, 19, 19, 16, 15, 17, 1, 20, 1, 0, 20, 20, 0, 16], "vertices": [1, 10, -14.03, 1.42, 1, 1, 10, -12.37, -5.98, 1, 1, 10, -6.43, -11.43, 1, 2, 10, 8.75, -15.54, 0.88291, 11, -12.86, -13.71, 0.11709, 3, 10, 24.42, -19.34, 0.23483, 11, 2.04, -19.9, 0.73938, 12, -22.21, -17.26, 0.02579, 4, 10, 41.37, -21.23, 0.00378, 11, 18.48, -24.4, 0.69331, 12, -6.46, -23.78, 0.29014, 13, -22.64, -25.14, 0.01277, 3, 11, 34.46, -20.46, 0.21107, 12, 9.89, -21.86, 0.56464, 13, -6.43, -22.27, 0.22429, 3, 11, 51.46, -12.9, 0.00154, 12, 27.69, -16.49, 0.0905, 13, 11.03, -15.87, 0.90796, 2, 12, 36.88, -13.71, 0.00115, 13, 20.04, -12.57, 0.99885, 1, 13, 20.43, 0.23, 1, 3, 11, 54.28, 16.13, 0.00181, 12, 34.11, 11.97, 0.01307, 13, 15.78, 12.91, 0.98511, 3, 11, 44.95, 20.19, 0.04474, 12, 25.37, 17.16, 0.20414, 13, 6.75, 17.58, 0.75112, 3, 11, 39.41, 28.23, 0.12523, 12, 20.87, 25.83, 0.36959, 13, 1.76, 25.98, 0.50518, 4, 10, 42.01, 31.35, 0.00014, 11, 27.29, 27.44, 0.29966, 12, 8.74, 26.56, 0.43178, 13, -10.39, 26, 0.26842, 4, 10, 28.6, 24.46, 0.06295, 11, 12.97, 22.72, 0.69226, 12, -6.05, 23.66, 0.20266, 13, -24.99, 22.24, 0.04213, 4, 10, 12.54, 18.35, 0.55816, 11, -3.84, 19.18, 0.43591, 12, -23.17, 22.25, 0.00593, 13, -42, 19.84, 0, 2, 10, -2.94, 8.6, 0.9911, 11, -20.65, 11.95, 0.0089, 1, 10, -4.97, -2.63, 1, 2, 10, 9.55, -5.74, 0.97385, 11, -10.54, -4.15, 0.02615, 2, 10, 11.29, 6.47, 0.86077, 11, -6.92, 7.64, 0.13923, 2, 10, -3.49, 2.26, 0.99998, 11, -22.18, 5.78, 2e-05, 3, 10, 26.85, -5.97, 0.06609, 11, 6.51, -7.07, 0.92787, 12, -16.17, -5.09, 0.00604, 4, 10, 28.57, 10.15, 0.05203, 11, 10.72, 8.58, 0.90188, 12, -10.05, 9.92, 0.04332, 13, -28.18, 8.29, 0.00277, 3, 11, 24.64, -10.18, 0.41214, 12, 1.42, -10.44, 0.55733, 13, -15.55, -11.36, 0.03053, 3, 11, 26.8, 10.81, 0.25417, 12, 6.19, 10.12, 0.64543, 13, -11.99, 9.43, 0.1004, 3, 11, 42.85, 9.41, 0.0092, 12, 21.94, 6.72, 0.10572, 13, 3.93, 6.96, 0.88509, 3, 11, 42.21, -4.67, 0.00293, 12, 19.55, -7.16, 0.24181, 13, 2.36, -7.03, 0.75527], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 0, 32]}}, "CAPTAIN MARVEL WILD/HAIR BACK 1": {"CAPTAIN MARVEL WILD/HAIR BACK 1": {"type": "mesh", "hull": 19, "width": 55, "height": 111, "uvs": [0.57532, 0, 0.44986, 0, 0.22404, 0.06331, 0.09357, 0.17769, 0.03335, 0.36666, 0.02834, 0.56309, 0.16884, 0.75952, 0.36957, 0.92363, 0.58034, 1, 0.80615, 0.98579, 0.86637, 0.87142, 0.97175, 0.70979, 0.8463, 0.55315, 0.69136, 0.42007, 0.57318, 0.32125, 0.44307, 0.2138, 0.4758, 0.15164, 0.57744, 0.10991, 0.6667, 0.07326, 0.53362, 0.07878, 0.49037, 0.03922, 0.3107, 0.09197, 0.41052, 0.12165, 0.20755, 0.19749, 0.32068, 0.19914, 0.17428, 0.364, 0.42715, 0.35081, 0.21753, 0.53711, 0.48704, 0.47281, 0.33732, 0.70032, 0.57688, 0.63108, 0.4937, 0.84101, 0.77319, 0.76682], "triangles": [7, 31, 8, 9, 8, 32, 8, 31, 32, 9, 32, 10, 10, 32, 11, 6, 29, 7, 7, 29, 31, 31, 30, 32, 31, 29, 30, 32, 12, 11, 32, 30, 12, 29, 28, 30, 6, 27, 29, 6, 5, 27, 29, 27, 28, 27, 5, 25, 30, 13, 12, 30, 28, 13, 5, 4, 25, 27, 25, 26, 27, 26, 28, 25, 24, 26, 28, 14, 13, 28, 26, 14, 4, 3, 25, 24, 25, 23, 25, 3, 23, 26, 15, 14, 26, 24, 15, 24, 22, 15, 23, 21, 24, 23, 3, 2, 15, 22, 16, 24, 21, 22, 23, 2, 21, 16, 19, 17, 16, 20, 19, 16, 22, 20, 22, 1, 20, 22, 21, 1, 18, 17, 0, 21, 2, 1, 17, 19, 0, 19, 20, 0, 20, 1, 0], "vertices": [1, 18, -8.41, -3.75, 1, 1, 18, -2.92, -7.94, 1, 2, 18, 11.21, -9.91, 0.91869, 19, -7.55, -6.85, 0.08131, 3, 18, 24.62, -4.18, 0.01664, 19, 6.76, -9.64, 0.93187, 20, -12.47, -5.32, 0.05149, 2, 20, 8.34, -9.58, 0.92121, 21, -15.2, -5.57, 0.07879, 1, 21, 5.54, -12.3, 1, 3, 21, 28.65, -11.4, 0.28923, 22, 0.08, -12.08, 0.71057, 23, -18.95, -12.56, 0.0002, 2, 22, 21.29, -13.97, 0.39249, 23, 2.3, -13.92, 0.60751, 2, 22, 34.99, -9.65, 0.00233, 23, 15.89, -9.25, 0.99767, 1, 23, 21.7, 1.84, 1, 3, 20, 66.39, 33.64, 0.00733, 22, 32.8, 11.48, 0.0782, 23, 13.17, 11.81, 0.91447, 5, 19, 47.52, 54.86, 0.00163, 20, 48.73, 40.25, 0.08461, 21, 36.49, 32.41, 0.03905, 22, 21.77, 26.77, 0.49365, 23, 1.77, 26.83, 0.38105, 5, 19, 33.21, 42.81, 0.02885, 20, 31.04, 34.14, 0.30073, 21, 17.84, 30.98, 0.17851, 22, 3.68, 31.51, 0.41553, 23, -16.44, 31.11, 0.07638, 5, 19, 21.89, 30.06, 0.151, 20, 15.9, 26.3, 0.61658, 21, 1.2, 27.23, 0.10744, 22, -13.27, 33.39, 0.12185, 23, -33.43, 32.56, 0.00313, 5, 18, 13.35, 24.5, 5e-05, 19, 13.54, 20.42, 0.42762, 20, 4.65, 20.31, 0.54296, 21, -11.2, 24.28, 0.00636, 22, -25.96, 34.64, 0.02301, 4, 18, 11.79, 10.68, 0.21174, 19, 4.49, 9.86, 0.7352, 20, -7.59, 13.7, 0.05255, 22, -39.81, 35.94, 0.00051, 2, 18, 6.17, 6.29, 0.86813, 19, -2.63, 9.39, 0.13187, 2, 18, -1.09, 6.01, 0.99975, 19, -8.79, 13.22, 0.00025, 1, 18, -7.46, 5.76, 1, 1, 18, -1.27, 1.8, 1, 1, 18, -2.05, -3.13, 1, 2, 18, 9.36, -4.48, 0.98553, 19, -6.04, -1.32, 0.01447, 2, 18, 7, 1.47, 0.98201, 19, -4.65, 4.93, 0.01799, 2, 19, 6.87, -3, 0.98539, 20, -9.99, 0.84, 0.01461, 3, 18, 16.15, 5.3, 0.07247, 19, 5.07, 2.96, 0.92691, 20, -9.52, 7.05, 0.00062, 2, 20, 8.39, -1.83, 0.98952, 21, -13.18, 1.92, 0.01048, 4, 19, 19.19, 13.84, 0.34933, 20, 7.56, 12.13, 0.63527, 21, -10.45, 15.64, 0.00178, 22, -28.07, 26.23, 0.01361, 1, 21, 5.87, -1.51, 1, 5, 19, 31, 21.25, 0.08388, 20, 21.24, 14.81, 0.64094, 21, 3.46, 14.76, 0.19359, 22, -15.21, 20.87, 0.08084, 23, -35.05, 20, 0.00074, 2, 21, 25.13, -0.6, 0.32295, 22, 0.27, -0.72, 0.67705, 5, 19, 46.1, 31.49, 0.00919, 20, 39.01, 18.95, 0.14625, 21, 21.7, 14.27, 0.31187, 22, 1.87, 14.45, 0.49787, 23, -17.82, 14.01, 0.03482, 2, 22, 17.94, -3.04, 0.68381, 23, -1.32, -3.07, 0.31619, 5, 19, 56.98, 46.5, 0.00025, 20, 54.56, 29.05, 0.03792, 21, 39.29, 20.1, 0.01276, 22, 20.41, 14.23, 0.44093, 23, 0.72, 14.25, 0.50813], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 0, 36, 32, 34, 34, 36, 26, 28, 28, 30]}}, "CAPTAIN MARVEL WILD/HAIR BACK 2": {"CAPTAIN MARVEL WILD/HAIR BACK 2": {"type": "mesh", "hull": 20, "width": 89, "height": 72, "uvs": [0.22532, 0.01148, 0.10154, 0.08231, 0.00069, 0.21264, 0, 0.43648, 0.02361, 0.66881, 0.113, 0.84731, 0.28033, 0.95497, 0.57143, 1, 0.6977, 0.99022, 0.78689, 0.98331, 0.83754, 0.90477, 0.92212, 0.77364, 0.91754, 0.64331, 0.84618, 0.62185, 0.74792, 0.59231, 0.67286, 0.5178, 0.5508, 0.41381, 0.46141, 0.29198, 0.36743, 0.13898, 0.33305, 0.03414, 0.12217, 0.30331, 0.24365, 0.20698, 0.1726, 0.53281, 0.32388, 0.37981, 0.23678, 0.71981, 0.40639, 0.53564, 0.37889, 0.83598, 0.55538, 0.63764, 0.60352, 0.87564, 0.69062, 0.72264, 0.72271, 0.89548, 0.80293, 0.74248], "triangles": [27, 15, 14, 29, 27, 14, 31, 14, 13, 29, 14, 31, 28, 27, 29, 30, 29, 31, 28, 29, 30, 31, 13, 12, 31, 12, 11, 10, 31, 11, 30, 31, 10, 9, 30, 10, 8, 28, 30, 8, 30, 9, 7, 26, 28, 7, 28, 8, 4, 22, 24, 26, 24, 25, 26, 25, 27, 5, 4, 24, 26, 27, 28, 6, 24, 26, 5, 24, 6, 6, 26, 7, 16, 25, 23, 16, 23, 17, 22, 23, 25, 27, 16, 15, 25, 16, 27, 24, 22, 25, 21, 0, 19, 21, 19, 18, 1, 0, 21, 20, 2, 1, 21, 20, 1, 17, 23, 21, 17, 21, 18, 20, 21, 23, 3, 2, 20, 22, 20, 23, 3, 20, 22, 4, 3, 22], "vertices": [1, 14, -10.83, 8.73, 1, 1, 14, -10.91, -3.41, 1, 3, 14, -6.24, -15.53, 0.99644, 15, -26.98, -9.85, 0.0004, 16, -46.37, 0.82, 0.00315, 3, 14, 8.32, -22.45, 0.80765, 15, -14.3, -19.79, 0.1039, 16, -36.26, -11.72, 0.08845, 3, 14, 24.35, -27.67, 0.32958, 15, 0.2, -28.4, 0.28236, 16, -24.08, -23.38, 0.38806, 3, 14, 39.36, -25.94, 0.08142, 15, 15.23, -30.01, 0.197, 16, -9.8, -28.34, 0.72158, 4, 14, 52.72, -15.77, 0.00206, 15, 30.49, -23.02, 0.02606, 16, 6.65, -24.97, 0.96915, 17, -21.58, -18.09, 0.00273, 2, 16, 28.8, -11.14, 0.22137, 17, 4.18, -13.79, 0.77863, 2, 16, 37.08, -3.51, 0.00087, 17, 14.75, -9.9, 0.99913, 1, 17, 22.21, -7.16, 1, 1, 17, 24.91, -0.45, 1, 2, 15, 55.25, 30.08, 0.0004, 17, 29.43, 10.75, 0.9996, 3, 15, 47.59, 35.52, 0.00666, 16, 36.51, 28.21, 0.00113, 17, 26.36, 19.62, 0.99221, 3, 15, 42.47, 31.46, 0.02345, 16, 30.61, 25.41, 0.01499, 17, 19.83, 19.29, 0.96157, 3, 15, 35.43, 25.86, 0.12669, 16, 22.48, 21.54, 0.11195, 17, 10.84, 18.83, 0.76136, 4, 14, 39.11, 29.24, 0.00102, 15, 27.09, 23.88, 0.36415, 16, 13.91, 21.49, 0.2239, 17, 2.91, 22.06, 0.41093, 4, 14, 27.71, 22.6, 0.07214, 15, 14.51, 19.9, 0.78992, 16, 0.76, 20.45, 0.07153, 17, -9.65, 26.13, 0.06641, 4, 14, 16.39, 19.14, 0.41964, 15, 2.7, 19.01, 0.57815, 16, -10.94, 22.25, 1e-05, 17, -19.78, 32.26, 0.0022, 2, 14, 2.86, 16.26, 0.92127, 15, -11.13, 19.17, 0.07873, 2, 14, -5.27, 16.71, 0.99264, 15, -18.97, 21.39, 0.00736, 3, 14, 4.27, -8.53, 0.9847, 15, -15.19, -5.32, 0.00566, 16, -33.86, 2.57, 0.00965, 2, 14, 2.6, 4.21, 0.99219, 15, -14.03, 7.47, 0.00781, 3, 14, 21.13, -11.5, 0.40451, 15, 0.61, -11.93, 0.41223, 16, -19.96, -7.42, 0.18327, 2, 14, 16.9, 5.37, 0.53665, 15, 0.18, 5.47, 0.46335, 3, 14, 35.75, -12.07, 0.04661, 15, 14.74, -15.68, 0.24883, 16, -7.04, -14.27, 0.70456, 3, 14, 30.18, 7.24, 0.01367, 15, 13.54, 4.37, 0.98359, 17, -19.45, 14.05, 0.00274, 3, 15, 29.11, -10.83, 0.00172, 16, 8.05, -12.78, 0.98751, 17, -15.62, -7.37, 0.01077, 4, 14, 42.47, 16.11, 0.0002, 15, 27.48, 10.33, 0.27315, 16, 11.24, 8.2, 0.56397, 17, -4.65, 10.8, 0.16268, 2, 16, 25.37, -2.39, 0.04327, 17, 4.35, -4.39, 0.95673, 3, 15, 39.7, 16.08, 0.05592, 16, 24.44, 11.04, 0.09347, 17, 8.63, 8.38, 0.8506, 1, 17, 14.93, -2.73, 1, 3, 15, 46.97, 23.09, 0.01505, 16, 33.1, 16.24, 0.00996, 17, 18.62, 9.87, 0.97499], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 22, 24, 32, 34, 34, 36, 36, 38, 0, 38, 28, 30, 30, 32, 14, 16, 16, 18, 24, 26, 26, 28, 18, 20, 20, 22]}}, "CAPTAIN MARVEL WILD/WILD": {"CAPTAIN MARVEL WILD/WILD": {"type": "mesh", "hull": 4, "width": 122, "height": 61, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, 58.59, -32.53, 1, 1, 2, -63.41, -32.53, 1, 1, 2, -63.41, 28.47, 1, 1, 2, 58.59, 28.47, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}, "animations": {"RUN": {"slots": {"CAPTAIN MARVEL WILD/EFFECT": {"attachment": [{"name": "CAPTAIN MARVEL WILD/1", "time": 0}, {"time": 0.0333, "name": "CAPTAIN MARVEL WILD/2"}, {"time": 0.0667, "name": "CAPTAIN MARVEL WILD/3"}, {"time": 0.1, "name": "CAPTAIN MARVEL WILD/4"}, {"time": 0.1333, "name": "CAPTAIN MARVEL WILD/5"}, {"time": 0.1667, "name": "CAPTAIN MARVEL WILD/6"}, {"time": 0.2, "name": "CAPTAIN MARVEL WILD/7"}, {"time": 0.2333, "name": "CAPTAIN MARVEL WILD/8"}, {"time": 0.2667, "name": "CAPTAIN MARVEL WILD/9"}, {"time": 0.3, "name": "CAPTAIN MARVEL WILD/10"}, {"time": 0.3333, "name": "CAPTAIN MARVEL WILD/11"}, {"time": 0.3667, "name": "CAPTAIN MARVEL WILD/12"}, {"time": 0.4, "name": "CAPTAIN MARVEL WILD/13"}, {"time": 0.4333, "name": "CAPTAIN MARVEL WILD/14"}, {"time": 0.4667, "name": "CAPTAIN MARVEL WILD/15"}, {"time": 0.5, "name": "CAPTAIN MARVEL WILD/16"}, {"time": 0.5333, "name": "CAPTAIN MARVEL WILD/17"}, {"time": 0.5667, "name": "CAPTAIN MARVEL WILD/18"}, {"time": 0.6, "name": "CAPTAIN MARVEL WILD/19"}, {"time": 0.6333, "name": "CAPTAIN MARVEL WILD/20"}, {"time": 0.6667, "name": "CAPTAIN MARVEL WILD/1"}, {"time": 0.7333, "name": "CAPTAIN MARVEL WILD/2"}, {"time": 0.7667, "name": "CAPTAIN MARVEL WILD/3"}, {"time": 0.8, "name": "CAPTAIN MARVEL WILD/4"}, {"time": 0.8333, "name": "CAPTAIN MARVEL WILD/5"}, {"time": 0.8667, "name": "CAPTAIN MARVEL WILD/6"}, {"time": 0.9, "name": "CAPTAIN MARVEL WILD/7"}, {"time": 0.9333, "name": "CAPTAIN MARVEL WILD/8"}, {"time": 0.9667, "name": "CAPTAIN MARVEL WILD/9"}, {"time": 1, "name": "CAPTAIN MARVEL WILD/10"}, {"time": 1.0333, "name": "CAPTAIN MARVEL WILD/11"}, {"time": 1.0667, "name": "CAPTAIN MARVEL WILD/12"}, {"time": 1.1, "name": "CAPTAIN MARVEL WILD/13"}, {"time": 1.1333, "name": "CAPTAIN MARVEL WILD/14"}, {"time": 1.1667, "name": "CAPTAIN MARVEL WILD/15"}, {"time": 1.2, "name": "CAPTAIN MARVEL WILD/16"}, {"time": 1.2333, "name": "CAPTAIN MARVEL WILD/17"}, {"time": 1.2667, "name": "CAPTAIN MARVEL WILD/18"}, {"time": 1.3, "name": "CAPTAIN MARVEL WILD/19"}, {"time": 1.3333, "name": "CAPTAIN MARVEL WILD/20"}, {"time": 1.3667, "name": "CAPTAIN MARVEL WILD/2"}, {"time": 1.4, "name": "CAPTAIN MARVEL WILD/3"}, {"time": 1.4333, "name": "CAPTAIN MARVEL WILD/4"}, {"time": 1.4667, "name": "CAPTAIN MARVEL WILD/5"}, {"time": 1.5, "name": "CAPTAIN MARVEL WILD/6"}, {"time": 1.5333, "name": "CAPTAIN MARVEL WILD/7"}, {"time": 1.5667, "name": "CAPTAIN MARVEL WILD/8"}, {"time": 1.6, "name": "CAPTAIN MARVEL WILD/9"}, {"time": 1.6333, "name": "CAPTAIN MARVEL WILD/10"}, {"time": 1.6667, "name": "CAPTAIN MARVEL WILD/11"}, {"time": 1.7, "name": "CAPTAIN MARVEL WILD/12"}, {"time": 1.7333, "name": "CAPTAIN MARVEL WILD/13"}, {"time": 1.7667, "name": "CAPTAIN MARVEL WILD/14"}, {"time": 1.8, "name": "CAPTAIN MARVEL WILD/15"}, {"time": 1.8333, "name": "CAPTAIN MARVEL WILD/16"}, {"time": 1.8667, "name": "CAPTAIN MARVEL WILD/17"}, {"time": 1.9, "name": "CAPTAIN MARVEL WILD/18"}, {"time": 1.9333, "name": "CAPTAIN MARVEL WILD/19"}, {"time": 1.9667, "name": "CAPTAIN MARVEL WILD/20"}, {"time": 2, "name": "CAPTAIN MARVEL WILD/1"}, {"time": 2.0667, "name": "CAPTAIN MARVEL WILD/2"}, {"time": 2.1, "name": "CAPTAIN MARVEL WILD/3"}, {"time": 2.1333, "name": "CAPTAIN MARVEL WILD/4"}, {"time": 2.1667, "name": "CAPTAIN MARVEL WILD/5"}, {"time": 2.2, "name": "CAPTAIN MARVEL WILD/6"}, {"time": 2.2333, "name": "CAPTAIN MARVEL WILD/7"}, {"time": 2.2667, "name": "CAPTAIN MARVEL WILD/8"}, {"time": 2.3, "name": "CAPTAIN MARVEL WILD/9"}, {"time": 2.3333, "name": "CAPTAIN MARVEL WILD/10"}, {"time": 2.3667, "name": "CAPTAIN MARVEL WILD/11"}, {"time": 2.4, "name": "CAPTAIN MARVEL WILD/12"}, {"time": 2.4333, "name": "CAPTAIN MARVEL WILD/13"}, {"time": 2.4667, "name": "CAPTAIN MARVEL WILD/14"}, {"time": 2.5, "name": "CAPTAIN MARVEL WILD/15"}, {"time": 2.5333, "name": "CAPTAIN MARVEL WILD/16"}, {"time": 2.5667, "name": "CAPTAIN MARVEL WILD/17"}, {"time": 2.6, "name": "CAPTAIN MARVEL WILD/18"}, {"time": 2.6333, "name": "CAPTAIN MARVEL WILD/19"}, {"time": 2.6667, "name": "CAPTAIN MARVEL WILD/20"}]}}, "bones": {"bone6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -19.08}, {"time": 0.3333, "angle": -37.18}, {"time": 0.5, "angle": -43.33}, {"time": 0.6667, "angle": 18.83}, {"time": 0.8333, "angle": 34.48}, {"time": 1, "angle": -11.79}, {"time": 1.1667, "angle": 19.2}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -19.08}, {"time": 1.6667, "angle": -37.18}, {"time": 1.8333, "angle": -43.33}, {"time": 2, "angle": 18.83}, {"time": 2.1667, "angle": 34.48}, {"time": 2.3333, "angle": -11.79}, {"time": 2.5, "angle": 19.2}, {"time": 2.6667, "angle": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -60.29}, {"time": 0.3333, "angle": 5.03}, {"time": 0.5, "angle": -121.73}, {"time": 0.6667, "angle": 37.73}, {"time": 0.8333, "angle": -5.45}, {"time": 1, "angle": -82.66}, {"time": 1.1667, "angle": 8.75}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -60.29}, {"time": 1.6667, "angle": 5.03}, {"time": 1.8333, "angle": -121.73}, {"time": 2, "angle": 37.73}, {"time": 2.1667, "angle": -5.45}, {"time": 2.3333, "angle": -82.66}, {"time": 2.5, "angle": 8.75}, {"time": 2.6667, "angle": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -1.4}, {"time": 0.3333, "angle": -8.76}, {"time": 0.5, "angle": -3.88}, {"time": 0.6667, "angle": -4.42}, {"time": 0.8333, "angle": 0.29}, {"time": 1, "angle": 1.06}, {"time": 1.1667, "angle": -2.97}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -1.4}, {"time": 1.6667, "angle": -8.76}, {"time": 1.8333, "angle": -3.88}, {"time": 2, "angle": -4.42}, {"time": 2.1667, "angle": 0.29}, {"time": 2.3333, "angle": 1.06}, {"time": 2.5, "angle": -2.97}, {"time": 2.6667, "angle": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -1.81}, {"time": 0.3333, "angle": -14.02}, {"time": 0.5, "angle": -3.81}, {"time": 0.6667, "angle": -0.74}, {"time": 0.8333, "angle": 10.69}, {"time": 1, "angle": -1.6}, {"time": 1.1667, "angle": 6.25}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -1.81}, {"time": 1.6667, "angle": -14.02}, {"time": 1.8333, "angle": -3.81}, {"time": 2, "angle": -0.74}, {"time": 2.1667, "angle": 10.69}, {"time": 2.3333, "angle": -1.6}, {"time": 2.5, "angle": 6.25}, {"time": 2.6667, "angle": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -13.31}, {"time": 0.3333, "angle": -8.85}, {"time": 0.5, "angle": -5.01}, {"time": 0.6667, "angle": -0.07}, {"time": 0.8333, "angle": 4.12}, {"time": 1, "angle": 6.19}, {"time": 1.1667, "angle": 5.65}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -13.31}, {"time": 1.6667, "angle": -8.85}, {"time": 1.8333, "angle": -5.01}, {"time": 2, "angle": -0.07}, {"time": 2.1667, "angle": 4.12}, {"time": 2.3333, "angle": 6.19}, {"time": 2.5, "angle": 5.65}, {"time": 2.6667, "angle": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -11.37}, {"time": 0.3333, "angle": -26.79}, {"time": 0.5, "angle": -16.39}, {"time": 0.6667, "angle": -3.37}, {"time": 0.8333, "angle": 6.49}, {"time": 1, "angle": 11.61}, {"time": 1.1667, "angle": 0.15}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -11.37}, {"time": 1.6667, "angle": -26.79}, {"time": 1.8333, "angle": -16.39}, {"time": 2, "angle": -3.37}, {"time": 2.1667, "angle": 6.49}, {"time": 2.3333, "angle": 11.61}, {"time": 2.5, "angle": 0.15}, {"time": 2.6667, "angle": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -12.33}, {"time": 0.3333, "angle": -40.59}, {"time": 0.5, "angle": -25.84}, {"time": 0.6667, "angle": 1.06}, {"time": 0.8333, "angle": 18.33}, {"time": 1, "angle": 25.91}, {"time": 1.1667, "angle": -13.41}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -12.33}, {"time": 1.6667, "angle": -40.59}, {"time": 1.8333, "angle": -25.84}, {"time": 2, "angle": 1.06}, {"time": 2.1667, "angle": 18.33}, {"time": 2.3333, "angle": 25.91}, {"time": 2.5, "angle": -13.41}, {"time": 2.6667, "angle": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 2.45}, {"time": 0.3333, "angle": 4.2}, {"time": 0.5, "angle": -44.54}, {"time": 0.6667, "angle": 20.6}, {"time": 0.8333, "angle": 44.23}, {"time": 1, "angle": 81.61}, {"time": 1.1667, "angle": 0.51}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": 2.45}, {"time": 1.6667, "angle": 4.2}, {"time": 1.8333, "angle": -44.54}, {"time": 2, "angle": 20.6}, {"time": 2.1667, "angle": 44.23}, {"time": 2.3333, "angle": 81.61}, {"time": 2.5, "angle": 0.51}, {"time": 2.6667, "angle": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 1.4}, {"time": 0.3333, "angle": 1.77}, {"time": 0.5, "angle": 3.7}, {"time": 0.6667, "angle": 5.98}, {"time": 0.8333, "angle": 7.32}, {"time": 1, "angle": 10.46}, {"time": 1.1667, "angle": 14.01}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": 1.4}, {"time": 1.6667, "angle": 1.77}, {"time": 1.8333, "angle": 3.7}, {"time": 2, "angle": 5.98}, {"time": 2.1667, "angle": 7.32}, {"time": 2.3333, "angle": 10.46}, {"time": 2.5, "angle": 14.01}, {"time": 2.6667, "angle": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -31.98}, {"time": 0.3333, "angle": 4.25}, {"time": 0.5, "angle": 32.66}, {"time": 0.6667, "angle": 36.14}, {"time": 0.8333, "angle": 21.58}, {"time": 1, "angle": -50.3}, {"time": 1.1667, "angle": 36.37}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -31.98}, {"time": 1.6667, "angle": 4.25}, {"time": 1.8333, "angle": 32.66}, {"time": 2, "angle": 36.14}, {"time": 2.1667, "angle": 21.58}, {"time": 2.3333, "angle": -50.3}, {"time": 2.5, "angle": 36.37}, {"time": 2.6667, "angle": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -40.63}, {"time": 0.3333, "angle": -50.49}, {"time": 0.5, "angle": 2.14}, {"time": 0.6667, "angle": 10.59}, {"time": 0.8333, "angle": 21.48}, {"time": 1, "angle": 3.54}, {"time": 1.1667, "angle": -62.66}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -40.63}, {"time": 1.6667, "angle": -50.49}, {"time": 1.8333, "angle": 2.14}, {"time": 2, "angle": 10.59}, {"time": 2.1667, "angle": 21.48}, {"time": 2.3333, "angle": 3.54}, {"time": 2.5, "angle": -62.66}, {"time": 2.6667, "angle": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -6.88}, {"time": 0.3333, "angle": -18.67}, {"time": 0.5, "angle": -14.9}, {"time": 0.6667, "angle": -5.53}, {"time": 0.8333, "angle": 0.97}, {"time": 1, "angle": 7.78}, {"time": 1.1667, "angle": -31.81}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -6.88}, {"time": 1.6667, "angle": -18.67}, {"time": 1.8333, "angle": -14.9}, {"time": 2, "angle": -5.53}, {"time": 2.1667, "angle": 0.97}, {"time": 2.3333, "angle": 7.78}, {"time": 2.5, "angle": -31.81}, {"time": 2.6667, "angle": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -0.13}, {"time": 0.3333, "angle": -2.76}, {"time": 0.5, "angle": -3.06}, {"time": 0.6667, "angle": -0.02}, {"time": 0.8333, "angle": 2.44}, {"time": 1, "angle": 6.76}, {"time": 1.1667, "angle": -0.66}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -0.13}, {"time": 1.6667, "angle": -2.76}, {"time": 1.8333, "angle": -3.06}, {"time": 2, "angle": -0.02}, {"time": 2.1667, "angle": 2.44}, {"time": 2.3333, "angle": 6.76}, {"time": 2.5, "angle": -0.66}, {"time": 2.6667, "angle": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": 4.75}, {"time": 0.3333, "angle": 4.42}, {"time": 0.5, "angle": 3.14}, {"time": 0.6667, "angle": 4.91}, {"time": 0.8333, "angle": 6.66}, {"time": 1, "angle": 10.54}, {"time": 1.1667, "angle": 5.18}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": 4.75}, {"time": 1.6667, "angle": 4.42}, {"time": 1.8333, "angle": 3.14}, {"time": 2, "angle": 4.91}, {"time": 2.1667, "angle": 6.66}, {"time": 2.3333, "angle": 10.54}, {"time": 2.5, "angle": 5.18}, {"time": 2.6667, "angle": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -23.96}, {"time": 0.3333, "angle": -69.82}, {"time": 0.5, "angle": 12.41}, {"time": 0.6667, "angle": 16.63}, {"time": 0.8333, "angle": 24.38}, {"time": 1, "angle": 4.09}, {"time": 1.1667, "angle": 24.93}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -23.96}, {"time": 1.6667, "angle": -69.82}, {"time": 1.8333, "angle": 12.41}, {"time": 2, "angle": 16.63}, {"time": 2.1667, "angle": 24.38}, {"time": 2.3333, "angle": 4.09}, {"time": 2.5, "angle": 24.93}, {"time": 2.6667, "angle": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -11.64}, {"time": 0.3333, "angle": -4.48}, {"time": 0.5, "angle": -3.69}, {"time": 0.6667, "angle": 6.4}, {"time": 0.8333, "angle": 14.76}, {"time": 1, "angle": 25.15}, {"time": 1.1667, "angle": -10.23}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -11.64}, {"time": 1.6667, "angle": -4.48}, {"time": 1.8333, "angle": -3.69}, {"time": 2, "angle": 6.4}, {"time": 2.1667, "angle": 14.76}, {"time": 2.3333, "angle": 25.15}, {"time": 2.5, "angle": -10.23}, {"time": 2.6667, "angle": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1667, "angle": -2.7}, {"time": 0.3333, "angle": -1.82}, {"time": 0.5, "angle": -2.58}, {"time": 0.6667, "angle": 0.9}, {"time": 0.8333, "angle": 4.09}, {"time": 1, "angle": 10.35}, {"time": 1.1667, "angle": -0.1}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -2.7}, {"time": 1.6667, "angle": -1.82}, {"time": 1.8333, "angle": -2.58}, {"time": 2, "angle": 0.9}, {"time": 2.1667, "angle": 4.09}, {"time": 2.3333, "angle": 10.35}, {"time": 2.5, "angle": -0.1}, {"time": 2.6667, "angle": 0}]}, "bone": {"translate": [{"time": 0}, {"time": 0.3333, "y": 4.84}, {"time": 0.6667}, {"time": 1, "y": -7.5}, {"time": 1.3333}, {"time": 1.6667, "y": 4.84}, {"time": 2}, {"time": 2.3333, "y": -7.5}, {"time": 2.6667}]}, "WILD": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3333, "x": 1.2, "y": 1.2}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1, "x": 1.2, "y": 1.2}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.2, "y": 1.2}, {"time": 2, "x": 1, "y": 1}, {"time": 2.3333, "x": 1.2, "y": 1.2}, {"time": 2.6667, "x": 1, "y": 1}]}}, "deform": {"default": {"CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD": {"CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD": [{"time": 0}, {"time": 0.6667, "offset": 62, "vertices": [1.20649, 3.67514, 0.83097, 2.53128, 0.78481, 2.39066, 1.09572, 3.33775, 1.02183, 3.11263, 0.97566, 2.97201, 0.78843, 2.40166, 0.44843, 1.36599, 0.53034, 1.61551, -0.27699, -0.84375, -0.114, -0.34727, -0.114, -0.34727, 0.12076, 0.36786, 0.94375, 2.87481, 1.53143, 4.66496, 1.19129, 3.62886, 0.92106, 2.80569, 1.19129, 3.62886, 0.54723, 1.66696, 1.10122, 3.35448, 1.10122, 3.35448, 0.91655, 2.79197, 0.983, 2.99434, 0.16102, 0.49048, 0.50782, 1.54689, 0.64632, 1.96879, 0.64632, 1.96879, 0.64632, 1.96879, 0.64632, 1.96879, 0.64632, 1.96879, 0.41549, 1.26563, 0.41549, 1.26563, 0.41549, 1.26563, 0.8884, 2.7062, 0.36932, 1.12501, 0.27699, 0.84377, 0.24069, 0.73318, 0, 0, 0.12076, 0.36786, 0.69461, 1.64135, 0.69248, 2.10941, 0.27699, 0.84376, 0.87162, 2.65511, 0.87162, 2.65511, 0.87162, 2.65511, 0.87162, 2.65511, 0.87162, 2.65511, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.15961, -0.48619, 0.9972, 3.03764, 0.9972, 3.03764, 1.4566, 4.43704, 0.83886, 2.5553, 0.83886, 2.5553, 0.45715, 1.39255, 0.49656, 1.51259, 0.22632, 0.68942, 0.4569, 0.44274, 0.18229, 0.08075, 0.27462, 0.36201, 0.4486, 2.76698, 0.79057, 3.38044, -0.36932, -1.125, -0.36932, -1.125, -0.36932, -1.125, -0.36932, -1.125, -0.36932, -1.125]}, {"time": 1.3333}, {"time": 2, "offset": 62, "vertices": [1.20649, 3.67514, 0.83097, 2.53128, 0.78481, 2.39066, 1.09572, 3.33775, 1.02183, 3.11263, 0.97566, 2.97201, 0.78843, 2.40166, 0.44843, 1.36599, 0.53034, 1.61551, -0.27699, -0.84375, -0.114, -0.34727, -0.114, -0.34727, 0.12076, 0.36786, 0.94375, 2.87481, 1.53143, 4.66496, 1.19129, 3.62886, 0.92106, 2.80569, 1.19129, 3.62886, 0.54723, 1.66696, 1.10122, 3.35448, 1.10122, 3.35448, 0.91655, 2.79197, 0.983, 2.99434, 0.16102, 0.49048, 0.50782, 1.54689, 0.64632, 1.96879, 0.64632, 1.96879, 0.64632, 1.96879, 0.64632, 1.96879, 0.64632, 1.96879, 0.41549, 1.26563, 0.41549, 1.26563, 0.41549, 1.26563, 0.8884, 2.7062, 0.36932, 1.12501, 0.27699, 0.84377, 0.24069, 0.73318, 0, 0, 0.12076, 0.36786, 0.69461, 1.64135, 0.69248, 2.10941, 0.27699, 0.84376, 0.87162, 2.65511, 0.87162, 2.65511, 0.87162, 2.65511, 0.87162, 2.65511, 0.87162, 2.65511, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.27699, -0.84375, -0.15961, -0.48619, 0.9972, 3.03764, 0.9972, 3.03764, 1.4566, 4.43704, 0.83886, 2.5553, 0.83886, 2.5553, 0.45715, 1.39255, 0.49656, 1.51259, 0.22632, 0.68942, 0.4569, 0.44274, 0.18229, 0.08075, 0.27462, 0.36201, 0.4486, 2.76698, 0.79057, 3.38044, -0.36932, -1.125, -0.36932, -1.125, -0.36932, -1.125, -0.36932, -1.125, -0.36932, -1.125]}, {"time": 2.6667}]}}}}, "START": {"slots": {"CAPTAIN MARVEL WILD/EFFECT": {"attachment": [{"name": "CAPTAIN MARVEL WILD/1", "time": 0}, {"time": 0.1, "name": "CAPTAIN MARVEL WILD/1"}]}}, "bones": {"bone18": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone6": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone17": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone10": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone11": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone8": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone": {"translate": [{"curve": "stepped", "time": 0}, {"time": 0.1}]}, "bone5": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone7": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone9": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone15": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone12": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone13": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone14": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone4": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone19": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "bone20": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}, "WILD": {"scale": [{"curve": "stepped", "time": 0, "x": 1, "y": 1}, {"time": 0.1, "x": 1, "y": 1}]}, "bone21": {"rotate": [{"curve": "stepped", "time": 0, "angle": 0}, {"time": 0.1, "angle": 0}]}}, "deform": {"default": {"CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD": {"CAPTAIN MARVEL WILD/CAPTAIN MARVEL HEAD": [{"curve": "stepped", "time": 0}, {"time": 0.1}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]