[1, ["ecpdLyjvZBwrvm+cedCcQy", "825TQ2kU9Ktq1Ncj5HdPmn", "fdNoodJKVLj4dF1TLppv2g", "bdw+FTwl9LGomK1KepNE1T", "b1UvINmAxEg5ejloARYcCA", "feLeyuaX9PqrSCEi/w9xcv", "d9HZ/NiNRLeZvLRiMcKA3d", "62nxWktOdKoa3OfPnC8ftE", "d6L407T4lE+qcik3SiyKAa", "a9VpD0DP5LJYQPXITZq+uj", "8fXSIo/cRERbeSHVKEi8hB", "94qTDfxx1Jdo+HoT1Ml9Vp", "78zzbRbvpPFr22PGqVXcQs", "e1ZiWrTGlF67wJoCcuhXaA", "8f1yTqlfNHwK1xw166qEL4", "c5tsUtteZEgIalmou8sMJj", "b1US3H4gNIGLJoo9wL0Rix", "bepPBsdKNI47j8v7TMSq7+", "eabX5aHu5LHYOneX/wzcap", "4b4clu0YdGvKu9WhXWkE9e", "2cWB/vWPRHja3uQTinHH30", "5baAf8iEBD+KVS7vHerhSd", "88bnJvLEdIaZ9AIpT3fOL7"], ["node", "_spriteFrame", "_textureSetter", "_N$target", "_N$normalSprite", "root", "data", "_parent", "_defaultClip"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_eulerAngles"], 1, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 6, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["9f9e1IJ4WFMSqhdfSr9lY+W", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["<PERSON>.<PERSON>", ["node", "_N$content"], 3, 1, 1], ["34c4fFXj/hAgrtv/y80/0lh", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3]], [[4, 0, 1, 2], [1, 0, 5, 3, 2, 4, 6, 2], [2, 2, 3, 4, 1], [1, 0, 5, 7, 2, 4, 6, 2], [2, 0, 1, 2, 3, 4, 3], [1, 0, 5, 7, 3, 2, 4, 6, 2], [3, 1, 0, 2, 3, 4, 5, 6, 7, 3], [3, 0, 2, 3, 4, 5, 6, 7, 2], [5, 0, 1, 3, 3], [5, 0, 1, 2, 3, 4], [6, 0, 2], [1, 0, 7, 3, 2, 2], [1, 0, 7, 3, 2, 4, 8, 6, 2], [1, 0, 5, 3, 2, 4, 6, 9, 2], [1, 0, 1, 5, 3, 2, 4, 6, 3], [1, 0, 5, 7, 3, 2, 4, 2], [7, 0, 1, 2, 1], [8, 0, 1], [4, 1, 1], [2, 0, 2, 3, 4, 2], [9, 0, 1, 2, 3, 4, 4], [10, 0, 1, 1], [11, 0, 1], [3, 2, 8, 1], [12, 0, 1, 2, 2]], [[[{"name": "avenger_bangthuong_btn_left", "rect": [0, 0, 119, 121], "offset": [0, 0], "originalSize": [119, 121], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [3]], [[{"name": "title_bang<PERSON><PERSON>ong", "rect": [0, 0, 272, 49], "offset": [0, 0], "originalSize": [272, 49], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [4]], [[{"name": "avenger_bangthuong_4", "rect": [0, 0, 1032, 507], "offset": [0, 0], "originalSize": [1032, 507], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [5]], [[{"name": "avenger_bangthuong_2", "rect": [0, 0, 1032, 534], "offset": [0, 0], "originalSize": [1032, 534], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [6]], [[{"name": "avenger_btn_sieutoc", "rect": [0, 0, 101, 52], "offset": [0, 0], "originalSize": [101, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [7]], [[{"name": "avenger_bangthuong_5", "rect": [0, 0, 874, 534], "offset": [0, 0], "originalSize": [874, 534], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [8]], [[[10, "cb<PERSON>elp<PERSON>iew"], [11, "cb<PERSON>elp<PERSON>iew", [-4, -5], [[16, -2, [28, 29], 27], [17, -3]], [18, -1]], [5, "nen popup copy", 1, [-7, -8, -9, -10, -11, -12], [[2, -6, [25], 26]], [0, "23+C1lte5C1Z0ckiYe9xLR", 1], [5, 1091, 576], [-9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "content", [-14, -15, -16, -17], [[20, 1, 1, 15, -13, [5, 3552, 450]]], [0, "17CDBHb99O9p75a2SW3aAW", 1], [5, 3552, 450], [0, 0, 0.5], [-444, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pageview", 2, [-20], [[21, -18, 3], [22, -19]], [0, "02K20uTS1J2ZDzoWaH6De/", 1], [5, 888, 450], [-2, -47, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 2, [[6, 0.9, 3, -22, [[8, "9f9e1IJ4WFMSqhdfSr9lY+W", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -21, 4], [4, 2, false, -23, [5], 6]], [0, "a5Qo7w8IVBBrhcez8T4MEN", 1], [5, 83, 75], [489.229, 237.596, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBack", 2, [[4, 2, false, -24, [7], 8], [7, 3, -26, [[9, "34c4fFXj/hAgrtv/y80/0lh", "switchPageClicked", "-1", 4]], [4, 4294967295], [4, 4294967295], -25, 18]], [0, "be3WOoxQZIv4mXwVe19GGP", 1], [5, 119, 121], [-543.147, -5.127, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnContinue", 2, [[4, 2, false, -27, [19], 20], [6, 1.1, 3, -29, [[8, "9f9e1IJ4WFMSqhdfSr9lY+W", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -28, 21]], [0, "3brCgfngxDVoR/wAok/YvW", 1], [5, 101, 52], [-10.9, -263.821, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "btnNext", 2, [[4, 2, false, -30, [22], 23], [7, 3, -32, [[9, "34c4fFXj/hAgrtv/y80/0lh", "switchPageClicked", "1", 4]], [4, 4294967295], [4, 4294967295], -31, 24]], [0, "89fW6X4EFLkphipUQNqFPH", 1], [5, 119, 121], [537.103, 0.148, 0, 0, 0, 1, 6.123233995736766e-17, -1, 1, 1], [1, 0, 0, 180]], [14, "black", 100, 1, [[19, 0, -33, [0], 1], [23, -34, [4, 4292269782]]], [0, "ea5s3ePzNBV6CAPxpJ69fG", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 4, [3], [[24, 0, -35, [17]]], [0, "b8oNxm0ClGwaUS2BRHp7XX", 1], [5, 888, 450]], [1, "title_chonDong", 2, [[2, -36, [2], 3]], [0, "13Vi2zKZVKdIFkiEDTJVGJ", 1], [5, 272, 49], [3.8, 244, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "paytable", 3, [-37], [0, "b1JBouKB9KuIE/f+ITaRTk", 1], [5, 888, 450], [444, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "body_BThuog_1", 12, [[2, -38, [9], 10]], [0, "6eLakjayxBkpPD8qApyLO6", 1], [5, 1032, 470], [-1.117, 14.3, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [3, "paytable", 3, [-39], [0, "52L/WIiVNLiJ8XtvUjQH/w", 1], [5, 888, 450], [1332, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "body_BThuog_2", 14, [[2, -40, [11], 12]], [0, "a7gi1V21dJL4GQhOf5yviw", 1], [5, 1032, 507], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [3, "paytable", 3, [-41], [0, "f4eL/0qXRPtpsqhq/A44R5", 1], [5, 888, 450], [2220, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "body_BThuog_3", 16, [[2, -42, [13], 14]], [0, "dax34I+WdF/KrdUxyY38Zi", 1], [5, 874, 534], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [3, "paytable", 3, [-43], [0, "c3Jzxp85lOa4IgMPrtcnO/", 1], [5, 888, 450], [3108, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "body_BThuog_4", 18, [[2, -44, [15], 16]], [0, "d7QOcysMpAm53UsqmF+I+X", 1], [5, 1032, 534], [0, 22, 0, 0, 0, 0, 1, 0.8, 0.8, 1]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, -1, 9, 0, -2, 2, 0, 0, 2, 0, -1, 11, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 4, 0, 0, 3, 0, -1, 12, 0, -2, 14, 0, -3, 16, 0, -4, 18, 0, 0, 4, 0, 0, 4, 0, -1, 10, 0, 3, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, 0, 7, 0, 3, 7, 0, 0, 7, 0, 0, 8, 0, 3, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, 0, 13, 0, -1, 15, 0, 0, 15, 0, -1, 17, 0, 0, 17, 0, -1, 19, 0, 0, 19, 0, 6, 1, 3, 7, 10, 44], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 4, -1, 1, 4, -1, 1, 4, -1, 1, 8, -1, -2], [0, 9, 0, 10, 1, 0, 11, 0, 12, 0, 13, 0, 14, 0, 15, 0, 16, 0, 1, 0, 17, 1, 0, 18, 1, 0, 19, 2, 2, 20]], [[{"name": "avenger_bangthuong_3", "rect": [0, 0, 1032, 470], "offset": [0, 0], "originalSize": [1032, 470], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [21]], [[{"name": "avenger_bangthuong_btn_right", "rect": [0, 0, 119, 121], "offset": [0, 0], "originalSize": [119, 121], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [22]]]]