[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "55EtmQs7VLCJIesZC+sxNd", "a1aMC0baJB6Jh8NP6nD6ri", "a9VpD0DP5LJYQPXITZq+uj", "0c0JFaVa5LUY7HePA0Jp7A", "1cZEkZm5BPF6cdg+OTu01j", "0aglqLzDhI2JhpuVM7xFjs", "19OT1duxNGuqo9Nv4M0I4I", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "_N$content", "data", "_N$normalSprite", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_components", "_prefab", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint", "_color"], 0, 9, 4, 1, 5, 2, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 6, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["60736eqO5xOf4CAS6qwQBlA", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3]], [[7, 0, 1, 2], [0, 0, 5, 3, 4, 6, 2], [4, 0, 2], [0, 0, 7, 3, 4, 2], [0, 0, 5, 7, 3, 4, 6, 8, 2], [0, 0, 1, 5, 3, 4, 6, 8, 3], [0, 0, 5, 7, 3, 4, 6, 9, 8, 2], [0, 0, 5, 7, 3, 4, 6, 9, 2], [0, 0, 5, 3, 4, 10, 6, 9, 2], [0, 0, 2, 5, 3, 4, 6, 8, 3], [5, 0, 1, 2, 1], [6, 0, 1], [3, 0, 1, 2, 3, 4, 5, 6, 2], [3, 1, 7, 1], [8, 0, 1, 2, 3], [1, 0, 4, 5, 6, 2], [1, 4, 5, 6, 1], [1, 1, 0, 4, 5, 6, 3], [1, 2, 4, 5, 6, 2], [1, 0, 3, 4, 5, 6, 3], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 1]], [[[{"name": "text_hd_ld", "rect": [14, 11, 948, 1291], "offset": [1, 40], "originalSize": [974, 1393], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [2]], [[{"name": "Title_HD", "rect": [1, 5, 440, 113], "offset": [0.5, -2.5], "originalSize": [441, 118], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [3]], [[[2, "LoDeHelpView"], [3, "LoDeHelpView", [-4, -5, -6, -7, -8], [[10, -2, [13, 14], 12], [11, -3]], [0, "3bpGNCc7BEI7l9RIkrL54h", -1]], [4, "btnClose", 1, [-11], [[12, 3, -10, [[14, "60736eqO5xOf4CAS6qwQBlA", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -9, 11]], [0, "1csgJ/hoZJL7PzPlMhv0hT", 1], [5, 120, 39], [0, -260, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "black", 100, 1, [[15, 0, -12, [0], 1], [13, -13, [4, 4292269782]]], [0, "f1jaybGb1LIrwsh18/v50X", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "scrollview", 1, [-16], [[20, false, 0.75, 0.23, null, null, -15, -14]], [0, "2d0ZQ6/BxLDqIlBwLfzGKo", 1], [5, 1020, 530], [0, 0.5, 1], [0, 295, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "view", 4, [-18], [[21, -17, [8]]], [0, "6eJ9n1KVdOsodmh75oElH7", 1], [5, 1000, 530], [0, 0.5, 1]], [8, "text_hd_ld", 5, [[16, -19, [6], 7]], [0, "11EwkT9iJPCYcO/CFY32Tt", 1], [4, 4278190080], [5, 948, 1291], [0, 0.5, 1]], [1, "bg_content2", 1, [[17, 1, 0, -20, [2], 3]], [0, "e33+kz1XZLxYpQaRK9FU3C", 1], [5, 1168, 800]], [9, "Title_HD", false, 1, [[18, false, -21, [4], 5]], [0, "a7CB5J6LdLB44b+ynXO7Gu", 1], [5, 440, 113], [0, 295.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 2, [[19, 2, false, -22, [9], 10]], [0, "aarI5wZuZHCLkirQagUDy0", 1], [5, 108, 37]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 7, 0, -3, 8, 0, -4, 4, 0, -5, 2, 0, 4, 2, 0, 0, 2, 0, -1, 9, 0, 0, 3, 0, 0, 3, 0, 5, 6, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 6, 1, 22], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 7, 8, -1, -2], [0, 4, 0, 5, 0, 6, 0, 7, 0, 0, 8, 9, 1, 1, 10]]]]