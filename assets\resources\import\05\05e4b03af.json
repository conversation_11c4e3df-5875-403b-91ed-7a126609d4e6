[1, ["ecpdLyjvZBwrvm+cedCcQy", "6fgBCSDDdPMInvyNlggls2", "24xd2Xl+xHVZeWwPN10Wzf", "08iyQNo3JBW5bLGk07dtfI", "9fXvdCkOdOQ5g+W8hWBf41", "30SFO9f3xCKLc2nqMMpbAg", "ccyx5BnPNOdqUU1Ffe2FNF", "0bH0vFZ/tA4aBA017o6pgg", "e7hGggnrBElK+iKLz5LXN6", "05LgaxFeBAeLJ8ACj0ZZ+S", "76idLiVk9CWaSmqgVAAxwY", "13mvzB/HJFfoaL+PVlmDhi", "d68OxEfjlPQpqnGXWBUW9H", "7aNc4tYvpM+a0EdyqtDpWU", "80uDVB/o5CSJ3RSpZOJCbe", "3dWbidMjtMJbw5JxGzAh8z", "31VgVlLlFDyLAYrTgVJkEN", "2es/UAgjhOPrHDMeO6nEFC", "b9OTpbyadKlK6AYXZAKBCS", "453apgR3dB07HonQvOjD00", "f9LXxCHNxC4aeUB3e5nqWL", "61fWfnAr5BnLdxBSFjSIvb", "82WLQWbr1I3LUfsDAUn0R2", "fdNoodJKVLj4dF1TLppv2g", "b1KRr+snVI4ILgmkzw/fOX", "ecN9sMisxG17e0erBwxvP6", "0dwDWyo7xOeKhZa8CBYEnu", "a9VpD0DP5LJYQPXITZq+uj", "26XBIiR41EpJSfFK/OQnr/", "2a27zPk91Bf7iAxuxCbBAq", "78syxAT1pPY7sxIWu/LEol", "c1y3UL3AVHoqWPxPdQzt/K", "3cH7OinulEzIKmTiAdc0ul", "8fOh7kpadLK4TelBsDdO26", "a2MjXRFdtLlYQ5ouAFv/+R", "a856OrGNNLtL4gTmWx+vyx", "5aFaVAKepOVoF397CY8Xyr", "b8EW9snbRCFZGQOZaztrs7", "0fUrWdkHVBl4gRYvzgcqao", "d9csZTfLtEZIOWofc/YBA5", "e8urEIeU1K1rVEmds9rCjZ", "15Qd8NgURNPK4zsPfg2aPF", "2cWB/vWPRHja3uQTinHH30", "b439qzgolDFJ3h/DI0FWJ8", "cdbLn3EjlPNaJD6cQIOF1X", "d1Gcgc3T5KL4aM9uLgQTgf"], ["node", "_spriteFrame", "_parent", "_N$file", "checkMark", "_textureSetter", "root", "lbTotalXiu", "lbTotalTai", "btnBack", "btnNext", "pageView", "taiXiuGraphDice3View", "taiXiuGraphDiceSumView", "taiXiuGraphCatCauView", "taiXiuGraph100View", "_N$target", "_N$content", "data", "lbResult", "lbSessionID", "toggleDiceSum", "toggleDice3", "toggleDice2", "toggleDice1", "_defaultClip"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_trs", "_parent", "_children", "_anchorPoint", "_color"], 0, 4, 5, 9, 7, 1, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint", "_color"], 1, 1, 2, 4, 5, 7, 2, 5, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_lineHeight", "_materials", "node", "_N$file"], -4, 3, 1, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingY", "_enabled", "_N$spacingX", "_N$startAxis", "_N$paddingBottom", "_N$verticalDirection", "_N$horizontalDirection", "node", "_layoutSize"], -6, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["<PERSON><PERSON>", ["vertical", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "_enabled", "node", "_N$content"], -4, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["ef32bQW2e5LaKiYTRZis+WJ", ["node", "taiXiuGraph100View", "taiXiuGraphCatCauView", "taiXiuGraphDiceSumView", "taiXiuGraphDice3View", "pageView", "btnNext", "btnBack", "lbTotalTai", "lbTotalXiu"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Graphics", ["_lineWidth", "node", "_materials", "_strokeColor"], 2, 1, 3, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["928b0yp59BPCK5nFQGw3r8B", ["node", "nodeParent", "nodeTaiTemp", "nodeXiuTemp"], 3, 1, 1, 1, 1], ["f3a25Z3OctP2ahLNMz5caI7", ["node", "nodeParent", "nodeTaiTemp", "nodeXiuTemp", "sfTaiXiu"], 3, 1, 1, 1, 1, 3], ["dd7a4VPzcFMe4NZ40zKlaUz", ["node", "nodeGraphics", "nodeParent", "nodeTaiTemp", "nodeXiuTemp", "sfTaiXiu"], 3, 1, 1, 1, 1, 1, 3], ["cc.Toggle", ["_N$transition", "node", "_N$normalColor", "_N$target", "checkEvents"], 2, 1, 5, 1, 9], ["319fe3TIjZLh5aT+W80vXL2", ["node", "nodeGraphics1", "nodeGraphics2", "nodeGraphics3", "nodeDice1Temp", "nodeDice2Temp", "nodeDice3Temp", "colorDice1", "colorDice2", "colorDice3", "spriteDice"], 3, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 3], ["<PERSON>.<PERSON>", ["bounceDuration", "node", "_N$content", "pageEvents"], 2, 1, 1, 9]], [[7, 0, 1, 2], [0, 0, 7, 5, 3, 4, 2], [0, 0, 7, 5, 3, 4, 6, 2], [1, 1, 4, 5, 6, 2], [14, 0, 1, 2, 3], [0, 0, 7, 8, 5, 3, 4, 6, 2], [0, 0, 7, 5, 3, 4, 9, 6, 2], [10, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 6, 2, 3, 4, 8, 7, 7], [1, 0, 4, 5, 6, 2], [0, 0, 1, 7, 5, 3, 4, 9, 6, 3], [2, 0, 2, 7, 3, 4, 5, 6, 2], [2, 0, 2, 3, 4, 5, 2], [13, 0, 1, 2, 3, 2], [1, 4, 5, 6, 1], [1, 0, 1, 4, 5, 6, 3], [1, 3, 0, 4, 5, 3], [16, 0, 1, 1], [20, 0, 1, 2, 3, 4, 2], [1, 1, 4, 5, 2], [3, 0, 1, 2, 3, 4, 8, 7, 6], [0, 0, 8, 3, 4, 6, 2], [0, 0, 8, 5, 3, 4, 9, 6, 2], [0, 0, 7, 5, 3, 4, 9, 2], [2, 0, 2, 3, 4, 5, 6, 2], [1, 0, 1, 4, 5, 3], [5, 2, 7, 1], [5, 1, 0, 2, 3, 4, 5, 6, 3], [8, 0, 1, 2, 3, 4, 5, 7, 8, 7], [9, 0, 2], [0, 0, 8, 5, 3, 2], [0, 0, 8, 3, 6, 2], [0, 0, 1, 7, 8, 5, 3, 4, 6, 3], [0, 0, 5, 3, 4, 9, 6, 2], [0, 0, 2, 7, 5, 3, 4, 6, 3], [0, 0, 3, 4, 9, 6, 2], [0, 0, 7, 8, 5, 3, 4, 2], [0, 0, 8, 5, 3, 4, 2], [0, 0, 8, 5, 3, 4, 6, 2], [0, 0, 7, 8, 3, 9, 6, 2], [0, 0, 2, 7, 5, 3, 10, 4, 9, 6, 3], [0, 0, 7, 5, 3, 10, 4, 6, 2], [2, 0, 1, 2, 7, 3, 4, 5, 6, 3], [2, 0, 2, 3, 4, 5, 8, 6, 2], [2, 0, 2, 3, 4, 9, 5, 6, 2], [11, 0, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [7, 1, 1], [4, 3, 1, 0, 2, 9, 10, 5], [4, 1, 0, 2, 9, 10, 4], [4, 3, 1, 0, 4, 9, 10, 5], [4, 0, 5, 6, 4, 2, 7, 8, 9, 10, 8], [1, 2, 0, 1, 4, 5, 6, 4], [1, 2, 4, 5, 6, 2], [5, 0, 2, 3, 4, 5, 6, 2], [8, 6, 0, 1, 2, 3, 4, 5, 7, 8, 8], [15, 0, 1], [3, 0, 1, 2, 3, 4, 8, 7, 9, 6], [3, 0, 1, 5, 2, 4, 8, 7, 6], [3, 0, 1, 6, 5, 2, 3, 7, 7], [3, 0, 1, 6, 5, 2, 3, 4, 8, 7, 8], [17, 0, 1, 2, 3, 1], [18, 0, 1, 2, 3, 4, 1], [19, 0, 1, 2, 3, 4, 5, 1], [21, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [22, 0, 1, 2, 3, 2]], [[[{"name": "nut_tai", "rect": [0, 0, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [5], [25]], [[{"name": "nut_xiu", "rect": [0, 0, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [5], [26]], [[[29, "taiXiuGraphView"], [30, "taiXiuGraphView", [-13, -14, -15, -16, -17, -18, -19], [[45, -2, [113, 114], 112], [46, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [47, -1]], [21, "page_2", [-20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33], [0, "180if/3X9I26r2zvXfL42M", 1], [5, 1050, 600], [1575, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "temp", [-34, -35, -36, -37, -38, -39, -40], [0, "30IMlsJH1EyITqqnRU+nU3", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "page_1", [-41, -42, -43, -44, -45, -46, -47], [0, "3bmetvDZZCJYF7Pzous+gQ", 1], [5, 1050, 600], [525, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "layout-numberBot", false, 2, [-49, -50, -51, -52, -53, -54], [[48, false, 1, 2, 9, -48, [5, 50, 201]]], [0, "f0yymOci9Fhr6RPRL5BGH+", 1], [5, 50, 201], [-365, -118, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "layout-parentDice1", [-56, -57], [[13, 1, -55, [61], [4, 4294967295]]], [0, "236vpd2ytI4q9cgtfXteHL", 1], [5, 950, 210], [0, 1, 0.5], [474, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraphDice3View", 2, [[[14, -58, [85], 86], -59], 4, 1], [0, "6e6it3wRBIFZ+x5XMsHW35", 1], [5, 1039, 163], [0, -71.833, 0, 0, 0, 0, 1, 0.8, 1, 1]], [22, "content", [4, 2], [[49, 1, 1, 15, -60, [5, 2100, 600]]], [0, "8bQGt5xtNPOqg+pqO6p4Qw", 1], [5, 2100, 600], [0, 0, 0.5], [-524, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "layout-parentDiceSum", [[13, 1, -61, [64], [4, 4294967295]]], [0, "c7cWr+aKRL/7qzm0/lCGAO", 1], [5, 950, 210], [0, 1, 0.5], [474, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnBackSession", 1, [[[15, 0, false, -62, [6], 7], -63], 4, 1], [0, "d4KwTQgstMYLzPxxVvTr3P", 1], [5, 50, 62], [-535, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnNextSession", 1, [[[15, 0, false, -64, [8], 9], -65], 4, 1], [0, "34dpTSOlNAdYmcP0GDboYj", 1], [5, 50, 62], [535, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 1, [[9, 0, -66, [10], 11], [54, 3, -68, [[4, "ef32bQW2e5LaKiYTRZis+WJ", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -67]], [0, "d555BWhqdOwqXaJVVgCSqx", 1], [5, 100, 50], [551.3, 327.1, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "pageview", 1, [3, -70], [-69], [0, "f141bbdD5Berpy4/Chtz9K", 1], [5, 954, 500], [0, -49, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dot_Tai", 3, [[15, 0, false, -71, [12], 13]], [0, "d6NlaknBdGvIXf4PumChbu", 1], [5, 21, 21]], [2, "dot_Xiu", 3, [[15, 0, false, -72, [14], 15]], [0, "0bIVe6rz1MRIi7AYH0jVPv", 1], [5, 21, 21], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "scrollview", 4, [-76], [[55, false, false, 0.1, false, 0.23, null, null, -74, -73], [56, -75]], [0, "40Q0dqGURD3aWi7Q2DZHUV", 1], [5, 930, 163], [0, -126, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "TotalTai", 4, [-78, -79], [[9, 0, -77, [51], 52]], [0, "5bVc82IM9IiI3aHHrKzpV7", 1], [5, 110, 33], [-55, 272, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "TotalXiu", 4, [-81, -82], [[9, 0, -80, [55], 56]], [0, "71ZoiEirJF058lF2LZVJM/", 1], [5, 110, 39], [71, 272, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraphDiceSumView", 2, [[[14, -83, [57], 58], -84], 4, 1], [0, "52C5dSde1PjLa67i8fXz5u", 1], [5, 1039, 163], [0, 137.76, 0, 0, 0, 0, 1, 0.8, 1, 1]], [42, "toggle-sumDice", false, 2, [-86, -87], [-85], [0, "15I+Jqkw5KaakFUGXsx9tz", 1], [5, 28, 28], [-259, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-session", 2, [-89, -90], [[50, false, 1, 1, 5, -88, [5, 835.3900000000001, 50]]], [0, "6ewYJJEjlK+Z0fgmwahsUl", 1], [5, 835.3900000000001, 50], [0, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "toggle-dice1", 2, [-92, -93], [-91], [0, "2c5l/9HBZBfrBYEiUF+SL+", 1], [5, 161, 65], [-196, -228, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "toggle-dice2", 2, [-95, -96], [-94], [0, "7bANuUrAxLw6oOf9yjq64X", 1], [5, 161, 65], [10, -228, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "toggle-dice3", 2, [-98, -99], [-97], [0, "7bMxkmc4ZCAJjpN+3DY8Tt", 1], [5, 161, 65], [216, -228, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "black", 100, 1, [[9, 0, -100, [0], 1], [26, -101, [4, 4292269782]]], [0, "6fMEV9KgxOi6M7CBjegh34", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[52, 1, 0, false, -102, [2], 3], [26, -103, [4, 4292269782]]], [0, "49EMnkmKhL6py+EaO6hM+w", 1], [5, 1200, 710]], [2, "dot_Tai_number", 3, [[3, false, -104, [16], 17]], [0, "27q+0FAatOfaeIdON0l8gr", 1], [5, 23, 23], [0, 74, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "dot_Xiu_number", 3, [[3, false, -105, [18], 19]], [0, "f0x0xEp8tKiIkzrf7+vwBv", 1], [5, 23, 23], [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Dice1", 3, [[3, false, -106, [20], 21]], [0, "b0rFsQOIlF35r6VEwVNhCU", 1], [5, 27, 27]], [1, "Dice2", 3, [[3, false, -107, [22], 23]], [0, "95RokRFStHUr+29MzmMdxw", 1], [5, 27, 27]], [1, "Dice3", 3, [[3, false, -108, [24], 25]], [0, "31jBDaiFZBk6KT1DUq+jm9", 1], [5, 27, 27]], [5, "view", 13, [8], [[17, -109, [111]]], [0, "fcP4Uw5XFF7oY+K8DBI+PP", 1], [5, 1050, 600], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraph100View", 4, [[-110, [14, -111, [28], 29]], 1, 4], [0, "2dlu4wPfJDgbYzCpyHfc7Y", 1], [5, 954, 151], [0, 126, 0, 0, 0, 0, 1, 1, 1.25, 1]], [2, "layout-parent100", 4, [[51, 3, 1, 6, 25.5, 20, 0, 1, -112, [5, 895, 197]]], [0, "8aXleEb1NNQ4u6gAdLeQeL", 1], [5, 895, 197], [-5, 125, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraphCatCauView", 4, [[[53, 2, -113, [30], 31], -114], 4, 1], [0, "01P4RtrWxIwKordymiIfba", 1], [5, 1039, 163], [0, -126, 0, 0, 0, 0, 1, 0.9, 1, 1]], [35, "layout-parentCatCau", [0, "7ahe1YbOBCqb5ydqyynyTp", 1], [5, 930, 163], [0, 1, 0.5], [472, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "view", 16, [36], [[17, -115, [32]]], [0, "6b0POiuihHHr6aC5qXeG3x", 1], [5, 930, 163]], [37, "view", [9], [[17, -116, [63]]], [0, "555H2o6atNto04Z/Z6Ojzt", 1], [5, 960, 210]], [38, "scrollview-diceSum", [38], [[28, false, 0.1, false, 0.23, null, null, -117, 9]], [0, "83Aq3p1uRJD7O0ZOeuXswJ", 1], [5, 960, 210], [0, 434, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "offset-graph", 2, [39, -118], [0, "8c9uMQpw1GUrJ9bHtNfGyJ", 1], [0, 0.5, 1], [0, -243, 0, 0, 0, 0, 1, 0.88, 0.88, 1]], [5, "scrollview-dice3", 40, [-120], [[28, false, 0.1, false, 0.23, null, null, -119, 6]], [0, "b9703nnnVPJZGlBUBleRCp", 1], [5, 960, 210], [0, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "view", 41, [6], [[17, -121, [62]]], [0, "02MWGQi+dFuI5F+CiNKuig", 1], [5, 960, 210], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "layout-parentDice2", 6, [[13, 1, -122, [59], [4, 4294967295]]], [0, "63neosd+BKIYIZTOQOBmQW", 1], [5, 950, 210], [0, 1, 0.5]], [23, "layout-parentDice3", 6, [[13, 1, -123, [60], [4, 4294967295]]], [0, "31UUgVtB5DvLEMkooBsjEj", 1], [5, 950, 210], [0, 1, 0.5]], [1, "Background", 20, [[25, 0, false, -124, [65]]], [0, "32CzNmTxdMxaFOGf1+BDDi", 1], [5, 36, 36]], [1, "Background", 22, [[3, false, -125, [87], 88]], [0, "f4AdEbn3VCcKZCCZ2ZKtY1", 1], [5, 161, 65]], [1, "Background", 23, [[3, false, -126, [90], 91]], [0, "107DiTmYZBZqQYVo5fNaQA", 1], [5, 161, 65]], [1, "Background", 24, [[3, false, -127, [93], 94]], [0, "2b1GW3CXJHHaUVrwGIVrBw", 1], [5, 161, 65]], [2, "title", 1, [[57, "LỊCH SỬ PHIÊN", 24, false, 1, 1, -128, [4], 5]], [0, "481KT6MTVEMpD9loqG2lDT", 1], [5, 288, 40], [0, 347, 0, 0, 0, 0, 1, 1, 1, 1]], [27, 1.1, 3, 10, [[4, "ef32bQW2e5LaKiYTRZis+WJ", "backPageClicked", 1]], [4, 4294967295], [4, 2533359615], 10], [27, 1.1, 3, 11, [[4, "ef32bQW2e5LaKiYTRZis+WJ", "nextPageClicked", 1]], [4, 4294967295], [4, 2533359615], 11], [40, "bg", 41, 4, [[9, 0, -129, [26], 27]], [0, "b7Rd1V0nVPK4PEN9wG6P84", 1], [4, 4294967195], [5, 1053, 556], [0, 0.5019883919711405, 1], [-1, 302, 0, 0, 0, 0, 1, 1, 1, 1]], [61, 33, 34, 14, 15], [62, 35, 36, 27, 28, [33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48]], [2, "lbTitleTai", 17, [[20, "Tài:", 20, false, 1, 1, -130, [49]]], [0, "54R37vjBlHW6DwdlPb9ReW", 1], [5, 33.34, 50.4], [-22, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "lbTotalTai", 17, [-131], [0, "f3XHXBihVGU4DY4rhh4NAC", 1], [5, 19.77, 50.4], [0, 0, 0.5], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "23", 16, false, false, 1, 56, [50]], [41, "lbTitleXiu", 18, [[20, "Xỉu:", 20, false, 1, 1, -132, [53]]], [0, "d9czs+S9ZHYbka9YUBg3Sq", 1], [4, 4278190080], [5, 34.46, 50.4], [-14, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "lbTotalXiu", 18, [-133], [0, "35t1sYUgJDopg4qHbGRi1u", 1], [4, 4278190080], [5, 19.77, 50.4], [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "34", 16, false, 1, 1, 59, [54]], [63, 19, 9, 9, 14, 15, [69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84]], [18, 3, 20, [4, 4292269782], 45, [[4, "aeec11T33BKF6yhz9yeEiVB", "toggleDrawDiceSumClicked", 19]]], [12, "checkmark", 20, [-134], [0, "d4V50o7h9II5nlNdCecoMk", 1], [5, 35, 35]], [25, 0, false, 63, [66]], [59, "<PERSON><PERSON><PERSON> gần đ<PERSON><PERSON> nhất: #123456", 18, 50, false, false, 1, [68]], [24, "lbSession", 21, [65], [0, "93aCZmwGZMta2g7HSTK3Pl", 1], [5, 443.81, 50], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "lbResult", 21, [-135], [0, "41oK/qPLFL9ZGzEvWfKISS", 1], [5, 192.5, 50], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "Tài 14: (0-0-0)", 16, 50, false, false, 1, 1, 67, [67]], [64, 7, 6, 43, 44, 29, 30, 31, [4, 4294901984], [4, 4278585011], [4, 4288282368], [96, 97, 98]], [18, 3, 22, [4, 4292269782], 46, [[4, "319fe3TIjZLh5aT+W80vXL2", "toggleDrawDice1Clicked", 7]]], [12, "checkmark", 22, [-136], [0, "37jBKn3GVHxKlxJwAq8Mwu", 1], [5, 161, 65]], [19, false, 71, [89]], [18, 3, 23, [4, 4292269782], 47, [[4, "319fe3TIjZLh5aT+W80vXL2", "toggleDrawDice2Clicked", 7]]], [12, "checkmark", 23, [-137], [0, "73puLOXvdGGrcqFNUYS702", 1], [5, 161, 65]], [19, false, 74, [92]], [18, 3, 24, [4, 4292269782], 48, [[4, "319fe3TIjZLh5aT+W80vXL2", "toggleDrawDice3Clicked", 7]]], [12, "checkmark", 24, [-138], [0, "78wAT0KYVJiZahTQE4lms5", 1], [5, 161, 65]], [19, false, 77, [95]], [2, "layout-numberTop", 2, [[14, -139, [99], 100]], [0, "f4fe2olC9Nz6Tnk9lGuubE", 1], [5, 21, 386], [-440, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 5, [[8, "6", 20, 46, false, 2, 1, -140, [101]]], [0, "6dWd1Yf4BISbd4MjGRb0I5", 1], [5, 11.12, 57.96], [0, 1, 0.5], [0, 87.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 5, [[8, "5", 20, 46, false, 2, 1, -141, [102]]], [0, "aaIaVtZTlBwIKdxGHYd4uk", 1], [5, 11.12, 57.96], [0, 1, 0.5], [0, 52.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 5, [[8, "4", 20, 46, false, 2, 1, -142, [103]]], [0, "62ABrQRkpKX40bAeyrVvQa", 1], [5, 11.12, 57.96], [0, 1, 0.5], [0, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 5, [[8, "3", 20, 46, false, 2, 1, -143, [104]]], [0, "29TWkn3M5C3YZgr7ou9yD2", 1], [5, 11.12, 57.96], [0, 1, 0.5], [0, -17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 5, [[8, "2", 20, 46, false, 2, 1, -144, [105]]], [0, "99nYClREZAZ4AEYPHJCtC/", 1], [5, 11.12, 57.96], [0, 1, 0.5], [0, -52.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 5, [[8, "1", 20, 46, false, 2, 1, -145, [106]]], [0, "96LBjH1PdOLI/vG3iAfRpI", 1], [5, 11.12, 57.96], [0, 1, 0.5], [0, -87.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbSum", false, 2, [[16, false, 0, -146, [107]]], [0, "62gHVhe4lOC7Awes+FOXPM", 1], [5, 80, 31], [0, 0, 0.5], [-238.2, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbDice1", false, 2, [[16, false, 0, -147, [108]]], [0, "21LQxg2MRHyJVbVItMjKUw", 1], [5, 100, 30], [0, 0, 0.5], [-112.7, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbDice2", false, 2, [[16, false, 0, -148, [109]]], [0, "17H9vLVUNEaKhQHy/CEIbE", 1], [5, 101, 30], [0, 0, 0.5], [33, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbDice3", false, 2, [[16, false, 0, -149, [110]]], [0, "13A8VPEztLJ50uUX6FEv7X", 1], [5, 102, 30], [0, 0, 0.5], [188, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [65, 0.5, 13, 8, [[4, "ef32bQW2e5LaKiYTRZis+WJ", "pageEvent", 1]]]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 60, 0, 8, 57, 0, 9, 50, 0, 10, 51, 0, 11, 90, 0, 12, 69, 0, 13, 61, 0, 14, 54, 0, 15, 53, 0, 0, 1, 0, -1, 25, 0, -2, 26, 0, -3, 49, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, -7, 13, 0, -1, 19, 0, -2, 7, 0, -3, 20, 0, -4, 22, 0, -5, 23, 0, -6, 24, 0, -7, 79, 0, -8, 5, 0, -9, 21, 0, -10, 86, 0, -11, 87, 0, -12, 88, 0, -13, 89, 0, -14, 40, 0, -1, 14, 0, -2, 15, 0, -3, 27, 0, -4, 28, 0, -5, 29, 0, -6, 30, 0, -7, 31, 0, -1, 52, 0, -2, 33, 0, -3, 35, 0, -4, 34, 0, -5, 16, 0, -6, 17, 0, -7, 18, 0, 0, 5, 0, -1, 80, 0, -2, 81, 0, -3, 82, 0, -4, 83, 0, -5, 84, 0, -6, 85, 0, 0, 6, 0, -1, 43, 0, -2, 44, 0, 0, 7, 0, -2, 69, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, -2, 50, 0, 0, 11, 0, -2, 51, 0, 0, 12, 0, 16, 12, 0, 0, 12, 0, -1, 90, 0, -2, 32, 0, 0, 14, 0, 0, 15, 0, 17, 36, 0, 0, 16, 0, 0, 16, 0, -1, 37, 0, 0, 17, 0, -1, 55, 0, -2, 56, 0, 0, 18, 0, -1, 58, 0, -2, 59, 0, 0, 19, 0, -2, 61, 0, -1, 62, 0, -1, 45, 0, -2, 63, 0, 0, 21, 0, -1, 66, 0, -2, 67, 0, -1, 70, 0, -1, 46, 0, -2, 71, 0, -1, 73, 0, -1, 47, 0, -2, 74, 0, -1, 76, 0, -1, 48, 0, -2, 77, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, -1, 53, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, -2, 54, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, -2, 41, 0, 0, 41, 0, -1, 42, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 52, 0, 0, 55, 0, -1, 57, 0, 0, 58, 0, -1, 60, 0, -1, 64, 0, -1, 68, 0, -1, 72, 0, -1, 75, 0, -1, 78, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 18, 1, 2, 2, 8, 3, 2, 13, 4, 2, 8, 6, 2, 42, 8, 2, 32, 9, 2, 38, 36, 2, 37, 38, 2, 39, 39, 2, 40, 61, 19, 68, 61, 20, 65, 61, 21, 62, 62, 4, 64, 65, 0, 66, 69, 22, 76, 69, 23, 73, 69, 24, 70, 70, 4, 72, 73, 4, 75, 76, 4, 78, 149], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 57, 60, 68, 65, 72, 75, 78], [-1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -2, -3, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 25, -1, -2, 3, 3, 3, 3, 1, 1, 1], [0, 27, 0, 28, 0, 2, 0, 29, 0, 30, 0, 31, 0, 32, 0, 33, 0, 3, 0, 4, 0, 6, 0, 7, 0, 8, 0, 34, 0, 35, 0, 5, 0, 4, 9, 10, 11, 12, 13, 14, 15, 3, 16, 17, 18, 19, 20, 21, 22, 0, 0, 0, 36, 0, 0, 0, 37, 0, 5, 1, 1, 1, 0, 0, 1, 0, 0, 0, 0, 4, 9, 10, 11, 12, 13, 14, 15, 3, 16, 17, 18, 19, 20, 21, 22, 0, 5, 0, 38, 0, 0, 39, 0, 0, 40, 0, 6, 7, 8, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 23, 42, 24, 24, 2, 2, 43, 44, 45]]]]