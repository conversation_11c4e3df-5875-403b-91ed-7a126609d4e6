[1, ["ecpdLyjvZBwrvm+cedCcQy", "72EKsAkolNtpUElKmtBenK", "cay6tgSCdKKopEvBmTQVQc", "35EPCHHCJGpaVFcwh+xHqk", "71bIKsys9KR4sOBz5VOn9c", "ffRmWquFlBD4ZGCDEcgj8j", "daJwnJi9ZHIqS6MWAm7ijq"], ["_textureSetter", "_spriteFrame", "node", "root", "spriteDice3", "spriteDice2", "spriteDice1", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 9, 4, 5, 7], ["c27ebX2iBlHl6bqV/wfRVCM", ["node", "spriteDice1", "spriteDice2", "spriteDice3"], 3, 1, 1, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["cc.Sprite", ["_sizeMode", "node", "_materials"], 2, 1, 3]], [[2, 0, 1, 2], [7, 0, 1, 2, 2], [1, 0, 1, 2, 3, 4, 5, 2], [3, 0, 2], [4, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 4, 2], [5, 0, 1, 2, 3, 1], [6, 0, 1, 2, 3, 4, 4], [2, 1, 1]], [[[{"name": "xi_ngau_bau", "rect": [0, 0, 50, 55], "offset": [0, 0], "originalSize": [50, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [1]], [[{"name": "xi_ngau_ca", "rect": [0, 0, 50, 55], "offset": [0, 0], "originalSize": [50, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [2]], [[[3, "itemGraph"], [4, "temp", [-7, -8, -9], [[6, -5, -4, -3, -2], [7, 1, 2, 7, -6, [5, 88, 182]]], [8, -1], [5, 88, 182], [427, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "node1", 1, [-10], [0, "54v5KWmEZBIqTaEPCXdfVj", 1], [5, 51, 56], [0, 63, 0, 0, 0, 0, 1, 1, 1, 1]], [1, 0, 2, [0]], [5, "node2", 1, [-11], [0, "93i8LoJDtLY7jzsRXX2yF6", 1], [5, 51, 56]], [1, 0, 4, [1]], [2, "node3", 1, [-12], [0, "89tnn7sAVKjYjh9pp9BxTY", 1], [5, 51, 56], [0, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [1, 0, 6, [2]]], 0, [0, 3, 1, 0, 4, 7, 0, 5, 5, 0, 6, 3, 0, 2, 1, 0, 2, 1, 0, -1, 2, 0, -2, 4, 0, -3, 6, 0, -1, 3, 0, -1, 5, 0, -1, 7, 0, 7, 1, 12], [0, 0, 0, 3, 5, 7], [-1, -1, -1, 1, 1, 1], [0, 0, 0, 3, 4, 5]], [[{"name": "xi_ngau_cua", "rect": [0, 0, 50, 55], "offset": [0, 0], "originalSize": [50, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [6]]]]