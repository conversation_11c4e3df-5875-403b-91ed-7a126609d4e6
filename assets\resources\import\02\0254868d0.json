[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "03pVqZRMRJ8J9rEG0CTk3X", "a9VpD0DP5LJYQPXITZq+uj", "d9pBF3jZlAHaC8NSgrQKk6", "1ewsTTeZRBbL9DYfWsJfc7", "c1y3UL3AVHoqWPxPdQzt/K", "69E+9KPNRO5bLljy4NX01E", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "_N$content", "data", "_N$file", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["ab2c91Cx6dMU40l0Q/NBOp7", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -2, 1, 3, 6]], [[7, 0, 1, 2], [0, 0, 4, 6, 3, 2, 5, 8, 7, 2], [0, 0, 4, 3, 2, 5, 7, 2], [2, 1, 2, 1], [4, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 4, 6, 3, 2, 5, 2], [0, 0, 4, 6, 3, 2, 5, 7, 2], [0, 0, 1, 4, 3, 2, 5, 7, 3], [0, 0, 4, 6, 2, 5, 8, 7, 2], [0, 0, 4, 3, 2, 5, 2], [5, 0, 1, 2, 1], [6, 0, 1], [1, 2, 0, 1, 3, 4, 5, 4], [1, 0, 3, 4, 5, 2], [1, 0, 1, 3, 4, 5, 3], [1, 3, 4, 5, 1], [2, 0, 1, 3, 4, 5, 6, 2], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 6]], [[[{"name": "text_HuongDan", "rect": [37, 16, 945, 504], "offset": [9.5, 74], "originalSize": [1000, 684], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [2], [2]], [[[4, "BauCuaHelpView"], [5, "BauCuaHelpView", [-4, -5, -6, -7], [[11, -2, [12, 13], 11], [12, -3]], [0, "23Op3MY7xIHbvjPhrdUieQ", -1]], [6, "nen popup", 1, [-10], [[13, 1, 0, false, -8, [4], 5], [3, -9, [4, 4292269782]]], [0, "d6hSiU+9ZABocQrZQqZ6ai", 1], [5, 1100, 618]], [7, "btnClose", 1, [-13], [[17, 3, -12, [[18, "ab2c91Cx6dMU40l0Q/NBOp7", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -11]], [0, "91+KCuNe1LVrGjm1o+6vIu", 1], [5, 80, 80], [514.011, 288.033, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "black", 100, 1, [[14, 0, -14, [0], 1], [3, -15, [4, 4292269782]]], [0, "8dTCQ2mOpI2YZ+HjfJY447", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "scrollview", 1, [-18], [[19, false, 0.75, 0.23, null, null, -17, -16]], [0, "77wF9noo1NFaRC3D6uYZQ+", 1], [5, 1020, 510], [0, 0.5, 1], [0, 225, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "view", 5, [-20], [[20, 0, -19, [10]]], [0, "b1ZyZ+nXxMDo++1IxGY12V", 1], [5, 1000, 490], [0, 0.5, 1], [0, -6.255, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "content", 6, [-21], [0, "94D1SVpM1BE7K86UOOeZ/I", 1], [5, 1000, 490], [0, 0.5, 1], [0, -4.691, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 2, [[21, "LUẬT CHƠI BẦU CUA", 25, false, 1, 1, -22, [2], 3]], [0, "b8cygrv05Kg7cydT4qJ7r9", 1], [5, 424.22, 40], [-8.218, 294.562, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sprite", 3, [[15, 2, false, -23, [6], 7]], [0, "5fzYfrJm5EnqEmPVPNrOxl", 1], [5, 69, 36]], [2, "text_HuongDan", 7, [[16, -24, [8], 9]], [0, "3ceYxZPd5FFazeFeFlQTY2", 1], [5, 945, 504], [0, -257.545, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -3, 3, 0, -4, 5, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, 4, 3, 0, 0, 3, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, 5, 7, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, -1, 7, 0, -1, 10, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 6, 1, 24], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 7, -1, 1, -1, 1, -1, 1, -1, 8, -1, -2], [0, 3, 0, 4, 0, 5, 0, 6, 0, 7, 0, 1, 1, 8]]]]