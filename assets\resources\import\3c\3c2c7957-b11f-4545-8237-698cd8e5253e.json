[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "a6BLW8ML9DWqqQhW7u/S55", "31i1IMhRRIjqTqhWc9ovtj", "5fMtiDRalHlK6fXQYtsqq/", "c1y3UL3AVHoqWPxPdQzt/K", "9d+PxZkqRKJ7i9c7u2FTVj", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "lbBet", "lbTime", "_defaultClip", "root", "XXHistoryListView", "lbWin", "lbResult", "lbSide", "lbSession", "lbSessionID", "scrollView", "_N$target", "data", "_parent", "_N$content"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_N$overflow", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 5, 5], ["fd16083QSxDaILdLOvlNKYC", ["node", "lbSession", "lbTime", "lbSide", "lbResult", "lbBet", "lbWin"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node"], -2, 1], ["7db6by5MyFPbputnQXLGklv", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["3424fflYk9HPq3+Lx5PqbAS", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["bc433nzD+9Lpofw02nvS9AQ", ["node", "XXHistoryListView"], 3, 1, 1]], [[5, 0, 1, 2], [2, 0, 1, 5, 7, 2, 3, 4, 6, 8, 9, 9], [0, 0, 4, 3, 2, 5, 7, 2], [3, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 6, 4, 5, 2], [2, 0, 1, 5, 7, 2, 3, 4, 6, 8, 9, 10, 9], [0, 0, 4, 6, 3, 2, 5, 7, 2], [1, 0, 4, 5, 6, 2], [4, 1, 2, 1], [6, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 1, 4, 3, 2, 5, 7, 3], [0, 0, 4, 3, 2, 5, 2], [0, 0, 4, 6, 2, 7, 2], [0, 0, 6, 3, 2, 5, 2], [0, 0, 4, 6, 3, 2, 5, 2], [0, 0, 4, 2, 5, 8, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [1, 2, 0, 1, 4, 5, 6, 4], [1, 0, 1, 4, 5, 6, 3], [1, 3, 2, 0, 1, 4, 5, 6, 5], [4, 0, 1, 3, 4, 5, 6, 2], [5, 1, 1], [2, 0, 1, 2, 3, 4, 8, 9, 10, 6], [2, 0, 1, 5, 2, 3, 4, 6, 8, 9, 8], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 4, 5, 1], [10, 0, 1, 2, 3, 4, 5, 6, 1], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6], [13, 0, 1, 2, 3, 4, 5, 4], [14, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 1], [16, 0, 1, 1]], [[9, "xxHistoryView"], [10, "xxHistoryView", [-5, -6, -7, -8, -9, -10, -11], [[32, -2, [29, 30], 28], [33, -4, -3]], [22, -1]], [14, "<PERSON><PERSON>", [-19, -20, -21, -22, -23, -24], [[27, -18, -17, -16, -15, -14, -13, -12]], [0, "c03EP2OPFNrp+aYhJ5du2W", 1], [5, 994, 62]], [6, "title", 1, [-30, -31, -32, -33, -34, -35], [[20, false, 1, 0, false, -25, [19], 20], [26, -29, -28, -27, -26, [4, 4278246399], [4, 4294829568]]], [0, "efs7nosoVII6XsFBJoKoMJ", 1], [5, 994, 50], [0, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "scrollview", 1, [-40, -41], [[-36, [30, 20, 10, 400, -38, 2, -37], -39], 1, 4, 1], [0, "abSSjEdQlNB4cvSvKu3MQ6", 1], [5, 1050, 440], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-44], [[21, 3, -43, [[25, "bc433nzD+9Lpofw02nvS9AQ", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -42]], [0, "41AQmfG8xD3ZeMCc57ejLi", 1], [5, 80, 80], [512.227, 276.591, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "black", 100, 1, [[7, 0, -45, [0], 1], [8, -46, [4, 4292269782]]], [0, "42rMGqoOpIe7ID7aLGs+wC", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "nen popup", 1, [[18, 1, 0, false, -47, [2], 3], [8, -48, [4, 4292269782]]], [0, "36JROz1sFDf7BeArBagBa0", 1], [5, 1084, 618]], [15, "view", 4, [-50], [[28, 0, -49, [27]]], [0, "43NNnxb6JJ6JhB1thbRMyx", 1], [5, 1020, 440]], [29, false, 0.75, 0.23, null, null, 4], [2, "New Label", 1, [[23, "LỊCH SỬ  XÓC ĐĨA", 25, false, 1, 1, -51, [4], 5]], [0, "0dXSxNpchH0LSHzDwaBGS2", 1], [5, 375, 40], [0, 298.434, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bg_content", 1, [[7, 0, -52, [6], 7]], [0, "3eM0sCp4pKOZp5pONfaIWW", 1], [5, 1020, 498], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 5, [[19, 0, false, -53, [8], 9]], [0, "4bDaWWPVNPAYKYkDnGdVZl", 1], [5, 69, 40], [-8.003, 12.626, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbSession", 3, [-54], [0, "de4BQi7o5NUZbm41t3VNDD", 1], [5, 150, 30], [-436, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "PHIÊN", 22, 48, false, false, 1, 1, 1, 13, [10]], [3, "lbTime", 3, [-55], [0, "84IM/F8wJKOJyMlFpguzIy", 1], [5, 200, 38], [-277, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "THỜI GIAN", 22, 48, false, false, 1, 1, 1, 15, [11]], [3, "lbSide", 3, [-56], [0, "c2U4nmsSNJyIRfDwuGH1Aw", 1], [5, 200, 38], [-85, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ĐẶT CỬA", 22, 48, false, false, 1, 1, 1, 17, [12]], [2, "lbResult", 3, [[5, "KẾT QUẢ", 22, 48, false, false, 1, 1, 1, -57, [13], 14]], [0, "9fQMck7AJC87VxLigcOUN5", 1], [5, 200, 38], [115.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbBet", 3, [[5, "TỔNG ĐẶT", 22, 48, false, false, 1, 1, 1, -58, [15], 16]], [0, "77ZG6l6s5JSbu0S/LL+MIN", 1], [5, 200, 38], [281.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTotalWin", 3, [[5, "THỰC NHẬN", 22, 48, false, false, 1, 1, 1, -59, [17], 18]], [0, "a9I8MCqcBDpIht4haiecgq", 1], [5, 200, 38], [412, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "temp", 4, [2], [0, "04lhjSK1pBj7ZYveVkUJ75", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbSession", 2, [-60], [0, "319fn37gBCZ62yHC5y9xrL", 1], [4, 4284344318], [5, 150, 30], [-436, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "#1741424", 22, 48, false, false, 1, 1, 2, 23, [21]], [3, "lbTime", 2, [-61], [0, "34AA9nIE9IvbFmu/wnm9RE", 1], [5, 180, 38], [-277, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "30-10-2019 10:02", 22, 48, false, false, 1, 1, 2, 25, [22]], [3, "lbSide", 2, [-62], [0, "caib+JMPVMP768FjCUuXQE", 1], [5, 180, 38], [-76.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "CHẴN (4 TRẮNG)", 22, 48, false, false, 1, 1, 2, 27, [23]], [3, "lbResult", 2, [-63], [0, "8dLZTrp6NEp7tTqw+Turar", 1], [5, 200, 38], [114.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "CHẮN (2 ĐEN, 2 TRẮNG)", 22, 48, false, 1, 1, 2, 29, [24]], [4, "lbBet", 2, [-64], [0, "c8w/u44GVBmo4qC9YIok4S", 1], [4, 4282969994], [5, 200, 38], [283.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "500.000", 22, 48, false, false, 1, 1, 1, 31, [25]], [4, "lbWin", 2, [-65], [0, "81OYHUMSpPCrXi7pqyNjpk", 1], [4, 4284344318], [5, 200, 38], [412, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1000.000", 22, 48, false, false, 1, 1, 1, 33, [26]], [16, "content", 8, [0, "ed5AKSSuJHTKrl7BoJXDyq", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [31, 20, 10, 400, 4, 2, 9]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 36, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 10, 0, -4, 11, 0, -5, 5, 0, -6, 3, 0, -7, 4, 0, 8, 34, 0, 3, 32, 0, 9, 30, 0, 10, 28, 0, 4, 26, 0, 11, 24, 0, 0, 2, 0, -1, 23, 0, -2, 25, 0, -3, 27, 0, -4, 29, 0, -5, 31, 0, -6, 33, 0, 0, 3, 0, 3, 18, 0, 4, 16, 0, 12, 14, 0, 0, 3, 0, -1, 13, 0, -2, 15, 0, -3, 17, 0, -4, 19, 0, -5, 20, 0, -6, 21, 0, -1, 9, 0, 13, 9, 0, 0, 4, 0, -3, 36, 0, -1, 22, 0, -2, 8, 0, 14, 5, 0, 0, 5, 0, -1, 12, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 35, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, -1, 14, 0, -1, 16, 0, -1, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, -1, 24, 0, -1, 26, 0, -1, 28, 0, -1, 30, 0, -1, 32, 0, -1, 34, 0, 15, 1, 2, 16, 22, 9, 17, 35, 65], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 16, 18, 24, 26, 28, 30, 32, 34], [-1, 2, -1, 2, -1, 1, -1, 2, -1, 2, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, -1, -1, -1, -1, -1, 5, -1, -2, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 9, 0, 0, 0, 0, 0, 0, 0, 3, 3, 10, 1, 1, 1, 2, 2, 2, 2, 2, 2]]