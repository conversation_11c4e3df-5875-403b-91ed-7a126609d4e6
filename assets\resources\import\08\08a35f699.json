[1, ["09mGnbZaZMWaRX1f6hHX5t", "ddyACJHodOX5L4jreHW3LR", "8fEBn0EPtIcZvHv4KcGqo5", "ab9Mogw9lFYrCHpZuvITqq", "308UKdWBlL2I4LSqWZLHP9", "b6/RS/B+1ObZWhhetLIXjr", "8dEH7yrddPVr62K050BfiJ", "5d2QozbHVKZoGt3a2kOXZe", "e8H+4Ra71L0pKiU9PO6JTG", "799CMWQhlF9bQyrVn++O7T", "a7GoFSuz1ArZW3eIwMS5V3", "197tgWRalN/47Ea/kTt4nY", "900AOlHRhMmbV0lxDauiB3", "32jjNQSmlF65CiQo0X/0Ae", "feBeQl11lGxb6JD5E1XNBy", "17+vwqpoFIR5QvFXONnsih", "c9m+0qtwlOT5rseSDdqjrw", "16k617qKFIA5KrCtnlGpX3", "e2HqZj/JlD+5QdzyW4/YIP", "54ySmGNhJLwZjPNYuGwq4y", "4aXKH1NvNKvLS461d5zJcH", "19griIwWRLMZM0GFrtQqAY", "27lG4FgW5ILq5PjQ3b3SIL", "72T5rqdQNJLL6MYPQGOAML", "9eAHqanwRCmrAOfQZlXgPo", "46wEmnkbJG+oEL1/SUDcxQ", "7cIO9UUMFHzL94qGcZ1hB0", "5cIpqj6+RIfqSqr4sJNDXo", "abvRSIpxtFpJj0b3Z7ETkJ", "0ekcyjvsNHxZr4oHAU+cBd", "fczHR43tBGda1Z6/VRGUyQ", "a8G7rUziZPhKR/+seBu+jq", "deqcuxXnRCtJe5uyY1v3Ss", "ccuJcVK49FerQNLS4T+rsd", "9fbqxpyodPhZcys9U7bVAO", "2dbg+ylN9Iyb/OMD7MiI4J", "2eCHOHGctOFoutobWM2KPp", "f5Q6XHs/xB/I3wHKQ8uj/W", "57cxJM5hxP85Nk3f3ooyxO", "bb1h/OsuVDB5a3ABnMIq2L", "3228rOxilBqa+KFfj8QHpW", "c8tP+XVC1IyKWER8xyv6sm", "e8OXjRnBtMeYdajS4CmN+l", "9eDh2bBTpBp6vgxbgPusUe", "b0slL5XaFJz4WL8WMho7Wc", "be0lFkgxZGT6auSVwAQzgG", "0eGM+2ORFK+62IuXpC4SOb", "0ddjxkFkNAb5z3o3ecSoaz", "d6IVS3oDhK7JlkmWgtCCYm", "0dv5GNSD5DLaEggGhDwpNw", "d8dD+7YwZMv410+I/oz5D1", "5f6LzVP4ZJ16IExic4Ox62", "ffRy3RLnxIUaT4LT8Q4jqp", "43+1T9OcZIyLDHqJpydsx2", "caLpwqlnhBj6xnmcs61QW4", "84POkKy0xE2oRVOQWP2gaQ", "ebPdw/apZAkI+ALXoZbG6H", "77b770rDpMgrE6ZbHc7CLA", "622agNb3RD6I8LvuAUFvfe", "26qHaWaLROmbt7ecpTWPZy", "0bl8JoPJZAU5FSV2I4FPi4", "55lBsPvoZPm4OvXfItyL9O", "75GNWQpd1LLYaAzMftCT92", "8bZ6jlOjBL941sv0DwC0Lz"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "bc_wave32", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "bc_wave15", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [1]], [[{"name": "bc_wave3", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [2]], [[{"name": "bc_wave1", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [3]], [[{"name": "bc_wave7", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [4]], [[{"name": "bc_wave8", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [5]], [[{"name": "bc_wave21", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [6]], [[{"name": "bc_wave22", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [7]], [[{"name": "bc_wave26", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [8]], [[{"name": "bc_wave11", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [9]], [[{"name": "bc_wave6", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [10]], [[{"name": "bc_wave5", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [11]], [[{"name": "bc_wave24", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [12]], [[{"name": "bc_wave13", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [13]], [[{"name": "bc_wave9", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [14]], [[[0, "wave", 1, 32, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.03125}, "value", 6, 1], [{"frame": 0.0625}, "value", 6, 2], [{"frame": 0.09375}, "value", 6, 3], [{"frame": 0.125}, "value", 6, 4], [{"frame": 0.15625}, "value", 6, 5], [{"frame": 0.1875}, "value", 6, 6], [{"frame": 0.21875}, "value", 6, 7], [{"frame": 0.25}, "value", 6, 8], [{"frame": 0.28125}, "value", 6, 9], [{"frame": 0.3125}, "value", 6, 10], [{"frame": 0.34375}, "value", 6, 11], [{"frame": 0.375}, "value", 6, 12], [{"frame": 0.40625}, "value", 6, 13], [{"frame": 0.4375}, "value", 6, 14], [{"frame": 0.46875}, "value", 6, 15], [{"frame": 0.5}, "value", 6, 16], [{"frame": 0.53125}, "value", 6, 17], [{"frame": 0.5625}, "value", 6, 18], [{"frame": 0.59375}, "value", 6, 19], [{"frame": 0.625}, "value", 6, 20], [{"frame": 0.65625}, "value", 6, 21], [{"frame": 0.6875}, "value", 6, 22], [{"frame": 0.71875}, "value", 6, 23], [{"frame": 0.75}, "value", 6, 24], [{"frame": 0.78125}, "value", 6, 25], [{"frame": 0.8125}, "value", 6, 26], [{"frame": 0.84375}, "value", 6, 27], [{"frame": 0.875}, "value", 6, 28], [{"frame": 0.90625}, "value", 6, 29], [{"frame": 0.9375}, "value", 6, 30], [{"frame": 0.96875}, "value", 6, 31]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46]], [[{"name": "bc_wave12", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [47]], [[{"name": "bc_wave10", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [48]], [[{"name": "bc_wave29", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [49]], [[{"name": "bc_wave20", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [50]], [[{"name": "bc_wave17", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [51]], [[{"name": "bc_wave14", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [52]], [[{"name": "bc_wave30", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [53]], [[{"name": "bc_wave25", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [54]], [[{"name": "bc_wave31", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [55]], [[{"name": "bc_wave27", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [56]], [[{"name": "bc_wave2", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [57]], [[{"name": "bc_wave19", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [58]], [[{"name": "bc_wave18", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [59]], [[{"name": "bc_wave4", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [60]], [[{"name": "bc_wave28", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [61]], [[{"name": "bc_wave23", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [62]], [[{"name": "bc_wave16", "rect": [0, 0, 256, 256], "offset": [0, 0], "originalSize": [256, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [63]]]]