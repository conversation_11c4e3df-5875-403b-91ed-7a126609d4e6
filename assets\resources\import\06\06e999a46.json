[1, ["ecpdLyjvZBwrvm+cedCcQy", "b2/azgn+FKA5IG0yrqWMgJ", "fdNoodJKVLj4dF1TLppv2g", "74CDTnSZFAJY865f+HIBCe", "89O2DqyfdIbKaEQ6saZmTj", "a9VpD0DP5LJYQPXITZq+uj", "dairgy7kdA0ZN7fLIs37gj", "83esreDA5PPoUmAk6McrcF", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30", "dd/+0VMmVAl4bzL6jIbnx/", "213udNju5BWY/83k5YRc8Q", "f2stFpE3lHCpkXh5S0jtcK", "32fjUA9P5PQ6/mnP+CQc4K", "d2IW3DxZRPIp10qBmaluaJ"], ["node", "_spriteFrame", "_textureSetter", "root", "spriteHelp", "_N$target", "data", "_N$normalSprite", "_defaultClip"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children"], 1, 9, 4, 1, 5, 7, 2], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 6, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["e2b5akJ4gdHdoj+kYLBspp7", ["node", "spriteHelp", "sfHelps"], 3, 1, 1, 3], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[8, 0, 1, 2], [1, 0, 4, 2, 3, 5, 6, 2], [2, 2, 3, 4, 1], [1, 0, 4, 7, 2, 3, 5, 6, 2], [4, 0, 2], [1, 0, 7, 2, 3, 2], [1, 0, 1, 4, 2, 3, 5, 6, 3], [5, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 2, 1], [7, 0, 1, 2, 1], [3, 0, 1, 2, 3, 4, 5, 6, 2], [3, 1, 7, 1], [9, 0, 1, 2, 3], [2, 0, 2, 3, 4, 2], [2, 0, 2, 3, 2], [2, 0, 1, 2, 3, 4, 3]], [[[[4, "t<PERSON>elp<PERSON>iew"], [5, "t<PERSON>elp<PERSON>iew", [-5, -6, -7, -8, -9], [[8, -2, [13, 14], 12], [9, -4, -3, [15, 16, 17, 18]]], [0, "db0UjIuTBI75qOy4nVPaSL", -1]], [3, "btnClose", 1, [-12], [[10, 3, -11, [[12, "9f9e1IJ4WFMSqhdfSr9lY+W", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -10, 11]], [0, "1emKUxCEpHg7uTiaUrfucN", 1], [5, 80, 80], [352.276, 268.589, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "black", 100, 1, [[13, 0, -13, [0], 1], [11, -14, [4, 4292269782]]], [0, "a6InXHYIBNpqXjot67SF8J", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "nen popup", 1, [-16], [[2, -15, [4], 5]], [0, "4azqGc0b5HYre2J474wn1H", 1], [5, 342, 448], [-258.683, 60.21, 0, 0, 0, 0, 1, 1.4, 1.4, 1]], [1, "nen popup", 4, [[2, -17, [2], 3]], [0, "f7KJIYMk9IoJNHBtKuqcHh", 1], [5, 342, 448], [341.435, 0, 0, 0, 0, 0, 1, -1, 1, -1]], [1, "hd", 1, [[2, -18, [6], 7]], [0, "9baro7vu1C14sHpKYHjuO1", 1], [5, 171, 30], [-16.188, 274.585, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "sprite", 1, [-19], [0, "23NmChIH1AIaCvQwfHjn/P", 1], [5, 793, 458], [-8.468, -2, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [14, 2, 7, [8]], [1, "sprite", 2, [[15, 2, false, -20, [9], 10]], [0, "7dNIci/0ZJurwUz/ZE7GfP", 1], [5, 81, 81], [2.903, 1.938, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 8, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 6, 0, -4, 7, 0, -5, 2, 0, 5, 2, 0, 0, 2, 0, -1, 9, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 6, 0, -1, 8, 0, 0, 9, 0, 6, 1, 20], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8], [-1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 7, 8, -1, -2, -1, -2, -3, -4, 1], [0, 5, 0, 1, 0, 1, 0, 6, 0, 0, 7, 8, 2, 2, 9, 3, 10, 4, 4, 3]], [[{"name": "HD-dongngo", "rect": [0, 0, 793, 458], "offset": [0, 0], "originalSize": [793, 458], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [11]], [[{"name": "<PERSON>-th<PERSON>an", "rect": [0, 0, 793, 465], "offset": [0, 0], "originalSize": [793, 465], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [12]], [[{"name": "hd", "rect": [0, 0, 171, 30], "offset": [0, 0], "originalSize": [171, 30], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [13]], [[{"name": "<PERSON>-tao nguy", "rect": [0, 0, 793, 454], "offset": [0, 0], "originalSize": [793, 454], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [14]]]]