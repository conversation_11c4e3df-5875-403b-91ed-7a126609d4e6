[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "1cDpn7iXVFOJlUGFAdLw99", "a3l6ic4lJMhqWRY3K84LO+", "8bB40N/LdMGKgrvO5fBKpm", "dbh0K0Ui1BtbmJ6/AFxXVS", "017Jn3Zv1Ft7hygdjpaSoK", "84sL1PMLRE7bCsBxO5IwzV", "62f4+oyDZEa4GHSq2gua7c", "0e9P4V1GdM85UkCubmruRY", "42FqTAlBVHKoGRMN4Pw/kZ", "71NbLtWZVENZ16emRloX5s", "baj4v5trtOyI7JTchbXAiK", "7a/QZLET9IDreTiBfRn2PD", "76XIlclNBJFY6dbyGQN/Jp", "b1FdnLn49AQY9AUBmOdLZD", "72hOUIvdtMLZ0oT88tRKh8", "5ckbco7cROAJ92hw6PR5eT", "2fLK3rZuRMHKzfC0bRnM3/", "6do44g8xpGK7zzhPP3GOTF", "e4nmUQqB1B5oOPip8ewaBd", "adQbfcQ29JB5aSRs90Tsf6", "f9dTdOo0hB9ZCzesk+z1/5", "08vpyWtphHz5eOotQtCgxB", "89t1t1HzJPfIsXsUwt4Hk2", "5cCKXcafNGnK0Jt9MkRbqF", "be4PQo8dtCYp+vwDjyX56P", "0bCEtI7ptOmZHipubmGQ+v", "11ONk2ePRLa7ZuMeQ9nQtF", "96NF2Gu/FBsYEqsfJEuyf0", "f8zmoIsllG8ozQ9xZTcydw", "65STJRaOhGZJTO2AQIt2zc", "64mtbyC+RKQLqTXA21mmM5", "b5JYZRxtJDz5nGPQJj7fgE", "bbrDBQOvlCBYBb6UsTyg3Y", "60lCKF0BFLRJpfGf89TIRK", "d4zGCcnPVNtbt0FMclu24y", "deFzX2fuBDiJAudVaDc1Oo", "554WRGGlZH0YTRJUPl5Rdx", "90XwFT8bVHFITGkNTMggT+", "aaD8ucCqlIW77wPW5kPw+W", "6cH6A0iI5NobNHapuiYy9w", "4fMAqa1JBAG7TTx9FzOqP9", "8bk4OB12VHrqJItHKofG9f", "44F5RqzeBAOYwVwVHHVTHG", "6265JGQBhAA4xZCkfma9xo", "dbrfKp8QRLXo+LAovF6R1r", "02EpDKtlpDCaBTEg+Y4cu2", "107rft5IRHs5zAD6hNodbh", "24+l98WfxK3YoNjF9BLqfl", "5cqfMMaphGDY1i7mqBlPy+", "74KjI2RXFEFJxQh8wshuC1", "a6ZySYg75Lno2EdvKNyB/W", "44CFdkRFVAOJNvaq+Wx5rT", "e526rqZzNOY7Bay4w07KVp", "7b+59R6DVD35LfHouZ7XJv", "48rNiLsKJE9JxNBJsPpDup", "44ITAzWyFPA5PB1n9SWdco", "83z2yOsnJPr4ovwF+m5dnP", "a33UZXc/5Pzo5TFjSuAxtE", "ccN4ASiitC95aD9Lut28Yz", "1dLy7PiR9Oere6/9SHKyW0", "a7I9cRgV9HIKJiDta28BvA", "f0jwjWQBZMJoR/9qG8ndYb", "abpLQmo1tJV6dbSUq6/UgU", "4drVsf/TtC+pLe8U+CSoYP", "deaTin3HZDCJ8fs6I4a9Q1", "5flru/uyhBPrLlpwDJVuDL", "16FxOm/kFCfbuRieySZ9mO", "57kqKFRGhAXKrsfkQaVkbb", "24QqTxi+lOpIzDbF7Rl9lo", "33Ssg8kdBEabsK0pHAam8E", "8d4IfWJkVN2L5GPfcsGaxm", "b7SNZp6JZF+bzzVHd+74VX", "60Cjw/qchBi6zTtOycNTE/", "d0l3SkFW5CqKqu6M7Hh2xu", "a5Pxb/df1CmIvZ3VE2Tm9A", "7a3tJazMtEGbqPycbPkbHj", "2ez3r//fJInoIlG7i9pDsI", "c0T2zi605LLoTN9Z3ryrPx", "47xjJCY2FEjoU5RCewX7ay", "a6cxNiBy5BcoIp7fWn8Z/6", "bdvJIkJ3JFga0lMXl8x205", "c9xWHktBNFsY0/dVOwqdx0", "ffmdogqx5K0ZFkjKs9NAFe", "d9NoL6doFJPIbcLTX4TWes", "e8zUIRYL5G2ZmZpvwo2fcL", "dcCTQQjzJI9b2zI8+0Up0D", "4blzOf71JK25bvyete8VyO", "5169CJhrNAmJAIU6SpY390", "a3i9OOwSBDwImAHGdyegcH", "a0fj39KGRMbqTGurBNhSP8", "37whHwSRJK+Lif1i0ZnGUO", "c92KSKE4FIaq4HQkEV8XqL", "d09sx5MfdGV5S9gI8MaI8P", "89FB9kDXRCk6fWkBkmp0WC", "425OyJiKNBEqTIincFeXrD", "b6ncPWds1LN42CD6PDZAIL", "eaOrgifZJI3rZL8jovxyWR", "363oVUjhJPwaDfOLZMXfyb", "a6Ydnifa5O0auqa9VzhHps", "911LLlfY1MRK/td4XQs4O+", "e5KCyRNWNOwbYhKVVpO27z", "be4bdSJlxF1pV2p/b1VRXO", "daEjx/aJpA6KlBIWq74594", "7bmWz3SbpE8YTGsALDvaTR", "edqtZ+KBxLFLEpyMVyK1fa", "f10mlGIaBJ04bgbDT+bUVp", "0bmetR1uNBWqu7H4QFpcox", "15zHi1VSNCDKrfXEL26FgV", "7bnBb+uj1Cz5jhl7DTOCT4", "8494ISLDFPkYi2F4uKxX/z", "28I33u5hxNJ7vQEg89Yzcf", "7fGEq8BPZGtr8pGKZzLel9", "faiQhppi9DlL2cQuNvzivL", "45CtNC6mZISLuITqPqjev+", "57zZN6zpdJSo7WK4MYkiTW", "8eu5lDi55CQbUTy+ZEJDuJ", "a0VIOUnqlECbYXmuQPB42u", "f3Fr4uDTdI1Y3VC+i8z1hr", "92EQr8uldAypNjzGyXF4v/", "3ewe2727lLVoJ0ADZ8W05J", "e7Fj4Fv9dFza13rOJG5SHm", "39f7+X/G1ISouId8kveHFD", "45c/a/PatNa6lfosNdvRPt", "afYrEVl7xB36C5GVg5Ufiq", "23KDdWwRNKJJpUdOSxvwmM", "7904g+IcRAR72oGvrjwQBV", "8fSA+XMDRMgog5+JKqHoFc", "7dFN9M5VhLBbPjJLZpEK6L", "f26343FIdChJbsteFZN2Jb", "f8ikgKlIRA8KZ+2oAI42zr"], ["node", "_spriteFrame", "_N$file", "_N$target", "_parent", "_N$skeletonData", "_normalMaterial", "_N$normalSprite", "_defaultClip", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_N$barSprite", "_clip", "_N$font", "layoutCardGame", "lbSID", "lbMessage", "prefabHelp", "prefabLayoutWrapCard", "spriteCardBack", "prefabChat", "sfAvatarDef", "bmfWin", "bmfLose", "sfBorderCard", "sfCardBack", "prefab", "root", "nodeParentChat", "nodeMainRoomView", "nodeChooseRoomView", "animationNofity", "nodeRegisterLeave", "spriteBack", "spriteSound", "layoutContentCard", "buttonDanh", "buttonXepBai", "buttonChon", "buttonBoLuot", "layoutButton", "dealer", "lbPlayerStatus", "lbInfo", "lbTableId", "lbRoomID", "btnSendChat", "editBoxChat", "chatListView", "lbTotalWin", "lbNickName", "lbRank", "iconRank", "rtAdmin", "lbName", "chipBet", "moveCard", "musicBackground", "_N$content", "TLMNTopListView", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs", "_anchorPoint", "_color"], 0, 4, 1, 9, 5, 2, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_fillType", "_fillStart", "_fillRange", "_srcBlendFactor", "node", "_materials", "_spriteFrame", "_fillCenter"], -5, 1, 3, 6, 5], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_anchorPoint", "_color", "_children"], 1, 2, 4, 5, 1, 7, 5, 5, 2], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_string", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_N$interactable", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_normalMaterial", "_N$normalSprite"], 0, 1, 9, 1, 5, 5, 5, 6, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children"], 1, 12, 4, 5, 1, 7, 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$affectedByScale", "_N$paddingLeft", "_N$paddingRight", "_N$paddingTop", "_N$paddingBottom", "_N$spacingY", "node", "_layoutSize"], -7, 1, 5], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "_animationName", "defaultAnimation", "node", "_N$skeletonData", "_materials"], -1, 1, 6, 3], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$handleTouchEvent", "node", "_N$font"], -3, 1, 6], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.AudioSource", ["preload", "_volume", "_loop", "node"], 0, 1], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_prefab"], 2, 1, 12, 4], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["8be9fmDgjRGuJDpCIp+Bx7T", ["node", "nodeUser", "lbName", "lbMessage", "rtAdmin"], 3, 1, 1, 1, 1, 1], ["87b0b6j4kBLKKRiRXRQjf/0", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "editingReturn", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 9, 1, 1, 1], ["c9ea2HJ+4FBwJf8JdBQBbUQ", ["channelId", "node", "chatListView", "editBoxChat", "btnSendChat"], 2, 1, 1, 1, 1], ["fde5fYTdUdLFaHQ7QSWDYdb", ["node"], 3, 1], ["e63a8GDCoBJzrUnbCTfn9Hs", ["node", "iconRank", "lbRank", "lbSID", "lbNickName", "lbTotalWin", "spTop"], 3, 1, 1, 1, 1, 1, 1, 3], ["63e69hqxiNBDZood3oxrSds", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["d7e53janRVDYbu49BC/dgcL", ["node", "TLMNTopListView"], 3, 1, 1], ["6d778KTeFpKA4z4LqE+Fin1", ["node", "prefabHelp"], 3, 1, 6], ["87c34hvqC1AcbGs5+NTs4WA", ["node", "musicBackground", "moveCard", "chipBet"], 3, 1, 1, 1, 1], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["3e540mOKLZMLI5LC2NNOdwF", ["node", "cardOnHand", "layoutCard"], 3, 1, 1, 1], ["2d79eSpNN9AurP/ZR5pfwNJ", ["ordinalValue", "cardNumber", "node"], 1, 1], ["832cdd6gThBUZ3q0q4hjRy5", ["messWinPosY", "node", "nodeMessage", "lbMessage"], 2, 1, 1, 1], ["aa0ebuePJNGjaJ+PGuzW9E2", ["node", "layoutCardGame", "sfSounds", "spriteSound", "spriteBack", "nodeRegisterLeave"], 3, 1, 1, 3, 1, 1, 1], ["c640du6Nt5BRrAkpiwohzN3", ["node", "layoutCardGame", "layoutContentCard", "prefabLayoutWrapCard"], 3, 1, 1, 1, 6], ["0b7c8vAZhtLbqM8oqzmuWlT", ["node", "layoutButton", "buttonBoLuot", "buttonChon", "buttonXepBai", "buttonDanh"], 3, 1, 1, 1, 1, 1, 1], ["53b88trXlpM65FqpX9o6BHx", ["node", "lbSID", "lbRoomID", "lbTableId", "lbInfo", "lbPlayerStatus", "TLMNPlayers", "dealer", "spriteCardBack"], 3, 1, 1, 1, 1, 1, 1, 2, 1, 6], ["0f177JJ4r9JpZg0SeTDkSog", ["node", "nodeChooseRoomView", "nodeMainRoomView", "nodeParentChat", "prefabChat"], 3, 1, 1, 1, 1, 6], ["09d764EYGtI06IOUPrt3aKW", ["node", "spGameResult", "colorDark", "colorWhite", "sfBack", "sfSuitCo", "sfSuitRo", "sfSuitTep", "sfSuitBich", "animationNofity", "sfAvatarDef", "bmfWin", "bmfLose", "sfBorderCard", "sfCardBack"], 3, 1, 12, 5, 5, 3, 3, 3, 3, 3, 1, 6, 6, 6, 6, 6], ["ecbe1Xn8BZJ2KneOLuQ5ukd", ["node", "prefab"], 3, 1, 6]], [[17, 0, 1, 2], [0, 0, 4, 5, 3, 6, 8, 2], [3, 3, 0, 1, 4, 2, 8, 9, 10, 6], [0, 0, 4, 7, 5, 3, 6, 8, 2], [1, 8, 9, 10, 1], [0, 0, 4, 5, 3, 6, 2], [1, 0, 8, 9, 10, 2], [0, 0, 4, 5, 3, 10, 6, 2], [3, 3, 0, 1, 4, 2, 8, 10, 6], [8, 0, 1, 3, 3], [13, 1, 2, 1], [1, 1, 0, 8, 10, 3], [0, 0, 1, 4, 5, 3, 6, 8, 3], [4, 0, 3, 4, 6, 5, 9, 2], [8, 0, 1, 2, 3, 4], [0, 0, 4, 5, 3, 6, 9, 8, 2], [0, 0, 1, 4, 5, 3, 6, 9, 8, 3], [0, 0, 2, 4, 7, 5, 3, 6, 8, 3], [2, 0, 5, 9, 2, 3, 4, 6, 2], [1, 1, 0, 8, 9, 10, 3], [0, 0, 4, 7, 5, 3, 6, 9, 8, 2], [2, 0, 5, 2, 3, 4, 7, 6, 2], [1, 1, 0, 2, 8, 9, 10, 4], [3, 3, 0, 1, 4, 2, 8, 9, 6], [6, 0, 1, 2, 10, 11, 4], [23, 0, 1], [0, 0, 1, 4, 5, 3, 10, 6, 8, 3], [0, 0, 1, 4, 7, 3, 3], [5, 0, 7, 2, 3, 4, 6, 2], [1, 1, 0, 8, 3], [4, 0, 3, 4, 6, 7, 8, 5, 2], [3, 3, 0, 5, 1, 4, 2, 7, 8, 10, 8], [6, 0, 1, 2, 4, 10, 11, 5], [13, 0, 1, 2, 3, 2], [29, 0, 1, 2, 2], [30, 0, 1], [7, 0, 1, 2, 4, 6, 5, 4], [7, 3, 0, 1, 2, 4, 5, 5], [31, 0, 1, 2, 1], [0, 0, 1, 4, 7, 5, 3, 6, 8, 3], [0, 0, 4, 7, 5, 3, 10, 6, 8, 2], [2, 0, 1, 5, 2, 3, 4, 7, 6, 3], [2, 0, 5, 2, 3, 2], [5, 0, 5, 2, 3, 4, 2], [1, 8, 10, 1], [1, 1, 0, 4, 5, 6, 8, 9, 11, 6], [4, 1, 0, 3, 4, 6, 7, 8, 5, 3], [32, 0, 1, 2, 3], [0, 0, 4, 7, 5, 3, 2], [0, 0, 4, 7, 3, 2], [0, 0, 4, 7, 3, 8, 2], [0, 0, 4, 7, 5, 3, 8, 2], [0, 0, 4, 7, 5, 3, 6, 2], [0, 0, 1, 4, 7, 3, 8, 3], [2, 0, 5, 2, 3, 4, 2], [2, 0, 5, 2, 3, 4, 6, 2], [1, 0, 2, 8, 9, 3], [16, 0, 1], [3, 3, 0, 1, 4, 2, 8, 6], [6, 0, 1, 10, 11, 3], [18, 0, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 3, 2], [14, 0, 2], [0, 0, 7, 5, 3, 2], [0, 0, 7, 5, 3, 6, 8, 2], [0, 0, 7, 5, 3, 6, 9, 8, 2], [0, 0, 7, 5, 3, 6, 9, 2], [0, 0, 1, 4, 5, 3, 10, 6, 9, 3], [0, 0, 7, 5, 3, 10, 6, 8, 2], [0, 0, 4, 7, 5, 3, 6, 9, 2], [0, 0, 4, 3, 6, 9, 2], [0, 0, 1, 2, 4, 5, 3, 6, 9, 8, 4], [0, 0, 1, 4, 3, 3], [0, 0, 1, 4, 3, 8, 3], [0, 0, 4, 3, 6, 2], [0, 0, 1, 2, 4, 5, 3, 6, 8, 4], [0, 0, 4, 3, 2], [0, 0, 1, 2, 7, 5, 3, 6, 4], [2, 0, 5, 2, 3, 8, 4, 7, 2], [2, 0, 9, 2, 3, 4, 6, 2], [2, 0, 5, 2, 3, 8, 4, 7, 6, 2], [2, 0, 9, 2, 3, 4, 7, 6, 2], [2, 0, 1, 5, 2, 3, 8, 4, 7, 6, 3], [2, 0, 5, 9, 2, 3, 8, 4, 7, 6, 2], [2, 0, 5, 2, 3, 8, 4, 6, 2], [2, 0, 5, 2, 3, 8, 4, 2], [2, 0, 1, 5, 2, 3, 4, 6, 3], [5, 0, 5, 2, 3, 4, 6, 2], [5, 0, 1, 5, 2, 3, 4, 6, 3], [5, 0, 1, 5, 2, 3, 4, 3], [15, 0, 1, 2, 3, 2], [1, 1, 0, 8, 9, 3], [1, 8, 1], [1, 0, 8, 10, 2], [1, 3, 8, 9, 2], [1, 1, 0, 4, 5, 6, 8, 11, 6], [1, 0, 2, 8, 9, 10, 3], [1, 0, 8, 2], [1, 3, 7, 0, 2, 8, 5], [4, 3, 4, 1], [4, 1, 2, 0, 3, 4, 7, 8, 5, 4], [4, 0, 3, 4, 6, 5, 2], [4, 0, 3, 4, 6, 7, 8, 5, 10, 2], [3, 3, 0, 5, 6, 1, 4, 2, 8, 10, 8], [3, 0, 5, 6, 1, 4, 2, 8, 9, 7], [3, 0, 5, 6, 1, 2, 7, 8, 9, 7], [3, 0, 5, 6, 1, 2, 7, 8, 7], [3, 3, 0, 5, 6, 1, 2, 7, 8, 9, 8], [3, 3, 0, 5, 6, 1, 7, 8, 9, 7], [3, 0, 1, 4, 2, 8, 5], [3, 3, 0, 5, 6, 1, 4, 2, 7, 8, 9], [6, 3, 0, 1, 5, 6, 7, 8, 2, 9, 10, 11, 10], [6, 3, 0, 1, 2, 4, 10, 11, 6], [9, 0, 1, 2, 3, 4, 5, 6, 7], [9, 0, 1, 2, 3, 4, 5, 6, 7, 7], [19, 0, 1, 2, 3, 4, 1], [10, 0, 1, 2], [11, 0, 5, 6, 2], [20, 0, 1, 2, 3, 4, 3], [21, 0, 1, 2, 3, 4, 5, 6, 3], [22, 0, 1, 2, 3, 4, 2], [24, 0, 1, 2, 3, 4, 5, 6, 1], [25, 0, 1, 2, 3, 4, 3], [26, 0, 1, 1], [27, 0, 1, 1], [12, 1, 2, 0, 3, 4], [28, 0, 1, 2, 3, 1], [7, 3, 0, 1, 2, 4, 5], [7, 0, 1, 4, 3], [33, 0, 1, 2, 3, 2], [34, 0, 1, 2, 3, 4, 5, 1], [35, 0, 1, 2, 3, 1], [36, 0, 1, 2, 3, 4, 5, 1], [37, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [38, 0, 1, 2, 3, 4, 1], [39, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1], [40, 0, 1, 1]], [[64, "TLMNLobby"], [65, "TLMNLobby", [-9, -10], [[136, -5, -4, -3, -2, 265], [137, -7, [[267, 268, 269, 270, null, null, 271], 6, 6, 6, 6, 0, 0, 6], [4, 4288322204], [4, 4294967295], [274, 275], [277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289], [290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302], [303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315], [316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328], -6, 266, 272, 273, 276, 329], [138, -8, 330]], [0, "43UGbK/WRL8LuWl/3T8ARm", -1]], [51, "MainGame", 1, [-36, -37, -38, -39, -40, -41], [[132, -15, -14, [261, 262], -13, -12, -11], [133, -18, -17, -16, 263], [134, -24, -23, -22, -21, -20, -19], [135, -35, -34, -33, -32, -31, -30, [-26, -27, -28, -29], -25, 264]], [0, "7eKVLgpTRK5KyfJybzOgkq", 1], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "game", 2, [-42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53], [0, "45s95xgutKc7b397xt8k3o", 1]], [28, "btnPos1", [-56, -57, -58, -59, -60, -61, -62, -63, -64, -65], [[[10, -54, [145, 146]], -55], 4, 1], [0, "b157f3JkdDiayPySG+U+Yf", 1], [5, 120, 120], [-539, -309, 0, 0, 0, 0, 1, 1, 1, 1]], [92, "layoutPositionCard", 3, [[-66, -67, -68, -69, -70, -71, -72, -73, [74, "posCardStartSlide", false, -74, [0, "cbamR4WbdHKJFbvKzbpWGz", 1]], [75, "posCardStartSlide copy", false, -75, [0, "fcr4aEJ+FI8rS240tHfRxC", 1], [-40, -145, 0, 0, 0, 0, 1, 1, 1, 1]], -76], 1, 1, 1, 1, 1, 1, 1, 1, 4, 4, 1], [0, "5bvqFaFoJDo4ZOqzT5u0fr", 1]], [28, "btnPos2", [-79, -80, -81, -82, -83, -84, -85, -86, -87, -88], [[[10, -77, [193, 194]], -78], 4, 1], [0, "b774yl/75J/56gBWtuj1I7", 1], [5, 100, 100], [-553.8, -48.6, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "btnPos3", [-91, -92, -93, -94, -95, -96, -97, -98, -99, -100], [[[10, -89, [219, 220]], -90], 4, 1], [0, "9a4fUP+/VPQ6aB+gjJkSC0", 1], [5, 90, 90], [42.5, 226, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "btnPos4", [-103, -104, -105, -106, -107, -108, -109, -110, -111, -112], [[[10, -101, [244, 245]], -102], 4, 1], [0, "58enIAlO9E26QHRrGeg/UP", 1], [5, 90, 90], [548.7, -48, 0, 0, 0, 0, 1, 1, 1, 1]], [66, "layoutRoom", [-114, -115, -116, -117, -118, -119, -120, -121, -122], [[113, false, 1, 3, 20, 20, 20, 15, 60, 20, -113, [5, 580, 483]]], [0, "997n4x2KRCv5zmurFudRkb", 1], [5, 580, 483], [33, -63.2, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "Rooms", 1, [-124, -125, 9, -126, -127, -128], [[126, -123, 98]], [0, "f0xZm+6dBPt6GcBwAVz6Po", 1]], [3, "chatView", 10, [-133, -134, -135, -136, -137], [[122, "bai", -132, -131, -130, -129]], [0, "f1NUN1zRtKVpmJgwvtZvI+", 1], [5, 358, 495], [-451, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [70, "item", [-144, -145, -146, -147, -148, -149], [[123, -143, -142, -141, -140, -139, -138, [90, 91, 92]]], [0, "58ZymcCT9JOqghoN58xm7N", 1], [4, 4278190080], [5, 240, 60], [-2, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bgSID", 3, [-151, -152, -153, -154, -155, -156], [[19, 1, 0, -150, [117], 118]], [0, "77B0CIA6FDL6Ut/5771QWp", 1], [5, 170, 66], [-409.7, 324, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "layoutButtons", 3, [-158, -159, -160, -161, -162], [[114, false, 1, 1, 60, true, -157, [5, 780, 60]]], [0, "ebNo2CC/ZK4JgNjPhq8YX2", 1], [5, 780, 60], [0, 0, 0.5], [-162, -313, 0, 0, 0, 0, 1, 1, 1, 1]], [68, "item-horizontal", [-164, -165, -166, -167], [[59, 1, 1, -163, [5, 279.39, 33]]], [0, "55cT/PlKVEH4WQNx1UctWE", 1], [5, 279.39, 33], [0, 0, 0.5]], [3, "1k", 9, [-171, -172], [[4, -168, [12], 13], [13, 3, -170, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "1000", 1]], [4, 4292269782], -169, 14]], [0, "51raaw6LNEa43ohzQTKFc9", 1], [5, 136, 136], [-202, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "2k", 200, 9, [-176, -177], [[4, -173, [19], 20], [13, 3, -175, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "2000", 1]], [4, 4292269782], -174, 21]], [0, "cfnoL+3yNKLb4kW0Nqsgqc", 1], [5, 136, 136], [-6, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "5k", 9, [-181, -182], [[4, -178, [26], 27], [13, 3, -180, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "5000", 1]], [4, 4292269782], -179, 28]], [0, "b4KqRfM+hMJ5ePQLZlIlzb", 1], [5, 136, 136], [190, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "10k", 9, [-186, -187], [[4, -183, [33], 34], [13, 3, -185, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "10000", 1]], [4, 4292269782], -184, 35]], [0, "1eHi2ud3JPRZXfEBxMZn17", 1], [5, 136, 136], [-202, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "20K", 200, 9, [-191, -192], [[4, -188, [40], 41], [13, 3, -190, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "20000", 1]], [4, 4292269782], -189, 42]], [0, "5eLJiWjW9CdYdMWBJkcpU+", 1], [5, 136, 136], [-6, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "50K", 200, 9, [-196, -197], [[4, -193, [47], 48], [13, 3, -195, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "50000", 1]], [4, 4292269782], -194, 49]], [0, "2cWLsY+P9L4alpj48rPz/C", 1], [5, 136, 136], [190, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "100K", 200, 9, [-201, -202], [[4, -198, [54], 55], [13, 3, -200, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "100000", 1]], [4, 4292269782], -199, 56]], [0, "94XNN7RVFHk5d7ftEjDOn8", 1], [5, 136, 136], [-202, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "200k", 200, 9, [-206, -207], [[4, -203, [61], 62], [13, 3, -205, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "200000", 1]], [4, 4292269782], -204, 63]], [0, "9eqEDckJxIKpHmL7f8GhnR", 1], [5, 136, 136], [-6, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "500K", 200, 9, [-211, -212], [[4, -208, [68], 69], [13, 3, -210, [[14, "0f177JJ4r9JpZg0SeTDkSog", "setBetRoom", "500000", 1]], [4, 4292269782], -209, 70]], [0, "e3IUuM54pENqTsXMWB7kTu", 1], [5, 136, 136], [190, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [67, "item", [-218, 15], [[24, 1, 2, 5, -213, [5, -5, 33]], [117, -217, 15, -216, -215, -214]], [0, "854Zh5jWNC2ZFkKj51qdrL", 1], [5, -5, 33], [0, 0, 0.5], [-170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scrollview", 11, [-221, -222], [-219, -220], [0, "22q7CIYOZBx5NMqIAGFYBv", 1], [5, 360, 270], [-7, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [81, "editbox-chat", [-224, -225, -226], [-223], [0, "d4zMobgTREZZECQOE639DT", 1], [5, 235, 30], [-43, 4.5, 0, 0, 0, 0, 1, 1, 1, 1]], [83, "scrollviewRanks", [12, -229], [-227, -228], [0, "957Plg/4FAKqVfrf2nWWu7", 1], [5, 240, 350], [0, 0.5, 1], [0, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "audioPool", 2, [-234, -235, -236], [[128, -233, -232, -231, -230]], [0, "cbMRREoIdF7oCg/7LgkOOT", 1]], [50, "user-pos", 3, [4, 6, 7, 8], [0, "50FZlNnrxMR4D/TM3EHxxD", 1], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [79, "offset-message", false, 0, [-238, -239], [[100, false, 768, 2, false, -237]], [0, "0bb2XrKshDBrUuYjUrR0xr", 1], [5, 451, 80]], [39, "scrollview", false, 11, [-243], [[62, false, 0.75, 0.23, null, null, -241, -240], [60, -242]], [0, "5dCD4o/C9CLIo3aAeh8LIj", 1], [5, 360, 270], [0, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnSend", 11, [-245], [-244], [0, "a44KtUHYJOHac9wvtEOW+C", 1], [5, 80, 70], [108, -200.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 10, [[6, 2, -246, [96], 97], [103, 3, -248, [[9, "6d778KTeFpKA4z4LqE+Fin1", "createHelpView", 10]], [4, 4292269782], -247]], [0, "5eGQzidUlAIK598BlBa2D0", 1], [5, 51, 51], [326, 199, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnClose", 3, [-251], [[46, 0.9, 3, -250, [[9, "aa0ebuePJNGjaJ+PGuzW9E2", "backClicked", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -249]], [0, "a0FOF/LjFJEb52LH+bchag", 1], [5, 100, 96], [-589, 312, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnSound", 3, [-254], [[46, 0.9, 3, -253, [[9, "aa0ebuePJNGjaJ+PGuzW9E2", "soundClicked", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -252]], [0, "31ao5WselFzLNJ4ucqDT6C", 1], [5, 80, 70], [588, 311.5, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "nodeStatus", false, 3, [-255, -256, -257], [0, "4dehIT1EVAI69DvShvlPOt", 1], [0, 97, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ava_money", 4, [-259, -260], [[4, -258, [132], 133]], [0, "4fXR3PTgJHZ5N4X9cHBPzx", 1], [5, 121, 51], [-4.1, -84.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layoutName", 38, [-262, -263], [[24, 1, 1, 2, -261, [5, 89.8, 20]]], [0, "7d/QMR8wlLvrj+c1/xn19A", 1], [5, 89.8, 20], [4, 13.2, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "cards_user1", false, 5, [-265], [[44, -264, 170]], [0, "40O5HLZiBGs7WTWOvso2ip", 1], [5, 49, 71], [-424, -209, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "layoutCard_user1", 5, [[32, 1, 1, -20, true, -266, [5, 20, 120]], [10, -267, [147]]], [0, "9e3qfBa/5PiIHOFk5tU5RA", 1], [5, 20, 120], [0, 0, 0.5], [-438, -199, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "cards_user2", 5, [-269], [[4, -268, [150], 151]], [0, "b5EctEj81HHLcVLd01n4dO", 1], [4, 4288322204], [5, 56, 81], [-437, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "layoutCard_user2", 5, [-271], [[32, 1, 1, -55, true, -270, [5, 55, 120]]], [0, "8fa4D1PopJvYcfFcmqqMzN", 1], [5, 55, 120], [0, 0, 0.5], [-464.3, 3, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [12, "item1 copy", false, 43, [[11, 1, 0, -272, 152], [47, 0, 0, -273], [10, -274, [153, 154]]], [0, "81cmZj+UhI/6QfyVaxRB3j", 1], [5, 107, 153], [45.***************, 0, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [40, "cards_user3", 5, [-276], [[4, -275, [157], 158]], [0, "f2p98GeR5GuqVkoYjpMKfy", 1], [4, 4288322204], [5, 56, 81], [-79.7, 275, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "layoutCard_user3", 5, [-278], [[32, 1, 1, -55, true, -277, [5, 55, 120]]], [0, "c9jPmWOLtAQ68ZXv0FKoQW", 1], [5, 55, 120], [0, 0, 0.5], [120, 257, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [12, "item1 copy", false, 46, [[11, 1, 0, -279, 159], [47, 0, 0, -280], [10, -281, [160, 161]]], [0, "00P2gaLN1B2b524ugh72ND", 1], [5, 107, 153], [45.***************, -0.7, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [40, "cards_user4", 5, [-283], [[4, -282, [164], 165]], [0, "11ruUoXgtDwbYSmV/kQXxr", 1], [4, 4288322204], [5, 56, 81], [430.5, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "layoutCard_user4", 5, [-285], [[32, 1, 1, -55, true, -284, [5, 55, 120]]], [0, "7fmyE8BE5IX74LA9FFNj6T", 1], [5, 55, 120], [0, 1, 0.5], [460, 5, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [12, "item1 copy", false, 49, [[11, 1, 0, -286, 166], [47, 0, 0, -287], [10, -288, [167, 168]]], [0, "aa5jp5MgxEtq/QciB9MHxw", 1], [5, 107, 153], [-45.475, 0, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [3, "ava_money", 6, [-290, -291], [[4, -289, [180], 181]], [0, "55gulSz55P+rdZak56OVwE", 1], [5, 121, 51], [-2.4, -69.7, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layoutName", 51, [-293, -294], [[24, 1, 1, 2, -292, [5, 89.8, 20]]], [0, "7aBRC4drRNf47CyblMdNlt", 1], [5, 89.8, 20], [5.4, 14.9, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ava_money", 7, [-296, -297], [[4, -295, [205], 206]], [0, "4461r7CKRNQpKx3hNVHg0D", 1], [5, 121, 51], [-2, -70, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layoutName", 53, [-299, -300], [[24, 1, 1, 2, -298, [5, 89.8, 20]]], [0, "48iouVv5RKyZjP0aGEBYRp", 1], [5, 89.8, 20], [3.9, 9.8, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ava_money", 8, [-302, -303], [[4, -301, [231], 232]], [0, "5fii79updLA6FpHXqIQAlf", 1], [5, 121, 51], [0, -71.9, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layoutName", 55, [-305, -306], [[24, 1, 1, 2, -304, [5, 89.8, 20]]], [0, "20rxmJEGhNWolTdUUCazjz", 1], [5, 89.8, 20], [4.6, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "bgLeaveRoom", false, 3, [-308], [[11, 1, 0, -307, 247]], [0, "10+rT9jxZKWqQfe88zCUNO", 1], [5, 200, 40], [-538.7, 267, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnChat", 3, [-311], [[46, 0.9, 3, -310, [[9, "0f177JJ4r9JpZg0SeTDkSog", "chatClicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -309]], [0, "26lb4JVbBF2LN/0Cde6QAC", 1], [5, 80, 70], [-416.2, -320.9, 0, 0, 0, 0, 1, 1, 1, 1]], [77, "btnBatDau", false, 0, 14, [[104, 3, -313, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onStartGame", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -312, 250], [11, 1, 0, -314, 251]], [0, "d5TF0I+VNAub7CdaEYstL1", 1], [5, 150, 63], [170, 297, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "popupSlotsView", 2, [31], [[10, -315, [260]], [131, 0, -317, 31, -316]], [0, "dbVvcZ60BCoba43SjhXiQB", 1], [5, 444, 220]], [49, "bg", 10, [-318, -319], [0, "8eBI+hpD5Hga3pBK3ZdpGD", 1]], [1, "bg", 61, [[6, 0, -320, [0], 1], [57, -321]], [0, "d4niu5xwZCnpyibMZNsHhb", 1], [5, 1561, 720], [0, 5.684341886080802e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icoBack@2x", 61, [[4, -322, [2], 3], [101, -323, [[9, "0f177JJ4r9JpZg0SeTDkSog", "<PERSON><PERSON><PERSON><PERSON>", 1]]]], [0, "cbzPicdYJNdZmbl2uWrpjH", 1], [5, 36, 53], [-600.355, 235.964, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "gameName", 10, [-325], [[6, 0, -324, [6], 7]], [0, "e6nvLFe0NN14WHvPg+pT1V", 1], [5, 403, 40], [39, 198, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_popup_chat", 11, [[22, 1, 0, false, -326, [71], 72], [60, -327]], [0, "b62IujiHFIuI4Gg7ZAQM20", 1], [5, 358, 495], [-9.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "view", 26, [-329], [[61, 0, -328, [77]]], [0, "15pdNJTb5LZqF/ItLfhN66", 1], [5, 340, 380], [-6, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "content", 66, [[59, 1, 2, -330, [5, 340, 0]]], [0, "b6JfyU1KtCY5uXsl9RnVZA", 1], [5, 340, 0], [0, 0.5, 1], [0, 131, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "view", 32, [-332], [[118, 0, -331]], [0, "67ExJoEBJOip0kA4GEdCq5", 1], [5, 360, 270], [0, 0.5, 1], [0, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "rtChat", 68, [[116, false, "", 22, 345, 33, false, -333, 78]], [0, "cccAKjRMRIlr3OzZcQYGO3", 1], [5, 345, 33], [0, 0, 1], [-173, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "textbox_chat", 11, [27], [[19, 1, 0, -334, [81], 82]], [0, "b8ZxmjuR5DAYFxLaSeFFhU", 1], [5, 340, 60], [-12, -200.7, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "nodeRanks", 10, [-337], [[125, -336, -335]], [0, "9bAzSPo0hG3ZhM0q2uAGtt", 1], [490, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "bg_bxh", 71, [28], [[4, -338, [94], 95]], [0, "c6jC46oPdJDaXa+dPdhilz", 1], [5, 287, 459]], [12, "avatar", false, 12, [[11, 1, 0, -339, 86], [25, -340]], [0, "b7NvJHK19MdKhVU2JsVl9x", 1], [5, 37, 37], [-44.2, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [85, "lbMoney", 12, [-342], [-341], [0, "56alkXfQhKDIn8Im7ZpRp5", 1], [4, 4278255612], [5, 104.5, 40], [0, 0, 0.5], [-85, -8.5, 0, 0, 0, 0, 1, 1, 1, 1]], [71, "view", 28, [-344], [[61, 0, -343, [93]]], [0, "d0o7a+eftBt4f6NVrvz4Xx", 1], [5, 240, 350], [0, 0.5, 1]], [5, "bg", 3, [[6, 0, -345, [99], 100], [57, -346]], [0, "c5U4dkIglAHZ4+F+RdE3Px", 1], [5, 1561, 723]], [3, "table", 3, [-348], [[4, -347, [103], 104]], [0, "44Kb6lqUJAqrCjRbuR78K9", 1], [5, 1118, 490], [0, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [89, "lbStatus", 37, [[-349, [10, -350, [120]]], 1, 4], [0, "a8+pwRX19GCahYN9WZkYI3", 1], [5, 178.2, 24], [-2, 34.1, 0, 0, 0, 0, 1, 1, 1, 1]], [90, "lbPlayerStatus", false, 37, [[-351, [10, -352, [121]]], 1, 4], [0, "28Vw6dua9Gq6+5WzAFJg6E", 1], [5, 0, 18], [-2, -115, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Avatar", 4, [[22, 1, 0, false, -353, [122], 123], [25, -354]], [0, "abKKQNNFVMJ6wXjo6b1dkI", 1], [4, 4293322470], [5, 120, 120]], [43, "timeprogress", 4, [[-355, [34, 2, -357, -356]], 1, 4], [0, "4em+yD2HRMhpnq59p2uwSO", 1], [5, 100, 100]], [1, "lbChip", 38, [[2, "10.M", 18, false, 1, 1, -358, [130], 131], [35, -359]], [0, "cblSnGMbVCnohww2Vyi/KH", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbWin", false, 4, [[8, "+100.000", 20, false, 1, 1, -360, 139], [33, true, -361, [141], 140]], [0, "e9cdIyRgJK/JOtaHXwN/2z", 1], [5, 153.13, 25], [0, 0.5, 0.500000000000002], [0, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "chat", false, 4, [-362, -363], [0, "4adnTHhUhCS6eU3xYiNx5e", 1]], [3, "bubble", 84, [-365], [[11, 1, 0, -364, 144]], [0, "e3Uke/FxRD7YyH/SY8xKiu", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [76, "layoutCardGame", 5, [0, "abLmNDf3VP7pUwV6yrtCtw", 1], [5, 200, 100]], [7, "Avatar", 6, [[22, 1, 0, false, -366, [171], 172], [25, -367]], [0, "2eyhhs+slJAZw3DbofwPYf", 1], [4, 4293322470], [5, 90, 90]], [91, "timeprogress", false, 6, [[-368, [34, 2, -370, -369]], 1, 4], [0, "29g8R6w0xHDKfXvan/5OGF", 1], [5, 90, 90]], [1, "lbChip", 51, [[2, "10.M", 18, false, 1, 1, -371, [178], 179], [35, -372]], [0, "524VtW7q1NM5lfuK483NrE", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbWin", false, 6, [[8, "+100.000", 20, false, 1, 1, -373, 187], [33, true, -374, [189], 188]], [0, "e3ucaFtXBHCrZVbWN5h3jz", 1], [5, 153.13, 25], [0, 0.5, 0.500000000000002], [0, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "chat", false, 6, [-375, -376], [0, "42hD/sAuhKyoJCdKQgnl4A", 1]], [3, "bubble", 91, [-378], [[11, 1, 0, -377, 192]], [0, "bb8iygziNC5ojLPGfFtU6r", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Avatar", 7, [[22, 1, 0, false, -379, [195], 196], [25, -380]], [0, "205ghe5A9O2I+t5A2msKPf", 1], [4, 4293322470], [5, 90, 90]], [43, "timeprogress", 7, [[-381, [34, 2, -383, -382]], 1, 4], [0, "52z5dZdXtIGou1IHzuP9hV", 1], [5, 90, 90]], [1, "lbChip", 53, [[2, "10.M", 18, false, 1, 1, -384, [203], 204], [35, -385]], [0, "dbv/oxDvNBVImXgBWKtFn3", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbWin", 7, [[2, "+100.000", 20, false, 1, 1, -386, [212], 213], [33, true, -387, [215], 214]], [0, "e3TMquIJtGy4J3akfMCFNL", 1], [5, 153.13, 40], [0, 0.5, 0.500000000000002], [157, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "chat", false, 7, [-388, -389], [0, "83W2kxmAZA1bbRnrsj8O1C", 1]], [3, "bubble", 97, [-391], [[11, 1, 0, -390, 218]], [0, "88pnGekW9DGoxko/WrzT8I", 1], [5, 192, 65], [106.6, 29.9, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Avatar", 8, [[22, 1, 0, false, -392, [221], 222], [25, -393]], [0, "7enL6lwJhDlL1EGaBR40X+", 1], [4, 4293322470], [5, 90, 90]], [43, "timeprogress", 8, [[-394, [34, 2, -396, -395]], 1, 4], [0, "e11Ylh7wlNzqQW7wqjLcqb", 1], [5, 90, 90]], [1, "lbChip", 55, [[2, "10.M", 18, false, 1, 1, -397, [229], 230], [35, -398]], [0, "87+MOQDNhHFp4DYo0AYc04", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbWin", false, 8, [[8, "+100.000", 20, false, 1, 1, -399, 238], [33, true, -400, [240], 239]], [0, "f6sPLtxihOUZhQZAlb3gFc", 1], [5, 153.13, 25], [0, 0.5, 0.500000000000002], [0, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "chat", false, 8, [-401, -402], [0, "dajEnKO2ZG9oMZmgi4NYye", 1]], [3, "bubble", 103, [-404], [[11, 1, 0, -403, 243]], [0, "b4QOql0zdOmoUocP9/GfBK", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnBoLuot", 14, [-406], [-405], [0, "dfaqp2J0VOH6NWCULtXg1X", 1], [5, 150, 60], [75, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Background", 105, [[19, 1, 0, -407, [252], 253]], [0, "52m/oNLL1PWaEiOG2KPImJ", 1], [4, 4293322470], [5, 150, 60]], [18, "btnBoChon", 14, [-409], [-408], [0, "96PY44jtVDpLYp3S2CMzPT", 1], [5, 150, 60], [285, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Background", 107, [[19, 1, 0, -410, [254], 255]], [0, "10yD13oJRLPL2VxINGpiwG", 1], [4, 4293322470], [5, 150, 60]], [18, "btnXepBai", 14, [-412], [-411], [0, "73zDh/wt9BHoxDm/QZZ8et", 1], [5, 150, 60], [495, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Background", 109, [[19, 1, 0, -413, [256], 257]], [0, "6bup6X3HRMG4+7IQmWPNYj", 1], [4, 4293322470], [5, 150, 60]], [18, "btnDanhBai", 14, [-415], [-414], [0, "78d3nzxHdA0LQee97K3c/n", 1], [5, 150, 60], [705, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Background", 111, [[19, 1, 0, -416, [258], 259]], [0, "84eiby0wJNNrtoKYl1gKHz", 1], [4, 4293322470], [5, 150, 60]], [1, "name", 64, [[2, "TIẾN LÊN MIỀN NAM", 34, false, 1, 1, -417, [4], 5]], [0, "c7+ZDRY2JL+aITBAY5KNJA", 1], [5, 323.85, 40], [-12, 4.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 16, [[6, 0, -418, [8], 9]], [0, "65oUWJi/lPUqiGDjlt2mKk", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 16, [[2, "1K", 25, false, 1, 1, -419, [10], 11]], [0, "63f3qHgdlPv48Kt1JiAqfU", 1], [5, 46.09, 40]], [1, "chip_demo", 17, [[6, 0, -420, [15], 16]], [0, "1eulZrfwJPjbe2fNiPAjv7", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 17, [[2, "2K", 25, false, 1, 1, -421, [17], 18]], [0, "12Rv1vzJJO7bypP62cRESn", 1], [5, 54.69, 40]], [1, "chip_demo", 18, [[6, 0, -422, [22], 23]], [0, "b2eC9kka5LjJYfNJGvVc32", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 18, [[2, "5K", 25, false, 1, 1, -423, [24], 25]], [0, "9fjGzkOjdLwZgrCHocecLi", 1], [5, 56.25, 40]], [1, "chip_demo", 19, [[6, 0, -424, [29], 30]], [0, "b09gjlp7VDC6iTuRX43lJu", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 19, [[2, "10K", 25, false, 1, 1, -425, [31], 32]], [0, "e15InUta1BSK4aO8B4brjy", 1], [5, 73.44, 40]], [1, "chip_demo", 20, [[6, 0, -426, [36], 37]], [0, "65wPgZUh1EKrQHvUG+A/6m", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 20, [[2, "20K", 25, false, 1, 1, -427, [38], 39]], [0, "a488ThlMRNzJ6pzhFfGw2x", 1], [5, 82.03, 40]], [1, "chip_demo", 21, [[6, 0, -428, [43], 44]], [0, "5eqHh3OWlMYoY6X8g3TOeB", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 21, [[2, "50K", 25, false, 1, 1, -429, [45], 46]], [0, "4ebSxnAbJP7KneYQTNwkrz", 1], [5, 83.59, 40]], [1, "chip_demo", 22, [[6, 0, -430, [50], 51]], [0, "b0Rgj3s41BD7UAtO7vVqVh", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 22, [[2, "100K", 25, false, 1, 1, -431, [52], 53]], [0, "882aazQBhC+4+y+wUZpn1I", 1], [5, 100.78, 40]], [1, "chip_demo", 23, [[6, 0, -432, [57], 58]], [0, "c3+IZSF+xAdJQwMvGOAaE7", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 23, [[2, "200K", 25, false, 1, 1, -433, [59], 60]], [0, "e4fzFf019L9LQWOyu3C3nt", 1], [5, 109.38, 40]], [1, "chip_demo", 24, [[6, 0, -434, [64], 65]], [0, "b3+w0mCl5DfpN1OsLpLwHI", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbRoomValue", 24, [[2, "500K", 25, false, 1, 1, -435, [66], 67]], [0, "7e2ROwbl1G47OyBQ7+UUat", 1], [5, 110.94, 40]], [50, "temp", 26, [25], [0, "cduz4rH9lPnphdcEk3h/fL", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "rtChat", false, 25, [-436], [0, "36k+g5cltEDLMJ5QtOharJ", 1], [5, 345, 33], [0, 0, 0.5], [0, 16.5, 0, 0, 0, 0, 1, 1, 1, 1]], [115, false, "", 20, 345, 33, false, 133], [69, "lbSID", false, 15, [[105, "[TQ]", 18, 48, false, false, 1, 1, -437, 73]], [0, "5aHCW3pQFEa6D6laNSccyT", 1], [4, 4279026733], [5, 33.3, 21.6], [0, 0, 0.5]], [80, "lbNickName", 15, [-438], [0, "7fatO296VM67Zia0u0w47P", 1], [4, 4281523194], [5, 95.39, 60.48], [0, 0, 0.5]], [106, 19, 48, false, false, 1, 1, 136, [74]], [12, "V1", false, 15, [[44, -439, 75]], [0, "c4UdSsckJKRprHUcW2QTyW", 1], [5, 30, 28], [133.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbMessage", 15, [-440], [0, "2fm0rBOzVIZJPVnZ/VRvCE", 1], [5, 184, 24], [0, 0, 0.5], [95.39, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [107, 19, 48, false, false, 1, 1, 139, [76]], [119, false, 26, 67], [120, 15, 400, 26, 25, 141], [54, "BACKGROUND_SPRITE", 27, [-441], [0, "84FMKuZ6lNkpEPD5gvDKaD", 1], [5, 235, 30]], [93, 1, 0, 143, [79]], [41, "TEXT_LABEL", false, 27, [-442], [0, "0axGxleW9HpJqy4zgDZeMJ", 1], [5, 233, 30], [0, 0, 1], [-115.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [108, 20, 30, false, false, 1, 1, 145], [82, "PLACEHOLDER_LABEL", 27, [-443], [0, "beKhhyb8ZMNrEk5hMinexW", 1], [4, 4290493371], [5, 233, 30], [0, 0, 1], [-115.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [109, "<PERSON><PERSON>i dung tin nhắn ...", 20, 30, false, false, 1, 1, 147, [80]], [121, 255, 6, 27, [[9, "c9ea2HJ+4FBwJf8JdBQBbUQ", "editingReturn", 11]], 146, 148, 144], [1, "sprite", 33, [[6, 2, -444, [83], 84]], [0, "37WfzQB7VD1KbiazEEXB01", 1], [5, 82, 46], [-2.6, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [102, 1.1, false, 3, 33, [[9, "c9ea2HJ+4FBwJf8JdBQBbUQ", "sendChatClicked", 11]], [4, 4294967295], [4, 4294967295], 33], [41, "iconRank", false, 12, [-445], [0, "35v1ubSfFBm5E4WTDzhwj8", 1], [5, 36, 29], [0, 0, 0.5], [-113, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [94, 152], [55, "lbRank", 12, [-446], [0, "75rl4cK25Lg7hjYZ0WouzQ", 1], [5, 15, 40], [-108.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "0", 25, false, 1, 1, 154, [85]], [84, "lbSID", false, 12, [-447], [0, "a65PIkvLBB45ZMbMv9E1cK", 1], [4, 4280264453], [5, 32.73, 17], [0, 0, 0.5], [-86.1, 13.4, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "[TQ] ", 17, false, 1, 1, 156], [21, "lbNickName", 12, [-448], [0, "87HcHD719FTa/VcxCacqRe", 1], [5, 170, 17], [0, 0, 0.5], [-85, 13.4, 0, 0, 0, 0, 1, 1, 1, 1]], [110, "900MMMM99009009", 17, 17, false, false, 2, 158, [87]], [16, "chip_demo", false, 74, [[95, 0, -449, 88]], [0, "70+4EmQWRJArqpip3YZfzV", 1], [5, 25, 25], [0, 1, 0.5], [-0.9, -3.8, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "90.000.000", 20, false, 1, 1, 74, [89]], [72, "content", 75, [0, "2aYvv7cKRMhKIeMkKL/IjN", 1], [5, 240, 350], [0, 0.5, 1]], [62, false, 0.75, 0.23, null, null, 28, 162], [124, 5, 100, 28, 12, 163], [42, "musicBackground", 29, [-450], [0, "a3waudf2NAtIVfA6PO/tum", 1]], [127, 0.2, true, true, 165], [42, "chipBet", 29, [-451], [0, "58ee7mTsxGX641o0vN84K7", 1]], [63, true, 167], [42, "moveCard", 29, [-452], [0, "fdMtb7qNBKhJZfVojMXFBR", 1]], [63, true, 169], [1, "nameBC", 77, [[4, -453, [101], 102]], [0, "bd9GpLfcRHgYOE+oXxBmrF", 1], [5, 442, 65], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "table", 3, [[96, false, -454, [105]]], [0, "f3J9pmJodOS5bREjIaVJf1", 1], [5, 967, 573], [0, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [86, "bgBack", 35, [-455], [0, "31pqr0DlhJsaENGuNW90o/", 1], [4, 4293322470], [5, 36, 53], [0, 11.6, 0, 0, 0, 0, 1, 1, 1, 1]], [56, 2, false, 173, [106]], [87, "bgSound", 36, [-456], [0, "79CtnCB4ZC+Jp1Ki1u1JkU", 1], [4, 4293322470], [5, 81, 82]], [56, 2, false, 175, [107]], [15, "lbTable", 13, [[2, "Bàn", 17, false, 1, 1, -457, [108], 109]], [0, "05RdY9cJJDQqhVanQuN5bn", 1], [5, 31.02, 40], [0, 0, 0.5], [-64.9, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbTableValue", 13, [-458], [0, "ea458Y/75HdIZPTojsfseX", 1], [5, 39.1, 40], [0, 0, 0.5], [-9, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [23, ": 012", 17, false, 1, 1, 178, [110]], [15, "lbRoomID", 13, [[2, "Phòng", 17, false, 1, 1, -459, [111], 112]], [0, "0bI0BhEppJwads7ZNWrJSo", 1], [5, 50.58, 40], [0, 0, 0.5], [-64.2, 2.9, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbRoomIDValue", 13, [-460], [0, "45uaUSEOBH1pdS6JG79HFq", 1], [5, 53.98, 40], [0, 0, 0.5], [-9.1, 3.3, 0, 0, 0, 0, 1, 1, 1, 1]], [23, ": 1.000", 17, false, 1, 1, 181, [113]], [15, "lbSID", 13, [[2, "<PERSON><PERSON><PERSON>", 17, false, 1, 1, -461, [114], 115]], [0, "21aLWJI5dBA7BqGBxDCuJg", 1], [5, 45.05, 40], [0, 0, 0.5], [-64.8, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbSIDValue", 13, [-462], [0, "39TuHfFhtK5oepxHyOx4XC", 1], [5, 78.62, 40], [0, 0, 0.5], [-8.7, -17.8, 0, 0, 0, 0, 1, 1, 1, 1]], [23, ": #123456", 17, false, 1, 1, 184, [116]], [1, "bg_noti", 37, [[44, -463, 119]], [0, "82ciH34QJF9awy2C10kQ5U", 1], [5, 274, 49], [0, 32.8, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "CHO PHIEN MOI", 24, false, 1, 1, 78], [111, 18, false, 1, 1, 79], [45, 3, 0, 2, 0.5, 1, 81, [124], [0, 0.5, 0.5]], [5, "ava_sheld", 4, [[6, 0, -464, [125], 126]], [0, "a9BRlff5NI3poUC087Bmvp", 1], [5, 120, 120]], [26, "lbSID", false, 39, [[8, "[TQ]", 16, false, 1, 1, -465, 127]], [0, "322pPg0mRKWZUwqAt1l3NL", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lbName", 39, [[2, "Academy...", 18, false, 1, 1, -466, [128], 129]], [0, "d6x2U+ZepCIYizBEhLj9jP", 1], [4, 4284012543], [5, 89.8, 40]], [1, "nodeOut", 4, [[4, -467, [134], 135]], [0, "2d9RBQtwpPVL3RAFnghbvV", 1], [5, 36, 39], [54.4, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "notify", false, 4, [[29, 1, 0, -468]], [0, "f1Y0dQLAdHlZvUVhiq7gvP", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "lbResult", false, 0, 4, [[8, "<PERSON> đôi thông", 25, false, 1, 1, -469, 136]], [0, "bePoet16BKNbzHSXHAU4E9", 1], [5, 144.38, 25], [0, 0, 0.5], [19, -14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 4, [[36, 0, false, "tquy", -470, [137], 138]], [0, "b54UV4FQNMUrrnuXTqmHEC", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "emotion", 84, [[37, "1-waaaht", 0, false, "1-waaaht", -471, 142]], [0, "04MjLBDOFCJKii/rxBvA1V", 1], [5, 123, 110]], [7, "lbChat", 85, [[31, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -472, 143]], [0, "165s/k4ydJK7aDo7tTOo7o", 1], [4, 4278190080], [5, 172.4, 55]], [38, 4, 40, 41], [1, "lbNumberCard", 42, [[2, "13", 30, false, 1, 1, -473, [148], 149]], [0, "4bAKzaWXlGA5xg1vJ9yKwL", 1], [5, 35.25, 40], [-3.3, 2.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNumberCard", 45, [[2, "13", 30, false, 1, 1, -474, [155], 156]], [0, "91sIcZXZhCH6RCjPThh/cX", 1], [5, 35.25, 40], [-2.8, 2.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNumberCard", 48, [[2, "13", 30, false, 1, 1, -475, [162], 163]], [0, "18gD9Xwp1Duo9YWhZIEGpY", 1], [5, 35.25, 40], [-2.3, 2.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNumberCard", 40, [[8, "13", 18, false, 1, 1, -476, 169]], [0, "00YlL5UxJAwpQ+OmnF0h3a", 1], [5, 21.15, 18], [-1.2, 1.9, 0, 0, 0, 0, 1, 1, 1, 1]], [97, 3, 0, 2, 0.5, 1, 88, [0, 0.5, 0.5]], [5, "ava_sheld", 6, [[6, 0, -477, [173], 174]], [0, "3fw0oH04FAp5FeunpLe38d", 1], [5, 90, 90]], [26, "lbSID", false, 52, [[8, "[TQ]", 16, false, 1, 1, -478, 175]], [0, "d0BmCNHvFPTpnVLUIgY0sz", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lbName", 52, [[2, "Academy...", 18, false, 1, 1, -479, [176], 177]], [0, "f3J8oUlA1JtKGF5cTyhdKE", 1], [4, 4284012543], [5, 89.8, 40]], [1, "nodeOut", 6, [[4, -480, [182], 183]], [0, "d7fsCAHYJC9Yk4qwePsBVl", 1], [5, 36, 39], [4.6, 39.6, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbResult", false, 6, [[8, "<PERSON> đôi thông", 23, false, 1, 1, -481, 184]], [0, "3eg0Wcyq5HnJJOW/GWq0uS", 1], [5, 132.82, 23], [0, 0, 0.5], [105, -83.7, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "notify", false, 6, [[29, 1, 0, -482]], [0, "24rMUAovtJ/oUGN3G50Cnd", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 6, [[36, 0, false, "animation", -483, [185], 186]], [0, "19QJHXHc5GXIBpYhconv6z", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "emotion", 91, [[37, "1-waaaht", 0, false, "1-waaaht", -484, 190]], [0, "0dnhXRZOJAiLgpmp4yLGU2", 1], [5, 123, 110]], [7, "lbChat", 92, [[31, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -485, 191]], [0, "83fDTYWplBbY2GMuBy1XAl", 1], [4, 4278190080], [5, 172.4, 55]], [38, 6, 42, 43], [45, 3, 0, 2, 0.5, 1, 94, [197], [0, 0.5, 0.5]], [5, "ava_sheld", 7, [[6, 0, -486, [198], 199]], [0, "c2N8csb3ZGS78tBCTPH3rK", 1], [5, 90, 90]], [26, "lbSID", false, 54, [[8, "[TQ]", 16, false, 1, 1, -487, 200]], [0, "69ONUzkrJFH6z47R0B6c5I", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lbName", 54, [[2, "Academy...", 18, false, 1, 1, -488, [201], 202]], [0, "b1q7cJRXdJDqP4O4rveE/H", 1], [4, 4284012543], [5, 89.8, 40]], [1, "nodeOut", 7, [[4, -489, [207], 208]], [0, "47JvPnlAFDVpMwz+f9flMu", 1], [5, 36, 39], [0.2, 40.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbResult", false, 7, [[8, "<PERSON> đôi thông", 23, false, 1, 1, -490, 209]], [0, "aefRCqGCJArqmsq6U9V14I", 1], [5, 132.82, 23], [0, 1, 0.5], [-87, -135, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "notify", false, 7, [[29, 1, 0, -491]], [0, "95IDrvO8NIbLbkfM2CGiEh", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 7, [[36, 0, false, "animation", -492, [210], 211]], [0, "ceDDWPUGdK0oCSl/zmweVQ", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "emotion", 97, [[37, "1-waaaht", 0, false, "1-waaaht", -493, 216]], [0, "b9YOtN7JZDcbc7f+sAJ3qo", 1], [5, 123, 110]], [7, "lbChat", 98, [[31, "Ah", 30, 50, false, 1, 1, 2, -494, 217]], [0, "ed58PZTYZJCpxND6DOaYvX", 1], [4, 4278190080], [5, 172.4, 55]], [38, 7, 45, 46], [45, 3, 0, 2, 0.5, 1, 100, [223], [0, 0.5, 0.5]], [5, "ava_sheld", 8, [[6, 0, -495, [224], 225]], [0, "7eZZhm5UVMa4I44tCCJibF", 1], [5, 90, 90]], [26, "lbSID", false, 56, [[8, "[TQ]", 16, false, 1, 1, -496, 226]], [0, "262v97R/hIwYS7xer3doQH", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lbName", 56, [[2, "Academy...", 18, false, 1, 1, -497, [227], 228]], [0, "855lXWjsFAsK6zMBo06EB6", 1], [4, 4284012543], [5, 89.8, 40]], [1, "nodeOut", 8, [[4, -498, [233], 234]], [0, "07uKJZ/wNNKaw33texd9Di", 1], [5, 36, 39], [2.4, 38, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbResult", false, 8, [[8, "<PERSON> đôi thông", 23, false, 1, 1, -499, 235]], [0, "93WXlFgABAIq+4JVqhxQwt", 1], [5, 132.82, 23], [0, 1, 0.5], [-75, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "notify", false, 8, [[29, 1, 0, -500]], [0, "5c9THpNrdICZjzXIdjMB3y", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 8, [[36, 0, false, "animation", -501, [236], 237]], [0, "bdaTiawKpCJZYTytHKfZpP", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "emotion", 103, [[37, "1-waaaht", 0, false, "1-waaaht", -502, 241]], [0, "5bkEAPw9FByrlj2LpDxOVk", 1], [5, 123, 110]], [7, "lbChat", 104, [[31, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -503, 242]], [0, "239rjnTqNAKp5nDMp9leBf", 1], [4, 4278190080], [5, 172.4, 55]], [38, 8, 48, 49], [5, "lbSID", 57, [[8, "<PERSON><PERSON><PERSON> ký rời bàn", 20, false, 1, 1, -504, 246]], [0, "83mn8TIXdAbqHtrZf7vvOf", 1], [5, 144, 20]], [5, "Background", 58, [[98, 2, false, -505, [248], 249]], [0, "c0UDydDEpHNJJypLY2z8jt", 1], [5, 60, 61]], [30, 2, 105, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onBoLuot", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 106], [30, 2, 107, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onBoChon", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 108], [30, 2, 109, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onSortCard", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 110], [30, 2, 111, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onBanhBai", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 112], [88, "dealer_xd", false, 2, [-506], [0, "b8x0LYsr5H8K80iOjb6/Pw", 1], [5, 151.57, 182.34], [0, 185, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [129, "animation", 0, false, "animation", 243], [78, "parentChat", 2, [0, "79o1+Tah5KioMcpLWGGkzt", 1]], [53, "nodeNotify", false, 2, [-507], [0, "40/txBfmpLj7XrDTzNOefL", 1], [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "anim", 246, [-508], [0, "0bCWnhh/NBw5n7o8/JeIRk", 1], [5, 408.34, 328], [0, 80, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [130, 0, false, 247], [5, "bg_tex1", 31, [[99, 0, -509]], [0, "e4hph3WaNIqYNcTG52K+WC", 1], [5, 800, 80]], [54, "lbMessage", 31, [-510], [0, "98IiCbUYRAeYQZ4Qfsx37i", 1], [5, 700, 45]], [112, "Dat cuoc thanh cong ", 36, 50, false, false, 1, 1, 2, 250]], 0, [0, 28, 1, 0, 29, 245, 0, 30, 2, 0, 31, 10, 0, 0, 1, 0, 32, 248, 0, 0, 1, 0, 0, 1, 0, -1, 10, 0, -2, 2, 0, 33, 57, 0, 34, 174, 0, 35, 176, 0, 15, 86, 0, 0, 2, 0, 36, 86, 0, 15, 5, 0, 0, 2, 0, 37, 242, 0, 38, 241, 0, 39, 240, 0, 40, 239, 0, 41, 14, 0, 0, 2, 0, 42, 244, 0, -1, 199, 0, -2, 214, 0, -3, 225, 0, -4, 236, 0, 43, 188, 0, 44, 187, 0, 45, 179, 0, 46, 182, 0, 16, 185, 0, 0, 2, 0, -1, 29, 0, -2, 3, 0, -3, 243, 0, -4, 245, 0, -5, 246, 0, -6, 60, 0, -1, 76, 0, -2, 77, 0, -3, 172, 0, -4, 35, 0, -5, 36, 0, -6, 13, 0, -7, 37, 0, -8, 30, 0, -9, 5, 0, -10, 57, 0, -11, 58, 0, -12, 14, 0, 0, 4, 0, -2, 199, 0, -1, 80, 0, -2, 81, 0, -3, 190, 0, -4, 38, 0, -5, 193, 0, -6, 194, 0, -7, 195, 0, -8, 196, 0, -9, 83, 0, -10, 84, 0, -1, 41, 0, -2, 40, 0, -3, 42, 0, -4, 43, 0, -5, 45, 0, -6, 46, 0, -7, 48, 0, -8, 49, 0, 4, 5, 0, 4, 5, 0, -11, 86, 0, 0, 6, 0, -2, 214, 0, -1, 87, 0, -2, 88, 0, -3, 205, 0, -4, 51, 0, -5, 208, 0, -6, 209, 0, -7, 210, 0, -8, 211, 0, -9, 90, 0, -10, 91, 0, 0, 7, 0, -2, 225, 0, -1, 93, 0, -2, 94, 0, -3, 216, 0, -4, 53, 0, -5, 219, 0, -6, 220, 0, -7, 221, 0, -8, 222, 0, -9, 96, 0, -10, 97, 0, 0, 8, 0, -2, 236, 0, -1, 99, 0, -2, 100, 0, -3, 227, 0, -4, 55, 0, -5, 230, 0, -6, 231, 0, -7, 232, 0, -8, 233, 0, -9, 102, 0, -10, 103, 0, 0, 9, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, -6, 21, 0, -7, 22, 0, -8, 23, 0, -9, 24, 0, 0, 10, 0, -1, 61, 0, -2, 64, 0, -4, 11, 0, -5, 71, 0, -6, 34, 0, 47, 151, 0, 48, 149, 0, 49, 142, 0, 0, 11, 0, -1, 65, 0, -2, 26, 0, -3, 32, 0, -4, 70, 0, -5, 33, 0, 50, 161, 0, 51, 159, 0, 16, 157, 0, 52, 155, 0, 53, 153, 0, 0, 12, 0, -1, 152, 0, -2, 154, 0, -3, 73, 0, -4, 156, 0, -5, 158, 0, -6, 74, 0, 0, 13, 0, -1, 177, 0, -2, 178, 0, -3, 180, 0, -4, 181, 0, -5, 183, 0, -6, 184, 0, 0, 14, 0, -1, 59, 0, -2, 105, 0, -3, 107, 0, -4, 109, 0, -5, 111, 0, 0, 15, 0, -1, 135, 0, -2, 136, 0, -3, 138, 0, -4, 139, 0, 0, 16, 0, 3, 16, 0, 0, 16, 0, -1, 114, 0, -2, 115, 0, 0, 17, 0, 3, 17, 0, 0, 17, 0, -1, 116, 0, -2, 117, 0, 0, 18, 0, 3, 18, 0, 0, 18, 0, -1, 118, 0, -2, 119, 0, 0, 19, 0, 3, 19, 0, 0, 19, 0, -1, 120, 0, -2, 121, 0, 0, 20, 0, 3, 20, 0, 0, 20, 0, -1, 122, 0, -2, 123, 0, 0, 21, 0, 3, 21, 0, 0, 21, 0, -1, 124, 0, -2, 125, 0, 0, 22, 0, 3, 22, 0, 0, 22, 0, -1, 126, 0, -2, 127, 0, 0, 23, 0, 3, 23, 0, 0, 23, 0, -1, 128, 0, -2, 129, 0, 0, 24, 0, 3, 24, 0, 0, 24, 0, -1, 130, 0, -2, 131, 0, 0, 25, 0, 54, 134, 0, 17, 140, 0, 55, 137, 0, 0, 25, 0, -1, 133, 0, -1, 141, 0, -2, 142, 0, -1, 132, 0, -2, 66, 0, -1, 149, 0, -1, 143, 0, -2, 145, 0, -3, 147, 0, -1, 163, 0, -2, 164, 0, -2, 75, 0, 56, 168, 0, 57, 170, 0, 58, 166, 0, 0, 29, 0, -1, 165, 0, -2, 167, 0, -3, 169, 0, 0, 31, 0, -1, 249, 0, -2, 250, 0, 59, 69, 0, 0, 32, 0, 0, 32, 0, -1, 68, 0, -1, 151, 0, -1, 150, 0, 0, 34, 0, 3, 34, 0, 0, 34, 0, 3, 35, 0, 0, 35, 0, -1, 173, 0, 3, 36, 0, 0, 36, 0, -1, 175, 0, -1, 186, 0, -2, 78, 0, -3, 79, 0, 0, 38, 0, -1, 39, 0, -2, 82, 0, 0, 39, 0, -1, 191, 0, -2, 192, 0, 0, 40, 0, -1, 203, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, -1, 200, 0, 0, 43, 0, -1, 44, 0, 0, 44, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, -1, 201, 0, 0, 46, 0, -1, 47, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, -1, 202, 0, 0, 49, 0, -1, 50, 0, 0, 50, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, -1, 52, 0, -2, 89, 0, 0, 52, 0, -1, 206, 0, -2, 207, 0, 0, 53, 0, -1, 54, 0, -2, 95, 0, 0, 54, 0, -1, 217, 0, -2, 218, 0, 0, 55, 0, -1, 56, 0, -2, 101, 0, 0, 56, 0, -1, 228, 0, -2, 229, 0, 0, 57, 0, -1, 237, 0, 3, 58, 0, 0, 58, 0, -1, 238, 0, 3, 59, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 17, 251, 0, 0, 60, 0, -1, 62, 0, -2, 63, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, -1, 113, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, -1, 67, 0, 0, 67, 0, 0, 68, 0, -1, 69, 0, 0, 69, 0, 0, 70, 0, 60, 164, 0, 0, 71, 0, -1, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, -1, 161, 0, -1, 160, 0, 0, 75, 0, -1, 162, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, -1, 171, 0, -1, 187, 0, 0, 78, 0, -1, 188, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, -1, 189, 0, 12, 189, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, -1, 197, 0, -2, 85, 0, 0, 85, 0, -1, 198, 0, 0, 87, 0, 0, 87, 0, -1, 204, 0, 12, 204, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, -1, 212, 0, -2, 92, 0, 0, 92, 0, -1, 213, 0, 0, 93, 0, 0, 93, 0, -1, 215, 0, 12, 215, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, -1, 223, 0, -2, 98, 0, 0, 98, 0, -1, 224, 0, 0, 99, 0, 0, 99, 0, -1, 226, 0, 12, 226, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, -1, 234, 0, -2, 104, 0, 0, 104, 0, -1, 235, 0, -1, 239, 0, -1, 106, 0, 0, 106, 0, -1, 240, 0, -1, 108, 0, 0, 108, 0, -1, 241, 0, -1, 110, 0, 0, 110, 0, -1, 242, 0, -1, 112, 0, 0, 112, 0, 0, 113, 0, 0, 114, 0, 0, 115, 0, 0, 116, 0, 0, 117, 0, 0, 118, 0, 0, 119, 0, 0, 120, 0, 0, 121, 0, 0, 122, 0, 0, 123, 0, 0, 124, 0, 0, 125, 0, 0, 126, 0, 0, 127, 0, 0, 128, 0, 0, 129, 0, 0, 130, 0, 0, 131, 0, -1, 134, 0, 0, 135, 0, -1, 137, 0, 0, 138, 0, -1, 140, 0, -1, 144, 0, -1, 146, 0, -1, 148, 0, 0, 150, 0, -1, 153, 0, -1, 155, 0, -1, 157, 0, -1, 159, 0, 0, 160, 0, -1, 166, 0, -1, 168, 0, -1, 170, 0, 0, 171, 0, 0, 172, 0, -1, 174, 0, -1, 176, 0, 0, 177, 0, -1, 179, 0, 0, 180, 0, -1, 182, 0, 0, 183, 0, -1, 185, 0, 0, 186, 0, 0, 190, 0, 0, 191, 0, 0, 192, 0, 0, 193, 0, 0, 194, 0, 0, 195, 0, 0, 196, 0, 0, 197, 0, 0, 198, 0, 0, 200, 0, 0, 201, 0, 0, 202, 0, 0, 203, 0, 0, 205, 0, 0, 206, 0, 0, 207, 0, 0, 208, 0, 0, 209, 0, 0, 210, 0, 0, 211, 0, 0, 212, 0, 0, 213, 0, 0, 216, 0, 0, 217, 0, 0, 218, 0, 0, 219, 0, 0, 220, 0, 0, 221, 0, 0, 222, 0, 0, 223, 0, 0, 224, 0, 0, 227, 0, 0, 228, 0, 0, 229, 0, 0, 230, 0, 0, 231, 0, 0, 232, 0, 0, 233, 0, 0, 234, 0, 0, 235, 0, 0, 237, 0, 0, 238, 0, -1, 244, 0, -1, 247, 0, -1, 248, 0, 0, 249, 0, -1, 251, 0, 61, 1, 4, 4, 30, 6, 4, 30, 7, 4, 30, 8, 4, 30, 9, 4, 10, 12, 4, 28, 15, 4, 25, 25, 4, 132, 27, 4, 70, 28, 4, 72, 31, 4, 60, 510], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 134, 137, 140, 146, 148, 153, 155, 157, 159, 161, 166, 168, 170, 174, 176, 179, 182, 185, 187, 188, 189, 204, 215, 226, 239, 239, 239, 239, 240, 240, 240, 240, 241, 241, 241, 241, 242, 242, 242, 242, 244, 248, 251], [-1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, 2, -1, 1, -1, -1, 14, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -2, -3, -1, -1, 1, -1, 1, 18, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 1, 1, -1, -1, -1, 1, -1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, 2, 8, -1, 5, 2, 1, -1, -2, -1, -1, 2, -1, 1, 1, -1, -2, -1, 2, -1, 1, 1, -1, -2, -1, 2, -1, 1, 1, -1, -2, 2, 1, -1, 1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, 2, 8, -1, 5, 2, 1, -1, -2, -1, 1, -1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, -1, 2, 8, -1, 5, 2, 1, -1, -2, -1, 1, -1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, 2, 8, -1, 5, 2, 1, -1, -2, 2, 1, -1, 1, 7, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -2, 19, 20, 21, 22, -1, -2, -3, -4, -7, 23, 24, -1, -2, 25, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, 26, 27, 14, 2, 2, 2, 2, 1, 2, 2, 2, 2, 13, 13, 13, 1, 1, 2, 2, 2, 2, 2, 1, 1, 1, 1, 7, 9, 10, 11, 7, 9, 10, 11, 7, 9, 10, 11, 7, 9, 10, 11, 5, 5, 2], [0, 40, 0, 28, 0, 1, 0, 41, 0, 21, 0, 7, 0, 42, 0, 0, 5, 0, 2, 0, 3, 0, 0, 21, 0, 2, 0, 3, 0, 0, 5, 0, 2, 0, 3, 0, 0, 5, 0, 2, 0, 3, 0, 0, 5, 0, 2, 0, 3, 0, 0, 5, 0, 2, 0, 3, 0, 0, 5, 0, 2, 0, 3, 0, 0, 5, 0, 2, 0, 3, 0, 0, 43, 6, 0, 44, 0, 0, 29, 0, 0, 0, 45, 0, 46, 0, 47, 0, 21, 0, 30, 48, 49, 0, 0, 50, 0, 51, 52, 0, 53, 0, 54, 0, 55, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 31, 56, 32, 32, 0, 9, 0, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 7, 4, 4, 14, 1, 15, 16, 17, 57, 0, 1, 0, 18, 19, 22, 23, 0, 1, 0, 18, 19, 22, 23, 0, 1, 0, 18, 19, 22, 23, 1, 18, 0, 9, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 7, 4, 4, 14, 1, 15, 16, 17, 0, 9, 0, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 0, 7, 4, 4, 14, 1, 15, 16, 17, 0, 9, 0, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 7, 4, 4, 14, 1, 15, 16, 17, 1, 31, 0, 58, 33, 33, 0, 24, 0, 25, 0, 26, 0, 27, 59, 34, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 19, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 29, 35, 35, 6, 1, 30, 1, 1, 1, 1, 128, 129, 130, 28, 34, 1, 1, 1, 1, 1, 20, 20, 20, 20, 24, 36, 24, 36, 25, 37, 25, 37, 26, 38, 26, 38, 27, 39, 27, 39, 131, 8, 1]]