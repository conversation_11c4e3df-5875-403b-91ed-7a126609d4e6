[1, ["ecpdLyjvZBwrvm+cedCcQy", "e75jiE1LBGAawoYXG73m4e", "adw94Z+hpN57wutNivq8Q5", "8bo+zw+UdH6bz8bdhxNl6r", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "f3TtjOS/NBqbNBHzH/oGL3", "40iEs6WntFCKAZKl2VoxhX", "88Rwo8yjhPuLbVLSt1HafE", "8evSAQFN9NorJL2WFXV+6p", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30", "d9PAHIHORExqCgxxaxjheI"], ["_spriteFrame", "node", "_N$file", "root", "lbTotalWin", "lbTotalBet", "lbSessionID", "_N$target", "data", "_N$normalSprite", "_defaultClip", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_active", "_obj<PERSON><PERSON>s", "_prefab", "_parent", "_contentSize", "_components", "_trs", "_children", "_anchorPoint"], -1, 4, 1, 5, 9, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "node", "_materials", "_N$file"], -2, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 6, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["a32b2ibP/BAfZsc22Ob3J8Y", ["node", "lbSessionID", "lbTotalBet", "lbTotalWin", "spriteIcons"], 3, 1, 1, 1, 1, 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[4, 0, 1, 2], [0, 0, 5, 9, 4, 6, 8, 2], [3, 0, 1, 2, 3, 4, 2], [1, 0, 2, 3, 2], [0, 0, 5, 9, 7, 4, 6, 8, 2], [0, 0, 5, 7, 4, 6, 8, 2], [3, 0, 1, 2, 3, 4, 5, 6, 2], [1, 2, 3, 4, 1], [1, 0, 1, 2, 3, 4, 3], [2, 0, 1, 2, 3, 5, 6, 5], [0, 0, 5, 7, 4, 6, 10, 8, 2], [2, 0, 1, 2, 3, 5, 6, 7, 5], [7, 0, 2], [0, 0, 9, 7, 4, 2], [0, 0, 5, 9, 4, 6, 2], [0, 0, 1, 5, 7, 4, 6, 8, 3], [0, 0, 2, 5, 7, 4, 6, 8, 3], [0, 0, 3, 5, 7, 4, 6, 8, 3], [0, 0, 5, 7, 4, 6, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 1], [4, 1, 1], [10, 0, 1, 2, 3, 4, 4], [1, 0, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 6, 2], [5, 1, 7, 1], [11, 0, 1, 2, 3], [2, 0, 1, 2, 4, 3, 5, 6, 7, 6]], [[[[12, "dbSessionDetailView"], [13, "dbSessionDetailView", [-22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34], [[19, -2, [40, 41], 39], [20, -21, -20, -19, -18, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17]]], [21, -1]], [4, "slotsView", 1, [-36, -37, -38, -39, -40], [[22, 1, 1, 10, -35, [5, 840, 450]]], [0, "48wxb/UVlPApG85YqKpi28", 1], [5, 840, 450], [-29, -28, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [4, "BG_Panel_Info_half", 1, [-43], [[7, -42, [6], 7]], [0, "8f4Kg0bApAxqnawpbP8aor", -41], [5, 628, 641], [-234.935, 53.628, 0, 0, 0, 0, 1, 0.822, 0.822, 0.822]], [1, "1", 2, [-44, -45, -46], [0, "b2tGx6I7tB/LT6ajstpXSB", 1], [5, 160, 450], [-340, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 2, [-47, -48, -49], [0, "917zC1iYNP2IgiCA5ZlLqS", 1], [5, 160, 450], [-170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "3", 2, [-50, -51, -52], [0, "aaZ6gwcKhJrZ2XhqaPpBpn", 1], [5, 160, 450]], [1, "4", 2, [-53, -54, -55], [0, "4eoVw9l01IR4ez5OuQXeLx", 1], [5, 160, 450], [170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "5", 2, [-56, -57, -58], [0, "09mrigWCRMw5zEnMhBOOg/", 1], [5, 160, 450], [340, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnClose", 1, [-61], [[24, 3, -60, [[26, "a32b2ibP/BAfZsc22Ob3J8Y", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -59, 38]], [0, "19SYbYM6JGWYDvSQBIzE4d", 1], [5, 80, 80], [496.484, 238.285, 0, 0, 0, 0, 1, 1.448, 1.448, 0]], [15, "black", 100, 1, [[23, 0, -62, [0], 1], [25, -63, [4, 4292269782]]], [0, "743dvdW5RKloOYBNbFA0Ql", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "nen popup", false, 1, [[8, 0, false, -64, [2], 3]], [0, "67Cf/ekYBIhac7W7TG596S", 1], [5, 854, 497], [-24, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "BG_Panel_Info_half copy", 512, 3, [[7, -65, [4], 5]], [0, "6aQsYvizZAJrzke2DehTMc", 3], [5, 628, 641], [624.751, 0, 0, 0, 0, 0, 1, -1, 1, -1]], [5, "tex_bangthuong", 1, [[8, 2, false, -66, [8], 9]], [0, "caqD2UGKBBkY/+AjAlfJHL", 1], [5, 96, 38], [-37, 260, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "label", 1, [[27, "CUNG HỶ PHÁT TÀI - CHI TIẾT PHIÊN:", 22, false, 1, 1, -67, [10], 11]], [0, "73HYmx13NLXK5u4o6Ej9G0", 1], [5, 379.8, 40], [-102, 210, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbSessionID", 1, [-68], [0, "e2ezjeFCZN56rMf5gGTIVw", 1], [5, 114.95, 40], [0, 0, 0.5], [95, 210, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "#12345678", 22, false, 1, 15, [12]], [10, "label", 1, [[11, "Cược:", 22, false, 1, -69, [13], 14]], [0, "fd7tzgRr1Af7eXVru64PNh", 1], [5, 57.75, 40], [0, 0, 0.5], [-411, 176.2, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbTotalBet", 1, [-70], [0, "eebuCl+fBJkJ1Nc3EE/Ei7", 1], [5, 83.05, 40], [0, 0, 0.5], [-344, 176.2, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "250.000", 22, false, 1, 18, [15]], [10, "label", 1, [[11, "Thắng:", 22, false, 1, -71, [16], 17]], [0, "bdbNPldQ1Ger3136M9rpl1", 1], [5, 68.75, 40], [0, 0, 0.5], [-411, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbTotalWin", 1, [-72], [0, "38lBczmi9FG6bhaXe9tbEw", 1], [5, 83.05, 40], [0, 0, 0.5], [-333, 147.2, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "250.000", 22, false, 1, 21, [18]], [5, "background", 1, [[7, -73, [19], 20]], [0, "d6BUz9EAtCdZJkH7yOtQvL", 1], [5, 872, 478], [-29, -28, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "slot1", 4, [-74], [0, "3cFoRW2slJGKSSADbuTvl1", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 24, [-75], [0, "0eRHJHXdtJP4+pZzItz5eM", 1], [5, 151, 153]], [3, 2, 25, [21]], [1, "slot2", 4, [-76], [0, "816DTpXkBNspCkR6mYsyrq", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 27, [-77], [0, "b3a++8ZJxNyY/9NE2Ep2CI", 1], [5, 151, 153]], [3, 2, 28, [22]], [1, "slot3", 4, [-78], [0, "3dK677QkhGZ5Lh/WOEugSk", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 30, [-79], [0, "39wqw07JFDr74YuFH8vo2k", 1], [5, 151, 153]], [3, 2, 31, [23]], [1, "slot1", 5, [-80], [0, "7eYQfw+x9KyaVZFP9IzdAO", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 33, [-81], [0, "a2TBCSsIBFx6TNOxbZllbG", 1], [5, 151, 153]], [3, 2, 34, [24]], [1, "slot2", 5, [-82], [0, "1dI3xS5jVKWosY9vgxTylj", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 36, [-83], [0, "a6gyKOXwdIp5rViAfapuDv", 1], [5, 151, 153]], [3, 2, 37, [25]], [1, "slot3", 5, [-84], [0, "91Czg3MSpKNLCeq1hDuYBt", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 39, [-85], [0, "b4W/GRf0xPsJrK0qdMczEQ", 1], [5, 151, 153]], [3, 2, 40, [26]], [1, "slot1", 6, [-86], [0, "394LJilkZFXo5po8NYfRZl", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 42, [-87], [0, "89psbnzqJK6b/pb4aLxnhu", 1], [5, 151, 153]], [3, 2, 43, [27]], [1, "slot2", 6, [-88], [0, "17ANSXpmxOz7pq1L+VDLMk", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 45, [-89], [0, "7a+vqqtBhLJqI4l2GdkY7r", 1], [5, 151, 153]], [3, 2, 46, [28]], [1, "slot3", 6, [-90], [0, "ebD9wsvIlNvK02cODvQhAh", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 48, [-91], [0, "0aUSojTUtE15ds6jye7Iij", 1], [5, 151, 153]], [3, 2, 49, [29]], [1, "slot1", 7, [-92], [0, "87JAxaV6VPJ4CN0KrNnN7t", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 51, [-93], [0, "04RBeC/xFNtayXjHNdYuVM", 1], [5, 151, 153]], [3, 2, 52, [30]], [1, "slot2", 7, [-94], [0, "abL55a8JJE+bQy9t4pLM+S", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 54, [-95], [0, "1dFKDtOiBBR6SLj1NtqRW2", 1], [5, 151, 153]], [3, 2, 55, [31]], [1, "slot3", 7, [-96], [0, "59QRQTJLBDoIW3sX0GEwB1", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 57, [-97], [0, "d1bPBbfllH4IAxzhRYyf6U", 1], [5, 151, 153]], [3, 2, 58, [32]], [1, "slot1", 8, [-98], [0, "1f85IMSZhLrokTnRlb06/9", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 60, [-99], [0, "07NmdBJJtCVpMGxEkRdy0A", 1], [5, 151, 153]], [3, 2, 61, [33]], [1, "slot2", 8, [-100], [0, "d7K62uiPZJmrAEvuNIhht9", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 63, [-101], [0, "7aWMXrHlpJm6whSEg1RqYT", 1], [5, 151, 153]], [3, 2, 64, [34]], [1, "slot3", 8, [-102], [0, "3cuOpwjyRPq5dCa1MlM0Dm", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 66, [-103], [0, "eaQH9aUBRLW4Gq8M0Rk3v9", 1], [5, 151, 153]], [3, 2, 67, [35]], [18, "sprite", 9, [[8, 2, false, -104, [36], 37]], [0, "4cxKx1E2BPToD38nY/OS/5", 1], [5, 51, 36]]], 0, [0, 3, 1, 0, 1, 1, 0, -1, 26, 0, -2, 35, 0, -3, 44, 0, -4, 53, 0, -5, 62, 0, -6, 29, 0, -7, 38, 0, -8, 47, 0, -9, 56, 0, -10, 65, 0, -11, 32, 0, -12, 41, 0, -13, 50, 0, -14, 59, 0, -15, 68, 0, 4, 22, 0, 5, 19, 0, 6, 16, 0, 1, 1, 0, -1, 10, 0, -2, 11, 0, -3, 3, 0, -4, 13, 0, -5, 14, 0, -6, 15, 0, -7, 17, 0, -8, 18, 0, -9, 20, 0, -10, 21, 0, -11, 23, 0, -12, 2, 0, -13, 9, 0, 1, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, 3, 3, 0, 1, 3, 0, -1, 12, 0, -1, 24, 0, -2, 27, 0, -3, 30, 0, -1, 33, 0, -2, 36, 0, -3, 39, 0, -1, 42, 0, -2, 45, 0, -3, 48, 0, -1, 51, 0, -2, 54, 0, -3, 57, 0, -1, 60, 0, -2, 63, 0, -3, 66, 0, 7, 9, 0, 1, 9, 0, -1, 69, 0, 1, 10, 0, 1, 10, 0, 1, 11, 0, 1, 12, 0, 1, 13, 0, 1, 14, 0, -1, 16, 0, 1, 17, 0, -1, 19, 0, 1, 20, 0, -1, 22, 0, 1, 23, 0, -1, 25, 0, -1, 26, 0, -1, 28, 0, -1, 29, 0, -1, 31, 0, -1, 32, 0, -1, 34, 0, -1, 35, 0, -1, 37, 0, -1, 38, 0, -1, 40, 0, -1, 41, 0, -1, 43, 0, -1, 44, 0, -1, 46, 0, -1, 47, 0, -1, 49, 0, -1, 50, 0, -1, 52, 0, -1, 53, 0, -1, 55, 0, -1, 56, 0, -1, 58, 0, -1, 59, 0, -1, 61, 0, -1, 62, 0, -1, 64, 0, -1, 65, 0, -1, 67, 0, -1, 68, 0, 1, 69, 0, 8, 1, 104], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 19, 22, 26, 29, 32, 35, 38, 41, 44, 47, 50, 53, 56, 59, 62, 65, 68], [-1, 0, -1, 0, -1, 0, -1, 0, -1, 0, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 0, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 0, 9, 10, -1, -2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 5, 0, 6, 0, 3, 0, 3, 0, 7, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 10, 4, 4, 11, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[{"name": "icon_wild", "rect": [2, 2, 148, 150], "offset": [0.5, -0.5], "originalSize": [151, 153], "capInsets": [0, 0, 0, 0]}], [6], 0, [0], [11], [12]]]]