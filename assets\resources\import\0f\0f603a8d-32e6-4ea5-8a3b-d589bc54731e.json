[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "d82n49/IVAvIEqsa0xvvk0", "90Ss2Yf1lLHYPvA9KDgBaE", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "7daFLwLYNNmZ9x0849nYBc", "c25Leu0BdNDphgb/Hp9jw/", "1ewsTTeZRBbL9DYfWsJfc7", "c1y3UL3AVHoqWPxPdQzt/K", "951XXymkBM9YRsGEWVKOSD", "24xd2Xl+xHVZeWwPN10Wzf", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_defaultClip", "root", "miniPokerTopListView", "lbDesc", "lbWin", "lbBet", "lbNickName", "lbSID", "lbTime", "_N$target", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 1, 5, 2, 7, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 1, 1, 2, 4, 5, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["d88a7AsGeFLk7jtNbbuP6f3", ["node", "lbTime", "lbSID", "lbNickName", "lbBet", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["c1b59tmvLdASZ5k7UIBTw9R", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["550a3LIF35I4YE38c3nOZIM", ["node", "miniPokerTopListView"], 3, 1, 1]], [[8, 0, 1, 2], [0, 0, 4, 3, 2, 5, 7, 2], [3, 0, 4, 5, 6, 2], [1, 0, 1, 4, 5, 2, 3, 6, 7, 8, 9, 10, 9], [1, 0, 1, 4, 5, 2, 3, 6, 7, 8, 9, 9], [0, 0, 4, 6, 3, 2, 5, 7, 2], [2, 0, 2, 3, 4, 7, 5, 6, 2], [0, 0, 4, 6, 3, 2, 5, 2], [6, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 1, 4, 3, 2, 5, 7, 3], [0, 0, 4, 3, 2, 5, 2], [0, 0, 4, 6, 2, 7, 2], [0, 0, 6, 3, 2, 5, 2], [0, 0, 4, 3, 2, 5, 8, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 2, 3, 4, 5, 6, 2], [2, 0, 1, 2, 3, 4, 7, 5, 6, 3], [2, 0, 2, 3, 4, 5, 2], [3, 2, 0, 1, 4, 5, 6, 4], [3, 0, 1, 4, 5, 6, 3], [3, 3, 2, 0, 1, 4, 5, 6, 5], [4, 1, 2, 1], [4, 0, 1, 3, 4, 5, 6, 2], [9, 0, 1, 2, 3], [1, 0, 1, 4, 5, 2, 3, 6, 8, 8], [1, 0, 1, 4, 5, 2, 3, 6, 8, 9, 8], [1, 0, 1, 2, 3, 8, 9, 10, 5], [5, 0, 1, 2, 5, 6, 4], [5, 3, 0, 1, 4, 5, 6, 5], [10, 0, 1, 2, 3, 4, 5, 6, 1], [11, 0, 1, 1], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 3, 4, 5, 4], [14, 0, 1], [15, 0, 1, 2, 1], [16, 0, 1, 1]], [[8, "minipokerTopView"], [9, "minipokerTopView", [-5, -6, -7, -8, -9, -10], [[35, -2, [39, 40], 38], [36, -4, -3]], [0, "24LoTNQThH84u0j00dD+zj", -1]], [7, "nen popup", 1, [-12, -13, -14, -15, -16, -17], [[19, 1, 0, false, -11, [14], 15]], [0, "0eoLUP6I5EY5fL0R2xuw36", 1], [5, 1084, 618]], [13, "<PERSON><PERSON>", [-25, -26, -27, -28, -29], [[30, -24, -23, -22, -21, -20, -19, -18]], [0, "f7qZKRkDhK4aA3KSy/w5Hs", 1], [5, 994, 50]], [5, "title", 1, [-31, -32, -33, -34, -35], [[21, false, 1, 0, false, -30, [28], 29]], [0, "3clrdreh9MaaMtywU1U/K0", 1], [5, 994, 50], [0, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", 1, [-39, -40], [[-36, -37, [34, -38]], 1, 1, 4], [0, "572jSz+xhJSaDw+taLs3OJ", 1], [5, 1050, 465], [0, -68, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnClose", 1, [-43], [[23, 3, -42, [[24, "550a3LIF35I4YE38c3nOZIM", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -41]], [0, "bcocruUdZMCbrWfnAx80ZA", 1], [5, 80, 80], [506.4, 288.1, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 3, [-45, -46], [[28, 1, 1, 5, -44, [5, 104.5, 50]]], [0, "d6CGm0XNxGXYQODBqVjovE", 1], [5, 104.5, 50], [-156, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "black", 100, 1, [[2, 0, -47, [0], 1], [22, -48, [4, 4292269782]]], [0, "c3X3bSUe9DQL4FgyWebmrz", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "view", 5, [-50], [[31, -49, [35]]], [0, "076SJmP89MDa4aieApJ3hg", 1], [5, 1050, 465]], [14, "content", 9, [[29, false, 1, 2, 10, -51, [5, 1000, 0]]], [0, "9048yKPdVGN6O6TqXnxmhC", 1], [5, 1000, 0], [0, 0.5, 1], [0, 227, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgcontent", 2, [[2, 0, -52, [2], 3]], [0, "2dHvlcXvtIubp+DciEZ05s", 1], [5, 1050, 500], [0, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line1", 2, [[2, 0, -53, [4], 5]], [0, "b0AGSuR8lKnpa0wnshig9D", 1], [5, 1000, 16], [0, 171, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line2", 2, [[2, 0, -54, [6], 7]], [0, "4aJddAdoJC0KBiteTTIya7", 1], [5, 25, 500], [-284, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line3", 2, [[2, 0, -55, [8], 9]], [0, "230Qmkee9MgqcjTcUaJd5b", 1], [5, 25, 500], [-38, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line4", 2, [[2, 0, -56, [10], 11]], [0, "f0La17Pr1JaaMURSgcm2Cz", 1], [5, 25, 500], [144, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line5", 2, [[2, 0, -57, [12], 13]], [0, "f0HNQyDO9Bl63LFx2T87Ai", 1], [5, 25, 500], [316, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "sprite", 6, [[20, 2, false, -58, [16], 17]], [0, "0e7SLnQl9NopMP7NBZDY4V", 1], [5, 69, 36]], [1, "lbTime", 4, [[3, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, -59, [18], 19]], [0, "71XjbhLbZCeZ9SuGXpyDIF", 1], [5, 200, 38], [-393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 4, [[3, "TÊN NHÂN VẬT", 22, 50, false, false, 1, 1, 1, -60, [20], 21]], [0, "d58/Fe6lFNuJlv0VsOk8cp", 1], [5, 200, 38], [-156, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 4, [[3, "MỨC ĐẶT", 22, 50, false, false, 1, 1, 1, -61, [22], 23]], [0, "93ZoBh1Z5DfKIy5W62A5UP", 1], [5, 200, 38], [44, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 4, [[3, "THẮNG", 22, 50, false, false, 1, 1, 1, -62, [24], 25]], [0, "31zGpjMD5F84QK4WPhq0DG", 1], [5, 200, 38], [218, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 4, [[3, "LOẠI", 22, 50, false, false, 1, 1, 1, -63, [26], 27]], [0, "8dRzDGLjlNuI8w5EQCG0aM", 1], [5, 200, 38], [404, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "temp", 5, [3], [0, "e1Xj2WfNlETpFb4E+3li2k", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTime", 3, [-64], [0, "d7WzfY8npH8qTYWCvsI/AR", 1], [5, 200, 38], [-393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, 24, [30]], [17, "lbSID", false, 7, [-65], [0, "a8Hnfoe4RBT7wYRDX77Kce", 1], [4, 4279026733], [5, 40.7, 26.4], [-54.74999999999999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "[TQ]", 22, 48, false, false, 1, 1, 26], [18, "lbNickName", 7, [-66], [0, "41pgXIFX5JeqHkLEHNmfv6", 1], [5, 104.5, 26.4]], [26, "<PERSON><PERSON><PERSON>", 22, 48, false, false, 1, 1, 28, [31]], [6, "lbBet", 3, [-67], [0, "63g2AdEG9BVZrfq6OCkyWG", 1], [4, 4278315513], [5, 200, 38], [44, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "TIỀN ĐẶT", 22, 50, false, false, 1, 1, 1, 30, [32]], [6, "lbWin", 3, [-68], [0, "75UkFQ1rNM9LVDja5gzshL", 1], [4, 4278315513], [5, 200, 38], [218, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "KẾT QUẢ", 22, 50, false, false, 1, 1, 1, 32, [33]], [6, "lbDesc", 3, [-69], [0, "2avPsz6v1FDITmWy66nGm8", 1], [4, 4278315513], [5, 200, 38], [404, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "TRẢ LẠI", 22, 50, false, false, 1, 1, 1, 34, [34]], [32, false, 0.75, 0.23, null, null, 5, 10], [33, 20, 10, 400, 5, 3, 36], [1, "title", 1, [[27, "XẾP HẠNG MINI POKER", 26, false, 1, -70, [36], 37]], [0, "6bHFUa8oFNXZAO1PtBuDAB", 1], [5, 488.31, 32.5], [0, 298, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 37, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 6, 0, -4, 4, 0, -5, 5, 0, -6, 38, 0, 0, 2, 0, -1, 11, 0, -2, 12, 0, -3, 13, 0, -4, 14, 0, -5, 15, 0, -6, 16, 0, 6, 35, 0, 7, 33, 0, 8, 31, 0, 9, 29, 0, 10, 27, 0, 11, 25, 0, 0, 3, 0, -1, 24, 0, -2, 7, 0, -3, 30, 0, -4, 32, 0, -5, 34, 0, 0, 4, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 21, 0, -5, 22, 0, -1, 36, 0, -2, 37, 0, 0, 5, 0, -1, 23, 0, -2, 9, 0, 12, 6, 0, 0, 6, 0, -1, 17, 0, 0, 7, 0, -1, 26, 0, -2, 28, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, -1, 25, 0, -1, 27, 0, -1, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, 0, 38, 0, 13, 1, 3, 14, 23, 70], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 27, 29, 31, 33, 35], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, -1, -1, -1, -1, -1, 1, 3, -1, -2, 1, 1, 1, 1, 1, 1], [0, 5, 0, 6, 0, 7, 0, 3, 0, 3, 0, 3, 0, 3, 0, 8, 0, 9, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 10, 0, 0, 0, 0, 0, 0, 0, 11, 4, 4, 12, 1, 1, 1, 1, 1, 1]]