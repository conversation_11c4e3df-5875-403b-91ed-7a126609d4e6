[1, ["ecpdLyjvZBwrvm+cedCcQy", "c7NEvMzpdJLL2QqEd7wquk", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "e8IBvrERRE2LaF0HBSj8t2", "64Z6GjmBFBkJwpFnbYxlQW", "10GVzfS+pKHb5D8hBKK4Ay", "a9VpD0DP5LJYQPXITZq+uj", "a6BLW8ML9DWqqQhW7u/S55", "31i1IMhRRIjqTqhWc9ovtj", "5fMtiDRalHlK6fXQYtsqq/", "d4xPcPQZNFRacXxr5KXwsO", "9d+PxZkqRKJ7i9c7u2FTVj", "2cWB/vWPRHja3uQTinHH30", "51PYD25hRHZbG17qPTtV1n", "d1lSgb6MRH96ulREQhAYTy", "95lrip/aZJLbAPnBeJ4BiA"], ["node", "_N$file", "_spriteFrame", "_textureSetter", "lbBet", "lbTime", "root", "historyListView", "spriteDiceBet", "spriteDice3", "spriteDice2", "spriteDice1", "lbWin", "lbSession", "lbSessionID", "_N$target", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["b22bank0CtMTLPf3q6tAqKY", ["node", "historyListView"], 3, 1, 1], ["68169Gz/XhFvLKzSv2R3VTm", ["node", "lbSession", "lbTime", "lbBet", "lbWin", "spriteDice1", "spriteDice2", "spriteDice3", "spriteDiceBet"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 5, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["40e3bPIF7lJzr1GFaohNoPK", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[5, 0, 1, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 5, 6, 2, 3, 4, 7, 8, 9, 9], [0, 0, 5, 3, 2, 4, 7, 2], [1, 1, 0, 4, 5, 3], [2, 0, 1, 2, 3, 7, 4, 5, 2], [4, 0, 1, 5, 6, 2, 3, 4, 7, 8, 9, 10, 9], [0, 0, 5, 6, 3, 2, 4, 7, 2], [1, 0, 4, 5, 6, 2], [6, 1, 6, 1], [7, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [0, 0, 5, 3, 2, 4, 2], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 6, 2, 7, 2], [0, 0, 5, 2, 4, 8, 7, 2], [2, 0, 1, 6, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 8, 5, 2], [8, 0, 1, 2, 1], [9, 0, 1, 1], [5, 1, 1], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [1, 3, 1, 0, 2, 4, 5, 6, 5], [1, 1, 0, 2, 4, 5, 6, 4], [1, 0, 2, 4, 5, 6, 3], [11, 0, 1, 2, 3, 4, 5, 1], [6, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3], [13, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 8, 9, 10, 6], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 3, 4, 5, 4]], [[[{"name": "sb_deer", "rect": [0, 0, 88, 88], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [5]], [[{"name": "sb_gourd", "rect": [0, 0, 88, 88], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [6]], [[[10, "BauCuaHistoryView"], [11, "BauCuaHistoryView", [-5, -6, -7, -8, -9, -10, -11], [[20, -2, [31, 32], 30], [21, -4, -3]], [22, -1]], [12, "<PERSON><PERSON>", [-21, -22, -23, -24, -25, -26, -27, -28], [[23, -20, -19, -18, -17, -16, -15, -14, -13, -12]], [0, "d5YZl94/FKbYp3uTVcy7Hk", 1], [5, 994, 62]], [7, "title", 1, [-34, -35, -36, -37, -38, -39], [[24, false, 1, 0, false, -29, [19], 20], [27, -33, -32, -31, -30, [4, **********], [4, **********]]], [0, "f8RbxV+nhP8LdGGAJVp2hU", 1], [5, 994, 50], [0, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scrollview", 1, [-42, -43], [-40, -41], [0, "27zxOHxBZByJU31x16Si/c", 1], [5, 1050, 440], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnClose", 1, [-46], [[28, 3, -45, [[29, "b22bank0CtMTLPf3q6tAqKY", "closeClicked", 1]], [4, **********], [4, **********], -44]], [0, "e571kM6whIbr4ks4khlfLl", 1], [5, 80, 80], [507.763, 284.221, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "black", 100, 1, [[8, 0, -47, [0], 1], [9, -48, [4, **********]]], [0, "565dwnQ11LJ6V7ltWiZGWO", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "nen popup", 1, [[25, 1, 0, false, -49, [2], 3], [9, -50, [4, **********]]], [0, "7bJfUFD+JI1rahkrLuIwGt", 1], [5, 1084, 618]], [15, "view", 4, [-52], [[30, 0, -51, [29]]], [0, "f45tOXwKpPP6H4ulbxODnN", 1], [5, 1050, 440]], [3, "New Label", 1, [[31, "LỊCH SỬ BẦU CUA", 30, false, 1, 1, -53, [4], 5]], [0, "19g9M+lT5GU43fqeSoaOq7", 1], [5, 435.94, 40], [0, 307.313, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg_content", 1, [[8, 0, -54, [6], 7]], [0, "83Rgd22mtICpW8kkl++KSP", 1], [5, 1063, 520], [0, -34.057, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 5, [[26, 0, false, -55, [8], 9]], [0, "5bebeNi+RFupwweTzX3GTt", 1], [5, 80, 50], [-8.854, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbSession", 3, [-56], [0, "dc/ocMojxMcYhc01XwE1eg", 1], [5, 150, 30], [-436, 0.308, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHIÊN", 20, 48, false, false, 1, 1, 1, 12, [10]], [1, "lbTime", 3, [-57], [0, "15wlqsS3NLpZyrbGQ+o3lZ", 1], [5, 200, 38], [-265.3, 0.308, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THỜI GIAN", 20, 48, false, false, 1, 1, 1, 14, [11]], [1, "lbSide", 3, [-58], [0, "27lS1e3XtDCL3Nxqx+jgUA", 1], [5, 100, 38], [-84, 0.308, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ĐẶT CỬA", 20, 48, false, false, 1, 1, 1, 16, [12]], [3, "lbResult", 3, [[6, "KẾT QUẢ", 20, 48, false, false, 1, 1, 1, -59, [13], 14]], [0, "5bWjPa4vtH1YcDhpv0qoIK", 1], [5, 200, 38], [89, 0.308, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbBet", 3, [[6, "TỔNG ĐẶT", 20, 48, false, false, 1, 1, 1, -60, [15], 16]], [0, "399QhW8vZAxaJUhuLA6Z0M", 1], [5, 200, 38], [256.9, 0.308, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbTotalWin", 3, [[6, "THỰC NHẬN", 20, 48, false, false, 1, 1, 1, -61, [17], 18]], [0, "fcvLgpdnVAIrX6Rf0KqWzM", 1], [5, 200, 38], [412, 0.308, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "temp", 4, [2], [0, "7cmfizqS1JSZCGvAmmiNKu", 1], [0, 20000, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSession", 2, [-62], [0, "47sPpN0KdCBKz8i1+EtS1w", 1], [4, 4284344318], [5, 120, 30], [-435, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "#999", 22, 48, false, false, 1, 1, 2, 22, [21]], [19, "lbTime", 2, [-63], [0, "2fZ9IPh/xMoKImT0xtrtoW", 1], [5, 200, 38], [0, 0, 0.5], [-365, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "14-11-2019 15:50", 22, 48, false, false, 1, 1, 2, 24, [22]], [1, "diceBet", 2, [-64], [0, "89XEf7EZ5CA5I6n53wO3kY", 1], [5, 40, 36], [-80.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 1, 0, 26, [23]], [1, "dice1", 2, [-65], [0, "b5ThFEDV9P1IpJLygQHfan", 1], [5, 40, 40], [40.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 1, 0, 28, [24]], [1, "dice2", 2, [-66], [0, "295yzN2xRFbbM4Z8YH7E8r", 1], [5, 40, 40], [94.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 1, 0, 30, [25]], [1, "dice3", 2, [-67], [0, "1bWh2NPZpAsZNURPbdA3YM", 1], [5, 40, 40], [142.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, 1, 0, 32, [26]], [5, "lbBet", 2, [-68], [0, "38ssqn52ZOP573uR0AVYKR", 1], [4, 4282969994], [5, 200, 38], [256.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "1000.000", 22, 48, false, false, 1, 1, 1, 34, [27]], [5, "lbWin", 2, [-69], [0, "b6zEmQCNhLPJe4cp57JDtT", 1], [4, 4284344318], [5, 200, 38], [412, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "100.000", 22, 48, false, false, 1, 1, 1, 36, [28]], [17, "content", 8, [0, "06sTzhPGxChanLxWG0WdGo", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [32, false, 0.75, 0.23, null, null, 4, 38], [33, 20, 10, 400, 4, 2, 39]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 40, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 9, 0, -4, 10, 0, -5, 5, 0, -6, 3, 0, -7, 4, 0, 8, 27, 0, 9, 33, 0, 10, 31, 0, 11, 29, 0, 12, 37, 0, 4, 35, 0, 5, 25, 0, 13, 23, 0, 0, 2, 0, -1, 22, 0, -2, 24, 0, -3, 26, 0, -4, 28, 0, -5, 30, 0, -6, 32, 0, -7, 34, 0, -8, 36, 0, 0, 3, 0, 4, 17, 0, 5, 15, 0, 14, 13, 0, 0, 3, 0, -1, 12, 0, -2, 14, 0, -3, 16, 0, -4, 18, 0, -5, 19, 0, -6, 20, 0, -1, 39, 0, -2, 40, 0, -1, 21, 0, -2, 8, 0, 15, 5, 0, 0, 5, 0, -1, 11, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 38, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, -1, 15, 0, -1, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, -1, 23, 0, -1, 25, 0, -1, 27, 0, -1, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, -1, 37, 0, 16, 1, 2, 17, 21, 69], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 15, 17, 23, 25, 27, 29, 31, 33, 35, 37], [-1, 2, -1, 2, -1, 1, -1, 2, -1, 2, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 18, -1, -2, 1, 1, 1, 1, 1, 2, 2, 2, 2, 1, 1], [0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 13, 1, 1, 1, 2, 2, 4, 4, 14, 15, 2, 2]], [[{"name": "sb_crab", "rect": [0, 0, 88, 88], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [16]]]]