[1, ["c01T2xn5ZNHL0VinfJWghz", "c1echIwgNAKKaQFuK3mAKf", "3aHL+1SUZMg6w8GgMWopjo", "07x94IMulBr7mWlgIbYsZP", "5911oI49dBF6kGVjoKIxBh", "33i4YlkstBS7iP1jgSTOyt", "62vO2j3mZJG5d306/Sbm1N", "14FazyMvBArLfOkE3BgJDm", "91WMiTXFBOk7CHVM/rfIxo", "25RYxwJV1EDYPRNnpOvmO/", "b8sHqcAGNN0Lap45itlOZQ", "c4mTuSmG5LUKqUwWHcadHk", "959R64DItPuqGu3Ha+dQUu", "a8gLK1CJRMNYnAvESV+CkO", "caNkuZXKlAVqNhU4DopotC", "64uOEn4kRNlKYj5shh+Y/Q", "b95XiQ4/NENJ/+Jjs+lgQp", "04gB5bTzNPPYHVKMfV6NXV", "eeC2PS2KFOYbRWXh7okuxk", "87/2dcZqpOfZVYcDgszY0x", "8e0L6MCQVCZbkDeZtlF0+/", "016CdR3nxAIKKWZO/IScJu", "56cEJ1DRdHXoarY/trT2k7", "afDuQq08ZK84w57ZgCzell", "17GtRfsZ1E7Z+XFCPpyz3L", "97wepJnS1ASJPmVPqWnYjJ", "5f6mYPmaJJt4PrnEJVXZi8", "abrBvDewtMVKFaIUHxr1Cr", "55Oi62At5Dsb/z9UBMxOm9", "ba35xkMNBN6YaPhI7AJ+s/", "5cz5w/XbZCOaNIayWhq3kl", "c3GpE1T/NHybkRL22qXmqs", "12tq4HhcBJxpsL+G005vLY", "14wNH0f71H6pca4fzw45O2", "fd+eMjEklPvpgtvHgYQ19G", "de4T0De5dJ4a5gcBSm/lwc", "d08hsLhshFVJGUW/Oqy5Bv", "34b7WMPqVGl5aTkARY6oqk", "dd2JmbzyVE0aOA/BRj1snk", "9dkCi8b/RJEqun7oCamHTS", "8bOD9JEq9FGpYXNTxARmkq"], ["_textureSetter", "line-01", "line-01un", "line-02", "line-02un", "line-03", "line-03un", "line-04", "line-04un", "line-05", "line-05un", "line-06", "line-06un", "line-07", "line-07un", "line-08", "line-08un", "line-09", "line-09un", "line-10", "line-10un", "line-11", "line-11un", "line-12", "line-12un", "line-13", "line-13un", "line-14", "line-14un", "line-15", "line-15un", "line-16", "line-16un", "line-17", "line-17un", "line-18", "line-18un", "line-19", "line-19un", "line-20", "line-20un"], ["cc.SpriteFrame", ["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[1, 0, 1, 2]], [[[{"name": "line-16un", "rect": [360, 0, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-12un", "rect": [180, 64, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-01un", "rect": [0, 128, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-14un", "rect": [270, 128, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-11un", "rect": [90, 0, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-02un", "rect": [90, 256, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-15un", "rect": [270, 64, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-13un", "rect": [0, 448, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-03un", "rect": [0, 192, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-20un", "rect": [90, 448, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-10un", "rect": [270, 384, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-04un", "rect": [360, 256, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-06un", "rect": [270, 192, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-05un", "rect": [90, 320, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-08un", "rect": [180, 320, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-07un", "rect": [0, 384, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-18un", "rect": [180, 256, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-19un", "rect": [360, 128, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "linetable.plist", [{}, "line-01", 6, 0, "line-01un", 6, 1, "line-02", 6, 2, "line-02un", 6, 3, "line-03", 6, 4, "line-03un", 6, 5, "line-04", 6, 6, "line-04un", 6, 7, "line-05", 6, 8, "line-05un", 6, 9, "line-06", 6, 10, "line-06un", 6, 11, "line-07", 6, 12, "line-07un", 6, 13, "line-08", 6, 14, "line-08un", 6, 15, "line-09", 6, 16, "line-09un", 6, 17, "line-10", 6, 18, "line-10un", 6, 19, "line-11", 6, 20, "line-11un", 6, 21, "line-12", 6, 22, "line-12un", 6, 23, "line-13", 6, 24, "line-13un", 6, 25, "line-14", 6, 26, "line-14un", 6, 27, "line-15", 6, 28, "line-15un", 6, 29, "line-16", 6, 30, "line-16un", 6, 31, "line-17", 6, 32, "line-17un", 6, 33, "line-18", 6, 34, "line-18un", 6, 35, "line-19", 6, 36, "line-19un", 6, 37, "line-20", 6, 38, "line-20un", 6, 39]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40]], [[{"name": "line-09un", "rect": [0, 256, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "line-17un", "rect": [90, 64, 88, 62], "offset": [0, 0], "originalSize": [88, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]