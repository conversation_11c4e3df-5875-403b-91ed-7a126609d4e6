[1, ["dduOXAqPtEOZlLjBszo4Aq"], ["spriteFrame"], [["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[0, 0, 1, 2, 4]], [[0, "Font_HelveticaNeue_Effect-export", 32, {"commonHeight": 39, "fontSize": 32, "atlasName": "Font_HelveticaNeue_Effect-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 160, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 12, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 410, "y": 84, "width": 11, "height": 27}}, "35": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 234, "y": 364, "width": 23, "height": 27}}, "36": {"xOffset": 0, "yOffset": 9, "xAdvance": 25, "rect": {"x": 209, "y": 0, "width": 24, "height": 33}}, "37": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 28, "width": 34, "height": 29}}, "38": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 154, "y": 274, "width": 27, "height": 27}}, "39": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 318, "y": 404, "width": 11, "height": 13}}, "40": {"xOffset": 0, "yOffset": 11, "xAdvance": 15, "rect": {"x": 394, "y": 52, "width": 14, "height": 34}}, "41": {"xOffset": 0, "yOffset": 11, "xAdvance": 15, "rect": {"x": 395, "y": 87, "width": 14, "height": 34}}, "42": {"xOffset": 0, "yOffset": 12, "xAdvance": 18, "rect": {"x": 183, "y": 247, "width": 17, "height": 14}}, "43": {"xOffset": 0, "yOffset": 18, "xAdvance": 25, "rect": {"x": 208, "y": 397, "width": 24, "height": 21}}, "44": {"xOffset": 0, "yOffset": 30, "xAdvance": 12, "rect": {"x": 53, "y": 240, "width": 11, "height": 13}}, "45": {"xOffset": 0, "yOffset": 24, "xAdvance": 18, "rect": {"x": 35, "y": 249, "width": 17, "height": 8}}, "46": {"xOffset": 0, "yOffset": 30, "xAdvance": 12, "rect": {"x": 306, "y": 413, "width": 11, "height": 9}}, "47": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 377, "y": 292, "width": 19, "height": 28}}, "48": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 259, "y": 232, "width": 23, "height": 27}}, "49": {"xOffset": 0, "yOffset": 12, "xAdvance": 17, "rect": {"x": 377, "y": 29, "width": 16, "height": 27}}, "50": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 171, "width": 23, "height": 27}}, "51": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 265, "width": 22, "height": 27}}, "52": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 98, "width": 23, "height": 27}}, "53": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 321, "width": 22, "height": 27}}, "54": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 362, "width": 23, "height": 27}}, "55": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 349, "width": 22, "height": 27}}, "56": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 300, "width": 23, "height": 27}}, "57": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 293, "width": 22, "height": 27}}, "58": {"xOffset": 0, "yOffset": 18, "xAdvance": 12, "rect": {"x": 397, "y": 327, "width": 11, "height": 21}}, "59": {"xOffset": 0, "yOffset": 18, "xAdvance": 12, "rect": {"x": 410, "y": 112, "width": 11, "height": 25}}, "63": {"xOffset": 0, "yOffset": 11, "xAdvance": 23, "rect": {"x": 354, "y": 57, "width": 22, "height": 28}}, "64": {"xOffset": 0, "yOffset": 11, "xAdvance": 31, "rect": {"x": 33, "y": 356, "width": 30, "height": 29}}, "65": {"xOffset": 0, "yOffset": 12, "xAdvance": 30, "rect": {"x": 65, "y": 114, "width": 29, "height": 27}}, "66": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 391, "width": 26, "height": 27}}, "67": {"xOffset": 0, "yOffset": 11, "xAdvance": 29, "rect": {"x": 125, "y": 100, "width": 28, "height": 28}}, "68": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 154, "y": 61, "width": 27, "height": 27}}, "69": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 271, "width": 23, "height": 27}}, "70": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 331, "y": 0, "width": 23, "height": 27}}, "71": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 95, "y": 324, "width": 29, "height": 28}}, "72": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 190, "width": 26, "height": 27}}, "73": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 387, "width": 10, "height": 27}}, "74": {"xOffset": 0, "yOffset": 12, "xAdvance": 22, "rect": {"x": 377, "y": 213, "width": 21, "height": 27}}, "75": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 155, "y": 236, "width": 27, "height": 27}}, "76": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 126, "width": 23, "height": 27}}, "77": {"xOffset": 0, "yOffset": 12, "xAdvance": 32, "rect": {"x": 33, "y": 328, "width": 31, "height": 27}}, "78": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 36, "width": 26, "height": 27}}, "79": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 94, "y": 393, "width": 29, "height": 28}}, "80": {"xOffset": 0, "yOffset": 12, "xAdvance": 26, "rect": {"x": 209, "y": 275, "width": 25, "height": 27}}, "81": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 64, "y": 356, "width": 29, "height": 30}}, "82": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 363, "width": 26, "height": 27}}, "83": {"xOffset": 0, "yOffset": 11, "xAdvance": 27, "rect": {"x": 183, "y": 218, "width": 26, "height": 28}}, "84": {"xOffset": 0, "yOffset": 12, "xAdvance": 26, "rect": {"x": 209, "y": 247, "width": 25, "height": 27}}, "85": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 162, "width": 26, "height": 27}}, "86": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 126, "y": 274, "width": 27, "height": 27}}, "87": {"xOffset": 0, "yOffset": 12, "xAdvance": 38, "rect": {"x": 0, "y": 0, "width": 37, "height": 27}}, "88": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 153, "y": 302, "width": 27, "height": 27}}, "89": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 125, "y": 72, "width": 28, "height": 27}}, "90": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 64, "width": 26, "height": 27}}, "91": {"xOffset": 0, "yOffset": 11, "xAdvance": 14, "rect": {"x": 399, "y": 152, "width": 13, "height": 34}}, "92": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 377, "y": 321, "width": 19, "height": 28}}, "93": {"xOffset": 0, "yOffset": 11, "xAdvance": 14, "rect": {"x": 396, "y": 384, "width": 13, "height": 34}}, "94": {"xOffset": 0, "yOffset": 12, "xAdvance": 25, "rect": {"x": 126, "y": 302, "width": 24, "height": 19}}, "95": {"xOffset": 0, "yOffset": 35, "xAdvance": 24, "rect": {"x": 33, "y": 387, "width": 23, "height": 8}}, "97": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 209, "y": 303, "width": 23, "height": 22}}, "98": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 390, "width": 23, "height": 27}}, "99": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 307, "y": 63, "width": 22, "height": 22}}, "100": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 307, "y": 0, "width": 23, "height": 27}}, "101": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 354, "y": 115, "width": 22, "height": 22}}, "102": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 377, "y": 92, "width": 16, "height": 28}}, "103": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 354, "y": 86, "width": 22, "height": 28}}, "104": {"xOffset": 0, "yOffset": 12, "xAdvance": 22, "rect": {"x": 377, "y": 241, "width": 21, "height": 27}}, "105": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 399, "y": 0, "width": 10, "height": 27}}, "106": {"xOffset": 0, "yOffset": 12, "xAdvance": 14, "rect": {"x": 396, "y": 350, "width": 13, "height": 33}}, "107": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 377, "width": 22, "height": 27}}, "108": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 359, "width": 10, "height": 27}}, "109": {"xOffset": 0, "yOffset": 17, "xAdvance": 32, "rect": {"x": 0, "y": 396, "width": 31, "height": 22}}, "110": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 241, "width": 21, "height": 22}}, "111": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 282, "y": 394, "width": 23, "height": 22}}, "112": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 307, "y": 213, "width": 23, "height": 28}}, "113": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 307, "y": 242, "width": 23, "height": 28}}, "114": {"xOffset": 0, "yOffset": 17, "xAdvance": 16, "rect": {"x": 394, "y": 29, "width": 15, "height": 22}}, "115": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 377, "y": 269, "width": 21, "height": 22}}, "116": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 378, "y": 121, "width": 16, "height": 26}}, "117": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 30, "width": 21, "height": 22}}, "118": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 355, "y": 138, "width": 22, "height": 22}}, "119": {"xOffset": 0, "yOffset": 17, "xAdvance": 32, "rect": {"x": 32, "y": 396, "width": 31, "height": 22}}, "120": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 283, "y": 35, "width": 23, "height": 22}}, "121": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 283, "y": 58, "width": 23, "height": 28}}, "122": {"xOffset": 0, "yOffset": 18, "xAdvance": 23, "rect": {"x": 355, "y": 161, "width": 22, "height": 21}}, "123": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 377, "y": 57, "width": 16, "height": 34}}, "124": {"xOffset": 0, "yOffset": 9, "xAdvance": 10, "rect": {"x": 411, "y": 284, "width": 9, "height": 36}}, "125": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 378, "y": 148, "width": 16, "height": 34}}, "192": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 35, "y": 135, "width": 29, "height": 34}}, "193": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 35, "y": 100, "width": 29, "height": 34}}, "194": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 0, "width": 29, "height": 34}}, "195": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 35, "y": 206, "width": 29, "height": 33}}, "200": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 0, "width": 24, "height": 34}}, "201": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 35, "width": 24, "height": 34}}, "202": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 209, "y": 169, "width": 24, "height": 34}}, "204": {"xOffset": 0, "yOffset": 5, "xAdvance": 14, "rect": {"x": 397, "y": 292, "width": 13, "height": 34}}, "205": {"xOffset": 0, "yOffset": 4, "xAdvance": 14, "rect": {"x": 399, "y": 217, "width": 13, "height": 35}}, "210": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 65, "y": 142, "width": 29, "height": 34}}, "211": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 96, "y": 287, "width": 29, "height": 34}}, "212": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 214, "width": 29, "height": 34}}, "213": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 95, "y": 147, "width": 29, "height": 33}}, "217": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 182, "y": 127, "width": 26, "height": 34}}, "218": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 182, "y": 92, "width": 26, "height": 34}}, "221": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 125, "y": 165, "width": 28, "height": 34}}, "224": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 173, "width": 23, "height": 29}}, "225": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 334, "width": 23, "height": 29}}, "226": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 392, "width": 23, "height": 29}}, "227": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 235, "y": 271, "width": 23, "height": 28}}, "232": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 259, "y": 260, "width": 23, "height": 29}}, "233": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 259, "y": 0, "width": 23, "height": 29}}, "234": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 282, "y": 117, "width": 23, "height": 29}}, "236": {"xOffset": 0, "yOffset": 10, "xAdvance": 14, "rect": {"x": 399, "y": 187, "width": 13, "height": 29}}, "237": {"xOffset": 0, "yOffset": 10, "xAdvance": 15, "rect": {"x": 395, "y": 122, "width": 14, "height": 29}}, "242": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 283, "y": 210, "width": 23, "height": 29}}, "243": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 283, "y": 180, "width": 23, "height": 29}}, "244": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 306, "y": 271, "width": 23, "height": 29}}, "245": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 306, "y": 87, "width": 23, "height": 28}}, "249": {"xOffset": 0, "yOffset": 10, "xAdvance": 22, "rect": {"x": 377, "y": 183, "width": 21, "height": 29}}, "250": {"xOffset": 0, "yOffset": 10, "xAdvance": 22, "rect": {"x": 355, "y": 0, "width": 21, "height": 29}}, "253": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 306, "y": 143, "width": 23, "height": 35}}, "258": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 64, "y": 387, "width": 29, "height": 33}}, "259": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 234, "y": 144, "width": 23, "height": 28}}, "272": {"xOffset": 0, "yOffset": 12, "xAdvance": 30, "rect": {"x": 65, "y": 328, "width": 29, "height": 27}}, "273": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 38, "y": 0, "width": 26, "height": 27}}, "296": {"xOffset": 0, "yOffset": 6, "xAdvance": 19, "rect": {"x": 377, "y": 350, "width": 18, "height": 33}}, "297": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 377, "y": 384, "width": 18, "height": 28}}, "360": {"xOffset": 0, "yOffset": 6, "xAdvance": 27, "rect": {"x": 182, "y": 264, "width": 26, "height": 33}}, "361": {"xOffset": 0, "yOffset": 11, "xAdvance": 22, "rect": {"x": 377, "y": 0, "width": 21, "height": 28}}, "416": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 58, "width": 34, "height": 28}}, "417": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 125, "y": 322, "width": 27, "height": 23}}, "431": {"xOffset": 0, "yOffset": 10, "xAdvance": 33, "rect": {"x": 0, "y": 260, "width": 32, "height": 29}}, "432": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 153, "y": 389, "width": 27, "height": 23}}, "7840": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 124, "y": 353, "width": 28, "height": 31}}, "7841": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 258, "y": 144, "width": 23, "height": 26}}, "7842": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 170, "width": 29, "height": 35}}, "7843": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 234, "y": 303, "width": 23, "height": 30}}, "7844": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 28, "width": 29, "height": 35}}, "7845": {"xOffset": 0, "yOffset": 7, "xAdvance": 25, "rect": {"x": 209, "y": 34, "width": 24, "height": 32}}, "7846": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 64, "width": 29, "height": 35}}, "7847": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 235, "y": 203, "width": 23, "height": 32}}, "7848": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 96, "y": 249, "width": 29, "height": 37}}, "7849": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 235, "y": 236, "width": 23, "height": 34}}, "7850": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 94, "y": 356, "width": 29, "height": 36}}, "7851": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 283, "y": 0, "width": 23, "height": 34}}, "7852": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 125, "y": 200, "width": 28, "height": 38}}, "7853": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 258, "y": 328, "width": 23, "height": 33}}, "7854": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 0, "width": 29, "height": 37}}, "7855": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 234, "y": 109, "width": 23, "height": 34}}, "7856": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 38, "width": 29, "height": 37}}, "7857": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 258, "y": 109, "width": 23, "height": 34}}, "7858": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 76, "width": 29, "height": 37}}, "7859": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 330, "y": 63, "width": 23, "height": 34}}, "7860": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 95, "y": 74, "width": 29, "height": 36}}, "7861": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 307, "y": 28, "width": 23, "height": 34}}, "7862": {"xOffset": 0, "yOffset": 6, "xAdvance": 29, "rect": {"x": 125, "y": 34, "width": 28, "height": 37}}, "7863": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 259, "y": 199, "width": 23, "height": 32}}, "7864": {"xOffset": 0, "yOffset": 12, "xAdvance": 25, "rect": {"x": 209, "y": 137, "width": 24, "height": 31}}, "7865": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 282, "y": 90, "width": 23, "height": 26}}, "7866": {"xOffset": 0, "yOffset": 4, "xAdvance": 25, "rect": {"x": 209, "y": 67, "width": 24, "height": 35}}, "7867": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 259, "y": 30, "width": 23, "height": 30}}, "7868": {"xOffset": 0, "yOffset": 6, "xAdvance": 25, "rect": {"x": 209, "y": 103, "width": 24, "height": 33}}, "7869": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 259, "y": 61, "width": 23, "height": 28}}, "7870": {"xOffset": 0, "yOffset": 4, "xAdvance": 25, "rect": {"x": 210, "y": 204, "width": 24, "height": 35}}, "7871": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 208, "y": 331, "width": 25, "height": 32}}, "7872": {"xOffset": 0, "yOffset": 4, "xAdvance": 24, "rect": {"x": 331, "y": 191, "width": 23, "height": 35}}, "7873": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 282, "y": 147, "width": 23, "height": 32}}, "7874": {"xOffset": 0, "yOffset": 2, "xAdvance": 24, "rect": {"x": 331, "y": 227, "width": 23, "height": 37}}, "7875": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 282, "y": 290, "width": 23, "height": 34}}, "7876": {"xOffset": 0, "yOffset": 3, "xAdvance": 24, "rect": {"x": 331, "y": 154, "width": 23, "height": 36}}, "7877": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 282, "y": 325, "width": 23, "height": 34}}, "7878": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 70, "width": 24, "height": 38}}, "7879": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 282, "y": 360, "width": 23, "height": 33}}, "7880": {"xOffset": 0, "yOffset": 4, "xAdvance": 13, "rect": {"x": 410, "y": 0, "width": 12, "height": 35}}, "7881": {"xOffset": 0, "yOffset": 9, "xAdvance": 13, "rect": {"x": 399, "y": 253, "width": 12, "height": 30}}, "7882": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 409, "y": 52, "width": 11, "height": 31}}, "7883": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 327, "width": 10, "height": 31}}, "7884": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 95, "y": 181, "width": 29, "height": 32}}, "7885": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 306, "y": 116, "width": 23, "height": 26}}, "7886": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 95, "y": 111, "width": 29, "height": 35}}, "7887": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 283, "y": 240, "width": 23, "height": 30}}, "7888": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 65, "y": 177, "width": 29, "height": 35}}, "7889": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 208, "y": 364, "width": 25, "height": 32}}, "7890": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 65, "y": 213, "width": 29, "height": 35}}, "7891": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 306, "y": 301, "width": 23, "height": 32}}, "7892": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 66, "y": 249, "width": 29, "height": 37}}, "7893": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 306, "y": 334, "width": 23, "height": 34}}, "7894": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 66, "y": 287, "width": 29, "height": 36}}, "7895": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 306, "y": 369, "width": 23, "height": 34}}, "7896": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 35, "width": 29, "height": 38}}, "7897": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 307, "y": 179, "width": 23, "height": 33}}, "7898": {"xOffset": 0, "yOffset": 5, "xAdvance": 35, "rect": {"x": 0, "y": 122, "width": 34, "height": 34}}, "7899": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 146, "width": 27, "height": 29}}, "7900": {"xOffset": 0, "yOffset": 5, "xAdvance": 35, "rect": {"x": 0, "y": 87, "width": 34, "height": 34}}, "7901": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 0, "width": 27, "height": 29}}, "7902": {"xOffset": 0, "yOffset": 4, "xAdvance": 35, "rect": {"x": 0, "y": 224, "width": 34, "height": 35}}, "7903": {"xOffset": 0, "yOffset": 9, "xAdvance": 28, "rect": {"x": 154, "y": 30, "width": 27, "height": 30}}, "7904": {"xOffset": 0, "yOffset": 6, "xAdvance": 35, "rect": {"x": 0, "y": 157, "width": 34, "height": 33}}, "7905": {"xOffset": 0, "yOffset": 11, "xAdvance": 28, "rect": {"x": 154, "y": 89, "width": 27, "height": 28}}, "7906": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 191, "width": 34, "height": 32}}, "7907": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 154, "y": 118, "width": 27, "height": 27}}, "7908": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 331, "width": 26, "height": 31}}, "7909": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 214, "width": 21, "height": 26}}, "7910": {"xOffset": 0, "yOffset": 4, "xAdvance": 27, "rect": {"x": 182, "y": 0, "width": 26, "height": 35}}, "7911": {"xOffset": 0, "yOffset": 9, "xAdvance": 22, "rect": {"x": 355, "y": 183, "width": 21, "height": 30}}, "7912": {"xOffset": 0, "yOffset": 5, "xAdvance": 33, "rect": {"x": 0, "y": 290, "width": 32, "height": 34}}, "7913": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 176, "width": 27, "height": 29}}, "7914": {"xOffset": 0, "yOffset": 5, "xAdvance": 33, "rect": {"x": 0, "y": 325, "width": 32, "height": 34}}, "7915": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 206, "width": 27, "height": 29}}, "7916": {"xOffset": 0, "yOffset": 4, "xAdvance": 33, "rect": {"x": 0, "y": 360, "width": 32, "height": 35}}, "7917": {"xOffset": 0, "yOffset": 9, "xAdvance": 28, "rect": {"x": 153, "y": 330, "width": 27, "height": 30}}, "7918": {"xOffset": 0, "yOffset": 6, "xAdvance": 33, "rect": {"x": 33, "y": 260, "width": 32, "height": 33}}, "7919": {"xOffset": 0, "yOffset": 11, "xAdvance": 28, "rect": {"x": 181, "y": 302, "width": 27, "height": 28}}, "7920": {"xOffset": 0, "yOffset": 10, "xAdvance": 33, "rect": {"x": 33, "y": 294, "width": 32, "height": 33}}, "7921": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 153, "y": 361, "width": 27, "height": 27}}, "7922": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 126, "y": 239, "width": 28, "height": 34}}, "7923": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 330, "y": 299, "width": 23, "height": 35}}, "7924": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 124, "y": 385, "width": 28, "height": 31}}, "7925": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 331, "y": 28, "width": 23, "height": 28}}, "7926": {"xOffset": 0, "yOffset": 4, "xAdvance": 29, "rect": {"x": 125, "y": 129, "width": 28, "height": 35}}, "7927": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 330, "y": 335, "width": 23, "height": 36}}, "7928": {"xOffset": 0, "yOffset": 6, "xAdvance": 29, "rect": {"x": 125, "y": 0, "width": 28, "height": 33}}, "7929": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 330, "y": 372, "width": 23, "height": 34}}, "8221": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 259, "y": 90, "width": 16, "height": 14}}}, "kerningDict": {}}]], 0, 0, [0], [0], [0]]