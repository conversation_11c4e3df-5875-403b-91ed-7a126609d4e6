[1, ["ecpdLyjvZBwrvm+cedCcQy", "d82n49/IVAvIEqsa0xvvk0", "60lCKF0BFLRJpfGf89TIRK", "90Ss2Yf1lLHYPvA9KDgBaE", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "24xd2Xl+xHVZeWwPN10Wzf", "26XBIiR41EpJSfFK/OQnr/", "b00TtEdt5M4YNseM/Y6dpy", "b9sAPrSq9Bhq4NL0/rodI/", "78syxAT1pPY7sxIWu/LEol", "ffmyHfSHRCZr6cq5VJN6LN", "c25Leu0BdNDphgb/Hp9jw/", "2cWB/vWPRHja3uQTinHH30", "32CN7KCP9DYbDCW8N7PSkZ", "45wrrBM+VO/IPs52mxUvQ6"], ["node", "_N$file", "_spriteFrame", "_N$target", "_parent", "_defaultClip", "root", "nodeLBPage", "nodePagePrevious", "nodePageNext", "taiXiuHistoryListView", "lbchitiet", "lbWin", "lbRefund", "lbBet", "lbResult", "lbSide", "lbTime", "lbSession", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_anchorPoint"], 0, 4, 5, 9, 1, 7, 2, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_children"], 1, 1, 2, 4, 5, 7, 5, 2], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_string", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_N$overflow", "_enableWrapText", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["0065dMq3aNKwKnpaYZcykoq", ["node", "lbSession", "lbTime", "lbSide", "lbResult", "lbBet", "lbRefund", "lbWin", "lbchitiet"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Mask", ["_enabled", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["_enabled", "horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["4c7dbaMruJLTqUeXtzXdpOr", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["c0561QFLZtF/7GL3L6IUsOm", ["node", "taiXiuHistoryListView", "nodePageNext", "nodePagePrevious", "nodeLBPage"], 3, 1, 1, 1, 1, 1]], [[5, 0, 1, 2], [0, 0, 6, 5, 3, 4, 7, 2], [3, 0, 3, 4, 5, 2], [2, 2, 0, 1, 3, 4, 8, 9, 10, 6], [2, 2, 0, 5, 7, 1, 3, 4, 6, 8, 9, 9], [1, 0, 2, 3, 4, 5, 6, 2], [1, 0, 2, 3, 4, 7, 5, 6, 2], [3, 3, 4, 5, 1], [4, 0, 1, 3, 4, 5, 6, 2], [7, 0, 1, 2, 3], [0, 0, 6, 8, 5, 3, 4, 2], [0, 0, 2, 6, 5, 3, 4, 7, 3], [4, 1, 2, 1], [6, 0, 2], [0, 0, 8, 5, 3, 2], [0, 0, 1, 6, 5, 3, 4, 7, 3], [0, 0, 6, 8, 5, 3, 4, 7, 2], [0, 0, 1, 6, 5, 3, 4, 3], [0, 0, 8, 3, 4, 7, 2], [0, 0, 6, 8, 3, 7, 2], [0, 0, 8, 5, 3, 4, 2], [0, 0, 6, 3, 4, 9, 7, 2], [1, 0, 2, 8, 3, 4, 5, 6, 2], [1, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 2, 3, 4, 7, 5, 6, 3], [3, 1, 0, 2, 3, 4, 5, 4], [3, 0, 3, 4, 2], [5, 1, 1], [2, 2, 0, 1, 3, 8, 9, 10, 5], [2, 2, 0, 1, 3, 4, 8, 9, 6], [2, 2, 0, 5, 1, 3, 4, 8, 9, 7], [2, 0, 5, 1, 6, 8, 9, 5], [8, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 7], [11, 0, 1, 2, 3, 4, 5, 4], [12, 0, 1, 2, 1], [13, 0, 1, 2, 3, 4, 1]], [[13, "taiXiuSieuTocHistoryView"], [14, "taiXiuHistoryView", [-8, -9, -10, -11, -12, -13, -14], [[37, -2, [50, 51], 49], [38, -7, -6, -5, -4, -3]], [28, -1]], [20, "<PERSON><PERSON>", [-24, -25, -26, -27, -28, -29, -30, -31, -32], [[33, -23, -22, -21, -20, -19, -18, -17, -16, -15]], [0, "e1RO1bFy9GXqoEREZGp73Q", 1], [5, 1160, 72]], [18, "title", [-33, -34, -35, -36, -37, -38, -39, -40, -41], [0, "10CTtkUqxNs5qkB1/X4xvt", 1], [5, 1050, 50], [0, 233, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "info", 1, [-43, 3, -44, -45, -46], [[2, 0, -42, [30], 31]], [0, "82H4SrLZtEaZNlwtSHtdUE", 1], [5, 1160, 530], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnNext", 1, [[7, -47, [34], 35], [8, 3, -49, [[9, "c0561QFLZtF/7GL3L6IUsOm", "pageNextClicked", 1]], [4, 4294967295], [4, 4294967295], -48]], [0, "74KXkKipdPRJ0g9yHK08Qg", 1], [5, 23, 35], [593.984, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]], [1, "btnPrev", 1, [[7, -50, [36], 37], [8, 3, -52, [[9, "c0561QFLZtF/7GL3L6IUsOm", "pagePreviousClicked", 1]], [4, 4294967295], [4, 4294967295], -51]], [0, "25jsx5K2xFIJ4eNnT6+1Cd", 1], [5, 23, 35], [-591.854, -2.65, 0, 0, 0, 0, 1, 2, 2, 1]], [22, "scrollview", 1, [-55, -56], [-53, -54], [0, "1epo55TkJFH7Il1JiIxOXI", 1], [5, 1160, 450], [0, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "nen popup", 1, [-59], [[26, 1, 0, false, -57, [4], 5], [12, -58, [4, 4292269782]]], [0, "f75IaZcAlFCqkYqZpdAkDw", 1], [5, 1200, 654]], [1, "btnClose", 1, [[7, -60, [32], 33], [8, 3, -62, [[9, "c0561QFLZtF/7GL3L6IUsOm", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -61]], [0, "f0tBL3UKhJqbndztf9vB5r", 1], [5, 96, 50], [553, 300, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "black", 100, 1, [[2, 0, -63, [0], 1], [12, -64, [4, 4292269782]]], [0, "14IYFa3aZNFaHKTHE1Pvh+", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "view", 7, [-66], [[34, false, -65, [48]]], [0, "67khkH9nZLVqtbYtjnBTzu", 1], [5, 1160, 455]], [1, "Title", 8, [[29, "LỊCH SỬ CƯỢC TÀI XỈU", 21, false, 1, -67, [2], 3]], [0, "9dedxcf+5L3awufrSvoaaY", 1], [5, 393.75, 26.25], [0, 308, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "789600", 66, 4, [[27, 0, -68, [6]]], [0, "68X3QTi5lNaZa8/TPH07ek", 1], [5, 600, 270]], [1, "<PERSON><PERSON>", 3, [[3, "<PERSON><PERSON><PERSON>", 22, false, 1, 1, -69, [7], 8]], [0, "2b5Ykk9rZES67WXhD3y3y4", 1], [5, 71.5, 27.5], [-481, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 3, [[2, 0, -70, [9], 10]], [0, "b9K8IfoVxPk5hb9Em9TU3t", 1], [5, 25, 530], [-389, -233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Time", 3, [[3, "<PERSON><PERSON><PERSON><PERSON> gian", 22, false, 1, 1, -71, [11], 12]], [0, "65r2ICCAxLQKLmeBysqfRe", 1], [5, 122.38, 27.5], [-303, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 3, [[2, 0, -72, [13], 14]], [0, "c6A8uwLwZKXLm0waYjz+fv", 1], [5, 25, 530], [-198, -233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Total", 3, [[3, "<PERSON><PERSON><PERSON>", 22, false, 1, 1, -73, [15], 16]], [0, "0cOepVyjlFQ6iEdBwmxW6j", 1], [5, 147.13, 27.5], [-114, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 3, [[2, 0, -74, [17], 18]], [0, "c02xaV/OFA1piJl5PW2Fsk", 1], [5, 25, 530], [-14, -233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "<PERSON><PERSON>", 3, [[3, "<PERSON><PERSON><PERSON><PERSON>", 22, false, 1, 1, -75, [19], 20]], [0, "02Y/s9E0BBq78D2XB1n2ed", 1], [5, 145.06, 27.5], [74, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 3, [[2, 0, -76, [21], 22]], [0, "abJeQ0QbVCErDy4xQ9pPzt", 1], [5, 25, 530], [176, -233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "detail", 3, [[3, "<PERSON>", 22, false, 1, 1, -77, [23], 24]], [0, "30J9PIzgtJpaksDMMn3Vub", 1], [5, 175.31, 27.5], [366, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "Betside", false, 4, [[3, "<PERSON><PERSON><PERSON> đặt", 22, false, 1, 1, -78, [25], 26]], [0, "f8rCqdLelAfr7RLvQN1Sqx", 1], [5, 110, 27.5], [-217, 249, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "<PERSON><PERSON><PERSON>", false, 4, [[3, "<PERSON><PERSON><PERSON> qu<PERSON>", 22, false, 1, 1, -79, [27], 28]], [0, "95Jg7JWGdEurjBt8FGb/cy", 1], [5, 104.5, 27.5], [-77, 249, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "page_index", 4, [-80], [0, "32FpEwQQdDn7dQgJeAtZOS", 1], [5, 98.5, 50.4], [420.025, 302.9, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "Trang: 1", 24, false, 1, 1, 25, [29]], [19, "temp", 7, [2], [0, "bcQyI1jVxLOpTCbyPzYoO/", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbSession", 2, [-81], [0, "e2rYZFKCRDZJjYTbBxRT7t", 1], [4, 4278231805], [5, 90, 30], [-487, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "PHIÊN", 21, 48, false, false, 1, 1, 1, 28, [38]], [5, "lbTime", 2, [-82], [0, "7fQqU7LdNCMZIKfRAEsp1k", 1], [5, 140, 41.58], [-302, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "THỜI GIAN", 21, 33, false, false, 1, 1, 3, 30, [39]], [23, "lbResult", false, 2, [-83], [0, "f8QmVZqhtEz7UAi9CNtj7e", 1], [5, 140, 38], [-77.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "KẾT QUẢ", 20, 48, false, false, 1, 1, 1, 32, [40]], [6, "lbBet", 2, [-84], [0, "4e3MJ6YOxMTIKfxd2vILNz", 1], [4, 4278231805], [5, 182, 38], [-114, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "TIỀN ĐẶT", 21, 48, false, false, 1, 1, 1, 34, [41]], [24, "lbBetside", false, 2, [-85], [0, "74N5AuGBJEqqcqnKM+6Nxh", 1], [5, 51.39, 37.8]], [31, "Label", 20, 30, false, 1, 1, 36, [42]], [25, "lbRefund", false, 2, [-86], [0, "e38izChKlPOZA/jAYCqsKH", 1], [4, 4285592575], [5, 170, 38], [274, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "HOÀN TRẢ", 20, 48, false, false, 1, 1, 1, 38, [43]], [6, "lbWin", 2, [-87], [0, "83YYKEvDdFj6rCkmWojtNP", 1], [4, 4285592575], [5, 180, 38], [73, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "THẮNG", 21, 48, false, false, 1, 1, 1, 40, [44]], [5, "detail", 2, [-88], [0, "23ZVykF19B5I7olJucO4pB", 1], [5, 395, 25.2], [371, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 18, 20, false, 3, 42, [45]], [1, "line", 2, [[2, 0, -89, [46], 47]], [0, "0c8JEWk/RMKKvYuHyHdBrK", 1], [5, 1300, 16], [-1, -47.089, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "content", 11, [0, "6fu3jDAHBLkJ6j5RKi4DF1", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [35, false, false, 0.75, 0.23, null, null, 7, 45], [36, 1, 4, 400, 7, 2, 46]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 26, 0, 8, 6, 0, 9, 5, 0, 10, 47, 0, 0, 1, 0, -1, 10, 0, -2, 8, 0, -3, 4, 0, -4, 9, 0, -5, 5, 0, -6, 6, 0, -7, 7, 0, 11, 43, 0, 12, 41, 0, 13, 39, 0, 14, 35, 0, 15, 33, 0, 16, 37, 0, 17, 31, 0, 18, 29, 0, 0, 2, 0, -1, 28, 0, -2, 30, 0, -3, 32, 0, -4, 34, 0, -5, 36, 0, -6, 38, 0, -7, 40, 0, -8, 42, 0, -9, 44, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, -4, 17, 0, -5, 18, 0, -6, 19, 0, -7, 20, 0, -8, 21, 0, -9, 22, 0, 0, 4, 0, -1, 13, 0, -3, 23, 0, -4, 24, 0, -5, 25, 0, 0, 5, 0, 3, 5, 0, 0, 5, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, -1, 46, 0, -2, 47, 0, -1, 27, 0, -2, 11, 0, 0, 8, 0, 0, 8, 0, -1, 12, 0, 0, 9, 0, 3, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, -1, 45, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, -1, 26, 0, -1, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, -1, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, 0, 44, 0, 19, 1, 2, 4, 27, 3, 4, 4, 89], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 29, 31, 35, 37, 41, 43], [-1, 2, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 2, -1, 2, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, 5, -1, -2, 1, 1, 1, 1, 1, 1, 1], [0, 5, 0, 6, 0, 7, 0, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 3, 0, 1, 0, 1, 0, 1, 0, 0, 8, 0, 9, 0, 10, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 4, 4, 13, 14, 2, 2, 2, 15, 2, 2]]