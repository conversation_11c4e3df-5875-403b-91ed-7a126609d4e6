[1, ["5aknhnQpBBHqygZRz65r9a"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "item_06_ani", "\nitem_06_ani.png\nsize: 136,184\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nicon06\n  rotate: false\n  xy: 2, 45\n  size: 132, 137\n  orig: 132, 137\n  offset: 0, 0\n  index: -1\nicon06_e\n  rotate: false\n  xy: 2, 2\n  size: 41, 41\n  orig: 41, 41\n  offset: 0, 0\n  index: -1\n", ["item_06_ani.png"], {"skeleton": {"hash": "s7UEa0Rr33YpvE3uO+MIf0To270", "spine": "3.6.53", "width": 132, "height": 137}, "bones": [{"name": "root"}, {"name": "icon06", "parent": "root", "length": 51.17, "rotation": 38.29, "x": 1.11, "y": -0.99}, {"name": "icon6", "parent": "icon06", "x": 0.14, "y": 0.87}], "slots": [{"name": "icon06", "bone": "icon06", "attachment": "icon06"}, {"name": "icon06_e", "bone": "icon6", "color": "ffffff2e", "attachment": "icon06_e", "blend": "additive"}], "skins": {"default": {"icon06": {"icon06": {"x": 1.04, "y": 3.69, "rotation": -38.29, "width": 132, "height": 137}}, "icon06_e": {"icon06_e": {"x": -3.4, "y": 28.53, "rotation": -38.29, "width": 41, "height": 41}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "icon06": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "icon6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -89.25}, {"time": 1.5, "angle": -179.74}, {"time": 2.2333, "angle": 89.38}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}}, "lose": {"slots": {"icon06": {"color": [{"time": 0, "color": "3c3c3cff"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "icon06": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "icon6": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}, "win": {"slots": {"icon06_e": {"color": [{"time": 0.4333, "color": "ffffff4a", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff4a"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "icon06": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": 0, "y": 10.22}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 1.1667, "x": 0, "y": 10.22}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.9, "x": 0, "y": 10.22}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0, "y": 10.22}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.7333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "icon6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7333, "angle": -89.25}, {"time": 1.5, "angle": -179.74}, {"time": 2.2333, "angle": 89.38}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]