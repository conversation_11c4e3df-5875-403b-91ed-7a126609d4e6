[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "4cJiSTT0BC65mvgQIxzL/F", "d4xPcPQZNFRacXxr5KXwsO", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_parent", "_defaultClip", "root", "_N$target", "_N$content", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 1, 5, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_active", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint"], 1, 12, 9, 4, 5, 5], ["cc.Mask", ["_enabled", "_N$alphaThreshold", "node", "_materials"], 1, 1, 3], ["<PERSON><PERSON>", ["_enabled", "horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["5ef69MihgFG7ZSQ76C/Hm2q", ["node"], 3, 1]], [[3, 0, 1, 2], [2, 1, 2, 1], [4, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 2, 5, 4, 3, 6, 7, 3], [0, 0, 5, 4, 3, 6, 2], [0, 0, 1, 5, 8, 4, 3, 6, 9, 7, 3], [0, 0, 1, 5, 4, 3, 6, 9, 3], [0, 0, 1, 5, 3, 6, 9, 3], [0, 0, 5, 8, 4, 3, 6, 7, 2], [0, 0, 5, 4, 3, 6, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 4, 5, 6, 2], [1, 2, 0, 1, 4, 5, 6, 4], [1, 3, 0, 1, 4, 5, 4], [1, 4, 5, 6, 1], [2, 0, 1, 3, 4, 5, 6, 2], [3, 1, 1], [6, 0, 1, 2, 3, 3], [7, 0, 1, 2, 3, 4, 5, 6, 7, 7], [8, 0, 1, 2, 3], [9, 0, 1, 2, 1], [10, 0, 1]], [[2, "xxliveHelpView"], [3, "xxHelpView", [-4, -5, -6, -7], [[21, -2, [9, 10], 8], [22, -3]], [17, -1]], [11, "view", false, [[-9, [8, "content", false, -10, [0, "6eS1CW3TJNdavE5LCzsQ5a", 1], [5, 1000, 490], [0, 0.5, 1]]], 1, 4], [[18, false, 0, -8, [5]]], [0, "78eu9y9hpD/Ir3FrWShcLM", 1], [5, 1000, 490], [0, 0.5, 1]], [9, "btnClose", 1, [-13], [[16, 3, -12, [[20, "5ef69MihgFG7ZSQ76C/Hm2q", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -11]], [0, "ebs6RVxrFF1YHcsOw4LG6n", 1], [5, 80, 80], [515.641, 287.642, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "black", 100, 1, [[12, 0, -14, [0], 1], [1, -15, [4, 4292269782]]], [0, "7aOqrdgQRGR7QHOxTJzw8g", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nen popup", 1, [[13, 1, 0, false, -16, [2], 3], [1, -17, [4, 4292269782]]], [0, "4aBEcuqLtCkIbgYjXr2o1D", 1], [5, 1100, 618]], [6, "scrollview", false, 1, [2], [[19, false, false, 0.75, 0.23, null, null, -19, -18]], [0, "75pHVqs3FI+o+zjd1MSZNG", 1], [5, 1020, 510], [0, 0.5, 1], [0, 225, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "TEXT_HD", false, 2, [[14, false, 2, false, -20, [4]]], [0, "98GcaTvlhF/Ikm1llRspaJ", 1], [5, 1000, 684], [0, 0.5, 1]], [10, "Btn_Close", 3, [[15, -21, [6], 7]], [0, "b3mYJCdLNC7rLUVrfxbo9R", 1], [5, 69, 36], [0, 3.487, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 3, 0, 0, 2, 0, -1, 7, 0, 2, 2, 0, 5, 3, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 6, 7, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 7, 1, 2, 2, 6, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, -1, 1, 3, -1, -2], [0, 2, 0, 3, 0, 0, 0, 4, 1, 1, 5]]