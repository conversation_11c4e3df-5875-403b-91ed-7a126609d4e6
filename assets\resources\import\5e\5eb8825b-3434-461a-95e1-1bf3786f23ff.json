[1, ["96nMmH02pAXbAMuYpQSpOw"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "STTT-Anim-BonusItem", "\nSTTT-Anim-BonusItem.png\nsize: 178,523\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nLayer 680\n  rotate: false\n  xy: 2, 387\n  size: 137, 134\n  orig: 137, 134\n  offset: 0, 0\n  index: -1\nbg\n  rotate: false\n  xy: 2, 256\n  size: 127, 127\n  orig: 127, 127\n  offset: 0, 0\n  index: -1\nflare\n  rotate: false\n  xy: 68, 3\n  size: 74, 75\n  orig: 74, 75\n  offset: 0, 0\n  index: -1\nnapmo\n  rotate: true\n  xy: 2, 2\n  size: 100, 62\n  orig: 100, 62\n  offset: 0, 0\n  index: -1\nngoc1\n  rotate: true\n  xy: 146, 9\n  size: 69, 24\n  orig: 69, 24\n  offset: 0, 0\n  index: -1\nngoc2\n  rotate: false\n  xy: 143, 431\n  size: 29, 23\n  orig: 29, 23\n  offset: 0, 0\n  index: -1\nngoc3\n  rotate: true\n  xy: 143, 458\n  size: 37, 31\n  orig: 37, 31\n  offset: 0, 0\n  index: -1\nngoc4\n  rotate: false\n  xy: 68, 84\n  size: 22, 18\n  orig: 22, 18\n  offset: 0, 0\n  index: -1\nngoctrai\n  rotate: true\n  xy: 116, 82\n  size: 41, 44\n  orig: 41, 44\n  offset: 0, 0\n  index: -1\nngoctrai2\n  rotate: true\n  xy: 143, 499\n  size: 22, 32\n  orig: 22, 32\n  offset: 0, 0\n  index: -1\nsang1\n  rotate: false\n  xy: 128, 228\n  size: 45, 10\n  orig: 45, 10\n  offset: 0, 0\n  index: -1\nsang2\n  rotate: true\n  xy: 133, 242\n  size: 69, 43\n  orig: 69, 43\n  offset: 0, 0\n  index: -1\nshadow\n  rotate: true\n  xy: 2, 106\n  size: 39, 110\n  orig: 39, 110\n  offset: 0, 0\n  index: -1\ntext\n  rotate: true\n  xy: 143, 315\n  size: 112, 28\n  orig: 112, 28\n  offset: 0, 0\n  index: -1\nthanduoi\n  rotate: false\n  xy: 2, 149\n  size: 115, 79\n  orig: 115, 79\n  offset: 0, 0\n  index: -1\ntoi\n  rotate: false\n  xy: 2, 232\n  size: 122, 20\n  orig: 122, 20\n  offset: 0, 0\n  index: -1\ntreasure\n  rotate: true\n  xy: 121, 127\n  size: 97, 44\n  orig: 97, 44\n  offset: 0, 0\n  index: -1\n", ["STTT-Anim-BonusItem.png"], {"skeleton": {"hash": "NAVh0SRxVSZbclxS+EAe7Mb30L0", "spine": "3.7.94", "width": 170.83, "height": 142, "images": "./images/", "audio": "/Volumes/Lulu/SonTinhThuyTinh/Spine/Bonus-Item"}, "bones": [{"name": "root"}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "x": -3.74, "y": -12.92, "color": "ff0000ff"}, {"name": "text", "parent": "root", "x": 0.34, "y": -51}, {"name": "sang", "parent": "root"}, {"name": "sang1", "parent": "sang", "color": "fff846ff"}, {"name": "sang2", "parent": "sang", "color": "ffe028ff"}, {"name": "ngocTrai", "parent": "root", "x": 10.54, "y": -2.72, "color": "0684ffff"}, {"name": "ngoctrai2", "parent": "root", "x": 35.36, "y": 2.72, "color": "0e85ffff"}, {"name": "ngoc1", "parent": "root", "x": -54.4, "y": -29.92, "color": "ff4a4aff"}, {"name": "ngoc2", "parent": "root", "color": "e75858ff"}, {"name": "ngoc3", "parent": "root", "x": 45.56, "y": -41.48, "color": "be5d5dff"}, {"name": "Ngoc4", "parent": "root", "x": -54.74, "y": -39.44, "color": "d54b4bff"}, {"name": "napmo", "parent": "root"}, {"name": "flare", "parent": "root", "x": -46.97, "y": 38.73, "scaleX": 0.827, "scaleY": 0.827, "color": "f5a407ff"}, {"name": "bg", "parent": "root"}, {"name": "sang3", "parent": "root", "x": 2.74, "scaleX": -1}, {"name": "sang4", "parent": "sang3", "color": "fff846ff"}, {"name": "sang5", "parent": "sang3", "color": "ffe028ff"}, {"name": "treasure", "parent": "root"}, {"name": "flare2", "parent": "root", "x": 15.66, "y": 26.37, "scaleX": 0.868, "scaleY": 0.868, "color": "f5a407ff"}, {"name": "flare3", "parent": "root", "x": -3.3, "y": -24.72, "scaleX": 0.763, "scaleY": 0.763, "color": "f5a407ff"}, {"name": "flare4", "parent": "root", "x": -21.42, "y": -2.47, "scaleX": 0.855, "scaleY": 0.855, "color": "f5a407ff"}, {"name": "flare5", "parent": "root", "x": 43.67, "y": -9.06, "scaleX": 0.471, "scaleY": 0.471, "color": "f5a407ff"}], "slots": [{"name": "bg", "bone": "bg", "attachment": "bg"}, {"name": "shadow", "bone": "<PERSON><PERSON><PERSON>", "attachment": "shadow"}, {"name": "Layer 680", "bone": "root", "attachment": "Layer 680"}, {"name": "napmo", "bone": "napmo", "attachment": "napmo"}, {"name": "<PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON>", "attachment": "<PERSON><PERSON><PERSON>"}, {"name": "treasure", "bone": "treasure", "attachment": "treasure"}, {"name": "ngoctrai2", "bone": "ngoctrai2", "attachment": "ngoctrai2"}, {"name": "ngoctrai", "bone": "ngocTrai", "attachment": "ngoctrai"}, {"name": "ngoc4", "bone": "Ngoc4", "attachment": "ngoc4"}, {"name": "ngoc3", "bone": "ngoc3", "attachment": "ngoc3"}, {"name": "ngoc2", "bone": "ngoc2", "attachment": "ngoc2"}, {"name": "ngoc1", "bone": "ngoc1", "attachment": "ngoc1"}, {"name": "toi", "bone": "text", "attachment": "toi"}, {"name": "sang2", "bone": "sang2", "attachment": "sang2"}, {"name": "sang4", "bone": "sang5", "attachment": "sang2"}, {"name": "sang1", "bone": "sang1", "attachment": "sang1"}, {"name": "sang3", "bone": "sang4", "attachment": "sang1"}, {"name": "flare", "bone": "flare", "attachment": "flare"}, {"name": "flare2", "bone": "flare2", "attachment": "flare"}, {"name": "flare3", "bone": "flare3", "attachment": "flare"}, {"name": "flare5", "bone": "flare5", "attachment": "flare"}, {"name": "flare4", "bone": "flare4", "attachment": "flare"}, {"name": "text", "bone": "text", "attachment": "text"}], "skins": {"default": {"Layer 680": {"Layer 680": {"x": 0.45, "y": 5.47, "width": 137, "height": 134}}, "bg": {"bg": {"x": -0.55, "y": -1.03, "width": 127, "height": 127}}, "flare": {"flare": {"x": 2.74, "y": -4.64, "width": 74, "height": 75}}, "flare2": {"flare": {"x": 2.74, "y": -4.64, "width": 74, "height": 75}}, "flare3": {"flare": {"x": 2.74, "y": -4.64, "width": 74, "height": 75}}, "flare4": {"flare": {"x": 2.74, "y": -4.64, "width": 74, "height": 75}}, "flare5": {"flare": {"x": 2.74, "y": -4.64, "width": 74, "height": 75}}, "napmo": {"napmo": {"type": "mesh", "hull": 8, "width": 100, "height": 62, "uvs": [1, 0.66587, 1, 0.85837, 0.9103, 0.95755, 0.17715, 0.79381, 0, 0.27037, 0, 0.18287, 0.12939, 0, 0.75218, 0], "triangles": [2, 3, 0, 3, 7, 0, 1, 2, 0, 4, 5, 6, 7, 4, 6, 3, 4, 7], "vertices": [1, 1, 66.69, 36.1, 1, 1, 1, 66.69, 24.17, 1, 1, 1, 57.72, 18.02, 1, 1, 1, -15.59, 28.17, 1, 2, 12, -37.05, 47.7, 0.73, 1, -33.31, 60.62, 0.27, 2, 12, -37.05, 53.13, 0.91, 1, -33.31, 66.05, 0.09, 1, 12, -24.11, 64.47, 1, 1, 12, 38.17, 64.47, 1], "edges": [2, 0, 12, 14, 8, 10, 10, 12, 14, 0, 2, 4, 4, 6, 6, 8]}}, "ngoc1": {"ngoc1": {"x": 22.85, "y": 2.39, "width": 69, "height": 24}}, "ngoc2": {"ngoc2": {"x": -39.55, "y": -39.03, "width": 29, "height": 23}}, "ngoc3": {"ngoc3": {"x": -2.11, "y": 2.45, "width": 37, "height": 31}}, "ngoc4": {"ngoc4": {"x": -2.31, "y": 1.91, "width": 22, "height": 18}}, "ngoctrai": {"ngoctrai": {"x": 1.91, "y": -12.81, "width": 41, "height": 44}}, "ngoctrai2": {"ngoctrai2": {"x": 0.59, "y": -8.25, "width": 22, "height": 32}}, "sang1": {"sang1": {"x": -61.55, "y": -51.53, "width": 45, "height": 10}}, "sang2": {"sang2": {"x": -42.55, "y": -48.03, "width": 69, "height": 43}}, "sang3": {"sang1": {"x": -61.55, "y": -51.53, "width": 45, "height": 10}}, "sang4": {"sang2": {"x": -42.55, "y": -48.03, "width": 69, "height": 43}}, "shadow": {"shadow": {"x": 54.19, "y": 16.39, "width": 39, "height": 110}}, "text": {"text": {"x": -0.39, "y": 0.47, "width": 112, "height": 28}}, "thanduoi": {"thanduoi": {"x": 2.19, "y": -3.11, "width": 115, "height": 79}}, "toi": {"toi": {"x": 0.61, "y": -2.53, "width": 122, "height": 20}}, "treasure": {"treasure": {"type": "mesh", "hull": 9, "width": 97, "height": 44, "uvs": [1, 0.45572, 1, 0.6864, 0.49796, 1, 0.41425, 1, 0.38734, 0.86435, 0, 0.62049, 0, 0.31072, 0.13322, 0, 0.83579, 0], "triangles": [1, 4, 0, 2, 3, 4, 1, 2, 4, 4, 5, 6, 4, 8, 0, 8, 6, 7, 8, 4, 6], "vertices": [1, 1, 50.69, 27.33, 1, 1, 1, 50.69, 17.18, 1, 1, 1, 2, 3.39, 1, 1, 1, -6.12, 3.39, 1, 1, 1, -8.73, 9.35, 1, 1, 1, -46.31, 20.08, 1, 2, 18, -50.05, 20.79, 0.85, 1, -46.31, 33.71, 0.15, 1, 18, -37.12, 34.47, 1, 1, 18, 31.03, 34.47, 1], "edges": [4, 6, 2, 0, 14, 16, 10, 12, 12, 14, 16, 0, 2, 4, 10, 8, 8, 6]}}}}, "animations": {"animation": {"slots": {"Layer 680": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}]}, "bg": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}, "flare": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffff00", "curve": [0.173, 0.28, 0.75, 1]}, {"time": 0.8667, "color": "ffffffff", "curve": [0.318, 0, 0.924, 0.84]}, {"time": 1.3333, "color": "ffffff00"}]}, "flare2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.173, 0.28, 0.75, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [0.318, 0, 0.924, 0.84]}, {"time": 1.6333, "color": "ffffff00"}]}, "flare3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffff00", "curve": [0.173, 0.28, 0.75, 1]}, {"time": 1.2333, "color": "ffffffff", "curve": [0.318, 0, 0.924, 0.84]}, {"time": 1.7, "color": "ffffff00"}]}, "flare4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffff00", "curve": [0.173, 0.28, 0.75, 1]}, {"time": 1.4667, "color": "ffffffff", "curve": [0.318, 0, 0.924, 0.84]}, {"time": 1.9333, "color": "ffffff00"}]}, "flare5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2333, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.318, 0, 0.924, 0.84]}, {"time": 2, "color": "ffffff00"}]}, "sang1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffb6"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffffb7"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffffb7"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffffb7"}, {"time": 1, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffffb7"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffffb7"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffb7"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffffb7"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffffb7"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffffb7"}, {"time": 1.8, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffffb7"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffffb7"}]}, "sang2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff74", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff74", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}, "sang3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffb6"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffffb7"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffffb7"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffffb7"}, {"time": 1, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffffb7"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffffb7"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffffb7"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffffb7"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffffb7"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.7333, "color": "ffffffb7"}, {"time": 1.8, "color": "ffffffff"}, {"time": 1.8667, "color": "ffffffb7"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffffb7"}]}, "sang4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffff74", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff74", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff"}]}, "shadow": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}]}}, "bones": {"napmo": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0667, "x": 1, "y": 0.943}, {"time": 0.1333, "x": 1, "y": 1}, {"time": 0.2, "x": 1, "y": 0.943}, {"time": 0.2667, "x": 1, "y": 1}, {"time": 0.3333, "x": 1, "y": 0.943}, {"time": 0.4, "x": 1, "y": 1}]}, "text": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 0, "y": 12.5}, {"time": 0.3333, "x": 0, "y": 20.45}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.4333, "x": 0, "y": -1.14}, {"time": 0.5, "x": 0, "y": 0}], "scale": [{"time": 0.4, "x": 1, "y": 1}, {"time": 0.4333, "x": 1, "y": 0.943}, {"time": 0.5, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": -6.66, "y": 0}, {"time": 0.3333, "x": 8.02, "y": 0}, {"time": 0.4, "x": 0, "y": 0}]}, "sang1": {"scale": [{"time": 0, "x": 0.795, "y": 1, "curve": "stepped"}, {"time": 0.7667, "x": 0.795, "y": 1}, {"time": 1.5, "x": 1, "y": 1}]}, "sang4": {"scale": [{"time": 0, "x": 0.795, "y": 1, "curve": "stepped"}, {"time": 0.7667, "x": 0.795, "y": 1}, {"time": 1.5, "x": 1, "y": 1}]}, "ngoc2": {"translate": [{"time": 0.5, "x": 0, "y": 0, "curve": [0.216, 0.35, 0.668, 1]}, {"time": 0.7667, "x": 0, "y": 27.82, "curve": [0.472, 0, 0.9, 0.89]}, {"time": 1, "x": 0, "y": 0}]}, "ngoc3": {"translate": [{"time": 0.4333, "x": 0, "y": 0, "curve": [0.216, 0.35, 0.668, 1]}, {"time": 0.7, "x": 0, "y": 27.82, "curve": [0.472, 0, 0.9, 0.89]}, {"time": 0.9333, "x": 0, "y": 0}]}, "Ngoc4": {"translate": [{"time": 0.4, "x": 0, "y": 0, "curve": [0.216, 0.35, 0.668, 1]}, {"time": 0.6667, "x": 0, "y": 27.82, "curve": [0.472, 0, 0.9, 0.89]}, {"time": 0.9, "x": 0, "y": 0}]}, "ngoc1": {"translate": [{"time": 0.4333, "x": 0, "y": 0, "curve": [0.216, 0.35, 0.668, 1]}, {"time": 0.7, "x": 0, "y": 27.82, "curve": [0.472, 0, 0.9, 0.89]}, {"time": 0.9333, "x": 0, "y": 0}]}, "ngocTrai": {"scale": [{"time": 0, "x": 1, "y": 0.672, "curve": [0.207, 0.29, 0.634, 1]}, {"time": 0.3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 8.1, "y": 16.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 3.62, "y": 6.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "ngoctrai2": {"scale": [{"time": 0, "x": 1, "y": 0.672, "curve": [0.207, 0.29, 0.634, 1]}, {"time": 0.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 8.1, "y": 16.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": 3.62, "y": 6.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "treasure": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.245, 0.27, 0.654, 1]}, {"time": 0.2, "x": 1, "y": 1.134, "curve": [0.366, 0, 0.914, 0.92]}, {"time": 0.3667, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]