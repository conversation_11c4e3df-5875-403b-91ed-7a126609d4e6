[1, ["572jbIldFCAZPMmu+lPXXi", "feA1ILgjpJw6wnH1B8jMrQ", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "f6IBI1IKJCNaXlwZsHvdAJ", "a9VpD0DP5LJYQPXITZq+uj", "ffw9P0ZiFLRb96S+X/Fgwm", "25bZ6LNu1ETo4+3yJxsik2", "1cIgMKIypJ7Jy+JHaSTGQS", "8fHyFNHiRB97qc0DJBUEe2", "74qCZlsOtBcqB3v53xd5qm", "2cWB/vWPRHja3uQTinHH30", "75hA4L6aZHNp4gu9Lgqpwp"], ["_spriteFrame", "node", "_N$skeletonData", "_N$file", "_textureSetter", "root", "lbTotalWin", "lbSessionID", "_N$target", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_contentSize", "_parent", "_children", "_components", "_trs"], 1, 4, 5, 1, 2, 9, 7], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_spriteFrame"], 0, 1, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 1, 1, 2, 4, 5, 7, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_N$file"], -5, 1, 6], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "defaultAnimation", "_animationName", "node"], -1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["28f77yBgxZMGLXyCMrVDPMk", ["node", "lbSessionID", "lbTotalWin", "spriteIcons", "skeletonIcons", "kernelIcons"], 3, 1, 1, 1, 2, 2, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1]], [[10, 0, 1, 2], [2, 0, 2, 3, 4, 5, 6, 2], [0, 0, 4, 5, 2, 3, 7, 2], [1, 0, 1, 3, 3], [2, 0, 1, 2, 3, 4, 5, 3], [6, 0, 1, 4, 3], [0, 0, 4, 5, 2, 3, 2], [0, 0, 4, 5, 6, 2, 3, 7, 2], [0, 0, 4, 6, 2, 3, 7, 2], [1, 3, 4, 1], [13, 0, 1, 2], [7, 0, 2], [0, 0, 5, 6, 2, 2], [0, 0, 5, 2, 3, 2], [0, 0, 1, 4, 6, 2, 3, 7, 3], [0, 0, 4, 6, 2, 3, 2], [2, 0, 2, 3, 4, 7, 5, 6, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 1], [5, 0, 1, 2, 3, 4, 5, 2], [5, 1, 6, 1], [11, 0, 1, 2, 3], [12, 0, 1, 2, 3, 4, 4], [1, 0, 3, 4, 2], [1, 2, 0, 1, 3, 4, 4], [1, 0, 1, 3, 4, 3], [6, 2, 0, 1, 3, 4, 5], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [3, 0, 1, 2, 3, 4, 5, 6, 8, 9, 8], [3, 0, 1, 2, 3, 4, 5, 6, 8, 8]], [[[{"name": "icon0", "rect": [16, 5, 102, 103], "offset": [0, 0.5], "originalSize": [134, 114], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [4]], [[[11, "tqSessionDetailView"], [12, "tqSessionDetailView", [-26, -27, -28, -29, -30, -31, -32], [[17, -2, [8, 9], 7], [18, -25, -24, -23, [-14, -15, -16, -17, -18, -19, -20, -21, -22], [-5, -6, -7, -8, -9, -10, -11, -12, -13], [-3, -4]]], [0, "f0A+o6i+lEz4UCTSf/hJ6P", -1]], [2, "spinView", 1, [-33, -34, -35], [0, "37XuAKjh5Fl5zynIOw7LHg", 1], [5, 550, 308], [0, -19, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [13, "slots", [-36, -37, -38], [0, "584GAP/4hO7bRJUffr5lGu", 1], [5, 320, 260]], [2, "1", 3, [-39, -40, -41], [0, "e5ZIHQFTdBXaFWv6krqDmu", 1], [5, 72, 300], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "2", 3, [-42, -43, -44], [0, "01DfXnQhpJV63m+9WQy/TA", 1], [5, 72, 300]], [2, "3", 3, [-45, -46, -47], [0, "aaFT8RKIFAOJHrK6gTmVGd", 1], [5, 72, 300], [120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnClose", 1, [-50], [[19, 3, -49, [[21, "28f77yBgxZMGLXyCMrVDPMk", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -48]], [0, "64EC8lAVZFH5f2YArTP0T+", 1], [5, 80, 80], [546, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "layout-totalwin", 1, [-52, -53], [[22, 1, 1, 5, -51, [5, 200.2, 50]]], [0, "81HPqfXAxBLbNdYk8egk5w", 1], [5, 200.2, 50], [0, -260, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "black", 100, 1, [[23, 0, -54, 0], [20, -55, [4, 4292269782]]], [0, "b5j7ZdVw5Iqbh4RoIe6u8C", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "colum", 1, [-57], [[9, -56, 3]], [0, "6alp34KFhE0rdSOpachQN4", 1], [5, 1137, 75], [0, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "mask", 2, [3], [[10, 1, -58]], [0, "d7yCvpwHVIIYdZqaQQQ/nW", 1], [5, 360, 329], [-70, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "slot1", 4, [-59, -60], [0, "fahWDOhqRKLapIrevpZg0s", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "slot2", 4, [-61, -62], [0, "66YJjKKbhG64Oz5HW+Of/J", 1], [5, 72, 96]], [2, "slot3", 4, [-63, -64], [0, "bb7FIvWzBAIZzhTr8LtHdH", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "slot1", 5, [-65, -66], [0, "e6o7bwXrhGXJevNf53ItRk", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "slot2", 5, [-67, -68], [0, "282fBWdWhOib/z9cxDshPF", 1], [5, 72, 96]], [2, "slot3", 5, [-69, -70], [0, "a6YrWtIwFKBYbUUHpDIbRi", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "slot1", 6, [-71, -72], [0, "dbllne465B4aJHaeS4I73+", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "slot2", 6, [-73, -74], [0, "01BHYEQeJI77eracLbfUbx", 1], [5, 72, 96]], [2, "slot3", 6, [-75, -76], [0, "5czVHgo3NLpKFjmvtSdura", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "mask-kernel", 2, [-78], [[10, 0, -77]], [0, "a0OCRD3XlOko3HSdITQSJ8", 1], [5, 222, 276], [246, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "slots", 21, [-79, -80], [0, "6ccAtkeoBMTptLoNFum1EF", 1], [5, 222, 276]], [8, "nen popup", 1, [[24, 1, 0, false, -81, 1]], [0, "ddFopHK2VI568WqhE2JkTu", 1], [5, 1084, 580], [0, -18, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "sprite", 10, [[9, -82, 2]], [0, "9225fb6BBJ0LiavuNggIjd", 1], [5, 284, 38], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg", 2, [[9, -83, 4]], [0, "78fEne2ipN9LoQeJ6OqxNW", 1], [5, 833, 593], [0, 30, 0, 0, 0, 0, 1, 0.93, 0.93, 1]], [1, "skeleton", 12, [-84], [0, "a2T+DqtYNMvqbhGCdZicDh", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 26], [4, "icon", false, 12, [-85], [0, "86HmXmqHZGsZY044kbCjli", 1], [5, 134, 114]], [3, 2, false, 28], [1, "skeleton", 13, [-86], [0, "7d6SU4nGpHpq6XeLWvL4/3", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 30], [4, "icon", false, 13, [-87], [0, "5cIhTus0REwJOaFwUitLf8", 1], [5, 134, 114]], [3, 2, false, 32], [1, "skeleton", 14, [-88], [0, "b5nCywk0BOspe4WRgxk6Bx", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 34], [4, "icon", false, 14, [-89], [0, "a26SZXvJ5JGY6k9Dmet5Ll", 1], [5, 134, 114]], [3, 2, false, 36], [1, "skeleton", 15, [-90], [0, "bb6o+LrhJFyb5ojn4gDK6i", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 38], [4, "icon", false, 15, [-91], [0, "82CE3sSqVNY4/pIxCGJ99g", 1], [5, 134, 114]], [3, 2, false, 40], [1, "skeleton", 16, [-92], [0, "c6EMNuXvJGjbMnrCfnxz/f", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 42], [4, "icon", false, 16, [-93], [0, "d7KINctXFK848dqkLyWFUQ", 1], [5, 134, 114]], [3, 2, false, 44], [1, "skeleton", 17, [-94], [0, "6d7ISmWkJMX435JUUYJBzl", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 46], [4, "icon", false, 17, [-95], [0, "31wodC+RNOQoiPlnXepO7T", 1], [5, 134, 114]], [3, 2, false, 48], [1, "skeleton", 18, [-96], [0, "93deiekYhGQ67wpTZy6u0G", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 50], [4, "icon", false, 18, [-97], [0, "8aiJpovqtCcqBYg3z6Oz+e", 1], [5, 134, 114]], [3, 2, false, 52], [1, "skeleton", 19, [-98], [0, "66CwvYYWNDeKlpTB1RMJX7", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [5, 0, false, 54], [4, "icon", false, 19, [-99], [0, "91naZdAwhEWLgagTcYed0S", 1], [5, 134, 114]], [3, 2, false, 56], [1, "skeleton", 20, [-100], [0, "4a8IyYyD9K2YiuFRCyzCN6", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "Idle", 0, false, "Idle", 58], [4, "icon", false, 20, [-101], [0, "720ImkH/FNF7u8HMs0o9p5", 1], [5, 134, 114]], [3, 2, false, 60], [2, "4", 22, [-102], [0, "b2rNWzvwBFRZgMq7olGS4I", 1], [5, 72, 300], [-55, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "slot2", 62, [-103], [0, "c5cYhcCtpOBYTqlCDE75uG", 1], [5, 72, 96]], [1, "icon", 63, [-104], [0, "93xcmzDRtMP5+zwdWjLLRU", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [3, 2, false, 64], [2, "5", 22, [-105], [0, "bc1tmGvs1Ea420yeWa8SSe", 1], [5, 72, 300], [55, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "slot2", 66, [-106], [0, "56OwVq/uJPH5ucg0uvdl6A", 1], [5, 72, 96]], [1, "icon", 67, [-107], [0, "5aqmroADFORqA28JZttIY6", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [3, 2, false, 68], [15, "sprite", 7, [[25, 2, false, -108, 5]], [0, "29aAILWoFHXphMrz/UMGGA", 1], [5, 66, 67]], [1, "lbSession", 1, [-109], [0, "7cFU8wTcdJaK0/7RbKjEzL", 1], [5, 405, 30], [0, 209, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "PHIÊN: #13423534234", 24, 50, false, false, 1, 1, 1, 71], [8, "lbTotalWin", 8, [[28, "Tổng thắng: ", 32, 50, false, false, 1, 1, -110, 6]], [0, "b8cnEdzUJO76++bNhFdpuy", 1], [5, 175.2, 40], [-12.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalWinVal", 8, [-111], [0, "b3sZyknaFJGqFPMGZWf1qH", 1], [4, 4278249468], [5, 20, 40], [90.1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "0", 32, 50, false, false, 1, 1, 74]], 0, [0, 5, 1, 0, 1, 1, 0, -1, 65, 0, -2, 69, 0, -1, 27, 0, -2, 39, 0, -3, 51, 0, -4, 31, 0, -5, 43, 0, -6, 55, 0, -7, 35, 0, -8, 47, 0, -9, 59, 0, -1, 29, 0, -2, 41, 0, -3, 53, 0, -4, 33, 0, -5, 45, 0, -6, 57, 0, -7, 37, 0, -8, 49, 0, -9, 61, 0, 6, 75, 0, 7, 72, 0, 1, 1, 0, -1, 9, 0, -2, 23, 0, -3, 10, 0, -4, 2, 0, -5, 7, 0, -6, 71, 0, -7, 8, 0, -1, 25, 0, -2, 11, 0, -3, 21, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, 8, 7, 0, 1, 7, 0, -1, 70, 0, 1, 8, 0, -1, 73, 0, -2, 74, 0, 1, 9, 0, 1, 9, 0, 1, 10, 0, -1, 24, 0, 1, 11, 0, -1, 26, 0, -2, 28, 0, -1, 30, 0, -2, 32, 0, -1, 34, 0, -2, 36, 0, -1, 38, 0, -2, 40, 0, -1, 42, 0, -2, 44, 0, -1, 46, 0, -2, 48, 0, -1, 50, 0, -2, 52, 0, -1, 54, 0, -2, 56, 0, -1, 58, 0, -2, 60, 0, 1, 21, 0, -1, 22, 0, -1, 62, 0, -2, 66, 0, 1, 23, 0, 1, 24, 0, 1, 25, 0, -1, 27, 0, -1, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, -1, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, -1, 45, 0, -1, 47, 0, -1, 49, 0, -1, 51, 0, -1, 53, 0, -1, 55, 0, -1, 57, 0, -1, 59, 0, -1, 61, 0, -1, 63, 0, -1, 64, 0, -1, 65, 0, -1, 67, 0, -1, 68, 0, -1, 69, 0, 1, 70, 0, -1, 72, 0, 1, 73, 0, -1, 75, 0, 9, 1, 3, 10, 11, 111], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 53, 55, 57, 59, 61, 65, 69, 72, 75], [0, 0, 0, 0, 0, 0, 3, 11, -1, -2, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 0, 0, 3, 3], [5, 6, 7, 8, 9, 10, 2, 3, 3, 11, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 2, 2]], [[{"name": "main", "rect": [11, 0, 833, 593], "offset": [5, 0.5], "originalSize": [845, 594], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [12]]]]