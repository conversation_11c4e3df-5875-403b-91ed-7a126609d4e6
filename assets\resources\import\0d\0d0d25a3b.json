[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "ffw9P0ZiFLRb96S+X/Fgwm", "bcbgFB+epIlKdGw1JroyQo", "1cIgMKIypJ7Jy+JHaSTGQS", "d7mlxe6itGj4XYIX1nVyPI", "b7kNQjb55NHqu0qtt3mwcN", "7b1ewg3cdHw4QC79TcvPfi", "74qCZlsOtBcqB3v53xd5qm", "2cWB/vWPRHja3uQTinHH30", "7c+Q1Gtz1P1bPccssmPG85", "ffNaXj5HFGS4JcdSIrK702", "a5IclcNVVBOZNxXiPbD08w"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "data", "_parent", "_N$file", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 9, 4, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["2f106F6d2hKtaXpXFK6nvkh", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "_materials", "_N$file"], -1, 1, 3, 6]], [[3, 0, 1, 2, 2], [0, 0, 5, 2, 3, 4, 7, 2], [0, 0, 5, 6, 2, 3, 4, 7, 2], [1, 0, 1, 3, 4, 5, 3], [1, 3, 4, 5, 1], [5, 0, 2], [0, 0, 6, 2, 3, 2], [0, 0, 6, 2, 3, 4, 8, 7, 2], [0, 0, 1, 5, 2, 3, 4, 7, 3], [0, 0, 5, 6, 2, 3, 4, 2], [0, 0, 5, 2, 3, 4, 2], [6, 0, 1, 2, 1], [7, 0, 1], [3, 1, 2, 1], [8, 0, 1, 2, 3, 4, 4], [4, 0, 1, 2, 3, 4, 5, 2], [4, 1, 6, 1], [9, 0, 1, 2, 3], [1, 0, 3, 4, 5, 2], [1, 3, 4, 1], [1, 2, 0, 1, 3, 4, 5, 4], [10, 0, 1, 2, 3, 4, 5, 6, 6], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 5]], [[[[5, "tqHelpView"], [6, "tqHelpView", [-4, -5, -6, -7, -8], [[11, -2, [19, 20], 18], [12, -3]], [13, -1, 0]], [7, "content", [-10, -11, -12], [[14, 1, 2, 20, -9, [5, 1050, 1476]]], [0, "4ar1t3C4ZOdZLxV+zlTkvk", 1, 0], [5, 1050, 1476], [0, 0.5, 1], [0, 250, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 1, [-15], [[15, 3, -14, [[17, "2f106F6d2hKtaXpXFK6nvkh", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -13]], [0, "46gAAqmuhNOZjMP7zH1gdu", 1, 0], [5, 80, 80], [546, 270, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "black", 100, 1, [[18, 0, -16, [0], 1], [16, -17, [4, 4292269782]]], [0, "cdFrnFJ3pKu6xzIW5k+tX6", 1, 0], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "colum", 1, [-19], [[4, -18, [7], 8]], [0, "108dPaHl1ABaiRWwRCBZXu", 1, 0], [5, 1137, 75], [0, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "hd", 5, [-21], [[19, -20, [6]]], [0, "1egyjK1VpF6pIzQu92reyU", 1, 0], [5, 213, 38], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "scrollview", 1, [-23], [[21, false, 0.75, 0.23, null, null, -22, 2]], [0, "13WBwFf9dKVoKm3plOQwEj", 1, 0], [5, 1050, 510], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 7, [2], [[22, 0, -24, [15]]], [0, "ccZkL3NgBHnJnTaLm0bbhy", 1, 0], [5, 1050, 510]], [1, "nen popup", 1, [[20, 1, 0, false, -25, [2], 3]], [0, "b6OZuNGTlMzrccmwACKkel", 1, 0], [5, 1084, 580], [0, -18, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title_BXH", 6, [[23, "Hướng Dẫn", 25, 0, false, -26, [4], 5]], [0, "bejuCfs4BDkpOgYeQxqB6E", 1, 0], [5, 226.56, 0], [0, 53, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hd_1", 2, [[3, 2, false, -27, [9], 10]], [0, "60FuIXLvJL17HD7ax2k7Ju", 1, 0], [5, 904, 466], [0, -233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hd_2", 2, [[3, 0, false, -28, [11], 12]], [0, "d4d3TSXR5Lz4zOT1Vk1z+5", 1, 0], [5, 879, 220], [0, -596, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "hd_3", 2, [[4, -29, [13], 14]], [0, "a6lV5blW9HhL4UfGyQOwWf", 1, 0], [5, 846, 750], [0, -1101, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sprite", 3, [[3, 2, false, -30, [16], 17]], [0, "00f38BbtpHBLM90JkgyLdk", 1, 0], [5, 66, 67]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 9, 0, -3, 5, 0, -4, 7, 0, -5, 3, 0, 0, 2, 0, -1, 11, 0, -2, 12, 0, -3, 13, 0, 4, 3, 0, 0, 3, 0, -1, 14, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, -1, 10, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 5, 1, 2, 6, 8, 30], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 7, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 8, -1, -2], [0, 2, 0, 3, 0, 4, 0, 0, 5, 0, 6, 0, 7, 0, 8, 0, 0, 9, 1, 1, 10]], [[{"name": "3", "rect": [0, 0, 846, 750], "offset": [0, 0], "originalSize": [846, 750], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [11]], [[{"name": "2", "rect": [0, 0, 877, 203], "offset": [-1, 0.5], "originalSize": [879, 204], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [12]], [[{"name": "1", "rect": [0, 0, 904, 465], "offset": [0, 0.5], "originalSize": [904, 466], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [13]]]]