[1, ["d32pz7oodPO6dPzV2Ti85D", "e4SKdPPOVNvbTRqwlzUX9d", "5eBnbwUYlKVrmMfZeTKT2a"], ["_textureSetter", "spriteFrame"], ["cc.SpriteFrame", ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 3, 4, 5], [2, 0, 1, 2, 4]], [[[{"name": "<PERSON><PERSON>", "rect": [2, 2, 507, 249], "offset": [-0.5, 1.5], "originalSize": [512, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "logoS86nho", "\nlogoS86nho.png\nsize: 1969,713\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nDice1 copy\n  rotate: false\n  xy: 1861, 478\n  size: 106, 96\n  orig: 125, 107\n  offset: 10, 3\n  index: -1\nLayer 1\n  rotate: true\n  xy: 220, 2\n  size: 44, 68\n  orig: 47, 70\n  offset: 2, 1\n  index: -1\nLayer 2\n  rotate: true\n  xy: 1880, 10\n  size: 54, 61\n  orig: 56, 63\n  offset: 1, 1\n  index: -1\nLayer 3\n  rotate: true\n  xy: 1883, 298\n  size: 57, 59\n  orig: 59, 61\n  offset: 1, 1\n  index: -1\nLayer 4\n  rotate: true\n  xy: 511, 2\n  size: 52, 61\n  orig: 54, 63\n  offset: 1, 1\n  index: -1\nLayer 5\n  rotate: true\n  xy: 574, 7\n  size: 47, 62\n  orig: 49, 64\n  offset: 1, 1\n  index: -1\nLayer 6\n  rotate: true\n  xy: 1896, 206\n  size: 90, 62\n  orig: 94, 65\n  offset: 1, 2\n  index: -1\nMachine copy\n  rotate: false\n  xy: 2, 559\n  size: 369, 152\n  orig: 393, 179\n  offset: 9, 11\n  index: -1\nPoker copy 2\n  rotate: true\n  xy: 749, 216\n  size: 211, 98\n  orig: 223, 118\n  offset: 10, 8\n  index: -1\nS86 copy 2\n  rotate: false\n  xy: 2, 417\n  size: 347, 140\n  orig: 351, 144\n  offset: 3, 1\n  index: -1\nfx/Comp 1/fx1_00000\n  rotate: false\n  xy: 887, 2\n  size: 1, 1\n  orig: 1, 1\n  offset: 0, 0\n  index: -1\nfx/Comp 1/fx1_00001\n  rotate: false\n  xy: 887, 2\n  size: 1, 1\n  orig: 1, 1\n  offset: 0, 0\n  index: -1\nfx/Comp 1/fx1_00002\n  rotate: false\n  xy: 887, 2\n  size: 1, 1\n  orig: 1, 1\n  offset: 0, 0\n  index: -1\nfx/Comp 1/fx1_00060\n  rotate: false\n  xy: 887, 2\n  size: 1, 1\n  orig: 1, 1\n  offset: 0, 0\n  index: -1\nfx/Comp 1/fx1_00003\n  rotate: false\n  xy: 220, 90\n  size: 19, 41\n  orig: 376, 160\n  offset: 21, 78\n  index: -1\nfx/Comp 1/fx1_00004\n  rotate: true\n  xy: 1547, 3\n  size: 34, 98\n  orig: 376, 160\n  offset: 18, 29\n  index: -1\nfx/Comp 1/fx1_00005\n  rotate: false\n  xy: 1713, 177\n  size: 47, 116\n  orig: 376, 160\n  offset: 15, 14\n  index: -1\nfx/Comp 1/fx1_00006\n  rotate: false\n  xy: 1880, 66\n  size: 58, 121\n  orig: 376, 160\n  offset: 14, 11\n  index: -1\nfx/Comp 1/fx1_00007\n  rotate: false\n  xy: 1790, 445\n  size: 69, 126\n  orig: 376, 160\n  offset: 13, 8\n  index: -1\nfx/Comp 1/fx1_00008\n  rotate: false\n  xy: 139, 2\n  size: 79, 129\n  orig: 376, 160\n  offset: 13, 6\n  index: -1\nfx/Comp 1/fx1_00009\n  rotate: true\n  xy: 383, 112\n  size: 89, 131\n  orig: 376, 160\n  offset: 12, 5\n  index: -1\nfx/Comp 1/fx1_00010\n  rotate: false\n  xy: 1779, 55\n  size: 99, 132\n  orig: 376, 160\n  offset: 12, 4\n  index: -1\nfx/Comp 1/fx1_00011\n  rotate: false\n  xy: 1669, 40\n  size: 108, 133\n  orig: 376, 160\n  offset: 12, 4\n  index: -1\nfx/Comp 1/fx1_00012\n  rotate: true\n  xy: 1577, 175\n  size: 118, 134\n  orig: 376, 160\n  offset: 12, 3\n  index: -1\nfx/Comp 1/fx1_00013\n  rotate: false\n  xy: 1755, 297\n  size: 126, 134\n  orig: 376, 160\n  offset: 12, 3\n  index: -1\nfx/Comp 1/fx1_00014\n  rotate: true\n  xy: 2, 2\n  size: 129, 135\n  orig: 376, 160\n  offset: 12, 3\n  index: -1\nfx/Comp 1/fx1_00015\n  rotate: true\n  xy: 241, 48\n  size: 131, 135\n  orig: 376, 160\n  offset: 12, 3\n  index: -1\nfx/Comp 1/fx1_00016\n  rotate: true\n  xy: 1280, 415\n  size: 155, 135\n  orig: 376, 160\n  offset: 12, 3\n  index: -1\nfx/Comp 1/fx1_00017\n  rotate: false\n  xy: 1269, 278\n  size: 167, 135\n  orig: 376, 160\n  offset: 12, 3\n  index: -1\nfx/Comp 1/fx1_00018\n  rotate: false\n  xy: 1577, 295\n  size: 176, 136\n  orig: 376, 160\n  offset: 12, 3\n  index: -1\nfx/Comp 1/fx1_00019\n  rotate: false\n  xy: 1417, 434\n  size: 185, 137\n  orig: 376, 160\n  offset: 13, 3\n  index: -1\nfx/Comp 1/fx1_00020\n  rotate: false\n  xy: 1028, 149\n  size: 195, 138\n  orig: 376, 160\n  offset: 13, 3\n  index: -1\nfx/Comp 1/fx1_00021\n  rotate: false\n  xy: 1063, 289\n  size: 204, 139\n  orig: 376, 160\n  offset: 13, 3\n  index: -1\nfx/Comp 1/fx1_00022\n  rotate: false\n  xy: 849, 290\n  size: 212, 139\n  orig: 376, 160\n  offset: 14, 3\n  index: -1\nfx/Comp 1/fx1_00023\n  rotate: false\n  xy: 524, 147\n  size: 219, 139\n  orig: 376, 160\n  offset: 15, 4\n  index: -1\nfx/Comp 1/fx1_00024\n  rotate: false\n  xy: 524, 288\n  size: 223, 139\n  orig: 376, 160\n  offset: 18, 4\n  index: -1\nfx/Comp 1/fx1_00025\n  rotate: true\n  xy: 383, 203\n  size: 225, 139\n  orig: 376, 160\n  offset: 22, 5\n  index: -1\nfx/Comp 1/fx1_00026\n  rotate: false\n  xy: 373, 430\n  size: 229, 139\n  orig: 376, 160\n  offset: 29, 5\n  index: -1\nfx/Comp 1/fx1_00027\n  rotate: false\n  xy: 1569, 573\n  size: 235, 138\n  orig: 376, 160\n  offset: 38, 6\n  index: -1\nfx/Comp 1/fx1_00028\n  rotate: false\n  xy: 1331, 573\n  size: 236, 138\n  orig: 376, 160\n  offset: 48, 6\n  index: -1\nfx/Comp 1/fx1_00029\n  rotate: false\n  xy: 852, 573\n  size: 238, 138\n  orig: 376, 160\n  offset: 57, 7\n  index: -1\nfx/Comp 1/fx1_00030\n  rotate: false\n  xy: 373, 571\n  size: 238, 140\n  orig: 376, 160\n  offset: 67, 7\n  index: -1\nfx/Comp 1/fx1_00031\n  rotate: false\n  xy: 2, 275\n  size: 237, 140\n  orig: 376, 160\n  offset: 77, 8\n  index: -1\nfx/Comp 1/fx1_00032\n  rotate: false\n  xy: 613, 571\n  size: 237, 140\n  orig: 376, 160\n  offset: 87, 9\n  index: -1\nfx/Comp 1/fx1_00033\n  rotate: false\n  xy: 2, 133\n  size: 237, 140\n  orig: 376, 160\n  offset: 96, 10\n  index: -1\nfx/Comp 1/fx1_00034\n  rotate: false\n  xy: 1092, 572\n  size: 237, 139\n  orig: 376, 160\n  offset: 106, 11\n  index: -1\nfx/Comp 1/fx1_00035\n  rotate: true\n  xy: 241, 181\n  size: 234, 140\n  orig: 376, 160\n  offset: 117, 11\n  index: -1\nfx/Comp 1/fx1_00036\n  rotate: false\n  xy: 604, 429\n  size: 228, 140\n  orig: 376, 160\n  offset: 129, 11\n  index: -1\nfx/Comp 1/fx1_00037\n  rotate: false\n  xy: 852, 431\n  size: 217, 140\n  orig: 376, 160\n  offset: 142, 11\n  index: -1\nfx/Comp 1/fx1_00038\n  rotate: true\n  xy: 745, 2\n  size: 212, 140\n  orig: 376, 160\n  offset: 149, 12\n  index: -1\nfx/Comp 1/fx1_00039\n  rotate: false\n  xy: 1071, 430\n  size: 207, 140\n  orig: 376, 160\n  offset: 157, 12\n  index: -1\nfx/Comp 1/fx1_00040\n  rotate: true\n  xy: 887, 87\n  size: 201, 139\n  orig: 376, 160\n  offset: 165, 13\n  index: -1\nfx/Comp 1/fx1_00041\n  rotate: false\n  xy: 1028, 8\n  size: 194, 139\n  orig: 376, 160\n  offset: 174, 13\n  index: -1\nfx/Comp 1/fx1_00042\n  rotate: false\n  xy: 1604, 433\n  size: 184, 138\n  orig: 376, 160\n  offset: 184, 14\n  index: -1\nfx/Comp 1/fx1_00043\n  rotate: true\n  xy: 1438, 256\n  size: 176, 137\n  orig: 376, 160\n  offset: 193, 15\n  index: -1\nfx/Comp 1/fx1_00044\n  rotate: false\n  xy: 1225, 140\n  size: 167, 136\n  orig: 376, 160\n  offset: 203, 16\n  index: -1\nfx/Comp 1/fx1_00045\n  rotate: false\n  xy: 1806, 576\n  size: 157, 135\n  orig: 376, 160\n  offset: 213, 17\n  index: -1\nfx/Comp 1/fx1_00046\n  rotate: false\n  xy: 1394, 119\n  size: 148, 135\n  orig: 376, 160\n  offset: 222, 17\n  index: -1\nfx/Comp 1/fx1_00047\n  rotate: true\n  xy: 1224, 2\n  size: 136, 134\n  orig: 376, 160\n  offset: 234, 17\n  index: -1\nfx/Comp 1/fx1_00048\n  rotate: false\n  xy: 1547, 39\n  size: 120, 134\n  orig: 376, 160\n  offset: 250, 17\n  index: -1\nfx/Comp 1/fx1_00049\n  rotate: true\n  xy: 1360, 4\n  size: 113, 133\n  orig: 376, 160\n  offset: 257, 18\n  index: -1\nfx/Comp 1/fx1_00050\n  rotate: true\n  xy: 1762, 189\n  size: 106, 132\n  orig: 376, 160\n  offset: 264, 18\n  index: -1\nfx/Comp 1/fx1_00051\n  rotate: true\n  xy: 378, 12\n  size: 98, 131\n  orig: 376, 160\n  offset: 272, 19\n  index: -1\nfx/Comp 1/fx1_00052\n  rotate: true\n  xy: 516, 56\n  size: 89, 130\n  orig: 376, 160\n  offset: 281, 19\n  index: -1\nfx/Comp 1/fx1_00053\n  rotate: true\n  xy: 887, 5\n  size: 80, 127\n  orig: 376, 160\n  offset: 290, 20\n  index: -1\nfx/Comp 1/fx1_00054\n  rotate: false\n  xy: 648, 21\n  size: 70, 124\n  orig: 376, 160\n  offset: 300, 21\n  index: -1\nfx/Comp 1/fx1_00055\n  rotate: false\n  xy: 1883, 357\n  size: 60, 119\n  orig: 376, 160\n  offset: 310, 22\n  index: -1\nfx/Comp 1/fx1_00056\n  rotate: false\n  xy: 1495, 4\n  size: 50, 113\n  orig: 376, 160\n  offset: 319, 23\n  index: -1\nfx/Comp 1/fx1_00057\n  rotate: true\n  xy: 290, 6\n  size: 40, 67\n  orig: 376, 160\n  offset: 329, 25\n  index: -1\nfx/Comp 1/fx1_00058\n  rotate: false\n  xy: 849, 232\n  size: 28, 56\n  orig: 376, 160\n  offset: 339, 30\n  index: -1\nfx/Comp 1/fx1_00059\n  rotate: true\n  xy: 1544, 200\n  size: 13, 31\n  orig: 376, 160\n  offset: 351, 39\n  index: -1\nfx/special_star\n  rotate: true\n  xy: 1544, 215\n  size: 39, 30\n  orig: 47, 48\n  offset: 8, 9\n  index: -1\n", ["logoS86nho.png"], {"skeleton": {"hash": "IoWjONt/edl1V1el0mUhoDyz4oE", "spine": "3.8.75", "x": -107.29, "y": -72.95, "width": 228.51, "height": 145.37}, "bones": [{"name": "root"}, {"name": "Dice1 copy", "parent": "root", "x": 3.41, "y": -69.27, "scaleX": 0.4637, "scaleY": 0.4637}, {"name": "Dice1 copy2", "parent": "Dice1 copy", "x": -12.93, "y": 225.56}, {"name": "Layer 6", "parent": "Dice1 copy", "x": -142.99, "y": 275.92}, {"name": "Dice1 copy4", "parent": "Dice1 copy", "x": -184.77, "y": 70.18}, {"name": "Layer 1", "parent": "Dice1 copy", "x": 166.37, "y": 155.56}, {"name": "Layer 5", "parent": "Dice1 copy", "x": -121.35, "y": 120.54}, {"name": "Layer 4", "parent": "Dice1 copy", "x": -49.42, "y": 118.76}, {"name": "Layer 3", "parent": "Dice1 copy", "x": 31.83, "y": 121.43}, {"name": "Layer 2", "parent": "Dice1 copy", "x": 102.43, "y": 117.43}, {"name": "star", "parent": "Dice1 copy", "x": -126.59, "y": 295.71}, {"name": "star2", "parent": "Dice1 copy", "rotation": 42.8, "x": -162.18, "y": 262.35, "scaleX": 0.7114, "scaleY": 0.7114}, {"name": "star3", "parent": "Dice1 copy", "rotation": 23.04, "x": -75.23, "y": 267.07, "scaleX": 0.7114, "scaleY": 0.7114}, {"name": "star4", "parent": "Dice1 copy", "rotation": -33.52, "x": -90.87, "y": 174.39, "scaleX": -0.1573, "scaleY": -0.1573}, {"name": "star5", "parent": "Dice1 copy", "rotation": -33.52, "x": 4.6, "y": 274.88, "scaleX": -0.8557, "scaleY": -0.8557}, {"name": "star6", "parent": "Dice1 copy", "rotation": -33.52, "x": -39.81, "y": 218.94, "scaleX": -0.8557, "scaleY": -0.8557}, {"name": "star7", "parent": "Dice1 copy", "rotation": 48.91, "x": 39.61, "y": 189.05, "scaleX": -0.8557, "scaleY": -0.8557}, {"name": "star8", "parent": "Dice1 copy", "rotation": -8.02, "x": 144.23, "y": 269.33, "scaleX": -0.8557, "scaleY": -0.8557}, {"name": "star9", "parent": "Dice1 copy", "rotation": -48.93, "x": 103.23, "y": 231.32, "scaleX": -1.0414, "scaleY": -1.0414}, {"name": "star10", "parent": "Dice1 copy", "rotation": 18.38, "x": 163.87, "y": 98.95, "scaleX": -1.0414, "scaleY": -1.0414}, {"name": "star11", "parent": "Dice1 copy", "rotation": -25.89, "x": 206.57, "y": 100.23, "scaleX": -1.3914, "scaleY": -1.3914}, {"name": "star12", "parent": "Dice1 copy", "rotation": 32.12, "x": 111.35, "y": 65.22, "scaleX": -2.2113, "scaleY": -2.2113}], "slots": [{"name": "Machine copy", "bone": "Dice1 copy", "attachment": "Machine copy"}, {"name": "Layer 1", "bone": "Layer 1", "attachment": "Layer 1"}, {"name": "clip1", "bone": "Dice1 copy", "attachment": "clip1"}, {"name": "Layer 2", "bone": "Layer 2", "attachment": "Layer 2"}, {"name": "Layer 3", "bone": "Layer 3", "attachment": "Layer 3"}, {"name": "Layer 4", "bone": "Layer 4", "attachment": "Layer 4"}, {"name": "Layer 5", "bone": "Layer 5", "attachment": "Layer 5"}, {"name": "S86 copy 2", "bone": "Dice1 copy", "attachment": "S86 copy 2"}, {"name": "Poker copy 2", "bone": "Dice1 copy", "attachment": "Poker copy 2"}, {"name": "Dice1 copy", "bone": "Dice1 copy4", "attachment": "Dice1 copy"}, {"name": "fx2", "bone": "Dice1 copy2", "attachment": "fx/Comp 1/fx1_00037"}, {"name": "Layer 6", "bone": "Layer 6", "attachment": "Layer 6"}, {"name": "fx/special_star", "bone": "star", "attachment": "fx/special_star"}, {"name": "fx/special_star4", "bone": "star4", "attachment": "fx/special_star"}, {"name": "fx/special_star5", "bone": "star5", "attachment": "fx/special_star"}, {"name": "fx/special_star6", "bone": "star6", "attachment": "fx/special_star"}, {"name": "fx/special_star7", "bone": "star7", "attachment": "fx/special_star"}, {"name": "fx/special_star8", "bone": "star8", "attachment": "fx/special_star"}, {"name": "fx/special_star9", "bone": "star9", "attachment": "fx/special_star"}, {"name": "fx/special_star10", "bone": "star10", "attachment": "fx/special_star"}, {"name": "fx/special_star11", "bone": "star11", "attachment": "fx/special_star"}, {"name": "fx/special_star12", "bone": "star12", "attachment": "fx/special_star"}, {"name": "fx/special_star2", "bone": "star2", "attachment": "fx/special_star"}, {"name": "fx/special_star3", "bone": "star3", "attachment": "fx/special_star"}], "skins": [{"name": "default", "attachments": {"Layer 1": {"Layer 1": {"x": 14.66, "y": 28.14, "width": 47, "height": 70}}, "Layer 2": {"Layer 2": {"x": 0.1, "y": 4.76, "width": 56, "height": 63}}, "Layer 3": {"Layer 3": {"x": -0.81, "y": -0.23, "width": 59, "height": 61}}, "Layer 4": {"Layer 4": {"x": -1.05, "y": 3.43, "width": 54, "height": 63}}, "Layer 5": {"Layer 5": {"x": 1.37, "y": 1.16, "width": 49, "height": 64}}, "Layer 6": {"Layer 6": {"x": -6.49, "y": -1.59, "scaleX": 0.6, "scaleY": 0.6, "rotation": 8.34, "width": 94, "height": 65}}, "Dice1 copy": {"Dice1 copy": {"x": 4.79, "y": 19.01, "scaleX": 0.94, "scaleY": 0.94, "width": 125, "height": 107}}, "Machine copy": {"Machine copy": {"x": -3.98, "y": 143.19, "width": 393, "height": 179}}, "fx/special_star10": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx/special_star11": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx/special_star12": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx/special_star2": {"fx/special_star": {"x": -3.64, "y": -4.47, "scaleX": 0.7, "scaleY": 0.7, "width": 47, "height": 48}}, "fx/special_star3": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx/special_star4": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx2": {"fx/Comp 1/fx1_00000": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00001": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00002": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00003": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00004": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00005": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00006": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00007": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00008": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00009": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00010": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00011": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00012": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00013": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00014": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00015": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00016": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00017": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00018": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00019": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00020": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00021": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00022": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00023": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00024": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00025": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00026": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00027": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00028": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00029": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00030": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00031": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00032": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00033": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00034": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00035": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00036": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00037": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00038": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00039": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00040": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00041": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00042": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00043": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00044": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00045": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00046": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00047": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00048": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00049": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00050": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00051": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00052": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00053": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00054": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00055": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00056": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00057": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00058": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00059": {"width": 376, "height": 160}, "fx/Comp 1/fx1_00060": {"width": 376, "height": 160}}, "clip1": {"clip1": {"type": "clipping", "end": "Layer 5", "vertexCount": 5, "vertices": [-150.9, 160.8, 126.5, 161.73, 150.78, 110.36, 140.51, 78.61, -165.37, 79.54]}}, "fx/special_star7": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx/special_star8": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx/special_star9": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "fx/special_star6": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}, "Poker copy 2": {"Poker copy 2": {"x": 176.02, "y": 81.69, "scaleX": 0.7, "scaleY": 0.7, "width": 223, "height": 118}}, "fx/special_star": {"fx/special_star": {"x": -7.06, "y": -8.17, "scaleX": 0.6, "scaleY": 0.6, "width": 47, "height": 48}}, "S86 copy 2": {"S86 copy 2": {"x": -11.98, "y": 225.69, "width": 351, "height": 144}}, "fx/special_star5": {"fx/special_star": {"x": 0.5, "width": 47, "height": 48}}}}], "animations": {"animation": {"slots": {"fx/special_star11": {"color": [{"time": 0.7333, "color": "ffffffff"}, {"time": 0.9, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9667, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 0.5333, "name": "fx/special_star"}]}, "fx/special_star7": {"color": [{"time": 2.0667, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.3, "color": "ffffffff", "curve": "stepped"}, {"time": 3.5, "color": "ffffffff"}, {"time": 3.6667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.8667, "name": "fx/special_star"}]}, "fx/special_star9": {"color": [{"time": 2.2667, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 4.0333, "color": "ffffffff"}, {"time": 4.2, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 2.0667, "name": "fx/special_star"}]}, "fx/special_star10": {"color": [{"time": 2.4667, "color": "ffffffff"}, {"time": 2.6333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 2.2667, "name": "fx/special_star"}]}, "fx2": {"attachment": [{"name": "fx/Comp 1/fx1_00000"}, {"time": 0.0333, "name": "fx/Comp 1/fx1_00001"}, {"time": 0.0667, "name": "fx/Comp 1/fx1_00002"}, {"time": 0.1, "name": "fx/Comp 1/fx1_00003"}, {"time": 0.1333, "name": "fx/Comp 1/fx1_00004"}, {"time": 0.1667, "name": "fx/Comp 1/fx1_00005"}, {"time": 0.2, "name": "fx/Comp 1/fx1_00006"}, {"time": 0.2333, "name": "fx/Comp 1/fx1_00007"}, {"time": 0.2667, "name": "fx/Comp 1/fx1_00008"}, {"time": 0.3, "name": "fx/Comp 1/fx1_00009"}, {"time": 0.3333, "name": "fx/Comp 1/fx1_00010"}, {"time": 0.3667, "name": "fx/Comp 1/fx1_00011"}, {"time": 0.4, "name": "fx/Comp 1/fx1_00012"}, {"time": 0.4333, "name": "fx/Comp 1/fx1_00013"}, {"time": 0.4667, "name": "fx/Comp 1/fx1_00014"}, {"time": 0.5, "name": "fx/Comp 1/fx1_00015"}, {"time": 0.5333, "name": "fx/Comp 1/fx1_00016"}, {"time": 0.5667, "name": "fx/Comp 1/fx1_00017"}, {"time": 0.6, "name": "fx/Comp 1/fx1_00018"}, {"time": 0.6333, "name": "fx/Comp 1/fx1_00019"}, {"time": 0.6667, "name": "fx/Comp 1/fx1_00020"}, {"time": 0.7, "name": "fx/Comp 1/fx1_00021"}, {"time": 0.7333, "name": "fx/Comp 1/fx1_00022"}, {"time": 0.7667, "name": "fx/Comp 1/fx1_00023"}, {"time": 0.8, "name": "fx/Comp 1/fx1_00024"}, {"time": 0.8333, "name": "fx/Comp 1/fx1_00025"}, {"time": 0.8667, "name": "fx/Comp 1/fx1_00026"}, {"time": 0.9, "name": "fx/Comp 1/fx1_00027"}, {"time": 0.9333, "name": "fx/Comp 1/fx1_00028"}, {"time": 0.9667, "name": "fx/Comp 1/fx1_00029"}, {"time": 1, "name": "fx/Comp 1/fx1_00030"}, {"time": 1.0333, "name": "fx/Comp 1/fx1_00031"}, {"time": 1.0667, "name": "fx/Comp 1/fx1_00032"}, {"time": 1.1, "name": "fx/Comp 1/fx1_00033"}, {"time": 1.1333, "name": "fx/Comp 1/fx1_00034"}, {"time": 1.2, "name": "fx/Comp 1/fx1_00036"}, {"time": 1.2333, "name": "fx/Comp 1/fx1_00037"}, {"time": 1.2667, "name": "fx/Comp 1/fx1_00038"}, {"time": 1.3, "name": "fx/Comp 1/fx1_00039"}, {"time": 1.3333, "name": "fx/Comp 1/fx1_00040"}, {"time": 1.3667, "name": "fx/Comp 1/fx1_00041"}, {"time": 1.4, "name": "fx/Comp 1/fx1_00042"}, {"time": 1.4333, "name": "fx/Comp 1/fx1_00043"}, {"time": 1.4667, "name": "fx/Comp 1/fx1_00044"}, {"time": 1.5, "name": "fx/Comp 1/fx1_00045"}, {"time": 1.5333, "name": "fx/Comp 1/fx1_00046"}, {"time": 1.5667, "name": "fx/Comp 1/fx1_00047"}, {"time": 1.6, "name": "fx/Comp 1/fx1_00048"}, {"time": 1.6333, "name": "fx/Comp 1/fx1_00049"}, {"time": 1.7, "name": "fx/Comp 1/fx1_00051"}, {"time": 1.7667, "name": "fx/Comp 1/fx1_00053"}, {"time": 1.8, "name": "fx/Comp 1/fx1_00054"}, {"time": 1.8333, "name": "fx/Comp 1/fx1_00055"}, {"time": 1.8667, "name": "fx/Comp 1/fx1_00056"}, {"time": 1.9, "name": "fx/Comp 1/fx1_00057"}, {"time": 1.9333, "name": "fx/Comp 1/fx1_00058"}, {"time": 1.9667, "name": "fx/Comp 1/fx1_00059"}, {"time": 2, "name": "fx/Comp 1/fx1_00060"}]}, "fx/special_star": {"color": [{"time": 1.7667, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.9667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.5667, "name": "fx/special_star"}]}, "fx/special_star2": {"color": [{"time": 2.2, "color": "ffffffff"}, {"time": 2.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2333, "color": "ffffffff"}, {"time": 3.4, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 2, "name": "fx/special_star"}]}, "fx/special_star5": {"color": [{"time": 2.7667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.8333, "color": "ffffffff"}, {"time": 4, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 2.5667, "name": "fx/special_star"}]}, "fx/special_star6": {"color": [{"time": 2.4333, "color": "ffffffff"}, {"time": 2.6, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 2.2333, "name": "fx/special_star"}]}, "fx/special_star4": {"color": [{"time": 1.7667, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.9667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.5667, "name": "fx/special_star"}]}, "fx/special_star8": {"color": [{"time": 2.4, "color": "ffffffff"}, {"time": 2.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.4, "color": "ffffffff", "curve": "stepped"}, {"time": 3.6, "color": "ffffffff"}, {"time": 3.7667, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 2.2, "name": "fx/special_star"}]}, "fx/special_star12": {"color": [{"time": 2.0333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.4333, "color": "ffffff00"}, {"time": 3.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 4.1667, "color": "ffffffff"}, {"time": 4.3333, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 1.8333, "name": "fx/special_star"}]}, "fx/special_star3": {"color": [{"time": 2.2, "color": "ffffffff"}, {"time": 2.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2333, "color": "ffffffff"}, {"time": 3.4, "color": "ffffff00"}], "attachment": [{"name": null}, {"time": 2, "name": "fx/special_star"}]}}, "bones": {"star": {"rotate": [{"time": 1.5667}, {"time": 1.9333, "angle": -74.59}, {"time": 2.6}, {"time": 2.9667, "angle": -74.59}], "scale": [{"time": 1.5667}, {"time": 1.9333, "x": 2.351, "y": 2.351}, {"time": 2.6}, {"time": 2.9667, "x": 2.351, "y": 2.351}]}, "star2": {"rotate": [{"time": 2}, {"time": 2.3667, "angle": -74.59}, {"time": 3.0333}, {"time": 3.4, "angle": -74.59}], "scale": [{"time": 2}, {"time": 2.3667, "x": 2.351, "y": 2.351}, {"time": 3.0333}, {"time": 3.4, "x": 2.351, "y": 2.351}]}, "star3": {"rotate": [{"time": 2}, {"time": 2.3667, "angle": -74.59}, {"time": 3.0333}, {"time": 3.4, "angle": -74.59}], "scale": [{"time": 2}, {"time": 2.3667, "x": 2.351, "y": 2.351}, {"time": 3.0333}, {"time": 3.4, "x": 2.351, "y": 2.351}]}, "star4": {"rotate": [{"time": 1.5667}, {"time": 1.9333, "angle": -74.59}, {"time": 2.6}, {"time": 2.9667, "angle": -74.59}], "scale": [{"time": 1.5667}, {"time": 1.9333, "x": 2.351, "y": 2.351}, {"time": 2.6}, {"time": 2.9667, "x": 2.351, "y": 2.351}]}, "star5": {"rotate": [{"time": 2.5667}, {"time": 2.9333, "angle": -74.59}, {"time": 3.6333}, {"time": 4, "angle": -74.59}], "scale": [{"time": 2.5667}, {"time": 2.9333, "x": 2.351, "y": 2.351}, {"time": 3.6333}, {"time": 4, "x": 2.351, "y": 2.351}]}, "star6": {"rotate": [{"time": 2.2333}, {"time": 2.6, "angle": -74.59}], "scale": [{"time": 2.2333}, {"time": 2.6, "x": 2.351, "y": 2.351}]}, "star7": {"rotate": [{"time": 1.8667}, {"time": 2.2333, "angle": -74.59}, {"time": 3.3}, {"time": 3.6667, "angle": -74.59}], "scale": [{"time": 1.8667}, {"time": 2.2333, "x": 2.351, "y": 2.351}, {"time": 3.3}, {"time": 3.6667, "x": 2.351, "y": 2.351}]}, "star8": {"rotate": [{"time": 2.2}, {"time": 2.5667, "angle": -74.59}, {"time": 3.4}, {"time": 3.7667, "angle": -74.59}], "scale": [{"time": 2.2}, {"time": 2.5667, "x": 2.351, "y": 2.351}, {"time": 3.4}, {"time": 3.7667, "x": 2.351, "y": 2.351}]}, "star9": {"rotate": [{"time": 2.0667}, {"time": 2.4333, "angle": -74.59}, {"time": 3.8333}, {"time": 4.2, "angle": -74.59}], "scale": [{"time": 2.0667}, {"time": 2.4333, "x": 2.351, "y": 2.351}, {"time": 3.8333}, {"time": 4.2, "x": 2.351, "y": 2.351}]}, "star10": {"rotate": [{"time": 2.2667}, {"time": 2.6333, "angle": -74.59}], "scale": [{"time": 2.2667}, {"time": 2.6333, "x": 2.351, "y": 2.351}]}, "star11": {"rotate": [{"time": 0.5333}, {"time": 0.9, "angle": -74.59}, {"time": 1.7667}, {"time": 2.1333, "angle": -74.59}], "scale": [{"time": 0.5333}, {"time": 0.9, "x": 2.351, "y": 2.351}, {"time": 1.7667}, {"time": 2.1333, "x": 2.351, "y": 2.351}]}, "star12": {"rotate": [{"time": 1.8333}, {"time": 2.2, "angle": -74.59}, {"time": 3.0667}, {"time": 3.4333, "angle": -74.59}, {"time": 3.9667}, {"time": 4.3333, "angle": -74.59}], "scale": [{"time": 1.8333}, {"time": 2.2, "x": 2.351, "y": 2.351}, {"time": 3.0667}, {"time": 3.4333, "x": 2.351, "y": 2.351}, {"time": 3.9667}, {"time": 4.3333, "x": 2.351, "y": 2.351}]}, "Dice1 copy4": {"rotate": [{"angle": 11.77}, {"time": 0.3}, {"time": 0.9, "angle": 21.64}, {"time": 1.4667}, {"time": 2.0667, "angle": 21.64}, {"time": 2.3333, "angle": 11.77}, {"time": 2.6333}, {"time": 3.2333, "angle": 21.64}, {"time": 3.8}, {"time": 4.4, "angle": 21.64}, {"time": 4.6667, "angle": 11.77}], "translate": [{}, {"time": 0.6, "y": 22.72}, {"time": 1.1667}, {"time": 1.7667, "y": 22.72}, {"time": 2.3333}, {"time": 2.9333, "y": 22.72}, {"time": 3.5}, {"time": 4.1, "y": 22.72}, {"time": 4.6667}]}, "Layer 1": {"rotate": [{}, {"time": 0.2, "angle": -50.16, "curve": "stepped"}, {"time": 0.3667, "angle": -50.16}, {"time": 0.6333}], "translate": [{}, {"time": 0.2, "x": 4.51, "y": -28.98, "curve": "stepped"}, {"time": 0.3667, "x": 4.51, "y": -28.98}, {"time": 0.6333}]}, "Layer 5": {"translate": [{"time": 0.2}, {"time": 0.4333, "y": -90.16, "curve": "stepped"}, {"time": 0.4667, "y": 75.99}, {"time": 0.7, "y": -90.16, "curve": "stepped"}, {"time": 0.7333, "y": 75.99}, {"time": 0.9667, "y": -90.16, "curve": "stepped"}, {"time": 1, "y": 75.99}, {"time": 1.2333, "y": -90.16, "curve": "stepped"}, {"time": 1.2667, "y": 75.99}, {"time": 1.6667, "y": -90.16, "curve": "stepped"}, {"time": 1.7, "y": 75.99}, {"time": 2.2333}]}, "Layer 4": {"translate": [{"time": 0.2}, {"time": 0.5, "y": -90.16, "curve": "stepped"}, {"time": 0.5333, "y": 75.99}, {"time": 0.7667, "y": -90.16, "curve": "stepped"}, {"time": 0.8, "y": 75.99}, {"time": 1.0333, "y": -90.16, "curve": "stepped"}, {"time": 1.0667, "y": 75.99}, {"time": 1.3, "y": -90.16, "curve": "stepped"}, {"time": 1.3333, "y": 75.99}, {"time": 1.7333, "y": -90.16, "curve": "stepped"}, {"time": 1.7667, "y": 75.99}, {"time": 2.3}]}, "Layer 3": {"translate": [{"time": 0.3333}, {"time": 0.5667, "y": -90.16, "curve": "stepped"}, {"time": 0.6, "y": 75.99}, {"time": 0.8333, "y": -90.16, "curve": "stepped"}, {"time": 0.8667, "y": 75.99}, {"time": 1.1, "y": -90.16, "curve": "stepped"}, {"time": 1.1333, "y": 75.99}, {"time": 1.3667, "y": -90.16, "curve": "stepped"}, {"time": 1.4, "y": 75.99}, {"time": 1.8, "y": -90.16, "curve": "stepped"}, {"time": 1.8333, "y": 75.99}, {"time": 2.3667}]}, "Layer 2": {"translate": [{"time": 0.2667}, {"time": 0.5, "y": -90.16, "curve": "stepped"}, {"time": 0.5333, "y": 75.99}, {"time": 0.7667, "y": -90.16, "curve": "stepped"}, {"time": 0.8, "y": 75.99}, {"time": 1.0333, "y": -90.16, "curve": "stepped"}, {"time": 1.0667, "y": 75.99}, {"time": 1.3, "y": -90.16, "curve": "stepped"}, {"time": 1.3333, "y": 75.99}, {"time": 1.7333, "y": -90.16, "curve": "stepped"}, {"time": 1.7667, "y": 75.99}, {"time": 2.3}]}}}}}, [0]]], 0, 0, [0], [-1], [1]], [[[1, "<PERSON><PERSON>", 32, {"commonHeight": 37, "fontSize": 32, "atlasName": "Arial.png", "fontDefDictionary": {"32": {"xOffset": 0, "yOffset": 29, "xAdvance": 9, "rect": {"x": 243, "y": 234, "width": 0, "height": 0}}, "33": {"xOffset": 3, "yOffset": 6, "xAdvance": 9, "rect": {"x": 491, "y": 183, "width": 4, "height": 23}}, "34": {"xOffset": 1, "yOffset": 6, "xAdvance": 11, "rect": {"x": 162, "y": 234, "width": 9, "height": 9}}, "35": {"xOffset": 0, "yOffset": 6, "xAdvance": 18, "rect": {"x": 48, "y": 157, "width": 18, "height": 24}}, "36": {"xOffset": 1, "yOffset": 4, "xAdvance": 18, "rect": {"x": 375, "y": 67, "width": 16, "height": 29}}, "37": {"xOffset": 2, "yOffset": 6, "xAdvance": 28, "rect": {"x": 121, "y": 129, "width": 25, "height": 25}}, "38": {"xOffset": 1, "yOffset": 6, "xAdvance": 21, "rect": {"x": 405, "y": 129, "width": 20, "height": 24}}, "39": {"xOffset": 1, "yOffset": 6, "xAdvance": 6, "rect": {"x": 156, "y": 234, "width": 4, "height": 9}}, "40": {"xOffset": 2, "yOffset": 6, "xAdvance": 11, "rect": {"x": 85, "y": 2, "width": 8, "height": 31}}, "41": {"xOffset": 2, "yOffset": 6, "xAdvance": 11, "rect": {"x": 2, "y": 2, "width": 8, "height": 31}}, "42": {"xOffset": 1, "yOffset": 6, "xAdvance": 12, "rect": {"x": 125, "y": 234, "width": 11, "height": 10}}, "43": {"xOffset": 2, "yOffset": 10, "xAdvance": 19, "rect": {"x": 91, "y": 234, "width": 16, "height": 16}}, "44": {"xOffset": 3, "yOffset": 26, "xAdvance": 9, "rect": {"x": 173, "y": 234, "width": 4, "height": 8}}, "45": {"xOffset": 1, "yOffset": 19, "xAdvance": 11, "rect": {"x": 211, "y": 234, "width": 9, "height": 3}}, "46": {"xOffset": 3, "yOffset": 26, "xAdvance": 9, "rect": {"x": 205, "y": 234, "width": 4, "height": 4}}, "47": {"xOffset": 0, "yOffset": 6, "xAdvance": 9, "rect": {"x": 110, "y": 157, "width": 9, "height": 24}}, "48": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 138, "y": 157, "width": 15, "height": 24}}, "49": {"xOffset": 3, "yOffset": 6, "xAdvance": 18, "rect": {"x": 109, "y": 209, "width": 9, "height": 23}}, "50": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 457, "y": 183, "width": 16, "height": 23}}, "51": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 121, "y": 157, "width": 15, "height": 24}}, "52": {"xOffset": 0, "yOffset": 6, "xAdvance": 18, "rect": {"x": 125, "y": 183, "width": 16, "height": 23}}, "53": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 439, "y": 183, "width": 16, "height": 23}}, "54": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 387, "y": 129, "width": 16, "height": 24}}, "55": {"xOffset": 2, "yOffset": 6, "xAdvance": 18, "rect": {"x": 40, "y": 209, "width": 15, "height": 23}}, "56": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 2, "y": 157, "width": 16, "height": 24}}, "57": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 231, "y": 129, "width": 16, "height": 24}}, "58": {"xOffset": 3, "yOffset": 12, "xAdvance": 9, "rect": {"x": 456, "y": 209, "width": 4, "height": 17}}, "59": {"xOffset": 3, "yOffset": 12, "xAdvance": 9, "rect": {"x": 232, "y": 209, "width": 4, "height": 22}}, "60": {"xOffset": 2, "yOffset": 10, "xAdvance": 19, "rect": {"x": 73, "y": 234, "width": 16, "height": 16}}, "61": {"xOffset": 2, "yOffset": 13, "xAdvance": 19, "rect": {"x": 138, "y": 234, "width": 16, "height": 10}}, "62": {"xOffset": 2, "yOffset": 10, "xAdvance": 19, "rect": {"x": 55, "y": 234, "width": 16, "height": 16}}, "63": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 175, "y": 129, "width": 15, "height": 24}}, "64": {"xOffset": 2, "yOffset": 6, "xAdvance": 32, "rect": {"x": 107, "y": 2, "width": 30, "height": 31}}, "65": {"xOffset": 0, "yOffset": 6, "xAdvance": 21, "rect": {"x": 171, "y": 209, "width": 22, "height": 23}}, "66": {"xOffset": 2, "yOffset": 6, "xAdvance": 21, "rect": {"x": 2, "y": 209, "width": 18, "height": 23}}, "67": {"xOffset": 2, "yOffset": 6, "xAdvance": 23, "rect": {"x": 315, "y": 129, "width": 21, "height": 24}}, "68": {"xOffset": 2, "yOffset": 6, "xAdvance": 23, "rect": {"x": 88, "y": 209, "width": 19, "height": 23}}, "69": {"xOffset": 3, "yOffset": 6, "xAdvance": 21, "rect": {"x": 57, "y": 209, "width": 18, "height": 23}}, "70": {"xOffset": 3, "yOffset": 6, "xAdvance": 20, "rect": {"x": 22, "y": 209, "width": 16, "height": 23}}, "71": {"xOffset": 2, "yOffset": 6, "xAdvance": 25, "rect": {"x": 270, "y": 129, "width": 22, "height": 24}}, "72": {"xOffset": 3, "yOffset": 6, "xAdvance": 23, "rect": {"x": 146, "y": 209, "width": 18, "height": 23}}, "73": {"xOffset": 3, "yOffset": 6, "xAdvance": 9, "rect": {"x": 410, "y": 183, "width": 4, "height": 23}}, "74": {"xOffset": 1, "yOffset": 6, "xAdvance": 16, "rect": {"x": 338, "y": 129, "width": 13, "height": 24}}, "75": {"xOffset": 2, "yOffset": 6, "xAdvance": 21, "rect": {"x": 287, "y": 183, "width": 19, "height": 23}}, "76": {"xOffset": 2, "yOffset": 6, "xAdvance": 18, "rect": {"x": 206, "y": 183, "width": 15, "height": 23}}, "77": {"xOffset": 2, "yOffset": 6, "xAdvance": 27, "rect": {"x": 143, "y": 183, "width": 22, "height": 23}}, "78": {"xOffset": 2, "yOffset": 6, "xAdvance": 23, "rect": {"x": 167, "y": 183, "width": 19, "height": 23}}, "79": {"xOffset": 2, "yOffset": 6, "xAdvance": 25, "rect": {"x": 361, "y": 157, "width": 22, "height": 24}}, "80": {"xOffset": 2, "yOffset": 6, "xAdvance": 21, "rect": {"x": 244, "y": 183, "width": 18, "height": 23}}, "81": {"xOffset": 1, "yOffset": 6, "xAdvance": 25, "rect": {"x": 2, "y": 129, "width": 23, "height": 26}}, "82": {"xOffset": 3, "yOffset": 6, "xAdvance": 23, "rect": {"x": 416, "y": 183, "width": 21, "height": 23}}, "83": {"xOffset": 1, "yOffset": 6, "xAdvance": 21, "rect": {"x": 226, "y": 157, "width": 19, "height": 24}}, "84": {"xOffset": 1, "yOffset": 6, "xAdvance": 20, "rect": {"x": 120, "y": 209, "width": 19, "height": 23}}, "85": {"xOffset": 3, "yOffset": 6, "xAdvance": 23, "rect": {"x": 437, "y": 157, "width": 19, "height": 24}}, "86": {"xOffset": 0, "yOffset": 6, "xAdvance": 21, "rect": {"x": 331, "y": 183, "width": 21, "height": 23}}, "87": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 378, "y": 183, "width": 30, "height": 23}}, "88": {"xOffset": 0, "yOffset": 6, "xAdvance": 21, "rect": {"x": 264, "y": 183, "width": 21, "height": 23}}, "89": {"xOffset": 0, "yOffset": 6, "xAdvance": 21, "rect": {"x": 308, "y": 183, "width": 21, "height": 23}}, "90": {"xOffset": 1, "yOffset": 6, "xAdvance": 20, "rect": {"x": 223, "y": 183, "width": 19, "height": 23}}, "91": {"xOffset": 2, "yOffset": 6, "xAdvance": 9, "rect": {"x": 474, "y": 2, "width": 7, "height": 30}}, "93": {"xOffset": 1, "yOffset": 6, "xAdvance": 9, "rect": {"x": 483, "y": 2, "width": 7, "height": 30}}, "94": {"xOffset": 1, "yOffset": 6, "xAdvance": 15, "rect": {"x": 109, "y": 234, "width": 14, "height": 13}}, "95": {"xOffset": 0, "yOffset": 33, "xAdvance": 18, "rect": {"x": 222, "y": 234, "width": 19, "height": 3}}, "96": {"xOffset": 1, "yOffset": 6, "xAdvance": 11, "rect": {"x": 197, "y": 234, "width": 6, "height": 5}}, "97": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 293, "y": 209, "width": 16, "height": 18}}, "98": {"xOffset": 2, "yOffset": 6, "xAdvance": 18, "rect": {"x": 458, "y": 157, "width": 15, "height": 24}}, "99": {"xOffset": 1, "yOffset": 12, "xAdvance": 16, "rect": {"x": 345, "y": 209, "width": 15, "height": 18}}, "100": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 475, "y": 157, "width": 15, "height": 24}}, "101": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 327, "y": 209, "width": 16, "height": 18}}, "102": {"xOffset": 0, "yOffset": 6, "xAdvance": 9, "rect": {"x": 2, "y": 183, "width": 10, "height": 24}}, "103": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 191, "y": 157, "width": 15, "height": 24}}, "104": {"xOffset": 2, "yOffset": 6, "xAdvance": 18, "rect": {"x": 195, "y": 209, "width": 14, "height": 23}}, "105": {"xOffset": 2, "yOffset": 6, "xAdvance": 7, "rect": {"x": 141, "y": 209, "width": 3, "height": 23}}, "106": {"xOffset": -1, "yOffset": 6, "xAdvance": 7, "rect": {"x": 100, "y": 35, "width": 7, "height": 30}}, "107": {"xOffset": 2, "yOffset": 6, "xAdvance": 16, "rect": {"x": 475, "y": 183, "width": 14, "height": 23}}, "108": {"xOffset": 2, "yOffset": 6, "xAdvance": 7, "rect": {"x": 166, "y": 209, "width": 3, "height": 23}}, "109": {"xOffset": 2, "yOffset": 12, "xAdvance": 27, "rect": {"x": 462, "y": 209, "width": 23, "height": 17}}, "110": {"xOffset": 2, "yOffset": 12, "xAdvance": 18, "rect": {"x": 440, "y": 209, "width": 14, "height": 17}}, "111": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 275, "y": 209, "width": 16, "height": 18}}, "112": {"xOffset": 2, "yOffset": 12, "xAdvance": 18, "rect": {"x": 309, "y": 157, "width": 15, "height": 24}}, "113": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 344, "y": 157, "width": 15, "height": 24}}, "114": {"xOffset": 2, "yOffset": 12, "xAdvance": 11, "rect": {"x": 27, "y": 234, "width": 10, "height": 17}}, "115": {"xOffset": 1, "yOffset": 12, "xAdvance": 16, "rect": {"x": 311, "y": 209, "width": 14, "height": 18}}, "116": {"xOffset": 1, "yOffset": 7, "xAdvance": 9, "rect": {"x": 77, "y": 209, "width": 9, "height": 23}}, "117": {"xOffset": 2, "yOffset": 12, "xAdvance": 18, "rect": {"x": 39, "y": 234, "width": 14, "height": 17}}, "118": {"xOffset": 0, "yOffset": 12, "xAdvance": 16, "rect": {"x": 401, "y": 209, "width": 16, "height": 17}}, "119": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 2, "y": 234, "width": 23, "height": 17}}, "120": {"xOffset": 0, "yOffset": 12, "xAdvance": 16, "rect": {"x": 383, "y": 209, "width": 16, "height": 17}}, "121": {"xOffset": 1, "yOffset": 12, "xAdvance": 16, "rect": {"x": 155, "y": 157, "width": 16, "height": 24}}, "122": {"xOffset": 1, "yOffset": 12, "xAdvance": 16, "rect": {"x": 487, "y": 209, "width": 15, "height": 17}}, "123": {"xOffset": 1, "yOffset": 6, "xAdvance": 11, "rect": {"x": 95, "y": 2, "width": 10, "height": 31}}, "124": {"xOffset": 3, "yOffset": 6, "xAdvance": 8, "rect": {"x": 56, "y": 2, "width": 3, "height": 31}}, "125": {"xOffset": 1, "yOffset": 6, "xAdvance": 11, "rect": {"x": 139, "y": 2, "width": 10, "height": 31}}, "126": {"xOffset": 1, "yOffset": 15, "xAdvance": 19, "rect": {"x": 179, "y": 234, "width": 16, "height": 6}}, "192": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 127, "y": 67, "width": 22, "height": 29}}, "193": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 109, "y": 98, "width": 22, "height": 29}}, "194": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 43, "y": 67, "width": 22, "height": 29}}, "195": {"xOffset": 0, "yOffset": 1, "xAdvance": 21, "rect": {"x": 333, "y": 98, "width": 22, "height": 28}}, "200": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 453, "y": 35, "width": 18, "height": 29}}, "201": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 377, "y": 35, "width": 18, "height": 29}}, "202": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 397, "y": 35, "width": 18, "height": 29}}, "204": {"xOffset": 0, "yOffset": 0, "xAdvance": 9, "rect": {"x": 325, "y": 35, "width": 6, "height": 29}}, "205": {"xOffset": 2, "yOffset": 0, "xAdvance": 9, "rect": {"x": 367, "y": 67, "width": 6, "height": 29}}, "210": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 52, "y": 35, "width": 22, "height": 30}}, "211": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 280, "y": 2, "width": 22, "height": 30}}, "212": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 238, "y": 2, "width": 22, "height": 30}}, "213": {"xOffset": 2, "yOffset": 1, "xAdvance": 25, "rect": {"x": 229, "y": 67, "width": 22, "height": 29}}, "217": {"xOffset": 3, "yOffset": 0, "xAdvance": 23, "rect": {"x": 193, "y": 2, "width": 19, "height": 30}}, "218": {"xOffset": 3, "yOffset": 0, "xAdvance": 23, "rect": {"x": 348, "y": 2, "width": 19, "height": 30}}, "221": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 2, "y": 67, "width": 21, "height": 29}}, "224": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 326, "y": 157, "width": 16, "height": 24}}, "225": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 273, "y": 157, "width": 16, "height": 24}}, "226": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 173, "y": 157, "width": 16, "height": 24}}, "227": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 50, "y": 183, "width": 16, "height": 24}}, "232": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 291, "y": 157, "width": 16, "height": 24}}, "233": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 255, "y": 157, "width": 16, "height": 24}}, "234": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 208, "y": 157, "width": 16, "height": 24}}, "236": {"xOffset": 1, "yOffset": 6, "xAdvance": 9, "rect": {"x": 247, "y": 157, "width": 6, "height": 24}}, "237": {"xOffset": 3, "yOffset": 6, "xAdvance": 9, "rect": {"x": 68, "y": 183, "width": 6, "height": 24}}, "242": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 403, "y": 157, "width": 16, "height": 24}}, "243": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 32, "y": 183, "width": 16, "height": 24}}, "244": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 385, "y": 157, "width": 16, "height": 24}}, "245": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 14, "y": 183, "width": 16, "height": 24}}, "249": {"xOffset": 2, "yOffset": 6, "xAdvance": 18, "rect": {"x": 492, "y": 157, "width": 14, "height": 24}}, "250": {"xOffset": 2, "yOffset": 6, "xAdvance": 18, "rect": {"x": 421, "y": 157, "width": 14, "height": 24}}, "253": {"xOffset": 1, "yOffset": 6, "xAdvance": 16, "rect": {"x": 330, "y": 2, "width": 16, "height": 30}}, "258": {"xOffset": 0, "yOffset": 1, "xAdvance": 21, "rect": {"x": 343, "y": 67, "width": 22, "height": 29}}, "259": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 213, "y": 129, "width": 16, "height": 24}}, "272": {"xOffset": 0, "yOffset": 6, "xAdvance": 23, "rect": {"x": 354, "y": 183, "width": 22, "height": 23}}, "273": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 474, "y": 129, "width": 17, "height": 24}}, "296": {"xOffset": -1, "yOffset": 1, "xAdvance": 9, "rect": {"x": 67, "y": 67, "width": 11, "height": 29}}, "297": {"xOffset": -1, "yOffset": 6, "xAdvance": 9, "rect": {"x": 112, "y": 183, "width": 11, "height": 23}}, "360": {"xOffset": 3, "yOffset": 1, "xAdvance": 23, "rect": {"x": 2, "y": 98, "width": 19, "height": 29}}, "361": {"xOffset": 2, "yOffset": 6, "xAdvance": 18, "rect": {"x": 371, "y": 129, "width": 14, "height": 24}}, "416": {"xOffset": 2, "yOffset": 6, "xAdvance": 27, "rect": {"x": 20, "y": 157, "width": 26, "height": 24}}, "417": {"xOffset": 1, "yOffset": 12, "xAdvance": 21, "rect": {"x": 362, "y": 209, "width": 19, "height": 18}}, "431": {"xOffset": 3, "yOffset": 6, "xAdvance": 27, "rect": {"x": 427, "y": 129, "width": 24, "height": 24}}, "432": {"xOffset": 2, "yOffset": 12, "xAdvance": 21, "rect": {"x": 419, "y": 209, "width": 19, "height": 17}}, "7840": {"xOffset": 0, "yOffset": 6, "xAdvance": 21, "rect": {"x": 378, "y": 98, "width": 22, "height": 28}}, "7841": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 188, "y": 183, "width": 16, "height": 23}}, "7842": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 393, "y": 67, "width": 22, "height": 29}}, "7843": {"xOffset": 1, "yOffset": 5, "xAdvance": 18, "rect": {"x": 103, "y": 129, "width": 16, "height": 25}}, "7844": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 417, "y": 67, "width": 22, "height": 29}}, "7845": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 233, "y": 98, "width": 16, "height": 29}}, "7846": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 23, "y": 98, "width": 22, "height": 29}}, "7847": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 299, "y": 67, "width": 16, "height": 29}}, "7848": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 353, "y": 35, "width": 22, "height": 29}}, "7849": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 109, "y": 35, "width": 16, "height": 30}}, "7850": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 441, "y": 67, "width": 22, "height": 29}}, "7851": {"xOffset": 1, "yOffset": 1, "xAdvance": 18, "rect": {"x": 435, "y": 35, "width": 16, "height": 29}}, "7852": {"xOffset": 0, "yOffset": 3, "xAdvance": 21, "rect": {"x": 12, "y": 2, "width": 22, "height": 31}}, "7853": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 177, "y": 98, "width": 16, "height": 29}}, "7854": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 85, "y": 98, "width": 22, "height": 29}}, "7855": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 169, "y": 67, "width": 16, "height": 29}}, "7856": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 205, "y": 67, "width": 22, "height": 29}}, "7857": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 67, "y": 98, "width": 16, "height": 29}}, "7858": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 133, "y": 98, "width": 22, "height": 29}}, "7859": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 155, "y": 35, "width": 16, "height": 30}}, "7860": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 271, "y": 98, "width": 22, "height": 29}}, "7861": {"xOffset": 1, "yOffset": 1, "xAdvance": 18, "rect": {"x": 295, "y": 98, "width": 16, "height": 29}}, "7862": {"xOffset": 0, "yOffset": 3, "xAdvance": 21, "rect": {"x": 61, "y": 2, "width": 22, "height": 31}}, "7863": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 486, "y": 98, "width": 16, "height": 28}}, "7864": {"xOffset": 3, "yOffset": 6, "xAdvance": 21, "rect": {"x": 313, "y": 98, "width": 18, "height": 28}}, "7865": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 94, "y": 183, "width": 16, "height": 23}}, "7866": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 251, "y": 98, "width": 18, "height": 29}}, "7867": {"xOffset": 1, "yOffset": 5, "xAdvance": 18, "rect": {"x": 148, "y": 129, "width": 16, "height": 25}}, "7868": {"xOffset": 3, "yOffset": 1, "xAdvance": 21, "rect": {"x": 157, "y": 98, "width": 18, "height": 29}}, "7869": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 493, "y": 129, "width": 16, "height": 24}}, "7870": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 47, "y": 98, "width": 18, "height": 29}}, "7871": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 215, "y": 98, "width": 16, "height": 29}}, "7872": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 195, "y": 98, "width": 18, "height": 29}}, "7873": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 491, "y": 35, "width": 16, "height": 29}}, "7874": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 333, "y": 35, "width": 18, "height": 29}}, "7875": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 417, "y": 2, "width": 16, "height": 30}}, "7876": {"xOffset": 3, "yOffset": 0, "xAdvance": 21, "rect": {"x": 273, "y": 35, "width": 18, "height": 29}}, "7877": {"xOffset": 1, "yOffset": 1, "xAdvance": 18, "rect": {"x": 473, "y": 35, "width": 16, "height": 29}}, "7878": {"xOffset": 3, "yOffset": 3, "xAdvance": 21, "rect": {"x": 36, "y": 2, "width": 18, "height": 31}}, "7879": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 417, "y": 35, "width": 16, "height": 29}}, "7880": {"xOffset": 2, "yOffset": 0, "xAdvance": 9, "rect": {"x": 293, "y": 35, "width": 7, "height": 29}}, "7881": {"xOffset": 0, "yOffset": 5, "xAdvance": 7, "rect": {"x": 166, "y": 129, "width": 7, "height": 25}}, "7882": {"xOffset": 3, "yOffset": 6, "xAdvance": 9, "rect": {"x": 402, "y": 98, "width": 4, "height": 28}}, "7883": {"xOffset": 2, "yOffset": 6, "xAdvance": 7, "rect": {"x": 480, "y": 98, "width": 4, "height": 28}}, "7884": {"xOffset": 2, "yOffset": 6, "xAdvance": 25, "rect": {"x": 80, "y": 67, "width": 22, "height": 29}}, "7885": {"xOffset": 1, "yOffset": 12, "xAdvance": 18, "rect": {"x": 76, "y": 183, "width": 16, "height": 23}}, "7886": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 214, "y": 2, "width": 22, "height": 30}}, "7887": {"xOffset": 1, "yOffset": 5, "xAdvance": 18, "rect": {"x": 48, "y": 129, "width": 16, "height": 25}}, "7888": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 393, "y": 2, "width": 22, "height": 30}}, "7889": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 25, "y": 67, "width": 16, "height": 29}}, "7890": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 76, "y": 35, "width": 22, "height": 30}}, "7891": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 253, "y": 67, "width": 16, "height": 29}}, "7892": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 369, "y": 2, "width": 22, "height": 30}}, "7893": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 456, "y": 2, "width": 16, "height": 30}}, "7894": {"xOffset": 2, "yOffset": 0, "xAdvance": 25, "rect": {"x": 28, "y": 35, "width": 22, "height": 30}}, "7895": {"xOffset": 1, "yOffset": 1, "xAdvance": 18, "rect": {"x": 151, "y": 67, "width": 16, "height": 29}}, "7896": {"xOffset": 2, "yOffset": 3, "xAdvance": 25, "rect": {"x": 151, "y": 2, "width": 22, "height": 31}}, "7897": {"xOffset": 1, "yOffset": 6, "xAdvance": 18, "rect": {"x": 187, "y": 67, "width": 16, "height": 29}}, "7898": {"xOffset": 2, "yOffset": 0, "xAdvance": 27, "rect": {"x": 245, "y": 35, "width": 26, "height": 30}}, "7899": {"xOffset": 1, "yOffset": 6, "xAdvance": 21, "rect": {"x": 453, "y": 129, "width": 19, "height": 24}}, "7900": {"xOffset": 2, "yOffset": 0, "xAdvance": 27, "rect": {"x": 127, "y": 35, "width": 26, "height": 30}}, "7901": {"xOffset": 1, "yOffset": 6, "xAdvance": 21, "rect": {"x": 68, "y": 157, "width": 19, "height": 24}}, "7902": {"xOffset": 2, "yOffset": 0, "xAdvance": 27, "rect": {"x": 191, "y": 35, "width": 26, "height": 30}}, "7903": {"xOffset": 1, "yOffset": 5, "xAdvance": 21, "rect": {"x": 27, "y": 129, "width": 19, "height": 25}}, "7904": {"xOffset": 2, "yOffset": 1, "xAdvance": 27, "rect": {"x": 271, "y": 67, "width": 26, "height": 29}}, "7905": {"xOffset": 1, "yOffset": 6, "xAdvance": 21, "rect": {"x": 89, "y": 157, "width": 19, "height": 24}}, "7906": {"xOffset": 2, "yOffset": 6, "xAdvance": 27, "rect": {"x": 465, "y": 67, "width": 26, "height": 29}}, "7907": {"xOffset": 1, "yOffset": 12, "xAdvance": 21, "rect": {"x": 211, "y": 209, "width": 19, "height": 23}}, "7908": {"xOffset": 3, "yOffset": 6, "xAdvance": 23, "rect": {"x": 357, "y": 98, "width": 19, "height": 28}}, "7909": {"xOffset": 2, "yOffset": 12, "xAdvance": 18, "rect": {"x": 259, "y": 209, "width": 14, "height": 22}}, "7910": {"xOffset": 3, "yOffset": 0, "xAdvance": 23, "rect": {"x": 435, "y": 2, "width": 19, "height": 30}}, "7911": {"xOffset": 2, "yOffset": 5, "xAdvance": 18, "rect": {"x": 66, "y": 129, "width": 14, "height": 25}}, "7912": {"xOffset": 3, "yOffset": 0, "xAdvance": 27, "rect": {"x": 219, "y": 35, "width": 24, "height": 30}}, "7913": {"xOffset": 2, "yOffset": 6, "xAdvance": 21, "rect": {"x": 192, "y": 129, "width": 19, "height": 24}}, "7914": {"xOffset": 3, "yOffset": 0, "xAdvance": 27, "rect": {"x": 2, "y": 35, "width": 24, "height": 30}}, "7915": {"xOffset": 2, "yOffset": 6, "xAdvance": 21, "rect": {"x": 249, "y": 129, "width": 19, "height": 24}}, "7916": {"xOffset": 3, "yOffset": 0, "xAdvance": 27, "rect": {"x": 304, "y": 2, "width": 24, "height": 30}}, "7917": {"xOffset": 2, "yOffset": 5, "xAdvance": 21, "rect": {"x": 82, "y": 129, "width": 19, "height": 25}}, "7918": {"xOffset": 3, "yOffset": 1, "xAdvance": 27, "rect": {"x": 317, "y": 67, "width": 24, "height": 29}}, "7919": {"xOffset": 2, "yOffset": 6, "xAdvance": 21, "rect": {"x": 294, "y": 129, "width": 19, "height": 24}}, "7920": {"xOffset": 3, "yOffset": 6, "xAdvance": 27, "rect": {"x": 454, "y": 98, "width": 24, "height": 28}}, "7921": {"xOffset": 2, "yOffset": 12, "xAdvance": 21, "rect": {"x": 238, "y": 209, "width": 19, "height": 22}}, "7922": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 302, "y": 35, "width": 21, "height": 29}}, "7923": {"xOffset": 1, "yOffset": 6, "xAdvance": 16, "rect": {"x": 262, "y": 2, "width": 16, "height": 30}}, "7924": {"xOffset": 0, "yOffset": 6, "xAdvance": 21, "rect": {"x": 408, "y": 98, "width": 21, "height": 28}}, "7925": {"xOffset": 1, "yOffset": 12, "xAdvance": 16, "rect": {"x": 353, "y": 129, "width": 16, "height": 24}}, "7926": {"xOffset": 0, "yOffset": 0, "xAdvance": 21, "rect": {"x": 104, "y": 67, "width": 21, "height": 29}}, "7927": {"xOffset": 1, "yOffset": 5, "xAdvance": 16, "rect": {"x": 175, "y": 2, "width": 16, "height": 31}}, "7928": {"xOffset": 0, "yOffset": 1, "xAdvance": 21, "rect": {"x": 431, "y": 98, "width": 21, "height": 28}}, "7929": {"xOffset": 1, "yOffset": 6, "xAdvance": 16, "rect": {"x": 173, "y": 35, "width": 16, "height": 30}}}, "kerningDict": {}}]], 0, 0, [0], [1], [2]]]]