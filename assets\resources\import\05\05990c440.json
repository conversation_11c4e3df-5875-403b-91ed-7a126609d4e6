[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "a2wOmqZANHUKGY1LWhO/Jv", "a9VpD0DP5LJYQPXITZq+uj", "4dMTEeFXBJvYPZhOZS34f8", "898GrURr9CVKAa0yGY9IHW", "c1y3UL3AVHoqWPxPdQzt/K", "2cWB/vWPRHja3uQTinHH30", "52jmchGnVHU56fj8CLdj9q"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 0, 9, 4, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["9563aJgykFDxb+TJAinNxEq", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["_enabled", "horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3]], [[7, 0, 1, 2], [0, 0, 6, 3, 4, 5, 2], [1, 0, 1, 3, 4, 5, 3], [4, 0, 2], [0, 0, 7, 3, 4, 2], [0, 0, 7, 3, 4, 5, 9, 8, 2], [0, 0, 6, 7, 3, 4, 5, 8, 2], [0, 0, 1, 6, 3, 4, 5, 8, 3], [0, 0, 2, 6, 7, 3, 4, 5, 8, 3], [0, 0, 6, 7, 3, 4, 5, 2], [0, 0, 6, 3, 4, 5, 8, 2], [5, 0, 1, 2, 1], [6, 0, 1], [8, 0, 1, 2, 3, 4, 4], [3, 0, 1, 2, 3, 4, 5, 2], [3, 1, 6, 1], [9, 0, 1, 2, 3], [1, 0, 3, 4, 5, 2], [1, 2, 0, 1, 3, 4, 5, 4], [10, 0, 1, 2, 3, 4, 5, 6, 7, 7], [11, 0, 1, 1]], [[[{"name": "luatCHoiKim<PERSON>uong", "rect": [0, 0, 1195, 671], "offset": [0, 0], "originalSize": [1195, 671], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [2]], [[[3, "777HelpView"], [4, "777HelpView", [-4, -5, -6, -7], [[11, -2, [10, 11], 9], [12, -3]], [0, "c9mG3sPQJHJK+pzyqBV0a/", -1]], [5, "content", [-9], [[13, 1, 2, 50, -8, [5, 1050, 900]]], [0, "a0kO/8AU1Pdb7w+t4cOLil", 1], [5, 1050, 900], [0, 0.5, 1], [0, 245, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-12], [[14, 3, -11, [[16, "9563aJgykFDxb+TJAinNxEq", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -10]], [0, "3dfm3pLOFOuJtH5oIzCcNA", 1], [5, 80, 80], [508.4, 288.1, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "black", 100, 1, [[17, 0, -13, [0], 1], [15, -14, [4, 4292269782]]], [0, "f3BCpMDy1KSJTd5SNqmpnW", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "scrollview", false, 1, [-16], [[19, false, false, 0.75, 0.23, null, null, -15, 2]], [0, "73k14KF5dA3b5m1kq3HHhc", 1], [5, 1050, 510], [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 5, [2], [[20, -17, [6]]], [0, "52jHkTrDVMFJwLvrUmy/jk", 1], [5, 1050, 500]], [1, "nen popup", 1, [[18, 1, 0, false, -18, [2], 3]], [0, "faEnfWDERPJ53ho1KQT+pK", 1], [5, 1084, 618]], [10, "guide1", 2, [[2, 2, false, -19, [4], 5]], [0, "08CSFF/MBMapNL+g2G0TzO", 1], [5, 998, 900], [0, -450, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 3, [[2, 2, false, -20, [7], 8]], [0, "8fgtg29pVLEYjYsIXDxxjN", 1], [5, 69, 36]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 7, 0, -3, 5, 0, -4, 3, 0, 0, 2, 0, -1, 8, 0, 4, 3, 0, 0, 3, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 5, 1, 2, 6, 6, 20], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, -1, 1, 7, -1, -2], [0, 3, 0, 4, 0, 5, 0, 0, 6, 1, 1, 7]], [[{"name": "text-thele-new", "rect": [0, 12, 998, 876], "offset": [0, 0], "originalSize": [998, 900], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [8]]]]