[1, ["bes1hyeyJGVqqZGzXobpa4"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "thanrung_play", "\nthanrung_play.png\nsize: 512,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nBONUS\n  rotate: false\n  xy: 338, 464\n  size: 154, 46\n  orig: 154, 46\n  offset: 0, 0\n  index: -1\nbgbonus\n  rotate: false\n  xy: 2, 151\n  size: 182, 182\n  orig: 182, 182\n  offset: 0, 0\n  index: -1\nbonus2\n  rotate: false\n  xy: 2, 99\n  size: 157, 50\n  orig: 157, 50\n  offset: 0, 0\n  index: -1\nhopngoc\n  rotate: false\n  xy: 198, 361\n  size: 138, 149\n  orig: 138, 149\n  offset: 0, 0\n  index: -1\nrubi1\n  rotate: false\n  xy: 494, 469\n  size: 16, 16\n  orig: 16, 16\n  offset: 0, 0\n  index: -1\nrubi2\n  rotate: false\n  xy: 161, 129\n  size: 21, 20\n  orig: 21, 20\n  offset: 0, 0\n  index: -1\nrubi3\n  rotate: true\n  xy: 494, 487\n  size: 23, 16\n  orig: 23, 16\n  offset: 0, 0\n  index: -1\nrubi4\n  rotate: false\n  xy: 95, 66\n  size: 44, 31\n  orig: 44, 31\n  offset: 0, 0\n  index: -1\nrubi5\n  rotate: false\n  xy: 2, 2\n  size: 30, 30\n  orig: 30, 30\n  offset: 0, 0\n  index: -1\nrubi6\n  rotate: false\n  xy: 198, 336\n  size: 31, 23\n  orig: 31, 23\n  offset: 0, 0\n  index: -1\nrubi7\n  rotate: false\n  xy: 338, 424\n  size: 44, 38\n  orig: 44, 38\n  offset: 0, 0\n  index: -1\nrubi8\n  rotate: false\n  xy: 34, 8\n  size: 29, 24\n  orig: 29, 24\n  offset: 0, 0\n  index: -1\nrubibig\n  rotate: false\n  xy: 2, 34\n  size: 91, 63\n  orig: 91, 63\n  offset: 0, 0\n  index: -1\nsatr2\n  rotate: false\n  xy: 2, 335\n  size: 194, 175\n  orig: 194, 175\n  offset: 0, 0\n  index: -1\n", ["thanrung_play.png"], {"skins": {"default": {"bgbonus": {"bgbonus": {"x": -5.79, "width": 182, "y": 5.63, "height": 182}}, "satr2": {"satr2": {"scaleX": 0.455, "scaleY": 0.455, "x": 0.37, "width": 194, "y": -0.29, "height": 175}}, "rubi7": {"rubi7": {"x": 0.47, "width": 44, "y": -0.23, "height": 38}}, "rubi6": {"rubi6": {"x": -1.85, "width": 31, "y": 1.27, "height": 23}}, "rubi8": {"rubi8": {"x": 0.98, "width": 29, "y": -1.32, "height": 24}}, "rubibig2": {"rubibig": {"width": 91, "type": "mesh", "hull": 8, "height": 63, "triangles": [9, 4, 5, 10, 7, 0, 6, 7, 10, 9, 5, 6, 10, 9, 6, 3, 4, 9, 8, 3, 9, 1, 11, 10, 1, 10, 0, 8, 9, 10, 11, 8, 10, 2, 8, 11, 3, 8, 2, 2, 11, 1], "uvs": [0.8507, 0.05514, 1, 0.34443, 0.49614, 1, 0, 0.36157, 0.16383, 0.04871, 0.32618, 0.02025, 0.48479, 0.02514, 0.65739, 0.02165, 0.19795, 0.51371, 0.29883, 0.13014, 0.68454, 0.13443, 0.77949, 0.52228], "vertices": [33.06, 0.64, 46.57, -17.56, 0.72, -58.86, -44.43, -18.64, -29.52, 1.07, -20.07, 4.14, -0.22, 4.14, 15.57, 4.14, -24.93, -28.9, -17.23, -4.06, 18.04, -4.41, 26.72, -28.86], "edges": [8, 6, 6, 4, 18, 8, 8, 10, 18, 16, 16, 4, 16, 6, 20, 22, 22, 4, 22, 2, 2, 4, 20, 0, 0, 2, 0, 14, 10, 12, 12, 14, 18, 20, 16, 22]}}, "rubi3": {"rubi3": {"x": 0.69, "width": 23, "y": 0.83, "height": 16}}, "rubi2": {"rubi2": {"x": -0.02, "width": 21, "y": 1.01, "height": 20}}, "rubi5": {"rubi5": {"x": -0.43, "width": 30, "y": 1.14, "height": 30}}, "rubi4": {"rubi4": {"x": 4.86, "width": 44, "y": -2.1, "height": 31}}, "BONUS": {"BONUS": {"x": 2.65, "width": 154, "y": -1.18, "height": 46}}, "bonus2": {"bonus2": {"width": 157, "y": -3.1, "height": 50}}, "hopngoc": {"hopngoc": {"x": 3.93, "width": 138, "y": 0.82, "height": 149}}, "rubi1": {"rubi1": {"x": -0.75, "width": 16, "y": -1.44, "height": 16}}}}, "skeleton": {"images": "./thanrung_play/", "width": 182, "spine": "3.6.53", "hash": "klVxnp8cki+RW19WCBGSYSNCbRM", "height": 182}, "slots": [{"attachment": "bgbonus", "name": "bgbonus", "bone": "root"}, {"attachment": "hopngoc", "name": "hopngoc", "bone": "hopngoc"}, {"attachment": "rubi1", "name": "rubi1", "bone": "rubi1"}, {"attachment": "rubi2", "name": "rubi2", "bone": "rubi2"}, {"attachment": "rubi3", "name": "rubi3", "bone": "rubi3"}, {"attachment": "rubi4", "name": "rubi4", "bone": "rubi4"}, {"attachment": "rubi5", "name": "rubi5", "bone": "rubi5"}, {"attachment": "rubibig", "name": "rubibig2", "bone": "rubibig"}, {"attachment": "rubi6", "name": "rubi6", "bone": "rubi6"}, {"attachment": "rubi7", "name": "rubi7", "bone": "rubi7"}, {"attachment": "rubi8", "name": "rubi8", "bone": "rubi8"}, {"attachment": "BONUS", "name": "BONUS", "bone": "BONUS"}, {"color": "ffffff00", "attachment": "satr2", "blend": "additive", "name": "satr2", "bone": "satr2"}, {"color": "ffffff00", "blend": "additive", "name": "bonus2", "bone": "bone"}], "bones": [{"name": "root"}, {"parent": "root", "name": "BONUS", "x": -4.44, "y": -55.19}, {"parent": "BONUS", "name": "bone", "x": 4.44, "y": 2.55}, {"parent": "root", "name": "hopngoc", "x": -5.73, "y": 1.31}, {"parent": "root", "name": "rubi1", "x": -54.04, "y": 46.07}, {"parent": "root", "name": "rubi2", "x": 34.73, "y": 64.62}, {"parent": "root", "name": "rubi3", "x": -21.98, "y": 67.8}, {"parent": "root", "name": "rubi4", "x": 33.35, "y": 10.23}, {"parent": "root", "name": "rubi5", "x": 59.64, "y": 31.49}, {"parent": "root", "name": "rubi6", "x": -43.44, "y": -6.14}, {"parent": "root", "name": "rubi7", "x": -62.26, "y": 15.86}, {"parent": "root", "name": "rubi8", "x": -18.27, "y": -31.05}, {"parent": "root", "name": "rubibig", "x": -3.37, "y": 48.49}, {"parent": "rubibig", "name": "rubibig2", "x": 0.53, "y": -58.03}, {"scaleX": 0.356, "parent": "rubibig", "scaleY": 0.356, "name": "satr2", "x": 21.92, "y": -27.54}], "animations": {"animation": {"slots": {"satr2": {"color": [{"color": "ffffff00", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffffff", "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"color": "ffffff00", "time": 1.0667}]}, "bonus2": {"color": [{"color": "ffffff00", "time": 0, "curve": [0, 0.24, 0.75, 1]}, {"color": "ffffffff", "time": 0.4333, "curve": [0, 0.22, 0.75, 1]}, {"color": "ffffff00", "time": 0.6333}]}}, "bones": {"satr2": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 117.48, "time": 1.0667}], "scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.531, "y": 1.531, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0.743, "y": 0.743, "time": 1.0667}], "translate": [{"x": 0.37, "y": 0.37, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": -48.18, "y": 0.37, "time": 0.6667}]}, "bone": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0, 0.24, 0.75, 1]}, {"x": 1.287, "y": 1.287, "time": 0.2333, "curve": [0.25, 0, 1, 0.73]}, {"x": 0.899, "y": 0.899, "time": 0.4333, "curve": [0, 0.22, 0.75, 1]}, {"x": 1.364, "y": 1.364, "time": 0.6333}]}, "rubi7": {"rotate": [{"angle": 6.29, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 12.59, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": 6.29, "time": 2}], "scale": [{"x": 1.049, "y": 1.049, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.5}, {"x": 1.098, "y": 1.098, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"x": 1.049, "y": 1.049, "time": 2}], "translate": [{"x": 1.1, "y": 1.86, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 2.19, "y": 3.71, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"x": 1.1, "y": 1.86, "time": 2}]}, "rubi6": {"rotate": [{"angle": -1.28, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"angle": 0, "time": 0.1667}, {"angle": -13.24, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"angle": -1.28, "time": 2}], "scale": [{"x": 1.005, "y": 1.005, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"x": 1, "y": 1, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.048, "y": 1.048, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"x": 1.005, "y": 1.005, "time": 2}], "translate": [{"x": 0, "y": 0.65, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"x": 0, "y": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 6.75, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"x": 0, "y": 0.65, "time": 2}]}, "rubi8": {"rotate": [{"angle": 5.52, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 11.05, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": 5.52, "time": 2}], "scale": [{"x": 1.028, "y": 1.028, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.057, "y": 1.057, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"x": 1.028, "y": 1.028, "time": 2}], "translate": [{"x": 0, "y": 2.42, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.5}, {"x": 0, "y": 4.85, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"x": 0, "y": 2.42, "time": 2}]}, "rubi3": {"rotate": [{"angle": 3.73, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 13.14, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 3.73, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3333}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 1.37, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"x": 0, "y": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 4.82, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"x": 0, "y": 1.37, "time": 2}]}, "rubibig": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.094, "y": 1.094, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 6.51, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "rubi2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"angle": 0, "time": 2}], "scale": [{"x": 1.042, "y": 1.042, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"x": 1, "y": 1, "time": 0.3333}, {"x": 1.149, "y": 1.149, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"x": 1.042, "y": 1.042, "time": 2}], "translate": [{"x": 0, "y": 1.74, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"x": 0, "y": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 6.13, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"x": 0, "y": 1.74, "time": 2}]}, "rubi5": {"rotate": [{"angle": 8.51, "time": 0, "curve": [0.382, 0.58, 0.731, 1]}, {"angle": 0, "time": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"angle": 35.12, "time": 1.3, "curve": [0.243, 0, 0.655, 0.63]}, {"angle": 8.51, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": -0.21, "y": 2.33, "time": 0, "curve": [0.382, 0.58, 0.731, 1]}, {"x": 0, "y": 0, "time": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"x": -0.88, "y": 9.64, "time": 1.3, "curve": [0.243, 0, 0.655, 0.63]}, {"x": -0.21, "y": 2.33, "time": 2}]}, "rubi4": {"rotate": [{"angle": 10.13, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 0, "time": 0.6667}, {"angle": 14.15, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": 10.13, "time": 2}], "scale": [{"x": 1.048, "y": 1.048, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"x": 1, "y": 1, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.067, "y": 1.067, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"x": 1.048, "y": 1.048, "time": 2}], "translate": [{"x": 0, "y": 3.86, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"x": 0, "y": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 5.39, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"x": 0, "y": 3.86, "time": 2}]}, "BONUS": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0, 0.29, 0.75, 1]}, {"x": 1.258, "y": 1.258, "time": 0.2333, "curve": [0.25, 0, 1, 0.76]}, {"x": 0.85, "y": 0.85, "time": 0.4, "curve": [0, 0.26, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.5667}]}, "hopngoc": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.67, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.18, "time": 0.4, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.54, "time": 0.5333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.39, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 7.33, "time": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1}], "scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"curve": "stepped", "x": 1.068, "y": 1.068, "time": 0.3333}, {"x": 1.068, "y": 1.068, "time": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 0.8}]}, "rubi1": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"angle": 0, "time": 2}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3333}, {"x": 1, "y": 1, "time": 2}], "translate": [{"x": 0, "y": 0.99, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"x": 0, "y": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 3.5, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"x": 0, "y": 0.99, "time": 2}]}}, "deform": {"default": {"rubibig2": {"rubibig": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"offset": 16, "time": 1, "vertices": [-4.42399, 2.21201, -2.21201, 0.31599, -5.02299, 0.19999, -8.42098, 0.69001], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]