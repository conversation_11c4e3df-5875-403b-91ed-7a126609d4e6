[1, ["ecpdLyjvZBwrvm+cedCcQy", "9awKuJe0ZAdYuaoLZYOaoE", "c1wU3moYhLD5kcPlTyLZdA", "21Him8aAdMrIQb/pjZOPcq", "5dymnr47ZCQbdFWa5R0fXs", "72hOmdmRVOroAyvCL3NlhD", "22jiCBYxBA56//kXLWPV3+", "b8SLH+vEdL7L4naFWSTFcr", "d3fY1Ht7xOALhU0VInGTqc", "5b4hxm6C5Ac4ld/9Eul9gs", "f9/02DZphCbKENzqKecj5l", "bb6vT7FedI8ZHcQf2pzr6Z", "32+h9fDRBJHb83ignkVK77", "9evCZeDmNAVLBYU+AIUM/Z", "a9VpD0DP5LJYQPXITZq+uj", "33ORapUatKRaYrTfoA3zjX", "2c5Qe0LPxHk5No1ESq/gf5", "017Jn3Zv1Ft7hygdjpaSoK", "fe0Q4nSnxJbYIf1G3Q2Lx5", "49mwvGKDlEqbNnn0IdTrDT", "2atMffXglD1obk1ZHE13mS", "5drovY8MlOspJiOCZY7Xl4", "c6k1MPIApGFrS5RsKkmSDM", "a0OD6Di4pNeoIWF8VQftPV", "e0nqCFjNhM17+CB6MQ6unj", "c6jR5K5WZNeb7flMBDkGut", "eeDMKtsPVFRqsepGwW6knW", "8dZECSSLxGsKFbxX2Bjrut", "acwsoRumJP+YuQgSe+jpE/", "065nqC3vNH4pBQzTIwwCAu", "04Vdr7AjFEMLzfCxECrkjT", "161lpNle1E1KEyNK2S+y5C", "dd8evaX0ZL8qf9OcIQoNjz", "20el/BDytKPKAT1pBpoktj", "9cbts3WDJHzZf2IvuzDJk7", "5eXuauMpxJjZ9ihsV4DwjV", "6eZnM64IxOP6c3N5MxCA8w", "30TnRXZVlAEZSIseGVZxMN", "dcA7X5vW5M25Yw0F4N6qqY", "a0HDoSsMJCdYF8UBOUFFsp", "8bkpA4LWJIjZ/IkVJCrxpi", "c5gw3vNUNOArKjsySV3lqg", "4doqH33zpG97eZvjEpi87M"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "_N$normalSprite", "_defaultClip", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "btnContinue", "btnClose", "lbiX2", "lbiLastWin", "data", "_parent", "sfPicked", "sfMiss", "sfResultPicked", "sfResultMiss"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_zIndex", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children"], -1, 9, 4, 5, 1, 7, 2], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$overflow", "_spacingX", "node", "_N$file", "_materials"], -6, 1, 6, 3], ["cc.Node", ["_name", "_zIndex", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 1, 12, 4, 5, 7, 1, 2], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$normalColor", "_N$target"], 1, 1, 9, 5, 5, 5, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_active", "_zIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 0, 1, 2, 4, 5, 7], ["3e3banFXDxO+Zyge/3lWOgS", ["node", "lbiLastWin", "lbiX2", "btnClose", "btnContinue", "btnPicks", "spriteResults", "lbiResults", "sfNormals", "sfPicked", "sfMiss", "sfResultPicked", "sfResultMiss"], 3, 1, 1, 1, 1, 1, 2, 2, 2, 3, 6, 6, 6, 6], ["cc.Layout", ["_enabled", "_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1]], [[6, 0, 1, 2], [2, 0, 1, 2, 3, 4, 3], [4, 0, 1, 6, 2, 3, 4, 5, 3], [2, 2, 3, 4, 1], [14, 0, 1], [1, 0, 1, 7, 4, 5, 6, 8, 3], [5, 0, 1, 2, 3, 4, 5, 7, 3], [1, 0, 1, 7, 9, 4, 5, 6, 8, 3], [1, 0, 2, 1, 7, 9, 4, 5, 6, 8, 4], [4, 0, 1, 6, 7, 2, 3, 4, 5, 3], [9, 0, 1, 2, 3, 4, 5, 6, 7, 4], [12, 0, 1, 2, 1], [7, 0, 1, 2, 3, 4], [2, 2, 4, 1], [2, 0, 1, 2, 3], [3, 0, 2, 3, 1, 9, 10, 5], [3, 0, 2, 3, 6, 1, 4, 5, 7, 9, 11, 10, 9], [7, 0, 1, 3, 3], [8, 0, 2], [1, 0, 1, 9, 4, 5, 6, 3], [1, 0, 3, 1, 7, 4, 5, 6, 4], [1, 0, 1, 7, 4, 5, 6, 3], [1, 0, 2, 1, 7, 4, 5, 6, 8, 4], [1, 0, 7, 4, 5, 6, 8, 2], [4, 0, 1, 7, 2, 3, 4, 5, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [6, 1, 1], [11, 0, 1, 2, 3, 4, 5, 6, 6], [2, 0, 2, 3, 4, 2], [2, 0, 1, 2, 3, 3], [3, 0, 2, 3, 1, 9, 11, 10, 5], [3, 0, 2, 1, 4, 5, 9, 11, 10, 6], [3, 0, 1, 8, 4, 5, 9, 11, 10, 6], [5, 2, 6, 1], [5, 0, 1, 2, 3, 4, 5, 3], [13, 0, 1]], [[[{"name": "btn_choiTiep_da", "rect": [0, 0, 185, 185], "offset": [0, 0], "originalSize": [185, 185], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [10]], [[{"name": "btn_Dung_da", "rect": [0, 0, 185, 185], "offset": [0, 0], "originalSize": [185, 185], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [11]], [[{"name": "tex_trung", "rect": [0, 0, 139, 61], "offset": [0, 0], "originalSize": [139, 61], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [12]], [[{"name": "logo", "rect": [0, 0, 387, 101], "offset": [0, 0], "originalSize": [387, 101], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [13]], [[[18, "dbX2GameView"], [19, "dbX2GameView", 0, [-19, -20, -21, -22, -23, -24, -25, -26, -27], [[25, -18, -17, -16, -15, -14, [-10, -11, -12, -13], [-6, -7, -8, -9], [-2, -3, -4, -5], [59, 60, 61, 62], 63, 64, 65, 66]], [26, -1], [5, 1280, 720]], [7, "layout", 0, 1, [-29, -30, -31, -32, -33, -34, -35, -36], [[27, false, 1, 1, 100, 20, -28, [5, 914, 186]]], [0, "3egqFujgdA5KF4X/i6HsgO", 1], [5, 914, 186], [0, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "button", 0, 2, [-39, -40], [[[1, 2, false, -37, [25], 26], -38], 4, 1], [0, "c3k9K2iFlI3Jo/IE21lF/h", 1], [5, 300, 238], [-380, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "button", 0, 2, [-43, -44], [[[1, 2, false, -41, [31], 32], -42], 4, 1], [0, "063fFLVZRNFb4D50/wcrEg", 1], [5, 300, 238], [-126, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "button", 0, 2, [-47, -48], [[[1, 2, false, -45, [37], 38], -46], 4, 1], [0, "78LsUIChdP8IQZPS520ZqD", 1], [5, 300, 238], [127.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "button", 0, 2, [-51, -52], [[[1, 2, false, -49, [43], 44], -50], 4, 1], [0, "593UsikIVF9atRFvxDT/3n", 1], [5, 300, 238], [380.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "bg_coin", false, 0, 3, [-55], [[3, -53, [21], 22], [11, -54, [24], 23]], [0, "95AnxjHG1Bh5cR40xfgT1w", 1], [5, 222, 54], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg_coin", false, 0, 4, [-58], [[13, -56, 28], [11, -57, [30], 29]], [0, "17DI60YF5GkqU1HOc+xWIt", 1], [5, 222, 54], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg_coin", false, 0, 5, [-61], [[13, -59, 34], [11, -60, [36], 35]], [0, "bb2jFencBGX69ejKBH2Cou", 1], [5, 222, 54], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg_coin", false, 0, 6, [-64], [[13, -62, 40], [11, -63, [42], 41]], [0, "4fpugZ8ctArpLRAIpzp3pT", 1], [5, 222, 54], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "lbWin", 0, [-67], [[[16, "1,000,000,000", 15.5, 78, false, false, 1, 1, 2, -65, [47], 48], -66], 4, 1], [0, "48ynYTNilNmYQCFY00bMKQ", 1], [5, 233.95, 37.78], [10.547, 4.428, 0, 0, 0, 0, 1, 0.754, 0.754, 0.754]], [2, "btnStop", 0, 1, [[[1, 2, false, -68, [55], 56], -69], 4, 1], [0, "59cwnW/xJOZZqOKvOhMPrd", 1], [5, 185, 185], [-448, -251, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "black", 0, 0, 1, [[28, 0, -70, [0], 1], [33, -71, [4, 4292269782]]], [0, "8boV/cHa1F+7naKgg3D87A", 1], [5, 3000, 3000]], [21, "sprite", 0, 1, [[1, 2, false, -72, [2], 3], [35, -73]], [0, "cfkmBjIRBIqbGh3JE9+T/d", 1], [5, 1560, 720]], [7, "bg_tex", 0, 1, [-75], [[3, -74, [8], 9]], [0, "84FOAj3wdDeqw82mugKekH", 1], [5, 656, 52], [0, 180, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbWin", 0, 7, [[[30, "1,000,000,000", 16, 78, false, -76, [19], 20], -77], 4, 1], [0, "b0KOZJbJdHLKAOTPslOGxY", 1], [5, 225.5, 78], [0, -20.365, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbWin", 0, 8, [[[15, "1,000,000,000", 16, 78, false, -78, 27], -79], 4, 1], [0, "22/D5qcvRNKoV9E13j/opl", 1], [5, 24.5, 39], [0, -20.365, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbWin", 0, 9, [[[15, "1,000,000,000", 16, 78, false, -80, 33], -81], 4, 1], [0, "62/wZWViFGOqYDl5S5AnRh", 1], [5, 24.5, 39], [0, -20.365, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbWin", 0, 10, [[[15, "1,000,000,000", 16, 78, false, -82, 39], -83], 4, 1], [0, "6c8u/q8DVKYK+Bm4pfxv8E", 1], [5, 24.5, 39], [0, -20.365, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "spriteWin", 0, 1, [11], [[1, 2, false, -84, [49], 50]], [0, "00PiAxJU1IaIdsVs+F40tO", 1], [5, 221, 58], [-155.457, -259.26, 0, 0, 0, 0, 1, 1.49, 1.49, 1.49]], [7, "spriteX2", 0, 1, [-86], [[1, 2, false, -85, [53], 54]], [0, "b7w5+qJ/xLr6lOVCEIkYik", 1], [5, 424, 122], [156.015, -261.283, 0, 0, 0, 0, 1, 0.695, 0.695, 0.695]], [2, "lbX2", 0, 21, [[[16, "1,000,000,000", 15.5, 78, false, false, 1, 1, 2, -87, [51], 52], -88], 4, 1], [0, "23s6rC1vxNFJaacA97Stkf", 1], [5, 233.95, 37.78], [1.933, 13.999, 0, 0, 0, 0, 1, 1.624, 1.624, 1.624]], [2, "btnQuickPlay", 0, 1, [[[1, 2, false, -89, [57], 58], -90], 4, 1], [0, "faAQjtoJNCpr68f0hbNE4w", 1], [5, 185, 185], [468, -251, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "logo", false, 0, 1, [[3, -91, [4], 5]], [0, "27pGpu5HdNPanTbVCABB8I", 1], [5, 387, 101], [0, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "label", 0, 15, [[31, "Chọn 1 túi và mở", 30, false, 1, 1, -92, [6], 7]], [0, "29A7KZMuNNRp3DciGyL05l", 1], [5, 234, 40], [19, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_ngoc", 0, 2, [[3, -93, [10], 11]], [0, "91PprcYyFG1p0oaMNciHiH", 1], [5, 165, 76], [-357.1, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_ngoc", 0, 2, [[3, -94, [12], 13]], [0, "074SyjYfZM1LiYL6AV9i3g", 1], [5, 165, 76], [-100.9, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_ngoc", 0, 2, [[3, -95, [14], 15]], [0, "4eCtL4GXtAGKigyFtcQy0e", 1], [5, 165, 76], [153.1, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_ngoc", 0, 2, [[3, -96, [16], 17]], [0, "12Bi9fbmJAm6wBryEdnarz", 1], [5, 165, 76], [402.8, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "spriteResult", false, 0, 3, [-97], [0, "79cF4tiUJDCZfq502wdw6m", 1], [5, 139, 61], [0, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [29, 2, false, 30, [18]], [4, 16], [6, 1.05, 3, 3, [[12, "3e3banFXDxO+Zyge/3lWOgS", "pickClicked", "0", 1]], [4, 4294967295], [4, 4294967295], 3], [10, "spriteResult", false, 0, 4, [-98], [0, "3dlO2e8IVK4JhaxAt14a2o", 1], [5, 139, 61], [0, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 2, false, 34], [4, 17], [6, 1.05, 3, 4, [[12, "3e3banFXDxO+Zyge/3lWOgS", "pickClicked", "1", 1]], [4, 4294967295], [4, 4294967295], 4], [10, "spriteResult", false, 0, 5, [-99], [0, "devlms9CRKaotvVgBxQaOO", 1], [5, 139, 61], [0, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 2, false, 38], [4, 18], [6, 1.05, 3, 5, [[12, "3e3banFXDxO+Zyge/3lWOgS", "pickClicked", "2", 1]], [4, 4294967295], [4, 4294967295], 5], [10, "spriteResult", false, 0, 6, [-100], [0, "a8AYFkRf9DsaVemDT+Gd6S", 1], [5, 139, 61], [0, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 2, false, 42], [4, 19], [6, 1.05, 3, 6, [[12, "3e3banFXDxO+Zyge/3lWOgS", "pickClicked", "3", 1]], [4, 4294967295], [4, 4294967295], 6], [23, "New Label", 11, [[32, "X2", false, -2, 1, 1, -101, [45], 46]], [0, "e8d0AzJTFN1JDKY3miN6mE", 1], [5, 86.75, 40], [259.329, 75.912, 0, 0, 0, 0, 1, 0.516, 0.516, 0.516]], [4, 11], [4, 22], [6, 1.05, 2, 12, [[17, "3e3banFXDxO+Zyge/3lWOgS", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], 12], [34, 1.05, 2, 23, [[17, "3e3banFXDxO+Zyge/3lWOgS", "continueClicked", 1]], [4, 4294967295], [4, 4294967295]]], 0, [0, 9, 1, 0, -1, 32, 0, -2, 36, 0, -3, 40, 0, -4, 44, 0, -1, 31, 0, -2, 35, 0, -3, 39, 0, -4, 43, 0, -1, 33, 0, -2, 37, 0, -3, 41, 0, -4, 45, 0, 10, 50, 0, 11, 49, 0, 12, 48, 0, 13, 47, 0, 0, 1, 0, -1, 13, 0, -2, 14, 0, -3, 24, 0, -4, 15, 0, -5, 2, 0, -6, 20, 0, -7, 21, 0, -8, 12, 0, -9, 23, 0, 0, 2, 0, -1, 26, 0, -2, 27, 0, -3, 28, 0, -4, 29, 0, -5, 3, 0, -6, 4, 0, -7, 5, 0, -8, 6, 0, 0, 3, 0, -2, 33, 0, -1, 30, 0, -2, 7, 0, 0, 4, 0, -2, 37, 0, -1, 34, 0, -2, 8, 0, 0, 5, 0, -2, 41, 0, -1, 38, 0, -2, 9, 0, 0, 6, 0, -2, 45, 0, -1, 42, 0, -2, 10, 0, 0, 7, 0, 0, 7, 0, -1, 16, 0, 0, 8, 0, 0, 8, 0, -1, 17, 0, 0, 9, 0, 0, 9, 0, -1, 18, 0, 0, 10, 0, 0, 10, 0, -1, 19, 0, 0, 11, 0, -2, 47, 0, -1, 46, 0, 0, 12, 0, -2, 49, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 25, 0, 0, 16, 0, -2, 32, 0, 0, 17, 0, -2, 36, 0, 0, 18, 0, -2, 40, 0, 0, 19, 0, -2, 44, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, -2, 48, 0, 0, 23, 0, -2, 50, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, -1, 31, 0, -1, 35, 0, -1, 39, 0, -1, 43, 0, 0, 46, 0, 14, 1, 11, 15, 20, 101], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 33, 35, 37, 39, 41, 43, 45, 49, 49, 49, 49, 50, 50, 50, 50], [-1, 1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 3, -1, 1, 5, -1, -1, 1, 3, 1, 5, -1, -1, 1, 3, 1, 5, -1, -1, 1, 3, 1, 5, -1, -1, 1, -1, 3, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, -1, -2, -3, -4, 16, 17, 18, 19, 1, 4, 1, 4, 1, 4, 1, 4, 4, 6, 7, 8, 4, 6, 7, 8], [0, 14, 0, 15, 0, 16, 0, 17, 0, 18, 0, 4, 0, 4, 0, 4, 0, 4, 0, 0, 2, 0, 5, 1, 1, 0, 6, 2, 5, 1, 1, 0, 6, 2, 5, 1, 1, 0, 6, 2, 5, 1, 1, 0, 6, 0, 19, 0, 2, 0, 20, 0, 2, 0, 21, 0, 7, 0, 8, 22, 23, 24, 25, 26, 27, 3, 28, 3, 9, 3, 9, 3, 9, 3, 9, 7, 7, 7, 29, 8, 8, 8, 30]], [[{"name": "bg_ngoc", "rect": [0, 0, 165, 76], "offset": [0, 0], "originalSize": [165, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [31]], [[{"name": "bg_coin", "rect": [0, 0, 222, 54], "offset": [0, 0], "originalSize": [222, 54], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [32]], [[{"name": "truot", "rect": [0, 0, 118, 158], "offset": [0, 0], "originalSize": [118, 158], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [33]], [[{"name": "2", "rect": [0, 0, 154, 156], "offset": [0, 0], "originalSize": [154, 156], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [34]], [[{"name": "tex_truot", "rect": [0, 0, 134, 66], "offset": [0, 0], "originalSize": [134, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [35]], [[{"name": "btn_Dung", "rect": [0, 0, 185, 185], "offset": [0, 0], "originalSize": [185, 185], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [36]], [[{"name": "4", "rect": [0, 0, 153, 153], "offset": [0, 0], "originalSize": [153, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [37]], [[{"name": "1", "rect": [0, 0, 154, 154], "offset": [0, 0], "originalSize": [154, 154], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [38]], [[{"name": "btn_choiTiep", "rect": [0, 0, 185, 185], "offset": [0, 0], "originalSize": [185, 185], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [39]], [[{"name": "3", "rect": [0, 0, 153, 155], "offset": [0, 0], "originalSize": [153, 155], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [40]], [[{"name": "effect", "rect": [3, 4, 247, 249], "offset": [1.5, -0.5], "originalSize": [250, 256], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [41]], [[{"name": "bg_tex", "rect": [0, 0, 656, 52], "offset": [0, 0], "originalSize": [656, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [42]]]]