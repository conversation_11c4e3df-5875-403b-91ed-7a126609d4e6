[1, ["424PJ/J7pMF5OyiBwvAiVz"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "thanrung2_play", "\nthanrung2_play.png\nsize: 512,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nJackPot2\n  rotate: false\n  xy: 347, 298\n  size: 155, 47\n  orig: 155, 47\n  offset: 0, 0\n  index: -1\nLayer 1\n  rotate: false\n  xy: 155, 153\n  size: 164, 52\n  orig: 164, 52\n  offset: 0, 0\n  index: -1\nLayer 10\n  rotate: true\n  xy: 2, 2\n  size: 209, 111\n  orig: 209, 111\n  offset: 0, 0\n  index: -1\nLayer 11\n  rotate: true\n  xy: 155, 69\n  size: 82, 125\n  orig: 82, 125\n  offset: 0, 0\n  index: -1\nLayer 12\n  rotate: false\n  xy: 359, 67\n  size: 53, 68\n  orig: 53, 68\n  offset: 0, 0\n  index: -1\nLayer 13\n  rotate: false\n  xy: 455, 208\n  size: 53, 88\n  orig: 53, 88\n  offset: 0, 0\n  index: -1\nLayer 14\n  rotate: false\n  xy: 359, 52\n  size: 14, 13\n  orig: 14, 13\n  offset: 0, 0\n  index: -1\nLayer 15\n  rotate: true\n  xy: 329, 308\n  size: 17, 16\n  orig: 17, 16\n  offset: 0, 0\n  index: -1\nLayer 16\n  rotate: true\n  xy: 329, 217\n  size: 79, 124\n  orig: 79, 124\n  offset: 0, 0\n  index: -1\nLayer 17\n  rotate: true\n  xy: 414, 103\n  size: 32, 30\n  orig: 32, 30\n  offset: 0, 0\n  index: -1\nLayer 18\n  rotate: false\n  xy: 115, 15\n  size: 32, 34\n  orig: 32, 34\n  offset: 0, 0\n  index: -1\nLayer 19\n  rotate: false\n  xy: 414, 77\n  size: 26, 24\n  orig: 26, 24\n  offset: 0, 0\n  index: -1\nLayer 2\n  rotate: false\n  xy: 282, 42\n  size: 75, 109\n  orig: 75, 109\n  offset: 0, 0\n  index: -1\nLayer 20\n  rotate: false\n  xy: 85, 213\n  size: 76, 296\n  orig: 76, 296\n  offset: 0, 0\n  index: -1\nLayer 21\n  rotate: false\n  xy: 163, 207\n  size: 164, 118\n  orig: 164, 118\n  offset: 0, 0\n  index: -1\nLayer 22\n  rotate: false\n  xy: 2, 213\n  size: 81, 296\n  orig: 81, 296\n  offset: 0, 0\n  index: -1\nLayer 23\n  rotate: true\n  xy: 492, 166\n  size: 40, 18\n  orig: 40, 18\n  offset: 0, 0\n  index: -1\nLayer 24\n  rotate: true\n  xy: 259, 40\n  size: 27, 11\n  orig: 27, 11\n  offset: 0, 0\n  index: -1\nLayer 25\n  rotate: false\n  xy: 359, 137\n  size: 79, 78\n  orig: 79, 78\n  offset: 0, 0\n  index: -1\nLayer 3\n  rotate: true\n  xy: 115, 51\n  size: 160, 38\n  orig: 160, 38\n  offset: 0, 0\n  index: -1\nRectangle 1\n  rotate: true\n  xy: 155, 8\n  size: 59, 102\n  orig: 59, 102\n  offset: 0, 0\n  index: -1\nbg2jack\n  rotate: false\n  xy: 347, 347\n  size: 162, 162\n  orig: 162, 162\n  offset: 0, 0\n  index: -1\nbgbonus\n  rotate: false\n  xy: 163, 327\n  size: 182, 182\n  orig: 182, 182\n  offset: 0, 0\n  index: -1\nelip\n  rotate: false\n  xy: 440, 156\n  size: 50, 50\n  orig: 50, 50\n  offset: 0, 0\n  index: -1\n", ["thanrung2_play.png"], {"skins": {"default": {"Layer 26": {"Layer 25": {"x": 2.86, "width": 79, "y": 0.85, "height": 78}}, "Layer 25": {"Layer 25": {"x": 2.86, "width": 79, "y": 0.85, "height": 78}}, "Layer 24": {"Layer 24": {"rotation": 167.76, "x": 8.12, "width": 27, "y": 1.16, "height": 11}}, "cliping": {"cliping": {"color": "ce3a3aff", "end": "cliping", "type": "clipping", "vertexCount": 21, "vertices": [79.17, -63.87, 75.99, -71.1, 71.75, -75.72, 64.21, -78.91, 47.17, -78.88, -64.13, -79.52, -69.51, -76.33, -74.97, -71.45, -78.54, -65.2, -78.87, 59.91, -77.32, 67.9, -72.12, 74.55, -65.91, 78.31, -58.51, 78.51, -28.05, 78.91, 65.26, 79.07, 70.57, 76.47, 76.49, 70.9, 80.06, 63.27, 79.79, 41.84, 79.52, -40.16]}}, "Layer 23": {"Layer 23": {"rotation": 11.52, "x": 8.14, "width": 40, "y": -2.3, "height": 18}}, "bgbonus": {"bgbonus": {"width": 182, "height": 182}}, "Layer 27": {"Layer 25": {"x": 2.86, "width": 79, "y": 0.85, "height": 78}}, "JackPot2": {"JackPot2": {"x": -2.84, "width": 155, "y": -2.75, "height": 47}}, "Layer 22": {"Layer 22": {"rotation": -162.12, "x": 17.58, "width": 81, "y": 10.78, "height": 296}}, "Layer 21": {"Layer 21": {"width": 164, "type": "mesh", "hull": 52, "height": 118, "triangles": [0, 58, 59, 58, 48, 59, 48, 49, 59, 51, 0, 59, 59, 49, 50, 51, 59, 50, 4, 56, 3, 56, 57, 3, 3, 57, 2, 57, 58, 2, 2, 58, 1, 56, 47, 57, 1, 58, 0, 47, 48, 57, 57, 48, 58, 5, 55, 4, 55, 56, 4, 44, 45, 55, 55, 45, 56, 45, 46, 56, 56, 46, 47, 53, 54, 6, 53, 42, 54, 6, 54, 5, 5, 54, 55, 42, 43, 54, 54, 44, 55, 54, 43, 44, 24, 52, 23, 24, 61, 52, 23, 52, 60, 53, 52, 61, 61, 39, 40, 60, 52, 53, 53, 61, 40, 40, 41, 53, 41, 42, 53, 53, 6, 7, 31, 32, 30, 30, 32, 33, 33, 34, 30, 30, 34, 29, 34, 35, 29, 29, 35, 28, 35, 36, 28, 28, 36, 27, 36, 37, 27, 27, 37, 26, 37, 38, 26, 26, 38, 25, 25, 61, 24, 38, 39, 25, 25, 39, 61, 18, 12, 13, 18, 19, 12, 16, 14, 15, 16, 17, 14, 14, 17, 13, 17, 18, 13, 19, 11, 12, 19, 20, 11, 20, 10, 11, 20, 21, 10, 21, 9, 10, 21, 22, 9, 23, 60, 22, 22, 60, 9, 60, 8, 9, 8, 60, 53, 53, 7, 8], "uvs": [0.85088, 0.0811, 0.84973, 0.21812, 0.82222, 0.26114, 0.79585, 0.31052, 0.76146, 0.36469, 0.72363, 0.40612, 0.71331, 0.47941, 0.72822, 0.5288, 0.72822, 0.63554, 0.73395, 0.75503, 0.79241, 0.80442, 0.83827, 0.83469, 0.89444, 0.8793, 0.96207, 0.92869, 1, 0.95259, 1, 1, 0.98156, 1, 0.92997, 0.96534, 0.88297, 0.94781, 0.82107, 0.92073, 0.76261, 0.89205, 0.70185, 0.86656, 0.63995, 0.84425, 0.54366, 0.81876, 0.4462, 0.82036, 0.3568, 0.81398, 0.28, 0.83151, 0.22498, 0.84107, 0.15734, 0.85222, 0.09888, 0.85859, 0.04385, 0.89683, 0.04041, 0.94622, 0, 0.95578, 0, 0.87612, 0.02093, 0.82673, 0.07939, 0.79646, 0.14588, 0.763, 0.21007, 0.72635, 0.26166, 0.70405, 0.32929, 0.67856, 0.38317, 0.59093, 0.40036, 0.50171, 0.43131, 0.47463, 0.41871, 0.39815, 0.55856, 0.25476, 0.59524, 0.20219, 0.65256, 0.16554, 0.70758, 0.15917, 0.75573, 0.14483, 0.77292, 0.07314, 0.80273, 0, 0.85546, 0, 0.50927, 0.68812, 0.51385, 0.58137, 0.52875, 0.4093, 0.6388, 0.31849, 0.69497, 0.2452, 0.75688, 0.21334, 0.80388, 0.17669, 0.81648, 0.08429, 0.63766, 0.72795, 0.43131, 0.6929], "vertices": [3, 58, -60.9, 80.98, 0, 50, 29.32, 3.14, 0.11619, 51, 12.13, -8.18, 0.88381, 2, 50, 20.35, -10.31, 0.99706, 51, -3.74, -11.31, 0.00294, 5, 55, 1.09, 64.55, 0.00046, 58, -54.49, 60.19, 0, 48, 48.48, -35.75, 0.00066, 49, 39.66, -6.24, 0.00087, 50, 13.8, -12.11, 0.99801, 5, 55, -0.87, 57.56, 0.00531, 58, -55.37, 52.99, 0, 48, 41.39, -34.22, 0.01451, 49, 34.25, -11.08, 0.07306, 50, 7, -14.64, 0.90712, 6, 55, -3.85, 49.57, 0.02615, 58, -57.12, 44.64, 0, 47, 42.03, -39.93, 0.00036, 48, 33.24, -31.72, 0.08115, 49, 27.44, -16.2, 0.31155, 50, -1.21, -16.92, 0.58078, 6, 55, -7.9, 42.79, 0.08728, 58, -60.09, 37.33, 0, 47, 36.36, -34.44, 0.00969, 48, 26.23, -28.08, 0.2414, 49, 20.37, -19.74, 0.428, 50, -9.08, -17.64, 0.23363, 6, 55, -6.4, 34.11, 0.25879, 58, -57.29, 28.97, 0, 47, 27.56, -33.91, 0.054, 48, 17.65, -30.11, 0.37207, 49, 16.97, -27.86, 0.26538, 50, -15.21, -23.97, 0.04975, 6, 55, -2.03, 29.54, 0.40508, 58, -52.29, 25.11, 0, 47, 22.11, -37.11, 0.07737, 48, 13.35, -34.74, 0.33057, 49, 18.19, -34.07, 0.17068, 50, -16.33, -30.19, 0.0163, 6, 55, 2.47, 17.77, 0.70641, 58, -46.07, 14.16, 0, 47, 9.63, -38.8, 0.06899, 48, 1.88, -39.94, 0.16277, 49, 15.65, -46.4, 0.06105, 50, -23.2, -40.75, 0.00078, 4, 55, 8.38, 4.94, 0.98723, 47, -4.22, -41.61, 0.00051, 48, -10.58, -46.62, 0.0092, 49, 13.73, -60.4, 0.00307, 2, 55, 19.42, 2.92, 0.00991, 56, 3.37, 3.08, 0.99009, 2, 56, 11.69, 2.98, 1, 58, -18.77, 2.65, 0, 2, 58, -8.16, 2.62, 0.00053, 57, 6.67, 2.63, 0.99947, 1, 58, 4.37, 3.03, 1, 1, 58, 11.17, 3.65, 1, 1, 58, 13.93, -1.21, 1, 1, 58, 11.3, -2.71, 1, 2, 58, 1.93, -3.33, 0.8037, 57, 16.75, -3.33, 0.1963, 2, 56, 23.94, -6.06, 0.00455, 57, 9.02, -5.33, 0.99545, 3, 55, 28.71, -8.22, 0.00097, 56, 13.38, -7.41, 0.67443, 57, -1.38, -7.56, 0.3246, 2, 55, 18.55, -8.48, 0.29786, 56, 3.26, -8.36, 0.70214, 2, 55, 8.16, -9.23, 0.95447, 56, -7.05, -9.8, 0.04553, 2, 55, -2.26, -10.4, 0.90058, 47, -16.71, -27.74, 0.09942, 3, 55, -18.08, -13.23, 0.36204, 46, -31.99, 15.47, 0.02319, 47, -15.84, -11.69, 0.61477, 3, 55, -32.94, -19.11, 0.03514, 46, -16.24, 12.72, 0.34536, 47, -18.16, 4.12, 0.61951, 2, 46, -1.97, 9.29, 0.91523, 47, -19.37, 18.75, 0.08477, 2, 46, 10.79, 9.02, 0.9073, 52, -5.16, 9.28, 0.0927, 2, 46, 19.87, 8.47, 0.19715, 52, 3.88, 8.29, 0.80285, 2, 52, 14.97, 7, 0.89037, 53, -2.35, 7.51, 0.10963, 2, 52, 24.48, 5.51, 0.02585, 53, 6.76, 4.42, 0.97415, 3, 58, -128.42, -68.09, 0, 53, 16.84, 5.01, 0.31399, 54, 4.63, 4.08, 0.68601, 3, 58, -126.03, -73.44, 0, 53, 19.65, 10.15, 0.07903, 54, 8.74, 8.25, 0.92097, 3, 58, -131.24, -77.69, 0, 53, 26.19, 8.58, 0.02307, 54, 14.6, 4.95, 0.97693, 1, 54, 8.68, -2.35, 1, 1, 54, 2.34, -4.72, 1, 2, 58, -129.2, -54.91, 0, 53, 6.81, -3.58, 1, 3, 58, -121.67, -46.09, 0, 52, 14.37, -3.68, 0.97688, 53, -4.76, -2.91, 0.02312, 3, 58, -114.66, -37.13, 0, 46, 19.79, -5.29, 0.14791, 52, 3.12, -5.45, 0.85209, 3, 58, -108.6, -30.67, 0, 46, 10.99, -6.32, 0.98125, 52, -5.72, -6.05, 0.01875, 3, 58, -100.44, -22.57, 0, 46, -0.47, -7.24, 0.88415, 47, -4.14, 25.36, 0.11585, 4, 58, -97.87, -9.22, 0, 46, -11.05, -15.79, 0.26698, 47, 7.29, 17.99, 0.72137, 48, -16.68, 13.77, 0.01164, 3, 46, -15.75, -25.62, 0.06072, 47, 18.1, 16.6, 0.7405, 48, -5.93, 15.55, 0.19878, 3, 46, -21.33, -27.83, 0.01845, 47, 21.94, 11.99, 0.45042, 48, -0.92, 12.25, 0.53113, 3, 46, -20.95, -37.08, 0.00018, 47, 30.61, 15.25, 0.06368, 48, 6.44, 17.85, 0.93614, 2, 48, 31.32, 3.95, 0.62118, 49, -2.54, 3.22, 0.37882, 1, 49, 4.6, 8.08, 1, 2, 49, 14.68, 10.42, 0.96832, 50, -3.38, 12.51, 0.03168, 3, 49, 23.67, 9.34, 0.4098, 50, 4.6, 8.23, 0.56721, 51, -1.7, 12.93, 0.02299, 3, 49, 31.75, 9.4, 0.01153, 50, 12.14, 5.34, 0.32653, 51, 1.57, 5.55, 0.66194, 1, 51, 10.43, 4.52, 1, 1, 51, 19.88, 1.51, 1, 2, 58, -64.97, 89.67, 0, 51, 21.65, -6.96, 1, 6, 55, -28.86, -0.85, 0.07573, 58, -74.22, -8.97, 0, 46, -29.27, -0.72, 0.00299, 47, -1.31, -4.04, 0.91937, 48, -18.59, -9.8, 0.00164, 49, -20.77, -45.24, 0.00028, 5, 55, -32.65, 11.19, 0.02168, 58, -79.79, 2.35, 0, 47, 11.27, -3.1, 0.95012, 48, -6.81, -5.28, 0.02642, 49, -17.49, -33.05, 0.00178, 1, 48, 12.69, 0.87, 1, 5, 55, -24.59, 47.48, 0.01437, 58, -77.3, 39.45, 0, 48, 29.9, -11.14, 0.223, 49, 8.83, -6.8, 0.74398, 50, -15.1, -1.39, 0.01865, 4, 55, -19.07, 58.85, 9e-05, 58, -73.56, 51.52, 0, 48, 41.58, -15.97, 0.00044, 49, 19.6, -0.19, 0.99947, 4, 55, -10.93, 65.99, 0.00011, 58, -66.59, 59.8, 0, 48, 49.2, -23.66, 0.00017, 50, 7.89, -1.54, 0.99972, 1, 50, 16.71, -2.11, 1, 3, 58, -65.62, 77.87, 0, 50, 24.39, 5.9, 0.05535, 51, 10.6, -2.74, 0.94465, 5, 55, -7.51, 2.28, 0.76847, 58, -53.59, -2.66, 0, 47, -3.16, -25.54, 0.19429, 48, -14.18, -30.91, 0.03015, 49, -1.09, -54.09, 0.00709, 4, 55, -40.6, -5.94, 0.00044, 58, -85.06, -15.78, 0, 46, -16.6, -2.51, 0.29201, 47, -3.58, 8.55, 0.70755], "edges": [66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 28, 26, 28, 64, 66, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 30, 32, 34, 32]}}, "Layer 20": {"Layer 20": {"rotation": -9, "x": -10.33, "width": 76, "y": -7.86, "height": 296}}, "Layer 15": {"Layer 15": {"rotation": -90, "x": -0.37, "width": 17, "y": 0.25, "height": 16}}, "Layer 14": {"Layer 14": {"rotation": -90, "x": -0.09, "width": 14, "y": 0.35, "height": 13}}, "elip": {"elip": {"width": 50, "height": 50}}, "Layer 13": {"Layer 13": {"width": 53, "type": "mesh", "hull": 30, "height": 88, "triangles": [30, 28, 29, 0, 30, 29, 27, 28, 30, 30, 0, 1, 31, 27, 30, 31, 30, 1, 31, 1, 2, 31, 26, 27, 32, 31, 2, 32, 26, 31, 3, 32, 2, 33, 25, 26, 33, 26, 32, 24, 25, 33, 4, 32, 3, 34, 24, 33, 32, 6, 33, 32, 4, 6, 5, 6, 4, 7, 33, 6, 34, 35, 23, 34, 23, 24, 35, 22, 23, 36, 22, 35, 22, 19, 21, 34, 33, 7, 8, 34, 7, 35, 34, 8, 35, 8, 9, 19, 20, 21, 36, 19, 22, 18, 19, 36, 9, 36, 35, 37, 18, 36, 9, 37, 36, 38, 18, 37, 10, 37, 9, 17, 18, 38, 38, 37, 10, 39, 17, 38, 15, 16, 17, 14, 15, 17, 39, 14, 17, 13, 38, 10, 13, 10, 11, 13, 11, 12, 39, 38, 13, 14, 39, 13], "uvs": [1, 0.12727, 1, 0.18812, 1, 0.28581, 0.97163, 0.37467, 0.9572, 0.45292, 0.9459, 0.54729, 0.84647, 0.62843, 0.72138, 0.68542, 0.59308, 0.72405, 0.46638, 0.76365, 0.46477, 0.84093, 0.52251, 0.88053, 0.55298, 0.95104, 0.39581, 1, 0.15364, 1, 0, 0.93945, 0, 0.88, 0.09712, 0.80852, 0.15646, 0.72352, 0.08911, 0.68102, 0, 0.60182, 0, 0.51585, 0.1098, 0.41224, 0.31508, 0.34753, 0.50272, 0.32241, 0.51555, 0.24804, 0.71281, 0.17849, 0.7481, 0.14179, 0.75932, 0, 1, 0, 0.87862, 0.12727, 0.85296, 0.19295, 0.79362, 0.32528, 0.6605, 0.41704, 0.529, 0.47113, 0.39749, 0.54454, 0.32371, 0.65465, 0.30652, 0.76009, 0.28728, 0.81707, 0.252, 0.92815], "vertices": [2, 38, 1.76, 6.01, 0.99787, 37, -5.22, -5.83, 0.00213, 1, 38, 6.89, 7.57, 1, 3, 40, -27.01, 10.15, 0.00013, 39, -10.71, 5.15, 0.02557, 38, 15.11, 10.08, 0.9743, 4, 41, -24.49, 31.62, 0.00097, 40, -19.98, 13.88, 0.02297, 39, -4.47, 10.09, 0.44462, 38, 23.03, 10.91, 0.53144, 4, 41, -17.57, 31.69, 0.01161, 40, -14.13, 17.6, 0.11061, 39, 0.61, 14.8, 0.74895, 38, 29.84, 12.19, 0.12883, 4, 41, -9.25, 32.09, 0.04338, 40, -7.29, 22.34, 0.25516, 39, 6.5, 20.69, 0.69238, 38, 37.96, 14.03, 0.00909, 3, 41, -1.53, 27.71, 0.11619, 40, 1.58, 22.71, 0.40443, 39, 15.15, 22.65, 0.47938, 3, 41, 4.24, 21.73, 0.26877, 40, 9.64, 20.69, 0.48338, 39, 23.45, 22.11, 0.24786, 3, 41, 8.43, 15.39, 0.53282, 40, 16.55, 17.52, 0.37965, 39, 30.81, 20.24, 0.08753, 4, 42, -4.57, 9.18, 0.09448, 41, 12.7, 9.14, 0.79713, 40, 23.48, 14.47, 0.09747, 39, 38.17, 18.49, 0.01092, 4, 42, 2.2, 9.85, 0.74487, 41, 19.46, 9.87, 0.25176, 40, 28.83, 18.67, 0.00328, 39, 42.68, 23.58, 9e-05, 2, 42, 5.33, 13.27, 0.94748, 41, 22.55, 13.32, 0.05252, 2, 42, 11.32, 15.56, 0.99893, 41, 28.52, 15.67, 0.00107, 1, 42, 16.52, 7.76, 1, 1, 42, 17.94, -5, 1, 2, 42, 13.54, -13.68, 0.99976, 41, 31.02, -13.55, 0.00024, 2, 42, 8.34, -14.26, 0.98211, 41, 25.82, -14.18, 0.01789, 2, 42, 1.52, -9.83, 0.70592, 41, 18.96, -9.82, 0.29408, 3, 42, -6.26, -7.54, 0.04955, 41, 11.16, -7.59, 0.94769, 40, 31.02, -0.54, 0.00276, 2, 41, 7.87, -11.59, 0.9195, 40, 30.34, -5.67, 0.0805, 2, 41, 1.52, -17.11, 0.65355, 40, 27.87, -13.72, 0.34645, 2, 41, -5.99, -18.02, 0.41287, 40, 21.98, -18.46, 0.58713, 2, 41, -15.74, -13.33, 0.11602, 40, 11.23, -19.64, 0.88398, 4, 41, -22.7, -3.21, 0.00153, 40, -0.03, -14.73, 0.857, 39, 20.31, -14.46, 0.14114, 38, 30.88, -23.07, 0.00033, 3, 40, -7.99, -8.37, 0.15728, 39, 11.34, -9.64, 0.73402, 38, 25.87, -14.2, 0.1087, 3, 40, -13.51, -11.94, 0.00596, 39, 6.54, -14.15, 0.63483, 38, 19.41, -15.46, 0.35921, 3, 39, -5.37, -11.94, 0.08091, 38, 10.51, -7.24, 0.89852, 37, -9.73, 9.39, 0.02057, 3, 39, -8.89, -13.17, 0.00953, 38, 6.87, -6.39, 0.84313, 37, -6.5, 7.52, 0.14735, 1, 37, 5.98, 6.92, 1, 2, 38, -8.95, 2.75, 0.12032, 37, 5.98, -5.83, 0.87968, 2, 38, 3.64, -0.14, 0.99387, 37, -5.22, 0.6, 0.00613, 1, 38, 9.56, 0.24, 1, 4, 41, -27.68, 21.73, 0, 40, -17.45, 3.81, 0.00042, 39, -0.17, 0.63, 0.49843, 38, 21.62, 0.62, 0.50115, 3, 41, -18.82, 15.7, 0.00151, 40, -6.74, 3.37, 0.03025, 39, 10.45, 2.13, 0.96825, 3, 41, -13.26, 9.35, 0.0015, 40, 1.34, 0.92, 0.90291, 39, 18.84, 1.18, 0.09559, 1, 40, 10.74, -0.46, 1, 3, 41, 4.08, 0.48, 0.9865, 40, 20.74, 2.57, 0.0133, 39, 37.63, 6.29, 0.0002, 3, 41, 13.4, 0.69, 0.99858, 40, 28.54, 7.67, 0.00134, 39, 44.38, 12.72, 7e-05, 3, 42, 1.15, 0.26, 0.97737, 41, 18.5, 0.28, 0.0226, 40, 33.09, 10.02, 3e-05, 1, 42, 11.08, -0.51, 1], "edges": [56, 58, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 40, 42, 40, 38, 38, 36, 36, 34, 34, 32, 30, 32, 30, 28, 26, 28, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 58]}}, "Layer 12": {"Layer 12": {"width": 53, "type": "mesh", "hull": 20, "height": 68, "triangles": [24, 23, 5, 10, 23, 24, 8, 9, 10, 8, 10, 24, 7, 5, 6, 24, 5, 7, 8, 24, 7, 22, 21, 4, 12, 13, 11, 21, 11, 13, 22, 11, 21, 23, 11, 22, 5, 22, 4, 23, 22, 5, 10, 11, 23, 0, 1, 2, 3, 0, 2, 3, 21, 0, 3, 4, 21, 19, 17, 18, 20, 17, 19, 16, 17, 20, 14, 15, 16, 0, 21, 20, 20, 13, 14, 19, 0, 20, 16, 20, 14, 21, 13, 20], "uvs": [0.83967, 0.06677, 1, 0.19921, 1, 0.42692, 0.87544, 0.54774, 0.65186, 0.65927, 0.6459, 0.74757, 0.75918, 0.85212, 0.67273, 1, 0.18979, 1, 0.1242, 0.94971, 0.22258, 0.82657, 0.21363, 0.73363, 0.04967, 0.64765, 0, 0.52218, 0, 0.40601, 0, 0.2271, 0.27624, 0.20851, 0.38654, 0.12486, 0.43126, 0, 0.63695, 0, 0.45511, 0.31307, 0.46107, 0.42227, 0.47001, 0.61048, 0.43424, 0.73595, 0.46405, 0.87768], "vertices": [2, 33, 15.03, 24.22, 0.65504, 34, -18.42, 15.8, 0.34496, 3, 33, 27.36, 23.09, 0.36109, 34, -10.11, 24.98, 0.63491, 35, -27.37, 22.69, 0.004, 3, 33, 37.6, 11.48, 0.08733, 34, 5.33, 26.19, 0.84255, 35, -12.1, 25.23, 0.07012, 3, 33, 38.09, 0.95, 0.01019, 34, 14.04, 20.25, 0.7791, 35, -2.91, 20.07, 0.21072, 3, 34, 22.52, 9.03, 0.15024, 35, 6.52, 9.63, 0.79322, 36, -5.26, 11.31, 0.05654, 3, 34, 28.53, 9.18, 0.00446, 35, 12.49, 10.3, 0.52712, 36, 0.69, 10.47, 0.46842, 2, 35, 18.52, 17.4, 0.12142, 36, 8.31, 15.82, 0.87858, 2, 35, 29.19, 14.53, 0.0088, 36, 17.92, 10.36, 0.9912, 1, 36, 15.65, -15.13, 1, 2, 35, 30.6, -14.71, 0.00244, 36, 11.94, -18.29, 0.99756, 4, 33, 24.68, -36.16, 0.00729, 34, 35.64, -12.77, 0, 35, 21.48, -10.94, 0.19268, 36, 4.06, -12.36, 0.80003, 4, 33, 20.15, -31.74, 0.05673, 34, 29.38, -13.73, 0.01217, 35, 15.33, -12.45, 0.65161, 36, -2.28, -12.27, 0.27949, 4, 33, 9.76, -33.1, 0.2081, 34, 24.23, -22.85, 0.06952, 35, 10.99, -21.98, 0.71048, 36, -8.87, -20.41, 0.0119, 3, 33, 2.15, -28.45, 0.3552, 34, 15.93, -26.14, 0.10512, 35, 3, -25.98, 0.53968, 3, 33, -3.08, -22.52, 0.55139, 34, 8.06, -26.76, 0.10421, 35, -4.79, -27.28, 0.3444, 3, 33, -11.13, -13.4, 0.77905, 34, -4.07, -27.71, 0.05142, 35, -16.79, -29.28, 0.16953, 3, 33, -0.99, -2.77, 0.9539, 34, -6.48, -13.22, 0.01805, 35, -20.44, -15.05, 0.02805, 1, 33, -0.37, 5.37, 1, 2, 33, -4.21, 13.3, 0.99988, 34, -21.25, -6.13, 0.00012, 2, 33, 3.97, 20.51, 0.91867, 34, -22.11, 4.73, 0.08133, 3, 33, 10.82, -1.82, 0.80661, 34, -0.13, -3.21, 0.19024, 35, -14.99, -4.53, 0.00315, 3, 33, 15.97, -7.18, 0.08093, 34, 7.25, -2.31, 0.90045, 35, -7.71, -2.99, 0.01862, 2, 33, 24.8, -16.47, 0.00135, 35, 4.83, -0.42, 0.99865, 3, 33, 29.02, -24.12, 0.00116, 35, 13.56, -0.89, 0.93305, 36, -1.09, -0.64, 0.06578, 2, 35, 22.81, 2.25, 0.00016, 36, 8.65, 0.08, 0.99984], "edges": [30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30]}}, "Layer 19": {"Layer 19": {"width": 26, "type": "mesh", "hull": 11, "height": 24, "triangles": [7, 8, 11, 5, 6, 7, 5, 7, 11, 3, 11, 0, 4, 5, 11, 4, 11, 3, 11, 8, 9, 3, 0, 1, 9, 10, 0, 11, 9, 0, 2, 3, 1], "uvs": [0.82932, 0.35987, 1, 0.54687, 1, 0.74762, 0.67448, 0.92362, 0.28102, 1, 0, 0.80262, 0, 0.48637, 0.13887, 0.46162, 0.18963, 0.21137, 0.44348, 0, 0.73032, 0, 0.28352, 0.53651], "vertices": [2, 22, 1.86, 9.29, 0.94889, 23, -5.41, 10.97, 0.05111, 2, 22, 4.1, 15.19, 0.83648, 23, -1.78, 16.13, 0.16352, 2, 22, 8.48, 17.19, 0.77325, 23, 2.97, 16.98, 0.22675, 2, 22, 15.84, 11.24, 0.38538, 23, 8.62, 9.4, 0.61462, 2, 22, 21.75, 2.7, 0.00019, 23, 12.24, -0.34, 0.99981, 1, 23, 8.87, -8.37, 1, 2, 22, 13.57, -9.07, 0.02731, 23, 1.4, -9.72, 0.97269, 2, 22, 11.53, -6.03, 0.24201, 23, 0.18, -6.27, 0.75799, 2, 22, 5.52, -7.32, 0.9539, 23, -5.97, -6.03, 0.0461, 1, 22, -1.83, -3.43, 1, 1, 22, -4.93, 3.36, 1, 1, 23, 0.37, -0.27, 1], "edges": [20, 0, 0, 2, 18, 20, 18, 16, 16, 14, 14, 12, 10, 12, 10, 8, 8, 6, 2, 4, 6, 4]}}, "Layer 18": {"Layer 18": {"width": 32, "type": "mesh", "hull": 20, "height": 34, "triangles": [15, 16, 12, 13, 14, 15, 12, 13, 15, 20, 18, 19, 16, 17, 18, 11, 16, 18, 11, 18, 20, 10, 11, 20, 10, 20, 2, 12, 16, 11, 20, 19, 0, 20, 0, 1, 2, 20, 1, 21, 10, 2, 21, 2, 3, 9, 10, 21, 3, 4, 5, 6, 7, 8, 5, 6, 21, 5, 21, 3, 9, 21, 6, 6, 8, 9], "uvs": [1, 0.15411, 1, 0.32752, 0.88631, 0.46941, 0.90142, 0.59765, 1, 0.6883, 1, 0.82821, 0.72136, 1, 0.46174, 1, 0.4073, 0.82033, 0.52874, 0.65874, 0.5199, 0.46547, 0.39218, 0.43394, 0.23306, 0.48714, 0.08649, 0.60735, 0, 0.58173, 0, 0.37679, 0.17862, 0.30782, 0.13465, 0.11667, 0.46546, 0, 0.87793, 0, 0.69996, 0.16988, 0.70624, 0.64873], "vertices": [1, 26, 0.49, 9.46, 1, 2, 27, -5.86, 8.24, 0.00104, 26, 6.37, 9.14, 0.99896, 2, 27, -0.51, 4.68, 0.40695, 26, 11.01, 4.69, 0.59305, 2, 27, 3.74, 5.76, 0.97259, 26, 15.38, 5.01, 0.02741, 1, 27, 6.32, 9.74, 1, 1, 27, 11.04, 10.32, 1, 1, 27, 17.93, 2.19, 1, 1, 27, 18.94, -6.06, 1, 2, 27, 13.19, -9.01, 0.99995, 25, -0.16, -15.89, 5e-05, 3, 27, 7.35, -6.26, 0.94282, 26, 16.82, -7.46, 0.01202, 25, 5.35, -12.53, 0.04516, 3, 27, 1.14, -8.73, 0.28655, 26, 10.27, -8.79, 0.12895, 25, 6.7, -5.98, 0.5845, 3, 27, 0.54, -12.71, 0.02589, 26, 8.98, -12.61, 0.0141, 25, 3.7, -3.29, 0.96001, 2, 25, -0.78, -3.01, 0.7204, 24, -3.24, -1.6, 0.2796, 2, 25, -5.7, -5.03, 0.10525, 24, -7.23, 1.91, 0.89475, 2, 25, -7.79, -3.03, 0.05313, 24, -6.36, 4.68, 0.94687, 1, 24, 0.61, 4.68, 1, 2, 25, 1.46, 2.79, 0.67258, 24, 2.95, -1.04, 0.32742, 2, 25, 3.07, 9.24, 0.96591, 24, 9.45, 0.37, 0.03409, 1, 25, 14.32, 8.1, 1, 2, 26, -4.96, 5.85, 0.87623, 25, 26.15, 2.26, 0.12377, 1, 25, 13.69, 1.53, 1, 3, 27, 6.42, -1.16, 0.99368, 26, 16.8, -2.27, 0.00022, 25, 10.13, -14.55, 0.0061], "edges": [30, 32, 32, 34, 34, 36, 36, 38, 38, 0, 28, 30, 28, 26, 26, 24, 24, 22, 22, 20, 0, 2, 2, 4, 12, 10, 20, 18, 18, 16, 12, 14, 16, 14, 4, 6, 8, 10, 6, 8]}}, "Layer 17": {"Layer 17": {"width": 32, "type": "mesh", "hull": 17, "height": 30, "triangles": [18, 15, 16, 17, 18, 16, 0, 17, 16, 17, 0, 1, 4, 18, 17, 3, 4, 17, 3, 17, 1, 2, 3, 1, 14, 15, 18, 18, 13, 14, 19, 18, 4, 19, 13, 18, 5, 19, 4, 20, 19, 5, 20, 5, 6, 12, 13, 19, 20, 12, 19, 9, 10, 11, 12, 9, 11, 9, 12, 20, 7, 8, 20, 7, 20, 6, 9, 20, 8], "uvs": [0.92463, 0.2305, 1, 0.41033, 1, 0.60207, 0.75807, 0.54791, 0.5976, 0.49807, 0.58135, 0.53491, 0.63214, 0.6779, 0.65245, 0.87724, 0.43511, 1, 0.16089, 1, 0, 0.8859, 0, 0.77216, 0.18933, 0.63133, 0.19136, 0.51867, 0.09183, 0.27817, 0.28479, 0.05067, 0.50823, 0, 0.80073, 0.37674, 0.40261, 0.25324, 0.36401, 0.50674, 0.36604, 0.6909], "vertices": [2, 29, 1.79, -5.58, 0.18301, 28, 5.44, -0.76, 0.81699, 2, 29, -2.56, -1.58, 0.38705, 28, 0.05, -3.17, 0.61295, 1, 29, -4.84, 3.7, 1, 3, 31, -0.24, 13.3, 0.00337, 30, 5.03, 12.54, 0.0202, 29, 2.91, 5.28, 0.97643, 3, 31, -1.34, 8.06, 0.1558, 30, 5.65, 7.22, 0.29847, 29, 8.21, 5.95, 0.54573, 3, 31, -0.19, 7.63, 0.35741, 30, 6.87, 7.18, 0.3455, 29, 8.25, 7.17, 0.2971, 3, 31, 3.96, 9.58, 0.82596, 30, 10.19, 10.34, 0.12692, 29, 5.06, 10.46, 0.04712, 3, 31, 9.87, 10.69, 0.97952, 30, 15.45, 13.27, 0.0189, 29, 2.09, 15.69, 0.00158, 1, 31, 14.08, 4.03, 1, 1, 31, 14.75, -4.71, 1, 1, 31, 11.73, -10.11, 1, 1, 31, 8.33, -10.37, 1, 2, 31, 3.65, -4.66, 0.96512, 30, 14.42, -3.26, 0.03488, 3, 31, 0.28, -4.85, 0.50479, 30, 11.28, -4.51, 0.49477, 29, 19.9, 11.67, 0.00043, 3, 31, -6.67, -8.58, 0.00104, 30, 5.87, -10.25, 0.9022, 29, 25.69, 6.31, 0.09677, 2, 30, -2.82, -7.22, 0.31567, 29, 22.73, -2.4, 0.68433, 2, 29, 16.77, -6.63, 0.99993, 28, 12.36, 12.56, 7e-05, 3, 31, -5.46, 14.27, 1e-05, 30, -0.23, 11.8, 6e-05, 29, 3.69, 0.02, 0.99994, 2, 30, 1.31, -1.38, 0.8451, 29, 16.86, 1.68, 0.1549, 3, 31, -0.5, 0.63, 0.07683, 30, 8.8, 0.44, 0.91988, 29, 14.97, 9.15, 0.00329, 3, 31, 5, 1.12, 0.98726, 30, 13.86, 2.65, 0.01073, 29, 12.72, 14.2, 0.00201], "edges": [2, 0, 0, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 20, 22, 20, 18, 16, 18, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 2, 4, 6, 4]}}, "Layer 16": {"Layer 16": {"width": 79, "type": "mesh", "hull": 36, "height": 124, "triangles": [15, 16, 13, 13, 51, 50, 15, 13, 14, 17, 51, 16, 13, 16, 51, 18, 19, 17, 17, 20, 51, 17, 19, 20, 51, 20, 50, 50, 49, 13, 50, 20, 49, 49, 20, 22, 13, 49, 48, 48, 49, 22, 22, 20, 21, 12, 13, 48, 11, 12, 47, 48, 22, 47, 47, 12, 48, 47, 22, 23, 11, 47, 46, 47, 24, 26, 47, 23, 24, 11, 46, 45, 47, 26, 46, 46, 26, 27, 24, 25, 26, 11, 45, 10, 10, 45, 38, 45, 44, 38, 27, 28, 36, 45, 46, 27, 45, 27, 36, 45, 36, 44, 44, 36, 37, 10, 38, 9, 9, 38, 39, 44, 37, 38, 37, 90, 38, 36, 89, 37, 36, 28, 29, 56, 72, 2, 2, 72, 0, 0, 55, 35, 35, 58, 53, 29, 30, 84, 37, 89, 90, 29, 85, 57, 89, 88, 90, 90, 88, 43, 89, 57, 88, 88, 57, 86, 7, 8, 40, 40, 8, 39, 8, 9, 39, 6, 7, 40, 5, 40, 4, 40, 5, 6, 38, 91, 39, 39, 92, 40, 39, 91, 92, 56, 3, 93, 93, 94, 56, 3, 56, 2, 40, 93, 4, 3, 4, 93, 40, 92, 93, 91, 43, 97, 91, 90, 43, 91, 96, 92, 91, 97, 96, 92, 96, 93, 96, 97, 42, 96, 95, 93, 93, 95, 94, 97, 43, 42, 43, 88, 42, 42, 41, 96, 96, 41, 69, 42, 88, 86, 96, 69, 95, 95, 70, 94, 94, 71, 56, 95, 69, 70, 42, 87, 41, 41, 52, 62, 41, 78, 52, 52, 76, 34, 61, 52, 53, 52, 34, 53, 53, 34, 35, 38, 90, 91, 89, 29, 57, 57, 85, 86, 94, 70, 71, 89, 36, 29, 2, 0, 1, 34, 74, 33, 30, 31, 84, 32, 33, 73, 86, 87, 42, 79, 78, 41, 87, 79, 41, 79, 87, 80, 79, 80, 78, 29, 84, 85, 86, 85, 82, 85, 84, 82, 86, 81, 87, 86, 82, 81, 84, 83, 82, 84, 31, 83, 87, 81, 80, 83, 31, 73, 81, 76, 80, 80, 76, 77, 82, 54, 81, 81, 54, 76, 83, 73, 82, 82, 73, 54, 54, 73, 74, 78, 80, 77, 74, 75, 54, 54, 75, 76, 73, 33, 74, 75, 74, 34, 76, 75, 34, 77, 76, 52, 78, 77, 52, 73, 31, 32, 69, 41, 64, 62, 52, 61, 69, 64, 65, 41, 62, 63, 64, 63, 61, 63, 62, 61, 59, 60, 53, 71, 72, 56, 69, 65, 70, 70, 66, 71, 70, 65, 66, 66, 67, 71, 71, 67, 72, 67, 55, 72, 72, 55, 0, 65, 68, 66, 65, 61, 68, 67, 66, 59, 66, 68, 59, 67, 58, 55, 67, 59, 58, 61, 60, 68, 68, 60, 59, 35, 55, 58, 53, 58, 59, 65, 64, 61, 60, 61, 53, 41, 63, 64], "uvs": [1, 0.17052, 1, 0.37084, 0.89062, 0.36406, 0.73265, 0.47922, 0.67902, 0.4787, 0.72584, 0.51751, 0.738, 0.56299, 0.72281, 0.63073, 0.69091, 0.68783, 0.63319, 0.74493, 0.56179, 0.79331, 0.47369, 0.81751, 0.41293, 0.83396, 0.40018, 0.88572, 0.45334, 0.93604, 0.43967, 1, 0.24368, 1, 0.11309, 1, 0.03106, 0.9612, 0.04322, 0.90701, 0.11461, 0.87217, 0.0979, 0.85475, 0.09546, 0.82041, 0.04838, 0.7788, 0, 0.70719, 0, 0.60751, 0.07116, 0.52719, 0.1699, 0.48267, 0.23291, 0.46178, 0.27391, 0.38923, 0.22986, 0.33793, 0.23746, 0.2489, 0.18885, 0.21213, 0.18429, 0.126, 0.3848, 0, 0.90885, 0, 0.26936, 0.47245, 0.38632, 0.47535, 0.45923, 0.49664, 0.561, 0.48987, 0.63391, 0.47826, 0.49417, 0.246, 0.45771, 0.34181, 0.45619, 0.40084, 0.37496, 0.53106, 0.33243, 0.59977, 0.30812, 0.6588, 0.28838, 0.71493, 0.26863, 0.79331, 0.25132, 0.85959, 0.24372, 0.91088, 0.24068, 0.95249, 0.5346, 0.14731, 0.57174, 0.0461, 0.37573, 0.20515, 0.72318, 0.193, 0.73061, 0.36947, 0.36541, 0.39839, 0.70098, 0.17149, 0.66522, 0.16383, 0.63106, 0.16137, 0.59214, 0.17085, 0.5703, 0.18899, 0.57125, 0.21117, 0.58518, 0.22972, 0.63138, 0.22206, 0.66809, 0.21117, 0.69404, 0.20411, 0.63707, 0.19161, 0.61492, 0.25714, 0.66112, 0.26056, 0.70353, 0.25129, 0.72505, 0.22831, 0.33332, 0.19802, 0.34661, 0.18552, 0.37446, 0.17382, 0.42035, 0.17402, 0.45357, 0.18249, 0.4637, 0.20628, 0.46085, 0.22705, 0.43649, 0.23733, 0.39503, 0.23693, 0.34661, 0.2331, 0.32889, 0.21414, 0.31655, 0.27241, 0.34946, 0.28269, 0.3925, 0.27926, 0.43459, 0.26999, 0.38454, 0.39922, 0.3684, 0.43487, 0.43726, 0.45955, 0.52656, 0.45132, 0.60941, 0.42938, 0.66106, 0.40128, 0.69226, 0.36975, 0.6546, 0.36084, 0.59004, 0.38346, 0.5158, 0.40402], "vertices": [1, 11, 33.39, -34.62, 1, 1, 11, 8.55, -34.62, 1, 1, 11, 9.39, -25.98, 1, 5, 13, -34.8, -7.13, 0.10856, 11, -4.89, -13.5, 0.8688, 15, -13.12, 20.27, 0.01841, 16, -25.23, 24.36, 0.00367, 17, -34.72, 27.09, 0.00056, 5, 13, -34.73, -2.89, 0.04398, 11, -4.82, -9.26, 0.83723, 15, -11.32, 16.43, 0.09898, 16, -24.04, 20.3, 0.016, 17, -33.84, 22.94, 0.00381, 5, 13, -39.54, -6.59, 0.00422, 11, -9.63, -12.96, 0.64145, 15, -8.61, 21.86, 0.27675, 16, -20.54, 25.26, 0.05984, 17, -29.97, 27.62, 0.01774, 6, 13, -45.18, -7.55, 0.00013, 11, -15.27, -13.92, 0.51178, 15, -3.96, 25.19, 0.3538, 16, -15.44, 27.85, 0.09969, 17, -24.69, 29.81, 0.03455, 18, -33.63, 34.91, 4e-05, 5, 11, -23.67, -12.72, 0.32498, 15, 4.12, 27.79, 0.40148, 16, -7.06, 29.19, 0.1862, 17, -16.23, 30.51, 0.08468, 18, -25.16, 34.41, 0.00266, 5, 11, -30.75, -10.2, 0.19796, 15, 11.59, 28.62, 0.35857, 16, 0.45, 28.88, 0.26965, 17, -8.77, 29.64, 0.16157, 18, -17.9, 32.49, 0.01225, 5, 11, -37.83, -5.64, 0.09995, 15, 19.95, 27.61, 0.24643, 16, 8.56, 26.63, 0.32433, 17, -0.85, 26.77, 0.28928, 18, -10.46, 28.53, 0.04001, 5, 11, -43.83, 0, 0.04499, 15, 27.81, 25.17, 0.13198, 16, 15.96, 23.02, 0.29632, 17, 6.26, 22.61, 0.43099, 18, -4.02, 23.41, 0.09572, 6, 11, -46.83, 6.96, 0.01686, 15, 33.55, 20.22, 0.05167, 16, 20.89, 17.26, 0.1875, 17, 10.73, 16.49, 0.52455, 18, -0.45, 16.72, 0.21621, 19, -11.02, 15.78, 0.00322, 6, 11, -48.87, 11.76, 0.00431, 15, 37.49, 16.8, 0.01207, 16, 24.26, 13.28, 0.06504, 17, 13.79, 12.27, 0.41966, 18, 1.98, 12.11, 0.4352, 19, -8.17, 11.41, 0.06373, 6, 11, -55.29, 12.76, 0.00015, 15, 43.7, 18.7, 0.00024, 16, 30.69, 14.22, 0.00362, 17, 20.27, 12.71, 0.0733, 18, 8.46, 11.64, 0.42797, 19, -1.68, 11.53, 0.49472, 3, 17, 25.42, 18.2, 0.00097, 18, 14.33, 16.34, 0.13823, 19, 3.74, 16.75, 0.8608, 2, 18, 22.33, 15.92, 0.05002, 19, 11.74, 17.07, 0.94998, 1, 19, 14.43, 1.82, 1, 2, 18, 24.47, -9.79, 0.0034, 19, 16.22, -8.34, 0.9966, 2, 18, 20.21, -16.65, 0.038, 19, 12.61, -15.56, 0.962, 3, 17, 29.13, -14.19, 0.00073, 18, 13.43, -16.25, 0.10921, 19, 5.82, -15.78, 0.89007, 4, 16, 35.77, -7.83, 0.00106, 17, 23.66, -9.65, 0.07338, 18, 8.66, -10.99, 0.40194, 19, 0.59, -10.98, 0.52362, 4, 16, 34.1, -9.73, 0.00581, 17, 21.85, -11.42, 0.17767, 18, 6.62, -12.48, 0.51406, 19, -1.31, -12.65, 0.30245, 4, 16, 30.09, -11.17, 0.0313, 17, 17.74, -12.56, 0.40841, 18, 2.39, -13.03, 0.46387, 19, -5.47, -13.58, 0.09642, 5, 15, 43.94, -12.09, 0.00508, 16, 26.27, -16.25, 0.12412, 17, 13.54, -17.33, 0.63215, 18, -2.44, -17.16, 0.23252, 19, -9.9, -18.14, 0.00613, 4, 15, 37.62, -19.41, 0.05213, 16, 18.92, -22.54, 0.30184, 17, 5.73, -23.04, 0.57981, 18, -10.97, -21.71, 0.06623, 4, 15, 26.51, -24.82, 0.21814, 16, 7.11, -26.2, 0.4434, 17, -6.32, -25.79, 0.33239, 18, -23.29, -22.73, 0.00607, 4, 12, -39.97, 25.73, 0.00127, 15, 15.09, -24.12, 0.47382, 16, -4.06, -23.78, 0.38483, 17, -17.28, -22.53, 0.14007, 5, 12, -34.45, 17.93, 0.01676, 11, -5.31, 30.96, 0.00093, 15, 6.72, -19.52, 0.73208, 16, -11.65, -17.97, 0.20924, 17, -24.4, -16.16, 0.04098, 5, 12, -31.86, 12.95, 0.05917, 11, -2.72, 25.98, 0.02556, 15, 2.21, -16.18, 0.8278, 16, -15.6, -13.98, 0.07844, 17, -28.03, -11.88, 0.00903, 1, 11, 6.27, 22.74, 1, 1, 11, 12.63, 26.22, 1, 2, 12, -5.46, 12.59, 0.512, 11, 23.67, 25.62, 0.488, 2, 12, -0.9, 16.43, 0.68001, 11, 28.23, 29.46, 0.31999, 1, 11, 38.91, 29.82, 1, 1, 11, 54.54, 13.98, 1, 1, 11, 54.54, -27.42, 1, 5, 12, -33.18, 10.07, 0.05887, 11, -4.05, 23.1, 0.02743, 15, 2.14, -13.01, 0.85312, 16, -15.19, -10.84, 0.05563, 17, -27.38, -8.78, 0.00494, 4, 12, -33.54, 0.83, 0.03518, 11, -4.41, 13.86, 0.07351, 15, -1.58, -4.55, 0.89028, 16, -17.58, -1.91, 0.00102, 5, 12, -36.18, -4.93, 0.0016, 11, -7.05, 8.1, 0.13585, 15, -1.72, 1.79, 0.86138, 16, -16.77, 4.37, 0.00102, 17, -27.8, 6.51, 0.00015, 5, 13, -36.11, 6.43, 0.00074, 11, -6.21, 0.06, 0.67172, 15, -6, 8.65, 0.31321, 16, -19.95, 11.8, 0.01182, 17, -30.41, 14.16, 0.00251, 5, 13, -34.68, 0.67, 0.01909, 11, -4.77, -5.7, 0.83553, 15, -9.81, 13.2, 0.12679, 16, -23.04, 16.88, 0.01513, 17, -33.1, 19.46, 0.00346, 3, 12, -5.1, -7.69, 0.25795, 11, 24.03, 5.34, 0.73623, 15, -30.88, -9.33, 0.00582, 3, 12, -16.98, -4.81, 0.19152, 11, 12.15, 8.22, 0.69491, 15, -18.94, -6.72, 0.11357, 1, 11, 4.83, 8.34, 1, 3, 12, -40.45, 1.73, 0.00294, 15, 5.03, -2.33, 0.99636, 16, -10.72, -0.72, 0.0007, 4, 12, -48.97, 5.09, 5e-05, 15, 14.16, -1.63, 0.84784, 16, -1.59, -1.4, 0.15175, 17, -13.1, -0.41, 0.00036, 2, 16, 5.97, -1.07, 0.99977, 17, -5.54, -0.65, 0.00023, 2, 16, 13.08, -0.49, 0.04233, 17, 1.59, -0.62, 0.95767, 2, 11, -43.83, 23.16, 0, 17, 11.42, 0.03, 1, 1, 18, 6.21, -0.35, 1, 2, 18, 12.6, -0.42, 0.0009, 19, 3.54, -0.1, 0.9991, 1, 19, 8.67, 0.56, 1, 2, 12, 7.14, -10.88, 0.06113, 11, 36.27, 2.15, 0.93887, 1, 11, 48.82, -0.79, 1, 2, 12, -0.03, 1.67, 0.99564, 15, -31.34, -19.96, 0.00436, 1, 13, 0.7, -6.38, 1, 2, 13, -21.19, -6.97, 0.016, 11, 8.72, -13.34, 0.984, 1, 11, 5.14, 15.51, 1, 2, 13, 3.36, -4.63, 0.99199, 11, 33.27, -11, 0.00801, 2, 13, 4.31, -1.8, 0.96, 11, 34.22, -8.17, 0.04, 1, 13, 4.62, 0.9, 1, 1, 13, 3.44, 3.97, 1, 1, 13, 1.19, 5.7, 1, 2, 13, -1.56, 5.62, 0.96799, 11, 28.35, -0.75, 0.03201, 2, 13, -3.86, 4.52, 0.952, 11, 26.05, -1.85, 0.048, 2, 13, -2.91, 0.87, 0.96001, 11, 27, -5.5, 0.03999, 1, 13, -1.56, -2.03, 1, 1, 13, -0.68, -4.08, 1, 2, 13, 0.87, 0.42, 0.92811, 11, 30.78, -5.95, 0.07189, 2, 13, -7.26, 2.17, 0.61585, 11, 22.65, -4.2, 0.38415, 2, 13, -7.68, -1.48, 0.61525, 11, 22.23, -7.85, 0.38475, 2, 13, -6.53, -4.83, 0.77806, 11, 23.38, -11.2, 0.22194, 2, 13, -3.68, -6.53, 0.93656, 11, 26.23, -12.9, 0.06344, 3, 12, 0.85, 5.02, 0.98994, 11, 29.98, 18.05, 3e-05, 15, -30.67, -23.36, 0.01004, 3, 12, 2.4, 3.97, 0.97447, 11, 31.53, 17, 0.02163, 15, -32.53, -23.09, 0.0039, 3, 12, 3.85, 1.77, 0.992, 11, 32.98, 14.8, 0.00797, 15, -34.79, -21.75, 3e-05, 2, 12, 3.83, -1.86, 0.984, 11, 32.96, 11.17, 0.016, 1, 12, 2.78, -4.48, 1, 1, 12, -0.17, -5.28, 1, 1, 12, -2.75, -5.06, 1, 1, 12, -4.02, -3.13, 1, 3, 12, -3.97, 0.14, 0.984, 11, 25.16, 13.17, 0.01374, 15, -28.47, -16.86, 0.00226, 3, 12, -3.5, 3.97, 0.93403, 11, 25.63, 17, 0.03186, 15, -27.22, -20.51, 0.03411, 2, 12, -1.15, 5.37, 0.97915, 15, -28.72, -22.8, 0.02085, 3, 12, -8.37, 6.34, 0.79886, 11, 20.76, 19.37, 0.09809, 15, -21.8, -20.51, 0.10305, 3, 12, -9.65, 3.74, 0.70681, 11, 19.48, 16.77, 0.18467, 15, -21.79, -17.62, 0.10851, 3, 12, -9.22, 0.34, 0.61871, 11, 19.91, 13.37, 0.30515, 15, -23.66, -14.74, 0.07614, 3, 12, -8.07, -2.98, 0.6909, 11, 21.06, 10.05, 0.28467, 15, -26.15, -12.26, 0.02443, 1, 11, 5.03, 14, 1, 1, 11, 0.61, 15.28, 1, 1, 11, -2.45, 9.84, 1, 1, 11, -1.43, 2.78, 1, 1, 11, 1.29, -3.76, 1, 1, 11, 4.78, -7.84, 1, 1, 11, 8.69, -10.31, 1, 1, 11, 9.79, -7.33, 1, 2, 13, -22.92, 4.14, 0.01778, 11, 6.99, -2.23, 0.98222, 1, 11, 4.44, 3.63, 1], "edges": [68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 72, 74, 74, 76, 76, 78, 78, 80, 6, 4, 4, 2, 2, 0, 68, 70, 0, 70, 82, 84, 84, 86, 56, 54, 54, 52, 52, 50, 48, 50, 48, 46, 46, 44, 80, 8, 8, 6, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 30, 32, 32, 34, 110, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 110, 138, 140, 140, 142, 142, 144, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 146, 168, 170, 170, 172, 172, 174, 86, 176, 176, 114, 114, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 86]}}, "Rectangle 1": {"Rectangle 1": {"x": 0.19, "width": 59, "y": 2.2, "height": 102}}, "bg2jack": {"bg2jack": {"width": 162, "height": 162}}, "Layer 1": {"Layer 1": {"width": 164, "type": "mesh", "hull": 24, "height": 52, "triangles": [15, 16, 24, 13, 14, 25, 25, 15, 24, 25, 14, 15, 16, 17, 24, 24, 18, 25, 24, 17, 18, 10, 11, 27, 11, 26, 27, 11, 12, 26, 26, 13, 25, 26, 12, 13, 26, 25, 21, 20, 21, 19, 18, 19, 25, 21, 25, 19, 7, 8, 28, 28, 8, 9, 28, 9, 27, 9, 10, 27, 1, 26, 0, 22, 0, 21, 22, 23, 0, 21, 0, 26, 2, 27, 1, 26, 1, 27, 7, 28, 29, 7, 29, 30, 3, 29, 2, 29, 28, 2, 2, 28, 27, 7, 30, 6, 6, 30, 5, 30, 4, 5, 30, 29, 4, 4, 29, 3], "uvs": [0.71576, 0.12336, 0.80181, 0.24613, 0.86872, 0.34305, 0.9435, 0.44966, 1, 0.5692, 1, 0.78566, 0.91687, 0.89228, 0.83589, 0.93751, 0.71404, 1, 0.57984, 1, 0.46409, 1, 0.33604, 1, 0.22233, 0.94074, 0.14038, 0.85351, 0.05126, 0.73074, 0, 0.59182, 0, 0.3172, 0.06937, 0.1912, 0.15849, 0.1169, 0.25683, 0.07166, 0.35722, 0, 0.47708, 0, 0.55493, 0, 0.6082, 0, 0.15267, 0.44966, 0.27252, 0.50136, 0.40774, 0.54336, 0.53067, 0.59505, 0.66384, 0.64997, 0.77755, 0.65643, 0.90252, 0.68874], "vertices": [1, 55, 6.42, 1.71, 1, 1, 56, 5.97, 1.11, 1, 1, 57, 2.55, 1.35, 1, 2, 58, 1.12, 2.57, 0.82962, 57, 15.95, 2.57, 0.17038, 1, 58, 12.25, 1.75, 1, 2, 58, 17.81, -8.04, 0.97079, 57, 32.63, -8.06, 0.02921, 5, 58, 8.7, -19.6, 0.62335, 57, 23.5, -19.61, 0.33184, 56, 37.18, -21.49, 0.0307, 55, 51.51, -23.86, 0.01331, 46, -85.77, 60.9, 0.00081, 6, 58, -1.69, -28.2, 0.23974, 57, 13.11, -28.2, 0.48753, 56, 26.11, -29.19, 0.16086, 55, 39.95, -30.8, 0.09936, 46, -72.29, 60.77, 0.01247, 53, -91.56, 81.85, 2e-05, 7, 58, -17.46, -40.9, 0.02812, 57, -2.68, -40.88, 0.27335, 56, 9.33, -40.51, 0.24283, 55, 22.44, -40.97, 0.36496, 46, -52.05, 60.3, 0.08721, 52, -65.4, 63.59, 0.00083, 53, -71.91, 76.96, 0.00271, 7, 58, -36.59, -51.77, 8e-05, 57, -21.82, -51.73, 0.08683, 56, -10.65, -49.74, 0.11675, 55, 1.88, -48.83, 0.48445, 46, -30.41, 56.26, 0.27629, 52, -43.99, 58.5, 0.01495, 53, -51.68, 68.29, 0.02065, 6, 57, -38.34, -61.09, 0.02398, 56, -27.89, -57.69, 0.03913, 55, -15.85, -55.61, 0.34849, 46, -11.75, 52.78, 0.45707, 52, -25.52, 54.1, 0.06213, 53, -34.23, 60.82, 0.06919, 6, 57, -56.61, -71.44, 0.00347, 56, -46.96, -66.49, 0.00779, 55, -35.46, -63.11, 0.15076, 46, 8.89, 48.92, 0.4779, 52, -5.09, 49.23, 0.17132, 53, -14.93, 52.54, 0.18877, 7, 57, -74.36, -77.95, 0.00012, 56, -65.18, -71.51, 0.00081, 55, -53.98, -66.89, 0.04625, 46, 26.66, 42.47, 0.30489, 52, 12.33, 41.92, 0.26704, 53, 1, 42.37, 0.37974, 54, -0.41, 44.33, 0.00116, 6, 56, -79.28, -73.02, 1e-05, 55, -68.16, -67.46, 0.01209, 46, 39.04, 35.55, 0.14227, 52, 24.36, 34.39, 0.2461, 53, 11.56, 32.9, 0.56035, 54, 7.17, 32.34, 0.03919, 5, 55, -84.09, -66.71, 0.00057, 46, 52.24, 26.59, 0.02746, 52, 37.1, 24.8, 0.10021, 53, 22.48, 21.28, 0.57198, 54, 14.5, 18.18, 0.29979, 4, 46, 59.17, 17.95, 0.00302, 52, 43.6, 15.82, 0.02311, 53, 27.36, 11.33, 0.30649, 54, 16.48, 7.27, 0.66738, 4, 46, 56.55, 3.91, 0, 52, 40.29, 1.93, 0, 53, 21.74, -1.8, 1e-05, 54, 7.49, -3.82, 0.99999, 2, 53, 8.7, -3.34, 0.80564, 54, -5.48, -1.74, 0.19436, 1, 52, 12.59, -2.18, 1, 1, 46, 12.8, -0.91, 1, 3, 55, -50.79, -13.3, 0.03317, 46, -4.06, -1.56, 0.96682, 53, -38.61, 6.12, 0, 2, 55, -32.43, -6.28, 0.41461, 46, -23.39, 2.05, 0.58539, 4, 57, -51.02, -8.5, 1e-05, 55, -20.5, -1.72, 0.69528, 46, -35.94, 4.39, 0.30472, 53, -68.41, 18.89, 0, 2, 55, -12.34, 1.4, 0.85264, 46, -44.53, 6, 0.14736, 5, 55, -73.77, -47.12, 0.0029, 46, 33.2, 15.27, 0.07064, 52, 17.53, 14.43, 0.38143, 53, 1.44, 14.39, 0.54124, 54, -7.62, 17.3, 0.0038, 6, 57, -78.46, -54.02, 0.00012, 56, -67.28, -47.32, 0.00069, 55, -54.45, -42.61, 0.0431, 46, 14.37, 21.52, 0.47822, 52, -0.97, 21.6, 0.29343, 53, -15.57, 24.61, 0.18444, 6, 57, -58.09, -44.98, 0.00519, 56, -46.23, -40.01, 0.01016, 55, -32.96, -36.73, 0.23995, 46, -7.02, 27.74, 0.61205, 52, -22.03, 28.86, 0.06748, 53, -35.09, 35.35, 0.06517, 6, 57, -39.22, -37.38, 0.0295, 56, -26.8, -34, 0.0486, 55, -13.17, -32.04, 0.50738, 46, -26.35, 34.08, 0.37542, 52, -41.02, 36.14, 0.0168, 53, -52.56, 45.76, 0.0223, 7, 58, -33.61, -29.14, 0.00237, 57, -18.81, -29.1, 0.12689, 56, -5.77, -27.44, 0.19829, 55, 8.25, -26.91, 0.5467, 46, -47.29, 40.9, 0.11997, 52, -61.61, 43.98, 0.00165, 53, -71.51, 56.99, 0.00414, 6, 58, -17.23, -20.22, 0.05141, 57, -2.42, -20.2, 0.37498, 56, 11.3, -19.93, 0.34771, 55, 25.79, -20.57, 0.20352, 46, -65.56, 44.65, 0.02217, 53, -88.52, 64.65, 0.0002, 5, 58, 1.42, -11.56, 0.52357, 57, 16.24, -11.56, 0.44395, 56, 30.61, -12.87, 0.02314, 55, 45.53, -14.82, 0.00885, 46, -85.4, 50.06, 0.00049], "edges": [32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 30, 32, 28, 30]}}, "Layer 2": {"Layer 2": {"width": 75, "type": "mesh", "hull": 22, "height": 109, "triangles": [30, 12, 31, 11, 12, 30, 10, 11, 30, 9, 30, 8, 10, 30, 9, 28, 33, 27, 6, 28, 27, 12, 32, 31, 34, 33, 28, 34, 31, 33, 29, 34, 28, 7, 28, 6, 29, 28, 7, 29, 30, 34, 8, 29, 7, 8, 30, 29, 34, 30, 31, 23, 14, 15, 24, 23, 16, 23, 13, 14, 32, 13, 23, 33, 24, 25, 12, 13, 32, 33, 31, 32, 24, 32, 23, 32, 24, 33, 15, 16, 23, 26, 18, 22, 17, 24, 16, 26, 25, 17, 26, 17, 18, 17, 25, 24, 27, 26, 3, 25, 26, 27, 33, 25, 27, 20, 21, 0, 22, 19, 20, 18, 19, 22, 22, 20, 0, 0, 1, 22, 2, 22, 1, 3, 22, 2, 26, 22, 3, 4, 27, 3, 5, 27, 4, 5, 6, 27], "uvs": [1, 0.17514, 0.92781, 0.45619, 0.92357, 0.49898, 0.89954, 0.55246, 0.86747, 0.66953, 0.81659, 0.76386, 0.70777, 0.85333, 0.52262, 0.91751, 0.33465, 0.95155, 0.29225, 1, 0.07742, 1, 0, 0.90098, 0, 0.77164, 0, 0.66175, 0, 0.54311, 0, 0.3462, 0.25224, 0.32004, 0.42042, 0.32199, 0.45293, 0.32101, 0.46282, 0.28698, 0.55469, 0, 1, 0, 0.7257, 0.46883, 0.17211, 0.57228, 0.30497, 0.55866, 0.40673, 0.54602, 0.4887, 0.45558, 0.50707, 0.64813, 0.47457, 0.76969, 0.36857, 0.88153, 0.16222, 0.88444, 0.16929, 0.78817, 0.16081, 0.65883, 0.34878, 0.66661, 0.32475, 0.78914], "vertices": [1, 6, 17.86, 14.44, 1, 2, 6, 48.97, 14.51, 0.99912, 64, -28.88, 50.62, 0.00088, 2, 6, 53.62, 15.02, 0.99174, 64, -24.2, 50.58, 0.00826, 3, 6, 59.68, 14.28, 0.95824, 63, 5.68, 50.66, 0.00011, 64, -18.28, 49.13, 0.04165, 3, 6, 72.66, 14.16, 0.78665, 63, 18.35, 47.79, 0.00641, 64, -5.39, 47.49, 0.20694, 4, 6, 83.45, 12.22, 0.61102, 7, -5.89, 32.53, 2e-05, 63, 28.49, 43.61, 0.011, 64, 5.1, 44.3, 0.37796, 4, 6, 94.49, 5.9, 0.41638, 7, 2.67, 41.94, 4e-05, 63, 37.93, 35.1, 0.00969, 64, 15.32, 36.74, 0.57389, 3, 6, 103.83, -6.53, 0.1965, 63, 44.42, 20.96, 0.00128, 64, 23.14, 23.3, 0.80222, 3, 6, 109.96, -19.76, 0.03227, 64, 27.68, 9.45, 0.58355, 65, 10.14, 9.89, 0.38418, 3, 6, 115.72, -21.96, 0.00605, 64, 33.15, 6.59, 0.18936, 65, 15.63, 7.09, 0.80458, 1, 65, 16.75, -8.99, 1, 3, 63, 41.2, -18.14, 0.01058, 64, 23.68, -15.94, 0.00907, 65, 6.38, -15.53, 0.98035, 4, 7, 55.34, 30.86, 0.01024, 63, 27.11, -17.63, 0.21648, 64, 9.61, -16.78, 0.28891, 65, -7.68, -16.51, 0.48437, 5, 7, 54.85, 18.89, 0.09901, 43, 8.65, 23.8, 0.00613, 63, 15.14, -17.19, 0.63028, 64, -2.34, -17.5, 0.17201, 65, -19.63, -17.34, 0.09258, 5, 7, 54.31, 5.97, 0.28463, 43, 12.62, 11.49, 0.28292, 63, 2.21, -16.72, 0.42103, 64, -15.25, -18.27, 0.00779, 65, -32.53, -18.24, 0.00363, 1, 43, 19.21, -8.94, 1, 3, 6, 43.3, -37.98, 0.00355, 7, 34.41, -17.55, 0.1909, 43, 2.08, -17.46, 0.80555, 3, 6, 41.28, -25.52, 0.13008, 7, 21.81, -16.82, 0.63145, 43, -9.99, -21.13, 0.23847, 3, 6, 40.75, -23.14, 0.24644, 7, 19.37, -16.82, 0.61575, 43, -12.28, -21.98, 0.13781, 3, 6, 36.96, -23.07, 0.4562, 7, 18.48, -20.5, 0.47916, 43, -11.85, -25.74, 0.06464, 2, 6, 4.96, -21.8, 0.99285, 7, 10.31, -51.47, 0.00715, 1, 6, -0.93, 11.08, 1, 2, 6, 53, -0.16, 0.72244, 7, -0.4, 0.12, 0.27756, 5, 7, 41.55, 9.68, 0.10039, 43, -0.64, 10.55, 0.0002, 63, 5.86, -3.94, 0.89646, 64, -12.85, -5.2, 0.002, 65, -30.26, -5.15, 0.00095, 4, 6, 68.2, -29.5, 0.00862, 7, 31.53, 8.6, 0.30052, 63, 4.74, 6.07, 0.68768, 64, -14.93, 4.66, 0.00318, 4, 6, 65.5, -22.23, 0.03831, 7, 23.85, 7.54, 0.68175, 63, 3.64, 13.75, 0.25181, 64, -16.76, 12.19, 0.02813, 3, 6, 54.71, -17.92, 0.00105, 7, 17.3, -2.05, 0.98923, 43, -19.34, -8.84, 0.00973, 4, 6, 75.13, -12.86, 0.26447, 7, 16.79, 18.97, 0.25922, 63, 15.04, 20.87, 0.19449, 64, -6.1, 20.37, 0.28181, 4, 6, 88.6, -12.92, 0.21224, 7, 19.77, 32.11, 0.04244, 63, 28.19, 17.95, 0.0667, 64, 7.27, 18.73, 0.67861, 5, 6, 102, -18.6, 0.07676, 7, 28.21, 43.96, 0.00081, 63, 40.08, 9.56, 6e-05, 64, 19.91, 11.53, 0.9013, 65, 2.35, 11.9, 0.02106, 2, 63, 39.84, -5.92, 0.00131, 65, 3.74, -3.52, 0.99869, 4, 7, 42.73, 33.18, 0.00018, 63, 29.37, -5.01, 0.03618, 64, 10.65, -4, 0.7202, 65, -6.77, -3.72, 0.24344, 4, 7, 42.78, 19.07, 0.02003, 63, 15.26, -5.13, 0.82986, 64, -3.38, -5.48, 0.12778, 65, -20.79, -5.34, 0.02233, 4, 6, 79.2, -24.19, 0.06356, 7, 28.73, 20.5, 0.10426, 63, 16.62, 8.93, 0.53739, 64, -3.38, 8.64, 0.29479, 4, 6, 92.67, -23.61, 0.05125, 7, 31.08, 33.77, 0.00906, 63, 29.9, 6.64, 0.013, 64, 10.06, 7.64, 0.92669], "edges": [34, 36, 36, 38, 34, 32, 40, 42, 38, 40, 0, 42, 0, 2, 2, 4, 4, 6, 32, 30, 28, 30, 28, 46, 46, 48, 48, 50, 50, 52, 52, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 26, 28, 24, 26, 22, 24, 18, 20, 22, 20]}}, "Layer 11": {"Layer 11": {"width": 82, "type": "mesh", "hull": 26, "height": 125, "triangles": [27, 5, 6, 28, 4, 5, 28, 5, 27, 29, 26, 1, 3, 29, 1, 3, 1, 2, 27, 6, 7, 20, 26, 29, 9, 4, 28, 3, 4, 9, 8, 28, 27, 8, 27, 7, 9, 28, 8, 29, 3, 30, 9, 30, 3, 18, 19, 31, 34, 11, 33, 17, 18, 31, 34, 17, 31, 16, 17, 34, 12, 33, 11, 33, 16, 34, 13, 32, 33, 16, 33, 32, 12, 13, 33, 15, 16, 32, 14, 32, 13, 15, 32, 14, 30, 31, 29, 19, 20, 29, 19, 29, 31, 30, 9, 10, 35, 31, 30, 34, 35, 30, 30, 11, 34, 10, 11, 30, 34, 31, 35, 26, 23, 24, 25, 26, 24, 0, 26, 25, 26, 0, 1, 22, 23, 26, 21, 22, 26, 20, 21, 26], "uvs": [0.39644, 0.24116, 0.413, 0.27523, 0.4459, 0.28489, 0.47534, 0.31329, 0.63111, 0.26781, 0.88134, 0.20987, 1, 0.31041, 1, 0.48535, 0.83026, 0.54386, 0.68393, 0.54158, 0.69699, 0.60951, 0.70109, 0.73764, 0.69972, 0.83065, 0.66558, 0.92652, 0.62051, 1, 0.41699, 1, 0.28041, 0.92025, 0.20529, 0.84857, 0.13153, 0.76094, 0.06734, 0.60414, 0.02754, 0.48543, 0.00596, 0.38331, 0, 0.35513, 0, 0.32483, 0, 0, 0.33923, 0, 0.2052, 0.35134, 0.91771, 0.31098, 0.75406, 0.35698, 0.33673, 0.46888, 0.5376, 0.57112, 0.36646, 0.57099, 0.49348, 0.92473, 0.47982, 0.82617, 0.45524, 0.73746, 0.44158, 0.62905], "vertices": [2, 60, 28.51, 17.08, 0.62304, 61, 12.35, 17.34, 0.37696, 2, 60, 32.86, 18.07, 0.34966, 61, 14.63, 13.49, 0.65034, 2, 60, 34.29, 20.66, 0.20873, 61, 17.53, 12.93, 0.79127, 2, 60, 38.04, 22.76, 0.06586, 61, 20.69, 10.01, 0.93414, 1, 61, 31.85, 18.44, 1, 1, 61, 50.2, 30.12, 1, 1, 61, 62.52, 20.08, 1, 1, 61, 67.45, -1.23, 1, 1, 61, 55.55, -11.49, 1, 1, 61, 43.79, -13.93, 1, 1, 61, 46.75, -21.96, 1, 1, 61, 50.69, -37.48, 1, 1, 61, 53.21, -48.83, 1, 1, 61, 53.19, -61.14, 1, 1, 61, 51.66, -70.92, 1, 1, 61, 35.4, -74.69, 1, 1, 61, 22.24, -67.51, 1, 1, 61, 14.22, -60.17, 1, 1, 61, 5.85, -50.86, 1, 2, 60, 71.44, -13.64, 0.03523, 61, -3.7, -32.96, 0.96477, 2, 60, 56.38, -15.64, 0.27232, 61, -10.23, -19.24, 0.72768, 2, 60, 43.51, -16.33, 0.72444, 61, -14.83, -7.2, 0.27556, 2, 60, 39.96, -16.52, 0.8433, 61, -16.1, -3.88, 0.1567, 2, 60, 36.18, -16.2, 0.93297, 61, -16.96, -0.19, 0.06703, 1, 60, -4.28, -12.77, 1, 1, 60, -1.93, 14.95, 1, 2, 60, 40.91, 0.29, 0.53161, 61, 0.18, 0.38, 0.46839, 1, 61, 55.96, 18.48, 1, 1, 61, 44.19, 9.85, 1, 1, 61, 14, -11.5, 1, 1, 61, 32.94, -20.23, 1, 1, 61, 19.26, -23.38, 1, 1, 61, 39.39, -64.11, 1, 1, 61, 35.52, -52.36, 1, 1, 61, 31.05, -42.01, 1, 1, 61, 26.9, -29.06, 1], "edges": [48, 50, 50, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 54, 54, 12, 12, 14, 14, 16, 16, 18, 58, 60, 60, 18, 58, 40, 40, 38, 18, 20, 20, 22, 38, 36, 22, 24, 24, 26, 26, 28, 36, 34, 34, 32, 28, 30, 32, 30, 10, 12, 40, 42, 42, 44, 44, 46, 46, 48]}}, "Layer 3": {"Layer 3": {"width": 160, "type": "mesh", "hull": 19, "height": 38, "triangles": [10, 11, 20, 11, 21, 20, 9, 20, 19, 9, 10, 20, 20, 21, 16, 16, 14, 15, 11, 22, 21, 11, 12, 22, 9, 19, 8, 8, 19, 7, 7, 19, 6, 19, 1, 6, 6, 2, 5, 6, 1, 2, 5, 2, 4, 20, 17, 19, 19, 0, 1, 0, 17, 18, 0, 19, 17, 20, 16, 17, 2, 3, 4, 14, 16, 21, 13, 21, 12, 14, 21, 13, 21, 22, 12], "uvs": [0.70824, 0.15974, 0.80633, 0.23895, 0.92055, 0.31816, 1, 0.32947, 1, 0.5275, 0.95011, 0.58974, 0.86411, 0.62368, 0.77005, 0.70289, 0.66389, 0.79342, 0.57924, 0.88394, 0.48786, 1, 0.08205, 1, 0, 0.34645, 0, 0.21066, 0.07264, 0.10881, 0.17477, 0, 0.30511, 0, 0.43546, 0, 0.58596, 0, 0.51205, 0.53881, 0.36021, 0.53316, 0.17477, 0.47658, 0.10624, 0.46526], "vertices": [1, 10, 20.57, -5.08, 1, 1, 10, 36.35, -2.53, 1, 1, 10, 54.55, 0.9, 1, 1, 10, 66.64, 4.85, 1, 1, 10, 69.22, -2.22, 1, 1, 10, 62.53, -7.17, 1, 1, 10, 50.04, -13.1, 1, 1, 10, 36.93, -21.08, 1, 1, 10, 22.15, -30.13, 1, 1, 10, 10.61, -38, 1, 1, 10, -1.62, -47.15, 1, 1, 10, -62.62, -69.39, 1, 1, 10, -83.46, -50.56, 1, 1, 10, -85.23, -45.71, 1, 1, 10, -75.63, -38.09, 1, 1, 10, -61.7, -28.61, 1, 1, 10, -42.11, -21.47, 1, 1, 10, -22.51, -14.32, 1, 1, 10, 0.11, -6.08, 1, 1, 10, -3.99, -29.36, 1, 1, 10, -26.88, -37.48, 1, 1, 10, -55.5, -45.62, 1, 1, 10, -65.95, -48.98, 1], "edges": [6, 4, 4, 2, 2, 0, 0, 36, 34, 36, 32, 34, 30, 32, 30, 28, 28, 26, 24, 26, 24, 22, 20, 22, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 6, 8, 10, 8]}}, "Layer 10": {"Layer 10": {"width": 209, "type": "mesh", "hull": 32, "height": 111, "triangles": [12, 45, 11, 32, 12, 13, 5, 34, 37, 6, 34, 5, 34, 6, 7, 7, 8, 33, 7, 33, 34, 40, 26, 41, 19, 20, 21, 22, 19, 21, 44, 19, 22, 18, 19, 44, 23, 44, 22, 38, 23, 24, 38, 44, 23, 39, 24, 25, 38, 24, 39, 17, 18, 44, 17, 44, 38, 16, 17, 38, 39, 16, 38, 15, 16, 39, 30, 31, 0, 43, 29, 30, 1, 43, 30, 0, 1, 30, 43, 42, 28, 43, 28, 29, 41, 26, 27, 41, 27, 28, 41, 28, 42, 2, 42, 43, 2, 43, 1, 3, 42, 2, 41, 42, 3, 4, 41, 3, 37, 41, 4, 5, 37, 4, 40, 25, 26, 39, 25, 40, 37, 40, 41, 35, 39, 40, 36, 35, 40, 15, 39, 35, 37, 36, 40, 14, 15, 35, 32, 13, 14, 36, 37, 34, 35, 32, 14, 32, 35, 36, 33, 32, 36, 34, 33, 36, 45, 32, 33, 45, 12, 32, 45, 33, 8, 10, 11, 45, 9, 10, 45, 8, 9, 45], "uvs": [1, 0.07701, 0.95572, 0.16411, 0.87721, 0.30137, 0.78469, 0.39112, 0.70057, 0.44919, 0.69216, 0.53894, 0.67534, 0.63661, 0.67954, 0.8425, 0.54496, 0.93489, 0.40337, 1, 0.35991, 1, 0.23934, 0.83986, 0.22813, 0.71052, 0.25757, 0.64453, 0.23794, 0.5759, 0.26458, 0.45975, 0.15523, 0.4096, 0.08093, 0.32513, 0.03747, 0.20371, 0, 0.05589, 0, 0, 0.05008, 0, 0.1384, 0.04276, 0.22161, 0.05455, 0.31288, 0.06634, 0.42743, 0.03939, 0.53835, 0.02759, 0.65377, 0.03265, 0.75309, 0.08656, 0.85419, 0.05455, 0.93567, 0.04107, 1, 0, 0.34603, 0.6644, 0.47309, 0.68967, 0.59298, 0.65093, 0.35409, 0.49931, 0.46503, 0.51278, 0.57419, 0.48246, 0.22077, 0.22639, 0.33977, 0.26008, 0.48472, 0.26513, 0.6413, 0.24155, 0.77461, 0.19269, 0.88824, 0.13878, 0.11519, 0.15732, 0.38898, 0.83793], "vertices": [1, 10, 66.36, -0.61, 1, 1, 10, 55.55, -8.5, 1, 3, 4, 63.64, -89.46, 2e-05, 10, 36.72, -20.62, 0.99855, 9, 57.26, 56.71, 0.00143, 4, 4, 49.54, -72.9, 0.01146, 5, 20.32, -71.78, 0.00972, 10, 15.93, -27.03, 0.93921, 9, 37.93, 46.75, 0.03961, 5, 4, 39.26, -57.24, 0.08057, 5, 9.1, -56.79, 0.07643, 10, -2.51, -30.28, 0.61831, 11, -45.32, -40.04, 0.00083, 9, 20.35, 40.3, 0.22386, 4, 4, 29.16, -57.8, 0.12099, 5, -0.95, -57.97, 0.08348, 10, -5.99, -39.78, 0.36962, 9, 18.59, 30.34, 0.42591, 4, 4, 17.8, -56.84, 0.10465, 5, -12.35, -57.72, 0.05152, 10, -11.36, -49.83, 0.1815, 9, 15.07, 19.5, 0.66233, 4, 4, -4.25, -62.9, 0.00059, 5, -33.98, -65.13, 0.00144, 10, -14.51, -72.49, 0.0089, 9, 15.95, -3.35, 0.98907, 3, 3, 6.99, -35.02, 0.32374, 4, -20.64, -37.85, 0.0042, 9, -12.18, -13.61, 0.67207, 2, 3, -3.34, -6.36, 0.9706, 9, -41.77, -20.83, 0.0294, 2, 3, -4.3, 2.67, 0.98288, 59, 15.8, -20.61, 0.01712, 3, 4, -24.91, 26.75, 0.0069, 14, 2.16, 76.18, 0.00043, 59, -9.4, -2.83, 0.99267, 4, 3, 24.73, 33.47, 0.00121, 4, -11.47, 32.3, 0.13645, 14, 7.09, 62.49, 0.01627, 59, -11.74, 11.52, 0.84607, 4, 3, 32.66, 28.13, 0.02827, 4, -2.93, 27.98, 0.35917, 14, 2.37, 54.17, 0.06095, 59, -5.59, 18.85, 0.5516, 4, 3, 39.8, 33.01, 0.00523, 4, 3.55, 33.71, 0.51153, 14, 7.8, 47.43, 0.13025, 59, -9.69, 26.47, 0.35299, 4, 4, 17.37, 31.22, 0.47991, 5, -18.21, 30.15, 0.00083, 14, 4.68, 33.73, 0.39819, 59, -4.12, 39.36, 0.12107, 3, 4, 17.59, 54.74, 0.10973, 14, 28.16, 32.43, 0.87687, 59, -26.98, 44.93, 0.0134, 3, 4, 23.19, 72, 0.02661, 14, 45.14, 26.04, 0.9714, 59, -42.5, 54.3, 0.00199, 3, 4, 34.24, 83.91, 0.00255, 14, 56.53, 14.45, 0.99743, 59, -51.59, 67.78, 2e-05, 1, 14, 67.22, -0.25, 1, 1, 14, 68.36, -6.35, 1, 1, 14, 58.06, -8.26, 1, 1, 14, 39.05, -6.96, 1, 1, 14, 21.71, -8.85, 1, 2, 5, 26.52, 33.03, 0.06258, 14, 2.72, -11.04, 0.93742, 3, 5, 36.26, 10.96, 0.71185, 14, -20.27, -18.35, 0.17808, 11, 0.16, 17.05, 0.11007, 2, 10, -27.66, 21.75, 0.11048, 11, 1.47, -6.14, 0.88952, 2, 10, -4.01, 16.95, 0.76893, 11, 0.91, -30.26, 0.23107, 2, 10, 15.37, 7.41, 0.99918, 11, -5.07, -51.02, 0.00082, 1, 10, 36.8, 7.2, 1, 1, 10, 53.82, 5.68, 1, 1, 10, 67.86, 7.8, 1, 4, 3, 32.43, 9.51, 0.31364, 4, -0.87, 9.47, 0.46503, 14, -16.21, 52.96, 0.01708, 59, 12.9, 16.64, 0.20425, 5, 3, 32.46, -17.2, 0.3205, 4, 2.44, -17.02, 0.43212, 5, -30.13, -18.92, 0.00364, 10, -54.01, -48.2, 0.01091, 9, -27.2, 13.61, 0.23282, 5, 3, 39.39, -41.66, 0.0199, 4, 12.33, -40.45, 0.18726, 5, -18.81, -41.69, 0.04581, 10, -28.58, -48.37, 0.10209, 9, -2.14, 17.91, 0.64495, 4, 4, 17.36, 12, 0.83443, 5, -17.04, 10.97, 0.00028, 14, -14.52, 34.64, 0.10141, 59, 14.59, 34.97, 0.06388, 6, 3, 51.81, -13.44, 0.00732, 4, 21.18, -10.92, 0.82741, 5, -11.81, -11.67, 0.05592, 10, -52.21, -28.58, 0.02726, 11, -52.38, 9.19, 0.00029, 9, -28.88, 33.25, 0.08179, 6, 3, 57.57, -35.77, 0.00189, 4, 29.65, -32.36, 0.28482, 5, -2.03, -32.55, 0.18842, 10, -29.16, -29.27, 0.22718, 11, -49.02, -13.63, 0.01143, 9, -6.07, 36.61, 0.28625, 3, 4, 40.51, 46.03, 0.05315, 14, 18.4, 9.94, 0.94101, 59, -13.28, 65.26, 0.00584, 4, 4, 42.53, 20.96, 0.14183, 5, 7.53, 21.47, 0.21945, 14, -6.73, 9.07, 0.6319, 59, 11.59, 61.52, 0.00683, 5, 4, 48.88, -8.66, 0.01796, 5, 15.7, -7.71, 0.87738, 10, -43.33, -2.24, 0.04708, 11, -24.89, 5.07, 0.04529, 9, -24.77, 60.74, 0.01229, 5, 4, 58.88, -39.93, 0.02687, 5, 27.61, -38.3, 0.10082, 10, -10.65, -5.41, 0.73813, 11, -22.28, -27.65, 0.09092, 9, 7.96, 63.35, 0.04327, 4, 4, 70.5, -65.83, 0.00053, 5, 40.81, -63.43, 9e-05, 10, 17.73, -4.97, 0.99659, 9, 35.82, 68.78, 0.00279, 1, 10, 42.16, -3.26, 1, 3, 4, 42.96, 69.26, 0.00381, 14, 41.5, 6.42, 0.996, 59, -35.34, 72.93, 0.00019, 3, 3, 14.23, -1.46, 0.99378, 4, -17.58, -3.65, 0.00022, 9, -44.78, -2.84, 0.006], "edges": [38, 40, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 18, 20, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 0, 62, 2, 0, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62]}}}}, "skeleton": {"images": "./thanrung_play/", "width": 293.08, "spine": "3.6.53", "hash": "pJxDTZFA5fHAmINe+YIkPb1tbFA", "height": 354}, "slots": [{"attachment": "bgbonus", "name": "bgbonus", "bone": "root"}, {"attachment": "cliping", "name": "cliping", "bone": "root"}, {"attachment": "bg2jack", "name": "bg2jack", "bone": "root"}, {"attachment": "Layer 1", "name": "Layer 1", "bone": "Layer 81"}, {"attachment": "Layer 2", "name": "Layer 2", "bone": "Layer 101"}, {"attachment": "Layer 3", "name": "Layer 3", "bone": "Layer 13"}, {"attachment": "Layer 10", "name": "Layer 10", "bone": "Layer 17"}, {"attachment": "Layer 11", "name": "Layer 11", "bone": "Layer 95"}, {"attachment": "Layer 12", "name": "Layer 12", "bone": "Layer 36"}, {"attachment": "Layer 13", "name": "Layer 13", "bone": "Layer 41"}, {"attachment": "Layer 14", "name": "Layer 14", "bone": "Layer 14"}, {"attachment": "Layer 15", "name": "Layer 15", "bone": "Layer 15"}, {"attachment": "Layer 16", "name": "Layer 16", "bone": "Layer 18"}, {"attachment": "Layer 17", "name": "Layer 17", "bone": "Layer 31"}, {"attachment": "Layer 18", "name": "Layer 18", "bone": "Layer 27"}, {"attachment": "Layer 19", "name": "Layer 19", "bone": "Layer 25"}, {"attachment": "Layer 20", "name": "Layer 20", "bone": "Layer 77"}, {"attachment": "Layer 21", "name": "Layer 21", "bone": "Layer 79"}, {"attachment": "Layer 22", "name": "Layer 22", "bone": "Layer 78"}, {"attachment": "Layer 23", "name": "Layer 23", "bone": "Layer 23"}, {"attachment": "Layer 24", "name": "Layer 24", "bone": "Layer 24"}, {"attachment": "Layer 25", "blend": "additive", "name": "Layer 25", "bone": "Layer 111"}, {"attachment": "Layer 25", "blend": "additive", "name": "Layer 27", "bone": "Layer 113"}, {"attachment": "Layer 25", "blend": "additive", "name": "Layer 26", "bone": "Layer 112"}, {"color": "ffd211ff", "attachment": "elip", "blend": "additive", "name": "elip", "bone": "<PERSON><PERSON><PERSON>"}, {"attachment": "JackPot2", "name": "JackPot2", "bone": "JackPot2"}, {"color": "cfff0000", "attachment": "Rectangle 1", "blend": "additive", "name": "Rectangle 1", "bone": "Rectangle 1"}], "bones": [{"name": "root"}, {"parent": "root", "name": "JackPot2", "x": 8, "y": -56.82}, {"parent": "root", "name": "<PERSON>", "x": 3.13, "y": -90.89}, {"parent": "<PERSON>", "rotation": 83.91, "name": "Layer 10", "length": 32.13, "x": -16.89, "y": -9.51}, {"parent": "Layer 10", "rotation": -7.07, "name": "Layer 11", "length": 33.69, "x": 32.13}, {"parent": "Layer 11", "rotation": -3.54, "name": "Layer 12", "length": 26.69, "x": 33.69}, {"parent": "Layer 12", "rotation": -173.46, "name": "Layer 2", "length": 56.22, "x": 8.57, "y": 28.13}, {"parent": "Layer 2", "rotation": -77.48, "name": "Layer 3", "length": 40.1, "x": 52.97, "y": -0.58}, {"parent": "<PERSON>", "name": "Layer 9", "x": -2.72, "y": 4.4}, {"parent": "Layer 9", "name": "Layer 8", "x": 33.58, "y": 2.93}, {"parent": "Layer 12", "rotation": -63.18, "name": "Layer 13", "length": 59.13, "x": 37.25, "y": -45.36}, {"parent": "Layer 12", "rotation": 16.7, "name": "Layer 16", "length": 48.64, "x": 41, "y": -5.41}, {"parent": "Layer 16", "name": "Layer 14", "x": 29.13, "y": 13.03}, {"parent": "Layer 16", "name": "Layer 15", "x": 29.91, "y": -6.37}, {"parent": "Layer 12", "rotation": 96.19, "name": "Layer 17", "length": 58.6, "x": 15.83, "y": 29.14}, {"parent": "Layer 16", "rotation": 154.06, "name": "Layer 18", "length": 15.51, "x": -7.82, "y": 10.46}, {"parent": "Layer 18", "rotation": 8.7, "name": "Layer 19", "length": 11.45, "x": 15.51}, {"parent": "Layer 19", "rotation": 4.37, "name": "Layer 20", "length": 13.54, "x": 11.45}, {"parent": "Layer 20", "rotation": 8.11, "name": "Layer 21", "length": 9.08, "x": 13.54}, {"parent": "Layer 21", "rotation": -5.24, "name": "Layer 22", "length": 13.02, "x": 9.08}, {"parent": "Layer 16", "rotation": -101.52, "name": "Layer 23", "length": 20.39, "x": 39.41, "y": -2.1}, {"parent": "Layer 16", "rotation": 102.24, "name": "Layer 24", "length": 16.48, "x": 36.89, "y": 14.19}, {"parent": "Layer 16", "rotation": 155.46, "name": "Layer 25", "length": 9.81, "x": 25.45, "y": 4.49}, {"parent": "Layer 25", "rotation": 14.33, "name": "Layer 26", "length": 9.86, "x": 9.81}, {"parent": "Layer 16", "name": "Layer 27", "x": 10.12, "y": -2.3}, {"parent": "Layer 27", "rotation": -63.7, "name": "Layer 28", "length": 18.83, "x": -0.19, "y": -0.97}, {"parent": "Layer 28", "rotation": -113.18, "name": "Layer 29", "length": 10.69, "x": 18.83}, {"parent": "Layer 29", "rotation": -10.15, "name": "Layer 30", "length": 14.27, "x": 10.69}, {"parent": "Layer 16", "name": "Layer 31", "x": 8.18, "y": 15.55}, {"parent": "Layer 31", "rotation": 66.61, "name": "Layer 32", "length": 15.64, "x": -0.39, "y": -0.19}, {"parent": "Layer 32", "rotation": 90.49, "name": "Layer 33", "length": 9.48, "x": 15.49, "y": 0.36}, {"parent": "Layer 33", "rotation": 18.49, "name": "Layer 34", "length": 10.12, "x": 9.48}, {"parent": "Layer 16", "name": "Layer 36", "x": 0.37, "y": -12.54}, {"parent": "Layer 36", "rotation": -131.42, "name": "Layer 37", "length": 13.47, "x": -0.59, "y": -0.3}, {"parent": "Layer 37", "rotation": -53.06, "name": "Layer 38", "length": 15.19, "x": 13.47}, {"parent": "Layer 38", "rotation": -4.98, "name": "Layer 39", "length": 14.45, "x": 15.19}, {"parent": "Layer 39", "rotation": 14.54, "name": "Layer 40", "length": 13.42, "x": 14.45}, {"parent": "Layer 16", "name": "Layer 41", "x": 31.56, "y": 22.21}, {"parent": "Layer 41", "rotation": 163.07, "name": "Layer 42", "length": 21.42, "x": -1.78, "y": -0.59}, {"parent": "Layer 42", "rotation": -32.26, "name": "Layer 43", "length": 17.27, "x": 21.42}, {"parent": "Layer 43", "rotation": 10.37, "name": "Layer 44", "length": 17.53, "x": 17.69, "y": 0.03}, {"parent": "Layer 44", "rotation": 31.93, "name": "Layer 45", "length": 17.35, "x": 17.53}, {"parent": "Layer 45", "rotation": 0.54, "name": "Layer 46", "length": 16.14, "x": 17.35}, {"parent": "Layer 3", "rotation": -20.24, "name": "Layer 78", "length": 14.72, "x": 38.5, "y": -0.45}, {"parent": "Layer 78", "rotation": -153.12, "name": "Layer 77", "x": 14.29, "y": -0.14}, {"parent": "Layer 16", "name": "Layer 81", "x": 46.97, "y": 3.9}, {"parent": "Layer 81", "rotation": 100.57, "name": "Layer 79", "length": 16.4, "x": -7.7, "y": 24.6}, {"parent": "Layer 81", "rotation": -7.68, "name": "Layer 82", "length": 19.31, "x": 0.21, "y": -0.21}, {"parent": "Layer 82", "rotation": -16.7, "name": "Layer 83", "length": 30.21, "x": 19.31}, {"parent": "Layer 83", "rotation": -53.99, "name": "Layer 84", "length": 22.39, "x": 30.21}, {"parent": "Layer 84", "rotation": 21.39, "name": "Layer 85", "length": 15.39, "x": 22.39}, {"parent": "Layer 85", "rotation": 45.15, "name": "Layer 86", "length": 18.89, "x": 14.97, "y": 0.31}, {"parent": "Layer 79", "rotation": 2.82, "name": "Layer 87", "length": 18.56, "x": 16.4}, {"parent": "Layer 87", "rotation": 9.81, "name": "Layer 88", "length": 13.1, "x": 18.56}, {"parent": "Layer 88", "rotation": 15.84, "name": "Layer 89", "length": 10.24, "x": 13.49, "y": -0.17}, {"parent": "Layer 81", "rotation": -110.92, "name": "Layer 90", "length": 15.65, "x": -11.14, "y": -31.3}, {"parent": "Layer 90", "rotation": -3.85, "name": "Layer 91", "length": 15.39, "x": 15.85, "y": 0.08}, {"parent": "Layer 91", "rotation": -4.76, "name": "Layer 92", "length": 14.83, "x": 15.39}, {"parent": "Layer 92", "rotation": -0.07, "name": "Layer 93", "length": 10.88, "x": 14.83}, {"parent": "Layer 9", "name": "Layer 94", "x": -33.08, "y": 2.71}, {"parent": "Layer 12", "rotation": -158.46, "name": "Layer 95", "length": 41.21, "x": 17.22, "y": -46.7}, {"parent": "Layer 95", "rotation": 72.11, "name": "Layer 96", "length": 29.29, "x": 41.21}, {"parent": "Layer 96", "rotation": 60.61, "name": "Layer 97", "length": 16.5, "x": 44.61, "y": -7.52}, {"parent": "Layer 78", "rotation": 109.96, "name": "Layer 101", "length": 19.15, "x": -2.34, "y": 3.7}, {"parent": "Layer 101", "rotation": -5.52, "name": "Layer 102", "length": 17.43, "x": 19.15}, {"parent": "Layer 102", "rotation": -0.56, "name": "Layer 103", "length": 15, "x": 17.45, "y": -0.35}, {"parent": "<PERSON>", "name": "Layer 111", "x": 88.42, "y": 49.44}, {"parent": "<PERSON>", "name": "Layer 112", "x": 88.42, "y": 49.44}, {"parent": "<PERSON>", "name": "Layer 113", "x": 88.42, "y": 49.44}, {"parent": "root", "scaleY": 2.042, "rotation": 90, "name": "Rectangle 1", "x": 6.53, "y": -53.48}, {"scaleX": 2.578, "parent": "<PERSON>", "scaleY": 2.578, "name": "<PERSON><PERSON><PERSON>", "x": 89.42, "y": 47.79}], "animations": {"animation": {"slots": {"Layer 26": {"attachment": [{"name": null, "time": 0}]}, "Layer 25": {"attachment": [{"name": null, "time": 0}]}, "elip": {"attachment": [{"name": null, "time": 0}]}, "Layer 27": {"attachment": [{"name": null, "time": 0}]}, "Rectangle 1": {"color": [{"color": "cfff0000", "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"color": "cfff00ff", "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"color": "cfff0000", "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"color": "cfff00ff", "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"color": "cfff0050", "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"color": "cfff00ff", "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"color": "cfff0000", "time": 1.5667}]}}, "bones": {"Layer 46": {"rotate": [{"angle": -11.27, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 1.35, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -23.89, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -11.27, "time": 2}]}, "Layer 89": {"rotate": [{"angle": -3.01, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": -5.06, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.15, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": -3.01, "time": 2}]}, "Layer 45": {"rotate": [{"angle": -1.08, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 1.35, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -7.22, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": -1.08, "time": 2}]}, "Layer 84": {"rotate": [{"angle": 0.66, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.32, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 0.66, "time": 2}]}, "Layer 40": {"rotate": [{"angle": -7.15, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -20.9, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -7.15, "time": 2}]}, "Layer 83": {"rotate": [{"angle": -1.24, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"angle": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -12.83, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"angle": -1.24, "time": 2}]}, "Layer 82": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.91, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "Layer 88": {"rotate": [{"angle": -5.06, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.15, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -5.06, "time": 2}]}, "Layer 44": {"rotate": [{"angle": 0.96, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"angle": 1.35, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.7, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"angle": 0.96, "time": 2}]}, "Layer 87": {"rotate": [{"angle": -3.01, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 2.15, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -5.06, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -3.01, "time": 2}]}, "Layer 43": {"rotate": [{"angle": 1.35, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.5, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 1.35, "time": 2}]}, "Layer 86": {"rotate": [{"angle": -45.57, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -63.62, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -45.57, "time": 2}]}, "Layer 42": {"rotate": [{"angle": 1.35, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.5, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 1.35, "time": 2}]}, "Layer 85": {"rotate": [{"angle": -3.21, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.43, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -3.21, "time": 2}]}, "Layer 102": {"rotate": [{"angle": -5.95, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 15.52, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.46, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -5.95, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.6667}, {"x": 0, "y": 0, "time": 2}]}, "Layer 15": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.156, "y": 1, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 1, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.8333}]}, "Layer 103": {"rotate": [{"angle": -9.59, "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"angle": 10.66, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.46, "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"angle": -9.59, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.8333}, {"x": 0, "y": 0, "time": 2}]}, "Layer 14": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.156, "y": 1, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 1, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.8333}]}, "Layer 13": {"rotate": [{"angle": -9.13, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -18.25, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -9.13, "time": 2}]}, "Layer 12": {"rotate": [{"angle": 0.18, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0.63, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 0.18, "time": 2}], "translate": [{"x": -0.31, "y": -0.07, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"x": 0, "y": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.11, "y": -0.23, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"x": -0.31, "y": -0.07, "time": 2}]}, "Layer 19": {"rotate": [{"angle": 2.19, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"angle": 2.74, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.91, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"angle": 2.19, "time": 2}]}, "Layer 18": {"rotate": [{"angle": 2.74, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.91, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.74, "time": 2}]}, "Layer 17": {"rotate": [{"angle": 3.42, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 12.06, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 3.42, "time": 2}]}, "Layer 101": {"rotate": [{"angle": -1.74, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 7.98, "time": 0.5}, {"angle": -11.46, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -1.74, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.5}, {"x": 0, "y": 0, "time": 2}]}, "Layer 16": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -2.61, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "Rectangle 1": {"scale": [{"x": 1, "y": 1, "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.694, "y": 1.694, "time": 1.5667}], "translate": [{"x": 0, "y": 0, "time": 1.1667}]}, "Layer 9": {"scale": [{"x": 1, "y": 1, "time": 0}]}, "Tong": {"translate": [{"x": 8.74, "y": 8.79, "time": 0}]}, "Layer 91": {"rotate": [{"angle": -0.61, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": -1.7, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.13, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": -0.61, "time": 2}]}, "Layer 90": {"rotate": [{"angle": -1.33, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"angle": -1.7, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.13, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"angle": -1.33, "time": 2}]}, "Layer 95": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.12, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "Layer 93": {"rotate": [{"angle": 1.05, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": -1.7, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.13, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": 1.05, "time": 2}]}, "Layer 2": {"rotate": [{"angle": -0.68, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"angle": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 352.93, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"angle": -0.68, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.1667}, {"x": 0, "y": 0, "time": 2}]}, "Layer 92": {"rotate": [{"angle": 0.22, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": -1.7, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.13, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": 0.22, "time": 2}]}, "Layer 11": {"rotate": [{"angle": 0.06, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"angle": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0.63, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"angle": 0.06, "time": 2}], "translate": [{"x": -0.29, "y": -0.03, "time": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"x": 0, "y": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": -3.01, "y": -0.29, "time": 1.1667, "curve": [0.243, 0, 0.689, 0.75]}, {"x": -0.29, "y": -0.03, "time": 2}]}, "Layer 3": {"rotate": [{"angle": 2.44, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 8.61, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 2.44, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"x": 0, "y": 0, "time": 2}]}, "Layer 10": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0.63, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": -1.89, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2}]}, "Layer 97": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.12, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "Layer 96": {"rotate": [{"angle": 1.45, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.12, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 1.45, "time": 2}]}, "Layer 26": {"rotate": [{"angle": -1.75, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.5, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -1.75, "time": 2}], "scale": [{"x": 1.039, "y": 1.039, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"x": 1, "y": 1, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.077, "y": 1.077, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"x": 1.039, "y": 1.039, "time": 2}]}, "Layer 25": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.5, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "Layer 24": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": -2.9, "y": -0.01, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.6667}]}, "Layer 23": {"translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": -3.86, "y": -0.02, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.6667}]}, "Layer 29": {"rotate": [{"angle": 2.95, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": -3.3, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 9.21, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": 2.95, "time": 2}]}, "Layer 28": {"rotate": [{"angle": -0.74, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -4.36, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -0.74, "time": 2}]}, "JackPot2": {"scale": [{"x": 1, "y": 1, "time": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"x": 1, "y": 0.01, "time": 0.1667}, {"x": 1, "y": 1.199, "time": 0.3333}, {"x": 1, "y": 0, "time": 0.5}, {"x": 1, "y": 1.399, "time": 0.6667}, {"x": 1, "y": 0, "time": 0.8333, "curve": [0, 0.28, 0.75, 1]}, {"x": 1.222, "y": 1.222, "time": 1.1667, "curve": [0.25, 0, 1, 0.71]}, {"x": 1, "y": 1, "time": 1.5}], "translate": [{"x": -1.21, "y": -1.82, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.21, "y": 0.61, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.21, "y": -1.82, "time": 2}]}, "Layer 22": {"rotate": [{"angle": -4.56, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 2.74, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -7.45, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -4.56, "time": 2}]}, "Layer 21": {"rotate": [{"angle": -2.36, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 2.74, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -7.46, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -2.36, "time": 2}]}, "Layer 20": {"rotate": [{"angle": 0.78, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 2.74, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -4.16, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 0.78, "time": 2}]}, "Layer 37": {"rotate": [{"angle": 4.22, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 8.45, "time": 1.5, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": 4.22, "time": 2}]}, "Layer 79": {"rotate": [{"angle": 0.11, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 2.15, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -5.06, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": 0.11, "time": 2}]}, "Layer 78": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"angle": 0, "time": 2}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"x": 0, "y": 0, "time": 2}]}, "Layer 34": {"rotate": [{"angle": -6.18, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 6.67, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.28, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": -6.18, "time": 2}]}, "Layer 39": {"rotate": [{"angle": 8.51, "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"angle": -1.94, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 9.63, "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"angle": 8.51, "time": 2}]}, "Layer 38": {"rotate": [{"angle": 6.04, "time": 0, "curve": [0.351, 0.4, 0.757, 1]}, {"angle": 0, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 8.43, "time": 1.6667, "curve": [0.265, 0, 0.618, 0.43]}, {"angle": 6.04, "time": 2}]}, "Layer 33": {"rotate": [{"angle": -3.2, "time": 0, "curve": [0.382, 0.57, 0.735, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -11.28, "time": 1.3333, "curve": [0.243, 0, 0.649, 0.6]}, {"angle": -3.2, "time": 2}]}, "Layer 32": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 8.76, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2}]}, "Layer 30": {"rotate": [{"angle": 0.74, "time": 0, "curve": [0.311, 0.25, 0.757, 1]}, {"angle": -11.93, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.1, "time": 1.8333, "curve": [0.29, 0, 0.629, 0.38]}, {"angle": 0.74, "time": 2}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]