[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "adw94Z+hpN57wutNivq8Q5", "64mz+FORZEcawc82Z+6VA7", "693NzVwJNATa++M0jZzpuS", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "1dLz0IPHdDnYCGwnKmvxEo", "97PfppZ1dETrJuCDsgnBih", "c9qM75rxFL7qLQhl66DAEl", "c5H1zNoG5FNLT97wLAo1z6", "18s14wsJ1Nh7EeEYJxncZC", "c1iNYlndJPkbHWUAVaO4IP", "2cWB/vWPRHja3uQTinHH30", "81wnQ4xyFGJYePLVvA5Hj7", "52NwrUEelOea7J8/JsHGSO", "22i+iD1KJLOI3VVJsbMxGe"], ["node", "_N$file", "_spriteFrame", "_parent", "_textureSetter", "lbDesc", "lbWin", "lbRoom", "lbNickName", "lbSID", "lbTime", "lbSessionID", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "jackpotView", "bigWinView", "nodeBigWin", "nodeJackpot", "btnBigWin", "btnJackpot", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 1, 2, 4, 5, 7, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_N$interactable", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 0, 1, 9, 5, 5, 1, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["3c6a1IcT7pG8b7cqDRMjmCv", ["node", "btnJackpot", "btnBigWin", "nodeJackpot", "nodeBigWin", "bigWinView", "jackpotView"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["6c6718pco1KcbJmkeGVCmhO", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["3566aJpBw5KOJAxbKKG26cU", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["088def4kH5OCoVfmOqRAxDh", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["5f63a+KMdxKmINJ7inujvgg", ["node", "slotsLBJackpotListView"], 3, 1, 1], ["7843dJ1OI1CC4ZbqgEYhC5i", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["3f026hcsZBBlIzyApf7sacO", ["node", "slotsLBBigWinListView"], 3, 1, 1]], [[11, 0, 1, 2], [0, 0, 5, 3, 2, 4, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [1, 0, 6, 2, 3, 4, 5, 2], [1, 0, 6, 2, 3, 8, 4, 5, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [2, 0, 1, 3, 4, 5, 3], [14, 0, 1, 2, 3], [5, 0, 1, 2, 3, 4, 5, 6, 8, 8], [2, 3, 4, 5, 1], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 8, 7, 2], [0, 0, 5, 6, 2, 7, 2], [1, 0, 7, 2, 3, 4, 5, 2], [1, 0, 1, 6, 7, 2, 3, 3], [1, 0, 1, 6, 2, 3, 8, 4, 5, 3], [8, 0, 1, 2, 3, 4, 5, 2], [3, 0, 3, 4, 5, 6, 7, 2], [6, 0, 1, 2, 5, 6, 4], [6, 3, 0, 1, 4, 5, 6, 5], [15, 0, 1, 2, 2], [16, 0, 1, 2], [17, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 6, 3, 2, 4, 7, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [0, 0, 5, 3, 2, 4, 2], [9, 0, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 6, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 1], [2, 0, 3, 4, 5, 2], [2, 2, 0, 1, 3, 4, 5, 4], [3, 1, 0, 3, 4, 5, 6, 7, 3], [3, 3, 8, 1], [3, 2, 0, 3, 4, 5, 6, 7, 3], [18, 0, 1, 2, 3, 4, 5, 4], [19, 0, 1, 1], [20, 0, 1, 2, 3, 4, 5, 4], [21, 0, 1, 1]], [[[[24, "leaderboardView"], [25, "leaderboardView", [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22], [[30, -2, [35, 36], 34], [31, -9, -8, -7, -6, -5, -4, -3]], [0, "2dbllLgcFG1LLKGEfxBngg", -1]], [26, "<PERSON><PERSON>", [-31, -32, -33, -34, -35, -36], [[32, -30, -29, -28, -27, -26, -25, -24, -23]], [0, "f1Zr29nVFNc4+lqQgEWjLK", 1], [5, 799, 37], [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "<PERSON><PERSON>", [-45, -46, -47, -48, -49, -50], [[33, -44, -43, -42, -41, -40, -39, -38, -37]], [0, "e5AqW0a3tJpaBXmlbvndX4", 1], [5, 799, 37]], [6, "spriteBGTitle", 1, [-52, -53, -54, -55, -56, -57], [[10, -51, [28], 29]], [0, "0eRukxUudDlYHT262TAtUp", 1], [5, 803, 38], [0, 118, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "scrollview", [-60, -61], [-58, -59], [0, "b0J3oXQQxBy6yZE5O5fVCH", 1], [5, 803, 278], [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "scrollview", [-64, -65], [-62, -63], [0, "a9hyhDgjBKH7/C3JBN4i+/", 1], [5, 803, 278], [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "btnJackpot", 1, [[[7, 2, false, -66, [6], 7], -67], 4, 1], [0, "74GSqToTFF1qUkR44Tggnu", 1], [5, 142, 50], [-68, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "btnBigWin", 1, [[[7, 2, false, -68, [8], 9], -69], 4, 1], [0, "10bGZq5nRK3JsghawmYtEg", 1], [5, 142, 50], [68, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-72], [[18, 3, -71, [[8, "3c6a1IcT7pG8b7cqDRMjmCv", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -70]], [0, "51wos3Z/hLJZ3NhvZ6i6l2", 1], [5, 80, 80], [438, 245, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnContinue", 1, [-75], [[36, 1.1, 3, -74, [[8, "3c6a1IcT7pG8b7cqDRMjmCv", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -73]], [0, "43+T4wz0ZHgpXsNrMvNR+E", 1], [5, 180, 80], [0, -235, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbJackpotView", false, 1, [5], [-76], [0, "aaOIYmVT5Hwb4naB2vTaRD", 1]], [6, "layout-nick<PERSON><PERSON>", 2, [-78, -79], [[19, 1, 1, 5, -77, [5, 137, 50]]], [0, "6bq4j5uVNMwbn/VbeRJ2q1", 1], [5, 137, 50], [0.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbBig<PERSON>in<PERSON>iew", false, 1, [6], [-80], [0, "c07UuAtZ1NJaLvV6K8z0Gz", 1]], [6, "layout-nick<PERSON><PERSON>", 3, [-82, -83], [[19, 1, 1, 5, -81, [5, 137, 50]]], [0, "a8i3n9lqxAvpBKaJp30zfR", 1], [5, 137, 50], [0.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "black", 100, 1, [[34, 0, -84, [0], 1], [37, -85, [4, 4292269782]]], [0, "4cwykq/MFJ06igN5OdPLEK", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbJackpot", 1, [[3, "NỔ HŨ", 20, 50, false, false, 1, 1, 1, -86, [30], 31], [21, 2, -87, [4, 4281216558]]], [0, "9ec74J6chJhbd2bsvPKxay", 1], [5, 100, 30], [-68, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBigWin", 1, [[3, "THẮNG LỚN", 20, 50, false, false, 1, 1, 1, -88, [32], 33], [21, 2, -89, [4, 4281216558]]], [0, "a2jDHeNQJL2YjCeKOy99Zw", 1], [5, 123, 34], [68, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "view", 5, [-91], [[22, 0, -90]], [0, "fe1W/mSJtAPbxrG5J481Xn", 1], [5, 803, 270]], [12, "content", 18, [[20, false, 1, 2, 10, -92, [5, 1190, 75]]], [0, "f29EDfKPJIl6urWxmrRjWL", 1], [5, 803, 75], [0, 0.5, 1], [0, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "view", 6, [-94], [[22, 0, -93]], [0, "ddArICVlBNnr2Jqr0Pb8L1", 1], [5, 803, 270]], [12, "content", 20, [[20, false, 1, 2, 10, -95, [5, 1190, 75]]], [0, "cfbGoNyONLiZuO66CH8bIH", 1], [5, 803, 75], [0, 0.5, 1], [0, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[10, -96, [2], 3]], [0, "69Bzo+9IxFkbVJ1ebQF6yU", 1], [5, 1028, 674], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bảng thưởng", 1, [[10, -97, [4], 5]], [0, "7eeYVt0UlJHLTEY/LOlAFr", 1], [5, 179, 35], [14.752, 256.004, 0, 0, 0, 0, 1, 1, 1, 1]], [38, false, 2, 7, [[8, "3c6a1IcT7pG8b7cqDRMjmCv", "jackpotTabClicked", 1]], [4, 4294967295], [4, 4294967295], 7], [18, 2, 8, [[8, "3c6a1IcT7pG8b7cqDRMjmCv", "bigWinTabClicked", 1]], [4, 4294967295], [4, 4294967295], 8], [1, "sprite", 9, [[7, 2, false, -98, [10], 11]], [0, "52PT/dXKlLcpidUV31aXp4", 1], [5, 63, 59], [5.415, -5.61, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "sprite", 10, [[7, 2, false, -99, [12], 13]], [0, "61P29r18tDPYl40fVMls3G", 1], [5, 186, 73]], [1, "bg<PERSON><PERSON>nt", 1, [[35, 1, 0, false, -100, [14], 15]], [0, "fbq5GtuLFJFJsfTrTYnzbE", 1], [5, 803, 278], [0, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb\bSessionID", 4, [[3, "PHIÊN", 20, 50, false, false, 1, 1, 1, -101, [16], 17]], [0, "e3T1WgiUhDSb0qVJphhO46", 1], [5, 100, 30], [-339.6, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 4, [[3, "THỜI GIAN", 20, 50, false, false, 1, 1, 1, -102, [18], 19]], [0, "79UBLK0Y1NW5AeLXim6WOa", 1], [5, 100, 30], [-197.7, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbAccount", 4, [[3, "TÀI KHOẢN", 20, 50, false, false, 1, 1, 1, -103, [20], 21]], [0, "3abcoEyQ5DEaG0FWLnThT/", 1], [5, 100, 30], [0.9, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRoom", 4, [[3, "PHÒNG", 20, 50, false, false, 1, 1, 1, -104, [22], 23]], [0, "2dUEJ+aq5FHqc5N5Qiolff", 1], [5, 100, 30], [141, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 4, [[3, "THẮNG", 20, 50, false, false, 1, 1, 1, -105, [24], 25]], [0, "37MHwy4LpALIobx6bxnixP", 1], [5, 100, 30], [236, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 4, [[3, "MÔ TẢ", 20, 50, false, false, 1, 1, 1, -106, [26], 27]], [0, "27tWd7xlVNwZdFLZKRQ0oA", 1], [5, 100, 30], [335, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "temp", 5, [2], [0, "c472XMw+pH/5mxYda6sNNM", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lb\bSessionID", 2, [-107], [0, "210ThY/z1M6rDfyLVNiyla", 1], [5, 200, 30], [-339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "#********", 20, 50, false, false, 1, 1, 1, 36], [4, "lbTime", 2, [-108], [0, "d5J7ZEf+VByKHJjF7cqa9+", 1], [5, 220, 30], [-197.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "20:45 21-08-2018", 20, 50, false, false, 1, 1, 1, 38], [16, "lbSID", false, 12, [-109], [0, "6bb2HruPpNT6L6BsCwyKSI", 1], [4, 4279026733], [5, 37, 24], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "[TQ]", 20, 48, false, false, 1, 1, 40], [4, "lbNickName", 12, [-110], [0, "ff6e+PYfFCSJPwy+sAbQ3w", 1], [5, 95, 24], [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "<PERSON><PERSON><PERSON>", 20, 48, false, false, 1, 1, 42], [5, "lbRoom", 2, [-111], [0, "f8N6vb3utLI5BFvhBPn4lV", 1], [4, 4278255615], [5, 100, 30], [141, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "5.000", 20, 50, false, false, 1, 1, 1, 44], [5, "lbWin", 2, [-112], [0, "708v0jEOxFxYNffR2BceXP", 1], [4, 4278255615], [5, 200, 30], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "50.000.000", 20, 50, false, false, 1, 1, 1, 46], [5, "lbDesc", 2, [-113], [0, "aeFRezLlZOFqDukowi4dBc", 1], [4, 4278246399], [5, 200, 30], [335, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "NỔ HŨ", 20, 50, false, false, 1, 1, 1, 48], [23, false, 0.75, 0.23, null, null, 5, 19], [39, 15, 10, 400, 5, 2, 50], [40, 11, 51], [13, "temp", 6, [3], [0, "f3Wf2tspVOYpEZzb5nle5G", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lb\bSessionID", 3, [-114], [0, "bad3DFdvxLzpSucNBSjof+", 1], [5, 200, 30], [-339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHIÊN", 20, 50, false, false, 1, 1, 1, 54], [4, "lbTime", 3, [-115], [0, "60Y3X31idCX7keqwrSsan/", 1], [5, 220, 30], [-197.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THỜI GIAN", 20, 50, false, false, 1, 1, 1, 56], [16, "lbSID", false, 14, [-116], [0, "57qEd5e75DBJlNJKvqjfl+", 1], [4, 4279026733], [5, 37, 24], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "[TQ]", 20, 48, false, false, 1, 1, 58], [4, "lbNickName", 14, [-117], [0, "d2quDXnlNG7q6ZTEtTPCDB", 1], [5, 95, 24], [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "<PERSON><PERSON><PERSON>", 20, 48, false, false, 1, 1, 60], [5, "lbRoom", 3, [-118], [0, "1d+YIu7I5CBbWehFGShgma", 1], [4, 4278255615], [5, 100, 30], [141, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHÒNG", 20, 50, false, false, 1, 1, 1, 62], [5, "lbWin", 3, [-119], [0, "33Vmt0/7dCfIC+mJ85mx+7", 1], [4, 4278255615], [5, 200, 30], [224.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THẮNG", 20, 50, false, false, 1, 1, 1, 64], [5, "lbDesc", 3, [-120], [0, "5cWqbP6DVOnKMF8cXqgrhz", 1], [4, 4294829568], [5, 200, 30], [335, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "MÔ TẢ", 20, 50, false, false, 1, 1, 1, 66], [23, false, 0.75, 0.23, null, null, 6, 21], [41, 15, 10, 400, 6, 3, 68], [42, 13, 69]], 0, [0, 17, 1, 0, 0, 1, 0, 18, 52, 0, 19, 70, 0, 20, 13, 0, 21, 11, 0, 22, 25, 0, 23, 24, 0, 0, 1, 0, -1, 15, 0, -2, 22, 0, -3, 23, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, -8, 28, 0, -9, 4, 0, -10, 16, 0, -11, 17, 0, -12, 11, 0, -13, 13, 0, 5, 49, 0, 6, 47, 0, 7, 45, 0, 8, 43, 0, 9, 41, 0, 10, 39, 0, 11, 37, 0, 0, 2, 0, -1, 36, 0, -2, 38, 0, -3, 12, 0, -4, 44, 0, -5, 46, 0, -6, 48, 0, 5, 67, 0, 6, 65, 0, 7, 63, 0, 8, 61, 0, 9, 59, 0, 10, 57, 0, 11, 55, 0, 0, 3, 0, -1, 54, 0, -2, 56, 0, -3, 14, 0, -4, 62, 0, -5, 64, 0, -6, 66, 0, 0, 4, 0, -1, 29, 0, -2, 30, 0, -3, 31, 0, -4, 32, 0, -5, 33, 0, -6, 34, 0, -1, 50, 0, -2, 51, 0, -1, 35, 0, -2, 18, 0, -1, 68, 0, -2, 69, 0, -1, 53, 0, -2, 20, 0, 0, 7, 0, -2, 24, 0, 0, 8, 0, -2, 25, 0, 12, 9, 0, 0, 9, 0, -1, 26, 0, 12, 10, 0, 0, 10, 0, -1, 27, 0, -1, 52, 0, 0, 12, 0, -1, 40, 0, -2, 42, 0, -1, 70, 0, 0, 14, 0, -1, 58, 0, -2, 60, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, 0, 20, 0, -1, 21, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, -1, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, -1, 45, 0, -1, 47, 0, -1, 49, 0, -1, 55, 0, -1, 57, 0, -1, 59, 0, -1, 61, 0, -1, 63, 0, -1, 65, 0, -1, 67, 0, 24, 1, 2, 3, 35, 3, 3, 53, 5, 3, 11, 6, 3, 13, 120], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 24, 24, 24, 25, 25, 25, 25, 37, 39, 41, 43, 45, 47, 49, 55, 57, 59, 61, 63, 65, 67], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, 25, -1, -2, 13, 14, 15, 16, 13, 14, 15, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 6, 0, 7, 0, 8, 0, 4, 0, 3, 0, 9, 0, 10, 0, 11, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 12, 0, 2, 0, 2, 5, 5, 13, 3, 3, 3, 4, 3, 3, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[{"name": "button 2", "rect": [0, 0, 142, 50], "offset": [0, 0], "originalSize": [142, 50], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [14]], [[{"name": "button1", "rect": [0, 0, 142, 50], "offset": [0, 0], "originalSize": [142, 50], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [15]], [[{"name": "b<PERSON><PERSON><PERSON><PERSON> hạng", "rect": [0, 0, 179, 35], "offset": [0, 0], "originalSize": [179, 35], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [16]]]]