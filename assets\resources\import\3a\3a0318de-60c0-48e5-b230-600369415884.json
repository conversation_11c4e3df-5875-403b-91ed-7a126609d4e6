[1, ["60nVVu2/NI0rIt1OEqa/84"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "item4_p3", "\nitem4_p3.png\nsize: 168,168\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nitem4_p3\n  rotate: false\n  xy: 0, 28\n  size: 146, 140\n  orig: 146, 140\n  offset: 0, 0\n  index: -1\nsao\n  rotate: false\n  xy: 0, 0\n  size: 28, 28\n  orig: 30, 30\n  offset: 1, 1\n  index: -1\n", ["item4_p3.png"], {"skeleton": {"hash": "rEjRwZEiUgMcHElBa4L6qyqUyJQ", "spine": "3.6.53", "width": 150, "height": 135}, "bones": [{"name": "root"}, {"name": "item", "parent": "root"}, {"name": "sao", "parent": "item", "x": 22.74, "y": -21.65}], "slots": [{"name": "item", "bone": "item", "attachment": "item4_p3"}, {"name": "sao", "bone": "sao", "color": "ffffff00", "blend": "additive"}], "skins": {"default": {"item": {"item4_p3": {"width": 146, "height": 140}}, "sao": {"sao": {"x": -0.85, "y": 0.31, "width": 30, "height": 30}}}}, "animations": {"animation": {"slots": {"item": {"color": [{"time": 0, "color": "ffffffff"}]}, "sao": {"color": [{"time": 0, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "sao": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}, "win": {"slots": {"sao": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "sao"}, {"time": 0.5, "name": "sao"}, {"time": 1, "name": "sao"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 12.54}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.177, "y": 1.177}, {"time": 1, "x": 1, "y": 1}]}, "sao": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -88}, {"time": 1, "angle": 171.43}], "translate": [{"time": 0, "x": -8.64, "y": 0.35}, {"time": 0.5, "x": -9.49, "y": -0.75}, {"time": 1, "x": -4.87, "y": -5.68}], "scale": [{"time": 0, "x": 0.633, "y": 0.633}, {"time": 0.5, "x": 1.466, "y": 1.466}, {"time": 1, "x": 0.633, "y": 0.633}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]