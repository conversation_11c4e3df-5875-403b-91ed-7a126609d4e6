[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "84xfnELqxKRqm30lPZrw2z", "d82n49/IVAvIEqsa0xvvk0", "90Ss2Yf1lLHYPvA9KDgBaE", "42ubHxJ0NPDrmHQ0HqlaAN", "fdNoodJKVLj4dF1TLppv2g", "6fRJka0AdIh44AomkQXFvp", "a9VpD0DP5LJYQPXITZq+uj", "1ewsTTeZRBbL9DYfWsJfc7", "24xd2Xl+xHVZeWwPN10Wzf", "7daFLwLYNNmZ9x0849nYBc", "c1y3UL3AVHoqWPxPdQzt/K", "c25Leu0BdNDphgb/Hp9jw/", "951XXymkBM9YRsGEWVKOSD", "2cWB/vWPRHja3uQTinHH30", "43UTERCLBFc4Z+CB1AUpOF"], ["node", "_N$file", "_spriteFrame", "_parent", "_textureSetter", "lbDesc", "lbWin", "lbRoom", "lbNickName", "lbSID", "lbTime", "lbSessionID", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "seven77JackpotView", "seven77BigWinView", "nodeBigWin", "nodeJackpot", "btnBigWin", "btnJackpot", "_N$target", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 0, 4, 9, 1, 5, 2, 7, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_enableWrapText", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_children"], 1, 1, 2, 4, 5, 7, 5, 2], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_enabled", "_type", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "_enabled", "_N$interactable", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 0, 1, 9, 5, 5, 1, 5], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_children", "_parent"], 1, 12, 4, 5, 7, 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["e1650KcCVBGBIb27/vKGDIW", ["node", "btnJackpot", "btnBigWin", "nodeJackpot", "nodeBigWin", "seven77BigWinView", "seven77JackpotView"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["7d1b3RJbq1Nn46ut8/wPxfI", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["b112dxCbpxH2bWJIvx7ST28", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["579b7NDEktHdqw8CudCuHxL", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["299348Vq39AIYf9Tj3Xwc7Q", ["node", "seven77JackpotListView"], 3, 1, 1], ["461c4Oyy8hHSKyW+8XfjaCe", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["87b76tny+BAlYs6GjL8z81+", ["node", "seven77BigWinListView"], 3, 1, 1]], [[11, 0, 1, 2], [0, 0, 5, 4, 3, 6, 8, 2], [1, 0, 1, 3, 4, 2, 5, 6, 7, 8, 9, 9], [3, 0, 4, 5, 6, 2], [2, 0, 2, 3, 4, 5, 6, 2], [1, 0, 1, 3, 4, 2, 5, 6, 7, 8, 9, 10, 9], [2, 0, 2, 3, 4, 7, 5, 6, 2], [0, 0, 5, 7, 3, 8, 2], [0, 0, 5, 7, 4, 3, 6, 8, 2], [15, 0, 1, 2, 3], [0, 0, 7, 4, 3, 6, 2], [0, 0, 5, 7, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 9, 8, 2], [0, 0, 5, 4, 3, 6, 2], [6, 0, 6, 2, 3, 4, 5, 2], [6, 0, 1, 7, 2, 3, 4, 5, 3], [2, 0, 2, 8, 3, 4, 2], [2, 0, 1, 2, 3, 4, 7, 5, 6, 3], [2, 0, 2, 3, 4, 5, 2], [14, 0, 1], [3, 2, 0, 1, 4, 5, 6, 4], [3, 3, 0, 1, 4, 5, 6, 4], [7, 0, 1, 2, 5, 6, 4], [7, 3, 0, 1, 4, 5, 6, 5], [16, 0, 1, 1], [1, 0, 1, 3, 4, 2, 5, 6, 8, 8], [1, 0, 1, 3, 4, 2, 5, 6, 8, 9, 8], [17, 0, 1, 2, 3, 4, 5, 6, 6], [8, 0, 2], [0, 0, 7, 4, 3, 2], [0, 0, 5, 7, 3, 2], [0, 0, 1, 5, 4, 3, 6, 8, 3], [0, 0, 2, 5, 4, 3, 6, 8, 3], [9, 0, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 6, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 1], [3, 0, 1, 4, 5, 6, 3], [4, 0, 3, 4, 5, 6, 7, 2], [4, 3, 8, 1], [4, 1, 2, 0, 3, 4, 5, 6, 7, 4], [4, 1, 0, 3, 4, 5, 6, 7, 3], [1, 0, 1, 2, 8, 9, 10, 4], [1, 0, 1, 3, 4, 2, 5, 6, 8, 9, 10, 8], [18, 0, 1, 2, 3, 4, 5, 4], [19, 0, 1, 1], [20, 0, 1, 2, 3, 4, 5, 4], [21, 0, 1, 1]], [[[{"name": "button -on ", "rect": [0, 0, 186, 70], "offset": [0, 0], "originalSize": [186, 70], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [4], [7]], [[[28, "777TopView"], [29, "777TopView", [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21], [[33, -2, [55, 56], 54], [34, -9, -8, -7, -6, -5, -4, -3]], [0, "7fHXlkb8BCXoJIGfVmV1lL", -1]], [10, "item", [-30, -31, -32, -33, -34, -35], [[35, -29, -28, -27, -26, -25, -24, -23, -22]], [0, "07sJR8N9hIb78reZ3/cM4m", 1], [5, 994, 50]], [10, "item", [-44, -45, -46, -47, -48, -49], [[36, -43, -42, -41, -40, -39, -38, -37, -36]], [0, "a2FI8XBuFD8Ic5ksi+5vvy", 1], [5, 994, 50]], [30, "line", 1, [-50, -51, -52, -53, -54, -55], [0, "a5LnJ+oURP3LcVAKEL8nPj", 1]], [7, "title", 1, [-56, -57, -58, -59, -60, -61], [0, "897V4ZGXBLMpVpwTtr2IRN", 1], [0, 187, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "scrollview", [-65, -66], [[-62, -63, [19, -64]], 1, 1, 4], [0, "8dJto8JVlE34byKcwh1IIf", 1], [5, 1050, 430], [0, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "scrollview", [-70, -71], [[-67, -68, [19, -69]], 1, 1, 4], [0, "52mRHgn4RN6oOI35+Kbhf2", 1], [5, 1050, 430], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "btnJackpot", false, 1, [[[20, false, 2, false, -72, [6], 7], -73], 4, 1], [0, "2cPfkus7ZGyZnG0tSKtTfk", 1], [5, 186, 70], [-88, 202, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [15, "btnBigWin", false, 1, [[[20, false, 2, false, -74, [8], 9], -75], 4, 1], [0, "b5uyllx7lIYZsnsuiM3BmP", 1], [5, 186, 70], [88, 202, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [8, "btnClose", 1, [-78], [[38, 3, -77, [[9, "e1650KcCVBGBIb27/vKGDIW", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -76]], [0, "a1xJ2rI39IerWUqmtWBATF", 1], [5, 80, 80], [506.4, 289.1, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "jackpotView", 1, [6], [-79], [0, "a9f1PLYVdN47ebEHXr+LmD", 1]], [8, "layout-nick<PERSON><PERSON>", 2, [-81, -82], [[22, 1, 1, 5, -80, [5, 104.5, 50]]], [0, "09BihOJMtOT7YPrskt2ucH", 1], [5, 104.5, 50], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 1, [7], [-83], [0, "46nsbxP+FLmI4kXush5gbl", 1]], [8, "layout-nick<PERSON><PERSON>", 3, [-85, -86], [[22, 1, 1, 5, -84, [5, 104.5, 50]]], [0, "0aJbf+u4RMFqJX3+9FgvxX", 1], [5, 104.5, 50], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "black", 100, 1, [[3, 0, -87, [0], 1], [39, -88, [4, 4292269782]]], [0, "57lLe4ynVLcJhFmgutDRgy", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "view", 6, [-90], [[24, -89, [34]]], [0, "558uDhl5ZKK7fZoc1RW3Mt", 1], [5, 1050, 430]], [12, "content", 16, [[23, false, 1, 2, 10, -91, [5, 1000, -10]]], [0, "978HOoFu1BA6NTOUpp1E5m", 1], [5, 1000, -10], [0, 0.5, 1], [0, 188, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "view", 7, [-93], [[24, -92, [41]]], [0, "df9bCBadNAiLTpKg8lxE5o", 1], [5, 1050, 430]], [12, "content", 18, [[23, false, 1, 2, 10, -94, [5, 1000, -10]]], [0, "0bHoDJUVVJppOWfDlx2iGb", 1], [5, 1000, -10], [0, 0.5, 1], [0, 188, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "nen popup", 1, [[21, 1, 0, false, -95, [2], 3]], [0, "d3DK5ozCdAaZhXHa8nZITY", 1], [5, 1084, 618]], [1, "tit_popup", 1, [[42, "XẾP HẠNG KIM CƯƠNG", 26, false, -96, [4], 5]], [0, "22iyNewSRDf66hUxen29je", 1], [5, 480.19, 32.5], [0, 294, 0, 0, 0, 0, 1, 1, 1, 1]], [40, false, false, 2, 8, [[9, "e1650KcCVBGBIb27/vKGDIW", "jackpotTabClicked", 1]], [4, 4294967295], [4, 4294967295], 8], [41, false, 2, 9, [[9, "e1650KcCVBGBIb27/vKGDIW", "bigWinTabClicked", 1]], [4, 4294967295], [4, 4294967295], 9], [1, "bg<PERSON><PERSON>nt", 1, [[3, 0, -97, [10], 11]], [0, "48JEUcgORMzqOWi+0EujBb", 1], [5, 1050, 500], [0, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "sprite", 10, [[37, 2, false, -98, [12], 13]], [0, "90RM5FyTtKdZj+4toPB8z4", 1], [5, 69, 36]], [1, "1", 4, [[3, 0, -99, [14], 15]], [0, "143OfdgdRKmqHdYZ2BsvgG", 1], [5, 1050, 16], [0, 155, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 4, [[3, 0, -100, [16], 17]], [0, "6daQefIm5A14XimnXBWykI", 1], [5, 25, 550], [-399, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "3", 4, [[3, 0, -101, [18], 19]], [0, "88soeQffRMTra2M/oH14N9", 1], [5, 25, 550], [-177, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "4", 4, [[3, 0, -102, [20], 21]], [0, "34hSWBCJ9KS5HJ4UqMc8/E", 1], [5, 25, 550], [44, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "5", 4, [[3, 0, -103, [22], 23]], [0, "88ywxRuKJGxpLkgHLYyRta", 1], [5, 25, 550], [178, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "6", 4, [[3, 0, -104, [24], 25]], [0, "c5syP3mapIqZx3taoqn/SL", 1], [5, 25, 550], [319, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "title", false, 1, [[21, 1, 0, false, -105, [26], 27]], [0, "efejTqOXVO9pDWwhUJT8NN", 1], [5, 994, 50], [0, 142, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "temp", 6, [2], [0, "79hCYrm8ZMToLwlov4ZIlA", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbNo", 2, [-106], [0, "1ak95VC7FNao/DxmPGlv23", 1], [5, 150, 30], [-440, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "STT", 22, 50, false, false, 1, 1, 1, 34, [28]], [4, "lbTime", 2, [-107], [0, "a6z6k2m1tEKpShJ2PAwMOk", 1], [5, 200, 38], [-308, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, 36, [29]], [17, "lbSID", false, 12, [-108], [0, "99nMAHTEpEYJD4ZrLPswzA", 1], [4, 4279026733], [5, 40.7, 26.4], [-54.74999999999999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "[TQ]", 22, 48, false, false, 1, 1, 38], [18, "lbNickName", 12, [-109], [0, "70KLpTX7REgrqljLP5ds00", 1], [5, 104.5, 26.4]], [26, "<PERSON><PERSON><PERSON>", 22, 48, false, false, 1, 1, 40, [30]], [6, "lbRoom", 2, [-110], [0, "a2TiTUZJlPP6vjGgZ24gcM", 1], [4, 4278315513], [5, 200, 38], [104, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHÒNG", 22, 50, false, false, 1, 1, 1, 42, [31]], [6, "lbWin", 2, [-111], [0, "4avZy8ngJIloqhuYo1ryj8", 1], [4, 4278315513], [5, 200, 38], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THẮNG", 22, 50, false, false, 1, 1, 1, 44, [32]], [4, "lbDesc", 2, [-112], [0, "e2mHfGu/5Bvop97v0eAEDj", 1], [5, 200, 38], [393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "LOẠI", 22, 50, false, false, 1, 1, 1, 46, [33]], [27, false, 0.75, 0.23, null, null, 6, 17], [44, 20, 10, 400, 6, 2, 48], [45, 11, 49], [7, "temp", 7, [3], [0, "c6s9kn6MJJuIBotFpwNk8T", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbNo", 3, [-113], [0, "24IwIYFTRF8LA6s4pftn9x", 1], [5, 150, 30], [-440, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "STT", 22, 50, false, false, 1, 1, 1, 52, [35]], [4, "lbTime", 3, [-114], [0, "05ebpZxCZFH53dAOC7qGGo", 1], [5, 200, 38], [-308, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, 54, [36]], [17, "lbSID", false, 14, [-115], [0, "b6Ui8OtTRMKoRydvNYXAtZ", 1], [4, 4279026733], [5, 40.7, 27.5], [-54.74999999999999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "[TQ]", 22, 50, false, false, 1, 1, 56], [18, "lbNickName", 14, [-116], [0, "5fD2eB23RD7Lw307e1zY6a", 1], [5, 104.5, 27.5]], [26, "<PERSON><PERSON><PERSON>", 22, 50, false, false, 1, 1, 58, [37]], [6, "lbRoom", 3, [-117], [0, "b0as4ml8pOH7YtRCPfvfMA", 1], [4, 4278315513], [5, 200, 38], [104, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHÒNG", 22, 50, false, false, 1, 1, 1, 60, [38]], [6, "lbWin", 3, [-118], [0, "2bUhqZaflOhaqu96tdtjAG", 1], [4, 4278315513], [5, 200, 38], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THẮNG", 22, 50, false, false, 1, 1, 1, 62, [39]], [4, "lbDesc", 3, [-119], [0, "e9PPMmSDtENrs1JSbEM5hj", 1], [5, 200, 38], [393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "LOẠI", 22, 50, false, false, 1, 1, 1, 64, [40]], [27, false, 0.75, 0.23, null, null, 7, 19], [46, 20, 10, 400, 7, 3, 66], [47, 13, 67], [1, "lbNo", 5, [[43, "STT", 22, 50, false, false, 1, 1, -120, [42], 43]], [0, "44lhLQunpNeYI6mpl+nMlZ", 1], [5, 53.63, 34.38], [-440, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 5, [[5, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, -121, [44], 45]], [0, "40ILCNj4ZJSqZIEzr5AkO6", 1], [5, 200, 38], [-308, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 5, [[5, "TÀI KHOẢN", 22, 50, false, false, 1, 1, 1, -122, [46], 47]], [0, "c7f97XSMVMJYzZHTPmAaFp", 1], [5, 200, 38], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRoom", 5, [[5, "PHÒNG", 22, 50, false, false, 1, 1, 1, -123, [48], 49]], [0, "e2Eav7fRhF75tpGQpqk/NM", 1], [5, 200, 38], [104, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 5, [[5, "THẮNG", 22, 50, false, false, 1, 1, 1, -124, [50], 51]], [0, "51YHwRsNZOnLdD1Dj9lc20", 1], [5, 200, 38], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 5, [[5, "LOẠI", 22, 50, false, false, 1, 1, 1, -125, [52], 53]], [0, "e1OiMD7+NIbYufDVxFsWiN", 1], [5, 200, 38], [393, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 16, 1, 0, 0, 1, 0, 17, 50, 0, 18, 68, 0, 19, 13, 0, 20, 11, 0, 21, 23, 0, 22, 22, 0, 0, 1, 0, -1, 15, 0, -2, 20, 0, -3, 21, 0, -4, 8, 0, -5, 9, 0, -6, 24, 0, -7, 10, 0, -8, 4, 0, -9, 32, 0, -10, 11, 0, -11, 13, 0, -12, 5, 0, 5, 47, 0, 6, 45, 0, 7, 43, 0, 8, 41, 0, 9, 39, 0, 10, 37, 0, 11, 35, 0, 0, 2, 0, -1, 34, 0, -2, 36, 0, -3, 12, 0, -4, 42, 0, -5, 44, 0, -6, 46, 0, 5, 65, 0, 6, 63, 0, 7, 61, 0, 8, 59, 0, 9, 57, 0, 10, 55, 0, 11, 53, 0, 0, 3, 0, -1, 52, 0, -2, 54, 0, -3, 14, 0, -4, 60, 0, -5, 62, 0, -6, 64, 0, -1, 26, 0, -2, 27, 0, -3, 28, 0, -4, 29, 0, -5, 30, 0, -6, 31, 0, -1, 69, 0, -2, 70, 0, -3, 71, 0, -4, 72, 0, -5, 73, 0, -6, 74, 0, -1, 48, 0, -2, 49, 0, 0, 6, 0, -1, 33, 0, -2, 16, 0, -1, 66, 0, -2, 67, 0, 0, 7, 0, -1, 51, 0, -2, 18, 0, 0, 8, 0, -2, 22, 0, 0, 9, 0, -2, 23, 0, 23, 10, 0, 0, 10, 0, -1, 25, 0, -1, 50, 0, 0, 12, 0, -1, 38, 0, -2, 40, 0, -1, 68, 0, 0, 14, 0, -1, 56, 0, -2, 58, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 17, 0, 0, 17, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, -1, 35, 0, -1, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, -1, 45, 0, -1, 47, 0, -1, 53, 0, -1, 55, 0, -1, 57, 0, -1, 59, 0, -1, 61, 0, -1, 63, 0, -1, 65, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 24, 1, 2, 3, 33, 3, 3, 51, 6, 3, 11, 7, 3, 13, 125], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 22, 22, 22, 23, 23, 23, 23, 35, 37, 39, 41, 43, 45, 47, 53, 55, 57, 59, 61, 63, 65], [-1, 2, -1, 2, -1, 1, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 25, -1, -2, 12, 13, 14, 15, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 8, 0, 9, 0, 10, 0, 5, 0, 2, 0, 11, 0, 12, 0, 13, 0, 4, 0, 4, 0, 4, 0, 4, 0, 4, 0, 14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 3, 0, 3, 0, 3, 0, 3, 0, 3, 6, 6, 15, 2, 2, 2, 5, 2, 2, 2, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[{"name": "button- off", "rect": [0, 0, 186, 70], "offset": [0, 0], "originalSize": [186, 70], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [4], [16]]]]