[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "b6jjsC3HRNhK6jDrPDBfa2", "825TQ2kU9Ktq1Ncj5HdPmn", "adw94Z+hpN57wutNivq8Q5", "87gZ+pP7ZCNrJhB1FipFC7", "3egc9IKZhG6LhPK2W8CZj3", "e2xSxof3xCnIoaUKvS6jbh", "757YGyn6RC2LjkFXzkASKg", "e4tjnxfTtM1Z4yohK/+6NZ", "7aKH5jpdFBxKPiJfJ9MWBn", "ecZ2WxCZNK0L/qkvMpMRVd", "e9HqhmPmRMdZ1TnhgQKSAb", "a9VpD0DP5LJYQPXITZq+uj", "70CYgIRtVFj6E3C6R+VnK1", "dfmUGyly9Bs6tTDvjZqwNk", "ecegRD6jxDEKiI249qv0M2", "dfQo5p6SVOhImfY9X1/rY7", "017Jn3Zv1Ft7hygdjpaSoK", "7bjb1epaNLnr6qULKyTxxO", "9dcTUtg3RIRKWUvrF4VBuV", "3c+4Qx6plAqIPXf5sBzJ5G", "bbUg/Jik5JQLp+cIip3ZmP", "c4419ahQhGV4fpcZ9ELSJb", "83AJ3DaSFJOpoWBGEP9Y2f", "21lti13DVCOJ0nZSV56ljt", "a2kG02nsREnaKx3xnOb0jj", "f0l9aRJJ5CGY4MBF3H9CRu", "ca5vUy+i5A54COpjqa57x+", "99p94ZGIlGMI0qqphFIyH5", "9c/O7uFYRPLqLjmZitse2Y", "c61uwKX41E1bFbLMtbyQiw", "bd97zfsBZOr6xPn4hyYbZz", "2cX7YV18BCW7yYMRl3uMnb", "a7w0YFNnxFrrn0KNG+vQvA", "8dyFsuKYNHt7/B105P9g/S", "66RAsCVGxO46mM+uDj5QQL", "176DjUlClApYwobHuEV05M", "64z1oCc6pM97mpAiDsOeIV", "4541quv6BGM6kJdt0egNDK", "fejefp3nBBkqMgDkJLN18X", "9fSO0FYWtPf7qmi2z6TvCF", "6aH1xCQhBNEJ/JD4Cs+D3w", "27hqkVyjZCTbSHGNw8t10L", "b3JEgnCOZF4ZSlfuLA59Lg", "18nITWkOhPc7ERmRk3uwuR", "8d1nE4aENAZIG34zn+nOxR", "e2oEfz911PMr5ISUMZZg1K", "dcaHXCpLBCPp4mjI5c8oXN", "f9GOybE6VOLZrjwYfI7TUf", "8frLr5dz5IAbRdhUwcap/X", "504phcww5BTYalOcPni4WZ", "a011QZLzpGX4KfK4+YTx6U", "d4P+G6eVpH4aKcgbQBzUbj", "59Iahb+NFCerBkD0RV9+DD", "e1izUZFnlDl5Fgd4dBvU9H", "08Hhioq+xK8ooDBEaIhm1k", "41qBIlp8lNebyACX+Yh6Z/", "27J26jBkdJa7fnjXMQxs6J", "610sSrjNJOpo7n76fB60gQ", "d8FaiZWelLqaYrgJETLRhP", "bbRR039O9A45236DA0InFo", "49IlHNPtRDhKRgpzLd9azg", "4bZdEVT2xNbYT/64dNdTTq", "8ewLJc+a9J+r1ujWQCI1fo", "0eumunPbtG05d6TnT7bn5r", "e16vTjPjVCnL8omltjzPDM", "01uuU7RZFPPrgELYGhhzQ5", "f21D96SslKapMcFXJP7nTx", "64wfEWEmhGp4FdIbVzTfP+", "acVVmw/8lOs5Yk2cgaKDgR", "8d4V0jgZVHI5Y3+3sK0FDT", "f4fIK8QEFH7Ji5L8FDnXQm", "22efMmIAxLM4jBfOLsvIcp", "bfe4MO/vhJU5TrrFlwHsuL", "52EYYOhiRIDpJ1gvfi7GoD", "a37UG4EzdL06O8aHFlF4Mk", "fcAoIqyBxBRZxMhcnO35uc", "1cXio6TFxCRpkNZ6eqdktg", "32o/MaKqpCS4LOTZ1U8vS2", "1axyfRV/xAopm3SZRIBibp", "48f5RLD8JFnKA2dBy53RNG", "3ePZ1j0iJBObSD46vwWe9C", "188bExQoNBRZQ1FXKx+OLc", "93DOV+oY9CE7HTgvG2A5J+", "b0Uf+QdldNr6XY6nCzm5v4", "b11df6iUJBx5QqPUTpuIpX", "64VTl2BkVCZ44vnMVrELGq", "6bL94pkbNPlJcfDUlVnd6l", "20sxt9dQdEtq8FDCwW1WAB", "9bO3Q/u7NNibx+pmaV6RPQ", "42RTIQK8RNiIwNEMrwP9Ru", "22/8v75a1OFq1g+vk0srst", "61EjIZbuhCKYjy9Tksf+TI", "a33UZXc/5Pzo5TFjSuAxtE", "dbojFkgE5AGoyZMROvXMpH", "85hJw4dk1Ac7GUs67ck6kF", "eanixtpYpHsbw0G+NhHUga", "87Tnup/M5NkKovGA0jxsuz", "4f5fdUbENOsorjX3TFWSpX", "79zNW+xK1LKKfgSIOAcSZx", "89qq1cry5KTKQRyQR2YK2l", "b7M7we0k1EM7yr3MUJKStW", "0aP6phHCZBGLw/y4PT9bPx", "e3Vc/+ZudHVqIkunFmyQET", "2eHY9N7KtCM6XHzwZ3vpka", "6dLVFYdbtBspSvKdD9uRUQ", "38FMQS2aFOdq7itS0dIEmT", "23quAc+lZNu7js5xT3Drq9", "790nrqNj9CdLMcuMZU1UzW", "86WgjSKzZATI4q3zdPlz6Y", "a7ZIP+aGpJdJPFaAE8axIJ", "f4S4i8O1RNW7sL0mgtYQp9", "469njOFVZEarByQQkZSjfG", "fdbNT1a21IaY0uGeoLN1bw", "63AbzFwYtN2IaXML1hqkwA", "6f7R5QYqxHxqwmq+uaD1hB", "74qDVU1MdDRr8IDwIN7sxH", "a8M/e9EopHQrVSu8SuL7co", "03eVLA221AnbEYePrOjXwU", "4aUOiu0nVJ2ppFaNZ0XH+0", "1cZ5+8ovlJHLdmKVt32Ben", "36qRcMyGpIcryUV5Hhs1r0", "05ZgOShT5MhqKa5M7Y45oL", "84tsFICBtL6LLG9BG1kVas", "cbIKMBIF1GdoQuZGgDdLpe", "88hwZNl45OiITW12At4Hvu", "78Io9tR4FLe7xAShr2Kv57", "92UYFgEqtBG5yc70HT1LZr", "c9N4RvQb9Bi5l2fEJllLjG", "31FwEm7V9A9pkxVbThmxEd", "d5+9Um18FMt5euiAFYlVRM", "fe4UNhUuJPjrlI3KUWdn+c", "0cDsLmn4ZEWZde9H92bmSp", "39soo1WRBJT6QULc/JKIp2", "914kkYO+NFrq9HbI+ZAkTC", "36jkcZrFVLNbgtSBbCc5W7", "37g8H0lttEN5jxRHsrtlbz", "c37p0aw8JDnLdmYrCKBawM", "e9UQA36f1NfpVmp2tuMXTF", "9ap5kuYNZF85egM/iyP7U/", "8cXKYnSzlFI4wXvgoWer3m", "40aumnnANCrLOCDhD6bxeW", "b1F0FmTGxN0qnttCB1TaVI", "b51RfkXU1Hg77bbr8wQqv/", "7ePCYj/h5FYYksH7lL9YmF", "75fiqFRWZDcKsiYHetAsuc", "5eI07WBDFO9LoU4x6XjbE2", "ecs23Nay5F+Y5MmntkmZCN", "05SFIDhq1NUbyEQd3ZMTGm", "a7hXtifxZM+KdEMVbNg36+", "a8c+WRIxFH+61O2WdSsQNc", "82HjpVcEdN9JguhAGSmTOd", "f4c9R/FlZCwYYNxgtw/een", "16XGi+qnREGKblLHi+JLBZ", "9aSpDY4E5OSYX7h+dbtC3O", "36RLh18ldL9KkADLk6QODR", "91hAEMp4tCYa71w0GR/Ylj", "5aRpseSWFBxJdUDGcEAmQU", "b7XUv/mMJCArwUCtkdGLaI", "1bVciGJQBBW6OwpGOVk0m0", "ebLou6TXNKkY/zx5poV/q6", "35GDHAQ3VFULFMVCyo9+Wb", "c3hU47ZeBAKL8D1zORWAze", "6bQ9yGKo9AP5uEGWsOXKNa", "c13ttWs4xP9KLgGTzIEvmi", "f8dDcFnq5CULZ39JU3J/Ta"], ["node", "_spriteFrame", "_textureSetter", "_parent", "_N$file", "_N$skeletonData", "_N$target", "_N$normalSprite", "_clip", "_defaultClip", "_file", "spriteMusic", "spriteSound", "nodeOffset", "animation", "root", "nodeMain", "nodeRoom", "spriteFastSpin", "spriteAutoSpin", "spriteSpin", "btnSpin", "btnSelectBetLines", "btnBack", "lbFreeSpinText", "stopSpin5", "stopSpin4", "stopSpin3", "stopSpin2", "stopSpin1", "spin", "getBonus", "bigWin", "normalWin", "musicBackground", "nodeSpriteTry", "lbBetVal", "spriteBGRoom", "lbTotalLines", "lbSessionID", "lbiTotalWin", "lbiTotalBet", "particleWin", "particleBigWin", "particleJackpot", "nodeNormalWin", "nodeEffect", "btnHandle", "lbiJackpot", "spriteXHu", "lbWin", "lbMessage", "data", "_normalMaterial", "prefabHelp", "prefabBetLines", "prefabLeaderboard", "prefabHistory", "prefabSessionDetail", "prefabBonusGame", "sfPopupNormal", "sfPopupWin", "spriteFrame"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_opacity", "_prefab", "_components", "_children", "_parent", "_contentSize", "_trs", "_anchorPoint", "_color"], -1, 4, 9, 2, 1, 5, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_spriteFrame", "_materials"], -1, 1, 6, 3], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "_spacingX", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_N$interactable", "node", "clickEvents", "_N$target", "_N$pressedColor", "_N$disabledColor", "_N$normalColor", "_N$normalSprite", "_normalMaterial"], 0, 1, 9, 1, 5, 5, 5, 6, 6], ["cc.Node", ["_name", "_parent", "_prefab", "_components", "_contentSize", "_trs", "_anchorPoint", "_children", "_color"], 2, 1, 4, 2, 5, 7, 5, 9, 5], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "defaultSkin", "_animationName", "defaultAnimation", "_materials", "node", "_N$skeletonData"], -2, 3, 1, 6], ["cc.Node", ["_name", "_active", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_anchorPoint"], 1, 4, 1, 12, 5, 7, 12, 5], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingY", "_enabled", "_N$spacingX", "_N$paddingTop", "_N$paddingBottom", "node", "_layoutSize"], -4, 1, 5], ["cc.Node", ["_name", "_active", "_children", "_components", "_prefab", "_parent", "_trs", "_contentSize", "_anchorPoint"], 1, 2, 12, 4, 1, 7, 5, 5], ["cc.ParticleSystem", ["emissionRate", "life", "lifeVar", "angleVar", "startSize", "endSize", "startSpinVar", "endSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccelVar", "totalParticles", "_custom", "duration", "angle", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -14, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_trs", "_contentSize"], 2, 1, 2, 2, 4, 7, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.AudioSource", ["preload", "_loop", "node"], 1, 1], ["cc.Prefab", ["_name"], 2], ["111e0bhXjhKtJFVWcbT2N4L", ["node", "skeletonDataIcons", "bgRooms"], 3, 1, 3, 3], ["48121xYynhO56qpPvfYDts6", ["node", "nodeRoom", "nodeMain", "btnRooms"], 3, 1, 1, 1, 2], ["f31aeW4+QVND6JlhsMmqW4e", ["webScale", "node", "nodeScales"], 2, 1, 2], ["e9c64UPEudOYLSivEv7NBoj", ["node", "nodeLines", "iconSkeletons"], 3, 1, 2, 2], ["27803B0B7ZN+I0Q5PBtYj6/", ["node", "prefabHelp", "prefabBetLines", "prefabLeaderboard", "prefabHistory", "prefabSessionDetail", "prefabBonusGame"], 3, 1, 6, 6, 6, 6, 6, 6], ["ca012RPE/tBobOu1Tyx0LzF", ["node", "btnBack", "btnSelectBetLines", "btnSpin", "spriteSpin", "spriteAutoSpin", "spriteFastSpin", "sfSpins", "sfAutoSpins", "sfFastSpins", "sfSelectBetLines"], 3, 1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3], ["09681ScQ6hHdbVMbuqQVWId", ["node", "lbFreeSpinText"], 3, 1, 1], ["84604plrDxMdI2u54Ms+u+X", ["node", "musicBackground", "normalWin", "bigWin", "getBonus", "spin", "stopSpin1", "stopSpin2", "stopSpin3", "stopSpin4", "stopSpin5"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["dcd4akrIzFOe4glwzM/CGWZ", ["node", "animation", "nodeOffset", "spriteSound", "spriteMusic", "sfSounds", "sfMusics"], 3, 1, 1, 1, 1, 1, 3, 3], ["0e926h5bvJCrbphbkTZxDkS", ["node", "lbiJackpots"], 3, 1, 2], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["6245c28vjxNA7+sCzsGilk9", ["node", "lbiTotalBet", "lbiTotalWin", "lbSessionID", "lbTotalLines", "spinColumnViews", "spriteBGRoom", "lbBetVal", "nodeSpriteTry"], 3, 1, 1, 1, 1, 1, 2, 1, 1, 1], ["eec18Pz6uxPv5oH0NNMqLNk", ["node", "nodeEffect", "nodeJackpot", "nodeBigWin", "nodeNormalWin", "particleJackpot", "particleBigWin", "particleWin", "lbiTotalWins"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 2], ["8457bzXi+RCFrWEyccPy/PF", ["touchParent", "node", "btnHandle"], 2, 1, 1], ["47b4eqoA/hFw4ocENL+7owM", ["node"], 3, 1], ["d857dgwT55H868B0MQw1E2w", ["node"], 3, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["9e952KkK7dIaIHwrq2+CAV7", ["node", "lbiJackpot"], 3, 1, 1], ["5c1fa7YnkBEMqpU9n30ksJn", ["gameId", "node", "nodeX", "spriteXHu", "lbRemainJackpots"], 2, 1, 1, 1, 2], ["832cdd6gThBUZ3q0q4hjRy5", ["messWinPosY", "node", "nodeMessage", "lbMessage", "lbWin", "sfPopupNormal", "sfPopupWin"], 2, 1, 1, 1, 1, 6, 6], ["1da17DUoWRNe7yAIWs71WSJ", ["node", "skeletonIcons"], 3, 1, 2], ["cc.AudioClip", ["_name", "_native", "duration"], 0], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.AnimationClip", ["_name", "_duration", "speed", "wrapMode", "events", "curveData"], -3], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[14, 0, 1, 2], [1, 0, 7, 5, 4, 8, 9, 2], [1, 0, 1, 7, 5, 4, 8, 9, 3], [2, 4, 6, 5, 1], [2, 0, 1, 4, 5, 3], [1, 0, 7, 4, 2], [5, 0, 1, 3, 2, 4, 2], [12, 0, 1, 3, 3], [1, 0, 7, 6, 5, 4, 8, 9, 2], [1, 0, 7, 6, 4, 8, 9, 2], [1, 0, 7, 5, 4, 8, 2], [33, 0, 1], [5, 0, 1, 3, 2, 2], [5, 0, 1, 3, 2, 4, 5, 2], [15, 0, 2, 2], [7, 0, 3, 4, 2, 5, 6, 2], [13, 0, 1, 2, 3, 4, 6, 5, 2], [2, 0, 1, 4, 6, 5, 3], [6, 0, 1, 5, 3], [1, 0, 7, 6, 4, 8, 2], [9, 0, 5, 2, 3, 4, 7, 6, 2], [11, 1, 2, 3, 1], [2, 0, 1, 4, 6, 3], [3, 0, 3, 5, 1, 4, 2, 9, 10, 11, 7], [4, 0, 3, 4, 6, 7, 5, 9, 2], [4, 1, 2, 0, 3, 4, 6, 7, 5, 4], [12, 0, 1, 2, 3, 4], [38, 0, 1, 1], [39, 0, 1, 2, 4], [1, 0, 7, 6, 5, 4, 11, 8, 10, 9, 2], [5, 0, 1, 3, 2, 4, 6, 5, 2], [11, 0, 1, 2, 3, 2], [3, 0, 3, 5, 1, 4, 2, 9, 11, 7], [3, 0, 3, 1, 4, 2, 9, 10, 11, 6], [3, 0, 3, 5, 1, 4, 2, 9, 7], [4, 1, 3, 4, 8, 2], [6, 2, 0, 1, 5, 4], [6, 2, 4, 0, 1, 3, 6, 5, 7, 6], [6, 2, 4, 0, 3, 6, 5, 7, 5], [41, 0, 1, 2, 3, 4, 5, 7], [1, 0, 7, 6, 4, 2], [7, 0, 3, 4, 2, 5, 8, 6, 2], [8, 3, 1, 0, 4, 7, 8, 5], [11, 1, 2, 1], [2, 0, 4, 5, 2], [2, 0, 4, 6, 5, 2], [6, 2, 4, 0, 1, 3, 5, 6], [6, 0, 1, 6, 5, 3], [1, 0, 7, 6, 5, 4, 2], [1, 0, 6, 5, 4, 8, 9, 2], [1, 0, 1, 6, 5, 4, 9, 3], [1, 0, 7, 6, 4, 9, 2], [1, 0, 1, 7, 6, 5, 4, 8, 9, 3], [1, 0, 3, 7, 5, 4, 8, 9, 3], [7, 0, 1, 3, 4, 2, 5, 6, 3], [5, 0, 1, 7, 2, 2], [5, 0, 1, 3, 2, 5, 2], [8, 1, 0, 2, 7, 8, 4], [25, 0, 1, 2, 3, 4, 5, 6, 1], [2, 4, 6, 1], [3, 0, 3, 5, 6, 1, 4, 2, 7, 9, 10, 11, 9], [3, 0, 3, 5, 6, 1, 4, 2, 9, 10, 11, 8], [3, 0, 3, 5, 6, 1, 4, 2, 7, 9, 10, 9], [4, 3, 8, 1], [4, 3, 4, 8, 5, 1], [4, 1, 0, 3, 4, 6, 7, 5, 9, 3], [4, 3, 4, 8, 1], [4, 1, 0, 3, 4, 6, 7, 5, 3], [34, 0, 1], [6, 2, 0, 1, 6, 5, 4], [16, 0, 2], [1, 0, 6, 5, 4, 2], [1, 0, 6, 5, 4, 9, 2], [1, 0, 2, 6, 5, 4, 8, 9, 3], [1, 0, 2, 7, 6, 5, 4, 3], [1, 0, 7, 6, 4, 8, 10, 9, 2], [1, 0, 2, 1, 7, 6, 4, 4], [1, 0, 1, 6, 5, 4, 3], [1, 0, 6, 5, 4, 8, 10, 9, 2], [1, 0, 1, 7, 6, 5, 4, 8, 10, 9, 3], [1, 0, 1, 6, 5, 4, 8, 3], [1, 0, 2, 7, 6, 5, 4, 8, 10, 9, 3], [1, 0, 2, 7, 6, 5, 4, 8, 3], [1, 0, 3, 7, 5, 4, 8, 3], [1, 0, 7, 6, 5, 4, 8, 2], [1, 0, 1, 3, 7, 5, 4, 8, 4], [1, 0, 7, 5, 4, 9, 2], [7, 0, 7, 2, 2], [7, 0, 3, 4, 2, 5, 2], [9, 0, 5, 2, 3, 4, 2], [9, 0, 5, 2, 3, 4, 6, 2], [9, 0, 1, 2, 3, 4, 7, 8, 3], [5, 0, 1, 3, 2, 4, 6, 2], [5, 0, 1, 3, 2, 8, 4, 6, 5, 2], [13, 0, 1, 2, 3, 4, 5, 2], [17, 0, 1, 2, 1], [18, 0, 1, 2, 3, 1], [19, 0, 1, 2, 2], [14, 1, 1], [20, 0, 1, 2, 1], [8, 0, 7, 2], [8, 1, 0, 5, 6, 2, 7, 8, 6], [8, 3, 1, 0, 4, 2, 7, 8, 6], [21, 0, 1, 2, 3, 4, 5, 6, 1], [22, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [23, 0, 1, 1], [24, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [2, 2, 0, 1, 4, 6, 5, 4], [2, 3, 4, 6, 5, 2], [2, 2, 0, 1, 4, 5, 4], [2, 1, 4, 6, 5, 2], [2, 3, 0, 1, 4, 6, 5, 4], [2, 2, 0, 1, 4, 6, 4], [2, 0, 1, 4, 3], [26, 0, 1, 1], [27, 0, 1, 2, 2], [28, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [29, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [30, 0, 1, 2, 2], [3, 0, 3, 5, 6, 1, 8, 4, 2, 7, 9, 10, 11, 10], [3, 0, 3, 6, 1, 4, 2, 7, 9, 10, 11, 8], [3, 0, 3, 5, 1, 8, 4, 2, 9, 10, 8], [3, 0, 3, 5, 6, 1, 2, 7, 9, 10, 8], [3, 0, 3, 6, 1, 4, 2, 7, 9, 8], [3, 0, 5, 6, 1, 4, 2, 7, 9, 10, 8], [31, 0, 1], [32, 0, 1], [4, 1, 2, 0, 3, 6, 7, 5, 9, 4], [4, 2, 3, 4, 5, 10, 2], [4, 0, 3, 4, 6, 7, 5, 2], [12, 0, 1, 3], [35, 0, 1, 1], [36, 0, 1, 2, 3, 4, 2], [37, 0, 1, 2, 3, 4, 5, 6, 2], [6, 2, 0, 1, 3, 5, 5], [15, 1, 0, 2, 3], [10, 13, 0, 1, 2, 16, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22, 23, 24, 16], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22, 23, 24, 14], [10, 14, 13, 15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 17], [10, 14, 13, 15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 17, 18, 19, 20, 21, 22, 23, 24, 17], [40, 0, 1, 2, 3, 4, 5], [42, 0, 1, 2, 4]], [[[{"name": "head", "rect": [0, 0, 796, 69], "offset": [0, 0], "originalSize": [796, 69], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [51]], [[[70, "aquariumView"], [71, "aquariumView", [-17, -18, -19, -20, -21], [[95, -2, [289, 290, 291, 292, 293, 294, 295], [296, 297, 298]], [96, -10, -9, -8, [-3, -4, -5, -6, -7]], [97, 1, -16, [-11, -12, -13, -14, -15]]], [98, -1]], [72, "payLinesView", [-68, -69, -70, -71, -72, -73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92, -93, -94, -95, -96, -97], [[99, -67, [-37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61, -62, -63, -64, -65, -66], [-22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36]]], [0, "d2RUDt8ppLy7TZzft50WxD", 1], [0, 95, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "BGspin", 512, [-99, -100, -101, -102, -103, -104, -105, -106, -107, -108, -109, -110, -111, -112, -113, -114, -115, -116, -117, -118, -119, -120, -121, -122], [[100, 1, -98]], [0, "9cA9+fqitH0rhApMcw3Y8P", 1], [5, 39, 488], [-447.82, -23.025, 0, 0, 0, 0, 1, 1, 1, 1]], [87, "ATTACHED_NODE:root", [[[5, "ATTACHED_NODE:bone", -123, [0, "15mSHjlbVKcowxm+a88u2h", 1]], [5, "ATTACHED_NODE:bone2", -124, [0, "cbTpNnFhREkp90bQTij9lA", 1]], [5, "ATTACHED_NODE:bone3", -125, [0, "39cdq/n9VLDKVp9iW+yUMc", 1]], [5, "ATTACHED_NODE:bone4", -126, [0, "85SRs/QLZN46wCdXsLO3eb", 1]], [5, "ATTACHED_NODE:bone5", -127, [0, "3fEPnAS55M5oFfahCe6Zn1", 1]], [5, "ATTACHED_NODE:bone6", -128, [0, "88H8TzZnJI25DTo6333fnA", 1]], -129, [5, "ATTACHED_NODE:bone14", -130, [0, "105B7VfrdAi5/yQyASVcZX", 1]], [5, "ATTACHED_NODE:bone15", -131, [0, "dcbvK/ZONKyLChERe4lAvL", 1]], [5, "ATTACHED_NODE:bone16", -132, [0, "28btqJcYhMeII/Sa99Xc6I", 1]], [5, "ATTACHED_NODE:bone17", -133, [0, "09q/tO9KBKEaI+iu5JHEmM", 1]], [5, "ATTACHED_NODE:bone18", -134, [0, "26dhqVjOtAM4sWnCVDlmYv", 1]], [5, "ATTACHED_NODE:bone19", -135, [0, "231ACCYThPDYJdHGQtEYrC", 1]], [5, "ATTACHED_NODE:bone20", -136, [0, "d3hpAjP1pCVKxcnoka8bzi", 1]], [5, "ATTACHED_NODE:bone21", -137, [0, "df2fdl7n9EXouyeCeu3lBc", 1]], [5, "ATTACHED_NODE:bone22", -138, [0, "ec6ec8ie5DZKElbHsnkn2y", 1]], -139], 4, 4, 4, 4, 4, 4, 1, 4, 4, 4, 4, 4, 4, 4, 4, 4, 1], [0, "0ceJZ7L/1FloF4N+NDIqlY", 1]], [48, "slotsView", 1, [-150, 3, -151, -152, -153, -154, -155], [[103, -140, 261, 262, 263, 264, 265, 266], [104, -147, -146, -145, -144, -143, -142, -141, [267, 268], [269, 270], [271, 272], [273, 274]], [105, -149, -148]], [0, "e5eyoHVqpJmZYnNMZ8XGSn", 1]], [74, "audioPool", 512, 1, [-167, -168, -169, -170, -171, -172, -173, -174, -175, -176], [[106, -166, -165, -164, -163, -162, -161, -160, -159, -158, -157, -156]], [0, "ddMFfZT5VE86r5USPTKz+N", 1]], [75, "backgroundSlot", 5, [-177, -178, -179, -180, -181, -182, -183, -184, 2], [0, "c2twkooq5J5a+spJDpOGef", 1], [5, 954, 512], [0, 0.5, 0.3], [74.547, -18.571, 0, 0, 0, 0, 1, 1, 1, 1]], [76, "offset-room", 512, false, 1, [-185, -186, -187, -188, -189, -190, -191], [0, "43NKOQu+BC+rta3ciID1J8", 1]], [89, "<PERSON><PERSON><PERSON><PERSON>", 5, [-198], [[-192, [58, -197, -196, -195, -194, -193, [229, 230], [231, 232]]], 1, 4], [0, "67+yRuwzNCsYZQHi4i1MFP", 1]], [90, "<PERSON><PERSON><PERSON><PERSON>", 8, [-205], [[-199, [58, -204, -203, -202, -201, -200, [19, 20], [21, 22]]], 1, 4], [0, "e9LaaX5yFF7Y8tJqrIZqVi", 1], [514, 237, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "slots", [-207, -208, -209, -210, -211], [[42, false, 1, 1, 28, -206, [5, 812, 443]]], [0, "37xgRGJy9GVajzOVAroDPX", 1], [5, 900, 500], [0, -3, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "1", 11, [-214, -215, -216, -217], [[[21, -212, [115, 116, 117, 118], 114], -213], 4, 1], [0, "99KiLUQsBEE4+wWZmsSeta", 1], [5, 140, 443], [-369, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "2", 11, [-220, -221, -222, -223], [[[21, -218, [124, 125, 126, 127], 123], -219], 4, 1], [0, "6dxtfXUpVK1Z0534TZNaQd", 1], [5, 140, 443], [-185, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "3", 11, [-226, -227, -228, -229], [[[21, -224, [133, 134, 135, 136], 132], -225], 4, 1], [0, "3bTd4tkPFFh4h1vX3KJ4zb", 1], [5, 140, 443], [0, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "4", 11, [-232, -233, -234, -235], [[[21, -230, [142, 143, 144, 145], 141], -231], 4, 1], [0, "07XK9dppJFWbsI21MIvD3S", 1], [5, 140, 443], [189, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "5", 11, [-238, -239, -240, -241], [[[21, -236, [151, 152, 153, 154], 150], -237], 4, 1], [0, "4bLaVCb9dDlIFf1eXWoEny", 1], [5, 140, 443], [369, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "offset-jackpot", false, [-243, -244, -245, -246], [[31, true, -242, [242], 241]], [0, "25bd+ULutASrIavyugrwm9", 1], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "ATTACHED_NODE:bone7", 4, [[5, "ATTACHED_NODE:bone8", -247, [0, "10H3D7lapCI7ltl3fGc1kM", 1]], [5, "ATTACHED_NODE:bone9", -248, [0, "b9eir+jkVLs60kP2vjgekT", 1]], [5, "ATTACHED_NODE:bone10", -249, [0, "77F8io/+BAVYBUAi69H/tS", 1]], [5, "ATTACHED_NODE:bone11", -250, [0, "d4kRjIQdxDzLD6WyKV49km", 1]], [5, "ATTACHED_NODE:bone12", -251, [0, "817FrNe8NIMotfLDG5zY3f", 1]], [5, "ATTACHED_NODE:bone13", -252, [0, "12wBjPGBdGdbSf+AAhLY6M", 1]]], [0, "8dAF8g5RpDa77y7ABdWkLu", 1]], [77, "offset-bigWin", false, [-254, -255, -256, -257], [[31, true, -253, [250], 249]], [0, "baEhq3ndhPu5UR+6Lw2toR", 1]], [78, "layout", [-260, -261, -262, -263], [[44, 0, -258, 281], [101, 1, 2, 25, 25, 15, -259, [5, 190, 195]]], [0, "36g6ks0nhMb5BgvDoJOWdm", 1], [5, 190, 195], [0, 0.5, 1], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "jackpotView", 8, [-269, -270, -271, -272], [[114, -268, [-264, -265, -266, -267]]], [0, "2aTDy0s9pMl5AUZGjPYnl7", 1], [5, 1272, 500], [24, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "spinView", 7, [11], [[115, 1, -273, [155]], [116, -286, -285, -284, -283, -282, [-277, -278, -279, -280, -281], -276, -275, -274]], [0, "91o+BREo5EiqS4DRZCzNDE", 1], [5, 900, 494], [0, 75, 0, 0, 0, 0, 1, 1, 0.9, 1]], [51, "bot-bar", 7, [-287, -288, -289, -290, -291], [0, "67wuJZbNJLbpdBXqAR/AbY", 1], [0, -218, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "effect<PERSON>iew", 5, [-301], [[117, -300, -299, 17, 19, -298, -297, -296, -295, [-292, -293, -294]]], [0, "37E7YrEoVDfazYcoGaPrt5", 1]], [50, "offset-normalWin", false, [-303, -304, -305], [[31, true, -302, [260], 259]], [0, "94OA5I8ehDTLq9Z4/yU7OV", 1], [4.613, -140.354, 0, 0, 0, 0, 1, 1, 1, 1]], [91, "offset-xHu", false, [-309, 20], [[[118, null, -307, -306], -308], 4, 1], [0, "ca9zbZnM5C+aO0aE0nnxoY", 1], [5, 190, 190], [0, 0.5, 0.9]], [79, "bg_set_top", false, 10, [-311, -312], [[17, 0, false, -310, [15], 16]], [0, "34wsLILnZB2IRDjQU0yHVJ", 1], [5, 229, 120], [0, 0.5, 1], [-16.7, 0, 0, 0, 0, 0, 1, 0, 0, 1]], [16, "btnRoom1", 21, [-314, -315], [-313], [0, "2f1k7IWfBHMZyVoDFLDw7Z", 1], [5, 300, 400], [1.628, -290.832, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnRoom2", 21, [-317, -318], [-316], [0, "8dzc4ggihMoL2CfcbIGe6N", 1], [5, 300, 500], [-482.57, -68.706, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnRoom3", 21, [-320, -321], [-319], [0, "468xUwsChJ6bCtrcGDPZ4H", 1], [5, 300, 500], [506.455, -60.223, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnRoom4", 21, [-323, -324], [-322], [0, "8dTpHK0ZFCl5mggBaFb/xL", 1], [5, 300, 400], [7.466, 142.818, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "layout-btnLeft", 23, [-326, -327, -328], [[42, false, 1, 1, 40, -325, [5, 314, 80]]], [0, "0diFl/bKlDKr7lh9i7ZwNI", 1], [5, 314, 80], [-308.4, -0.5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "layout-btnRight", 23, [-330, -331, -332], [[42, false, 1, 1, 5, -329, [5, 450, 80]]], [0, "6fzAv2GilDa7qPRl3/13La", 1], [5, 450, 80], [492.397, 24.508, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBalance", 7, [[119, "0", 29, 34, false, false, 0.5, 1, 1, 2, -333, [173], 174], [125, -334], [126, -335], [11, -336]], [0, "6baP+fQclLoLydIlVYPEBu", 1], [5, 170, 34], [-358.702, 345.196, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "layout", [-338, -339, -340], [[102, false, 1, 1, -7, 10, -337, [5, 226, 260]]], [0, "69J91acSNPuayOCpDUmi9A", 1], [5, 226, 50], [338.995, -5.689, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "bg_set_top", 9, [-342, -343], [[17, 0, false, -341, [225], 226]], [0, "f37nNnzKhAgpf1I3y2NrSr", 1], [5, 229, 120], [644.333, 181, 0, 0, 0, 0, 1, 0, 0, 1]], [40, "offset-Effect", 24, [17, 19, 25], [0, "86+huT6dNCfqnvrzgoTht9", 1]], [80, "offset-message", false, [-345, -346], [[107, 1, 0, false, -344, [284], 285]], [0, "28Ivy05opMIauo9EbhwK6d", 1], [5, 777, 275]], [10, "bacground", 8, [[17, 0, false, -347, [0], 1], [63, -348, [4, 4292269782]], [68, -349]], [0, "7dlZn5OgxKppCqExq1lMsr", 1], [5, 1560, 732]], [8, "btnBack", 8, [-352], [[24, 3, -351, [[7, "48121xYynhO56qpPvfYDts6", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -350, 4]], [0, "0a+Wf1LEZLHoFSijAsbFgM", 1], [5, 140, 140], [-671.107, 295.363, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "btnSetting", false, 8, [-355], [[24, 3, -354, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "openSettingClicked", 10]], [4, 4294967295], [4, 4294967295], -353, 23]], [0, "4efjvW3nZKUKgocKAAHzzC", 1], [5, 140, 140], [572, 282, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "black", 0, 27, [[45, 0, -356, [7], 8], [64, -358, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "closeSettingClicked", 10]], [4, 4292269782], -357]], [0, "eckeEcTMtBmqXdhZ+aEiEL", 1], [5, 4000, 4000], [-498, -263, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "layout", 27, [-360, -361], [[57, 1, 2, 15, -359, [5, 200, 85]]], [0, "04hIzzQW1BRrK+SacyL6Gs", 1], [5, 200, 85], [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnSound", 43, [-363, -364], [[35, 1.1, -362, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "soundClicked", 10]], [4, 4292269782]]], [0, "095NUGpP9J2YZLs3bjH7fj", 1], [5, 200, 35], [0, 25, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [8, "btn\bMusic", 43, [-366, -367], [[35, 1.1, -365, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "musicClicked", 10]], [4, 4292269782]]], [0, "8bfrPVyCRBUKOe9InB2jNF", 1], [5, 120, 35], [0, -25, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [16, "btnRoomF", 8, [-369], [-368], [0, "860DlwfxtGnb8ALCQYTSsW", 1], [5, 260, 200], [-405.627, 269.218, 0, 0, 0, 0, 1, 1, 1, 1]], [88, "background", 5, [[-370, [63, -371, [4, 4292269782]], [68, -372]], 1, 4, 4], [0, "c9d13dAaRMnaoegn0z5YF/", 1], [5, 1561, 732]], [40, "backgroudgameslot", 5, [-373, -374, -375], [0, "86NHIoLgJMI5IDjZQ9PFh+", 1]], [8, "head", 7, [-377, -378], [[108, false, -376, [108], 109]], [0, "fePZ1nbCBHRKKS/2gcX8nh", 1], [5, 796, 69], [3, 387, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 32, [[17, 2, false, -379, [156], 157], [65, 1.05, 3, -381, [[7, "27803B0B7ZN+I0Q5PBtYj6/", "helpClicked", 5]], [4, 4294967295], [4, 4294967295], -380, 158]], [0, "11irMgjDxFioDr6xVD3rBP", 1], [5, 67, 55], [876.951, 352.786, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnLine", 32, [-383], [-382], [0, "8df0PqluBFgbhhGe+cvvY5", 1], [5, 142, 85], [-466.516, -72.035, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnBet", 32, [-386], [[127, 1.1, false, 3, -385, [4, 4294967295], [4, 4294967295], -384, 161]], [0, "1d4ysRYi9E8by+iYU8fidT", 1], [5, 150, 73], [68.545, -2.037, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "freespin", false, 23, [-388, -389], [[109, 1, 0, false, -387, 163]], [0, "0altiwey1B8KY5Pg19Nfwb", 1], [5, 287, 36], [0, 104, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "btnFastSpin", false, 33, [[-390, [128, false, -392, [[130, "6245c28vjxNA7+sCzsGilk9", "fastSpinClicked"]], -391, 167]], 1, 4], [0, "8creVPiPpEo6c343KfcrxT", 1], [5, 212, 39], [-273.174, -123.66, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "btnAutoSpin", false, 33, [[[65, 1.05, 3, -394, [[7, "6245c28vjxNA7+sCzsGilk9", "fastSpinClicked", 22]], [4, 4294967295], [4, 4294967295], -393, 168], -395], 4, 1], [0, "1e2RMwIUBBy7Qr/jctHcoL", 1], [5, 212, 39], [-273.777, -65.19, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "btnSpin", 33, [-396, -397], [0, "45dsgTQVpBAIEcs1+pYZR6", 1], [5, 258, 108], [-162.596, -94.344, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbJackpot", 7, [[[60, "0", 26, 64, false, false, 1, 1, 1, -398, [175], 176], -399, [131, -401, -400]], 4, 1, 4], [0, "b0v3s0wDdJpIjJ8KEOvkfH", 1], [5, 300, 50], [0, 339.971, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "top-bar", 5, [-402, 35], [0, "06+s/O0whGUYiSW230kpjl", 1], [0, 291.0000000000002, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnBack", 58, [-404], [-403], [0, "37lIn3iqlJZYJQU8EbTrNN", 1], [5, 80, 80], [-699.155, 19.048, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnLeaderboard", 35, [-407], [[24, 3, -406, [[7, "27803B0B7ZN+I0Q5PBtYj6/", "leaderboardClicked", 5]], [4, 4294967295], [4, 4294967295], -405, 211]], [0, "5bR8vOjY9Nu7WIn9JEmX6f", 1], [5, 80, 80], [305.709, -260.75, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnHistory", 35, [-410], [[24, 3, -409, [[7, "27803B0B7ZN+I0Q5PBtYj6/", "historyClicked", 5]], [4, 4294967295], [4, 4294967295], -408, 214]], [0, "ab34XXEGFNJ4M/I4TmTkaD", 1], [5, 80, 80], [301.173, -84.939, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnSetting", 35, [-413], [[24, 3, -412, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "openSettingClicked", 9]], [4, 4294967295], [4, 4294967295], -411, 233]], [0, "b5vCALyGdMyqMeWebThw05", 1], [5, 80, 80], [297.039, 15.319, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "black", 0, 36, [[45, 0, -414, [217], 218], [64, -416, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "closeSettingClicked", 9]], [4, 4292269782], -415]], [0, "2eQn+4UtBNw5lLjBTAFlxs", 1], [5, 4000, 4000], [-498, -263, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "layout", 36, [-418, -419], [[57, 1, 2, 15, -417, [5, 200, 85]]], [0, "60dNyFwBFJP6+c7KXqJ0b3", 1], [5, 200, 85], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnSound", 64, [-421, -422], [[35, 1.1, -420, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "soundClicked", 9]], [4, 4292269782]]], [0, "a6us3RKItPTpNY264BD/to", 1], [5, 200, 35], [0, 25, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [8, "btn\bMusic", 64, [-424, -425], [[35, 1.1, -423, [[7, "dcd4akrIzFOe4glwzM/CGWZ", "musicClicked", 9]], [4, 4292269782]]], [0, "04/cYC9ZNHUJsSTUA29Wnv", 1], [5, 120, 35], [0, -25, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [81, "xHuView", 512, 1, [26], [[132, 1, -431, 26, -430, [-426, -427, -428, -429]]], [0, "24VUtjJN9KGZmrPfjoiq6b", 1], [5, 190, 190], [0, 0.5, 0.7], [-562.977, 11.184, 0, 0, 0, 0, 1, 1, 1, 1]], [82, "popupSlotsView", 512, 1, [38], [[43, -432, [286]], [133, 27, -435, 38, -434, -433, 287, 288]], [0, "e7KLW4oJRDJKeUvc80m/DS", 1], [5, 444, 220]], [15, "lbJackpot1", 28, [[[23, "500.000", 26, 60, false, 1, 1, -436, [30], 31], -437], 4, 1], [0, "a5qhgIRnlFsb9z9phONzHH", 1], [5, 103.19, 60], [11.486, -3.832, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbJackpot2", 29, [[[23, "5.000.000", 26, 60, false, 1, 1, -438, [34], 35], -439], 4, 1], [0, "9dexZBa79BmaYR69ll6aaC", 1], [5, 127.56, 60], [0, -201.177, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbJackpot3", 30, [[[23, "25.000.000", 32, 60, false, 1, 1, -440, [38], 39], -441], 4, 1], [0, "26/rWepQRMRLgzeDDlN0L1", 1], [5, 177, 60], [-7.829, -142.698, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbJackpot4", 31, [[[23, "50,000,000", 26, 60, false, 1, 1, -442, [42], 43], -443], 4, 1], [0, "f7JijTPTBLPqOsziM2TP2P", 1], [5, 147.06, 60], [-15.761, -187.929, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "text_choiThu", 49, [[3, -444, [105], 106]], [0, "3b+v74Eu5E/qzm8rShIHQ0", 1], [5, 112, 136], [-700.224, 97.303, 0, 0, 0, 0, 1, 1, 1, 1]], [134, "default", 0, false, "animation", [111]], [36, "default", 0, false, [112]], [36, "default", 0, false, [113]], [46, "default", "animation", 0, false, "animation", [120]], [46, "default", "animation", 0, false, "animation", [121]], [46, "default", "animation", 0, false, "animation", [122]], [18, 0, false, [129]], [36, "default", 0, false, [130]], [18, 0, false, [131]], [18, 0, false, [138]], [18, 0, false, [139]], [18, 0, false, [140]], [36, "default", 0, false, [147]], [18, 0, false, [148]], [18, 0, false, [149]], [41, "lbTotalBetVal", 23, [[[120, "100", 22, false, false, 1, 1, 1, -445, [171], 172], -446], 4, 1], [0, "1ekpv23xJFvKK4qyTfnTSV", 1], [5, 90, 40], [0, 0, 0.5], [-606.366, -72.102, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "lbWinVal", 23, [[[60, "0", 36, 0, false, false, 1, 1, 2, -447, [164], 165], -448], 4, 1], [0, "1fY/l8KopEg6CeAtsl6iF0", 1], [5, 230, 0], [0, 0, 0.5], [-206.421, -80.319, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line1", false, 2, [[4, 2, false, -449, 177]], [0, "bcZDIOZ9NPEbQimiwtPqeF", 1], [5, 800, 30], [-58, 20.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line2", false, 2, [[4, 2, false, -450, 178]], [0, "5f1N5T4/NKf4yz0HmdhdAf", 1], [5, 800, 30], [-58, 177, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line3", false, 2, [[4, 2, false, -451, 179]], [0, "94mGKCk8xNWYt/Kl1fItzn", 1], [5, 800, 30], [-58, -136.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line4", false, 2, [[4, 2, false, -452, 180]], [0, "94EsSrT7dMgZ6phC0iHwMW", 1], [5, 798, 183], [-59, 101.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line5", false, 2, [[4, 2, false, -453, 181]], [0, "aaNB82mRBNUogaa6CebK3o", 1], [5, 798, 183], [-59, -59, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line6", false, 2, [[4, 2, false, -454, 182]], [0, "f8uzwNV4hLhZYOyUy4JfHX", 1], [5, 797, 218], [-59, 115, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line7", false, 2, [[4, 2, false, -455, 183]], [0, "e2AyubrGNEjr6rO/YgHM/R", 1], [5, 797, 218], [-59, -74, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line8", false, 2, [[4, 2, false, -456, 184]], [0, "1brAwFTShB86XRHXn+Z/81", 1], [5, 800, 418], [-58, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line9", false, 2, [[4, 2, false, -457, 185]], [0, "282RUv3a1ETqG7s8YFCSWh", 1], [5, 800, 418], [-58, -5, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line10", false, 2, [[4, 2, false, -458, 186]], [0, "8807tH+ZdCq45FPNVVAiLG", 1], [5, 800, 353], [-58, 13.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line11", false, 2, [[4, 2, false, -459, 187]], [0, "f7LhUNEEhHzb6PH13Hmo2+", 1], [5, 800, 353], [-58, 13.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line12", false, 2, [[4, 2, false, -460, 188]], [0, "8f0BV7pbxGY5jwuavIn6BE", 1], [5, 800, 353], [-58, 28, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line13", false, 2, [[4, 2, false, -461, 189]], [0, "a5CYkI+g1M7qze9QIyV985", 1], [5, 798, 356], [-59, 14, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line14", false, 2, [[4, 2, false, -462, 190]], [0, "f3BzO7uylKPpbTUW31x5mx", 1], [5, 798, 356], [-59, 14, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line15", false, 2, [[4, 2, false, -463, 191]], [0, "cfTTxKNVVJpY6fTf1LzhxQ", 1], [5, 800, 190], [-58, -68, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line16", false, 2, [[4, 2, false, -464, 192]], [0, "7cHCWqrgZCfL0dgTUEhYDD", 1], [5, 798, 240], [56, 135, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line17", false, 2, [[4, 2, false, -465, 193]], [0, "741klCaUhF0r8pEHA0HDy8", 1], [5, 798, 196], [56, -62.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line18", false, 2, [[4, 2, false, -466, 194]], [0, "16XYwnKWtO2K/rMiJ78hXV", 1], [5, 798, 196], [56, 73, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line19", false, 2, [[4, 2, false, -467, 195]], [0, "56BB8e05xJ0JAM88X7qC0k", 1], [5, 798, 383], [56, 33, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line20", false, 2, [[4, 2, false, -468, 196]], [0, "37J2HCZUtCMqO03mrrZijr", 1], [5, 799, 382], [55.5, 8.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line21", false, 2, [[4, 2, false, -469, 197]], [0, "2bbtwm1JlC5LrfDncmtfSt", 1], [5, 797, 194], [55.5, 95.5, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line22", false, 2, [[4, 2, false, -470, 198]], [0, "8bz9TYFOtMRaIciQUAk0FM", 1], [5, 797, 194], [56, -53.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line23", false, 2, [[4, 2, false, -471, 199]], [0, "090OaIVS1KA6TDOolAmRI6", 1], [5, 798, 356], [56.5, 14.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line24", false, 2, [[4, 2, false, -472, 200]], [0, "5cYKRtC1xAz5yHM6Gd7MZL", 1], [5, 798, 354], [55.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line25", false, 2, [[4, 2, false, -473, 201]], [0, "1dceFOr/dOJYI3CSGbWeXr", 1], [5, 798, 354], [56.5, 27, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line26", false, 2, [[4, 2, false, -474, 202]], [0, "c8ifB8W+lAIbCUOtGxQ1wv", 1], [5, 799, 354], [55.5, 14, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line27", false, 2, [[4, 2, false, -475, 203]], [0, "9b+PEAuj9B74F54yf6g1ju", 1], [5, 799, 354], [57, 27.5, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line28", false, 2, [[4, 2, false, -476, 204]], [0, "47kq4kb/5P6LYuy5MTZfzN", 1], [5, 797, 340], [56, 20, 0, 0, 0, 0, 1, 1, -1, 1]], [2, "line29", false, 2, [[4, 2, false, -477, 205]], [0, "d2gXmvWbxEf6aWscSwkY/S", 1], [5, 797, 340], [55.5, 21, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "line30", false, 2, [[4, 2, false, -478, 206]], [0, "899VWPZVpAC54UkZB1S3zZ", 1], [5, 796, 236], [56.5, -95.5, 0, 0, 0, 0, 1, 1, 1, 1]], [83, "black", 1, 17, [[45, 0, -479, [234], 235], [66, -480, [[7, "eec18Pz6uxPv5oH0NNMqLNk", "continueClicked", 24]], [4, 4292269782]]], [0, "acaSUumBtLAK9PCiSXPca6", 1], [5, 3000, 3000]], [84, "ani_nohu", 17, [-482], [[37, "default", "Jackpot", 0, false, "Jackpot", -481, [237], 238]], [0, "c0mf3WBZZCsIoUBdPA7M7/", 1], [5, 1700, 1700]], [15, "lbWin", 17, [[[61, "1,000,000,000", 32, 90, false, false, 1, 1, -483, [239], 240], -484], 4, 1], [0, "f4QOCoNnRONK4BOMQO/fLk", 1], [5, 231.82, 90], [2, 18, 0, 0, 0, 0, 1, 2.763, 2.763, 2.763]], [15, "lbWin", 19, [[[61, "0", 28, 90, false, false, 1, 1, -485, [247], 248], -486], 4, 1], [0, "1fRf7kmbhKkYV05C4Obq3t", 1], [5, 18.67, 90], [0, -115, 0, 0, 0, 0, 1, 2.851, 2.851, 2.851]], [94, "particleWin", 25, [-488], [-487], [0, "c22endHpRB16tYdhd5rK0T", 1], [-4, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbWin", 25, [[[23, "1,000,000,000", 32, 30, false, 1, 1, -489, [257], 258], -490], 4, 1], [0, "8dkZn/LTBFlJf9XNDuil+M", 1], [5, 231.82, 30], [1.153, -33.893, 0, 0, 0, 0, 1, 1.985, 1.985, 1.985]], [41, "logo", 26, [[-491, [31, true, -492, [276], 275]], 1, 4], [0, "52rhuIjolLmJUHmAxqnAgi", 1], [5, 90, 44], [0, 0.5, 1], [0, 58.1, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "lbRoom", 20, [-494], [[32, "100:", 20, 50, false, 1, 1, -493, 277]], [0, "60D7jtT6RCgYS/mh5NrWkk", 1], [4, 4278255615], [5, 40, 25], [0, 0, 0.5], [-73.5, -37.5, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "lbRoom", 20, [-496], [[32, "1K:", 20, 50, false, 1, 1, -495, 278]], [0, "d7vushItxB8Z+hLwhURLF5", 1], [4, 4278255615], [5, 30, 25], [0, 0, 0.5], [-73.5, -77.5, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "lbRoom", 20, [-498], [[32, "5K:", 20, 50, false, 1, 1, -497, 279]], [0, "b660wCv3tBrZtbrchsC9nJ", 1], [4, 4278255615], [5, 30, 25], [0, 0, 0.5], [-73.5, -117.5, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "lbRoom", 20, [-500], [[32, "10K:", 20, 50, false, 1, 1, -499, 280]], [0, "779gSESfxLSKCpeIZBixpD", 1], [4, 4278255615], [5, 41.5, 25], [0, 0, 0.5], [-73.5, -157.5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "musicBackground", 6, [-501], [0, "75elQvBpFM/YbOcvZ7m8Ug", 1]], [135, true, true, 132], [12, "normalWin", 6, [-502], [0, "c6T1DWycZMEKtZAOoZ/Jpy", 1]], [14, true, 134], [12, "bigWin", 6, [-503], [0, "b8Q7MuWe9AO7LcofEtu8KA", 1]], [14, true, 136], [12, "getBonus", 6, [-504], [0, "f9vpHcNnFFppvXucUldPmj", 1]], [14, true, 138], [12, "spin", 6, [-505], [0, "9btE3dlNVKsL+0SuIBXQGr", 1]], [14, true, 140], [12, "stopSpin1", 6, [-506], [0, "3dsNPKGLVOYoYn+LiweVyO", 1]], [14, true, 142], [12, "stopSpin2", 6, [-507], [0, "060Rb3p2RJP7wdFf0PwEmm", 1]], [14, true, 144], [12, "stopSpin3", 6, [-508], [0, "29xZCWJzJOO7Glv0vQOJUq", 1]], [14, true, 146], [12, "stopSpin4", 6, [-509], [0, "64nRuH97pO7op1eRJ1odyz", 1]], [14, true, 148], [12, "stopSpin5", 6, [-510], [0, "6eDtDqQW1KrpXI9tlUtOsa", 1]], [14, true, 150], [10, "sprite", 40, [[110, false, -511, [2], 3]], [0, "d1+16/FqBDrZ98vFn4xfTP", 1], [5, 78, 78]], [10, "sprite", 41, [[17, 0, false, -512, [5], 6]], [0, "34I6+u/v5PRLXY7xrorIst", 1], [5, 70, 72]], [13, "sound", 44, [-513], [0, "8eUv/gZ1JDPZYtDuygyCf7", 1], [5, 35, 38], [51.2, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 2, false, 154, [9]], [1, "label", 44, [[33, "<PERSON><PERSON>", 20, false, 1, 1, -514, [10], 11]], [0, "c2TO0y5SdNNaKZpiMLY/q/", 1], [5, 89, 40], [-24, 1.6, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "music", 45, [-515], [0, "06JOsMy+pE9bwM6f3AEwSe", 1], [5, 35, 38], [52, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 2, false, 157, [12]], [1, "label", 45, [[33, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -516, [13], 14]], [0, "57+53wl0pFR73Yu8WWZu0t", 1], [5, 87, 40], [-24, 1.6, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 10, [17, 18]], [10, "text_choiThu", 46, [[3, -517, [24], 25]], [0, "6awH0yNPZCsLlo2JRBeYoH", 1], [5, 258, 108]], [25, 1.05, false, 3, 46, [[26, "48121xYynhO56qpPvfYDts6", "roomClicked", "0", 1]], [4, 4294967295], [4, 4294967295], 46], [1, "logo", 8, [[111, false, 2, false, -518, [26], 27]], [0, "38h6S8i55IBJhIcwadhdH7", 1], [5, 901, 75], [14, 323, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "phong100", 28, [[38, "default", "animation", 0, "animation", -519, [28], 29]], [0, "8b7vt/5YZPVJQF3qcnqOAz", 1], [5, 260, 220]], [11, 69], [25, 1.05, false, 3, 28, [[26, "48121xYynhO56qpPvfYDts6", "roomClicked", "1", 1]], [4, 4294967295], [4, 4294967295], 28], [1, "phong1000", 29, [[38, "default", "animation", 0, "animation", -520, [32], 33]], [0, "1frKlggxVCIYgymtCZWyY7", 1], [5, 266, 419.56], [-13.4, -206.74, 0, 0, 0, 0, 1, 1, 1, 1]], [11, 70], [25, 1.05, false, 3, 29, [[26, "48121xYynhO56qpPvfYDts6", "roomClicked", "2", 1]], [4, 4294967295], [4, 4294967295], 29], [1, "phong5000", 30, [[38, "default", "animation", 0, "animation", -521, [36], 37]], [0, "b5Yq425lVNUa/vNCDfyU5p", 1], [5, 319, 436], [-17.228, -139.741, 0, 0, 0, 0, 1, 1, 1, 1]], [11, 71], [25, 1.05, false, 3, 30, [[26, "48121xYynhO56qpPvfYDts6", "roomClicked", "3", 1]], [4, 4294967295], [4, 4294967295], 30], [1, "phong10000", 31, [[38, "default", "animation", 0, "animation", -522, [40], 41]], [0, "cbwEXbnrFGKp6ur0qS8bz0", 1], [5, 406, 420], [-6.19, -175.46, 0, 0, 0, 0, 1, 1, 1, 1]], [11, 72], [25, 1.05, false, 3, 31, [[26, "48121xYynhO56qpPvfYDts6", "roomClicked", "4", 1]], [4, 4294967295], [4, 4294967295], 31], [112, 1, 0, false, 47, [44]], [10, "BGspin", 3, [[3, -523, [45], 46]], [0, "98Fgs9DlBGTYMhMb5no8Fu", 1], [5, 39, 488]], [1, "BGspin", 3, [[3, -524, [47], 48]], [0, "6as3CV2W9FqJzBgm9OqA63", 1], [5, 39, 488], [39, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -525, [49], 50]], [0, "46twX5sjxMdrdm+58u38KE", 1], [5, 39, 488], [78, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -526, [51], 52]], [0, "0d0hyjoWhJq5ZlFL1SyV63", 1], [5, 39, 488], [117, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -527, [53], 54]], [0, "7cZnSLGxtAdpsJNU4h8xo4", 1], [5, 39, 488], [156, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -528, [55], 56]], [0, "9dN4IuVslEF4LVH1oyZn7/", 1], [5, 39, 488], [195, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -529, [57], 58]], [0, "d3fA7SEnVL0qLsRYBt+Jm9", 1], [5, 39, 488], [234, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -530, [59], 60]], [0, "6eCP4TNolIIrtwtPYbaYcn", 1], [5, 39, 488], [273, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -531, [61], 62]], [0, "d4uusjzqpN7bGkXVBij1/7", 1], [5, 39, 488], [312, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -532, [63], 64]], [0, "282YH9QCRAbLLKuXqmZEod", 1], [5, 39, 488], [351, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -533, [65], 66]], [0, "26KnUQLZ9I854g8SPYsr1X", 1], [5, 39, 488], [390, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -534, [67], 68]], [0, "c2B5nIb45JILOXWCpHtK6l", 1], [5, 39, 488], [429, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -535, [69], 70]], [0, "e4p2LZemJLeaGg+jZDsOpC", 1], [5, 39, 488], [468, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -536, [71], 72]], [0, "dbiVUsMxxLwo27CNxz1j+X", 1], [5, 39, 488], [507, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -537, [73], 74]], [0, "1fHzif2XtM44S4dJHK8hZR", 1], [5, 39, 488], [546, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -538, [75], 76]], [0, "b141bolQlNsL1vuDXBGDbm", 1], [5, 39, 488], [585, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -539, [77], 78]], [0, "86KC19AtBJ1bNM1dA5qG8l", 1], [5, 39, 488], [624, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -540, [79], 80]], [0, "86APHZxuZKaIPCqIVRoXK2", 1], [5, 39, 488], [663, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -541, [81], 82]], [0, "cc7AH4jOxEeKRAtTZ6Gv9P", 1], [5, 39, 488], [702, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -542, [83], 84]], [0, "a0JE4Mx6xMb4Xadn4IeHwu", 1], [5, 39, 488], [741, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -543, [85], 86]], [0, "0eFExOL+pCtquDCxpNeZdv", 1], [5, 39, 488], [780, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -544, [87], 88]], [0, "19pUcTmuBFt4W1dI7nDh3z", 1], [5, 39, 488], [819, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -545, [89], 90]], [0, "2cc2HI0f1Fua3dwySiQZid", 1], [5, 39, 488], [858, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BGspin", 3, [[3, -546, [91], 92]], [0, "76f5gdVPJLPadGQRvIfrk2", 1], [5, 39, 488], [897, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "khung", 48, [[3, -547, [93], 94]], [0, "9eU6aLQRlPbrFlvOHj9VhC", 1], [5, 1398, 728], [-65.961, -1.231, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "maingame14", false, 48, [[3, -548, [95], 96]], [0, "8eXT5/025MjamwPaL0EBG+", 1], [5, 173, 92], [-239.604, -314.426, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "maingame15", false, 48, [[3, -549, [97], 98]], [0, "bfrKgveZxEOaXRsfietJhz", 1], [5, 271, 90], [-472.814, -314.734, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "mermaid_ani", false, 7, [[37, "default", "animation", 0, false, "animation", -550, [99], 100]], [0, "faabxnG2tFxr7b161dLgHd", 1], [5, 450, 618], [550, 129, 0, 0, 0, 0, 1, -1, 1, 1]], [2, "rong_ani", false, 7, [[37, "default", "animation", 0, false, "animation", -551, [101], 102]], [0, "75lg0OVB1K+pX/0sPprqXd", 1], [5, 346.5, 397.5], [-526, -137, 0, 0, 0, 0, 1, -1, 1, 1]], [2, "khung", false, 7, [[17, 2, false, -552, [103], 104]], [0, "16Cu1NJStMKq95Nl8Fwl0p", 1], [5, 1278, 674], [-1, 39, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lbSessionID", 49, [-553], [0, "b3pkPFVDBGBYsZy6D1bB5c", 1], [5, 18.88, 50], [-702.727, -124.129, 0, 0, 0, 0, 1, 1, 1, 1]], [121, "#1", 22, 50, false, 1, 1, 1, 207, [107]], [9, "slot0", 12, [-554], [0, "85tNSMys1Jcot9oS+vemxZ", 1], [5, 140, 140], [0, 330, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 209, [-555], [0, "3eKg9rZthHGbBkAZRaAbsK", 1], [5, 163, 191.08]], [69, "default", 0, false, 210, [110]], [9, "slot1", 12, [-556], [0, "19bHivuK1AHqVMW3wT84Tb", 1], [5, 140, 140], [0, 165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 212, [74], [0, "c5p4APNCFJVqJjzkiFdulD", 1], [5, 163, 191.08]], [19, "slot2", 12, [-557], [0, "46/8t+eHJIJ4IjUvGny8lf", 1], [5, 140, 140]], [6, "skeleton", 214, [75], [0, "acpBVLtHhKPpfK+oD5NhAM", 1], [5, 167.07, 149.86]], [9, "slot3", 12, [-558], [0, "36TckemWVFd7yMiNF/VN+z", 1], [5, 140, 140], [0, -165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 216, [76], [0, "b6xR1tv+dKKprEVX/ZA/j5", 1], [5, 157, 158]], [27, 12, [211, 74, 75, 76]], [9, "slot0", 13, [-559], [0, "3bybH4OF5GM5tXxLnI1NU8", 1], [5, 140, 140], [0, 330, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 219, [-560], [0, "4fH5buzD9Ab7amC/qwryQy", 1], [5, 163, 191.08]], [47, 0, false, 220, [119]], [9, "slot1", 13, [-561], [0, "9diTW2kINDXI620ITF7AlH", 1], [5, 140, 140], [0, 165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 222, [77], [0, "00/DbBAe5Ft6bSkny2TO+S", 1], [5, 153, 140]], [19, "slot2", 13, [-562], [0, "13BHbrqeZFkZ2uFjiZ6aTt", 1], [5, 140, 140]], [6, "skeleton", 224, [78], [0, "442yfURodPpbLlDQ032YUK", 1], [5, 141, 136]], [9, "slot3", 13, [-563], [0, "1fNbAKxPZDQb5T4Lr5LOb4", 1], [5, 140, 140], [0, -165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 226, [79], [0, "34Bqq63T1GPqYZcUwPM9ov", 1], [5, 144, 164]], [27, 13, [221, 77, 78, 79]], [9, "slot0", 14, [-564], [0, "cffNUcvNhCoINOGkWtIobP", 1], [5, 140, 140], [0, 330, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 229, [-565], [0, "5e1x22EU9Je7OCz0OWi+cM", 1], [5, 163, 191.08]], [47, 0, false, 230, [128]], [9, "slot1", 14, [-566], [0, "5awoxn/7tEJrgtMC5V8DwY", 1], [5, 140, 140], [0, 165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 232, [80], [0, "b6VOImN/FEuYPrn0mPdfAU", 1], [5, 133.79, 139]], [19, "slot2", 14, [-567], [0, "6dK94Szy5EFJw3boO9iC0v", 1], [5, 140, 140]], [6, "skeleton", 234, [81], [0, "7aAQGRX9VHro1+aDer7jFM", 1], [5, 163, 191.08]], [9, "slot3", 14, [-568], [0, "9djuixdk5AX6Q729gi4JbW", 1], [5, 140, 140], [0, -165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 236, [82], [0, "ea/mFrzoBBR6Kbzv2x/A3G", 1], [5, 167.07, 149.86]], [27, 14, [231, 80, 81, 82]], [9, "slot0", 15, [-569], [0, "22AD1oTyFEF5gAJc+6+6hJ", 1], [5, 140, 140], [0, 330, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 239, [-570], [0, "e3QrAGS3BBZay60rwroLo/", 1], [5, 163, 191.08]], [47, 0, false, 240, [137]], [9, "slot1", 15, [-571], [0, "07KLk8+VdHMoRv2nGIIPvM", 1], [5, 140, 140], [0, 165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 242, [83], [0, "baxkJe7hNDIZQLr1gTatPl", 1], [5, 157, 158]], [19, "slot2", 15, [-572], [0, "1co7K/dgpB5q/n0VBC/rtb", 1], [5, 140, 140]], [6, "skeleton", 244, [84], [0, "ab+eCDPeRE6p4x4AdZMku6", 1], [5, 153, 140]], [9, "slot3", 15, [-573], [0, "eaKk6276tNCL3EEHql<PERSON>uh", 1], [5, 140, 140], [0, -165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 246, [85], [0, "01mCarFP9AeqotLFQ49Zww", 1], [5, 141, 136]], [27, 15, [241, 83, 84, 85]], [9, "slot0", 16, [-574], [0, "82/IobgaVHjoRGL/+KnfRr", 1], [5, 140, 140], [0, 330, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 249, [-575], [0, "6fvfT9M7FBLLfpIiHgmrwn", 1], [5, 163, 191.08]], [69, "default", 0, false, 250, [146]], [9, "slot1", 16, [-576], [0, "c951c7h8lNV6PGB/gFqcEu", 1], [5, 140, 140], [0, 165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 252, [86], [0, "fdx2g9an5ANbIeFj2oTyml", 1], [5, 144, 164]], [19, "slot2", 16, [-577], [0, "68H0p8JPxCB5N1pl/w7VAD", 1], [5, 140, 140]], [6, "skeleton", 254, [87], [0, "44r8SVI41A+60f7PX0+Ps4", 1], [5, 133.79, 139]], [9, "slot3", 16, [-578], [0, "bccog7Q+VMCKVe1Xs03k70", 1], [5, 140, 140], [0, -165, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "skeleton", 256, [88], [0, "c3M5ln+ZhIg74ZhSE18zgC", 1], [5, 163, 191.08]], [27, 16, [251, 86, 87, 88]], [11, 89], [92, "lineVal", 51, [-579], [0, "63k1A0eoNDSqMbY1QFX7/G", 1], [5, 50, 0], [0, 0, 0.5]], [122, "20", 28, 0, false, false, 1, 1, 260, [159]], [67, 1.1, 3, 51, [[7, "27803B0B7ZN+I0Q5PBtYj6/", "betLinesClicked", 5]], [4, 4294967295], [4, 4294967295], 51], [93, "label", 52, [-580], [0, "dboncbL9VKY7q4QT9WED28", 1], [4, 4287993243], [5, 150, 34], [0, 0, 0.5], [-168.451, -80.733, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "100", 28, 26, false, false, 1, 1, 1, 263, [160]], [10, "bg-coin", 53, [[44, 0, -581, 162]], [0, "b8mnbdFphJSqsvL1wx8YKQ", 1], [5, 287, 36]], [13, "lbFreespin", 53, [-582], [0, "b5KOdoZgxIBpP3S3J+C54h", 1], [5, 200, 40], [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [123, "QUAY MIỄN PHÍ: X2", 21, false, false, 1, 1, 1, 266], [11, 90], [59, 54, [166]], [59, 55, [169]], [22, 2, false, 56, [170]], [67, 1.05, 3, 56, [[7, "6245c28vjxNA7+sCzsGilk9", "spinClicked", 22]], [4, 4294967295], [4, 4294967295], 56], [11, 57], [10, "btn_back", 59, [[3, -583, [207], 208]], [0, "75+Z2GA2JE26JhOMWgMx2D", 1], [5, 83, 70]], [129, 3, 59, [[7, "27803B0B7ZN+I0Q5PBtYj6/", "backClicked", 5]], [4, 4294967295], [4, 4294967295], 59], [10, "btn_VD", 60, [[3, -584, [209], 210]], [0, "40JqOh0DdJ54xwklBK8n2V", 1], [5, 67, 55]], [10, "btn_ls", 61, [[3, -585, [212], 213]], [0, "b2U3R9JZVJqKaR2ed9DUK4", 1], [5, 67, 55]], [10, "bnt_setting", 62, [[3, -586, [215], 216]], [0, "f2pemxX2xBiYYHEUn/GIzn", 1], [5, 94, 87]], [13, "sound", 65, [-587], [0, "b2eC+hnelMiYIrzzeOCItq", 1], [5, 35, 38], [51.2, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 2, false, 279, [219]], [1, "label", 65, [[33, "<PERSON><PERSON>", 20, false, 1, 1, -588, [220], 221]], [0, "e6Ag6O10dI6ZULK8ihv5xH", 1], [5, 89, 40], [-24, 1.6, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "music", 66, [-589], [0, "e587R+oFpBCrU8sm0nvaP0", 1], [5, 35, 38], [52, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 2, false, 282, [222]], [1, "label", 66, [[33, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -590, [223], 224]], [0, "b23gEwxOtMFbu/2HrS9qzT", 1], [5, 87, 40], [-24, 1.6, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 9, [227, 228]], [56, "noHu", 17, [-591], [0, "acwgwYQF1NXJ/tPemFQpwi", 1], [0, 409, 0, 0, 0, 0, 1, 1, 1, 1]], [136, 277, 184.66666666666666, 1.5, 0.5, 0, 0, 40, -1, 51, 500, 1, 0, 337, 0, 50, 286, [236], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 608, 1], [0, 0, -906]], [40, "ATTACHED_NODE_TREE", 122, [4], [0, "15XAc7BahM2ZR0Nd/evRHo", 1]], [55, "ATTACHED_NODE:bone24", 4, [[5, "ATTACHED_NODE:bone23", -592, [0, "f1RQAW91xFPIgThstPoLJk", 1]]], [0, "111hgOhaFLs4fowOEq/nE8", 1]], [11, 123], [85, "black", false, 100, 19, [[44, 0, -593, 243]], [0, "4ds1aXXZlHM4bFGFdfHujl", 1], [5, 3000, 3000]], [56, "Thang_1", 19, [-594], [0, "1fNlyuWP9HkJkq4Y2tpKGf", 1], [0, 79, 0, 0, 0, 0, 1, 1, 1, 1]], [137, 100, 1.5, 0.5, 58, 40, -1, 51, 500, 1, 850, 383, 0, 50, 292, [244], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 101, 73], [0, 0, -2400]], [10, "ani_Thag<PERSON>on", 19, [[37, "default", "<PERSON><PERSON>", 0, false, "<PERSON><PERSON>", -595, [245], 246]], [0, "fc9AdfAONNvIdLDk3/P8rV", 1], [5, 1700, 1700]], [11, 124], [86, "particleWin", 125, [[138, true, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, -596, [251], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400], 252, 253]], [0, "4b5ZMe6ldEfI8itRtkfvqY", 1], [-4, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [139, true, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, 125, [254], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [1, "bg-coin", 25, [[17, 2, false, -597, [255], 256]], [0, "7f22PqAnBIcbsjThtx4so6", 1], [5, 369, 77], [0, -32, 0, 0, 0, 0, 1, 1.437, 1.437, 1.437]], [11, 126], [113, 2, false, 127], [30, "lbRemain", 128, [-598], [0, "47LDLt3G1OtovcaXFmz0K8", 1], [5, 102.5, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "CÒN 26 HŨ", 20, 50, false, 1, 1, 301], [30, "lbRemain", 129, [-599], [0, "83mZduVbBDyqgS6TenOlsf", 1], [5, 102.5, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "CÒN 26 HŨ", 20, 50, false, 1, 1, 303], [30, "lbRemain", 130, [-600], [0, "e3M5kGCFVNwo/8KJyWcNkS", 1], [5, 91, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "CÒN 2 HŨ", 20, 50, false, 1, 1, 305], [30, "lbRemain", 131, [-601], [0, "69LQyLSXxGuokuVqRgdRnn", 1], [5, 91, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "CÒN 2 HŨ", 20, 50, false, 1, 1, 307], [66, 26, [[7, "5c1fa7YnkBEMqpU9n30ksJn", "openEventClicked", 67]], [4, 4292269782]], [13, "lbMessage", 38, [-602], [0, "65ACTNGl1MTI2S159EEA4b", 1], [5, 700, 70], [0, 27, 0, 0, 0, 0, 1, 1, 1, 1]], [124, "<PERSON><PERSON><PERSON> mừng bạn nhận được", 70, false, false, 1, 1, 3, 310, [282]], [13, "lbWin", 38, [-603], [0, "9aJPKIO9tLz5HuKMTplfjD", 1], [5, 666, 20], [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "0", 20, 20, false, false, 1, 1, 1, 312, [283]]], 0, [0, 15, 1, 0, 0, 1, 0, -1, 162, 0, -2, 166, 0, -3, 169, 0, -4, 172, 0, -5, 175, 0, 16, 5, 0, 17, 8, 0, 0, 1, 0, -1, 7, 0, -2, 24, 0, -3, 58, 0, -4, 24, 0, -5, 9, 0, 0, 1, 0, -1, 6, 0, -2, 8, 0, -3, 5, 0, -4, 67, 0, -5, 68, 0, -1, 74, 0, -2, 77, 0, -3, 80, 0, -4, 83, 0, -5, 86, 0, -6, 75, 0, -7, 78, 0, -8, 81, 0, -9, 84, 0, -10, 87, 0, -11, 76, 0, -12, 79, 0, -13, 82, 0, -14, 85, 0, -15, 88, 0, -1, 91, 0, -2, 92, 0, -3, 93, 0, -4, 94, 0, -5, 95, 0, -6, 96, 0, -7, 97, 0, -8, 98, 0, -9, 99, 0, -10, 100, 0, -11, 101, 0, -12, 102, 0, -13, 103, 0, -14, 104, 0, -15, 105, 0, -16, 106, 0, -17, 107, 0, -18, 108, 0, -19, 109, 0, -20, 110, 0, -21, 111, 0, -22, 112, 0, -23, 113, 0, -24, 114, 0, -25, 115, 0, -26, 116, 0, -27, 117, 0, -28, 118, 0, -29, 119, 0, -30, 120, 0, 0, 2, 0, -1, 91, 0, -2, 92, 0, -3, 93, 0, -4, 94, 0, -5, 95, 0, -6, 96, 0, -7, 97, 0, -8, 98, 0, -9, 99, 0, -10, 100, 0, -11, 101, 0, -12, 102, 0, -13, 103, 0, -14, 104, 0, -15, 105, 0, -16, 106, 0, -17, 107, 0, -18, 108, 0, -19, 109, 0, -20, 110, 0, -21, 111, 0, -22, 112, 0, -23, 113, 0, -24, 114, 0, -25, 115, 0, -26, 116, 0, -27, 117, 0, -28, 118, 0, -29, 119, 0, -30, 120, 0, 0, 3, 0, -1, 177, 0, -2, 178, 0, -3, 179, 0, -4, 180, 0, -5, 181, 0, -6, 182, 0, -7, 183, 0, -8, 184, 0, -9, 185, 0, -10, 186, 0, -11, 187, 0, -12, 188, 0, -13, 189, 0, -14, 190, 0, -15, 191, 0, -16, 192, 0, -17, 193, 0, -18, 194, 0, -19, 195, 0, -20, 196, 0, -21, 197, 0, -22, 198, 0, -23, 199, 0, -24, 200, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, -7, 18, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, 3, 4, 0, -17, 289, 0, 0, 5, 0, 18, 269, 0, 19, 270, 0, 20, 271, 0, 21, 272, 0, 22, 262, 0, 23, 275, 0, 0, 5, 0, 24, 267, 0, 0, 5, 0, -1, 47, 0, -3, 48, 0, -4, 7, 0, -5, 58, 0, -6, 24, 0, -7, 9, 0, 25, 151, 0, 26, 149, 0, 27, 147, 0, 28, 145, 0, 29, 143, 0, 30, 141, 0, 31, 139, 0, 32, 137, 0, 33, 135, 0, 34, 133, 0, 0, 6, 0, -1, 132, 0, -2, 134, 0, -3, 136, 0, -4, 138, 0, -5, 140, 0, -6, 142, 0, -7, 144, 0, -8, 146, 0, -9, 148, 0, -10, 150, 0, -1, 204, 0, -2, 205, 0, -3, 206, 0, -4, 49, 0, -5, 22, 0, -6, 34, 0, -7, 57, 0, -8, 23, 0, -1, 39, 0, -2, 40, 0, -3, 41, 0, -4, 46, 0, -5, 163, 0, -6, 21, 0, -7, 10, 0, -1, 285, 0, 11, 283, 0, 12, 280, 0, 13, 36, 0, 14, 285, 0, 0, 9, 0, -1, 36, 0, -1, 160, 0, 11, 158, 0, 12, 155, 0, 13, 27, 0, 14, 160, 0, 0, 10, 0, -1, 27, 0, 0, 11, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, 0, 12, 0, -2, 218, 0, -1, 209, 0, -2, 212, 0, -3, 214, 0, -4, 216, 0, 0, 13, 0, -2, 228, 0, -1, 219, 0, -2, 222, 0, -3, 224, 0, -4, 226, 0, 0, 14, 0, -2, 238, 0, -1, 229, 0, -2, 232, 0, -3, 234, 0, -4, 236, 0, 0, 15, 0, -2, 248, 0, -1, 239, 0, -2, 242, 0, -3, 244, 0, -4, 246, 0, 0, 16, 0, -2, 258, 0, -1, 249, 0, -2, 252, 0, -3, 254, 0, -4, 256, 0, 0, 17, 0, -1, 121, 0, -2, 286, 0, -3, 122, 0, -4, 123, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 0, 19, 0, -1, 291, 0, -2, 292, 0, -3, 294, 0, -4, 124, 0, 0, 20, 0, 0, 20, 0, -1, 128, 0, -2, 129, 0, -3, 130, 0, -4, 131, 0, -1, 165, 0, -2, 168, 0, -3, 171, 0, -4, 174, 0, 0, 21, 0, -1, 28, 0, -2, 29, 0, -3, 30, 0, -4, 31, 0, 0, 22, 0, 35, 73, 0, 36, 264, 0, 37, 176, 0, -1, 218, 0, -2, 228, 0, -3, 238, 0, -4, 248, 0, -5, 258, 0, 38, 261, 0, 39, 208, 0, 40, 268, 0, 41, 259, 0, 0, 22, 0, -1, 32, 0, -2, 53, 0, -3, 89, 0, -4, 90, 0, -5, 33, 0, -1, 290, 0, -2, 295, 0, -3, 299, 0, 42, 297, 0, 43, 293, 0, 44, 287, 0, 45, 25, 0, 46, 37, 0, 0, 24, 0, -1, 37, 0, 0, 25, 0, -1, 125, 0, -2, 298, 0, -3, 126, 0, 47, 309, 0, 0, 26, 0, -2, 309, 0, -1, 127, 0, 0, 27, 0, -1, 42, 0, -2, 43, 0, -1, 166, 0, -1, 164, 0, -2, 69, 0, -1, 169, 0, -1, 167, 0, -2, 70, 0, -1, 172, 0, -1, 170, 0, -2, 71, 0, -1, 175, 0, -1, 173, 0, -2, 72, 0, 0, 32, 0, -1, 50, 0, -2, 51, 0, -3, 52, 0, 0, 33, 0, -1, 54, 0, -2, 55, 0, -3, 56, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, -1, 60, 0, -2, 61, 0, -3, 62, 0, 0, 36, 0, -1, 63, 0, -2, 64, 0, 0, 38, 0, -1, 310, 0, -2, 312, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, 6, 40, 0, 0, 40, 0, -1, 152, 0, 6, 41, 0, 0, 41, 0, -1, 153, 0, 0, 42, 0, 6, 42, 0, 0, 42, 0, 0, 43, 0, -1, 44, 0, -2, 45, 0, 0, 44, 0, -1, 154, 0, -2, 156, 0, 0, 45, 0, -1, 157, 0, -2, 159, 0, -1, 162, 0, -1, 161, 0, -1, 176, 0, 0, 47, 0, 0, 47, 0, -1, 201, 0, -2, 202, 0, -3, 203, 0, 0, 49, 0, -1, 73, 0, -2, 207, 0, 0, 50, 0, 6, 50, 0, 0, 50, 0, -1, 262, 0, -1, 260, 0, 6, 52, 0, 0, 52, 0, -1, 263, 0, 0, 53, 0, -1, 265, 0, -2, 266, 0, -1, 269, 0, 6, 54, 0, 0, 54, 0, 6, 55, 0, 0, 55, 0, -2, 270, 0, -1, 271, 0, -2, 272, 0, 0, 57, 0, -2, 273, 0, 48, 273, 0, 0, 57, 0, -1, 59, 0, -1, 275, 0, -1, 274, 0, 6, 60, 0, 0, 60, 0, -1, 276, 0, 6, 61, 0, 0, 61, 0, -1, 277, 0, 6, 62, 0, 0, 62, 0, -1, 278, 0, 0, 63, 0, 6, 63, 0, 0, 63, 0, 0, 64, 0, -1, 65, 0, -2, 66, 0, 0, 65, 0, -1, 279, 0, -2, 281, 0, 0, 66, 0, -1, 282, 0, -2, 284, 0, -1, 302, 0, -2, 304, 0, -3, 306, 0, -4, 308, 0, 49, 300, 0, 0, 67, 0, 0, 68, 0, 50, 313, 0, 51, 311, 0, 0, 68, 0, 0, 69, 0, -2, 165, 0, 0, 70, 0, -2, 168, 0, 0, 71, 0, -2, 171, 0, 0, 72, 0, -2, 174, 0, 0, 73, 0, 0, 89, 0, -2, 259, 0, 0, 90, 0, -2, 268, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, 0, 109, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, 0, 113, 0, 0, 114, 0, 0, 115, 0, 0, 116, 0, 0, 117, 0, 0, 118, 0, 0, 119, 0, 0, 120, 0, 0, 121, 0, 0, 121, 0, 0, 122, 0, -1, 288, 0, 0, 123, 0, -2, 290, 0, 0, 124, 0, -2, 295, 0, -1, 297, 0, -1, 296, 0, 0, 126, 0, -2, 299, 0, -1, 300, 0, 0, 127, 0, 0, 128, 0, -1, 301, 0, 0, 129, 0, -1, 303, 0, 0, 130, 0, -1, 305, 0, 0, 131, 0, -1, 307, 0, -1, 133, 0, -1, 135, 0, -1, 137, 0, -1, 139, 0, -1, 141, 0, -1, 143, 0, -1, 145, 0, -1, 147, 0, -1, 149, 0, -1, 151, 0, 0, 152, 0, 0, 153, 0, -1, 155, 0, 0, 156, 0, -1, 158, 0, 0, 159, 0, 0, 161, 0, 0, 163, 0, 0, 164, 0, 0, 167, 0, 0, 170, 0, 0, 173, 0, 0, 177, 0, 0, 178, 0, 0, 179, 0, 0, 180, 0, 0, 181, 0, 0, 182, 0, 0, 183, 0, 0, 184, 0, 0, 185, 0, 0, 186, 0, 0, 187, 0, 0, 188, 0, 0, 189, 0, 0, 190, 0, 0, 191, 0, 0, 192, 0, 0, 193, 0, 0, 194, 0, 0, 195, 0, 0, 196, 0, 0, 197, 0, 0, 198, 0, 0, 199, 0, 0, 200, 0, 0, 201, 0, 0, 202, 0, 0, 203, 0, 0, 204, 0, 0, 205, 0, 0, 206, 0, -1, 208, 0, -1, 210, 0, -1, 211, 0, -1, 213, 0, -1, 215, 0, -1, 217, 0, -1, 220, 0, -1, 221, 0, -1, 223, 0, -1, 225, 0, -1, 227, 0, -1, 230, 0, -1, 231, 0, -1, 233, 0, -1, 235, 0, -1, 237, 0, -1, 240, 0, -1, 241, 0, -1, 243, 0, -1, 245, 0, -1, 247, 0, -1, 250, 0, -1, 251, 0, -1, 253, 0, -1, 255, 0, -1, 257, 0, -1, 261, 0, -1, 264, 0, 0, 265, 0, -1, 267, 0, 0, 274, 0, 0, 276, 0, 0, 277, 0, 0, 278, 0, -1, 280, 0, 0, 281, 0, -1, 283, 0, 0, 284, 0, -1, 287, 0, 3, 289, 0, 0, 291, 0, -1, 293, 0, 0, 294, 0, 0, 296, 0, 0, 298, 0, -1, 302, 0, -1, 304, 0, -1, 306, 0, -1, 308, 0, -1, 311, 0, -1, 313, 0, 52, 1, 2, 3, 7, 3, 3, 5, 4, 3, 288, 11, 3, 22, 17, 3, 37, 19, 3, 37, 20, 3, 26, 25, 3, 37, 26, 3, 67, 35, 3, 58, 38, 3, 68, 74, 0, 213, 75, 0, 215, 76, 0, 217, 77, 0, 223, 78, 0, 225, 79, 0, 227, 80, 0, 233, 81, 0, 235, 82, 0, 237, 83, 0, 243, 84, 0, 245, 85, 0, 247, 86, 0, 253, 87, 0, 255, 88, 0, 257, 603], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 133, 135, 137, 139, 141, 143, 145, 147, 149, 151, 155, 158, 176, 208, 211, 74, 75, 76, 221, 77, 78, 79, 231, 80, 81, 82, 241, 83, 84, 85, 251, 86, 87, 88, 261, 262, 264, 267, 269, 270, 271, 272, 275, 280, 283, 287, 287, 293, 293, 297, 297, 300, 302, 304, 306, 308, 311, 313], [-1, 1, -1, 1, 7, -1, 1, -1, 1, -1, -1, 4, -1, -1, 4, -1, 1, -1, -2, -1, -2, -1, -2, 7, -1, 1, -1, 1, -1, 5, -1, 4, -1, 5, -1, 4, -1, 5, -1, 4, -1, 5, -1, 4, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 5, -1, 5, -1, 1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 9, -1, -2, -3, -4, -1, -1, -1, -1, 9, -1, -2, -3, -4, -1, -1, -1, -1, 9, -1, -2, -3, -4, -1, -1, -1, -1, 9, -1, -2, -3, -4, -1, -1, -1, -1, 9, -1, -2, -3, -4, -1, -1, 1, 7, -1, -1, 7, 1, 1, -1, 4, -1, 53, 7, -1, -1, -1, 4, -1, 4, -1, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, -1, 1, -1, 1, 7, -1, 1, 7, -1, 1, -1, 1, -1, -1, 4, -1, -1, 4, -1, 1, -1, -2, -1, -2, -1, -2, 7, -1, 1, -1, -1, 5, -1, 4, 9, -1, 1, -1, -1, 5, -1, 4, 9, -1, -1, 10, 1, -1, -1, 1, -1, 4, 9, -1, 54, 55, 56, 57, 58, 59, -1, -2, -1, -2, -1, -2, -1, -2, 9, -1, 4, 4, 4, 4, 1, -1, -1, -1, 1, -1, 60, 61, -1, -2, -3, -4, -5, -6, -7, -1, -2, -3, 8, 8, 8, 8, 8, 8, 8, 8, 8, 8, 1, 1, 1, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 4, 7, 4, 4, 1, 1, 1, 7, 7, 1, 1, 10, 1, 10, 1, 10, 1, 1, 4, 4, 4, 4, 4, 4], [0, 23, 0, 52, 3, 0, 53, 0, 13, 0, 0, 4, 0, 0, 4, 0, 24, 25, 26, 14, 27, 15, 28, 3, 0, 54, 0, 55, 1, 56, 0, 16, 1, 57, 0, 16, 1, 58, 0, 16, 1, 59, 0, 16, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 60, 0, 61, 0, 62, 1, 63, 1, 64, 0, 65, 0, 66, 0, 0, 67, 1, 1, 1, 1, 5, 5, 8, 9, 10, 1, 1, 1, 1, 5, 5, 8, 9, 10, 1, 1, 1, 1, 5, 5, 8, 9, 10, 1, 1, 1, 1, 5, 5, 8, 9, 10, 1, 1, 1, 1, 5, 5, 8, 9, 10, 0, 0, 68, 3, 0, 0, 3, 29, 29, 0, 11, 0, 0, 3, 0, 0, 0, 19, 0, 19, 0, 11, 20, 20, 20, 30, 30, 31, 31, 32, 32, 69, 33, 33, 70, 71, 72, 73, 34, 34, 74, 75, 35, 35, 76, 36, 36, 37, 37, 38, 38, 77, 0, 78, 0, 79, 3, 0, 80, 3, 0, 81, 0, 13, 0, 0, 4, 0, 0, 4, 0, 24, 25, 26, 14, 27, 15, 28, 3, 0, 13, 0, 1, 39, 0, 11, 7, 7, 13, 0, 1, 39, 0, 11, 7, 7, 0, 40, 17, 0, 0, 82, 0, 11, 7, 7, 83, 84, 85, 86, 87, 88, 41, 89, 90, 91, 92, 21, 42, 42, 43, 43, 4, 4, 4, 4, 93, 0, 0, 0, 22, 94, 22, 22, 95, 96, 97, 98, 99, 100, 101, 44, 102, 44, 103, 104, 105, 106, 107, 12, 12, 12, 12, 12, 14, 15, 23, 19, 6, 6, 45, 46, 6, 47, 48, 49, 6, 50, 6, 45, 6, 46, 47, 48, 6, 49, 50, 6, 108, 3, 109, 4, 21, 21, 41, 3, 3, 14, 15, 110, 17, 111, 17, 40, 17, 112, 18, 18, 18, 18, 4, 113]], [[[28, "n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ".mp3", 87.32735], -1], 0, 0, [], [], []], [[{"name": "khung", "rect": [7, 0, 1271, 674], "offset": [3.5, 0], "originalSize": [1278, 674], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [114]], [[{"name": "line26", "rect": [0, 0, 799, 354], "offset": [0, 0], "originalSize": [799, 354], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [115]], [[{"name": "btn_ls", "rect": [0, 0, 67, 55], "offset": [0, 0], "originalSize": [67, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [116]], [[{"name": "btn_back", "rect": [0, 0, 83, 70], "offset": [0, 0], "originalSize": [83, 70], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [117]], [[{"name": "btn_quay_off", "rect": [0, 0, 258, 108], "offset": [0, 0], "originalSize": [258, 108], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [118]], [[{"name": "line16", "rect": [0, 0, 798, 240], "offset": [0, 0], "originalSize": [798, 240], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [119]], [[{"name": "btn_ST_on", "rect": [0, 0, 212, 39], "offset": [0, 0], "originalSize": [212, 39], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [120]], [[{"name": "line11", "rect": [0, 0, 800, 353], "offset": [0, 0], "originalSize": [800, 353], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [121]], [[[28, "BigWin2", ".mp3", 3.918375], -1], 0, 0, [], [], []], [[{"name": "btn_VD", "rect": [0, 0, 67, 55], "offset": [0, 0], "originalSize": [67, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [122]], [[{"name": "thantai_text_game", "rect": [0, 0, 259, 511], "offset": [-126.5, 0.5], "originalSize": [512, 512], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [123]], [[[28, "quaytacht", ".mp3", 2.351063], -1], 0, 0, [], [], []], [[{"name": "btn_ST_off", "rect": [0, 0, 212, 39], "offset": [0, 0], "originalSize": [212, 39], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [124]], [[{"name": "btn_TQ_Dung", "rect": [0, 0, 159, 39], "offset": [0, 0], "originalSize": [159, 39], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [125]], [[[140, "wineffect_tayduky", "\nwineffect_tayduky.png\nsize: 1301,1301\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nBIGWIN1\n  rotate: true\n  xy: 438, 279\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN2\n  rotate: true\n  xy: 511, 279\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN3\n  rotate: false\n  xy: 451, 4\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN4\n  rotate: true\n  xy: 584, 279\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN5\n  rotate: true\n  xy: 657, 305\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN6\n  rotate: true\n  xy: 730, 305\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN7\n  rotate: true\n  xy: 803, 305\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN8\n  rotate: true\n  xy: 876, 305\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nBIGWIN9\n  rotate: true\n  xy: 949, 305\n  size: 297, 71\n  orig: 297, 71\n  offset: 0, 0\n  index: -1\nFB0\n  rotate: false\n  xy: 211, 510\n  size: 64, 66\n  orig: 64, 66\n  offset: 0, 0\n  index: -1\nFB1\n  rotate: false\n  xy: 345, 2\n  size: 104, 73\n  orig: 104, 73\n  offset: 0, 0\n  index: -1\nFB10\n  rotate: false\n  xy: 1102, 309\n  size: 130, 78\n  orig: 130, 78\n  offset: 0, 0\n  index: -1\nFB11\n  rotate: true\n  xy: 1022, 355\n  size: 131, 78\n  orig: 134, 78\n  offset: 3, 0\n  index: -1\nFB12\n  rotate: false\n  xy: 277, 513\n  size: 129, 63\n  orig: 131, 63\n  offset: 2, 0\n  index: -1\nFB2\n  rotate: true\n  xy: 1022, 488\n  size: 146, 78\n  orig: 146, 78\n  offset: 0, 0\n  index: -1\nFB3\n  rotate: false\n  xy: 1119, 517\n  size: 175, 85\n  orig: 175, 85\n  offset: 0, 0\n  index: -1\nFB4\n  rotate: false\n  xy: 2, 605\n  size: 209, 87\n  orig: 209, 87\n  offset: 0, 0\n  index: -1\nFB5\n  rotate: true\n  xy: 608, 694\n  size: 230, 88\n  orig: 230, 88\n  offset: 0, 0\n  index: -1\nFB6\n  rotate: false\n  xy: 950, 21\n  size: 230, 86\n  orig: 230, 86\n  offset: 0, 0\n  index: -1\nFB7\n  rotate: true\n  xy: 690, 85\n  size: 218, 85\n  orig: 218, 85\n  offset: 0, 0\n  index: -1\nFB8\n  rotate: true\n  xy: 777, 102\n  size: 201, 82\n  orig: 201, 82\n  offset: 0, 0\n  index: -1\nFB9\n  rotate: false\n  xy: 750, 2\n  size: 198, 81\n  orig: 198, 90\n  offset: 0, 9\n  index: -1\nblackpoint\n  rotate: false\n  xy: 1263, 1298\n  size: 1, 1\n  orig: 1, 1\n  offset: 0, 0\n  index: -1\nbn0\n  rotate: true\n  xy: 149, 698\n  size: 97, 102\n  orig: 97, 112\n  offset: 0, 5\n  index: -1\nbn1\n  rotate: true\n  xy: 253, 698\n  size: 97, 102\n  orig: 97, 112\n  offset: 0, 5\n  index: -1\nbn2\n  rotate: true\n  xy: 97, 508\n  size: 95, 112\n  orig: 97, 112\n  offset: 1, 0\n  index: -1\nbn3\n  rotate: true\n  xy: 357, 698\n  size: 97, 102\n  orig: 97, 112\n  offset: 0, 5\n  index: -1\nbn4\n  rotate: true\n  xy: 461, 698\n  size: 97, 102\n  orig: 97, 112\n  offset: 0, 5\n  index: -1\nbonus0\n  rotate: false\n  xy: 2, 720\n  size: 145, 147\n  orig: 146, 148\n  offset: 0, 1\n  index: -1\nbonus1\n  rotate: true\n  xy: 97, 9\n  size: 497, 116\n  orig: 497, 116\n  offset: 0, 0\n  index: -1\nboxtienthang\n  rotate: true\n  xy: 345, 77\n  size: 434, 91\n  orig: 435, 91\n  offset: 0, 0\n  index: -1\ncloud1\n  rotate: false\n  xy: 1102, 389\n  size: 190, 126\n  orig: 234, 127\n  offset: 15, 1\n  index: -1\ncloud11\n  rotate: true\n  xy: 438, 77\n  size: 200, 127\n  orig: 234, 127\n  offset: 0, 0\n  index: -1\ncloud3\n  rotate: true\n  xy: 1119, 121\n  size: 186, 127\n  orig: 234, 127\n  offset: 22, 0\n  index: -1\ncloud5\n  rotate: true\n  xy: 990, 112\n  size: 191, 127\n  orig: 234, 127\n  offset: 16, 0\n  index: -1\ncloud7\n  rotate: true\n  xy: 861, 109\n  size: 194, 127\n  orig: 234, 127\n  offset: 11, 0\n  index: -1\ncloud9\n  rotate: true\n  xy: 567, 79\n  size: 198, 121\n  orig: 234, 127\n  offset: 4, 4\n  index: -1\ncuonvai\n  rotate: false\n  xy: 1240, 604\n  size: 59, 233\n  orig: 60, 234\n  offset: 0, 1\n  index: -1\ndomdom\n  rotate: false\n  xy: 149, 797\n  size: 443, 70\n  orig: 444, 71\n  offset: 0, 1\n  index: -1\njackpottext1\n  rotate: false\n  xy: 700, 1206\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\njackpottext2\n  rotate: false\n  xy: 700, 1111\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\njackpottext3\n  rotate: false\n  xy: 700, 1016\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\njackpottext4\n  rotate: false\n  xy: 700, 921\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\njackpottext5\n  rotate: false\n  xy: 698, 826\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\njackpottext6\n  rotate: false\n  xy: 698, 731\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\njackpottext7\n  rotate: false\n  xy: 698, 636\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\njackpottext8\n  rotate: true\n  xy: 2, 62\n  size: 540, 93\n  orig: 540, 93\n  offset: 0, 0\n  index: -1\nstaff\n  rotate: false\n  xy: 1242, 839\n  size: 19, 460\n  orig: 20, 461\n  offset: 0, 1\n  index: -1\nwineffect1\n  rotate: false\n  xy: 2, 1154\n  size: 696, 145\n  orig: 697, 146\n  offset: 0, 1\n  index: -1\nwineffect2\n  rotate: false\n  xy: 2, 869\n  size: 604, 197\n  orig: 605, 197\n  offset: 0, 0\n  index: -1\nwineffect3\n  rotate: false\n  xy: 213, 578\n  size: 436, 114\n  orig: 437, 115\n  offset: 0, 1\n  index: -1\nwineffect4\n  rotate: true\n  xy: 633, 926\n  size: 226, 65\n  orig: 226, 65\n  offset: 0, 0\n  index: -1\nwineffect5\n  rotate: true\n  xy: 215, 74\n  size: 434, 128\n  orig: 435, 128\n  offset: 0, 0\n  index: -1\nwineffect6\n  rotate: false\n  xy: 2, 1068\n  size: 629, 84\n  orig: 629, 84\n  offset: 0, 0\n  index: -1\n", ["wineffect_tayduky.png"], {"skeleton": {"hash": "uqb2wlndP2JXSf1odTg8BXAssPI", "spine": "3.7.93", "width": 1700, "height": 1700, "images": "", "audio": "/Users/<USER>/Trung/longshin/file Ve/tayduky/WineffectAnimation"}, "bones": [{"name": "root", "scaleX": 0.85, "scaleY": 0.85}, {"name": "bone", "parent": "root", "length": 40.46, "rotation": 90, "x": -2.03, "y": 8.95}, {"name": "bone2", "parent": "root", "length": 27.28, "rotation": 90, "x": 5.42, "y": -76.31, "scaleX": 1.567, "scaleY": 3.055}, {"name": "bone3", "parent": "root", "length": 27.28, "rotation": 90, "x": -287.3, "y": -78.14, "scaleX": 1.478, "scaleY": 2.475}, {"name": "bone4", "parent": "root", "length": 27.28, "rotation": 90, "x": 303.93, "y": -78.12, "scaleX": 1.288, "scaleY": 2.593}, {"name": "bone5", "parent": "root", "length": 48, "rotation": 90, "x": -0.69, "y": -10.73}, {"name": "bone6", "parent": "root", "length": 58.97, "rotation": 90, "x": 0.82, "y": -179.94}, {"name": "bone7", "parent": "root", "length": 54.75, "rotation": 90, "x": 1.54, "y": -142.06, "scaleX": 0.844, "scaleY": 0.844}, {"name": "bone8", "parent": "bone7", "length": 216.32, "rotation": 5.42, "x": 83.95, "y": 417.56}, {"name": "bone9", "parent": "bone7", "length": 255.05, "rotation": -5.09, "x": 62.05, "y": -415.37}, {"name": "bone10", "parent": "bone7", "length": 92.93, "rotation": 32.16, "x": 99.55, "y": 259.88}, {"name": "bone11", "parent": "bone7", "length": 92.77, "rotation": 27.55, "x": 67.97, "y": 82.27}, {"name": "bone12", "parent": "bone7", "length": 97.63, "rotation": -24.51, "x": 73.33, "y": -70.9}, {"name": "bone13", "parent": "bone7", "length": 111.26, "rotation": -29.17, "x": 93.86, "y": -232.72}, {"name": "bone14", "parent": "root", "length": 30.1, "rotation": 90.7, "x": 0.12, "y": 77.23}, {"name": "bone15", "parent": "root", "length": 18.08, "rotation": 90, "x": 0.07, "y": 46.15}, {"name": "bone16", "parent": "root", "length": 20.86, "rotation": 90, "x": 1.25, "y": 139.86}, {"name": "bone17", "parent": "root", "length": 38.43, "rotation": 90, "x": 37.32, "y": -0.14, "scaleX": 2.001, "scaleY": 2.001}, {"name": "bone18", "parent": "root", "length": 28.5, "rotation": 90, "x": 0.72, "y": 0.54}, {"name": "bone19", "parent": "root", "length": 28.5, "rotation": 90, "x": 0.72, "y": 0.54}, {"name": "bone20", "parent": "root", "length": 38.43, "rotation": 90, "x": 37.32, "y": -0.14, "scaleX": 2.001, "scaleY": 2.001}, {"name": "bone21", "parent": "root", "length": 64.8, "rotation": 90}, {"name": "bone22", "parent": "root", "length": 126.77, "rotation": 90, "scaleX": 2, "scaleY": 2}, {"name": "bone24", "parent": "root", "length": 126.73, "rotation": 90, "x": -0.09, "y": -1.72}, {"name": "bone23", "parent": "bone24", "length": 149.84, "x": -0.03, "y": 0.01, "scaleX": 2, "scaleY": 2}], "slots": [{"name": "blackpoint", "bone": "root", "color": "ffffff89", "attachment": "blackpoint"}, {"name": "cloud1", "bone": "bone3", "attachment": "cloud9"}, {"name": "cloud2", "bone": "bone4", "attachment": "cloud9"}, {"name": "cloud0", "bone": "bone2", "attachment": "cloud9"}, {"name": "wineffect1", "bone": "bone5", "attachment": "wineffect1"}, {"name": "jackpottext0", "bone": "bone", "attachment": "jackpottext8"}, {"name": "wineffect2", "bone": "root", "attachment": "wineffect2"}, {"name": "wineffect5", "bone": "root", "attachment": "wineffect5"}, {"name": "wineffect3", "bone": "root", "attachment": "wineffect3"}, {"name": "wineffect6", "bone": "root", "attachment": "wineffect3", "blend": "additive"}, {"name": "wineffect4", "bone": "root", "attachment": "wineffect4"}, {"name": "wineffect7", "bone": "bone18", "attachment": "wineffect6"}, {"name": "wineffect8", "bone": "bone19", "attachment": "wineffect6", "blend": "additive"}, {"name": "cuonvai", "bone": "root", "attachment": "cuonvai"}, {"name": "boxtienthang", "bone": "bone6", "attachment": "boxtienthang"}, {"name": "cuonvai2", "bone": "root", "attachment": "cuonvai"}, {"name": "BIGWIN0", "bone": "bone15"}, {"name": "do<PERSON><PERSON>", "bone": "bone16", "attachment": "do<PERSON><PERSON>"}, {"name": "staff", "bone": "bone23", "attachment": "staff"}, {"name": "bonus0", "bone": "bone22", "attachment": "bonus0"}, {"name": "FB0", "bone": "bone17"}, {"name": "FB1", "bone": "bone20", "blend": "additive"}, {"name": "bonus1", "bone": "bone21", "attachment": "bonus1"}, {"name": "bn0", "bone": "root", "attachment": "bn0", "blend": "additive"}, {"name": "bn1", "bone": "root", "attachment": "bn1", "blend": "additive"}, {"name": "bn2", "bone": "root", "attachment": "bn2", "blend": "additive"}, {"name": "bn3", "bone": "root", "attachment": "bn3", "blend": "additive"}, {"name": "bn4", "bone": "root", "attachment": "bn4", "blend": "additive"}], "skins": {"default": {"BIGWIN0": {"BIGWIN1": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN2": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN3": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN4": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN5": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN6": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN7": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN8": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}, "BIGWIN9": {"x": 39.34, "y": -0.43, "rotation": -90, "width": 424, "height": 102}}, "FB0": {"FB0": {"x": 0.64, "y": 133.14, "rotation": -90, "width": 92, "height": 94}, "FB1": {"x": 2.4, "y": 107.89, "rotation": -90, "width": 148, "height": 104}, "FB2": {"x": 2.01, "y": 78.73, "rotation": -90, "width": 209, "height": 112}, "FB3": {"x": 0.64, "y": 55.72, "rotation": -90, "width": 250, "height": 122}, "FB4": {"x": 1.62, "y": 33.69, "rotation": -90, "width": 299, "height": 124}, "FB5": {"x": 1.51, "y": 32.91, "rotation": -90, "width": 328, "height": 125}, "FB6": {"x": 2.24, "y": 32.89, "rotation": -90, "width": 328, "height": 123}, "FB7": {"x": 3.45, "y": 24.87, "rotation": -90, "width": 312, "height": 121}, "FB8": {"x": 4.25, "y": 13.64, "rotation": -90, "width": 287, "height": 117}, "FB9": {"x": -2.17, "y": 10.83, "rotation": -90, "width": 283, "height": 129}, "FB10": {"x": 1.04, "y": -35.68, "rotation": -90, "width": 186, "height": 112}, "FB11": {"x": 0.64, "y": -32.88, "rotation": -90, "width": 192, "height": 112}, "FB12": {"x": 9.46, "y": -28.87, "rotation": -90, "width": 187, "height": 90}}, "FB1": {"FB0": {"x": 0.64, "y": 133.14, "rotation": -90, "width": 92, "height": 94}, "FB1": {"x": 2.4, "y": 107.89, "rotation": -90, "width": 148, "height": 104}, "FB2": {"x": 2.01, "y": 78.73, "rotation": -90, "width": 209, "height": 112}, "FB3": {"x": 0.64, "y": 55.72, "rotation": -90, "width": 250, "height": 122}, "FB4": {"x": 1.62, "y": 33.69, "rotation": -90, "width": 299, "height": 124}, "FB5": {"x": 1.51, "y": 32.91, "rotation": -90, "width": 328, "height": 125}, "FB6": {"x": 2.24, "y": 32.89, "rotation": -90, "width": 328, "height": 123}, "FB7": {"x": 3.45, "y": 24.87, "rotation": -90, "width": 312, "height": 121}, "FB8": {"x": 4.25, "y": 13.64, "rotation": -90, "width": 287, "height": 117}, "FB9": {"x": -2.17, "y": 10.83, "rotation": -90, "width": 283, "height": 129}, "FB10": {"x": 1.04, "y": -35.68, "rotation": -90, "width": 186, "height": 112}, "FB11": {"x": 0.64, "y": -32.88, "rotation": -90, "width": 192, "height": 112}, "FB12": {"x": 9.46, "y": -28.87, "rotation": -90, "width": 187, "height": 90}}, "blackpoint": {"blackpoint": {"scaleX": 1000, "scaleY": 1000, "width": 2, "height": 2}}, "bn0": {"bn0": {"x": -279.98, "y": 5.02, "width": 138, "height": 160}}, "bn1": {"bn1": {"x": -135.02, "y": 4.75, "width": 138, "height": 160}}, "bn2": {"bn2": {"x": 4.26, "y": -0.4, "width": 138, "height": 160}}, "bn3": {"bn3": {"x": 147.06, "y": 4.94, "width": 138, "height": 160}}, "bn4": {"bn4": {"x": 283.97, "y": 5.05, "width": 138, "height": 160}}, "bonus0": {"bonus0": {"type": "mesh", "hull": 4, "width": 208, "height": 211, "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [104.48, 100.23, 104.48, -107.77, -106.52, -107.77, -106.52, 100.23], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "bonus1": {"bonus1": {"type": "mesh", "hull": 4, "width": 710, "height": 166, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-83, -355, -83, 355, 83, 355, 83, -355], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "boxtienthang": {"boxtienthang": {"type": "mesh", "hull": 4, "width": 621, "height": 130, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-69.14, -309.68, -69.14, 311.32, 60.86, 311.32, 60.86, -309.68], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "cloud0": {"cloud1": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud3": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud5": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud7": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud9": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud11": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}}, "cloud1": {"cloud1": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud3": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud5": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud7": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud9": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud11": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}}, "cloud2": {"cloud1": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud3": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud5": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud7": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud9": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}, "cloud11": {"x": -1.82, "y": 5.42, "rotation": -90, "width": 334, "height": 181}}, "cuonvai": {"cuonvai": {"type": "mesh", "hull": 4, "width": 85, "height": 334, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, -70.21, -27.83, 1, 1, 8, -62.18, 56.79, 1, 1, 8, 270.33, 25.23, 1, 1, 8, 262.3, -59.39, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "cuonvai2": {"cuonvai": {"type": "mesh", "hull": 4, "width": 85, "height": 334, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -48.54, 33.65, 1, 1, 9, -40.98, -51.18, 1, 1, 9, 291.7, -21.54, 1, 1, 9, 284.14, 63.29, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "domdom": {"domdom": {"type": "mesh", "hull": 4, "width": 634, "height": 101, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-64.71, -314.92, -64.71, 319.08, 36.29, 319.08, 36.29, -314.92], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "jackpottext0": {"jackpottext1": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}, "jackpottext2": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}, "jackpottext3": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}, "jackpottext4": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}, "jackpottext5": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}, "jackpottext6": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}, "jackpottext7": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}, "jackpottext8": {"x": 0.5, "rotation": -90, "width": 772, "height": 133}}, "staff": {"staff": {"type": "mesh", "hull": 4, "width": 28, "height": 658, "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [345.2, 14.09, 345.2, -13.91, -312.8, -13.91, -312.8, 14.09], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "wineffect1": {"wineffect1": {"type": "mesh", "hull": 4, "width": 995, "height": 208, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-93.27, -498.69, -93.27, 496.31, 114.73, 496.31, 114.73, -498.69], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "wineffect2": {"wineffect2": {"type": "mesh", "hull": 28, "width": 864, "height": 282, "uvs": [0, 0, 0.04668, 0.06391, 0.11157, 0.122, 0.17866, 0.16668, 0.26105, 0.19796, 0.35001, 0.21583, 0.4448, 0.22477, 0.52647, 0.22477, 0.61397, 0.21136, 0.71386, 0.18455, 0.81303, 0.15104, 0.89689, 0.1086, 0.95886, 0.05721, 1, 0, 0.97345, 0.84806, 0.91511, 0.88604, 0.85168, 0.92625, 0.77803, 0.94636, 0.70803, 0.9754, 0.64095, 0.99327, 0.55126, 1, 0.45209, 1, 0.36897, 1, 0.29241, 0.97987, 0.22168, 0.94413, 0.13272, 0.89274, 0.05907, 0.86147, 0, 0.81232], "triangles": [5, 25, 27, 25, 26, 27, 27, 1, 2, 27, 0, 1, 24, 25, 6, 27, 3, 4, 27, 2, 3, 21, 6, 7, 21, 22, 6, 24, 6, 22, 22, 23, 24, 6, 25, 5, 27, 4, 5, 19, 20, 7, 7, 20, 21, 18, 19, 7, 17, 7, 8, 14, 8, 9, 18, 7, 17, 15, 16, 17, 15, 17, 8, 9, 10, 14, 14, 15, 8, 10, 11, 14, 14, 12, 13, 11, 12, 14], "vertices": [1, 8, 199.73, -2.9, 1, 2, 10, 211.07, 24.79, 0.11975, 8, 177.98, -41.35, 0.88025, 3, 11, 278.09, 142.38, 0.00148, 10, 167.35, -13.96, 0.63833, 8, 156.37, -95.62, 0.36019, 3, 11, 240.11, 96.82, 0.06973, 10, 125.83, -56.32, 0.90874, 8, 138.35, -152.13, 0.02153, 2, 11, 199.36, 37.79, 0.42028, 10, 80.47, -111.89, 0.57972, 3, 12, 51.64, 245.59, 0.02321, 11, 159.34, -28.02, 0.88181, 10, 35.29, -174.28, 0.09498, 2, 12, 83.32, 170.02, 0.27932, 11, 119.22, -99.47, 0.72068, 2, 12, 112.59, 105.82, 0.67491, 11, 86.58, -162.03, 0.32509, 3, 13, 46.97, 181.74, 0.02333, 12, 147.4, 38.6, 0.93442, 11, 54.97, -230.8, 0.04225, 2, 13, 95.64, 110.06, 0.50667, 12, 190.08, -36.79, 0.49333, 2, 13, 145.66, 39.86, 0.9594, 12, 234.23, -110.83, 0.0406, 2, 9, 183.07, 90.6, 0.568, 13, 191.43, -17.56, 0.432, 2, 9, 202.26, 38.55, 0.616, 13, 230.18, -57.26, 0.384, 1, 9, 221.48, 4.58, 1, 1, 9, -18.77, 6.21, 1, 2, 9, -33.91, 55.46, 0.528, 13, 7.68, -138.19, 0.472, 2, 9, -50.06, 109.05, 0.616, 13, -28.94, -95.86, 0.384, 3, 9, -61.36, 171.93, 0.064, 13, -64.91, -43.06, 0.86575, 12, 17.61, -176.36, 0.07025, 2, 13, -101.54, 5.75, 0.5833, 12, -14.93, -124.73, 0.4167, 3, 13, -134.2, 53.9, 0.21962, 12, -43.56, -74.09, 0.77834, 11, -151.31, -149.47, 0.00205, 3, 13, -173.63, 120.64, 0.00958, 12, -77.43, -4.37, 0.80496, 11, -117.15, -79.89, 0.18546, 3, 12, -112.98, 73.59, 0.23203, 11, -77.51, -3.93, 0.76648, 10, -198.86, -131.23, 0.00149, 3, 12, -142.78, 138.94, 0.00544, 11, -44.29, 59.74, 0.88339, 10, -160.63, -70.43, 0.11117, 2, 11, -8.66, 115.77, 0.56212, 10, -120.61, -17.45, 0.43788, 3, 11, 28.54, 165.28, 0.18383, 10, -79.55, 28.92, 0.79819, 8, -83.42, -168.42, 0.01798, 3, 11, 76.94, 226.72, 0.00113, 10, -26.37, 86.27, 0.72129, 8, -61.73, -93.27, 0.27758, 2, 10, 14.97, 135.44, 0.2482, 8, -46.94, -30.76, 0.7518, 2, 10, 53.87, 171.27, 0.00446, 8, -28.32, 18.74, 0.99554], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54]}}, "wineffect3": {"wineffect3": {"type": "mesh", "hull": 4, "width": 624, "height": 164, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [312, 3.69, -312, 3.69, -312, 167.69, 312, 167.69], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "wineffect4": {"wineffect4": {"type": "mesh", "hull": 4, "width": 323, "height": 93, "uvs": [0, 0, 1, 0, 1, 1, 0, 1], "triangles": [3, 0, 1, 3, 1, 2], "vertices": [-322.92, 176.63, 323.08, 176.63, 323.08, -9.37, -322.92, -9.37], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "wineffect5": {"wineffect5": {"type": "mesh", "hull": 20, "width": 621, "height": 183, "uvs": [1, 1, 0.875, 1, 0.75, 1, 0.625, 1, 0.5, 1, 0.375, 1, 0.25, 1, 0.125, 1, 0, 1, 0, 0.5, 0, 0, 0.125, 0, 0.25, 0, 0.375, 0, 0.5, 0, 0.625, 0, 0.75, 0, 0.875, 0, 1, 0, 1, 0.5, 0.875, 0.5, 0.75, 0.5, 0.625, 0.5, 0.5, 0.5, 0.375, 0.5, 0.25, 0.5, 0.125, 0.5], "triangles": [21, 17, 16, 20, 17, 21, 2, 20, 21, 1, 20, 2, 20, 18, 17, 19, 18, 20, 1, 19, 20, 0, 19, 1, 23, 15, 14, 22, 15, 23, 4, 22, 23, 3, 22, 4, 22, 16, 15, 21, 16, 22, 3, 21, 22, 2, 21, 3, 25, 12, 26, 6, 25, 7, 25, 13, 12, 24, 13, 25, 6, 24, 25, 5, 24, 6, 24, 14, 13, 23, 14, 24, 5, 23, 24, 4, 23, 5, 9, 11, 10, 26, 11, 9, 8, 26, 9, 7, 26, 8, 26, 12, 11, 7, 25, 26], "vertices": [1, 13, -6.58, -98.21, 1, 2, 12, 39.06, -165.44, 0.03095, 13, -44.42, -30.43, 0.96905, 2, 12, 6.86, -94.81, 0.52929, 13, -82.26, 37.34, 0.47071, 3, 11, -100.75, -133.15, 0.00238, 12, -25.35, -24.18, 0.96911, 13, -120.1, 105.12, 0.02851, 2, 11, -64.85, -64.33, 0.40775, 12, -57.55, 46.45, 0.59225, 3, 10, -149.77, -126.74, 0.00108, 11, -28.94, 4.49, 0.96295, 12, -89.76, 117.08, 0.03596, 2, 10, -108.45, -61.03, 0.24184, 11, 6.97, 73.31, 0.75816, 2, 10, -67.13, 4.69, 0.81543, 11, 42.87, 142.13, 0.18457, 1, 10, -25.81, 70.4, 1, 1, 10, 51.65, 21.69, 1, 2, 10, 129.11, -27.01, 0.9752, 11, 241.03, 126.3, 0.0248, 2, 10, 87.79, -92.73, 0.68101, 11, 205.12, 57.48, 0.31899, 3, 10, 46.47, -158.44, 0.15301, 11, 169.21, -11.34, 0.83836, 12, 44.55, 263.63, 0.00863, 3, 10, 5.15, -224.15, 8e-05, 11, 133.31, -80.16, 0.82495, 12, 76.75, 193, 0.17497, 2, 11, 97.4, -148.98, 0.42464, 12, 108.95, 122.37, 0.57536, 3, 11, 61.49, -217.8, 0.07779, 12, 141.16, 51.74, 0.91848, 13, 39.68, 194.33, 0.00373, 2, 12, 173.36, -18.89, 0.70447, 13, 77.52, 126.55, 0.29553, 2, 12, 205.57, -89.52, 0.151, 13, 115.36, 58.77, 0.849, 1, 13, 153.2, -9.01, 1, 1, 13, 73.31, -53.61, 1, 2, 12, 122.31, -127.48, 0.04082, 13, 35.47, 14.17, 0.95918, 2, 12, 90.11, -56.85, 0.63238, 13, -2.37, 81.95, 0.36762, 2, 11, -19.63, -175.48, 0.02068, 12, 57.9, 13.78, 0.97932, 2, 11, 16.28, -106.66, 0.41492, 12, 25.7, 84.41, 0.58508, 2, 11, 52.18, -37.84, 0.89486, 12, -6.5, 155.04, 0.10514, 2, 10, -30.99, -109.73, 0.14731, 11, 88.09, 30.98, 0.85269, 2, 10, 10.33, -44.02, 0.77818, 11, 124, 99.81, 0.22182], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 0]}}, "wineffect6": {"wineffect3": {"type": "mesh", "hull": 4, "width": 624, "height": 164, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [312, 3.69, -312, 3.69, -312, 167.69, 312, 167.69], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "wineffect7": {"wineffect6": {"x": -0.54, "y": 0.22, "rotation": -90, "width": 899, "height": 120}}, "wineffect8": {"wineffect6": {"x": -0.54, "y": 0.22, "rotation": -90, "width": 899, "height": 120}}}}, "animations": {"Bigwin": {"slots": {"BIGWIN0": {"attachment": [{"time": 1.3333, "name": "BIGWIN1"}, {"time": 1.4, "name": "BIGWIN2"}, {"time": 1.4667, "name": "BIGWIN3"}, {"time": 1.5333, "name": "BIGWIN4"}, {"time": 1.6, "name": "BIGWIN5"}, {"time": 1.6667, "name": "BIGWIN6"}, {"time": 1.7333, "name": "BIGWIN7"}, {"time": 1.8, "name": "BIGWIN8"}, {"time": 1.8667, "name": "BIGWIN9"}, {"time": 1.9333, "name": null}, {"time": 2.4667, "name": "BIGWIN1"}, {"time": 2.5333, "name": "BIGWIN2"}, {"time": 2.6, "name": "BIGWIN3"}, {"time": 2.6667, "name": "BIGWIN4"}, {"time": 2.7333, "name": "BIGWIN5"}, {"time": 2.8, "name": "BIGWIN6"}, {"time": 2.8667, "name": "BIGWIN7"}, {"time": 2.9333, "name": "BIGWIN8"}, {"time": 3, "name": "BIGWIN9"}, {"time": 3.0667, "name": null}, {"time": 3.6, "name": "BIGWIN1"}, {"time": 3.6667, "name": "BIGWIN2"}, {"time": 3.7333, "name": "BIGWIN3"}, {"time": 3.8, "name": "BIGWIN4"}, {"time": 3.8667, "name": "BIGWIN5"}, {"time": 3.9333, "name": "BIGWIN6"}, {"time": 4, "name": "BIGWIN7"}, {"time": 4.0667, "name": "BIGWIN8"}, {"time": 4.1333, "name": "BIGWIN9"}, {"time": 4.2, "name": null}, {"time": 4.7333, "name": "BIGWIN1"}, {"time": 4.8, "name": "BIGWIN2"}, {"time": 4.8667, "name": "BIGWIN3"}, {"time": 4.9333, "name": "BIGWIN4"}, {"time": 5, "name": "BIGWIN5"}, {"time": 5.0667, "name": "BIGWIN6"}, {"time": 5.1333, "name": "BIGWIN7"}, {"time": 5.2, "name": "BIGWIN8"}, {"time": 5.2667, "name": "BIGWIN9"}, {"time": 5.3333, "name": null}, {"time": 5.8667, "name": "BIGWIN1"}, {"time": 5.9333, "name": "BIGWIN2"}, {"time": 6, "name": "BIGWIN3"}, {"time": 6.0667, "name": "BIGWIN4"}, {"time": 6.1333, "name": "BIGWIN5"}, {"time": 6.2, "name": "BIGWIN6"}, {"time": 6.2667, "name": "BIGWIN7"}, {"time": 6.3333, "name": "BIGWIN8"}, {"time": 6.4, "name": "BIGWIN9"}, {"time": 6.5333, "name": null}]}, "bn0": {"attachment": [{"time": 0, "name": null}]}, "bn1": {"attachment": [{"time": 0, "name": null}]}, "bn2": {"attachment": [{"time": 0, "name": null}]}, "bn3": {"attachment": [{"time": 0, "name": null}]}, "bn4": {"attachment": [{"time": 0, "name": null}]}, "bonus0": {"attachment": [{"time": 0, "name": null}]}, "bonus1": {"attachment": [{"time": 0, "name": null}]}, "cloud0": {"attachment": [{"time": 0, "name": null}]}, "cloud1": {"attachment": [{"time": 0, "name": null}]}, "cloud2": {"attachment": [{"time": 0, "name": null}]}, "domdom": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}]}, "jackpottext0": {"attachment": [{"time": 0, "name": null}]}, "staff": {"attachment": [{"time": 0, "name": null}]}, "wineffect1": {"attachment": [{"time": 0, "name": null}]}, "wineffect3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}]}, "wineffect4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.4, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}]}, "wineffect5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "fffffffe"}]}, "wineffect6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffff37"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.4333, "color": "ffffff00"}, {"time": 2.7, "color": "ffffff37"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 3.5667, "color": "ffffff00"}, {"time": 3.8333, "color": "ffffff37"}, {"time": 4.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 4.7, "color": "ffffff00"}, {"time": 4.9667, "color": "ffffff37"}, {"time": 5.2, "color": "ffffff00", "curve": "stepped"}, {"time": 5.8333, "color": "ffffff00"}, {"time": 6.1, "color": "ffffff37"}, {"time": 6.3333, "color": "ffffff00"}]}, "wineffect7": {"attachment": [{"time": 0, "name": null}]}, "wineffect8": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone8": {"rotate": [{"time": 0, "angle": -5.48}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": -21.17, "y": -383.66}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": 13.1, "y": -0.87}, {"time": 1, "x": 0, "y": 0}, {"time": 1.3333, "x": 13.1, "y": -0.87}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2, "x": 13.1, "y": -0.87}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.6667, "x": 13.1, "y": -0.87}, {"time": 3, "x": 0, "y": 0}, {"time": 3.3333, "x": 13.1, "y": -0.87}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 4, "x": 13.1, "y": -0.87}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.6667, "x": 13.1, "y": -0.87}, {"time": 5, "x": 0, "y": 0}, {"time": 5.3333, "x": 13.1, "y": -0.87}, {"time": 5.6667, "x": 0, "y": 0}, {"time": 6, "x": 13.1, "y": -0.87}, {"time": 6.3333, "x": 0, "y": 0}, {"time": 6.6667, "x": 13.1, "y": -0.87}]}, "bone9": {"rotate": [{"time": 0, "angle": 6.64}, {"time": 0.3333, "angle": 0}], "translate": [{"time": 0, "x": -20.11, "y": 379.47}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.6667, "x": 19.21, "y": -0.87}, {"time": 1, "x": 0, "y": 0}, {"time": 1.3333, "x": 19.21, "y": -0.87}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2, "x": 19.21, "y": -0.87}, {"time": 2.3333, "x": 0, "y": 0}, {"time": 2.6667, "x": 19.21, "y": -0.87}, {"time": 3, "x": 0, "y": 0}, {"time": 3.3333, "x": 19.21, "y": -0.87}, {"time": 3.6667, "x": 0, "y": 0}, {"time": 4, "x": 19.21, "y": -0.87}, {"time": 4.3333, "x": 0, "y": 0}, {"time": 4.6667, "x": 19.21, "y": -0.87}, {"time": 5, "x": 0, "y": 0}, {"time": 5.3333, "x": 19.21, "y": -0.87}, {"time": 5.6667, "x": 0, "y": 0}, {"time": 6, "x": 19.21, "y": -0.87}, {"time": 6.3333, "x": 0, "y": 0}, {"time": 6.6667, "x": 19.21, "y": -0.87}]}, "bone7": {"translate": [{"time": 0, "x": 0, "y": -57.04}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 1.1333, "x": 0, "y": 26.05}, {"time": 1.9, "x": 0, "y": 0}, {"time": 2.7, "x": 0, "y": 26.05}, {"time": 3.5, "x": 0, "y": 0}, {"time": 4.3, "x": 0, "y": 26.05}, {"time": 5.0667, "x": 0, "y": 0}, {"time": 5.8667, "x": 0, "y": 26.05}, {"time": 6.6667, "x": 0, "y": 0}], "scale": [{"time": 0.3333, "x": 1, "y": 1}, {"time": 1.1333, "x": 0.917, "y": 0.917}, {"time": 1.9, "x": 1, "y": 1}, {"time": 2.7, "x": 0.917, "y": 0.917}, {"time": 3.5, "x": 1, "y": 1}, {"time": 4.3, "x": 0.917, "y": 0.917}, {"time": 5.0667, "x": 1, "y": 1}, {"time": 5.8667, "x": 0.917, "y": 0.917}, {"time": 6.6667, "x": 1, "y": 1}]}, "bone10": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 22.95, "y": -3.17}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 22.95, "y": -3.17}, {"time": 1.6667, "x": 0.45, "y": -4.67}, {"time": 2, "x": 29.37, "y": -0.55}, {"time": 2.3333, "x": 22.95, "y": -3.17}, {"time": 3, "x": 0.45, "y": -4.67}, {"time": 3.6667, "x": 22.95, "y": -3.17}, {"time": 4.3333, "x": 0.45, "y": -4.67}, {"time": 5, "x": 22.95, "y": -3.17}, {"time": 5.6667, "x": 0.45, "y": -4.67}, {"time": 6, "x": 20.96, "y": 0.29}, {"time": 6.3333, "x": 22.95, "y": -3.17}, {"time": 6.6667, "x": 0, "y": 0}]}, "bone11": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 33.71, "y": 0.69}, {"time": 0.5, "x": 0, "y": 0}, {"time": 1, "x": 35.7, "y": -0.18}, {"time": 1.3333, "x": 16.89, "y": -0.18}, {"time": 1.6667, "x": 9.18, "y": 1.2}, {"time": 2, "x": 22.44, "y": 0.51}, {"time": 2.3333, "x": 19.07, "y": 1.2}, {"time": 3, "x": -1.91, "y": -0.18}, {"time": 3.6667, "x": 35.7, "y": -0.18}, {"time": 4.3333, "x": 22.35, "y": -0.18}, {"time": 4.6667, "x": 9.62, "y": 2.59}, {"time": 5, "x": 35.7, "y": -0.18}, {"time": 5.6667, "x": -1.91, "y": -0.18}, {"time": 6.3333, "x": 35.7, "y": -0.18}, {"time": 6.6667, "x": 0, "y": 0}]}, "bone12": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 26.91, "y": -0.79}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.8333, "x": 19.05, "y": -1.66}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.5, "x": 19.05, "y": -1.66}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 2.1667, "x": 19.05, "y": -1.66}, {"time": 2.5, "x": 0, "y": 0}, {"time": 2.8333, "x": 19.05, "y": -1.66}, {"time": 3.1667, "x": 0, "y": 0}, {"time": 3.5, "x": 19.05, "y": -1.66}, {"time": 3.8333, "x": 0, "y": 0}, {"time": 4.1667, "x": 19.05, "y": -1.66}, {"time": 4.5, "x": 0, "y": 0}, {"time": 4.8333, "x": 19.05, "y": -1.66}, {"time": 5.1667, "x": 0, "y": 0}, {"time": 5.5, "x": 19.05, "y": -1.66}, {"time": 5.8333, "x": 0, "y": 0}, {"time": 6.1667, "x": 19.05, "y": -1.66}, {"time": 6.5, "x": 0, "y": 0}]}, "bone13": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 15.61, "y": -2.04}, {"time": 0.5, "x": 4.37, "y": -2.62}, {"time": 1, "x": 5.52, "y": -4.56}, {"time": 1.6667, "x": 15.61, "y": -2.04}, {"time": 2, "x": 24.03, "y": -7.92}, {"time": 2.3333, "x": 15.61, "y": -2.04, "curve": "stepped"}, {"time": 3, "x": 15.61, "y": -2.04}, {"time": 3.3333, "x": 0.47, "y": -2.04}, {"time": 3.6667, "x": 15.61, "y": -2.04, "curve": "stepped"}, {"time": 4.3333, "x": 15.61, "y": -2.04}, {"time": 4.6667, "x": 4.68, "y": -3.72}, {"time": 5, "x": 15.61, "y": -2.04}, {"time": 5.6667, "x": 1.31, "y": -4.56}, {"time": 6.3333, "x": 15.61, "y": -2.04}, {"time": 6.6667, "x": 0, "y": 0}]}, "bone14": {"translate": [{"time": 0, "x": 1.17, "y": -96.08, "curve": "stepped"}, {"time": 0.3333, "x": 1.17, "y": -96.08}, {"time": 0.4, "x": 1.3, "y": -106.98}, {"time": 0.5333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.849, "y": 0.849, "curve": "stepped"}, {"time": 0.3333, "x": 0.849, "y": 0.849}, {"time": 0.5333, "x": 1, "y": 1}]}, "bone15": {"translate": [{"time": 0, "x": 0, "y": -16.83}], "scale": [{"time": 0, "x": 1.432, "y": 1.432}]}, "bone16": {"translate": [{"time": 0.6333, "x": 0, "y": 0}, {"time": 1.1667, "x": 0, "y": 63.24}], "scale": [{"time": 0.6333, "x": 1, "y": 1}, {"time": 1.1667, "x": 1.116, "y": 1.116}]}, "bone6": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.5, "x": 1.311, "y": 1.16}, {"time": 0.5667, "x": 1, "y": 1}, {"time": 0.6, "x": 1.134, "y": 1.043}, {"time": 0.6333, "x": 1, "y": 1}]}}, "deform": {"default": {"wineffect2": {"wineffect2": [{"time": 0, "offset": 6, "vertices": [-72.92785, -128.81808, -83.04173, -122.5417, -4.93294, -147.94666, -114.94331, -186.43182, -129.55034, -176.59366, -15.46777, -218.47086, -90.44469, -152.42847, -102.39884, -144.66843, 38.8647, -95.23473, -51.2183, -89.20084, -58.21937, -84.79684, 13.08246, -32.02455, -17.21498, -30.00607, -5.49371, 7.8079, 2.78059, 9.13307, -36.91314, 55.29114, -32.29607, 58.10841, 25.97482, 61.1959, -77.88824, 119.06119, -67.95151, 124.99797, -119.61374, 187.78503, -103.95264, 196.88634, 8.41248, 77.20377, -32.21461, 70.66461, 11.23688, 115.44154, -49.32338, 104.97797, 12.9442, 3.44167, 47.87058, -4.56113, 38.7947, 124.62062, -30.324, 126.94876, 47.61243, 39.16762, 20.91623, 57.99657, 35.17184, 201.59737, -72.76715, 191.26857, -56.97747, 196.55005, -40.01116, 140.49965, -28.45703, 143.28679, -17.95015, 90.25764, -10.55276, 91.41764, 65.61282, 64.52522, -23.91914, 25.05266, -21.80295, 26.91374, 7.82248, 33.74234, 3.85723, -36.23123, -26.20471, -25.31581, -28.15307, -23.12714, 34.98275, -79.04996, -40.84021, -76.18879, -46.82909, -72.66, -67.40248, -126.57735, -77.3541, -120.75156, -96.46735, -178.91693, -110.53014, -170.58679, -2.57754, -203.24994, -79.34126, -142.10667, -90.5018, -135.27191, -4.45841, -162.69423, -26.27348, -50.44229, 4.66113, -56.68375, -12.20981, -16.82016, -1.36376, -20.74088]}, {"time": 0.3333}]}, "wineffect5": {"wineffect5": [{"time": 0, "vertices": [-131.84898, 236.16888, -85.04928, 186.53041, -99.93246, 178.9997, -57.88612, 126.95622, -68.01658, 121.8306, 34.2537, 65.65756, -30.72296, 67.3819, -36.10067, 64.66165, 3.96773, 7.60793, -3.55879, 7.80767, -30.28467, -48.164, -26.31815, -50.44162, 23.60567, -51.76647, -65.13744, -103.59247, -56.60481, -108.49088, -99.9904, -159.02051, -86.89163, -166.54053, -134.84314, -214.44879, -134.84314, -214.44879, -134.84311, -214.44879, -117.17834, -224.58957, -99.99034, -159.02045, -86.89161, -166.54044, -65.1373, -103.59219, -56.60477, -108.49091, 50.76935, -111.3411, -35.68082, -54.84872, -31.16013, -57.53864, 26.22691, -59.94798, 3.96779, 7.60796, -3.55878, 7.80764, 34.25381, 65.65746, -30.72289, 67.38198, -36.10054, 64.66144, -57.88625, 126.95631, -68.01688, 121.83051, -85.04935, 186.5304, -99.93262, 178.99973, -131.84895, 236.16888, -131.84894, 236.16882, -75.64457, 176.85143, -89.77103, 170.11713, -48.93062, 121.91276, -58.67949, 117.53189, 33.67696, 54.72798, -22.45927, 60.20695, 0, 0, 0, 0, -28.73672, -56.16681, 26.63435, -57.19383, -59.22845, -99.88324, -51.01227, -104.3192, -89.09856, -145.49094, -77.12133, -152.17929]}, {"time": 0.3333}]}}}}, "Jackpot": {"slots": {"bn0": {"attachment": [{"time": 0, "name": null}]}, "bn1": {"attachment": [{"time": 0, "name": null}]}, "bn2": {"attachment": [{"time": 0, "name": null}]}, "bn3": {"attachment": [{"time": 0, "name": null}]}, "bn4": {"attachment": [{"time": 0, "name": null}]}, "bonus0": {"attachment": [{"time": 0, "name": null}]}, "bonus1": {"attachment": [{"time": 0, "name": null}]}, "boxtienthang": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff"}]}, "cloud0": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "cloud11"}, {"time": 0.2, "name": "cloud1"}, {"time": 0.3333, "name": "cloud3"}, {"time": 0.4667, "name": "cloud5"}, {"time": 0.6, "name": "cloud7"}, {"time": 0.7333, "name": "cloud9"}, {"time": 0.8667, "name": "cloud11"}, {"time": 1, "name": "cloud1"}, {"time": 1.1333, "name": "cloud3"}, {"time": 1.2667, "name": "cloud5"}, {"time": 1.4, "name": "cloud7"}, {"time": 1.5333, "name": "cloud9"}, {"time": 1.6667, "name": "cloud11"}, {"time": 1.8, "name": "cloud1"}, {"time": 1.9333, "name": "cloud3"}, {"time": 2.0667, "name": "cloud5"}, {"time": 2.2, "name": "cloud7"}, {"time": 2.3333, "name": "cloud9"}, {"time": 2.4667, "name": "cloud11"}, {"time": 2.6, "name": "cloud1"}, {"time": 2.7333, "name": "cloud3"}, {"time": 2.8667, "name": "cloud5"}, {"time": 3, "name": "cloud7"}, {"time": 3.1333, "name": "cloud9"}, {"time": 3.2667, "name": "cloud11"}, {"time": 3.4, "name": "cloud1"}, {"time": 3.5333, "name": "cloud3"}, {"time": 3.6667, "name": "cloud5"}, {"time": 3.8, "name": "cloud7"}, {"time": 3.9333, "name": "cloud9"}, {"time": 4.0667, "name": "cloud11"}, {"time": 4.2, "name": "cloud1"}, {"time": 4.3333, "name": "cloud3"}, {"time": 4.4667, "name": "cloud5"}, {"time": 4.6, "name": "cloud7"}, {"time": 4.7333, "name": "cloud9"}, {"time": 4.8667, "name": "cloud11"}, {"time": 5, "name": "cloud1"}, {"time": 5.1333, "name": "cloud3"}, {"time": 5.2667, "name": "cloud5"}, {"time": 5.4, "name": "cloud7"}, {"time": 5.5333, "name": "cloud9"}, {"time": 5.6667, "name": "cloud11"}, {"time": 5.8, "name": "cloud1"}, {"time": 5.9333, "name": "cloud3"}, {"time": 6.0667, "name": "cloud5"}, {"time": 6.2, "name": "cloud7"}, {"time": 6.3333, "name": "cloud9"}, {"time": 6.4667, "name": "cloud11"}, {"time": 6.6, "name": "cloud1"}, {"time": 6.7333, "name": "cloud3"}, {"time": 6.8667, "name": "cloud5"}, {"time": 7, "name": "cloud7"}, {"time": 7.1333, "name": "cloud9"}, {"time": 7.2667, "name": "cloud11"}, {"time": 7.4, "name": "cloud1"}, {"time": 7.5333, "name": "cloud3"}, {"time": 7.6667, "name": "cloud5"}, {"time": 7.8, "name": "cloud7"}, {"time": 7.9333, "name": "cloud9"}, {"time": 8.0667, "name": "cloud11"}, {"time": 8.2, "name": "cloud1"}, {"time": 8.3333, "name": "cloud3"}, {"time": 8.4667, "name": "cloud5"}, {"time": 8.6, "name": "cloud7"}, {"time": 8.7333, "name": "cloud9"}, {"time": 8.8667, "name": "cloud11"}, {"time": 9, "name": "cloud1"}, {"time": 9.1333, "name": "cloud3"}, {"time": 9.2667, "name": "cloud5"}, {"time": 9.4, "name": "cloud7"}, {"time": 9.5333, "name": "cloud9"}, {"time": 9.6667, "name": "cloud11"}, {"time": 9.8, "name": "cloud1"}, {"time": 9.9333, "name": "cloud3"}, {"time": 10.0667, "name": "cloud5"}, {"time": 10.2, "name": "cloud7"}, {"time": 10.3333, "name": "cloud9"}, {"time": 10.4667, "name": "cloud11"}, {"time": 10.6, "name": "cloud1"}, {"time": 10.7333, "name": "cloud3"}, {"time": 10.8667, "name": "cloud5"}, {"time": 11, "name": "cloud7"}, {"time": 11.1333, "name": "cloud9"}]}, "cloud1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0.0667, "name": "cloud1"}, {"time": 0.2, "name": "cloud3"}, {"time": 0.3333, "name": "cloud5"}, {"time": 0.4667, "name": "cloud7"}, {"time": 0.6, "name": "cloud9"}, {"time": 0.7333, "name": "cloud11"}, {"time": 0.8667, "name": "cloud1"}, {"time": 1, "name": "cloud3"}, {"time": 1.1333, "name": "cloud5"}, {"time": 1.2667, "name": "cloud7"}, {"time": 1.4, "name": "cloud9"}, {"time": 1.5333, "name": "cloud11"}, {"time": 1.6667, "name": "cloud1"}, {"time": 1.8, "name": "cloud3"}, {"time": 1.9333, "name": "cloud5"}, {"time": 2.0667, "name": "cloud7"}, {"time": 2.2, "name": "cloud9"}, {"time": 2.3333, "name": "cloud11"}, {"time": 2.4667, "name": "cloud1"}, {"time": 2.6, "name": "cloud3"}, {"time": 2.7333, "name": "cloud5"}, {"time": 2.8667, "name": "cloud7"}, {"time": 3, "name": "cloud9"}, {"time": 3.1333, "name": "cloud11"}, {"time": 3.2667, "name": "cloud1"}, {"time": 3.4, "name": "cloud3"}, {"time": 3.5333, "name": "cloud5"}, {"time": 3.6667, "name": "cloud7"}, {"time": 3.8, "name": "cloud9"}, {"time": 3.9333, "name": "cloud11"}, {"time": 4.0667, "name": "cloud1"}, {"time": 4.2, "name": "cloud3"}, {"time": 4.3333, "name": "cloud5"}, {"time": 4.4667, "name": "cloud7"}, {"time": 4.6, "name": "cloud9"}, {"time": 4.7333, "name": "cloud11"}, {"time": 4.8667, "name": "cloud1"}, {"time": 5, "name": "cloud3"}, {"time": 5.1333, "name": "cloud5"}, {"time": 5.2667, "name": "cloud7"}, {"time": 5.4, "name": "cloud9"}, {"time": 5.5333, "name": "cloud11"}, {"time": 5.6667, "name": "cloud1"}, {"time": 5.8, "name": "cloud3"}, {"time": 5.9333, "name": "cloud5"}, {"time": 6.0667, "name": "cloud7"}, {"time": 6.2, "name": "cloud9"}, {"time": 6.3333, "name": "cloud11"}, {"time": 6.4667, "name": "cloud1"}, {"time": 6.6, "name": "cloud3"}, {"time": 6.7333, "name": "cloud5"}, {"time": 6.8667, "name": "cloud7"}, {"time": 7, "name": "cloud9"}, {"time": 7.1333, "name": "cloud11"}, {"time": 7.2667, "name": "cloud1"}, {"time": 7.4, "name": "cloud3"}, {"time": 7.5333, "name": "cloud5"}, {"time": 7.6667, "name": "cloud7"}, {"time": 7.8, "name": "cloud9"}, {"time": 7.9333, "name": "cloud11"}, {"time": 8.0667, "name": "cloud1"}, {"time": 8.2, "name": "cloud3"}, {"time": 8.3333, "name": "cloud5"}, {"time": 8.4667, "name": "cloud7"}, {"time": 8.6, "name": "cloud9"}, {"time": 8.7333, "name": "cloud11"}, {"time": 8.8667, "name": "cloud1"}, {"time": 9, "name": "cloud3"}, {"time": 9.1333, "name": "cloud5"}, {"time": 9.2667, "name": "cloud7"}, {"time": 9.4, "name": "cloud9"}, {"time": 9.5333, "name": "cloud11"}, {"time": 9.6667, "name": "cloud1"}, {"time": 9.8, "name": "cloud3"}, {"time": 9.9333, "name": "cloud5"}, {"time": 10.0667, "name": "cloud7"}, {"time": 10.2, "name": "cloud9"}, {"time": 10.3333, "name": "cloud11"}, {"time": 10.4667, "name": "cloud1"}, {"time": 10.6, "name": "cloud3"}, {"time": 10.7333, "name": "cloud5"}, {"time": 10.8667, "name": "cloud7"}, {"time": 11, "name": "cloud9"}, {"time": 11.1333, "name": "cloud11"}]}, "cloud2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "cloud5"}, {"time": 0.1333, "name": "cloud7"}, {"time": 0.2667, "name": "cloud9"}, {"time": 0.4, "name": "cloud11"}, {"time": 0.5333, "name": "cloud1"}, {"time": 0.6667, "name": "cloud3"}, {"time": 0.8, "name": "cloud5"}, {"time": 0.9333, "name": "cloud7"}, {"time": 1.0667, "name": "cloud9"}, {"time": 1.2, "name": "cloud11"}, {"time": 1.3333, "name": "cloud1"}, {"time": 1.4667, "name": "cloud3"}, {"time": 1.6, "name": "cloud5"}, {"time": 1.7333, "name": "cloud7"}, {"time": 1.8667, "name": "cloud9"}, {"time": 2, "name": "cloud11"}, {"time": 2.1333, "name": "cloud1"}, {"time": 2.2667, "name": "cloud3"}, {"time": 2.4, "name": "cloud5"}, {"time": 2.5333, "name": "cloud7"}, {"time": 2.6667, "name": "cloud9"}, {"time": 2.8, "name": "cloud11"}, {"time": 2.9333, "name": "cloud1"}, {"time": 3.0667, "name": "cloud3"}, {"time": 3.2, "name": "cloud5"}, {"time": 3.3333, "name": "cloud7"}, {"time": 3.4667, "name": "cloud9"}, {"time": 3.6, "name": "cloud11"}, {"time": 3.7333, "name": "cloud1"}, {"time": 3.8667, "name": "cloud3"}, {"time": 4, "name": "cloud5"}, {"time": 4.1333, "name": "cloud7"}, {"time": 4.2667, "name": "cloud9"}, {"time": 4.4, "name": "cloud11"}, {"time": 4.5333, "name": "cloud1"}, {"time": 4.6667, "name": "cloud3"}, {"time": 4.8, "name": "cloud5"}, {"time": 4.9333, "name": "cloud7"}, {"time": 5.0667, "name": "cloud9"}, {"time": 5.2, "name": "cloud11"}, {"time": 5.3333, "name": "cloud1"}, {"time": 5.4667, "name": "cloud3"}, {"time": 5.6, "name": "cloud5"}, {"time": 5.7333, "name": "cloud7"}, {"time": 5.8667, "name": "cloud9"}, {"time": 6, "name": "cloud11"}, {"time": 6.1333, "name": "cloud1"}, {"time": 6.2667, "name": "cloud3"}, {"time": 6.4, "name": "cloud5"}, {"time": 6.5333, "name": "cloud7"}, {"time": 6.6667, "name": "cloud9"}, {"time": 6.8, "name": "cloud11"}, {"time": 6.9333, "name": "cloud1"}, {"time": 7.0667, "name": "cloud3"}, {"time": 7.2, "name": "cloud5"}, {"time": 7.3333, "name": "cloud7"}, {"time": 7.4667, "name": "cloud9"}, {"time": 7.6, "name": "cloud11"}, {"time": 7.7333, "name": "cloud1"}, {"time": 7.8667, "name": "cloud3"}, {"time": 8, "name": "cloud5"}, {"time": 8.1333, "name": "cloud7"}, {"time": 8.2667, "name": "cloud9"}, {"time": 8.4, "name": "cloud11"}, {"time": 8.5333, "name": "cloud1"}, {"time": 8.6667, "name": "cloud3"}, {"time": 8.8, "name": "cloud5"}, {"time": 8.9333, "name": "cloud7"}, {"time": 9.0667, "name": "cloud9"}, {"time": 9.2, "name": "cloud11"}, {"time": 9.3333, "name": "cloud1"}, {"time": 9.4667, "name": "cloud3"}, {"time": 9.6, "name": "cloud5"}, {"time": 9.7333, "name": "cloud7"}, {"time": 9.8667, "name": "cloud9"}, {"time": 10, "name": "cloud11"}, {"time": 10.1333, "name": "cloud1"}, {"time": 10.2667, "name": "cloud3"}, {"time": 10.4, "name": "cloud5"}, {"time": 10.5333, "name": "cloud7"}, {"time": 10.6667, "name": "cloud9"}, {"time": 10.8, "name": "cloud11"}, {"time": 10.9333, "name": "cloud1"}, {"time": 11.0667, "name": "cloud3"}]}, "cuonvai": {"attachment": [{"time": 0, "name": null}]}, "cuonvai2": {"attachment": [{"time": 0, "name": null}]}, "domdom": {"attachment": [{"time": 0, "name": null}]}, "jackpottext0": {"attachment": [{"time": 0, "name": null}, {"time": 0.7, "name": "jackpottext1"}, {"time": 0.8, "name": "jackpottext2"}, {"time": 0.9, "name": "jackpottext3"}, {"time": 1, "name": "jackpottext4"}, {"time": 1.1, "name": "jackpottext5"}, {"time": 1.2, "name": "jackpottext6"}, {"time": 1.3, "name": "jackpottext7"}, {"time": 1.4, "name": "jackpottext8"}, {"time": 1.5, "name": null}, {"time": 2.1, "name": "jackpottext1"}, {"time": 2.2, "name": "jackpottext2"}, {"time": 2.3, "name": "jackpottext3"}, {"time": 2.4, "name": "jackpottext4"}, {"time": 2.5, "name": "jackpottext5"}, {"time": 2.6, "name": "jackpottext6"}, {"time": 2.7, "name": "jackpottext7"}, {"time": 2.8, "name": "jackpottext8"}, {"time": 2.9, "name": null}, {"time": 3.5, "name": "jackpottext1"}, {"time": 3.6, "name": "jackpottext2"}, {"time": 3.7, "name": "jackpottext3"}, {"time": 3.8, "name": "jackpottext4"}, {"time": 3.9, "name": "jackpottext5"}, {"time": 4, "name": "jackpottext6"}, {"time": 4.1, "name": "jackpottext7"}, {"time": 4.2, "name": "jackpottext8"}, {"time": 4.3, "name": null}, {"time": 4.9, "name": "jackpottext1"}, {"time": 5, "name": "jackpottext2"}, {"time": 5.1, "name": "jackpottext3"}, {"time": 5.2, "name": "jackpottext4"}, {"time": 5.3, "name": "jackpottext5"}, {"time": 5.4, "name": "jackpottext6"}, {"time": 5.5, "name": "jackpottext7"}, {"time": 5.6, "name": "jackpottext8"}, {"time": 5.7, "name": null}, {"time": 6.3333, "name": "jackpottext1"}, {"time": 6.4333, "name": "jackpottext2"}, {"time": 6.5333, "name": "jackpottext3"}, {"time": 6.6333, "name": "jackpottext4"}, {"time": 6.7333, "name": "jackpottext5"}, {"time": 6.8333, "name": "jackpottext6"}, {"time": 6.9333, "name": "jackpottext7"}, {"time": 7.0333, "name": "jackpottext8"}, {"time": 7.1333, "name": null}, {"time": 7.7333, "name": "jackpottext1"}, {"time": 7.8333, "name": "jackpottext2"}, {"time": 7.9333, "name": "jackpottext3"}, {"time": 8.0333, "name": "jackpottext4"}, {"time": 8.1333, "name": "jackpottext5"}, {"time": 8.2333, "name": "jackpottext6"}, {"time": 8.3333, "name": "jackpottext7"}, {"time": 8.4333, "name": "jackpottext8"}, {"time": 8.5333, "name": null}, {"time": 9.1333, "name": "jackpottext1"}, {"time": 9.2333, "name": "jackpottext2"}, {"time": 9.3333, "name": "jackpottext3"}, {"time": 9.4333, "name": "jackpottext4"}, {"time": 9.5333, "name": "jackpottext5"}, {"time": 9.6333, "name": "jackpottext6"}, {"time": 9.7333, "name": "jackpottext7"}, {"time": 9.8333, "name": "jackpottext8"}, {"time": 9.9333, "name": null}]}, "staff": {"attachment": [{"time": 0, "name": null}]}, "wineffect2": {"attachment": [{"time": 0, "name": null}]}, "wineffect3": {"attachment": [{"time": 0, "name": null}]}, "wineffect4": {"attachment": [{"time": 0, "name": null}]}, "wineffect5": {"attachment": [{"time": 0, "name": null}]}, "wineffect6": {"attachment": [{"time": 0, "name": null}]}, "wineffect7": {"attachment": [{"time": 0, "name": null}]}, "wineffect8": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone": {"translate": [{"time": 0, "x": -0.53, "y": 0.53}], "scale": [{"time": 0, "x": 1.25, "y": 1.25}]}, "bone2": {"translate": [{"time": 0, "x": 0, "y": -100.69}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 1, "y": 1}]}, "bone3": {"translate": [{"time": 0, "x": 115.11, "y": -78.08}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 1, "y": 1}]}, "bone4": {"translate": [{"time": 0, "x": -127.23, "y": -87.8}, {"time": 0.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 1, "y": 1}]}, "bone5": {"scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1667, "x": 1.324, "y": 1.324}, {"time": 0.2667, "x": 1, "y": 1}, {"time": 0.3, "x": 1.104, "y": 1.104}, {"time": 0.3333, "x": 1, "y": 1}]}, "bone6": {"translate": [{"time": 0, "x": 0, "y": 34.4}, {"time": 0.5, "x": 0, "y": -9.59}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 1, "y": 1}]}}}, "bonus": {"slots": {"bn0": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "bn1": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "bn2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "bn3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "bn4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "bonus0": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00"}]}, "bonus1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00"}]}, "boxtienthang": {"attachment": [{"time": 0, "name": null}]}, "cloud0": {"attachment": [{"time": 0, "name": null}]}, "cloud1": {"attachment": [{"time": 0, "name": null}]}, "cloud2": {"attachment": [{"time": 0, "name": null}]}, "cuonvai": {"attachment": [{"time": 0, "name": null}]}, "cuonvai2": {"attachment": [{"time": 0, "name": null}]}, "domdom": {"attachment": [{"time": 0, "name": null}]}, "jackpottext0": {"attachment": [{"time": 0, "name": null}]}, "staff": {"color": [{"time": 2.5, "color": "ffffffff"}, {"time": 3, "color": "ffffff00"}]}, "wineffect1": {"attachment": [{"time": 0, "name": null}]}, "wineffect2": {"attachment": [{"time": 0, "name": null}]}, "wineffect3": {"attachment": [{"time": 0, "name": null}]}, "wineffect4": {"attachment": [{"time": 0, "name": null}]}, "wineffect5": {"attachment": [{"time": 0, "name": null}]}, "wineffect6": {"attachment": [{"time": 0, "name": null}]}, "wineffect7": {"attachment": [{"time": 0, "name": null}]}, "wineffect8": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"bone21": {"scale": [{"time": 0, "x": 3, "y": 3}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.3333, "x": 1, "y": 1}, {"time": 2.5, "x": 3, "y": 3}]}, "bone22": {"rotate": [{"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": -178.63}, {"time": 0.9333, "angle": 82.63}, {"time": 1, "angle": 0}, {"time": 1.1667, "angle": -178.63}, {"time": 1.2667, "angle": 82.63}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": -178.63}, {"time": 1.6, "angle": 82.63}, {"time": 1.6667, "angle": 0}, {"time": 1.8333, "angle": -178.63}, {"time": 1.9333, "angle": 82.63}, {"time": 2, "angle": 0}, {"time": 2.4333, "angle": -178.63}, {"time": 2.6667, "angle": 82.63}, {"time": 2.8333, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1.1667, "x": 1.277, "y": 1.277, "curve": "stepped"}, {"time": 1.8333, "x": 1.277, "y": 1.277}, {"time": 2.3333, "x": 3.551, "y": 3.551}]}, "bone23": {"rotate": [{"time": 0.8333, "angle": 0}, {"time": 0.9667, "angle": -179.39}, {"time": 1.0333, "angle": 83.37}, {"time": 1.1, "angle": 0}, {"time": 1.2, "angle": -179.39}, {"time": 1.3, "angle": 83.37}, {"time": 1.3333, "angle": 0}, {"time": 1.4667, "angle": -179.39}, {"time": 1.5333, "angle": 83.37}, {"time": 1.6, "angle": 0}, {"time": 1.7333, "angle": -179.39}, {"time": 1.8, "angle": 83.37}, {"time": 1.8333, "angle": 0}, {"time": 1.9667, "angle": -179.39}, {"time": 2.0333, "angle": 83.37}, {"time": 2.1, "angle": 0}, {"time": 2.2, "angle": -179.39}, {"time": 2.3, "angle": 83.37}, {"time": 2.3333, "angle": 0}, {"time": 2.4667, "angle": -179.39}, {"time": 2.5333, "angle": 83.37}, {"time": 2.6, "angle": 0}, {"time": 2.8667, "angle": -179.39}, {"time": 3.1, "angle": 83.37}, {"time": 3.3333, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.308, "y": 0.308, "curve": "stepped"}, {"time": 1.8333, "x": 0.308, "y": 0.308}, {"time": 2.1667, "x": 0.524, "y": 0.524}, {"time": 3.3333, "x": 1.717, "y": 1.717}]}}}, "free": {"slots": {"FB0": {"attachment": [{"time": 0.0667, "name": "FB0"}, {"time": 0.1333, "name": "FB1"}, {"time": 0.2, "name": "FB2"}, {"time": 0.2667, "name": "FB3"}, {"time": 0.3333, "name": "FB4"}, {"time": 0.4, "name": "FB5"}, {"time": 0.4667, "name": "FB6"}, {"time": 0.5333, "name": "FB7"}, {"time": 0.6, "name": "FB8"}, {"time": 0.6667, "name": "FB9"}, {"time": 0.7333, "name": "FB10"}, {"time": 0.8, "name": "FB11"}, {"time": 0.8667, "name": "FB12"}, {"time": 0.9333, "name": null}]}, "FB1": {"color": [{"time": 0, "color": "ffffff5b"}], "attachment": [{"time": 0.0667, "name": "FB0"}, {"time": 0.1333, "name": "FB1"}, {"time": 0.2, "name": "FB2"}, {"time": 0.2667, "name": "FB3"}, {"time": 0.3333, "name": "FB4"}, {"time": 0.4, "name": "FB5"}, {"time": 0.4667, "name": "FB6"}, {"time": 0.5333, "name": "FB7"}, {"time": 0.6, "name": "FB8"}, {"time": 0.6667, "name": "FB9"}, {"time": 0.7333, "name": "FB10"}, {"time": 0.8, "name": "FB11"}, {"time": 0.8667, "name": "FB12"}, {"time": 0.9333, "name": null}]}, "bn0": {"attachment": [{"time": 0, "name": null}]}, "bn1": {"attachment": [{"time": 0, "name": null}]}, "bn2": {"attachment": [{"time": 0, "name": null}]}, "bn3": {"attachment": [{"time": 0, "name": null}]}, "bn4": {"attachment": [{"time": 0, "name": null}]}, "bonus0": {"attachment": [{"time": 0, "name": null}]}, "bonus1": {"attachment": [{"time": 0, "name": null}]}, "boxtienthang": {"attachment": [{"time": 0, "name": null}]}, "cloud0": {"attachment": [{"time": 0, "name": null}]}, "cloud1": {"attachment": [{"time": 0, "name": null}]}, "cloud2": {"attachment": [{"time": 0, "name": null}]}, "cuonvai": {"attachment": [{"time": 0, "name": null}]}, "cuonvai2": {"attachment": [{"time": 0, "name": null}]}, "domdom": {"attachment": [{"time": 0, "name": null}]}, "jackpottext0": {"attachment": [{"time": 0, "name": null}]}, "staff": {"attachment": [{"time": 0, "name": null}]}, "wineffect1": {"attachment": [{"time": 0, "name": null}]}, "wineffect2": {"attachment": [{"time": 0, "name": null}]}, "wineffect3": {"attachment": [{"time": 0, "name": null}]}, "wineffect4": {"attachment": [{"time": 0, "name": null}]}, "wineffect5": {"attachment": [{"time": 0, "name": null}]}, "wineffect6": {"attachment": [{"time": 0, "name": null}]}, "wineffect7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.4667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}]}, "wineffect8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffff50"}, {"time": 1.6667, "color": "ffffff00"}]}}, "bones": {"bone17": {"translate": [{"time": 0, "x": 72.69, "y": 0}], "scale": [{"time": 0, "x": 1.716, "y": 1.939}]}, "bone18": {"scale": [{"time": 1.6667, "x": 1, "y": 1}, {"time": 1.8333, "x": 3.452, "y": 3.452}]}, "bone19": {"scale": [{"time": 1.6667, "x": 1, "y": 1}, {"time": 1.8333, "x": 3.452, "y": 3.452}]}, "bone20": {"translate": [{"time": 0, "x": 72.69, "y": 0}], "scale": [{"time": 0, "x": 1.716, "y": 1.939}]}}}}}, [0]]], 0, 0, [0], [-1], [126]], [[{"name": "bnt_setting", "rect": [0, 0, 94, 87], "offset": [0, 0], "originalSize": [94, 87], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [127]], [[{"name": "maingame15", "rect": [0, 0, 271, 90], "offset": [0, 0.5], "originalSize": [271, 91], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [128]], [[{"name": "line20", "rect": [0, 0, 799, 382], "offset": [0, 0], "originalSize": [799, 382], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [129]], [[{"name": "text_choiThu", "rect": [0, 0, 258, 108], "offset": [0, 0], "originalSize": [258, 108], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [130]], [[{"name": "line10", "rect": [0, 0, 800, 353], "offset": [0, 0], "originalSize": [800, 353], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [131]], [[{"name": "line28", "rect": [0, 0, 797, 340], "offset": [0, 0], "originalSize": [797, 340], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [132]], [[{"name": "line24", "rect": [0, 0, 798, 354], "offset": [0, 0], "originalSize": [798, 354], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [133]], [[{"name": "btn_dong", "rect": [0, 0, 142, 84], "offset": [0, 0.5], "originalSize": [142, 85], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [134]], [[{"name": "soun_on", "rect": [1, 2, 34, 32], "offset": [0.5, 1], "originalSize": [35, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [135]], [[[39, "columnStop", 2.4166666666666665, 4, "0", [{"frame": 2.4166666666666665, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 330}, {"frame": 1.3333333333333333, "value": -330}, {"frame": 1.35, "value": 330}, {"frame": 1.8333333333333333, "value": 270}, {"frame": 2.4166666666666665, "value": 330}], "active": [{"frame": 0, "value": true}, {"frame": 1.3333333333333333, "value": false}, {"frame": 1.35, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 165}, {"frame": 1, "value": -330}, {"frame": 1.0166666666666666, "value": 330}, {"frame": 1.35, "value": 165}, {"frame": 1.8333333333333333, "value": 105}, {"frame": 2.4166666666666665, "value": 165}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, 0]}, {"frame": 0.6666666666666666, "value": [0, -330]}, {"frame": 0.6833333333333333, "value": [0, 330]}, {"frame": 1.35, "value": [0, 0]}, {"frame": 1.8333333333333333, "value": [0, -60]}, {"frame": 2.4166666666666665, "value": [0, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -165]}, {"frame": 0.3333333333333333, "value": [0, -330]}, {"frame": 0.35, "value": [0, 330]}, {"frame": 1.35, "value": [0, -165]}, {"frame": 1.8333333333333333, "value": [0, -225]}, {"frame": 2.4166666666666665, "value": [0, -165]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[[39, "columnSpin2", 0.6833333333333333, 8, 2, [{"frame": 0.16666666666666666, "func": "3", "params": []}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 330}, {"frame": 0.6666666666666666, "value": -330}, {"frame": 0.6833333333333333, "value": 330}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 165}, {"frame": 0.5, "value": -330}, {"frame": 0.5166666666666667, "value": 330}, {"frame": 0.6833333333333333, "value": 165}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -330}, {"frame": 0.35, "value": 330}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -165]}, {"frame": 0.16666666666666666, "value": [0, -330]}, {"frame": 0.18333333333333332, "value": [0, 330]}, {"frame": 0.6833333333333333, "value": [0, -165]}]}}}}]], 0, 0, [], [], []], [[{"name": "bg_sound", "rect": [0, 0, 192, 86], "offset": [0, 0], "originalSize": [192, 86], "capInsets": [92, 38, 92, 38]}], [0], 0, [0], [2], [136]], [[[39, "columnSpin", 0.6833333333333333, 4, 2, [{"frame": 0.16666666666666666, "func": "randomIcon", "params": ["3"]}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 330}, {"frame": 0.6666666666666666, "value": -330}, {"frame": 0.6833333333333333, "value": 330}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 165}, {"frame": 0.5, "value": -330}, {"frame": 0.5166666666666667, "value": 330}, {"frame": 0.6833333333333333, "value": 165}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -330}, {"frame": 0.35, "value": 330}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -165]}, {"frame": 0.16666666666666666, "value": [0, -330]}, {"frame": 0.18333333333333332, "value": [0, 330]}, {"frame": 0.6833333333333333, "value": [0, -165]}]}}}}]], 0, 0, [], [], []], [[{"name": "line22", "rect": [0, 0, 797, 194], "offset": [0, 0], "originalSize": [797, 194], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [137]], [[{"name": "line14", "rect": [0, 0, 798, 356], "offset": [0, 0], "originalSize": [798, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [138]], [[{"name": "btn_TuQuay_off", "rect": [0, 0, 159, 39], "offset": [0, 0], "originalSize": [159, 39], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [139]], [[{"name": "line4", "rect": [0, 0, 798, 183], "offset": [0, 0], "originalSize": [798, 183], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [140]], [[{"name": "line1_2_3", "rect": [0, 0, 800, 30], "offset": [0, 0], "originalSize": [800, 30], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [141]], [[{"name": "btn_quay_on", "rect": [0, 0, 258, 108], "offset": [0, 0], "originalSize": [258, 108], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [142]], [[{"name": "button_quaylai", "rect": [0, 0, 78, 78], "offset": [0, 0], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [143]], [[{"name": "line23", "rect": [0, 0, 798, 356], "offset": [0, 0], "originalSize": [798, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [144]], [[{"name": "line17", "rect": [0, 0, 798, 196], "offset": [0, 0], "originalSize": [798, 196], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [145]], [[{"name": "line13", "rect": [0, 0, 798, 356], "offset": [0, 0], "originalSize": [798, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [146]], [[{"name": "maingame_BG", "rect": [0, 0, 800, 369], "offset": [0, 0], "originalSize": [800, 369], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [147]], [[{"name": "BGspin", "rect": [0, 0, 39, 488], "offset": [0, 0], "originalSize": [39, 488], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [148]], [[{"name": "bg2", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [149]], [[{"name": "maingame14", "rect": [0, 0, 173, 92], "offset": [0, 0], "originalSize": [173, 92], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [150]], [[{"name": "popup", "rect": [0, 0, 612, 275], "offset": [0, 0], "originalSize": [612, 275], "capInsets": [288, 128, 288, 128]}], [0], 0, [0], [2], [151]], [[{"name": "line8", "rect": [0, 0, 800, 418], "offset": [0, 0], "originalSize": [800, 418], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [152]], [[{"name": "line19", "rect": [0, 0, 798, 383], "offset": [0, 0], "originalSize": [798, 383], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [153]], [[{"name": "bg1", "rect": [0, 0, 1398, 768], "offset": [0, 0], "originalSize": [1398, 768], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [154]], [[{"name": "line6", "rect": [0, 0, 797, 218], "offset": [0, 0], "originalSize": [797, 218], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [155]], [[{"name": "music_off", "rect": [0, 0, 34, 37], "offset": [-0.5, 0.5], "originalSize": [35, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [156]], [[{"name": "btn_set", "rect": [0, 0, 59, 61], "offset": [0, 0], "originalSize": [59, 61], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [157]], [[{"name": "khung", "rect": [0, 0, 1398, 728], "offset": [0, 0], "originalSize": [1398, 728], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [158]], [[{"name": "music_on", "rect": [0, 2, 30, 35], "offset": [-2.5, -0.5], "originalSize": [35, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [159]], [[{"name": "logo", "rect": [2, 0, 895, 75], "offset": [-1, 0], "originalSize": [901, 75], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [160]], [[{"name": "choithu", "rect": [0, 0, 112, 136], "offset": [0, 0], "originalSize": [112, 136], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [161]], [[[28, "MoneyRaised", ".mp3", 1.044898], -1], 0, 0, [], [], []], [[[39, "columnStop2", 1.35, 8, "0", [{"frame": 1.35, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 330}, {"frame": 1.3333333333333333, "value": -330}, {"frame": 1.35, "value": 330}], "active": []}}, "slot1": {"props": {"y": [{"frame": 0, "value": 165}, {"frame": 1, "value": -330}, {"frame": 1.0166666666666666, "value": 330}, {"frame": 1.35, "value": 165}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, 0]}, {"frame": 0.6666666666666666, "value": [0, -330]}, {"frame": 0.6833333333333333, "value": [0, 330]}, {"frame": 1.35, "value": [0, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -165]}, {"frame": 0.3333333333333333, "value": [0, -330]}, {"frame": 0.35, "value": [0, 330]}, {"frame": 1.35, "value": [0, -165]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[[28, "stopspin", ".mp3", 0.36575], -1], 0, 0, [], [], []], [[[141, "thantai_text_game", 32, {"commonHeight": 40, "fontSize": 32, "atlasName": "thantai_text_game.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 104, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 9, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 0, "yOffset": 15, "xAdvance": 10, "rect": {"x": 250, "y": 279, "width": 8, "height": 25}}, "34": {"xOffset": 0, "yOffset": 15, "xAdvance": 15, "rect": {"x": 120, "y": 499, "width": 13, "height": 11}}, "35": {"xOffset": 0, "yOffset": 16, "xAdvance": 20, "rect": {"x": 201, "y": 303, "width": 18, "height": 24}}, "36": {"xOffset": 0, "yOffset": 14, "xAdvance": 20, "rect": {"x": 201, "y": 328, "width": 18, "height": 29}}, "37": {"xOffset": 0, "yOffset": 16, "xAdvance": 32, "rect": {"x": 0, "y": 26, "width": 30, "height": 25}}, "38": {"xOffset": 0, "yOffset": 15, "xAdvance": 26, "rect": {"x": 26, "y": 398, "width": 24, "height": 25}}, "39": {"xOffset": 0, "yOffset": 15, "xAdvance": 9, "rect": {"x": 236, "y": 369, "width": 7, "height": 11}}, "40": {"xOffset": 0, "yOffset": 15, "xAdvance": 12, "rect": {"x": 249, "y": 143, "width": 10, "height": 29}}, "41": {"xOffset": 0, "yOffset": 15, "xAdvance": 12, "rect": {"x": 249, "y": 83, "width": 10, "height": 29}}, "42": {"xOffset": 0, "yOffset": 15, "xAdvance": 18, "rect": {"x": 31, "y": 35, "width": 16, "height": 16}}, "43": {"xOffset": 0, "yOffset": 20, "xAdvance": 22, "rect": {"x": 27, "y": 313, "width": 20, "height": 21}}, "44": {"xOffset": 0, "yOffset": 32, "xAdvance": 12, "rect": {"x": 122, "y": 447, "width": 10, "height": 12}}, "45": {"xOffset": 0, "yOffset": 27, "xAdvance": 13, "rect": {"x": 52, "y": 388, "width": 11, "height": 6}}, "46": {"xOffset": 0, "yOffset": 31, "xAdvance": 10, "rect": {"x": 64, "y": 104, "width": 8, "height": 9}}, "47": {"xOffset": 0, "yOffset": 15, "xAdvance": 15, "rect": {"x": 236, "y": 341, "width": 13, "height": 27}}, "48": {"xOffset": 0, "yOffset": 16, "xAdvance": 20, "rect": {"x": 182, "y": 224, "width": 18, "height": 24}}, "49": {"xOffset": 0, "yOffset": 16, "xAdvance": 14, "rect": {"x": 238, "y": 58, "width": 12, "height": 24}}, "50": {"xOffset": 0, "yOffset": 16, "xAdvance": 20, "rect": {"x": 182, "y": 249, "width": 18, "height": 24}}, "51": {"xOffset": 0, "yOffset": 16, "xAdvance": 20, "rect": {"x": 182, "y": 274, "width": 18, "height": 24}}, "52": {"xOffset": 0, "yOffset": 16, "xAdvance": 22, "rect": {"x": 122, "y": 290, "width": 20, "height": 24}}, "53": {"xOffset": 0, "yOffset": 16, "xAdvance": 19, "rect": {"x": 220, "y": 114, "width": 17, "height": 24}}, "54": {"xOffset": 0, "yOffset": 16, "xAdvance": 21, "rect": {"x": 163, "y": 168, "width": 19, "height": 24}}, "55": {"xOffset": 0, "yOffset": 16, "xAdvance": 20, "rect": {"x": 182, "y": 381, "width": 18, "height": 24}}, "56": {"xOffset": 0, "yOffset": 16, "xAdvance": 20, "rect": {"x": 183, "y": 432, "width": 18, "height": 24}}, "57": {"xOffset": 0, "yOffset": 16, "xAdvance": 20, "rect": {"x": 183, "y": 80, "width": 18, "height": 24}}, "58": {"xOffset": 0, "yOffset": 21, "xAdvance": 10, "rect": {"x": 249, "y": 233, "width": 8, "height": 19}}, "59": {"xOffset": 0, "yOffset": 21, "xAdvance": 12, "rect": {"x": 238, "y": 83, "width": 10, "height": 23}}, "60": {"xOffset": 0, "yOffset": 20, "xAdvance": 20, "rect": {"x": 183, "y": 491, "width": 18, "height": 20}}, "61": {"xOffset": 0, "yOffset": 24, "xAdvance": 22, "rect": {"x": 99, "y": 499, "width": 20, "height": 12}}, "62": {"xOffset": 0, "yOffset": 20, "xAdvance": 20, "rect": {"x": 201, "y": 218, "width": 18, "height": 20}}, "63": {"xOffset": 0, "yOffset": 15, "xAdvance": 16, "rect": {"x": 236, "y": 407, "width": 14, "height": 25}}, "64": {"xOffset": 0, "yOffset": 18, "xAdvance": 28, "rect": {"x": 0, "y": 248, "width": 26, "height": 25}}, "65": {"xOffset": 0, "yOffset": 15, "xAdvance": 25, "rect": {"x": 76, "y": 381, "width": 23, "height": 25}}, "66": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 163, "y": 471, "width": 19, "height": 25}}, "67": {"xOffset": 0, "yOffset": 15, "xAdvance": 22, "rect": {"x": 122, "y": 264, "width": 20, "height": 25}}, "68": {"xOffset": 0, "yOffset": 15, "xAdvance": 24, "rect": {"x": 76, "y": 433, "width": 22, "height": 25}}, "69": {"xOffset": 0, "yOffset": 15, "xAdvance": 19, "rect": {"x": 202, "y": 482, "width": 17, "height": 25}}, "70": {"xOffset": 0, "yOffset": 15, "xAdvance": 18, "rect": {"x": 220, "y": 289, "width": 16, "height": 25}}, "71": {"xOffset": 0, "yOffset": 15, "xAdvance": 24, "rect": {"x": 76, "y": 486, "width": 22, "height": 25}}, "72": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 100, "y": 400, "width": 21, "height": 25}}, "73": {"xOffset": 0, "yOffset": 15, "xAdvance": 9, "rect": {"x": 250, "y": 372, "width": 7, "height": 25}}, "74": {"xOffset": 0, "yOffset": 15, "xAdvance": 16, "rect": {"x": 235, "y": 381, "width": 14, "height": 25}}, "75": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 122, "y": 57, "width": 21, "height": 25}}, "76": {"xOffset": 0, "yOffset": 15, "xAdvance": 18, "rect": {"x": 220, "y": 315, "width": 16, "height": 25}}, "77": {"xOffset": 0, "yOffset": 15, "xAdvance": 29, "rect": {"x": 0, "y": 52, "width": 27, "height": 25}}, "78": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 122, "y": 114, "width": 21, "height": 25}}, "79": {"xOffset": 0, "yOffset": 15, "xAdvance": 26, "rect": {"x": 26, "y": 462, "width": 24, "height": 25}}, "80": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 201, "y": 388, "width": 18, "height": 25}}, "81": {"xOffset": 0, "yOffset": 15, "xAdvance": 26, "rect": {"x": 28, "y": 85, "width": 24, "height": 29}}, "82": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 143, "y": 249, "width": 19, "height": 25}}, "83": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 182, "y": 406, "width": 18, "height": 25}}, "84": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 143, "y": 301, "width": 19, "height": 25}}, "85": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 122, "y": 31, "width": 21, "height": 25}}, "86": {"xOffset": 0, "yOffset": 15, "xAdvance": 25, "rect": {"x": 76, "y": 407, "width": 23, "height": 25}}, "87": {"xOffset": 0, "yOffset": 15, "xAdvance": 33, "rect": {"x": 0, "y": 0, "width": 31, "height": 25}}, "88": {"xOffset": 0, "yOffset": 15, "xAdvance": 24, "rect": {"x": 77, "y": 186, "width": 22, "height": 25}}, "89": {"xOffset": 0, "yOffset": 15, "xAdvance": 24, "rect": {"x": 77, "y": 160, "width": 22, "height": 25}}, "90": {"xOffset": 0, "yOffset": 15, "xAdvance": 22, "rect": {"x": 122, "y": 375, "width": 20, "height": 25}}, "91": {"xOffset": 0, "yOffset": 15, "xAdvance": 12, "rect": {"x": 249, "y": 173, "width": 10, "height": 29}}, "92": {"xOffset": 0, "yOffset": 15, "xAdvance": 14, "rect": {"x": 237, "y": 289, "width": 12, "height": 27}}, "93": {"xOffset": 0, "yOffset": 15, "xAdvance": 11, "rect": {"x": 249, "y": 203, "width": 9, "height": 29}}, "94": {"xOffset": 0, "yOffset": 16, "xAdvance": 21, "rect": {"x": 122, "y": 428, "width": 19, "height": 18}}, "95": {"xOffset": 0, "yOffset": 40, "xAdvance": 21, "rect": {"x": 100, "y": 428, "width": 19, "height": 4}}, "96": {"xOffset": 0, "yOffset": 15, "xAdvance": 12, "rect": {"x": 53, "y": 104, "width": 10, "height": 7}}, "97": {"xOffset": 0, "yOffset": 21, "xAdvance": 20, "rect": {"x": 143, "y": 381, "width": 18, "height": 19}}, "98": {"xOffset": 0, "yOffset": 14, "xAdvance": 21, "rect": {"x": 122, "y": 401, "width": 19, "height": 26}}, "99": {"xOffset": 0, "yOffset": 21, "xAdvance": 17, "rect": {"x": 220, "y": 361, "width": 15, "height": 19}}, "100": {"xOffset": 0, "yOffset": 14, "xAdvance": 21, "rect": {"x": 143, "y": 222, "width": 19, "height": 26}}, "101": {"xOffset": 0, "yOffset": 21, "xAdvance": 20, "rect": {"x": 145, "y": 0, "width": 18, "height": 19}}, "102": {"xOffset": 0, "yOffset": 14, "xAdvance": 16, "rect": {"x": 220, "y": 381, "width": 14, "height": 26}}, "103": {"xOffset": 0, "yOffset": 21, "xAdvance": 20, "rect": {"x": 163, "y": 227, "width": 18, "height": 26}}, "104": {"xOffset": 0, "yOffset": 14, "xAdvance": 19, "rect": {"x": 220, "y": 165, "width": 17, "height": 26}}, "105": {"xOffset": 0, "yOffset": 15, "xAdvance": 10, "rect": {"x": 249, "y": 253, "width": 8, "height": 25}}, "106": {"xOffset": 0, "yOffset": 15, "xAdvance": 13, "rect": {"x": 238, "y": 463, "width": 11, "height": 32}}, "107": {"xOffset": 0, "yOffset": 14, "xAdvance": 21, "rect": {"x": 143, "y": 327, "width": 19, "height": 26}}, "108": {"xOffset": 0, "yOffset": 14, "xAdvance": 9, "rect": {"x": 250, "y": 433, "width": 7, "height": 26}}, "109": {"xOffset": 0, "yOffset": 21, "xAdvance": 29, "rect": {"x": 0, "y": 98, "width": 27, "height": 19}}, "110": {"xOffset": 0, "yOffset": 21, "xAdvance": 19, "rect": {"x": 220, "y": 94, "width": 17, "height": 19}}, "111": {"xOffset": 0, "yOffset": 21, "xAdvance": 21, "rect": {"x": 143, "y": 487, "width": 19, "height": 19}}, "112": {"xOffset": 0, "yOffset": 21, "xAdvance": 21, "rect": {"x": 144, "y": 20, "width": 19, "height": 26}}, "113": {"xOffset": 0, "yOffset": 21, "xAdvance": 21, "rect": {"x": 143, "y": 354, "width": 19, "height": 26}}, "114": {"xOffset": 0, "yOffset": 21, "xAdvance": 14, "rect": {"x": 237, "y": 317, "width": 12, "height": 19}}, "115": {"xOffset": 0, "yOffset": 21, "xAdvance": 17, "rect": {"x": 220, "y": 341, "width": 15, "height": 19}}, "116": {"xOffset": 0, "yOffset": 16, "xAdvance": 16, "rect": {"x": 221, "y": 408, "width": 14, "height": 24}}, "117": {"xOffset": 0, "yOffset": 21, "xAdvance": 19, "rect": {"x": 220, "y": 269, "width": 17, "height": 19}}, "118": {"xOffset": 0, "yOffset": 21, "xAdvance": 21, "rect": {"x": 142, "y": 436, "width": 19, "height": 19}}, "119": {"xOffset": 0, "yOffset": 21, "xAdvance": 29, "rect": {"x": 0, "y": 78, "width": 27, "height": 19}}, "120": {"xOffset": 0, "yOffset": 21, "xAdvance": 21, "rect": {"x": 125, "y": 0, "width": 19, "height": 19}}, "121": {"xOffset": 0, "yOffset": 21, "xAdvance": 22, "rect": {"x": 122, "y": 315, "width": 20, "height": 26}}, "122": {"xOffset": 0, "yOffset": 21, "xAdvance": 19, "rect": {"x": 220, "y": 223, "width": 17, "height": 19}}, "123": {"xOffset": 0, "yOffset": 15, "xAdvance": 12, "rect": {"x": 249, "y": 113, "width": 10, "height": 29}}, "124": {"xOffset": 0, "yOffset": 13, "xAdvance": 8, "rect": {"x": 250, "y": 460, "width": 6, "height": 36}}, "125": {"xOffset": 0, "yOffset": 15, "xAdvance": 13, "rect": {"x": 238, "y": 433, "width": 11, "height": 29}}, "126": {"xOffset": 0, "yOffset": 25, "xAdvance": 22, "rect": {"x": 0, "y": 497, "width": 20, "height": 9}}, "192": {"xOffset": 0, "yOffset": 10, "xAdvance": 25, "rect": {"x": 76, "y": 350, "width": 23, "height": 30}}, "193": {"xOffset": 0, "yOffset": 10, "xAdvance": 25, "rect": {"x": 76, "y": 319, "width": 23, "height": 30}}, "194": {"xOffset": 0, "yOffset": 10, "xAdvance": 25, "rect": {"x": 52, "y": 357, "width": 23, "height": 30}}, "195": {"xOffset": 0, "yOffset": 9, "xAdvance": 25, "rect": {"x": 76, "y": 253, "width": 23, "height": 31}}, "200": {"xOffset": 0, "yOffset": 10, "xAdvance": 19, "rect": {"x": 220, "y": 0, "width": 17, "height": 30}}, "201": {"xOffset": 0, "yOffset": 10, "xAdvance": 19, "rect": {"x": 220, "y": 192, "width": 17, "height": 30}}, "202": {"xOffset": 0, "yOffset": 10, "xAdvance": 19, "rect": {"x": 202, "y": 38, "width": 17, "height": 30}}, "204": {"xOffset": 0, "yOffset": 10, "xAdvance": 12, "rect": {"x": 238, "y": 138, "width": 10, "height": 30}}, "205": {"xOffset": 0, "yOffset": 10, "xAdvance": 12, "rect": {"x": 238, "y": 107, "width": 10, "height": 30}}, "210": {"xOffset": 0, "yOffset": 10, "xAdvance": 26, "rect": {"x": 27, "y": 216, "width": 24, "height": 30}}, "211": {"xOffset": 0, "yOffset": 10, "xAdvance": 26, "rect": {"x": 27, "y": 185, "width": 24, "height": 30}}, "212": {"xOffset": 0, "yOffset": 10, "xAdvance": 26, "rect": {"x": 52, "y": 150, "width": 24, "height": 30}}, "213": {"xOffset": 0, "yOffset": 9, "xAdvance": 26, "rect": {"x": 27, "y": 281, "width": 24, "height": 31}}, "217": {"xOffset": 0, "yOffset": 10, "xAdvance": 23, "rect": {"x": 103, "y": 0, "width": 21, "height": 30}}, "218": {"xOffset": 0, "yOffset": 10, "xAdvance": 23, "rect": {"x": 122, "y": 83, "width": 21, "height": 30}}, "221": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 77, "y": 129, "width": 22, "height": 30}}, "224": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 164, "y": 28, "width": 18, "height": 25}}, "225": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 182, "y": 329, "width": 18, "height": 25}}, "226": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 183, "y": 54, "width": 18, "height": 25}}, "227": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 164, "y": 110, "width": 18, "height": 25}}, "232": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 183, "y": 105, "width": 18, "height": 25}}, "233": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 164, "y": 54, "width": 18, "height": 25}}, "234": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 182, "y": 355, "width": 18, "height": 25}}, "236": {"xOffset": 0, "yOffset": 15, "xAdvance": 12, "rect": {"x": 238, "y": 231, "width": 10, "height": 25}}, "237": {"xOffset": 0, "yOffset": 15, "xAdvance": 12, "rect": {"x": 238, "y": 257, "width": 10, "height": 25}}, "242": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 143, "y": 166, "width": 19, "height": 25}}, "243": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 143, "y": 140, "width": 19, "height": 25}}, "244": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 143, "y": 275, "width": 19, "height": 25}}, "245": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 162, "y": 381, "width": 19, "height": 25}}, "249": {"xOffset": 0, "yOffset": 15, "xAdvance": 19, "rect": {"x": 220, "y": 243, "width": 17, "height": 25}}, "250": {"xOffset": 0, "yOffset": 15, "xAdvance": 19, "rect": {"x": 220, "y": 139, "width": 17, "height": 25}}, "253": {"xOffset": 0, "yOffset": 15, "xAdvance": 22, "rect": {"x": 122, "y": 342, "width": 20, "height": 32}}, "258": {"xOffset": 0, "yOffset": 10, "xAdvance": 25, "rect": {"x": 57, "y": 0, "width": 23, "height": 30}}, "259": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 183, "y": 161, "width": 18, "height": 25}}, "272": {"xOffset": 0, "yOffset": 15, "xAdvance": 27, "rect": {"x": 0, "y": 471, "width": 25, "height": 25}}, "273": {"xOffset": 0, "yOffset": 14, "xAdvance": 24, "rect": {"x": 76, "y": 459, "width": 22, "height": 26}}, "296": {"xOffset": 0, "yOffset": 9, "xAdvance": 14, "rect": {"x": 238, "y": 26, "width": 12, "height": 31}}, "297": {"xOffset": 0, "yOffset": 15, "xAdvance": 14, "rect": {"x": 238, "y": 0, "width": 12, "height": 25}}, "360": {"xOffset": 0, "yOffset": 9, "xAdvance": 23, "rect": {"x": 100, "y": 335, "width": 21, "height": 31}}, "361": {"xOffset": 0, "yOffset": 15, "xAdvance": 19, "rect": {"x": 220, "y": 477, "width": 17, "height": 25}}, "416": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 0, "y": 183, "width": 26, "height": 28}}, "417": {"xOffset": 0, "yOffset": 18, "xAdvance": 23, "rect": {"x": 26, "y": 488, "width": 21, "height": 22}}, "431": {"xOffset": 0, "yOffset": 11, "xAdvance": 27, "rect": {"x": 26, "y": 368, "width": 25, "height": 29}}, "432": {"xOffset": 0, "yOffset": 16, "xAdvance": 23, "rect": {"x": 100, "y": 90, "width": 21, "height": 24}}, "7840": {"xOffset": 0, "yOffset": 15, "xAdvance": 25, "rect": {"x": 76, "y": 220, "width": 23, "height": 32}}, "7841": {"xOffset": 0, "yOffset": 21, "xAdvance": 20, "rect": {"x": 163, "y": 254, "width": 18, "height": 27}}, "7842": {"xOffset": 0, "yOffset": 7, "xAdvance": 25, "rect": {"x": 76, "y": 285, "width": 23, "height": 33}}, "7843": {"xOffset": 0, "yOffset": 13, "xAdvance": 20, "rect": {"x": 163, "y": 342, "width": 18, "height": 27}}, "7844": {"xOffset": 0, "yOffset": 7, "xAdvance": 25, "rect": {"x": 52, "y": 290, "width": 23, "height": 33}}, "7845": {"xOffset": 0, "yOffset": 11, "xAdvance": 22, "rect": {"x": 122, "y": 140, "width": 20, "height": 29}}, "7846": {"xOffset": 0, "yOffset": 8, "xAdvance": 25, "rect": {"x": 52, "y": 324, "width": 23, "height": 32}}, "7847": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 183, "y": 131, "width": 18, "height": 29}}, "7848": {"xOffset": 0, "yOffset": 6, "xAdvance": 25, "rect": {"x": 53, "y": 35, "width": 23, "height": 34}}, "7849": {"xOffset": 0, "yOffset": 10, "xAdvance": 20, "rect": {"x": 201, "y": 187, "width": 18, "height": 30}}, "7850": {"xOffset": 0, "yOffset": 6, "xAdvance": 26, "rect": {"x": 32, "y": 0, "width": 24, "height": 34}}, "7851": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 201, "y": 239, "width": 18, "height": 29}}, "7852": {"xOffset": 0, "yOffset": 10, "xAdvance": 26, "rect": {"x": 26, "y": 424, "width": 24, "height": 37}}, "7853": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 201, "y": 269, "width": 18, "height": 33}}, "7854": {"xOffset": 0, "yOffset": 7, "xAdvance": 25, "rect": {"x": 53, "y": 70, "width": 23, "height": 33}}, "7855": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 201, "y": 358, "width": 18, "height": 29}}, "7856": {"xOffset": 0, "yOffset": 7, "xAdvance": 25, "rect": {"x": 52, "y": 220, "width": 23, "height": 33}}, "7857": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 164, "y": 80, "width": 18, "height": 29}}, "7858": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 52, "y": 254, "width": 23, "height": 35}}, "7859": {"xOffset": 0, "yOffset": 10, "xAdvance": 20, "rect": {"x": 182, "y": 193, "width": 18, "height": 30}}, "7860": {"xOffset": 0, "yOffset": 6, "xAdvance": 26, "rect": {"x": 51, "y": 398, "width": 24, "height": 34}}, "7861": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 182, "y": 299, "width": 18, "height": 29}}, "7862": {"xOffset": 0, "yOffset": 9, "xAdvance": 26, "rect": {"x": 52, "y": 181, "width": 24, "height": 38}}, "7863": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 183, "y": 457, "width": 18, "height": 33}}, "7864": {"xOffset": 0, "yOffset": 15, "xAdvance": 19, "rect": {"x": 202, "y": 69, "width": 17, "height": 32}}, "7865": {"xOffset": 0, "yOffset": 21, "xAdvance": 20, "rect": {"x": 183, "y": 0, "width": 18, "height": 27}}, "7866": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 202, "y": 134, "width": 17, "height": 33}}, "7867": {"xOffset": 0, "yOffset": 13, "xAdvance": 20, "rect": {"x": 164, "y": 0, "width": 18, "height": 27}}, "7868": {"xOffset": 0, "yOffset": 9, "xAdvance": 19, "rect": {"x": 202, "y": 102, "width": 17, "height": 31}}, "7869": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 183, "y": 28, "width": 18, "height": 25}}, "7870": {"xOffset": 0, "yOffset": 7, "xAdvance": 21, "rect": {"x": 162, "y": 407, "width": 19, "height": 33}}, "7871": {"xOffset": 0, "yOffset": 11, "xAdvance": 23, "rect": {"x": 100, "y": 115, "width": 21, "height": 29}}, "7872": {"xOffset": 0, "yOffset": 8, "xAdvance": 19, "rect": {"x": 202, "y": 449, "width": 17, "height": 32}}, "7873": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 163, "y": 282, "width": 18, "height": 29}}, "7874": {"xOffset": 0, "yOffset": 6, "xAdvance": 20, "rect": {"x": 202, "y": 414, "width": 18, "height": 34}}, "7875": {"xOffset": 0, "yOffset": 10, "xAdvance": 21, "rect": {"x": 144, "y": 47, "width": 19, "height": 30}}, "7876": {"xOffset": 0, "yOffset": 6, "xAdvance": 19, "rect": {"x": 220, "y": 31, "width": 17, "height": 34}}, "7877": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 163, "y": 312, "width": 18, "height": 29}}, "7878": {"xOffset": 0, "yOffset": 10, "xAdvance": 19, "rect": {"x": 202, "y": 0, "width": 17, "height": 37}}, "7879": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 163, "y": 193, "width": 18, "height": 33}}, "7880": {"xOffset": 0, "yOffset": 7, "xAdvance": 12, "rect": {"x": 238, "y": 169, "width": 10, "height": 33}}, "7881": {"xOffset": 0, "yOffset": 13, "xAdvance": 12, "rect": {"x": 238, "y": 203, "width": 10, "height": 27}}, "7882": {"xOffset": 0, "yOffset": 15, "xAdvance": 9, "rect": {"x": 250, "y": 339, "width": 7, "height": 32}}, "7883": {"xOffset": 0, "yOffset": 15, "xAdvance": 10, "rect": {"x": 250, "y": 305, "width": 8, "height": 33}}, "7884": {"xOffset": 0, "yOffset": 15, "xAdvance": 26, "rect": {"x": 28, "y": 52, "width": 24, "height": 32}}, "7885": {"xOffset": 0, "yOffset": 21, "xAdvance": 21, "rect": {"x": 163, "y": 140, "width": 19, "height": 27}}, "7886": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 27, "y": 247, "width": 24, "height": 33}}, "7887": {"xOffset": 0, "yOffset": 13, "xAdvance": 21, "rect": {"x": 144, "y": 78, "width": 19, "height": 27}}, "7888": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 27, "y": 151, "width": 24, "height": 33}}, "7889": {"xOffset": 0, "yOffset": 11, "xAdvance": 23, "rect": {"x": 100, "y": 225, "width": 21, "height": 29}}, "7890": {"xOffset": 0, "yOffset": 8, "xAdvance": 26, "rect": {"x": 27, "y": 118, "width": 24, "height": 32}}, "7891": {"xOffset": 0, "yOffset": 11, "xAdvance": 21, "rect": {"x": 143, "y": 192, "width": 19, "height": 29}}, "7892": {"xOffset": 0, "yOffset": 6, "xAdvance": 26, "rect": {"x": 52, "y": 115, "width": 24, "height": 34}}, "7893": {"xOffset": 0, "yOffset": 10, "xAdvance": 21, "rect": {"x": 143, "y": 456, "width": 19, "height": 30}}, "7894": {"xOffset": 0, "yOffset": 6, "xAdvance": 26, "rect": {"x": 51, "y": 471, "width": 24, "height": 34}}, "7895": {"xOffset": 0, "yOffset": 11, "xAdvance": 21, "rect": {"x": 163, "y": 441, "width": 19, "height": 29}}, "7896": {"xOffset": 0, "yOffset": 10, "xAdvance": 26, "rect": {"x": 51, "y": 433, "width": 24, "height": 37}}, "7897": {"xOffset": 0, "yOffset": 15, "xAdvance": 21, "rect": {"x": 144, "y": 106, "width": 19, "height": 33}}, "7898": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 0, "y": 118, "width": 26, "height": 30}}, "7899": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 100, "y": 199, "width": 21, "height": 25}}, "7900": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 0, "y": 306, "width": 26, "height": 30}}, "7901": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 100, "y": 173, "width": 21, "height": 25}}, "7902": {"xOffset": 0, "yOffset": 7, "xAdvance": 28, "rect": {"x": 0, "y": 149, "width": 26, "height": 33}}, "7903": {"xOffset": 0, "yOffset": 13, "xAdvance": 23, "rect": {"x": 100, "y": 145, "width": 21, "height": 27}}, "7904": {"xOffset": 0, "yOffset": 9, "xAdvance": 28, "rect": {"x": 0, "y": 274, "width": 26, "height": 31}}, "7905": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 100, "y": 31, "width": 21, "height": 25}}, "7906": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 0, "y": 212, "width": 26, "height": 35}}, "7907": {"xOffset": 0, "yOffset": 18, "xAdvance": 23, "rect": {"x": 81, "y": 0, "width": 21, "height": 30}}, "7908": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 100, "y": 367, "width": 21, "height": 32}}, "7909": {"xOffset": 0, "yOffset": 21, "xAdvance": 19, "rect": {"x": 220, "y": 66, "width": 17, "height": 27}}, "7910": {"xOffset": 0, "yOffset": 7, "xAdvance": 23, "rect": {"x": 121, "y": 465, "width": 21, "height": 33}}, "7911": {"xOffset": 0, "yOffset": 13, "xAdvance": 19, "rect": {"x": 220, "y": 449, "width": 17, "height": 27}}, "7912": {"xOffset": 0, "yOffset": 10, "xAdvance": 27, "rect": {"x": 26, "y": 337, "width": 25, "height": 30}}, "7913": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 99, "y": 465, "width": 21, "height": 25}}, "7914": {"xOffset": 0, "yOffset": 10, "xAdvance": 27, "rect": {"x": 0, "y": 374, "width": 25, "height": 30}}, "7915": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 100, "y": 281, "width": 21, "height": 25}}, "7916": {"xOffset": 0, "yOffset": 7, "xAdvance": 27, "rect": {"x": 0, "y": 437, "width": 25, "height": 33}}, "7917": {"xOffset": 0, "yOffset": 13, "xAdvance": 23, "rect": {"x": 100, "y": 307, "width": 21, "height": 27}}, "7918": {"xOffset": 0, "yOffset": 9, "xAdvance": 27, "rect": {"x": 0, "y": 405, "width": 25, "height": 31}}, "7919": {"xOffset": 0, "yOffset": 15, "xAdvance": 23, "rect": {"x": 100, "y": 255, "width": 21, "height": 25}}, "7920": {"xOffset": 0, "yOffset": 11, "xAdvance": 27, "rect": {"x": 0, "y": 337, "width": 25, "height": 36}}, "7921": {"xOffset": 0, "yOffset": 16, "xAdvance": 23, "rect": {"x": 100, "y": 57, "width": 21, "height": 32}}, "7922": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 77, "y": 98, "width": 22, "height": 30}}, "7923": {"xOffset": 0, "yOffset": 15, "xAdvance": 22, "rect": {"x": 122, "y": 231, "width": 20, "height": 32}}, "7924": {"xOffset": 0, "yOffset": 15, "xAdvance": 24, "rect": {"x": 77, "y": 31, "width": 22, "height": 32}}, "7925": {"xOffset": 0, "yOffset": 21, "xAdvance": 22, "rect": {"x": 122, "y": 170, "width": 20, "height": 27}}, "7926": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 77, "y": 64, "width": 22, "height": 33}}, "7927": {"xOffset": 0, "yOffset": 13, "xAdvance": 21, "rect": {"x": 142, "y": 401, "width": 19, "height": 34}}, "7928": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 99, "y": 433, "width": 22, "height": 31}}, "7929": {"xOffset": 0, "yOffset": 15, "xAdvance": 22, "rect": {"x": 122, "y": 198, "width": 20, "height": 32}}}, "kerningDict": {}}]], 0, 0, [0], [62], [162]], [[{"name": "sound_off", "rect": [1, 2, 34, 35], "offset": [0.5, -0.5], "originalSize": [35, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [163]], [[{"name": "btn_HD", "rect": [0, 0, 67, 55], "offset": [0, 0], "originalSize": [67, 55], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [164]], [[{"name": "line15", "rect": [0, 0, 800, 190], "offset": [0, 0], "originalSize": [800, 190], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [165]], [[{"name": "line30", "rect": [0, 0, 796, 236], "offset": [0, 0], "originalSize": [796, 236], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [166]]]]