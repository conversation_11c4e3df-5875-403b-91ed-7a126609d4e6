[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2tBXzjmRHWIetS1zkxuiC", "d40pJjmyhHEbhzVbktBg8e", "67kUISXXxOLq1uBKZc4OOj", "deuUI+/ARHNJzRgUm/E5TO", "97s51Wx61O7JuAuUWKNJMS", "adw94Z+hpN57wutNivq8Q5", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "72prqSALtBo4Z/Xp+/hwt+", "6dASK3G0xITKlrnb7C9RnX", "2cWB/vWPRHja3uQTinHH30", "60v+vYN7BEk77zfnxGZGCx", "379bh9zcdNRrlOVVB/Yy8N", "58OuMu+11Oeanr1bkhj+qp", "e03N62YIJB/56aRTatYe1r", "634P9W6JFHUYkpPlMua58H", "e2fhlgUYxHPq5uMXIQ8k6C", "24xd2Xl+xHVZeWwPN10Wzf", "53DK8iqelAy7zW6nrAKHCm", "47dfDooNdEyoHVWd1IzGdk", "d82n49/IVAvIEqsa0xvvk0", "9fmizsfbtNnZvql2JHbSC7", "d4J0S4kkxE+IseIV2vnuUk", "f88FrrH8lKJKC/AOv025cs", "87BZv5te1Ie75L5OSDPEDz"], ["node", "_N$file", "_spriteFrame", "lbNumber", "spriteNumber", "sfChoose", "sfUnChoose", "_textureSetter", "_N$target", "_N$font", "_parent", "_defaultClip", "root", "nodeNotify", "lbTotalMoney", "lbUserBetValue", "lbTitleBet", "nodeConfirmBet", "lbChooseNumber", "edbBetValue", "layoutNumber", "data", "itemNumber"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs", "_color"], 0, 4, 1, 9, 5, 2, 7, 5], ["cc.Label", ["_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_string", "_lineHeight", "_N$overflow", "_enabled", "node", "_materials", "_N$file"], -5, 1, 3, 6], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint", "_color"], 1, 2, 4, 5, 1, 2, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "_N$spacingY", "node", "_layoutSize", "_N$cellSize"], -1, 1, 5, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "_N$normalColor", "clickEvents", "_N$target"], 1, 1, 5, 9, 1], ["cc.PrivateNode", ["_name", "_obj<PERSON><PERSON>s", "_active", "_zIndex", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], -1, 1, 9, 4, 5, 5, 7, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["103a3lJBYdEYI+EZc7vLAyc", ["node", "layoutNumber", "edbBetValue", "lbChooseNumber", "nodeConfirmBet", "lbTitleBet", "lbUserBetValue", "lbTotalMoney", "nodeNotify", "itemNumber"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["62f20CPLf5Fu6KCZcot4IZ+", ["node", "spriteNumber", "lbNumber", "sfChoose", "sfUnChoose"], 3, 1, 1, 1, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$horizontalAlign", "_N$fontSize", "_N$lineHeight", "node"], -2, 1], ["cc.EditBox", ["node", "textChanged", "editingDidEnded", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 3, 1, 9, 9, 1, 1, 1]], [[12, 0, 1, 2], [3, 0, 5, 2, 3, 9, 4, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [13, 0, 1, 2, 3, 4, 1], [4, 0, 4, 5, 2], [1, 4, 2, 0, 3, 1, 8, 9, 6], [1, 4, 2, 5, 0, 1, 8, 9, 10, 6], [14, 0, 1, 2, 3], [0, 0, 4, 7, 5, 3, 6, 2], [0, 0, 4, 7, 5, 3, 6, 8, 2], [6, 2, 3, 1], [4, 4, 5, 6, 1], [0, 0, 4, 5, 3, 6, 8, 2], [0, 0, 1, 4, 5, 3, 6, 3], [6, 0, 1, 2, 4, 3, 5, 3], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 5], [7, 0, 1, 2, 3, 4, 5, 6, 10, 7, 8, 9, 5], [15, 0, 1, 2, 3, 4, 5, 6], [0, 0, 2, 4, 5, 3, 6, 8, 3], [3, 0, 5, 6, 2, 3, 4, 8, 7, 2], [4, 0, 4, 5, 6, 2], [1, 4, 2, 5, 0, 3, 1, 8, 9, 10, 7], [0, 0, 4, 5, 3, 6, 2], [10, 0, 1, 2, 1], [4, 1, 0, 2, 4, 5, 6, 4], [1, 7, 4, 2, 5, 0, 3, 1, 8, 9, 10, 8], [8, 0, 2], [0, 0, 7, 5, 3, 2], [0, 0, 1, 4, 7, 3, 3], [0, 0, 1, 4, 7, 5, 3, 3], [0, 0, 4, 7, 3, 6, 8, 2], [0, 0, 4, 7, 3, 8, 2], [0, 0, 1, 4, 5, 3, 6, 8, 3], [0, 0, 4, 5, 3, 9, 6, 2], [3, 0, 6, 2, 3, 4, 7, 2], [3, 0, 1, 6, 2, 3, 4, 7, 3], [3, 0, 5, 2, 3, 4, 2], [3, 0, 1, 5, 2, 3, 4, 3], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [5, 1, 0, 2, 3, 4, 5, 6, 5], [5, 0, 4, 2], [4, 1, 4, 5, 6, 2], [4, 3, 1, 4, 3], [1, 4, 2, 0, 3, 1, 8, 9, 10, 6], [1, 2, 5, 0, 3, 1, 8, 9, 10, 6], [1, 2, 5, 0, 3, 1, 6, 8, 9, 7], [1, 4, 2, 5, 0, 3, 1, 6, 8, 9, 8], [1, 4, 0, 3, 1, 8, 9, 10, 5], [16, 0, 1, 2, 3, 4, 5, 1]], [[[{"name": "bpdere", "rect": [0, 0, 668, 361], "offset": [0, 0], "originalSize": [668, 361], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [7], [12]], [[{"name": "boderTienDanhCopy", "rect": [0, 0, 332, 81], "offset": [0, 0], "originalSize": [332, 81], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [7], [13]], [[{"name": "btn_number", "rect": [0, 0, 61, 63], "offset": [0, 0], "originalSize": [61, 63], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [7], [14]], [[{"name": "buttonOk", "rect": [0, 0, 165, 46], "offset": [0, 0.5], "originalSize": [165, 47], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [7], [15]], [[{"name": "buttonBack", "rect": [0, 0, 165, 47], "offset": [0, 0], "originalSize": [165, 47], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [7], [16]], [[[26, "LoDeChooseView"], [27, "LoDeChooseView", [-12, -13, -14, -15, -16, -17, -18, -19, -20, -21], [[23, -2, [100, 101], 99], [38, -11, -10, -9, -8, -7, -6, -5, -4, -3, 102]], [0, "aa4joq2LxOQppmr6YiGZwi", -1]], [8, "layoutNumber", 1, [-23, -24, -25, -26, -27, -28, -29, -30, -31, -32], [[39, 1, 3, 8.5, 8.5, -22, [5, 700, 63], [5, 61, 63]]], [0, "07QWCSsO1OU5JnSOG1CWcN", 1], [5, 700, 63]], [28, "nodeConfirm", false, 1, [-33, -34, -35, -36, -37, -38], [0, "51a9Lt5VZJlpNl6eNJul0X", 1]], [29, "nofity", false, 1, [-40, -41, -42], [[23, -39, [97, 98], 96]], [0, "e5oB6rwwJKkb9s4fEoLZvw", 1]], [34, "edbBetValue", [-44, -45, -46], [-43], [0, "95xUxmFApDa6v9V5XoUorW", 1], [5, 300, 70], [0, 2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "content", 3, [-48, -49, -50], [[40, 2, -47]], [0, "60txybCQ5JTKv2wI2Wftzv", 1], [5, 300, 200], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "lbNumberChoose", false, [-52, -53], [-51], [0, "69nPQqUS1ARrLDOaV0Qjmt", 1], [5, 194.26999999999998, 63], [0, -24, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-58], [[-54, [3, -57, -56, -55, 14, 15]], 1, 4], [0, "f4Jb1TAhNNcYvlscNcXf3X", 1], [5, 61, 63], [-319.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-63], [[-59, [3, -62, -61, -60, 18, 19]], 1, 4], [0, "49tyZdGhdJRrgtDXPjdVoW", 1], [5, 61, 63], [-250, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-68], [[-64, [3, -67, -66, -65, 22, 23]], 1, 4], [0, "30qwbqRaRNorStCdXItwGS", 1], [5, 61, 63], [-180.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-73], [[-69, [3, -72, -71, -70, 26, 27]], 1, 4], [0, "59f/qTu+ZGDKH9KPDacbM8", 1], [5, 61, 63], [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-78], [[-74, [3, -77, -76, -75, 30, 31]], 1, 4], [0, "ffUhVnc1FIsbEx64nKCCAf", 1], [5, 61, 63], [-41.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-83], [[-79, [3, -82, -81, -80, 34, 35]], 1, 4], [0, "93tFigq2pFMqzFSbdMfsfp", 1], [5, 61, 63], [28, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-88], [[-84, [3, -87, -86, -85, 38, 39]], 1, 4], [0, "59MbL0NSRAdJ/GSBAXdvVG", 1], [5, 61, 63], [97.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-93], [[-89, [3, -92, -91, -90, 42, 43]], 1, 4], [0, "1dniTOnPBBCrh0sGm0zfKz", 1], [5, 61, 63], [167, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-98], [[-94, [3, -97, -96, -95, 46, 47]], 1, 4], [0, "40vbfbR15CHayTK+/lFPid", 1], [5, 61, 63], [236.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "number", 2, [-103], [[-99, [3, -102, -101, -100, 50, 51]], 1, 4], [0, "3fYR8eEJRH/qiv5bqwYZff", 1], [5, 61, 63], [306, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btnCancel", 1, [-106], [[14, 1.1, 3, -105, [[7, "103a3lJBYdEYI+EZc7vLAyc", "onCancelClicked", 1]], [4, 4292269782], -104]], [0, "6eSSeoyiFB0qS2eDSKlAnt", 1], [5, 157, 50], [-527, 317, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btnOk", 1, [-109], [[14, 1.1, 3, -108, [[7, "103a3lJBYdEYI+EZc7vLAyc", "openConfirmBet", 1]], [4, 4292269782], -107]], [0, "59w9fUj9FJXYu0bv1GwkFT", 1], [5, 157, 50], [527, 314, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "lbTitleBet", 6, [-111, -112], [-110], [0, "81BHRy9mlBrreos9mCPN2B", 1], [5, 224.14999999999998, 63], [0, 0, 0.5], [-122, 68.5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "lbMoney", 6, [-114, -115], [-113], [0, "7fIiqZYVNC7pfSbPvO2oIm", 1], [5, 204.81, 63], [0, 0, 0.5], [-122, 5.5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "lbTotalMoney", 6, [-117, -118], [-116], [0, "07saezVF1Hhppd3M5gc1aO", 1], [5, 236.19, 63], [0, 0, 0.5], [-122, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btnCancel", 3, [-121], [[14, 1.1, 3, -120, [[7, "103a3lJBYdEYI+EZc7vLAyc", "chooseAgain", 1]], [4, 4292269782], -119]], [0, "fb4i63u0lEV7YTSJI1Kln8", 1], [5, 157, 50], [-145, -133, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "btnOk", 3, [-124], [[14, 1.1, 3, -123, [[7, "103a3lJBYdEYI+EZc7vLAyc", "confirmBet", 1]], [4, 4292269782], -122]], [0, "a6m4a5MO9Mtb3rZo8/ss6o", 1], [5, 157, 50], [144, -133, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "black", 100, 1, [[20, 0, -125, [0], 1], [10, -126, [4, 4292269782]]], [0, "1572uqq8JMopURgdeHImjD", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "nen popup", 1, [[24, 1, 0, false, -127, [2], 3], [10, -128, [4, 4292269782]]], [0, "0fnLCPglNMMq8vqcwxTjwJ", 1], [5, 1280, 700], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "title_0", 1, [-129, 7], [0, "64k0zKORdHhrjMHrKBzbgt", 1], [5, 441, 50], [0, 261, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "boxInput", 1, [-130, 5], [0, "b0GLjI+P5OZ7zvZ2Fb1AqK", 1], [0, -281, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn_pp", 18, [-132], [[11, -131, [58], 59]], [0, "cfsp/pRNxDSIbmvZCjq1hq", 1], [5, 165, 47]], [8, "btn_pp", 19, [-134], [[11, -133, [62], 63]], [0, "f7EH6Upb9EuoFJswJB85rx", 1], [5, 165, 46]], [18, "black copy", 229, 3, [[20, 0, -135, [64], 65], [10, -136, [4, 4292269782]]], [0, "96kAv6mRFMjLmwtwwwZ5AQ", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "nen popup", 3, [[24, 1, 0, false, -137, [66], 67], [10, -138, [4, 4292269782]]], [0, "a0QLqV8LdNwIcMB+RoF4RZ", 1], [5, 800, 400], [0, -16, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btn_pp", 23, [-140], [[11, -139, [84], 85]], [0, "02/kndjRVBZYjkXEVuBeiK", 1], [5, 165, 47]], [8, "btn_pp", 24, [-142], [[11, -141, [88], 89]], [0, "35BsQHoI1Op62FkdNy9MVf", 1], [5, 165, 46]], [18, "black", 100, 4, [[20, 0, -143, [90], 91], [10, -144, [4, 4292269782]]], [0, "3e7ag3ZNhDZa96Wkz56oFN", 1], [5, 3000, 3000], [0, 226, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "title", 1, [[43, "BẢNG CHỌN SỐ ĐỀ", 22, false, 1, 1, -145, [4], 5]], [0, "e2TFToSDJBVLkn0T4f97O+", 1], [5, 339.63, 27.5], [0, 347, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "lbBet", false, 27, [[44, 26, 50, false, 1, 1, -146, [6], 7]], [0, "97N8Kp3rpC4Khy+GVELyWF", 1], [5, 0, 32.5], [0, -35, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[6, "Đặt Đề: ", 26, 50, false, 1, -147, [8], 9]], [0, "5cHMxWWIpGQZcSynSBEfrF", 1], [5, 91.36, 63], [0, 0, 0], [-97.13499999999999, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[6, "03,04,05", 26, 50, false, 1, -148, [10], 11]], [0, "f8NUAegJBGF6V1OEibffBG", 1], [4, 4278255360], [5, 102.91, 63], [0, 0, 0], [-5.7749999999999915, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, false, "Đặt Đề: <color=#00ff00>03,04,05</c>", 1, 26, 50, 7], [1, "lbNumb", 8, [-149], [0, "4cZbnll1RJ66R/LvDcHCQo", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 41, [12]], [4, 0, 8, [13]], [1, "lbNumb", 9, [-150], [0, "8czeDbTlROP4v2C9uk6wER", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 44, [16]], [4, 0, 9, [17]], [1, "lbNumb", 10, [-151], [0, "barW9zjeRKYrtxo8wPfLf7", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 47, [20]], [4, 0, 10, [21]], [1, "lbNumb", 11, [-152], [0, "02aZB2dZJEdaYVQkbaCzv5", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 50, [24]], [4, 0, 11, [25]], [1, "lbNumb", 12, [-153], [0, "2cRjkwMCJOsb9e9//PrScY", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 53, [28]], [4, 0, 12, [29]], [1, "lbNumb", 13, [-154], [0, "b3+SEm525CE6VgvYfyxXQk", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 56, [32]], [4, 0, 13, [33]], [1, "lbNumb", 14, [-155], [0, "87B+ntrAZJtYaVtyAGiGsk", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 59, [36]], [4, 0, 14, [37]], [1, "lbNumb", 15, [-156], [0, "6aa1z5zqdPJrfhmsT41bgl", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 62, [40]], [4, 0, 15, [41]], [1, "lbNumb", 16, [-157], [0, "b1QQhWIRFLArIWHPa7ErhZ", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 65, [44]], [4, 0, 16, [45]], [1, "lbNumb", 17, [-158], [0, "4aab1+q7BMtrQTyxxpIO6R", 1], [4, 4278190080], [5, 34.95, 50.4]], [5, "00", 30, false, 1, 1, 68, [48]], [4, 0, 17, [49]], [22, "BACKGROUND_SPRITE", 28, [[41, 1, -159, [52], 53]], [0, "c4SifecANPrJq5G97QkAW+", 1], [5, 332, 81]], [36, "BACKGROUND_SPRITE", 5, [-160], [0, "40CdLBF0BMRLm+IE6vjimM", 1], [5, 300, 70]], [42, false, 1, 72], [37, "TEXT_LABEL", false, 5, [-161], [0, "eduPVq5uVBWo3gAPqDNACS", 1], [5, 300, 50]], [45, 22, 50, false, 1, 1, 1, 74, [54]], [1, "PLACEHOLDER_LABEL", 5, [-162], [0, "2af5mqo/ZEHZJuIUIwluJh", 1], [4, 4290493371], [5, 300, 60]], [46, "NHẬP TIỀN CƯỢC", 22, 50, false, 1, 1, 1, 76, [55]], [48, 5, [[7, "103a3lJBYdEYI+EZc7vLAyc", "onEditingValueChanged", 1]], [[7, "103a3lJBYdEYI+EZc7vLAyc", "onEditingValueDidEnd", 1]], 75, 77, 73], [13, "label", false, 29, [[25, false, "<PERSON><PERSON><PERSON>", 20, 50, false, 1, 1, -163, [56], 57]], [0, "62afqut1JK6KYPBv8LZNWn", 1], [5, 38.5, 25]], [13, "label", false, 30, [[25, false, "Đồng ý", 20, 50, false, 1, 1, -164, [60], 61]], [0, "82yx4OkCVGHoyPzjBqTOpZ", 1], [5, 65.5, 25]], [12, "label", 3, [[47, "Thông tin đặt cược", false, 1, 1, -165, [68], 69]], [0, "15VKmZ3itMybNfJ98RYLx/", 1], [5, 465, 50], [0, 139, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 20, [[6, "Đặt Đề: ", 30, 50, false, 1, -166, [70], 71]], [0, "afwOD61JtLEIV/b/ODkbHt", 1], [5, 105.41, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "RICHTEXT_CHILD", 1024, false, -32768, 20, [[6, "03,04,05", 30, 50, false, 1, -167, [72], 73]], [0, "279vVoH4FImahxwlY+NPpp", 1], [4, 4278255360], [5, 118.74, 63], [0, 0, 0], [105.41, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, false, "Đặt Đề: <color=#00ff00>03,04,05</c>", 1, 30, 50, 20], [15, "RICHTEXT_CHILD", 1024, false, -32768, 21, [[6, "Số tiền: ", 30, 50, false, 1, -168, [74], 75]], [0, "a1UZ89WU5Kr46+pseQPRi9", 1], [5, 110.02, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "RICHTEXT_CHILD", 1024, false, -32768, 21, [[6, "10.000", 30, 50, false, 1, -169, [76], 77]], [0, "a7iWgvfEtK2JZdVq1WSzw3", 1], [4, 4278451964], [5, 94.79, 63], [0, 0, 0], [110.02, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, false, "Số tiền: <color=#FCFE03>10.000</c>", 1, 30, 50, 21], [15, "RICHTEXT_CHILD", 1024, false, -32768, 22, [[6, "Tổng tiền: ", 30, 50, false, 1, -170, [78], 79]], [0, "9eLbUyOLVPArvHxmbMmiTn", 1], [5, 141.4, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "RICHTEXT_CHILD", 1024, false, -32768, 22, [[6, "20.000", 30, 50, false, 1, -171, [80], 81]], [0, "ed9ZemqXlH+ZuWGnJwukei", 1], [4, 4278451964], [5, 94.79, 63], [0, 0, 0], [141.4, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, false, "Tổng tiền: <color=#FCFE03>20.000</c>", 1, 30, 50, 22], [13, "label", false, 33, [[21, "<PERSON><PERSON><PERSON>", 20, 50, false, 1, 1, -172, [82], 83]], [0, "9eJs0gmeVIoooaOdfHursh", 1], [5, 38.5, 25]], [13, "label", false, 34, [[21, "Đồng ý", 20, 50, false, 1, 1, -173, [86], 87]], [0, "bcC7uQSQRNeJXKFG5X6uUv", 1], [5, 65.5, 25]], [22, "bg", 4, [[11, -174, [92], 93]], [0, "e6/ulRrblFY5Al7Ip0xOF5", 1], [5, 579, 56]], [33, "label", 4, [[21, "<PERSON><PERSON>", 23, 50, false, 1, 1, -175, [94], 95]], [0, "99JYhJdR5OqrHBv7xsmTqw", 1], [4, 4278651133], [5, 113.28, 28.75]]], 0, [0, 12, 1, 0, 0, 1, 0, 13, 4, 0, 14, 90, 0, 15, 87, 0, 16, 84, 0, 17, 3, 0, 18, 40, 0, 19, 78, 0, 20, 2, 0, 0, 1, 0, -1, 25, 0, -2, 26, 0, -3, 36, 0, -4, 27, 0, -5, 2, 0, -6, 28, 0, -7, 18, 0, -8, 19, 0, -9, 3, 0, -10, 4, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, -6, 13, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, -10, 17, 0, -1, 31, 0, -2, 32, 0, -3, 81, 0, -4, 6, 0, -5, 23, 0, -6, 24, 0, 0, 4, 0, -1, 35, 0, -2, 93, 0, -3, 94, 0, -1, 78, 0, -1, 72, 0, -2, 74, 0, -3, 76, 0, 0, 6, 0, -1, 20, 0, -2, 21, 0, -3, 22, 0, -1, 40, 0, -1, 38, 0, -2, 39, 0, -1, 43, 0, 3, 42, 0, 4, 43, 0, 0, 8, 0, -1, 41, 0, -1, 46, 0, 3, 45, 0, 4, 46, 0, 0, 9, 0, -1, 44, 0, -1, 49, 0, 3, 48, 0, 4, 49, 0, 0, 10, 0, -1, 47, 0, -1, 52, 0, 3, 51, 0, 4, 52, 0, 0, 11, 0, -1, 50, 0, -1, 55, 0, 3, 54, 0, 4, 55, 0, 0, 12, 0, -1, 53, 0, -1, 58, 0, 3, 57, 0, 4, 58, 0, 0, 13, 0, -1, 56, 0, -1, 61, 0, 3, 60, 0, 4, 61, 0, 0, 14, 0, -1, 59, 0, -1, 64, 0, 3, 63, 0, 4, 64, 0, 0, 15, 0, -1, 62, 0, -1, 67, 0, 3, 66, 0, 4, 67, 0, 0, 16, 0, -1, 65, 0, -1, 70, 0, 3, 69, 0, 4, 70, 0, 0, 17, 0, -1, 68, 0, 8, 18, 0, 0, 18, 0, -1, 29, 0, 8, 19, 0, 0, 19, 0, -1, 30, 0, -1, 84, 0, -1, 82, 0, -2, 83, 0, -1, 87, 0, -1, 85, 0, -2, 86, 0, -1, 90, 0, -1, 88, 0, -2, 89, 0, 8, 23, 0, 0, 23, 0, -1, 33, 0, 8, 24, 0, 0, 24, 0, -1, 34, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, -1, 37, 0, -1, 71, 0, 0, 29, 0, -1, 79, 0, 0, 30, 0, -1, 80, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, -1, 91, 0, 0, 34, 0, -1, 92, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, -1, 42, 0, -1, 45, 0, -1, 48, 0, -1, 51, 0, -1, 54, 0, -1, 57, 0, -1, 60, 0, -1, 63, 0, -1, 66, 0, -1, 69, 0, 0, 71, 0, -1, 73, 0, -1, 75, 0, -1, 77, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 85, 0, 0, 86, 0, 0, 88, 0, 0, 89, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 21, 1, 5, 10, 28, 7, 10, 27, 175], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 42, 43, 45, 46, 48, 49, 51, 52, 54, 55, 57, 58, 60, 61, 63, 64, 66, 67, 69, 70, 75, 77, 84, 87, 90], [-1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, -1, 5, 6, -1, 2, -1, -1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 2, -1, 2, -1, 1, 11, -1, -2, 11, -1, -2, 22, 9, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 2, 1, 1, 9, 9, 9], [0, 8, 0, 17, 0, 18, 0, 6, 0, 1, 0, 1, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 0, 2, 3, 0, 19, 0, 0, 0, 6, 0, 9, 0, 6, 0, 10, 0, 8, 0, 20, 0, 21, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 6, 0, 9, 0, 6, 0, 10, 0, 8, 0, 22, 0, 6, 7, 7, 11, 7, 7, 11, 23, 1, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 4, 5, 6, 6, 1, 1, 1]], [[{"name": "nameBar", "rect": [0, 0, 579, 56], "offset": [0, 0], "originalSize": [579, 56], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [7], [24]], [[{"name": "btn_number_press", "rect": [0, 0, 61, 63], "offset": [0, 0], "originalSize": [61, 63], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [7], [25]]]]