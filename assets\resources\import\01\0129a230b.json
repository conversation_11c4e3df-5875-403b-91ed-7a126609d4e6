[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "a6TVJhpzhP5rdcqBHeEV+E", "a9VpD0DP5LJYQPXITZq+uj", "62TTC6G+hHCamgiYceJ0w6", "e8zRISlSpJaY0/oMNgi0gY", "7a/QZLET9IDreTiBfRn2PD", "7fuafF7otG17KL3OIVYHQh", "e1NJ8c5BZHI5JcP4RUdG0s", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "data", "_N$file", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["83612/tvDhOLah+ftXBatoj", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$content", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node"], -3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_spacingX", "node", "_materials", "_N$file"], -1, 1, 3, 6]], [[2, 0, 1, 2], [0, 0, 4, 6, 3, 2, 5, 7, 2], [0, 0, 4, 3, 2, 5, 2], [0, 0, 4, 3, 2, 5, 7, 2], [3, 1, 6, 1], [1, 0, 1, 3, 4, 5, 3], [5, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 1, 4, 3, 2, 5, 7, 3], [0, 0, 4, 6, 3, 2, 5, 8, 7, 2], [0, 0, 4, 6, 3, 2, 5, 8, 2], [0, 0, 4, 6, 2, 5, 8, 2], [6, 0, 1, 2, 1], [7, 0, 1], [2, 1, 1], [3, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3], [1, 0, 3, 4, 5, 2], [1, 2, 0, 1, 3, 4, 5, 4], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2, 3, 4, 5, 6, 7], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 5]], [[[{"name": "TEXT_HD", "rect": [0, 0, 926, 334], "offset": [0, 0], "originalSize": [926, 334], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [2], [2]], [[[6, "dragonTigerHelpView"], [7, "dragonTigerHelpView", [-4, -5, -6, -7, -8], [[12, -2, [13, 14], 12], [13, -3]], [14, -1]], [1, "btnClose", 1, [-11], [[15, 3, -10, [[16, "83612/tvDhOLah+ftXBatoj", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -9]], [0, "13yjKDFnVNZ6AAvSO91mEo", 1], [5, 80, 80], [517.371, 276.634, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "black", 100, 1, [[17, 0, -12, [0], 1], [4, -13, [4, 4292269782]]], [0, "9082MxgbxHNIBcevNUN1fe", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nen popup", 1, [[18, 1, 0, false, -14, [2], 3], [4, -15, [4, 4292269782]]], [0, "241vm6yaND3ruq4O7+83Ie", 1], [5, 1100, 618]], [1, "title_HD", 1, [-17], [[19, "default", "animation", 0, false, "animation", -16, [6]]], [0, "8dmvpZRXdCmo9CQe3GXE52", 1], [5, 270, 89.04], [0, 274.367, 0, 0, 0, 0, 1, 1.5, 1, 1]], [9, "scrollview", 1, [-19], [[20, false, 0.75, 0.23, null, null, null, -18]], [0, "cafBi9cEdFdLzXmmoC3Kup", 1], [5, 1020, 510], [0, 0.5, 1], [0, 225, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "view", 6, [-21], [[21, 0, -20, [9]]], [0, "99WhUSkfJBTazBqOuKi6GR", 1], [5, 1000, 510], [0, 0.5, 1]], [3, "New Node", 5, [[22, "HƯỚNG DẪN", 25, false, 2, -22, [4], 5]], [0, "053AOSIAhEG7cD+ZjIbReT", 1], [5, 258.19, 31.25], [0, 34.473, 0, 0, 0, 0, 1, 0.7, 1, 1]], [11, "content", 7, [-23], [0, "c8imFkEyFAa7y4BOT8FjJy", 1], [5, 1000, 510], [0, 0.5, 1]], [3, "TEXT_HD", 9, [[5, 2, false, -24, [7], 8]], [0, "8bvoOJfvpAfal6nrJuXCF/", 1], [5, 926, 334], [0, -204, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 2, [[5, 0, false, -25, [10], 11]], [0, "5fLv6TrQJIla6eMV12P+6g", 1], [5, 70, 70]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 2, 0, 4, 2, 0, 0, 2, 0, -1, 11, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, -1, 8, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, -1, 9, 0, 0, 8, 0, -1, 10, 0, 0, 10, 0, 0, 11, 0, 5, 1, 25], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 6, -1, -1, 1, -1, -1, 1, 7, -1, -2], [0, 3, 0, 4, 0, 5, 6, 0, 7, 0, 0, 8, 1, 1, 9]]]]