[1, ["45VlIt8htFebvSzLjo5wDL"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "bonus_p2", "\nbonus_p2.png\nsize: 267,267\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbg2\n  rotate: false\n  xy: 0, 0\n  size: 141, 133\n  orig: 141, 133\n  offset: 0, 0\n  index: -1\nitem_bonus2\n  rotate: true\n  xy: 181, 169\n  size: 98, 65\n  orig: 98, 65\n  offset: 0, 0\n  index: -1\nsao\n  rotate: false\n  xy: 181, 141\n  size: 28, 28\n  orig: 30, 30\n  offset: 1, 1\n  index: -1\nsheld\n  rotate: false\n  xy: 0, 133\n  size: 142, 134\n  orig: 142, 134\n  offset: 0, 0\n  index: -1\ntext_bn\n  rotate: true\n  xy: 142, 137\n  size: 130, 39\n  orig: 130, 39\n  offset: 0, 0\n  index: -1\n", ["bonus_p2.png"], {"skeleton": {"hash": "Gey2RBCtsS1te2uwGumeHZoxPCs", "spine": "3.6.53", "width": 142, "height": 140.17}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "item_frs1", "parent": "bone", "x": 8.51, "y": -17.23}, {"name": "sao", "parent": "bone", "x": 46.05, "y": -38.88}, {"name": "text_frs", "parent": "root", "length": 40, "rotation": 1.33, "x": -21.25, "y": -52.11}], "slots": [{"name": "bg1", "bone": "bone", "attachment": "bg2"}, {"name": "sheld", "bone": "bone", "attachment": "sheld"}, {"name": "item", "bone": "item_frs1", "attachment": "item_bonus2"}, {"name": "item_frs1", "bone": "item_frs1"}, {"name": "text_frs", "bone": "text_frs", "attachment": "text_bn"}, {"name": "sao", "bone": "sao", "color": "ffffff00", "attachment": "sao", "blend": "additive"}], "skins": {"default": {"bg1": {"bg2": {"width": 141, "height": 133}}, "item": {"item_bonus2": {"x": -5.58, "y": 11.16, "width": 98, "height": 65}}, "sao": {"sao": {"x": -0.29, "y": -0.84, "width": 30, "height": 30}}, "sheld": {"sheld": {"width": 142, "height": 134}}, "text_frs": {"text_bn": {"x": 19.71, "y": -2.02, "rotation": -1.33, "width": 130, "height": 39}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}, "item_frs1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -3.19}, {"time": 0.7333, "x": 0, "y": 10.52}, {"time": 1.5, "x": 0, "y": -3.19}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.7333, "x": 1.061, "y": 1.035}, {"time": 1.5, "x": 1, "y": 1}]}, "text_frs": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -3.45}, {"time": 1, "angle": 1.98}, {"time": 1.5, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": -0.03, "y": 15.33}, {"time": 1, "x": -0.23, "y": 11.41}, {"time": 1.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": 1}]}}}, "win": {"slots": {"sao": {"color": [{"time": 0.8333, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "sao"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 1}]}, "item_frs1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.9333, "x": 0, "y": 8.36}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 1}]}, "text_frs": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.9, "x": -0.14, "y": 6.1}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.9, "x": 1.121, "y": 1.121}, {"time": 1.8333, "x": 1, "y": 1}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4667, "x": 1.093, "y": 1.093}, {"time": 0.8667, "x": 1, "y": 1}, {"time": 1.3333, "x": 1.093, "y": 1.093}, {"time": 1.8333, "x": 1, "y": 1}]}, "sao": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.8333, "angle": -80.93}, {"time": 1.8333, "angle": -178.62}], "translate": [{"time": 0, "x": -78.25, "y": 55.69}, {"time": 0.8333, "x": -74.83, "y": 65.14}, {"time": 1.8333, "x": -73.74, "y": 58.34}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 1.209, "y": 1.209}, {"time": 1.8333, "x": 0.686, "y": 0.686}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]