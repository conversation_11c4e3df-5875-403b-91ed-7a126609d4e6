[1, ["ecpdLyjvZBwrvm+cedCcQy", "8bo+zw+UdH6bz8bdhxNl6r", "fdNoodJKVLj4dF1TLppv2g", "69xAok1m1M+pdu8BCQP2Mq", "4bLUAx7E9E4acD94l3HGxy", "54MqSSN+BNUaLDpPbDphq+", "04EsZ/T+hEebSP1ggZG4r6", "36WYzPks9EY6GxgFnU7bg0", "a9VpD0DP5LJYQPXITZq+uj", "f3TtjOS/NBqbNBHzH/oGL3", "51VoaYQp9IipCqk3lhnni2", "89WDor2/dBYK8nJ+owzcqD", "0dTgPgJClOJpaVzRIIGGVg", "edW2J5ruRM95PTIe94mz/v", "55jT3WNA5PR4OYTTRzkntz", "d7WDd7YLlKG42Zbw/TSLF1", "509CzWQ51HBKKnjsumSGsC", "f4cH1pcN5Ga6sQPfUrY0xo", "85tTyNBt1Pj4M6d6mPW57i", "8evSAQFN9NorJL2WFXV+6p", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30", "dbrfMs+X5Gi7ZzsJhOw3Mb", "2dTZ9o85RCe5tPSvX0rTeR", "66eWk+cd9L25KdoFP6MIfq", "77AonYQ+dBTa3fDxUxbDHX"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "data", "_parent", "_N$normalSprite", "_defaultClip"], [["cc.Node", ["_name", "_zIndex", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], -1, 4, 9, 5, 1, 7, 2, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 6, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["9f9e1IJ4WFMSqhdfSr9lY+W", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["<PERSON>.<PERSON>", ["node", "_N$content"], 3, 1, 1], ["34c4fFXj/hAgrtv/y80/0lh", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3]], [[3, 0, 1, 2], [2, 0, 1, 2, 3, 4, 3], [0, 0, 1, 7, 5, 4, 6, 3], [0, 0, 1, 7, 9, 4, 6, 8, 3], [0, 0, 1, 7, 9, 5, 4, 6, 8, 3], [0, 0, 1, 7, 5, 4, 6, 8, 3], [2, 2, 3, 4, 1], [5, 0, 2], [0, 0, 1, 9, 5, 4, 3], [0, 0, 1, 9, 5, 4, 6, 10, 8, 3], [0, 0, 7, 9, 5, 4, 6, 8, 2], [0, 0, 2, 1, 7, 5, 4, 6, 8, 4], [0, 0, 1, 7, 9, 5, 4, 6, 3], [0, 0, 3, 1, 7, 5, 4, 6, 8, 4], [0, 0, 7, 5, 4, 6, 8, 2], [6, 0, 1, 2, 1], [7, 0, 1], [3, 1, 1], [8, 0, 1, 2, 3, 4, 4], [2, 0, 2, 3, 4, 2], [9, 0, 1, 1], [10, 0, 1], [4, 0, 1, 2, 3, 4, 5, 6, 2], [4, 1, 7, 1], [11, 0, 1, 2, 3], [12, 0, 1, 2, 2]], [[[{"name": "bg_nohu ", "rect": [0, 0, 684, 435], "offset": [0, 0], "originalSize": [684, 435], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [3]], [[{"name": "nhanheso_lt ", "rect": [2, 0, 678, 434], "offset": [0.5, 0], "originalSize": [681, 434], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [4]], [[{"name": "tex_bangthuong", "rect": [0, 0, 168, 33], "offset": [0, 0], "originalSize": [168, 33], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "bg_will", "rect": [6, 0, 722, 432], "offset": [2.5, 0], "originalSize": [729, 432], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[{"name": "bg_dong", "rect": [1, 0, 713, 432], "offset": [0, 0], "originalSize": [715, 432], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [7]], [[[7, "dbHelpView"], [8, "dbHelpView", 0, [-4, -5, -6, -7, -8, -9], [[15, -2, [31, 32], 30], [16, -3]], [17, -1]], [9, "content", 0, [-11, -12, -13, -14, -15, -16, -17], [[18, 1, 1, 15, -10, [5, 5271, 450]]], [0, "32aW1Xv1pDoYfNhdZSAoXS", 1], [5, 5271, 450], [0, 0, 0.5], [-376.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "BG_Panel_Info_half", 1, [-20], [[6, -19, [6], 7]], [0, "f5GAjVU2NHP4RtjU7ock1M", -18], [5, 628, 641], [-269.425, 35.767, 0, 0, 0, 0, 1, 0.843, 0.843, 0.843]], [4, "pageview", 0, 1, [-23, -24], [[20, -21, 2], [21, -22]], [0, "93apLjYVVEioMdddaNHk2x", 1], [5, 753, 400], [-24, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnClose", 0, 1, [-27], [[22, 3, -26, [[24, "9f9e1IJ4WFMSqhdfSr9lY+W", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -25, 29]], [0, "4e2+4rF51KroR+Xh4xlJ5l", 1], [5, 80, 80], [470.524, 233.549, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "black", 100, 0, 1, [[19, 0, -28, [0], 1], [23, -29, [4, 4292269782]]], [0, "ea5s3ePzNBV6CAPxpJ69fG", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "view", 0, 4, [2], [[25, 0, -30, [26]]], [0, "50VbZeg5pDVJROZGt2oLFb", 1], [5, 753, 450]], [13, "nen popup", false, 0, 1, [[1, 0, false, -31, [2], 3]], [0, "8alirQGUhEzrdeFSMG8PaU", 1], [5, 854, 497], [-24, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "BG_Panel_Info_half copy", 3, [[6, -32, [4], 5]], [0, "6aQsYvizZAJrzke2DehTMc", 3], [5, 628, 641], [624.751, 0, 0, 0, 0, 0, 1, -1, 1, -1]], [5, "tex_bangthuong", 0, 1, [[1, 2, false, -33, [8], 9]], [0, "34W4IALGRKJIlL19/6gzS8", 1], [5, 168, 33], [-37, 260, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg_bangthuong", 0, 4, [[1, 2, false, -34, [10], 11]], [0, "6dXbe4xLJAk6gPMQgUQ3vS", 1], [5, 759, 378], [0, -4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "0.jackpot", 0, 2, [-35], [0, "0b2fuVlKhBb46my4WbnrqI", 1], [5, 753, 450], [376.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 0, 12, [[1, 2, false, -36, [12], 13]], [0, "e1jwnfKmFH46zb2pLNgljj", 1], [5, 684, 435]], [3, "1.payTable", 0, 2, [-37], [0, "77HZSNE/tAR5w18Wg7FJMN", 1], [5, 753, 450], [1129.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 0, 14, [[1, 2, false, -38, [14], 15]], [0, "63N2oof81CRZgznHDW7mI7", 1], [5, 740, 434]], [3, "2.expanding<PERSON>ild", 0, 2, [-39], [0, "0ct1oDY2ZC7Y8qEpQWn5yt", 1], [5, 753, 450], [1882.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 0, 16, [[1, 2, false, -40, [16], 17]], [0, "aeOO3VlhtLarXbWx/ZWitK", 1], [5, 729, 432]], [3, "3.t<PERSON><PERSON><PERSON>", 0, 2, [-41], [0, "27OMsXlJ5KzqmVtZOaobZb", 1], [5, 753, 450], [2635.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 0, 18, [[1, 2, false, -42, [18], 19]], [0, "49p/MeZmtNQ6fAtpTCqCRt", 1], [5, 758, 432]], [3, "4.multiplier", 0, 2, [-43], [0, "a0iVo3YoVOoKD0/XpnIzSE", 1], [5, 753, 450], [3388.5, 8.526512829121202e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 0, 20, [[1, 2, false, -44, [20], 21]], [0, "8fjjvNlUlHhJeD2lXyX7xv", 1], [5, 681, 434]], [3, "5.bonus", 0, 2, [-45], [0, "48GpCylTZPdoemT76cDQPJ", 1], [5, 753, 450], [4141.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 0, 22, [[1, 2, false, -46, [22], 23]], [0, "d7uMobqPRMCZvMD9Iwh1PJ", 1], [5, 739, 431]], [3, "6.betlines", 0, 2, [-47], [0, "b3u3nuJ0ZKh6TIolKgG2QU", 1], [5, 753, 450], [4894.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 0, 24, [[1, 2, false, -48, [24], 25]], [0, "7eEvtG4PFOu4A+n9z6/ZfJ", 1], [5, 715, 432]], [2, "sprite", 0, 5, [[1, 2, false, -49, [27], 28]], [0, "f8PlbYNLlA46PYMkIWPfhS", 1], [5, 51, 36]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 6, 0, -2, 8, 0, -3, 3, 0, -4, 10, 0, -5, 4, 0, -6, 5, 0, 0, 2, 0, -1, 12, 0, -2, 14, 0, -3, 16, 0, -4, 18, 0, -5, 20, 0, -6, 22, 0, -7, 24, 0, 3, 3, 0, 0, 3, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, -1, 11, 0, -2, 7, 0, 4, 5, 0, 0, 5, 0, -1, 26, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, 0, 13, 0, -1, 15, 0, 0, 15, 0, -1, 17, 0, 0, 17, 0, -1, 19, 0, 0, 19, 0, -1, 21, 0, 0, 21, 0, -1, 23, 0, 0, 23, 0, -1, 25, 0, 0, 25, 0, 0, 26, 0, 5, 1, 2, 6, 7, 49], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 7, 8, -1, -2], [0, 8, 0, 9, 0, 1, 0, 1, 0, 10, 0, 11, 0, 12, 0, 13, 0, 14, 0, 15, 0, 16, 0, 17, 0, 18, 0, 0, 19, 20, 2, 2, 21]], [[{"name": "bg_bangthuong", "rect": [0, 0, 759, 378], "offset": [0, 0], "originalSize": [759, 378], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [22]], [[{"name": "bg_tichluy", "rect": [1, 0, 756, 432], "offset": [0, 0], "originalSize": [758, 432], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [23]], [[{"name": "bg-t<PERSON><PERSON><PERSON>", "rect": [0, 0, 740, 433], "offset": [0, 0.5], "originalSize": [740, 434], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [24]], [[{"name": "bg_bonus", "rect": [1, 0, 737, 430], "offset": [0, 0.5], "originalSize": [739, 431], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [25]]]]