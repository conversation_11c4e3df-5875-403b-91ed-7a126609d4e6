[1, 0, 0, [["cc.AnimationClip", ["_name", "_duration", "curveData"], 1, 11], ["cc.AudioClip", ["_name", "_native", "duration"], 0]], [[0, 0, 1, 2, 3], [1, 0, 1, 2, 4]], [[[[0, "change_status", 0.11666666666666667, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.11666666666666667, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 2, 2]], [{"frame": 0.11666666666666667}, "value", 8, [0, 1, 1]]], 11, 11]]]]], 0, 0, [], [], []], [[[1, "mucis-bg", ".mp3", 30.72], -1], 0, 0, [], [], []]]]