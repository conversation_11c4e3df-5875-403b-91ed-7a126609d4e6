[1, ["8fdYiZXYxA1pp66od+XOkk", "42C3g++kZJCpOgfokgjK6Y", "8aFVzXX5dCIoGrCJD2Mnt7", "ddekgLvGFPVpwpTaSIgOYS", "7ea1PYSRJElacSdcq5CBGS", "00CNVVQVNMVroYopsrmHrK", "f2r0XhGmZFjaSLO5j5Ws94", "8f/Z1fchFPcaquPcw4mzqj", "39nIfuRGVG6r3MTKra+JiA"], ["_textureSetter", "spriteFrame"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "curveData", "wrapMode", "speed", "events"], -3], ["cc.AudioClip", ["_name", "_native", "duration"], 0], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0], ["cc.TTFFont", ["_name", "_native"], 1]], [[2, 0, 1, 2, 4], [1, 0, 1, 4, 3, 5, 2, 7], [1, 0, 1, 2, 4], [3, 0, 1, 2, 4], [1, 0, 1, 3, 2, 5], [1, 0, 1, 4, 3, 2, 6], [4, 0, 1, 3]], [[[{"name": "fonJackpotIdMember-export", "rect": [0, 0, 84, 83], "offset": [-0.5, 0.5], "originalSize": [85, 84], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "button_click", ".wav", 0.2535], -1], 0, 0, [], [], []], [[[4, "openBetInput", 0.2833333333333333, "0", {"paths": {"bg-input": {"props": {"scaleY": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 1.1}, {"frame": 0.2833333333333333, "value": 1}]}}}}]], 0, 0, [], [], []], [[[0, "win_music", ".mp3", 1.56735], -1], 0, 0, [], [], []], [[{"name": "ellipse6", "rect": [0, 0, 51, 51], "offset": [0, 0], "originalSize": [51, 51], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [1]], [[[0, "win_money", ".mp3", 1.541224], -1], 0, 0, [], [], []], [[[5, "lightNhapNhay", 2.8333333333333335, 0.9, "2", {"props": {"opacity": [{"frame": 0, "value": 229.5}, {"frame": 0.16666666666666666, "value": 0}, {"frame": 0.3333333333333333, "value": 255}, {"frame": 0.5, "value": 0}, {"frame": 0.6666666666666666, "value": 255}, {"frame": 0.8333333333333334, "value": 0}, {"frame": 1, "value": 255}, {"frame": 1.1666666666666667, "value": 0}, {"frame": 1.3333333333333333, "value": 255}, {"frame": 1.5, "value": 0}, {"frame": 1.6666666666666667, "value": 255}, {"frame": 1.8333333333333333, "value": 0}, {"frame": 2, "value": 255}, {"frame": 2.1666666666666665, "value": 0}, {"frame": 2.3333333333333335, "value": 255}, {"frame": 2.5, "value": 0}, {"frame": 2.6666666666666665, "value": 255}, {"frame": 2.8333333333333335, "value": 0}]}}]], 0, 0, [], [], []], [[[3, "fontnumber-export", 32, {"commonHeight": 39, "fontSize": 32, "atlasName": "fontnumber-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 160, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 20, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "35": {"xOffset": 0, "yOffset": -33, "xAdvance": 60, "rect": {"x": 0, "y": 0, "width": 59, "height": 72}}, "43": {"xOffset": 0, "yOffset": -21, "xAdvance": 60, "rect": {"x": 0, "y": 73, "width": 59, "height": 60}}, "44": {"xOffset": 0, "yOffset": 20, "xAdvance": 26, "rect": {"x": 35, "y": 207, "width": 25, "height": 32}}, "45": {"xOffset": 0, "yOffset": 0, "xAdvance": 35, "rect": {"x": 0, "y": 220, "width": 34, "height": 18}}, "46": {"xOffset": 0, "yOffset": 21, "xAdvance": 20, "rect": {"x": 60, "y": 73, "width": 19, "height": 18}}, "48": {"xOffset": 4, "yOffset": -33, "xAdvance": 52, "rect": {"x": 60, "y": 0, "width": 51, "height": 72}}, "49": {"xOffset": 8, "yOffset": -34, "xAdvance": 35, "rect": {"x": 206, "y": 146, "width": 34, "height": 73}}, "50": {"xOffset": 6, "yOffset": -33, "xAdvance": 47, "rect": {"x": 163, "y": 0, "width": 46, "height": 72}}, "51": {"xOffset": 5, "yOffset": -33, "xAdvance": 50, "rect": {"x": 157, "y": 73, "width": 49, "height": 72}}, "52": {"xOffset": 3, "yOffset": -33, "xAdvance": 54, "rect": {"x": 0, "y": 134, "width": 53, "height": 72}}, "53": {"xOffset": 5, "yOffset": -33, "xAdvance": 49, "rect": {"x": 157, "y": 146, "width": 48, "height": 72}}, "54": {"xOffset": 4, "yOffset": -33, "xAdvance": 51, "rect": {"x": 106, "y": 146, "width": 50, "height": 72}}, "55": {"xOffset": 4, "yOffset": -33, "xAdvance": 51, "rect": {"x": 106, "y": 73, "width": 50, "height": 72}}, "56": {"xOffset": 4, "yOffset": -33, "xAdvance": 52, "rect": {"x": 54, "y": 134, "width": 51, "height": 72}}, "57": {"xOffset": 4, "yOffset": -33, "xAdvance": 51, "rect": {"x": 112, "y": 0, "width": 50, "height": 72}}}, "kerningDict": {}}]], 0, 0, [0], [1], [2]], [[{"name": "icon_new", "rect": [0, 0, 49, 49], "offset": [0, 0], "originalSize": [49, 49], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [3]], [[{"name": "fontnumber-export", "rect": [0, 0, 240, 239], "offset": [0, 0], "originalSize": [240, 239], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [4]], [[[1, "light_on", 0.3333333333333333, 2, "36", [{"frame": 0, "func": "lightOnEvent", "params": []}], {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": 150}]}}]], 0, 0, [], [], []], [[[3, "fonJackpotIdMember-export", 32, {"commonHeight": 39, "fontSize": 32, "atlasName": "fonJackpotIdMember-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 160, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 20, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "35": {"xOffset": 0, "yOffset": 15, "xAdvance": 20, "rect": {"x": 0, "y": 0, "width": 19, "height": 24}}, "43": {"xOffset": 0, "yOffset": 19, "xAdvance": 20, "rect": {"x": 0, "y": 25, "width": 19, "height": 20}}, "44": {"xOffset": 0, "yOffset": 31, "xAdvance": 10, "rect": {"x": 13, "y": 71, "width": 9, "height": 12}}, "45": {"xOffset": 3, "yOffset": 25, "xAdvance": 13, "rect": {"x": 0, "y": 71, "width": 12, "height": 7}}, "46": {"xOffset": 0, "yOffset": 31, "xAdvance": 9, "rect": {"x": 20, "y": 25, "width": 8, "height": 8}}, "48": {"xOffset": 1, "yOffset": 15, "xAdvance": 18, "rect": {"x": 38, "y": 0, "width": 17, "height": 24}}, "49": {"xOffset": 1, "yOffset": 15, "xAdvance": 13, "rect": {"x": 72, "y": 0, "width": 12, "height": 24}}, "50": {"xOffset": 2, "yOffset": 15, "xAdvance": 16, "rect": {"x": 56, "y": 0, "width": 15, "height": 24}}, "51": {"xOffset": 1, "yOffset": 15, "xAdvance": 17, "rect": {"x": 55, "y": 25, "width": 16, "height": 24}}, "52": {"xOffset": 0, "yOffset": 15, "xAdvance": 19, "rect": {"x": 0, "y": 46, "width": 18, "height": 24}}, "53": {"xOffset": 1, "yOffset": 15, "xAdvance": 17, "rect": {"x": 55, "y": 50, "width": 16, "height": 24}}, "54": {"xOffset": 1, "yOffset": 15, "xAdvance": 18, "rect": {"x": 37, "y": 50, "width": 17, "height": 24}}, "55": {"xOffset": 1, "yOffset": 15, "xAdvance": 18, "rect": {"x": 37, "y": 25, "width": 17, "height": 24}}, "56": {"xOffset": 1, "yOffset": 15, "xAdvance": 18, "rect": {"x": 20, "y": 0, "width": 17, "height": 24}}, "57": {"xOffset": 1, "yOffset": 15, "xAdvance": 18, "rect": {"x": 19, "y": 46, "width": 17, "height": 24}}}, "kerningDict": {}}]], 0, 0, [0], [1], [5]], [[{"name": "Popup", "rect": [0, 0, 860, 50], "offset": [0, 0], "originalSize": [860, 50], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [6]], [[[2, "light_off", 0.3333333333333333, {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": 200}]}}]], 0, 0, [], [], []], [[[0, "roll_dice", ".mp3", 1.6196], -1], 0, 0, [], [], []], [[[1, "closeBetInput", 0.2833333333333333, 2, "36", [{"frame": 0, "func": "closeBetInputEvent", "params": []}], {"paths": {"bg-input": {"props": {"scaleY": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 1.1}, {"frame": 0.2833333333333333, "value": 1}]}}}}]], 0, 0, [], [], []], [[{"name": "member2", "rect": [0, 0, 74, 29], "offset": [0, 0], "originalSize": [74, 29], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [7]], [[[6, "UTM Avo", "UTM Avo.ttf"], -1], 0, 0, [], [], []], [[{"name": "member", "rect": [0, 0, 74, 28], "offset": [0, 0.5], "originalSize": [74, 29], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [8]], [[[2, "winFade", 2.5166666666666666, {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 2.0166666666666666, "value": 255}, {"frame": 2.5166666666666666, "value": 0}]}}]], 0, 0, [], [], []]]]