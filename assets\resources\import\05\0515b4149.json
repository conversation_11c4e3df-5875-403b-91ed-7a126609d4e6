[1, ["ecpdLyjvZBwrvm+cedCcQy", "024jzNA5VJHJGZJ8voZ3Bb", "caWBgnC85P9aNFixOcRijq", "bcx6dfUpRL0ZLuiJLy4CQN", "39V/uQkjxMZpCHVVNeshVR", "63dO2hnoVAca8uIctJ71K8", "83FRgNEkxGPKcvX9DK29f9", "5b4hxm6C5Ac4ld/9Eul9gs", "adw94Z+hpN57wutNivq8Q5", "a9VpD0DP5LJYQPXITZq+uj", "99J2QecxNPRYx5qFMv0Wc2", "48hPyll2dMAJLRxEnwX/uP", "86PLJNkOVCnYAZByNhGWAe", "b2/azgn+FKA5IG0yrqWMgJ", "4caIcmJW1FB5QbvAya8PrE", "c8N1w2OphO16uL0390wWLR", "a0v1JVi/pAeImhmvDQTmTX", "c1AgGk9utBvKbav2uV78dH", "eebn1a/NBEMrkAl/wGOq58", "36GI6/cLJD2qJzuLmVgLCd", "345+29eENDG6OaTTeWk6iF", "74+wZ+WfBNGq/MqKDqvLXo", "eeC6XZ5WhIuLLE9WC7x6SE", "0dJ/mJqzhEJIVye3BSiNwn", "6a5h44kzdEZLpsX5Jh8O+o", "7dQD/RBfRLmLp1stcNujBz", "51FZYBQJFK65dvF81ruSmN", "558auGlFxBeJpFG2Vd9bRW", "9a9X+ezL9IWa3EHQUC3wnM", "63Pu+Xd7FCoJCJZPqWoTTr", "1bc7NtZIhJC4AI7O+bS8yw", "4bVgXAG3xAs5FbABA4Xd/h", "1754W36ylH1qtHcHXVFkjU", "70HMlzaeZM0pGuEu7jYBl0", "f40hFgF/5PDo6kkfMLjXkJ", "48a0wo011Hervoke2vHpgW"], ["node", "_spriteFrame", "_N$file", "_N$target", "_textureSetter", "_N$normalSprite", "root", "btnQuickPlay", "lbiWin", "lbRemaining", "lbMultiplier", "lbTime", "nodeResult", "nodeLucky", "nodePick", "nodeBonus", "nodeStart", "nodeParentPick", "nodeParentLucky", "lbResult", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_N$normalSprite"], 1, 1, 9, 5, 5, 1, 5, 6], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_spriteFrame", "_materials"], 1, 1, 6, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 5], ["cc.Layout", ["_enabled", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_resize", "node", "_layoutSize"], -2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.AnimationClip", ["_name", "_duration", "speed", "curveData"], 0, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["1d83ePWgQxN1pPCR+yPOTsM", ["node", "nodeStart", "nodeBonus", "nodePick", "nodeLucky", "nodeResult", "lbTime", "lbMultiplier", "lbRemaining", "lbiWin", "btnQuickPlay"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["4e192aI76hKPLcnfOmDsnB4", ["node", "nodeParentPick", "sfPrizes"], 3, 1, 1, 3], ["6e496YHT+lKNLZIUKk2jCpv", ["node", "nodeParentLucky", "sfPrizes"], 3, 1, 1, 3], ["7269ege3OpEV4lKd6fET93f", ["node", "lbResult"], 3, 1, 1], ["09052Zq0oVJl59gzr1ZolUq", ["node"], 3, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["cc.Animation", ["node", "_clips"], 3, 1, 3]], [[12, 0, 1, 2], [18, 0, 1], [19, 0, 1, 1], [0, 0, 6, 8, 3, 4, 5, 7, 2], [7, 0, 1, 2, 3, 4], [4, 0, 1, 2, 4, 3, 3], [0, 0, 6, 3, 4, 5, 2], [0, 0, 2, 6, 3, 4, 5, 7, 3], [3, 2, 3, 4, 5, 6, 1], [2, 0, 3, 1, 4, 2, 7, 8, 9, 6], [0, 0, 2, 6, 3, 4, 5, 3], [4, 2, 4, 3, 1], [0, 0, 6, 8, 3, 4, 5, 9, 7, 2], [0, 0, 6, 3, 4, 5, 9, 2], [3, 0, 1, 2, 3, 4, 5, 6, 8, 3], [4, 0, 1, 2, 3, 3], [2, 0, 3, 1, 4, 2, 7, 9, 6], [0, 0, 6, 3, 4, 5, 7, 2], [5, 0, 1, 2, 3, 4, 5, 2], [4, 0, 2, 4, 3, 2], [7, 0, 1, 3, 3], [2, 0, 3, 5, 6, 1, 2, 7, 8, 9, 7], [0, 0, 1, 6, 8, 3, 4, 5, 7, 3], [0, 0, 6, 3, 4, 5, 9, 7, 2], [3, 2, 7, 1], [3, 2, 3, 7, 1], [2, 0, 3, 6, 1, 2, 7, 8, 6], [9, 0, 2], [0, 0, 8, 3, 4, 5, 2], [0, 0, 6, 8, 3, 4, 5, 2], [0, 0, 1, 6, 3, 4, 5, 7, 3], [0, 0, 1, 6, 3, 4, 5, 3], [5, 0, 1, 2, 3, 4, 6, 5, 2], [10, 0, 1, 2, 3, 4, 5, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [13, 0, 1, 2, 1], [6, 0, 4, 1, 2, 3, 5, 6, 6], [6, 0, 1, 2, 3, 5, 5], [14, 0, 1, 2, 1], [15, 0, 1, 1], [16, 0, 1], [3, 0, 1, 2, 3, 4, 5, 6, 3], [17, 0, 1], [2, 0, 3, 6, 1, 4, 2, 7, 8, 9, 7], [2, 0, 3, 5, 1, 4, 2, 7, 8, 9, 7], [2, 0, 3, 6, 1, 4, 2, 7, 8, 7], [2, 0, 5, 1, 4, 2, 7, 8, 9, 6], [2, 0, 3, 5, 1, 4, 2, 7, 8, 7], [8, 0, 1, 3, 3], [8, 0, 1, 2, 3, 4]], [[[{"name": "item_g2", "rect": [9, 13, 93, 64], "offset": [0.5, 10], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [14]], [[[27, "tkBonusGameView"], [28, "tkBonusGameView", [-13, -14, -15, -16], [[34, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [0, "6fl0ppGaVK6J1jykS3QEm+", -1], [5, 1280, 720]], [29, "bonusGamePickView", 1, [-19, -20, -21, -22, -23, -24, -25, -26, -27], [[35, -18, -17, [160, 161, 162]]], [0, "b1mPc2fbFJzKwTgDGESQWe", 1], [5, 1280, 720]], [3, "layout-pick", 2, [-29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48], [[36, false, 1, 3, 50, 25, -28, [5, 634, 435]]], [0, "3dL1S9XpZHLZGaMSaUpIk2", 1], [5, 634, 435], [-110, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "bonusGameLuckyView", false, 2, [-52, -53, -54, -55, -56, -57], [[38, -50, -49, [147, 148]], [37, false, 3, 80, 80, -51]], [0, "428D5N+dVOsZOoLaxXJFB9", 1], [5, 640, 190], [-124, -85, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "bonusGameResultView", false, 1, [-60, -61, -62, -63, -64, -65], [[39, -59, -58]], [0, "e3+hEREDhArZp9kohFmFcX", 1], [5, 1174, 452], [0, 55, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bonusGameStartView", 1, [-67, -68, -69, -70], [[40, -66]], [0, "a2FzyZJ4FI5YmozDBBjBT9", 1], [5, 1174, 452], [0, 55, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-73, -74], [[8, -72, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "1", 2]], [4, 4294967295], [4, 4294967295], -71]], [0, "b0ecocl8VIHrr0/R9zRB88", 1], [5, 83, 90], [-275.5, 172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-77, -78], [[8, -76, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "2", 2]], [4, 4294967295], [4, 4294967295], -75]], [0, "5ddPUqDVFH6Jj/AwcT0+dI", 1], [5, 83, 90], [-142.5, 172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-81, -82], [[8, -80, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "3", 2]], [4, 4294967295], [4, 4294967295], -79]], [0, "3azvs9WYFD6KxUdWZy5rrk", 1], [5, 83, 90], [-9.5, 172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-85, -86], [[8, -84, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "4", 2]], [4, 4294967295], [4, 4294967295], -83]], [0, "10wIwiyOFLtoW2QWJLX//1", 1], [5, 83, 90], [123.5, 172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-89, -90], [[8, -88, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "5", 2]], [4, 4294967295], [4, 4294967295], -87]], [0, "fePlWNhLNDGIQhxvmxbRKf", 1], [5, 83, 90], [256.5, 172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-93, -94], [[8, -92, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "6", 2]], [4, 4294967295], [4, 4294967295], -91]], [0, "c97aqr/VlDiKhTWXNP56bB", 1], [5, 83, 90], [-275.5, 57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-97, -98], [[8, -96, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "7", 2]], [4, 4294967295], [4, 4294967295], -95]], [0, "b5ibhzYiVOCKFmbzPHbWWx", 1], [5, 83, 90], [-142.5, 57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-101, -102], [[8, -100, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "8", 2]], [4, 4294967295], [4, 4294967295], -99]], [0, "512GBRhJdGQqYG2f0se+AF", 1], [5, 83, 90], [-9.5, 57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-105, -106], [[8, -104, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "9", 2]], [4, 4294967295], [4, 4294967295], -103]], [0, "bakJaQFc9OPIhUndN/ypHM", 1], [5, 83, 90], [123.5, 57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-109, -110], [[8, -108, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "10", 2]], [4, 4294967295], [4, 4294967295], -107]], [0, "f2SdSKTUBExqntZdNVjKQB", 1], [5, 83, 90], [256.5, 57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-113, -114], [[8, -112, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "11", 2]], [4, 4294967295], [4, 4294967295], -111]], [0, "7akXclgoFHlK1xXDRejgBK", 1], [5, 83, 90], [-275.5, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-117, -118], [[8, -116, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "12", 2]], [4, 4294967295], [4, 4294967295], -115]], [0, "dcFgnzVHhG/KNzcYaqua5p", 1], [5, 83, 90], [-142.5, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-121, -122], [[8, -120, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "13", 2]], [4, 4294967295], [4, 4294967295], -119]], [0, "9caXW4u/xFPoTSYYfb5PZ/", 1], [5, 83, 90], [-9.5, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-125, -126], [[8, -124, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "14", 2]], [4, 4294967295], [4, 4294967295], -123]], [0, "2e4SHS+/hHBalDxr63Rjjs", 1], [5, 83, 90], [123.5, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-129, -130], [[8, -128, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "15", 2]], [4, 4294967295], [4, 4294967295], -127]], [0, "4cQUxDnLpHQbZ6R7e8VtFj", 1], [5, 83, 90], [256.5, -57.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-133, -134], [[8, -132, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "16", 2]], [4, 4294967295], [4, 4294967295], -131]], [0, "82MH1JYfZKZLaRdCvISVYN", 1], [5, 83, 90], [-275.5, -172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-137, -138], [[8, -136, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "17", 2]], [4, 4294967295], [4, 4294967295], -135]], [0, "37L1155jZPOY8fCylMl5fY", 1], [5, 83, 90], [-142.5, -172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-141, -142], [[8, -140, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "18", 2]], [4, 4294967295], [4, 4294967295], -139]], [0, "f5C+OrK1VFfaO6K2OLgeVu", 1], [5, 83, 90], [-9.5, -172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-145, -146], [[8, -144, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "19", 2]], [4, 4294967295], [4, 4294967295], -143]], [0, "c2GHcxpApIOJei0zf9gQho", 1], [5, 83, 90], [123.5, -172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 3, [-149, -150], [[8, -148, [[4, "4e192aI76hKPLcnfOmDsnB4", "pickClicked", "20", 2]], [4, 4294967295], [4, 4294967295], -147]], [0, "25VnpolRREC5ut0VG+AObQ", 1], [5, 83, 90], [256.5, -172.5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "button", 4, [-153, -154], [[14, 1.05, 3, -152, [[4, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "1", 4]], [4, 4294967295], [4, 4294967295], -151, 126]], [0, "5e/7Lw1wpOZJ3i6xV6ebQt", 1], [5, 160, 110], [0, 0.5, 0], [-240, 95, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "button", 4, [-157, -158], [[14, 1.05, 3, -156, [[4, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "2", 4]], [4, 4294967295], [4, 4294967295], -155, 130]], [0, "15W4Zx9wlCiqroqO3DKvHH", 1], [5, 160, 110], [0, 0.5, 0], [0, 95, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "button", 4, [-161, -162], [[14, 1.05, 3, -160, [[4, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "3", 4]], [4, 4294967295], [4, 4294967295], -159, 134]], [0, "20UUs4M95MXbpTWg6ocV5C", 1], [5, 160, 110], [0, 0.5, 0], [240, 95, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "button", 4, [-165, -166], [[14, 1.05, 3, -164, [[4, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "4", 4]], [4, 4294967295], [4, 4294967295], -163, 138]], [0, "73F+dOgHNLB4e97fMbSn8K", 1], [5, 160, 110], [0, 0.5, 0], [-240, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "button", 4, [-169, -170], [[14, 1.05, 3, -168, [[4, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "5", 4]], [4, 4294967295], [4, 4294967295], -167, 142]], [0, "b5AuReaWVHIYwUoAukYXUT", 1], [5, 160, 110], [0, 0.5, 0], [0, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "button", 4, [-173, -174], [[14, 1.05, 3, -172, [[4, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "6", 4]], [4, 4294967295], [4, 4294967295], -171, 146]], [0, "69cmOOF8VL6J4YHB0FnN1v", 1], [5, 160, 110], [0, 0.5, 0], [240, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "bacground", 2, [[5, 2, false, -175, [10], 11], [24, -176, [4, 4292269782]], [42, -177]], [0, "c6uXavV/VOmra5SDrwcMSF", 1], [5, 2024, 960]], [3, "multiplier", 2, [-179, -180], [[11, -178, [18], 19]], [0, "5fF1r5GuFJPbeRBTt0sZdf", 1], [5, 218, 98], [-396.238, -353.934, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "remaining", 2, [-183], [[21, "<PERSON><PERSON> lượt mở túi quà:  ", 28, 52, false, false, 1, -181, [21], 22], [1, -182]], [0, "c2u9a5sdFNw6uRQE76WHs4", 1], [5, 249.9, 52], [0, 0, 0.5], [-257, 192, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 7, [[9, "+2000", 32, false, 1, 1, -184, [25], 26], [2, -185, [27]], [1, -186]], [0, "f8OOA8SYpNXK8S7H5NQWEv", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 8, [[9, "+2000", 32, false, 1, 1, -187, [30], 31], [2, -188, [32]], [1, -189]], [0, "06/2frtJ9HX4ZcvKc0FSLb", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 9, [[9, "+2000", 32, false, 1, 1, -190, [35], 36], [2, -191, [37]], [1, -192]], [0, "07zeMIlyVPhKEs0DnFbqDQ", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 10, [[9, "+2000", 32, false, 1, 1, -193, [40], 41], [2, -194, [42]], [1, -195]], [0, "6fzXYlyLFHcqmnyhE3iF7H", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 11, [[9, "+2000", 32, false, 1, 1, -196, [45], 46], [2, -197, [47]], [1, -198]], [0, "86d7YgxrZCKr01kCgEw7oT", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 12, [[9, "+2000", 32, false, 1, 1, -199, [50], 51], [2, -200, [52]], [1, -201]], [0, "698vY6IRVHeIf6Wl0NqvxT", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 13, [[9, "+2000", 32, false, 1, 1, -202, [55], 56], [2, -203, [57]], [1, -204]], [0, "f6wn3lCA5CbrAoQfiV5lLA", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 14, [[9, "+2000", 32, false, 1, 1, -205, [60], 61], [2, -206, [62]], [1, -207]], [0, "ccAy1SxxNMgaXnWxNqrG4N", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 15, [[9, "+2000", 32, false, 1, 1, -208, [65], 66], [2, -209, [67]], [1, -210]], [0, "065stpztxKYIX4NJDwjVt8", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 16, [[9, "+2000", 32, false, 1, 1, -211, [70], 71], [2, -212, [72]], [1, -213]], [0, "838ULA/dlCh7HqYvmyfz9A", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 17, [[9, "+2000", 32, false, 1, 1, -214, [75], 76], [2, -215, [77]], [1, -216]], [0, "2fUDYg3ZRHybv+ahu95NHM", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 18, [[9, "+2000", 32, false, 1, 1, -217, [80], 81], [2, -218, [82]], [1, -219]], [0, "490J11B4lJs6Q0K0OwBAwU", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 19, [[9, "+2000", 32, false, 1, 1, -220, [85], 86], [2, -221, [87]], [1, -222]], [0, "92RRh3seJFmaNAPPZfR414", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 20, [[9, "+2000", 32, false, 1, 1, -223, [90], 91], [2, -224, [92]], [1, -225]], [0, "e2XMMJEpxHCJbPhFUVYQ2C", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 21, [[9, "+2000", 32, false, 1, 1, -226, [95], 96], [2, -227, [97]], [1, -228]], [0, "31gZ8ydktNTYHzdFFQvdMa", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 22, [[9, "+2000", 32, false, 1, 1, -229, [100], 101], [2, -230, [102]], [1, -231]], [0, "da83Ffr2BLIozrj8/HaLot", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 23, [[9, "+2000", 32, false, 1, 1, -232, [105], 106], [2, -233, [107]], [1, -234]], [0, "4aVdHtFQpF6IEOWVTx7omx", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 24, [[9, "+2000", 32, false, 1, 1, -235, [110], 111], [2, -236, [112]], [1, -237]], [0, "01+sMurrNMQ4BfubtM1Jda", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 25, [[9, "+2000", 32, false, 1, 1, -238, [115], 116], [2, -239, [117]], [1, -240]], [0, "feWJJndLdFQ541pZx7ecvJ", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 26, [[9, "+2000", 32, false, 1, 1, -241, [120], 121], [2, -242, [122]], [1, -243]], [0, "8cv+dn1h1GFqdT6zbu0Gm1", 1], [5, 82, 40], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "label", 0, 27, [[16, "200.000", 32, false, 1, 1, -244, 124], [2, -245, [125]], [1, -246]], [0, "22V6pK0LVD2IgW9LtPJzv9", 1], [5, 328.75, 50]], [10, "label", 0, 28, [[16, "200.000", 32, false, 1, 1, -247, 128], [2, -248, [129]], [1, -249]], [0, "125KdWwGhDtIYY5dEsH6Y7", 1], [5, 328.75, 50]], [10, "label", 0, 29, [[16, "200.000", 32, false, 1, 1, -250, 132], [2, -251, [133]], [1, -252]], [0, "a4Kepp2lBCVbP4pj21fn8Z", 1], [5, 328.75, 50]], [10, "label", 0, 30, [[16, "200.000", 32, false, 1, 1, -253, 136], [2, -254, [137]], [1, -255]], [0, "745ZVkRnhG44h+FmGCKANF", 1], [5, 328.75, 50]], [10, "label", 0, 31, [[16, "200.000", 32, false, 1, 1, -256, 140], [2, -257, [141]], [1, -258]], [0, "48JoT3cf1HkLS6nL9HGZoH", 1], [5, 328.75, 50]], [10, "label", 0, 32, [[16, "200.000", 32, false, 1, 1, -259, 144], [2, -260, [145]], [1, -261]], [0, "18umh8qZJCnYGl5uB0+3Ea", 1], [5, 328.75, 50]], [3, "timer", 2, [-263, -264], [[11, -262, [152], 153]], [0, "dd/svggx5JwLwULYF20bKG", 1], [5, 218, 98], [126.416, -349.217, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnQuickPlay", 2, [[[5, 2, false, -265, [158], 159], -266], 4, 1], [0, "feufIO/VRBAJTZEWrQTlcY", 1], [5, 109, 116], [285.97, -301.643, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "black", 0, 1, [[19, 0, -267, [0], 1], [24, -268, [4, 4292269782]]], [0, "e19Argo6RMbovbQbLsAnw0", 1], [5, 3000, 3000]], [10, "black", 0, 6, [[19, 0, -269, [2], 3], [25, -270, [[20, "09052Zq0oVJl59gzr1ZolUq", "startClicked", 6]], [4, 4292269782]]], [0, "8drcUoHlBOip3lw/vSAc0g", 1], [5, 3000, 3000]], [23, "lbMultiplier", 34, [[21, "<PERSON><PERSON> số: ", 28, 52, false, false, 1, -271, [14], 15], [1, -272]], [0, "1ds47po4pM/ZWALBAvfisL", 1], [5, 81.2, 52], [0, 0, 0.5], [-69, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbMultiplierVal", 34, [[-273, [2, -274, [17]]], 1, 4], [0, "f5jYWPFSZBU6tmzGgcObYR", 1], [5, 55, 40], [40, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "lbRemaining", 35, [[-275, [1, -276]], 1, 4], [0, "02SfAShBlESIZZhlUKiO5F", 1], [5, 38, 40], [0, 0, 0.5], [260.781, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "lbMultiplier", 62, [[21, "<PERSON><PERSON> chơi:  ", 28, 52, false, false, 1, -277, [149], 150], [1, -278]], [0, "09+9qLIaRMZrjsh5YNEIkO", 1], [5, 109.9, 52], [0, 0, 0.5], [-70, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbTime", 62, [[-279, [1, -280]], 1, 4], [0, "7ehCRtzI9CCp+CO3mr3bGe", 1], [5, 38, 40], [53, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "balance", 2, [-282], [[19, 2, -281, [156], 157]], [0, "cbMC3GDjFB4r38+CKqD/NR", 1], [5, 272, 66], [-131.14, -356.122, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbWin", 71, [[[43, "0", 32, false, false, 1, 1, -283, [154], 155], -284], 4, 1], [0, "e0DK9Nu6hLr7nlz/+02JA2", 1], [5, 25, 40], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "black", 100, 5, [[19, 0, -285, [163], 164], [25, -286, [[20, "7269ege3OpEV4lKd6fET93f", "closeClicked", 5]], [4, 4292269782]]], [0, "a9IrEj7vRBEKPyBbxzzRMj", 1], [5, 3000, 3000]], [3, "nen popup", 5, [-288], [[11, -287, [169], 170]], [0, "20actRQc1JhoWO6V2oUHWw", 1], [5, 342, 448], [-344.042, -39.867, 0, 0, 0, 0, 1, 1.4, 1.4, 1]], [6, "bg_tb", 6, [[11, -289, [4], 5]], [0, "94N6FAzSVIjpaA2Tw5/lXp", 1], [5, 1174, 451]], [17, "text_tb", 6, [[11, -290, [6], 7]], [0, "bdDaWAPsBLfaaSVzR2MlIK", 1], [5, 298, 94], [-55, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "label", 6, [[44, "<PERSON><PERSON><PERSON> mừng bạn đã trúng\nBONUS GAME", 46, 60, false, 1, 1, -291, [8], 9]], [0, "f43IbuThJLcJs4Jwuz0D+9", 1], [5, 516.35, 120], [-158, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "body", false, 2, [[5, 2, false, -292, [12], 13]], [0, "c56v5jIjBMCLnOmDKFQLFq", 1], [5, 1468, 821], [67, -65, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "x15", 32, false, false, 1, 67, [16]], [26, "15", 32, false, false, 1, 68, [20]], [6, "prize", 7, [[5, 2, false, -293, [23], 24]], [0, "ddrcSWInZEV5E8V8e9Ec3/", 1], [5, 117, 95]], [6, "prize", 8, [[5, 2, false, -294, [28], 29]], [0, "33nQ9oMrFDZ48mghm8lGvZ", 1], [5, 117, 95]], [6, "prize", 9, [[5, 2, false, -295, [33], 34]], [0, "54jSli8NtHpq/8X7dTS9dv", 1], [5, 117, 95]], [6, "prize", 10, [[5, 2, false, -296, [38], 39]], [0, "d8QhLh3A9LFrptgDp1tx29", 1], [5, 117, 95]], [6, "prize", 11, [[5, 2, false, -297, [43], 44]], [0, "dffGmrcORDD5CfMzgAoZ14", 1], [5, 117, 95]], [6, "prize", 12, [[5, 2, false, -298, [48], 49]], [0, "72U6CsiBZHE691h8obP/eg", 1], [5, 117, 95]], [6, "prize", 13, [[5, 2, false, -299, [53], 54]], [0, "f1XYL35pJCMJQ86HdZoEGw", 1], [5, 117, 95]], [6, "prize", 14, [[5, 2, false, -300, [58], 59]], [0, "62VTmb5GlIIJAXmNWUOjXk", 1], [5, 117, 95]], [6, "prize", 15, [[5, 2, false, -301, [63], 64]], [0, "ebGGhTQGlCkLvPqKH/zODQ", 1], [5, 117, 95]], [6, "prize", 16, [[5, 2, false, -302, [68], 69]], [0, "17jA4RHzxAiI9N7lbcW1Bw", 1], [5, 117, 95]], [6, "prize", 17, [[5, 2, false, -303, [73], 74]], [0, "05sbDUCOlLDL+TFXKyBU4a", 1], [5, 117, 95]], [6, "prize", 18, [[5, 2, false, -304, [78], 79]], [0, "3fe5M+CK1IRLoHosJjeaKL", 1], [5, 117, 95]], [6, "prize", 19, [[5, 2, false, -305, [83], 84]], [0, "3bBw33YAtH95EZqxxBIrow", 1], [5, 117, 95]], [6, "prize", 20, [[5, 2, false, -306, [88], 89]], [0, "5c1qqFIy9PEpZqKhQ/uncb", 1], [5, 117, 95]], [6, "prize", 21, [[5, 2, false, -307, [93], 94]], [0, "ea0CCbV6pBUpAOPyedbJOc", 1], [5, 117, 95]], [6, "prize", 22, [[5, 2, false, -308, [98], 99]], [0, "37xDtxGcdOcqpsk+GPgH+T", 1], [5, 117, 95]], [6, "prize", 23, [[5, 2, false, -309, [103], 104]], [0, "98hrWzo69BToi0BHzE0L0W", 1], [5, 117, 95]], [6, "prize", 24, [[5, 2, false, -310, [108], 109]], [0, "29Bvo3fqNNBq+LYFOPn95T", 1], [5, 117, 95]], [6, "prize", 25, [[5, 2, false, -311, [113], 114]], [0, "69gp3hsflLVqJCi+eYDzeo", 1], [5, 117, 95]], [6, "prize", 26, [[5, 2, false, -312, [118], 119]], [0, "30kDOHVNZAp4E6YVWZZZUr", 1], [5, 117, 95]], [13, "prize", 27, [[15, 2, false, -313, 123]], [0, "c47+R2RtlN4bCwLmuwqoq7", 1], [5, 160, 110], [0, 0.5, 0]], [13, "prize", 28, [[15, 2, false, -314, 127]], [0, "89awAKGMhGfq6XapdzKsoh", 1], [5, 160, 110], [0, 0.5, 0]], [13, "prize", 29, [[15, 2, false, -315, 131]], [0, "6daGuQqo9JjI2KKIoOgWxX", 1], [5, 160, 110], [0, 0.5, 0]], [13, "prize", 30, [[15, 2, false, -316, 135]], [0, "40El9baJRAm4by3ojaMi+k", 1], [5, 160, 110], [0, 0.5, 0]], [13, "prize", 31, [[15, 2, false, -317, 139]], [0, "3aIjUenTVMa5w0ObWkLBbi", 1], [5, 160, 110], [0, 0.5, 0]], [13, "prize", 32, [[15, 2, false, -318, 143]], [0, "9fywHIVEBLupVQOM0gK50f", 1], [5, 160, 110], [0, 0.5, 0]], [45, "15", 32, false, false, 1, 1, 70, [151]], [1, 72], [41, 1.05, 3, 63, [[20, "1d83ePWgQxN1pPCR+yPOTsM", "quickPlayClicked", 1]], [4, 4294967295], [4, 4294967295], 63], [31, "bg_tb", false, 5, [[11, -319, [165], 166]], [0, "4aevG4NmBPN6IExo7YaLrp", 1], [5, 1174, 451]], [17, "nen popup", 74, [[11, -320, [167], 168]], [0, "75hVWlTNhCYYU3RE1/lWsp", 1], [5, 342, 448], [341.435, 0, 0, 0, 0, 0, 1, -1, 1, -1]], [17, "label", 5, [[46, "<PERSON><PERSON><PERSON> mừng bạn đã được", 50, false, 1, 1, -321, [171], 172]], [0, "62liHlguVILqpTdquRjbES", 1], [5, 444, 50], [-173, -22, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "lbWin", 5, [-322], [0, "860VtqvRhICIfmeghkR+1b", 1], [5, 46, 50], [-173, -89, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "0", 32, 50, false, 1, 1, 113, [173]], [17, "text_tb", 5, [[11, -323, [174], 175]], [0, "7cslc/uEZFrKL9R9piNCsK", 1], [5, 298, 94], [-101.606, 167.49, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 6, 1, 0, 7, 109, 0, 8, 108, 0, 9, 80, 0, 10, 79, 0, 11, 107, 0, 12, 5, 0, 13, 4, 0, 14, 3, 0, 15, 2, 0, 16, 6, 0, 0, 1, 0, -1, 64, 0, -2, 6, 0, -3, 2, 0, -4, 5, 0, 17, 3, 0, 0, 2, 0, -1, 33, 0, -2, 78, 0, -3, 34, 0, -4, 35, 0, -5, 3, 0, -6, 4, 0, -7, 62, 0, -8, 71, 0, -9, 63, 0, 0, 3, 0, -1, 7, 0, -2, 8, 0, -3, 9, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, -7, 13, 0, -8, 14, 0, -9, 15, 0, -10, 16, 0, -11, 17, 0, -12, 18, 0, -13, 19, 0, -14, 20, 0, -15, 21, 0, -16, 22, 0, -17, 23, 0, -18, 24, 0, -19, 25, 0, -20, 26, 0, 18, 4, 0, 0, 4, 0, 0, 4, 0, -1, 27, 0, -2, 28, 0, -3, 29, 0, -4, 30, 0, -5, 31, 0, -6, 32, 0, 19, 114, 0, 0, 5, 0, -1, 73, 0, -2, 110, 0, -3, 74, 0, -4, 112, 0, -5, 113, 0, -6, 115, 0, 0, 6, 0, -1, 65, 0, -2, 75, 0, -3, 76, 0, -4, 77, 0, 3, 7, 0, 0, 7, 0, -1, 81, 0, -2, 36, 0, 3, 8, 0, 0, 8, 0, -1, 82, 0, -2, 37, 0, 3, 9, 0, 0, 9, 0, -1, 83, 0, -2, 38, 0, 3, 10, 0, 0, 10, 0, -1, 84, 0, -2, 39, 0, 3, 11, 0, 0, 11, 0, -1, 85, 0, -2, 40, 0, 3, 12, 0, 0, 12, 0, -1, 86, 0, -2, 41, 0, 3, 13, 0, 0, 13, 0, -1, 87, 0, -2, 42, 0, 3, 14, 0, 0, 14, 0, -1, 88, 0, -2, 43, 0, 3, 15, 0, 0, 15, 0, -1, 89, 0, -2, 44, 0, 3, 16, 0, 0, 16, 0, -1, 90, 0, -2, 45, 0, 3, 17, 0, 0, 17, 0, -1, 91, 0, -2, 46, 0, 3, 18, 0, 0, 18, 0, -1, 92, 0, -2, 47, 0, 3, 19, 0, 0, 19, 0, -1, 93, 0, -2, 48, 0, 3, 20, 0, 0, 20, 0, -1, 94, 0, -2, 49, 0, 3, 21, 0, 0, 21, 0, -1, 95, 0, -2, 50, 0, 3, 22, 0, 0, 22, 0, -1, 96, 0, -2, 51, 0, 3, 23, 0, 0, 23, 0, -1, 97, 0, -2, 52, 0, 3, 24, 0, 0, 24, 0, -1, 98, 0, -2, 53, 0, 3, 25, 0, 0, 25, 0, -1, 99, 0, -2, 54, 0, 3, 26, 0, 0, 26, 0, -1, 100, 0, -2, 55, 0, 3, 27, 0, 0, 27, 0, -1, 101, 0, -2, 56, 0, 3, 28, 0, 0, 28, 0, -1, 102, 0, -2, 57, 0, 3, 29, 0, 0, 29, 0, -1, 103, 0, -2, 58, 0, 3, 30, 0, 0, 30, 0, -1, 104, 0, -2, 59, 0, 3, 31, 0, 0, 31, 0, -1, 105, 0, -2, 60, 0, 3, 32, 0, 0, 32, 0, -1, 106, 0, -2, 61, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, -1, 66, 0, -2, 67, 0, 0, 35, 0, 0, 35, 0, -1, 68, 0, 0, 36, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, -1, 69, 0, -2, 70, 0, 0, 63, 0, -2, 109, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, -1, 79, 0, 0, 67, 0, -1, 80, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, -1, 107, 0, 0, 70, 0, 0, 71, 0, -1, 72, 0, 0, 72, 0, -2, 108, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, -1, 111, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 90, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, -1, 114, 0, 0, 115, 0, 20, 1, 323], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 80, 107, 109, 114], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, -1, -1, 1, -1, -1, 2, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, 1, 2, -1, 5, 1, 2, -1, 5, 1, 2, -1, 5, 1, 2, -1, 5, 1, 2, -1, 5, 1, 2, -1, 5, -1, -2, -1, 2, -1, -1, 1, -1, 2, -1, 1, -1, 1, -1, -2, -3, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, 1, 2, 2, 2, 5, 2], [0, 9, 0, 9, 0, 10, 0, 11, 0, 8, 0, 15, 0, 16, 0, 8, 0, 17, 0, 12, 0, 0, 8, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 0, 2, 0, 1, 3, 4, 5, 6, 7, 4, 5, 6, 7, 4, 5, 6, 7, 4, 5, 6, 7, 4, 5, 6, 7, 4, 5, 6, 7, 4, 18, 0, 8, 0, 0, 12, 0, 1, 0, 19, 0, 20, 21, 22, 23, 0, 9, 0, 10, 0, 13, 0, 13, 0, 8, 0, 0, 11, 1, 1, 1, 24, 5]], [[{"name": "btn_choiNhanh", "rect": [0, 0, 109, 116], "offset": [0, 0], "originalSize": [109, 116], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [25]], [[{"name": "sheld_money", "rect": [0, 0, 272, 66], "offset": [0, 0], "originalSize": [272, 66], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [26]], [[{"name": "cl", "rect": [0, 0, 160, 110], "offset": [0, 0], "originalSize": [160, 110], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [27]], [[{"name": "text_tb", "rect": [0, 0, 298, 94], "offset": [0, 0], "originalSize": [298, 94], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [28]], [[{"name": "item_key", "rect": [13, 13, 92, 86], "offset": [4, -1], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [29]], [[[48, "bonusPickLuck", 0.21666666666666667, [{}, "props", 11, [{"y": [{"frame": 0, "value": 136}, {"frame": 0.16666666666666666, "value": 210}, {"frame": 0.21666666666666667, "value": 175}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.06666666666666667, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0.6, 0.6]], [{"frame": 0.21666666666666667}, "value", 8, [0, 1, 1]]], 11, 11]]]]], 0, 0, [], [], []], [[{"name": "sheld", "rect": [0, 2, 218, 98], "offset": [0, 0], "originalSize": [218, 102], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [30]], [[{"name": "bg_tb", "rect": [0, 1, 1174, 451], "offset": [0, -0.5], "originalSize": [1174, 452], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [31]], [[{"name": "body", "rect": [0, 2, 1468, 819], "offset": [0, -1], "originalSize": [1468, 821], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [32]], [[[49, "showPrize", 0.16666666666666666, 0.6, [{}, "props", 11, [{"y": [{"frame": 0, "value": -19.8}, {"frame": 0.11666666666666667, "value": -3.9}, {"frame": 0.16666666666666666, "value": -20}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.05, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0.8, 0.8]], [{"frame": 0.16666666666666666}, "value", 8, [0, 1, 1]]], 11, 11]]]]], 0, 0, [], [], []], [[{"name": "BG_Transition", "rect": [0, 0, 2024, 960], "offset": [0, 0], "originalSize": [2024, 960], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [33]], [[{"name": "item_g1", "rect": [3, 7, 105, 74], "offset": [0.5, 11], "originalSize": [110, 110], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [34]], [[{"name": "cl_open", "rect": [0, 0, 284, 350], "offset": [0, 0], "originalSize": [284, 350], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [35]]]]