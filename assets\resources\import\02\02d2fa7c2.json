[1, ["ecpdLyjvZBwrvm+cedCcQy", "a911dPIMpLobb8/U+XIQ+A", "32sheeEg9LSpns5pXk0yjh", "5b4hxm6C5Ac4ld/9Eul9gs", "9awKuJe0ZAdYuaoLZYOaoE", "94fNVj1WxCx7wC92hGNSS+", "e9tp5v/7FK+KSwUBY+WZJR", "6c/8lOs/RBG4ivlLtVl7RB", "5dsIbAWRxKLp5H1jMaR4Q8", "a9VpD0DP5LJYQPXITZq+uj", "18nNRHsvpNI7ZjuAJbOstD", "76pclzljVDbI+UmT6c5WjT", "876e1VwC1IlZH/EzpRPzer", "12Sui+GudG6avfjb5H+Rxd", "0bAozzgJ9JvpgubgTb9pa2", "5e+wb5QOFNML3qwBEtYvOL", "23ozn+429Kp72XoT33u0+q", "e1tEqGg81GKIiGtn+po6xU", "b7JF4OK9pJF4vKK2IEWjvZ", "cfrhI6ooVM+JOj9q4eWZuc", "fbZao1QEBC+bVY15XTvwP7", "354AUNgidL9In9OORqZvXr", "c7Zy1fXlpJhL/POO5HfD65", "8cVTByU4hMZLOUWQrmJF91", "0fAsppQtBEE4Ce1rSlIIfB", "95wW9kNtNLG7r16rsgnk0R", "1c9RB+nXJIA4P6r8370BE7", "c1AgGk9utBvKbav2uV78dH", "4fHNdkgJFAOKQvG3BqwbvN", "e8WSZqZF9HD4y2GAoJCK6q", "86V8yLqPFE5am6lqNMMlu0", "a7oJTL3HdEkq9rOSkesPl/", "a0D70TRcpC66A1iPIptISA", "81hQVq7YxOOI4iwrzxGVp/", "6a5h44kzdEZLpsX5Jh8O+o", "66hKr1SX1FCalkSOiIXjn4", "adwFrpwztK25/Gxq9y0vwQ", "44St+kIHZJGoorAqYIZaZu", "3bcCrcFydLs4RnOUBPyT87", "73yHv8Qi5PuKONAZhZx2tn", "98Nu1q3HVJ0pFodTA8Jd4S", "03t5wfuN9KwqoE5CjjlRA7", "dfuVJ1O7dNbK4//9DClzEm", "b6ANxUhRxDsreqoEyHc6+v", "266Z/kvKNKJ6PHPpKuXsMS", "f9G62/fw9Fl5ETZ3ulrFie", "d6KhOeiDNK47XWTprb1xqU", "98ETnfRqVME7wHCpkZSwrP", "154hzCpmVDGoXrggYK8jzO", "dem3YjbVpPWrL9TYOyKf5X", "e3mh/NPM1ODYhXUWY7T5jx", "0dnw7740VCoINeG/NJKQCs", "d10sojgX1Lvo71i4xL7r7N"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_N$normalSprite", "_N$target", "_defaultClip", "root", "btnQuickPlay", "lbiWin", "lbTime", "nodeRemain", "nodeResult", "nodeMulti", "nodePick", "nodeBonus", "nodeStart", "lbMultiplier", "lbRemaining", "target", "lbResult", "data", "sfMiss", "sfWin"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_anchorPoint"], 0, 4, 5, 9, 1, 2, 7, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_enableWrapText", "_spacingX", "node", "_N$file", "_materials"], -5, 1, 6, 3], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_spriteFrame", "_materials"], 1, 1, 6, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_children"], 2, 1, 12, 4, 5, 7, 5, 5, 2], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_N$normalSprite"], 1, 1, 9, 5, 5, 1, 5, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 2, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "_enabled", "node", "_layoutSize"], -2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["8ce3eNnmvlHMakcC6rKL2d3", ["node", "nodeStart", "nodeBonus", "nodePick", "nodeMulti", "nodeResult", "nodeRemain", "lbTime", "lbiWin", "btnQuickPlay"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["0790e2K+y5Ns4QcxPeKjbL8", ["node", "btnPicks", "lbRemaining", "lbMultiplier", "sfMiss", "sfWin"], 3, 1, 2, 1, 1, 6, 6], ["09052Zq0oVJl59gzr1ZolUq", ["node"], 3, 1], ["7269ege3OpEV4lKd6fET93f", ["node", "lbResult"], 3, 1, 1], ["457fdQZoe9DAJLBhibZ4Ll4", ["node", "btnPicks", "spriteResults", "sfMultipliers"], 3, 1, 2, 2, 3], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["cc.AnimationClip", ["_name", "_duration", "speed", "curveData"], 0, 11]], [[7, 0, 1, 2], [9, 0, 1, 2, 3, 4], [18, 0, 1], [3, 0, 1, 2, 4, 3, 3], [10, 0, 1, 1], [1, 0, 6, 5, 3, 4, 8, 2], [2, 0, 3, 4, 1, 5, 2, 8, 10, 9, 7], [1, 0, 2, 6, 5, 3, 4, 8, 3], [6, 0, 1, 6, 2, 3, 4, 5, 2], [5, 2, 3, 4, 5, 6, 1], [1, 0, 6, 5, 3, 4, 2], [3, 0, 1, 2, 3, 3], [3, 2, 3, 1], [1, 0, 6, 7, 5, 3, 4, 9, 8, 2], [1, 0, 2, 6, 7, 5, 3, 4, 8, 3], [1, 0, 6, 5, 3, 4, 9, 2], [4, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 8, 3], [2, 0, 3, 4, 1, 5, 2, 8, 9, 7], [1, 0, 6, 7, 5, 3, 4, 8, 2], [5, 0, 1, 2, 3, 4, 5, 6, 3], [9, 0, 1, 3, 3], [1, 0, 1, 6, 7, 5, 3, 4, 8, 3], [1, 0, 2, 6, 5, 3, 4, 3], [4, 0, 1, 8, 2, 3, 4, 5, 2], [3, 0, 1, 2, 4, 3], [5, 2, 3, 7, 1], [10, 0, 1, 2, 1], [1, 0, 6, 7, 5, 3, 4, 2], [3, 0, 2, 4, 3, 2], [3, 2, 4, 3, 1], [5, 2, 7, 1], [11, 0, 2], [1, 0, 7, 5, 3, 4, 2], [1, 0, 1, 6, 7, 3, 4, 8, 3], [1, 0, 1, 6, 7, 5, 3, 4, 9, 8, 3], [6, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 6, 4, 7, 5, 2], [4, 0, 1, 2, 3, 4, 7, 5, 2], [4, 0, 1, 2, 3, 6, 4, 5, 2], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [7, 1, 1], [13, 0, 1, 2, 3, 4, 5, 1], [8, 4, 0, 1, 2, 3, 5, 6, 6], [8, 0, 1, 2, 3, 5, 6, 5], [3, 0, 2, 3, 2], [14, 0, 1], [15, 0, 1, 1], [16, 0, 1, 2, 3, 1], [17, 0, 1], [2, 0, 3, 4, 6, 1, 5, 2, 8, 9, 8], [2, 0, 3, 4, 1, 7, 8, 10, 9, 6], [2, 0, 3, 6, 1, 2, 8, 6], [2, 0, 3, 4, 6, 1, 2, 8, 7], [2, 0, 3, 6, 1, 5, 2, 8, 10, 7], [2, 0, 4, 1, 5, 2, 8, 9, 6], [2, 0, 4, 1, 5, 2, 8, 6], [19, 0, 1, 2, 3, 4]], [[[{"name": "avenger_symbol_1_9", "rect": [0, 0, 114, 126], "offset": [0, 0], "originalSize": [114, 126], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [16]], [[{"name": "X3", "rect": [0, 0, 95, 70], "offset": [0, 0], "originalSize": [95, 70], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [17]], [[{"name": "icon6-0", "rect": [0, 0, 107, 137], "offset": [0, 0], "originalSize": [107, 137], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [18]], [[[32, "cbBonusGameView"], [33, "cbBonusGameView", [-12, -13, -14, -15], [[40, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [41, -1], [5, 1280, 720]], [28, "bonusGamePickView", 1, [-31, -32, -33, -34, -35, -36, -37, -38, -39], [[42, -30, [-18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29], -17, -16, 128, 129]], [0, "5dDOS9ZLdOS5wUD0JOtnXD", 1], [5, 1280, 720]], [22, "layout-pick", false, 2, [-41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52], [[43, false, 1, 3, 50, 12, -40, [5, 780, 474]]], [0, "7cJ0yRNndP/6/XkUTdGd4a", 1], [5, 780, 474], [4, -92, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "bonusGameLuckyView", false, 2, [-53, -54, -55, -56, -57], [0, "f5WX0fDyJDgbwh8Fddg8iz", 1], [5, 640, 190], [0, -84.99999999999989, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "bonusGameStartView", 1, [-62, -63], [[3, 2, false, -58, [6], 7], [46, -59], [26, -61, [[21, "09052Zq0oVJl59gzr1ZolUq", "startClicked", -60]], [4, 4292269782]]], [0, "37SV/MHJBItK4WKTsqseMc", 1], [5, 807, 481], [0, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "bonusGameResultView", false, 1, [-67, -68, -69], [[11, 2, false, -64, 132], [47, -66, -65]], [0, "f4g8abQc5A15Rd/IJjO/uX", 1], [5, 738, 522], [0, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "bonusGameMultiView", 2, [-77], [[48, -76, [-73, -74, -75], [-70, -71, -72], [110, 111, 112, 113, 114, 115]]], [0, "a0LFw5MNNOV554PbAICCK+", 1], [5, 1280, 720]], [8, "button", 3, [-79, -80], [-78], [0, "d36Ag0HghDKY21QD16j9tw", 1], [5, 150, 150], [-315, 162, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button1", 3, [-82, -83], [-81], [0, "6fO32PH8NMd7AdeFPqEbMX", 1], [5, 150, 150], [-115, 162, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button2", 3, [-85, -86], [-84], [0, "698kSnUrBKgZv27n0dZR3p", 1], [5, 150, 150], [85, 162, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button3", 3, [-88, -89], [-87], [0, "af1g3J0y5Np7iqDR90TkGd", 1], [5, 150, 150], [285, 162, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button4", 3, [-91, -92], [-90], [0, "74c/9N0BpLU67TjCw29P58", 1], [5, 150, 150], [-315, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button5", 3, [-94, -95], [-93], [0, "3bfUVixt5PVZIVmoVEVqNY", 1], [5, 150, 150], [-115, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button6", 3, [-97, -98], [-96], [0, "ceEyE85iZPZ6/BNQY5Fd6N", 1], [5, 150, 150], [85, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button7", 3, [-100, -101], [-99], [0, "70epkkmgxL2YgKPmWLpkE9", 1], [5, 150, 150], [285, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button8", 3, [-103, -104], [-102], [0, "f9BYP8lmdEsqIHQiRgiG4w", 1], [5, 150, 150], [-315, -162, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button9", 3, [-106, -107], [-105], [0, "16eVTGnpZHdKbcNrfW64tT", 1], [5, 150, 150], [-115, -162, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button10", 3, [-109, -110], [-108], [0, "67ycFy4mJBDKxied0cGI9i", 1], [5, 150, 150], [85, -162, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "button11", 3, [-112, -113], [-111], [0, "ecP29i8i5HK74El4wKihwg", 1], [5, 150, 150], [285, -162, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "button", 4, [-116, -117], [[17, 1.05, 3, -115, [[1, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "0", 4]], [4, 4294967295], [4, 4294967295], -114, 74]], [0, "40McOqFrNJb6UzMfm55Xf9", 1], [5, 160, 110], [0, 0.5, 0], [-131, -88, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "button1", 4, [-120, -121], [[17, 1.05, 3, -119, [[1, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "1", 4]], [4, 4294967295], [4, 4294967295], -118, 79]], [0, "12313OnYlHoJzDllcCB2q9", 1], [5, 160, 110], [0, 0.5, 0], [153, -88, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "button2", 4, [-124, -125], [[17, 1.05, 3, -123, [[1, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "2", 4]], [4, 4294967295], [4, 4294967295], -122, 84]], [0, "60beaF3blHmKpK8EzEwxXP", 1], [5, 160, 110], [0, 0.5, 0], [-283, -204, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "button3", 4, [-128, -129], [[17, 1.05, 3, -127, [[1, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "3", 4]], [4, 4294967295], [4, 4294967295], -126, 89]], [0, "38nsRMD+VHo4SNV+pknET+", 1], [5, 160, 110], [0, 0.5, 0], [7.4, -204, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "button4", 4, [-132, -133], [[17, 1.05, 3, -131, [[1, "6e496YHT+lKNLZIUKk2jCpv", "openChestClicked", "4", 4]], [4, 4294967295], [4, 4294967295], -130, 94]], [0, "7eeYYc/k5NfKNpCjMHPCWF", 1], [5, 160, 110], [0, 0.5, 0], [286, -204, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "layout copy", 7, [-135, -136, -137], [[44, 1, 1, 70, 20, -134, [5, 482, 250]]], [0, "0atz9pEYBBy5VJOlBpeBvG", 1], [5, 482, 250], [0, -120, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "button", 25, [-140], [[[3, 2, false, -138, [98], 99], -139], 4, 1], [0, "d07awKUwdFMrGNm6SZoyuM", 1], [5, 114, 126], [-184, -9, 0, 0, 0, 0, 1, 1, 1, 0]], [24, "button1", 25, [-143], [[[3, 2, false, -141, [103], 104], -142], 4, 1], [0, "13jQSiYsxAy4vS88SITGL+", 1], [5, 114, 126], [0, -9, 0, 0, 0, 0, 1, 1, 1, 0]], [24, "button2", 25, [-146], [[[3, 2, false, -144, [108], 109], -145], 4, 1], [0, "e0Xme2dqZH9aa5jiAWkMlM", 1], [5, 114, 126], [184, -9, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "bacground", 2, [[3, 0, false, -147, [8], 9], [31, -148, [4, 4292269782]], [49, -149]], [0, "8fZLSDo3ROkKImsodw+ihL", 1], [5, 1280, 720]], [7, "label", 0, 8, [[6, "+2000", 20, 76, false, 1, 1, -150, [12], 13], [4, -151, [14]], [2, -152]], [0, "4aertJv6RHeaoZvdL4Hvx7", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 9, [[6, "+2000", 20, 76, false, 1, 1, -153, [17], 18], [4, -154, [19]], [2, -155]], [0, "1cA7kiHYdGV5WE4QxHTyqR", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 10, [[6, "+2000", 20, 76, false, 1, 1, -156, [22], 23], [4, -157, [24]], [2, -158]], [0, "5cCx2xNJpGE4OkMgcus/qc", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 11, [[6, "+2000", 20, 76, false, 1, 1, -159, [27], 28], [4, -160, [29]], [2, -161]], [0, "64HocntlhP3aJXemfsOfje", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 12, [[6, "+2000", 20, 76, false, 1, 1, -162, [32], 33], [4, -163, [34]], [2, -164]], [0, "dbBusq+olCY6KbSDBSN6f5", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 13, [[6, "+2000", 20, 76, false, 1, 1, -165, [37], 38], [4, -166, [39]], [2, -167]], [0, "b69JA6bJRDt6wMBr5AgPQ7", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 14, [[6, "+2000", 20, 76, false, 1, 1, -168, [42], 43], [4, -169, [44]], [2, -170]], [0, "88V7ILbzJF1JUdGRLqG1bM", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 15, [[6, "+2000", 20, 76, false, 1, 1, -171, [47], 48], [4, -172, [49]], [2, -173]], [0, "c9IMdaHWdHbK8uX+K8LXAs", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 16, [[6, "+2000", 20, 76, false, 1, 1, -174, [52], 53], [4, -175, [54]], [2, -176]], [0, "23c1MkxslPl7X1S7rU0ZwI", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 17, [[6, "+2000", 20, 76, false, 1, 1, -177, [57], 58], [4, -178, [59]], [2, -179]], [0, "4fH2g8gB9F+a8/nkukrvkW", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 18, [[6, "+2000", 20, 76, false, 1, 1, -180, [62], 63], [4, -181, [64]], [2, -182]], [0, "c8N2QbIdBJIbfwE3E/McZD", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 0, 19, [[6, "+2000", 20, 76, false, 1, 1, -183, [67], 68], [4, -184, [69]], [2, -185]], [0, "78cF+x9apK4ZeVrfF7jLOn", 1], [5, 135, 76], [-9, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bg_money", 0, 20, [-188], [[12, -186, 72], [4, -187, [73]]], [0, "07v0lNVFlBXIWGQGLmU7uF", 1], [5, 274, 44], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bg_money", 0, 21, [-191], [[12, -189, 77], [4, -190, [78]]], [0, "e5i2aXQgBLRr0OPGeC83KN", 1], [5, 274, 44], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bg_money", 0, 22, [-194], [[12, -192, 82], [4, -193, [83]]], [0, "7fwZg93qdCd5ZdFV6God+6", 1], [5, 274, 44], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bg_money", 0, 23, [-197], [[12, -195, 87], [4, -196, [88]]], [0, "53da4VMh1HR7xcFnbROjzT", 1], [5, 274, 44], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bg_money", 0, 24, [-200], [[12, -198, 92], [4, -199, [93]]], [0, "53dDUofNpBHZeCAqG74alH", 1], [5, 274, 44], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "remaining", false, 2, [-202], [[50, "<PERSON><PERSON> lượt mở túi quà :  ", 28, 52, false, false, 2, 1, -201, 118]], [0, "7dM7XZj7dP3423syqGHVYK", 1], [5, 261.52, 52], [0, 1, 0.5], [118, 169, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnQuickPlay", 2, [[[3, 2, false, -203, [126], 127], -204], 4, 1], [0, "08RcxA7SxPsLMJ+y1Yj4qp", 1], [5, 407, 135], [422.209, -298.817, 0, 0, 0, 0, 1, 1, 1, 0]], [23, "black", 100, 1, [[29, 0, -205, [0], 1], [31, -206, [4, 4292269782]]], [0, "08BOhSLxhLXJuh+a/E+i9M", 1], [5, 3000, 3000]], [23, "black", 0, 5, [[29, 0, -207, [2], 3], [26, -208, [[21, "09052Zq0oVJl59gzr1ZolUq", "startClicked", 5]], [4, 4292269782]]], [0, "53lxsVKc5FWpI/OT/oMgFt", 1], [5, 3000, 3000]], [10, "label", 42, [[18, "******.000", 20, 76, false, 1, 1, -209, 71], [2, -210]], [0, "135kmyaLVNyJuY6bbQMbhj", 1], [5, 228.75, 47.5]], [10, "label", 43, [[18, "******.000", 20, 76, false, 1, 1, -211, 76], [2, -212]], [0, "9aM2ma9xdOfavINYYH0dcP", 1], [5, 228.75, 47.5]], [10, "label", 44, [[18, "******.000", 20, 76, false, 1, 1, -213, 81], [2, -214]], [0, "aeKC6RZaRJxYbMYqWRJMrG", 1], [5, 228.75, 47.5]], [10, "label", 45, [[18, "******.000", 20, 76, false, 1, 1, -215, 86], [2, -216]], [0, "655+HKlw1JfoWsDDvBj5wB", 1], [5, 228.75, 47.5]], [10, "label", 46, [[18, "******.000", 20, 76, false, 1, 1, -217, 91], [2, -218]], [0, "67XdAv4aJBqqaN377GBvP/", 1], [5, 228.75, 47.5]], [16, "result", 26, [[-219, [27, -220, [97], 96]], 1, 4], [0, "d1Y1V4EZlBDL4Es6u2CoC4", 1], [5, 258, 311], [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "result", 27, [[-221, [27, -222, [102], 101]], 1, 4], [0, "e6omavwhtElod7LG6XtDxa", 1], [5, 258, 311], [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "result", 28, [[-223, [27, -224, [107], 106]], 1, 4], [0, "59sl7LHvRFF4qIbkdnpTOh", 1], [5, 258, 311], [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "multiplier", false, 2, [-226], [[12, -225, 117]], [0, "e8gLgcQSRLtLraEYoD1p8b", 1], [5, 266, 99], [-416.4, 297.9, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "lbMultiplierVal", 59, [[-227, [4, -228, [116]]], 1, 4], [0, "81UcEqqUVFBb136Io5OMqp", 1], [4, 4290430164], [5, 53.28, 40], [0, 0, 0.5], [37.8, 6.6, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "lbRemaining", 47, [[-229, [2, -230]], 1, 4], [0, "f0iiFZy/FBpIkpCBSB72pM", 1], [5, 31.5, 30.63], [0, 0, 0.5], [-4.5, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Title_bonusG", 2, [-232], [[30, -231, [121], 122]], [0, "94uOpY/bdOf6+Jek5VFn4e", 1], [5, 461, 110], [362.594, 304.975, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbBalance", 62, [[[51, "0", 30, 70, false, 2, -233, [119], 120], -234], 4, 1], [0, "33zrFbs9BGibTZTS/InR/W", 1], [5, 20.6, 70], [-104.905, -15.142, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "box_sieuHu", 2, [-236], [[30, -235, [124], 125]], [0, "25uksq1ylNKLzO7JdhezdA", 1], [5, 461, 110], [-333.911, 307.066, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "lbTime", 64, [[-237, [2, -238]], 1, 4], [0, "edL/WpK/ZO/Ll87D8QSTip", 1], [4, 4290430164], [5, 40.6, 40], [73, 4.3, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "black", 0, 6, [[45, 0, -239, 130], [26, -240, [[21, "7269ege3OpEV4lKd6fET93f", "closeClicked", 6]], [4, 4292269782]]], [0, "938ruw7jBEo7J25rK5M6Hl", 1], [5, 3000, 3000]], [5, "label", 5, [[6, "<PERSON><PERSON><PERSON> mừng bạn đã trúng\nBONUS GAME", 45, 70, false, 1, 1, -241, [4], 5]], [0, "13wjIgf7xBKqm09q4okoHb", 1], [5, 490.69, 158.2], [0, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "prize", 8, [[3, 2, false, -242, [10], 11]], [0, "89ckdGfJVCrYUwzX2iX67s", 1], [5, 121, 152], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 8, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "0", 2]], [4, 4294967295], [4, 4294967295], 8], [5, "prize", 9, [[3, 2, false, -243, [15], 16]], [0, "75F9mkk/xEH4HX+k7JyCcb", 1], [5, 130, 164], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 9, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "1", 2]], [4, 4294967295], [4, 4294967295], 9], [5, "prize", 10, [[3, 2, false, -244, [20], 21]], [0, "e9J2RXSLhEq5EmRBU/ThP9", 1], [5, 134, 155], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 10, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "2", 2]], [4, 4294967295], [4, 4294967295], 10], [5, "prize", 11, [[3, 2, false, -245, [25], 26]], [0, "6be9Qzj4xNzYdKolpe9Ojs", 1], [5, 107, 137], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 11, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "3", 2]], [4, 4294967295], [4, 4294967295], 11], [5, "prize", 12, [[3, 2, false, -246, [30], 31]], [0, "1bUocE529NGZihxVmlR1Cb", 1], [5, 121, 152], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 12, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "4", 2]], [4, 4294967295], [4, 4294967295], 12], [5, "prize", 13, [[3, 2, false, -247, [35], 36]], [0, "67q+n7shlEKKXKjaLOjiTz", 1], [5, 130, 164], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 13, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "5", 2]], [4, 4294967295], [4, 4294967295], 13], [5, "prize", 14, [[3, 2, false, -248, [40], 41]], [0, "526jACmkhJSqlSQRctxjp6", 1], [5, 134, 155], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 14, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "6", 2]], [4, 4294967295], [4, 4294967295], 14], [5, "prize", 15, [[3, 2, false, -249, [45], 46]], [0, "d9JiJWhmlHeZ7+T28IsxL4", 1], [5, 107, 137], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 15, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "7", 2]], [4, 4294967295], [4, 4294967295], 15], [5, "prize", 16, [[3, 2, false, -250, [50], 51]], [0, "beClXUSc9IK6Ev4WL/pyYy", 1], [5, 121, 152], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 16, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "8", 2]], [4, 4294967295], [4, 4294967295], 16], [5, "prize", 17, [[3, 2, false, -251, [55], 56]], [0, "4eC3xgEO5NIrlkR3qUmqjn", 1], [5, 130, 164], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 17, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "9", 2]], [4, 4294967295], [4, 4294967295], 17], [5, "prize", 18, [[3, 2, false, -252, [60], 61]], [0, "beqMCOHShMZYmKppN1zq/d", 1], [5, 134, 155], [7.848, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 18, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "10", 2]], [4, 4294967295], [4, 4294967295], 18], [5, "prize", 19, [[3, 2, false, -253, [65], 66]], [0, "5bR8kAQ5ZPm6mVDLfj0weo", 1], [5, 107, 137], [10, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 19, [[1, "0790e2K+y5Ns4QcxPeKjbL8", "pickClicked", "11", 2]], [4, 4294967295], [4, 4294967295], 19], [15, "prize", 20, [[11, 2, false, -254, 70]], [0, "02mxVosLlFn5Gr7LRrQVC3", 1], [5, 200, 161], [0, 0.5, 0]], [15, "prize", 21, [[11, 2, false, -255, 75]], [0, "cbJ1FmFOpAn6R8qVvbXMFC", 1], [5, 200, 161], [0, 0.5, 0]], [15, "prize", 22, [[11, 2, false, -256, 80]], [0, "37+piySONJmKLpFAoOjnYY", 1], [5, 200, 161], [0, 0.5, 0]], [15, "prize", 23, [[11, 2, false, -257, 85]], [0, "4fvXL9J3FBWK2BKBvI/mqs", 1], [5, 200, 161], [0, 0.5, 0]], [15, "prize", 24, [[11, 2, false, -258, 90]], [0, "e04RdQ6KlCUZcrQ+K2V6/e", 1], [5, 200, 161], [0, 0.5, 0]], [25, 2, false, 56, [95]], [20, 1.05, 3, 26, [[1, "457fdQZoe9DAJLBhibZ4Ll4", "pickClicked", "0", 7]], [4, 4294967295], [4, 4294967295], 26], [25, 2, false, 57, [100]], [20, 1.05, 3, 27, [[1, "457fdQZoe9DAJLBhibZ4Ll4", "pickClicked", "1", 7]], [4, 4294967295], [4, 4294967295], 27], [25, 2, false, 58, [105]], [20, 1.05, 3, 28, [[1, "457fdQZoe9DAJLBhibZ4Ll4", "pickClicked", "2", 7]], [4, 4294967295], [4, 4294967295], 28], [52, "x15", 32, false, false, 1, 60], [53, "15", 14, 70, false, false, 1, 61], [2, 63], [54, "60", 32, false, false, 1, 1, 65, [123]], [20, 0.9, 3, 48, [[21, "8ce3eNnmvlHMakcC6rKL2d3", "quickPlayClicked", 1]], [4, 4294967295], [4, 4294967295], 48], [5, "label", 6, [[55, "<PERSON><PERSON><PERSON> mừng bạn đã được", 50, false, 1, 1, -259, 131]], [0, "3aMbpHSqdKnq7uKzCNW9hV", 1], [5, 425.96, 50], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "lbWin", 6, [-260], [0, "978QQ6miRL7LQldSCCe1Dh", 1], [5, 57.5, 100], [0, -113, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "0", 80, false, 1, 1, 109]], 0, [0, 7, 1, 0, 8, 107, 0, 9, 105, 0, 10, 106, 0, 11, 47, 0, 12, 6, 0, 13, 7, 0, 14, 3, 0, 15, 2, 0, 16, 5, 0, 0, 1, 0, -1, 49, 0, -2, 5, 0, -3, 2, 0, -4, 6, 0, 17, 103, 0, 18, 104, 0, -1, 69, 0, -2, 71, 0, -3, 73, 0, -4, 75, 0, -5, 77, 0, -6, 79, 0, -7, 81, 0, -8, 83, 0, -9, 85, 0, -10, 87, 0, -11, 89, 0, -12, 91, 0, 0, 2, 0, -1, 29, 0, -2, 3, 0, -3, 4, 0, -4, 7, 0, -5, 59, 0, -6, 47, 0, -7, 62, 0, -8, 64, 0, -9, 48, 0, 0, 3, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, -6, 13, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, -10, 17, 0, -11, 18, 0, -12, 19, 0, -1, 20, 0, -2, 21, 0, -3, 22, 0, -4, 23, 0, -5, 24, 0, 0, 5, 0, 0, 5, 0, 19, 5, 0, 0, 5, 0, -1, 50, 0, -2, 67, 0, 0, 6, 0, 20, 110, 0, 0, 6, 0, -1, 66, 0, -2, 108, 0, -3, 109, 0, -1, 97, 0, -2, 99, 0, -3, 101, 0, -1, 98, 0, -2, 100, 0, -3, 102, 0, 0, 7, 0, -1, 25, 0, -1, 69, 0, -1, 68, 0, -2, 30, 0, -1, 71, 0, -1, 70, 0, -2, 31, 0, -1, 73, 0, -1, 72, 0, -2, 32, 0, -1, 75, 0, -1, 74, 0, -2, 33, 0, -1, 77, 0, -1, 76, 0, -2, 34, 0, -1, 79, 0, -1, 78, 0, -2, 35, 0, -1, 81, 0, -1, 80, 0, -2, 36, 0, -1, 83, 0, -1, 82, 0, -2, 37, 0, -1, 85, 0, -1, 84, 0, -2, 38, 0, -1, 87, 0, -1, 86, 0, -2, 39, 0, -1, 89, 0, -1, 88, 0, -2, 40, 0, -1, 91, 0, -1, 90, 0, -2, 41, 0, 5, 20, 0, 0, 20, 0, -1, 92, 0, -2, 42, 0, 5, 21, 0, 0, 21, 0, -1, 93, 0, -2, 43, 0, 5, 22, 0, 0, 22, 0, -1, 94, 0, -2, 44, 0, 5, 23, 0, 0, 23, 0, -1, 95, 0, -2, 45, 0, 5, 24, 0, 0, 24, 0, -1, 96, 0, -2, 46, 0, 0, 25, 0, -1, 26, 0, -2, 27, 0, -3, 28, 0, 0, 26, 0, -2, 98, 0, -1, 56, 0, 0, 27, 0, -2, 100, 0, -1, 57, 0, 0, 28, 0, -2, 102, 0, -1, 58, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, -1, 51, 0, 0, 43, 0, 0, 43, 0, -1, 52, 0, 0, 44, 0, 0, 44, 0, -1, 53, 0, 0, 45, 0, 0, 45, 0, -1, 54, 0, 0, 46, 0, 0, 46, 0, -1, 55, 0, 0, 47, 0, -1, 61, 0, 0, 48, 0, -2, 107, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, -1, 97, 0, 0, 56, 0, -1, 99, 0, 0, 57, 0, -1, 101, 0, 0, 58, 0, 0, 59, 0, -1, 60, 0, -1, 103, 0, 0, 60, 0, -1, 104, 0, 0, 61, 0, 0, 62, 0, -1, 63, 0, 0, 63, 0, -2, 105, 0, 0, 64, 0, -1, 65, 0, -1, 106, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 70, 0, 0, 72, 0, 0, 74, 0, 0, 76, 0, 0, 78, 0, 0, 80, 0, 0, 82, 0, 0, 84, 0, 0, 86, 0, 0, 88, 0, 0, 90, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 108, 0, -1, 110, 0, 21, 1, 260], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 98, 99, 100, 101, 102, 103, 104, 106, 107, 110], [-1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, -1, 1, -1, 2, -1, 1, 2, 1, -1, 4, 1, 2, 1, -1, 4, 1, 2, 1, -1, 4, 1, 2, 1, -1, 4, 1, 2, 1, -1, 4, -1, 6, -1, -1, 1, -1, 6, -1, -1, 1, -1, 6, -1, -1, 1, -1, -2, -3, -4, -5, -6, -1, 1, 2, -1, 2, -1, 1, -1, -1, 1, -1, 1, 22, 23, 1, 2, 1, 1, 4, 1, 4, 1, 4, 2, 2, 2, 4, 2], [0, 9, 0, 9, 0, 8, 0, 19, 0, 20, 0, 10, 0, 1, 2, 0, 11, 0, 1, 2, 0, 12, 0, 1, 2, 0, 13, 0, 1, 2, 0, 10, 0, 1, 2, 0, 11, 0, 1, 2, 0, 12, 0, 1, 2, 0, 13, 0, 1, 2, 0, 10, 0, 1, 2, 0, 11, 0, 1, 2, 0, 12, 0, 1, 2, 0, 13, 0, 1, 2, 5, 1, 6, 2, 3, 5, 1, 6, 2, 3, 5, 1, 6, 2, 3, 5, 1, 6, 2, 3, 5, 1, 6, 2, 3, 0, 4, 4, 0, 21, 0, 4, 4, 0, 14, 0, 4, 4, 0, 14, 7, 22, 23, 24, 25, 26, 27, 28, 8, 0, 15, 0, 29, 0, 0, 30, 0, 31, 7, 32, 9, 8, 33, 7, 3, 7, 3, 7, 3, 8, 1, 15, 34, 1]], [[{"name": "icon4-0", "rect": [0, 0, 121, 152], "offset": [0, 0], "originalSize": [121, 152], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [35]], [[{"name": "X5", "rect": [0, 0, 95, 70], "offset": [0, 0], "originalSize": [95, 70], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [36]], [[[57, "showPrize", 0.16666666666666666, 0.6, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.05, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0.8, 0.8]], [{"frame": 0.16666666666666666}, "value", 8, [0, 1, 1]]], 11, 11]]]]], 0, 0, [], [], []], [[{"name": "avenger_symbol_2_9", "rect": [0, 0, 114, 126], "offset": [0, 0], "originalSize": [114, 126], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [37]], [[{"name": "box_heSo", "rect": [0, 0, 266, 99], "offset": [0, 0], "originalSize": [266, 99], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [38]], [[{"name": "iron-1", "rect": [0, 0, 258, 311], "offset": [0, 0], "originalSize": [258, 311], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [39]], [[{"name": "icon2-0", "rect": [0, 0, 130, 164], "offset": [0, 0], "originalSize": [130, 164], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [40]], [[{"name": "popup", "rect": [2, 0, 734, 522], "offset": [0, 0], "originalSize": [738, 522], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [41]], [[{"name": "avenger_top_0_1", "rect": [0, 0, 461, 110], "offset": [0, 0], "originalSize": [461, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [42]], [[{"name": "icon1-0", "rect": [0, 0, 134, 155], "offset": [0, 0], "originalSize": [134, 155], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [43]], [[{"name": "x2", "rect": [0, 0, 98, 70], "offset": [0, 0], "originalSize": [98, 70], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [44]], [[{"name": "Gold", "rect": [2, 0, 198, 161], "offset": [1, 0], "originalSize": [200, 161], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [45]], [[{"name": "X4", "rect": [0, 0, 97, 70], "offset": [0, 0], "originalSize": [97, 70], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [46]], [[{"name": "iron-0", "rect": [0, 0, 258, 310], "offset": [0, 0], "originalSize": [258, 310], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [47]], [[{"name": "BUTTON SPIN", "rect": [3, 0, 404, 125], "offset": [1.5, 5], "originalSize": [407, 135], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [48]], [[{"name": "X1", "rect": [0, 0, 88, 71], "offset": [0, 0], "originalSize": [88, 71], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [49]], [[{"name": "avenger_top_0_2", "rect": [0, 0, 461, 110], "offset": [0, 0], "originalSize": [461, 110], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [50]], [[{"name": "bg_money", "rect": [6, 0, 264, 44], "offset": [1, 0], "originalSize": [274, 44], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [51]], [[{"name": "avenger_mng_bg_2", "rect": [0, 0, 1559, 720], "offset": [0, 0], "originalSize": [1559, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [52]]]]