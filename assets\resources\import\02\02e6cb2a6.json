[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "886kdL1Y1BHL4+GYy8mp+o", "a9VpD0DP5LJYQPXITZq+uj", "41df9pkxdAy5GrqhcABLBI", "40Au7leqpI6KKSfsb14Ksf", "c1y3UL3AVHoqWPxPdQzt/K", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_parent", "_textureSetter", "root", "_N$target", "_N$content", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_children", "_anchorPoint", "_trs"], 0, 4, 9, 1, 5, 2, 5, 7], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 9, 5, 5, 5, 1], ["cc.Sprite", ["_type", "_isTrimmedMode", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint"], 2, 12, 9, 4, 5, 5], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["c1258OEgGhNlanTanxfRHzr", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["_enabled", "horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1]], [[8, 0, 1, 2], [2, 2, 3, 4, 5, 2], [0, 0, 5, 4, 3, 6, 9, 2], [1, 2, 4, 1], [10, 0, 1, 2, 3], [4, 0, 2], [0, 0, 7, 4, 3, 2], [0, 0, 5, 7, 4, 3, 6, 2], [0, 0, 5, 3, 6, 8, 2], [0, 0, 1, 5, 4, 3, 6, 9, 3], [0, 0, 2, 5, 7, 4, 3, 6, 8, 9, 3], [0, 0, 5, 4, 3, 6, 8, 2], [5, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 2, 1], [7, 0, 1], [2, 0, 1, 3, 4, 5, 3], [1, 0, 2, 3, 5, 6, 7, 2], [1, 1, 0, 2, 3, 3], [9, 0, 1, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 7]], [[[{"name": "helptxmd5", "rect": [0, 0, 1195, 671], "offset": [0, 0], "originalSize": [1195, 671], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [3], [2]], [[[5, "taiXiuMd5HelpView"], [6, "taiXiuHelpView", [-4, -5, -6, -7], [[13, -2, [10, 11], 9], [14, -3]], [0, "482uhYoORHP6+1RUzk1glq", -1]], [7, "nen popup", 1, [-10], [[15, 1, false, -8, [2], 3], [3, -9, [4, 4292269782]]], [0, "241vm6yaND3ruq4O7+83Ie", 1], [5, 1195, 671]], [12, "view", [[-12, [8, "content", -13, [0, "c8imFkEyFAa7y4BOT8FjJy", 1], [5, 1000, 560], [0, 0.5, 1]]], 1, 4], [[18, -11, [6]]], [0, "99WhUSkfJBTazBqOuKi6GR", 1], [5, 1000, 560], [0, 0.5, 1]], [2, "btnClose", 1, [[1, 0, -14, [7], 8], [16, 3, -16, [[4, "c1258OEgGhNlanTanxfRHzr", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -15]], [0, "13yjKDFnVNZ6AAvSO91mEo", 1], [5, 90, 50], [553.7, 307.7, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "black", 100, 1, [[1, 0, -17, [0], 1], [3, -18, [4, 4292269782]]], [0, "9082MxgbxHNIBcevNUN1fe", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "scrollview", false, 1, [3], [[19, false, false, 0.75, 0.23, null, null, -20, -19]], [0, "cafBi9cEdFdLzXmmoC3Kup", 1], [5, 1000, 560], [0, 0.5, 1], [0, 252, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "hd_pic", 3, [[1, 0, -21, [4], 5]], [0, "75OFmMkLdI0K6hWJBCYuHP", 1], [5, 890, 640], [0, 0.5, 1]], [2, "btnRule", 2, [[17, 0.9, 3, -22, [[4, "c1258OEgGhNlanTanxfRHzr", "showRuleClicked", 1]]]], [0, "a40wahOqRPdIdCgFBqySOi", 1], [5, 779, 30], [0, -91, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, -3, 6, 0, -4, 4, 0, 0, 2, 0, 0, 2, 0, -1, 8, 0, 0, 3, 0, -1, 7, 0, 2, 3, 0, 0, 4, 0, 5, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 6, 7, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 7, 1, 3, 2, 6, 22], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, -1, 1, 8, -1, -2], [0, 3, 0, 4, 0, 5, 0, 0, 6, 1, 1, 7]]]]