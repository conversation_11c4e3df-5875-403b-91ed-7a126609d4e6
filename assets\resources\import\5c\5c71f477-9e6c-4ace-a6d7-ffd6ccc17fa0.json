[1, ["ecpdLyjvZBwrvm+cedCcQy", "d82n49/IVAvIEqsa0xvvk0", "90Ss2Yf1lLHYPvA9KDgBaE", "7a/QZLET9IDreTiBfRn2PD", "21tx2pDSpElYCwzUj6L+8q", "d0eRki7tdFCa9wS+t6obx8", "24xd2Xl+xHVZeWwPN10Wzf", "97be04EipF04Aia2h48z4C", "41obftpCRMW6380/g5lNfM", "c4E6vT74FAMY2sFh5Bh9cH", "b5qw/Tc11MyrVtdNkz9Fim", "fdNoodJKVLj4dF1TLppv2g", "06RCegptdIBYzb8YE5jwlN", "73dYUDQRFCEJymCU8UE4Bv", "a9VpD0DP5LJYQPXITZq+uj", "1ewsTTeZRBbL9DYfWsJfc7", "83G0v9bH5EBJH+BvUuy/Le", "c1y3UL3AVHoqWPxPdQzt/K", "c25Leu0BdNDphgb/Hp9jw/", "2cWB/vWPRHja3uQTinHH30", "f0puneJcNGfY+3VzwuGjGx", "4d4OkqjRJEeZGzHA0GW7zL", "1efjUgjgRNroUUKxfQSfVE", "24Jdc6Aj5CiJf1WtGQrr1q", "bfBjN8MAhH37ZondOTDPHj", "60lCKF0BFLRJpfGf89TIRK", "bcbgFB+epIlKdGw1JroyQo"], ["node", "_N$file", "_spriteFrame", "_N$skeletonData", "_N$disabledSprite", "_defaultClip", "_N$pressedSprite", "_N$hoverSprite", "root", "btnBack", "btnNext", "lbTotalRefundXiu", "lbTotalRefundTai", "lbTotalBetXiu", "lbTotalBetTai", "spriteDice3", "spriteDice2", "spriteDice1", "lblTotalDice", "lbXiu", "lbTai", "nodeXiu", "nodeTai", "lbSessionID", "xiuSessionDetailListView", "taiSessionDetailListView", "lbRefund", "lbBet", "lbNickName", "lbTime", "_N$target", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 1, 5, 7, 2, 5], ["cc.Label", ["_isSystemFontUsed", "_N$verticalAlign", "_string", "_N$horizontalAlign", "_fontSize", "_lineHeight", "_enableWrapText", "_enabled", "_N$overflow", "_spacingX", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_eulerAngles", "_children"], 2, 1, 12, 4, 5, 7, 5, 2], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "_animationName", "defaultSkin", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["859d5jytnFJVKa9eiFCDL5A", ["node", "lbTime", "lbNickName", "lbBet", "lbRefund"], 3, 1, 1, 1, 1, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["6aaf3llI/tNzJea/4Ilfu6n", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["4fff3XPOO1MjZhjkGQ4GcnW", ["node", "taiSessionDetailListView", "xiuSessionDetailListView", "lbSessionID", "nodeTai", "nodeXiu", "nodeEffectTais", "nodeEffectXius", "lbTai", "lbXiu", "lblTotalDice", "spriteDice1", "spriteDice2", "spriteDice3", "lbTotalBetTai", "lbTotalBetXiu", "lbTotalRefundTai", "lbTotalRefundXiu", "btnNext", "btnBack", "sfDices"], 3, 1, 1, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3]], [[6, 0, 1, 2], [0, 0, 5, 4, 3, 6, 7, 2], [4, 0, 2, 3, 4, 5, 6, 2], [2, 0, 4, 5, 6, 2], [1, 2, 4, 5, 6, 0, 3, 1, 10, 11, 12, 8], [0, 0, 5, 8, 4, 3, 6, 7, 2], [2, 0, 4, 5, 2], [2, 4, 5, 6, 1], [1, 2, 4, 5, 6, 0, 3, 1, 10, 11, 8], [4, 0, 2, 3, 4, 5, 7, 6, 2], [1, 2, 4, 5, 6, 0, 3, 1, 8, 10, 11, 12, 9], [2, 1, 4, 5, 6, 2], [1, 2, 4, 5, 6, 0, 1, 10, 11, 7], [7, 3, 0, 1, 2, 4, 5, 6, 5], [10, 0, 1, 2, 3], [0, 0, 5, 8, 3, 6, 7, 2], [0, 0, 2, 5, 4, 3, 6, 7, 3], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 9, 7, 2], [3, 0, 1, 7, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [2, 3, 2, 0, 1, 4, 5, 6, 5], [5, 1, 0, 2, 3, 4, 5, 6, 3], [1, 7, 4, 5, 0, 3, 1, 10, 11, 7], [1, 2, 0, 3, 1, 10, 11, 12, 5], [8, 3, 0, 1, 4, 5, 6, 5], [12, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1], [9, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 4, 3, 6, 2], [0, 0, 5, 8, 3, 7, 2], [0, 0, 8, 4, 3, 6, 2], [3, 0, 1, 2, 3, 4, 5, 6, 2], [3, 0, 1, 2, 3, 4, 5, 2], [2, 2, 0, 1, 4, 5, 6, 4], [5, 2, 7, 1], [5, 0, 2, 3, 4, 5, 6, 2], [6, 1, 1], [1, 2, 4, 0, 3, 1, 10, 11, 12, 6], [1, 2, 0, 3, 1, 10, 11, 5], [1, 2, 4, 5, 6, 0, 9, 3, 1, 10, 11, 9], [7, 0, 1, 2, 4, 5, 6, 4], [8, 0, 1, 2, 5, 6, 4], [11, 0, 1, 2, 3, 4, 1], [16, 0, 1, 2, 1], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1]], [[30, "taiXiuSieuTocSessionDetailView"], [31, "taiXiuSessionDetailView", [-23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52], [[48, -2, [92, 93], 91], [49, -22, -21, -20, -19, -18, -17, [-16], [-15], -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, [94, 95, 96, 97, 98, 99]]], [41, -1]], [35, "<PERSON><PERSON>", [-58, -59, -60, -61, -62], [[47, -57, -56, -55, -54, -53]], [0, "71ryvLiWRHNZ72pUdSb1nZ", 1], [5, 487, 50]], [15, "title-left", 1, [-63, -64, -65, -66, -67, -68, -69], [0, "b6JEenaVFBS7z+g+IyhepT", 1], [5, 500, 30], [-270, 113, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "title-left", 1, [-70, -71, -72, -73, -74, -75, -76], [0, "64QNvvetVL/YQbDEoJkayN", 1], [5, 500, 30], [259, 113, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "total-tai", 1, [-78, -79, -80, -81], [[21, false, 1, 0, false, -77, [57], 58]], [0, "68AEbHKg9Nkrqq9xnVN5pA", 1], [5, 487, 50], [-263, -251, 0, 0, 0, 0, 1, 1, 1.1, 1]], [5, "total-xiu", 1, [-83, -84, -85, -86], [[21, false, 1, 0, false, -82, [65], 66]], [0, "edcF8gL3xC7LxuSKn58Byp", 1], [5, 487, 50], [255, -251, 0, 0, 0, 0, 1, 1, 1.1, 1]], [19, "scrollview-tai", 1, [-90], [[-87, -88, [29, -89]], 1, 1, 4], [0, "88JCrALKFAUpWp5WoQimue", 1], [5, 500, 330], [-271, -72, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "scrollview-xiu", 1, [-94], [[-91, -92, [29, -93]], 1, 1, 4], [0, "2ddmR339lKipLmv+IS5GO8", 1], [5, 500, 330], [261, -72, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "btnBackSession", 1, [[[11, false, -95, [14], 15], -96], 4, 1], [0, "a1/Xw4XI9Efqch1Y2QPfXA", 1], [5, 23, 35], [-264, 175, 0, 0, 0, 1, 6.123233995736766e-17, 1.5, 1.5, 1], [1, 0, 0, 180]], [37, "btnNextSession", 1, [[[11, false, -97, [16], 17], -98], 4, 1], [0, "7aGsIOsXtFmLjSOqwKajgi", 1], [5, 23, 35], [285, 175, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [5, "btnClose", 1, [-101], [[40, 3, -100, [[14, "4fff3XPOO1MjZhjkGQ4GcnW", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -99]], [0, "3fm/KsSmRIhZb4QfbTz1Z7", 1], [5, 80, 80], [506, 314, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "black", 100, 1, [[3, 0, -102, [0], 1], [39, -103, [4, 4292269782]]], [0, "73lxVvwE9MRa8dsxQFhD7s", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nen popup", 1, [-105], [[38, 1, 0, false, -104, [4], 5]], [0, "2fr4Dq6y5KzZ+Yak0PO0Lv", 1], [5, 1084, 643], [0, 13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tai", 1, [[13, "default", "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -106, [6], 7]], [0, "6e8L4KFxhDLqvKZGBZ5rBz", 1], [5, 311, 301], [-425, 229, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "xiu", 1, [[13, "default", "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -107, [8], 9]], [0, "3bFmADV8xAOIuKUBJLqn2y", 1], [5, 311, 301], [419, 220, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "anim<PERSON>iu", 1, [[13, "default", "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -108, [10], 11]], [0, "0daFa7RzhH2KpgtmIQuzho", 1], [5, 311, 301], [419, 218.9, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "<PERSON>im<PERSON><PERSON>", 1, [[45, "<PERSON><PERSON><PERSON>", 0, "<PERSON><PERSON><PERSON>", -109, [12], 13]], [0, "720d4EvSVB44o04gMRq1Da", 1], [5, 311, 301], [-425, 229, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "layout-nick<PERSON><PERSON>", 2, [-111], [[46, 1, 1, 5, -110, [5, 88.63, 50]]], [0, "de4BopV8pFSYo7bDIInaMf", 1], [5, 88.63, 50], [-43.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "view", 7, [-113], [[26, -112, [75]]], [0, "71Q05LiVxPPJIKjeAM9XAP", 1], [5, 520, 320]], [18, "content", 19, [[25, false, 1, 2, 10, -114, [5, 500, 0]]], [0, "b7dmvL2HVHX7fIuRA/Ag5M", 1], [5, 500, 0], [0, 0.5, 1], [0, 143, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "view", 8, [-116], [[26, -115, [76]]], [0, "67q2jbDQpMBIvP3fdPPK+E", 1], [5, 520, 320]], [18, "content", 21, [[25, false, 1, 2, 10, -117, [5, 500, 0]]], [0, "f5tn1TjUZLo56Ux4l6QVTZ", 1], [5, 500, 0], [0, 0.5, 1], [0, 143, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 13, [[42, "THỐNG KÊ PHIÊN", 26, false, 1, 1, -118, [2], 3]], [0, "58GgUEANJHmJ0nYofrVaDR", 1], [5, 359.94, 32.5], [0, 307, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 1.1, 3, 9, [[14, "4fff3XPOO1MjZhjkGQ4GcnW", "backSessionClicked", 1]], [4, 4294967295], [4, 2533359615], 9], [22, 1.1, 3, 10, [[14, "4fff3XPOO1MjZhjkGQ4GcnW", "nextSessionClicked", 1]], [4, 4294967295], [4, 2533359615], 10], [2, "dice_1", 1, [-119], [0, "beCIT6/4xIfaYmsw/KuqcW", 1], [5, 68, 68], [-174, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 0, 26, [18]], [1, "plus", 1, [[7, -120, [19], 20]], [0, "60OfZtwotP26HIVEEBoIsd", 1], [5, 63, 62], [10, 174, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "dice_2", 1, [-121], [0, "30g9bUqh9Ps7G37Z4mtI1j", 1], [5, 68, 68], [-51, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 0, 29, [21]], [1, "plus", 1, [[7, -122, [22], 23]], [0, "8fgouryOBKmqNpDkeD/bVz", 1], [5, 63, 62], [-111, 174, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [2, "dice_3", 1, [-123], [0, "d5LCgzgMVHFYOhxizPCkt6", 1], [5, 68, 68], [69, 168, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 0, 32, [24]], [1, "blance", 1, [[7, -124, [25], 26]], [0, "bdoz2xj4xPPp5jG9H7Xkhz", 1], [5, 26, 20], [136, 174, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTotal", 1, [-125], [0, "dcqQo54ltCoprhX3gOTs5o", 1], [5, 78.75, 50], [204, 242, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "16", false, 1, 1, 35, [27]], [33, "sprite", 11, [[11, false, -126, [28], 29]], [0, "5etFymNvRBXa2EMi7WLiog", 1], [5, 69, 36]], [2, "lbSession", 1, [-127], [0, "86uT48h3lJGI0sxEL84nAJ", 1], [5, 319.94, 63], [0, 230, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "PHIEN xxxxxx - Ngay xx-xx-xxxx", 20, 50, false, false, 1, 1, 38, [30]], [20, "lbTai", false, 1, [-128], [0, "0ej0TJSFNByoIi5It8ttNw", 1], [5, 0, 62.5], [-183, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [23, false, 50, 50, false, 1, 1, 40, [31]], [20, "lbXiu", false, 1, [-129], [0, "45tCrKGHhCs7WcYPrFcX02", 1], [5, 0, 62.5], [161, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [23, false, 50, 50, false, 1, 1, 42, [32]], [1, "bgContentLeft", 1, [[7, -130, [33], 34]], [0, "84f4y50/dHer6EMc/sMAGX", 1], [5, 678, 375], [-270, -54, 0, 0, 0, 0, 1, 0.76, 1, 1]], [1, "bgContentRight", 1, [[7, -131, [35], 36]], [0, "f7N4ItcUhKWYDYLKExNlzc", 1], [5, 678, 375], [260, -54, 0, 0, 0, 0, 1, 0.76, 1, 1]], [1, "lbTime", 3, [[10, "Thời Gian", 14, 48, false, false, 1, 1, 1, -132, [37], 38]], [0, "a7P3r6o6tJd5nqybfqadK1", 1], [5, 91, 30], [-199, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1", 3, [[3, 0, -133, [39], 40]], [0, "e43MAY8+tJLIiEGR8X2azY", 1], [5, 25, 379], [-132, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 3, [[4, "<PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -134, [41], 42]], [0, "72foDeQ7FHqInZnuc7gvGQ", 1], [5, 113.31, 21], [-40.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 3, [[3, 0, -135, [43], 44]], [0, "1dh0g9XFxH6apNQMeogUPK", 1], [5, 25, 379], [75, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 3, [[4, "Đặt <PERSON><PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -136, [45], 46]], [0, "2eCQE+JktBoLKRaHUSqhCX", 1], [5, 83.13, 21], [111.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "3", 3, [[3, 0, -137, [47], 48]], [0, "f1JqTZE9ZHFqg7jg3gINs2", 1], [5, 25, 379], [166, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRefund", 3, [[10, "<PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, 1, -138, [49], 50]], [0, "21XikddZxMV5l22B+I/YpR", 1], [5, 69.13, 30], [205, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTotal", 5, [[4, "Tổng:", 20, 48, false, false, 1, 1, -139, [51], 52]], [0, "7daJgWOppHAJsNUklY9moP", 1], [5, 66.25, 30], [-57, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbTotalBetTai", 5, [-140], [0, "e3Zbcr/sNDG7jpAHFYDabX", 1], [5, 111.25, 30], [0, 0, 0.5], [-12, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "200.000k", 20, 48, false, false, 1, 54, [53]], [9, "lbTotalRefundTai", 5, [-141], [0, "cdLSka7hxNuLhsAdpSlEe1", 1], [5, 111.25, 30], [0, 0, 0.5], [134, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "200.000k", 20, 48, false, false, 1, 56, [54]], [1, "line", 5, [[24, "/", false, 1, 1, -142, [55], 56]], [0, "214LeS3aJG6KOxZf8yuEfN", 1], [5, 23.75, 50], [115, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTotal", 6, [[4, "Tổng:", 20, 48, false, false, 1, 1, -143, [59], 60]], [0, "0aOfqXMMFGeo1YQKOuXk/X", 1], [5, 66.25, 30], [-60, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbTotalBetXiu", 6, [-144], [0, "84o9B2+IdK7LOnQhRP7NJX", 1], [5, 111.25, 30], [0, 0, 0.5], [-14.5, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "200.000k", 20, 48, false, false, 1, 60, [61]], [1, "line", 6, [[24, "/", false, 1, 1, -145, [62], 63]], [0, "bcrDJH40ZJoprL3WlY2YO+", 1], [5, 23.75, 50], [117, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbTotalRefundXiu", 6, [-146], [0, "a7vc/UGLRMhI5I/zJxsuC8", 1], [5, 111.25, 30], [0, 0, 0.5], [135, -5, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "200.000k", 20, 48, false, false, 1, 1, 63, [64]], [16, "duoi", false, 1, [[6, 0, -147, [67]]], [0, "91JO6t0rNBKaSwkY5ByBCV", 1], [5, 1000, 7], [-3, -216, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "tren", false, 1, [[6, 0, -148, [68]]], [0, "01picr/hhJZI+lhZJ/pVHi", 1], [5, 1000, 7], [-3, 80, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "temp", 1, [2], [0, "62A/+E4ExPdaFKSkp9wUQi", 1], [-263, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTime", 2, [-149], [0, "b7qcJGKF1P14DeeP4yOyS8", 1], [5, 100.13, 27], [-203.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "<PERSON><PERSON><PERSON><PERSON> gian", 18, 48, false, false, 1, 1, 68, [69]], [2, "lbBet", 2, [-150], [0, "3dX1ux7n9HPabr89PWqxvw", 1], [5, 83.25, 27], [105.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "100.000", 18, 48, false, false, 1, 1, 70, [70]], [2, "lbRefund", 2, [-151], [0, "e2lPHzPuVFQ6sm3W7oPgeH", 1], [5, 96.75, 27], [199.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "1000.000", 18, 48, false, false, 1, 1, 72, [71]], [2, "lbNickName", 18, [-152], [0, "ebJ1H3uwpH7q3xmRYROblX", 1], [5, 88.63, 18], [0, 17, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "<PERSON><PERSON><PERSON>", 12, 48, false, false, -1, 1, 1, 74, [72]], [1, "line", 2, [[3, 0, -153, [73], 74]], [0, "8ckGpUPIRAT7COPlYCzNW3", 1], [5, 486, 16], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [27, false, 0.75, 0.23, null, null, 7, 20], [28, 20, 10, 400, 7, 2, 77], [27, false, 0.75, 0.23, null, null, 8, 22], [28, 20, 10, 400, 8, 2, 79], [1, "lbTime", 4, [[10, "Thời Gian", 14, 48, false, false, 1, 1, 1, -154, [77], 78]], [0, "d1+NFvSMtPooSJ34oYUxuv", 1], [5, 91, 30], [-199, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1", 4, [[3, 0, -155, [79], 80]], [0, "7fa95reG1Ee4mqfaYC7Ldh", 1], [5, 25, 379], [-132, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 4, [[4, "<PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -156, [81], 82]], [0, "96onTuoBVDraFuqO3Dek25", 1], [5, 113.31, 21], [-40.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 4, [[3, 0, -157, [83], 84]], [0, "3bcGyE8NlNnbZDFIB6tUby", 1], [5, 25, 379], [75, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 4, [[4, "Đặt <PERSON><PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, -158, [85], 86]], [0, "48t4b+IPtHqINBbVpc6XB6", 1], [5, 83.13, 21], [111.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "3", 4, [[3, 0, -159, [87], 88]], [0, "adOMb8yM1FSItThenCnzY6", 1], [5, 25, 379], [166, -157, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRefund", 4, [[10, "<PERSON><PERSON><PERSON>", 14, 48, false, false, 1, 1, 1, -160, [89], 90]], [0, "05EGAjSkNHtYLO+re0dNeh", 1], [5, 69.13, 30], [205, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 8, 1, 0, 0, 1, 0, 9, 24, 0, 10, 25, 0, 11, 64, 0, 12, 57, 0, 13, 61, 0, 14, 55, 0, 15, 33, 0, 16, 30, 0, 17, 27, 0, 18, 36, 0, 19, 43, 0, 20, 41, 0, -1, 16, 0, -1, 17, 0, 21, 15, 0, 22, 14, 0, 23, 39, 0, 24, 80, 0, 25, 78, 0, 0, 1, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, -6, 17, 0, -7, 9, 0, -8, 10, 0, -9, 26, 0, -10, 28, 0, -11, 29, 0, -12, 31, 0, -13, 32, 0, -14, 34, 0, -15, 35, 0, -16, 11, 0, -17, 38, 0, -18, 40, 0, -19, 42, 0, -20, 44, 0, -21, 45, 0, -22, 3, 0, -23, 5, 0, -24, 6, 0, -25, 65, 0, -26, 66, 0, -27, 67, 0, -28, 7, 0, -29, 8, 0, -30, 4, 0, 26, 73, 0, 27, 71, 0, 28, 75, 0, 29, 69, 0, 0, 2, 0, -1, 68, 0, -2, 70, 0, -3, 72, 0, -4, 18, 0, -5, 76, 0, -1, 46, 0, -2, 47, 0, -3, 48, 0, -4, 49, 0, -5, 50, 0, -6, 51, 0, -7, 52, 0, -1, 81, 0, -2, 82, 0, -3, 83, 0, -4, 84, 0, -5, 85, 0, -6, 86, 0, -7, 87, 0, 0, 5, 0, -1, 53, 0, -2, 54, 0, -3, 56, 0, -4, 58, 0, 0, 6, 0, -1, 59, 0, -2, 60, 0, -3, 62, 0, -4, 63, 0, -1, 77, 0, -2, 78, 0, 0, 7, 0, -1, 19, 0, -1, 79, 0, -2, 80, 0, 0, 8, 0, -1, 21, 0, 0, 9, 0, -2, 24, 0, 0, 10, 0, -2, 25, 0, 30, 11, 0, 0, 11, 0, -1, 37, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, -1, 23, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, -1, 74, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 23, 0, -1, 27, 0, 0, 28, 0, -1, 30, 0, 0, 31, 0, -1, 33, 0, 0, 34, 0, -1, 36, 0, 0, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, -1, 55, 0, -1, 57, 0, 0, 58, 0, 0, 59, 0, -1, 61, 0, 0, 62, 0, -1, 64, 0, 0, 65, 0, 0, 66, 0, -1, 69, 0, -1, 71, 0, -1, 73, 0, -1, 75, 0, 0, 76, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 31, 1, 2, 32, 67, 160], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 24, 24, 25, 27, 30, 33, 36, 39, 55, 57, 61, 64, 69, 71, 73, 75], [-1, 2, -1, 1, -1, 2, -1, 3, -1, 3, -1, 3, -1, 3, -1, 2, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, -1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 1, -1, 2, -1, 1, -1, -1, 1, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 5, -1, -2, -1, -2, -3, -4, -5, -6, 6, 7, 4, 4, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 14, 0, 6, 0, 15, 3, 4, 3, 4, 3, 4, 3, 4, 0, 7, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 16, 0, 0, 17, 0, 0, 0, 0, 9, 0, 9, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 1, 0, 0, 0, 1, 0, 10, 0, 1, 0, 0, 1, 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 18, 0, 0, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 11, 11, 19, 5, 20, 21, 22, 23, 24, 12, 12, 13, 13, 5, 5, 5, 6, 25, 1, 1, 1, 1, 1, 1, 1, 26]]