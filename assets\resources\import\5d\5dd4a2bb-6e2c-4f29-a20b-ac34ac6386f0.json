[1, ["cdGBCZ3L1D0Y52MVh+UV1L"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "x10_ani", "\nx10_ani.png\nsize: 439,92\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nX1\n  rotate: false\n  xy: 368, 9\n  size: 27, 19\n  orig: 27, 19\n  offset: 0, 0\n  index: -1\nX10\n  rotate: false\n  xy: 368, 30\n  size: 39, 19\n  orig: 39, 19\n  offset: 0, 0\n  index: -1\nX2\n  rotate: false\n  xy: 411, 52\n  size: 27, 19\n  orig: 27, 19\n  offset: 0, 0\n  index: -1\nX3\n  rotate: false\n  xy: 397, 9\n  size: 27, 19\n  orig: 27, 19\n  offset: 0, 0\n  index: -1\nX4\n  rotate: false\n  xy: 411, 73\n  size: 28, 19\n  orig: 28, 19\n  offset: 0, 0\n  index: -1\nX5\n  rotate: false\n  xy: 409, 30\n  size: 27, 19\n  orig: 27, 19\n  offset: 0, 0\n  index: -1\nngoc_on\n  rotate: false\n  xy: 368, 51\n  size: 41, 41\n  orig: 41, 41\n  offset: 0, 0\n  index: -1\nx10_bg\n  rotate: true\n  xy: 0, 0\n  size: 92, 366\n  orig: 92, 366\n  offset: 0, 0\n  index: -1\n", ["x10_ani.png"], {"skeleton": {"hash": "NMd5VGZuQ3TEmGiuDiiSnYnMZsE", "spine": "3.6.53", "width": 92, "height": 366}, "bones": [{"name": "root"}, {"name": "X1", "parent": "root", "x": -3.19, "y": -76.85}, {"name": "X2", "parent": "root", "x": -4.39, "y": -34.85}, {"name": "X3", "parent": "root", "x": -4.79, "y": 7.15}, {"name": "X4", "parent": "root", "x": -3.99, "y": 49.55}, {"name": "X5", "parent": "root", "x": -4.39, "y": 90.35}, {"name": "X10", "parent": "root", "x": -4.79, "y": 133.55}, {"name": "ngoc_on", "parent": "root", "length": 29.68, "rotation": 91.55, "x": -4.01, "y": -91.94}, {"name": "ngoc_on2", "parent": "root", "length": 23.27, "rotation": 91.97, "x": -3.2, "y": -48.63}, {"name": "ngoc_on3", "parent": "root", "length": 29.72, "rotation": 93.09, "x": -4.81, "y": -7.73}, {"name": "ngoc_on4", "parent": "root", "length": 35.29, "rotation": 90, "x": -4.81, "y": 32.37}, {"name": "ngoc_on5", "parent": "root", "length": 32.24, "rotation": 95.71, "x": -1.6, "y": 74.07}, {"name": "ngoc_on7", "parent": "root", "length": 34.57, "rotation": 93.99, "x": -2.4, "y": 115.78}], "slots": [{"name": "x10_bg", "bone": "root", "attachment": "x10_bg"}, {"name": "ngoc_on", "bone": "ngoc_on", "attachment": "ngoc_on"}, {"name": "ngoc_on2", "bone": "ngoc_on2", "attachment": "ngoc_on"}, {"name": "ngoc_on3", "bone": "ngoc_on3", "attachment": "ngoc_on"}, {"name": "ngoc_on4", "bone": "ngoc_on4", "attachment": "ngoc_on"}, {"name": "ngoc_on5", "bone": "ngoc_on5", "attachment": "ngoc_on"}, {"name": "ngoc_on6", "bone": "ngoc_on7", "attachment": "ngoc_on"}, {"name": "X1", "bone": "X1", "attachment": "X1"}, {"name": "X2", "bone": "X2", "attachment": "X2"}, {"name": "X3", "bone": "X3", "attachment": "X3"}, {"name": "X4", "bone": "X4", "attachment": "X4"}, {"name": "X5", "bone": "X5", "attachment": "X5"}, {"name": "X10", "bone": "X10", "attachment": "X10"}], "skins": {"default": {"X1": {"X1": {"x": 0.8, "y": -0.4, "width": 27, "height": 19}}, "X10": {"X10": {"x": 1.6, "width": 39, "height": 19}}, "X2": {"X2": {"x": 0.8, "y": -0.4, "width": 27, "height": 19}}, "X3": {"X3": {"x": 2.4, "y": -1.2, "width": 27, "height": 19}}, "X4": {"X4": {"x": 1.6, "y": -1.2, "width": 28, "height": 19}}, "X5": {"X5": {"x": 1.6, "y": -0.4, "width": 27, "height": 19}}, "ngoc_on": {"ngoc_on": {"x": 15.55, "y": -1.65, "rotation": -91.55, "width": 41, "height": 41}}, "ngoc_on2": {"ngoc_on": {"x": 13.5, "y": -0.89, "rotation": -91.97, "width": 41, "height": 41}}, "ngoc_on3": {"ngoc_on": {"x": 14.33, "y": -2.81, "rotation": -93.09, "width": 41, "height": 41}}, "ngoc_on4": {"ngoc_on": {"x": 16.69, "y": -2.03, "rotation": -90, "width": 41, "height": 41}}, "ngoc_on5": {"ngoc_on": {"x": 16.66, "y": -0.48, "rotation": -95.71, "width": 41, "height": 41}}, "ngoc_on6": {"ngoc_on": {"x": 17.31, "y": -0.83, "rotation": -93.99, "width": 41, "height": 41}}, "x10_bg": {"x10_bg": {"width": 92, "height": 366}}}}, "animations": {"x0": {"slots": {"X1": {"color": [{"time": 0, "color": "ffffff00"}]}, "X2": {"color": [{"time": 0, "color": "ffffff00"}]}, "X3": {"color": [{"time": 0, "color": "ffffff00"}]}, "X4": {"color": [{"time": 0, "color": "ffffff00"}]}, "X5": {"color": [{"time": 0, "color": "ffffff00"}]}, "X10": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on2": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on3": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on4": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on5": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on6": {"color": [{"time": 0, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "X1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "X3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "X4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "X5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "X10": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "ngoc_on": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "ngoc_on2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "ngoc_on3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "ngoc_on4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "ngoc_on5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "ngoc_on7": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}, "x1": {"slots": {"X1": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "X2": {"color": [{"time": 0, "color": "ffffff00"}]}, "X3": {"color": [{"time": 0, "color": "ffffff00"}]}, "X4": {"color": [{"time": 0, "color": "ffffff00"}]}, "X5": {"color": [{"time": 0, "color": "ffffff00"}]}, "X10": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on2": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on3": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on4": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on5": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on6": {"color": [{"time": 0, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.257, "y": 1.257}, {"time": 1, "x": 0.869, "y": 0.869}, {"time": 1.3333, "x": 1.257, "y": 1.257}, {"time": 1.6667, "x": 0.869, "y": 0.869}, {"time": 2, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}, "x2": {"slots": {"X1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "X3": {"color": [{"time": 0, "color": "ffffff00"}]}, "X4": {"color": [{"time": 0, "color": "ffffff00"}]}, "X5": {"color": [{"time": 0, "color": "ffffff00"}]}, "X10": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on3": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on4": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on5": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on6": {"color": [{"time": 0, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.257, "y": 1.257}, {"time": 1, "x": 0.869, "y": 0.869}, {"time": 1.3333, "x": 1.257, "y": 1.257}, {"time": 1.6667, "x": 0.869, "y": 0.869}, {"time": 2, "x": 1, "y": 1}]}, "X3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}, "x3": {"slots": {"X1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X4": {"color": [{"time": 0, "color": "ffffff00"}]}, "X5": {"color": [{"time": 0, "color": "ffffff00"}]}, "X10": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 1, "x": 1.257, "y": 1.257}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.257, "y": 1.257}, {"time": 2, "x": 1, "y": 1}]}, "X4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}, "x4": {"slots": {"X1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X5": {"color": [{"time": 0, "color": "ffffff00"}]}, "X10": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on4": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 1, "x": 1.257, "y": 1.257}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.257, "y": 1.257}, {"time": 2, "x": 1, "y": 1}]}, "X5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}, "x5": {"slots": {"X1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X5": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X10": {"color": [{"time": 0, "color": "ffffff00"}]}, "ngoc_on": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}, "ngoc_on6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 2, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 1, "x": 1.257, "y": 1.257}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.257, "y": 1.257}, {"time": 2, "x": 1, "y": 1}]}, "X10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on": {"rotate": [{"time": 2, "angle": 0}], "translate": [{"time": 2, "x": 0, "y": 0}], "scale": [{"time": 2, "x": 1, "y": 1}]}, "ngoc_on2": {"rotate": [{"time": 2, "angle": 0}], "translate": [{"time": 2, "x": 0, "y": 0}], "scale": [{"time": 2, "x": 1, "y": 1}]}, "ngoc_on3": {"rotate": [{"time": 2, "angle": 0}], "translate": [{"time": 2, "x": 0, "y": 0}], "scale": [{"time": 2, "x": 1, "y": 1}]}, "ngoc_on4": {"rotate": [{"time": 2, "angle": 0}], "translate": [{"time": 2, "x": 0, "y": 0}], "scale": [{"time": 2, "x": 1, "y": 1}]}, "ngoc_on5": {"rotate": [{"time": 2, "angle": 0}], "translate": [{"time": 2, "x": 0, "y": 0}], "scale": [{"time": 2, "x": 1, "y": 1}]}, "ngoc_on7": {"rotate": [{"time": 2, "angle": 0}], "translate": [{"time": 2, "x": 0, "y": 0}], "scale": [{"time": 2, "x": 1, "y": 1}]}}}, "x10": {"slots": {"X1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X5": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "X10": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on5": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}]}, "ngoc_on6": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "X10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 1, "x": 1.257, "y": 1.257}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.257, "y": 1.257}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}, "ngoc_on7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]