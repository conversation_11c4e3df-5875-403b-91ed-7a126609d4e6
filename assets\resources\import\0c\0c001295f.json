[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "2e26nMhTZGwa57Oq3/bQJF", "f2V4IqURNNz4QWqzqivR1+", "a9VpD0DP5LJYQPXITZq+uj", "11QyUZyEZN+b6JxyhKfcHa", "bcbgFB+epIlKdGw1JroyQo", "8dcmb4bohHTryWcVOceDpw", "a1X2aVDMpKaKg/9LQbwoQI", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "_N$target", "_N$content", "data", "_N$file", "_N$normalSprite", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint"], 1, 4, 9, 1, 5, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 6, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint"], 2, 12, 9, 4, 5, 5], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["b692alQpqVKjby8UD5ybi3x", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "node", "_materials", "_N$file"], -1, 1, 3, 6]], [[3, 0, 1, 2, 2], [0, 0, 4, 3, 2, 5, 2], [4, 1, 7, 1], [1, 0, 3, 4, 5, 2], [5, 0, 2], [0, 0, 7, 3, 2, 2], [0, 0, 4, 2, 5, 8, 2], [0, 0, 4, 7, 3, 2, 5, 6, 2], [0, 0, 1, 4, 3, 2, 5, 6, 3], [0, 0, 4, 7, 3, 2, 5, 8, 6, 2], [0, 0, 4, 3, 2, 5, 8, 2], [0, 0, 4, 3, 2, 5, 6, 2], [6, 0, 1, 2, 3, 4, 5, 2], [7, 0, 1, 2, 1], [8, 0, 1], [3, 1, 2, 1], [9, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 6, 2], [10, 0, 1, 2, 3], [1, 2, 0, 1, 3, 4, 5, 4], [1, 0, 1, 3, 4, 5, 3], [11, 0, 1, 2, 3, 4, 5, 6, 6], [12, 0, 1, 2, 3, 4, 5, 6, 5]], [[[{"name": "bg_content2", "rect": [0, 0, 1064, 644], "offset": [0, 0], "originalSize": [1064, 644], "capInsets": [40, 40, 40, 40]}], [2], 0, [0], [2], [2]], [[{"name": "HuongDan_poker", "rect": [0, 0, 922, 662], "offset": [0, 0], "originalSize": [922, 662], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [3]], [[[4, "pkHelpView"], [5, "pkHelpView", [-4, -5, -6, -7, -8], [[13, -2, [13, 14], 12], [14, -3]], [15, -1, 0]], [12, "view", [[-10, [6, "content", -11, [0, "22qcWMBZpMY7+oCV5hSrYM", 1, 0], [5, 1000, 510], [0, 0.5, 1]]], 1, 4], [[16, 0, -9, [8]]], [0, "5aq9wPJW1AGr5PtrA44zmY", 1, 0], [5, 1000, 510], [0, 0.5, 1]], [7, "btnClose", 1, [-14], [[17, 3, -13, [[18, "b692alQpqVKjby8UD5ybi3x", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -12, 11]], [0, "c3tVBFu1tF/pCk5QLxariV", 1, 0], [5, 80, 80], [523.4, 297.1, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "black", 100, 1, [[3, 0, -15, [0], 1], [2, -16, [4, 4292269782]]], [0, "aeRkUFjptGSKbQ3LrJg5Eh", 1, 0], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[19, 1, 0, false, -17, [2], 3], [2, -18, [4, 4292269782]]], [0, "c3yv+nfcdDSIqfzq6jRTBc", 1, 0], [5, 1084, 618]], [9, "scrollview", 1, [2], [[21, false, 0.75, 0.23, null, null, -20, -19]], [0, "78NAyXBV9DqZ1HNGnXu8MM", 1, 0], [5, 1000, 510], [0, 0.5, 1], [0, 225, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "hd_pic", 2, [[3, 0, -21, [6], 7]], [0, "24BFf7K11IxpJLQeIh4FoR", 1, 0], [5, 890, 640], [0, 0.5, 1]], [11, "title_BXH", 1, [[22, "Hướng Dẫn", 25, 0, false, -22, [4], 5]], [0, "fb5/KZgzpFAo+ghnQgfTfY", 1, 0], [5, 226.56, 0], [0, 323.20000000000005, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 3, [[20, 2, false, -23, [9], 10]], [0, "8eCOCpKOpE9qW+z8/N3kfM", 1, 0], [5, 70, 70]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 8, 0, -4, 6, 0, -5, 3, 0, 0, 2, 0, -1, 7, 0, 3, 2, 0, 5, 3, 0, 0, 3, 0, -1, 9, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 6, 7, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 7, 1, 2, 3, 6, 23], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 8, -1, 1, -1, -1, 1, 9, 10, -1, -2], [0, 4, 0, 5, 0, 6, 0, 7, 0, 0, 8, 9, 1, 1, 10]]]]