[1, ["6dlhsceg1GQ5dRpyP4EwQs", "98ljXDFqpApKs1UTrxjB6X"], ["spriteFrame", "_textureSetter"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[[0, "HemiHeadNumber", 32, {"commonHeight": 42, "fontSize": 32, "atlasName": "HemiHeadNumber.png", "fontDefDictionary": {"32": {"xOffset": 0, "yOffset": 0, "xAdvance": 9, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "35": {"xOffset": 2, "yOffset": 13, "xAdvance": 17, "rect": {"x": 102, "y": 52, "width": 18, "height": 22}}, "42": {"xOffset": 4, "yOffset": 12, "xAdvance": 17, "rect": {"x": 32, "y": 76, "width": 16, "height": 16}}, "43": {"xOffset": 4, "yOffset": 15, "xAdvance": 20, "rect": {"x": 2, "y": 76, "width": 18, "height": 17}}, "44": {"xOffset": 0, "yOffset": 30, "xAdvance": 8, "rect": {"x": 50, "y": 76, "width": 8, "height": 8}}, "45": {"xOffset": 3, "yOffset": 23, "xAdvance": 12, "rect": {"x": 68, "y": 76, "width": 11, "height": 3}}, "46": {"xOffset": 2, "yOffset": 30, "xAdvance": 8, "rect": {"x": 60, "y": 76, "width": 6, "height": 5}}, "47": {"xOffset": 1, "yOffset": 12, "xAdvance": 13, "rect": {"x": 11, "y": 2, "width": 17, "height": 24}}, "48": {"xOffset": 2, "yOffset": 13, "xAdvance": 21, "rect": {"x": 30, "y": 2, "width": 21, "height": 22}}, "49": {"xOffset": 4, "yOffset": 13, "xAdvance": 10, "rect": {"x": 47, "y": 28, "width": 10, "height": 22}}, "50": {"xOffset": 2, "yOffset": 13, "xAdvance": 21, "rect": {"x": 23, "y": 28, "width": 22, "height": 22}}, "51": {"xOffset": 2, "yOffset": 13, "xAdvance": 20, "rect": {"x": 59, "y": 28, "width": 21, "height": 22}}, "52": {"xOffset": 2, "yOffset": 13, "xAdvance": 20, "rect": {"x": 2, "y": 28, "width": 19, "height": 22}}, "53": {"xOffset": 2, "yOffset": 13, "xAdvance": 21, "rect": {"x": 97, "y": 2, "width": 21, "height": 22}}, "54": {"xOffset": 2, "yOffset": 13, "xAdvance": 21, "rect": {"x": 74, "y": 2, "width": 21, "height": 22}}, "55": {"xOffset": 3, "yOffset": 13, "xAdvance": 18, "rect": {"x": 53, "y": 2, "width": 19, "height": 22}}, "56": {"xOffset": 2, "yOffset": 13, "xAdvance": 21, "rect": {"x": 79, "y": 52, "width": 21, "height": 22}}, "57": {"xOffset": 2, "yOffset": 13, "xAdvance": 21, "rect": {"x": 32, "y": 52, "width": 21, "height": 22}}, "58": {"xOffset": 2, "yOffset": 19, "xAdvance": 8, "rect": {"x": 22, "y": 76, "width": 8, "height": 17}}, "66": {"xOffset": 2, "yOffset": 13, "xAdvance": 21, "rect": {"x": 55, "y": 52, "width": 22, "height": 22}}, "75": {"xOffset": 2, "yOffset": 13, "xAdvance": 23, "rect": {"x": 82, "y": 28, "width": 24, "height": 22}}, "77": {"xOffset": 2, "yOffset": 13, "xAdvance": 26, "rect": {"x": 2, "y": 52, "width": 28, "height": 22}}, "92": {"xOffset": 6, "yOffset": 12, "xAdvance": 13, "rect": {"x": 2, "y": 2, "width": 7, "height": 24}}}, "kerningDict": {}}]], 0, 0, [0], [0], [0]], [[{"name": "HemiHeadNumber", "rect": [2, 2, 118, 91], "offset": [-3, 16.5], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]]]]