[1, ["05VYpUo1FJrKn6skWueadG", "40sa1lCoVGFpOWBmcMPays"], ["_textureSetter", "spriteFrame"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[{"name": "font_header_popup", "rect": [0, 0, 510, 257], "offset": [-1, 127.5], "originalSize": [512, 512], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "font_header_popup", 32, {"commonHeight": 37, "fontSize": 32, "atlasName": "font_header_popup.png", "fontDefDictionary": {"0": {"xOffset": 3, "yOffset": 7, "xAdvance": 24, "rect": {"x": 260, "y": 235, "width": 18, "height": 23}}, "10": {"xOffset": -1, "yOffset": 0, "xAdvance": 0, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": -1, "yOffset": 0, "xAdvance": 9, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 2, "yOffset": 5, "xAdvance": 10, "rect": {"x": 493, "y": 131, "width": 6, "height": 25}}, "34": {"xOffset": 1, "yOffset": 5, "xAdvance": 15, "rect": {"x": 202, "y": 235, "width": 14, "height": 10}}, "35": {"xOffset": -1, "yOffset": 5, "xAdvance": 18, "rect": {"x": 119, "y": 159, "width": 20, "height": 25}}, "36": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 411, "y": 100, "width": 17, "height": 30}}, "37": {"xOffset": 0, "yOffset": 5, "xAdvance": 26, "rect": {"x": 145, "y": 131, "width": 25, "height": 26}}, "38": {"xOffset": 0, "yOffset": 5, "xAdvance": 23, "rect": {"x": 139, "y": 159, "width": 23, "height": 25}}, "39": {"xOffset": 0, "yOffset": 5, "xAdvance": 8, "rect": {"x": 216, "y": 235, "width": 6, "height": 10}}, "40": {"xOffset": 1, "yOffset": 5, "xAdvance": 11, "rect": {"x": 278, "y": 68, "width": 10, "height": 31}}, "41": {"xOffset": 0, "yOffset": 5, "xAdvance": 11, "rect": {"x": 288, "y": 68, "width": 10, "height": 31}}, "42": {"xOffset": -1, "yOffset": 5, "xAdvance": 12, "rect": {"x": 183, "y": 235, "width": 13, "height": 12}}, "43": {"xOffset": 0, "yOffset": 9, "xAdvance": 19, "rect": {"x": 129, "y": 235, "width": 18, "height": 18}}, "44": {"xOffset": 1, "yOffset": 24, "xAdvance": 9, "rect": {"x": 196, "y": 235, "width": 6, "height": 11}}, "45": {"xOffset": 1, "yOffset": 18, "xAdvance": 11, "rect": {"x": 222, "y": 235, "width": 11, "height": 6}}, "46": {"xOffset": 1, "yOffset": 24, "xAdvance": 9, "rect": {"x": 233, "y": 235, "width": 6, "height": 6}}, "47": {"xOffset": -2, "yOffset": 5, "xAdvance": 9, "rect": {"x": 499, "y": 131, "width": 12, "height": 25}}, "48": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 162, "y": 159, "width": 17, "height": 25}}, "49": {"xOffset": 2, "yOffset": 5, "xAdvance": 18, "rect": {"x": 179, "y": 159, "width": 12, "height": 25}}, "50": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 191, "y": 159, "width": 17, "height": 25}}, "51": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 208, "y": 159, "width": 17, "height": 25}}, "52": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 225, "y": 159, "width": 18, "height": 25}}, "53": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 243, "y": 159, "width": 18, "height": 25}}, "54": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 261, "y": 159, "width": 17, "height": 25}}, "55": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 278, "y": 159, "width": 17, "height": 25}}, "56": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 295, "y": 159, "width": 17, "height": 25}}, "57": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 312, "y": 159, "width": 17, "height": 25}}, "58": {"xOffset": 2, "yOffset": 11, "xAdvance": 10, "rect": {"x": 338, "y": 210, "width": 6, "height": 19}}, "59": {"xOffset": 2, "yOffset": 11, "xAdvance": 10, "rect": {"x": 502, "y": 185, "width": 6, "height": 24}}, "61": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 165, "y": 235, "width": 18, "height": 13}}, "63": {"xOffset": 1, "yOffset": 5, "xAdvance": 20, "rect": {"x": 100, "y": 159, "width": 19, "height": 25}}, "64": {"xOffset": 0, "yOffset": 5, "xAdvance": 31, "rect": {"x": 212, "y": 0, "width": 32, "height": 32}}, "65": {"xOffset": -1, "yOffset": 5, "xAdvance": 23, "rect": {"x": 329, "y": 159, "width": 25, "height": 25}}, "66": {"xOffset": 1, "yOffset": 5, "xAdvance": 22, "rect": {"x": 354, "y": 159, "width": 21, "height": 25}}, "67": {"xOffset": 0, "yOffset": 5, "xAdvance": 23, "rect": {"x": 375, "y": 159, "width": 22, "height": 25}}, "68": {"xOffset": 1, "yOffset": 5, "xAdvance": 23, "rect": {"x": 397, "y": 159, "width": 22, "height": 25}}, "69": {"xOffset": 1, "yOffset": 5, "xAdvance": 21, "rect": {"x": 419, "y": 159, "width": 19, "height": 25}}, "70": {"xOffset": 1, "yOffset": 5, "xAdvance": 20, "rect": {"x": 438, "y": 159, "width": 18, "height": 25}}, "71": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 456, "y": 159, "width": 24, "height": 25}}, "72": {"xOffset": 1, "yOffset": 5, "xAdvance": 22, "rect": {"x": 480, "y": 159, "width": 20, "height": 25}}, "73": {"xOffset": 1, "yOffset": 5, "xAdvance": 8, "rect": {"x": 500, "y": 159, "width": 6, "height": 25}}, "74": {"xOffset": 0, "yOffset": 5, "xAdvance": 17, "rect": {"x": 0, "y": 185, "width": 16, "height": 25}}, "75": {"xOffset": 1, "yOffset": 5, "xAdvance": 23, "rect": {"x": 16, "y": 185, "width": 23, "height": 25}}, "76": {"xOffset": 1, "yOffset": 5, "xAdvance": 20, "rect": {"x": 39, "y": 185, "width": 19, "height": 25}}, "77": {"xOffset": 1, "yOffset": 5, "xAdvance": 27, "rect": {"x": 58, "y": 185, "width": 25, "height": 25}}, "78": {"xOffset": 1, "yOffset": 5, "xAdvance": 22, "rect": {"x": 83, "y": 185, "width": 20, "height": 25}}, "79": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 103, "y": 185, "width": 25, "height": 25}}, "80": {"xOffset": 1, "yOffset": 5, "xAdvance": 20, "rect": {"x": 128, "y": 185, "width": 19, "height": 25}}, "81": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 120, "y": 131, "width": 25, "height": 27}}, "82": {"xOffset": 1, "yOffset": 5, "xAdvance": 22, "rect": {"x": 147, "y": 185, "width": 22, "height": 25}}, "83": {"xOffset": 0, "yOffset": 5, "xAdvance": 21, "rect": {"x": 169, "y": 185, "width": 20, "height": 25}}, "84": {"xOffset": 0, "yOffset": 5, "xAdvance": 20, "rect": {"x": 189, "y": 185, "width": 20, "height": 25}}, "85": {"xOffset": 1, "yOffset": 5, "xAdvance": 22, "rect": {"x": 209, "y": 185, "width": 20, "height": 25}}, "86": {"xOffset": -1, "yOffset": 5, "xAdvance": 21, "rect": {"x": 229, "y": 185, "width": 23, "height": 25}}, "87": {"xOffset": -1, "yOffset": 5, "xAdvance": 31, "rect": {"x": 252, "y": 185, "width": 33, "height": 25}}, "88": {"xOffset": -1, "yOffset": 5, "xAdvance": 21, "rect": {"x": 285, "y": 185, "width": 23, "height": 25}}, "89": {"xOffset": -2, "yOffset": 5, "xAdvance": 20, "rect": {"x": 308, "y": 185, "width": 25, "height": 25}}, "90": {"xOffset": -1, "yOffset": 5, "xAdvance": 20, "rect": {"x": 333, "y": 185, "width": 21, "height": 25}}, "91": {"xOffset": 1, "yOffset": 5, "xAdvance": 11, "rect": {"x": 322, "y": 68, "width": 10, "height": 31}}, "93": {"xOffset": 0, "yOffset": 5, "xAdvance": 11, "rect": {"x": 332, "y": 68, "width": 10, "height": 31}}, "94": {"xOffset": 1, "yOffset": 5, "xAdvance": 19, "rect": {"x": 147, "y": 235, "width": 18, "height": 14}}, "95": {"xOffset": -2, "yOffset": 31, "xAdvance": 18, "rect": {"x": 239, "y": 235, "width": 21, "height": 5}}, "97": {"xOffset": 0, "yOffset": 11, "xAdvance": 18, "rect": {"x": 344, "y": 210, "width": 18, "height": 19}}, "98": {"xOffset": 1, "yOffset": 5, "xAdvance": 19, "rect": {"x": 354, "y": 185, "width": 18, "height": 25}}, "99": {"xOffset": 0, "yOffset": 11, "xAdvance": 18, "rect": {"x": 362, "y": 210, "width": 18, "height": 19}}, "100": {"xOffset": 0, "yOffset": 5, "xAdvance": 19, "rect": {"x": 372, "y": 185, "width": 18, "height": 25}}, "101": {"xOffset": 0, "yOffset": 11, "xAdvance": 18, "rect": {"x": 380, "y": 210, "width": 19, "height": 19}}, "102": {"xOffset": 0, "yOffset": 5, "xAdvance": 11, "rect": {"x": 390, "y": 185, "width": 12, "height": 25}}, "103": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 402, "y": 185, "width": 18, "height": 25}}, "104": {"xOffset": 1, "yOffset": 5, "xAdvance": 19, "rect": {"x": 420, "y": 185, "width": 17, "height": 25}}, "105": {"xOffset": 2, "yOffset": 5, "xAdvance": 10, "rect": {"x": 437, "y": 185, "width": 6, "height": 25}}, "106": {"xOffset": -2, "yOffset": 5, "xAdvance": 10, "rect": {"x": 342, "y": 68, "width": 11, "height": 31}}, "107": {"xOffset": 2, "yOffset": 5, "xAdvance": 18, "rect": {"x": 443, "y": 185, "width": 17, "height": 25}}, "108": {"xOffset": 2, "yOffset": 5, "xAdvance": 10, "rect": {"x": 460, "y": 185, "width": 6, "height": 25}}, "109": {"xOffset": 1, "yOffset": 11, "xAdvance": 28, "rect": {"x": 399, "y": 210, "width": 26, "height": 19}}, "110": {"xOffset": 1, "yOffset": 11, "xAdvance": 19, "rect": {"x": 425, "y": 210, "width": 17, "height": 19}}, "111": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 442, "y": 210, "width": 19, "height": 19}}, "112": {"xOffset": 1, "yOffset": 11, "xAdvance": 19, "rect": {"x": 466, "y": 185, "width": 18, "height": 25}}, "113": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 484, "y": 185, "width": 18, "height": 25}}, "114": {"xOffset": 2, "yOffset": 11, "xAdvance": 13, "rect": {"x": 461, "y": 210, "width": 12, "height": 19}}, "115": {"xOffset": 0, "yOffset": 11, "xAdvance": 18, "rect": {"x": 473, "y": 210, "width": 17, "height": 19}}, "116": {"xOffset": 0, "yOffset": 5, "xAdvance": 11, "rect": {"x": 0, "y": 210, "width": 11, "height": 25}}, "117": {"xOffset": 1, "yOffset": 11, "xAdvance": 19, "rect": {"x": 490, "y": 210, "width": 17, "height": 19}}, "118": {"xOffset": -1, "yOffset": 11, "xAdvance": 17, "rect": {"x": 0, "y": 235, "width": 19, "height": 19}}, "119": {"xOffset": -1, "yOffset": 11, "xAdvance": 25, "rect": {"x": 19, "y": 235, "width": 27, "height": 19}}, "120": {"xOffset": -1, "yOffset": 11, "xAdvance": 18, "rect": {"x": 46, "y": 235, "width": 20, "height": 19}}, "121": {"xOffset": -1, "yOffset": 11, "xAdvance": 17, "rect": {"x": 11, "y": 210, "width": 19, "height": 25}}, "122": {"xOffset": 0, "yOffset": 11, "xAdvance": 16, "rect": {"x": 66, "y": 235, "width": 16, "height": 19}}, "123": {"xOffset": 0, "yOffset": 5, "xAdvance": 12, "rect": {"x": 298, "y": 68, "width": 12, "height": 31}}, "124": {"xOffset": 2, "yOffset": 5, "xAdvance": 9, "rect": {"x": 207, "y": 0, "width": 5, "height": 32}}, "125": {"xOffset": 0, "yOffset": 5, "xAdvance": 12, "rect": {"x": 310, "y": 68, "width": 12, "height": 31}}, "192": {"xOffset": -1, "yOffset": -2, "xAdvance": 23, "rect": {"x": 294, "y": 0, "width": 25, "height": 32}}, "193": {"xOffset": -1, "yOffset": -2, "xAdvance": 23, "rect": {"x": 269, "y": 0, "width": 25, "height": 32}}, "194": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 378, "y": 68, "width": 25, "height": 31}}, "200": {"xOffset": 1, "yOffset": -2, "xAdvance": 21, "rect": {"x": 319, "y": 0, "width": 19, "height": 32}}, "201": {"xOffset": 1, "yOffset": -2, "xAdvance": 21, "rect": {"x": 338, "y": 0, "width": 19, "height": 32}}, "202": {"xOffset": 1, "yOffset": -1, "xAdvance": 21, "rect": {"x": 403, "y": 68, "width": 19, "height": 31}}, "204": {"xOffset": -1, "yOffset": -2, "xAdvance": 8, "rect": {"x": 357, "y": 0, "width": 9, "height": 32}}, "205": {"xOffset": 0, "yOffset": -2, "xAdvance": 8, "rect": {"x": 366, "y": 0, "width": 9, "height": 32}}, "210": {"xOffset": 0, "yOffset": -2, "xAdvance": 24, "rect": {"x": 375, "y": 0, "width": 25, "height": 32}}, "211": {"xOffset": 0, "yOffset": -2, "xAdvance": 24, "rect": {"x": 244, "y": 0, "width": 25, "height": 32}}, "212": {"xOffset": 0, "yOffset": -1, "xAdvance": 24, "rect": {"x": 422, "y": 68, "width": 25, "height": 31}}, "213": {"xOffset": 0, "yOffset": -1, "xAdvance": 24, "rect": {"x": 447, "y": 68, "width": 25, "height": 31}}, "217": {"xOffset": 1, "yOffset": -2, "xAdvance": 22, "rect": {"x": 400, "y": 0, "width": 20, "height": 32}}, "218": {"xOffset": 1, "yOffset": -2, "xAdvance": 22, "rect": {"x": 420, "y": 0, "width": 20, "height": 32}}, "221": {"xOffset": -2, "yOffset": -2, "xAdvance": 20, "rect": {"x": 440, "y": 0, "width": 25, "height": 32}}, "224": {"xOffset": 0, "yOffset": 4, "xAdvance": 18, "rect": {"x": 207, "y": 131, "width": 18, "height": 26}}, "225": {"xOffset": 0, "yOffset": 4, "xAdvance": 18, "rect": {"x": 170, "y": 131, "width": 18, "height": 26}}, "226": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 48, "y": 210, "width": 18, "height": 25}}, "227": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 30, "y": 210, "width": 18, "height": 25}}, "232": {"xOffset": 0, "yOffset": 4, "xAdvance": 18, "rect": {"x": 225, "y": 131, "width": 19, "height": 26}}, "233": {"xOffset": 0, "yOffset": 4, "xAdvance": 18, "rect": {"x": 244, "y": 131, "width": 19, "height": 26}}, "234": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 66, "y": 210, "width": 19, "height": 25}}, "236": {"xOffset": 0, "yOffset": 4, "xAdvance": 10, "rect": {"x": 502, "y": 100, "width": 9, "height": 26}}, "237": {"xOffset": 1, "yOffset": 4, "xAdvance": 10, "rect": {"x": 263, "y": 131, "width": 9, "height": 26}}, "242": {"xOffset": 0, "yOffset": 4, "xAdvance": 19, "rect": {"x": 272, "y": 131, "width": 19, "height": 26}}, "243": {"xOffset": 0, "yOffset": 4, "xAdvance": 19, "rect": {"x": 188, "y": 131, "width": 19, "height": 26}}, "244": {"xOffset": 0, "yOffset": 5, "xAdvance": 19, "rect": {"x": 85, "y": 210, "width": 19, "height": 25}}, "245": {"xOffset": 0, "yOffset": 5, "xAdvance": 19, "rect": {"x": 104, "y": 210, "width": 19, "height": 25}}, "249": {"xOffset": 1, "yOffset": 4, "xAdvance": 19, "rect": {"x": 291, "y": 131, "width": 17, "height": 26}}, "250": {"xOffset": 1, "yOffset": 4, "xAdvance": 19, "rect": {"x": 308, "y": 131, "width": 17, "height": 26}}, "253": {"xOffset": -1, "yOffset": 4, "xAdvance": 17, "rect": {"x": 465, "y": 0, "width": 19, "height": 32}}, "258": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 472, "y": 68, "width": 25, "height": 31}}, "259": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 123, "y": 210, "width": 18, "height": 25}}, "272": {"xOffset": -1, "yOffset": 5, "xAdvance": 23, "rect": {"x": 141, "y": 210, "width": 24, "height": 25}}, "273": {"xOffset": 0, "yOffset": 5, "xAdvance": 20, "rect": {"x": 165, "y": 210, "width": 20, "height": 25}}, "296": {"xOffset": -3, "yOffset": -1, "xAdvance": 8, "rect": {"x": 0, "y": 100, "width": 16, "height": 31}}, "297": {"xOffset": -2, "yOffset": 5, "xAdvance": 10, "rect": {"x": 185, "y": 210, "width": 15, "height": 25}}, "360": {"xOffset": 1, "yOffset": -1, "xAdvance": 22, "rect": {"x": 16, "y": 100, "width": 20, "height": 31}}, "361": {"xOffset": 1, "yOffset": 5, "xAdvance": 19, "rect": {"x": 200, "y": 210, "width": 17, "height": 25}}, "416": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 217, "y": 210, "width": 28, "height": 25}}, "417": {"xOffset": 0, "yOffset": 11, "xAdvance": 23, "rect": {"x": 82, "y": 235, "width": 24, "height": 19}}, "431": {"xOffset": 1, "yOffset": 5, "xAdvance": 27, "rect": {"x": 245, "y": 210, "width": 27, "height": 25}}, "432": {"xOffset": 1, "yOffset": 11, "xAdvance": 23, "rect": {"x": 106, "y": 235, "width": 23, "height": 19}}, "7840": {"xOffset": -1, "yOffset": 5, "xAdvance": 23, "rect": {"x": 484, "y": 0, "width": 25, "height": 32}}, "7841": {"xOffset": 0, "yOffset": 11, "xAdvance": 18, "rect": {"x": 325, "y": 131, "width": 18, "height": 26}}, "7842": {"xOffset": -1, "yOffset": -2, "xAdvance": 23, "rect": {"x": 0, "y": 36, "width": 25, "height": 32}}, "7843": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 0, "y": 131, "width": 18, "height": 28}}, "7844": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 36, "y": 100, "width": 25, "height": 31}}, "7845": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 25, "y": 36, "width": 18, "height": 32}}, "7846": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 61, "y": 100, "width": 25, "height": 31}}, "7847": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 43, "y": 36, "width": 18, "height": 32}}, "7848": {"xOffset": -1, "yOffset": -3, "xAdvance": 23, "rect": {"x": 113, "y": 0, "width": 25, "height": 33}}, "7849": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 61, "y": 36, "width": 18, "height": 32}}, "7850": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 86, "y": 100, "width": 25, "height": 31}}, "7851": {"xOffset": 0, "yOffset": 0, "xAdvance": 18, "rect": {"x": 428, "y": 100, "width": 18, "height": 30}}, "7852": {"xOffset": -1, "yOffset": 1, "xAdvance": 23, "rect": {"x": 0, "y": 0, "width": 25, "height": 36}}, "7853": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 79, "y": 36, "width": 18, "height": 32}}, "7854": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 353, "y": 68, "width": 25, "height": 31}}, "7855": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 97, "y": 36, "width": 18, "height": 32}}, "7856": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 136, "y": 100, "width": 25, "height": 31}}, "7857": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 115, "y": 36, "width": 18, "height": 32}}, "7858": {"xOffset": -1, "yOffset": -3, "xAdvance": 23, "rect": {"x": 138, "y": 0, "width": 25, "height": 33}}, "7859": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 133, "y": 36, "width": 18, "height": 32}}, "7860": {"xOffset": -1, "yOffset": -1, "xAdvance": 23, "rect": {"x": 111, "y": 100, "width": 25, "height": 31}}, "7861": {"xOffset": 0, "yOffset": 0, "xAdvance": 18, "rect": {"x": 446, "y": 100, "width": 18, "height": 30}}, "7862": {"xOffset": -1, "yOffset": 1, "xAdvance": 23, "rect": {"x": 25, "y": 0, "width": 25, "height": 36}}, "7863": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 151, "y": 36, "width": 18, "height": 32}}, "7864": {"xOffset": 1, "yOffset": 5, "xAdvance": 21, "rect": {"x": 169, "y": 36, "width": 19, "height": 32}}, "7865": {"xOffset": 0, "yOffset": 11, "xAdvance": 18, "rect": {"x": 343, "y": 131, "width": 19, "height": 26}}, "7866": {"xOffset": 1, "yOffset": -2, "xAdvance": 21, "rect": {"x": 188, "y": 36, "width": 19, "height": 32}}, "7867": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 18, "y": 131, "width": 19, "height": 28}}, "7868": {"xOffset": 1, "yOffset": -1, "xAdvance": 21, "rect": {"x": 161, "y": 100, "width": 19, "height": 31}}, "7869": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 272, "y": 210, "width": 19, "height": 25}}, "7870": {"xOffset": 1, "yOffset": -1, "xAdvance": 21, "rect": {"x": 180, "y": 100, "width": 19, "height": 31}}, "7871": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 207, "y": 36, "width": 19, "height": 32}}, "7872": {"xOffset": 1, "yOffset": -1, "xAdvance": 21, "rect": {"x": 199, "y": 100, "width": 19, "height": 31}}, "7873": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 226, "y": 36, "width": 19, "height": 32}}, "7874": {"xOffset": 1, "yOffset": -3, "xAdvance": 21, "rect": {"x": 163, "y": 0, "width": 19, "height": 33}}, "7875": {"xOffset": 0, "yOffset": -2, "xAdvance": 18, "rect": {"x": 245, "y": 36, "width": 19, "height": 32}}, "7876": {"xOffset": 1, "yOffset": -1, "xAdvance": 21, "rect": {"x": 218, "y": 100, "width": 19, "height": 31}}, "7877": {"xOffset": 0, "yOffset": 0, "xAdvance": 18, "rect": {"x": 464, "y": 100, "width": 19, "height": 30}}, "7878": {"xOffset": 1, "yOffset": 1, "xAdvance": 21, "rect": {"x": 50, "y": 0, "width": 19, "height": 36}}, "7879": {"xOffset": 0, "yOffset": 5, "xAdvance": 18, "rect": {"x": 264, "y": 36, "width": 19, "height": 32}}, "7880": {"xOffset": -1, "yOffset": -2, "xAdvance": 8, "rect": {"x": 283, "y": 36, "width": 10, "height": 32}}, "7881": {"xOffset": 0, "yOffset": 2, "xAdvance": 10, "rect": {"x": 497, "y": 68, "width": 10, "height": 28}}, "7882": {"xOffset": 1, "yOffset": 5, "xAdvance": 8, "rect": {"x": 293, "y": 36, "width": 6, "height": 32}}, "7883": {"xOffset": 2, "yOffset": 5, "xAdvance": 10, "rect": {"x": 299, "y": 36, "width": 6, "height": 32}}, "7884": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 305, "y": 36, "width": 25, "height": 32}}, "7885": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 362, "y": 131, "width": 19, "height": 26}}, "7886": {"xOffset": 0, "yOffset": -2, "xAdvance": 24, "rect": {"x": 330, "y": 36, "width": 25, "height": 32}}, "7887": {"xOffset": 0, "yOffset": 2, "xAdvance": 19, "rect": {"x": 37, "y": 131, "width": 19, "height": 28}}, "7888": {"xOffset": 0, "yOffset": -1, "xAdvance": 24, "rect": {"x": 237, "y": 100, "width": 25, "height": 31}}, "7889": {"xOffset": 0, "yOffset": -2, "xAdvance": 19, "rect": {"x": 355, "y": 36, "width": 19, "height": 32}}, "7890": {"xOffset": 0, "yOffset": -1, "xAdvance": 24, "rect": {"x": 262, "y": 100, "width": 25, "height": 31}}, "7891": {"xOffset": 0, "yOffset": -2, "xAdvance": 19, "rect": {"x": 374, "y": 36, "width": 19, "height": 32}}, "7892": {"xOffset": 0, "yOffset": -3, "xAdvance": 24, "rect": {"x": 182, "y": 0, "width": 25, "height": 33}}, "7893": {"xOffset": 0, "yOffset": -2, "xAdvance": 19, "rect": {"x": 393, "y": 36, "width": 19, "height": 32}}, "7894": {"xOffset": 0, "yOffset": -1, "xAdvance": 24, "rect": {"x": 287, "y": 100, "width": 25, "height": 31}}, "7895": {"xOffset": 0, "yOffset": 0, "xAdvance": 19, "rect": {"x": 483, "y": 100, "width": 19, "height": 30}}, "7896": {"xOffset": 0, "yOffset": 1, "xAdvance": 24, "rect": {"x": 69, "y": 0, "width": 25, "height": 36}}, "7897": {"xOffset": 0, "yOffset": 5, "xAdvance": 19, "rect": {"x": 412, "y": 36, "width": 19, "height": 32}}, "7898": {"xOffset": 0, "yOffset": -2, "xAdvance": 27, "rect": {"x": 431, "y": 36, "width": 28, "height": 32}}, "7899": {"xOffset": 0, "yOffset": 4, "xAdvance": 23, "rect": {"x": 381, "y": 131, "width": 24, "height": 26}}, "7900": {"xOffset": 0, "yOffset": -2, "xAdvance": 27, "rect": {"x": 459, "y": 36, "width": 28, "height": 32}}, "7901": {"xOffset": 0, "yOffset": 4, "xAdvance": 23, "rect": {"x": 405, "y": 131, "width": 24, "height": 26}}, "7902": {"xOffset": 0, "yOffset": -2, "xAdvance": 27, "rect": {"x": 0, "y": 68, "width": 28, "height": 32}}, "7903": {"xOffset": 0, "yOffset": 2, "xAdvance": 23, "rect": {"x": 56, "y": 131, "width": 24, "height": 28}}, "7904": {"xOffset": 0, "yOffset": -1, "xAdvance": 27, "rect": {"x": 312, "y": 100, "width": 28, "height": 31}}, "7905": {"xOffset": 0, "yOffset": 5, "xAdvance": 23, "rect": {"x": 291, "y": 210, "width": 24, "height": 25}}, "7906": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 28, "y": 68, "width": 28, "height": 32}}, "7907": {"xOffset": 0, "yOffset": 11, "xAdvance": 23, "rect": {"x": 429, "y": 131, "width": 24, "height": 26}}, "7908": {"xOffset": 1, "yOffset": 5, "xAdvance": 22, "rect": {"x": 487, "y": 36, "width": 20, "height": 32}}, "7909": {"xOffset": 1, "yOffset": 11, "xAdvance": 19, "rect": {"x": 453, "y": 131, "width": 17, "height": 26}}, "7910": {"xOffset": 1, "yOffset": -2, "xAdvance": 22, "rect": {"x": 56, "y": 68, "width": 20, "height": 32}}, "7911": {"xOffset": 1, "yOffset": 2, "xAdvance": 19, "rect": {"x": 80, "y": 131, "width": 17, "height": 28}}, "7912": {"xOffset": 1, "yOffset": -2, "xAdvance": 27, "rect": {"x": 76, "y": 68, "width": 27, "height": 32}}, "7913": {"xOffset": 1, "yOffset": 4, "xAdvance": 23, "rect": {"x": 470, "y": 131, "width": 23, "height": 26}}, "7914": {"xOffset": 1, "yOffset": -2, "xAdvance": 27, "rect": {"x": 103, "y": 68, "width": 27, "height": 32}}, "7915": {"xOffset": 1, "yOffset": 4, "xAdvance": 23, "rect": {"x": 0, "y": 159, "width": 23, "height": 26}}, "7916": {"xOffset": 1, "yOffset": -2, "xAdvance": 27, "rect": {"x": 130, "y": 68, "width": 27, "height": 32}}, "7917": {"xOffset": 1, "yOffset": 2, "xAdvance": 23, "rect": {"x": 97, "y": 131, "width": 23, "height": 28}}, "7918": {"xOffset": 1, "yOffset": -1, "xAdvance": 27, "rect": {"x": 340, "y": 100, "width": 27, "height": 31}}, "7919": {"xOffset": 1, "yOffset": 5, "xAdvance": 23, "rect": {"x": 315, "y": 210, "width": 23, "height": 25}}, "7920": {"xOffset": 1, "yOffset": 5, "xAdvance": 27, "rect": {"x": 157, "y": 68, "width": 27, "height": 32}}, "7921": {"xOffset": 1, "yOffset": 11, "xAdvance": 23, "rect": {"x": 23, "y": 159, "width": 23, "height": 26}}, "7922": {"xOffset": -2, "yOffset": -2, "xAdvance": 20, "rect": {"x": 184, "y": 68, "width": 25, "height": 32}}, "7923": {"xOffset": -1, "yOffset": 4, "xAdvance": 17, "rect": {"x": 209, "y": 68, "width": 19, "height": 32}}, "7924": {"xOffset": -2, "yOffset": 5, "xAdvance": 20, "rect": {"x": 228, "y": 68, "width": 25, "height": 32}}, "7925": {"xOffset": -1, "yOffset": 11, "xAdvance": 17, "rect": {"x": 46, "y": 159, "width": 19, "height": 26}}, "7926": {"xOffset": -2, "yOffset": -2, "xAdvance": 20, "rect": {"x": 253, "y": 68, "width": 25, "height": 32}}, "7927": {"xOffset": -1, "yOffset": 2, "xAdvance": 17, "rect": {"x": 94, "y": 0, "width": 19, "height": 34}}, "7928": {"xOffset": -2, "yOffset": -1, "xAdvance": 20, "rect": {"x": 367, "y": 100, "width": 25, "height": 31}}, "7929": {"xOffset": -1, "yOffset": 5, "xAdvance": 17, "rect": {"x": 392, "y": 100, "width": 19, "height": 31}}, "8470": {"xOffset": 1, "yOffset": 5, "xAdvance": 36, "rect": {"x": 65, "y": 159, "width": 35, "height": 25}}}, "kerningDict": {"2097217": -1, "2097241": -1, "3211313": -2, "4259872": -1, "4259924": -2, "4259926": -2, "4259927": -2, "4259929": -3, "4259958": -1, "4259959": -1, "4259961": -1, "4587564": -4, "4587566": -4, "4587585": -2, "4980768": -1, "4980820": -2, "4980822": -2, "4980823": -2, "4980825": -3, "4980857": -1, "5242912": -1, "5242924": -4, "5242926": -4, "5242945": -2, "5374038": -1, "5374039": -1, "5374041": -1, "5505068": -4, "5505069": -2, "5505070": -4, "5505082": -4, "5505083": -4, "5505089": -2, "5505103": -1, "5505121": -2, "5505123": -2, "5505125": -2, "5505129": -1, "5505135": -2, "5505138": -2, "5505139": -2, "5505141": -2, "5505143": -2, "5505145": -2, "5636140": -3, "5636141": -2, "5636142": -3, "5636154": -2, "5636155": -2, "5636161": -2, "5636193": -2, "5636197": -2, "5636201": -1, "5636207": -2, "5636210": -2, "5636213": -1, "5636217": -1, "5701676": -2, "5701677": -1, "5701678": -2, "5701690": -1, "5701691": -1, "5701697": -2, "5701729": -1, "5701733": -1, "5701743": -1, "5701746": -1, "5701749": -1, "5701753": -1, "5832736": -1, "5832748": -4, "5832749": -2, "5832750": -4, "5832762": -2, "5832763": -2, "5832769": -3, "5832801": -2, "5832805": -2, "5832809": -1, "5832815": -2, "5832816": -2, "5832817": -2, "5832821": -2, "5832822": -2, "7471148": -2, "7471150": -2, "7733292": -2, "7733294": -2, "7798828": -1, "7798830": -1, "7929900": -2, "7929902": -2}}]], 0, 0, [0], [1], [1]]]]