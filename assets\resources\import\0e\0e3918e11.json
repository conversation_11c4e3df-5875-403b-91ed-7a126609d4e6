[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "cbjKcidM1K67GNLatRAgOV", "b2/azgn+FKA5IG0yrqWMgJ", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "b5/QWhthBHT6OEj+Fm7hCy", "0d9sTwpbxOCK+yRzVc7Wid", "602S7V2NJDy6N6TZKwg9zV", "38WQHknJJMcZggn/Jc2gLS", "0dwlpy0VRJG4hpV3/T5Pde", "96EVsPRlNEuoHOiXK0Lfso", "94JQMwRJZGFpZoV1UsU3kw", "0cqKVevVRNd5ksQ/DcxL2b", "60ZPh6qj5JZ6qABL4Eddb3", "9f9s4nH/hMu4EgRubsfyG8", "9eGKJ3s3lMnbYd6Xrn85FH", "0aWUU9ToJMiotDL3R0qou+", "760X6VxApGX4e0vPzu75zI", "cd0zs0DG9LI7+ixSf9OIjN", "51EIM2OtNOtqqFj5Z2ZnPX", "23V6fpFpZGaJPCuSInBQS5", "ffoTzR2kpC9rR81zS00zW3", "51Vr0FlCFMFIIhaJBAqEtw", "38Ka1Cv2JG+5CzzmYQQh1y", "fbWAGSBr5GUb1nkXf4VZDt", "53Hv5AGblKT4O+smSNOomj", "66EGi7r6ZA2KgBkb9FxhOA", "83esreDA5PPoUmAk6McrcF", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30", "fbYJDJs3RLy4Y6kV1W8y7C", "0bn+qvEvdNzowrB9Ht/QGr", "ebEdoAyGJEqa9aN6fqzJW9", "60hlEIw7pLWIApV4/3734w", "0b9azDkpBK37s45iz7R+tn", "1bG/FZmyVPhppEVqWYDEwn", "61dkXV+DJOi5gIk+ATJvme", "96Qj/0+ZRM9ZAlTTD7Hlqo", "94NfgoiRdEVLc0d2XFDMAN", "d3+lMSviBDoY84R9VgLmNc", "7aZNQ726pIUYZvZqWMOC+a", "55gBtjGflOuqRxKUnIoBtO", "3f3Dxqyi9GRYh4th+eAkvE", "2dnzVAO1pFoKETm4e1Wkel", "f3cxmcNHpKeZFpKKTHLu0s", "e3LpPZB7JJOYv5PYzIap7A", "f64bq9KT9KFLE+xlnchhRE", "d9GwlJp+9DCaxN+SVNtMJa", "adIez8L+JJIap579dKak0w", "07j2+KmQ5LiqxUXuvZ7is+", "a9O2PzGmlMP5ZaBV1eRrCR", "c8iwwy4+1I/4GpIl6TQFlW", "8dBzSXYCtJWJ+1VbFkiREE"], ["node", "_spriteFrame", "_N$target", "_textureSetter", "_N$file", "root", "data", "_N$normalSprite", "_defaultClip"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children"], 1, 9, 4, 1, 5, 7, 2], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 6, 5], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "_resize", "node", "_layoutSize"], -2, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["a4c84JoE69IlpundA0+AU+n", ["node", "nodeLines"], 3, 1, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.LabelOutline", ["_width", "node", "_color"], 2, 1, 5]], [[9, 0, 1, 2], [1, 0, 4, 7, 2, 3, 5, 6, 2], [3, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 4, 2, 3, 5, 6, 2], [2, 0, 1, 2, 3, 4, 3], [5, 0, 1, 2, 3, 4], [2, 2, 3, 4, 1], [1, 0, 4, 2, 3, 5, 2], [5, 0, 1, 3, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [11, 0, 1, 2, 2], [6, 0, 2], [1, 0, 7, 2, 3, 2], [1, 0, 1, 4, 2, 3, 5, 6, 3], [7, 0, 1, 2, 1], [8, 0, 1, 1], [4, 2, 0, 1, 3, 5, 6, 5], [4, 4, 0, 1, 5, 6, 4], [2, 0, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 3], [3, 2, 8, 1]], [[[[11, "tkBetLinesView"], [12, "tkBetLinesView", [-24, -25, -26, -27, -28, -29, -30], [[14, -2, [70, 71], 69], [15, -23, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22]]], [0, "e5f/aStl9CTLKP8FdTjvP9", -1]], [1, "layout-lines", 1, [-32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51], [[16, false, 3, 25, 18, -31, [5, 760, 350]]], [0, "63gc/awxtCjopSoC3UNgMP", 1], [5, 760, 350], [-5, 43, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-button", 1, [-53, -54, -55, -56], [[17, 1, 1, 55, -52, [5, 757, 60]]], [0, "2eiUJ+1OdKKJlxTlAq2I2b", 1], [5, 757, 60], [8, -216, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnEven", 3, [-60], [[6, -57, [52], 53], [2, 1.05, 3, -59, [[8, "a4c84JoE69IlpundA0+AU+n", "selectEvenClicked", 1]], [4, 4294967295], [4, 4294967295], -58]], [0, "03LZ8Aj1VPrZpeB+xFi6HB", 1], [5, 148, 65], [-304.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnOdd", 3, [-64], [[6, -61, [56], 57], [2, 1.05, 3, -63, [[8, "a4c84JoE69IlpundA0+AU+n", "selectOddClicked", 1]], [4, 4294967295], [4, 4294967295], -62]], [0, "5b5hkb/EJMjquEAcb8GDgm", 1], [5, 148, 65], [-101.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnNone", 3, [-68], [[6, -65, [60], 61], [2, 1.05, 3, -67, [[8, "a4c84JoE69IlpundA0+AU+n", "selectNoneClicked", 1]], [4, 4294967295], [4, 4294967295], -66]], [0, "4cGgY49ZRAop4bFzToh29U", 1], [5, 148, 65], [101.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnAll", 3, [-72], [[6, -69, [64], 65], [2, 1.05, 3, -71, [[8, "a4c84JoE69IlpundA0+AU+n", "selectAllClicked", 1]], [4, 4294967295], [4, 4294967295], -70]], [0, "5do6z5lslIKJS1nLcBdcpc", 1], [5, 148, 65], [304.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-75], [[2, 1.05, 3, -74, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "0", 1]], [4, 4294967295], [4, 4294967295], -73]], [0, "7e7zuDD7lDGrz//aHtS7jH", 1], [5, 132, 80], [-317.5, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-78], [[2, 1.05, 3, -77, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "1", 1]], [4, 4294967295], [4, 4294967295], -76]], [0, "6fdifpI2pCs5dDaD0OCjG0", 1], [5, 132, 80], [-150.9, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-81], [[2, 1.05, 3, -80, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "2", 1]], [4, 4294967295], [4, 4294967295], -79]], [0, "f5pjeYB/NCTL+LP7+kI1wz", 1], [5, 132, 80], [14.1, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-84], [[2, 1.05, 3, -83, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "3", 1]], [4, 4294967295], [4, 4294967295], -82]], [0, "090y3qE0hJjpdo+1qdHF1G", 1], [5, 132, 80], [178.2, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-87], [[2, 1.05, 3, -86, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "4", 1]], [4, 4294967295], [4, 4294967295], -85]], [0, "ebSQxjhgFBm7a9juR0ncIq", 1], [5, 132, 80], [341, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-90], [[2, 1.05, 3, -89, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "5", 1]], [4, 4294967295], [4, 4294967295], -88]], [0, "9bwJZqg0FEMbD3waA3w6dG", 1], [5, 132, 80], [-321.3, 39.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-93], [[2, 1.05, 3, -92, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "6", 1]], [4, 4294967295], [4, 4294967295], -91]], [0, "d7gvzexTtBOrJa7iGrTEcP", 1], [5, 132, 80], [-151, 35.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-96], [[2, 1.05, 3, -95, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "7", 1]], [4, 4294967295], [4, 4294967295], -94]], [0, "c29d464sxN9asALq3vnjg/", 1], [5, 132, 80], [14.1, 35.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-99], [[2, 1.05, 3, -98, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "8", 1]], [4, 4294967295], [4, 4294967295], -97]], [0, "35poeXo0NJjbsSV/z0wtpb", 1], [5, 132, 80], [178.8, 35.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-102], [[2, 1.05, 3, -101, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "9", 1]], [4, 4294967295], [4, 4294967295], -100]], [0, "0dkO8u5zlL25XtxEhzmQlB", 1], [5, 132, 80], [340.8, 35.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-105], [[2, 1.05, 3, -104, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "10", 1]], [4, 4294967295], [4, 4294967295], -103]], [0, "45fpJzge5NaJIWVaw4O2YA", 1], [5, 132, 80], [-315, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-108], [[2, 1.05, 3, -107, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "11", 1]], [4, 4294967295], [4, 4294967295], -106]], [0, "1aVrboweZGWZvj5cEFrgSE", 1], [5, 132, 80], [-150.9, -62.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-111], [[2, 1.05, 3, -110, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "12", 1]], [4, 4294967295], [4, 4294967295], -109]], [0, "32cItptO5JhoyBq3HgWEjC", 1], [5, 132, 80], [13.9, -63.4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-114], [[2, 1.05, 3, -113, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "13", 1]], [4, 4294967295], [4, 4294967295], -112]], [0, "5ePz27yahO1bszUm4PC4CK", 1], [5, 132, 80], [179.2, -62.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-117], [[2, 1.05, 3, -116, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "14", 1]], [4, 4294967295], [4, 4294967295], -115]], [0, "55dw4aPcRA4rmg2JXiOMJS", 1], [5, 132, 80], [340.8, -63.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-120], [[2, 1.05, 3, -119, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "15", 1]], [4, 4294967295], [4, 4294967295], -118]], [0, "f0H0wYEH1D0Yb6ggm7z8iG", 1], [5, 132, 80], [-315.5, -165.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-123], [[2, 1.05, 3, -122, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "16", 1]], [4, 4294967295], [4, 4294967295], -121]], [0, "6fTEaR8JJKhKidmQDtbjfz", 1], [5, 132, 80], [-151.6, -165.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-126], [[2, 1.05, 3, -125, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "17", 1]], [4, 4294967295], [4, 4294967295], -124]], [0, "333rdkrbNNiZqpUL3WeXxD", 1], [5, 132, 80], [13.7, -166.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-129], [[2, 1.05, 3, -128, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "18", 1]], [4, 4294967295], [4, 4294967295], -127]], [0, "25CL5QDPtNsL9zPfrj8/7f", 1], [5, 132, 80], [178.8, -166.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "button", 2, [-132], [[2, 1.05, 3, -131, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "19", 1]], [4, 4294967295], [4, 4294967295], -130]], [0, "9fum7S6lhPWKSRmdyhbzB6", 1], [5, 132, 80], [340.9, -165.4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 1, [-135], [[19, 1.1, 3, -134, [[8, "a4c84JoE69IlpundA0+AU+n", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -133, 68]], [0, "af8PIayyBBYJsmpczyrKNM", 1], [5, 80, 80], [468, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[18, 0, -136, [0], 1], [20, -137, [4, 4292269782]]], [0, "98IINDEvpJEp0fJkyGTYVo", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [-139], [[6, -138, [4], 5]], [0, "c1UjIsEQRI8YyWqWmuBaPL", 1], [5, 342, 448], [-257.216, 46.767, 0, 0, 0, 0, 1, 1.5, 1.4, 1]], [3, "sprite", 8, [[4, 2, false, -140, [10], 11]], [0, "d5yE/V2exJjJJvbAL1Al28", 1], [5, 104, 16], [0.9, -0.4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 9, [[4, 2, false, -141, [12], 13]], [0, "d0o7ywpetLk526XU8MFCC6", 1], [5, 104, 16], [0, 18.4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 10, [[4, 2, false, -142, [14], 15]], [0, "173hBBHFVI7Lb7HJ0TYtlY", 1], [5, 104, 16], [1, -18.8, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 11, [[4, 2, false, -143, [16], 17]], [0, "e20RqFciVLnbdwXDMRWgHN", 1], [5, 104, 36], [1.1, 8.1, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 12, [[4, 2, false, -144, [18], 19]], [0, "bb8/YCdjtHMp2Q1BvDparl", 1], [5, 104, 35], [-0.7, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 13, [[4, 2, false, -145, [20], 21]], [0, "e35nYXRhBPsKWfvuklFHV0", 1], [5, 104, 35], [1.5, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 14, [[4, 2, false, -146, [22], 23]], [0, "c2Z+yBdORH9rcrPpR2R9mm", 1], [5, 104, 35], [2, -10.2, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 15, [[4, 2, false, -147, [24], 25]], [0, "d0vhS6kT1IRLmZrWXTdvR0", 1], [5, 97, 52], [0.1, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 16, [[4, 2, false, -148, [26], 27]], [0, "7cF3OpvFVHU69qbkVTRKXc", 1], [5, 97, 52], [4, -0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 17, [[4, 2, false, -149, [28], 29]], [0, "37KvB0O9JBz77xDKRpui85", 1], [5, 105, 53], [2, -0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "sprite", 18, [[4, 2, false, -150, [30], 31]], [0, "bb0Cukp4FE2pRO2pHYmx0a", 1], [5, 90, 53]], [3, "sprite", 19, [[4, 2, false, -151, [32], 33]], [0, "2cDje6IRJLX6vNedcofakd", 1], [5, 89, 52], [1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 20, [[4, 2, false, -152, [34], 35]], [0, "f8U1YeD+pNJp5r9oYNxHxI", 1], [5, 103, 51], [1, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 21, [[4, 2, false, -153, [36], 37]], [0, "853d0L0+RL5Jtus2Q+AflU", 1], [5, 103, 51], [0.7, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 22, [[4, 2, false, -154, [38], 39]], [0, "9975ApDIhIIYsEVa6IE3kr", 1], [5, 99, 36], [1, -7.4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 23, [[4, 2, false, -155, [40], 41]], [0, "29+NCxvt9Db5vjHW5Ed2OI", 1], [5, 99, 36], [1.5, 7.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 24, [[4, 2, false, -156, [42], 43]], [0, "2bwMcmPG9K4JrDfH22ZxHf", 1], [5, 100, 36], [2, -8.5, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 25, [[4, 2, false, -157, [44], 45]], [0, "a6pMpRWQtGKYwHRcv7JgbA", 1], [5, 99, 36], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 26, [[4, 2, false, -158, [46], 47]], [0, "feXIdyD2JH4ZngKO6Gtd2X", 1], [5, 110, 53], [1.5, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 27, [[4, 2, false, -159, [48], 49]], [0, "a4t4173AFIY4KNLS9JL9Q/", 1], [5, 110, 53], [1.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "label", 4, [[9, "DÒNG CHẴN", 22, 50, false, false, 1, 1, 1, -160, [50], 51], [10, 1.5, -161, [4, 4278190080]]], [0, "3fyBKuI3dJJbFejIhEtoMi", 1], [5, 140, 34]], [7, "label", 5, [[9, "DÒNG LẺ", 22, 50, false, false, 1, 1, 1, -162, [54], 55], [10, 1.5, -163, [4, 4278190080]]], [0, "f8N2OWCABMTJCTUwe0Tp3s", 1], [5, 140, 34]], [7, "label", 6, [[9, "BỎ CHỌN", 22, 50, false, false, 1, 1, 1, -164, [58], 59], [10, 1.5, -165, [4, 4278190080]]], [0, "81qRg5C6RPorNtVt++48eU", 1], [5, 140, 34]], [7, "label", 7, [[9, "TẤT CẢ", 22, 50, false, false, 1, 1, 1, -166, [62], 63], [10, 1.5, -167, [4, 4278190080]]], [0, "9aBRE97eRMnJgo/CT1DV12", 1], [5, 140, 34]], [3, "nen popup", 30, [[6, -168, [2], 3]], [0, "56Exz8jB5JUaCgbvdUs+KD", 1], [5, 342, 448], [342.154, 0.8540000000000001, 0, 0, 0, 0, 1, -1, 1, 1]], [3, "chondong", 1, [[6, -169, [6], 7]], [0, "54nKTvLy9MTZ2qYY95nsj0", 1], [5, 172, 31], [0, 253, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "chon dong", 1, [[6, -170, [8], 9]], [0, "303fLRye5HxL2PbFcnTZiT", 1], [5, 807, 386], [-2, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 28, [[4, 2, false, -171, [66], 67]], [0, "f1miPbN9FMPqMAAVovib1V", 1], [5, 81, 81], [-64.754, 1.799, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, 0, 1, 0, -1, 31, 0, -2, 32, 0, -3, 33, 0, -4, 34, 0, -5, 35, 0, -6, 36, 0, -7, 37, 0, -8, 38, 0, -9, 39, 0, -10, 40, 0, -11, 41, 0, -12, 42, 0, -13, 43, 0, -14, 44, 0, -15, 45, 0, -16, 46, 0, -17, 47, 0, -18, 48, 0, -19, 49, 0, -20, 50, 0, 0, 1, 0, -1, 29, 0, -2, 30, 0, -3, 56, 0, -4, 57, 0, -5, 2, 0, -6, 3, 0, -7, 28, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, -6, 13, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, -10, 17, 0, -11, 18, 0, -12, 19, 0, -13, 20, 0, -14, 21, 0, -15, 22, 0, -16, 23, 0, -17, 24, 0, -18, 25, 0, -19, 26, 0, -20, 27, 0, 0, 3, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, 0, 4, 0, 2, 4, 0, 0, 4, 0, -1, 51, 0, 0, 5, 0, 2, 5, 0, 0, 5, 0, -1, 52, 0, 0, 6, 0, 2, 6, 0, 0, 6, 0, -1, 53, 0, 0, 7, 0, 2, 7, 0, 0, 7, 0, -1, 54, 0, 2, 8, 0, 0, 8, 0, -1, 31, 0, 2, 9, 0, 0, 9, 0, -1, 32, 0, 2, 10, 0, 0, 10, 0, -1, 33, 0, 2, 11, 0, 0, 11, 0, -1, 34, 0, 2, 12, 0, 0, 12, 0, -1, 35, 0, 2, 13, 0, 0, 13, 0, -1, 36, 0, 2, 14, 0, 0, 14, 0, -1, 37, 0, 2, 15, 0, 0, 15, 0, -1, 38, 0, 2, 16, 0, 0, 16, 0, -1, 39, 0, 2, 17, 0, 0, 17, 0, -1, 40, 0, 2, 18, 0, 0, 18, 0, -1, 41, 0, 2, 19, 0, 0, 19, 0, -1, 42, 0, 2, 20, 0, 0, 20, 0, -1, 43, 0, 2, 21, 0, 0, 21, 0, -1, 44, 0, 2, 22, 0, 0, 22, 0, -1, 45, 0, 2, 23, 0, 0, 23, 0, -1, 46, 0, 2, 24, 0, 0, 24, 0, -1, 47, 0, 2, 25, 0, 0, 25, 0, -1, 48, 0, 2, 26, 0, 0, 26, 0, -1, 49, 0, 2, 27, 0, 0, 27, 0, -1, 50, 0, 2, 28, 0, 0, 28, 0, -1, 58, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, -1, 55, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 6, 1, 171], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 4, -1, 1, -1, 4, -1, 1, -1, 4, -1, 1, -1, 4, -1, 1, -1, 1, 7, 8, -1, -2], [0, 5, 0, 3, 0, 3, 0, 6, 0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 12, 0, 13, 0, 14, 0, 15, 0, 16, 0, 17, 0, 18, 0, 19, 0, 20, 0, 21, 0, 22, 0, 23, 0, 24, 0, 25, 0, 26, 0, 27, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 28, 29, 4, 4, 30]], [[{"name": "10", "rect": [0, 0, 105, 53], "offset": [0, 0], "originalSize": [105, 53], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [31]], [[{"name": "6", "rect": [0, 0, 104, 35], "offset": [0, 0], "originalSize": [104, 35], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [32]], [[{"name": "3", "rect": [0, 0, 104, 16], "offset": [0, 0], "originalSize": [104, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [33]], [[{"name": "chondong-of", "rect": [0, 0, 807, 386], "offset": [0, 2], "originalSize": [807, 390], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [34]], [[{"name": "14", "rect": [0, 0, 103, 51], "offset": [0, 0], "originalSize": [103, 51], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [35]], [[{"name": "17", "rect": [0, 0, 100, 36], "offset": [0, 0], "originalSize": [100, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [36]], [[{"name": "2", "rect": [0, 0, 104, 16], "offset": [0, 0], "originalSize": [104, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [37]], [[{"name": "13", "rect": [0, 0, 103, 51], "offset": [0, 0], "originalSize": [103, 51], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [38]], [[{"name": "16", "rect": [0, 0, 99, 36], "offset": [0, 0], "originalSize": [99, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [39]], [[{"name": "19", "rect": [0, 0, 110, 53], "offset": [0, 0], "originalSize": [110, 53], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [40]], [[{"name": "7", "rect": [0, 0, 104, 35], "offset": [0, 0], "originalSize": [104, 35], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [41]], [[{"name": "1", "rect": [0, 0, 104, 16], "offset": [0, 0], "originalSize": [104, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [42]], [[{"name": "20", "rect": [0, 0, 110, 53], "offset": [0, 0], "originalSize": [110, 53], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [43]], [[{"name": "11", "rect": [0, 0, 90, 53], "offset": [0, 0], "originalSize": [90, 53], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [44]], [[{"name": "5", "rect": [0, 0, 104, 35], "offset": [0, 0], "originalSize": [104, 35], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [45]], [[{"name": "4", "rect": [0, 0, 104, 36], "offset": [0, 0], "originalSize": [104, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [46]], [[{"name": "9", "rect": [0, 0, 97, 52], "offset": [0, 0], "originalSize": [97, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [47]], [[{"name": "8", "rect": [0, 0, 97, 52], "offset": [0, 0], "originalSize": [97, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [48]], [[{"name": "chondong", "rect": [0, 0, 172, 31], "offset": [0, 0], "originalSize": [172, 31], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [49]], [[{"name": "button -chondong", "rect": [0, 1, 148, 65], "offset": [-1, -0.5], "originalSize": [150, 66], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [50]], [[{"name": "12", "rect": [0, 0, 89, 52], "offset": [0, 0], "originalSize": [89, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [51]], [[{"name": "18", "rect": [0, 0, 99, 36], "offset": [0, 0], "originalSize": [99, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [52]], [[{"name": "15", "rect": [0, 0, 99, 36], "offset": [0, 0], "originalSize": [99, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [53]]]]