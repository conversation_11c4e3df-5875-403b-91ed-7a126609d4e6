[1, ["ecpdLyjvZBwrvm+cedCcQy", "45XCTgB4pF+aKXShRo9KPW", "017Jn3Zv1Ft7hygdjpaSoK", "e2xSxof3xCnIoaUKvS6jbh", "f5w/1BRn5Ayb8UXB+OlMh+", "805DNvw/hGprvtpZ3gUy0M", "f3ycwK291I6ZPYQ336FEi3", "55/3bNGFpMjoAN/D7MBftu", "d4IylIAJVL4bp53bTImGyY", "adw94Z+hpN57wutNivq8Q5", "a9VpD0DP5LJYQPXITZq+uj", "3c5yg9ZL9KYbuq+0I9LrZ6", "dfQo5p6SVOhImfY9X1/rY7", "27hqkVyjZCTbSHGNw8t10L", "7a/QZLET9IDreTiBfRn2PD", "a9Z6PjFIpGOK21hJdG5iCC", "bfws0w5+9EeYfI++woq+Hd", "d0QJt4c/pFIoSlZ2x8GZ1f", "b41ffp1d1HJoNxYwWBRJUT", "1ddAvXCdBMwoeaqviW3lHE", "c1zXnUTyROjY2nUNNVeKZh", "61HVnbcgdC8Y97oTVcl5a1", "fdNoodJKVLj4dF1TLppv2g", "9dUG+V+1ZPhIDW7bv21gaX", "fejefp3nBBkqMgDkJLN18X", "40+Q2pwEpE+JW/MRWAAhLw", "5bCpcg1YhD+Y4M4eAfEyQS", "afCoZ3dsZC4bksVEuCzWSE", "e8xayZtQJHO6U1ZFTE524W", "0bQqxwtMhDU7zPXlKLW+3W", "f1H5VRlh9HLp0rNBdrx09q", "6ceBbC+F9AP6LU5G5lQuMc", "fdwumvnxFIQ7ARG0fJde2F", "b9Q7sWZhRBL4ef0FMrSYWh", "f9H/TN4uZNH6T4hXteihIn", "47xL5CWgdKnrDKQoNb365W", "b1I21wXQVBa7ER9zP8sVgM", "8dYf0tt+VK1Z033UuzS0GP", "b07A2i3ydAJ5/GQr24gjvI", "47LOJLiLNG3JJ2hACJj9s8", "ddICMPF7lG/5HEbbNQgnPo", "90B3qfUelHnJjXMMGrw3Am", "a46abmKQZCVZtN4zUzt3Vb", "61EjIZbuhCKYjy9Tksf+TI", "9fAU1h4RRFtLazpYTFVeHC", "f2Qm6od79JYKEigFXDfYE/", "8egAxCyXtGpZqVqiMFrC+i", "e73ogmUbtIL54H1dNSCqtK", "09OXvY2e9Pdrnj1dz+O07/", "72jhCfqQdCJJRTkZKzv26I", "f9ZJxQwjZEy77dE9Q/9UII", "28Pggrxs9NOZApIZB434Bg", "8dq7kn5plAQ5iEmMydPf1x", "d1AYT+F4tNXq0mUysL7ZyF", "04Av3VXFtHXordj9C3rjOy", "c2UTpR0HdFKqba10HAXIzA", "0f0zqy2S9FirqR1ofxLLWf", "2cWB/vWPRHja3uQTinHH30", "0eCTWmFnVPVJhP1ps7+ECc", "9frwXR9sxNfKSNLLQLvT9q", "64so2rlHtIF6DYMVVbrOJy", "0aAe0H1hhMxYu0ztLA1BZn", "bePJWviJlFvqsunOCCgFXB", "f9rbWUKHVPIY1faloCw/1U", "a4Awtth9NN+J6pm9Eu8mHB", "20aVUAbvZGiaPoDFHqz4i6", "32cMT6+FpIebjbwEVS2lZc", "5fkznvKMNNQqH3+wRrTpSd", "01YdB3sX1LQptyq4Qa36Fa", "f5K1zAzyNEY7HGd3V4QrY8", "4cc+Qbp+pBqq8lbcwlU6XF", "468sv63tNFVJmu2S/c5PRX", "252cE/3bJKb7d0HNGOvPcP", "c6zLsThGRJ8r0zjyeudxg0", "e4hweFWUFBsKEyM8YpgrZU", "58LM46JoVNhbEqbw2vIc3u", "bf7Ns2fG5P36whr5rgNIWp", "a1rHNDmdNHtY4Ww73h+Wgz", "d2tym/f9FG/IFaj9K7WxNR", "c2gD+Q3whNNbF6yA1Kyw0S", "5bX1B46mxAMJgrPPOWi9X1", "b1elflIoxDp6yJ3RSfxxtS", "24AUD8kb9EQowR79Zi8otf", "ddNPgfJ3ZDw5MI6x/l/p7M", "acCtJptdlNPpZgy9B/la9v", "4ee+x6wltNJpC8DmYFaQJE", "f9mncju/NMPo0a24H/YpEK", "19UdfxG5tDEIcK17U781Az", "c7XAldc51LFoHZzjLun1VZ", "e7tmqNkB9JsLZg1tjKSNES", "b3ezOn6btFWZibMKbMlnS7", "f9ZmyYZRxPRrOCzQDH/mev", "c2bvKR/9NDGqzzGpYHtMLu", "35SFg+lzdMyLMK/j9NNm/k", "8dB7muDKRE1L8e3v92hNj2", "caN2voBcFDu617fhgGze3t", "adpfyZyl5F+KopnmwWVHgt", "66huTtsmZMK4bmWDV26QuB", "92qQdRahVLbarMAhaP4k+9", "9fBoAsyKZMK59TBIppjuC5", "ebCTLYGJRMyIH0G5Yj2ieK", "2aXTfQi4lNhrR/eSSEqbJ3", "d19isN8UlKIJtVTw62SI8w", "72Td5ygV1FLojVmlwNDivH", "1cbe75cH1If5eF7wPhouB9", "93LgGNc7dMP4b1F0fPXAI+", "12QEdy2mFAbI4x37d7oOTj", "75c6maCQFDV7KEQxMDDegH", "8c0RuKOQpK2bJwW+zjDaFz", "18KIjUQbVDEqk5L9C2GYUM", "d9uFCJzDJP7oCKCEUiI8ry", "458znB1zRMMaLSBNVW6prF", "83nf9Qw8JOg7R+v6PLng8B", "88Ame13zlJeoG9ek61HTIr", "2aai8/eA9NW6kyro5mByGt", "deql/2B3lM9qeQtF0goJlF", "5916cOpslNep9WDnVETXzq", "e2YinZ7wdHALZW9JiSTV4P", "5fuLBjrHFAjYJH5UDCTV8h", "abgrOtLzhPZ5bICOCX3WIf", "1cD4AIf5BMnKulgVF6BrTD", "29wuRgy5dGdp7cRNRYLYU+", "11sMkUp+tPUqq3ghEoQ4Bk", "7euozZIPNDH6VcSf6up+jP", "0fYDqNMuZOpYo71Ym8VHMe", "f4S4i8O1RNW7sL0mgtYQp9", "97nsE5g7xAI5QkUDJqMkH7", "798hYMhbVPxJDyFv2cIZzg", "b0Rr3fdvdECpD0VnxOf3L2", "5fh2/dIMFMhrTqDoYKqQJy", "d3Tb9jaDxF9LUJYysQLbFA", "88p84kxIhE4peZEOwkXuxI", "a63VYGFi9BFq1rrSqcFm6J", "a7ZIP+aGpJdJPFaAE8axIJ", "b1oY5zpVZGB5o82vcdtDUR", "8fYcl5xS5FgqXU6oNMMKy4", "f4KHbHWs9I2qdslqJPc2oX", "85mda7JsJFt7iM9RpoWq4r", "3d83PugFlIEoKhnBovk2nb", "aaFuzeipZP275FcUOpMUd1", "04qCk7RgtN+rFrVbcT9Vsz", "42qL9ATZBImp9m1DpXpGSY", "7eDeFMDupAeLINFWkNmGGs", "b39AAZVmhBwo6RhggWiI/M", "8fcMu+m75M7Jjv9fmoszUu", "e3OZfyaypE0I6UNkSLpU8T", "4aaSLDn+JBbYyx4GNXchEg", "2a7PQTlEpE3J+1OrOYY0vP", "37bx4L5UVGK6i9r0L54paK", "8cX9xdpltCy7WbhLtx4Nh/", "8agJa2gbZGkLcttIH21sPl", "69Dvx8VyBJ3oA+BhGuBOyI", "c9czbBFENJFrjOBRfET9S7", "a6oJfk8BVH66eZXloqvo54", "69vWrfrdhFVr6dchnbQ6hX", "78ciCkJj1FsomcZgH7xP/Z", "9fXnqKj21IDKCjItGamuru", "4bTfGrscJMNYaPZFLu8W0Z", "b31FXfcXtMi4cG6XLatmq/", "a4gMqwPOxOx7C/cjY6B8ii", "04C1J7jB5AUaNym92nAuC8", "87cJ7IdlNDdK0sM3y5xfjW", "82hHGEvUhKQoPkUwMfPVGS", "cfDr4smChARImoXyhoXd4S", "87nFOkH21PqKCyPNBSmvDZ", "e8Z7DVUBFCYpnVOoLXeo2x", "d68YIOYwpHyJ5uzyn/PwU2", "c31mTLzHJGtZJinQ+jEBpH", "ecYAgSLgFN3LG1IsI+9YmH", "85nbYSdRNLraoM9aa/JE5G", "3cF0q/fZdBv78uPE0Sy/Us", "90UCDkuO1BnJ1feMSeVGQk", "c1d6w2L/pLx58Yc2WnxkeL", "d4JQ+lIhNKdLI5XOKveMkH", "4aGWDe+DlJl549NInyvXvH", "ebvyKLymFBd4R0x0rq+PKa", "ab7jxCJeFEd5bvnjICy7A5", "7ev6x0nFZO/rr7mQfvM6BD", "e3vzpZiQNAzoLybZSrbh0o", "8fJ9BrjFFBxY1iS4nO+bht", "238t2TD2lEepS6LWoOcaOL", "ed4cwNhhJJ8bVGZhsIxNrw", "4av/sWjEZADKtNCJpU1P3k", "e09nhdHdNP6YpWRPRmYltO", "8bkGfKci9LpoYRaVh0EXxF"], ["_textureSetter", "node", "_spriteFrame", "_N$file", "_defaultClip", "_N$target", "_parent", "_N$normalSprite", "_N$disabledSprite", "_file", "_N$skeletonData", "root", "nodeScale", "btnScale", "skSpin", "btnSpin", "btnAutoSpin", "btnFastSpin", "btn10000", "btn1000", "btn100", "nodeEffect", "nodeFreeSpin", "nodeGroupBot2", "nodeGroupBot", "nodeClose", "nodeGroupLeft", "miniPokerImage", "lbSessionID", "lbiTotalWin", "spritePrize", "lbiJackpot", "target", "lbiNormalWin", "lbiBigWin", "lbiJackpotWin", "particleNormalWin", "particleBigWin", "particleJackpot", "nodeNormalWin", "nodeBigWin", "nodeJackpot", "spriteXHu", "lbFreeSpin", "data", "prefabHelp", "prefabHistory", "prefabTop"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint", "_color"], 0, 9, 4, 5, 1, 2, 7, 5, 5], ["cc.Node", ["_name", "_prefab", "_parent", "_contentSize", "_trs", "_components", "_children", "_anchorPoint", "_color"], 2, 4, 1, 5, 7, 12, 2, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_children", "_color"], 2, 1, 2, 4, 5, 7, 5, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_isSystemFontUsed", "_N$verticalAlign", "_string", "_N$horizontalAlign", "_fontSize", "_lineHeight", "_spacingX", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingTop", "_N$paddingBottom", "_enabled", "_N$spacingX", "node", "_layoutSize"], -4, 1, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$normalColor", "_N$target", "_N$pressedColor", "_N$disabledColor"], 1, 1, 9, 5, 1, 5, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "loop", "_playTimes", "node", "_materials", "_N$skeletonData"], -3, 1, 3, 6], ["cc.ParticleSystem", ["emissionRate", "life", "lifeVar", "angleVar", "startSize", "endSize", "startSpinVar", "endSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccelVar", "_custom", "totalParticles", "duration", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity"], -13, 1, 3, 8, 8, 8, 8, 5, 5], ["cc.AnimationClip", ["_name", "_duration", "speed", "wrapMode", "events", "curveData"], -3], ["cc.Prefab", ["_name"], 2], ["99917hXwY5OtJEKKXKhbWqG", ["node", "nodeScale"], 3, 1, 1], ["ff10eTzhF5JPqCfpea30kPH", ["node", "miniPokerImage", "nodeGroupLeft", "nodeClose", "nodeGroupBot", "nodeGroupBot2", "nodeFreeSpin", "nodeEffect", "btn100", "btn1000", "btn10000", "btnFastSpin", "btnAutoSpin", "btnSpin", "skSpin", "btnScale", "spriteScaleIcon"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], ["f74d33qIsFLnqhF+m+kFzXV", ["node"], 3, 1], ["8457bzXi+RCFrWEyccPy/PF", ["touchParent", "node"], 2, 1], ["deb15KPphtHg79JsHj1lW5I", ["node", "sfCards"], 3, 1, 3], ["13c70uI3v5Ixo6840sXzNOh", ["node", "sfPrizes"], 3, 1, 3], ["659377lP2ZAP4WdgLMuhQyJ", ["node", "prefabHelp", "prefabHistory", "prefabTop"], 3, 1, 6, 6, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["f5318h6KIRDuKf5y28YJJVE", ["node", "spinColumnViews", "spritePrize", "lbiTotalWin", "lbSessionID"], 3, 1, 2, 1, 1, 1], ["771e8y7n1VP1aeZ/pHg9LlL", ["node", "lbiJackpot"], 3, 1, 1], ["56484KW9tNHfJQ7QLw3FvTb", ["node", "nodeJackpot", "nodeBigWin", "nodeNormalWin", "particleJackpot", "particleBigWin", "particleNormalWin", "lbiJackpotWin", "lbiBigWin", "lbiNormalWin"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["1505fBqDhVP3bauxrJQx3ia", ["node"], 3, 1], ["5c1fa7YnkBEMqpU9n30ksJn", ["gameId", "node", "nodeX", "spriteXHu", "lbRemainJackpots"], 2, 1, 1, 1, 2], ["857aeBK45xJHrUWPSfQz4On", ["node", "lbFreeSpin"], 3, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["7a0ebyMgkNPAZjEK15JokRl", ["node", "spriteIcons"], 3, 1, 2], ["6a5dfjnHR1NxokD7pQT7izs", ["node", "sfChips", "sfFastSpins", "sfAutoSpins"], 3, 1, 3, 3, 3]], [[21, 0, 1, 2], [9, 0, 1, 3, 3], [4, 0, 1, 2, 3, 3], [7, 0, 1, 2, 3, 6, 7, 5, 3], [4, 0, 1, 2, 3, 4, 3], [2, 0, 2, 5, 1, 3, 4, 2], [1, 0, 6, 3, 4, 5, 8, 2], [3, 0, 1, 2, 3, 4, 5, 2], [4, 2, 3, 4, 1], [8, 1, 2, 3, 1], [29, 0, 1], [3, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 3, 2], [5, 2, 4, 5, 0, 3, 1, 7, 8, 7], [30, 0, 1, 1], [2, 0, 2, 6, 5, 1, 3, 4, 2], [1, 0, 6, 7, 3, 4, 5, 8, 2], [1, 0, 6, 7, 3, 4, 10, 5, 9, 8, 2], [3, 0, 1, 2, 3, 4, 6, 5, 2], [5, 2, 4, 5, 0, 3, 1, 7, 8, 9, 7], [12, 0, 1, 2, 3, 4, 5, 7], [2, 0, 2, 5, 1, 3, 2], [1, 0, 1, 6, 7, 3, 4, 8, 3], [3, 0, 1, 2, 3, 5, 2], [7, 2, 3, 4, 1], [9, 0, 1, 2, 3, 4], [4, 0, 2, 3, 4, 2], [5, 2, 4, 0, 3, 1, 7, 8, 9, 6], [1, 0, 6, 7, 3, 4, 5, 9, 8, 2], [1, 0, 1, 6, 7, 3, 4, 5, 8, 3], [1, 0, 1, 2, 6, 3, 4, 5, 4], [4, 0, 2, 4, 2], [11, 14, 15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 17], [13, 0, 2], [2, 0, 6, 5, 1, 3, 4, 2], [2, 0, 2, 6, 1, 4, 2], [2, 0, 2, 6, 5, 1, 3, 2], [2, 0, 2, 5, 1, 3, 7, 4, 2], [2, 0, 2, 5, 1, 8, 3, 7, 4, 2], [2, 0, 2, 6, 1, 3, 4, 2], [2, 0, 2, 1, 4, 2], [1, 0, 6, 7, 3, 4, 5, 2], [1, 0, 7, 3, 4, 5, 9, 2], [1, 0, 7, 3, 4, 5, 2], [1, 0, 7, 3, 4, 5, 8, 2], [1, 0, 6, 7, 3, 4, 2], [1, 0, 2, 6, 3, 4, 5, 3], [1, 0, 2, 6, 3, 4, 5, 8, 3], [1, 0, 1, 6, 3, 4, 5, 8, 3], [3, 0, 1, 7, 2, 3, 4, 6, 5, 2], [3, 0, 1, 2, 3, 8, 4, 6, 5, 2], [14, 0, 1, 1], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 1], [16, 0, 1], [17, 0, 1, 2], [18, 0, 1, 1], [19, 0, 1, 1], [20, 0, 1, 2, 3, 1], [22, 0, 1, 2, 3, 4, 1], [23, 0, 1, 1], [7, 2, 3, 4, 5, 1], [4, 2, 3, 1], [6, 0, 1, 3, 4, 2, 7, 8, 6], [6, 0, 1, 2, 7, 8, 4], [6, 0, 1, 7, 8, 3], [6, 5, 0, 1, 6, 7, 8, 5], [24, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [25, 0, 1], [26, 0, 1, 2, 3, 4, 2], [27, 0, 1, 1], [5, 2, 0, 6, 3, 1, 7, 8, 9, 6], [5, 2, 0, 3, 1, 7, 8, 9, 5], [5, 4, 5, 0, 1, 7, 8, 5], [28, 0, 1, 2, 2], [10, 0, 1, 4, 2, 3, 5, 6, 7, 7], [10, 0, 1, 2, 3, 6, 7, 8, 5], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 15], [31, 0, 1, 2, 3, 1]], [[[{"name": "card_7_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [25]], [[{"name": "card_6_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [26]], [[{"name": "card_9_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [27]], [[{"name": "card_13_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [28]], [[{"name": "haidoi", "rect": [0, 0, 110, 34], "offset": [0, 0], "originalSize": [110, 34], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [29]], [[{"name": "card_4_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [30]], [[{"name": "card_4_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [31]], [[{"name": "card_8_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [32]], [[{"name": "card_11_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [33]], [[{"name": "doiJ", "rect": [0, 0, 201, 42], "offset": [0, 0], "originalSize": [201, 42], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [34]], [[{"name": "card_1_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [35]], [[{"name": "thungJ", "rect": [0, 0, 279, 43], "offset": [0, 0], "originalSize": [279, 43], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [36]], [[{"name": "card_5_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [37]], [[{"name": "card_6_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [38]], [[[20, "columnSpin", 0.6833333333333333, 6, 2, [{"frame": 0.3333333333333333, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 100}, {"frame": 0.6666666666666666, "value": -100}, {"frame": 0.6833333333333333, "value": 100}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -100}, {"frame": 0.35, "value": 100}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "card_12_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [39]], [[{"name": "card_10_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [40]], [[{"name": "card_9_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [41]], [[{"name": "card_11_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [42]], [[[33, "minipokerView"], [34, "minipokerView", [-27, -28], [[[9, -2, [117, 118], 116], [51, -4, -3], [52, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, [129, 130]], -21, [53, -22], [54, null, -23], [55, -24, [131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182]], [56, -25, [183, 184, 185, 186, 187, 188, 189, 190, 191]], [57, -26, 192, 193, 194]], 4, 4, 4, 1, 4, 4, 4, 4, 4], [0, "3f6RErltlALpSQvm0eiR8n", -1], [5, 700, 400], [0, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "offset-scale", 1, [-29, -30, -31, -32, -33, -34, -35, -36, -37], [0, "f3r3nfE5NC+JSI3cgzDhNX", 1], [0, -37, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [41, "spin1View", 2, [-49, -50, -51, -52, -53, -54, -55], [[58, -46, [-41, -42, -43, -44, -45], -40, -39, -38], [59, -48, -47]], [0, "56K39/5llF06dZ6CIHahPr", 1], [5, 500, 240]], [42, "offset-xHu", [-59, -60, -61], [[60, -58, [[1, "5c1fa7YnkBEMqpU9n30ksJn", "openEventClicked", -57]], [4, 4292269782], -56]], [0, "33y14Gi3JK+bVJHK8Sw7/B", 1], [5, 190, 170], [0, 0.5, 0.8]], [28, "layout", 4, [-64, -65, -66, -67], [[26, 0, -62, [15], 16], [62, 1, 2, 25, 25, 15, -63, [5, 190, 195]]], [0, "9frESJ01FHuLBhzLWRoXsg", 1], [5, 190, 195], [0, 0.5, 1], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "layout-btn", 2, [-69, -70, -71, -72], [[63, 1, 2, 20, -68, [5, 60, 322]]], [0, "6eUmq1bdNEQ5Hz6drmirHJ", 1], [5, 60, 322], [-405, -12, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "slots", [-74, -75, -76, -77, -78], [[64, 1, 1, -73, [5, 360, 300]]], [0, "d9B8EvCD5CqotqJlPv76Vj", 1], [5, 360, 300]], [44, "layout-btn", [-80, -81, -82, -83, -84], [[65, false, 1, 1, 15, -79, [5, 652, 50]]], [0, "feNvv09lJAEaO0O6zT3LTY", 1], [5, 652, 50], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "effect<PERSON>iew", 2, [-95, -96, -97], [[66, -94, -93, -92, -91, -90, -89, -88, -87, -86, -85]], [0, "16L/s8XZZAWo+/jZuXYWgH", 1]], [22, "offset-jackpot", false, 9, [-99, -100, -101, -102], [[12, true, -98, [103], 102]], [0, "7bT9F9rUpPXqxl10oYLDCx", 1], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "bgWin", false, 3, [-106, -107], [[8, -103, [75], 76], [12, true, -104, [78, 79], 77], [67, -105]], [0, "ba6Df0TJVNLZFhTkZDWq+e", 1], [5, 362, 44], [0, -33.7, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "offset-bigWin", false, 9, [-109, -110, -111], [[12, true, -108, [109], 108]], [0, "e1SMv6RN5Ggb3Xzgp5hwAT", 1], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "offset-normalWin", false, 9, [-113, -114, -115], [[12, true, -112, [115], 114]], [0, "35YQqcu9FPgKGL0HWyLYxo", 1], [0, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "xHuView", 1, [4], [[68, 11, -121, 4, -120, [-116, -117, -118, -119]]], [0, "85a5DD9wpEkLqgK+D2u2vg", 1], [5, 190, 190], [0, 0.5, 0.7], [-470, 98, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 2, [[4, 2, false, -122, [28], 29], [3, 1.1, 3, -124, [[1, "99917hXwY5OtJEKKXKhbWqG", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -123]], [0, "311qjfkBNA+4vhigjuIhq3", 1], [5, 73, 73], [357, 160, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "1", 7, [-127, -128], [[[9, -125, [41, 42, 43], 40], -126], 4, 1], [0, "e5FoK0eU1CoYHu8Q6JiE2J", 1], [5, 72, 300], [-144, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "2", 7, [-131, -132], [[[9, -129, [47, 48, 49], 46], -130], 4, 1], [0, "6dZuoqRgpMUoX7NGYZdQ24", 1], [5, 72, 300], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "3", 7, [-135, -136], [[[9, -133, [53, 54, 55], 52], -134], 4, 1], [0, "5aOhdkefhEn7pFXTQJFzYk", 1], [5, 72, 300]], [15, "4", 7, [-139, -140], [[[9, -137, [59, 60, 61], 58], -138], 4, 1], [0, "49o2qWM81D/aXZj1j12N+B", 1], [5, 72, 300], [72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "5", 7, [-143, -144], [[[9, -141, [65, 66, 67], 64], -142], 4, 1], [0, "54oSKg3YdFQbkbgjRUdfNF", 1], [5, 72, 300], [144, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnTop", 6, [[4, 2, false, -145, [20], 21], [3, 1.1, 3, -147, [[1, "ff10eTzhF5JPqCfpea30kPH", "topClicked", 1]], [4, 4294967295], [4, 4294967295], -146]], [0, "c9thfWj61N4I2b9TAB0zdq", 1], [5, 63, 63], [0, 129.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnHistory", 6, [[4, 2, false, -148, [22], 23], [3, 1.1, 3, -150, [[1, "ff10eTzhF5JPqCfpea30kPH", "historyClicked", 1]], [4, 4294967295], [4, 4294967295], -149]], [0, "be9MYMxHJBNLY6O+p3WJWd", 1], [5, 63, 63], [0, 46.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnHelp", 6, [[4, 2, false, -151, [24], 25], [3, 1.1, 3, -153, [[1, "ff10eTzhF5JPqCfpea30kPH", "helpClicked", 1]], [4, 4294967295], [4, 4294967295], -152]], [0, "dbpoOheNJK3L5H/PDtZaz4", 1], [5, 63, 63], [0, -36.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnScale", 6, [[[4, 2, false, -154, [26], 27], -155], 4, 1], [0, "35oh+iLiVLT54eFUI+bYeB", 1], [5, 73, 73], [0, -124.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnAutoSpin", 8, [[[4, 2, false, -156, [80], 81], -157], 4, 1], [0, "a6nm5FMwVP+YK+OJ9xViPX", 1], [5, 152, 50], [-250, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btn100", 8, [[[4, 2, false, -158, [82], 83], -159], 4, 1], [0, "c31HAiKX1BK5KEfgnvPjr4", 1], [5, 96, 50], [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "btn1000", 8, [[[4, 2, false, -160, [84], 85], -161], 4, 1], [0, "09PfEc+GxAOLcEPi+Pj2Ae", 1], [5, 96, 50]], [5, "btn10000", 8, [[[4, 2, false, -162, [86], 87], -163], 4, 1], [0, "8aewv2iFNKDq+/DOv4aU4F", 1], [5, 96, 50], [111, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnFastSpin", 8, [[[4, 2, false, -164, [88], 89], -165], 4, 1], [0, "2bhQ/tFV9OI4qZrNxM9zGD", 1], [5, 152, 50], [250, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "freeSpinView", 2, [-168], [[69, -167, -166]], [0, "79UlBFOc5JdaGrXs1l2cUw", 1], [5, 362, 44], [0, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "logo", 4, [[-169, [12, true, -170, [2], 1]], 1, 4], [0, "3doVyNuI9Cf6iLq+rFbPOS", 1], [5, 90, 44], [0, 0.5, 1], [0, 58.1, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "lbRoom", 5, [-172], [[19, "100:", 20, 50, false, 1, 1, -171, [4], 5]], [0, "cfqdrvO2NFKLegONnyTvIj", 1], [4, 4278255615], [5, 39.5, 25], [0, 0, 0.5], [-73.5, -37.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "lbRoom", 5, [-174], [[19, "1K:", 20, 50, false, 1, 1, -173, [7], 8]], [0, "12QASMKzpBHZYuEqsKAGOV", 1], [4, 4278255615], [5, 29.5, 25], [0, 0, 0.5], [-73.5, -77.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "lbRoom", 5, [-176], [[19, "10K:", 20, 50, false, 1, 1, -175, [10], 11]], [0, "3c5jKPIABKhbTgyPM20O1I", 1], [4, 4278255615], [5, 41, 25], [0, 0, 0.5], [-73.5, -117.5, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "lbRoom", 5, [-178], [[19, "30K:", 20, 50, false, 1, 1, -177, [13], 14]], [0, "cdkmMi6mVOcIN8FI9HJ7kG", 1], [4, 4278255615], [5, 41, 25], [0, 0, 0.5], [-73.5, -157.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "close", 4, [[26, 0, -179, [17], 18], [24, -180, [[1, "5c1fa7YnkBEMqpU9n30ksJn", "onClickHide", 14]], [4, 4292269782]]], [0, "3bntdAZjFDII8mSqA5mvKY", 1], [5, 35, 35], [-85.7, 9.8, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "btnSpin", 2, [-182], [-181], [0, "2ehs8dOfdDxbrYFyX4j7W7", 1], [5, 80, 180], [0, 0.4, 0.5], [384.9, 33, 0, 0, 0, 0, 1, 1, 1, 0.8]], [16, "bgJackpot", 3, [-184], [[8, -183, [34], 35]], [0, "5dry25fItJ8ZRY8TqYE1ZS", 1], [5, 243, 54], [0, -204, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "lbJackpot", 38, [[[27, "000000000", 26, false, 1, 1, -185, [32], 33], -186], 4, 1], [0, "c84OKujxZL16jGazcfB0Kp", 1], [4, 4287821816], [5, 135.2, 26], [0, 0, 0.5], [-81, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "spinView", 3, [7], [[73, 1, -187, [68]]], [0, "02xB992yVKPoZwmtI1a6fU", 1], [5, 394, 96], [0, 18, 0, 0, 0, 0, 1, 1.65, 1.65, 1]], [21, "lbWin", 11, [[[70, "0", false, -3, 1, 1, -188, [73], 74], -189], 4, 1], [0, "a3A7wOU79ISrhp34m/IozQ", 1], [5, 31.25, 50]], [39, "khung duoi-sprite", 2, [8], [0, "dfhBQfUwBH7Zfesz7DbfHQ", 1], [5, 660, 50], [0, -127, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "spriteBG", false, 30, [-191], [[8, -190, [93], 94]], [0, "16C3GFE4dMSbloy4EY64a+", 1], [5, 362, 44], [0, -178, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbFreeSpin", 43, [[-192, [10, -193]], 1, 4], [0, "f4g/VoIvBNJajIdv0t+PZ1", 1], [5, 196.3, 32.5]], [46, "black", 1, 10, [[26, 0, -194, [95], 96], [24, -195, [[1, "56484KW9tNHfJQ7QLw3FvTb", "continueClicked", 9]], [4, 4292269782]]], [0, "7de1iwDMVLW7uCdBVA1AsG", 1], [5, 3000, 3000]], [5, "lbWin", 10, [[[71, "100.000", false, 1, 1, -196, [100], 101], -197], 4, 1], [0, "51OuYb/Z1PC6sEW88wsv9o", 1], [5, 325, 50], [0, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", 12, [[[27, "100.000", 35, false, 1, 1, -198, [106], 107], -199], 4, 1], [0, "895/qblj9EZrN7N41kvQHe", 1], [5, 284.38, 43.75], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", 13, [[[27, "100.000", 35, false, 1, 1, -200, [112], 113], -201], 4, 1], [0, "95FVJE5gdMFKBiPsaV8MnX", 1], [5, 284.38, 43.75], [0, 13, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 2, false, 31, [0]], [18, "lbRemain", 32, [-202], [0, "a7APEmWHBLDLxOM4gNXgOA", 1], [5, 102.5, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 26 HŨ", 20, 50, false, 1, 1, 50, [3]], [18, "lbRemain", 33, [-203], [0, "0177gEbodE3J706dLG4xxS", 1], [5, 102.5, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 26 HŨ", 20, 50, false, 1, 1, 52, [6]], [18, "lbRemain", 34, [-204], [0, "d4aEWKn7dL8IEhh4hsGY6p", 1], [5, 91, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 2 HŨ", 20, 50, false, 1, 1, 54, [9]], [18, "lbRemain", 35, [-205], [0, "58AgLT3/lN+ZATvMMor6JQ", 1], [5, 91, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 1 HŨ", 20, 50, false, 1, 1, 56, [12]], [7, "skeleton", 37, [-206], [0, "11U0EeRbxJ5Y7KxrK+HZLB", 1], [5, 43, 177], [11, -39, 0, 0, 0, 0, 1, 1, 1, 1]], [74, "waiting", 0, false, false, "waiting", 1, 58, [19]], [24, 37, [[1, "ff10eTzhF5JPqCfpea30kPH", "spinClicked", 1]], [4, 4292269782]], [3, 1.1, 3, 24, [[1, "ff10eTzhF5JPqCfpea30kPH", "scaleClick", 1]], [4, 4294967295], [4, 4294967295], 24], [6, "bodertable", 3, [[8, -207, [30], 31]], [0, "68U6Y3auVOU7N9GUxKT3MK", 1], [5, 753, 502], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [10, 39], [6, "main-cards", 3, [[8, -208, [36], 37]], [0, "19PLXnA75OCKseVsFHcIoU", 1], [5, 692, 200], [0, 18.8, 0, 0, 0, 0, 1, 0.94, 0.94, 1]], [7, "slot0", 16, [-209], [0, "4catqSqmxPCb+PJ5ij//FF", 1], [5, 72, 96], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 2, false, 65, [38]], [11, "slot1", 16, [-210], [0, "ebblTH/yBF6YL7GLjX+S2g", 1], [5, 72, 96]], [2, 2, false, 67, [39]], [14, 16, [66, 68]], [7, "slot0", 17, [-211], [0, "aaPbfQXmNG1L8FziZ6Wvm+", 1], [5, 72, 96], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 2, false, 70, [44]], [11, "slot1", 17, [-212], [0, "c3WSHm1XlKZb/1JBBse4OV", 1], [5, 72, 96]], [2, 2, false, 72, [45]], [14, 17, [71, 73]], [7, "slot0", 18, [-213], [0, "0c3TqDAn1Ci5mTDVbBpGBw", 1], [5, 72, 96], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 2, false, 75, [50]], [11, "slot1", 18, [-214], [0, "038dSoQaZKY61/mCiSzQkL", 1], [5, 72, 96]], [2, 2, false, 77, [51]], [14, 18, [76, 78]], [7, "slot0", 19, [-215], [0, "8cJwFqkmdJKJH4TUc7B7Dk", 1], [5, 72, 96], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 2, false, 80, [56]], [11, "slot1", 19, [-216], [0, "6c2ZHqVvxI0IdL48br7dfa", 1], [5, 72, 96]], [2, 2, false, 82, [57]], [14, 19, [81, 83]], [7, "slot0", 20, [-217], [0, "00QvSETUBOFILEDLauMHNw", 1], [5, 72, 96], [0, 100, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 2, false, 85, [62]], [11, "slot1", 20, [-218], [0, "90nztcTKdFZZc86X9pydMv", 1], [5, 72, 96]], [2, 2, false, 87, [63]], [14, 20, [86, 88]], [47, "shadows", 235, 3, [[8, -219, [69], 70]], [0, "33eBuvw0VGK4z8MnVPC8Ey", 1], [5, 640, 182], [0, 18.5, 0, 0, 0, 0, 1, 0.94, 0.9, 1]], [50, "lbSessionId", 3, [-220], [0, "90ECDoOThDI5c+Ve56rLJT", 1], [4, 4292598747], [5, 0, 20], [0, 0, 0.5], [129, 121, 0, 0, 0, 0, 1, 1, 1, 1]], [72, 16, 50, false, 1, 91, [71]], [7, "spritePrize", 11, [-221], [0, "f8IhXi5u1AFJUxMDXyip5R", 1], [5, 249, 43], [0, 49, 0, 0, 0, 0, 1, 1, 1, 1]], [61, 93, [72]], [10, 41], [3, 1.1, 3, 25, [[1, "ff10eTzhF5JPqCfpea30kPH", "autoSpinClicked", 1]], [4, 4294967295], [4, 4294967295], 25], [3, 1.1, 3, 26, [[25, "ff10eTzhF5JPqCfpea30kPH", "roomClicked", "1", 1]], [4, 4294967295], [4, 4294967295], 26], [3, 1.1, 3, 27, [[25, "ff10eTzhF5JPqCfpea30kPH", "roomClicked", "2", 1]], [4, 4294967295], [4, 4294967295], 27], [3, 1.1, 3, 28, [[25, "ff10eTzhF5JPqCfpea30kPH", "roomClicked", "3", 1]], [4, 4294967295], [4, 4294967295], 28], [3, 1.1, 3, 29, [[1, "ff10eTzhF5JPqCfpea30kPH", "fastSpinClicked", 1]], [4, 4294967295], [4, 4294967295], 29], [48, "coin", false, 2, [[8, -222, [90], 91]], [0, "a36Hlaw8ZGB5IFqXEK0tMB", 1], [5, 67, 69], [-193.4, 131.5, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [40, "khungduoi-label", 2, [0, "e2+8VM1wZLha8YdiNGY6qv", 1], [0, -113, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "<PERSON>uay miễn phí: 1", 26, 50, false, 1, 1, 44, [92]], [23, "particleBigWin", 10, [-223], [0, "7ek6DgGB1PY4yKhl0Qefzz", 1], [0, 250, 0, 0, 0, 0, 1, 1, 1, 1]], [76, 100, 1.5, 0.5, 58, 40, -1, 51, 500, 1, 850, 383, 0, 50, {"custom": false}, 104, [97], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 101, 73], [0, 0, -2400]], [6, "noHu_mn", 10, [[75, "Idle", 0, false, "Idle", -224, [98], 99]], [0, "904CkhUadFa6stjKuhI1sQ", 1], [5, 1280, 720], [0, 80, 0, 0, 0, 0, 1, 0.76923, 0.76923, 0.76923]], [10, 46], [30, "black", false, 1, 12, [[31, 0, -225, 104]], [0, "04gAiTiHVDcrubhQiEzHzB", 1], [5, 3000, 3000]], [23, "particleWin", 12, [-226], [0, "2acOs8Zm1JSJgNEDEJdTzt", 1], [9, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, {"custom": false}, 109, [105], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [10, 47], [30, "black", false, 1, 13, [[31, 0, -227, 110]], [0, "aeyZiWJCdFpa7OZ9gP+w8m", 1], [5, 3000, 3000]], [23, "particleWin", 13, [-228], [0, "82SjSFGYBO0bVuILzLycdw", 1], [9, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, {"custom": false}, 113, [111], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [10, 48], [77, 1, [119, 120, 121, 122, 123, 124], [125, 126], [127, 128]]], 0, [0, 11, 1, 0, 1, 1, 0, 12, 2, 0, 1, 1, 0, 13, 61, 0, 14, 59, 0, 15, 60, 0, 16, 96, 0, 17, 100, 0, 18, 99, 0, 19, 98, 0, 20, 97, 0, 21, 9, 0, 22, 30, 0, 23, 102, 0, 24, 42, 0, 25, 15, 0, 26, 6, 0, 27, 116, 0, 1, 1, 0, -4, 116, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, -1, 14, 0, -2, 2, 0, -1, 37, 0, -2, 6, 0, -3, 15, 0, -4, 3, 0, -5, 42, 0, -6, 101, 0, -7, 102, 0, -8, 30, 0, -9, 9, 0, 28, 92, 0, 29, 95, 0, 30, 94, 0, -1, 69, 0, -2, 74, 0, -3, 79, 0, -4, 84, 0, -5, 89, 0, 1, 3, 0, 31, 63, 0, 1, 3, 0, -1, 62, 0, -2, 38, 0, -3, 64, 0, -4, 40, 0, -5, 90, 0, -6, 91, 0, -7, 11, 0, 5, 4, 0, 32, 14, 0, 1, 4, 0, -1, 31, 0, -2, 5, 0, -3, 36, 0, 1, 5, 0, 1, 5, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, 1, 6, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, -4, 24, 0, 1, 7, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, 1, 8, 0, -1, 25, 0, -2, 26, 0, -3, 27, 0, -4, 28, 0, -5, 29, 0, 33, 115, 0, 34, 111, 0, 35, 107, 0, 36, 114, 0, 37, 110, 0, 38, 105, 0, 39, 13, 0, 40, 12, 0, 41, 10, 0, 1, 9, 0, -1, 10, 0, -2, 12, 0, -3, 13, 0, 1, 10, 0, -1, 45, 0, -2, 104, 0, -3, 106, 0, -4, 46, 0, 1, 11, 0, 1, 11, 0, 1, 11, 0, -1, 93, 0, -2, 41, 0, 1, 12, 0, -1, 108, 0, -2, 109, 0, -3, 47, 0, 1, 13, 0, -1, 112, 0, -2, 113, 0, -3, 48, 0, -1, 51, 0, -2, 53, 0, -3, 55, 0, -4, 57, 0, 42, 49, 0, 1, 14, 0, 1, 15, 0, 5, 15, 0, 1, 15, 0, 1, 16, 0, -2, 69, 0, -1, 65, 0, -2, 67, 0, 1, 17, 0, -2, 74, 0, -1, 70, 0, -2, 72, 0, 1, 18, 0, -2, 79, 0, -1, 75, 0, -2, 77, 0, 1, 19, 0, -2, 84, 0, -1, 80, 0, -2, 82, 0, 1, 20, 0, -2, 89, 0, -1, 85, 0, -2, 87, 0, 1, 21, 0, 5, 21, 0, 1, 21, 0, 1, 22, 0, 5, 22, 0, 1, 22, 0, 1, 23, 0, 5, 23, 0, 1, 23, 0, 1, 24, 0, -2, 61, 0, 1, 25, 0, -2, 96, 0, 1, 26, 0, -2, 97, 0, 1, 27, 0, -2, 98, 0, 1, 28, 0, -2, 99, 0, 1, 29, 0, -2, 100, 0, 43, 103, 0, 1, 30, 0, -1, 43, 0, -1, 49, 0, 1, 31, 0, 1, 32, 0, -1, 50, 0, 1, 33, 0, -1, 52, 0, 1, 34, 0, -1, 54, 0, 1, 35, 0, -1, 56, 0, 1, 36, 0, 1, 36, 0, -1, 60, 0, -1, 58, 0, 1, 38, 0, -1, 39, 0, 1, 39, 0, -2, 63, 0, 1, 40, 0, 1, 41, 0, -2, 95, 0, 1, 43, 0, -1, 44, 0, -1, 103, 0, 1, 44, 0, 1, 45, 0, 1, 45, 0, 1, 46, 0, -2, 107, 0, 1, 47, 0, -2, 111, 0, 1, 48, 0, -2, 115, 0, -1, 51, 0, -1, 53, 0, -1, 55, 0, -1, 57, 0, -1, 59, 0, 1, 62, 0, 1, 64, 0, -1, 66, 0, -1, 68, 0, -1, 71, 0, -1, 73, 0, -1, 76, 0, -1, 78, 0, -1, 81, 0, -1, 83, 0, -1, 86, 0, -1, 88, 0, 1, 90, 0, -1, 92, 0, -1, 94, 0, 1, 101, 0, -1, 105, 0, 1, 106, 0, 1, 108, 0, -1, 110, 0, 1, 112, 0, -1, 114, 0, 44, 1, 4, 6, 14, 7, 6, 40, 8, 6, 42, 228], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 51, 53, 55, 57, 59, 66, 68, 71, 73, 76, 78, 81, 83, 86, 88, 92, 94, 97, 97, 98, 98, 99, 99, 103, 105, 105, 110, 110, 114, 114], [-1, 4, -1, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, 2, -1, 2, -1, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 3, -1, 2, -1, 2, -1, -1, 4, -1, -2, -3, -1, -1, 4, -1, -2, -3, -1, -1, 4, -1, -2, -3, -1, -1, 4, -1, -2, -3, -1, -1, 4, -1, -2, -3, -1, -1, 2, -1, -1, -1, 3, -1, 2, 4, -1, -2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, -1, 2, -1, 2, -1, -1, 10, -1, 3, 4, -1, 2, -1, -1, 3, 4, -1, 2, -1, -1, 3, 4, -1, 4, -1, -2, -1, -2, -3, -4, -5, -6, -1, -2, -1, -2, -1, -2, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -1, -2, -3, -4, -5, -6, -7, -8, -9, 45, 46, 47, 2, 3, 3, 3, 3, 10, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 3, 2, 7, 8, 7, 8, 7, 8, 3, 9, 2, 9, 2, 9, 2], [0, 13, 13, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 43, 0, 44, 14, 0, 45, 0, 46, 0, 47, 0, 8, 0, 48, 0, 49, 0, 9, 0, 50, 0, 51, 0, 0, 1, 1, 5, 6, 0, 0, 1, 1, 5, 6, 0, 0, 1, 1, 5, 6, 0, 0, 1, 1, 5, 6, 0, 0, 1, 1, 5, 6, 0, 0, 52, 0, 0, 0, 53, 0, 15, 16, 16, 54, 0, 17, 0, 18, 0, 19, 0, 20, 0, 21, 0, 55, 0, 0, 15, 0, 10, 0, 14, 56, 0, 11, 3, 3, 10, 0, 0, 11, 3, 3, 10, 0, 0, 11, 3, 3, 22, 22, 57, 58, 18, 59, 19, 60, 20, 61, 21, 17, 62, 8, 8, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 4, 111, 112, 113, 23, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 2, 2, 2, 2, 126, 7, 4, 7, 4, 7, 4, 7, 4, 7, 4, 9, 23, 127, 128, 129, 130, 131, 132, 9, 133, 12, 24, 12, 24, 12]], [[{"name": "card_11_0", "rect": [1, 0, 70, 96], "offset": [0, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [134]], [[{"name": "card_1_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [135]], [[{"name": "culu", "rect": [0, 0, 83, 34], "offset": [0, 0], "originalSize": [83, 34], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [136]], [[{"name": "card_6_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [137]], [[{"name": "1k_disable", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [138]], [[{"name": "card_6_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [139]], [[{"name": "Sanh2", "rect": [0, 0, 79, 35], "offset": [0, 0], "originalSize": [79, 35], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [140]], [[{"name": "card_10_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [141]], [[{"name": "card_3_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [142]], [[{"name": "card_7_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [143]], [[{"name": "100_disable", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [144]], [[[20, "columnStop", 1, 4, "0", [{"frame": 1, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 100}, {"frame": 0.6666666666666666, "value": -100}, {"frame": 0.6833333333333333, "value": 100}, {"frame": 0.8333333333333334, "value": 60}, {"frame": 1, "value": 100}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -100}, {"frame": 0.35, "value": 100}, {"frame": 0.6833333333333333, "value": 0}, {"frame": 0.8333333333333334, "value": -40}, {"frame": 1, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "card_13_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [145]], [[{"name": "card_1_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [146]], [[{"name": "10k_disable", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [147]], [[{"name": "card_8_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [148]], [[{"name": "card_7_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [149]], [[{"name": "shadows_1", "rect": [0, 0, 640, 182], "offset": [0, 0], "originalSize": [640, 182], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [150]], [[{"name": "card_11_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [151]], [[{"name": "card_5_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [152]], [[{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rect": [0, 0, 249, 43], "offset": [0, 0], "originalSize": [249, 43], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [153]], [[{"name": "card_12_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [154]], [[{"name": "card_3_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [155]], [[{"name": "card_3_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [156]], [[{"name": "10k", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [157]], [[{"name": "sam", "rect": [0, 0, 72, 34], "offset": [0, 0], "originalSize": [72, 34], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [158]], [[{"name": "card_10_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [159]], [[{"name": "card_9_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [160]], [[{"name": "100", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [161]], [[{"name": "card_7_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [162]], [[{"name": "card_3_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [163]], [[{"name": "card_2_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [164]], [[{"name": "card_5_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [165]], [[{"name": "card_5_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [166]], [[{"name": "card_12_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [167]], [[{"name": "card_1_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [168]], [[{"name": "card_8_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [169]], [[{"name": "card_2_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [170]], [[{"name": "card_4_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [171]], [[{"name": "1k", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [172]], [[{"name": "card_10_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [173]], [[{"name": "card_9_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [174]], [[{"name": "tuquy", "rect": [0, 0, 122, 41], "offset": [0, 0], "originalSize": [122, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [175]], [[{"name": "thung", "rect": [0, 0, 97, 41], "offset": [0, 0], "originalSize": [97, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [176]], [[{"name": "card_13_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [177]], [[{"name": "card_2_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [178]], [[{"name": "card_13_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [179]], [[[20, "columnStop2", 0.6833333333333333, 4, "0", [{"frame": 0.6833333333333333, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 100}, {"frame": 0.6666666666666666, "value": -100}, {"frame": 0.6833333333333333, "value": 100}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -100}, {"frame": 0.35, "value": 100}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "card_8_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [180]], [[{"name": "card_11_0", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [181]], [[{"name": "card_4_1", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [182]], [[{"name": "card_12_2", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [183]], [[{"name": "card_2_3", "rect": [0, 0, 71, 96], "offset": [-0.5, 0], "originalSize": [72, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [184]]]]