[1, ["08N/zQIh9HD4WE6wVD0fe8"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "ani_Thag<PERSON>on", "\nani_ThagLon.png\nsize: 991,414\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nTL_ca\n  rotate: false\n  xy: 655, 3\n  size: 334, 269\n  orig: 334, 269\n  offset: 0, 0\n  index: -1\nTL_s\n  rotate: false\n  xy: 2, 117\n  size: 651, 155\n  orig: 651, 155\n  offset: 0, 0\n  index: -1\nTL_text\n  rotate: false\n  xy: 2, 2\n  size: 633, 113\n  orig: 633, 113\n  offset: 0, 0\n  index: -1\nbg_Thag\n  rotate: false\n  xy: 2, 274\n  size: 738, 138\n  orig: 738, 138\n  offset: 0, 0\n  index: -1\n", ["ani_ThagLon.png"], {"skeleton": {"hash": "PlkGxU9nB543Ul2EBW5MIbsgot8", "spine": "3.6.53", "width": 1025.63, "height": 269}, "bones": [{"name": "root"}, {"name": "tam", "parent": "root", "x": 83.64}, {"name": "TL_ca", "parent": "tam", "x": -458.3, "y": 32.93}, {"name": "TL_ca2", "parent": "TL_ca", "x": -54.15, "y": 6.5}, {"name": "TL_ca3", "parent": "TL_ca", "x": 41.15, "y": 16.61}, {"name": "TL_ca4", "parent": "TL_ca", "length": 65.08, "rotation": -130.05, "x": -77.98, "y": 88.08}, {"name": "TL_ca5", "parent": "TL_ca4", "length": 68.05, "rotation": 22.77, "x": 65.63, "y": 0.46}, {"name": "TL_ca6", "parent": "TL_ca5", "length": 23.04, "rotation": 49.48, "x": 67.84, "y": 0.69}, {"name": "TL_ca7", "parent": "TL_ca", "length": 83.9, "rotation": -37.66, "x": 73.64, "y": 93.14}, {"name": "TL_ca8", "parent": "TL_ca7", "length": 90.12, "rotation": -24.13, "x": 83.9}, {"name": "TL_ca9", "parent": "TL_ca8", "length": 40.05, "rotation": -56.16, "x": 90.12}, {"name": "TL_ca10", "parent": "TL_ca", "length": 58.48, "rotation": -122.91, "x": 22.38, "y": -46.93}, {"name": "TL_ca11", "parent": "TL_ca10", "length": 32.2, "rotation": 103.25, "x": 58.48}, {"name": "TL_ca12", "parent": "TL_ca", "length": 44.11, "rotation": -158.9, "x": -21.66, "y": 88.81}, {"name": "bg_Thag", "parent": "tam", "length": 108.4, "rotation": 89.16, "x": -2.11, "y": -51.36}, {"name": "TL_s", "parent": "bg_Thag", "length": 88.62, "rotation": -53.76, "x": 119.66, "y": -318.26}, {"name": "TL_text", "parent": "tam", "length": 283.35, "rotation": -1.54, "x": -117.45, "y": 105.42}], "slots": [{"name": "bg_Thag", "bone": "bg_Thag", "attachment": "bg_Thag"}, {"name": "TL_s", "bone": "TL_s", "attachment": "TL_s"}, {"name": "TL_ca", "bone": "TL_ca", "attachment": "TL_ca"}, {"name": "TL_text", "bone": "TL_text", "attachment": "TL_text"}], "skins": {"default": {"TL_ca": {"TL_ca": {"type": "mesh", "hull": 37, "uvs": [0.59935, 0.08835, 0.70508, 0.16858, 0.74229, 0.14913, 0.8304, 0.20262, 0.93614, 0.40684, 1, 0.6062, 1, 0.83215, 0.94174, 0.93774, 0.85434, 0.89375, 0.84725, 0.80869, 0.90158, 0.75589, 0.82835, 0.67377, 0.6748, 0.70603, 0.51417, 0.84682, 0.46692, 0.92015, 0.59449, 0.94654, 0.57086, 1, 0.36298, 1, 0.31574, 0.90841, 0.39842, 0.80282, 0.31337, 0.80282, 0.26377, 0.69137, 0.25904, 0.64444, 0.14802, 0.65324, 0.10077, 0.6943, 0.12439, 0.77056, 0, 0.82922, 0, 0.54764, 0.04171, 0.38046, 0.09841, 0.25434, 0.2118, 0.16634, 0.20707, 0.09008, 0.25904, 0, 0.31973, 0, 0.36608, 0, 0.46032, 0, 0.54256, 0, 0.28801, 0.04945, 0.31934, 0.06404, 0.36438, 0.08106, 0.41333, 0.06404, 0.84215, 0.28771, 0.3675, 0.03984, 0.42927, 0.03198, 0.32949, 0.03394, 0.23192, 0.4616, 0.25353, 0.55554, 0.30541, 0.58506, 0.36594, 0.57969, 0.4135, 0.57164, 0.42647, 0.52333, 0.45673, 0.56896, 0.54968, 0.57433, 0.26866, 0.4267, 0.34216, 0.44281, 0.41782, 0.44549, 0.5151, 0.41597, 0.57995, 0.33276, 0.32054, 0.16367, 0.29677, 0.21198, 0.23624, 0.24419, 0.20381, 0.30056, 0.22543, 0.35424, 0.31406, 0.66558, 0.43944, 0.63606, 0.63399, 0.45891, 0.71181, 0.3435, 0.45457, 0.78099, 0.56265, 0.69779, 0.63615, 0.63069, 0.72262, 0.50186, 0.75936, 0.3945, 0.7853, 0.34618, 0.71181, 0.2254, 0.69019, 0.2764], "triangles": [43, 34, 35, 44, 33, 34, 42, 34, 43, 44, 34, 42, 37, 32, 33, 37, 33, 44, 38, 37, 44, 40, 42, 43, 42, 38, 44, 39, 42, 40, 39, 38, 42, 31, 32, 37, 58, 38, 39, 58, 30, 31, 31, 37, 58, 58, 37, 38, 59, 30, 58, 0, 35, 36, 43, 35, 40, 35, 57, 40, 40, 58, 39, 40, 57, 58, 57, 59, 58, 56, 59, 57, 59, 62, 60, 59, 54, 62, 54, 53, 62, 59, 55, 54, 56, 55, 59, 45, 62, 53, 54, 45, 53, 16, 14, 15, 17, 14, 16, 64, 68, 67, 19, 64, 67, 20, 63, 19, 13, 67, 68, 13, 68, 12, 13, 19, 67, 14, 19, 13, 18, 19, 14, 17, 18, 14, 10, 5, 6, 6, 8, 9, 6, 9, 10, 7, 8, 6, 11, 4, 5, 11, 70, 4, 10, 11, 5, 73, 1, 2, 73, 2, 3, 74, 0, 1, 74, 1, 73, 4, 41, 3, 73, 3, 41, 72, 74, 73, 57, 0, 74, 72, 66, 74, 57, 74, 66, 73, 41, 72, 71, 66, 72, 72, 41, 4, 0, 57, 35, 65, 57, 66, 65, 66, 71, 70, 65, 71, 4, 71, 72, 4, 70, 71, 26, 27, 24, 26, 24, 25, 28, 45, 27, 23, 27, 45, 24, 27, 23, 60, 30, 59, 29, 30, 60, 61, 29, 60, 62, 61, 60, 28, 29, 61, 28, 61, 62, 45, 28, 62, 56, 57, 65, 52, 56, 65, 52, 50, 56, 69, 65, 70, 52, 65, 69, 69, 70, 11, 68, 52, 69, 12, 69, 11, 68, 69, 12, 68, 64, 52, 54, 46, 45, 54, 47, 46, 48, 47, 54, 22, 46, 47, 23, 45, 46, 23, 46, 22, 63, 47, 48, 22, 47, 63, 21, 22, 63, 20, 21, 63, 50, 55, 56, 52, 51, 50, 54, 50, 48, 50, 54, 55, 49, 50, 51, 50, 49, 48, 64, 49, 51, 52, 64, 51, 48, 64, 63, 64, 48, 49, 19, 63, 64], "vertices": [4, 4, 13.7, 116.29, 0.00095, 5, -119.77, 72.85, 0.00509, 8, -39.16, 19.99, 0.66942, 13, -87.26, -13.58, 0.32455, 2, 8, 1.98, 24.48, 0.99607, 13, -112.43, 19.27, 0.00393, 1, 8, 8.62, 36.22, 1, 1, 8, 40.71, 42.81, 1, 2, 8, 102.23, 20.89, 0.16013, 9, 8.18, 26.56, 0.83987, 1, 9, 65.52, 20.01, 1, 1, 10, 23.37, 19.2, 1, 1, 10, 57.58, 15.33, 1, 1, 10, 60.81, -16.01, 1, 3, 4, 96.5, -77.48, 0, 9, 89.41, -50.7, 0.005, 10, 41.71, -28.82, 0.995, 4, 4, 114.65, -63.28, 0.00602, 9, 85.47, -27.99, 0.25724, 10, 20.66, -19.45, 0.73556, 11, -72.69, 111.88, 0.00118, 5, 4, 90.19, -41.19, 0.14967, 8, 117.6, -57.95, 0.02358, 9, 54.44, -39.1, 0.74091, 10, 12.61, -51.41, 0.05179, 11, -77.95, 79.34, 0.03406, 4, 4, 38.9, -49.87, 0.48599, 8, 82.3, -96.15, 0.03032, 9, 37.85, -88.4, 0.1912, 11, -42.81, 41, 0.2925, 4, 4, -14.75, -87.74, 0.00278, 9, 45.86, -153.58, 0.0006, 11, 18.14, 16.53, 0.99284, 12, 25.33, 35.48, 0.00378, 2, 11, 43.27, 13.99, 0.45262, 12, 17.11, 11.6, 0.54738, 2, 11, 26.09, 53.62, 0, 12, 59.62, 19.24, 1, 2, 11, 42.44, 54.81, 0, 12, 57.03, 3.04, 1, 2, 11, 80.16, -3.48, 0.32368, 12, -8.36, -20.31, 0.67632, 2, 11, 68.05, -30.12, 0.84484, 12, -31.51, -2.42, 0.15516, 3, 2, -12.26, -59.3, 0.13704, 3, 41.89, -65.8, 0.10992, 11, 29.2, -22.36, 0.75304, 3, 2, -40.66, -59.3, 0.2707, 3, 13.49, -65.8, 0.36477, 11, 44.63, -46.21, 0.36452, 5, 2, -57.23, -29.32, 0.2184, 3, -3.08, -35.82, 0.62259, 6, 45.24, 79.66, 0.00534, 7, 45.35, 68.49, 4e-05, 11, 28.46, -76.41, 0.15362, 5, 2, -58.81, -16.69, 0.11117, 3, -4.66, -23.19, 0.78113, 6, 33.66, 74.4, 0.04354, 7, 33.82, 73.88, 0.00151, 11, 18.72, -84.59, 0.06265, 4, 3, -41.74, -25.56, 0.3693, 5, 93.55, 55.23, 0.00451, 6, 46.93, 39.69, 0.51768, 7, 16.07, 41.24, 0.10852, 3, 3, -57.52, -36.6, 0.07269, 6, 62.17, 27.91, 0.36511, 7, 17.01, 22, 0.5622, 3, 3, -49.63, -57.12, 0.00404, 6, 79.41, 41.54, 0.01872, 7, 38.57, 17.74, 0.97724, 2, 6, 106.82, 6.55, 0, 7, 29.79, -25.82, 1, 1, 6, 34.5, -15.95, 1, 2, 5, 60.22, -19.16, 0.89711, 6, -12.59, -16.01, 0.10289, 1, 5, 22.07, -26.5, 1, 2, 5, -20.42, -12.74, 0.71897, 13, 41.06, -40.62, 0.28103, 3, 5, -35.11, -27.15, 0.53161, 8, -142.6, -60.42, 0.00147, 13, 35.14, -60.32, 0.46693, 3, 5, -64.83, -29.45, 0.39744, 8, -143.67, -30.63, 0.0173, 13, 10.22, -76.68, 0.58526, 3, 5, -77.87, -13.94, 0.29841, 8, -127.62, -18.25, 0.04905, 13, -8.69, -69.38, 0.65254, 3, 5, -87.83, -2.09, 0.2008, 8, -115.36, -8.79, 0.10408, 13, -23.13, -63.81, 0.69512, 3, 5, -108.09, 22.01, 0.06562, 8, -90.44, 10.44, 0.29024, 13, -52.49, -52.47, 0.64414, 3, 5, -125.76, 43.04, 0.02353, 8, -68.7, 27.22, 0.4467, 13, -78.12, -42.58, 0.52977, 3, 5, -60.87, -13.49, 0.36877, 8, -127.88, -35.25, 0.02359, 13, 5.99, -60.78, 0.60764, 3, 5, -64.6, -2.95, 0.29523, 8, -117.2, -31.97, 0.04311, 13, -2.36, -53.36, 0.66166, 3, 5, -70.77, 11.51, 0.18444, 8, -102.49, -26.4, 0.08945, 13, -14.75, -43.67, 0.72612, 4, 4, -48.43, 122.83, 0.00025, 5, -84.8, 21.08, 0.10846, 8, -92.35, -12.79, 0.18085, 13, -31.65, -42.05, 0.71044, 2, 8, 57.8, 27.08, 0.99709, 9, -34.9, 14.04, 0.00291, 3, 5, -79.93, 5.17, 0.19359, 8, -108.44, -16.99, 0.10338, 13, -19.71, -53.64, 0.70303, 4, 4, -43.11, 131.45, 7e-05, 5, -94.83, 19.6, 0.09627, 8, -93.4, -2.71, 0.21971, 13, -39.72, -48.18, 0.68395, 3, 5, -72.98, -5.57, 0.27582, 8, -119.46, -23.49, 0.05668, 13, -8.44, -59.69, 0.6675, 6, 2, -67.87, 32.49, 0.00779, 3, -13.72, 25.99, 0.48064, 4, -109.02, 15.88, 9e-05, 5, 36.05, 43.51, 0.17814, 6, -10.62, 51.14, 0.05039, 13, 63.39, 35.9, 0.28296, 5, 3, -6.5, 0.72, 0.9243, 5, 50.75, 65.3, 0.01805, 6, 11.37, 65.54, 0.04131, 7, 12.6, 85.06, 0.00056, 13, 65.75, 62.07, 0.01577, 5, 2, -43.32, -0.72, 0.18933, 3, 10.83, -7.22, 0.77248, 6, 13.8, 84.44, 0.001, 11, -3.1, -80.26, 0.03014, 13, 52.44, 75.72, 0.00705, 4, 2, -23.1, 0.72, 0.56298, 3, 31.05, -5.78, 0.37587, 11, -15.3, -64.08, 0.03098, 13, 33.06, 81.66, 0.03017, 4, 2, -7.22, 2.89, 0.87523, 3, 46.93, -3.61, 0.09991, 11, -25.74, -51.92, 0.0027, 13, 17.47, 85.35, 0.02216, 5, 2, -2.89, 15.88, 0.71214, 3, 51.26, 9.39, 0.08038, 4, -44.04, -0.72, 0.10736, 8, -13.39, -107.92, 0.00145, 13, 8.74, 74.79, 0.09867, 4, 2, 7.22, 3.61, 0.83553, 4, -33.93, -13, 0.13635, 11, -34.19, -40.19, 0.01631, 13, 3.73, 89.88, 0.01182, 4, 2, 38.27, 2.17, 0.14242, 4, -2.89, -14.44, 0.72416, 9, -13.12, -108.48, 0.01235, 11, -49.85, -13.34, 0.12107, 6, 2, -55.59, 41.88, 0.04359, 3, -1.44, 35.38, 0.36238, 4, -96.75, 25.27, 0.0026, 5, 20.97, 46.87, 0.07449, 6, -23.23, 60.07, 0.00625, 13, 48.56, 31.56, 0.51068, 6, 2, -31.05, 37.54, 0.22975, 3, 23.1, 31.05, 0.3027, 4, -72.2, 20.94, 0.03208, 5, 8.49, 68.44, 0.00097, 8, -48.91, -107.97, 0.00058, 13, 27.21, 44.44, 0.43392, 5, 2, -5.78, 36.82, 0.3897, 3, 48.37, 30.32, 0.10675, 4, -46.93, 20.22, 0.16716, 8, -28.47, -93.11, 0.01745, 13, 3.9, 54.22, 0.31895, 5, 2, 26.71, 44.76, 0.13692, 3, 80.86, 38.27, 0.0056, 4, -14.44, 28.16, 0.52114, 8, -7.6, -66.97, 0.14386, 13, -29.27, 58.51, 0.19247, 4, 2, 48.37, 67.15, 0.01733, 4, 7.22, 50.54, 0.3194, 8, -4.13, -36.02, 0.51914, 13, -57.54, 45.42, 0.14413, 3, 5, -44.34, 14.6, 0.22383, 8, -100.5, -52.94, 0.01678, 13, 6.91, -28.21, 0.7594, 3, 5, -29.28, 16.88, 0.22724, 8, -98.85, -68.08, 0.00101, 13, 19, -18.94, 0.77174, 2, 5, -9.64, 6.98, 0.59853, 13, 40.98, -18.14, 0.40147, 3, 3, -23.1, 69.31, 0.00353, 5, 8.93, 8.45, 0.71334, 13, 56.54, -7.89, 0.28313, 5, 2, -70.03, 61.37, 0.00275, 3, -15.88, 54.87, 0.08047, 5, 15.34, 23.27, 0.25742, 6, -37.55, 40.49, 0.00096, 13, 55.01, 8.18, 0.65841, 4, 2, -40.43, -22.38, 0.28619, 3, 13.72, -28.88, 0.56009, 6, 33.63, 93.64, 0.00121, 11, 13.51, -66.07, 0.15251, 4, 2, 1.44, -14.44, 0.71895, 3, 55.59, -20.94, 0.048, 4, -39.71, -31.05, 0.02242, 11, -15.9, -35.23, 0.21062, 5, 4, 25.27, 16.61, 0.62599, 8, 30.9, -51.85, 0.31861, 9, -27.17, -68.99, 0.0386, 11, -91.21, -6.56, 0.00719, 13, -62.16, 83.58, 0.00961, 4, 4, 51.26, 47.65, 0.07803, 8, 32.51, -11.39, 0.91742, 9, -42.25, -31.41, 0.00419, 13, -97.58, 63.98, 0.00036, 3, 2, 6.5, -53.43, 0.07318, 3, 60.65, -59.93, 0.03172, 11, 14.08, -9.81, 0.89509, 5, 2, 42.6, -31.05, 0.05941, 4, 1.44, -47.65, 0.30667, 8, 51.29, -117.28, 0.00027, 9, 18.19, -120.36, 0.03404, 11, -24.32, 8.34, 0.59961, 5, 2, 67.15, -13, 0.00593, 4, 25.99, -29.6, 0.5879, 8, 59.7, -87.99, 0.03765, 9, 13.89, -90.2, 0.1279, 11, -52.81, 19.15, 0.24063, 4, 4, 54.87, 5.05, 0.37726, 8, 61.39, -42.91, 0.36955, 9, -3, -48.37, 0.22143, 11, -97.59, 24.57, 0.03176, 4, 4, 67.15, 33.93, 0.07383, 8, 53.46, -12.55, 0.88582, 9, -22.65, -23.9, 0.0385, 11, -128.51, 19.18, 0.00185, 1, 8, 52.38, 3.03, 1, 1, 8, 13.1, 13.76, 1, 3, 4, 44.04, 65.7, 0.00465, 8, 15.76, -1.52, 0.99494, 13, -97.35, 44.54, 0.00041]}}, "TL_s": {"TL_s": {"x": -180.23, "y": 128.55, "rotation": -35.39, "width": 651, "height": 155}}, "TL_text": {"TL_text": {"x": 117.13, "y": 13.72, "rotation": 1.54, "width": 633, "height": 113}}, "bg_Thag": {"bg_Thag": {"x": 51.39, "y": -1.35, "rotation": -89.16, "width": 738, "height": 138}}}}, "animations": {"animation": {"slots": {"TL_ca": {"attachment": [{"time": 0, "name": "TL_ca"}, {"time": 1.3333, "name": "TL_ca"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": -13.08, "y": 0}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 16.65, "y": -1.19}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 6.64}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 6.64}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca6": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 6.64}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -9.58}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca8": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -9.58}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca9": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -9.58}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca10": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -25.68}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -25.68}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_ca12": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -15.86}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "bg_Thag": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_s": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.51, "y": 24.34}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "TL_text": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.48, "y": 17.76}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}}, "deform": {"default": {"TL_ca": {"TL_ca": [{"time": 0}, {"time": 0.6667, "offset": 146, "vertices": [-9.44673, -14.33289, -15.26696, -7.84796]}, {"time": 1.3333}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]