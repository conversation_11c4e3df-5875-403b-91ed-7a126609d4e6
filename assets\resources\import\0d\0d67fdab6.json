[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "a6BLW8ML9DWqqQhW7u/S55", "e9QAQxLnlD1Z9UkBWg0Wry", "d4xPcPQZNFRacXxr5KXwsO", "31i1IMhRRIjqTqhWc9ovtj", "2cWB/vWPRHja3uQTinHH30", "d73rySVcpNlbP+3lHSBbkB", "40Tzv9VSNLpa6jWUXAKYX2"], ["node", "_N$file", "_spriteFrame", "_parent", "root", "layoutDices", "lbTotalLobster", "lbTotalCrab", "lbTotalFish", "lbTotalRooster", "lbTotalGourd", "lbTotalDeer", "_N$target", "data", "_defaultClip", "itemDice", "_textureSetter"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "node", "_materials", "_N$file"], -3, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["7fea977mi1AvZqkYwZeUuAa", ["node", "lbTotalDeer", "lbTotalGourd", "lbTotalRooster", "lbTotalFish", "lbTotalCrab", "lbTotalLobster", "layoutDices", "itemDice"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$startAxis", "_N$spacingX", "_N$verticalDirection", "_N$horizontalDirection", "node", "_layoutSize"], -3, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["vertical", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3]], [[2, 0, 1, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [4, 0, 1, 5, 2, 3, 4, 6, 7, 7], [0, 0, 6, 3, 2, 4, 7, 2], [0, 0, 5, 3, 2, 4, 2], [0, 0, 5, 6, 2, 2], [3, 1, 6, 1], [6, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 3, 2, 4, 8, 7, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 7, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [2, 1, 1], [1, 3, 4, 5, 1], [1, 0, 3, 4, 5, 2], [1, 2, 0, 1, 3, 4, 5, 4], [1, 0, 1, 3, 4, 5, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 7], [3, 0, 1, 2, 3, 4, 5, 2], [11, 0, 1, 2, 3], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 6, 7, 8, 6]], [[[[7, "BauCuaGraphView"], [8, "BauCuaGraphView", [-11, -12, -13, -14, -15, -16], [[14, -2, [18, 19], 17], [15, -10, -9, -8, -7, -6, -5, -4, -3, 20]], [16, -1]], [3, "bgTop", [-18, -19, -20, -21, -22, -23], [[17, -17, [10], 11]], [0, "8fLmpCJxxG2btN8hlKFwuL", 1], [5, 912, 223], [0, 112, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "layout", [[21, 1, 3, 1, 18, 0, 1, -24, [5, 0, 280]]], [0, "69AFGgkHtMiLPlzD68m9dz", 1], [5, 0, 280], [0, 1, 0.5], [474, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnClose", 1, [-27], [[22, 3, -26, [[23, "7fea977mi1AvZqkYwZeUuAa", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -25]], [0, "aeu1T3VyhNA6snV3ZLrZ1d", 1], [5, 80, 80], [504.254, 330.702, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "black", 100, 1, [[18, 0, -28, [0], 1], [6, -29, [4, 4292269782]]], [0, "db+byOhjdEc53nMX4VYcJf", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nen popup", 1, [[19, 1, 0, false, -30, [2], 3], [6, -31, [4, 4292269782]]], [0, "e19Ecev1dP9L1lJuY9a/3r", 1], [5, 1084, 700]], [3, "scrollview", [-33], [[24, false, 0.75, 0.23, null, null, -32, 3]], [0, "1dtVoAi3VK3r5xjtHx7j3a", 1], [5, 950, 300], [-5, -156, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "view", 7, [3], [[25, 0, -34, [12]]], [0, "bfgo040BJCkooT76z6PE9r", 1], [5, 950, 300]], [5, "top", 1, [2], [0, "7bcrRN9rVMUJIVcEXELV0J", 1]], [1, "lbTotalHuou", 2, [-35], [0, "70tPdeFaJD6KZRFhzvnVJt", 1], [4, 4278584839], [5, 18.75, 50], [-391.625, -52.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6", 30, 50, false, 1, 1, 10, [4]], [1, "lbTotalBau", 2, [-36], [0, "93+DcwWKNFSoW17YEDJB/Z", 1], [4, 4278584839], [5, 18.75, 50], [-223.875, -52.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6", 30, 50, false, 1, 1, 12, [5]], [1, "lbTotalGa", 2, [-37], [0, "feJalvi4dOcqWLJpYqRGz0", 1], [4, 4278584839], [5, 18.75, 50], [-81.125, -52.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6", 30, 50, false, 1, 1, 14, [6]], [1, "lbTotalCa", 2, [-38], [0, "e3XelVC/VFbJTSMHw38Gjv", 1], [4, 4278584839], [5, 18.75, 50], [62.625, -52.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6", 30, 50, false, 1, 1, 16, [7]], [1, "lbTotalCua", 2, [-39], [0, "6fsiJbS5BDsYsMFgO3EB8D", 1], [4, 4278584839], [5, 35.25, 50], [216.375, -52.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "99", 30, 50, false, 1, 1, 18, [8]], [1, "lbTotalTom", 2, [-40], [0, "feE9VDe3xEerKkHF7aJSY7", 1], [4, 4278584839], [5, 18.75, 50], [371.125, -52.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6", 30, 50, false, 1, 1, 20, [9]], [5, "bottom", 1, [7], [0, "d8CYLY6zVI+IYkYLM52il5", 1]], [4, "sprite", 4, [[20, 0, false, -41, [13], 14]], [0, "6fgm1C/ERCBa5G2t9HIune", 1], [5, 70, 40]], [13, "New Label", 1, [[26, "SOI CẦU BẦU CUA", 30, false, 1, 1, -42, [15], 16]], [0, "e0evx3uP1PxodoCxLi0LMy", 1], [5, 445.31, 40], [0, 339.286, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 3, 0, 6, 21, 0, 7, 19, 0, 8, 17, 0, 9, 15, 0, 10, 13, 0, 11, 11, 0, 0, 1, 0, -1, 5, 0, -2, 6, 0, -3, 9, 0, -4, 22, 0, -5, 4, 0, -6, 24, 0, 0, 2, 0, -1, 10, 0, -2, 12, 0, -3, 14, 0, -4, 16, 0, -5, 18, 0, -6, 20, 0, 0, 3, 0, 12, 4, 0, 0, 4, 0, -1, 23, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, -1, 11, 0, -1, 13, 0, -1, 15, 0, -1, 17, 0, -1, 19, 0, -1, 21, 0, 0, 23, 0, 0, 24, 0, 13, 1, 2, 3, 9, 3, 3, 8, 7, 3, 22, 42], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 13, 15, 17, 19, 21], [-1, 2, -1, 2, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, 2, -1, 1, 14, -1, -2, 15, 1, 1, 1, 1, 1, 1], [0, 3, 0, 4, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 6, 0, 7, 2, 2, 8, 9, 1, 1, 1, 1, 1, 1]], [[{"name": "soiCau", "rect": [0, 0, 912, 223], "offset": [-2, 0.5], "originalSize": [916, 224], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [16], [10]]]]