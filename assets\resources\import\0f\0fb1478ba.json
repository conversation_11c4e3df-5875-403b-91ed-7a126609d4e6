[1, ["adw94Z+hpN57wutNivq8Q5", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "2b3NtpYNVJ1rSb2CAsqqBM", "a9VpD0DP5LJYQPXITZq+uj", "ffw9P0ZiFLRb96S+X/Fgwm", "2e2m4PvvdIBIOXRhYAG3+6", "1cIgMKIypJ7Jy+JHaSTGQS", "beL2Don21Kyq3/VR7C09IC", "74qCZlsOtBcqB3v53xd5qm", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_textureSetter", "root", "tqHistoryListView", "lbWin", "lbBet", "lbTime", "lbSessionID", "_N$target", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_skewX", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_spriteFrame"], 0, 1, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 9, 5, 5, 5, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_N$file"], -5, 1, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["e22f11Wz+ZDGJ7iDneu85hA", ["node", "tqHistoryListView"], 3, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["6d26dpUoPdLj5irV5uQDtJE", ["node", "lbSessionID", "lbTime", "lbBet", "lbWin"], 3, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1], ["cc.Layout", ["_enabled", "_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["4f5e48A0DRParwU8Njy+dQq", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[10, 0, 1, 2], [0, 0, 6, 4, 3, 5, 7, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [0, 0, 6, 8, 4, 3, 5, 7, 2], [3, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 6, 4, 5, 2], [1, 2, 0, 1, 3, 4, 4], [1, 3, 4, 1], [13, 0, 1, 2, 3], [6, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 5, 2], [0, 0, 1, 6, 4, 3, 5, 7, 3], [0, 0, 2, 6, 4, 3, 9, 5, 7, 3], [0, 0, 6, 8, 4, 3, 5, 2], [0, 0, 6, 4, 3, 5, 10, 7, 2], [0, 0, 6, 4, 3, 5, 2], [0, 0, 6, 8, 3, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 1], [9, 0, 1, 1], [11, 0, 1, 2, 3, 4, 1], [1, 0, 3, 4, 2], [1, 0, 1, 3, 4, 3], [12, 0, 1], [2, 0, 2, 3, 5, 6, 7, 2], [2, 2, 4, 1], [2, 1, 0, 2, 3, 4, 3], [14, 0, 1, 2], [15, 0, 1, 2, 3, 4, 5, 5], [16, 0, 1, 2, 3, 4, 5, 6, 6], [17, 0, 1, 2, 3, 4, 5, 4]], [[[{"name": "lsc", "rect": [0, 0, 247, 41], "offset": [0, 0], "originalSize": [247, 41], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [3], [3]], [[[10, "tqHistoryView"], [11, "tqHistoryView", [-5, -6, -7, -8, -9, -10], [[20, -2, [13, 14], 12], [21, -4, -3]], [0, "dbhZNN8a1K6ZtT0JSKKDwF", -1]], [12, "item", [-16, -17, -18, -19, -20], [[22, -15, -14, -13, -12, -11]], [0, "84T3M6+MpDCJ5P194C5D3A", 1], [5, 994, 50]], [4, "title", 1, [-22, -23, -24, -25, -26], [[7, 1, 0, false, -21, 9]], [0, "35slQ5lH1EarG6JxCGY+4r", 1], [5, 994, 50], [0, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "scrollview", 1, [-30, -31], [[-27, -28, [25, -29]], 1, 1, 4], [0, "55NIn4B6tI5paKgkT+5ywL", 1], [5, 1050, 465], [0, -68, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnClose", 1, [-34], [[26, 3, -33, [[9, "e22f11Wz+ZDGJ7iDneu85hA", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -32]], [0, "f52uykQEtLS62dcAeFujgy", 1], [5, 80, 80], [546, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[23, 0, -35, 0], [27, -36, [4, 4292269782]]], [0, "afIgttINtAvLEGJnj6ticw", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "colum", 1, [-38], [[8, -37, 3]], [0, "61mikfawJEmKYenwU50tdb", 1], [5, 1137, 75], [0, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "lbDetail", 5, 2, [[2, "<PERSON>em chi tiết", 22, 50, false, false, 1, 1, 1, -39, 11], [28, 1.1, 3, -40, [[9, "6d26dpUoPdLj5irV5uQDtJE", "openDetailClicked", 2]], [4, 4292269782]]], [0, "bcvbdOA/tNmqlgwGXQMtxo", 1], [4, 4278315513], [5, 200, 38], [391, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 4, [-42], [[29, 0, -41]], [0, "dftcfGbaZPPaPSQ6kggHqe", 1], [5, 1050, 465]], [16, "content", 9, [[30, false, 1, 2, 10, -43, [5, 1000, -10]]], [0, "f7G3gw66VCOrbywx6Qp161", 1], [5, 1000, -10], [0, 0.5, 1], [0, 227, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[7, 1, 0, false, -44, 1]], [0, "1111e1yfxP1aRBGHL3XdPl", 1], [5, 1084, 580], [0, -18, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 7, [[8, -45, 2]], [0, "88RxhiZflID7qy4qoKmMIK", 1], [5, 247, 41], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbSession", 3, [[2, "PHIÊN", 22, 50, false, false, 1, 1, 1, -46, 4]], [0, "89pzrY/6RJi41DWHzIjC+V", 1], [5, 150, 30], [-407, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 3, [[2, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, -47, 5]], [0, "cbYh+2lLZO/o/B9Kr/SmZW", 1], [5, 200, 38], [-208, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 3, [[2, "ĐẶT", 22, 50, false, false, 1, 1, 1, -48, 6]], [0, "b82Sciz8ZBabfg2deiVBRY", 1], [5, 200, 38], [3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 3, [[2, "THẮNG", 22, 50, false, false, 1, 1, 1, -49, 7]], [0, "a2OKVDi4RDEqLQxGeSCBbc", 1], [5, 200, 38], [190, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDetail", 3, [[2, "CHI TIẾT", 22, 50, false, false, 1, 1, 1, -50, 8]], [0, "46BxD3m4NERJC8aJueKjDP", 1], [5, 200, 38], [391, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "sprite", 5, [[24, 2, false, -51, 10]], [0, "9f5XiCQ69D+oqlrykiJ3Cm", 1], [5, 66, 67]], [18, "temp", 4, [2], [0, "16+Qr71cxERbz/cqC94yFK", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSession", 2, [-52], [0, "16Z1ovZm9N2bKIFc5hqDv/", 1], [5, 150, 30], [-407, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "#12345678", 22, 50, false, false, 1, 1, 1, 20], [5, "lbTime", 2, [-53], [0, "2byd0GhhVBG4Vm6cO46nH+", 1], [5, 200, 38], [-208, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "14-04-2019 23:44", 22, 50, false, false, 1, 1, 1, 22], [6, "lbBet", 2, [-54], [0, "b0ybGb9i1F4bujbZm3R7pa", 1], [4, 4278315513], [5, 200, 38], [3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "10.000", 22, 50, false, false, 1, 1, 1, 24], [6, "lbWin", 2, [-55], [0, "480EdgSIVCqr8EaGbQaQ9S", 1], [4, 4278315513], [5, 200, 38], [190, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "100.000.000", 22, 50, false, false, 1, 1, 1, 26], [31, false, 0.75, 0.23, null, null, 4, 10], [32, 20, 10, 400, 4, 2, 28]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 29, 0, 0, 1, 0, -1, 6, 0, -2, 11, 0, -3, 7, 0, -4, 3, 0, -5, 5, 0, -6, 4, 0, 6, 27, 0, 7, 25, 0, 8, 23, 0, 9, 21, 0, 0, 2, 0, -1, 20, 0, -2, 22, 0, -3, 24, 0, -4, 26, 0, -5, 8, 0, 0, 3, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 16, 0, -5, 17, 0, -1, 28, 0, -2, 29, 0, 0, 4, 0, -1, 19, 0, -2, 9, 0, 10, 5, 0, 0, 5, 0, -1, 18, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 12, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, -1, 21, 0, -1, 23, 0, -1, 25, 0, -1, 27, 0, 11, 1, 2, 12, 19, 55], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 23, 25, 27], [2, 2, 2, 2, 1, 1, 1, 1, 1, 2, 2, 1, 13, -1, -2, 1, 1, 1, 1], [4, 5, 6, 7, 0, 0, 0, 0, 0, 8, 9, 1, 2, 2, 10, 1, 1, 1, 1]]]]