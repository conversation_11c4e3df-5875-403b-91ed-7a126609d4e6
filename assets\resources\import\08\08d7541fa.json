[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "eevR0424BEsqIqx6GSEXpl", "26SJrbKAJG1bGGAwBc75ju", "a9VpD0DP5LJYQPXITZq+uj", "ceyrb6tuZGl6Fs42cZVBU4", "9ckLI/EN1CG6SDo3Y0td1i", "e1NJ8c5BZHI5JcP4RUdG0s", "1bBqA+nudMWJkFHm+JNxE4", "2cWB/vWPRHja3uQTinHH30", "d5IKXtM/9Cjqhc8duWeQLm"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "_N$content", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint"], 0, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["047601jfqdM77E6SSj77g44", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3]], [[3, 0, 1, 2], [0, 0, 5, 4, 3, 6, 2], [4, 1, 6, 1], [1, 3, 4, 5, 1], [5, 0, 2], [0, 0, 7, 4, 3, 2], [0, 0, 5, 7, 4, 3, 6, 8, 2], [0, 0, 1, 5, 4, 3, 6, 8, 3], [0, 0, 5, 7, 4, 3, 6, 9, 8, 2], [0, 0, 5, 7, 4, 3, 6, 9, 2], [0, 0, 5, 7, 3, 6, 9, 2], [0, 0, 2, 5, 4, 3, 6, 8, 3], [0, 0, 5, 4, 3, 6, 9, 8, 2], [6, 0, 1, 2, 1], [7, 0, 1], [3, 1, 1], [4, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3], [1, 0, 3, 4, 5, 2], [1, 2, 0, 1, 3, 4, 5, 4], [1, 0, 1, 3, 4, 5, 3], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2, 2]], [[[{"name": "HuongDan_baccarat", "rect": [0, 0, 922, 1054], "offset": [0, 0], "originalSize": [922, 1054], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [2]], [[{"name": "title", "rect": [0, 1, 395, 104], "offset": [0, -0.5], "originalSize": [395, 105], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [3]], [[[4, "BacaratHelpView"], [5, "BacaratHelpView", [-4, -5, -6, -7, -8], [[13, -2, [12, 13], 11], [14, -3]], [15, -1]], [6, "btnClose", 1, [-11], [[16, 3, -10, [[17, "047601jfqdM77E6SSj77g44", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -9]], [0, "edYuqjxW1Af7Cx7LGwA2/0", 1], [5, 80, 80], [515.161, 220.96, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "black", 100, 1, [[18, 0, -12, [0], 1], [2, -13, [4, 4292269782]]], [0, "62kek6KtpLoJfKT+AdSPsi", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[19, 1, 0, false, -14, [2], 3], [2, -15, [4, 4292269782]]], [0, "04SHISSB9DZbso8E6x6/Rj", 1], [5, 1100, 618]], [8, "scrollview", 1, [-18], [[21, false, 0.75, 0.23, null, null, -17, -16]], [0, "42Gku0ZPVG2afJQrSatHAN", 1], [5, 1000, 450], [0, 0.5, 1], [0, 177.975, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 5, [-20], [[22, 0, -19, [10]]], [0, "aeCVS5v0FGi5w92d8ZIb0E", 1], [5, 1000, 450], [0, 0.5, 1]], [10, "content", 6, [-21], [0, "bdW77Fn0VD6LcX64hqECnL", 1], [5, 1000, 1230], [0, 0.5, 1]], [11, "title_HD", false, 1, [[3, -22, [4], 5]], [0, "faq41NGoFDXoLFuYz1+P7r", 1], [5, 395, 104], [0, 296, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 2, [[20, 0, false, -23, [6], 7]], [0, "cdwT7WMp9NbLtBEVuhn8qz", 1], [5, 70, 70]], [12, "HuongDan_baccarat", 7, [[3, -24, [8], 9]], [0, "aca2RBUuVAKYYnKGEUW0i3", 1], [5, 922, 1054], [0, 0.5, 1], [0, -24, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 8, 0, -4, 2, 0, -5, 5, 0, 4, 2, 0, 0, 2, 0, -1, 9, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 5, 7, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, -1, 7, 0, -1, 10, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 6, 1, 24], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 7, -1, -2], [0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 1, 1, 9]], [[{"name": "popup_rule", "rect": [0, 0, 1156, 642], "offset": [0, 0], "originalSize": [1156, 642], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [10]]]]