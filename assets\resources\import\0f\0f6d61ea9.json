[1, ["efGXqozGlOSZZ7Xmww0dw1", "20xkjVvY5LD41GTt3ZJy/+"], ["spriteFrame", "_textureSetter"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[[0, "SVN-franko", 32, {"commonHeight": 42, "fontSize": 32, "atlasName": "SVN-franko.png", "fontDefDictionary": {"32": {"xOffset": 0, "yOffset": 36, "xAdvance": 9, "rect": {"x": 458, "y": 241, "width": 0, "height": 0}}, "33": {"xOffset": 3, "yOffset": 13, "xAdvance": 12, "rect": {"x": 468, "y": 191, "width": 6, "height": 23}}, "34": {"xOffset": 3, "yOffset": 13, "xAdvance": 17, "rect": {"x": 251, "y": 241, "width": 12, "height": 11}}, "35": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 327, "y": 191, "width": 16, "height": 23}}, "36": {"xOffset": 0, "yOffset": 10, "xAdvance": 17, "rect": {"x": 183, "y": 41, "width": 17, "height": 30}}, "37": {"xOffset": 1, "yOffset": 12, "xAdvance": 22, "rect": {"x": 2, "y": 138, "width": 21, "height": 25}}, "38": {"xOffset": 0, "yOffset": 13, "xAdvance": 20, "rect": {"x": 395, "y": 138, "width": 20, "height": 24}}, "39": {"xOffset": 2, "yOffset": 13, "xAdvance": 9, "rect": {"x": 230, "y": 241, "width": 5, "height": 11}}, "40": {"xOffset": 2, "yOffset": 13, "xAdvance": 11, "rect": {"x": 350, "y": 108, "width": 7, "height": 26}}, "41": {"xOffset": 1, "yOffset": 13, "xAdvance": 11, "rect": {"x": 492, "y": 108, "width": 7, "height": 26}}, "42": {"xOffset": 1, "yOffset": 13, "xAdvance": 12, "rect": {"x": 237, "y": 241, "width": 12, "height": 11}}, "43": {"xOffset": 2, "yOffset": 20, "xAdvance": 19, "rect": {"x": 440, "y": 216, "width": 17, "height": 17}}, "44": {"xOffset": 1, "yOffset": 30, "xAdvance": 9, "rect": {"x": 265, "y": 241, "width": 7, "height": 11}}, "45": {"xOffset": 1, "yOffset": 26, "xAdvance": 9, "rect": {"x": 382, "y": 241, "width": 8, "height": 5}}, "46": {"xOffset": 2, "yOffset": 30, "xAdvance": 9, "rect": {"x": 374, "y": 241, "width": 6, "height": 6}}, "47": {"xOffset": -2, "yOffset": 13, "xAdvance": 15, "rect": {"x": 75, "y": 138, "width": 14, "height": 24}}, "48": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 286, "y": 138, "width": 16, "height": 24}}, "49": {"xOffset": 3, "yOffset": 13, "xAdvance": 17, "rect": {"x": 199, "y": 191, "width": 13, "height": 23}}, "50": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 437, "y": 138, "width": 16, "height": 24}}, "51": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 125, "y": 138, "width": 17, "height": 24}}, "52": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 38, "y": 191, "width": 17, "height": 23}}, "53": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 57, "y": 138, "width": 16, "height": 24}}, "54": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 38, "y": 138, "width": 17, "height": 24}}, "55": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 2, "y": 191, "width": 15, "height": 23}}, "56": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 321, "y": 138, "width": 17, "height": 24}}, "57": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 471, "y": 138, "width": 17, "height": 24}}, "58": {"xOffset": 2, "yOffset": 20, "xAdvance": 9, "rect": {"x": 432, "y": 216, "width": 6, "height": 17}}, "59": {"xOffset": 1, "yOffset": 20, "xAdvance": 9, "rect": {"x": 90, "y": 216, "width": 7, "height": 22}}, "60": {"xOffset": 2, "yOffset": 20, "xAdvance": 19, "rect": {"x": 396, "y": 216, "width": 17, "height": 17}}, "61": {"xOffset": 2, "yOffset": 22, "xAdvance": 19, "rect": {"x": 287, "y": 241, "width": 17, "height": 11}}, "62": {"xOffset": 2, "yOffset": 20, "xAdvance": 19, "rect": {"x": 345, "y": 216, "width": 17, "height": 17}}, "63": {"xOffset": 0, "yOffset": 13, "xAdvance": 14, "rect": {"x": 164, "y": 138, "width": 14, "height": 24}}, "64": {"xOffset": 1, "yOffset": 13, "xAdvance": 26, "rect": {"x": 2, "y": 165, "width": 24, "height": 24}}, "65": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 291, "y": 191, "width": 18, "height": 23}}, "66": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 366, "y": 191, "width": 17, "height": 23}}, "67": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 376, "y": 138, "width": 17, "height": 24}}, "68": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 111, "y": 165, "width": 17, "height": 23}}, "69": {"xOffset": 1, "yOffset": 13, "xAdvance": 18, "rect": {"x": 94, "y": 165, "width": 15, "height": 23}}, "70": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 147, "y": 165, "width": 14, "height": 23}}, "71": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 144, "y": 138, "width": 18, "height": 24}}, "72": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 19, "y": 191, "width": 17, "height": 23}}, "73": {"xOffset": 2, "yOffset": 13, "xAdvance": 9, "rect": {"x": 480, "y": 165, "width": 6, "height": 23}}, "74": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 490, "y": 138, "width": 10, "height": 24}}, "75": {"xOffset": 1, "yOffset": 13, "xAdvance": 20, "rect": {"x": 2, "y": 216, "width": 18, "height": 23}}, "76": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 163, "y": 165, "width": 14, "height": 23}}, "77": {"xOffset": 1, "yOffset": 13, "xAdvance": 24, "rect": {"x": 70, "y": 165, "width": 22, "height": 23}}, "78": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 196, "y": 165, "width": 17, "height": 23}}, "79": {"xOffset": 0, "yOffset": 13, "xAdvance": 19, "rect": {"x": 231, "y": 138, "width": 18, "height": 24}}, "80": {"xOffset": 1, "yOffset": 13, "xAdvance": 18, "rect": {"x": 412, "y": 165, "width": 17, "height": 23}}, "81": {"xOffset": 0, "yOffset": 13, "xAdvance": 19, "rect": {"x": 245, "y": 76, "width": 18, "height": 29}}, "82": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 215, "y": 165, "width": 17, "height": 23}}, "83": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 180, "y": 138, "width": 17, "height": 24}}, "84": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 326, "y": 165, "width": 17, "height": 23}}, "85": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 107, "y": 138, "width": 16, "height": 24}}, "86": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 234, "y": 165, "width": 18, "height": 23}}, "87": {"xOffset": 0, "yOffset": 13, "xAdvance": 26, "rect": {"x": 401, "y": 191, "width": 26, "height": 23}}, "88": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 429, "y": 191, "width": 18, "height": 23}}, "89": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 162, "y": 191, "width": 18, "height": 23}}, "90": {"xOffset": 0, "yOffset": 13, "xAdvance": 16, "rect": {"x": 253, "y": 191, "width": 16, "height": 23}}, "91": {"xOffset": 2, "yOffset": 13, "xAdvance": 12, "rect": {"x": 359, "y": 108, "width": 8, "height": 26}}, "92": {"xOffset": -2, "yOffset": 13, "xAdvance": 15, "rect": {"x": 455, "y": 138, "width": 14, "height": 24}}, "93": {"xOffset": 1, "yOffset": 13, "xAdvance": 12, "rect": {"x": 369, "y": 108, "width": 8, "height": 26}}, "94": {"xOffset": 2, "yOffset": 13, "xAdvance": 19, "rect": {"x": 213, "y": 241, "width": 15, "height": 12}}, "95": {"xOffset": 0, "yOffset": 38, "xAdvance": 16, "rect": {"x": 430, "y": 241, "width": 16, "height": 2}}, "96": {"xOffset": -1, "yOffset": 14, "xAdvance": 11, "rect": {"x": 402, "y": 241, "width": 8, "height": 5}}, "97": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 415, "y": 216, "width": 15, "height": 17}}, "98": {"xOffset": 2, "yOffset": 13, "xAdvance": 17, "rect": {"x": 385, "y": 191, "width": 14, "height": 23}}, "99": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 329, "y": 216, "width": 14, "height": 17}}, "100": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 75, "y": 191, "width": 14, "height": 23}}, "101": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 380, "y": 216, "width": 14, "height": 17}}, "102": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 25, "y": 138, "width": 11, "height": 24}}, "103": {"xOffset": 0, "yOffset": 19, "xAdvance": 17, "rect": {"x": 91, "y": 191, "width": 17, "height": 23}}, "104": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 311, "y": 191, "width": 14, "height": 23}}, "105": {"xOffset": 1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 22, "y": 216, "width": 6, "height": 23}}, "106": {"xOffset": -1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 445, "y": 76, "width": 8, "height": 28}}, "107": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 182, "y": 191, "width": 15, "height": 23}}, "108": {"xOffset": 1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 110, "y": 191, "width": 6, "height": 23}}, "109": {"xOffset": 1, "yOffset": 20, "xAdvance": 24, "rect": {"x": 459, "y": 216, "width": 22, "height": 17}}, "110": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 87, "y": 241, "width": 14, "height": 17}}, "111": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 70, "y": 241, "width": 15, "height": 17}}, "112": {"xOffset": 2, "yOffset": 20, "xAdvance": 17, "rect": {"x": 240, "y": 216, "width": 14, "height": 22}}, "113": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 207, "y": 216, "width": 14, "height": 22}}, "114": {"xOffset": 1, "yOffset": 20, "xAdvance": 13, "rect": {"x": 57, "y": 241, "width": 11, "height": 17}}, "115": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 364, "y": 216, "width": 14, "height": 17}}, "116": {"xOffset": 0, "yOffset": 14, "xAdvance": 11, "rect": {"x": 134, "y": 191, "width": 11, "height": 23}}, "117": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 2, "y": 241, "width": 14, "height": 17}}, "118": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 103, "y": 241, "width": 14, "height": 17}}, "119": {"xOffset": 0, "yOffset": 20, "xAdvance": 20, "rect": {"x": 35, "y": 241, "width": 20, "height": 17}}, "120": {"xOffset": 0, "yOffset": 20, "xAdvance": 15, "rect": {"x": 18, "y": 241, "width": 15, "height": 17}}, "121": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 276, "y": 216, "width": 14, "height": 21}}, "122": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 483, "y": 216, "width": 13, "height": 17}}, "123": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 456, "y": 108, "width": 11, "height": 26}}, "124": {"xOffset": 2, "yOffset": 13, "xAdvance": 7, "rect": {"x": 225, "y": 138, "width": 4, "height": 24}}, "125": {"xOffset": 0, "yOffset": 13, "xAdvance": 11, "rect": {"x": 420, "y": 108, "width": 11, "height": 26}}, "126": {"xOffset": 2, "yOffset": 25, "xAdvance": 19, "rect": {"x": 357, "y": 241, "width": 15, "height": 6}}, "161": {"xOffset": 3, "yOffset": 21, "xAdvance": 11, "rect": {"x": 49, "y": 216, "width": 4, "height": 22}}, "162": {"xOffset": 2, "yOffset": 17, "xAdvance": 16, "rect": {"x": 147, "y": 191, "width": 13, "height": 23}}, "163": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 251, "y": 138, "width": 17, "height": 24}}, "164": {"xOffset": 1, "yOffset": 19, "xAdvance": 16, "rect": {"x": 166, "y": 241, "width": 13, "height": 13}}, "165": {"xOffset": 1, "yOffset": 13, "xAdvance": 17, "rect": {"x": 234, "y": 191, "width": 17, "height": 23}}, "166": {"xOffset": 2, "yOffset": 14, "xAdvance": 6, "rect": {"x": 55, "y": 216, "width": 3, "height": 22}}, "167": {"xOffset": 2, "yOffset": 14, "xAdvance": 16, "rect": {"x": 336, "y": 108, "width": 12, "height": 27}}, "168": {"xOffset": 0, "yOffset": 36, "xAdvance": 16, "rect": {"x": 454, "y": 241, "width": 0, "height": 0}}, "169": {"xOffset": 1, "yOffset": 13, "xAdvance": 26, "rect": {"x": 28, "y": 165, "width": 24, "height": 24}}, "170": {"xOffset": 0, "yOffset": 14, "xAdvance": 9, "rect": {"x": 318, "y": 241, "width": 9, "height": 10}}, "171": {"xOffset": 1, "yOffset": 23, "xAdvance": 16, "rect": {"x": 181, "y": 241, "width": 14, "height": 13}}, "172": {"xOffset": 1, "yOffset": 24, "xAdvance": 18, "rect": {"x": 329, "y": 241, "width": 17, "height": 9}}, "173": {"xOffset": 1, "yOffset": 28, "xAdvance": 11, "rect": {"x": 450, "y": 241, "width": 0, "height": 0}}, "174": {"xOffset": 1, "yOffset": 13, "xAdvance": 26, "rect": {"x": 199, "y": 138, "width": 24, "height": 24}}, "175": {"xOffset": -1, "yOffset": 17, "xAdvance": 11, "rect": {"x": 418, "y": 241, "width": 10, "height": 2}}, "176": {"xOffset": 1, "yOffset": 14, "xAdvance": 13, "rect": {"x": 274, "y": 241, "width": 11, "height": 11}}, "177": {"xOffset": 1, "yOffset": 17, "xAdvance": 18, "rect": {"x": 292, "y": 216, "width": 16, "height": 20}}, "178": {"xOffset": 0, "yOffset": 15, "xAdvance": 10, "rect": {"x": 119, "y": 241, "width": 10, "height": 14}}, "179": {"xOffset": 0, "yOffset": 15, "xAdvance": 10, "rect": {"x": 131, "y": 241, "width": 9, "height": 14}}, "180": {"xOffset": 1, "yOffset": 14, "xAdvance": 11, "rect": {"x": 392, "y": 241, "width": 8, "height": 5}}, "181": {"xOffset": -2, "yOffset": 22, "xAdvance": 18, "rect": {"x": 256, "y": 216, "width": 18, "height": 21}}, "182": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 142, "y": 108, "width": 16, "height": 27}}, "183": {"xOffset": 2, "yOffset": 24, "xAdvance": 8, "rect": {"x": 412, "y": 241, "width": 4, "height": 4}}, "184": {"xOffset": 1, "yOffset": 36, "xAdvance": 11, "rect": {"x": 348, "y": 241, "width": 7, "height": 7}}, "185": {"xOffset": 2, "yOffset": 15, "xAdvance": 10, "rect": {"x": 142, "y": 241, "width": 6, "height": 14}}, "186": {"xOffset": 1, "yOffset": 14, "xAdvance": 10, "rect": {"x": 306, "y": 241, "width": 10, "height": 10}}, "187": {"xOffset": 1, "yOffset": 23, "xAdvance": 16, "rect": {"x": 197, "y": 241, "width": 14, "height": 13}}, "188": {"xOffset": 0, "yOffset": 36, "xAdvance": 28, "rect": {"x": 456, "y": 241, "width": 0, "height": 0}}, "189": {"xOffset": 0, "yOffset": 36, "xAdvance": 28, "rect": {"x": 448, "y": 241, "width": 0, "height": 0}}, "190": {"xOffset": 0, "yOffset": 36, "xAdvance": 28, "rect": {"x": 452, "y": 241, "width": 0, "height": 0}}, "191": {"xOffset": 1, "yOffset": 21, "xAdvance": 14, "rect": {"x": 60, "y": 216, "width": 12, "height": 22}}, "192": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 443, "y": 41, "width": 18, "height": 30}}, "193": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 42, "y": 76, "width": 18, "height": 30}}, "194": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 2, "y": 76, "width": 18, "height": 30}}, "195": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 463, "y": 41, "width": 18, "height": 30}}, "200": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 483, "y": 41, "width": 15, "height": 30}}, "201": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 371, "y": 41, "width": 15, "height": 30}}, "202": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 228, "y": 76, "width": 15, "height": 30}}, "204": {"xOffset": -1, "yOffset": 7, "xAdvance": 9, "rect": {"x": 282, "y": 41, "width": 9, "height": 30}}, "205": {"xOffset": 2, "yOffset": 7, "xAdvance": 9, "rect": {"x": 186, "y": 76, "width": 9, "height": 30}}, "210": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 333, "y": 41, "width": 18, "height": 30}}, "211": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 406, "y": 41, "width": 18, "height": 30}}, "212": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 262, "y": 41, "width": 18, "height": 30}}, "213": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 293, "y": 41, "width": 18, "height": 30}}, "217": {"xOffset": 1, "yOffset": 7, "xAdvance": 19, "rect": {"x": 388, "y": 41, "width": 16, "height": 30}}, "218": {"xOffset": 1, "yOffset": 7, "xAdvance": 19, "rect": {"x": 353, "y": 41, "width": 16, "height": 30}}, "221": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 313, "y": 41, "width": 18, "height": 30}}, "224": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 270, "y": 165, "width": 15, "height": 23}}, "225": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 179, "y": 165, "width": 15, "height": 23}}, "226": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 130, "y": 165, "width": 15, "height": 23}}, "227": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 304, "y": 138, "width": 15, "height": 24}}, "232": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 254, "y": 165, "width": 14, "height": 23}}, "233": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 448, "y": 165, "width": 14, "height": 23}}, "234": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 54, "y": 165, "width": 14, "height": 23}}, "236": {"xOffset": -2, "yOffset": 14, "xAdvance": 8, "rect": {"x": 298, "y": 165, "width": 9, "height": 23}}, "237": {"xOffset": 1, "yOffset": 14, "xAdvance": 8, "rect": {"x": 287, "y": 165, "width": 9, "height": 23}}, "242": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 345, "y": 165, "width": 15, "height": 23}}, "243": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 379, "y": 165, "width": 15, "height": 23}}, "244": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 431, "y": 165, "width": 15, "height": 23}}, "245": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 340, "y": 138, "width": 15, "height": 24}}, "249": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 396, "y": 165, "width": 14, "height": 23}}, "250": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 464, "y": 165, "width": 14, "height": 23}}, "253": {"xOffset": 0, "yOffset": 14, "xAdvance": 14, "rect": {"x": 286, "y": 108, "width": 14, "height": 27}}, "258": {"xOffset": 0, "yOffset": 8, "xAdvance": 18, "rect": {"x": 472, "y": 76, "width": 18, "height": 28}}, "259": {"xOffset": 1, "yOffset": 15, "xAdvance": 15, "rect": {"x": 223, "y": 216, "width": 15, "height": 22}}, "272": {"xOffset": 0, "yOffset": 13, "xAdvance": 20, "rect": {"x": 345, "y": 191, "width": 19, "height": 23}}, "273": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 57, "y": 191, "width": 16, "height": 23}}, "296": {"xOffset": -1, "yOffset": 7, "xAdvance": 9, "rect": {"x": 214, "y": 76, "width": 12, "height": 30}}, "297": {"xOffset": -2, "yOffset": 13, "xAdvance": 8, "rect": {"x": 488, "y": 165, "width": 12, "height": 23}}, "360": {"xOffset": 1, "yOffset": 7, "xAdvance": 19, "rect": {"x": 222, "y": 41, "width": 16, "height": 30}}, "361": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 270, "y": 138, "width": 14, "height": 24}}, "416": {"xOffset": 0, "yOffset": 9, "xAdvance": 19, "rect": {"x": 18, "y": 108, "width": 20, "height": 28}}, "417": {"xOffset": 1, "yOffset": 17, "xAdvance": 15, "rect": {"x": 310, "y": 216, "width": 17, "height": 20}}, "431": {"xOffset": 1, "yOffset": 8, "xAdvance": 20, "rect": {"x": 282, "y": 76, "width": 20, "height": 29}}, "432": {"xOffset": 1, "yOffset": 15, "xAdvance": 16, "rect": {"x": 99, "y": 216, "width": 18, "height": 22}}, "7840": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 330, "y": 76, "width": 18, "height": 29}}, "7841": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 309, "y": 165, "width": 15, "height": 23}}, "7842": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 459, "y": 2, "width": 18, "height": 33}}, "7843": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 215, "y": 108, "width": 15, "height": 27}}, "7844": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 77, "y": 41, "width": 22, "height": 33}}, "7845": {"xOffset": 1, "yOffset": 11, "xAdvance": 15, "rect": {"x": 433, "y": 108, "width": 21, "height": 26}}, "7846": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 422, "y": 2, "width": 18, "height": 34}}, "7847": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 318, "y": 108, "width": 16, "height": 27}}, "7848": {"xOffset": 0, "yOffset": 0, "xAdvance": 18, "rect": {"x": 41, "y": 2, "width": 20, "height": 36}}, "7849": {"xOffset": 1, "yOffset": 7, "xAdvance": 15, "rect": {"x": 22, "y": 76, "width": 18, "height": 30}}, "7850": {"xOffset": 0, "yOffset": 1, "xAdvance": 18, "rect": {"x": 180, "y": 2, "width": 18, "height": 35}}, "7851": {"xOffset": 1, "yOffset": 8, "xAdvance": 15, "rect": {"x": 455, "y": 76, "width": 15, "height": 28}}, "7852": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 143, "y": 2, "width": 18, "height": 36}}, "7853": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 411, "y": 76, "width": 15, "height": 29}}, "7854": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 280, "y": 2, "width": 18, "height": 34}}, "7855": {"xOffset": 1, "yOffset": 9, "xAdvance": 15, "rect": {"x": 40, "y": 108, "width": 15, "height": 28}}, "7856": {"xOffset": 0, "yOffset": 2, "xAdvance": 18, "rect": {"x": 220, "y": 2, "width": 18, "height": 34}}, "7857": {"xOffset": 1, "yOffset": 9, "xAdvance": 15, "rect": {"x": 492, "y": 76, "width": 15, "height": 28}}, "7858": {"xOffset": 0, "yOffset": 0, "xAdvance": 18, "rect": {"x": 123, "y": 2, "width": 18, "height": 36}}, "7859": {"xOffset": 1, "yOffset": 7, "xAdvance": 15, "rect": {"x": 197, "y": 76, "width": 15, "height": 30}}, "7860": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 300, "y": 2, "width": 18, "height": 34}}, "7861": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 232, "y": 108, "width": 15, "height": 27}}, "7862": {"xOffset": 0, "yOffset": 8, "xAdvance": 18, "rect": {"x": 200, "y": 2, "width": 18, "height": 34}}, "7863": {"xOffset": 1, "yOffset": 15, "xAdvance": 15, "rect": {"x": 57, "y": 108, "width": 15, "height": 28}}, "7864": {"xOffset": 1, "yOffset": 13, "xAdvance": 18, "rect": {"x": 265, "y": 76, "width": 15, "height": 29}}, "7865": {"xOffset": 1, "yOffset": 20, "xAdvance": 15, "rect": {"x": 118, "y": 191, "width": 14, "height": 23}}, "7866": {"xOffset": 1, "yOffset": 3, "xAdvance": 18, "rect": {"x": 442, "y": 2, "width": 15, "height": 33}}, "7867": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 302, "y": 108, "width": 14, "height": 27}}, "7868": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 426, "y": 41, "width": 15, "height": 30}}, "7869": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 91, "y": 138, "width": 14, "height": 24}}, "7870": {"xOffset": 1, "yOffset": 3, "xAdvance": 18, "rect": {"x": 55, "y": 41, "width": 20, "height": 33}}, "7871": {"xOffset": 1, "yOffset": 11, "xAdvance": 15, "rect": {"x": 398, "y": 108, "width": 20, "height": 26}}, "7872": {"xOffset": 1, "yOffset": 2, "xAdvance": 18, "rect": {"x": 342, "y": 2, "width": 16, "height": 34}}, "7873": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 106, "y": 108, "width": 16, "height": 27}}, "7874": {"xOffset": 1, "yOffset": 0, "xAdvance": 18, "rect": {"x": 103, "y": 2, "width": 18, "height": 36}}, "7875": {"xOffset": 1, "yOffset": 7, "xAdvance": 15, "rect": {"x": 62, "y": 76, "width": 18, "height": 30}}, "7876": {"xOffset": 1, "yOffset": 1, "xAdvance": 18, "rect": {"x": 163, "y": 2, "width": 15, "height": 35}}, "7877": {"xOffset": 1, "yOffset": 8, "xAdvance": 15, "rect": {"x": 2, "y": 108, "width": 14, "height": 28}}, "7878": {"xOffset": 1, "yOffset": 7, "xAdvance": 18, "rect": {"x": 24, "y": 2, "width": 15, "height": 36}}, "7879": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 378, "y": 76, "width": 14, "height": 29}}, "7880": {"xOffset": 1, "yOffset": 3, "xAdvance": 9, "rect": {"x": 44, "y": 41, "width": 9, "height": 33}}, "7881": {"xOffset": 0, "yOffset": 10, "xAdvance": 8, "rect": {"x": 501, "y": 108, "width": 8, "height": 26}}, "7882": {"xOffset": 2, "yOffset": 13, "xAdvance": 9, "rect": {"x": 322, "y": 76, "width": 6, "height": 29}}, "7883": {"xOffset": 1, "yOffset": 13, "xAdvance": 8, "rect": {"x": 370, "y": 76, "width": 6, "height": 29}}, "7884": {"xOffset": 0, "yOffset": 13, "xAdvance": 19, "rect": {"x": 104, "y": 76, "width": 18, "height": 30}}, "7885": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 362, "y": 165, "width": 15, "height": 23}}, "7886": {"xOffset": 0, "yOffset": 3, "xAdvance": 19, "rect": {"x": 402, "y": 2, "width": 18, "height": 34}}, "7887": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 269, "y": 108, "width": 15, "height": 27}}, "7888": {"xOffset": 0, "yOffset": 3, "xAdvance": 19, "rect": {"x": 479, "y": 2, "width": 22, "height": 33}}, "7889": {"xOffset": 1, "yOffset": 11, "xAdvance": 16, "rect": {"x": 469, "y": 108, "width": 21, "height": 26}}, "7890": {"xOffset": 0, "yOffset": 2, "xAdvance": 19, "rect": {"x": 382, "y": 2, "width": 18, "height": 34}}, "7891": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 124, "y": 108, "width": 16, "height": 27}}, "7892": {"xOffset": 0, "yOffset": 0, "xAdvance": 19, "rect": {"x": 2, "y": 2, "width": 20, "height": 37}}, "7893": {"xOffset": 1, "yOffset": 7, "xAdvance": 16, "rect": {"x": 202, "y": 41, "width": 18, "height": 30}}, "7894": {"xOffset": 0, "yOffset": 1, "xAdvance": 19, "rect": {"x": 83, "y": 2, "width": 18, "height": 36}}, "7895": {"xOffset": 1, "yOffset": 8, "xAdvance": 16, "rect": {"x": 428, "y": 76, "width": 15, "height": 28}}, "7896": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 63, "y": 2, "width": 18, "height": 36}}, "7897": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 394, "y": 76, "width": 15, "height": 29}}, "7898": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 139, "y": 41, "width": 20, "height": 30}}, "7899": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 30, "y": 216, "width": 17, "height": 23}}, "7900": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 117, "y": 41, "width": 20, "height": 30}}, "7901": {"xOffset": 1, "yOffset": 14, "xAdvance": 15, "rect": {"x": 449, "y": 191, "width": 17, "height": 23}}, "7902": {"xOffset": 0, "yOffset": 3, "xAdvance": 19, "rect": {"x": 258, "y": 2, "width": 20, "height": 34}}, "7903": {"xOffset": 1, "yOffset": 10, "xAdvance": 15, "rect": {"x": 196, "y": 108, "width": 17, "height": 27}}, "7904": {"xOffset": 0, "yOffset": 7, "xAdvance": 19, "rect": {"x": 240, "y": 41, "width": 20, "height": 30}}, "7905": {"xOffset": 1, "yOffset": 13, "xAdvance": 15, "rect": {"x": 357, "y": 138, "width": 17, "height": 24}}, "7906": {"xOffset": 0, "yOffset": 9, "xAdvance": 19, "rect": {"x": 2, "y": 41, "width": 20, "height": 33}}, "7907": {"xOffset": 1, "yOffset": 17, "xAdvance": 15, "rect": {"x": 379, "y": 108, "width": 17, "height": 26}}, "7908": {"xOffset": 1, "yOffset": 13, "xAdvance": 19, "rect": {"x": 304, "y": 76, "width": 16, "height": 29}}, "7909": {"xOffset": 1, "yOffset": 20, "xAdvance": 16, "rect": {"x": 119, "y": 216, "width": 14, "height": 22}}, "7910": {"xOffset": 1, "yOffset": 3, "xAdvance": 19, "rect": {"x": 240, "y": 2, "width": 16, "height": 34}}, "7911": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 90, "y": 108, "width": 14, "height": 27}}, "7912": {"xOffset": 1, "yOffset": 7, "xAdvance": 20, "rect": {"x": 124, "y": 76, "width": 20, "height": 30}}, "7913": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 214, "y": 191, "width": 18, "height": 23}}, "7914": {"xOffset": 1, "yOffset": 7, "xAdvance": 20, "rect": {"x": 82, "y": 76, "width": 20, "height": 30}}, "7915": {"xOffset": 1, "yOffset": 14, "xAdvance": 16, "rect": {"x": 271, "y": 191, "width": 18, "height": 23}}, "7916": {"xOffset": 1, "yOffset": 3, "xAdvance": 20, "rect": {"x": 320, "y": 2, "width": 20, "height": 34}}, "7917": {"xOffset": 1, "yOffset": 10, "xAdvance": 16, "rect": {"x": 249, "y": 108, "width": 18, "height": 27}}, "7918": {"xOffset": 1, "yOffset": 7, "xAdvance": 20, "rect": {"x": 161, "y": 41, "width": 20, "height": 30}}, "7919": {"xOffset": 1, "yOffset": 13, "xAdvance": 16, "rect": {"x": 417, "y": 138, "width": 18, "height": 24}}, "7920": {"xOffset": 1, "yOffset": 8, "xAdvance": 20, "rect": {"x": 360, "y": 2, "width": 20, "height": 34}}, "7921": {"xOffset": 1, "yOffset": 15, "xAdvance": 16, "rect": {"x": 160, "y": 108, "width": 18, "height": 27}}, "7922": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 166, "y": 76, "width": 18, "height": 30}}, "7923": {"xOffset": 0, "yOffset": 14, "xAdvance": 14, "rect": {"x": 180, "y": 108, "width": 14, "height": 27}}, "7924": {"xOffset": 0, "yOffset": 13, "xAdvance": 18, "rect": {"x": 350, "y": 76, "width": 18, "height": 29}}, "7925": {"xOffset": 0, "yOffset": 20, "xAdvance": 14, "rect": {"x": 74, "y": 216, "width": 14, "height": 22}}, "7926": {"xOffset": 0, "yOffset": 3, "xAdvance": 18, "rect": {"x": 24, "y": 41, "width": 18, "height": 33}}, "7927": {"xOffset": 0, "yOffset": 10, "xAdvance": 14, "rect": {"x": 101, "y": 41, "width": 14, "height": 31}}, "7928": {"xOffset": 0, "yOffset": 7, "xAdvance": 18, "rect": {"x": 146, "y": 76, "width": 18, "height": 30}}, "7929": {"xOffset": 0, "yOffset": 13, "xAdvance": 14, "rect": {"x": 74, "y": 108, "width": 14, "height": 28}}, "9679": {"xOffset": 3, "yOffset": 20, "xAdvance": 19, "rect": {"x": 150, "y": 241, "width": 14, "height": 14}}, "9824": {"xOffset": 2, "yOffset": 14, "xAdvance": 24, "rect": {"x": 161, "y": 216, "width": 21, "height": 22}}, "9827": {"xOffset": 1, "yOffset": 15, "xAdvance": 24, "rect": {"x": 135, "y": 216, "width": 24, "height": 22}}, "9829": {"xOffset": 2, "yOffset": 14, "xAdvance": 24, "rect": {"x": 184, "y": 216, "width": 21, "height": 22}}, "9830": {"xOffset": 3, "yOffset": 14, "xAdvance": 24, "rect": {"x": 476, "y": 191, "width": 18, "height": 23}}}, "kerningDict": {}}]], 0, 0, [0], [0], [0]], [[{"name": "SVN-franko", "rect": [2, 2, 507, 256], "offset": [-0.5, 126], "originalSize": [512, 512], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]]]]