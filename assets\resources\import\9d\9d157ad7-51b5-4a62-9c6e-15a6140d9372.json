[1, ["ccX0Hb4ntI7YUkd30s/9FA"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "phong10000", "\nphong10000.png\nsize: 1024,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n17_body\n  rotate: false\n  xy: 2, 188\n  size: 232, 322\n  orig: 232, 322\n  offset: 0, 0\n  index: -1\n17_hair\n  rotate: false\n  xy: 837, 391\n  size: 48, 33\n  orig: 48, 33\n  offset: 0, 0\n  index: -1\n17_hand\n  rotate: true\n  xy: 2, 56\n  size: 130, 216\n  orig: 130, 216\n  offset: 0, 0\n  index: -1\n17_rau1\n  rotate: false\n  xy: 209, 2\n  size: 27, 22\n  orig: 27, 22\n  offset: 0, 0\n  index: -1\n17_rau2\n  rotate: false\n  xy: 524, 214\n  size: 27, 19\n  orig: 27, 19\n  offset: 0, 0\n  index: -1\n17_rau3\n  rotate: false\n  xy: 837, 426\n  size: 67, 84\n  orig: 67, 84\n  offset: 0, 0\n  index: -1\n4_phong\n  rotate: false\n  xy: 236, 235\n  size: 319, 140\n  orig: 319, 140\n  offset: 0, 0\n  index: -1\nlightining2/0\n  rotate: true\n  xy: 236, 162\n  size: 71, 286\n  orig: 71, 286\n  offset: 0, 0\n  index: -1\nlightining2/1\n  rotate: true\n  xy: 566, 313\n  size: 71, 286\n  orig: 71, 286\n  offset: 0, 0\n  index: -1\nlightining2/10\n  rotate: true\n  xy: 220, 53\n  size: 107, 261\n  orig: 107, 261\n  offset: 0, 0\n  index: -1\nlightining2/11\n  rotate: true\n  xy: 2, 18\n  size: 36, 200\n  orig: 36, 200\n  offset: 0, 0\n  index: -1\nlightining2/2\n  rotate: true\n  xy: 557, 240\n  size: 71, 286\n  orig: 71, 286\n  offset: 0, 0\n  index: -1\nlightining2/3\n  rotate: true\n  xy: 566, 386\n  size: 124, 269\n  orig: 124, 269\n  offset: 0, 0\n  index: -1\nlightining2/4\n  rotate: true\n  xy: 483, 58\n  size: 90, 262\n  orig: 90, 262\n  offset: 0, 0\n  index: -1\nlightining2/5\n  rotate: true\n  xy: 747, 74\n  size: 74, 261\n  orig: 74, 261\n  offset: 0, 0\n  index: -1\nlightining2/6\n  rotate: true\n  xy: 204, 26\n  size: 25, 201\n  orig: 25, 201\n  offset: 0, 0\n  index: -1\nlightining2/7\n  rotate: true\n  xy: 557, 150\n  size: 88, 263\n  orig: 88, 263\n  offset: 0, 0\n  index: -1\nlightining2/8\n  rotate: false\n  xy: 906, 248\n  size: 76, 262\n  orig: 76, 262\n  offset: 0, 0\n  index: -1\nlightining2/9\n  rotate: true\n  xy: 2, 2\n  size: 14, 205\n  orig: 14, 205\n  offset: 0, 0\n  index: -1\nmay1\n  rotate: false\n  xy: 236, 377\n  size: 328, 133\n  orig: 328, 133\n  offset: 0, 0\n  index: -1\n", ["phong10000.png"], {"skins": {"default": {"may5": {"may1": {"scaleX": -1, "x": 2.69, "width": 328, "y": 23.88, "height": 133}}, "17_hair": {"17_hair": {"width": 48, "type": "mesh", "hull": 21, "height": 33, "triangles": [14, 13, 28, 28, 13, 11, 28, 11, 10, 11, 13, 12, 15, 14, 27, 27, 10, 9, 27, 14, 10, 14, 28, 10, 7, 25, 26, 7, 26, 8, 26, 9, 8, 25, 15, 26, 26, 27, 9, 26, 15, 27, 5, 25, 6, 7, 6, 25, 25, 16, 15, 4, 24, 5, 5, 24, 25, 4, 3, 24, 25, 24, 16, 24, 23, 16, 24, 2, 23, 24, 3, 2, 23, 1, 22, 23, 2, 1, 23, 17, 16, 23, 22, 17, 22, 18, 17, 1, 0, 22, 0, 21, 22, 22, 21, 18, 0, 20, 21, 21, 19, 18, 21, 20, 19], "uvs": [0, 0.26447, 0.08395, 0.49165, 0.15133, 0.73219, 0.23679, 0.89399, 0.42053, 0.99326, 0.64628, 1, 0.84317, 0.91391, 0.96917, 0.765, 1, 0.59318, 1, 0.34691, 0.91798, 0.12355, 0.83923, 0, 0.63842, 0, 0.74473, 0.14646, 0.79198, 0.25527, 0.74079, 0.38127, 0.57542, 0.30682, 0.39429, 0.20373, 0.29192, 0.10064, 0.17773, 0, 0, 0, 0.07187, 0.16946, 0.20967, 0.36992, 0.35142, 0.5131, 0.50105, 0.59902, 0.68216, 0.58757, 0.85936, 0.51885, 0.87118, 0.32412, 0.81605, 0.11793], "vertices": [2, 10, 4.87, -5.69, 0.992, 11, -9.87, -5.1, 0.008, 2, 10, 13.02, -8.14, 0.49885, 11, -1.88, -8.04, 0.50115, 3, 10, 20.92, -11.47, 0.02707, 11, 5.8, -11.84, 0.86965, 12, -11.17, -7.53, 0.10329, 2, 11, 12.41, -13.11, 0.61267, 12, -5.89, -11.71, 0.38733, 3, 11, 21.19, -9.71, 0.11992, 12, 3.46, -12.75, 0.87941, 13, -14.6, -4.75, 0.00068, 2, 12, 14.02, -10.34, 0.68882, 13, -4.8, -9.38, 0.31118, 3, 12, 22.5, -5.29, 0.06603, 13, 4.99, -10.64, 0.86423, 14, -9.84, -5.47, 0.06974, 2, 13, 12.52, -8.63, 0.47418, 14, -2.9, -9, 0.52582, 2, 13, 16.18, -4.06, 0.08146, 14, 2.89, -8.04, 0.91854, 2, 14, 10.31, -4.74, 0.56315, 15, -2.26, -4.68, 0.43685, 2, 15, 6.09, -4.35, 0.83873, 16, -1.86, -4.07, 0.16127, 2, 15, 11.41, -2.72, 0.05221, 16, 3.67, -3.46, 0.94779, 1, 16, 9.4, 4.28, 1, 2, 15, 9.03, 3.47, 0.04589, 16, 2.48, 3.06, 0.95411, 6, 11, 18.38, 20.34, 0.00019, 12, 14.85, 15.2, 0.00594, 13, 11.62, 10.2, 0.01867, 14, 9.02, 5.61, 0.08648, 15, 4.81, 2.99, 0.77572, 16, -1.75, 3.38, 0.11299, 5, 11, 19.3, 15.6, 0.01606, 12, 13.47, 10.57, 0.14754, 13, 7.68, 7.41, 0.29676, 14, 4.22, 6.16, 0.35231, 15, 2.14, 7.01, 0.18732, 5, 11, 11.73, 12.17, 0.28766, 12, 5.18, 11.03, 0.54288, 13, 1.44, 12.89, 0.13651, 14, 3.24, 14.42, 0.02685, 15, 7.81, 13.09, 0.0061, 4, 10, 16.83, 9.11, 0.18041, 11, 2.96, 8.95, 0.70334, 12, -4.08, 12.22, 0.11145, 13, -5.11, 19.55, 0.00481, 3, 10, 10.95, 8.04, 0.7362, 11, -2.97, 8.24, 0.25706, 12, -9.68, 14.33, 0.00674, 2, 10, 4.73, 6.51, 0.99361, 11, -9.28, 7.09, 0.00639, 1, 10, -1.3, 0.48, 1, 1, 10, 5.09, -1.03, 1, 2, 10, 14.45, -1.03, 0.48325, 11, -0.03, -1.03, 0.51675, 3, 11, 8.2, -0.06, 0.99997, 12, -3.6, 1.82, 3e-05, 13, -11.16, 11.07, 0, 4, 11, 15.45, 2.59, 0.01885, 12, 4.05, 0.81, 0.97793, 13, -5.76, 5.55, 0.00305, 14, -7.02, 13.76, 0.00017, 5, 11, 21.71, 8.64, 0.00534, 12, 12.39, 3.28, 0.26408, 13, 2.33, 2.34, 0.70664, 14, -3.14, 5.97, 0.01989, 15, -2.76, 12.51, 0.00404, 1, 14, 2.38, -0.88, 1, 5, 11, 22.73, 21.16, 2e-05, 12, 19.09, 13.92, 0.00076, 13, 14.16, 6.57, 0.00265, 14, 8.49, 1.21, 0.01555, 15, 1.11, 0.56, 0.98101, 1, 16, 1.2, -0.25, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40]}}, "17_body": {"17_body": {"width": 232, "type": "mesh", "hull": 27, "height": 322, "triangles": [55, 54, 65, 56, 44, 55, 57, 43, 56, 8, 65, 9, 66, 55, 65, 56, 55, 66, 57, 56, 66, 58, 57, 66, 42, 57, 58, 59, 58, 66, 8, 66, 65, 7, 66, 8, 59, 66, 7, 59, 42, 58, 59, 6, 42, 6, 59, 7, 48, 50, 51, 55, 44, 54, 63, 11, 10, 51, 50, 63, 52, 48, 51, 53, 52, 51, 64, 63, 10, 51, 63, 64, 53, 51, 64, 54, 53, 64, 64, 10, 9, 45, 52, 53, 45, 53, 54, 65, 54, 64, 65, 64, 9, 50, 48, 49, 61, 14, 13, 61, 13, 12, 62, 61, 12, 62, 12, 11, 49, 61, 62, 50, 49, 62, 63, 62, 11, 50, 62, 63, 60, 37, 15, 60, 15, 14, 36, 37, 60, 67, 36, 60, 60, 14, 61, 60, 49, 68, 49, 60, 61, 41, 18, 17, 40, 41, 17, 41, 40, 19, 15, 17, 16, 37, 40, 17, 37, 39, 40, 39, 36, 38, 39, 34, 40, 37, 17, 15, 19, 40, 34, 41, 19, 18, 39, 35, 34, 20, 19, 34, 21, 20, 34, 37, 36, 39, 38, 35, 39, 33, 35, 38, 33, 38, 36, 76, 21, 34, 32, 35, 33, 22, 21, 76, 75, 76, 31, 35, 76, 34, 35, 31, 76, 35, 32, 31, 32, 33, 36, 67, 32, 36, 67, 31, 32, 68, 31, 67, 30, 75, 31, 30, 31, 68, 22, 75, 23, 75, 22, 76, 74, 23, 75, 68, 67, 60, 49, 48, 68, 42, 71, 43, 56, 43, 44, 43, 45, 44, 54, 44, 45, 47, 48, 52, 46, 47, 52, 45, 46, 52, 74, 75, 30, 48, 69, 68, 30, 68, 69, 24, 23, 74, 47, 69, 48, 29, 74, 30, 29, 30, 69, 25, 24, 74, 70, 69, 47, 29, 69, 70, 73, 74, 29, 25, 74, 73, 70, 47, 46, 28, 73, 29, 28, 29, 70, 71, 70, 46, 28, 70, 71, 72, 73, 28, 71, 46, 43, 25, 72, 26, 73, 72, 25, 27, 28, 71, 72, 28, 27, 43, 46, 45, 43, 57, 42, 42, 6, 5, 26, 72, 0, 2, 1, 0, 72, 2, 0, 2, 72, 27, 3, 2, 27, 27, 71, 4, 3, 27, 4, 42, 4, 71, 4, 42, 5], "uvs": [0.00194, 0.8798, 0.00344, 0.96824, 0.18956, 1, 0.35302, 1, 0.56811, 1, 0.66279, 1, 0.70576, 1, 0.82836, 0.94046, 0.92945, 0.84593, 1, 0.7204, 1, 0.56698, 0.94021, 0.45076, 0.89074, 0.32368, 0.80685, 0.23535, 0.70361, 0.19816, 0.64124, 0.18421, 0.69501, 0.07263, 0.57886, 0, 0.40034, 0, 0.21322, 0, 0.15299, 0.08038, 0.14224, 0.17956, 0.05405, 0.29114, 0, 0.44146, 0.00028, 0.57008, 0.06911, 0.63207, 0, 0.76534, 0.35947, 0.89552, 0.34872, 0.7576, 0.35087, 0.62742, 0.36163, 0.52049, 0.37023, 0.39497, 0.38959, 0.30509, 0.40249, 0.25085, 0.25956, 0.22116, 0.3208, 0.24323, 0.47581, 0.24116, 0.54854, 0.20669, 0.40309, 0.206, 0.40309, 0.16049, 0.40787, 0.09155, 0.40213, 0.0357, 0.66279, 0.9149, 0.63268, 0.78717, 0.67748, 0.76075, 0.65712, 0.72554, 0.60905, 0.67623, 0.60905, 0.62165, 0.61638, 0.57176, 0.67341, 0.52304, 0.70681, 0.55473, 0.71007, 0.59347, 0.68074, 0.63162, 0.70925, 0.66567, 0.72636, 0.71086, 0.72229, 0.75841, 0.67585, 0.79186, 0.65052, 0.83844, 0.67452, 0.90473, 0.73677, 0.91919, 0.61325, 0.3435, 0.71433, 0.3872, 0.76054, 0.46626, 0.78942, 0.57654, 0.79519, 0.67642, 0.78364, 0.78254, 0.75187, 0.84912, 0.49321, 0.36235, 0.48298, 0.45676, 0.48298, 0.55265, 0.48503, 0.65148, 0.49936, 0.77687, 0.22091, 0.78425, 0.19839, 0.65591, 0.19224, 0.532, 0.21272, 0.39186, 0.2085, 0.31683], "vertices": [3, 2, -85.63, -19.04, 0.75229, 3, -36.28, 84.78, 0.24749, 4, -120.55, 94.74, 0.00022, 2, 2, -85.28, -47.51, 0.86326, 3, -64.68, 82.61, 0.13674, 2, 2, -42.1, -57.74, 0.9616, 3, -72.12, 38.86, 0.0384, 1, 2, -4.18, -57.74, 1, 1, 2, 45.72, -57.74, 1, 1, 2, 67.69, -57.74, 1, 2, 2, 77.66, -57.74, 0.14637, 9, 81.2, 13.56, 0.85363, 1, 9, 52.28, 32, 1, 2, 8, 96.49, 28.93, 0.14998, 9, 15.01, 41.37, 0.85002, 3, 7, 140.9, 39.27, 0.00456, 8, 57.57, 48.6, 0.85307, 9, -28.58, 40.27, 0.14238, 2, 7, 92.87, 50.81, 0.3184, 8, 8.34, 52.7, 0.6816, 2, 7, 53.24, 46.08, 0.8898, 8, -30.11, 41.98, 0.1102, 2, 6, 64.72, 15.43, 0.05469, 7, 10.77, 44.48, 0.94531, 3, 5, 6.1, -95.13, 0.01507, 6, 36.77, 35.6, 0.56048, 7, -21.43, 32.21, 0.42445, 3, 5, 17.53, -70.91, 0.13935, 6, 10.18, 38.76, 0.81426, 7, -38.68, 11.72, 0.0464, 4, 4, 101.03, -57.14, 0.00013, 5, 21.7, -56.35, 0.38661, 6, -4.96, 38.08, 0.61252, 7, -46.43, -1.3, 0.00074, 2, 5, 57.9, -68.01, 0.73721, 6, -5.39, 76.11, 0.26279, 2, 5, 80.68, -40.55, 0.86, 6, -38.67, 88.99, 0.14, 2, 5, 79.74, 0.86, 0.99419, 6, -77.63, 74.96, 0.00581, 2, 4, 161.92, 41.2, 4e-05, 5, 78.77, 44.26, 0.99996, 2, 4, 136.27, 55.58, 0.03203, 5, 52.58, 57.65, 0.96797, 3, 3, 190.81, 66.76, 0.0061, 4, 104.38, 58.59, 0.27587, 5, 20.6, 59.43, 0.71802, 3, 3, 153.65, 84.88, 0.08357, 4, 68.78, 79.62, 0.68034, 5, -15.78, 79.07, 0.23609, 4, 2, -86.08, 122.11, 0, 3, 104.54, 94.28, 0.35123, 4, 20.58, 92.93, 0.62006, 5, -64.46, 90.52, 0.0287, 4, 2, -86.01, 80.69, 0.01371, 3, 63.21, 91.56, 0.60698, 4, -20.83, 93.53, 0.37877, 5, -105.86, 89.53, 0.00055, 3, 2, -70.05, 60.73, 0.084, 3, 44.32, 74.35, 0.72865, 4, -41.04, 77.88, 0.18735, 3, 2, -86.08, 17.82, 0.4548, 3, 0.46, 87.59, 0.53003, 4, -83.69, 94.6, 0.01517, 1, 2, -2.68, -24.1, 1, 3, 2, -5.18, 20.31, 0.02738, 3, 8.14, 7.02, 0.97252, 4, -82.49, 13.67, 0.0001, 3, 2, -4.68, 62.23, 0.00031, 3, 50.01, 9.21, 0.99193, 4, -40.59, 12.5, 0.00776, 2, 3, 84.53, 8.93, 0.8148, 4, -6.2, 9.45, 0.1852, 3, 3, 124.99, 9.53, 0.00501, 4, 34.18, 6.81, 0.99475, 5, -47.56, 4.99, 0.00024, 3, 3, 154.16, 6.9, 7e-05, 4, 63.04, 1.86, 0.99765, 5, -18.52, 1.15, 0.00229, 3, 4, 80.46, -1.41, 0.61818, 5, -1, -1.45, 0.37376, 6, -49.81, -0.87, 0.00806, 3, 3, 179.19, 38.74, 0.00324, 4, 90.55, 31.59, 0.32429, 5, 7.81, 31.91, 0.67246, 3, 3, 173.02, 24.11, 0.00082, 4, 83.22, 17.5, 0.41746, 5, 1.03, 17.55, 0.58172, 3, 4, 83.31, -18.47, 0.29175, 5, 2.51, -18.39, 0.45536, 6, -34.86, 7.83, 0.25289, 3, 4, 94.14, -35.52, 0.04098, 5, 13.98, -35.01, 0.47647, 6, -22.74, 23.99, 0.48255, 2, 5, 13.45, -1.27, 0.99481, 6, -54.57, 12.77, 0.00519, 2, 5, 28.09, -0.94, 0.99701, 6, -59.53, 26.55, 0.00299, 2, 5, 50.31, -1.55, 0.99739, 6, -66, 47.82, 0.00261, 2, 5, 68.26, 0.19, 0.9956, 6, -73.35, 64.29, 0.0044, 2, 2, 67.69, -30.34, 0.95281, 3, -37.73, -68.95, 0.04719, 2, 2, 60.7, 10.79, 0.26727, 3, 2.87, -59.34, 0.73273, 2, 2, 71.1, 19.3, 0.12621, 3, 12.02, -69.16, 0.87379, 2, 2, 66.37, 30.64, 0.04995, 3, 23.04, -63.72, 0.95005, 2, 2, 55.22, 46.51, 0.00594, 3, 38.16, -51.57, 0.99406, 2, 3, 55.71, -50.45, 0.98368, 4, -39.69, -47.42, 0.01632, 3, 3, 71.85, -51.11, 0.89757, 4, -23.65, -49.38, 0.10117, 6, 31.88, -81.28, 0.00126, 2, 6, 39.02, -62.04, 0.696, 7, 61.4, -19.55, 0.304, 2, 7, 73.13, -14.4, 0.63165, 8, -1.24, -14.76, 0.36835, 3, 7, 85.44, -16.58, 0.21047, 8, 11.25, -15.04, 0.78724, 9, -39.24, -37.72, 0.00229, 3, 7, 95.79, -26.07, 0.10275, 8, 22.93, -22.84, 0.86929, 9, -25.27, -39.06, 0.02796, 3, 7, 107.99, -22.2, 0.01858, 8, 34.4, -17.16, 0.93223, 9, -17.85, -28.62, 0.04919, 3, 7, 123.07, -21.74, 0.00073, 8, 49.24, -14.41, 0.83855, 9, -6.09, -19.18, 0.16072, 2, 8, 64.41, -16.63, 0.30693, 9, 8.33, -13.94, 0.69307, 2, 8, 74.26, -28.26, 0.12308, 9, 22.5, -19.52, 0.87692, 2, 8, 88.71, -35.36, 0.02803, 9, 38.6, -18.93, 0.97197, 1, 9, 55.96, -5.32, 1, 1, 9, 54.47, 9.79, 1, 3, 3, 145.15, -45.67, 0.00027, 4, 49.85, -49.83, 0.13429, 6, 6.31, -12.38, 0.86544, 1, 7, 21.09, -0.09, 1, 1, 7, 48.35, 4.38, 1, 2, 7, 84.44, 2.59, 0.02518, 8, 7.35, 3.76, 0.97482, 1, 8, 39.51, 2.42, 1, 1, 9, 9.78, 2.21, 1, 1, 9, 32.38, 4, 1, 2, 4, 44.22, -21.89, 0.73897, 6, -17.84, -27.52, 0.26103, 3, 3, 106.81, -17.85, 0.11156, 4, 13.86, -19.02, 0.84065, 6, -9.78, -56.93, 0.0478, 3, 3, 76, -19.83, 0.91345, 4, -17.01, -18.53, 0.08506, 6, 0.68, -85.98, 0.00149, 1, 3, 44.27, -22.35, 1, 2, 2, 29.77, 14.11, 0.21574, 3, 4.19, -28.26, 0.78426, 3, 2, -34.83, 11.73, 0.43031, 3, -2.32, 36.06, 0.56564, 4, -90.6, 43.45, 0.00405, 3, 2, -40.05, 53.06, 0.07131, 3, 38.58, 43.92, 0.84691, 4, -49.2, 48.02, 0.08178, 4, 2, -41.48, 92.96, 0.00351, 3, 78.31, 47.9, 0.61026, 4, -9.28, 48.8, 0.38549, 5, -92.6, 45.28, 0.00073, 3, 3, 123.65, 46.06, 0.13772, 4, 35.76, 43.33, 0.8125, 5, -47.38, 41.54, 0.04979, 3, 3, 147.69, 48.59, 0.0489, 4, 59.94, 43.93, 0.75369, 5, -23.25, 43.07, 0.1974], "edges": [2, 4, 4, 6, 6, 8, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 2, 0, 0, 52, 6, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 42, 68, 68, 70, 70, 66, 66, 72, 72, 74, 74, 30, 8, 10, 10, 12, 10, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 14]}}, "may4": {"may1": {"x": -48.15, "width": 328, "y": -0.6, "height": 133}}, "17_rau3": {"17_rau3": {"width": 67, "type": "mesh", "hull": 18, "height": 84, "triangles": [7, 23, 8, 23, 9, 8, 23, 7, 22, 7, 6, 22, 6, 5, 22, 23, 22, 9, 9, 22, 10, 5, 21, 22, 22, 21, 10, 5, 20, 21, 11, 10, 12, 12, 10, 21, 20, 5, 4, 3, 20, 4, 14, 20, 15, 21, 20, 12, 14, 12, 20, 14, 13, 12, 19, 20, 3, 19, 3, 1, 16, 15, 19, 15, 20, 19, 1, 18, 19, 1, 0, 18, 3, 2, 1, 16, 18, 17, 16, 19, 18, 18, 0, 17], "uvs": [0.28763, 0, 0.1333, 0.11043, 0, 0.21394, 0, 0.41257, 0.04562, 0.63078, 0.19644, 0.77626, 0.34375, 0.93293, 0.52263, 1, 0.7892, 1, 0.7892, 0.88537, 0.85233, 0.75388, 0.98211, 0.614, 1, 0.50209, 1, 0.379, 0.90144, 0.22793, 0.73659, 0.10483, 0.57174, 0.07406, 0.49808, 0, 0.38245, 0.10204, 0.40701, 0.2671, 0.42806, 0.44614, 0.48067, 0.6168, 0.53679, 0.78466, 0.62447, 0.92734], "vertices": [1, 29, -1.57, -6.88, 1, 4, 29, 7.34, -17.54, 0.88479, 30, -11.72, -16.66, 0.10584, 31, -28.1, -16.58, 0.00589, 32, -46.03, -9.67, 0.00347, 4, 29, 15.71, -26.77, 0.6191, 30, -4.1, -26.53, 0.29747, 31, -20.52, -26.47, 0.05678, 32, -40.04, -20.6, 0.02665, 4, 29, 32.38, -27.36, 0.23379, 30, 12.47, -28.44, 0.35677, 31, -3.95, -28.43, 0.26144, 32, -23.97, -25.07, 0.14799, 5, 29, 50.81, -24.95, 0.0281, 30, 31.03, -27.51, 0.11191, 31, 14.62, -27.55, 0.36988, 32, -5.49, -27.03, 0.4847, 33, -25.32, -23.06, 0.00541, 5, 29, 63.38, -15.28, 0.00031, 30, 44.33, -18.87, 0.00792, 31, 27.94, -18.95, 0.12198, 32, 8.99, -20.56, 0.70313, 33, -9.95, -19.15, 0.16666, 3, 31, 42.17, -10.7, 2e-05, 32, 24.31, -14.58, 0.17524, 33, 6.16, -15.86, 0.82474, 1, 33, 16.38, -7.43, 1, 2, 32, 37.73, 12.67, 0.01121, 33, 24.02, 8.71, 0.98879, 4, 30, 57.99, 19.53, 5e-05, 31, 41.71, 19.41, 0.00759, 32, 28.45, 15.25, 0.15086, 33, 15.31, 12.83, 0.84151, 4, 30, 47.5, 25, 0.0127, 31, 31.24, 24.91, 0.10975, 32, 18.94, 22.28, 0.52879, 33, 7.14, 21.38, 0.34876, 5, 29, 51.62, 37.8, 0.00313, 30, 36.82, 34.99, 0.07764, 31, 20.6, 34.92, 0.30441, 32, 9.95, 33.8, 0.54513, 33, 0.23, 34.26, 0.06969, 5, 29, 42.26, 39.33, 0.01872, 30, 27.62, 37.25, 0.14334, 31, 11.4, 37.22, 0.37584, 32, 1.21, 37.47, 0.44119, 33, -7.75, 39.36, 0.02091, 5, 29, 31.93, 39.7, 0.05816, 30, 17.35, 38.44, 0.22947, 31, 1.13, 38.44, 0.39753, 32, -8.75, 40.24, 0.31304, 33, -17.1, 43.79, 0.0018, 4, 29, 19.01, 33.55, 0.18474, 30, 3.99, 33.33, 0.33662, 31, -12.24, 33.37, 0.3222, 32, -22.75, 37.28, 0.15644, 4, 29, 8.29, 22.87, 0.47661, 30, -7.55, 23.55, 0.31438, 31, -23.81, 23.62, 0.16006, 32, -35.66, 29.4, 0.04895, 4, 29, 5.32, 11.93, 0.83941, 30, -11.39, 12.87, 0.11166, 31, -27.68, 12.95, 0.04016, 32, -41.11, 19.45, 0.00876, 4, 29, -1.07, 7.21, 0.98917, 30, -18.13, 8.68, 0.00631, 31, -34.44, 8.79, 0.00411, 32, -48.43, 16.36, 0.00041, 1, 29, 7.22, -0.83, 1, 2, 30, 3.46, 0.05, 0.99998, 31, -12.87, 0.09, 2e-05, 3, 29, 36.21, 1.2, 0.00019, 31, 2.23, -0.28, 0.99923, 32, -13.57, 1.81, 0.00058, 2, 31, 16.88, 1.53, 0.04766, 32, 1.18, 1.37, 0.95234, 2, 32, 15.77, 1.22, 0.38201, 33, 0.43, 1.16, 0.61799, 2, 32, 28.89, 3.67, 0.00569, 33, 13.78, 1.35, 0.99431], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34]}}, "may3": {"may1": {"x": -48.15, "width": 328, "y": -0.6, "height": 133}}, "17_rau2": {"17_rau2": {"width": 27, "type": "mesh", "hull": 20, "height": 19, "triangles": [10, 9, 8, 24, 10, 8, 24, 8, 7, 11, 10, 24, 23, 24, 7, 23, 7, 22, 12, 11, 24, 12, 24, 23, 13, 12, 23, 22, 13, 23, 22, 14, 13, 16, 15, 22, 15, 14, 22, 6, 5, 21, 22, 6, 21, 7, 6, 22, 18, 22, 21, 17, 22, 18, 17, 16, 22, 20, 4, 3, 20, 3, 2, 5, 4, 20, 21, 5, 20, 20, 2, 1, 1, 19, 20, 19, 21, 20, 1, 0, 19, 18, 21, 19], "uvs": [1, 0.44991, 1, 0.20192, 0.89049, 0.06813, 0.76878, 0, 0.58278, 0, 0.48175, 0.13665, 0.37842, 0.26391, 0.26361, 0.32265, 0.15568, 0.22476, 0.06383, 0.07465, 0, 0.16276, 0, 0.38465, 0.04546, 0.62286, 0.12354, 0.83496, 0.21539, 0.9818, 0.40827, 1, 0.5736, 1, 0.73664, 0.83496, 0.80553, 0.59675, 0.90656, 0.48254, 0.71385, 0.19829, 0.55769, 0.4006, 0.39695, 0.57682, 0.23852, 0.57028, 0.08927, 0.37124], "vertices": [2, 25, 0.82, 8.12, 0.99164, 26, -6.05, 9.55, 0.00836, 1, 25, -2.34, 4.62, 1, 1, 25, -1.85, 0.76, 1, 1, 25, -0.28, -2.41, 1, 3, 25, 3.45, -5.77, 0.97071, 26, -6.31, -4.59, 0.0278, 28, 6.75, -15.09, 0.00149, 4, 25, 7.21, -5.67, 0.69809, 26, -2.6, -5.26, 0.26492, 27, -2.78, -10.31, 0.00209, 28, 5.72, -11.47, 0.0349, 4, 25, 10.9, -5.75, 0.15429, 26, 1, -6.08, 0.5783, 27, 0.33, -8.33, 0.04586, 28, 4.88, -7.87, 0.22155, 4, 25, 13.95, -7, 0.00593, 26, 3.73, -7.93, 0.25649, 27, 3.56, -7.68, 0.05791, 28, 5.33, -4.61, 0.67966, 3, 26, 3.95, -11.38, 0.02199, 27, 6.17, -9.95, 0.00162, 28, 8.34, -2.9, 0.97639, 1, 28, 12.02, -2.03, 1, 1, 28, 11.34, 0.27, 1, 1, 28, 7.61, 2.24, 1, 2, 27, 10.22, -2.9, 0.00231, 28, 3.03, 3.26, 0.99769, 2, 27, 8.73, 1.39, 0.62982, 28, -1.52, 3.27, 0.37018, 2, 27, 6.68, 4.52, 0.99789, 28, -5.14, 2.38, 0.00211, 2, 26, 11.81, 2.82, 0.18398, 27, 1.58, 5.62, 0.81602, 3, 25, 16.37, 8.16, 0.01129, 26, 9.18, 6.42, 0.71418, 27, -2.83, 6.28, 0.27454, 3, 25, 11, 8.78, 0.15671, 26, 4.05, 8.13, 0.83556, 27, -7.65, 3.82, 0.00773, 2, 25, 6.58, 6.67, 0.62535, 26, -0.7, 6.96, 0.37465, 2, 25, 3.11, 6.89, 0.94157, 26, -4.06, 7.88, 0.05843, 1, 25, 3.35, -0.61, 1, 4, 25, 9.05, -0.58, 0.16723, 26, 0.24, -0.64, 0.82583, 27, -4.08, -5.05, 0.00099, 28, 0.33, -10.95, 0.00596, 3, 26, 5.5, -2.17, 0.52811, 27, 0.71, -2.37, 0.36914, 28, -0.62, -5.55, 0.10276, 3, 26, 7.93, -5.7, 0.06458, 27, 4.92, -3.12, 0.17847, 28, 1.49, -1.82, 0.75695, 3, 26, 7.25, -11.18, 8e-05, 27, 8.35, -7.46, 1e-05, 28, 6.71, -0.01, 0.99991], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 0, 38, 4, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 20]}}, "4_phong": {"4_phong": {"x": -0.5, "width": 319, "height": 140}}, "lightining2/1": {"lightining2/7": {"width": 88, "height": 263}, "lightining2/6": {"width": 25, "height": 201}, "lightining2/5": {"width": 74, "height": 261}, "lightining2/10": {"width": 107, "height": 261}, "lightining2/4": {"width": 90, "height": 262}, "lightining2/11": {"width": 36, "height": 200}, "lightining2/9": {"width": 14, "height": 205}, "lightining2/8": {"width": 76, "height": 262}, "lightining2/3": {"width": 124, "height": 269}, "lightining2/2": {"width": 71, "height": 286}, "lightining2/1": {"width": 71, "height": 286}, "lightining2/0": {"width": 71, "height": 286}}, "may2": {"may1": {"x": -48.15, "width": 328, "y": -0.6, "height": 133}}, "17_hand": {"17_hand": {"width": 130, "type": "mesh", "hull": 28, "height": 216, "triangles": [11, 10, 12, 12, 10, 13, 10, 37, 13, 35, 36, 10, 35, 18, 36, 13, 37, 14, 37, 15, 14, 10, 36, 37, 18, 17, 36, 36, 17, 37, 17, 16, 37, 37, 16, 15, 27, 28, 0, 27, 26, 28, 4, 3, 30, 25, 24, 29, 3, 29, 30, 30, 31, 4, 24, 23, 29, 29, 23, 30, 23, 22, 30, 2, 1, 3, 3, 1, 28, 1, 0, 28, 26, 25, 28, 28, 29, 3, 28, 25, 29, 6, 32, 7, 31, 21, 32, 32, 33, 7, 7, 9, 8, 7, 33, 9, 21, 34, 32, 32, 34, 33, 21, 20, 34, 20, 19, 34, 33, 10, 9, 35, 33, 34, 33, 35, 10, 34, 19, 18, 35, 34, 18, 4, 31, 5, 5, 31, 6, 30, 22, 31, 31, 32, 6, 31, 22, 21], "uvs": [0.13918, 1, 0.27224, 1, 0.30638, 0.89896, 0.2698, 0.85347, 0.35027, 0.8021, 0.41123, 0.73019, 0.43562, 0.66855, 0.55023, 0.62452, 0.64776, 0.56875, 0.64045, 0.50712, 0.62826, 0.41319, 0.66753, 0.4731, 1, 0.47105, 1, 0.15552, 0.82359, 0, 0.69174, 0.00238, 0.54127, 0.00509, 0.31586, 0.1542, 0.28742, 0.3302, 0.20416, 0.43165, 0.22244, 0.51109, 0.28742, 0.54287, 0.1534, 0.57954, 0.07013, 0.65776, 0.02139, 0.73965, 0, 0.81298, 0, 0.91198, 0.02952, 1, 0.1209, 0.88998, 0.13106, 0.80076, 0.17776, 0.71154, 0.24884, 0.62476, 0.40115, 0.56976, 0.47832, 0.49031, 0.36053, 0.49887, 0.47962, 0.43012, 0.53225, 0.3063, 0.63273, 0.15656], "vertices": [1, 19, 41.16, 8.63, 1, 2, 18, 75.02, 39.83, 3e-05, 19, 38.91, 25.78, 0.99997, 2, 18, 54.23, 31.83, 0.09523, 19, 16.69, 27.34, 0.90477, 2, 18, 48.5, 22.54, 0.37274, 19, 7.56, 21.35, 0.62726, 2, 18, 33.53, 25.39, 0.8775, 19, -4.8, 30.27, 0.1225, 3, 17, 64.44, 24.56, 0.03656, 18, 16.17, 23.72, 0.95868, 19, -21.23, 36.11, 0.00476, 2, 17, 51.78, 19.37, 0.41413, 18, 3.24, 19.23, 0.58587, 2, 17, 35.37, 25.94, 0.95451, 18, -12.79, 26.67, 0.04549, 1, 17, 18.19, 29.22, 1, 1, 17, 7.92, 20.69, 1, 2, 17, -7.64, 7.58, 0.4737, 20, -5.46, -11.43, 0.5263, 2, 17, -0.1, 19.27, 0.21682, 20, -17.12, -19.02, 0.78318, 2, 17, -25.65, 54.13, 0.04188, 20, -8.05, -61.28, 0.95812, 1, 20, 58.73, -47.66, 1, 1, 20, 87.06, -18.47, 1, 1, 20, 83.13, -1.78, 1, 1, 20, 78.65, 17.27, 1, 2, 17, -29.43, -58.03, 0.00039, 20, 41.23, 39.54, 0.99961, 2, 17, 3.62, -38.88, 0.32835, 20, 3.25, 35.57, 0.67165, 2, 17, 27.73, -34.9, 0.80457, 20, -20.39, 41.79, 0.19543, 2, 17, 40.29, -22.97, 0.93348, 20, -36.73, 36.04, 0.06652, 3, 17, 40.94, -12.1, 0.90291, 20, -41.76, 26.39, 0.01474, 18, -9.28, -11.62, 0.08235, 2, 17, 57.53, -21.65, 0.09871, 18, 6.77, -22.04, 0.90129, 1, 18, 26.83, -22.08, 1, 2, 18, 45.15, -17.91, 0.91155, 19, -12.61, -13.87, 0.08845, 2, 18, 60, -11.73, 0.19143, 19, 3.46, -14.57, 0.80857, 1, 19, 24.66, -11.78, 1, 1, 19, 43.01, -5.51, 1, 2, 18, 65.57, 10.46, 0.0072, 19, 17.9, 3.18, 0.9928, 2, 18, 48.61, 1.21, 0.82864, 19, -1.38, 1.98, 0.17136, 1, 18, 29.1, -4.04, 1, 2, 17, 58.24, -5.87, 0.01725, 18, 8.33, -6.33, 0.98275, 1, 17, 37.05, 3.3, 1, 1, 17, 17.26, 1.45, 1, 2, 17, 27.68, -9.92, 0.96447, 20, -30.55, 18.98, 0.03553, 2, 17, 6.59, -5.99, 0.87599, 20, -12.91, 6.77, 0.12401, 2, 17, -19.13, -16.02, 0.00377, 20, 14.67, 5.42, 0.99623, 1, 20, 48.97, -0.92, 1], "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 2, 0, 0, 54, 0, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 40, 68, 68, 66, 66, 20, 70, 72, 72, 74, 28, 30, 30, 32, 74, 30]}}, "17_rau1": {"17_rau1": {"width": 27, "type": "mesh", "hull": 21, "height": 22, "triangles": [26, 16, 14, 26, 14, 12, 14, 13, 12, 16, 15, 14, 17, 16, 26, 26, 12, 11, 25, 17, 26, 25, 26, 11, 18, 17, 25, 10, 25, 11, 24, 18, 25, 24, 25, 10, 24, 23, 18, 9, 24, 10, 23, 24, 9, 8, 23, 9, 22, 19, 18, 23, 22, 18, 4, 21, 22, 5, 4, 22, 23, 6, 5, 23, 5, 22, 7, 6, 23, 7, 23, 8, 21, 0, 20, 21, 20, 19, 1, 0, 21, 2, 1, 21, 3, 2, 21, 22, 21, 19, 4, 3, 21], "uvs": [0.16952, 0.15665, 0.08946, 0.25781, 0, 0.37083, 0, 0.54274, 0.13049, 0.65264, 0.22234, 0.82455, 0.34174, 1, 0.52086, 1, 0.71374, 1, 0.85841, 0.89219, 0.95026, 0.62165, 1, 0.4131, 1, 0.1482, 0.9824, 0, 0.93687, 0, 0.88137, 0, 0.82167, 0.15947, 0.72752, 0.30601, 0.60812, 0.37928, 0.46115, 0.28065, 0.27975, 0.1482, 0.21815, 0.39022, 0.38119, 0.57623, 0.5603, 0.68895, 0.72104, 0.61286, 0.81288, 0.45505, 0.90473, 0.24933], "vertices": [1, 21, -1.08, 2.41, 1, 2, 21, -1.04, -0.69, 0.99617, 22, -9.37, 1.12, 0.00383, 2, 21, -0.99, -4.16, 0.93262, 22, -9.93, -2.3, 0.06738, 2, 21, 1.69, -6.83, 0.7946, 22, -7.77, -5.4, 0.2054, 2, 21, 5.89, -6.05, 0.37806, 22, -3.49, -5.37, 0.62194, 2, 21, 10.31, -6.97, 0.00573, 22, 0.7, -7.05, 0.99427, 2, 22, 5.55, -8.38, 0.99976, 23, -8.72, 1.44, 0.00024, 2, 22, 9.52, -5.61, 0.81369, 23, -5.36, -2.03, 0.18631, 2, 22, 13.79, -2.63, 0.13202, 23, -1.74, -5.78, 0.86798, 1, 23, 2.68, -6.94, 1, 2, 23, 8.69, -4.58, 0.78353, 24, -1.42, -4.36, 0.21647, 2, 23, 12.92, -2.36, 0.03046, 24, 3.3, -3.56, 0.96954, 1, 24, 8.54, -1.01, 1, 1, 24, 11.26, 0.84, 1, 1, 24, 10.72, 1.95, 1, 1, 24, 10.07, 3.3, 1, 3, 21, 11.41, 14.82, 0.0003, 23, 13.58, 4.98, 0.00342, 24, 6.21, 3.21, 0.99629, 4, 21, 11.89, 10.74, 0.03454, 22, 5.37, 10.1, 0.01825, 23, 9.5, 4.57, 0.25305, 24, 2.2, 4.09, 0.69417, 4, 21, 10.75, 7.32, 0.28339, 22, 3.65, 6.94, 0.15533, 23, 6.1, 5.76, 0.41696, 24, -0.66, 6.28, 0.14432, 4, 21, 6.41, 6.05, 0.87872, 22, -0.85, 6.45, 0.0341, 23, 4.9, 10.13, 0.07928, 24, -0.45, 10.8, 0.0079, 2, 21, 0.89, 4.65, 0.9991, 23, 3.59, 15.67, 0.0009, 2, 21, 3.48, -0.29, 0.99212, 22, -4.85, 0.72, 0.00788, 1, 22, 1.1, -0.12, 1, 4, 21, 14.66, 1.59, 0.00367, 22, 6.48, 0.61, 0.87396, 23, 0.3, 1.96, 0.12225, 24, -7.35, 4.46, 0.00013, 1, 23, 4.52, 0, 1, 4, 21, 15.84, 10.05, 0.00255, 22, 9.14, 8.73, 0.00141, 23, 8.74, 0.63, 0.41709, 24, 0.26, 0.58, 0.57895, 3, 21, 14.39, 15.01, 1e-05, 23, 13.72, 1.99, 0.00015, 24, 5.41, 0.33, 0.99983], "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 0, 40, 0, 2, 2, 4, 2, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 26, 28, 28, 30, 52, 28]}}, "lightining2/0": {"lightining2/7": {"width": 88, "height": 263}, "lightining2/6": {"width": 25, "height": 201}, "lightining2/5": {"width": 74, "height": 261}, "lightining2/10": {"width": 107, "height": 261}, "lightining2/4": {"width": 90, "height": 262}, "lightining2/11": {"width": 36, "height": 200}, "lightining2/9": {"width": 14, "height": 205}, "lightining2/8": {"width": 76, "height": 262}, "lightining2/3": {"width": 124, "height": 269}, "lightining2/2": {"width": 71, "height": 286}, "lightining2/1": {"width": 71, "height": 286}, "lightining2/0": {"width": 71, "height": 286}}, "may1": {"may1": {"x": -11.41, "width": 328, "y": 16.19, "height": 133}}}}, "skeleton": {"images": "./images/", "width": 394.86, "spine": "3.6.53", "hash": "2ZLpEAUq1IqdsMIHV5l3ykhKWGA", "height": 493.59}, "slots": [{"attachment": "lightining2/0", "blend": "additive", "name": "lightining2/0", "bone": "fxsau"}, {"attachment": "lightining2/0", "blend": "additive", "name": "lightining2/1", "bone": "fxsau2"}, {"attachment": "17_body", "name": "17_body", "bone": "17_body"}, {"attachment": "17_hair", "name": "17_hair", "bone": "17_hair"}, {"attachment": "17_rau3", "name": "17_rau3", "bone": "17_rau9"}, {"attachment": "17_rau2", "name": "17_rau2", "bone": "17_rau5"}, {"attachment": "17_hand", "name": "17_hand", "bone": "17_hand5"}, {"attachment": "17_rau1", "name": "17_rau1", "bone": "17_rau1"}, {"attachment": "may1", "name": "may1", "bone": "4_phong"}, {"attachment": "may1", "name": "may5", "bone": "4_phong"}, {"attachment": "may1", "name": "may2", "bone": "may1"}, {"attachment": "may1", "name": "may3", "bone": "may2"}, {"attachment": "may1", "name": "may4", "bone": "may3"}, {"attachment": "4_phong", "name": "4_phong", "bone": "4_phong"}], "bones": [{"name": "root"}, {"parent": "root", "name": "4_phong"}, {"parent": "4_phong", "name": "17_body", "x": -10.21, "y": 103.01}, {"parent": "17_body", "rotation": 86.32, "name": "17_body2", "length": 91.47, "x": 1.3, "y": 11.74}, {"parent": "17_body2", "rotation": 4.59, "name": "17_body3", "length": 81.51, "x": 91.47}, {"parent": "17_body3", "rotation": -2.2, "name": "17_body4", "length": 58.04, "x": 81.51}, {"parent": "17_body3", "rotation": -110.72, "name": "17_body5", "length": 21.63, "x": 63.65, "y": -48.31}, {"parent": "17_body5", "rotation": -56.68, "name": "17_body6", "length": 76.6, "x": 21.63}, {"parent": "17_body6", "rotation": -8.76, "name": "17_body7", "length": 63.71, "x": 76.6}, {"parent": "17_body7", "rotation": -28.26, "name": "17_body8", "length": 40.83, "x": 63.68, "y": -0.41}, {"parent": "17_body4", "rotation": -133.71, "name": "17_hair", "length": 14.41, "x": 35.6, "y": -20.12}, {"parent": "17_hair", "rotation": 3.45, "name": "17_hair2", "length": 12.23, "x": 14.41}, {"parent": "17_hair2", "rotation": 27.52, "name": "17_hair3", "length": 12.01, "x": 12.23}, {"parent": "17_hair3", "rotation": 38.14, "name": "17_hair4", "length": 8.66, "x": 12.01}, {"parent": "17_hair4", "rotation": 41.93, "name": "17_hair5", "length": 8.19, "x": 8.66}, {"parent": "17_hair5", "rotation": 49.78, "name": "17_hair6", "length": 7.16, "x": 8.19}, {"parent": "17_hair6", "rotation": 10.71, "name": "17_hair7", "length": 6.99, "x": 7.16}, {"parent": "17_body3", "rotation": 143.44, "name": "17_hand", "length": 49.58, "x": 44.2, "y": 59.06}, {"parent": "17_hand", "rotation": 3.11, "name": "17_hand2", "length": 50.69, "x": 49.58}, {"parent": "17_hand2", "rotation": 25.06, "name": "17_hand3", "length": 40.64, "x": 50.69}, {"parent": "17_hand", "rotation": -155.88, "name": "17_hand5", "length": 78.03, "x": -7.96, "y": -5.09}, {"parent": "17_body4", "rotation": -133.71, "name": "17_rau1", "length": 8.53, "x": 13.81, "y": -3.76}, {"parent": "17_rau1", "rotation": 10.12, "name": "17_rau2", "length": 8.37, "x": 8.38, "y": -0.15}, {"parent": "17_rau2", "rotation": 80.85, "name": "17_rau3", "length": 8.68, "x": 8.37}, {"parent": "17_rau3", "rotation": 18.09, "name": "17_rau4", "length": 8.56, "x": 8.68}, {"parent": "17_body4", "rotation": 133.38, "name": "17_rau5", "length": 8.69, "x": 14.7, "y": 2.92}, {"parent": "17_rau5", "rotation": 11.75, "name": "17_rau6", "length": 6.7, "x": 8.69}, {"parent": "17_rau6", "rotation": -45.41, "name": "17_rau7", "length": 5.68, "x": 6.7}, {"parent": "17_rau7", "rotation": -70.67, "name": "17_rau8", "length": 8.93, "x": 6.14, "y": -1.12}, {"parent": "17_body4", "rotation": -176.69, "name": "17_rau9", "length": 17.69, "x": 6.04, "y": -0.6}, {"parent": "17_rau9", "rotation": 4.56, "name": "17_rau10", "length": 16.33, "x": 17.69}, {"parent": "17_rau10", "rotation": 0.17, "name": "17_rau11", "length": 15.92, "x": 16.33}, {"parent": "17_rau11", "rotation": 8.77, "name": "17_rau12", "length": 15.54, "x": 15.92}, {"parent": "17_rau12", "rotation": 9.79, "name": "17_rau13", "length": 17.03, "x": 15.54}, {"scaleX": 2, "parent": "4_phong", "scaleY": 1.5, "name": "fxsau", "x": 60.03, "y": 187.03}, {"scaleX": 2, "parent": "4_phong", "scaleY": 1.4, "name": "fxsau2", "x": -111.54, "y": 186.1}, {"parent": "4_phong", "name": "may1", "x": 3.15, "y": 65.1}, {"scaleX": -1, "parent": "4_phong", "name": "may2", "x": -43.48, "y": 52.28}, {"parent": "4_phong", "rotation": 20.2, "name": "may3", "x": 53.97, "y": 44.17}], "animations": {"animation": {"slots": {"may4": {"color": [{"color": "ffffff00", "curve": "stepped", "time": 0}, {"color": "ffffff00", "time": 0.6667}, {"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "time": 1.6667}]}, "may3": {"color": [{"color": "ffffffa3", "time": 0}, {"color": "ffffff00", "curve": "stepped", "time": 0.3}, {"color": "ffffff00", "time": 0.3333}, {"color": "ffffffff", "time": 0.8333}, {"color": "ffffff00", "curve": "stepped", "time": 1.3333}, {"color": "ffffff00", "time": 2}, {"color": "ffffffff", "time": 2.5}, {"color": "ffffffa3", "time": 2.6667}]}, "lightining2/1": {"attachment": [{"name": null, "time": 0}, {"name": null, "time": 0.3333}, {"name": "lightining2/0", "time": 0.3667}, {"name": null, "time": 0.4}, {"name": "lightining2/1", "time": 0.4333}, {"name": null, "time": 0.4667}, {"name": "lightining2/2", "time": 0.5}, {"name": "lightining2/3", "time": 0.5333}, {"name": null, "time": 0.5667}, {"name": "lightining2/4", "time": 0.6}, {"name": null, "time": 0.6333}, {"name": "lightining2/5", "time": 0.6667}, {"name": null, "time": 0.7}, {"name": "lightining2/6", "time": 0.7333}, {"name": "lightining2/7", "time": 0.7667}, {"name": null, "time": 0.8}, {"name": "lightining2/8", "time": 0.8333}, {"name": null, "time": 0.8667}, {"name": "lightining2/9", "time": 0.9}, {"name": null, "time": 0.9333}, {"name": "lightining2/10", "time": 0.9667}, {"name": null, "time": 1}, {"name": "lightining2/11", "time": 1.0333}, {"name": null, "time": 1.0667}, {"name": null, "time": 1.6667}, {"name": "lightining2/0", "time": 1.7}, {"name": null, "time": 1.7333}, {"name": "lightining2/1", "time": 1.7667}, {"name": null, "time": 1.8}, {"name": "lightining2/2", "time": 1.8333}, {"name": "lightining2/3", "time": 1.8667}, {"name": null, "time": 1.9}, {"name": "lightining2/4", "time": 1.9333}, {"name": null, "time": 1.9667}, {"name": "lightining2/5", "time": 2}, {"name": null, "time": 2.0333}, {"name": "lightining2/6", "time": 2.0667}, {"name": "lightining2/7", "time": 2.1}, {"name": null, "time": 2.1333}, {"name": "lightining2/8", "time": 2.1667}, {"name": null, "time": 2.2}, {"name": "lightining2/9", "time": 2.2333}, {"name": null, "time": 2.2667}, {"name": "lightining2/10", "time": 2.3}, {"name": null, "time": 2.3333}, {"name": "lightining2/11", "time": 2.3667}, {"name": null, "time": 2.4}]}, "may2": {"color": [{"color": "ffffff00", "time": 0}, {"color": "ffffffff", "time": 0.5}, {"color": "ffffff00", "curve": "stepped", "time": 1}, {"color": "ffffff00", "time": 1.3333}, {"color": "ffffffff", "time": 1.8}, {"color": "ffffff00", "time": 2.3333}]}, "lightining2/0": {"attachment": [{"name": null, "time": 0}, {"name": "lightining2/0", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "lightining2/1", "time": 0.1}, {"name": null, "time": 0.1333}, {"name": "lightining2/2", "time": 0.1667}, {"name": "lightining2/3", "time": 0.2}, {"name": null, "time": 0.2333}, {"name": "lightining2/4", "time": 0.2667}, {"name": null, "time": 0.3}, {"name": "lightining2/5", "time": 0.3333}, {"name": null, "time": 0.3667}, {"name": "lightining2/6", "time": 0.4}, {"name": "lightining2/7", "time": 0.4333}, {"name": null, "time": 0.4667}, {"name": "lightining2/8", "time": 0.5}, {"name": null, "time": 0.5333}, {"name": "lightining2/9", "time": 0.5667}, {"name": null, "time": 0.6}, {"name": "lightining2/10", "time": 0.6333}, {"name": null, "time": 0.6667}, {"name": "lightining2/11", "time": 0.7}, {"name": null, "time": 0.7333}, {"name": null, "time": 1.3333}, {"name": "lightining2/0", "time": 1.3667}, {"name": null, "time": 1.4}, {"name": "lightining2/1", "time": 1.4333}, {"name": null, "time": 1.4667}, {"name": "lightining2/2", "time": 1.5}, {"name": "lightining2/3", "time": 1.5333}, {"name": null, "time": 1.5667}, {"name": "lightining2/4", "time": 1.6}, {"name": null, "time": 1.6333}, {"name": "lightining2/5", "time": 1.6667}, {"name": null, "time": 1.7}, {"name": "lightining2/6", "time": 1.7333}, {"name": "lightining2/7", "time": 1.7667}, {"name": null, "time": 1.8}, {"name": "lightining2/8", "time": 1.8333}, {"name": null, "time": 1.8667}, {"name": "lightining2/9", "time": 1.9}, {"name": null, "time": 1.9333}, {"name": "lightining2/10", "time": 1.9667}, {"name": null, "time": 2}, {"name": "lightining2/11", "time": 2.0333}, {"name": null, "time": 2.0667}]}}, "bones": {"fxsau": {"translate": [{"x": 0, "y": 6.88, "time": 0}]}, "17_rau11": {"rotate": [{"angle": 4.77, "time": 0, "curve": [0.367, 0.46, 0.754, 1]}, {"angle": -3.32, "time": 0.2667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 0.7, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 1.1667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 1.6, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 2.0333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 2.4667, "curve": [0.255, 0, 0.62, 0.47]}, {"angle": 4.77, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.7667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"x": 0.09, "y": 0.36, "time": 0, "curve": [0.382, 0.56, 0.738, 1]}, {"x": 0, "y": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0.29, "y": 1.14, "time": 0.6, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.0667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0.29, "y": 1.14, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.9333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0.29, "y": 1.14, "time": 2.3667, "curve": [0.244, 0, 0.644, 0.58]}, {"x": 0.09, "y": 0.36, "time": 2.6667}]}, "17_hair": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"angle": 0, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau10": {"rotate": [{"angle": -1.98, "time": 0, "curve": [0.381, 0.59, 0.729, 1]}, {"angle": -3.32, "time": 0.1333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.62, "time": 0.5667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 1.0333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.62, "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 1.9, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.62, "time": 2.3333, "curve": [0.243, 0, 0.658, 0.64]}, {"angle": -1.98, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.7667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"x": -0.11, "y": 0.08, "time": 0, "curve": [0.367, 0.63, 0.705, 1]}, {"x": 0, "y": 0, "time": 0.0667, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.48, "y": 0.98, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 0.9667, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.48, "y": 0.98, "time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": -1.48, "y": 0.98, "time": 2.2667, "curve": [0.244, 0, 0.697, 0.78]}, {"x": -0.11, "y": 0.08, "time": 2.6667}]}, "17_rau13": {"rotate": [{"angle": 9.94, "time": 0, "curve": [0.355, 0.65, 0.689, 1]}, {"angle": 10.29, "time": 0.0333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 0.9333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 1.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 2.2667, "curve": [0.246, 0, 0.719, 0.87]}, {"angle": 9.94, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.7667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.9}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.7667}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_body": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"angle": 0, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau12": {"rotate": [{"angle": 7.24, "time": 0, "curve": [0.342, 0.36, 0.757, 1]}, {"angle": -3.32, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 0.7667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 1.2333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 1.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -3.32, "time": 2.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 10.29, "time": 2.5333, "curve": [0.271, 0, 0.619, 0.41]}, {"angle": 7.24, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.7667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.9}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.7667}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau8": {"rotate": [{"angle": 5.2, "time": 0, "curve": [0.345, 0.37, 0.757, 1]}, {"angle": 0, "time": 0.4667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.86, "time": 1.1333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.86, "time": 2.4667, "curve": [0.269, 0, 0.618, 0.42]}, {"angle": 5.2, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.4667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.8}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.4667}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.8}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau7": {"rotate": [{"angle": 3.43, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.86, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.86, "time": 2.3333, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": 3.43, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.6667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.6667}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau6": {"rotate": [{"angle": 1.26, "time": 0, "curve": [0.379, 0.6, 0.724, 1]}, {"angle": 0, "time": 0.1667}, {"angle": 6.86, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.86, "time": 2.1667, "curve": [0.242, 0, 0.667, 0.67]}, {"angle": 1.26, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.1667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.5}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.1667}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau5": {"rotate": [{"angle": 0.29, "time": 0, "curve": [0.36, 0.64, 0.695, 1]}, {"angle": 0, "time": 0.0667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.86, "time": 0.7333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"angle": 6.86, "time": 2.0667, "curve": [0.245, 0, 0.711, 0.83]}, {"angle": 0.29, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.0667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.4}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.0667}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.4}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau9": {"rotate": [{"angle": 353.18, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.62, "time": 0.4333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 353.18, "time": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.62, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 353.18, "time": 1.7667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.62, "time": 2.2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 353.18, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.9}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.7667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.9}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.7667}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hand5": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.42, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.42, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau4": {"rotate": [{"angle": -3.16, "time": 0, "curve": [0.375, 0.5, 0.75, 1]}, {"angle": 0, "time": 0.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.31, "time": 1, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.31, "time": 2.3333, "curve": [0.25, 0, 0.625, 0.5]}, {"angle": -3.16, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3333}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.6667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.6667}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau3": {"rotate": [{"angle": -1.16, "time": 0, "curve": [0.379, 0.6, 0.724, 1]}, {"angle": 0, "time": 0.1667}, {"angle": -6.31, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.31, "time": 2.1667, "curve": [0.242, 0, 0.667, 0.67]}, {"angle": -1.16, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.1667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.5}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.1667}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5}, {"x": 0, "y": 0, "time": 2.6667}]}, "may3": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.6667}, {"angle": 0, "time": 1.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"x": 1, "y": 1, "time": 0.6667}, {"x": 1.062, "y": 1.062, "time": 1.6667}], "translate": [{"curve": "stepped", "x": 1.09, "y": -23, "time": 0}, {"x": 1.09, "y": -23, "time": 0.6667}, {"x": -1.09, "y": -3.29, "time": 1.6667}]}, "17_hand2": {"rotate": [{"angle": -1.63, "time": 0, "curve": [0.382, 0.58, 0.731, 1]}, {"angle": 0, "time": 0.2, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.75, "time": 0.8667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.5333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.75, "time": 2.2, "curve": [0.243, 0, 0.655, 0.63]}, {"angle": -1.63, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.2}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.5333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.2}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_rau2": {"rotate": [{"angle": -0.27, "time": 0, "curve": [0.36, 0.64, 0.695, 1]}, {"angle": 0, "time": 0.0667, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.31, "time": 0.7333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.31, "time": 2.0667, "curve": [0.245, 0, 0.711, 0.83]}, {"angle": -0.27, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.0667}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.4}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.0667}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.4}, {"x": 0, "y": 0, "time": 2.6667}]}, "may2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 0.3}, {"curve": "stepped", "angle": 0, "time": 0.3333}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"curve": "stepped", "angle": 0, "time": 2}, {"angle": 0, "time": 2.6667}], "scale": [{"x": 1.043, "y": 1.043, "time": 0}, {"curve": "stepped", "x": 1.062, "y": 1.062, "time": 0.3}, {"x": 1, "y": 1, "time": 0.3333}, {"x": 1.062, "y": 1.062, "time": 1.3333}, {"x": 1, "y": 1, "time": 2}, {"x": 1.043, "y": 1.043, "time": 2.6667}], "translate": [{"x": -0.42, "y": -9.4, "time": 0}, {"curve": "stepped", "x": -1.09, "y": -3.29, "time": 0.3}, {"x": 1.09, "y": -23, "time": 0.3333}, {"x": -1.09, "y": -3.29, "time": 1.3333}, {"x": 1.09, "y": -23, "time": 2}, {"x": -0.42, "y": -9.4, "time": 2.6667}]}, "17_rau1": {"rotate": [{"angle": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.31, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -6.31, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hand3": {"rotate": [{"angle": -4.49, "time": 0, "curve": [0.379, 0.52, 0.747, 1]}, {"angle": 0, "time": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"angle": -10.34, "time": 0.9667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.6333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -10.34, "time": 2.3, "curve": [0.247, 0, 0.63, 0.53]}, {"angle": -4.49, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.3}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.6333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.3}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.6333}, {"x": 0, "y": 0, "time": 2.6667}]}, "may1": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"angle": 0, "time": 2.3333}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.062, "y": 1.062, "time": 1}, {"x": 1, "y": 1, "time": 1.3333}, {"x": 1.062, "y": 1.062, "time": 2.3333}], "translate": [{"x": 1.09, "y": -23, "time": 0}, {"x": -1.09, "y": -3.29, "time": 1}, {"x": 1.09, "y": -23, "time": 1.3333}, {"x": -1.09, "y": -3.29, "time": 2.3333}]}, "fxsau2": {"translate": [{"x": 39.78, "y": 6.88, "time": 0}]}, "17_hair2": {"rotate": [{"angle": 5.95, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hair6": {"rotate": [{"angle": 5.95, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hair5": {"rotate": [{"angle": 5.95, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hair4": {"rotate": [{"angle": 5.95, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hair3": {"rotate": [{"angle": 5.95, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hair7": {"rotate": [{"angle": 5.95, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -8.76, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"angle": 5.95, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_body2": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"angle": 0, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 1.25, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 1.25, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_body8": {"rotate": [{"angle": 0, "time": 0}], "scale": [{"x": 1, "y": 1, "time": 0}], "translate": [{"x": 0, "y": 0, "time": 0}]}, "17_body7": {"rotate": [{"angle": 1.78, "time": 0, "curve": [0.382, 0.58, 0.731, 1]}, {"angle": 0, "time": 0.2}, {"angle": 7.37, "time": 0.8667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.5333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 7.37, "time": 2.2, "curve": [0.243, 0, 0.655, 0.63]}, {"angle": 1.78, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.2}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.5333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.2}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.5333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_body4": {"rotate": [{"angle": -0.47, "time": 0, "curve": [0.382, 0.57, 0.737, 1]}, {"angle": -1.83, "time": 0.2333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.64, "time": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.83, "time": 1.5667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.64, "time": 2.2333, "curve": [0.244, 0, 0.646, 0.59]}, {"angle": -0.47, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"x": 0.45, "y": -0.01, "time": 0, "curve": [0.379, 0.6, 0.724, 1]}, {"x": 0, "y": 0, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": 2.46, "y": -0.04, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 2.46, "y": -0.04, "time": 2.1667, "curve": [0.242, 0, 0.667, 0.67]}, {"x": 0.45, "y": -0.01, "time": 2.6667}]}, "17_body3": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"angle": 0, "time": 2.6667}], "scale": [{"x": 1.004, "y": 1.006, "time": 0, "curve": [0.379, 0.6, 0.724, 1]}, {"x": 1, "y": 1, "time": 0.1667, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.02, "y": 1.03, "time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"x": 1, "y": 1, "time": 1.5, "curve": [0.25, 0, 0.75, 1]}, {"x": 1.02, "y": 1.03, "time": 2.1667, "curve": [0.242, 0, 0.667, 0.67]}, {"x": 1.004, "y": 1.006, "time": 2.6667}], "translate": [{"x": 0, "y": 0, "time": 0, "curve": [0.25, 0, 0.75, 1]}, {"x": 2.5, "y": 0.16, "time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"x": 2.5, "y": 0.16, "time": 2, "curve": [0.25, 0, 0.75, 1]}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_body6": {"rotate": [{"angle": 0.46, "time": 0, "curve": [0.375, 0.62, 0.716, 1]}, {"angle": 0, "time": 0.1333, "curve": [0.25, 0, 0.75, 1]}, {"angle": 3.55, "time": 0.8, "curve": [0.25, 0, 0.75, 1]}, {"angle": 0, "time": 1.4667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 3.55, "time": 2.1333, "curve": [0.243, 0, 0.68, 0.71]}, {"angle": 0.46, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 0.1333}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.4667}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 0.1333}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.4667}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_body5": {"rotate": [{"curve": "stepped", "angle": 0, "time": 0}, {"curve": "stepped", "angle": 0, "time": 1.3333}, {"angle": 0, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}, "17_hand": {"rotate": [{"angle": 2.09, "time": 0, "curve": [0.369, 0.63, 0.706, 1]}, {"angle": 2.37, "time": 0.1, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.01, "time": 0.7667, "curve": [0.25, 0, 0.75, 1]}, {"angle": 2.37, "time": 1.4333, "curve": [0.25, 0, 0.75, 1]}, {"angle": -1.01, "time": 2.1, "curve": [0.244, 0, 0.694, 0.77]}, {"angle": 2.09, "time": 2.6667}], "scale": [{"curve": "stepped", "x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1, "y": 1, "time": 1.3333}, {"x": 1, "y": 1, "time": 2.6667}], "translate": [{"curve": "stepped", "x": 0, "y": 0, "time": 0}, {"curve": "stepped", "x": 0, "y": 0, "time": 1.3333}, {"x": 0, "y": 0, "time": 2.6667}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]