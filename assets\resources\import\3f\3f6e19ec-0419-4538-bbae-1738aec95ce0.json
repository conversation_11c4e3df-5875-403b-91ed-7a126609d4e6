[1, ["757vsQM+dH/oR4brcAxVgO"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "TX JP", "\nTX JP.png\nsize: 1904,722\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\ndau rong\n  rotate: true\n  xy: 729, 13\n  size: 182, 160\n  orig: 182, 160\n  offset: 0, 0\n  index: -1\neffect mat\n  rotate: true\n  xy: 381, 32\n  size: 64, 29\n  orig: 64, 29\n  offset: 0, 0\n  index: -1\nexplore 2\n  rotate: false\n  xy: 2, 100\n  size: 723, 620\n  orig: 723, 620\n  offset: 0, 0\n  index: -1\nexplore 3\n  rotate: true\n  xy: 974, 207\n  size: 513, 488\n  orig: 513, 488\n  offset: 0, 0\n  index: -1\nfireball\n  rotate: false\n  xy: 729, 199\n  size: 241, 521\n  orig: 241, 521\n  offset: 0, 0\n  index: -1\ngold\n  rotate: true\n  xy: 1206, 57\n  size: 146, 101\n  orig: 146, 101\n  offset: 0, 0\n  index: -1\njackpot\n  rotate: false\n  xy: 414, 42\n  size: 294, 54\n  orig: 294, 54\n  offset: 0, 0\n  index: -1\nlight JP\n  rotate: false\n  xy: 1466, 2\n  size: 396, 396\n  orig: 396, 396\n  offset: 0, 0\n  index: -1\nrong\n  rotate: false\n  xy: 2, 12\n  size: 300, 84\n  orig: 300, 84\n  offset: 0, 0\n  index: -1\nrong effect\n  rotate: false\n  xy: 893, 22\n  size: 309, 173\n  orig: 309, 173\n  offset: 0, 0\n  index: -1\nstar\n  rotate: false\n  xy: 306, 25\n  size: 71, 71\n  orig: 71, 71\n  offset: 0, 0\n  index: -1\nxiNgau1@3x copy 2\n  rotate: false\n  xy: 1466, 402\n  size: 436, 318\n  orig: 436, 318\n  offset: 0, 0\n  index: -1\n", ["TX JP.png"], {"skeleton": {"hash": "dy7shMLsGlNmxGGZVoq/PhAtme0", "spine": "3.7.93", "width": 1808, "height": 1730.99, "images": "./images/", "audio": "/Users/<USER>/Duc/Sgame_Web"}, "bones": [{"name": "root", "x": -11.11, "y": -252.9}, {"name": "dau rong", "parent": "root", "x": -264.05, "y": 1373.34, "scaleX": 0.707, "scaleY": 0.707}, {"name": "effect mat", "parent": "dau rong", "x": -50.14, "y": -147.74}, {"name": "effect mat3", "parent": "effect mat", "x": -87.03, "y": 12.77}, {"name": "xx", "parent": "root", "x": 114.46, "y": 1379.12}, {"name": "xx2", "parent": "root", "x": 290.91, "y": 1323.22}, {"name": "star", "parent": "root", "x": 276.93, "y": 1462.98, "scaleX": 0.544, "scaleY": 0.544}, {"name": "rong", "parent": "dau rong", "x": 98.86, "y": -104.77}, {"name": "gold", "parent": "root", "x": 55.86, "y": 1316.96, "scaleX": 0.899, "scaleY": 0.899}, {"name": "effect mat2", "parent": "dau rong", "x": 5.62, "y": -148.27, "scaleX": -1}, {"name": "effect mat4", "parent": "effect mat2", "x": -87.03, "y": 12.77}, {"name": "star2", "parent": "root", "x": 325.84, "y": 1321.7, "scaleX": 0.44, "scaleY": 0.44}, {"name": "star3", "parent": "root", "x": 143.8, "y": 1389.62, "scaleX": 0.544, "scaleY": 0.544}, {"name": "star4", "parent": "root", "x": 35.12, "y": 1297.24, "scaleX": 0.44, "scaleY": 0.44}, {"name": "star5", "parent": "root", "x": 211.72, "y": 1408.64}, {"name": "star6", "parent": "root", "x": -8.35, "y": 1289.09, "scaleX": 0.44, "scaleY": 0.44}, {"name": "star7", "parent": "root", "x": -165.94, "y": 1318.98}, {"name": "star8", "parent": "root", "x": -54.54, "y": 1343.43, "scaleX": 0.44, "scaleY": 0.44}, {"name": "star9", "parent": "root", "x": -228.43, "y": 1348.87, "scaleX": 0.544, "scaleY": 0.544}, {"name": "star10", "parent": "root", "x": -193.11, "y": 1376.04, "scaleX": 0.44, "scaleY": 0.44}, {"name": "star11", "parent": "root", "x": -445.79, "y": 1294.53, "scaleX": 0.544, "scaleY": 0.544}, {"name": "star12", "parent": "root", "x": -410.47, "y": 1329.85, "scaleX": 0.44, "scaleY": 0.44}, {"name": "fireball", "parent": "root", "length": 232.29, "rotation": 90.8, "x": 599.8, "y": 409.04, "scaleX": 0.589, "scaleY": 0.589}, {"name": "fireball2", "parent": "fireball", "length": 209.71, "rotation": -1.68, "x": 232.29}, {"name": "fireball3", "parent": "fireball2", "length": 192.01, "rotation": -0.56, "x": 209.71}, {"name": "fireball4", "parent": "fireball3", "length": 193.56, "rotation": 1.44, "x": 192.01}, {"name": "fireball5", "parent": "fireball4", "length": 229.05, "rotation": -0.4, "x": 193.56}, {"name": "fire path", "parent": "root", "x": 694.97, "y": 947.78}, {"name": "fireball6", "parent": "root", "length": 232.29, "rotation": 90.8, "x": 599.8, "y": 409.04, "scaleX": 0.589, "scaleY": 0.589}, {"name": "fireball7", "parent": "fireball6", "length": 209.71, "rotation": -1.68, "x": 232.29}, {"name": "fireball8", "parent": "fireball7", "length": 192.01, "rotation": -0.56, "x": 209.71}, {"name": "fireball9", "parent": "fireball8", "length": 193.56, "rotation": 1.44, "x": 192.01}, {"name": "fireball10", "parent": "fireball9", "length": 229.05, "rotation": -0.4, "x": 193.56}, {"name": "fireball path2", "parent": "root", "x": 746.31, "y": 814.45}, {"name": "fireball path3", "parent": "root", "x": 746.31, "y": 814.45}, {"name": "fireball11", "parent": "root", "length": 232.29, "rotation": 90.8, "x": 599.8, "y": 409.04, "scaleX": 0.589, "scaleY": 0.589}, {"name": "fireball12", "parent": "fireball11", "length": 209.71, "rotation": -1.68, "x": 232.29}, {"name": "fireball13", "parent": "fireball12", "length": 192.01, "rotation": -0.56, "x": 209.71}, {"name": "fireball14", "parent": "fireball13", "length": 193.56, "rotation": 1.44, "x": 192.01}, {"name": "fireball15", "parent": "fireball14", "length": 229.05, "rotation": -0.4, "x": 193.56}, {"name": "fire path2", "parent": "root", "x": -735.48, "y": 942.52, "scaleX": -1}, {"name": "fireball path4", "parent": "root", "x": -684.13, "y": 809.19, "scaleX": -1}, {"name": "fireball path5", "parent": "root", "x": -684.13, "y": 809.19, "scaleX": -1}, {"name": "fireball16", "parent": "root", "length": 232.29, "rotation": 90.8, "x": 599.8, "y": 409.04, "scaleX": 0.589, "scaleY": 0.589}, {"name": "fireball17", "parent": "fireball16", "length": 209.71, "rotation": -1.68, "x": 232.29}, {"name": "fireball18", "parent": "fireball17", "length": 192.01, "rotation": -0.56, "x": 209.71}, {"name": "fireball19", "parent": "fireball18", "length": 193.56, "rotation": 1.44, "x": 192.01}, {"name": "fireball20", "parent": "fireball19", "length": 229.05, "rotation": -0.4, "x": 193.56}, {"name": "fireball21", "parent": "root", "length": 232.29, "rotation": 90.8, "x": 599.8, "y": 409.04, "scaleX": 0.589, "scaleY": 0.589}, {"name": "fireball22", "parent": "fireball21", "length": 209.71, "rotation": -1.68, "x": 232.29}, {"name": "fireball23", "parent": "fireball22", "length": 192.01, "rotation": -0.56, "x": 209.71}, {"name": "fireball24", "parent": "fireball23", "length": 193.56, "rotation": 1.44, "x": 192.01}, {"name": "fireball25", "parent": "fireball24", "length": 229.05, "rotation": -0.4, "x": 193.56}, {"name": "fireball26", "parent": "root", "length": 232.29, "rotation": 90.8, "x": 599.8, "y": 409.04, "scaleX": 0.589, "scaleY": 0.589}, {"name": "fireball27", "parent": "fireball26", "length": 209.71, "rotation": -1.68, "x": 232.29}, {"name": "fireball28", "parent": "fireball27", "length": 192.01, "rotation": -0.56, "x": 209.71}, {"name": "fireball29", "parent": "fireball28", "length": 193.56, "rotation": 1.44, "x": 192.01}, {"name": "fireball30", "parent": "fireball29", "length": 229.05, "rotation": -0.4, "x": 193.56}, {"name": "circle", "parent": "root", "x": 3.46, "y": 796.46}, {"name": "light JP", "parent": "root", "x": 0.2, "y": 1299.27}, {"name": "explore 3", "parent": "root", "x": 3.83, "y": 1312.67, "scaleX": -0.334, "scaleY": -0.334}, {"name": "rong effect", "parent": "root", "x": -77.99, "y": 1282.32, "scaleX": 0.707, "scaleY": 0.707}, {"name": "explore 4", "parent": "root", "x": 3.83, "y": 788.32, "scaleX": -0.334, "scaleY": -0.334}, {"name": "explore 2", "parent": "root", "rotation": 180, "x": -5.04, "y": 788.37}], "slots": [{"name": "Layer 0", "bone": "root"}, {"name": "fireball", "bone": "fireball5", "attachment": "fireball"}, {"name": "fireball4", "bone": "fireball20", "attachment": "fireball"}, {"name": "fireball2", "bone": "fireball10", "attachment": "fireball"}, {"name": "fireball5", "bone": "fireball25", "attachment": "fireball"}, {"name": "fireball3", "bone": "fireball15", "attachment": "fireball"}, {"name": "fireball6", "bone": "fireball30", "attachment": "fireball"}, {"name": "xiNgau1@3x copy 2", "bone": "root"}, {"name": "rong", "bone": "rong", "attachment": "rong"}, {"name": "gold", "bone": "gold", "attachment": "gold"}, {"name": "xx2", "bone": "xx2"}, {"name": "xx", "bone": "xx"}, {"name": "jackpot", "bone": "root", "attachment": "jackpot"}, {"name": "dau rong", "bone": "dau rong", "attachment": "dau rong"}, {"name": "explore 3", "bone": "explore 3", "attachment": "explore 3"}, {"name": "explore 4", "bone": "explore 4", "attachment": "explore 3"}, {"name": "explore 2", "bone": "explore 2", "attachment": "explore 2"}, {"name": "effect mat", "bone": "effect mat3", "attachment": "effect mat"}, {"name": "effect mat2", "bone": "effect mat4", "attachment": "effect mat"}, {"name": "light JP", "bone": "light JP", "attachment": "light JP"}, {"name": "rong effect", "bone": "rong effect", "attachment": "rong effect"}, {"name": "star", "bone": "star", "attachment": "star"}, {"name": "star8", "bone": "star8", "attachment": "star"}, {"name": "star9", "bone": "star9", "attachment": "star"}, {"name": "star2", "bone": "star2", "attachment": "star"}, {"name": "star7", "bone": "star7", "attachment": "star"}, {"name": "star10", "bone": "star10", "attachment": "star"}, {"name": "star3", "bone": "star3", "attachment": "star"}, {"name": "star6", "bone": "star6", "attachment": "star"}, {"name": "star11", "bone": "star11", "attachment": "star"}, {"name": "star4", "bone": "star4", "attachment": "star"}, {"name": "star5", "bone": "star5", "attachment": "star"}, {"name": "star12", "bone": "star12", "attachment": "star"}, {"name": "fireball path", "bone": "fire path", "attachment": "fireball path"}, {"name": "fireball path4", "bone": "fire path2", "attachment": "fireball path"}, {"name": "fireball path2", "bone": "fireball path2", "attachment": "fireball path"}, {"name": "fireball path5", "bone": "fireball path4", "attachment": "fireball path"}, {"name": "fireball path3", "bone": "fireball path3", "attachment": "fireball path"}, {"name": "fireball path6", "bone": "fireball path5", "attachment": "fireball path"}], "path": [{"name": "fireball path", "order": 0, "target": "fireball path", "bones": ["fireball", "fireball2", "fireball3", "fireball4", "fireball5"]}, {"name": "fireball path2", "order": 1, "target": "fireball path2", "rotation": -12.5, "bones": ["fireball6", "fireball7", "fireball8", "fireball9", "fireball10"]}, {"name": "fireball path3", "order": 2, "target": "fireball path3", "bones": ["fireball11", "fireball12", "fireball13", "fireball14", "fireball15"]}, {"name": "fireball path4", "order": 3, "target": "fireball path4", "bones": ["fireball16", "fireball17", "fireball18", "fireball19", "fireball20"]}, {"name": "fireball path5", "order": 4, "target": "fireball path5", "bones": ["fireball21", "fireball22", "fireball23", "fireball24", "fireball25"]}, {"name": "fireball path6", "order": 5, "target": "fireball path6", "bones": ["fireball26", "fireball27", "fireball28", "fireball29", "fireball30"]}], "skins": {"default": {"dau rong": {"dau rong": {"x": -51.13, "y": -96.85, "rotation": -0.12, "width": 455, "height": 400}}, "effect mat": {"effect mat": {"type": "mesh", "hull": 8, "width": 161, "height": 72, "uvs": [0.25785, 0.10679, 0.49274, 0.12271, 0.78695, 0.15984, 0.99999, 0.48348, 0.99999, 0.76468, 0.72289, 0.80712, 0.22937, 0.54184, 0, 0.16515, 0.49274, 0.63204], "triangles": [6, 7, 0, 6, 0, 1, 6, 1, 8, 6, 8, 5, 8, 2, 3, 5, 8, 3, 4, 5, 3, 8, 1, 2], "vertices": [2, 2, -81.88, 29.93, 0.03385, 3, 5.15, 17.16, 0.96615, 2, 2, -44.06, 28.78, 0.47751, 3, 42.97, 16.01, 0.52249, 1, 2, 3.3, 26.11, 1, 1, 2, 37.6, 2.81, 1, 1, 2, 37.6, -17.44, 1, 1, 2, -7.01, -20.49, 1, 2, 2, -86.47, -1.39, 0.0272, 3, 0.56, -14.17, 0.9728, 1, 3, -36.37, 12.96, 1, 2, 2, -44.06, -7.89, 0.55081, 3, 42.96, -20.66, 0.44919], "edges": [6, 8, 6, 4, 4, 2, 2, 0, 0, 14, 14, 12, 12, 16, 16, 10, 10, 8, 10, 12]}}, "effect mat2": {"effect mat": {"type": "mesh", "hull": 8, "width": 161, "height": 72, "uvs": [0.25785, 0.10679, 0.49274, 0.12271, 0.78695, 0.15984, 0.99999, 0.48348, 0.99999, 0.76468, 0.72289, 0.80712, 0.22937, 0.54184, 0, 0.16515, 0.49274, 0.63204], "triangles": [6, 7, 0, 6, 0, 1, 6, 1, 8, 6, 8, 5, 8, 2, 3, 5, 8, 3, 4, 5, 3, 8, 1, 2], "vertices": [2, 9, -81.88, 29.93, 0.03385, 10, 5.15, 17.16, 0.96615, 2, 9, -44.06, 28.78, 0.47751, 10, 42.97, 16.01, 0.52249, 1, 9, 3.3, 26.11, 1, 1, 9, 37.6, 2.81, 1, 1, 9, 37.6, -17.44, 1, 1, 9, -7.01, -20.49, 1, 2, 9, -86.47, -1.39, 0.0272, 10, 0.56, -14.17, 0.9728, 1, 10, -36.37, 12.96, 1, 2, 9, -44.06, -7.89, 0.55081, 10, 42.96, -20.66, 0.44919], "edges": [6, 8, 6, 4, 4, 2, 2, 0, 0, 14, 14, 12, 12, 16, 16, 10, 10, 8, 10, 12]}}, "explore 2": {"explore 2": {"x": -11.91, "y": -39.19, "rotation": 180, "width": 1808, "height": 1550}}, "explore 3": {"explore 3": {"x": 4.09, "y": -34.55, "scaleX": 1.77, "width": 1283, "height": 1221}}, "explore 4": {"explore 3": {"x": 4.09, "y": -34.55, "width": 1283, "height": 1221}}, "fireball": {"fireball": {"type": "mesh", "hull": 13, "width": 602, "height": 1302, "uvs": [1, 0.31591, 0.86707, 0.43879, 0.74043, 0.59673, 0.7065, 0.7395, 0.64335, 0.89693, 0.48958, 0.97438, 0.31659, 0.88932, 0.26991, 0.74712, 0.22598, 0.61382, 0.1793, 0.4602, 0.06672, 0.31038, 0.20748, 0, 0.83031, 0], "triangles": [9, 10, 11, 1, 11, 12, 1, 12, 0, 11, 1, 9, 2, 9, 1, 8, 9, 2, 2, 7, 8, 3, 7, 2, 4, 6, 7, 3, 4, 7, 5, 6, 4], "vertices": [2, 24, 276.67, -295.25, 0.61578, 25, 77.2, -297.29, 0.38422, 2, 23, 322.27, -220.4, 0.32156, 24, 114.72, -219.29, 0.67844, 1, 23, 115.49, -147.34, 1, 2, 22, 157.83, -127.65, 0.64611, 23, -70.7, -129.77, 0.35389, 1, 22, -46.6, -86.79, 1, 1, 22, -146.13, 7.17, 1, 1, 22, -33.95, 109.76, 1, 1, 22, 151.56, 135.29, 1, 3, 22, 325.48, 159.33, 0.18679, 23, 88.48, 161.98, 0.44921, 24, -122.82, 160.79, 0.364, 3, 24, 76.42, 193.92, 0.45817, 25, -110.66, 196.77, 0.45445, 26, -305.6, 194.62, 0.08738, 2, 25, 84.39, 264.54, 0.45172, 26, -111.03, 263.77, 0.54828, 1, 26, 293.68, 181.88, 1, 2, 25, 488.51, -195.14, 0.24432, 26, 296.32, -193.06, 0.75568], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 20, 22, 24, 0]}}, "fireball path": {"fireball path": {"type": "path", "vertexCount": 6, "lengths": [1723.83, 2634.93], "vertices": [-556.82, -343.04, -426.73, -346.18, 174.03, -360.66, 265.92, 1288.25, -465.73, 559.36, -553.93, 471.49]}}, "fireball path2": {"fireball path": {"type": "path", "vertexCount": 6, "lengths": [1812.58, 2690.88], "vertices": [-827.68, -246.9, -711.66, -305.83, -137.06, -597.73, 695.62, 584.13, -458.24, 540.18, -582.65, 535.44]}}, "fireball path3": {"fireball path": {"type": "path", "vertexCount": 6, "lengths": [1758.73, 2622.45], "vertices": [-810.97, -221.73, -711.66, -305.83, -184.83, -752.04, 518.41, 283.94, -506, 573.25, -625.82, 607.08]}}, "fireball path4": {"fireball path": {"type": "path", "vertexCount": 6, "lengths": [1541.21, 2463.83], "vertices": [-839.72, -464.25, -709.63, -467.42, 34.42, -485.58, 237.5, 345.03, -509.82, 386.68, -634.13, 393.61]}}, "fireball path5": {"fireball path": {"type": "path", "vertexCount": 6, "lengths": [1573.71, 2249.42], "vertices": [-775.29, -192.32, -711.66, -305.83, -467.72, -741.01, 601.29, 15.07, -469.26, 393.22, -586.65, 434.69]}}, "fireball path6": {"fireball path": {"type": "path", "vertexCount": 6, "lengths": [2015.6, 3068.58], "vertices": [-840.08, -326.89, -711.66, -305.83, 465.47, -112.76, 264.9, 1279.6, -443.54, 646.73, -536.39, 563.78]}}, "fireball2": {"fireball": {"type": "mesh", "hull": 13, "width": 602, "height": 1302, "uvs": [1, 0.31591, 0.86707, 0.43879, 0.74043, 0.59673, 0.7065, 0.7395, 0.64335, 0.89693, 0.48958, 0.97438, 0.31659, 0.88932, 0.26991, 0.74712, 0.22598, 0.61382, 0.1793, 0.4602, 0.06672, 0.31038, 0.20748, 0, 0.83031, 0], "triangles": [9, 10, 11, 1, 11, 12, 1, 12, 0, 11, 1, 9, 2, 9, 1, 8, 9, 2, 2, 7, 8, 3, 7, 2, 4, 6, 7, 3, 4, 7, 5, 6, 4], "vertices": [2, 30, 276.67, -295.25, 0.61578, 31, 77.2, -297.29, 0.38422, 2, 29, 322.27, -220.4, 0.32156, 30, 114.72, -219.29, 0.67844, 1, 29, 115.49, -147.34, 1, 2, 28, 157.83, -127.65, 0.64611, 29, -70.7, -129.77, 0.35389, 1, 28, -46.6, -86.79, 1, 1, 28, -146.13, 7.17, 1, 1, 28, -33.95, 109.76, 1, 1, 28, 151.56, 135.29, 1, 3, 28, 325.48, 159.33, 0.18679, 29, 88.48, 161.98, 0.44921, 30, -122.82, 160.79, 0.364, 3, 30, 76.42, 193.92, 0.45817, 31, -110.66, 196.77, 0.45445, 32, -305.6, 194.62, 0.08738, 2, 31, 84.39, 264.54, 0.45172, 32, -111.03, 263.77, 0.54828, 1, 32, 293.68, 181.88, 1, 2, 31, 488.51, -195.14, 0.24432, 32, 296.32, -193.06, 0.75568], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 20, 22, 24, 0]}}, "fireball3": {"fireball": {"type": "mesh", "hull": 13, "width": 602, "height": 1302, "uvs": [1, 0.31591, 0.86707, 0.43879, 0.74043, 0.59673, 0.7065, 0.7395, 0.64335, 0.89693, 0.48958, 0.97438, 0.31659, 0.88932, 0.26991, 0.74712, 0.22598, 0.61382, 0.1793, 0.4602, 0.06672, 0.31038, 0.20748, 0, 0.83031, 0], "triangles": [9, 10, 11, 1, 11, 12, 1, 12, 0, 11, 1, 9, 2, 9, 1, 8, 9, 2, 2, 7, 8, 3, 7, 2, 4, 6, 7, 3, 4, 7, 5, 6, 4], "vertices": [2, 37, 276.67, -295.25, 0.61578, 38, 77.2, -297.29, 0.38422, 2, 36, 322.27, -220.4, 0.32156, 37, 114.72, -219.29, 0.67844, 1, 36, 115.49, -147.34, 1, 2, 35, 157.83, -127.65, 0.64611, 36, -70.7, -129.77, 0.35389, 1, 35, -46.6, -86.79, 1, 1, 35, -146.13, 7.17, 1, 1, 35, -33.95, 109.76, 1, 1, 35, 151.56, 135.29, 1, 3, 35, 325.48, 159.33, 0.18679, 36, 88.48, 161.98, 0.44921, 37, -122.82, 160.79, 0.364, 3, 37, 76.42, 193.92, 0.45817, 38, -110.66, 196.77, 0.45445, 39, -305.6, 194.62, 0.08738, 2, 38, 84.39, 264.54, 0.45172, 39, -111.03, 263.77, 0.54828, 1, 39, 293.68, 181.88, 1, 2, 38, 488.51, -195.14, 0.24432, 39, 296.32, -193.06, 0.75568], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 20, 22, 24, 0]}}, "fireball4": {"fireball": {"type": "mesh", "hull": 13, "width": 602, "height": 1302, "uvs": [1, 0.31591, 0.86707, 0.43879, 0.74043, 0.59673, 0.7065, 0.7395, 0.64335, 0.89693, 0.48958, 0.97438, 0.31659, 0.88932, 0.26991, 0.74712, 0.22598, 0.61382, 0.1793, 0.4602, 0.06672, 0.31038, 0.20748, 0, 0.83031, 0], "triangles": [9, 10, 11, 1, 11, 12, 1, 12, 0, 11, 1, 9, 2, 9, 1, 8, 9, 2, 2, 7, 8, 3, 7, 2, 4, 6, 7, 3, 4, 7, 5, 6, 4], "vertices": [2, 45, 276.67, -295.25, 0.61578, 46, 77.2, -297.29, 0.38422, 2, 44, 322.27, -220.4, 0.32156, 45, 114.72, -219.29, 0.67844, 1, 44, 115.49, -147.34, 1, 2, 43, 157.83, -127.65, 0.64611, 44, -70.7, -129.77, 0.35389, 1, 43, -46.6, -86.79, 1, 1, 43, -146.13, 7.17, 1, 1, 43, -33.95, 109.76, 1, 1, 43, 151.56, 135.29, 1, 3, 43, 325.48, 159.33, 0.18679, 44, 88.48, 161.98, 0.44921, 45, -122.82, 160.79, 0.364, 3, 45, 76.42, 193.92, 0.45817, 46, -110.66, 196.77, 0.45445, 47, -305.6, 194.62, 0.08738, 2, 46, 84.39, 264.54, 0.45172, 47, -111.03, 263.77, 0.54828, 1, 47, 293.68, 181.88, 1, 2, 46, 488.51, -195.14, 0.24432, 47, 296.32, -193.06, 0.75568], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 20, 22, 24, 0]}}, "fireball5": {"fireball": {"type": "mesh", "hull": 13, "width": 602, "height": 1302, "uvs": [1, 0.31591, 0.86707, 0.43879, 0.74043, 0.59673, 0.7065, 0.7395, 0.64335, 0.89693, 0.48958, 0.97438, 0.31659, 0.88932, 0.26991, 0.74712, 0.22598, 0.61382, 0.1793, 0.4602, 0.06672, 0.31038, 0.20748, 0, 0.83031, 0], "triangles": [9, 10, 11, 1, 11, 12, 1, 12, 0, 11, 1, 9, 2, 9, 1, 8, 9, 2, 2, 7, 8, 3, 7, 2, 4, 6, 7, 3, 4, 7, 5, 6, 4], "vertices": [2, 50, 276.67, -295.25, 0.61578, 51, 77.2, -297.29, 0.38422, 2, 49, 322.27, -220.4, 0.32156, 50, 114.72, -219.29, 0.67844, 1, 49, 115.49, -147.34, 1, 2, 48, 157.83, -127.65, 0.64611, 49, -70.7, -129.77, 0.35389, 1, 48, -46.6, -86.79, 1, 1, 48, -146.13, 7.17, 1, 1, 48, -33.95, 109.76, 1, 1, 48, 151.56, 135.29, 1, 3, 48, 325.48, 159.33, 0.18679, 49, 88.48, 161.98, 0.44921, 50, -122.82, 160.79, 0.364, 3, 50, 76.42, 193.92, 0.45817, 51, -110.66, 196.77, 0.45445, 52, -305.6, 194.62, 0.08738, 2, 51, 84.39, 264.54, 0.45172, 52, -111.03, 263.77, 0.54828, 1, 52, 293.68, 181.88, 1, 2, 51, 488.51, -195.14, 0.24432, 52, 296.32, -193.06, 0.75568], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 20, 22, 24, 0]}}, "fireball6": {"fireball": {"type": "mesh", "hull": 13, "width": 602, "height": 1302, "uvs": [1, 0.31591, 0.86707, 0.43879, 0.74043, 0.59673, 0.7065, 0.7395, 0.64335, 0.89693, 0.48958, 0.97438, 0.31659, 0.88932, 0.26991, 0.74712, 0.22598, 0.61382, 0.1793, 0.4602, 0.06672, 0.31038, 0.20748, 0, 0.83031, 0], "triangles": [9, 10, 11, 1, 11, 12, 1, 12, 0, 11, 1, 9, 2, 9, 1, 8, 9, 2, 2, 7, 8, 3, 7, 2, 4, 6, 7, 3, 4, 7, 5, 6, 4], "vertices": [2, 55, 276.67, -295.25, 0.61578, 56, 77.2, -297.29, 0.38422, 2, 54, 322.27, -220.4, 0.32156, 55, 114.72, -219.29, 0.67844, 1, 54, 115.49, -147.34, 1, 2, 53, 157.83, -127.65, 0.64611, 54, -70.7, -129.77, 0.35389, 1, 53, -46.6, -86.79, 1, 1, 53, -146.13, 7.17, 1, 1, 53, -33.95, 109.76, 1, 1, 53, 151.56, 135.29, 1, 3, 53, 325.48, 159.33, 0.18679, 54, 88.48, 161.98, 0.44921, 55, -122.82, 160.79, 0.364, 3, 55, 76.42, 193.92, 0.45817, 56, -110.66, 196.77, 0.45445, 57, -305.6, 194.62, 0.08738, 2, 56, 84.39, 264.54, 0.45172, 57, -111.03, 263.77, 0.54828, 1, 57, 293.68, 181.88, 1, 2, 56, 488.51, -195.14, 0.24432, 57, 296.32, -193.06, 0.75568], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 22, 24, 20, 22, 24, 0]}}, "gold": {"gold": {"x": 110.51, "y": -9.1, "width": 365, "height": 253}}, "jackpot": {"jackpot": {"x": -1.06, "y": 1220.46, "width": 734, "height": 136}}, "light JP": {"light JP": {"x": 0.29, "y": -10.72, "width": 990, "height": 990}}, "rong": {"rong": {"x": -15.74, "y": 17.78, "width": 750, "height": 210}}, "rong effect": {"rong effect": {"x": -161.45, "y": 27.96, "width": 773, "height": 432}}, "star": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star10": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star11": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star12": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star2": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star3": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star4": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star5": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star6": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star7": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star8": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "star9": {"star": {"x": 0.51, "y": -1.02, "width": 177, "height": 177}}, "xiNgau1@3x copy 2": {"xiNgau1@3x copy 2": {"x": -329.56, "y": 999.46, "width": 1091, "height": 796}}}}, "animations": {"JP idle": {"slots": {"effect mat": {"color": [{"time": 0.2333, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}]}, "effect mat2": {"color": [{"time": 0.2333, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 1.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffffff"}]}, "explore 2": {"attachment": [{"time": 0, "name": null}]}, "explore 3": {"attachment": [{"time": 0, "name": null}]}, "explore 4": {"attachment": [{"time": 0, "name": null}]}, "fireball": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "fireball2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "fireball3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "fireball4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "fireball5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "fireball6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "light JP": {"attachment": [{"time": 0, "name": null}]}, "rong effect": {"attachment": [{"time": 0, "name": null}]}, "star": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff47"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.1333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "star2": {"color": [{"time": 0, "color": "ffffff8d"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff47"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.3333, "color": "ffffff8d"}], "attachment": [{"time": 1.9667, "name": null}]}, "star3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff47"}, {"time": 2.3333, "color": "ffffff00"}]}, "star4": {"color": [{"time": 0.4, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff47"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "star5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff47"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "star6": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff47"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.1333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}]}, "star7": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff47"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "star8": {"color": [{"time": 0.7333, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff47"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.9667, "color": "ffffffff"}]}, "star9": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff47"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "star10": {"color": [{"time": 0.6667, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff47"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2.1333, "color": "ffffffff"}]}, "star11": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff47"}, {"time": 2.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}, "star12": {"color": [{"time": 0.4, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff47"}, {"time": 1.3667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": null}, {"time": 1.6333, "name": "star"}]}}, "bones": {"dau rong": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0, "y": 3.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0, "y": 0}]}, "xx": {"translate": [{"time": 0, "x": 0, "y": 2.74, "curve": [0.36, 0.43, 0.755, 1]}, {"time": 0.3667, "x": 0, "y": 7.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 0, "y": 0, "curve": [0.259, 0, 0.618, 0.45]}, {"time": 1.1667, "x": 0, "y": 2.74, "curve": [0.36, 0.43, 0.755, 1]}, {"time": 1.5333, "x": 0, "y": 7.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1, "x": 0, "y": 0, "curve": [0.259, 0, 0.618, 0.45]}, {"time": 2.3333, "x": 0, "y": 2.74}]}, "xx2": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0, "y": 13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 0, "y": 13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0, "y": 0}]}, "gold": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 0, "y": -2.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0, "y": 0}]}, "effect mat3": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3667, "x": 11.49, "y": 18.29}, {"time": 0.7, "x": 42.02, "y": 1.08}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.5333, "x": 11.49, "y": 18.29}, {"time": 1.8667, "x": 42.02, "y": 1.08}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3667, "x": 0.236, "y": 1}, {"time": 0.7, "x": 0.551, "y": 1.41}, {"time": 1.1667, "x": 1, "y": 1}, {"time": 1.5333, "x": 0.236, "y": 1}, {"time": 1.8667, "x": 0.551, "y": 1.41}, {"time": 2.3333, "x": 1, "y": 1}]}, "effect mat4": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3667, "x": 11.49, "y": 18.29}, {"time": 0.7, "x": 42.02, "y": 1.08}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.5333, "x": 11.49, "y": 18.29}, {"time": 1.8667, "x": 42.02, "y": 1.08}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3667, "x": 0.236, "y": 1}, {"time": 0.7, "x": 0.551, "y": 1.41}, {"time": 1.1667, "x": 1, "y": 1}, {"time": 1.5333, "x": 0.236, "y": 1}, {"time": 1.8667, "x": 0.551, "y": 1.41}, {"time": 2.3333, "x": 1, "y": 1}]}, "star": {"rotate": [{"time": 0, "angle": 84}, {"time": 0.1333, "angle": 117.6}, {"time": 1.3, "angle": -120}, {"time": 1.8333, "angle": 0}, {"time": 2.3333, "angle": 84}], "translate": [{"time": 0, "x": 0, "y": 34.79}, {"time": 1.6333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 2.3333, "x": 0, "y": 34.79}], "scale": [{"time": 0, "x": 0.416, "y": 0.416}, {"time": 0.1333, "x": 0.556, "y": 0.556}, {"time": 0.6667, "x": 0.925, "y": 0.925}, {"time": 1.3, "x": 0.591, "y": 0.591}, {"time": 1.6333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.8333, "x": 0.067, "y": 0.067}, {"time": 2.3333, "x": 0.416, "y": 0.416}]}, "star2": {"rotate": [{"time": 0, "angle": 28}, {"time": 0.3333, "angle": 84}, {"time": 0.4667, "angle": 117.6}, {"time": 1.6333, "angle": -120}, {"time": 2.1667, "angle": 0}, {"time": 2.3333, "angle": 28}], "translate": [{"time": 0, "x": 0, "y": 11.6}, {"time": 0.3333, "x": 0, "y": 34.79}, {"time": 1.9667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.3333, "x": 0, "y": 11.6}], "scale": [{"time": 0, "x": 0.183, "y": 0.183}, {"time": 0.3333, "x": 0.416, "y": 0.416}, {"time": 0.4667, "x": 0.556, "y": 0.556}, {"time": 1, "x": 0.925, "y": 0.925}, {"time": 1.6333, "x": 0.591, "y": 0.591}, {"time": 1.9667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 2.1667, "x": 0.067, "y": 0.067}, {"time": 2.3333, "x": 0.183, "y": 0.183}]}, "star3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6, "angle": 117.6}, {"time": 1.7667, "angle": -120}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 2.1333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.6, "x": 0.556, "y": 0.556}, {"time": 1.1667, "x": 0.925, "y": 0.925}, {"time": 1.7667, "x": 0.591, "y": 0.591}, {"time": 2.1333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 2.3333, "x": 0.067, "y": 0.067}]}, "star4": {"rotate": [{"time": 0, "angle": 148.2}, {"time": 0.8333, "angle": -120}, {"time": 1.3667, "angle": 0}, {"time": 1.8333, "angle": 84}, {"time": 2.0333, "angle": 117.6}, {"time": 2.3333, "angle": 148.2}], "translate": [{"time": 0, "x": 0, "y": 69.59}, {"time": 1.1667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0}, {"time": 1.8333, "x": 0, "y": 34.79}, {"time": 2.3333, "x": 0, "y": 69.59}], "scale": [{"time": 0, "x": 0.74, "y": 0.74}, {"time": 0.2, "x": 0.925, "y": 0.925}, {"time": 0.8333, "x": 0.591, "y": 0.591}, {"time": 1.1667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.3667, "x": 0.067, "y": 0.067}, {"time": 1.8333, "x": 0.416, "y": 0.416}, {"time": 2.0333, "x": 0.556, "y": 0.556}, {"time": 2.3333, "x": 0.74, "y": 0.74}]}, "star5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6, "angle": 117.6}, {"time": 1.7667, "angle": -120}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 2.1333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.6, "x": 0.556, "y": 0.556}, {"time": 1.1667, "x": 0.925, "y": 0.925}, {"time": 1.7667, "x": 0.591, "y": 0.591}, {"time": 2.1333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 2.3333, "x": 0.067, "y": 0.067}]}, "star6": {"rotate": [{"time": 0, "angle": 84}, {"time": 0.1333, "angle": 117.6}, {"time": 1.3, "angle": -120}, {"time": 1.8333, "angle": 0}, {"time": 2.3333, "angle": 84}], "translate": [{"time": 0, "x": 0, "y": 34.79}, {"time": 1.6333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 2.3333, "x": 0, "y": 34.79}], "scale": [{"time": 0, "x": 0.416, "y": 0.416}, {"time": 0.1333, "x": 0.556, "y": 0.556}, {"time": 0.6667, "x": 0.925, "y": 0.925}, {"time": 1.3, "x": 0.591, "y": 0.591}, {"time": 1.6333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.8333, "x": 0.067, "y": 0.067}, {"time": 2.3333, "x": 0.416, "y": 0.416}]}, "star7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6, "angle": 117.6}, {"time": 1.7667, "angle": -120}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 2.1333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.6, "x": 0.556, "y": 0.556}, {"time": 1.1667, "x": 0.925, "y": 0.925}, {"time": 1.7667, "x": 0.591, "y": 0.591}, {"time": 2.1333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 2.3333, "x": 0.067, "y": 0.067}]}, "star8": {"rotate": [{"time": 0, "angle": 112}, {"time": 0.0333, "angle": 117.6}, {"time": 0.3333, "angle": 148.2}, {"time": 1.1667, "angle": -120}, {"time": 1.7, "angle": 0}, {"time": 2.1667, "angle": 84}, {"time": 2.3333, "angle": 112}], "translate": [{"time": 0, "x": 0, "y": 46.39}, {"time": 0.3333, "x": 0, "y": 69.59}, {"time": 1.5, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2.1667, "x": 0, "y": 34.79}, {"time": 2.3333, "x": 0, "y": 46.39}], "scale": [{"time": 0, "x": 0.532, "y": 0.532}, {"time": 0.0333, "x": 0.556, "y": 0.556}, {"time": 0.3333, "x": 0.74, "y": 0.74}, {"time": 0.5333, "x": 0.925, "y": 0.925}, {"time": 1.1667, "x": 0.591, "y": 0.591}, {"time": 1.5, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.7, "x": 0.067, "y": 0.067}, {"time": 2.1667, "x": 0.416, "y": 0.416}, {"time": 2.3333, "x": 0.532, "y": 0.532}]}, "star9": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6, "angle": 117.6}, {"time": 1.7667, "angle": -120}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 2.1333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.6, "x": 0.556, "y": 0.556}, {"time": 1.1667, "x": 0.925, "y": 0.925}, {"time": 1.7667, "x": 0.591, "y": 0.591}, {"time": 2.1333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 2.3333, "x": 0.067, "y": 0.067}]}, "star10": {"rotate": [{"time": 0, "angle": 84}, {"time": 0.1333, "angle": 117.6}, {"time": 1.3, "angle": -120}, {"time": 1.8333, "angle": 0}, {"time": 2.3333, "angle": 84}], "translate": [{"time": 0, "x": 0, "y": 34.79}, {"time": 1.6333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 2.3333, "x": 0, "y": 34.79}], "scale": [{"time": 0, "x": 0.416, "y": 0.416}, {"time": 0.1333, "x": 0.556, "y": 0.556}, {"time": 0.6667, "x": 0.925, "y": 0.925}, {"time": 1.3, "x": 0.591, "y": 0.591}, {"time": 1.6333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.8333, "x": 0.067, "y": 0.067}, {"time": 2.3333, "x": 0.416, "y": 0.416}]}, "star11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6, "angle": 117.6}, {"time": 1.7667, "angle": -120}, {"time": 2.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 2.1333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.6, "x": 0.556, "y": 0.556}, {"time": 1.1667, "x": 0.925, "y": 0.925}, {"time": 1.7667, "x": 0.591, "y": 0.591}, {"time": 2.1333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 2.3333, "x": 0.067, "y": 0.067}]}, "star12": {"rotate": [{"time": 0, "angle": 148.2}, {"time": 0.8333, "angle": -120}, {"time": 1.3667, "angle": 0}, {"time": 1.8333, "angle": 84}, {"time": 2.0333, "angle": 117.6}, {"time": 2.3333, "angle": 148.2}], "translate": [{"time": 0, "x": 0, "y": 69.59}, {"time": 1.1667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0}, {"time": 1.8333, "x": 0, "y": 34.79}, {"time": 2.3333, "x": 0, "y": 69.59}], "scale": [{"time": 0, "x": 0.74, "y": 0.74}, {"time": 0.2, "x": 0.925, "y": 0.925}, {"time": 0.8333, "x": 0.591, "y": 0.591}, {"time": 1.1667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.3667, "x": 0.067, "y": 0.067}, {"time": 1.8333, "x": 0.416, "y": 0.416}, {"time": 2.0333, "x": 0.556, "y": 0.556}, {"time": 2.3333, "x": 0.74, "y": 0.74}]}, "fireball": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.9333, "x": 0.496, "y": 0.496}, {"time": 1.1667, "x": 0.508, "y": 0.508}, {"time": 2.1, "x": 0.496, "y": 0.496}]}, "fireball6": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.9333, "x": 0.496, "y": 0.496}, {"time": 1.1667, "x": 0.508, "y": 0.508}, {"time": 2.1, "x": 0.496, "y": 0.496}]}, "fireball11": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.9333, "x": 0.496, "y": 0.496}, {"time": 1.1667, "x": 0.508, "y": 0.508}, {"time": 2.1, "x": 0.496, "y": 0.496}]}, "fireball16": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.9333, "x": 0.496, "y": 0.496}, {"time": 1.1667, "x": 0.508, "y": 0.508}, {"time": 2.1, "x": 0.496, "y": 0.496}]}, "fireball21": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.9333, "x": 0.496, "y": 0.496}, {"time": 1.1667, "x": 0.508, "y": 0.508}, {"time": 2.1, "x": 0.496, "y": 0.496}]}, "fireball26": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.9333, "x": 0.496, "y": 0.496}, {"time": 1.1667, "x": 0.508, "y": 0.508}, {"time": 2.1, "x": 0.496, "y": 0.496}]}, "rong effect": {"translate": [{"time": 1.1667, "x": 0, "y": 3.53}]}}}, "JP no hu": {"slots": {"effect mat": {"color": [{"time": 0.1761, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0095, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}]}, "effect mat2": {"color": [{"time": 0.1761, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0095, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff"}]}, "explore 2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffff82"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}]}, "explore 3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}]}, "explore 4": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.2, "color": "fffffffb"}, {"time": 1.6667, "color": "ffffff00"}]}, "fireball": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "fireball2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "fireball3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "fireball4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "fireball5": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "fireball6": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}]}, "light JP": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 1.0667, "color": "ffffff77"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}]}, "rong effect": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffff71"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}]}, "star": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff47"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff47"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff"}]}, "star2": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff47"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff47"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff"}]}, "star3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff47"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff47"}, {"time": 1.6667, "color": "ffffff00"}]}, "star4": {"color": [{"time": 0.1761, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff47"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0095, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff47"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}]}, "star5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff47"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff47"}, {"time": 1.6667, "color": "ffffff00"}]}, "star6": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff47"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff47"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff"}]}, "star7": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff47"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff47"}, {"time": 1.6667, "color": "ffffff00"}]}, "star8": {"color": [{"time": 0.1761, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff47"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0095, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff47"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}]}, "star9": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff47"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff47"}, {"time": 1.6667, "color": "ffffff00"}]}, "star10": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.4667, "color": "ffffff47"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff47"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff"}]}, "star11": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.6333, "color": "ffffff47"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff47"}, {"time": 1.6667, "color": "ffffff00"}]}, "star12": {"color": [{"time": 0.1761, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff47"}, {"time": 0.5, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0095, "color": "ffffffff"}, {"time": 1.1333, "color": "ffffff47"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4333, "color": "ffffffff"}]}}, "bones": {"dau rong": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -7.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0, "y": -7.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}]}, "xx": {"translate": [{"time": 0, "x": 0, "y": 2.74, "curve": [0.36, 0.43, 0.755, 1]}, {"time": 0.2667, "x": 0, "y": 7.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.259, 0, 0.618, 0.45]}, {"time": 0.8333, "x": 0, "y": 2.74, "curve": [0.36, 0.43, 0.755, 1]}, {"time": 1.1, "x": 0, "y": 7.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 0, "curve": [0.259, 0, 0.618, 0.45]}, {"time": 1.6667, "x": 0, "y": 2.74}]}, "xx2": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": 13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 0, "y": 13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}]}, "gold": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1.01, "y": 1.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1.01, "y": 1.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1}]}, "effect mat3": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": 11.49, "y": 18.29}, {"time": 0.5, "x": 42.02, "y": 1.08}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.1, "x": 11.49, "y": 18.29}, {"time": 1.3333, "x": 42.02, "y": 1.08}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.2667, "x": 0.236, "y": 1}, {"time": 0.5, "x": 0.551, "y": 1.41}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 1.1, "x": 0.236, "y": 1}, {"time": 1.3333, "x": 0.551, "y": 1.41}, {"time": 1.6667, "x": 1, "y": 1}]}, "effect mat4": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": 11.49, "y": 18.29}, {"time": 0.5, "x": 42.02, "y": 1.08}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.1, "x": 11.49, "y": 18.29}, {"time": 1.3333, "x": 42.02, "y": 1.08}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.2667, "x": 0.236, "y": 1}, {"time": 0.5, "x": 0.551, "y": 1.41}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 1.1, "x": 0.236, "y": 1}, {"time": 1.3333, "x": 0.551, "y": 1.41}, {"time": 1.6667, "x": 1, "y": 1}]}, "star": {"rotate": [{"time": 0, "angle": 84}, {"time": 0.0667, "angle": 117.6}, {"time": 0.4667, "angle": -120}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": 84}, {"time": 0.9, "angle": 117.6}, {"time": 1.3, "angle": -120}, {"time": 1.5, "angle": 0}, {"time": 1.6667, "angle": 84}], "translate": [{"time": 0, "x": 0, "y": 34.79}, {"time": 0.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 34.79}, {"time": 1.4333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 34.79}], "scale": [{"time": 0, "x": 0.416, "y": 0.416}, {"time": 0.0667, "x": 0.556, "y": 0.556}, {"time": 0.2667, "x": 0.925, "y": 0.925}, {"time": 0.4667, "x": 0.591, "y": 0.591}, {"time": 0.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.6667, "x": 0.067, "y": 0.067}, {"time": 0.8333, "x": 0.416, "y": 0.416}, {"time": 0.9, "x": 0.556, "y": 0.556}, {"time": 1.1, "x": 0.925, "y": 0.925}, {"time": 1.3, "x": 0.591, "y": 0.591}, {"time": 1.4333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.5, "x": 0.067, "y": 0.067}, {"time": 1.6667, "x": 0.416, "y": 0.416}]}, "star2": {"rotate": [{"time": 0, "angle": 84}, {"time": 0.0667, "angle": 117.6}, {"time": 0.4667, "angle": -120}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": 84}, {"time": 0.9, "angle": 117.6}, {"time": 1.3, "angle": -120}, {"time": 1.5, "angle": 0}, {"time": 1.6667, "angle": 84}], "translate": [{"time": 0, "x": 0, "y": 34.79}, {"time": 0.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 34.79}, {"time": 1.4333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 34.79}], "scale": [{"time": 0, "x": 0.416, "y": 0.416}, {"time": 0.0667, "x": 0.556, "y": 0.556}, {"time": 0.2667, "x": 0.925, "y": 0.925}, {"time": 0.4667, "x": 0.591, "y": 0.591}, {"time": 0.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.6667, "x": 0.067, "y": 0.067}, {"time": 0.8333, "x": 0.416, "y": 0.416}, {"time": 0.9, "x": 0.556, "y": 0.556}, {"time": 1.1, "x": 0.925, "y": 0.925}, {"time": 1.3, "x": 0.591, "y": 0.591}, {"time": 1.4333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.5, "x": 0.067, "y": 0.067}, {"time": 1.6667, "x": 0.416, "y": 0.416}]}, "star3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 117.6}, {"time": 0.6333, "angle": -120}, {"time": 0.8333, "angle": 0}, {"time": 1.0667, "angle": 117.6}, {"time": 1.4667, "angle": -120}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.2333, "x": 0.556, "y": 0.556}, {"time": 0.4333, "x": 0.925, "y": 0.925}, {"time": 0.6333, "x": 0.591, "y": 0.591}, {"time": 0.7667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.8333, "x": 0.067, "y": 0.067}, {"time": 1.0667, "x": 0.556, "y": 0.556}, {"time": 1.2667, "x": 0.925, "y": 0.925}, {"time": 1.4667, "x": 0.591, "y": 0.591}, {"time": 1.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.6667, "x": 0.067, "y": 0.067}]}, "star4": {"rotate": [{"time": 0, "angle": 148.2}, {"time": 0.3, "angle": -120}, {"time": 0.5, "angle": 0}, {"time": 0.6667, "angle": 84}, {"time": 0.7333, "angle": 117.6}, {"time": 0.8333, "angle": 148.2}, {"time": 1.1333, "angle": -120}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": 84}, {"time": 1.5667, "angle": 117.6}, {"time": 1.6667, "angle": 148.2}], "translate": [{"time": 0, "x": 0, "y": 69.59}, {"time": 0.4333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 34.79}, {"time": 0.8333, "x": 0, "y": 69.59}, {"time": 1.2667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 34.79}, {"time": 1.6667, "x": 0, "y": 69.59}], "scale": [{"time": 0, "x": 0.74, "y": 0.74}, {"time": 0.1, "x": 0.925, "y": 0.925}, {"time": 0.3, "x": 0.591, "y": 0.591}, {"time": 0.4333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.5, "x": 0.067, "y": 0.067}, {"time": 0.6667, "x": 0.416, "y": 0.416}, {"time": 0.7333, "x": 0.556, "y": 0.556}, {"time": 0.8333, "x": 0.74, "y": 0.74}, {"time": 0.9333, "x": 0.925, "y": 0.925}, {"time": 1.1333, "x": 0.591, "y": 0.591}, {"time": 1.2667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.3333, "x": 0.067, "y": 0.067}, {"time": 1.5, "x": 0.416, "y": 0.416}, {"time": 1.5667, "x": 0.556, "y": 0.556}, {"time": 1.6667, "x": 0.74, "y": 0.74}]}, "star5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 117.6}, {"time": 0.6333, "angle": -120}, {"time": 0.8333, "angle": 0}, {"time": 1.0667, "angle": 117.6}, {"time": 1.4667, "angle": -120}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.2333, "x": 0.556, "y": 0.556}, {"time": 0.4333, "x": 0.925, "y": 0.925}, {"time": 0.6333, "x": 0.591, "y": 0.591}, {"time": 0.7667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.8333, "x": 0.067, "y": 0.067}, {"time": 1.0667, "x": 0.556, "y": 0.556}, {"time": 1.2667, "x": 0.925, "y": 0.925}, {"time": 1.4667, "x": 0.591, "y": 0.591}, {"time": 1.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.6667, "x": 0.067, "y": 0.067}]}, "star6": {"rotate": [{"time": 0, "angle": 84}, {"time": 0.0667, "angle": 117.6}, {"time": 0.4667, "angle": -120}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": 84}, {"time": 0.9, "angle": 117.6}, {"time": 1.3, "angle": -120}, {"time": 1.5, "angle": 0}, {"time": 1.6667, "angle": 84}], "translate": [{"time": 0, "x": 0, "y": 34.79}, {"time": 0.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 34.79}, {"time": 1.4333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 34.79}], "scale": [{"time": 0, "x": 0.416, "y": 0.416}, {"time": 0.0667, "x": 0.556, "y": 0.556}, {"time": 0.2667, "x": 0.925, "y": 0.925}, {"time": 0.4667, "x": 0.591, "y": 0.591}, {"time": 0.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.6667, "x": 0.067, "y": 0.067}, {"time": 0.8333, "x": 0.416, "y": 0.416}, {"time": 0.9, "x": 0.556, "y": 0.556}, {"time": 1.1, "x": 0.925, "y": 0.925}, {"time": 1.3, "x": 0.591, "y": 0.591}, {"time": 1.4333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.5, "x": 0.067, "y": 0.067}, {"time": 1.6667, "x": 0.416, "y": 0.416}]}, "star7": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 117.6}, {"time": 0.6333, "angle": -120}, {"time": 0.8333, "angle": 0}, {"time": 1.0667, "angle": 117.6}, {"time": 1.4667, "angle": -120}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.2333, "x": 0.556, "y": 0.556}, {"time": 0.4333, "x": 0.925, "y": 0.925}, {"time": 0.6333, "x": 0.591, "y": 0.591}, {"time": 0.7667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.8333, "x": 0.067, "y": 0.067}, {"time": 1.0667, "x": 0.556, "y": 0.556}, {"time": 1.2667, "x": 0.925, "y": 0.925}, {"time": 1.4667, "x": 0.591, "y": 0.591}, {"time": 1.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.6667, "x": 0.067, "y": 0.067}]}, "star8": {"rotate": [{"time": 0, "angle": 148.2}, {"time": 0.3, "angle": -120}, {"time": 0.5, "angle": 0}, {"time": 0.6667, "angle": 84}, {"time": 0.7333, "angle": 117.6}, {"time": 0.8333, "angle": 148.2}, {"time": 1.1333, "angle": -120}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": 84}, {"time": 1.5667, "angle": 117.6}, {"time": 1.6667, "angle": 148.2}], "translate": [{"time": 0, "x": 0, "y": 69.59}, {"time": 0.4333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 34.79}, {"time": 0.8333, "x": 0, "y": 69.59}, {"time": 1.2667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 34.79}, {"time": 1.6667, "x": 0, "y": 69.59}], "scale": [{"time": 0, "x": 0.74, "y": 0.74}, {"time": 0.1, "x": 0.925, "y": 0.925}, {"time": 0.3, "x": 0.591, "y": 0.591}, {"time": 0.4333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.5, "x": 0.067, "y": 0.067}, {"time": 0.6667, "x": 0.416, "y": 0.416}, {"time": 0.7333, "x": 0.556, "y": 0.556}, {"time": 0.8333, "x": 0.74, "y": 0.74}, {"time": 0.9333, "x": 0.925, "y": 0.925}, {"time": 1.1333, "x": 0.591, "y": 0.591}, {"time": 1.2667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.3333, "x": 0.067, "y": 0.067}, {"time": 1.5, "x": 0.416, "y": 0.416}, {"time": 1.5667, "x": 0.556, "y": 0.556}, {"time": 1.6667, "x": 0.74, "y": 0.74}]}, "star9": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 117.6}, {"time": 0.6333, "angle": -120}, {"time": 0.8333, "angle": 0}, {"time": 1.0667, "angle": 117.6}, {"time": 1.4667, "angle": -120}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.2333, "x": 0.556, "y": 0.556}, {"time": 0.4333, "x": 0.925, "y": 0.925}, {"time": 0.6333, "x": 0.591, "y": 0.591}, {"time": 0.7667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.8333, "x": 0.067, "y": 0.067}, {"time": 1.0667, "x": 0.556, "y": 0.556}, {"time": 1.2667, "x": 0.925, "y": 0.925}, {"time": 1.4667, "x": 0.591, "y": 0.591}, {"time": 1.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.6667, "x": 0.067, "y": 0.067}]}, "star10": {"rotate": [{"time": 0, "angle": 84}, {"time": 0.0667, "angle": 117.6}, {"time": 0.4667, "angle": -120}, {"time": 0.6667, "angle": 0}, {"time": 0.8333, "angle": 84}, {"time": 0.9, "angle": 117.6}, {"time": 1.3, "angle": -120}, {"time": 1.5, "angle": 0}, {"time": 1.6667, "angle": 84}], "translate": [{"time": 0, "x": 0, "y": 34.79}, {"time": 0.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.8333, "x": 0, "y": 34.79}, {"time": 1.4333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.5, "x": 0, "y": 0}, {"time": 1.6667, "x": 0, "y": 34.79}], "scale": [{"time": 0, "x": 0.416, "y": 0.416}, {"time": 0.0667, "x": 0.556, "y": 0.556}, {"time": 0.2667, "x": 0.925, "y": 0.925}, {"time": 0.4667, "x": 0.591, "y": 0.591}, {"time": 0.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.6667, "x": 0.067, "y": 0.067}, {"time": 0.8333, "x": 0.416, "y": 0.416}, {"time": 0.9, "x": 0.556, "y": 0.556}, {"time": 1.1, "x": 0.925, "y": 0.925}, {"time": 1.3, "x": 0.591, "y": 0.591}, {"time": 1.4333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.5, "x": 0.067, "y": 0.067}, {"time": 1.6667, "x": 0.416, "y": 0.416}]}, "star11": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.2333, "angle": 117.6}, {"time": 0.6333, "angle": -120}, {"time": 0.8333, "angle": 0}, {"time": 1.0667, "angle": 117.6}, {"time": 1.4667, "angle": -120}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.6, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.067, "y": 0.067}, {"time": 0.2333, "x": 0.556, "y": 0.556}, {"time": 0.4333, "x": 0.925, "y": 0.925}, {"time": 0.6333, "x": 0.591, "y": 0.591}, {"time": 0.7667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.8333, "x": 0.067, "y": 0.067}, {"time": 1.0667, "x": 0.556, "y": 0.556}, {"time": 1.2667, "x": 0.925, "y": 0.925}, {"time": 1.4667, "x": 0.591, "y": 0.591}, {"time": 1.6, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.6667, "x": 0.067, "y": 0.067}]}, "star12": {"rotate": [{"time": 0, "angle": 148.2}, {"time": 0.3, "angle": -120}, {"time": 0.5, "angle": 0}, {"time": 0.6667, "angle": 84}, {"time": 0.7333, "angle": 117.6}, {"time": 0.8333, "angle": 148.2}, {"time": 1.1333, "angle": -120}, {"time": 1.3333, "angle": 0}, {"time": 1.5, "angle": 84}, {"time": 1.5667, "angle": 117.6}, {"time": 1.6667, "angle": 148.2}], "translate": [{"time": 0, "x": 0, "y": 69.59}, {"time": 0.4333, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": 34.79}, {"time": 0.8333, "x": 0, "y": 69.59}, {"time": 1.2667, "x": 0, "y": 160.05, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.5, "x": 0, "y": 34.79}, {"time": 1.6667, "x": 0, "y": 69.59}], "scale": [{"time": 0, "x": 0.74, "y": 0.74}, {"time": 0.1, "x": 0.925, "y": 0.925}, {"time": 0.3, "x": 0.591, "y": 0.591}, {"time": 0.4333, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 0.5, "x": 0.067, "y": 0.067}, {"time": 0.6667, "x": 0.416, "y": 0.416}, {"time": 0.7333, "x": 0.556, "y": 0.556}, {"time": 0.8333, "x": 0.74, "y": 0.74}, {"time": 0.9333, "x": 0.925, "y": 0.925}, {"time": 1.1333, "x": 0.591, "y": 0.591}, {"time": 1.2667, "x": 0.404, "y": 0.404, "curve": "stepped"}, {"time": 1.3333, "x": 0.067, "y": 0.067}, {"time": 1.5, "x": 0.416, "y": 0.416}, {"time": 1.5667, "x": 0.556, "y": 0.556}, {"time": 1.6667, "x": 0.74, "y": 0.74}]}, "fireball": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.6667, "x": 0.496, "y": 0.496}, {"time": 0.9667, "x": 0.851, "y": 0.851}]}, "fireball6": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.6667, "x": 0.496, "y": 0.496}, {"time": 0.9667, "x": 0.851, "y": 0.851}]}, "fireball11": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.6667, "x": 0.496, "y": 0.496}, {"time": 0.9667, "x": 0.851, "y": 0.851}]}, "fireball16": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.6667, "x": 0.496, "y": 0.496}, {"time": 0.9667, "x": 0.851, "y": 0.851}]}, "fireball21": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.6667, "x": 0.496, "y": 0.496}, {"time": 0.9667, "x": 0.851, "y": 0.851}]}, "fireball26": {"rotate": [{"time": 0, "angle": -103.75}], "translate": [{"time": 0, "x": -748.55, "y": 288.52}], "scale": [{"time": 0, "x": 0.508, "y": 0.508}, {"time": 0.6667, "x": 0.496, "y": 0.496}, {"time": 0.9667, "x": 0.851, "y": 0.851}]}, "circle": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1333, "angle": 117.6}, {"time": 0.3, "angle": -120}, {"time": 0.4333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.1, "x": 15.27, "y": 0}, {"time": 0.1667, "x": -5.74, "y": 0}, {"time": 0.2333, "x": 7.24, "y": 0}, {"time": 0.3333, "x": -26.35, "y": 0}, {"time": 0.4333, "x": 0, "y": 0}]}, "light JP": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 120}, {"time": 1.0667, "angle": -120}, {"time": 1.6667, "angle": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.67, "y": 0.67}, {"time": 1.2, "x": 0.655, "y": 0.655}, {"time": 1.3667, "x": 1.541, "y": 1.541}, {"time": 1.5, "x": 1.114, "y": 1.114}]}, "explore 3": {"scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.8, "x": 1.522, "y": 1.522}, {"time": 1, "x": 1, "y": 1}]}, "rong effect": {"translate": [{"time": 0, "x": 9.49, "y": -7.12}, {"time": 1.2667, "x": 0, "y": 0}, {"time": 1.3667, "x": 142.38, "y": -2.37}, {"time": 1.6667, "x": 284.69, "y": -2.37}], "scale": [{"time": 1.2667, "x": 1, "y": 1}, {"time": 1.3667, "x": 1.475, "y": 1.475}, {"time": 1.6667, "x": 2.08, "y": 2.08}]}, "explore 4": {"scale": [{"time": 0, "x": 1.345, "y": 1.345}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 0.7333, "x": 2.126, "y": 2.126}, {"time": 1.1, "x": 1, "y": 1}]}, "explore 2": {"rotate": [{"time": 0, "angle": 180}, {"time": 0.5, "angle": -60}, {"time": 1.0667, "angle": 60}, {"time": 1.6667, "angle": 180}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 0.434, "y": 0.434}, {"time": 0.7333, "x": 1.008, "y": 1.008}, {"time": 1.6667, "x": 0.771, "y": 0.771}]}}, "paths": {"fireball path": {"position": [{"time": 0, "position": -0.4019}, {"time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "position": 0.86, "curve": "stepped"}, {"time": 1.6667, "position": -0.4019}]}, "fireball path2": {"position": [{"time": 0, "position": -0.4019}, {"time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "position": 0.86, "curve": "stepped"}, {"time": 1.6667, "position": -0.4019}]}, "fireball path3": {"position": [{"time": 0, "position": -0.4019}, {"time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "position": 0.86, "curve": "stepped"}, {"time": 1.6667, "position": -0.4019}]}, "fireball path4": {"position": [{"time": 0, "position": -0.4019}, {"time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "position": 0.86, "curve": "stepped"}, {"time": 1.6667, "position": -0.4019}]}, "fireball path5": {"position": [{"time": 0, "position": -0.4019}, {"time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "position": 0.86, "curve": "stepped"}, {"time": 1.6667, "position": -0.4019}]}, "fireball path6": {"position": [{"time": 0, "position": -0.4019}, {"time": 0.8333, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "position": 0.86, "curve": "stepped"}, {"time": 1.6667, "position": -0.4019}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]