[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "e5/rg0C8NG2oD8IPbSsM+Z", "b0gtZg9ZBIhKVpd6KuWiRr", "e1NJ8c5BZHI5JcP4RUdG0s", "9d+PxZkqRKJ7i9c7u2FTVj", "2cWB/vWPRHja3uQTinHH30", "d5uTQJTiJNC68RJJCBPnoX", "b3jvKTsHJKM4n+yRVZPy8F"], ["node", "_N$file", "_spriteFrame", "lbBet", "lbTime", "_textureSetter", "root", "historyListView", "lbSessionID", "lbWin", "lbRefund", "lbResult", "lbSide", "lbSession", "_N$target", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 1, 5, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_color"], 2, 1, 2, 4, 5, 7, 2, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$overflow", "_enableWrapText", "_N$verticalAlign", "node", "_materials"], -5, 1, 3], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["f6c9fCgiwZD4LZvSi9f1r+Z", ["node", "historyListView"], 3, 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 5, 5], ["3b98ex1jiVGqJi16JO3Fvjc", ["node", "lbSession", "lbTime", "lbSide", "lbResult", "lbBet", "lbRefund", "lbWin"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["0a9402v8J1KU4ad3pVsj7Ot", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[5, 0, 1, 2], [3, 0, 1, 2, 6, 3, 4, 5, 8, 9, 8], [0, 0, 5, 4, 3, 6, 7, 2], [2, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 6, 3, 4, 7, 5, 8, 9, 9], [2, 0, 1, 2, 3, 7, 4, 5, 2], [0, 0, 5, 8, 4, 3, 6, 7, 2], [6, 1, 6, 1], [7, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 6, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 4, 3, 6, 2], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 2, 5, 4, 3, 6, 7, 3], [0, 0, 5, 8, 3, 7, 2], [0, 0, 5, 3, 6, 9, 7, 2], [2, 0, 1, 6, 2, 3, 4, 5, 2], [8, 0, 1, 2, 1], [9, 0, 1, 1], [5, 1, 1], [1, 3, 2, 0, 1, 4, 5, 6, 5], [1, 0, 4, 5, 6, 2], [1, 2, 0, 1, 4, 5, 6, 4], [1, 0, 4, 5, 2], [1, 4, 5, 6, 1], [1, 0, 1, 4, 5, 6, 3], [10, 0, 1, 2, 3, 4, 5, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7, 1], [6, 0, 1, 2, 3, 4, 5, 2], [12, 0, 1, 2, 3], [13, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 7, 5, 8, 9, 8], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 3, 4, 5, 4]], [[[[8, "BacaratHistoryView"], [9, "BacaratHistoryView", [-5, -6, -7, -8, -9, -10, -11], [[18, -2, [27, 28], 26], [19, -4, -3]], [20, -1]], [6, "title", 1, [-17, -18, -19, -20, -21, -22, -23], [[21, false, 1, 0, false, -12, [16], 17], [27, -16, -15, -14, -13, [4, 4278246399], [4, 4294829568]]], [0, "48v211hllFiotpVQ3ke40P", 1], [5, 994, 50], [0, 169.373, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "<PERSON><PERSON>", [-32, -33, -34, -35, -36, -37, -38], [[28, -31, -30, -29, -28, -27, -26, -25, -24]], [0, "1eGzZxUzRJLryQoPAUs1/k", 1], [5, 994, 62]], [17, "scrollview", 1, [-41, -42], [-39, -40], [0, "5fapKa+LJHfYJnKfxzMbR3", 1], [5, 1000, 410], [0, -69.976, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-45], [[29, 3, -44, [[30, "f6c9fCgiwZD4LZvSi9f1r+Z", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -43]], [0, "ebSQP0PKNIeY5i7PPEE5CS", 1], [5, 80, 80], [504.338, 223.31, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "black", 100, 1, [[22, 0, -46, [0], 1], [7, -47, [4, 4292269782]]], [0, "43KEBGUuFCObspBfMGvhef", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "nen popup", 1, [[23, 1, 0, false, -48, [2], 3], [7, -49, [4, 4292269782]]], [0, "a20qI87q9Do4ga3JgLbtUu", 1], [5, 1084, 618]], [13, "view", 4, [-51], [[31, 0, -50, [25]]], [0, "f2uxixrKVBIL5M+12tbTVN", 1], [5, 1000, 410]], [2, "bg_content", 1, [[24, 0, -52, [4]]], [0, "80+VdSjFlEqqSoK6gb0G0I", 1], [5, 1020, 520], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "title_lschoi", false, 1, [[25, -53, [5], 6]], [0, "d68E2C/mFIsKBzsb1d9VVG", 1], [5, 395, 104], [0, 296, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sprite", 5, [[26, 0, false, -54, [7], 8]], [0, "646HluxjJAXqBxEVD2pW9n", 1], [5, 70, 70], [0.633, 2.284, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbSession", 2, [-55], [0, "a3l/iabPZN9aR1ki9fAFf6", 1], [5, 150, 30], [-436, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "PHIÊN", 15, 48, false, false, 1, 1, 12, [9]], [3, "lbTime", 2, [-56], [0, "19KcDRjfVIbbHJqE2BhEzn", 1], [5, 200, 38], [-294, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "THỜI GIAN", 15, 48, false, false, 1, 1, 14, [10]], [3, "lbSide", 2, [-57], [0, "0cvAujr8FGFbMBvJaa4426", 1], [5, 100, 38], [-152, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ĐẶT CỬA", 15, 48, false, false, 1, 1, 16, [11]], [2, "lbResult", 2, [[1, "KẾT QUẢ", 15, 48, false, false, 1, 1, -58, [12]]], [0, "24gnyxVBxNW53C+Ce36ozp", 1], [5, 200, 38], [-8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbBet", 2, [[1, "TỔNG ĐẶT", 15, 48, false, false, 1, 1, -59, [13]]], [0, "1e0yPSYbVLrb5ZOR91Q9VD", 1], [5, 200, 38], [136, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbRefund", 2, [[1, "HOÀN TRẢ", 15, 48, false, false, 1, 1, -60, [14]]], [0, "7d7PVjaA5MkZu51YPhfaeI", 1], [5, 200, 38], [271, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbTotalWin", 2, [[1, "THỰC NHẬN", 15, 48, false, false, 1, 1, -61, [15]]], [0, "0cLuZsYOpGpaCY3ECSViZa", 1], [5, 200, 38], [412, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "temp", 4, [3], [0, "8bg5A/mRVHlpaKVj6yfNDW", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSession", 3, [-62], [0, "92iA1fnQpOUb6N/Csp+x3V", 1], [4, 4284344318], [5, 150, 30], [-436, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "PHIÊN", 22, 48, false, false, 1, 1, 1, 23, [18]], [3, "lbTime", 3, [-63], [0, "69zhOy6PRAOamoZdlXAPay", 1], [5, 170, 38], [-293, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "THOI GIAN", 22, 48, false, false, 1, 1, 2, 25, [19]], [3, "lbSide", 3, [-64], [0, "a5eg2omHJFTKdYp3aMpip2", 1], [5, 100, 38], [-152, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "ĐẶT CỬA", 22, 48, false, false, 1, 1, 1, 27, [20]], [3, "lbResult", 3, [-65], [0, "f44KhaaN1PfJTPMvuqZi8Y", 1], [5, 190, 38], [-8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "KET QUA", 22, 48, false, 1, 1, 2, 29, [21]], [5, "lbBet", 3, [-66], [0, "6du0KoEtBMP5YdEiFc0mdO", 1], [4, 4282969994], [5, 200, 38], [136, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "TIỀN ĐẶT", 22, 48, false, false, 1, 1, 1, 31, [22]], [5, "lbRefund", 3, [-67], [0, "a8H2XfPldFs6KpjcdDnDT1", 1], [4, 4284344318], [5, 200, 38], [271, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "HOÀN TRẢ", 22, 48, false, false, 1, 1, 1, 33, [23]], [5, "lbWin", 3, [-68], [0, "72rvPkqCZOV6F683Ox6l3N", 1], [4, 4284344318], [5, 200, 38], [412, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "HOÀN TRẢ", 22, 48, false, false, 1, 1, 1, 35, [24]], [16, "content", 8, [0, "70R0tbEIVDY6Uu9kTsQgBV", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [33, false, 0.75, 0.23, null, null, 4, 37], [34, 20, 10, 400, 4, 3, 38]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 39, 0, 0, 1, 0, -1, 6, 0, -2, 7, 0, -3, 9, 0, -4, 10, 0, -5, 5, 0, -6, 2, 0, -7, 4, 0, 0, 2, 0, 3, 17, 0, 4, 15, 0, 8, 13, 0, 0, 2, 0, -1, 12, 0, -2, 14, 0, -3, 16, 0, -4, 18, 0, -5, 19, 0, -6, 20, 0, -7, 21, 0, 9, 36, 0, 10, 34, 0, 3, 32, 0, 11, 30, 0, 12, 28, 0, 4, 26, 0, 13, 24, 0, 0, 3, 0, -1, 23, 0, -2, 25, 0, -3, 27, 0, -4, 29, 0, -5, 31, 0, -6, 33, 0, -7, 35, 0, -1, 38, 0, -2, 39, 0, -1, 22, 0, -2, 8, 0, 14, 5, 0, 0, 5, 0, -1, 11, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 37, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, -1, 15, 0, -1, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, -1, 24, 0, -1, 26, 0, -1, 28, 0, -1, 30, 0, -1, 32, 0, -1, 34, 0, -1, 36, 0, 15, 1, 3, 16, 22, 68], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 26, 28, 30, 32, 34, 36], [-1, 2, -1, 2, -1, -1, 2, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, 17, -1, -2, 1, 1, 1, 1, 1, 1, 1], [0, 3, 0, 4, 0, 0, 5, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 8, 1, 1, 1, 1, 1, 1, 1]], [[{"name": "title_ls", "rect": [0, 1, 395, 104], "offset": [0, -0.5], "originalSize": [395, 105], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [5], [9]], [[{"name": "popup_history_frame", "rect": [0, 0, 992, 642], "offset": [0, 0], "originalSize": [992, 642], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [5], [10]]]]