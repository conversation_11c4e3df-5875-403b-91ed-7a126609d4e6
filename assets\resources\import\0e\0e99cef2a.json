[1, ["a6D/9AbelM5qLpkvYTitvQ", "adw94Z+hpN57wutNivq8Q5", "fdNoodJKVLj4dF1TLppv2g", "675D1Q43xD57QCwGlAVzB3", "01DroK3clDLrLCG45dkV1D", "a9VpD0DP5LJYQPXITZq+uj", "0ceK4cj1JISJUc3qrQg76f", "bdHGrSx15Oe47RindYAQvK", "b3po1Eho1K278HATkyrGO1", "27szoTNSJOeL+wH0RSNbhM", "825TQ2kU9Ktq1Ncj5HdPmn", "2cWB/vWPRHja3uQTinHH30", "3cv1QEGBtGsaQ4kdDDh0cF", "7ajwW0ABpBQKyuttoFKD1h", "40rimjPZJE7I8Oq2r9fnmK"], ["_spriteFrame", "node", "_N$file", "_textureSetter", "root", "lbTotalWin", "lbTotalBet", "lbSessionID", "_N$target", "data", "_N$normalSprite", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_parent", "_contentSize", "_components", "_trs", "_children", "_color", "_anchorPoint"], 1, 4, 1, 5, 9, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_spriteFrame"], 1, 1, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "node", "_N$file"], -2, 1, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_color", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 5, 7], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 6, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["a32b2ibP/BAfZsc22Ob3J8Y", ["node", "lbSessionID", "lbTotalBet", "lbTotalWin", "spriteIcons"], 3, 1, 1, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[9, 0, 1, 2], [0, 0, 3, 7, 2, 4, 6, 2], [4, 0, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3], [0, 0, 3, 5, 2, 4, 6, 2], [4, 0, 1, 2, 3, 5, 4, 6, 7, 2], [2, 2, 3, 1], [3, 0, 1, 2, 3, 5, 5], [0, 0, 3, 7, 5, 2, 4, 6, 2], [0, 0, 3, 5, 2, 8, 4, 9, 6, 2], [3, 0, 1, 2, 3, 5, 6, 5], [6, 0, 2], [0, 0, 7, 5, 2, 2], [0, 0, 3, 7, 2, 4, 2], [0, 0, 1, 3, 5, 2, 4, 6, 3], [0, 0, 3, 5, 2, 8, 4, 6, 2], [7, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 1], [10, 0, 1, 2, 3, 4, 4], [2, 0, 1, 2, 3, 3], [2, 0, 2, 3, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 3], [5, 2, 8, 1], [11, 0, 1, 2, 3], [3, 0, 1, 2, 4, 3, 5, 6, 6]], [[[{"name": "title_ctp", "rect": [0, 0, 255, 52], "offset": [0, 0], "originalSize": [255, 52], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [3]], [[{"name": "btn_close", "rect": [1, 0, 82, 88], "offset": [0.5, 0], "originalSize": [83, 88], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [4]], [[[11, "cbSessionDetailView"], [12, "cbSessionDetailView", [-22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32], [[16, -2, [10, 11], 9], [17, -21, -20, -19, -18, [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17]]], [0, "39KDqWaQJOv6+2xebncaHY", -1]], [8, "slotsView", 1, [-34, -35, -36, -37, -38], [[18, 1, 1, 10, -33, [5, 840, 450]]], [0, "48wxb/UVlPApG85YqKpi28", 1], [5, 840, 450], [0, -63, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [1, "1", 2, [-39, -40, -41], [0, "b2tGx6I7tB/LT6ajstpXSB", 1], [5, 160, 450], [-340, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 2, [-42, -43, -44], [0, "917zC1iYNP2IgiCA5ZlLqS", 1], [5, 160, 450], [-170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "3", 2, [-45, -46, -47], [0, "aaZ6gwcKhJrZ2XhqaPpBpn", 1], [5, 160, 450]], [1, "4", 2, [-48, -49, -50], [0, "4eoVw9l01IR4ez5OuQXeLx", 1], [5, 160, 450], [170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "5", 2, [-51, -52, -53], [0, "09mrigWCRMw5zEnMhBOOg/", 1], [5, 160, 450], [340, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnClose", 1, [[19, 2, false, -54, 7], [21, 0.9, 3, -56, [[23, "a32b2ibP/BAfZsc22Ob3J8Y", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -55, 8]], [0, "19SYbYM6JGWYDvSQBIzE4d", 1], [5, 80, 80], [446.1, 246.3, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "black", 100, 1, [[20, 0, -57, 0], [22, -58, [4, 4292269782]]], [0, "743dvdW5RKloOYBNbFA0Ql", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "nen popup copy", 1, [-60], [[6, -59, 2]], [0, "faWkUFJYBNlL3W5WhP8aqz", 1], [5, 950, 612], [-9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "title_chonDong", 10, [[6, -61, 1]], [0, "e8nlNeu+BN/4s7HcnGOjAJ", 1], [5, 255, 52], [3.8, 241, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "label", 1, [[24, "COWBOY - CHI TIẾT PHIÊN:", 22, false, 1, 1, -62, 3]], [0, "73HYmx13NLXK5u4o6Ej9G0", 1], [4, 4291029493], [5, 278.3, 22], [-66, 170, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSessionID", 1, [-63], [0, "e2ezjeFCZN56rMf5gGTIVw", 1], [4, 4278242559], [5, 114.95, 22], [0, 0, 0.5], [80, 170, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "#12345678", 22, false, 1, 13], [9, "label", 1, [[10, "Cược:", 22, false, 1, -64, 4]], [0, "fd7tzgRr1Af7eXVru64PNh", 1], [4, 4291029493], [5, 57.75, 22], [0, 0, 0.5], [-411, 136, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbTotalBet", 1, [-65], [0, "eebuCl+fBJkJ1Nc3EE/Ei7", 1], [4, 4278242559], [5, 83.05, 22], [0, 0, 0.5], [-344, 136, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "250.000", 22, false, 1, 16], [9, "label", 1, [[10, "Thắng:", 22, false, 1, -66, 5]], [0, "bdbNPldQ1Ger3136M9rpl1", 1], [4, 4291029493], [5, 68.75, 22], [0, 0, 0.5], [-411, 107, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbTotalWin", 1, [-67], [0, "38lBczmi9FG6bhaXe9tbEw", 1], [4, 4278242559], [5, 83.05, 22], [0, 0, 0.5], [-333, 107, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "250.000", 22, false, 1, 19], [4, "bg_chiTiet", 1, [[6, -68, 6]], [0, "b0kYf5/MBOyJZZh/TzYNu1", 1], [5, 473, 259], [0, -63, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "slot1", 3, [-69], [0, "3cFoRW2slJGKSSADbuTvl1", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 22, [-70], [0, "0eRHJHXdtJP4+pZzItz5eM", 1], [5, 150, 143]], [3, 2, false, 23], [1, "slot2", 3, [-71], [0, "816DTpXkBNspCkR6mYsyrq", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 25, [-72], [0, "b3a++8ZJxNyY/9NE2Ep2CI", 1], [5, 150, 143]], [3, 2, false, 26], [1, "slot3", 3, [-73], [0, "3dK677QkhGZ5Lh/WOEugSk", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 28, [-74], [0, "39wqw07JFDr74YuFH8vo2k", 1], [5, 150, 143]], [3, 2, false, 29], [1, "slot1", 4, [-75], [0, "7eYQfw+x9KyaVZFP9IzdAO", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 31, [-76], [0, "a2TBCSsIBFx6TNOxbZllbG", 1], [5, 150, 143]], [3, 2, false, 32], [1, "slot2", 4, [-77], [0, "1dI3xS5jVKWosY9vgxTylj", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 34, [-78], [0, "a6gyKOXwdIp5rViAfapuDv", 1], [5, 150, 143]], [3, 2, false, 35], [1, "slot3", 4, [-79], [0, "91Czg3MSpKNLCeq1hDuYBt", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 37, [-80], [0, "b4W/GRf0xPsJrK0qdMczEQ", 1], [5, 150, 143]], [3, 2, false, 38], [1, "slot1", 5, [-81], [0, "394LJilkZFXo5po8NYfRZl", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 40, [-82], [0, "89psbnzqJK6b/pb4aLxnhu", 1], [5, 150, 143]], [3, 2, false, 41], [1, "slot2", 5, [-83], [0, "17ANSXpmxOz7pq1L+VDLMk", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 43, [-84], [0, "7a+vqqtBhLJqI4l2GdkY7r", 1], [5, 150, 143]], [3, 2, false, 44], [1, "slot3", 5, [-85], [0, "ebD9wsvIlNvK02cODvQhAh", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 46, [-86], [0, "0aUSojTUtE15ds6jye7Iij", 1], [5, 150, 143]], [3, 2, false, 47], [1, "slot1", 6, [-87], [0, "87JAxaV6VPJ4CN0KrNnN7t", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 49, [-88], [0, "04RBeC/xFNtayXjHNdYuVM", 1], [5, 150, 143]], [3, 2, false, 50], [1, "slot2", 6, [-89], [0, "abL55a8JJE+bQy9t4pLM+S", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 52, [-90], [0, "1dFKDtOiBBR6SLj1NtqRW2", 1], [5, 150, 143]], [3, 2, false, 53], [1, "slot3", 6, [-91], [0, "59QRQTJLBDoIW3sX0GEwB1", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 55, [-92], [0, "d1bPBbfllH4IAxzhRYyf6U", 1], [5, 150, 143]], [3, 2, false, 56], [1, "slot1", 7, [-93], [0, "1f85IMSZhLrokTnRlb06/9", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 58, [-94], [0, "07NmdBJJtCVpMGxEkRdy0A", 1], [5, 150, 143]], [3, 2, false, 59], [1, "slot2", 7, [-95], [0, "d7K62uiPZJmrAEvuNIhht9", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 61, [-96], [0, "7aWMXrHlpJm6whSEg1RqYT", 1], [5, 150, 143]], [3, 2, false, 62], [1, "slot3", 7, [-97], [0, "3cuOpwjyRPq5dCa1MlM0Dm", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icon", 64, [-98], [0, "eaQH9aUBRLW4Gq8M0Rk3v9", 1], [5, 150, 143]], [3, 2, false, 65]], 0, [0, 4, 1, 0, 1, 1, 0, -1, 24, 0, -2, 33, 0, -3, 42, 0, -4, 51, 0, -5, 60, 0, -6, 27, 0, -7, 36, 0, -8, 45, 0, -9, 54, 0, -10, 63, 0, -11, 30, 0, -12, 39, 0, -13, 48, 0, -14, 57, 0, -15, 66, 0, 5, 20, 0, 6, 17, 0, 7, 14, 0, 1, 1, 0, -1, 9, 0, -2, 10, 0, -3, 12, 0, -4, 13, 0, -5, 15, 0, -6, 16, 0, -7, 18, 0, -8, 19, 0, -9, 21, 0, -10, 2, 0, -11, 8, 0, 1, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, -1, 22, 0, -2, 25, 0, -3, 28, 0, -1, 31, 0, -2, 34, 0, -3, 37, 0, -1, 40, 0, -2, 43, 0, -3, 46, 0, -1, 49, 0, -2, 52, 0, -3, 55, 0, -1, 58, 0, -2, 61, 0, -3, 64, 0, 1, 8, 0, 8, 8, 0, 1, 8, 0, 1, 9, 0, 1, 9, 0, 1, 10, 0, -1, 11, 0, 1, 11, 0, 1, 12, 0, -1, 14, 0, 1, 15, 0, -1, 17, 0, 1, 18, 0, -1, 20, 0, 1, 21, 0, -1, 23, 0, -1, 24, 0, -1, 26, 0, -1, 27, 0, -1, 29, 0, -1, 30, 0, -1, 32, 0, -1, 33, 0, -1, 35, 0, -1, 36, 0, -1, 38, 0, -1, 39, 0, -1, 41, 0, -1, 42, 0, -1, 44, 0, -1, 45, 0, -1, 47, 0, -1, 48, 0, -1, 50, 0, -1, 51, 0, -1, 53, 0, -1, 54, 0, -1, 56, 0, -1, 57, 0, -1, 59, 0, -1, 60, 0, -1, 62, 0, -1, 63, 0, -1, 65, 0, -1, 66, 0, 9, 1, 98], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 17, 20, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 66], [0, 0, 0, 2, 2, 2, 0, 0, 10, 11, -1, -2, 2, 2, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [5, 6, 7, 1, 1, 1, 8, 9, 10, 2, 2, 11, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], [[{"name": "item_wild1", "rect": [0, 0, 150, 143], "offset": [0, 0], "originalSize": [150, 143], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [12]], [[{"name": "bg_chiTiet", "rect": [0, 0, 473, 259], "offset": [0, 0], "originalSize": [473, 259], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [13]], [[{"name": "bg_popup", "rect": [3, 0, 944, 612], "offset": [0, 0], "originalSize": [950, 612], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [14]]]]