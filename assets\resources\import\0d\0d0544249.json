[1, ["ecpdLyjvZBwrvm+cedCcQy", "35yTCcnGpNe4AvwNC8XcqP", "a2MjXRFdtLlYQ5ouAFv/+R"], ["node", "_parent", "_N$file", "root", "_N$target", "data", "_spriteFrame"], [["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_color", "_anchorPoint"], 1, 9, 4, 5, 1, 2, 7, 5, 5], ["cc.Node", ["_name", "_children", "_prefab", "_contentSize", "_trs", "_components", "_parent"], 2, 2, 4, 5, 7, 12, 1], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_isSystemFontUsed", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials", "_N$file"], -2, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_top", "node"], -2, 1], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.TTFFont", ["_name", "_native"], 1], ["cc.Prefab", ["_name"], 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$startAxis", "node", "_layoutSize", "_N$cellSize"], 0, 1, 5, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$target"], 2, 1, 9, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 5, 2, 3, 4, 7, 2], [2, 0, 2, 3, 2], [0, 0, 5, 2, 3, 8, 4, 7, 2], [3, 0, 2, 1, 3, 4, 5, 6, 6], [3, 0, 2, 1, 5, 6, 4], [4, 0, 1, 2, 5, 4], [3, 0, 1, 3, 4, 5, 6, 7, 5], [6, 0, 1, 3], [7, 0, 2], [1, 0, 1, 5, 2, 3, 2], [1, 0, 1, 2, 3, 4, 2], [1, 0, 6, 1, 2, 3, 4, 2], [1, 0, 6, 1, 5, 2, 3, 4, 2], [0, 0, 5, 6, 2, 3, 4, 2], [0, 0, 6, 2, 3, 4, 9, 7, 2], [0, 0, 6, 2, 3, 4, 2], [0, 0, 1, 5, 2, 3, 8, 4, 3], [4, 0, 3, 4, 5, 4], [8, 0, 1], [5, 1, 2, 1], [2, 1, 0, 2, 3, 3], [2, 0, 2, 3, 4, 2], [9, 0, 1, 2, 3, 4, 5, 4], [10, 0, 1, 2, 3, 2], [11, 0, 1, 2, 3], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 5, 6, 6]], [[[[8, "UTM_Facebook_K_T", "UTM_Facebook_K_T.ttf"], -1], 0, 0, [], [], []], [[[9, "PopupHonors"], [10, "PopupHonors", [-4, -5, -6, -7, -8, -9, -10, -11], [[[6, 45, 1560, 720, -2], [19, -3], null], 4, 4, 0], [20, -1, 0], [5, 1560, 720]], [11, "<PERSON><PERSON>", [-12, -13, -14, -15, -16, -17, -18, -19, -20], [0, "71vdP90m1EZbrSeYxE1Sbg", 1, 0], [5, 664.5, 50], [0, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Container", 1, [-22, -23, -24, -25], [[21, 1, 0, -21, [21]]], [0, "5bZVL3VAtCyYDNzH0mVhla", 1, 0], [5, 981, 598.2]], [12, "headerBg", 3, [-26, -27, -28, -29, -30], [0, "39pn9epPlM248qa4QBPG9t", 1, 0], [5, 650, 40], [1, 193.622, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "ListItem", [2], [[23, 1, 2, 1, -31, [5, 1000, 50], [5, 684, 40.7]], [18, 1, 131.97699999999998, 0.05000000000001137, -32]], [0, "09f/b6lK5NLar/U7AZvsoA", 1, 0], [5, 1000, 50], [0, 0.5, 1], [0, 199.95, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BtnClose", 3, [[2, 0, -33, [3]], [24, 3, -35, [[25, "9cfee6LNFFGQbgMsj9+Dkqn", "dismiss", 1]], -34]], [0, "e4CsXmxjREsLSQGn8wykBc", 1, 0], [5, 80, 50], [450.863, 273.814, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "view", [5], [[26, 0, -36, [20]], [6, 45, 240, 250, -37]], [0, "17mUPLeyBIb6sQ80lrz8JZ", 1, 0], [5, 1000, 400]], [13, "New ScrollView", 3, [7], [[[27, false, 0.75, 0.23, null, null, -38, 5], null], 4, 0], [0, "baagsduQxCSZx476WnzBX8", 1, 0], [5, 1000, 400], [4.093, -21.344, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Bg", 128, 1, [[22, 0, -39, [0], 1]], [0, "34PdH0ZetGIb3ozaZjbFF1", 1, 0], [4, **********], [5, 1560, 720]], [1, "tab_duatop_taixiu_on", 3, [[2, 0, -40, [2]]], [0, "e9b7sEC2JNroGe4cKHpYyx", 1, 0], [5, 250, 40], [14.519, 279.029, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "TitleRank", 4, [[4, "Hạng", 15, false, 1, 1, -41, [4]]], [0, "45rIoE7GBLPJ0IOc7nQ7e7", 1, 0], [4, **********], [5, 61.41, 40], [-392.272, 30.386, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "TitleAccount", 4, [[4, "<PERSON><PERSON><PERSON>", 15, false, 1, 1, -42, [5]]], [0, "6cgmCIm+JLF4GAuN1Ax3Zi", 1, 0], [4, **********], [5, 155.63, 40], [-236.467, 30.386, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "TitleWin", 4, [[4, "Tổng <PERSON>", 15, false, 1, 1, -43, [6]]], [0, "86jYMOt6JFJZgM7nIR5Zof", 1, 0], [4, **********], [5, 147.19, 40], [-32.646, 30.386, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "LBL", 4, [[5, "Đạt T<PERSON>", 15, false, -44, [7]]], [0, "1aFknBOf9PRK3KQITxHb+4", 1, 0], [5, 190.78, 40], [167.798, 17.707, 0, 0, 0, 0, 1, 0.8, 1, 1]], [1, "LBL", 4, [[5, "Thưởng", 15, false, -45, [8]]], [0, "3fPmLxZ0pMbom7GobxXgfE", 1, 0], [5, 94.69, 40], [372.55, 17.894, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 2, [[2, 0, -46, [9]]], [0, "40doCII/NPzrAviYyENP6i", 1, 0], [5, 650, 50], [-0.131, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ic_rank", 2, [[2, 0, -47, [10]]], [0, "39GBA7HfxMqLZHGbQba9x2", 1, 0], [5, 58, 50.6666667], [-395.796, -1.389, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lblWin", 2, [[4, "0", 20, false, 1, 1, -48, [11]]], [0, "49G88myWZNVK+QPhWJdc51", 1, 0], [5, 11.88, 40], [-36.269, 32.3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lblAccount", 2, [[7, "TÀI KHOẢN", false, 1, 1, -49, [12], 13]], [0, "9cf5Qm51lPloIZYL9is4cp", 1, 0], [4, **********], [5, 196.54, 50.4], [-246.896, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [1, "lblRank", 2, [[7, "HẠNG", false, 1, 1, -50, [14], 15]], [0, "5aGAoP0cRLnJBsWHyzeFa4", 1, 0], [5, 102.91, 50.4], [-395.038, 0, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [1, "lbltongcuoc", 2, [[5, "3tỷ", 20, false, -51, [16]]], [0, "e9Ho3SkztPHaLMDW0mY5qw", 1, 0], [5, 33.75, 40], [116.477, 25.76, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tong<PERSON><PERSON>", 2, [[2, 0, -52, [17]]], [0, "67TQFDsK1KRIeCUBPAaMR7", 1, 0], [5, 30, 30], [227.456, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "thuong", 2, [[2, 0, -53, [18]]], [0, "aeKPp5FGpLnqOyWxkn+2ig", 1, 0], [5, 40, 40], [312.784, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lblthuong", 2, [[5, "200.000.000", 20, false, -54, [19]]], [0, "25Uq/uPOBDvLKjb6uP2YSd", 1, 0], [5, 118.13, 40], [394.756, 20.001, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 1, [[2, 0, -55, [22]]], [0, "a9rIaR3U5J/b0BKrMBoa2a", 1, 0], [5, 5, 380], [-126.717, -13.749, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 1, [[2, 0, -56, [23]]], [0, "5eZEamqVJFGIpNpxuijO15", 1, 0], [5, 5, 380], [180.152, -14.749, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line copy", 1, [[2, 0, -57, [24]]], [0, "55HhpQPc5IMZ+dLvX+cTTQ", 1, 0], [5, 5, 380], [-340.201, -13.749, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line copy", 1, [[2, 0, -58, [25]]], [0, "546gYUQ9hKxa8uDQFUzIt7", 1, 0], [5, 5, 380], [59.706, -13.749, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line copy", 1, [[2, 0, -59, [26]]], [0, "86IuPpZHZELJ5Yy1okoLlc", 1, 0], [5, 5, 380], [287.543, -13.749, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lineNgang", 1, [[2, 0, -60, [27]]], [0, "6foiMll59A8qOiGvWjbWQ0", 1, 0], [5, 900, 25], [0, 186.731, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 9, 0, -2, 3, 0, -3, 25, 0, -4, 26, 0, -5, 27, 0, -6, 28, 0, -7, 29, 0, -8, 30, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, -6, 21, 0, -7, 22, 0, -8, 23, 0, -9, 24, 0, 0, 3, 0, -1, 10, 0, -2, 6, 0, -3, 4, 0, -4, 8, 0, -1, 11, 0, -2, 12, 0, -3, 13, 0, -4, 14, 0, -5, 15, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 4, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 5, 1, 2, 1, 5, 5, 1, 7, 7, 1, 8, 60], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 6, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1], [0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]]]