[1, ["ecpdLyjvZBwrvm+cedCcQy", "32blRba/NGLbfyIAMoEk84", "9bYDnu5NdJH7u79ZpCFpEO", "adw94Z+hpN57wutNivq8Q5", "24xd2Xl+xHVZeWwPN10Wzf", "53DK8iqelAy7zW6nrAKHCm", "43eUQArQFEEKA2dDw0kvR9", "75Yu0xV2RB0YELQVIQbE4o", "23ZjzPZXlBFaIMHdbxZsM9", "c3nzmTt5BIS4OCSTuRDKiO", "davTu67GxAea463woqHlbB", "feSppro/hPIImeuP5yW/YW", "04l3Co2jpFnIujoTSnX/T4", "f5+3xtPLlArqrO4SDD8ZRM", "d82n49/IVAvIEqsa0xvvk0", "31EHgnPtpLH7w0aFujZEcd", "a2MPgOmy9MrLYBxUynZCAD", "b6CgQ7Rt9JcpRCpTbz8DgW", "34vcGs6ONFH6sb09TtuGF3", "15YOjSp6xBrqb7tgTXEbuk", "c41xGSevBBRL3z/+SoOCag", "38MPPGV0VP44BQJNIkSVa4", "e4XOO14ipLgJxlIBE+q2fb", "c5VAwMpVJOWKsDoLIoLPnM", "da7pCqRPVLCpDIJvRN3rYJ", "59ZBAYgNRMQb42qo95EBMi", "01TqLe7tVNdrgcsOZ4uIDS", "c9TzPLeGpO95LfBYktoIDQ", "c3MVJqNYFFwJhikABELvlF", "db3G5S5C9AJrOT8owtXvXC", "d3wCKa44BLmJxEuLvPT4hx", "14UZS+SetItaisR12xxLVl", "7a8264sN5JYJXLu3M7lKMe", "14Pj+L7AVL9p8n3RDpwrvp", "9bulT4/DBEHYHUKlJOXohn", "f0vI+11CJLyY8NGQVkB5RS", "a7ZkntB+9PSoKNp98Jq25/", "21ivear/NN16H810MQ+kZJ", "954x8AHatGK5IqMUlN+hwX", "7fHna9WYFG9ZsrhUmWh6PX", "aczgzFdE1NlIbRbT1WSO01", "fc8n2p+PBNmIh6Bw7p4D1h", "34rWcuAzJHvKwYVs7etjOQ", "8dJOSfB8ZF0aWc4ibT7XNm", "2aw2TlbzVD9qGv+2r8UYsp", "abUNU2dwdCjbeCLyh281wS", "c21um74wdEgLAYVgpYVvuy", "2ehv2DWYZCCaC7PuCJpGS+", "bfnZbQ2zxF1LUbox4+Xvhv", "d1aAc515NMnq9SVkWXuF9O", "a88MbrXJpKdYL1/iWyo51w", "081bS6OrdMPZM+YewrutqZ", "ccFoHZBMVNgItdLg3vBl4m", "0dXxTzXZpLsLC/F1xmtNsY", "0aCwqHD3VDn65ueqd2IOBL"], ["node", "_N$file", "_spriteFrame", "_parent", "_normalMaterial", "_textureSetter", "_N$target", "_N$font", "body", "header", "_N$background", "_N$placeholderLabel", "_N$textLabel", "_N$normalSprite", "_N$content", "root", "arrow", "PanelMenu", "lbMutilXien4Shadown", "lbMutilXien4", "lbMutilXien3Shadown", "lbMutilXien3", "lbMutilXien2Shadown", "lbMutilXien2", "lbMutilLoShadown", "lbMutilLo", "lbMutilDeCuoiShadown", "lbMutilDeCuoi", "lbMutilDeDauShadown", "lbMutilDeDau", "lbMutilDeShadown", "lbMutilDe", "lbiBalance", "lbNotifyTimeWait", "btnNextResult", "btnPrevResult", "lbG1", "lbGDB", "lbTimer", "lbChanel", "lbTitle", "contentBetting", "contentBetted", "nodeResult", "nodeBetting", "nodeBetted", "btnNodeResult", "btnNodeBetting", "btnNodeBetted", "data", "prefabHelp", "prefabHistory", "prefabTop", "prefabChooseView", "itemTemplate"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_children", "_parent", "_trs", "_anchorPoint", "_color", "_eulerAngles"], 0, 4, 5, 9, 2, 1, 7, 5, 5, 5], "cc.SpriteFrame", ["cc.Label", ["_isSystemFontUsed", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_lineHeight", "_enableWrapText", "_N$overflow", "_enabled", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_top", "_left", "alignMode", "_bottom", "_right", "node"], -5, 1], ["cc.Sprite", ["_sizeMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "duration", "node", "clickEvents", "_N$normalColor", "_N$target", "_normalMaterial", "_N$pressedColor", "_N$disabledColor", "_N$normalSprite"], 0, 1, 9, 5, 1, 6, 5, 5, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint", "_children"], 1, 1, 2, 4, 5, 7, 5, 5, 2], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_color"], 1, 1, 12, 4, 5, 7, 5, 5], ["cc.Layout", ["_N$layoutType", "_resize", "_enabled", "_N$spacingX", "_N$spacingY", "_N$paddingTop", "_N$paddingBottom", "_N$verticalDirection", "node", "_layoutSize"], -5, 1, 5], ["cc.PrivateNode", ["_name", "_obj<PERSON><PERSON>s", "_zIndex", "_active", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], -1, 1, 9, 4, 5, 5, 7, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["3b6acKa2bVF47OyLNTM29it", ["node", "prefabHelp", "prefabHistory", "prefabTop"], 3, 1, 6, 6, 6], ["de9d4qcMbZCtKf2NtJTNE75", ["node", "header", "body", "lbiBalance", "lbMutilDe", "lbMutilDeShadown", "lbMutilDeDau", "lbMutilDeDauShadown", "lbMutilDeCuoi", "lbMutilDeCuoiShadown", "lbMutilLo", "lbMutilLoShadown", "lbMutilXien2", "lbMutilXien2Shadown", "lbMutilXien3", "lbMutilXien3Shadown", "lbMutilXien4", "lbMutilXien4Shadown", "PanelMenu", "arrow", "prefabChooseView"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["8c4f9H6G1ZA+6Ngtqd3Seko", ["node", "lbTitle", "lbChanel", "lbTimer", "lbGDB", "lbG1", "lbG2", "lbG3", "lbG4", "lbG5", "lbG6", "lbG7", "btnPrevResult", "btnNextResult", "colorWait", "colorBet", "lbNotifyTimeWait"], 3, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 1, 1, 5, 5, 1], ["a9d32SdRgVECLP77s7IuK93", ["node", "btnNodeBetted", "btnNodeBetting", "btnNodeResult", "nodeBetted", "nodeBetting", "nodeResult", "contentBetted", "contentBetting", "itemTemplate"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$lineHeight", "node", "_N$font"], -1, 1, 6], ["db150+aUctGxY2RIIUbBb1w", ["node", "header", "body"], 3, 1, 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 1, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1]], [[16, 0, 1, 2], [0, 0, 7, 5, 3, 4, 8, 2], [2, 4, 1, 0, 2, 3, 9, 10, 11, 6], [4, 3, 4, 5, 1], [2, 4, 1, 5, 0, 3, 9, 10, 11, 6], [0, 0, 7, 6, 5, 3, 4, 8, 2], [0, 0, 7, 6, 3, 8, 2], [5, 0, 1, 3, 4, 7, 3], [10, 0, 1, 3, 3], [0, 0, 7, 5, 3, 10, 4, 8, 2], [9, 0, 1, 3, 2, 4, 5, 6, 7, 8, 9, 5], [6, 0, 2, 3, 4, 5, 6, 2], [2, 4, 1, 5, 0, 2, 3, 9, 10, 7], [2, 4, 0, 2, 3, 9, 10, 11, 5], [10, 0, 1, 2, 3, 4], [9, 0, 1, 3, 2, 4, 5, 6, 10, 7, 8, 9, 5], [0, 0, 7, 5, 3, 4, 2], [0, 0, 6, 5, 3, 4, 8, 2], [2, 4, 1, 5, 0, 2, 3, 9, 10, 11, 7], [2, 1, 5, 0, 2, 3, 9, 10, 6], [0, 0, 7, 6, 3, 2], [4, 0, 3, 4, 5, 2], [6, 0, 2, 3, 4, 7, 5, 6, 2], [5, 0, 1, 3, 4, 5, 6, 3], [0, 0, 6, 5, 3, 4, 9, 8, 2], [0, 0, 2, 7, 6, 5, 3, 4, 8, 3], [5, 0, 1, 3, 4, 3], [0, 0, 7, 5, 3, 10, 4, 2], [17, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 7, 6, 5, 3, 4, 3], [4, 1, 0, 3, 4, 5, 3], [0, 0, 1, 7, 6, 3, 3], [2, 8, 4, 1, 5, 6, 0, 2, 3, 7, 9, 10, 11, 10], [18, 0, 1, 2, 1], [0, 0, 7, 6, 5, 3, 4, 2], [10, 1], [9, 0, 1, 2, 4, 5, 6, 7, 8, 9, 4], [0, 0, 6, 5, 3, 10, 4, 2], [0, 0, 1, 7, 5, 3, 10, 4, 8, 3], [0, 0, 1, 7, 5, 3, 4, 3], [7, 0, 2, 3, 4, 5, 6, 2], [7, 0, 2, 3, 4, 5, 2], [3, 5, 0, 1, 2, 8, 5], [3, 5, 0, 4, 1, 2, 8, 6], [4, 1, 3, 4, 5, 2], [4, 1, 0, 3, 4, 3], [5, 0, 1, 3, 4, 5, 8, 9, 6, 10, 3], [19, 0, 1, 2, 3, 4, 5, 3], [2, 4, 1, 5, 6, 0, 2, 3, 7, 9, 10, 9], [9, 0, 1, 2, 4, 5, 6, 10, 7, 8, 9, 4], [0, 0, 1, 6, 5, 3, 4, 8, 3], [0, 0, 6, 5, 3, 4, 2], [0, 0, 5, 3, 4, 9, 2], [0, 0, 1, 7, 5, 3, 4, 8, 3], [0, 0, 7, 6, 5, 3, 4, 9, 2], [6, 0, 1, 2, 9, 3, 4, 5, 6, 3], [7, 0, 1, 2, 3, 4, 5, 6, 3], [3, 5, 0, 4, 7, 3, 6, 1, 2, 8, 9], [8, 1, 0, 5, 6, 7, 8, 9, 6], [4, 1, 3, 4, 2], [20, 0, 1, 2, 3, 4, 5, 6, 6], [2, 4, 1, 0, 2, 9, 10, 11, 5], [2, 1, 5, 6, 0, 2, 3, 7, 9, 10, 8], [22, 0, 1, 1], [11, 0, 2], [0, 0, 6, 3, 8, 2], [0, 0, 1, 6, 5, 3, 4, 9, 8, 3], [0, 0, 1, 7, 6, 5, 3, 4, 8, 3], [0, 0, 7, 6, 3, 4, 8, 2], [0, 0, 6, 3, 2], [0, 0, 6, 3, 4, 8, 2], [0, 0, 1, 2, 7, 6, 5, 3, 4, 8, 4], [0, 0, 7, 5, 3, 4, 8, 11, 2], [0, 0, 7, 6, 5, 3, 8, 2], [0, 0, 1, 7, 6, 3, 4, 3], [6, 0, 1, 2, 3, 4, 7, 5, 8, 6, 3], [6, 0, 2, 3, 4, 7, 5, 8, 6, 2], [7, 0, 1, 2, 3, 4, 5, 7, 6, 3], [7, 0, 2, 3, 4, 8, 5, 7, 6, 2], [12, 0, 1, 2, 3, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 1], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [3, 0, 3, 6, 1, 2, 8, 6], [3, 0, 1, 8, 3], [3, 0, 4, 3, 8, 4], [3, 5, 0, 4, 3, 6, 1, 2, 8, 8], [3, 0, 7, 8, 3], [8, 2, 0, 8, 3], [8, 0, 3, 4, 8, 9, 4], [8, 1, 0, 8, 9, 3], [4, 0, 3, 4, 2], [4, 2, 3, 4, 5, 2], [5, 2, 0, 1, 3, 4, 7, 4], [5, 3, 4, 1], [21, 0, 1], [2, 4, 1, 0, 2, 3, 9, 10, 6], [2, 1, 5, 6, 0, 2, 7, 9, 10, 7], [23, 0, 1]], [[[[64, "LoDeLobby"], [17, "LoDeLobby", [-67, -68, -69, -70], [[79, -2, 717, 718, 719], [80, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, 720], [81, -56, -55, -54, -53, -52, -51, [-49, -50], [-43, -44, -45, -46, -47, -48], [-39, -40, -41, -42], [-33, -34, -35, -36, -37, -38], [-30, -31, -32], [-26, -27, -28, -29], -25, -24, [4, 4278190308], [4, 4278255442], -23], [82, -65, -64, -63, -62, -61, -60, -59, -58, -57, 721], [83, 45, -1, 1, 1398, 786, -66]], [0, "9ctV/XGR1GBI2hCHiGTyWw", -1], [5, 1561, 732], [780.5, 367, 0, 0, 0, 0, 1, 1, 1, 1]], [65, "<PERSON><PERSON><PERSON><PERSON>", [-71, -72, -73, -74, -75, -76, -77, -78, -79, -80], [0, "5ewnwEijFE8pOSfRuQmuKi", 1], [0, 124, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "layoutBet", false, [-82, -83, -84, -85, -86, -87, -88], [[88, false, 3, -81]], [0, "59JJK4sCdGYbilt5rm0J7M", 1], [5, 300, 200], [4.422, 207.852, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-90, -91, -92, -93, -94, -95, -96], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>27</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 99</color> ", 30, 50, -89, 142]], [0, "adO1xubmJJxpc9++WLmXxX", 1], [5, 438.65000000000003, 63], [0, 0, 0.5], [-253, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-98, -99, -100, -101, -102, -103, -104], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>4</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 95</color> ", 30, 50, -97, 202]], [0, "73OnOKTG1CtZgeh220C1+O", 1], [5, 421.44, 63], [0, 0, 0.5], [-253, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-106, -107, -108, -109, -110, -111, -112], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>1</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 95</color> ", 30, 50, -105, 250]], [0, "96j7B899VHhrJG/KVi3l+L", 1], [5, 421.44, 63], [0, 0, 0.5], [-253, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [66, "rate", false, [-114, -115, -116, -117, -118, -119, -120], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>27</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 99</color> ", 30, 50, -113, 308]], [0, "60z3vACpBPWZzECLmsmy1l", 1], [5, 438.65000000000003, 63], [0, 0, 0.5], [-253, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-122, -123, -124, -125, -126, -127, -128], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>1</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 9.50</color> ", 26, 50, -121, 368]], [0, "4fqvzHJAtCi6H3sj9x/uI8", 1], [5, 387.81, 63], [0, 0, 0.5], [-216, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-130, -131, -132, -133, -134, -135, -136], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>1</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 9.50</color> ", 26, 50, -129, 415]], [0, "977JEc2klG24HkTim9+DZb", 1], [5, 387.81, 63], [0, 0, 0.5], [-200, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-138, -139, -140, -141, -142, -143, -144], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>1</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 17</color> ", 26, 50, -137, 477]], [0, "ec1COZHG5FcYy9ZFbhd31a", 1], [5, 365.25, 63], [0, 0, 0.5], [-194.645, -43, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-146, -147, -148, -149, -150, -151, -152], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>1</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 65</color> ", 26, 50, -145, 524]], [0, "54kGDqGwRN6Inhvua0NtT8", 1], [5, 365.25, 63], [0, 0, 0.5], [-211, -43, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "rate", [-154, -155, -156, -157, -158, -159, -160], [[28, false, "<PERSON><PERSON> to<PERSON> <color=#ffff00>1</c> lô. Đặt <color=#ffff00>1</color> ăn<color=#ffff00> 250</color> ", 26, 50, -153, 571]], [0, "9du5N9XZJL96BXUAQnpe0p", 1], [5, 380.15999999999997, 63], [0, 0, 0.5], [-207, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "text", 2, [-161, -162, -163, -164, -165, -166, -167, -168], [0, "ccLCGW+ZBO8rhmu+sa9Cit", 1]], [17, "center", [-170, 3, -171, -172, -173, -174], [[21, 0, -169, [606], 607]], [0, "86J2AhovBKCLa29hC9NwWE", 1], [5, 560, 650], [0, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnDe", 3, [-178, -179, -180, -181], [[30, 1, 0, -175, [34], 35], [23, 1.1, 3, -177, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "1", 1]], [4, 4292269782], -176]], [0, "01w4MSFSlGgqjSqiPyYlFO", 1], [5, 168, 67], [-160.2, 85.1, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnLo", 3, [-185, -186, -187, -188], [[30, 1, 0, -182, [42], 43], [23, 1.1, 3, -184, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "4", 1]], [4, 4292269782], -183]], [0, "269GM/+exJ1KmuqvZ6DaRs", 1], [5, 168, 67], [7, 85.1, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnDeSoDau", 3, [-192, -193, -194, -195], [[30, 1, 0, -189, [50], 51], [23, 1.1, 3, -191, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "2", 1]], [4, 4292269782], -190]], [0, "8c9/By1HNGuo9iIZBZcRYI", 1], [5, 168, 67], [171.543, 84.069, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnDeSoCuoi", 3, [-199, -200, -201, -202], [[30, 1, 0, -196, [58], 59], [23, 1.1, 3, -198, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "3", 1]], [4, 4292269782], -197]], [0, "adBxXnOh9G24jBLLp4UEiV", 1], [5, 168, 67], [-157.904, 17.783, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnXien2", 3, [-206, -207, -208, -209], [[30, 1, 0, -203, [66], 67], [23, 1.1, 3, -205, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "5", 1]], [4, 4292269782], -204]], [0, "fezCvhoOhBtLBTLY77E4ah", 1], [5, 168, 67], [9.049, 12.366, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnXien3", 3, [-213, -214, -215, -216], [[30, 1, 0, -210, [74], 75], [23, 1.1, 3, -212, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "6", 1]], [4, 4292269782], -211]], [0, "99ZCOB63hGMYq6D81X4Cfd", 1], [5, 168, 67], [171.854, 13.152, 0, 0, 0, 0, 1, 1, 1, 1]], [67, "btnXien4", false, 3, [-220, -221, -222, -223], [[30, 1, 0, -217, [82], 83], [23, 1.1, 3, -219, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "7", 1]], [4, 4292269782], -218]], [0, "ccpL9ufsZLsKpy/dFcy4al", 1], [5, 350, 67], [0.9, -163, 0, 0, 0, 0, 1, 1, 1, 1]], [68, "header", 14, [-224, -225, -226, -227, -228, -229], [0, "48rWPtT+hPhYyuq3I1rEaA", 1], [5, 300, 200], [0, 260, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "body", 14, [-230, -231, -232, -233, -234, -235], [0, "58PzS/kOFDrJzICkFcFcKP", 1]], [17, "layoutBet", [-237, -238, -239, -240, -241, -242], [[3, -236, [174], 175]], [0, "7fDX0vH/JLgaGTjrt5lS19", 1], [5, 512, 69], [0, -141, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutBet", [-244, -245, -246, -247, -248, -249], [[3, -243, [234], 235]], [0, "fcGc1lge9Be6wBfHdK0Rnb", 1], [5, 512, 69], [0, -141, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutBet", [-251, -252, -253, -254, -255, -256], [[3, -250, [282], 283]], [0, "3dUO5qlAhETrHFBCB/7oCO", 1], [5, 512, 69], [0, -141, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "layoutBet", false, [-258, -259, -260, -261, -262, -263], [[3, -257, [338], 339]], [0, "73v55ILABLCpnCBi8cNrm+", 1], [5, 512, 69], [0, -141, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutBet", [-265, -266, -267, -268, -269, -270], [[3, -264, [399], 400]], [0, "a5X2kf4+1IQaK0/tVYgtTE", 1], [5, 512, 69], [0, -141, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutBet", [-272, -273, -274, -275, -276, -277], [[3, -271, [446], 447]], [0, "05/3YEf0JCk5t+/R5eqz6+", 1], [5, 512, 69], [0, -141, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutBet", [-279, -280, -281, -282, -283, -284], [[3, -278, [508], 509]], [0, "5aEQAPgKBEz7tnwoC4uOLW", 1], [5, 512, 69], [0, -101, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutBet", [-286, -287, -288, -289, -290, -291], [[3, -285, [555], 556]], [0, "168+U8nTNKSLf4ITTogsI3", 1], [5, 512, 69], [0, -101, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layoutBet", [-293, -294, -295, -296, -297, -298], [[3, -292, [602], 603]], [0, "54N9P7EwVPM7K2hDWXrQTF", 1], [5, 512, 69], [0, -101, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "loxien", false, 23, [-302, -303], [[33, -301, -300, -299]], [0, "65PAGrIzZJAJ4uUFTpiicb", 1], [5, 202.25, 50.4]], [20, "g3", 2, [-304, -305, -306, -307, -308, -309], [0, "b0xiLAnCFB6JgIiLfcmaYp", 1]], [20, "g5", 2, [-310, -311, -312, -313, -314, -315], [0, "f8OGs6v65Ph5nBaW3kyzFp", 1]], [17, "selectChanel", [-317, -318, -319, -320], [[3, -316, [22], 23]], [0, "76tzMRA6hLn5NpbaprQTW9", 1], [5, 379, 76], [0, 194, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "baolo", 23, [-324, -325], [[33, -323, -322, -321]], [0, "73pbGu3NhA0J60cEVOZhAc", 1], [5, 0, 50.4], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "da<PERSON><PERSON>", false, 23, [-329, -330], [[33, -328, -327, -326]], [0, "c01f0ug9lLoZaKpXJdwdD/", 1], [5, 202.25, 50.4]], [29, "3cang", false, 23, [-334, -335], [[33, -333, -332, -331]], [0, "c6rzNfxdJPKrO//GMq9lto", 1], [5, 202.25, 50.4]], [29, "daud<PERSON>i", false, 23, [-339, -340], [[33, -338, -337, -336]], [0, "cbmzup8RZNeJuYTvrRQFO9", 1], [5, 202.25, 50.4]], [17, "right", [-342, -343, -344, -345], [[21, 0, -341, [688], 689]], [0, "115n8tgUFIqam6YvbGJTTr", 1], [5, 405, 550], [493, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "btnBeted", [-348, -349], [[23, 1.05, 3, -347, [[8, "a9d32SdRgVECLP77s7IuK93", "onReloadDataBetted", 1]], [4, 4292269782], -346]], [0, "41OASegP9OXIln0WjyCuXi", 1], [5, 113, 44], [-113, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [17, "btnBetting", [-352, -353], [[23, 1.05, 3, -351, [[8, "a9d32SdRgVECLP77s7IuK93", "onReloadDataBetting", 1]], [4, 4292269782], -350]], [0, "59xChbD6JF1YByw3QLl6Ek", 1], [5, 113, 44], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [17, "btnResult", [-356, -357], [[23, 1.05, 3, -355, [[8, "a9d32SdRgVECLP77s7IuK93", "onLoadResult", 1]], [4, 4292269782], -354]], [0, "3fCwZJIBtCZ7FyfRJkgYjW", 1], [5, 113, 44], [113, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [51, "btnPopups", [-359, -360, -361, -362], [[89, 2, 20, 30, -358, [5, 86, 380]]], [0, "410xKe4xxM56N2dcHgMRK+", 1], [5, 86, 380]], [5, "header", 1, [-365, -366], [[21, 0, -363, [11], 12], [84, 41, 1560, -364]], [0, "1f1555a2VH8JMDtKUzznB9", 1], [5, 1561, 86], [0, 323, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "left", 46, [-369, -370], [[3, -367, [7], 8], [85, 9, -1, -0.5, -368]], [0, "6delgvwHJN6J7pMxxgqD6O", 1], [5, 480, 109], [-541.5, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [69, "bacang", [7, -371, 27, -372], [0, "82ej7ihXhIV7a1495GfKpP", 1]], [5, "EditBox", 27, [-377, -378, -379], [[47, 250, 2, -376, -375, -374, -373]], [0, "79xxhLODNBmZkyMv5Cm/1L", 1], [5, 430, 50], [-23, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "value", [-384, -385, -386], [[47, 36, 2, -383, -382, -381, -380]], [0, "07Gqp6lkxMCIguZxA2ej8f", 1], [5, 332, 81], [0, 41, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "value", [-391, -392, -393], [[47, 36, 2, -390, -389, -388, -387]], [0, "977/o4QEFAdqFIBlYdLLPO", 1], [5, 332, 81], [0, 45, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "header", 33, [-394, -395, -396], [0, "9ezLacDaRJW4+M24S7dUij", 1], [0, 33, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "body", 33, [-397, -398, -399], [0, "e7lQ5O79xEE4mMGqDspb+o", 1], [0, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "header", 41, [42, 43, 44], [[90, 1, 1, -400, [5, 339, 39]]], [0, "02rhlb/7BF1K8bWJK/oWv+", 1], [5, 339, 39], [0, 288, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "g4", 2, [-401, -402, -403, -404], [0, "f5u64dBRdP94DOtRAuw5te", 1]], [20, "g7", 2, [-405, -406, -407, -408], [0, "6fp4K9HCxL+JlZuT/f+sQK", 1]], [70, "header", [-409, -410, -411, -412], [0, "99ER2foMlLVYM3OWv79Q2E", 1], [5, 476, 58], [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "bg", [-414, 45], [[3, -413, [715], 716]], [0, "8amppJMwJHbK2v1dZxNwAY", 1], [5, 122, 456]], [37, "Background", [-416, -417], [[44, 1, -415, [696], 697]], [0, "77WP32hzhJF4YvigzPlflX", 1], [4, 4293322470], [5, 86, 76]], [37, "Background", [-419, -420], [[44, 1, -418, [703], 704]], [0, "09v+gk6AlBk52yorJr7+N1", 1], [4, 4293322470], [5, 77, 74]], [37, "Background", [-422, -423], [[44, 1, -421, [710], 711]], [0, "aeRjmeyeNC16ERs0sErOkv", 1], [4, 4293322470], [5, 62, 75]], [6, "content", 1, [-424, 14, 41], [0, "2dhY6uY7xBn4L+IVYWOH8l", 1], [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "left", 62, [-426, 36], [[21, 0, -425, [24], 25]], [0, "banmW/iU1Hlardo2u+ucZo", 1], [5, 400, 650], [-485, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "baolo", 22, [-429], [[21, 0, -427, [86], 87], [7, 1.1, 3, -428, [[8, "de9d4qcMbZCtKf2NtJTNE75", "onSelectType", 1]], 88]], [0, "cbg+YHqNRFwIoM/0rI2fOF", 1], [5, 160, 61], [-178, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "da<PERSON><PERSON>", 99, 22, [-432], [[21, 0, -430, [91], 92], [7, 1.1, 3, -431, [[8, "de9d4qcMbZCtKf2NtJTNE75", "onSelectType", 1]], 93]], [0, "56cejs8LFAU6GZP9XgNZlF", 1], [5, 160, 61], [1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "3cang", 99, 22, [-435], [[21, 0, -433, [96], 97], [7, 1.1, 3, -434, [[8, "de9d4qcMbZCtKf2NtJTNE75", "onSelectType", 1]], 98]], [0, "07x8dOEn1EHpKXPMbtrYMq", 1], [5, 160, 61], [178, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "daud<PERSON>i", 99, 22, [-438], [[21, 0, -436, [101], 102], [7, 1.1, 3, -437, [[8, "de9d4qcMbZCtKf2NtJTNE75", "onSelectType", 1]], 103]], [0, "76RaLemVtGTZJKibzsTLsF", 1], [5, 160, 61], [-178, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "loxien", 99, 22, [-441], [[21, 0, -439, [106], 107], [7, 1.1, 3, -440, [[8, "de9d4qcMbZCtKf2NtJTNE75", "onSelectType", 1]], 108]], [0, "c8v1Wa9A9MYZ0h8lQrGgQA", 1], [5, 160, 61], [0, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "<PERSON><PERSON><PERSON>", 99, 22, [-444], [[21, 0, -442, [111], 112], [93, 0, 1.1, 3, -443, [[8, "de9d4qcMbZCtKf2NtJTNE75", "onSelectType", 1]], 113]], [0, "07lX+85mVAObPttZGkEopw", 1], [5, 160, 61], [178, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "header", 37, [-445, -446], [0, "65Qdg+gGpBfrH9zoukBDpA", 1], [0, 19, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lo2so", 70, [-449], [[3, -447, [120], 121], [7, 1.1, 3, -448, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 37]], 122]], [0, "9ceRSxRddJGqT1ZXoeAi+U", 1], [5, 258, 90], [-131, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "lo3so", 99, 70, [-452], [[3, -450, [125], 126], [7, 1.1, 3, -451, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 37]], 127]], [0, "aatcKZWQhJ/5z5Rh2CQk9Z", 1], [5, 258, 90], [138, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "body", 37, [-453, -454], [0, "f0B+BjZPJJB4Jgrujh3J8P", 1]], [20, "lo2so", 73, [4, -455, 24], [0, "86p+ZXWVhLwIYS2w95S7x0", 1]], [6, "Bet", 24, [-456, -457, -458], [0, "de1GKRsk9L7L8vGHeBziEi", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 24, [-459, -460, -461], [0, "0ft7XxmxRK46ScU+ctBbj4", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "header", 38, [-462, -463], [0, "22+TyLaT1OWr+tFdLkCbnz", 1], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "<PERSON><PERSON><PERSON>", 77, [-466], [[3, -464, [180], 181], [7, 1.1, 3, -465, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 38]], 182]], [0, "46Hmh0Fd9OaZKsitHHeV2j", 1], [5, 258, 90], [-131, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 99, 77, [-469], [[3, -467, [185], 186], [7, 1.1, 3, -468, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 38]], 187]], [0, "09qRA2aK1PA6jLssqR/n85", 1], [5, 258, 90], [138, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "body", 38, [-470, -471], [0, "99ZBNUztJGObngcozf53Yu", 1], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "<PERSON><PERSON><PERSON>", 80, [5, -472, 25], [0, "d436eGUdRGNaZU7TZJJvCs", 1]], [6, "Bet", 25, [-473, -474, -475], [0, "13+MsEGqpCGamaIMv11KUd", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 25, [-476, -477, -478], [0, "94nsNpetBN9aTh1QyQHi0o", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "<PERSON><PERSON><PERSON><PERSON><PERSON>", false, 80, [6, -479, 26], [0, "b1jMFo40pAOZQquwuaIpUR", 1]], [6, "Bet", 26, [-480, -481, -482], [0, "c3VsQoQBpIvbJDDq2AhVRg", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 26, [-483, -484, -485], [0, "894HwQ1A9NQI1cHwkgLN2M", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "header", 39, [-486, -487], [0, "33O9HTW1FC5qxIN4Ssxp5L", 1], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "bacang", 87, [-490], [[3, -488, [286], 287], [7, 1.1, 3, -489, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 39]], 288]], [0, "c11+gNGh9MKJDcOVp3CGu8", 1], [5, 258, 90]], [71, "<PERSON><PERSON><PERSON><PERSON><PERSON>", false, 99, 87, [-493], [[3, -491, [291], 292], [7, 1.1, 3, -492, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 39]], 293]], [0, "ddzIF8MuFFyb4Udb+8CPIj", 1], [5, 258, 90], [138, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "body", 39, [48, -494], [0, "0bmloBFFxIMYHuyHn4ITm4", 1], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Bet", 27, [-495, 50, -496], [0, "a60QgD+epM4pRh9fsNs7nz", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 27, [-497, 51, -498], [0, "dacda5VZdHsby3+8IOGipn", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "header", 40, [-499, -500], [0, "dcvaeCsFREbJKjJ6+1JokJ", 1], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "dau", 93, [-503], [[3, -501, [346], 347], [7, 1.1, 3, -502, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 40]], 348]], [0, "eeHtpWuzBAsaN1Gx2v2Rar", 1], [5, 258, 90], [-131, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "duoi", 99, 93, [-506], [[3, -504, [351], 352], [7, 1.1, 3, -505, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 40]], 353]], [0, "aaTJ8/L1JNv77G9BZVycPV", 1], [5, 258, 90], [138, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "body", 40, [-507, -508], [0, "b8Kf77GTtGeplBeVJEDE36", 1], [0, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "dau", 96, [8, -509, 28], [0, "88lOBB8t1Ka5lAw8lhalaS", 1]], [6, "Bet", 28, [-510, -511, -512], [0, "c5yVRmpE9B14e8NWKNb1VH", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 28, [-513, -514, -515], [0, "b7PuDfPrVLbaSDfmFQYQz+", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "duoi", false, 96, [9, -516, 29], [0, "eeUac6khVD/IODJew4xxDS", 1]], [6, "Bet", 29, [-517, -518, -519], [0, "e8qal4+tlAY4qpZw8uBg4F", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 29, [-520, -521, -522], [0, "3dwVE2uQBCo5OhHK2TzrQc", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "xien2", 52, [-525], [[3, -523, [450], 451], [7, 1.1, 3, -524, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 33]], 452]], [0, "e2I7OQYrVHi7C08Fl0psEZ", 1], [5, 258, 90], [-131, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "xien3", 99, 52, [-528], [[3, -526, [455], 456], [7, 1.1, 3, -527, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 33]], 457]], [0, "1ae9NU7jNPvYLGX1iAzhZ6", 1], [5, 258, 90], [138, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "xien4", 99, 52, [-531], [[3, -529, [460], 461], [7, 1.1, 3, -530, [[8, "db150+aUctGxY2RIIUbBb1w", "onSelectType", 33]], 462]], [0, "e65yO438xMYa2lmew2ieF7", 1], [5, 258, 90], [0, -79, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "xien2", 53, [10, -532, 30], [0, "26RrOdrdhJipkL+RMzudgj", 1]], [6, "Bet", 30, [-533, -534, -535], [0, "89XIznOQxFk4XNZmIbIC8r", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 30, [-536, -537, -538], [0, "cdzFKJ5oVH5Ip0NB1Vwlst", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "xien3", false, 53, [11, -539, 31], [0, "8eEKGEYG9AkJYa0WP38ttq", 1]], [6, "Bet", 31, [-540, -541, -542], [0, "4757YwwX1MGKfKlJEWKtMG", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 31, [-543, -544, -545], [0, "d1VX8oe8ZHtZGCVUmmUy8K", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "xien4", false, 53, [12, -546, 32], [0, "805K/9TolNZZoLka13o7ij", 1]], [6, "Bet", 32, [-547, -548, -549], [0, "d0xd3t0DFHBJpzXNUVXZQ8", 1], [-485, 184, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "total", 32, [-550, -551, -552], [0, "49525qLaBPe7Mu45qloL4a", 1], [-485, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "scrollview", [-556], [[45, 1, 0, -553, [633]], [60, false, 0.75, 0.23, null, null, -555, -554]], [0, "b5X/j3hDpEgpiSmWgwl+BX", 1], [5, 380, 530], [0, 0.5, 1], [0, 286.1, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "content", [[58, 1, 2, 10, 10, 0, -557, [5, 380, 800]]], [0, "2ayjn2QalO+JMq43vQyBU6", 1], [5, 380, 800], [0, 0.5, 1]], [24, "scrollview", [-561], [[45, 1, 0, -558, [635]], [60, false, 0.75, 0.23, null, null, -560, -559]], [0, "c2ke1RsLRJ7L2vbs6ueKeP", 1], [5, 380, 530], [0, 0.5, 1], [0, 286.1, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "content", [[58, 1, 2, 10, 10, 0, -562, [5, 380, 80]]], [0, "beffp4iJ9P5JBNMDRRDejU", 1], [5, 380, 80], [0, 0.5, 1]], [6, "layoutResult", 41, [2, 57], [0, "2eaEzA3phPx4A/jyq2+weX", 1], [0, -24, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "g6", 2, [-563, -564, -565], [0, "49K05K47RJbYJxwic8koeb", 1]], [55, "btnResultNext", false, 57, [-567], [-566], [0, "445H8sPlZAoLkX9IECiqmT", 1], [5, 50, 60], [152, 155, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "btnResultPrevious", false, 57, [-569], [-568], [0, "7ducj+Q6FH05drGP3ljWUd", 1], [5, 50, 60], [-147, 155, 0, 0, 0, 0, 1, 1, 1, 1]], [72, "btnBack", 58, [[3, -570, [690], 691], [94, -571, [[8, "de9d4qcMbZCtKf2NtJTNE75", "toggleShow", 1]]]], [0, "c81pIFSERIvbxoOcq2mwFR", 1], [5, 85, 87], [-60, 3, 0, 0, 0, 1, 6.123233995736766e-17, 0.8, 0.8, 1], [1, 0, 0, 180]], [16, "bg", 1, [[21, 0, -572, [0], 1], [95, -573]], [0, "6fIs5ZL5ZGKK5KyUOYyMY4", 1], [5, 1398, 786]], [5, "bgblance", 47, [-575], [[91, 0, -574, [4]]], [0, "4aO1fpHOdInrvZcm6tKnyA", 1], [5, 269, 50], [16, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "lblblance", 125, [[[61, "123456789", 20, false, 1, -576, [2], 3], -577], 4, 1], [0, "faOfRm7gZEJYoee9f777JE", 1], [5, 191.25, 40], [0, 29, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "header", 63, [-578, -579], [0, "8clwpAK5dHbaMV6Zi0Q5Ap", 1], [0, 250, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnhelp", 74, [[3, -580, [143], 144], [7, 1.1, 3, -581, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 145]], [0, "ab/3YUcvBFXLSfmNd+1dC4", 1], [5, 70, 36], [223, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 24, [[3, -582, [148], 149], [7, 1.1, 3, -583, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "4", 1]], 150]], [0, "7cKPLMLA5O47MrVCQZwlkS", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 75, [-585], [[3, -584, [155], 156]], [0, "08p+K5FrVDYreC12Ux3vRG", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 76, [-587], [[3, -586, [163], 164]], [0, "18x03gRulP74DnZ4WZNR5Q", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 24, [-588, -589], [0, "3dp/Gk1V1MeKXRhdeVAHln", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 24, [-591], [[26, 1.1, 3, -590, [[35]]]], [0, "4cSL38l3ZF2LehzjWdzr8d", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnIC", 133, [[3, -592, [171], 172], [7, 1.1, 3, -593, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "4", 1]], 173]], [0, "dduzLTYc1M/rRyaUgXNWZc", 1], [5, 161, 58]], [1, "btnhelp", 81, [[3, -594, [203], 204], [7, 1.1, 3, -595, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 205]], [0, "1f/e4KJ+9JeJpHHE55QVaJ", 1], [5, 70, 36], [223, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 25, [[3, -596, [208], 209], [7, 1.1, 3, -597, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "2", 1]], 210]], [0, "6eo/o6MYNH/IiJhIgBai9/", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 82, [-599], [[3, -598, [215], 216]], [0, "8dka28roJCXqy2B1kSBkQM", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 83, [-601], [[3, -600, [223], 224]], [0, "d8e6cyVLZCh6dOQMlAuFuG", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 25, [-602, -603], [0, "02r0HftCBO1aoDt2/ZBm5/", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 25, [-605], [[26, 1.1, 3, -604, [[35]]]], [0, "26nzQACixJcYs+/TW1tJhQ", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnIC", 140, [[3, -606, [231], 232], [7, 1.1, 3, -607, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "2", 1]], 233]], [0, "c9nBGsDQNFy4qtu7YaFDKw", 1], [5, 161, 58]], [1, "btnhelp", 84, [[3, -608, [251], 252], [7, 1.1, 3, -609, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 253]], [0, "48SxeJyBdIPZMtnWE2BeIk", 1], [5, 70, 36], [223, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 26, [[3, -610, [256], 257], [7, 1.1, 3, -611, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "1", 1]], 258]], [0, "81HgTVncNGJ6tnO5v93qrZ", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 85, [-613], [[3, -612, [263], 264]], [0, "a1v0TkpRlCuJMQm6lyp2jY", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 86, [-615], [[3, -614, [271], 272]], [0, "4dvNkCWQ1Ge78rRqgIJ0ZN", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 26, [-616, -617], [0, "31sgutfphMGK+zJbdYUvxq", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 26, [-619], [[26, 1.1, 3, -618, [[35]]]], [0, "7cq/leVuxCNI3WorFwZeRJ", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnIC", 147, [[3, -620, [279], 280], [7, 1.1, 3, -621, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "1", 1]], 281]], [0, "597f2gAwVPWbU+8D7SpOgV", 1], [5, 161, 58]], [53, "btnhelp", false, 48, [[3, -622, [309], 310], [7, 1.1, 3, -623, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 311]], [0, "1c8LAJ+2dLI7sIlbFOR21a", 1], [5, 70, 36], [223, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "BACKGROUND_SPRITE", 49, [[-624, [42, 0, 45, 160, 40, -625]], 1, 4], [0, "0ecZBejtxGB5Or+rSpUBez", 1], [5, 430, 50]], [77, "TEXT_LABEL", false, 49, [[-626, [86, 0, 45, 2, -16, 16, 158, 40, -627]], 1, 4], [0, "d8sbR0lwZLwYK23bzPG5yx", 1], [5, 428, 50], [0, 0, 1], [-213, 41, 0, 0, 0, 0, 1, 1, 1, 1]], [78, "PLACEHOLDER_LABEL", 49, [[-628, [43, 0, 45, 2, 158, 40, -629]], 1, 4], [0, "442TsLW0lH/ZxBSNBRIFHW", 1], [4, 4290493371], [5, 428, 50], [0, 0, 1], [-213, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 27, [[3, -630, [315], 316], [7, 1.1, 3, -631, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "2", 1]], 317]], [0, "d0P3YqZZ1MEpXawcobSspV", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "BACKGROUND_SPRITE", 50, [[-632, [42, 0, 45, 160, 40, -633]], 1, 4], [0, "7fTCySHctKcJoLtqdvGLho", 1], [5, 332, 81]], [56, "TEXT_LABEL", false, 50, [[-634, [43, 0, 45, 2, 158, 40, -635]], 1, 4], [0, "8dzszjzbxAE48aO/85+Co9", 1], [5, 280, 60], [-13, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "PLACEHOLDER_LABEL", 50, [[-636, [57, 0, 45, 13, 39, -0.5, 21.5, 158, 40, -637]], 1, 4], [0, "6fzlGd/YpE+IaOxYzBEacu", 1], [5, 280, 60], [-13, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "BACKGROUND_SPRITE", 51, [[-638, [42, 0, 45, 160, 40, -639]], 1, 4], [0, "6e2mDgs9RIQbkaoaZTC+bS", 1], [5, 332, 81]], [56, "TEXT_LABEL", false, 51, [[-640, [43, 0, 45, 2, 158, 40, -641]], 1, 4], [0, "e6n9UYC3tJNYepz+hDCCOw", 1], [5, 280, 60], [-13, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "PLACEHOLDER_LABEL", 51, [[-642, [57, 0, 45, 13, 39, -0.5, 21.5, 158, 40, -643]], 1, 4], [0, "09R9iHJ8BOYbKEZHd+IhUA", 1], [5, 280, 60], [-13, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 27, [-644, -645], [0, "bakWjrF5VKFpjmQiZmfuZT", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 27, [-647], [[26, 1.1, 3, -646, [[35]]]], [0, "cdzayKkDVCVrADavZwqOEL", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnhelp", 97, [[3, -648, [369], 370], [7, 1.1, 3, -649, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 371]], [0, "7d1b6H685K85KCyQIFEK4f", 1], [5, 70, 36], [223, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 28, [[3, -650, [374], 375], [7, 1.1, 3, -651, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "2", 1]], 376]], [0, "96ho3D5XBPfbjKbDJ0UYlc", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 98, [-653], [[3, -652, [381], 382]], [0, "64jDeNlTNDGLJRVlHsnFLI", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 99, [-655], [[3, -654, [389], 390]], [0, "b8p4Ly1HxCy74h3F4JpgHw", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 28, [-656, -657], [0, "588KToWCZIHYccZV7XsFju", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 28, [-659], [[26, 1.1, 3, -658, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "2", 1]]]], [0, "fbCndTc0VHdorg7Him8VJK", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnhelp", 100, [[3, -660, [416], 417], [7, 1.1, 3, -661, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 418]], [0, "3f+2iKjtpG/Zf3fMES8mac", 1], [5, 70, 36], [223, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 29, [[3, -662, [421], 422], [7, 1.1, 3, -663, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "3", 1]], 423]], [0, "4aLzN0mH5LbItp31fM6WS1", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 101, [-665], [[3, -664, [428], 429]], [0, "d970SP/BpPL5vO1JoDtacl", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 102, [-667], [[3, -666, [436], 437]], [0, "56hgIO80tCSIRzrEIlAbE0", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 29, [-668, -669], [0, "c0Xf324phDP6eRODgx3Z0D", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 29, [-671], [[26, 1.1, 3, -670, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "3", 1]]]], [0, "a3tKf8CxRDjrE+QZhJRb8+", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnhelp", 106, [[3, -672, [478], 479], [7, 1.1, 3, -673, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 480]], [0, "82u5kAPTZEc5mekgD/ieTA", 1], [5, 70, 36], [209, -43, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 30, [[3, -674, [483], 484], [7, 1.1, 3, -675, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "5", 1]], 485]], [0, "a7qbo7ZDFFYJbkJeA62pOD", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 107, [-677], [[3, -676, [490], 491]], [0, "f4uLiO265MRbgbPNm2ynd8", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 108, [-679], [[3, -678, [498], 499]], [0, "115fPFgvNKcpZ7xyKJVFp2", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 30, [-680, -681], [0, "ddcVsOTSZLoKdNKOL1uZSl", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 30, [-683], [[26, 1.1, 3, -682, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "5", 1]]]], [0, "59Q83JBfpH6r/KWcBtYNzh", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnhelp", 109, [[3, -684, [525], 526], [7, 1.1, 3, -685, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 527]], [0, "01MM1BaPxFE5Cg4aA22c4A", 1], [5, 70, 36], [206, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 31, [[3, -686, [530], 531], [7, 1.1, 3, -687, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "6", 1]], 532]], [0, "d3P96jm1FNnr36S0XP3JJl", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 110, [-689], [[3, -688, [537], 538]], [0, "f1R1vVQZpABognuGwU4L7+", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 111, [-691], [[3, -690, [545], 546]], [0, "e6IC4vrmtKprxelFAkzJYl", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 31, [-692, -693], [0, "d8BHk4+nhCraPKHdpWaN4Y", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 31, [-695], [[26, 1.1, 3, -694, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "6", 1]]]], [0, "8a6BeVExtGRZ0S+Ou8xTC+", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnhelp", 112, [[3, -696, [572], 573], [7, 1.1, 3, -697, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], 574]], [0, "16R5/GpNBKVY6q2/XxW5yf", 1], [5, 70, 36], [208, -42, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnChoice", 32, [[3, -698, [577], 578], [7, 1.1, 3, -699, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "7", 1]], 579]], [0, "b9tXTitE5B7Ig/UwmohxLC", 1], [5, 34, 32], [219, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 113, [-701], [[3, -700, [584], 585]], [0, "36DF39JTtOq60c77yZkLoj", 1], [5, 332, 81], [0, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "value", 114, [-703], [[3, -702, [592], 593]], [0, "dd5I35jDdBm4eDymS+CAAS", 1], [5, 332, 81], [0, 47, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "win", 32, [-704, -705], [0, "bf5NqREmdHFoAmpCGXTDvW", 1], [-485, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnBet", 32, [-707], [[26, 1.1, 3, -706, [[14, "de9d4qcMbZCtKf2NtJTNE75", "onOpenChooseView", "7", 1]]]], [0, "f9qdJW7tdHmJnTvXKmQE9X", 1], [5, 150, 50], [-485, -99, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "active", 42, [-709], [[3, -708, [610], 611]], [0, "f3cXLY/8dGr5Z2hg2Ntc1S", 1], [5, 190, 44]], [29, "deactive", false, 42, [-711], [[3, -710, [614], 615]], [0, "30Vf4uiyFOXrvHj5zRoDmT", 1], [5, 190, 44]], [29, "active", false, 43, [-713], [[3, -712, [618], 619]], [0, "b2oarNdyxLyoDpwJ9pxFcI", 1], [5, 190, 44]], [34, "deactive", 43, [-715], [[3, -714, [622], 623]], [0, "41sZX3DJdHmZw+OfDXmfMt", 1], [5, 190, 44]], [29, "active", false, 44, [-717], [[3, -716, [626], 627]], [0, "d3Qsvso3BG642z/D/WOpOs", 1], [5, 190, 44]], [34, "deactive", 44, [-719], [[3, -718, [630], 631]], [0, "812APmbe1DqKiXNJ13S31M", 1], [5, 190, 44]], [6, "layoutBetting", 41, [115], [0, "e2+tniqatDgYUq0OYIj+VK", 1], [0, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "view", 115, [116], [[63, -720, [632]]], [0, "95dxUJFjpKLbkflvw0+Prk", 1], [5, 380, 530], [0, 0.5, 1]], [6, "layoutBetted", 41, [117], [0, "echk02pWBEs6aQld5mJLrE", 1], [0, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "view", 117, [118], [[63, -721, [634]]], [0, "cabH358tVJB5yQby74Wowc", 1], [5, 380, 530], [0, 0.5, 1]], [20, "g2", 2, [-722, -723], [0, "7fNSnz64hBopQ5LgHMjPid", 1]], [73, "panelMenu", 1, [58], [[87, 32, -40, -724]], [0, "cbTmlSI65K65Y3sJbFAvc5", 1], [820.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnTop", 45, [59], [[46, 1.1, 3, -725, [[8, "3b6acKa2bVF47OyLNTM29it", "createTopView", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 59, 698]], [0, "abuWBBnJZCd7qUobp441LF", 1], [5, 77, 74], [0, 153, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "btnHelp", 45, [60], [[46, 1.1, 3, -726, [[8, "3b6acKa2bVF47OyLNTM29it", "createHelpView", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 60, 705]], [0, "f6dKsAZzRMyo2YH70z9Sj3", 1], [5, 77, 74], [0, 49, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [5, "btnHistory", 45, [61], [[46, 1.1, 3, -727, [[8, "3b6acKa2bVF47OyLNTM29it", "createHistoryView", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 61, 712]], [0, "48vBJhL9RCeZMgOBtXdiMY", 1], [5, 77, 74], [0, -55, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [5, "btnBack", 45, [-729], [[26, 1.1, 3, -728, [[8, "de9d4qcMbZCtKf2NtJTNE75", "backClicked", 1]]]], [0, "e0YhZP/lRGlLN8cVXcM9NP", 1], [5, 77, 74], [0, -159, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [98, 126], [1, "lbcoin", 47, [[61, "$", 30, false, 1, -730, [5], 6]], [0, "edBSYxr81FB6utUTPxY/Vn", 1], [5, 32.81, 40], [-156, 53, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbNoti", 46, [[2, "<PERSON><PERSON><PERSON> quý khách đại cát đại lợi.", 30, false, 1, 1, -731, [9], 10]], [0, "acq7A50x9IGYue3vlyoO9p", 1], [4, 4287621884], [5, 455.21, 50.4], [205, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "lbTimer", false, 127, [-732], [0, "d046Vk7dZD+K8N18nBOAl/", 1], [4, 4294963234], [5, 55.6, 20], [0, 0, 0.5], [-100, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "000000", 16, 50, false, 1, 1, 211, [13]], [76, "lbTimerCount", 127, [-733], [0, "7bdmRvQmVI9bCWP8rAhid+", 1], [4, 4278255442], [5, 138.75, 50], [0, 0, 0.5], [-58, 13, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "00000000", 30, 50, false, 1, 1, 213, [14]], [11, "lbChanel", 36, [-734], [0, "47ZBm7rdJAsYW921vDJ1b2", 1], [5, 68.76, 50.4], [-115, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [96, "15-04", 26, false, 1, 1, 215, [15]], [1, "arrowleft", 36, [[3, -735, [16], 17]], [0, "c6FlEKxMpK/r+lAwQ+RVMU", 1], [5, 33, 25], [-52, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "arrowRight", 36, [[3, -736, [18], 19]], [0, "7dziFsUIhPcKtA7Phex1h5", 1], [5, 33, 25], [148, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "kenh", 36, [[2, "<PERSON><PERSON><PERSON>", 26, false, 1, 1, -737, [20], 21]], [0, "casoTuV4pCNr2a59Uj6OsG", 1], [5, 118.88, 50.4], [58, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "Text_chondc", false, 14, [[92, false, -738, [26], 27]], [0, "0eVdW1ZFFCzrZkUhfJpkCn", 1], [5, 235, 39], [-4.4, 159.1, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbshadown", 15, [[18, "ĐỀ", 22, 50, false, 1, 1, -739, [28], 29]], [0, "95m8+A7dZLEbCtCgr1kNiS", 1], [4, 4281348144], [5, 28.05, 27.5], [-1.9, 13.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb1", 15, [[18, "ĐỀ", 22, 50, false, 1, 1, -740, [30], 31]], [0, "70CxkgiidBS4j0BFUinoVn", 1], [5, 28.05, 27.5], [-0.9, 14.1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbshadownMutiple", 15, [-741], [0, "eee5606C9Ic5ACTHOC/z6H", 1], [4, 4281348144], [5, 0, 22.5], [-1.9, -9.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 223, [32]], [11, "lbMutiple", 15, [-742], [0, "16zMdJ8HlOcahPnWIxMbS2", 1], [5, 0, 22.5], [-0.9, -8.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 225, [33]], [9, "lbshadown", 16, [[18, "\rLÔ", 22, 50, false, 1, 1, -743, [36], 37]], [0, "3fSJgIdxVCEqu8b7GIAhWQ", 1], [4, 4281348144], [5, 28.05, 27.5], [-1.9, 13.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb1", 16, [[18, "\rLÔ", 22, 50, false, 1, 1, -744, [38], 39]], [0, "07Xnnx2/dHi4fY4zjVRbxJ", 1], [5, 28.05, 27.5], [-0.9, 14.1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbshadownMutiple\u001c", 16, [-745], [0, "e2UnCMmDxMqKxaiZxRC1xm", 1], [4, 4281348144], [5, 0, 22.5], [-1.9, -9.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 229, [40]], [11, "lbMutiple", 16, [-746], [0, "a7qdeOBuRDXrHFQzW6P3x1", 1], [5, 0, 22.5], [-0.9, -8.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 231, [41]], [9, "lbshadown", 17, [[18, "ĐỀ SỐ ĐẦU", 22, 50, false, 1, 1, -747, [44], 45]], [0, "92KGgyBUBMRrYBdgw9iUXQ", 1], [4, 4281348144], [5, 112.75, 27.5], [-1.9, 13.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb1", 17, [[18, "ĐỀ SỐ ĐẦU", 22, 50, false, 1, 1, -748, [46], 47]], [0, "6fiSdaYsRLiqqPXLvnEZVi", 1], [5, 112.75, 27.5], [-0.9, 14.1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbshadownMutiple\u001c", 17, [-749], [0, "0eo/dyCo9O25xJUrOdvHF+", 1], [4, 4281348144], [5, 0, 22.5], [-1.9, -9.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 235, [48]], [11, "lbMutiple", 17, [-750], [0, "b6AsiK58VAp5pvh1MjhZv1", 1], [5, 0, 22.5], [-0.9, -8.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 237, [49]], [9, "lbshadown", 18, [[18, "ĐỀ SỐ CUỐI", 22, 50, false, 1, 1, -751, [52], 53]], [0, "7fmlsSwONNRrFNd9AFkHdh", 1], [4, 4281348144], [5, 119.35, 27.5], [-1.9, 13.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb1", 18, [[18, "ĐỀ SỐ CUỐI", 22, 50, false, 1, 1, -752, [54], 55]], [0, "09K/fl/NNCmpI0SYwMZldN", 1], [5, 119.35, 27.5], [-0.9, 14.1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbshadownMutiple\u001c", 18, [-753], [0, "4ciEHL+NxBR6wbwBL0ElNE", 1], [4, 4281348144], [5, 0, 22.5], [-1.9, -9.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 241, [56]], [11, "lbMutiple", 18, [-754], [0, "4eA1I+cj5Bc4olzQ3R1ReB", 1], [5, 0, 22.5], [-0.9, -8.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 243, [57]], [9, "lbshadown", 19, [[18, "XIÊN 2", 22, 50, false, 1, 1, -755, [60], 61]], [0, "69YKYGjjBH9a1NnZyvq3FS", 1], [4, 4281348144], [5, 68.2, 27.5], [-1.9, 13.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb1", 19, [[18, "XIÊN 2", 22, 50, false, 1, 1, -756, [62], 63]], [0, "77CHUNED9PG5YG5i/6c9Cq", 1], [5, 68.2, 27.5], [-0.9, 14.1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbshadownMutiple\u001c", 19, [-757], [0, "2fJkhKlcxE3K3QZ6jqhPSi", 1], [4, 4281348144], [5, 0, 22.5], [-1.9, -9.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 247, [64]], [11, "lbMutiple", 19, [-758], [0, "cf35S0a1NLN6JSzqo7Bn/s", 1], [5, 0, 22.5], [-0.9, -8.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 249, [65]], [9, "lbshadown", 20, [[18, "XIÊN 3", 22, 50, false, 1, 1, -759, [68], 69]], [0, "85XJfusGBGe77hLHD8lT20", 1], [4, 4281348144], [5, 68.2, 27.5], [-1.9, 13.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb1", 20, [[18, "XIÊN 3", 22, 50, false, 1, 1, -760, [70], 71]], [0, "8dF51g0rlPx67d1+Bikmbw", 1], [5, 68.2, 27.5], [-0.9, 14.1, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbshadownMutiple\u001c", 20, [-761], [0, "4ew1XAL6hMOo/m/qSUAvNf", 1], [4, 4281348144], [5, 0, 22.5], [-1.9, -9.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 253, [72]], [11, "lbMutiple", 20, [-762], [0, "fe38PJb2BKArpTT1nqLIpJ", 1], [5, 0, 22.5], [-0.9, -8.9, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 255, [73]], [9, "lbshadown", 21, [[18, "XIÊN 4", 22, 50, false, 1, 1, -763, [76], 77]], [0, "96ORSb3nVEWpe9bHXJNVix", 1], [4, 4281348144], [5, 69.3, 27.5], [-3.3, 14.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb1", 21, [[18, "XIÊN 4", 22, 50, false, 1, 1, -764, [78], 79]], [0, "edZ5ENE5FIC7h3VoOSqjh4", 1], [5, 69.3, 27.5], [-2.3, 15.7, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbshadownMutiple\u001c", 21, [-765], [0, "6eMQgcZOlHE4TI6DGmjCeO", 1], [4, 4281348144], [5, 0, 22.5], [-4.5, -10.5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 259, [80]], [11, "lbMutiple", 21, [-766], [0, "bdVtUKBZZA8YueBBab+Tmg", 1], [5, 0, 22.5], [-3.5, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 18, 50, false, 1, 1, 261, [81]], [1, "Label", 64, [[2, "Bao Lô", 20, false, 1, 1, -767, [84], 85]], [0, "08lM5nM5xJJYvsz2CGUh76", 1], [5, 115, 40], [0, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 65, [[2, "<PERSON><PERSON><PERSON>", 20, false, 1, 1, -768, [89], 90]], [0, "a1tTWzr6ZJk6yUudTGgChi", 1], [5, 137.5, 40], [0, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 66, [[2, "3 Càng", 20, false, 1, 1, -769, [94], 95]], [0, "97YwU0tHpBxb9gO4BDMTuA", 1], [5, 116.88, 40], [0, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 67, [[2, "<PERSON><PERSON><PERSON>", 20, false, 1, 1, -770, [99], 100]], [0, "06l/+IqwNKjYiTLEu/uYB+", 1], [5, 148.75, 40], [0, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 68, [[2, "<PERSON><PERSON>", 20, false, 1, 1, -771, [104], 105]], [0, "e28YEkrkRP+7Y7gm9qxhrq", 1], [5, 124.38, 40], [0, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 69, [[2, "<PERSON><PERSON>", 20, false, 1, 1, -772, [109], 110]], [0, "87bCZO99tCXJgi1a2EhIvn", 1], [5, 150.63, 40], [0, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "line", 14, [[3, -773, [114], 115]], [0, "82nlkNa+1Jz6+zZabZkxAb", 1], [5, 614, 13], [0, 119, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgBet", 14, [[21, 0, -774, [116], 117]], [0, "2fqk6ShD9AA5ijoT7vmDXn", 1], [5, 550, 361], [0, -82, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 71, [[2, "Lô 2 Số", 20, false, 1, 1, -775, [118], 119]], [0, "a50fj4IHxJgLAKcgGfGi80", 1], [5, 134.38, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 72, [[2, "Lô 3 Số", 20, false, 1, 1, -776, [123], 124]], [0, "4b/KgnFxFA7rp71pXiUTzx", 1], [5, 135, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "RICHTEXT_CHILD", 1024, -32768, 4, [[4, "<PERSON><PERSON> toán ", 30, 50, false, 1, -777, [128], 129]], [0, "5a6Mp/6w5CBLWK6sIVL88o", 1], [5, 177.03, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "RICHTEXT_CHILD", 1024, -32768, 4, [[4, "27", 30, 50, false, 1, -778, [130], 131]], [0, "f6Rc5n1YxNUq24Ks2mBFzb", 1], [4, 4278255615], [5, 34.42, 63], [0, 0, 0], [177.03, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "RICHTEXT_CHILD", 1024, -32768, 4, [[4, " lô. Đặt ", 30, 50, false, 1, -779, [132], 133]], [0, "f5S7kqD1xI9YHL7O6iRyyv", 1], [5, 112.65, 63], [0, 0, 0], [211.45, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "RICHTEXT_CHILD", 1024, -32768, 4, [[4, "1", 30, 50, false, 1, -780, [134], 135]], [0, "1cWhhUFNhJp77qGMCIOnxU", 1], [4, 4278255615], [5, 17.21, 63], [0, 0, 0], [324.1, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "RICHTEXT_CHILD", 1024, -32768, 4, [[4, " ăn", 30, 50, false, 1, -781, [136], 137]], [0, "7c4dPpmIROqYxjyqfU9s++", 1], [5, 45.28, 63], [0, 0, 0], [341.31, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "RICHTEXT_CHILD", 1024, -32768, 4, [[4, " 99", 30, 50, false, 1, -782, [138], 139]], [0, "56RM1gHFlE6Z+7v2F/FsAj", 1], [4, 4278255615], [5, 43.24, 63], [0, 0, 0], [386.59000000000003, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "RICHTEXT_CHILD", 1024, -32768, 4, [[4, " ", 30, 50, false, 1, -783, [140], 141]], [0, "8a8JKjySxMbJuZ+CUvlTd3", 1], [5, 8.82, 63], [0, 0, 0], [429.83000000000004, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 24, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -784, [146], 147]], [0, "c7AYyBn2pNgoTlbOBBFMmC", 1], [5, 129.38, 40], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 75, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -785, [151], 152]], [0, "68u04UoE5E6ohg/oTJY1Wj", 1], [4, 4278237439], [5, 219.38, 40], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 130, [[13, "1", false, 1, 1, -786, [153], 154]], [0, "83XJ7odTNDa7fybUvjCFHr", 1], [5, 20, 40], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 75, [[2, "K", 36, false, 1, 1, -787, [157], 158]], [0, "fer6pqvqVDqYJOXZK8Slx6", 1], [5, 21.38, 40], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 76, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -788, [159], 160]], [0, "daAQHZm79BYoT/r8bE8a6G", 1], [4, 4278237439], [5, 182.81, 40], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 131, [[13, "27", false, 1, 1, -789, [161], 162]], [0, "99sLnAhDpNQqB81aYsASoG", 1], [5, 41.25, 40], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 76, [[2, "K", 36, false, 1, 1, -790, [165], 166]], [0, "88cT+XfR5Ntpq58+7nShji", 1], [5, 21.38, 40], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 132, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -791, [167], 168]], [0, "960pBxBsJP4o6n2Q3uUq0O", 1], [4, 4278237439], [5, 229.69, 40]], [16, "win", 132, [[13, "99K", false, 1, 1, -792, [169], 170]], [0, "7caX10b0FODKqPp9jGauBp", 1], [5, 135, 40]], [31, "lo3so", false, 73, [-793], [0, "a9klBta9tAArYSBUaebjIn", 1]], [1, "inactive", 289, [[13, "S<PERSON><PERSON> ra mắt", false, 1, 1, -794, [176], 177]], [0, "dbUn4mg/9Nh4BmSDS6gT/s", 1], [5, 263.75, 50], [0, -97, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 78, [[2, "<PERSON><PERSON>", 20, false, 1, 1, -795, [178], 179]], [0, "e2xWsL0hZDB49/NFVKul+E", 1], [5, 120, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 79, [[2, "Đề Đặc Biệt", 20, false, 1, 1, -796, [183], 184]], [0, "d5XYHDyGJNDrieWPxtrqhl", 1], [5, 200, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 5, [[4, "<PERSON><PERSON> toán ", 30, 50, false, 1, -797, [188], 189]], [0, "641cHOrHFHZp51j2sU8p/p", 1], [5, 177.03, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 5, [[4, "4", 30, 50, false, 1, -798, [190], 191]], [0, "ab/ceFtfNLA5pa7Sc6JZI0", 1], [4, 4278255615], [5, 17.21, 63], [0, 0, 0], [177.03, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 5, [[4, " lô. Đặt ", 30, 50, false, 1, -799, [192], 193]], [0, "dcERxrhBVA3bNzfXrkfDNG", 1], [5, 112.65, 63], [0, 0, 0], [194.24, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 5, [[4, "1", 30, 50, false, 1, -800, [194], 195]], [0, "6aiSCMJyROz5rEzd6Ln7dG", 1], [4, 4278255615], [5, 17.21, 63], [0, 0, 0], [306.89, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 5, [[4, " ăn", 30, 50, false, 1, -801, [196], 197]], [0, "89LszQx0JKsZVO3sNuGVAH", 1], [5, 45.28, 63], [0, 0, 0], [324.09999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 5, [[4, " 95", 30, 50, false, 1, -802, [198], 199]], [0, "f1EhjnfQtMZqmnopuBq60K", 1], [4, 4278255615], [5, 43.24, 63], [0, 0, 0], [369.38, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 5, [[4, " ", 30, 50, false, 1, -803, [200], 201]], [0, "01t5p9lY1At5LFxclp90wc", 1], [5, 8.82, 63], [0, 0, 0], [412.62, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 25, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -804, [206], 207]], [0, "d18PD+HKRASrB8Voywgy8j", 1], [5, 129.38, 37.5], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 82, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -805, [211], 212]], [0, "5eKMCkqu5IZYFjqonzsVw0", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 137, [[13, "1", false, 1, 1, -806, [213], 214]], [0, "aaGeoLJk5GbZbtAO5kbPAF", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 82, [[2, "K", 36, false, 1, 1, -807, [217], 218]], [0, "7fszz740lFq7Mi658pLeL+", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 83, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -808, [219], 220]], [0, "eejs4o6mlAIqF5pQap4B0b", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 138, [[13, "4", false, 1, 1, -809, [221], 222]], [0, "cfQrLl22RJ4qUSRnr5KJ2s", 1], [5, 21.25, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 83, [[2, "K", 36, false, 1, 1, -810, [225], 226]], [0, "c1o6z4itxAZrkXjWkfwOaN", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 139, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -811, [227], 228]], [0, "54ehMfWDFET55+bjBe9H2x", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 139, [[13, "95K", false, 1, 1, -812, [229], 230]], [0, "1dG8aqayBGn7/Khl7H95mQ", 1], [5, 132.5, 50]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 6, [[4, "<PERSON><PERSON> toán ", 30, 50, false, 1, -813, [236], 237]], [0, "e2T+azlR9F6rWuksdaGMd4", 1], [5, 177.03, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 6, [[4, "1", 30, 50, false, 1, -814, [238], 239]], [0, "14CYxh1iFIgJEW03t+kmiz", 1], [4, 4278255615], [5, 17.21, 63], [0, 0, 0], [177.03, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 6, [[4, " lô. Đặt ", 30, 50, false, 1, -815, [240], 241]], [0, "d9aqYB56tFEoFsnW/TbxM8", 1], [5, 112.65, 63], [0, 0, 0], [194.24, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 6, [[4, "1", 30, 50, false, 1, -816, [242], 243]], [0, "2cfiuU/9pAToiz4nxtRba2", 1], [4, 4278255615], [5, 17.21, 63], [0, 0, 0], [306.89, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 6, [[4, " ăn", 30, 50, false, 1, -817, [244], 245]], [0, "38dGK5YNdFlKfkwP7UhtTf", 1], [5, 45.28, 63], [0, 0, 0], [324.09999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 6, [[4, " 95", 30, 50, false, 1, -818, [246], 247]], [0, "1dVpnHcW5DwZ+11GHKgdP6", 1], [4, 4278255615], [5, 43.24, 63], [0, 0, 0], [369.38, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 6, [[4, " ", 30, 50, false, 1, -819, [248], 249]], [0, "bfdz1alghDdpIRjvF+XmHR", 1], [5, 8.82, 63], [0, 0, 0], [412.62, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 26, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -820, [254], 255]], [0, "2e5z6D67VKh44OXRgwtpbn", 1], [5, 129.38, 37.5], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 85, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -821, [259], 260]], [0, "77t7eipcpPd7rWTWkaPcaX", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 144, [[13, "1", false, 1, 1, -822, [261], 262]], [0, "77GoaUyEVHDZTlpzyvQrz8", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 85, [[2, "K", 36, false, 1, 1, -823, [265], 266]], [0, "57mxauv9RGyqQzNchISJcH", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 86, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -824, [267], 268]], [0, "225jTs6OJHyYnZpR2oVFDH", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 145, [[13, "1", false, 1, 1, -825, [269], 270]], [0, "c05EiMzg5PIp4vqErWyE0u", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 86, [[2, "K", 36, false, 1, 1, -826, [273], 274]], [0, "7eLGWspA1Nx4y5qsOPSLfc", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 146, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -827, [275], 276]], [0, "5dwBUBJtJDAbmwtiPiUjuZ", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 146, [[13, "95K", false, 1, 1, -828, [277], 278]], [0, "fcWhtrrIFIK6tPRD6ZPmyN", 1], [5, 132.5, 50]], [1, "Label", 88, [[2, "Ba <PERSON>àng", 20, false, 1, 1, -829, [284], 285]], [0, "2cZ8uk+s1CHpMzUBfND5py", 1], [5, 137.5, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 89, [[2, "Đề Đặc Biệt", 20, false, 1, 1, -830, [289], 290]], [0, "d9fVONdsdBtZ8Mapwmq0w5", 1], [5, 200, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[4, "<PERSON><PERSON> toán ", 30, 50, false, 1, -831, [294], 295]], [0, "23du8B/WtDUKzMHPdq3LrV", 1], [5, 177.03, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[4, "27", 30, 50, false, 1, -832, [296], 297]], [0, "18gwhm3zBNXKxJtydD3luy", 1], [4, 4278255615], [5, 34.42, 63], [0, 0, 0], [177.03, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[4, " lô. Đặt ", 30, 50, false, 1, -833, [298], 299]], [0, "e2W8nybQZIT5sPRqcfxfK4", 1], [5, 112.65, 63], [0, 0, 0], [211.45, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[4, "1", 30, 50, false, 1, -834, [300], 301]], [0, "64Ro7FghlFub5rIrmDMPMf", 1], [4, 4278255615], [5, 17.21, 63], [0, 0, 0], [324.1, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[4, " ăn", 30, 50, false, 1, -835, [302], 303]], [0, "24nIXORwJOl4OPsxsH/3Tw", 1], [5, 45.28, 63], [0, 0, 0], [341.31, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[4, " 99", 30, 50, false, 1, -836, [304], 305]], [0, "79zEHEd3ZJ4KAOLXJtuUP9", 1], [4, 4278255615], [5, 43.24, 63], [0, 0, 0], [386.59000000000003, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 7, [[4, " ", 30, 50, false, 1, -837, [306], 307]], [0, "cfqzIh/aFEIKr8LvrREKCx", 1], [5, 8.82, 63], [0, 0, 0], [429.83000000000004, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [45, 1, 0, 150, [312]], [97, 20, 25, false, false, 1, 1, 151, [313]], [48, "GHI SỐ ĐỀ", 30, 25, false, false, 1, 1, 1, 152, [314]], [9, "text", 91, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -838, [318], 319]], [0, "7erp2/kM5Nq6ugaGUDJIAr", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [59, 1, 154, [320]], [62, 30, 25, false, false, 1, 1, 1, 155, [321]], [48, "1", 36, 25, false, false, 1, 1, 1, 156, [322]], [1, "chart", 91, [[2, "K", 36, false, 1, 1, -839, [323], 324]], [0, "fa4bm/gVJALILfv1ewsExC", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 92, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -840, [325], 326]], [0, "c4rbALtnFHv6vQ37Jw0SSG", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [59, 1, 157, [327]], [62, 30, 25, false, false, 1, 1, 1, 158, [328]], [48, "27", 36, 25, false, false, 1, 1, 1, 159, [329]], [1, "chart", 92, [[2, "K", 36, false, 1, 1, -841, [330], 331]], [0, "22+kf56L9M847CfNid8LFM", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 160, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -842, [332], 333]], [0, "dayaIHI4NFjreEBnDAm5o4", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 160, [[13, "99K", false, 1, 1, -843, [334], 335]], [0, "8bBAFCmRBKS7Paw/5p99Nu", 1], [5, 135, 50]], [16, "btnIC", 161, [[3, -844, [336], 337]], [0, "36IkX12wNFhJWVaYtFjXyr", 1], [5, 161, 58]], [1, "inactive", 48, [[13, "S<PERSON><PERSON> ra mắt", false, 1, 1, -845, [340], 341]], [0, "adPU9YcVdPRZ8ZOH3u1B3A", 1], [5, 263.75, 50], [0, -97, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "<PERSON><PERSON><PERSON><PERSON><PERSON>", false, 90, [-846], [0, "c155rPvA5PTbCHB3Xiem+h", 1]], [1, "inactive", 351, [[13, "S<PERSON><PERSON> ra mắt", false, 1, 1, -847, [342], 343]], [0, "14DfysKoJNLrBvsmIcmDE+", 1], [5, 263.75, 50], [0, -97, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 94, [[2, "<PERSON><PERSON><PERSON>", 20, false, 1, 1, -848, [344], 345]], [0, "d6KWgJ1PNLLJD4BITcCPVx", 1], [5, 63.13, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 95, [[2, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -849, [349], 350]], [0, "f94P8BxfpJSZYiRxFD+yJT", 1], [5, 72.5, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 8, [[4, "<PERSON><PERSON> toán ", 26, 50, false, 1, -850, [354], 355]], [0, "a0+d70H89FWJxGNWrqf+qg", 1], [5, 153.42, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 8, [[4, "1", 26, 50, false, 1, -851, [356], 357]], [0, "4agOi1+sRNV6xIuHMx03MO", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [153.42, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 8, [[4, " lô. Đặt ", 26, 50, false, 1, -852, [358], 359]], [0, "12aO/6O2FEfYZHR//4+Jw3", 1], [5, 97.63, 63], [0, 0, 0], [168.33999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 8, [[4, "1", 26, 50, false, 1, -853, [360], 361]], [0, "b4+yWH01ZEgog8yGqoGfdC", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [265.96999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 8, [[4, " ăn", 26, 50, false, 1, -854, [362], 363]], [0, "25IDQmvVhM6JOkgArz8D6y", 1], [5, 39.24, 63], [0, 0, 0], [280.89, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 8, [[4, " 9.50", 26, 50, false, 1, -855, [364], 365]], [0, "21se0o8R9BFLRs9PrfUa4x", 1], [4, 4278255615], [5, 60.04, 63], [0, 0, 0], [320.13, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 8, [[4, " ", 26, 50, false, 1, -856, [366], 367]], [0, "88aLtqHvJOZa44g+OplP03", 1], [5, 7.64, 63], [0, 0, 0], [380.17, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 28, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -857, [372], 373]], [0, "b6mi//1G5EMqY2xMPrIps/", 1], [5, 129.38, 37.5], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 98, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -858, [377], 378]], [0, "bfaZOBAF1MtauYctAgUavS", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 164, [[13, "1", false, 1, 1, -859, [379], 380]], [0, "e09/GCo79FCaaTMLlQlcT8", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 98, [[2, "K", 36, false, 1, 1, -860, [383], 384]], [0, "b8qt4KWxJPJZDz3FcBFLjv", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 99, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -861, [385], 386]], [0, "21U13LPadCp77UrqIknOs1", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 165, [[13, "4", false, 1, 1, -862, [387], 388]], [0, "b83FzuN/FOco1AcVuF5G+j", 1], [5, 21.25, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 99, [[2, "K", 36, false, 1, 1, -863, [391], 392]], [0, "bavCNAzhFBpJW1fwuMrg2+", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 166, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -864, [393], 394]], [0, "fcW2kZ0eJIJLYqdk33fhmq", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 166, [[13, "9.50K", false, 1, 1, -865, [395], 396]], [0, "57NGjz/9hPQI6tZ2i/2WvK", 1], [5, 196.25, 50]], [16, "btnIC", 167, [[3, -866, [397], 398]], [0, "1481NDgJdMrLBX5VFCfaup", 1], [5, 161, 58]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 9, [[4, "<PERSON><PERSON> toán ", 26, 50, false, 1, -867, [401], 402]], [0, "1aQSJWzPxNooupkcaeDhCI", 1], [5, 153.42, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 9, [[4, "1", 26, 50, false, 1, -868, [403], 404]], [0, "6buJm47aZFzIse08GvEi9A", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [153.42, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 9, [[4, " lô. Đặt ", 26, 50, false, 1, -869, [405], 406]], [0, "afsg7rwZpNSrRkiO3iF3sV", 1], [5, 97.63, 63], [0, 0, 0], [168.33999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 9, [[4, "1", 26, 50, false, 1, -870, [407], 408]], [0, "4fgo4XOrtG3arCUr8mQLCi", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [265.96999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 9, [[4, " ăn", 26, 50, false, 1, -871, [409], 410]], [0, "5d/RbklndA1YJ55Ql5E2Mp", 1], [5, 39.24, 63], [0, 0, 0], [280.89, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 9, [[4, " 9.50", 26, 50, false, 1, -872, [411], 412]], [0, "edEcCfb2NJF6I3oDiHtt17", 1], [4, 4278255615], [5, 60.04, 63], [0, 0, 0], [320.13, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 9, [[4, " ", 26, 50, false, 1, -873, [413], 414]], [0, "ceEmR43KpJt6RnUiNQwqfA", 1], [5, 7.64, 63], [0, 0, 0], [380.17, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 29, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -874, [419], 420]], [0, "c4AF6BOgxCyLviTjg8BjO6", 1], [5, 129.38, 37.5], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 101, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -875, [424], 425]], [0, "67pybXXWVKzJY9ejsNrAoN", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 170, [[13, "1", false, 1, 1, -876, [426], 427]], [0, "b3JplaK71GZKCcQnNpNb1a", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 101, [[2, "K", 36, false, 1, 1, -877, [430], 431]], [0, "eamSIh1mJBwoOOfUjYdIu8", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 102, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -878, [432], 433]], [0, "b4JhJnTtlKOoPLuNB/wgV9", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 171, [[13, "4", false, 1, 1, -879, [434], 435]], [0, "84tvhZq8lDW5Tbht7ntQDG", 1], [5, 21.25, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 102, [[2, "K", 36, false, 1, 1, -880, [438], 439]], [0, "088wax0fJO544JyrukpBn6", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 172, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -881, [440], 441]], [0, "degjUod3hAwpbpd+7sQXZ1", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 172, [[13, "9.50K", false, 1, 1, -882, [442], 443]], [0, "7cmB7o7WFForxcipHI6vAw", 1], [5, 196.25, 50]], [16, "btnIC", 173, [[3, -883, [444], 445]], [0, "82JtXIM3pPaarJas/5db7L", 1], [5, 161, 58]], [1, "Label", 103, [[2, "Xiên 2", 20, false, 1, 1, -884, [448], 449]], [0, "21fTcmXt5ObbuvnAGa/Ihu", 1], [5, 105.63, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 104, [[2, "Xiên 3", 20, false, 1, 1, -885, [453], 454]], [0, "ddQLPYE9lMcJau3M/H09NP", 1], [5, 106.25, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 105, [[2, "Xiên 4", 20, false, 1, 1, -886, [458], 459]], [0, "c2yr3dRRlAXJDPLPV1u0UR", 1], [5, 106.88, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 10, [[4, "<PERSON><PERSON> toán ", 26, 50, false, 1, -887, [463], 464]], [0, "3fjtyuccxNqpBXrb9OVIAT", 1], [5, 153.42, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 10, [[4, "1", 26, 50, false, 1, -888, [465], 466]], [0, "a72WmwQGdGzrUVXhx7z15I", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [153.42, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 10, [[4, " lô. Đặt ", 26, 50, false, 1, -889, [467], 468]], [0, "dcVMewF0BLxK/vI+pDsTfa", 1], [5, 97.63, 63], [0, 0, 0], [168.33999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 10, [[4, "1", 26, 50, false, 1, -890, [469], 470]], [0, "62phIq/BBCXaXdwfEP0/59", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [265.96999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 10, [[4, " ăn", 26, 50, false, 1, -891, [471], 472]], [0, "c6z/gWpUpKWKez9+qh0fsv", 1], [5, 39.24, 63], [0, 0, 0], [280.89, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 10, [[4, " 17", 26, 50, false, 1, -892, [473], 474]], [0, "77OExdTzFIRIbH0q1miNkw", 1], [4, 4278255615], [5, 37.48, 63], [0, 0, 0], [320.13, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 10, [[4, " ", 26, 50, false, 1, -893, [475], 476]], [0, "88juFKOQ1Ie7ng0z62/Pvs", 1], [5, 7.64, 63], [0, 0, 0], [357.61, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 30, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -894, [481], 482]], [0, "143KMAuDBMMJn/Iv+5m91f", 1], [5, 129.38, 37.5], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 107, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -895, [486], 487]], [0, "2e76sV8+BPnKW3gpS+SsEt", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 176, [[13, "1", false, 1, 1, -896, [488], 489]], [0, "42+IniTuxGLafI28VLyoOm", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 107, [[2, "K", 36, false, 1, 1, -897, [492], 493]], [0, "53VeCAq/xBvacKnjb7tTLS", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 108, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -898, [494], 495]], [0, "328tKGFdJPqobCHTjtmsGF", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 177, [[13, "1", false, 1, 1, -899, [496], 497]], [0, "718XvmFlFEpq5QHjgBMJxe", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 108, [[2, "K", 36, false, 1, 1, -900, [500], 501]], [0, "c85EdFwuBAObYI/bkpqebD", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 178, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -901, [502], 503]], [0, "f4dmu6Mu9PHINm9xWG6m+2", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 178, [[13, "17K", false, 1, 1, -902, [504], 505]], [0, "3cfh3DRJ5Ja43P1Kc8EIis", 1], [5, 120, 50]], [16, "btnIC", 179, [[3, -903, [506], 507]], [0, "7dyA6TxqBFE4+nbcYbLm5V", 1], [5, 161, 58]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 11, [[4, "<PERSON><PERSON> toán ", 26, 50, false, 1, -904, [510], 511]], [0, "c6pqtne0dIepjzAoIEeR3n", 1], [5, 153.42, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 11, [[4, "1", 26, 50, false, 1, -905, [512], 513]], [0, "cf/xsFQ4RJEY9xdGLSEF4Z", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [153.42, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 11, [[4, " lô. Đặt ", 26, 50, false, 1, -906, [514], 515]], [0, "714naTRINLCKOOezpB0DJM", 1], [5, 97.63, 63], [0, 0, 0], [168.33999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 11, [[4, "1", 26, 50, false, 1, -907, [516], 517]], [0, "66ZUFa/0ROs5zoedW4DW2x", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [265.96999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 11, [[4, " ăn", 26, 50, false, 1, -908, [518], 519]], [0, "faVDVUBuZB2qYEeH6s6boM", 1], [5, 39.24, 63], [0, 0, 0], [280.89, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 11, [[4, " 65", 26, 50, false, 1, -909, [520], 521]], [0, "e0laleTk5ALLHUpRg3WYgd", 1], [4, 4278255615], [5, 37.48, 63], [0, 0, 0], [320.13, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 11, [[4, " ", 26, 50, false, 1, -910, [522], 523]], [0, "847ds6Xv1OL74E5yZba07K", 1], [5, 7.64, 63], [0, 0, 0], [357.61, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 31, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -911, [528], 529]], [0, "893vhWh0FPaLc+9S4ZoauH", 1], [5, 129.38, 37.5], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 110, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -912, [533], 534]], [0, "4c9/PwUydH3afmNaoQJonZ", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 182, [[13, "1", false, 1, 1, -913, [535], 536]], [0, "4e4WF8wodB1YPnnm/5ViaH", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 110, [[2, "K", 36, false, 1, 1, -914, [539], 540]], [0, "609hwzEbdJKYZp8bUNW0Oc", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 111, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -915, [541], 542]], [0, "4dPd2KAC1OcLbldPDzvG2D", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 183, [[13, "1", false, 1, 1, -916, [543], 544]], [0, "90bhLtwIJFYY8VDWNQkpFE", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 111, [[2, "K", 36, false, 1, 1, -917, [547], 548]], [0, "97uksRapVMOob753iQq8wY", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 184, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -918, [549], 550]], [0, "7dxyTie1lKY4uIkCjKK++q", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 184, [[13, "65K", false, 1, 1, -919, [551], 552]], [0, "29rjuYg7JMqa21A+CVhT+i", 1], [5, 132.5, 50]], [16, "btnIC", 185, [[3, -920, [553], 554]], [0, "636O2cuX5Kka5sc9ityfK5", 1], [5, 161, 58]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 12, [[4, "<PERSON><PERSON> toán ", 26, 50, false, 1, -921, [557], 558]], [0, "d1dYnfV21ACosfVGZAq6Ud", 1], [5, 153.42, 63], [0, 0, 0], [0, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 12, [[4, "1", 26, 50, false, 1, -922, [559], 560]], [0, "e7rpbzWhFJ/asW8YOggcBc", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [153.42, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 12, [[4, " lô. Đặt ", 26, 50, false, 1, -923, [561], 562]], [0, "a7KVL2D/lNYYsQvS7gclPS", 1], [5, 97.63, 63], [0, 0, 0], [168.33999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 12, [[4, "1", 26, 50, false, 1, -924, [563], 564]], [0, "59HUEI9tVEiroiiaZQ2czA", 1], [4, 4278255615], [5, 14.92, 63], [0, 0, 0], [265.96999999999997, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 12, [[4, " ăn", 26, 50, false, 1, -925, [565], 566]], [0, "cemJufHsxKhI3M/B6hpQ0m", 1], [5, 39.24, 63], [0, 0, 0], [280.89, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "RICHTEXT_CHILD", 1024, false, -32768, 12, [[4, " 250", 26, 50, false, 1, -926, [567], 568]], [0, "ade2DaNFVO14DArTfs7saz", 1], [4, 4278255615], [5, 52.39, 63], [0, 0, 0], [320.13, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "RICHTEXT_CHILD", 1024, false, -32768, 12, [[4, " ", 26, 50, false, 1, -927, [569], 570]], [0, "35xsWNTM5A07xoYiUPYe1h", 1], [5, 7.64, 63], [0, 0, 0], [372.52, -31.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 32, [[2, "GHI SỐ ĐỀ", 30, false, 1, 1, -928, [575], 576]], [0, "2cqdrnBuJA7LQ7/q8xEGlp", 1], [5, 129.38, 37.5], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 113, [[2, "<PERSON><PERSON><PERSON><PERSON> c<PERSON> / 1 Con", 30, false, 1, 1, -929, [580], 581]], [0, "73vgFuke5CxKMWZaKN+/sd", 1], [4, 4278237439], [5, 219.38, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 188, [[13, "1", false, 1, 1, -930, [582], 583]], [0, "62a8Iu5DZDeaIg04Sb4rBv", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 113, [[2, "K", 36, false, 1, 1, -931, [586], 587]], [0, "7d+LLwCElIALcsI+AvXh+r", 1], [5, 21.38, 45], [134, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "text", 114, [[2, "<PERSON><PERSON><PERSON> tiền c<PERSON>", 30, false, 1, 1, -932, [588], 589]], [0, "22rLgUzEhEyLffvo+4DViJ", 1], [4, 4278237439], [5, 182.81, 37.5], [0, 117, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 189, [[13, "1", false, 1, 1, -933, [590], 591]], [0, "79cQYdVr5OQrANrGLhILQM", 1], [5, 20, 50], [0, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chart", 114, [[2, "K", 36, false, 1, 1, -934, [594], 595]], [0, "50tON14eNEsYLWmIubRqCy", 1], [5, 21.38, 45], [134, 57, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "text", 190, [[2, "<PERSON><PERSON><PERSON><PERSON> thắng / 1 con", 30, false, 1, 1, -935, [596], 597]], [0, "a5qtpVidVNzqN38JsOcOq+", 1], [4, 4278237439], [5, 229.69, 37.5]], [16, "win", 190, [[13, "250K", false, 1, 1, -936, [598], 599]], [0, "94hh3fwG5Dv6P0nOISGCkO", 1], [5, 172.5, 50]], [16, "btnIC", 191, [[3, -937, [600], 601]], [0, "a6hWzJF7xEmqL9nGG7de8j", 1], [5, 161, 58]], [74, "<PERSON><PERSON><PERSON>", false, 23, [-938], [0, "c16KF+mIdD7bCCuNzDRNmO", 1], [5, 195.64, 50.4]], [16, "Label", 443, [[13, "S<PERSON><PERSON> ra mắt", false, 1, 1, -939, [604], 605]], [0, "d5riwLpp1HF7D/MNoQTDCg", 1], [5, 370, 50]], [1, "Label", 192, [[2, "Đã đặt", 20, false, 1, 1, -940, [608], 609]], [0, "00yAQCQlNPobt8YlEBWHak", 1], [5, 112.5, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 193, [[2, "Đã đặt", 20, false, 1, 1, -941, [612], 613]], [0, "34+BoTUoNMhLcrBvS0dPiz", 1], [5, 112.5, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 194, [[2, "<PERSON><PERSON> đặt", 20, false, 1, 1, -942, [616], 617]], [0, "0fNrwWLDxEmqS8NhPn465N", 1], [5, 153.13, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 195, [[2, "<PERSON><PERSON> đặt", 20, false, 1, 1, -943, [620], 621]], [0, "7a4lYmVddCDrGK+XFWbyZR", 1], [5, 153.13, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 196, [[2, "<PERSON><PERSON><PERSON> qu<PERSON>", 20, false, 1, 1, -944, [624], 625]], [0, "ebG7OUsidBErDSthbSJv/o", 1], [5, 129.38, 25], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 197, [[2, "<PERSON><PERSON><PERSON> qu<PERSON>", 20, false, 1, 1, -945, [628], 629]], [0, "81O1KKYs1MN417DfpmcvIA", 1], [5, 129.38, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgcontent", 2, [[3, -946, [636], 637]], [0, "0eNU2i8RhAp7cuNw7dzHsz", 1], [5, 365, 442], [0, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "gia<PERSON><PERSON>", 13, [[2, "Giải ĐB", 20, false, 1, 1, -947, [638], 639]], [0, "7dr806jAtGMrkHwUCytnxp", 1], [4, 4278237439], [5, 60, 40], [-137, 105, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "giai1", 13, [[2, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -948, [640], 641]], [0, "87G19cJVlD6ranRECImkEv", 1], [4, 4278237439], [5, 74.38, 40], [-138, 61, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "giai2", 13, [[2, "Giải Nhì", 20, false, 1, 1, -949, [642], 643]], [0, "6dUzz8iUBPd7M9gLckXdWS", 1], [4, 4278237439], [5, 62.5, 40], [-136.108, 17.986, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "giai3", 13, [[2, "Giải Ba", 20, false, 1, 1, -950, [644], 645]], [0, "92Z4z3OLZIVp06fh6xYCHO", 1], [4, 4278237439], [5, 58.75, 40], [-133.741, -48.501999999999995, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "giai4", 13, [[2, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -951, [646], 647]], [0, "efu7JfVWdNMJg2SP3x+4sm", 1], [4, 4278237439], [5, 58.75, 40], [-136.374, -117.048, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "giai5", 13, [[2, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -952, [648], 649]], [0, "44yk3sn2VOIqgnlpBveTln", 1], [4, 4278237439], [5, 71.88, 40], [-133.374, -178.069, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "giai6", 13, [[2, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -953, [650], 651]], [0, "09+filsVlJmIk0bXlM23OO", 1], [4, 4278237439], [5, 65.63, 40], [-136.158, -245.09, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "giai7", 13, [[2, "<PERSON><PERSON><PERSON><PERSON>", 20, false, 1, 1, -954, [652], 653]], [0, "adZvj1Z7hJOLfSxOYDV+Fn", 1], [4, 4278237439], [5, 66.88, 40], [-134.525, -302.636, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "lbDB", 2, [-955], [0, "ed58wXHxJHdbliKzEglHOJ", 1], [4, 4278190335], [5, 63.8, 50], [35, 102.9, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "60347", 22, 50, false, 1, 1, 460, [654]], [11, "lb1", 2, [-956], [0, "51+JH7Um1CcreaSEMAn1XO", 1], [5, 58, 50], [34, 58.4, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "72943", 20, 50, false, 1, 1, 462, [655]], [11, "lb2-1", 202, [-957], [0, "61uoshB7NJ5qMrmkYFBnTs", 1], [5, 52.2, 50], [-19, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "52500", 18, 50, false, 1, 1, 464, [656]], [11, "lb2-2", 202, [-958], [0, "e8urjRtO1Jg527/zjWBfs4", 1], [5, 52.2, 50], [81, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "63708", 18, 50, false, 1, 1, 466, [657]], [11, "lb3-1", 34, [-959], [0, "06GNyxb5NFjbont9jBO0ht", 1], [5, 49.5, 50], [-53, -29.7, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "29081", 18, 50, false, 1, 1, 468, [658]], [11, "lb3-2", 34, [-960], [0, "b67ryahp9JOL5cg0+qFqut", 1], [5, 52.2, 50], [42, -29.7, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "28839", 18, 50, false, 1, 1, 470, [659]], [11, "lb3-3", 34, [-961], [0, "9abN5rOVlOb7XSz2i9I0x8", 1], [5, 49.5, 50], [125, -29.7, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "14561", 18, 50, false, 1, 1, 472, [660]], [11, "lb3-4", 34, [-962], [0, "afl2bXBHZOtZPHLjktb5yr", 1], [5, 52.2, 50], [-53, -71.8, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "09823", 18, 50, false, 1, 1, 474, [661]], [11, "lb3-5", 34, [-963], [0, "01KLRWXcJLk6XU5rknuYys", 1], [5, 53.1, 50], [42, -71.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "02094", 18, 50, false, 1, 1, 476, [662]], [11, "lb3-6", 34, [-964], [0, "ebG59BgyZMz61a34DZLXbr", 1], [5, 49.5, 50], [125, -72.3, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "44851", 18, 50, false, 1, 1, 478, [663]], [11, "1", 55, [-965], [0, "527OKfy7RJ5L09y+ATHeTD", 1], [5, 41.85, 50], [-59, -121.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "7678", 18, 50, false, 1, 1, 480, [664]], [11, "2", 55, [-966], [0, "6a7D504+dC8Zp8rPBjMqzS", 1], [5, 42.3, 50], [3, -121.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "5015", 18, 50, false, 1, 1, 482, [665]], [11, "3", 55, [-967], [0, "c6Tou90ytNO6oMa1VejPFM", 1], [5, 41.85, 50], [65, -121.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "8660", 18, 50, false, 1, 1, 484, [666]], [11, "4", 55, [-968], [0, "57j5J/D1FJ867V1CCGLm8k", 1], [5, 41.85, 50], [125, -121.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "1068", 18, 50, false, 1, 1, 486, [667]], [11, "1", 35, [-969], [0, "37di3ckrRLkIYXpR2HmpkA", 1], [5, 39.15, 50], [-53, -168, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "0871", 18, 50, false, 1, 1, 488, [668]], [11, "2", 35, [-970], [0, "327akkrkhMzIUi+Q4NaAMY", 1], [5, 41.85, 50], [43, -168, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "8747", 18, 50, false, 1, 1, 490, [669]], [11, "3", 35, [-971], [0, "0dkuYQw41I6a+NiPzZ5fZK", 1], [5, 41.85, 50], [125, -168, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "8892", 18, 50, false, 1, 1, 492, [670]], [11, "4", 35, [-972], [0, "8c24c3f8xPMLGJ/hWXVsgU", 1], [5, 41.85, 50], [-51, -201, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "8217", 18, 50, false, 1, 1, 494, [671]], [11, "5", 35, [-973], [0, "e1PhXvUkFJNZNIbeiw5vOn", 1], [5, 41.85, 50], [42, -200.8, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "4766", 18, 50, false, 1, 1, 496, [672]], [11, "6", 35, [-974], [0, "8dfer5+mhI8or5McskhQmU", 1], [5, 41.85, 50], [125, -201, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "6409", 18, 50, false, 1, 1, 498, [673]], [11, "1", 120, [-975], [0, "dfYlNcDxZAGJkyRfjd60Nn", 1], [5, 31.5, 50], [-50, -250.3, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "373", 18, 50, false, 1, 1, 500, [674]], [11, "2", 120, [-976], [0, "1dbm+3fz9Dlp3JK6nzp9lI", 1], [5, 31.5, 50], [42, -250.3, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "543", 18, 50, false, 1, 1, 502, [675]], [11, "3", 120, [-977], [0, "999l97DIFBK5S46xQ0gwWB", 1], [5, 31.5, 50], [125, -250.3, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "017", 18, 50, false, 1, 1, 504, [676]], [22, "1", 56, [-978], [0, "91bC8HDVVGkJ2m0NgSteR0", 1], [4, 4278190335], [5, 22.05, 50], [-51, -302.636, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "04", 18, 50, false, 1, 1, 506, [677]], [22, "2", 56, [-979], [0, "64w3xZ1CVFuJN7yQnVwlDa", 1], [4, 4278190335], [5, 21.15, 50], [0, -302.636, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "78", 18, 50, false, 1, 1, 508, [678]], [22, "3", 56, [-980], [0, "aaQK27KrFLI4+tqMGA/NrE", 1], [4, 4278190335], [5, 21.15, 50], [50, -302.636, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "48", 18, 50, false, 1, 1, 510, [679]], [22, "4", 56, [-981], [0, "b9a2Yt6YxLPYjRI0Ytsq7L", 1], [4, 4278190335], [5, 21.15, 50], [125, -302.636, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "87", 18, 50, false, 1, 1, 512, [680]], [1, "bg", 57, [[3, -982, [681], 682]], [0, "f9n4/+qqJKAoU0hB9qDTnO", 1], [5, 476, 58], [-36, 155, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbTitleResultDate", 57, [-983], [0, "9bIpEHnrxIeZ6+9Gg5bnRE", 1], [5, 63.8, 50], [10, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "00000", 22, 50, false, 1, 1, 515, [683]], [1, "btn_back", 121, [[3, -984, [684], 685]], [0, "43Ds9fCTlJgoZPOh7JyDm2", 1], [5, 30, 56], [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [23, 1.1, 3, 121, [[8, "8c4f9H6G1ZA+6Ngtqd3Seko", "getNextResult", 1]], [4, 4292269782], 121], [16, "btn_back", 122, [[3, -985, [686], 687]], [0, "c9DStrWzFEybOeAfAUJiES", 1], [5, 30, 56]], [23, 1.1, 3, 122, [[8, "8c4f9H6G1ZA+6Ngtqd3Seko", "getPreviousResult", 1]], [4, 4292269782], 122], [38, "Label copy", false, 59, [[32, false, "<PERSON><PERSON>", 20, 50, false, false, 1, 1, 1, -986, [692], 693]], [0, "d3QKBNDXJBf6LWoRI4/nww", 1], [4, 4280953386], [5, 100, 40], [-1, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "Label", false, 59, [[32, false, "<PERSON><PERSON>", 20, 50, false, false, 1, 1, 1, -987, [694], 695]], [0, "69NCesmh9NsaatC3D9Vqop", 1], [5, 100, 40]], [38, "Label", false, 60, [[32, false, "<PERSON><PERSON><PERSON>", 20, 50, false, false, 1, 1, 1, -988, [699], 700]], [0, "d55y1JIFhKAb/JZJg4M38E", 1], [4, 4280953386], [5, 100, 40], [-1, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "Label", false, 60, [[32, false, "<PERSON><PERSON><PERSON>", 20, 50, false, false, 1, 1, 1, -989, [701], 702]], [0, "05WHLNLd5JRYHFxjhnJTSl", 1], [5, 100, 40]], [38, "Label", false, 61, [[32, false, "<PERSON><PERSON><PERSON>", 20, 50, false, false, 1, 1, 1, -990, [706], 707]], [0, "dfDdPP725KzYrxRLDLLsru", 1], [4, 4280953386], [5, 100, 40], [-1, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "Label", false, 61, [[32, false, "<PERSON><PERSON><PERSON>", 20, 50, false, false, 1, 1, 1, -991, [708], 709]], [0, "ebGtff6chBT6jDkgpNAc0I", 1], [5, 100, 40]], [16, "icon", 207, [[21, 0, -992, [713], 714]], [0, "4fbYdqht1EQbCGO7IMbRC1", 1], [5, 77, 74]]], 0, [0, 15, 1, 0, 0, 1, 0, 16, 123, 0, 17, 58, 0, 18, 260, 0, 19, 262, 0, 20, 254, 0, 21, 256, 0, 22, 248, 0, 23, 250, 0, 24, 230, 0, 25, 232, 0, 26, 242, 0, 27, 244, 0, 28, 236, 0, 29, 238, 0, 30, 224, 0, 31, 226, 0, 32, 208, 0, 8, 23, 0, 9, 22, 0, 0, 1, 0, 33, 212, 0, 34, 518, 0, 35, 520, 0, -1, 507, 0, -2, 509, 0, -3, 511, 0, -4, 513, 0, -1, 501, 0, -2, 503, 0, -3, 505, 0, -1, 489, 0, -2, 491, 0, -3, 493, 0, -4, 495, 0, -5, 497, 0, -6, 499, 0, -1, 481, 0, -2, 483, 0, -3, 485, 0, -4, 487, 0, -1, 469, 0, -2, 471, 0, -3, 473, 0, -4, 475, 0, -5, 477, 0, -6, 479, 0, -1, 465, 0, -2, 467, 0, 36, 463, 0, 37, 461, 0, 38, 214, 0, 39, 216, 0, 40, 516, 0, 0, 1, 0, 41, 116, 0, 42, 118, 0, 43, 119, 0, 44, 198, 0, 45, 200, 0, 46, 44, 0, 47, 43, 0, 48, 42, 0, 0, 1, 0, 0, 1, 0, -1, 124, 0, -2, 46, 0, -3, 62, 0, -4, 203, 0, -1, 451, 0, -2, 13, 0, -3, 460, 0, -4, 462, 0, -5, 202, 0, -6, 34, 0, -7, 55, 0, -8, 35, 0, -9, 120, 0, -10, 56, 0, 0, 3, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -4, 18, 0, -5, 19, 0, -6, 20, 0, -7, 21, 0, 0, 4, 0, -1, 273, 0, -2, 274, 0, -3, 275, 0, -4, 276, 0, -5, 277, 0, -6, 278, 0, -7, 279, 0, 0, 5, 0, -1, 293, 0, -2, 294, 0, -3, 295, 0, -4, 296, 0, -5, 297, 0, -6, 298, 0, -7, 299, 0, 0, 6, 0, -1, 309, 0, -2, 310, 0, -3, 311, 0, -4, 312, 0, -5, 313, 0, -6, 314, 0, -7, 315, 0, 0, 7, 0, -1, 327, 0, -2, 328, 0, -3, 329, 0, -4, 330, 0, -5, 331, 0, -6, 332, 0, -7, 333, 0, 0, 8, 0, -1, 355, 0, -2, 356, 0, -3, 357, 0, -4, 358, 0, -5, 359, 0, -6, 360, 0, -7, 361, 0, 0, 9, 0, -1, 372, 0, -2, 373, 0, -3, 374, 0, -4, 375, 0, -5, 376, 0, -6, 377, 0, -7, 378, 0, 0, 10, 0, -1, 392, 0, -2, 393, 0, -3, 394, 0, -4, 395, 0, -5, 396, 0, -6, 397, 0, -7, 398, 0, 0, 11, 0, -1, 409, 0, -2, 410, 0, -3, 411, 0, -4, 412, 0, -5, 413, 0, -6, 414, 0, -7, 415, 0, 0, 12, 0, -1, 426, 0, -2, 427, 0, -3, 428, 0, -4, 429, 0, -5, 430, 0, -6, 431, 0, -7, 432, 0, -1, 452, 0, -2, 453, 0, -3, 454, 0, -4, 455, 0, -5, 456, 0, -6, 457, 0, -7, 458, 0, -8, 459, 0, 0, 14, 0, -1, 220, 0, -3, 22, 0, -4, 269, 0, -5, 270, 0, -6, 23, 0, 0, 15, 0, 6, 15, 0, 0, 15, 0, -1, 221, 0, -2, 222, 0, -3, 223, 0, -4, 225, 0, 0, 16, 0, 6, 16, 0, 0, 16, 0, -1, 227, 0, -2, 228, 0, -3, 229, 0, -4, 231, 0, 0, 17, 0, 6, 17, 0, 0, 17, 0, -1, 233, 0, -2, 234, 0, -3, 235, 0, -4, 237, 0, 0, 18, 0, 6, 18, 0, 0, 18, 0, -1, 239, 0, -2, 240, 0, -3, 241, 0, -4, 243, 0, 0, 19, 0, 6, 19, 0, 0, 19, 0, -1, 245, 0, -2, 246, 0, -3, 247, 0, -4, 249, 0, 0, 20, 0, 6, 20, 0, 0, 20, 0, -1, 251, 0, -2, 252, 0, -3, 253, 0, -4, 255, 0, 0, 21, 0, 6, 21, 0, 0, 21, 0, -1, 257, 0, -2, 258, 0, -3, 259, 0, -4, 261, 0, -1, 64, 0, -2, 65, 0, -3, 66, 0, -4, 67, 0, -5, 68, 0, -6, 69, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 40, 0, -5, 33, 0, -6, 443, 0, 0, 24, 0, -1, 280, 0, -2, 129, 0, -3, 75, 0, -4, 76, 0, -5, 132, 0, -6, 133, 0, 0, 25, 0, -1, 300, 0, -2, 136, 0, -3, 82, 0, -4, 83, 0, -5, 139, 0, -6, 140, 0, 0, 26, 0, -1, 316, 0, -2, 143, 0, -3, 85, 0, -4, 86, 0, -5, 146, 0, -6, 147, 0, 0, 27, 0, -1, 49, 0, -2, 153, 0, -3, 91, 0, -4, 92, 0, -5, 160, 0, -6, 161, 0, 0, 28, 0, -1, 362, 0, -2, 163, 0, -3, 98, 0, -4, 99, 0, -5, 166, 0, -6, 167, 0, 0, 29, 0, -1, 379, 0, -2, 169, 0, -3, 101, 0, -4, 102, 0, -5, 172, 0, -6, 173, 0, 0, 30, 0, -1, 399, 0, -2, 175, 0, -3, 107, 0, -4, 108, 0, -5, 178, 0, -6, 179, 0, 0, 31, 0, -1, 416, 0, -2, 181, 0, -3, 110, 0, -4, 111, 0, -5, 184, 0, -6, 185, 0, 0, 32, 0, -1, 433, 0, -2, 187, 0, -3, 113, 0, -4, 114, 0, -5, 190, 0, -6, 191, 0, 8, 53, 0, 9, 52, 0, 0, 33, 0, -1, 52, 0, -2, 53, 0, -1, 468, 0, -2, 470, 0, -3, 472, 0, -4, 474, 0, -5, 476, 0, -6, 478, 0, -1, 488, 0, -2, 490, 0, -3, 492, 0, -4, 494, 0, -5, 496, 0, -6, 498, 0, 0, 36, 0, -1, 215, 0, -2, 217, 0, -3, 218, 0, -4, 219, 0, 8, 73, 0, 9, 70, 0, 0, 37, 0, -1, 70, 0, -2, 73, 0, 8, 80, 0, 9, 77, 0, 0, 38, 0, -1, 77, 0, -2, 80, 0, 8, 90, 0, 9, 87, 0, 0, 39, 0, -1, 87, 0, -2, 90, 0, 8, 96, 0, 9, 93, 0, 0, 40, 0, -1, 93, 0, -2, 96, 0, 0, 41, 0, -1, 54, 0, -2, 198, 0, -3, 200, 0, -4, 119, 0, 6, 42, 0, 0, 42, 0, -1, 192, 0, -2, 193, 0, 6, 43, 0, 0, 43, 0, -1, 194, 0, -2, 195, 0, 6, 44, 0, 0, 44, 0, -1, 196, 0, -2, 197, 0, 0, 45, 0, -1, 204, 0, -2, 205, 0, -3, 206, 0, -4, 207, 0, 0, 46, 0, 0, 46, 0, -1, 47, 0, -2, 210, 0, 0, 47, 0, 0, 47, 0, -1, 125, 0, -2, 209, 0, -2, 149, 0, -4, 350, 0, 10, 334, 0, 11, 336, 0, 12, 335, 0, 0, 49, 0, -1, 150, 0, -2, 151, 0, -3, 152, 0, 10, 338, 0, 11, 340, 0, 12, 339, 0, 0, 50, 0, -1, 154, 0, -2, 155, 0, -3, 156, 0, 10, 343, 0, 11, 345, 0, 12, 344, 0, 0, 51, 0, -1, 157, 0, -2, 158, 0, -3, 159, 0, -1, 103, 0, -2, 104, 0, -3, 105, 0, -1, 106, 0, -2, 109, 0, -3, 112, 0, 0, 54, 0, -1, 480, 0, -2, 482, 0, -3, 484, 0, -4, 486, 0, -1, 506, 0, -2, 508, 0, -3, 510, 0, -4, 512, 0, -1, 514, 0, -2, 515, 0, -3, 121, 0, -4, 122, 0, 0, 58, 0, -1, 123, 0, 0, 59, 0, -1, 521, 0, -2, 522, 0, 0, 60, 0, -1, 523, 0, -2, 524, 0, 0, 61, 0, -1, 525, 0, -2, 526, 0, -1, 63, 0, 0, 63, 0, -1, 127, 0, 0, 64, 0, 0, 64, 0, -1, 263, 0, 0, 65, 0, 0, 65, 0, -1, 264, 0, 0, 66, 0, 0, 66, 0, -1, 265, 0, 0, 67, 0, 0, 67, 0, -1, 266, 0, 0, 68, 0, 0, 68, 0, -1, 267, 0, 0, 69, 0, 0, 69, 0, -1, 268, 0, -1, 71, 0, -2, 72, 0, 0, 71, 0, 0, 71, 0, -1, 271, 0, 0, 72, 0, 0, 72, 0, -1, 272, 0, -1, 74, 0, -2, 289, 0, -2, 128, 0, -1, 281, 0, -2, 130, 0, -3, 283, 0, -1, 284, 0, -2, 131, 0, -3, 286, 0, -1, 78, 0, -2, 79, 0, 0, 78, 0, 0, 78, 0, -1, 291, 0, 0, 79, 0, 0, 79, 0, -1, 292, 0, -1, 81, 0, -2, 84, 0, -2, 135, 0, -1, 301, 0, -2, 137, 0, -3, 303, 0, -1, 304, 0, -2, 138, 0, -3, 306, 0, -2, 142, 0, -1, 317, 0, -2, 144, 0, -3, 319, 0, -1, 320, 0, -2, 145, 0, -3, 322, 0, -1, 88, 0, -2, 89, 0, 0, 88, 0, 0, 88, 0, -1, 325, 0, 0, 89, 0, 0, 89, 0, -1, 326, 0, -2, 351, 0, -1, 337, 0, -3, 341, 0, -1, 342, 0, -3, 346, 0, -1, 94, 0, -2, 95, 0, 0, 94, 0, 0, 94, 0, -1, 353, 0, 0, 95, 0, 0, 95, 0, -1, 354, 0, -1, 97, 0, -2, 100, 0, -2, 162, 0, -1, 363, 0, -2, 164, 0, -3, 365, 0, -1, 366, 0, -2, 165, 0, -3, 368, 0, -2, 168, 0, -1, 380, 0, -2, 170, 0, -3, 382, 0, -1, 383, 0, -2, 171, 0, -3, 385, 0, 0, 103, 0, 0, 103, 0, -1, 389, 0, 0, 104, 0, 0, 104, 0, -1, 390, 0, 0, 105, 0, 0, 105, 0, -1, 391, 0, -2, 174, 0, -1, 400, 0, -2, 176, 0, -3, 402, 0, -1, 403, 0, -2, 177, 0, -3, 405, 0, -2, 180, 0, -1, 417, 0, -2, 182, 0, -3, 419, 0, -1, 420, 0, -2, 183, 0, -3, 422, 0, -2, 186, 0, -1, 434, 0, -2, 188, 0, -3, 436, 0, -1, 437, 0, -2, 189, 0, -3, 439, 0, 0, 115, 0, 14, 116, 0, 0, 115, 0, -1, 199, 0, 0, 116, 0, 0, 117, 0, 14, 118, 0, 0, 117, 0, -1, 201, 0, 0, 118, 0, -1, 500, 0, -2, 502, 0, -3, 504, 0, -1, 518, 0, -1, 517, 0, -1, 520, 0, -1, 519, 0, 0, 123, 0, 0, 123, 0, 0, 124, 0, 0, 124, 0, 0, 125, 0, -1, 126, 0, 0, 126, 0, -2, 208, 0, -1, 211, 0, -2, 213, 0, 0, 128, 0, 0, 128, 0, 0, 129, 0, 0, 129, 0, 0, 130, 0, -1, 282, 0, 0, 131, 0, -1, 285, 0, -1, 287, 0, -2, 288, 0, 0, 133, 0, -1, 134, 0, 0, 134, 0, 0, 134, 0, 0, 135, 0, 0, 135, 0, 0, 136, 0, 0, 136, 0, 0, 137, 0, -1, 302, 0, 0, 138, 0, -1, 305, 0, -1, 307, 0, -2, 308, 0, 0, 140, 0, -1, 141, 0, 0, 141, 0, 0, 141, 0, 0, 142, 0, 0, 142, 0, 0, 143, 0, 0, 143, 0, 0, 144, 0, -1, 318, 0, 0, 145, 0, -1, 321, 0, -1, 323, 0, -2, 324, 0, 0, 147, 0, -1, 148, 0, 0, 148, 0, 0, 148, 0, 0, 149, 0, 0, 149, 0, -1, 334, 0, 0, 150, 0, -1, 335, 0, 0, 151, 0, -1, 336, 0, 0, 152, 0, 0, 153, 0, 0, 153, 0, -1, 338, 0, 0, 154, 0, -1, 339, 0, 0, 155, 0, -1, 340, 0, 0, 156, 0, -1, 343, 0, 0, 157, 0, -1, 344, 0, 0, 158, 0, -1, 345, 0, 0, 159, 0, -1, 347, 0, -2, 348, 0, 0, 161, 0, -1, 349, 0, 0, 162, 0, 0, 162, 0, 0, 163, 0, 0, 163, 0, 0, 164, 0, -1, 364, 0, 0, 165, 0, -1, 367, 0, -1, 369, 0, -2, 370, 0, 0, 167, 0, -1, 371, 0, 0, 168, 0, 0, 168, 0, 0, 169, 0, 0, 169, 0, 0, 170, 0, -1, 381, 0, 0, 171, 0, -1, 384, 0, -1, 386, 0, -2, 387, 0, 0, 173, 0, -1, 388, 0, 0, 174, 0, 0, 174, 0, 0, 175, 0, 0, 175, 0, 0, 176, 0, -1, 401, 0, 0, 177, 0, -1, 404, 0, -1, 406, 0, -2, 407, 0, 0, 179, 0, -1, 408, 0, 0, 180, 0, 0, 180, 0, 0, 181, 0, 0, 181, 0, 0, 182, 0, -1, 418, 0, 0, 183, 0, -1, 421, 0, -1, 423, 0, -2, 424, 0, 0, 185, 0, -1, 425, 0, 0, 186, 0, 0, 186, 0, 0, 187, 0, 0, 187, 0, 0, 188, 0, -1, 435, 0, 0, 189, 0, -1, 438, 0, -1, 440, 0, -2, 441, 0, 0, 191, 0, -1, 442, 0, 0, 192, 0, -1, 445, 0, 0, 193, 0, -1, 446, 0, 0, 194, 0, -1, 447, 0, 0, 195, 0, -1, 448, 0, 0, 196, 0, -1, 449, 0, 0, 197, 0, -1, 450, 0, 0, 199, 0, 0, 201, 0, -1, 464, 0, -2, 466, 0, 0, 203, 0, 0, 204, 0, 0, 205, 0, 0, 206, 0, 0, 207, 0, -1, 527, 0, 0, 209, 0, 0, 210, 0, -1, 212, 0, -1, 214, 0, -1, 216, 0, 0, 217, 0, 0, 218, 0, 0, 219, 0, 0, 220, 0, 0, 221, 0, 0, 222, 0, -1, 224, 0, -1, 226, 0, 0, 227, 0, 0, 228, 0, -1, 230, 0, -1, 232, 0, 0, 233, 0, 0, 234, 0, -1, 236, 0, -1, 238, 0, 0, 239, 0, 0, 240, 0, -1, 242, 0, -1, 244, 0, 0, 245, 0, 0, 246, 0, -1, 248, 0, -1, 250, 0, 0, 251, 0, 0, 252, 0, -1, 254, 0, -1, 256, 0, 0, 257, 0, 0, 258, 0, -1, 260, 0, -1, 262, 0, 0, 263, 0, 0, 264, 0, 0, 265, 0, 0, 266, 0, 0, 267, 0, 0, 268, 0, 0, 269, 0, 0, 270, 0, 0, 271, 0, 0, 272, 0, 0, 273, 0, 0, 274, 0, 0, 275, 0, 0, 276, 0, 0, 277, 0, 0, 278, 0, 0, 279, 0, 0, 280, 0, 0, 281, 0, 0, 282, 0, 0, 283, 0, 0, 284, 0, 0, 285, 0, 0, 286, 0, 0, 287, 0, 0, 288, 0, -1, 290, 0, 0, 290, 0, 0, 291, 0, 0, 292, 0, 0, 293, 0, 0, 294, 0, 0, 295, 0, 0, 296, 0, 0, 297, 0, 0, 298, 0, 0, 299, 0, 0, 300, 0, 0, 301, 0, 0, 302, 0, 0, 303, 0, 0, 304, 0, 0, 305, 0, 0, 306, 0, 0, 307, 0, 0, 308, 0, 0, 309, 0, 0, 310, 0, 0, 311, 0, 0, 312, 0, 0, 313, 0, 0, 314, 0, 0, 315, 0, 0, 316, 0, 0, 317, 0, 0, 318, 0, 0, 319, 0, 0, 320, 0, 0, 321, 0, 0, 322, 0, 0, 323, 0, 0, 324, 0, 0, 325, 0, 0, 326, 0, 0, 327, 0, 0, 328, 0, 0, 329, 0, 0, 330, 0, 0, 331, 0, 0, 332, 0, 0, 333, 0, 0, 337, 0, 0, 341, 0, 0, 342, 0, 0, 346, 0, 0, 347, 0, 0, 348, 0, 0, 349, 0, 0, 350, 0, -1, 352, 0, 0, 352, 0, 0, 353, 0, 0, 354, 0, 0, 355, 0, 0, 356, 0, 0, 357, 0, 0, 358, 0, 0, 359, 0, 0, 360, 0, 0, 361, 0, 0, 362, 0, 0, 363, 0, 0, 364, 0, 0, 365, 0, 0, 366, 0, 0, 367, 0, 0, 368, 0, 0, 369, 0, 0, 370, 0, 0, 371, 0, 0, 372, 0, 0, 373, 0, 0, 374, 0, 0, 375, 0, 0, 376, 0, 0, 377, 0, 0, 378, 0, 0, 379, 0, 0, 380, 0, 0, 381, 0, 0, 382, 0, 0, 383, 0, 0, 384, 0, 0, 385, 0, 0, 386, 0, 0, 387, 0, 0, 388, 0, 0, 389, 0, 0, 390, 0, 0, 391, 0, 0, 392, 0, 0, 393, 0, 0, 394, 0, 0, 395, 0, 0, 396, 0, 0, 397, 0, 0, 398, 0, 0, 399, 0, 0, 400, 0, 0, 401, 0, 0, 402, 0, 0, 403, 0, 0, 404, 0, 0, 405, 0, 0, 406, 0, 0, 407, 0, 0, 408, 0, 0, 409, 0, 0, 410, 0, 0, 411, 0, 0, 412, 0, 0, 413, 0, 0, 414, 0, 0, 415, 0, 0, 416, 0, 0, 417, 0, 0, 418, 0, 0, 419, 0, 0, 420, 0, 0, 421, 0, 0, 422, 0, 0, 423, 0, 0, 424, 0, 0, 425, 0, 0, 426, 0, 0, 427, 0, 0, 428, 0, 0, 429, 0, 0, 430, 0, 0, 431, 0, 0, 432, 0, 0, 433, 0, 0, 434, 0, 0, 435, 0, 0, 436, 0, 0, 437, 0, 0, 438, 0, 0, 439, 0, 0, 440, 0, 0, 441, 0, 0, 442, 0, -1, 444, 0, 0, 444, 0, 0, 445, 0, 0, 446, 0, 0, 447, 0, 0, 448, 0, 0, 449, 0, 0, 450, 0, 0, 451, 0, 0, 452, 0, 0, 453, 0, 0, 454, 0, 0, 455, 0, 0, 456, 0, 0, 457, 0, 0, 458, 0, 0, 459, 0, -1, 461, 0, -1, 463, 0, -1, 465, 0, -1, 467, 0, -1, 469, 0, -1, 471, 0, -1, 473, 0, -1, 475, 0, -1, 477, 0, -1, 479, 0, -1, 481, 0, -1, 483, 0, -1, 485, 0, -1, 487, 0, -1, 489, 0, -1, 491, 0, -1, 493, 0, -1, 495, 0, -1, 497, 0, -1, 499, 0, -1, 501, 0, -1, 503, 0, -1, 505, 0, -1, 507, 0, -1, 509, 0, -1, 511, 0, -1, 513, 0, 0, 514, 0, -1, 516, 0, 0, 517, 0, 0, 519, 0, 0, 521, 0, 0, 522, 0, 0, 523, 0, 0, 524, 0, 0, 525, 0, 0, 526, 0, 0, 527, 0, 49, 1, 2, 3, 119, 3, 3, 14, 4, 3, 74, 5, 3, 81, 6, 3, 84, 7, 3, 48, 8, 3, 97, 9, 3, 100, 10, 3, 106, 11, 3, 109, 12, 3, 112, 14, 3, 62, 24, 3, 74, 25, 3, 81, 26, 3, 84, 27, 3, 48, 28, 3, 97, 29, 3, 100, 30, 3, 106, 31, 3, 109, 32, 3, 112, 36, 3, 63, 41, 3, 62, 42, 3, 54, 43, 3, 54, 44, 3, 54, 45, 3, 58, 48, 3, 90, 50, 3, 91, 51, 3, 92, 57, 3, 119, 58, 3, 203, 59, 3, 204, 60, 3, 205, 61, 3, 206, 115, 3, 198, 116, 3, 199, 117, 3, 200, 118, 3, 201, 992], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 212, 214, 216, 224, 226, 230, 232, 236, 238, 242, 244, 248, 250, 254, 256, 260, 262, 335, 336, 338, 339, 340, 343, 344, 345, 461, 463, 465, 467, 469, 471, 473, 475, 477, 479, 481, 483, 485, 487, 489, 491, 493, 495, 497, 499, 501, 503, 505, 507, 509, 511, 513, 516], [-1, 2, -1, 1, -1, -1, 1, -1, 2, -1, 1, -1, 2, -1, -1, -1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 1, -1, -1, -1, 2, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 2, -1, 2, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, 4, -1, 2, -1, 1, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, 4, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, 4, -1, 2, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, -1, -1, -1, 2, 4, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, -1, 2, 4, -1, 1, -1, 2, 4, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, -1, 2, -1, -1, -1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 2, -1, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 2, 13, -1, 1, -1, 1, -1, 2, 13, -1, 1, -1, 1, -1, 2, 13, -1, 2, -1, 2, 50, 51, 52, 53, 54, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 20, 0, 4, 0, 0, 4, 0, 21, 0, 2, 0, 22, 0, 0, 0, 0, 18, 0, 18, 0, 2, 0, 23, 0, 13, 0, 24, 0, 3, 0, 3, 0, 0, 0, 11, 0, 3, 0, 3, 0, 0, 0, 11, 0, 3, 0, 3, 0, 0, 0, 11, 0, 3, 0, 3, 0, 0, 0, 11, 0, 3, 0, 3, 0, 0, 0, 11, 0, 3, 0, 3, 0, 0, 0, 11, 0, 3, 0, 3, 0, 0, 0, 11, 0, 4, 0, 12, 0, 0, 4, 0, 12, 0, 0, 4, 0, 12, 0, 0, 4, 0, 12, 0, 0, 4, 0, 12, 0, 0, 4, 0, 12, 0, 0, 25, 0, 26, 0, 4, 0, 6, 0, 0, 4, 0, 6, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 0, 10, 0, 14, 0, 4, 0, 6, 0, 0, 4, 0, 6, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 0, 10, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 0, 10, 0, 4, 0, 6, 0, 0, 4, 0, 6, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 0, 0, 0, 8, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 1, 0, 4, 0, 9, 0, 10, 0, 14, 0, 14, 0, 4, 0, 6, 0, 0, 4, 0, 6, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 10, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 10, 0, 4, 0, 6, 0, 0, 4, 0, 6, 0, 0, 4, 0, 6, 0, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 10, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 10, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 0, 2, 2, 0, 7, 0, 0, 1, 0, 8, 0, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 1, 0, 5, 0, 1, 0, 1, 0, 4, 0, 9, 0, 10, 0, 4, 0, 13, 0, 4, 0, 15, 0, 4, 0, 16, 0, 4, 0, 15, 0, 4, 0, 16, 0, 4, 0, 15, 0, 4, 0, 16, 0, 0, 0, 0, 0, 27, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 0, 0, 19, 0, 19, 0, 13, 0, 29, 0, 3, 0, 3, 0, 30, 17, 0, 3, 0, 3, 0, 31, 17, 0, 3, 0, 3, 0, 32, 17, 0, 33, 0, 34, 35, 36, 37, 38, 39, 3, 3, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 2, 5, 1, 1, 5, 1, 1, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3]], [[{"name": "Icon_Exit", "rect": [0, 0, 53, 58], "offset": [0, 0], "originalSize": [53, 58], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [40]], [[{"name": "Icon_Info", "rect": [0, 0, 77, 74], "offset": [0, 0], "originalSize": [77, 74], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [41]], [[{"name": "btn_back", "rect": [0, 0, 30, 56], "offset": [0, 0], "originalSize": [30, 56], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [42]], [[{"name": "btnheader-at", "rect": [0, 0, 190, 44], "offset": [0, 0], "originalSize": [190, 44], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [43]], [[{"name": "boderHeaderCopy2", "rect": [0, 0, 480, 109], "offset": [0, 0], "originalSize": [480, 109], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [44]], [[{"name": "Icon_AvailableRoom", "rect": [0, 0, 62, 75], "offset": [0, 0], "originalSize": [62, 75], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [45]], [[{"name": "icoBackCopy", "rect": [0, 0, 122, 456], "offset": [0, 0], "originalSize": [122, 456], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [46]], [[{"name": "btnheaderiat", "rect": [0, 0, 190, 44], "offset": [0, 0], "originalSize": [190, 44], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [47]], [[{"name": "btn_pp", "rect": [0, 0, 157, 51], "offset": [0, 0], "originalSize": [157, 51], "capInsets": [10, 10, 10, 10]}], [1], 0, [0], [5], [48]], [[{"name": "bg", "rect": [0, 0, 1560, 720], "offset": [0, 0], "originalSize": [1560, 720], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [49]], [[{"name": "Icon_Ranking", "rect": [0, 0, 86, 76], "offset": [0, 0], "originalSize": [86, 76], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [50]], [[{"name": "Text_chondc", "rect": [0, 0, 235, 39], "offset": [0, 0], "originalSize": [235, 39], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [51]], [[{"name": "Btn_Back", "rect": [0, 0, 85, 87], "offset": [0, 0], "originalSize": [85, 87], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [52]], [[{"name": "topCenter", "rect": [0, 0, 552, 72], "offset": [0, 0], "originalSize": [552, 72], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [53]], [[{"name": "btn_Dat", "rect": [0, 0, 168, 67], "offset": [0, 0], "originalSize": [168, 67], "capInsets": [20, 8, 23, 18]}], [1], 0, [0], [5], [54]]]]