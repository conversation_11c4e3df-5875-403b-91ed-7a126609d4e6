[1, ["ecpdLyjvZBwrvm+cedCcQy", "69LxFgrGhGpqaJgP5QM/Se", "a9VpD0DP5LJYQPXITZq+uj", "7a/QZLET9IDreTiBfRn2PD", "45JHSSpthCCoVDAwY5uI4p", "825TQ2kU9Ktq1Ncj5HdPmn", "23iXkJ9qFGw5byHkVp2xAL", "852KDksvVDRa9hsC1BoCwL", "e2xSxof3xCnIoaUKvS6jbh", "e07bsmZslE+4xSHqXnZK0E", "3eThP3N/NEe7zhJruaUf/J", "2ejNmqLBFDfa8ABw2gPlbR", "f7pmr8naxMbKP0AIIjeIxr", "de5nlgBINE2KjgoF1Y2RJV", "5dsIbAWRxKLp5H1jMaR4Q8", "adw94Z+hpN57wutNivq8Q5", "ff3APWK/BETaREMLU3WDh4", "10ZjUBphlMEqVJ/O+EPoLU", "3f7veHw2xElonHlROrhKAC", "6cx9zzpUVHwrkgxM8w40xm", "adHB+lfFRLWosi+Z/JI/Ea", "3eqU0b7C5LVLpmpQGn51a+", "dfQo5p6SVOhImfY9X1/rY7", "017Jn3Zv1Ft7hygdjpaSoK", "aeGs965o9J1qJTKkeeo5Z7", "257f/tIf1BzpJt9WciHFHa", "81hQVq7YxOOI4iwrzxGVp/", "36mkY7ffxDOpplrWWMfZD6", "e8WSZqZF9HD4y2GAoJCK6q", "4bnfmTyEJDhLtkg/qbvWN4", "86V8yLqPFE5am6lqNMMlu0", "0cB1AxDc1OhZMtlOFccLMg", "52HdkvQv1KeoqKnlYMrU5f", "238Dx2EE5NV4KJsQtD9I3c", "21lti13DVCOJ0nZSV56ljt", "a2kG02nsREnaKx3xnOb0jj", "cerw+k8PVFh6iejN64nGu1", "48hDOaZldAH5LrJ2T3co3G", "9dFXrXUbVKYpxuFaYUDZNy", "feKZ+cVUNCObJaJgl9toBe", "7a8i9YNExP9YKJjbVR2mEV", "12aUJLhWJFpoF/tbpy20UN", "5cRAgRFlxGzp3LZhjobdOe", "a76Kfw8dpF5qbZJq7R0C9V", "feoXWzFOlIZ6kfr2jaBhRO", "3bMBY2p1NM76L+cofk8skq", "eeIVuWbtVGjZHadMbr5x7a", "1esrwb8IpLsIzenpqdnHWQ", "28NCfrqAREsYfHFOty9peH", "87cOZKfV9FfZnW6aI/B5xa", "2b1DmeOrNIWZf5wASIdhLJ", "24hkHXvwJGb76LY7PJgJqd", "70it1Ni5xNH7tKMvrC1xGy", "b58uMnPfZCbZsD2rsxFGnw", "16/66Wr0dITb/QvsTO5fD1", "e6hu8GlwVGmoV1hIV2zyAu", "b8gFdOvltEG6tiSUI5MEQk", "581GWGPOdH2bAR0BmAIIaY", "d2hllwP0FHBLJNAU1/YBmn", "1dlVEzfl9LwrX7yhOPQbyW", "93Tcuhf7JFAp8682j2JPJ1", "74XHjlUqdBQLLMsnNx8NJs", "a7oJTL3HdEkq9rOSkesPl/", "bepPBsdKNI47j8v7TMSq7+", "f9E4qSeDNB7rZMl1IdrSId", "27hqkVyjZCTbSHGNw8t10L", "c6355nL3lHQ6fwIpIu/cbO", "e7jUfV+wVN0JErw4xpZM3A", "7b4rAQK1hOvqUYwjnT/jFP", "af+BFiGXdP77wbIlfOIJzi", "dfk1skm9NHc7A8cd24h2JP", "a7ZIP+aGpJdJPFaAE8axIJ", "fcl/uK8/NJmqAsm+uyeToz", "8ccneBoXRCubPiOJU6rSIU", "016SXWL/BILLQWX2JYstKO", "1a4vksw7FIqa+oJ5wQYZbU", "b4YQO5eFFI+L5O3XYPBR5p", "7fzJkEcFZDX7L2gnfIJqmT", "19jIO3J5xIOp33N+oGQw/e", "44Re3ybHRKm5d8iAiyypFK", "23acNS6nNCNr19LE14Kb/c", "30oV5X5v1FwqVj9eqhxyw2", "57Iu22xTxEtY8164v19xYe", "e77vtwHllPp7H4khCbZa98", "5fRgdUjslJLZtcj//Ehhb0", "99Wt1uFSJMlpBL2ekJIwT0", "c17MitmYNEWbA1rLtybD9n", "59Uqvp+8ZJ5qRFijoL6zm4", "67rl1D0CFJQ5oi4H71ciqu", "ecHFsaKIZGAr5VMVRpijJh", "09l/2V6OtLF7RradoEgr4L", "3cJTOBBttISKfId27YO9Bp", "abgf5NpFhGZ7U/A+cwDZyF", "3eHR8/nStKgYUNUlhEUJ46", "60OcXXcz5GdZgycVPSM5LD", "3dF88x4xBONpUf7M4Lxb3/", "12fPODgS5PH6x40/ICM+SU", "c2hd122UROzrKF+8oVH/4s", "31bBVJwa9LgqdycczJIwZF", "d2ZC+A4ztFLadOvTa2VWBX", "80zZ7MnpVCXoOPNTd2a4WZ", "37Jp+EiddKcZTl2UpnGG1Z", "c6sg0HvqNHxqggdnMRRiAH", "f4qBQ9tlFBwr8gWdErlIbN", "35ERWSHctEequYRJoJvglC", "0132n2UO5HgZzLX/WhnxrR", "73jRuy/StKm4zmQo7eze3N", "73qQ9/isZKHovrabUGHL8z", "10Es8mEslLnZZuwyMvRK4d", "6eD3jB7UBGyq/gYCeA8G6+", "2aQx1uT15Hf4AxBa3eFeoO", "daiRm1VuRE/KabFxhpEG5Z", "96FSGPAe1C36Y7/JYNLEAu", "4aPWVcBG9J1a6NAaHWUZi0", "20NlzOu/pBLJcC3AdrsJD4", "7bre/ICERNGqAHD3EakmGF", "fb1NVtk/tPsIcntoUJuaBb", "876e1VwC1IlZH/EzpRPzer", "3f3OTO1pVLxL05kiTixh2D", "c7P/9m41BMsb7epeeRSoTd", "bb5Ju+UetMe63UJ6vQavi8", "f8UyfOpr5PEodcnj5OFa25", "f2wXhSL/xCwYw8qsb9A/Vf", "bb88hnsA1EP6W4iXko+PGR", "banSBwN1ZH2puBgqNf9knd", "3c9C/GwBZOopvVvOJuNQnS", "eclqlIXUdIP6Y7heaQwc2t", "fbYG4HZQtODolj3OWjPgSB", "0945YcVxdLuI8o8xVjyU7J", "88+nVUHxlECq7aOeFGKHRW", "9eYBlvGfdIz4Rri6Er9ONB", "eb7lmaztNNe5AKMVqNE9OJ", "59NmVyJaFA97O9o3TmRMmt", "c7FTqB8txIkKXvUfiAcwIx", "99p94ZGIlGMI0qqphFIyH5", "9651FWaflG3LJXg3y+V8Si", "60U9sZd3pGEJbyilXaozyg", "cfgfG/uDxMcoUekwnkcI3M", "a9+8Ab0iVNgLut9Nc+VYSc", "e0m6gik8NH+IV9EluIT1gQ", "24Uq6GLTtDN5QwmLFSVlNp", "f9a3gBJmJGTpfvyQDDUYUa", "12GJpOXY5FXY9b2psLePk5", "92R8ezLcJObZo/GOlG+1Yl", "35l7RNxMZNw7NKZvI7hA7Y", "14otY5fk1LbYzOgpEUwYRK", "7b4BhULnRJQIts+S37TJjS", "9d6AdbWQhDnqtw7KLqQbag", "61EjIZbuhCKYjy9Tksf+TI", "9fAU1h4RRFtLazpYTFVeHC", "a33UZXc/5Pzo5TFjSuAxtE", "34NWpIzK5Pc7fAV3Ejn63r", "579MPIz6xKULAeSu8MlAmH", "e0ueET7nZDr7nk5DTB6i+V", "51bTwLc/FGA7x5ZRralE9e", "04MchQ3plFzIkHzhJPZUI8", "3epUxwmbdHI4GyBj/2uXZU", "0bAozzgJ9JvpgubgTb9pa2", "77cm8hi4xBEJMByjNrt1Ba", "f3J3wwTuZKAYFQNVb0IUyb", "2ey8oDy2FOHZ+ExnPMQNly", "8bi1TqekZHHJb/qqdt3NSg", "daR3aBv2BAxLKIQZsNcaAH", "ce8nxABINBJKuRsH3BbDMN", "85oZlhp+lIQ5we5KfLJWzu", "daBx5mgjtB5o+48GGUwK55", "4d5dJeuSRG/pnYzpdX8FOK", "a3XDHg9+NG17dVToHqH8eI", "17jjGjeDdHt6rJ/4FJpKPP", "c5GIbG+WlHUZS6Mrfwopyd", "49I3Ex3vlIwIfU/lArREr6", "52LcY3TAVAQ4PZpag+0cnm", "bdxwY9dA5OkIKTyczYKWhS", "2cisq3sEtO7I8xslyXwfBz", "33qLDVNDpMOZM6TRBR1n1x", "d6ad6SJNBMyZtMr8KuV9aA", "a2tBXzjmRHWIetS1zkxuiC", "86WgjSKzZATI4q3zdPlz6Y", "fejefp3nBBkqMgDkJLN18X", "f4S4i8O1RNW7sL0mgtYQp9", "a911dPIMpLobb8/U+XIQ+A", "d6VSlMnXdCwoVTHfighJrf", "08JSz+a9pHWY18TbrgIqX4", "3dveSbxZFBw7Rynj98ViQI", "9dNMhFOH5D9pB7Ui6HFzK0", "8cjsKDDTNBTJN68xHCvXJ/", "1eja6fiG5OfaGS7h34hdbf", "cfLifEg7lKTZy3HftMjv0W", "b6SVuTF3JMIrs7wndzOCTz", "c0op8wGh9BA6ZwIA7nGINR", "53r+bDw+dOIZhu28ZP0nBw", "eaxVl5IcdD1I50kVrex7Bd", "4c0V2CFdtAAZC1u7RO5O0C", "215e03mnRLzbvoFxHmwoia", "6dd8yw24FMt6EpUCojUOsM", "23pglL/mxJlocAJ7fefZkG", "7b1bAdntZMSIwnXkfza7ZQ", "4cZXFOodROgqdMzoT4Rkjh", "7aFG3Y4oRHHqPKO7ib3EOJ", "41BpyKUlpGQ6GJF7spigrN", "31jRbSac9MmoOoZeTWY9eT", "48AgZFNBNPIKvC2l/A5V8l", "02i2SGkf1Ewq4f5KoU6MO8", "6bQOL8xYhOhYuvIG1Uj8wN", "9eeqnY+fZGebiBqTNw9VPj", "e4VTDkKPBAx6ggBarxGPrI", "57xPCPsRlIkpTknzNaOlGw", "6e+qli+DRCYYidMHgIW2wI", "67WWyzaEBO2Z/HkikCHWN9", "936LJUrehHHKqiNwgC3wUY", "b2ppX9rpJMQoorzdXGqIq4", "e2ICJJF2NDaZr1Dl3VdWEA", "80NfsQtY1PEpZLgsrKOmHn", "9a+jss6gZLCIde07V64y5l", "a0R8IF+GpDaquXMk9UmkL+", "fc9vR3fPtDobKRHIRmNsO5", "f9c9RRCgBNgaEbBestquhC", "e0OiQ+jj5CxZAUY28mO1y4", "b7GH4Ek/9HRrBnwWWHzljx", "e61fubqiFEKaI2ii2PGwsD", "34yyqv7HNK/rfhei8S71fy", "21YQW8RmxP65iO2cASflTV", "23HoaujLRCdpGNreverxGU"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "_N$skeletonData", "_clip", "_N$normalSprite", "_defaultClip", "_parent", "_N$target", "_file", "spriteMusic", "spriteSound", "nodeOffset", "animation", "root", "nodeMain", "nodeRoom", "expandWild", "x2Win", "x2Click", "miniGameActive", "openCard", "bonusWin", "bonusMiss", "bonusClick", "stopSpin5", "stopSpin4", "stopSpin3", "stopSpin2", "stopSpin1", "spin", "getBonus", "bigWin", "normalWin", "musicBackground", "spriteX2", "spriteFastSpin", "spriteAutoSpin", "spriteSpin", "spriteSelectBetLines", "btnSpin", "btnX2", "btnSelectBetLines", "btnBack", "lbFreeSpinText", "btnHandle", "animationReward", "lbReward", "particleReward", "particleWin", "particleBigWin", "particleJackpot", "nodeNormalWin", "nodeEffect", "spinView", "spriteXHu", "lbiJackpot", "lbWin", "lbMessage", "data", "lbiBet", "lbTotalLines", "lbSessionID", "lbiTotalWin", "lbiTotalBet", "prefabHelp", "prefabBetLines", "prefabLeaderboard", "prefabHistory", "prefabSessionDetail", "prefabX2Game", "prefabBonusGame", "sfPopupNormal", "sfPopupWin"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_obj<PERSON><PERSON>s", "_prefab", "_parent", "_components", "_children", "_trs", "_contentSize", "_anchorPoint", "_color"], -1, 4, 1, 9, 2, 7, 5, 5, 5], ["cc.Label", ["_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_fontSize", "_lineHeight", "_N$overflow", "_enableWrapText", "_spacingX", "_N$cacheMode", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_fillType", "_enabled", "node", "_spriteFrame", "_materials"], -2, 1, 6, 3], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], 1, 2, 4, 1, 5, 7, 2, 5, 5, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_N$normalSprite"], 1, 1, 9, 5, 5, 1, 5, 6], ["sp.Skeleton", ["_preCacheMode", "_animationName", "defaultSkin", "premultipliedAlpha", "defaultAnimation", "loop", "_playTimes", "_materials", "node", "_N$skeletonData"], -4, 3, 1, 6], ["cc.Node", ["_name", "_parent", "_prefab", "_contentSize", "_trs", "_components", "_anchorPoint", "_children", "_color"], 2, 1, 4, 5, 7, 12, 5, 12, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_enabled", "_N$spacingX", "_N$spacingY", "_N$paddingTop", "_N$paddingBottom", "node", "_layoutSize"], -4, 1, 5], ["cc.ParticleSystem", ["emissionRate", "life", "lifeVar", "angleVar", "startSize", "endSize", "_positionType", "speed", "speedVar", "tangentialAccel", "_custom", "totalParticles", "angle", "startSpinVar", "endSpin", "radialAccelVar", "_dstBlendFactor", "startSizeVar", "startSpin", "endSizeVar", "duration", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "gravity", "posVar"], -18, 1, 3, 8, 8, 8, 8, 5, 5], ["cc.Node", ["_name", "_active", "_children", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_anchorPoint"], 1, 2, 12, 4, 1, 5, 7, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "curveData", "wrapMode", "speed", "events"], -3], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.AudioSource", ["preload", "_volume", "_loop", "node"], 0, 1], ["cc.Prefab", ["_name"], 2], ["5c06dmsjgRI86aBwpJnaIGR", ["node", "icons", "skeletonDataIcons"], 3, 1, 3, 3], ["48121xYynhO56qpPvfYDts6", ["node", "nodeRoom", "nodeMain", "btnRooms"], 3, 1, 1, 1, 2], ["cda02L2WeNEGYTOgUaaQ03F", ["node", "nodeLines", "iconSkeletons", "nodeBlackBots", "nodeBlackTops"], 3, 1, 2, 2, 2, 2], ["63719vciutBRYbjSIQa5LWY", ["node", "musicBackground", "normalWin", "bigWin", "getBonus", "spin", "stopSpin1", "stopSpin2", "stopSpin3", "stopSpin4", "stopSpin5", "bonusClick", "bonusMiss", "bonusWin", "openCard", "miniGameActive", "x2Click", "x2Win", "expandWild"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["27803B0B7ZN+I0Q5PBtYj6/", ["node", "prefabHelp", "prefabBetLines", "prefabLeaderboard", "prefabHistory", "prefabSessionDetail", "prefabX2Game", "prefabBonusGame"], 3, 1, 6, 6, 6, 6, 6, 6, 6], ["ca012RPE/tBobOu1Tyx0LzF", ["node", "btnBack", "btnSelectBetLines", "btnX2", "btnSpin", "spriteSelectBetLines", "spriteSpin", "spriteAutoSpin", "spriteFastSpin", "spriteX2", "sfSpins", "sfAutoSpins", "sfFastSpins", "sfX2s"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 3, 3], ["5ae1cRTR91EKo1dRfEBKoCs", ["node", "lbFreeSpinText"], 3, 1, 1], ["0e926h5bvJCrbphbkTZxDkS", ["node", "lbiJackpots"], 3, 1, 2], ["dcd4akrIzFOe4glwzM/CGWZ", ["node", "animation", "nodeOffset", "spriteSound", "spriteMusic", "sfSounds", "sfMusics"], 3, 1, 1, 1, 1, 1, 3, 3], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["8457bzXi+RCFrWEyccPy/PF", ["touchParent", "node", "btnHandle"], 2, 1, 1], ["6b10eLy4IxKZqah/4LLbcrh", ["node", "nodeEffect", "nodeJackpot", "nodeBigWin", "nodeNormalWin", "particleJackpot", "particleBigWin", "particleWin", "lbiTotalWins", "nodeReward", "particleReward", "lbReward", "animationReward"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1], ["d8b47NXGrVB2qYWwAxFnfHg", ["node", "spinView"], 3, 1, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["47b4eqoA/hFw4ocENL+7owM", ["node"], 3, 1], ["d857dgwT55H868B0MQw1E2w", ["node"], 3, 1], ["5c1fa7YnkBEMqpU9n30ksJn", ["gameId", "node", "nodeX", "spriteXHu", "lbRemainJackpots"], 2, 1, 1, 1, 2], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["9e952KkK7dIaIHwrq2+CAV7", ["node", "lbiJackpot"], 3, 1, 1], ["832cdd6gThBUZ3q0q4hjRy5", ["messPosY", "messWinPosY", "node", "nodeMessage", "lbMessage", "lbWin", "sfPopupNormal", "sfPopupWin"], 1, 1, 1, 1, 1, 6, 6], ["9d3d9rV3HhCYK0RXnBis1Un", ["node", "spriteIcons", "skeletonIcons"], 3, 1, 2, 2], ["0f7c079iqNP46wGMXF0uuip", ["node", "spinColumnViews"], 3, 1, 2], ["cc.ProgressBar", ["_N$mode", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["f2e7alU2ytCupAFgS4RXj5c", ["node", "prgAccumulate", "lbTime", "lbTotalPrize", "nodeTichLuy", "nodeFx", "lb<PERSON><PERSON>l", "listPiceNV", "sfPiceNV", "nodePice", "nodeNVWin", "particleWin", "particleCoin"], 3, 1, 1, 1, 1, 1, 1, 1, 2, 3, 1, 1, 1, 1], ["cc.AudioClip", ["_name", "_native", "duration"], 0]], [[13, 0, 1, 2], [1, 0, 5, 6, 4, 9, 8, 2], [3, 5, 7, 6, 1], [3, 0, 1, 5, 7, 6, 3], [1, 0, 1, 5, 6, 4, 9, 3], [3, 2, 0, 1, 5, 6, 4], [1, 0, 1, 5, 7, 6, 4, 9, 8, 3], [1, 0, 5, 7, 6, 4, 9, 8, 2], [4, 0, 4, 2, 3, 5, 6, 2], [2, 3, 4, 0, 1, 2, 10, 11, 12, 6], [1, 0, 5, 6, 4, 9, 2], [14, 0, 1, 3, 3], [1, 0, 5, 7, 4, 9, 8, 2], [4, 0, 4, 2, 3, 2], [4, 0, 1, 4, 2, 3, 5, 3], [3, 0, 1, 5, 3], [15, 0, 3, 2], [30, 0, 1], [7, 0, 1, 5, 2, 3, 4, 2], [3, 0, 1, 5, 7, 3], [11, 1, 2, 1], [6, 2, 4, 0, 3, 1, 8, 7, 9, 6], [5, 1, 0, 2, 3, 4, 5, 6, 3], [2, 3, 4, 5, 0, 8, 1, 2, 10, 11, 12, 8], [6, 2, 0, 3, 1, 7, 5], [3, 0, 5, 7, 6, 2], [1, 0, 1, 5, 6, 4, 9, 8, 3], [4, 0, 4, 7, 2, 3, 5, 6, 2], [11, 1, 2, 3, 1], [14, 0, 1, 2, 3, 4], [2, 3, 4, 5, 0, 1, 2, 10, 11, 12, 7], [6, 2, 4, 0, 3, 1, 7, 6], [10, 0, 5, 2, 3, 4, 6, 7, 2], [4, 0, 4, 2, 3, 5, 8, 6, 2], [11, 0, 1, 2, 3, 2], [5, 2, 3, 7, 1], [2, 3, 4, 5, 0, 1, 2, 10, 11, 7], [37, 0, 1, 2, 1], [12, 0, 1, 4, 3, 5, 2, 7], [1, 0, 5, 7, 6, 4, 11, 9, 10, 8, 2], [5, 1, 0, 2, 3, 4, 5, 6, 8, 3], [5, 0, 2, 3, 4, 5, 6, 8, 2], [5, 1, 0, 2, 3, 7, 3], [6, 2, 0, 3, 1, 8, 7, 5], [1, 0, 5, 7, 6, 4, 9, 10, 8, 2], [1, 0, 2, 5, 6, 4, 9, 3], [4, 0, 4, 2, 3, 6, 2], [12, 0, 1, 2, 4], [1, 0, 5, 7, 4, 8, 2], [1, 0, 1, 2, 7, 6, 4, 9, 8, 4], [1, 0, 5, 7, 4, 2], [1, 0, 1, 7, 6, 4, 8, 3], [1, 0, 5, 7, 6, 4, 9, 2], [1, 0, 1, 2, 5, 6, 4, 9, 8, 4], [7, 0, 1, 5, 2, 8, 3, 4, 2], [10, 0, 5, 2, 3, 4, 7, 2], [4, 0, 4, 2, 3, 5, 2], [3, 0, 5, 6, 2], [25, 0, 1, 2, 3, 4, 5, 6, 1], [5, 2, 7, 1], [5, 2, 3, 7, 6, 1], [2, 3, 4, 5, 7, 0, 8, 1, 2, 6, 10, 11, 12, 10], [2, 3, 4, 5, 7, 0, 1, 2, 10, 11, 12, 8], [2, 3, 5, 0, 1, 2, 10, 11, 12, 6], [34, 0, 1], [9, 0, 1, 2, 3, 4, 5, 13, 14, 6, 7, 8, 9, 15, 10, 21, 22, 23, 24, 25, 26, 28, 27, 15], [41, 0, 1, 2, 4], [12, 0, 1, 3, 2, 5], [16, 0, 2], [1, 0, 7, 6, 4, 2], [1, 0, 7, 6, 4, 8, 2], [1, 0, 3, 5, 7, 6, 4, 3], [1, 0, 3, 1, 5, 7, 6, 4, 4], [1, 0, 7, 4, 2], [1, 0, 5, 4, 9, 8, 2], [1, 0, 5, 7, 6, 4, 2], [1, 0, 1, 5, 7, 6, 4, 3], [1, 0, 1, 5, 7, 4, 8, 3], [1, 0, 1, 5, 6, 4, 11, 9, 8, 3], [1, 0, 1, 2, 5, 6, 4, 9, 4], [1, 0, 5, 6, 4, 8, 2], [7, 0, 1, 7, 2, 3, 6, 4, 2], [7, 0, 1, 5, 2, 3, 2], [7, 0, 1, 5, 2, 3, 6, 4, 2], [10, 0, 5, 2, 3, 4, 6, 2], [10, 0, 1, 2, 3, 4, 6, 8, 3], [4, 0, 1, 7, 2, 3, 6, 3], [4, 0, 1, 4, 2, 3, 5, 6, 3], [4, 0, 4, 2, 3, 9, 5, 6, 2], [4, 0, 4, 2, 3, 5, 6, 10, 2], [17, 0, 1, 2, 1], [18, 0, 1, 2, 3, 1], [13, 1, 1], [19, 0, 1, 2, 3, 4, 1], [20, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 1], [21, 0, 1, 2, 3, 4, 5, 6, 7, 1], [22, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 1], [23, 0, 1, 1], [3, 0, 1, 5, 6, 3], [3, 2, 0, 1, 5, 7, 6, 4], [3, 5, 7, 1], [3, 2, 0, 5, 7, 6, 3], [3, 2, 3, 5, 7, 3], [3, 4, 5, 6, 2], [8, 2, 0, 1, 3, 7, 8, 5], [8, 0, 1, 3, 7, 8, 4], [8, 0, 1, 5, 6, 4, 7, 8, 6], [8, 2, 0, 1, 7, 8, 4], [8, 2, 0, 1, 4, 7, 8, 5], [24, 0, 1, 1], [26, 0, 1, 2, 2], [11, 0, 1, 2, 2], [27, 0, 1, 2, 2], [28, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [29, 0, 1, 1], [5, 2, 1], [5, 0, 2, 3, 4, 5, 8, 2], [5, 2, 3, 1], [5, 1, 0, 2, 4, 5, 6, 3], [2, 4, 5, 0, 1, 6, 9, 10, 12, 7], [2, 4, 5, 0, 1, 2, 6, 10, 11, 12, 7], [2, 3, 1, 2, 10, 11, 4], [2, 3, 4, 5, 0, 2, 11, 6], [2, 4, 0, 10, 11, 3], [2, 4, 5, 0, 1, 2, 6, 10, 11, 7], [2, 3, 4, 0, 1, 2, 6, 10, 11, 7], [2, 3, 5, 0, 1, 2, 6, 10, 7], [2, 3, 5, 0, 1, 2, 10, 6], [31, 0, 1], [32, 0, 1], [33, 0, 1, 2, 3, 4, 2], [6, 0, 3, 1, 7, 4], [6, 2, 4, 0, 5, 1, 6, 8, 7, 9, 7], [6, 2, 4, 0, 1, 8, 7, 9, 5], [6, 2, 4, 0, 3, 1, 8, 7, 6], [35, 0, 1, 1], [36, 0, 1, 2, 3, 4, 5, 6, 7, 3], [15, 1, 2, 0, 3, 4], [38, 0, 1, 1], [9, 16, 10, 11, 0, 1, 2, 12, 3, 4, 17, 5, 19, 18, 6, 7, 8, 9, 21, 22, 23, 24, 25, 26, 27, 18], [9, 16, 11, 0, 1, 2, 12, 3, 4, 17, 5, 18, 6, 7, 8, 9, 21, 22, 23, 24, 25, 26, 27, 16], [9, 11, 0, 1, 2, 12, 3, 4, 5, 13, 14, 6, 7, 8, 9, 15, 10, 21, 22, 23, 24, 25, 26, 28, 27, 17], [9, 11, 20, 0, 1, 2, 3, 4, 5, 13, 14, 6, 7, 8, 9, 15, 10, 21, 22, 23, 24, 25, 26, 28, 27, 17], [39, 0, 1, 2, 3, 3], [40, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1]], [[[{"name": "avenger_symbol_1_7", "rect": [0, 0, 144, 153], "offset": [0, 0], "originalSize": [144, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [72]], [[{"name": "Layer9", "rect": [0, 0, 64, 68], "offset": [0, 0], "originalSize": [64, 68], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [73]], [[{"name": "avenger_btn_back", "rect": [0, 0, 83, 74], "offset": [0, 0], "originalSize": [83, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [74]], [[{"name": "avenger_bg", "rect": [0, 0, 1559, 720], "offset": [0, 0], "originalSize": [1559, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [75]], [[{"name": "music_on", "rect": [1, 6, 138, 50], "offset": [0, -2], "originalSize": [140, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [76]], [[[47, "playFXnv", 1, {"props": {"scaleX": [{"frame": 0, "value": 0}, {"frame": 0.15, "value": 4}, {"frame": 1, "value": 0}]}}]], 0, 0, [], [], []], [[{"name": "line_10_11", "rect": [0, 0, 776, 315], "offset": [0, 0], "originalSize": [776, 315], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [77]], [[{"name": "line_20_21", "rect": [0, 0, 776, 298], "offset": [0, 0], "originalSize": [776, 298], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [78]], [[{"name": "Layer7_on", "rect": [0, 0, 87, 50], "offset": [0, 0], "originalSize": [87, 50], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [79]], [[{"name": "box_cuoc", "rect": [0, 0, 336, 89], "offset": [0, 0], "originalSize": [336, 89], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [80]], [[[38, "columnSpin", 0.6833333333333333, 4, 2, [{"frame": 0.16666666666666666, "func": "randomIcon", "params": ["3"]}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 298}, {"frame": 0.6666666666666666, "value": -302}, {"frame": 0.6833333333333333, "value": 298}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 148}, {"frame": 0.5, "value": -302}, {"frame": 0.5166666666666667, "value": 298}, {"frame": 0.6833333333333333, "value": 148}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": -2}, {"frame": 0.3333333333333333, "value": -302}, {"frame": 0.35, "value": 298}, {"frame": 0.6833333333333333, "value": -2}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -152]}, {"frame": 0.16666666666666666, "value": [0, -302]}, {"frame": 0.18333333333333332, "value": [0, 298]}, {"frame": 0.6833333333333333, "value": [0, -152]}]}}}}]], 0, 0, [], [], []], [[{"name": "avenger_setting_bg", "rect": [0, 0, 807, 481], "offset": [0, 0], "originalSize": [807, 481], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [81]], [[{"name": "line_4_5", "rect": [0, 0, 776, 323], "offset": [0, 0], "originalSize": [776, 323], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [82]], [[{"name": "line1_2_3", "rect": [0, 0, 773, 32], "offset": [0, 0], "originalSize": [773, 32], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [83]], [[{"name": "Layer8_on", "rect": [0, 0, 87, 48], "offset": [0, 0], "originalSize": [87, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [84]], [[{"name": "khung_o", "rect": [0, 0, 1102, 464], "offset": [0, 0], "originalSize": [1102, 464], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [85]], [[[67, "activeNV", 0.6666666666666666, "2", {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.3333333333333333, "value": 0}, {"frame": 0.6666666666666666, "value": 255}]}}]], 0, 0, [], [], []], [[{"name": "nv", "rect": [0, 0, 103, 146], "offset": [0, 0], "originalSize": [103, 146], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [86]], [[[38, "columnStop", 3.35, 4, "0", [{"frame": 2.6666666666666665, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 298}, {"frame": 1.3333333333333333, "value": -302}, {"frame": 1.35, "value": 298}, {"frame": 1.85, "value": 238}, {"frame": 2.3333333333333335, "value": 298}, {"frame": 2.8333333333333335, "value": 268}, {"frame": 3.35, "value": 298}], "active": [{"frame": 0, "value": true}, {"frame": 1.3333333333333333, "value": false}, {"frame": 1.35, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 148}, {"frame": 1, "value": -302}, {"frame": 1.0166666666666666, "value": 298}, {"frame": 1.35, "value": 148}, {"frame": 1.85, "value": 88}, {"frame": 2.3333333333333335, "value": 148}, {"frame": 2.8333333333333335, "value": 118}, {"frame": 3.35, "value": 148}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, -2]}, {"frame": 0.6666666666666666, "value": [0, -302]}, {"frame": 0.6833333333333333, "value": [0, 298]}, {"frame": 1.35, "value": [0, -2]}, {"frame": 1.85, "value": [0, -62]}, {"frame": 2.3333333333333335, "value": [0, -2]}, {"frame": 2.8333333333333335, "value": [0, -32]}, {"frame": 3.35, "value": [0, -2]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -152]}, {"frame": 0.3333333333333333, "value": [0, -302]}, {"frame": 0.35, "value": [0, 298]}, {"frame": 1.35, "value": [0, -152]}, {"frame": 1.85, "value": [0, -212]}, {"frame": 2.3333333333333335, "value": [0, -152]}, {"frame": 2.8333333333333335, "value": [0, -182]}, {"frame": 3.35, "value": [0, -152]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "avenger_symbol_1_12", "rect": [0, 0, 144, 166], "offset": [0, 0], "originalSize": [144, 166], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [87]], [[{"name": "mis", "rect": [0, 0, 114, 126], "offset": [0, 0], "originalSize": [114, 126], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [88]], [[{"name": "BG_MainG", "rect": [0, 0, 2024, 1200], "offset": [0, 0], "originalSize": [2024, 1200], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [89]], [[{"name": "Layer5_on", "rect": [0, 0, 66, 73], "offset": [0, 0], "originalSize": [66, 73], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [90]], [[{"name": "Layer5", "rect": [0, 0, 66, 73], "offset": [0, 0], "originalSize": [66, 73], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [91]], [[{"name": "avenger_symbol_1_8", "rect": [0, 0, 148, 153], "offset": [0, 0], "originalSize": [148, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [92]], [[{"name": "gold2", "rect": [6, 6, 39, 37], "offset": [1, 0], "originalSize": [49, 49], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [93]], [[{"name": "sound_on", "rect": [0, 8, 133, 45], "offset": [0, -1.5], "originalSize": [133, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [94]], [[{"name": "item_7", "rect": [1, 3, 122, 116], "offset": [0.5, -1.5], "originalSize": [123, 119], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [95]], [[{"name": "sound_off", "rect": [0, 0, 133, 58], "offset": [0, 0], "originalSize": [133, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [96]], [[{"name": "avenger_bottom", "rect": [0, 0, 983, 119], "offset": [0, 0], "originalSize": [983, 119], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [97]], [[{"name": "avenger_btn_lsgd", "rect": [0, 0, 18, 37], "offset": [0, 0], "originalSize": [18, 37], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [98]], [[{"name": "avenger_symbol_1_6", "rect": [0, 0, 144, 153], "offset": [0, 0], "originalSize": [144, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [99]], [[{"name": "avenger_btn_settings", "rect": [0, 0, 82, 85], "offset": [0, 0], "originalSize": [82, 85], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [100]], [[[68, "cb<PERSON><PERSON><PERSON>"], [69, "cb<PERSON><PERSON><PERSON>", [-11, -12, -13, -14, -15], [[90, -2, [469, 470, 471, 472, 473, 474, 475, 476, 477, 478, 479], [480, 481, 482, 483, 484, 485, 486, 487, 488, 489, 490]], [91, -10, -9, -8, [-3, -4, -5, -6, -7]]], [92, -1]], [70, "payLinesView", [-87, -88, -89, -90, -91, -92, -93, -94, -95, -96, -97, -98, -99, -100, -101, -102, -103, -104, -105, -106, -107, -108, -109, -110, -111], [[93, -86, [-61, -62, -63, -64, -65, -66, -67, -68, -69, -70, -71, -72, -73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85], [-46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60], [-31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45], [-16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30]]], [0, "77jjR26UtLL5TUg1hlO0PS", 1], [-26, 105, 0, 0, 0, 0, 1, 1, 1, 1]], [71, "audioPool", 512, 1, [-131, -132, -133, -134, -135, -136, -137, -138, -139, -140, -141, -142, -143, -144, -145, -146, -147, -148], [[94, -130, -129, -128, -127, -126, -125, -124, -123, -122, -121, -120, -119, -118, -117, -116, -115, -114, -113, -112]], [0, "dfIf2efElDdZkFdmyor3dW", 1]], [72, "slotsView", 512, false, 1, [-162, -163, -164, -165, -166, -167, -168, -169], [[95, -149, 431, 432, 433, 434, 435, 436, 437], [96, -159, -158, -157, -156, -155, -154, -153, -152, -151, -150, [438, 439], [440, 441], [442, 443], [444, 445]], [97, -161, -160]], [0, "4efK2eK9JGsrdlI/yqMomT", 1]], [48, "bot-bar", 4, [-170, -171, -172, -173, -174, -175, -176, -177, -178, -179, -180], [0, "70dJlAZTdNjqTLzY7IUvVa", 1], [-121, -315.4, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "top-bar", 4, [-181, -182, -183, -184, -185, -186, -187, -188, -189, -190, -191], [0, "b2d5t0nZJF9qW6tm5BWB56", 1], [0, 291.0000000000002, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "piceNV", [-192, -193, -194, -195, -196, -197, -198, -199, -200, -201, -202], [0, "d7YkXba6hEFLg+vjA9L1w/", 1]], [49, "progressTichLuy", false, 251.55074999996032, [-204, -205, -206, -207, -208, -209, -210, 7], [[3, 2, false, -203, [279], 280]], [0, "9asFshrFBAlYskehhTBZkQ", 1], [5, 200, 405], [536, 97, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "offset-room", 1, [-211, -212, -213, -214, -215, -216, -217], [0, "2cPtzD8sxJEaY8guV+mAy3", 1]], [7, "jackpotView", 9, [-224, -225, -226, -227, -228, -229], [[104, false, 1, 1, 8, -218, [5, 1224, 500]], [109, -223, [-219, -220, -221, -222]]], [0, "bePXyfpRFAD6mP+Q6OqIFN", 1], [5, 1224, 500], [24, -51, 0, 0, 0, 0, 1, 1, 1, 1]], [81, "backgroundSlot", 4, [[-230, -231, 8, 2, -232, -233, [74, "wildsUpper", -234, [0, "cb+A6fizpJyKKtuJCXFsJK", 1], [5, 1927.5, 450], [-25, 109, 0, 0, 0, 0, 1, 1, 1, 1]], -235], 1, 1, 1, 1, 1, 1, 4, 1], [0, "f6yT+8HuBF2LhSWNI/52Co", 1], [5, 872, 478], [0, 0.5, 0.3], [0, -95, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "<PERSON><PERSON><PERSON><PERSON>", 9, [-242], [[-236, [58, -241, -240, -239, -238, -237, [22, 23], [24, 25]]], 1, 4], [0, "46o+sZNJBFNYGhN1zprmGH", 1], [-66.356, -12.784, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "<PERSON><PERSON><PERSON><PERSON>", 4, [-249], [[-243, [58, -248, -247, -246, -245, -244, [202, 203], [204, 205]]], 1, 4], [0, "247D7knNlMMYLQZldBP7HY", 1], [-62.507, 19.189, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "btnRoom4", 10, [-251, -252, -253, -254], [-250], [0, "98kUXeI6ZHarnpZouH/bhc", 1], [5, 323, 410], [576.963, -50.686, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "spinView", 11, [-257], [[[110, 1, -255, [147]], -256], 4, 1], [0, "10v9xYijlEa41X4gSR+aL0", 1], [5, 1200, 456], [4, 90.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "slots", 15, [-259, -260, -261, -262, -263], [[105, 1, 1, 50, -258, [5, 1000, 450]]], [0, "af1SLMI4xLbKIaIqaWEiJF", 1], [5, 1000, 450], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "1", 16, [-266, -267, -268, -269], [[[28, -264, [82, 83, 84, 85], 81], -265], 4, 1], [0, "58vD10QFlLyYQwg6ALCFH8", 1], [5, 160, 450], [-420, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "2", 16, [-272, -273, -274, -275], [[[28, -270, [97, 98, 99, 100], 96], -271], 4, 1], [0, "e3N3D6a0xDbpeFCA6L59+a", 1], [5, 160, 450], [-210, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [84, "3", 16, [-278, -279, -280, -281], [[[28, -276, [113, 114, 115, 116], 112], -277], 4, 1], [0, "ceLE+00uFJNJCMQSsb0a3y", 1], [5, 160, 450]], [32, "4", 16, [-284, -285, -286, -287], [[[28, -282, [128, 129, 130, 131], 127], -283], 4, 1], [0, "7ev5KirGtARILRT8SddiZy", 1], [5, 160, 450], [210, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "5", 16, [-290, -291, -292, -293], [[[28, -288, [143, 144, 145, 146], 142], -289], 4, 1], [0, "5bcqJrZ6xCcJ1rG8tstwhX", 1], [5, 160, 450], [420, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "offset-jackpot", false, [-295, -296, -297, -298], [[34, true, -294, [407], 406]], [0, "66tN7ozslNuaFaz2Bsnr4F", 1], [0, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "offset-bigWin", false, [-300, -301, -302, -303], [[34, true, -299, [416], 415]], [0, "afRLh3WBdFL6wpWHFplk0e", 1], [0, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [86, "offset-reward", false, [-305, -306, -307, -308], [-304], [0, "5ffiREShVF3bM9VUOiTfq5", 1], [0, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [85, "offset-xHu", false, [-312, -313, -314], [[[112, null, -310, -309], -311], 4, 1], [0, "c2oeVLJB9M9Z5M9GUvwbni", 1], [5, 190, 190], [0, 0.5, 0.9]], [44, "layout", 25, [-317, -318, -319, -320], [[25, 0, -315, [461], 462], [106, 1, 2, 25, 25, 15, -316, [5, 190, 295]]], [0, "f4hPXzMvRO948AdqQsPiox", 1], [5, 190, 295], [0, 0.5, 1], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "btnRoom1", 10, [-322, -323, -324], [-321], [0, "2a4LOY9dpIBa3lv8bRl17g", 1], [5, 323, 410], [-632.333, -58.408, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "btnRoom2", 10, [-326, -327, -328], [-325], [0, "b1Uf5B/k1Bhb0EfEOKSKzl", 1], [5, 323, 410], [-283.644, -60.299, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "btnRoom3", 10, [-330, -331, -332], [-329], [0, "6e3KQR6H5GELB0qUu16lTB", 1], [5, 323, 409], [82.979, -70.803, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "effect<PERSON>iew", 4, [-345], [[113, -344, -343, 22, 23, -342, -341, -340, -339, [-336, -337, -338], 24, -335, -334, -333]], [0, "bfsxpBBAVMIKR5ktzKAyiI", 1]], [50, "offset-effect", 30, [22, 23, -346, 24], [0, "b3LJCyTw5IYLBzjATPPulJ", 1]], [76, "offset-normalWin", false, 31, [-348, -349, -350], [[34, true, -347, [422], 421]], [0, "caeJWtRXFIiaMo8Bf7upHn", 1]], [7, "bg_set_top", 12, [-352, -353], [[19, 2, false, -351, [19]]], [0, "4dsgerU+dDfqCZAJen+OEx", 1], [5, 220, 224], [0, -4, 0, 0, 0, 0, 1, 0, 0, 1]], [52, "layout", 33, [-355, -356, -357], [[107, false, 1, 2, -354, [5, 240, 111]]], [0, "a1JqP9n9BOfJejgnAKxnu1", 1], [5, 240, 111]], [27, "btnRoomFree", 10, [-359], [-358], [0, "83YLuJmWxDsI9WuA0N2YTX", 1], [5, 240, 100], [-25, 374.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot1", 17, [-360, -361, -362, -363], [0, "000PLZsD5Nv7f8f3dOzMUw", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot2", 17, [-364, -365, -366, -367], [0, "2a0FHhzIJPvL7ZtdsbARWW", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot3", 17, [-368, -369, -370, -371], [0, "c9Rs8manNFHLKtZgToXilR", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot1", 18, [-372, -373, -374, -375], [0, "deJIkB+AdK6ozB8HVKk3g2", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot2", 18, [-376, -377, -378, -379], [0, "eaqLJdLjBOYYsCLwfBLjKc", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot3", 18, [-380, -381, -382, -383], [0, "50WmHysytM7qgBmeBN+ita", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot1", 19, [-384, -385, -386, -387], [0, "f9YC+DnENB5ZVt6AcTfZ8u", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot2", 19, [-388, -389, -390, -391], [0, "d5BEGSLIVAsoRKH+nIblcP", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot3", 19, [-392, -393, -394, -395], [0, "fcXp/uFgVLlZPgl8ZVdd2l", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot1", 20, [-396, -397, -398, -399], [0, "fdl1ePE8VNFKqxx4MzJU9c", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot2", 20, [-400, -401, -402, -403], [0, "3foBHgocpDZZ4VWt2x10tb", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot3", 20, [-404, -405, -406, -407], [0, "51m2OSAldObZWCLQusWLd8", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot1", 21, [-408, -409, -410, -411], [0, "62NEZzDQVOc5nfm/Y6JxYb", 1], [5, 160, 150], [0, 148, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot2", 21, [-412, -413, -414, -415], [0, "d0UssCZy5DgIGgf1bcLhuX", 1], [5, 160, 150], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot3", 21, [-416, -417, -418, -419], [0, "b1MbRWuDhMMbhxf8g9bL0u", 1], [5, 160, 150], [0, -152, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnSpin", 5, [[-420, -421, [114, -423, -422]], 1, 1, 4], [0, "b8Wq0/4S5MJrUCx7HQmzAl", 1], [5, 400, 130], [639.323, 3.241, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btnHelp", false, 5, [[40, 1.05, 3, -425, [[11, "27803B0B7ZN+I0Q5PBtYj6/", "helpClicked", 4]], [4, 4294967295], [4, 4294967295], -424, 157], [3, 2, false, -426, [158], 159]], [0, "527Q/n605IJJ+XDzxN7b6q", 1], [5, 230, 92], [793.339, 206.357, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "btnLine", 5, [-429], [-427, -428], [0, "67W4xbWwxMS7X0fcHybWHu", 1], [5, 111, 89], [-306.278, -6.314, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBalance", 6, [[61, "100.000.000", 15, 50, false, false, 3, 1, 1, 2, -430, [187], 188], [17, -431], [128, -432], [129, -433]], [0, "11KeElPB5H46LoNUl65h/4", 1], [5, 200, 50], [353.143, 46.931, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "bg_set_top", 13, [-435, -436], [[19, 2, false, -434, [199]]], [0, "78s1XQ2aJEb7QdvWzJQNAn", 1], [5, 807, 481], [0, 0.5, 1], [92.1, -9.8, 0, 0, 0, 0, 1, 0, 0, 1]], [7, "layout", 55, [-438, -439, -440], [[108, false, 1, 2, -5, -437, [5, 240, 103]]], [0, "e6JXk3DAFIhJ9XYYjB1ERE", 1], [5, 240, 103], [0, -62, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "pice1", 7, [-442, -443], [[20, -441, [231]]], [0, "67fHq7fkxJcpktPlSXnOYs", 1], [5, 70, 41], [10, -27.1, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice2", 7, [-445, -446], [[20, -444, [236]]], [0, "37UxE+BqNEfYrany3czgLm", 1], [5, 84, 52], [6.6, -9.6, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice3", 7, [-448, -449], [[20, -447, [241]]], [0, "09i2HyK2pKXJi+V6szvz3O", 1], [5, 43, 65], [35.9, -6.5, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice4", 7, [-451, -452], [[20, -450, [246]]], [0, "c0RJFhmKlLE4Nnwpbcv2Wc", 1], [5, 50, 84], [6.8, 22.2, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice5", 7, [-454, -455], [[20, -453, [251]]], [0, "39zp+DCmhKhptsjAJuJlZa", 1], [5, 66, 73], [-19, 17.5, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice6", 7, [-457, -458], [[20, -456, [256]]], [0, "eaoSWBOQpPhp9XaVIcVQ2A", 1], [5, 67, 85], [-29, 56, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice7", 7, [-460, -461], [[20, -459, [261]]], [0, "ebJFp8cMVJ/5K/cHBA9Ehh", 1], [5, 87, 50], [8, 55, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice8", 7, [-463, -464], [[20, -462, [266]]], [0, "8as3CnVI1JqrsZo480Bb1Q", 1], [5, 87, 48], [-23, 87, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [7, "pice9", 7, [-466, -467], [[20, -465, [271]]], [0, "18kTmKaWZF+aNWG9dsDLGY", 1], [5, 64, 68], [14, 81, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [44, "xHuView", 1, [25], [[130, 3, -473, 25, -472, [-468, -469, -470, -471]]], [0, "bdHV9JDLJKLa3HxS5miTTT", 1], [5, 190, 190], [0, 0.5, 0.7], [-542, 179, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "offset-message", false, 0, [-475, -476], [[98, 2, false, -474, 465]], [0, "d8V9y1RLhJHrfC9tcAHU2J", 1], [5, 738, 522], [0, 36, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bacground", 9, [[3, 0, false, -477, [0], 1], [59, -478, [4, 4292269782]], [64, -479]], [0, "eeX0x4yjNGQLMXwtY8gBk9", 1], [5, 1561, 732]], [7, "btnBack", 9, [-482], [[41, 3, -481, [[11, "48121xYynhO56qpPvfYDts6", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -480, 11]], [0, "7314qX7r1K9JO8yQMW0Ulv", 1], [5, 140, 140], [-552.916, 349.294, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnSetting", 9, [-485], [[41, 3, -484, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "openSettingClicked", 12]], [4, 4294967295], [4, 4294967295], -483, 26]], [0, "45SX2pvp9MKpMACB083LZR", 1], [5, 140, 140], [540.413, 325.152, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "black", false, 0, 33, [[57, 0, -486, 14], [60, -488, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "closeSettingClicked", 12]], [4, 4292269782], -487]], [0, "6aqgMBHIZL157C9jHZh6IE", 1], [5, 4000, 4000], [-517, -334, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "background", 4, [[3, 0, false, -489, [57], 58], [59, -490, [4, 4292269782]], [64, -491]], [0, "9bopHfcyBPMo5P8IvheUQL", 1], [5, 1561, 732]], [7, "bg_khung", 11, [-493, -494], [[3, 0, false, -492, [67], 68]], [0, "c8TvDoPwtCIqsjHWUsqx2U", 1], [5, 1102, 464], [5, 88, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnAutoSpin", 5, [[-495, [40, 1.05, 3, -497, [[11, "0f7c079iqNP46wGMXF0uuip", "autoSpinClicked", 15]], [4, 4294967295], [4, 4294967295], -496, 149]], 1, 4], [0, "a8zeTMm7NCt4nM+bHSreG1", 1], [5, 400, 130], [639.098, 2.469, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "avenger_bottom", 5, [-499, -500], [[2, -498, [155], 156]], [0, "b4ow2ehHVIdb46Ku2riDSM", 1], [5, 983, 119], [0, 6.3870000000000005, 0, 0, 0, 0, 1, 1, 1, 1]], [87, "btnX2", false, 5, [-501, -502], [0, "cccv0fs5VJuJsjmaigbQ3d", 1], [5, 166, 88], [-604.621, -15.466, 0, 0, 0, 0, 1, 1, 1, 0]], [18, "btnFastSpin", 5, [[-503, [40, 1.05, 3, -505, [[11, "0f7c079iqNP46wGMXF0uuip", "fastSpinClicked", 15]], [4, 4294967295], [4, 4294967295], -504, 171]], 1, 4], [0, "b9exK4k+ZDY4slBz1H7OBn", 1], [5, 101, 52], [404.752, -2.439, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "avenger_top_0_1", 6, [-507, -508], [[2, -506, [181], 182]], [0, "48DviPR4NBxLh4kImsjZNP", 1], [5, 461, 110], [-417.887, 26.805, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnBack", 6, [[-509, [3, 2, false, -510, [183], 184]], 1, 4], [0, "d46hCUXGlHaLajOaox0EIe", 1], [5, 83, 74], [-526.996, 30.99, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHistory", 6, [[41, 3, -512, [[11, "27803B0B7ZN+I0Q5PBtYj6/", "historyClicked", 4]], [4, 4294967295], [4, 4294967295], -511, 189], [19, 2, false, -513, [190]]], [0, "ebMjFS06pHlbNs7wMflZmj", 1], [5, 69, 34], [630.275, -282.372, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_xephang", 6, [[2, -514, [191], 192], [115, -515], [116, 3, -516, [[11, "27803B0B7ZN+I0Q5PBtYj6/", "leaderboardClicked", 4]], [4, 4294967295], [4, 4294967295], 193]], [0, "9dih3kFyhKOa5XN/ywjKqC", 1], [5, 55, 47], [707.963, 7.296, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnSetting", 6, [[41, 3, -518, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "openSettingClicked", 13]], [4, 4294967295], [4, 4294967295], -517, 206], [3, 2, false, -519, [207], 208]], [0, "0c75/WiZFBBIQ/Q8AW1TqD", 1], [5, 82, 85], [556.543, 28.885, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "black", false, 0, 55, [[57, 0, -520, 194], [60, -522, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "closeSettingClicked", 13]], [4, 4292269782], -521]], [0, "a3zDWUU4hBD68lhmXyu9em", 1], [5, 4000, 4000], [-517, -334, 0, 0, 0, 0, 1, 1, 1, 1]], [77, "goldTichLuy", false, 6, [-523, -524], [0, "d623jD9IxPYbBHTHxW7YMY", 1], [515, -226, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeFX", 8, [[2, -525, [222], 223], [28, -526, [225], 224]], [0, "1dJ8pl5dxKDrzCM/wQiyFX", 1], [5, 59, 36], [9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "nv_win", false, 7, [[2, -527, [272], 273], [34, true, -528, [275], 274]], [0, "9a4zeAxzJMwIUUhGoAeURW", 1], [5, 103, 146], [-1, 33, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "nodePice", 7, [-529, -530], [0, "524MnAFKJA7pkoTYdRfN7b", 1], [5, 70, 41], [-537, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "line1", false, 2, [-532], [[3, 2, false, -531, [283], 284]], [0, "eaeE+mJ8BG0K147yEqq4mO", 1], [5, 773, 32], [-15.433, -12, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line2", false, 2, [-534], [[3, 2, false, -533, [287], 288]], [0, "0d00mRDKJEZqHnyhXABhiE", 1], [5, 773, 32], [-15.433, 140, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line3", false, 2, [-536], [[3, 2, false, -535, [291], 292]], [0, "26VgOx1CJDS5oLP74NB+e0", 1], [5, 773, 32], [-15.433, -164, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line4", false, 2, [-538], [[3, 2, false, -537, [295], 296]], [0, "21wwyk/ZRFdKee9QfrXX75", 1], [5, 776, 323], [-13.433, -9, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line5", false, 2, [-540], [[3, 2, false, -539, [299], 300]], [0, "25v5+4fiNGbZyYVIlWv6Sg", 1], [5, 776, 323], [-13.433, -15, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line6", false, 2, [-542], [[3, 2, false, -541, [303], 304]], [0, "aeXBlCETVB1baIaYFwa6Lm", 1], [5, 772, 162], [-16.433, 64, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line7", false, 2, [-544], [[3, 2, false, -543, [307], 308]], [0, "6dNEmO7ixJap3g3K6PoPKx", 1], [5, 772, 162], [-15.433, -88, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line8", false, 2, [-546], [[3, 2, false, -545, [311], 312]], [0, "15ySEqIoZFnK4tqEpOKYKE", 1], [5, 776, 350], [-13.433, 6, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line9", false, 2, [-548], [[3, 2, false, -547, [315], 316]], [0, "58Gbd3s29N2re2EIDqAx9D", 1], [5, 776, 350], [-12.433, -30.9, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line10", false, 2, [-550], [[3, 2, false, -549, [319], 320]], [0, "e0sX9qVYlEXpuwk/jlWT80", 1], [5, 776, 315], [-13.433, -14, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line11", false, 2, [-552], [[3, 2, false, -551, [323], 324]], [0, "c1SELiAhFOObWjivGij7h1", 1], [5, 776, 315], [-13.433, -12, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line12", false, 2, [-554], [[3, 2, false, -553, [327], 328]], [0, "30r6oWESNJWrg80ukbVn0e", 1], [5, 776, 163], [-13.433, 63, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line13", false, 2, [-556], [[3, 2, false, -555, [331], 332]], [0, "11s0cvYipNgr6DInVS2G+z", 1], [5, 776, 163], [-12.633, -88.1, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line14", false, 2, [-558], [[3, 2, false, -557, [335], 336]], [0, "44LG4P9/VI0r7UHVXtH6z6", 1], [5, 776, 201], [81.567, 80.5, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line15", false, 2, [-560], [[3, 2, false, -559, [339], 340]], [0, "06VcRcFoNKdbkcIkEfQas5", 1], [5, 776, 201], [81.567, -105, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line16", false, 2, [-562], [[3, 2, false, -561, [343], 344]], [0, "91zOxlD/pChJM49ie8FrSc", 1], [5, 776, 168], [81.067, 66, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line17", false, 2, [-564], [[3, 2, false, -563, [347], 348]], [0, "51DhJ61dpNsqBz7FoDRnYQ", 1], [5, 776, 168], [80.567, -91, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line18", false, 2, [-566], [[3, 2, false, -565, [351], 352]], [0, "d8ehEfXzJBbpnMt+8V4/Fk", 1], [5, 776, 334], [81.567, -12, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line19", false, 2, [-568], [[3, 2, false, -567, [355], 356]], [0, "b7uCgQI7NDgrWF1dm36+4D", 1], [5, 776, 334], [81.067, -11, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line20", false, 2, [-570], [[3, 2, false, -569, [359], 360]], [0, "85V2aAE3lFzLLkH87I1ED/", 1], [5, 776, 298], [80.567, -18, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line21", false, 2, [-572], [[3, 2, false, -571, [363], 364]], [0, "cczX72Q19H1YTWbxlMF64q", 1], [5, 776, 298], [80.567, -4, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line22", false, 2, [-574], [[3, 2, false, -573, [367], 368]], [0, "2aZY5YEqROR5GLLZ7+J3xU", 1], [5, 776, 313], [81.067, -11, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line23", false, 2, [-576], [[3, 2, false, -575, [371], 372]], [0, "0fBq++yYtH04uu/vUkDKtf", 1], [5, 776, 313], [81.067, -12, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line24", false, 2, [-578], [[3, 2, false, -577, [375], 376]], [0, "beyQ6qk8ZMqYo6yU0BTbSG", 1], [5, 786, 322], [75.567, -7, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "line25", false, 2, [-580], [[3, 2, false, -579, [379], 380]], [0, "29iHK6/JlPpbiq5e9hNLGv", 1], [5, 786, 322], [76.067, -17, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [7, "avenger_bg_jackpot", 11, [-582, -583], [[21, "default", "RUN", 0, false, "RUN", -581, [394], 395]], [0, "84A0sBBrBBSLsCvifSY8dY", 1], [5, 674.39, 381.04], [3.346, 408.656, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbJackpot", 113, [[[61, "0", 22, 80, false, false, 4, 1, 1, 2, -584, [390], 391], -585, [135, -587, -586]], 4, 1, 4], [0, "ccwOXon5BOIr6O5xwcuOZb", 1], [5, 319.81, 55], [-1.346, 26.816, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "popupSlotsView", 1, [67], [[20, -588, [466]], [136, -58, 5, -591, 67, -590, -589, 467, 468]], [0, "f5h40BudRGzYvBTZZ5+hPN", 1], [5, 444, 220]], [7, "avenger_top_0_2", 9, [-593], [[2, -592, [3], 4]], [0, "cbX5gbWcFHlqEaEsolSxN7", 1], [5, 461, 110], [427.171, 314.637, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "avenger_top_0_1", 9, [-595], [[2, -594, [7], 8]], [0, "d6sxytEKZLaYS53GGicKkO", 1], [5, 461, 110], [-427.823, 316.978, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnSound", 34, [-597], [[42, 1.1, 3, -596, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "soundClicked", 12]], [4, 4292269782]]], [0, "dab0q4H7pGu54coeYFV/Hz", 1], [5, 133, 58], [-4, 36.2, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btn\bMusic", 34, [-599], [[42, 1.1, 3, -598, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "musicClicked", 12]], [4, 4292269782]]], [0, "08n8cRfAZH1K0F0FAkDZBj", 1], [5, 133, 58], [-2, -35, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbJackpot1", 27, [[[23, "252512", 13, 70, false, 3, 1, 1, -600, [33], 34], -601], 4, 1], [0, "40iQNwbYNDDLYtV/Ez1vVU", 1], [5, 77.59, 70], [11.177, -140.038, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbJackpot2", 28, [[[23, "3254222", 13, 70, false, 3, 1, 1, -602, [39], 40], -603], 4, 1], [0, "68Y8akNaZHfa0eN9WGoT9C", 1], [5, 94.72, 70], [4.048, -138.794, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbJackpot3", 29, [[[23, "1000", 13, 70, false, 3, 1, 1, -604, [45], 46], -605], 4, 1], [0, "6154s2uxFJeLZrUXZ7pY2/", 1], [5, 53.09, 70], [70.312, -133.827, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbJackpot4", 14, [[[23, "51255414", 14, 70, false, 3, 1, 1, -606, [53], 54], -607], 4, 1], [0, "a9Pscm9stOA4vwR/m2jQy+", 1], [5, 105, 70], [-12.399, -144.594, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "slot0", 17, [-608, -609], [0, "9b4pvkf9ZBPaHWZ4tn2STZ", 1], [5, 160, 150], [0, 298, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "black1", false, 36, [[5, 1, 0, false, -610, 71]], [0, "ccTIyuBWxApqw2m5m6KzUD", 1], [5, 162.5, 150]], [31, "default", "symbolFx", 0, false, "symbolFx", [73]], [4, "black2", false, 36, [[5, 1, 0, false, -611, 74]], [0, "3eFToyocNEyahuXECiHgqX", 1], [5, 162.5, 150]], [4, "black1", false, 37, [[5, 1, 0, false, -612, 75]], [0, "f5CCIWNrJBAJzuq+vCd9cu", 1], [5, 162.5, 150]], [31, "default", "idle", 0, false, "idle", [76]], [4, "black2", false, 37, [[5, 1, 0, false, -613, 77]], [0, "81YnMHdxJJ9ImJu1VpIxXv", 1], [5, 162.5, 150]], [4, "black1", false, 38, [[5, 1, 0, false, -614, 78]], [0, "9154UZyiRCsbPS2TmD78Rs", 1], [5, 162.5, 150]], [31, "default", "idle", 0, false, "idle", [79]], [4, "black2", false, 38, [[5, 1, 0, false, -615, 80]], [0, "a7YFEvfc9Ap5JUmChY7zR5", 1], [5, 162.5, 150]], [12, "slot0", 18, [-616, -617], [0, "efdmQcPYlPT7//vNXMWwb6", 1], [5, 160, 150], [0, 298, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "black1", false, 39, [[5, 1, 0, false, -618, 87]], [0, "dfntPNNClAIIglNmg23H5a", 1], [5, 162.5, 150]], [31, "default", "symbolFx", 0, false, "symbolFx", [88]], [4, "black2", false, 39, [[5, 1, 0, false, -619, 89]], [0, "32qwiYYr9F550JPMHPL4GZ", 1], [5, 162.5, 150]], [4, "black1", false, 40, [[5, 1, 0, false, -620, 90]], [0, "234KcjAdVA+LRTbZXp09mA", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [91]], [4, "black2", false, 40, [[5, 1, 0, false, -621, 92]], [0, "c7T0eaac5Gya8TT8JL9TqP", 1], [5, 162.5, 150]], [4, "black1", false, 41, [[5, 1, 0, false, -622, 93]], [0, "85R0PCCeZOWI9i8vBGO6cu", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [94]], [4, "black2", false, 41, [[5, 1, 0, false, -623, 95]], [0, "13bz9DPWJEyKaLOgLW2+re", 1], [5, 162.5, 150]], [12, "slot0", 19, [-624, -625], [0, "d8uBc7gW9H5o+ZlMs+gmLc", 1], [5, 160, 150], [0, 298, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "black1", false, 42, [[99, 1, 0, false, -626, [102], 103]], [0, "c9c0n9DTRCWa8wBmTf7tEG", 1], [5, 162.5, 150]], [31, "default", "symbolFx", 0, false, "symbolFx", [104]], [4, "black2", false, 42, [[5, 1, 0, false, -627, 105]], [0, "13gfQtH5BF7rLX2MkiUg3R", 1], [5, 162.5, 150]], [4, "black1", false, 43, [[5, 1, 0, false, -628, 106]], [0, "c68JX1jPRH4YMhEZfWsrvn", 1], [5, 162.5, 150]], [131, 0, false, "animation", [107]], [4, "black2", false, 43, [[5, 1, 0, false, -629, 108]], [0, "f1jJ9Xz45F7KzNsC1Whq9F", 1], [5, 162.5, 150]], [4, "black1", false, 44, [[5, 1, 0, false, -630, 109]], [0, "9dvkYV3FdOiLKylqfL4Vr6", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [110]], [4, "black2", false, 44, [[5, 1, 0, false, -631, 111]], [0, "bcIw9app5DI5Tn5ECz9IM7", 1], [5, 162.5, 150]], [12, "slot0", 20, [-632, -633], [0, "34Dh6zTGhH9pcxmf0VHKfI", 1], [5, 160, 150], [0, 298, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "black1", false, 45, [[5, 1, 0, false, -634, 118]], [0, "cdsvuipdVNVYMKUDpZO+6F", 1], [5, 162.5, 150]], [31, "default", "symbolFx", 0, false, "symbolFx", [119]], [4, "black2", false, 45, [[5, 1, 0, false, -635, 120]], [0, "923qDHy4BPSIJ1Ah7knq7X", 1], [5, 162.5, 150]], [4, "black1", false, 46, [[5, 1, 0, false, -636, 121]], [0, "c286BtTPxAaJNvSdVCi2ui", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [122]], [4, "black2", false, 46, [[5, 1, 0, false, -637, 123]], [0, "07GUohNRpI0IlK2jyztv32", 1], [5, 162.5, 150]], [4, "black1", false, 47, [[5, 1, 0, false, -638, 124]], [0, "80i43wv9ZCQJ2MroaYpxlG", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [125]], [4, "black2", false, 47, [[5, 1, 0, false, -639, 126]], [0, "a8MxDps/BIcLj5p52WwW0H", 1], [5, 162.5, 150]], [12, "slot0", 21, [-640, -641], [0, "e5wiSjoQlLWpFUdiO2GwV8", 1], [5, 160, 150], [0, 298, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "black1", false, 48, [[5, 1, 0, false, -642, 133]], [0, "5foxmHLlBG/I9Nb+wB59OV", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [134]], [4, "black2", false, 48, [[5, 1, 0, false, -643, 135]], [0, "c11AHsXIlDloS9Wzuoj3aE", 1], [5, 162.5, 150]], [4, "black1", false, 49, [[5, 1, 0, false, -644, 136]], [0, "bcn9g1yW1BpaPfAEuHEMWp", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [137]], [4, "black2", false, 49, [[5, 1, 0, false, -645, 138]], [0, "2dY6tkYrROvK7H3RrRcSUQ", 1], [5, 162.5, 150]], [4, "black1", false, 50, [[5, 1, 0, false, -646, 139]], [0, "3avjtqex1NiKkA67yds2jE", 1], [5, 162.5, 150]], [24, "default", 0, false, "animation", [140]], [4, "black2", false, 50, [[5, 1, 0, false, -647, 141]], [0, "22XrW7IMhOTbiAcN1OqmTc", 1], [5, 162.5, 150]], [18, "lbTotalBetVal", 5, [[[62, "0", 10, 34, false, false, 1, 1, -648, [172], 173], -649], 4, 1], [0, "c9jovfufhDeYAcMqkxfl+H", 1], [5, 12.19, 34], [-296.04, -15.828, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btn_cuoc", 5, [-651], [[100, -650, [164]]], [0, "80eoLYdh9BpbPoYDyz7zTl", 1], [5, 234, 87], [-117.43, -35.148, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "label", 175, [[[23, "100", 10, 34, false, 3, 1, 1, -652, [162], 163], -653], 4, 1], [0, "59YQ8znGNJoIhhxzO7ObYD", 1], [5, 31.81, 34], [-13.927, 21.788, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbWinVal", 5, [[[62, "0", 20, 34, false, false, 1, 1, -654, [167], 168], -655], 4, 1], [0, "04lgQ8t7FLroU1bR4OjbJ7", 1], [5, 24.38, 34], [155.054, 23.146, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "avenger_btn_heltp", 78, [[2, -656, [176], 177], [40, 1.05, 3, -657, [[11, "27803B0B7ZN+I0Q5PBtYj6/", "helpClicked", 4]], [4, 4294967295], [4, 4294967295], 52, 178]], [0, "48mcBM8yBKyJYUKEskTmng", 1], [5, 230, 92], [55.905, 0.717, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnSound", 56, [-659], [[42, 1.1, 3, -658, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "soundClicked", 13]], [4, 4292269782]]], [0, "46VW84AGFLq73AWLxO838P", 1], [5, 133, 58], [0, -15.3, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btn\bMusic", 56, [-661], [[42, 1.1, 3, -660, [[11, "dcd4akrIzFOe4glwzM/CGWZ", "musicClicked", 13]], [4, 4292269782]]], [0, "f5xCjDmsJBf6x2Cuela/gZ", 1], [5, 133, 58], [0, -77.6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnLeaderboard", false, 6, [-663], [[117, -662, [[11, "27803B0B7ZN+I0Q5PBtYj6/", "destroyLeaderboardView", 4]]]], [0, "0fdt3Q/dVMJrFb9a0uU033", 1], [5, 134, 155], [-881.564, 15.939, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "progress-nv", 8, [-664, -665], [0, "e3RBP+iEFONoZya/kWhHCf", 1], [5, 103, 146], [-1, 33, 0, 0, 0, 0, 1, 1, 1, 1]], [78, "lbTotalPrize", false, 8, [[119, 22, 28, false, 1, 2, 1, -666, 218], [17, -667]], [0, "bdCM8rKzNHEbpYNbAhk/jr", 1], [4, 4280494683], [5, 103.2, 28], [1.3, -72.8, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "lbTotalPrize", 8, [[[120, 22, 23, false, 1, 1, 2, -668, [219], 220], -669], 4, 1], [0, "a85EBceAZH/qoelA1bw1eX", 1], [4, 4280494683], [5, 100.71, 23], [2.7, -93.2, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "lb<PERSON><PERSON>l", 8, [[-670, [17, -671]], 1, 4], [0, "fb09qBirVFKIsCEE9vcWRJ", 1], [4, 4280494683], [5, 100.71, 23], [3, 154.5, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "avenger_bg_play_trial", false, 113, [[101, 1, 0, -672, [392], 393], [22, 1.05, 3, -673, [[29, "48121xYynhO56qpPvfYDts6", "roomClicked", "0", 1]], [4, 4294967295], [4, 4294967295], 35]], [0, "43wIUeTgVPx73swglDDSal", 1], [5, 228, 63], [2.956, -60.055, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [6, "logoFs", false, 11, [-675], [[25, 0, -674, [397], 398]], [0, "181N37pd5Jq7pAnUIzICWD", 1], [5, 287, 36], [-27, -107, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "black", 1, 22, [[25, 0, -676, [399], 400], [35, -677, [[11, "6b10eLy4IxKZqah/4LLbcrh", "continueClicked", 30]], [4, 4292269782]]], [0, "f7yyeWprZP1aIR54WwxVrV", 1], [5, 3000, 3000]], [18, "lbWin", 22, [[[30, "1.000.00", 50, 70, false, 1, 1, -678, [404], 405], -679], 4, 1], [0, "e06k8FUixN2YE+n9KrU0Zy", 1], [5, 314.19, 70], [-8.618, -7.537, 0, 0, 0, 0, 1, 1, 1, 1]], [79, "black", false, 100, 23, [[25, 0, -680, [408], 409], [35, -681, [[11, "6b10eLy4IxKZqah/4LLbcrh", "continueClicked", 30]], [4, 4292269782]]], [0, "32Sh523PxPK4R+5bBSerRl", 1], [5, 3000, 3000]], [18, "lbWin", 23, [[[63, "0", 70, false, 1, 1, -682, [413], 414], -683], 4, 1], [0, "0c8X2kK5hN34b5KG+xJupe", 1], [5, 48.75, 70], [7.097, -248.414, 0, 0, 0, 0, 1, 1, 1, 1]], [82, "lbWin", 32, [[[30, "100.000.000", 36, 70, false, 1, 1, -684, [419], 420], -685], 4, 1], [0, "82wZGhTHVJSa83m69Jsb4z", 1], [5, 325.63, 70]], [45, "black", 1, 24, [[25, 0, -686, [423], 424], [35, -687, [[11, "6b10eLy4IxKZqah/4LLbcrh", "continueClicked", 30]], [4, 4292269782]]], [0, "8c7I51O7RDBI1TC8Rq3wgW", 1], [5, 3000, 3000]], [18, "lbWin", 24, [[[63, "0", 70, false, 1, 1, -688, [428], 429], -689], 4, 1], [0, "124wekoM5D35S9bDj9uAXo", 1], [5, 48.75, 70], [0, -194, 0, 0, 0, 0, 1, 1, 1, 1]], [83, "logo", 25, [[-690, [34, true, -691, [448], 447]], 1, 4], [0, "b6ZhP9Ti9KBLX/zm5iTlVU", 1], [5, 90, 44], [0, 0.5, 1], [0, 58.1, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "lbRoom", 26, [-693], [[30, "100:", 20, 50, false, 1, 1, -692, [450], 451]], [0, "3649A0WchGd6BDUlaCNbJg", 1], [4, 4278255615], [5, 40, 50], [0, 0, 0.5], [-73.5, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "lbRoom", 26, [-695], [[30, "1K:", 20, 50, false, 1, 1, -694, [453], 454]], [0, "31M+JBJVNAmqoGqmluMJM4", 1], [4, 4278255615], [5, 30, 50], [0, 0, 0.5], [-73.5, -115, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "lbRoom", 26, [-697], [[30, "5K:", 20, 50, false, 1, 1, -696, [456], 457]], [0, "c2boSrmvtPtpaEsafPGJR0", 1], [4, 4278255615], [5, 30, 50], [0, 0, 0.5], [-73.5, -180, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "lbRoom", 26, [-699], [[30, "10K:", 20, 50, false, 1, 1, -698, [459], 460]], [0, "0cDmMUb/9JXZy86nx/1Aye", 1], [4, 4278255615], [5, 41.5, 50], [0, 0, 0.5], [-73.5, -245, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "close", 25, [[2, -700, [463], 464], [35, -701, [[11, "5c1fa7YnkBEMqpU9n30ksJn", "onClickHide", 66]], [4, 4292269782]]], [0, "0bu7+RY39G6ZS05qjRoIpt", 1], [5, 45, 45], [-80, 13, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "musicBackground", 3, [-702], [0, "92oxGrfJJHFZAc7Sv4+ekx", 1]], [137, 0.8, true, true, 201], [13, "normalWin", 3, [-703], [0, "65G7EsPgtJn7EaspCX+FJG", 1]], [16, true, 203], [13, "bigWin", 3, [-704], [0, "9aURY2mtNNH50JeYNA1hYa", 1]], [16, true, 205], [13, "getBonus", 3, [-705], [0, "7a2HSkyUNHlqL2QN8b3cwF", 1]], [16, true, 207], [13, "spin", 3, [-706], [0, "15eF8TSsVPf4c4WV3VPijl", 1]], [16, true, 209], [13, "stopSpin1", 3, [-707], [0, "13NON7RklN3rKdyRc1af1u", 1]], [16, true, 211], [13, "stopSpin2", 3, [-708], [0, "b4iKvn3nlPuo3jjZLv7JA7", 1]], [16, true, 213], [13, "stopSpin3", 3, [-709], [0, "64lXpEH3JNvZVPsSZRf2gQ", 1]], [16, true, 215], [13, "stopSpin4", 3, [-710], [0, "2btA/JfR5A2KkbuTcePxAq", 1]], [16, true, 217], [13, "stopSpin5", 3, [-711], [0, "35T2YzUYlL2oZcPU19oSff", 1]], [16, true, 219], [13, "bonus_click", 3, [-712], [0, "daDNDepxtFVpGe8Kpd0imf", 1]], [16, true, 221], [13, "bonus_miss", 3, [-713], [0, "f8YQLBW05LI6LDKkDoizWI", 1]], [16, true, 223], [13, "bonus_win", 3, [-714], [0, "4cA2OVkvlKLaALd9AgMXfV", 1]], [16, true, 225], [13, "open_card", 3, [-715], [0, "6awbMasQNNU5n8+bi2KY+C", 1]], [16, true, 227], [13, "minigame_activate", 3, [-716], [0, "0e8Cd1ylVAda3n17aDpEiN", 1]], [16, true, 229], [13, "x2_click", 3, [-717], [0, "bd4tPuEexHTrHgswVgoDiq", 1]], [16, true, 231], [13, "x2_win", 3, [-718], [0, "4d1fbL5JJOooGyf6qkyUS6", 1]], [16, true, 233], [13, "expand_wild", 3, [-719], [0, "a76V0gtXBDZ7aS0fYGauDK", 1]], [16, true, 235], [1, "New Label", 116, [[121, "xxxx", 1, 1, -720, [2]]], [0, "f48BMubnNJqqO1KfU4+tL/", 1], [5, 80, 50.4], [-84.972, 9.997, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "avenger_btn_lsgd", 117, [[2, -721, [5], 6]], [0, "dacqKG2a5EmKokirxu0rep", 1], [5, 18, 37], [171.831, -9.311, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 69, [[3, 2, false, -722, [9], 10]], [0, "73G1emkcNDTJAC/KWfmFJO", 1], [5, 83, 74], [13.3, -28.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 70, [[3, 2, false, -723, [12], 13]], [0, "359R79dyhG0ISkE8RZy4qj", 1], [5, 82, 85], [-2.5, -7, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "sound", 118, [-724], [0, "26anQifkZPGKU4PXB2Xw2h", 1], [5, 140, 58]], [19, 2, false, 241, [15]], [1, "bg_set_line", 34, [[2, -725, [16], 17]], [0, "5aspUyNphE2LXn1i6FVAca", 1], [5, 807, 481], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "music", 119, [-726], [0, "958QyexAJIFKDCLJWA2tQT", 1], [5, 133, 58]], [19, 2, false, 244, [18]], [20, 12, [20, 21]], [26, "name", false, 10, [[2, -727, [27], 28]], [0, "05gCsYJ4ZFD48kuJNaZ7Gv", 1], [5, 650, 199], [-70.1, 310.9, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sprite", 35, [[21, "default", "animation", 0, false, "animation", -728, [29], 30]], [0, "cezaOE7HhEd6HdWNE0k4t/", 1], [5, 400, 100]], [22, 1.05, 3, 35, [[29, "48121xYynhO56qpPvfYDts6", "roomClicked", "0", 1]], [4, 4294967295], [4, 4294967295], 35], [1, "sprite", 27, [[21, "default", "RUN", 0, false, "RUN", -729, [31], 32]], [0, "4ePLWCmSdJMoxxeuvbXREi", 1], [5, 466.02, 607], [9.88, -271.693, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [17, 120], [1, "lbRoom", 27, [[23, "100", 14, 70, false, 4, 1, 1, -730, [35], 36]], [0, "496DiovAxEtpLLrEQNhJNk", 1], [5, 46.94, 70], [23.553, -84.555, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 1.05, 3, 27, [[29, "48121xYynhO56qpPvfYDts6", "roomClicked", "1", 1]], [4, 4294967295], [4, 4294967295], 27], [1, "p100_ani", 28, [[21, "default", "RUN", 0, false, "RUN", -731, [37], 38]], [0, "d2voHD4k1EfLGiIj51au+j", 1], [5, 446.77, 607], [7.993, -272.198, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [17, 121], [1, "lbRoom", 28, [[23, "1000", 14, 70, false, 2, 1, 1, -732, [41], 42]], [0, "eb4tONwT1MHaQP94eksleO", 1], [5, 54.56, 70], [13.646, -84.655, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 1.05, 3, 28, [[29, "48121xYynhO56qpPvfYDts6", "roomClicked", "2", 1]], [4, 4294967295], [4, 4294967295], 28], [1, "p5k_ani", 29, [[21, "default", "RUN", 0, false, "RUN", -733, [43], 44]], [0, "b6DpX+YwlIJYJjKWCAXA3g", 1], [5, 576.63, 605], [66.894, -263.247, 0, 0, 0, 0, 1, 0.9, 0.9, 0.9]], [17, 122], [1, "lbRoom", 29, [[23, "5000", 14, 70, false, 2, 1, 1, -734, [47], 48]], [0, "fdoU6gg3dAD69TSnjMWNsa", 1], [5, 58.5, 70], [77.785, -76.882, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 1.05, 3, 29, [[29, "48121xYynhO56qpPvfYDts6", "roomClicked", "3", 1]], [4, 4294967295], [4, 4294967295], 29], [1, "SET", 14, [[132, "default", "RUN", 0, false, "RUN", 1, -735, [49], 50]], [0, "77AhNJBaJGM4eHm/R2LUwp", 1], [5, 437.84, 605], [-13.132, -291.823, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "phong10000", 14, [[21, "default", "animation", 0, false, "animation", -736, [51], 52]], [0, "958YbwCa1C+7IzOrg1tIJF", 1], [5, 394.86, 493.59], [-0.714, -64.74, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 123], [1, "lbRoom", 14, [[23, "1,000", 14, 70, false, 2, 1, 1, -737, [55], 56]], [0, "c1d0OfrsVHk4BOiWp+UrWB", 1], [5, 54.56, 70], [-7.484, -82.88, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 1.05, 3, 14, [[29, "48121xYynhO56qpPvfYDts6", "roomClicked", "4", 1]], [4, 4294967295], [4, 4294967295], 14], [1, "animation_olympus", 4, [[133, "default", "animation", 0, "animation", -738, [59], 60]], [0, "93GpeYpk5I8odx8L9rrntc", 1], [5, 303, 398.49], [675.884, -109.496, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "phong10000", 4, [[21, "default", "animation", 0, false, "animation", -739, [61], 62]], [0, "57MciaEbtGUaue6TJRqYxT", 1], [5, 394.86, 493.59], [-650.178, -213.759, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "avenger_line_numb_1", 73, [[2, -740, [63], 64]], [0, "99yqs3cahAYZK1Atnu862G", 1], [5, 86, 83], [531.717, -1, 0, 0, 0, 0, 1, 1.25, 1, 1]], [1, "avenger_line_numb_1", 73, [[2, -741, [65], 66]], [0, "c3fi/HYCpNkYkRuAMCQa19", 1], [5, 86, 83], [-525.02, 3.728, 0, 0, 0, 0, 1, 1.25, 1, 1.25]], [14, "icon", false, 124, [-742], [0, "fe1PLUcatKkp8RupcW1fK0", 1], [5, 123, 119]], [19, 2, false, 271, [69]], [8, "skeleton", 124, [-743], [0, "59fjHLc81JoJ3MG9CKuLmd", 1], [5, 137, 152.48], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [134, "default", "idle", 0, false, "idle", 273, [70]], [14, "icon", false, 36, [-744], [0, "29jOPKbFZHAK8VgW24ysqM", 1], [5, 123, 119]], [19, 2, false, 275, [72]], [8, "skeleton", 36, [126], [0, "7ctf/xMjBDu4VRedjg47e4", 1], [5, 137, 152.48], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 37, [-745], [0, "57/+67WdtJbr5PB+bhG6oh", 1], [5, 123, 119]], [15, 2, false, 278], [8, "skeleton", 37, [129], [0, "47ZJ/iu2lPWrAAM8WtqRoF", 1], [5, 137, 152.48], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 38, [-746], [0, "b4McyhSYREroHnxcL9VcTU", 1], [5, 123, 119]], [15, 2, false, 281], [8, "skeleton", 38, [132], [0, "19fmBWimdF67wg1C92eZbH", 1], [5, 137, 152.48], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 17, [272, 276, 279, 282], [274, 126, 129, 132]], [14, "icon", false, 134, [-747], [0, "198zttwMBNKInPIRCy+wRA", 1], [5, 123, 119]], [15, 2, false, 285], [8, "skeleton", 134, [-748], [0, "f55Qgat/5G3p0XjRuAI9r4", 1], [5, 151, 132.72], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "default", 0, false, "animation", 287, [86]], [14, "icon", false, 39, [-749], [0, "dcOg059R5D5qkVVeb6Q008", 1], [5, 123, 119]], [15, 2, false, 289], [8, "skeleton", 39, [136], [0, "248/6nk8RGnYCSOSt2xfzp", 1], [5, 441.09, 401.94], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 40, [-750], [0, "762Ct4BT1HIaXy5JYrfATt", 1], [5, 123, 119]], [15, 2, false, 292], [8, "skeleton", 40, [139], [0, "4dw4ZOQstJnLvHVy3N60bH", 1], [5, 155.61, 131], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 41, [-751], [0, "a2uoIKckdDRoTsMsre1JXb", 1], [5, 123, 119]], [15, 2, false, 295], [8, "skeleton", 41, [142], [0, "d2sjqG3YFC86e0fRYNSt6U", 1], [5, 155.61, 131], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 18, [286, 290, 293, 296], [288, 136, 139, 142]], [14, "icon", false, 144, [-752], [0, "54Z2xXP6tH4YzRoUQMpcxw", 1], [5, 123, 119]], [15, 2, false, 299], [8, "skeleton", 144, [-753], [0, "e1oPBuXPdLGJRxihhS+oYK", 1], [5, 151, 132.72], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "default", 0, false, "animation", 301, [101]], [14, "icon", false, 42, [-754], [0, "b52O1DQOFAi79fqqORJ4q9", 1], [5, 123, 119]], [15, 2, false, 303], [8, "skeleton", 42, [146], [0, "45M7urZZVPVLTatpCnySiT", 1], [5, 145, 167], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 43, [-755], [0, "d8Sjck31VE9a1fJptCivWI", 1], [5, 123, 119]], [15, 2, false, 306], [8, "skeleton", 43, [149], [0, "bdMjgf5jtG1asOPXYsC/Kb", 1], [5, 150.5, 144.26], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 44, [-756], [0, "20Lu/Bq7NDSZq7MNnOYblS", 1], [5, 123, 119]], [15, 2, false, 309], [8, "skeleton", 44, [152], [0, "e1UGN+NMVIJpYoIswcNt7m", 1], [5, 150.5, 144.26], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 19, [300, 304, 307, 310], [302, 146, 149, 152]], [14, "icon", false, 154, [-757], [0, "e6CNjmgthAMLgK4avpRLpx", 1], [5, 123, 119]], [15, 2, false, 313], [8, "skeleton", 154, [-758], [0, "2dMEv6HtJOXZimPFq8DlRX", 1], [5, 151, 132.72], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "default", 0, false, "animation", 315, [117]], [14, "icon", false, 45, [-759], [0, "96MPstR5lOcIBZZtBmFUA1", 1], [5, 123, 119]], [15, 2, false, 317], [8, "skeleton", 45, [156], [0, "35WxSojAVPRpDjNxzqgjnZ", 1], [5, 378.46, 387.42], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 46, [-760], [0, "3awTD9N4VCMZnSU8tiLgLA", 1], [5, 123, 119]], [15, 2, false, 320], [8, "skeleton", 46, [159], [0, "0fSwdcCmJPnazPn7eLzImJ", 1], [5, 139, 131.06], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 47, [-761], [0, "2bvFk/vjlFnb1hSKpb2j+X", 1], [5, 123, 119]], [15, 2, false, 323], [8, "skeleton", 47, [162], [0, "3fRN2wrCBEEJzajuEUP2WX", 1], [5, 139, 131.06], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 20, [314, 318, 321, 324], [316, 156, 159, 162]], [14, "icon", false, 164, [-762], [0, "ffp+TguMdE8INVL1pbWmXy", 1], [5, 123, 119]], [15, 2, false, 327], [8, "skeleton", 164, [-763], [0, "afkEmJluhE2JAPoFx9x2bk", 1], [5, 151, 132.72], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "default", 0, false, "animation", 329, [132]], [14, "icon", false, 48, [-764], [0, "1d5b4YhGdCLYd5+gqN1s8Z", 1], [5, 123, 119]], [15, 2, false, 331], [8, "skeleton", 48, [166], [0, "67F/ZbGchKv4fm2c4ei+gA", 1], [5, 151, 132.72], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 49, [-765], [0, "dfhuKvjPpEHah1lu0snB6t", 1], [5, 123, 119]], [15, 2, false, 334], [8, "skeleton", 49, [169], [0, "bdP6V2kspL0pvaM4IGzASq", 1], [5, 151, 132.72], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "icon", false, 50, [-766], [0, "f1M/K5j5xK1LOBRnplQV0k", 1], [5, 123, 119]], [15, 2, false, 337], [8, "skeleton", 50, [172], [0, "95XaZ7WKBK8Kmik/vKhD1I", 1], [5, 151, 132.72], [0, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 21, [328, 332, 335, 338], [330, 166, 169, 172]], [138, 15, [284, 298, 312, 326, 340]], [17, 174], [19, 0, false, 74, [148]], [19, 0, false, 51, [150]], [118, 1.05, 3, 51, [4, 4294967295], [4, 4294967295], 51], [1, "avenger_bg_thongTin", 75, [[21, "default", "avenger_bg_thongTin", 0, false, "avenger_bg_thongTin", -767, [151], 152]], [0, "d1fzwURopJXbuiVpoowBB7", 1], [5, 362, 85], [144.619, 2.808, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "avenger_bg_so_phong", 75, [[2, -768, [153], 154]], [0, "67NSWGA2lE3JXlpzhfCtaI", 1], [5, 189, 94], [-121.601, -15.348, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "label", 53, [-769], [0, "baYr3O4+xH/LbjXGpE5kfo", 1], [5, 24.38, 34], [-6.198, 28.656, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "25", 10, 34, false, 1, 1, 348, [160]], [19, 2, false, 53, [161]], [22, 1.1, 3, 53, [[11, "27803B0B7ZN+I0Q5PBtYj6/", "betLinesClicked", 4]], [4, 4294967295], [4, 4294967295], 53], [17, 176], [26, "box_cuoc", false, 5, [[2, -770, [165], 166]], [0, "284LsDzWVKv5iFyBGxePce", 1], [5, 336, 89], [-620.308, 312.442, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 177], [19, 2, false, 76, [169]], [22, 1.05, 3, 76, [[11, "0f7c079iqNP46wGMXF0uuip", "x2Clicked", 15]], [4, 4294967295], [4, 4294967295], 76], [19, 2, false, 77, [170]], [122, "#1", 16, 50, false, 1, [214]], [33, "lbSessionID", 6, [358], [0, "72qSAaAl5LLL+cWwwtyEO7", 1], [5, 19.99, 63], [0, 0, 0.5], [-646.557, -608.692, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "avenger_top_0_2", 6, [[2, -771, [174], 175]], [0, "30dvJtILJKDLx0GqZxDdbd", 1], [5, 461, 110], [430.159, 24.225, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "avenger_btn_lsgd", 78, [[2, -772, [179], 180]], [0, "260zbvrFRIk4im8Hwi0YV6", 1], [5, 18, 37], [171.99, -8.179, 0, 0, 0, 0, 1, 1, 1, 1]], [22, 0.9, 3, 79, [[11, "27803B0B7ZN+I0Q5PBtYj6/", "backClicked", 4]], [4, 4294967295], [4, 4294967295], 79], [26, "coin", false, 6, [[2, -773, [185], 186]], [0, "b9XRpFGZhCdo5WQ6sj1+Bz", 1], [5, 48, 48], [-146.672, 38.659, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [8, "sound", 179, [-774], [0, "097ejC2idC9J7XFBHEKBq1", 1], [5, 140, 58], [0, 12.3, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 2, false, 364, [195]], [1, "bg_set_line", 56, [[25, 0, -775, [196], 197]], [0, "87igsvaEpLCpEjJ34OHNll", 1], [5, 600, 400], [0, -41.8, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "music", 180, [-776], [0, "eazgMFOn1PpLJz8DI40wDM", 1], [5, 133, 58], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 2, false, 367, [198]], [20, 13, [200, 201]], [10, "icon1-0", 181, [[2, -777, [209], 210]], [0, "e5NHMewdlCIpQDjU7kU8r6", 1], [5, 134, 155]], [13, "gold", 84, [-778], [0, "f7siMKjnxFOJi5amhTjHtF", 1]], [139, 1, true, 52, 47.272727272727266, 1.1, 0.4, 146, 180, 0, 50, 35, 7, 720, 1, 81, 23, 0, 371, [211], [4, 4284914175], [4, 422772736], [4, 4281545727], [4, 0], [0, -284, 370]], [45, "icon_gold", 250, 84, [[2, -779, [212], 213]], [0, "7bQ1bDzoFFG4sV99OIXpCY", 1], [5, 39, 37]], [1, "nv_off", 8, [[2, -780, [215], 216]], [0, "cf3enNQoxHv6g9GMAiUpcW", 1], [5, 103, 146], [-1, 31, 0, 0, 0, 0, 1, 0.98, 0.98, 1]], [102, 3, 1, 182, [217]], [143, 2, 0, 182, 375], [17, 184], [88, "lbTime", 8, [-781], [0, "2dl5cik2dDxJNfDf+B5KVV", 1], [4, 4291029493], [5, 0, 40], [0, -159.2, 0, 0, 0, 0, 1, 1, 1, 1]], [123, 16, false, 378, [221]], [124, 22, 23, false, 1, 1, 2, 185, [226]], [10, "on", 57, [[2, -782, [227], 228]], [0, "2b/jy54rFO2LtjDBl8ZECb", 1], [5, 70, 41]], [10, "off", 57, [[2, -783, [229], 230]], [0, "dcMW0LShJFmrBDKjbMjWSu", 1], [5, 70, 41]], [10, "on", 58, [[2, -784, [232], 233]], [0, "46qwm+3Q1Nmbc8Hlh4jNRi", 1], [5, 83, 52]], [10, "off", 58, [[2, -785, [234], 235]], [0, "c8qwHf00xPfIneHxQwdu0D", 1], [5, 84, 52]], [10, "on", 59, [[2, -786, [237], 238]], [0, "d4fpXir+RNCor+6O4iZsYB", 1], [5, 43, 61]], [10, "off", 59, [[2, -787, [239], 240]], [0, "e49KdofRhM7LWtuUmL2NYO", 1], [5, 43, 61]], [10, "on", 60, [[2, -788, [242], 243]], [0, "ceTXiarNpHGIebnnhY8DpP", 1], [5, 50, 84]], [10, "off", 60, [[2, -789, [244], 245]], [0, "79uLwnSDBNN5aqGz7A/M66", 1], [5, 50, 84]], [10, "on", 61, [[2, -790, [247], 248]], [0, "dfBsBPIrxBv6CMd53c6vug", 1], [5, 66, 73]], [10, "off", 61, [[2, -791, [249], 250]], [0, "8fDObX1dBIwoGelOd+I/Js", 1], [5, 66, 73]], [10, "on", 62, [[2, -792, [252], 253]], [0, "80NxMX65VArLsOylRIry8T", 1], [5, 67, 85]], [10, "off", 62, [[2, -793, [254], 255]], [0, "edJvWxK79CQZdLwHSxwGq3", 1], [5, 67, 85]], [10, "on", 63, [[2, -794, [257], 258]], [0, "8cTQUOsmNGrL+p3hLBnqo4", 1], [5, 87, 50]], [10, "off", 63, [[2, -795, [259], 260]], [0, "c3D7j5shdJbLWwcmfi/LVV", 1], [5, 87, 50]], [10, "on", 64, [[2, -796, [262], 263]], [0, "5cpZVMRZFJ9aomxAmFJIBW", 1], [5, 87, 48]], [10, "off", 64, [[2, -797, [264], 265]], [0, "43J0ThbcRN8L16bxNfrOIR", 1], [5, 87, 48]], [10, "on", 65, [[2, -798, [267], 268]], [0, "360fZJ0zFD0qC30k+AQEjz", 1], [5, 64, 68]], [10, "off", 65, [[2, -799, [269], 270]], [0, "40HNFAqX1G8JPaiVTghuC+", 1], [5, 64, 68]], [13, "particle", 87, [-800], [0, "9bgrfbeCtKF4t+mGER2GEO", 1]], [140, 1, 289, 262.7272727272727, 1.1, 0.4, 146, 180, 38, 10, 5, 720, 1, 81, 23, 0, 399, [276], [4, 4284914175], [4, 422772736], [4, 4281545727], [4, 0], [0, -6, 55]], [10, "pice", 87, [[25, 2, -801, [277], 278]], [0, "fd6MpvvjFOnrPBGXFydMC7", 1], [5, 84, 52]], [1, "New Label", 88, [[9, "1", 15, false, 1, 1, -802, [281], 282]], [0, "593NA3mk1LSaUUap+KXIOz", 1], [5, 13.59, 40], [-371.068, 15.163, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 89, [[9, "2", 15, false, 1, 1, -803, [285], 286]], [0, "82r2k+M4hL3LAqLfIPQh5k", 1], [5, 19.22, 40], [-371.068, 15.163, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 90, [[9, "3", 15, false, 1, 1, -804, [289], 290]], [0, "59Y3Q6aQ5EO5GZl4Fu+x4D", 1], [5, 17.81, 40], [-371.068, 15.163, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 91, [[9, "4", 15, false, 1, 1, -805, [293], 294]], [0, "ef4aKVt6BAQas6P+DQbA8/", 1], [5, 19.22, 40], [-374.822, 144.064, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 92, [[9, "5", 15, false, 1, 1, -806, [297], 298]], [0, "552+gi1OVDbp8LGXfCaYkP", 1], [5, 17.81, 40], [-372.319, 142.813, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 93, [[9, "6", 15, false, 1, 1, -807, [301], 302]], [0, "72Bx4wc5lFeLW6LOAMMgM9", 1], [5, 18.28, 40], [-370.442, -33.644, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 94, [[9, "7", 15, false, 1, 1, -808, [305], 306]], [0, "edg4LG0cFGD780vYng5N34", 1], [5, 16.88, 40], [-369.478, -34.642, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 95, [[9, "8", 15, false, 1, 1, -809, [309], 310]], [0, "905wB3/3xBCpob34BZjLq/", 1], [5, 19.22, 40], [-371.068, 172.567, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 96, [[9, "9", 15, false, 1, 1, -810, [313], 314]], [0, "9cqL5dSctCdJb9gkXZCM/C", 1], [5, 18.28, 40], [-373.285, 174.045, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 97, [[9, "10", 15, false, 1, 1, -811, [317], 318]], [0, "e1QSUFaFxAlZa60bBIJA6o", 1], [5, 31.41, 40], [-372.546, -40.261, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 98, [[9, "11", 15, false, 1, 1, -812, [321], 322]], [0, "fdDrP+8EhMs702Ud/vxsiC", 1], [5, 26.72, 40], [-371.807, -38.783, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 99, [[9, "12", 15, false, 1, 1, -813, [325], 326]], [0, "a9rQdZ8oFHPpnnWUTT+0oa", 1], [5, 32.34, 40], [-369.548, 37.196, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 100, [[9, "13", 15, false, 1, 1, -814, [329], 330]], [0, "4fSe8PkblEGIB9Jl/yyCQa", 1], [5, 30.94, 40], [-371.598, 37.416, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 101, [[9, "14", 15, false, 1, 1, -815, [333], 334]], [0, "acscYDHpxDs5HapzL/9V76", 1], [5, 32.34, 40], [371.453, 99.153, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 102, [[9, "15", 15, false, 1, 1, -816, [337], 338]], [0, "e3lp79K0FJm5NLD31S/0Ao", 1], [5, 30.94, 40], [371.453, 99.153, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 103, [[9, "16", 15, false, 1, 1, -817, [341], 342]], [0, "ffDsgFupJEHqkC+yBYlOMK", 1], [5, 31.41, 40], [372.326, -47.467, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 104, [[9, "17", 15, false, 1, 1, -818, [345], 346]], [0, "a7SJl821NAkriCcRdsxMIk", 1], [5, 30, 40], [374.944, -50.085, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 105, [[9, "18", 15, false, 1, 1, -819, [349], 350]], [0, "0aQeNP5BZLLpSi/eP+nz3r", 1], [5, 32.34, 40], [375.817, 165.481, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 106, [[9, "19", 15, false, 1, 1, -820, [353], 354]], [0, "b57MwDlSRJYZcjh8XSlTUL", 1], [5, 31.41, 40], [373.67, 167.14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 107, [[9, "20", 15, false, 1, 1, -821, [357], 358]], [0, "28ez6m3y1F46AaYLmNB2fX", 1], [5, 37.03, 40], [375.105, -116.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 108, [[9, "21", 15, false, 1, 1, -822, [361], 362]], [0, "761J4vdeBN7KPJwCB2CpH9", 1], [5, 32.34, 40], [371.453, -117.517, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 109, [[9, "22", 15, false, 1, 1, -823, [365], 366]], [0, "3bfUU4cdBEMqsHWuCpf4PY", 1], [5, 37.97, 40], [373.956, 54.726, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 110, [[9, "23", 15, false, 1, 1, -824, [369], 370]], [0, "47JfXXh85Bn4FKebmpGjYT", 1], [5, 36.56, 40], [372.079, 55.977, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 111, [[9, "24", 15, false, 1, 1, -825, [373], 374]], [0, "b1cloiX+hIm64ujIsNNf/t", 1], [5, 37.97, 40], [378.843, -78.204, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 112, [[9, "25", 15, false, 1, 1, -826, [377], 378]], [0, "23xmX6SPtOprBtKFJy8WA+", 1], [5, 36.56, 40], [376.626, -78.943, 0, 0, 0, 0, 1, 1, 1, 1]], [80, "accumulationView", 11, [[144, -827, 376, 379, 377, 84, 85, 380, [57, 58, 59, 60, 61, 62, 63, 64, 65], [381, 382, 383, 384, 385, 386, 387, 388, 389], 87, 86, 400, 372]], [0, "eaqhYN/HtBEZt91AjJ0yIi", 1], [-25, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 114], [89, "lbFreeSpin", 187, [-828], [0, "bayzeWDrtMFIV28BCBBkfH", 1], [5, 200, 40], [0, 0, 0, 0, 0, -1.2246467991473532e-16, 1, 1, 1, 1], [1, 0, 0, -1.4033418597069754e-14]], [125, "6 LƯỢT MIỄN PHÍ", 20, false, 1, 1, 1, 429, [396]], [1, "NH_dra", 22, [[21, "default", "disappear", 0, false, "disappear", -829, [401], 402]], [0, "704SfCnrJBIrhFiGgDVaPl", 1], [5, 1245, 1275], [0, -89, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "particleNoHu", 22, [-830], [0, "70x87dB+tEob4LJ5RYc5s9", 1], [0, 316, 0, 0, 0, 0, 1, 1, 1, 1]], [141, 277, 184.66666666666666, 1.5, 0.5, 0, 0, 40, -1, 51, 500, 1, 0, 337, 0, 50, {"custom": false}, 432, [403], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 608, 1], [0, 0, -906]], [17, 189], [46, "particleBigWin", 23, [-831], [0, "e2v25uQUFJjpIvd00KM7yM", 1], [0, 53, 0, 0, 0, 0, 1, 1, 1, 1]], [65, 100, 1.5, 0.5, 58, 40, -1, 51, 500, 1, 850, 383, 0, 50, {"custom": false}, 435, [410], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 101, 73], [0, 0, -2400]], [1, "TL_ani", 23, [[21, "default", "avenger_bigwin", 0, false, "avenger_bigwin", -832, [411], 412]], [0, "3bNLjtAp5KBIFkRxMJd2jO", 1], [5, 1134.81, 826.6], [0, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [17, 191], [46, "particleWin", 32, [-833], [0, "22jqGZ1v1KE7XJoRGzwitd", 1], [9, -36, 0, 0, 0, 0, 1, 1, 1, 1]], [142, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, {"custom": false}, 439, [417], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [4, "bg_Th", false, 32, [[103, false, -834, 418]], [0, "b1/L9AMyVArIhgZUB3IxZV", 1], [5, 640, 135]], [17, 192], [13, "particleBigWin", 24, [-835], [0, "acfCpOl65D1qB36jWp3Lzh", 1]], [65, 100, 1.5, 0.5, 58, 40, -1, 51, 500, 1, 850, 383, 0, 50, {"custom": false}, 443, [425], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 101, 73], [0, 0, -2400]], [10, "<PERSON><PERSON><PERSON><PERSON>_ani", 24, [[21, "default", "START", 0, false, "START", -836, [426], 427]], [0, "897o7FU/9EYr8VEkQo5b41", 1], [5, 658.03, 718.45]], [17, 194], [111, true, 24, [430]], [19, 2, false, 195, [446]], [33, "lbRemain", 196, [-837], [0, "b0svGwHOlEkbsE87LNhl9B", 1], [5, 102.5, 50], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "CÒN 26 HŨ", 20, 50, false, 1, 1, 449, [449]], [33, "lbRemain", 197, [-838], [0, "660bFnLRVKm66ahp67GF9b", 1], [5, 102.5, 50], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "CÒN 26 HŨ", 20, 50, false, 1, 1, 451, [452]], [33, "lbRemain", 198, [-839], [0, "3fEMbtrm5F07ICB7mx6G9p", 1], [5, 91, 50], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "CÒN 2 HŨ", 20, 50, false, 1, 1, 453, [455]], [33, "lbRemain", 199, [-840], [0, "19EC+3F3tPdYmdYFTSSOs/", 1], [5, 91, 50], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "CÒN 2 HŨ", 20, 50, false, 1, 1, 455, [458]], [35, 25, [[11, "5c1fa7YnkBEMqpU9n30ksJn", "openEventClicked", 66]], [4, 4292269782]], [8, "lbMessage", 67, [-841], [0, "db7HNXCpBDY7ILvT11GVSb", 1], [5, 620, 50], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [126, "<PERSON><PERSON><PERSON> mừng bạn đã được", 50, false, 1, 1, 3, 458], [8, "lbWin", 67, [-842], [0, "1flsoFVlxLoLabIi9X/cIQ", 1], [5, 57.5, 100], [0, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [127, "0", 80, false, 1, 1, 460]], 0, [0, 15, 1, 0, 0, 1, 0, -1, 253, 0, -2, 257, 0, -3, 266, 0, -4, 261, 0, -5, 249, 0, 16, 4, 0, 17, 9, 0, 0, 1, 0, -1, 3, 0, -2, 9, 0, -3, 4, 0, -4, 66, 0, -5, 115, 0, -1, 127, 0, -2, 137, 0, -3, 147, 0, -4, 157, 0, -5, 167, 0, -6, 130, 0, -7, 140, 0, -8, 150, 0, -9, 160, 0, -10, 170, 0, -11, 133, 0, -12, 143, 0, -13, 153, 0, -14, 163, 0, -15, 173, 0, -1, 125, 0, -2, 135, 0, -3, 145, 0, -4, 155, 0, -5, 165, 0, -6, 128, 0, -7, 138, 0, -8, 148, 0, -9, 158, 0, -10, 168, 0, -11, 131, 0, -12, 141, 0, -13, 151, 0, -14, 161, 0, -15, 171, 0, -1, 126, 0, -2, 136, 0, -3, 146, 0, -4, 156, 0, -5, 166, 0, -6, 129, 0, -7, 139, 0, -8, 149, 0, -9, 159, 0, -10, 169, 0, -11, 132, 0, -12, 142, 0, -13, 152, 0, -14, 162, 0, -15, 172, 0, -1, 88, 0, -2, 89, 0, -3, 90, 0, -4, 91, 0, -5, 92, 0, -6, 93, 0, -7, 94, 0, -8, 95, 0, -9, 96, 0, -10, 97, 0, -11, 98, 0, -12, 99, 0, -13, 100, 0, -14, 101, 0, -15, 102, 0, -16, 103, 0, -17, 104, 0, -18, 105, 0, -19, 106, 0, -20, 107, 0, -21, 108, 0, -22, 109, 0, -23, 110, 0, -24, 111, 0, -25, 112, 0, 0, 2, 0, -1, 88, 0, -2, 89, 0, -3, 90, 0, -4, 91, 0, -5, 92, 0, -6, 93, 0, -7, 94, 0, -8, 95, 0, -9, 96, 0, -10, 97, 0, -11, 98, 0, -12, 99, 0, -13, 100, 0, -14, 101, 0, -15, 102, 0, -16, 103, 0, -17, 104, 0, -18, 105, 0, -19, 106, 0, -20, 107, 0, -21, 108, 0, -22, 109, 0, -23, 110, 0, -24, 111, 0, -25, 112, 0, 18, 236, 0, 19, 234, 0, 20, 232, 0, 21, 230, 0, 22, 228, 0, 23, 226, 0, 24, 224, 0, 25, 222, 0, 26, 220, 0, 27, 218, 0, 28, 216, 0, 29, 214, 0, 30, 212, 0, 31, 210, 0, 32, 208, 0, 33, 206, 0, 34, 204, 0, 35, 202, 0, 0, 3, 0, -1, 201, 0, -2, 203, 0, -3, 205, 0, -4, 207, 0, -5, 209, 0, -6, 211, 0, -7, 213, 0, -8, 215, 0, -9, 217, 0, -10, 219, 0, -11, 221, 0, -12, 223, 0, -13, 225, 0, -14, 227, 0, -15, 229, 0, -16, 231, 0, -17, 233, 0, -18, 235, 0, 0, 4, 0, 36, 355, 0, 37, 357, 0, 38, 343, 0, 39, 344, 0, 40, 350, 0, 41, 345, 0, 42, 356, 0, 43, 351, 0, 44, 362, 0, 0, 4, 0, 45, 430, 0, 0, 4, 0, -1, 72, 0, -2, 267, 0, -3, 268, 0, -4, 11, 0, -5, 6, 0, -6, 5, 0, -7, 30, 0, -8, 13, 0, -1, 74, 0, -2, 51, 0, -3, 75, 0, -4, 52, 0, -5, 53, 0, -6, 175, 0, -7, 353, 0, -8, 174, 0, -9, 177, 0, -10, 76, 0, -11, 77, 0, -1, 360, 0, -2, 78, 0, -3, 79, 0, -4, 359, 0, -5, 363, 0, -6, 54, 0, -7, 80, 0, -8, 81, 0, -9, 82, 0, -10, 181, 0, -11, 84, 0, -1, 57, 0, -2, 58, 0, -3, 59, 0, -4, 60, 0, -5, 61, 0, -6, 62, 0, -7, 63, 0, -8, 64, 0, -9, 65, 0, -10, 86, 0, -11, 87, 0, 0, 8, 0, -1, 374, 0, -2, 182, 0, -3, 183, 0, -4, 184, 0, -5, 378, 0, -6, 85, 0, -7, 185, 0, -1, 68, 0, -2, 116, 0, -3, 117, 0, -4, 69, 0, -5, 70, 0, -6, 10, 0, -7, 12, 0, 0, 10, 0, -1, 251, 0, -2, 255, 0, -3, 259, 0, -4, 264, 0, 0, 10, 0, -1, 247, 0, -2, 35, 0, -3, 27, 0, -4, 28, 0, -5, 29, 0, -6, 14, 0, -1, 73, 0, -2, 15, 0, -5, 427, 0, -6, 113, 0, 8, 11, 0, -8, 187, 0, -1, 246, 0, 11, 245, 0, 12, 242, 0, 13, 33, 0, 14, 246, 0, 0, 12, 0, -1, 33, 0, -1, 369, 0, 11, 368, 0, 12, 365, 0, 13, 55, 0, 14, 369, 0, 0, 13, 0, -1, 55, 0, -1, 266, 0, -1, 262, 0, -2, 263, 0, -3, 123, 0, -4, 265, 0, 0, 15, 0, -2, 341, 0, -1, 16, 0, 0, 16, 0, -1, 17, 0, -2, 18, 0, -3, 19, 0, -4, 20, 0, -5, 21, 0, 0, 17, 0, -2, 284, 0, -1, 124, 0, -2, 36, 0, -3, 37, 0, -4, 38, 0, 0, 18, 0, -2, 298, 0, -1, 134, 0, -2, 39, 0, -3, 40, 0, -4, 41, 0, 0, 19, 0, -2, 312, 0, -1, 144, 0, -2, 42, 0, -3, 43, 0, -4, 44, 0, 0, 20, 0, -2, 326, 0, -1, 154, 0, -2, 45, 0, -3, 46, 0, -4, 47, 0, 0, 21, 0, -2, 340, 0, -1, 164, 0, -2, 48, 0, -3, 49, 0, -4, 50, 0, 0, 22, 0, -1, 188, 0, -2, 431, 0, -3, 432, 0, -4, 189, 0, 0, 23, 0, -1, 190, 0, -2, 435, 0, -3, 437, 0, -4, 191, 0, -1, 447, 0, -1, 193, 0, -2, 443, 0, -3, 445, 0, -4, 194, 0, 46, 457, 0, 0, 25, 0, -2, 457, 0, -1, 195, 0, -2, 26, 0, -3, 200, 0, 0, 26, 0, 0, 26, 0, -1, 196, 0, -2, 197, 0, -3, 198, 0, -4, 199, 0, -1, 253, 0, -1, 250, 0, -2, 120, 0, -3, 252, 0, -1, 257, 0, -1, 254, 0, -2, 121, 0, -3, 256, 0, -1, 261, 0, -1, 258, 0, -2, 122, 0, -3, 260, 0, 47, 447, 0, 48, 446, 0, 49, 444, 0, -1, 434, 0, -2, 438, 0, -3, 442, 0, 50, 440, 0, 51, 436, 0, 52, 433, 0, 53, 32, 0, 54, 31, 0, 0, 30, 0, -1, 31, 0, -3, 32, 0, 0, 32, 0, -1, 439, 0, -2, 441, 0, -3, 192, 0, 0, 33, 0, -1, 71, 0, -2, 34, 0, 0, 34, 0, -1, 118, 0, -2, 243, 0, -3, 119, 0, -1, 249, 0, -1, 248, 0, -1, 125, 0, -2, 275, 0, -3, 277, 0, -4, 127, 0, -1, 128, 0, -2, 278, 0, -3, 280, 0, -4, 130, 0, -1, 131, 0, -2, 281, 0, -3, 283, 0, -4, 133, 0, -1, 135, 0, -2, 289, 0, -3, 291, 0, -4, 137, 0, -1, 138, 0, -2, 292, 0, -3, 294, 0, -4, 140, 0, -1, 141, 0, -2, 295, 0, -3, 297, 0, -4, 143, 0, -1, 145, 0, -2, 303, 0, -3, 305, 0, -4, 147, 0, -1, 148, 0, -2, 306, 0, -3, 308, 0, -4, 150, 0, -1, 151, 0, -2, 309, 0, -3, 311, 0, -4, 153, 0, -1, 155, 0, -2, 317, 0, -3, 319, 0, -4, 157, 0, -1, 158, 0, -2, 320, 0, -3, 322, 0, -4, 160, 0, -1, 161, 0, -2, 323, 0, -3, 325, 0, -4, 163, 0, -1, 165, 0, -2, 331, 0, -3, 333, 0, -4, 167, 0, -1, 168, 0, -2, 334, 0, -3, 336, 0, -4, 170, 0, -1, 171, 0, -2, 337, 0, -3, 339, 0, -4, 173, 0, -1, 344, 0, -2, 345, 0, 55, 341, 0, 0, 51, 0, 9, 52, 0, 0, 52, 0, 0, 52, 0, -1, 350, 0, -2, 351, 0, -1, 348, 0, 0, 54, 0, 0, 54, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, -1, 83, 0, -2, 56, 0, 0, 56, 0, -1, 179, 0, -2, 366, 0, -3, 180, 0, 0, 57, 0, -1, 381, 0, -2, 382, 0, 0, 58, 0, -1, 383, 0, -2, 384, 0, 0, 59, 0, -1, 385, 0, -2, 386, 0, 0, 60, 0, -1, 387, 0, -2, 388, 0, 0, 61, 0, -1, 389, 0, -2, 390, 0, 0, 62, 0, -1, 391, 0, -2, 392, 0, 0, 63, 0, -1, 393, 0, -2, 394, 0, 0, 64, 0, -1, 395, 0, -2, 396, 0, 0, 65, 0, -1, 397, 0, -2, 398, 0, -1, 450, 0, -2, 452, 0, -3, 454, 0, -4, 456, 0, 56, 448, 0, 0, 66, 0, 0, 67, 0, -1, 458, 0, -2, 460, 0, 0, 68, 0, 0, 68, 0, 0, 68, 0, 9, 69, 0, 0, 69, 0, -1, 239, 0, 9, 70, 0, 0, 70, 0, -1, 240, 0, 0, 71, 0, 9, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, -1, 269, 0, -2, 270, 0, -1, 343, 0, 9, 74, 0, 0, 74, 0, 0, 75, 0, -1, 346, 0, -2, 347, 0, -1, 355, 0, -2, 356, 0, -1, 357, 0, 9, 77, 0, 0, 77, 0, 0, 78, 0, -1, 178, 0, -2, 361, 0, -1, 362, 0, 0, 79, 0, 9, 80, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 81, 0, 9, 82, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 9, 83, 0, 0, 83, 0, -1, 371, 0, -2, 373, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, -1, 399, 0, -2, 401, 0, 0, 88, 0, -1, 402, 0, 0, 89, 0, -1, 403, 0, 0, 90, 0, -1, 404, 0, 0, 91, 0, -1, 405, 0, 0, 92, 0, -1, 406, 0, 0, 93, 0, -1, 407, 0, 0, 94, 0, -1, 408, 0, 0, 95, 0, -1, 409, 0, 0, 96, 0, -1, 410, 0, 0, 97, 0, -1, 411, 0, 0, 98, 0, -1, 412, 0, 0, 99, 0, -1, 413, 0, 0, 100, 0, -1, 414, 0, 0, 101, 0, -1, 415, 0, 0, 102, 0, -1, 416, 0, 0, 103, 0, -1, 417, 0, 0, 104, 0, -1, 418, 0, 0, 105, 0, -1, 419, 0, 0, 106, 0, -1, 420, 0, 0, 107, 0, -1, 421, 0, 0, 108, 0, -1, 422, 0, 0, 109, 0, -1, 423, 0, 0, 110, 0, -1, 424, 0, 0, 111, 0, -1, 425, 0, 0, 112, 0, -1, 426, 0, 0, 113, 0, -1, 114, 0, -2, 186, 0, 0, 114, 0, -2, 428, 0, 57, 428, 0, 0, 114, 0, 0, 115, 0, 58, 461, 0, 59, 459, 0, 0, 115, 0, 0, 116, 0, -1, 237, 0, 0, 117, 0, -1, 238, 0, 0, 118, 0, -1, 241, 0, 0, 119, 0, -1, 244, 0, 0, 120, 0, -2, 251, 0, 0, 121, 0, -2, 255, 0, 0, 122, 0, -2, 259, 0, 0, 123, 0, -2, 264, 0, -1, 271, 0, -2, 273, 0, 0, 125, 0, 0, 127, 0, 0, 128, 0, 0, 130, 0, 0, 131, 0, 0, 133, 0, -1, 285, 0, -2, 287, 0, 0, 135, 0, 0, 137, 0, 0, 138, 0, 0, 140, 0, 0, 141, 0, 0, 143, 0, -1, 299, 0, -2, 301, 0, 0, 145, 0, 0, 147, 0, 0, 148, 0, 0, 150, 0, 0, 151, 0, 0, 153, 0, -1, 313, 0, -2, 315, 0, 0, 155, 0, 0, 157, 0, 0, 158, 0, 0, 160, 0, 0, 161, 0, 0, 163, 0, -1, 327, 0, -2, 329, 0, 0, 165, 0, 0, 167, 0, 0, 168, 0, 0, 170, 0, 0, 171, 0, 0, 173, 0, 0, 174, 0, -2, 342, 0, 0, 175, 0, -1, 176, 0, 0, 176, 0, -2, 352, 0, 0, 177, 0, -2, 354, 0, 0, 178, 0, 0, 178, 0, 0, 179, 0, -1, 364, 0, 0, 180, 0, -1, 367, 0, 0, 181, 0, -1, 370, 0, -1, 375, 0, -2, 376, 0, 0, 183, 0, 0, 183, 0, 0, 184, 0, -2, 377, 0, -1, 380, 0, 0, 185, 0, 0, 186, 0, 0, 186, 0, 0, 187, 0, -1, 429, 0, 0, 188, 0, 0, 188, 0, 0, 189, 0, -2, 434, 0, 0, 190, 0, 0, 190, 0, 0, 191, 0, -2, 438, 0, 0, 192, 0, -2, 442, 0, 0, 193, 0, 0, 193, 0, 0, 194, 0, -2, 446, 0, -1, 448, 0, 0, 195, 0, 0, 196, 0, -1, 449, 0, 0, 197, 0, -1, 451, 0, 0, 198, 0, -1, 453, 0, 0, 199, 0, -1, 455, 0, 0, 200, 0, 0, 200, 0, -1, 202, 0, -1, 204, 0, -1, 206, 0, -1, 208, 0, -1, 210, 0, -1, 212, 0, -1, 214, 0, -1, 216, 0, -1, 218, 0, -1, 220, 0, -1, 222, 0, -1, 224, 0, -1, 226, 0, -1, 228, 0, -1, 230, 0, -1, 232, 0, -1, 234, 0, -1, 236, 0, 0, 237, 0, 0, 238, 0, 0, 239, 0, 0, 240, 0, -1, 242, 0, 0, 243, 0, -1, 245, 0, 0, 247, 0, 0, 248, 0, 0, 250, 0, 0, 252, 0, 0, 254, 0, 0, 256, 0, 0, 258, 0, 0, 260, 0, 0, 262, 0, 0, 263, 0, 0, 265, 0, 0, 267, 0, 0, 268, 0, 0, 269, 0, 0, 270, 0, -1, 272, 0, -1, 274, 0, -1, 276, 0, -1, 279, 0, -1, 282, 0, -1, 286, 0, -1, 288, 0, -1, 290, 0, -1, 293, 0, -1, 296, 0, -1, 300, 0, -1, 302, 0, -1, 304, 0, -1, 307, 0, -1, 310, 0, -1, 314, 0, -1, 316, 0, -1, 318, 0, -1, 321, 0, -1, 324, 0, -1, 328, 0, -1, 330, 0, -1, 332, 0, -1, 335, 0, -1, 338, 0, 0, 346, 0, 0, 347, 0, -1, 349, 0, 0, 353, 0, 0, 360, 0, 0, 361, 0, 0, 363, 0, -1, 365, 0, 0, 366, 0, -1, 368, 0, 0, 370, 0, -1, 372, 0, 0, 373, 0, 0, 374, 0, -1, 379, 0, 0, 381, 0, 0, 382, 0, 0, 383, 0, 0, 384, 0, 0, 385, 0, 0, 386, 0, 0, 387, 0, 0, 388, 0, 0, 389, 0, 0, 390, 0, 0, 391, 0, 0, 392, 0, 0, 393, 0, 0, 394, 0, 0, 395, 0, 0, 396, 0, 0, 397, 0, 0, 398, 0, -1, 400, 0, 0, 401, 0, 0, 402, 0, 0, 403, 0, 0, 404, 0, 0, 405, 0, 0, 406, 0, 0, 407, 0, 0, 408, 0, 0, 409, 0, 0, 410, 0, 0, 411, 0, 0, 412, 0, 0, 413, 0, 0, 414, 0, 0, 415, 0, 0, 416, 0, 0, 417, 0, 0, 418, 0, 0, 419, 0, 0, 420, 0, 0, 421, 0, 0, 422, 0, 0, 423, 0, 0, 424, 0, 0, 425, 0, 0, 426, 0, 0, 427, 0, -1, 430, 0, 0, 431, 0, -1, 433, 0, -1, 436, 0, 0, 437, 0, -1, 440, 0, 0, 441, 0, -1, 444, 0, 0, 445, 0, -1, 450, 0, -1, 452, 0, -1, 454, 0, -1, 456, 0, -1, 459, 0, -1, 461, 0, 60, 1, 2, 8, 11, 7, 8, 8, 8, 8, 11, 22, 8, 31, 23, 8, 31, 24, 8, 31, 25, 8, 66, 67, 8, 115, 126, 0, 277, 129, 0, 280, 132, 0, 283, 136, 0, 291, 139, 0, 294, 142, 0, 297, 146, 0, 305, 149, 0, 308, 152, 0, 311, 156, 0, 319, 159, 0, 322, 162, 0, 325, 166, 0, 333, 169, 0, 336, 172, 0, 339, 341, 61, 352, 341, 62, 349, 341, 63, 358, 341, 64, 354, 341, 65, 342, 358, 0, 359, 842], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 202, 204, 206, 208, 210, 212, 214, 216, 218, 220, 222, 224, 226, 228, 230, 232, 234, 236, 242, 245, 272, 274, 276, 126, 279, 129, 282, 132, 286, 288, 290, 136, 293, 139, 296, 142, 300, 302, 304, 146, 307, 149, 310, 152, 314, 316, 318, 156, 321, 159, 324, 162, 328, 330, 332, 166, 335, 169, 338, 172, 343, 344, 345, 349, 351, 355, 356, 357, 365, 368, 372, 372, 358, 375, 379, 380, 400, 400, 430, 433, 433, 436, 436, 440, 440, 444, 444, 447, 448, 450, 452, 454, 456, 459, 461], [-1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, 6, -1, 1, 1, -1, -1, 1, -1, -1, -1, -2, -1, -2, -1, -2, 6, -1, 1, -1, 4, -1, 4, -1, 3, -1, 3, -1, 4, -1, 3, -1, 3, -1, 4, -1, 3, -1, 3, -1, 4, -1, 4, -1, 3, -1, 3, -1, 1, -1, 4, -1, 4, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, 1, -1, 1, 1, -1, 1, 7, -1, -2, -3, -4, -1, 1, -1, 1, 1, -1, 1, 1, -1, 1, 7, -1, -2, -3, -4, -1, -1, 1, -1, 1, 1, -1, 1, 1, -1, 1, 7, -1, -2, -3, -4, -1, 1, -1, 1, 1, -1, 1, 1, -1, 1, 7, -1, -2, -3, -4, -1, 1, -1, 1, 1, -1, 1, 1, -1, 1, 7, -1, -2, -3, -4, -1, -1, 6, -1, -1, 4, -1, 1, -1, 1, 6, -1, 1, -1, -1, -1, 3, -1, -1, 1, -1, 3, -1, -1, 6, -1, 3, -1, 1, -1, 1, 6, -1, 1, -1, 1, -1, 1, -1, 1, -1, 3, 6, -1, -1, 1, 6, 1, -1, -1, 1, -1, -1, -1, -2, -1, -2, -1, -2, 6, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 3, -1, 3, -1, -1, 1, 7, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, 1, 7, -1, -1, -1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -1, 3, -1, 1, -1, 4, -1, -1, 1, -1, 1, -1, 4, -1, -1, 3, 7, -1, -1, 1, -1, -1, 4, -1, 3, 7, -1, -1, 1, -1, 3, 7, -1, -1, 1, -1, -1, 4, -1, 3, -1, 66, 67, 68, 69, 70, 71, 72, -1, -2, -1, -2, -1, -2, -1, -2, -1, 7, -1, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, 1, -1, 1, 1, -1, 73, 74, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 1, 1, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 4, 1, 1, 6, 3, 6, 1, 6, 1, 1, 1, 10, 1, 3, 1, 3, 3, 10, 1, 3, 10, 1, 10, 1, 10, 1, 10, 1, 7, 1, 3, 3, 3, 3, 3, 3], [0, 101, 0, 0, 28, 0, 29, 0, 30, 0, 31, 5, 0, 32, 2, 0, 0, 33, 0, 0, 34, 35, 17, 36, 18, 37, 5, 0, 102, 3, 103, 3, 104, 0, 1, 0, 1, 3, 105, 0, 1, 0, 1, 3, 106, 0, 1, 0, 1, 3, 107, 3, 38, 0, 1, 0, 1, 0, 108, 3, 109, 3, 38, 0, 39, 0, 39, 0, 110, 0, 3, 2, 0, 3, 2, 2, 3, 2, 2, 3, 2, 6, 6, 11, 12, 13, 3, 2, 3, 2, 2, 3, 2, 2, 3, 2, 6, 6, 11, 12, 13, 3, 0, 2, 3, 2, 2, 3, 2, 2, 3, 2, 6, 6, 11, 12, 13, 3, 2, 3, 2, 2, 3, 2, 2, 3, 2, 6, 6, 11, 12, 13, 3, 2, 3, 2, 2, 3, 2, 2, 3, 2, 6, 6, 11, 12, 13, 0, 0, 5, 0, 3, 111, 0, 112, 0, 113, 5, 0, 40, 0, 0, 0, 1, 0, 0, 114, 0, 1, 0, 0, 5, 0, 1, 0, 28, 0, 40, 5, 0, 29, 0, 30, 0, 31, 0, 115, 0, 1, 5, 0, 0, 116, 5, 2, 0, 0, 33, 0, 0, 34, 35, 17, 36, 18, 37, 5, 0, 32, 0, 117, 0, 0, 118, 0, 0, 119, 0, 14, 0, 14, 0, 0, 120, 41, 41, 0, 0, 42, 0, 121, 7, 0, 24, 0, 122, 7, 0, 43, 0, 123, 7, 0, 44, 0, 124, 7, 0, 45, 0, 125, 7, 0, 46, 0, 46, 7, 0, 47, 0, 126, 7, 0, 48, 0, 127, 7, 0, 49, 0, 128, 7, 0, 129, 50, 50, 0, 0, 24, 0, 130, 0, 1, 0, 25, 0, 1, 0, 25, 0, 1, 0, 25, 0, 1, 0, 51, 0, 1, 0, 51, 0, 1, 0, 52, 0, 1, 0, 52, 0, 1, 0, 53, 0, 1, 0, 53, 0, 1, 0, 54, 0, 1, 0, 54, 0, 1, 0, 55, 0, 1, 0, 55, 0, 1, 0, 56, 0, 1, 0, 56, 0, 1, 0, 57, 0, 1, 0, 57, 0, 1, 0, 58, 0, 1, 0, 58, 0, 1, 0, 59, 0, 1, 0, 59, 0, 1, 0, 60, 0, 1, 0, 60, 0, 1, 0, 61, 0, 1, 0, 61, 42, 24, 43, 44, 45, 131, 47, 48, 49, 0, 1, 0, 132, 3, 133, 0, 0, 134, 0, 2, 3, 135, 0, 0, 1, 8, 8, 0, 2, 0, 3, 136, 0, 1, 8, 8, 0, 137, 0, 1, 8, 8, 0, 2, 0, 3, 138, 0, 1, 8, 139, 140, 141, 142, 143, 144, 145, 62, 19, 19, 19, 63, 146, 64, 147, 0, 65, 65, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 148, 0, 149, 26, 150, 26, 26, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 27, 20, 21, 9, 9, 21, 20, 27, 9, 21, 20, 162, 163, 164, 165, 166, 16, 16, 16, 16, 16, 66, 167, 67, 168, 169, 66, 67, 170, 17, 18, 4, 9, 4, 9, 4, 9, 4, 9, 4, 10, 4, 21, 4, 68, 4, 68, 4, 10, 4, 20, 4, 69, 4, 69, 4, 10, 4, 27, 4, 70, 4, 70, 4, 10, 4, 10, 4, 10, 4, 10, 19, 62, 5, 1, 5, 64, 5, 63, 17, 18, 171, 172, 14, 173, 15, 14, 174, 175, 176, 177, 22, 71, 22, 178, 22, 71, 22, 8, 179, 23, 23, 23, 23, 14, 180]], [[{"name": "avenger_symbol_1_4", "rect": [0, 0, 144, 157], "offset": [0, 0], "originalSize": [144, 157], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [181]], [[{"name": "line_16_17", "rect": [0, 0, 776, 168], "offset": [0, 0], "originalSize": [776, 168], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [182]], [[{"name": "avenger_bg_play_trial", "rect": [0, 0, 228, 63], "offset": [0, 0], "originalSize": [228, 63], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [183]], [[{"name": "Layer1_on", "rect": [0, 0, 70, 41], "offset": [0, 0], "originalSize": [70, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [184]], [[{"name": "BUTTON SPIN2", "rect": [3, 0, 404, 125], "offset": [1.5, 5], "originalSize": [407, 135], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [185]], [[{"name": "line_6_7", "rect": [0, 0, 772, 162], "offset": [0, 0], "originalSize": [772, 162], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [186]], [[{"name": "line_24_25", "rect": [0, 0, 786, 322], "offset": [0, 0], "originalSize": [786, 322], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [187]], [[{"name": "avenger_symbol_1_10", "rect": [0, 0, 144, 109], "offset": [0, 0], "originalSize": [144, 109], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [188]], [[{"name": "avenger_btn_bangthuong", "rect": [0, 0, 230, 92], "offset": [0, 0], "originalSize": [230, 92], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [189]], [[{"name": "coin", "rect": [16, 7, 48, 48], "offset": [0, -1], "originalSize": [80, 60], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [190]], [[{"name": "avenger_btn_sieutoc2", "rect": [0, 0, 130, 68], "offset": [0, 0], "originalSize": [130, 68], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [191]], [[[47, "activePice", 1.5, {"paths": {"off": {"props": {"opacity": [{"frame": 0, "value": 255}, {"frame": 0.5, "value": 0}, {"frame": 1.5, "value": 255}]}}}}]], 0, 0, [], [], []], [[{"name": "Layer9_on", "rect": [0, 0, 64, 68], "offset": [0, 0], "originalSize": [64, 68], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [192]], [[{"name": "nv_win", "rect": [0, 0, 103, 146], "offset": [0, 0], "originalSize": [103, 146], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [193]], [[{"name": "avenger_symbol_1_3", "rect": [0, 0, 144, 152], "offset": [0, 0], "originalSize": [144, 152], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [194]], [[{"name": "line_22_23", "rect": [0, 0, 776, 313], "offset": [0, 0], "originalSize": [776, 313], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [195]], [[{"name": "avenger_bg_so_phong", "rect": [0, 0, 189, 94], "offset": [0, 0], "originalSize": [189, 94], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [196]], [[{"name": "btn_x2_off", "rect": [1, 0, 164, 88], "offset": [0, 0], "originalSize": [166, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [197]], [[{"name": "<PERSON><PERSON><PERSON><PERSON>", "rect": [0, 0, 200, 405], "offset": [0, 0], "originalSize": [200, 405], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [198]], [[{"name": "Layer3_on", "rect": [0, 0, 43, 61], "offset": [0, 2], "originalSize": [43, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [199]], [[{"name": "Layer2_on", "rect": [1, 0, 83, 52], "offset": [0.5, 0], "originalSize": [84, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [200]], [[{"name": "line_8_9", "rect": [0, 0, 776, 350], "offset": [0, 0], "originalSize": [776, 350], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [201]], [[{"name": "line_14_15", "rect": [0, 0, 776, 201], "offset": [0, 0], "originalSize": [776, 201], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [202]], [[{"name": "Layer4", "rect": [0, 0, 50, 84], "offset": [0, 0], "originalSize": [50, 84], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [203]], [[{"name": "nv_fx", "rect": [0, 0, 59, 36], "offset": [0, 0], "originalSize": [59, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [204]], [[{"name": "Layer3", "rect": [0, 0, 43, 61], "offset": [0, 2], "originalSize": [43, 65], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [205]], [[{"name": "name", "rect": [2, 0, 650, 199], "offset": [1, 0.5], "originalSize": [652, 200], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [206]], [[{"name": "nv_off", "rect": [0, 0, 103, 146], "offset": [0, 0], "originalSize": [103, 146], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [207]], [[{"name": "music_off", "rect": [0, 0, 140, 58], "offset": [0, 0], "originalSize": [140, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [208]], [[{"name": "line_18_19", "rect": [0, 0, 776, 334], "offset": [0, 0], "originalSize": [776, 334], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [209]], [[[66, "cowboy", ".mp3", 508.848], -1], 0, 0, [], [], []], [[[38, "columnSpin2", 0.6833333333333333, 8, 2, [{"frame": 0.16666666666666666, "func": "randomIcon", "params": ["3"]}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 298}, {"frame": 0.6666666666666666, "value": -302}, {"frame": 0.6833333333333333, "value": 298}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 148}, {"frame": 0.5, "value": -302}, {"frame": 0.5166666666666667, "value": 298}, {"frame": 0.6833333333333333, "value": 148}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": -2}, {"frame": 0.3333333333333333, "value": -302}, {"frame": 0.35, "value": 298}, {"frame": 0.6833333333333333, "value": -2}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -152]}, {"frame": 0.16666666666666666, "value": [0, -302]}, {"frame": 0.18333333333333332, "value": [0, 298]}, {"frame": 0.6833333333333333, "value": [0, -152]}]}}}}]], 0, 0, [], [], []], [[{"name": "avenger_symbol_1_5", "rect": [0, 0, 144, 151], "offset": [0, 0], "originalSize": [144, 151], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [210]], [[{"name": "line_12_13", "rect": [0, 0, 776, 163], "offset": [0, 0], "originalSize": [776, 163], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [211]], [[{"name": "Layer6_on", "rect": [0, 0, 67, 85], "offset": [0, 0], "originalSize": [67, 85], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [212]], [[{"name": "Layer7", "rect": [0, 0, 87, 50], "offset": [0, 0], "originalSize": [87, 50], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [213]], [[{"name": "Layer6", "rect": [0, 0, 67, 85], "offset": [0, 0], "originalSize": [67, 85], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [214]], [[{"name": "Layer2", "rect": [0, 0, 84, 52], "offset": [0, 0], "originalSize": [84, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [215]], [[{"name": "avenger_symbol_1_11", "rect": [0, 0, 109, 150], "offset": [0, 0], "originalSize": [109, 150], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [216]], [[[38, "columnStop2", 1.35, 8, "0", [{"frame": 1.35, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 298}, {"frame": 1.3333333333333333, "value": -302}, {"frame": 1.35, "value": 298}], "active": []}}, "slot1": {"props": {"y": [{"frame": 0, "value": 148}, {"frame": 1, "value": -302}, {"frame": 1.0166666666666666, "value": 298}, {"frame": 1.35, "value": 148}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, -2]}, {"frame": 0.6666666666666666, "value": [0, -302]}, {"frame": 0.6833333333333333, "value": [0, 298]}, {"frame": 1.35, "value": [0, -2]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -152]}, {"frame": 0.3333333333333333, "value": [0, -302]}, {"frame": 0.35, "value": [0, 298]}, {"frame": 1.35, "value": [0, -152]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "Layer1", "rect": [0, 0, 70, 41], "offset": [0, 0], "originalSize": [70, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [217]], [[{"name": "btn_x2", "rect": [1, 1, 163, 87], "offset": [-0.5, -0.5], "originalSize": [166, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [218]], [[{"name": "Layer8", "rect": [0, 0, 87, 48], "offset": [0, 0], "originalSize": [87, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [219]], [[{"name": "icon_vinhdanh", "rect": [0, 0, 55, 47], "offset": [0, 0], "originalSize": [55, 47], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [220]], [[{"name": "avenger_line_numb_1", "rect": [0, 0, 86, 83], "offset": [0, 0], "originalSize": [86, 83], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [221]], [[{"name": "Layer4_on", "rect": [0, 0, 50, 84], "offset": [0, 0], "originalSize": [50, 84], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [222]], [[[66, "stopSpin", ".mp3", 0.312], -1], 0, 0, [], [], []]]]