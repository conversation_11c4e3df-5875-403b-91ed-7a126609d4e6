[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "43FgFyEmhMwp0gdhTuTevT", "11ewsaO/lFzIkmSDPO+x2z", "84cbWRHshDY6cM42UYQNGn", "94/Z4szM5GnqVf6Ola0ppg", "15TqJIGghPRbUeUWSV2ffV", "fa0JEQ585BFoq9kN30BSTe", "aa/mIwPsdKgofO2ymbtxIl", "97VdwFZ6dLdLWR9bX/Uo9i", "75XrxsWQ1LtrrCauLPArA2", "caMgThy1VMjaOc0JGk7H66", "a9VpD0DP5LJYQPXITZq+uj", "faOOS31DFIv44+SvzqdlNl", "80NC2FR4dIM6qbuBKEpntK", "3cH7OinulEzIKmTiAdc0ul", "8fOh7kpadLK4TelBsDdO26", "5a6wl5LzlIyqbuiHNwJCVz", "120JgFP1hK0ocW589ViyBA", "efY5X/fElAQ6uwXSaQ3Hds", "c8b+IZbOBLK4quBdDEfTbC", "5aFaVAKepOVoF397CY8Xyr", "eaqnIplqNErbdgYnwQx8GG", "eaU9euwJhIG45j5EP7fm6o", "81JpjfyB1JzZbzKubJlay8", "d82n49/IVAvIEqsa0xvvk0", "fdNoodJKVLj4dF1TLppv2g", "60lCKF0BFLRJpfGf89TIRK", "bcbgFB+epIlKdGw1JroyQo", "24xd2Xl+xHVZeWwPN10Wzf", "24v6oRaBNJt47BdFrohB1s", "0ftmlbT7ZPMLJhG7TJ1kXl", "5bRc9rVQdJx6ZpHghwNDsm", "bec67CsddD7Lwhqh+yDuBp", "f6xXtz6Z1Gob+d1RQqvdVi", "aeAOJIpadESo7MXyu/l9rt", "07MOFMlIZCjJsWBQAqMW2T", "a3TOsGkvFDJovW02XOvVKg", "ceExw9R5BBN7nnn8SSHY+s", "73JeKoKqZP4Kpr0uoJXjmd", "b1Hebqje5JC6cg3/XCnknh", "029qkQq95N77RJ+zdiMYTk", "c1pvZ+F8VM3JUtl1/Wvrxu", "c692dwZzRLDbeTcDi+7gNN", "e8ccUMCdtDupjR+r9zSXsP", "a3JTCykGNPBbVHrJGDdMpn", "8azDlpsXNK7az+dC0w4Agc", "bdgdVUvghB4J4Ge2BgVDIq", "840Jl5rg1M9a6NAk0zwPHW", "07UJyL9GZFwq7P5N4aFyG7", "92BAgPBhtAIJywBRed7h98", "61dtRy+zdNpI3ocHPomjBE", "a55ZLKPDlBIpma9VQ0L//B", "1b8xr2lRZBQ4n5tVAIsnnO", "b9sAPrSq9Bhq4NL0/rodI/", "84vbGj7+FNepG526cyvFRo", "0bqSDyYyVCEovDWnR1jQwP", "73Rg+ZnRZOn60ZqdpFecEI", "e6kpNRkItHg4ZnqZc+Ess2", "5f5tcsdq5HNKWCiBl7LeeP", "efrifjwBBLrIxQ6jyPG5K6", "1eV+M4bc1Ps5ip4r2UaoZr", "b7TNYoCb9JjooFRF2a22mg", "b15NZiqA1HhYVgch2FGGXJ", "c1uokXyclK8IpkAghkkzYy", "d1X2jV5upG6LexaG1LxyAM", "23vDYhkPxPt4SS21sPCZVs", "0aLgiltBdMk7EYPGA14agE", "27AHLJq+NAmrXyvotrlwk7", "97BlrVJNJH0ZxryHV1zovl", "75KR+WTuRPmbFyY0Hxq7t+", "0fdlXb4m1KtaqEAO3HYLt6", "d1MzziBA9I4bENTciYWb6n", "33MnE1Y21CgrnigkJQMfaA", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "8cJkMt10JCtovg68b+Tojs", "5b0LLm6bFBEa89UYnku39d", "15tjsA9n5C56nWOBRmNgZp", "8dRHka8jJPqZPQcCv3IO6p", "10paj1R+RLIZxdDGbCK2kj", "cbmJ50bIhGead/YYUqNPmO", "05/k69cl1FpKZs3PPmGgqu", "f7Mj46m8tNkosfZTddJM+8", "e8oN4BXXxAXZOQPuh6jx+c", "13L3QyYTNKUpNCGLIbJ4nY", "969g3eMQNF7I1Wuwx4GoCh", "4aHNYVSzVP07iqaaxv+0RS", "e8yPidfkFJ/Kr6rcBteued", "f9nBVbB69E7Jhl0LsbbfWP", "e88AGv8glOB7Z1WyUn9dZ6", "4482gLDRBB/qaawwh4zld6", "762zLkBgJKQYMSVAMqDp1k", "a7ZIP+aGpJdJPFaAE8axIJ", "dfQo5p6SVOhImfY9X1/rY7", "17W+i1Kr1Gk7n0Wbs02G0v", "2cWB/vWPRHja3uQTinHH30", "c1zvtyNAVNx4QBPvE3lMlL", "f7/d1P7idLGrOMu9cPBOXd", "3bPskVq/VCIrRDxbqDPZpl", "99WSttHhZOKry4tdZQAMxP", "906M2BbChPkbJ1IEy06w2e", "240UDX6P5A9qfRRxJohXQo", "d7UfaWJOVIRJE77kcYU9XG", "c0JzyHUexHBYz0GbAR5UQ0", "10iuQcxhxJ+IJHaM4cFvry", "aeY5rQWbpKLqinwNskoJCm", "17AVOJWK1DF4sfIarOsL+y", "bfCSL/EGxNdac4DdARYnr3", "28K80YK2tNZ7rAdLZTYfOM", "eciFMHlpZD9pDJVpTnx9q/", "56lsK8AHBH/rbk+JK14E7+", "e9fPReeTtJNq8Y0AmNIkJB", "2cRQBzwjlGUpSJDJMZFvP2", "824SITQAZFlac31L0GwQZh", "86MrL7DiBK8YVFwL6QLu9g", "ccoxA7JOZAHZ9lwgQ/ZETh", "17tVvF+q5IBok8ATmQ3hqS"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "_N$skeletonData", "_parent", "_defaultClip", "_file", "_N$target", "_clip", "lblTextNotiNewGame", "Jackpot", "nodeBGTimer", "root", "nodeMain", "lbUserBetXiu", "lbUserBetTai", "lbTotalBetXiu", "lbTotalBetTai", "lbTimer", "lbBigTimer", "lbSessionID", "nodeTornado", "lbBetXiu", "lbBetTai", "nodeLBBetResult", "animationBetResult", "nodeLBBetXiu", "nodeLBBetTai", "rollDice", "nodeXiu", "nodeTai", "winSound", "lbTotalDice", "nodeBgTotalDice", "xnEffect", "xnAnimationplay", "nodeBowl", "nodeResultDice", "nodeResult", "editBoxDatXiu", "editBoxDatTai", "audioChonSo", "lbBetXiuTemp", "lbBetTaiTemp", "nodeinputxiu", "nodeinputtai", "nodeInputFree", "nodeInputValue", "nodeInput", "spriteChat", "spriteNan", "nodeChat", "nodeLightOff", "btnSendChat", "editBoxChat", "chatListView", "nodeDayThang", "nodeDayThua", "lbCordLost", "lbCordWin", "skeletonDragon2", "skeleton<PERSON><PERSON><PERSON>", "spriteButtonEvent", "nodeButtonEventPH", "rtAdmin", "lbMessage", "lbName", "nodeUser", "touchParent", "lbXiuWin", "lbTaiWin", "label", "data", "_normalMaterial", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail", "prefabGraph", "prefabJackpotHistory", "_N$font"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_children", "_trs", "_anchorPoint", "_color", "_eulerAngles"], 0, 9, 4, 1, 5, 2, 7, 5, 5, 5], ["cc.Label", ["_fontSize", "_N$verticalAlign", "_isSystemFontUsed", "_lineHeight", "_string", "_N$horizontalAlign", "_enableWrapText", "_N$overflow", "_spacingX", "_enabled", "_materials", "node", "_N$file"], -7, 3, 1, 6], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_color", "_children"], 0, 1, 2, 4, 5, 7, 5, 5, 2], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "_N$interactable", "_enabled", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$normalColor", "_N$target", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_normalMaterial"], -1, 1, 9, 5, 5, 5, 1, 6, 6, 6, 6], ["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_children", "_parent", "_trs", "_contentSize", "_components"], 0, 4, 2, 1, 7, 5, 12], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "_animationName", "premultipliedAlpha", "defaultSkin", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.ParticleSystem", ["emissionRate", "life", "lifeVar", "angleVar", "startSize", "_positionType", "speed", "speedVar", "tangentialAccel", "_dstBlendFactor", "totalParticles", "startSizeVar", "startSpin", "tangentialAccelVar", "radialAccel", "_enabled", "endSize", "startSpinVar", "endSpin", "radialAccelVar", "_custom", "node", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_materials", "_spriteFrame"], -18, 1, 8, 8, 8, 8, 5, 5, 6, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.AudioSource", ["node", "_clip"], 3, 1, 6], ["cc.EditBox", ["returnType", "max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "editingReturn", "_N$textLabel", "_N$placeholderLabel", "_N$background", "textChanged"], 0, 1, 9, 1, 1, 1, 9], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.Prefab", ["_name"], 2], ["e835ay9V/BNqpybaM94kel3", ["node", "nodeTornado", "nodeBGTimer", "lbSessionID", "lbBigTimer", "lbTimer", "lbTotalBetTai", "lbTotalBetXiu", "lbUserBetTai", "lbUserBetXiu", "lblTextNotiNewGame", "nodeMain", "Jackpot"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["0c3ccv+tpRFabH83rbBdSy2", ["node", "lbBetTai", "lbBetXiu"], 3, 1, 1, 1], ["450baCQgWhDSrSp77gdGm7k", ["node", "nodeResult", "nodeResultDice", "nodeBowl", "nodeBGTimer", "xnAnimation", "xnAnimationplay", "xnEffect", "nodeBgTotalDice", "lbTotalDice", "nodeTaiWins", "nodeXiuWins", "winSound", "nodeTai", "nodeXiu", "rollDice", "Jackpot", "nodeLBBetTai", "nodeLBBetXiu", "animationBetResult", "nodeLBBetResult"], 3, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["1d8f7wrbA9IfIfaq0FoLVi8", ["node"], 3, 1], ["f74d33qIsFLnqhF+m+kFzXV", ["node"], 3, 1], ["28af0R4cHhFK5noZu/IG0XO", ["node", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail", "prefabGraph", "prefabJackpotHistory"], 3, 1, 6, 6, 6, 6, 6, 6], ["347b1xdyqpC24YIVRrVdPlw", ["node", "nodeInput", "nodeInputValue", "nodeInputFree", "nodeinputtai", "nodeinputxiu", "lbBetTaiTemp", "lbBetXiuTemp", "lblTextNotiNewGame", "audioChonSo", "editBoxDatTai", "editBoxDatXiu"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["79a43qxc5JLRZdcLtAklZTY", ["node", "spriteSides", "sfSides"], 3, 1, 2, 3], ["d2f78nGJTNGm5ZkZaPiJU41", ["sfLights", "node", "nodeLightOff", "nodeChat", "spriteNan", "sfNans", "spriteChat", "sfChats"], 2, 1, 1, 1, 1, 3, 1, 3], ["c9ea2HJ+4FBwJf8JdBQBbUQ", ["node", "chatListView", "editBoxChat", "btnSendChat", "lblTextNotiNewGame"], 3, 1, 1, 1, 1, 1], ["7db83HkR/JO7aKBhmpOyOhT", ["node", "nodeEventPHs", "nodeButtonEventPH", "spriteButtonEvent", "skeleton<PERSON><PERSON><PERSON>", "skeletonDragon2", "lbCordWin", "lbCordLost", "spriteBalls", "sfButtonEvents", "nodeDayThua", "nodeDayThang"], 3, 1, 2, 1, 1, 1, 1, 1, 1, 2, 3, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["8be9fmDgjRGuJDpCIp+Bx7T", ["node", "nodeUser", "lbName", "lbMessage", "rtAdmin"], 3, 1, 1, 1, 1, 1], ["8457bzXi+RCFrWEyccPy/PF", ["node", "touchParent"], 3, 1, 1], ["8457bzXi+RCFrWEyccPy/PF", ["touchParent", "node", "btnHandle"], 2, 1, 1], ["cbf0avnl01NgI8Jid2gb8ms", ["node", "lbTaiWin", "lbXiuWin"], 3, 1, 1, 1], ["9d1bbBbrYlCd6GxBqWZU2YQ", ["node"], 3, 1], ["cc.Widget", ["alignMode", "_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -5, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node", "label"], 3, 1, 1], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$handleTouchEvent", "node"], -3, 1], ["<PERSON><PERSON>", ["horizontal", "node", "_N$content"], 2, 1, 1], ["87b0b6j4kBLKKRiRXRQjf/0", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1]], [[12, 0, 1, 2], [0, 0, 5, 3, 4, 6, 8, 2], [4, 0, 3, 4, 5, 6, 7, 2], [5, 0, 1, 4, 5, 6, 7, 3], [3, 4, 5, 6, 1], [10, 0, 1, 3, 3], [10, 0, 1, 2, 3, 4], [3, 0, 1, 4, 5, 3], [3, 0, 1, 4, 3], [3, 0, 4, 5, 2], [0, 0, 5, 7, 3, 4, 6, 8, 2], [4, 0, 1, 3, 4, 5, 6, 7, 3], [0, 0, 1, 5, 3, 4, 6, 8, 3], [3, 1, 4, 5, 6, 2], [5, 2, 4, 8, 2], [0, 0, 1, 5, 3, 4, 8, 11, 3], [4, 0, 3, 4, 5, 9, 6, 8, 7, 2], [15, 0, 1, 2, 3, 4, 5], [6, 0, 5, 8, 3, 7, 6, 2], [0, 0, 1, 5, 7, 3, 4, 6, 8, 3], [11, 15, 9, 10, 0, 1, 2, 3, 4, 11, 12, 5, 6, 7, 8, 13, 14, 21, 22, 23, 24, 25, 26, 27, 28, 17], [0, 0, 7, 3, 4, 6, 8, 2], [3, 0, 4, 5, 6, 2], [8, 4, 0, 1, 3, 2, 5, 6, 7, 6], [8, 4, 0, 1, 2, 5, 6, 7, 5], [1, 4, 0, 3, 2, 8, 5, 1, 11, 10, 8], [6, 0, 5, 4, 3, 2], [6, 0, 1, 5, 4, 3, 6, 3], [0, 0, 5, 3, 4, 6, 2], [4, 0, 3, 4, 5, 6, 2], [4, 0, 3, 4, 5, 2], [9, 1, 2, 3, 1], [9, 1, 2, 1], [9, 0, 1, 2, 3, 2], [13, 0, 1], [7, 1, 0, 2, 5, 6, 4], [3, 0, 1, 4, 5, 6, 3], [3, 3, 0, 4, 5, 3], [3, 1, 4, 5, 2], [5, 4, 5, 6, 7, 9, 1], [8, 0, 1, 3, 2, 5, 6, 5], [6, 0, 5, 4, 3, 7, 6, 2], [6, 0, 5, 4, 8, 3, 7, 6, 2], [6, 0, 5, 4, 3, 6, 2], [0, 0, 2, 5, 3, 4, 6, 8, 3], [0, 0, 1, 5, 3, 4, 6, 3], [4, 0, 3, 10, 4, 5, 6, 7, 2], [4, 0, 1, 2, 3, 10, 4, 5, 6, 7, 4], [4, 0, 1, 2, 3, 4, 5, 9, 6, 8, 7, 4], [4, 0, 2, 3, 4, 5, 9, 6, 8, 7, 3], [13, 0, 1, 1], [3, 4, 5, 1], [3, 4, 1], [3, 2, 0, 4, 3], [28, 0, 1], [8, 4, 0, 1, 2, 5, 6, 5], [8, 4, 0, 1, 3, 2, 5, 6, 6], [1, 0, 3, 6, 2, 5, 1, 10, 7], [1, 4, 0, 3, 2, 1, 11, 10, 6], [1, 0, 6, 1, 7, 11, 5], [1, 4, 0, 3, 6, 2, 5, 1, 11, 10, 8], [1, 4, 0, 3, 6, 2, 8, 5, 1, 11, 10, 9], [1, 4, 0, 3, 2, 5, 1, 11, 10, 7], [1, 4, 0, 2, 5, 11, 10, 12, 5], [14, 0, 1, 2, 3, 8, 4, 5, 6, 7, 4], [16, 0, 2], [6, 0, 4, 8, 3, 7, 6, 2], [6, 0, 5, 4, 3, 7, 2], [6, 0, 2, 5, 4, 8, 3, 7, 6, 3], [6, 0, 1, 5, 4, 3, 3], [0, 0, 7, 3, 4, 6, 9, 8, 2], [0, 0, 5, 7, 3, 4, 2], [0, 0, 1, 7, 3, 4, 6, 8, 3], [0, 0, 5, 7, 3, 4, 6, 2], [0, 0, 1, 5, 7, 3, 4, 3], [0, 0, 7, 3, 4, 6, 2], [0, 0, 1, 5, 7, 3, 4, 6, 9, 8, 3], [0, 0, 2, 5, 7, 3, 4, 3], [0, 0, 1, 5, 3, 4, 10, 6, 3], [0, 0, 1, 2, 5, 7, 3, 4, 6, 8, 4], [0, 0, 5, 3, 4, 2], [0, 0, 5, 3, 4, 8, 2], [4, 0, 1, 3, 4, 5, 6, 8, 7, 3], [4, 0, 3, 4, 5, 7, 2], [4, 0, 3, 4, 5, 6, 8, 7, 2], [4, 0, 1, 3, 4, 5, 9, 6, 7, 3], [9, 0, 1, 2], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [18, 0, 1, 2, 1], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1], [20, 0, 1], [21, 0, 1], [22, 0, 1, 2, 3, 4, 5, 6, 1], [12, 1, 1], [23, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [7, 3, 1, 0, 2, 5, 6, 5], [7, 3, 1, 0, 2, 4, 5, 6, 6], [7, 1, 0, 2, 4, 5, 6, 5], [7, 0, 2, 5, 6, 3], [7, 1, 0, 5, 6, 3], [24, 0, 1, 2, 1], [25, 0, 1, 2, 3, 4, 5, 6, 7, 2], [3, 2, 0, 1, 4, 5, 6, 4], [3, 2, 4, 5, 6, 2], [3, 0, 4, 2], [3, 2, 0, 4, 5, 3], [26, 0, 1, 2, 3, 4, 1], [27, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [29, 0, 1, 2, 3, 4, 1], [5, 4, 1], [5, 4, 8, 9, 1], [5, 4, 5, 8, 1], [5, 1, 4, 5, 8, 6, 7, 9, 10, 11, 12, 2], [5, 0, 1, 4, 5, 13, 3], [5, 3, 0, 1, 4, 5, 6, 7, 4], [5, 0, 2, 1, 4, 5, 6, 7, 9, 4], [5, 0, 4, 5, 2], [30, 0, 1, 1], [31, 0, 1, 2, 2], [32, 0, 1, 2, 1], [33, 0, 1], [10, 0, 1, 3], [8, 0, 1, 3, 2, 5, 6, 7, 5], [34, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [35, 0, 1, 1], [36, 0, 1, 1], [1, 4, 0, 3, 2, 8, 5, 1, 10, 8], [1, 0, 3, 6, 2, 1, 7, 11, 7], [1, 4, 0, 3, 6, 2, 1, 7, 11, 10, 8], [1, 0, 3, 6, 2, 1, 11, 10, 6], [1, 0, 3, 6, 2, 1, 7, 11, 10, 7], [1, 4, 5, 1, 10, 4], [1, 4, 0, 6, 1, 7, 11, 10, 6], [1, 4, 0, 3, 6, 1, 7, 11, 10, 7], [1, 0, 3, 2, 5, 11, 10, 5], [1, 0, 3, 6, 2, 7, 11, 10, 6], [1, 9, 0, 2, 5, 1, 11, 10, 6], [1, 0, 3, 2, 5, 1, 11, 10, 6], [14, 0, 1, 2, 3, 4, 5, 6, 7, 4], [37, 0, 1, 2, 3, 4, 5, 6, 7], [38, 0, 1, 2, 2], [39, 0, 1, 2, 3, 4, 3], [11, 9, 10, 0, 1, 2, 3, 4, 11, 12, 5, 6, 7, 8, 13, 14, 21, 22, 23, 24, 25, 26, 27, 28, 16], [11, 0, 1, 2, 3, 4, 16, 17, 18, 5, 6, 7, 8, 19, 20, 21, 29, 22, 23, 24, 25, 26, 27, 28, 30, 15]], [[[{"name": "bxh", "rect": [0, 0, 65, 65], "offset": [0, 0], "originalSize": [65, 65], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [30]], [[[17, "taixiu_superwin", "\ntaixiu_superwin.png\nsize: 2340,2340\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n00370\n  rotate: false\n  xy: 2, 20\n  size: 180, 101\n  orig: 650, 650\n  offset: 217, 146\n  index: -1\n00371\n  rotate: false\n  xy: 1997, 1088\n  size: 236, 126\n  orig: 650, 650\n  offset: 184, 151\n  index: -1\n00372\n  rotate: false\n  xy: 1655, 144\n  size: 223, 163\n  orig: 650, 650\n  offset: 179, 149\n  index: -1\n00373\n  rotate: false\n  xy: 1880, 114\n  size: 212, 193\n  orig: 650, 650\n  offset: 194, 144\n  index: -1\n00374\n  rotate: false\n  xy: 1410, 255\n  size: 243, 211\n  orig: 650, 650\n  offset: 168, 146\n  index: -1\n00375\n  rotate: false\n  xy: 932, 10\n  size: 225, 234\n  orig: 650, 650\n  offset: 186, 147\n  index: -1\n00376\n  rotate: true\n  xy: 421, 2\n  size: 242, 225\n  orig: 650, 650\n  offset: 177, 148\n  index: -1\n00377\n  rotate: false\n  xy: 1418, 468\n  size: 258, 262\n  orig: 650, 650\n  offset: 169, 146\n  index: -1\n00378\n  rotate: false\n  xy: 2061, 1522\n  size: 276, 299\n  orig: 650, 650\n  offset: 161, 138\n  index: -1\n00379\n  rotate: true\n  xy: 1678, 579\n  size: 272, 289\n  orig: 650, 650\n  offset: 172, 151\n  index: -1\n00380\n  rotate: false\n  xy: 1130, 1016\n  size: 290, 297\n  orig: 650, 650\n  offset: 166, 151\n  index: -1\n00381\n  rotate: true\n  xy: 2028, 1216\n  size: 304, 308\n  orig: 650, 650\n  offset: 155, 151\n  index: -1\n00382\n  rotate: false\n  xy: 1664, 1659\n  size: 395, 312\n  orig: 650, 650\n  offset: 118, 152\n  index: -1\n00383\n  rotate: false\n  xy: 1664, 1367\n  size: 362, 290\n  orig: 650, 650\n  offset: 140, 180\n  index: -1\n00384\n  rotate: false\n  xy: 827, 246\n  size: 326, 290\n  orig: 650, 650\n  offset: 151, 179\n  index: -1\n00385\n  rotate: true\n  xy: 1418, 732\n  size: 282, 258\n  orig: 650, 650\n  offset: 173, 208\n  index: -1\n00390\n  rotate: false\n  xy: 1984, 883\n  size: 206, 203\n  orig: 650, 650\n  offset: 215, 146\n  index: -1\n00391\n  rotate: true\n  xy: 1439, 9\n  size: 244, 193\n  orig: 650, 650\n  offset: 196, 144\n  index: -1\n00392\n  rotate: false\n  xy: 1678, 309\n  size: 283, 268\n  orig: 650, 650\n  offset: 190, 115\n  index: -1\n00393\n  rotate: false\n  xy: 827, 538\n  size: 305, 329\n  orig: 650, 650\n  offset: 189, 101\n  index: -1\n00394\n  rotate: false\n  xy: 1103, 1315\n  size: 305, 292\n  orig: 650, 650\n  offset: 185, 134\n  index: -1\n00395\n  rotate: false\n  xy: 1678, 853\n  size: 304, 254\n  orig: 650, 650\n  offset: 179, 178\n  index: -1\n00396\n  rotate: false\n  xy: 1678, 1109\n  size: 317, 256\n  orig: 650, 650\n  offset: 174, 183\n  index: -1\n00397\n  rotate: true\n  xy: 851, 1284\n  size: 320, 250\n  orig: 650, 650\n  offset: 169, 185\n  index: -1\n00398\n  rotate: true\n  xy: 1422, 1023\n  size: 318, 254\n  orig: 650, 650\n  offset: 169, 180\n  index: -1\n00399\n  rotate: false\n  xy: 1159, 15\n  size: 278, 232\n  orig: 650, 650\n  offset: 173, 204\n  index: -1\n00400\n  rotate: true\n  xy: 841, 912\n  size: 324, 287\n  orig: 650, 650\n  offset: 110, 146\n  index: -1\n00401\n  rotate: true\n  xy: 432, 246\n  size: 252, 295\n  orig: 650, 650\n  offset: 185, 138\n  index: -1\n00402\n  rotate: true\n  xy: 1410, 1343\n  size: 264, 248\n  orig: 650, 650\n  offset: 176, 139\n  index: -1\n00403\n  rotate: false\n  xy: 1155, 479\n  size: 261, 245\n  orig: 650, 650\n  offset: 182, 144\n  index: -1\n00404\n  rotate: true\n  xy: 648, 7\n  size: 237, 282\n  orig: 650, 650\n  offset: 184, 109\n  index: -1\n00405\n  rotate: true\n  xy: 1134, 726\n  size: 288, 282\n  orig: 650, 650\n  offset: 183, 110\n  index: -1\n00480\n  rotate: false\n  xy: 1270, 1609\n  size: 392, 366\n  orig: 650, 650\n  offset: 135, 146\n  index: -1\n00481\n  rotate: false\n  xy: 432, 500\n  size: 393, 367\n  orig: 650, 650\n  offset: 136, 143\n  index: -1\n00482\n  rotate: false\n  xy: 867, 1621\n  size: 401, 358\n  orig: 650, 650\n  offset: 135, 149\n  index: -1\n00483\n  rotate: false\n  xy: 1693, 1973\n  size: 407, 365\n  orig: 650, 650\n  offset: 128, 144\n  index: -1\n00484\n  rotate: false\n  xy: 448, 1976\n  size: 417, 362\n  orig: 650, 650\n  offset: 106, 141\n  index: -1\n00485\n  rotate: false\n  xy: 2, 860\n  size: 428, 371\n  orig: 650, 650\n  offset: 85, 140\n  index: -1\n00486\n  rotate: false\n  xy: 2, 1233\n  size: 429, 371\n  orig: 650, 650\n  offset: 81, 143\n  index: -1\n00487\n  rotate: false\n  xy: 439, 1606\n  size: 414, 368\n  orig: 650, 650\n  offset: 73, 145\n  index: -1\n00488\n  rotate: false\n  xy: 433, 1238\n  size: 416, 366\n  orig: 650, 650\n  offset: 67, 145\n  index: -1\n00489\n  rotate: false\n  xy: 2, 123\n  size: 417, 367\n  orig: 650, 650\n  offset: 64, 145\n  index: -1\n00490\n  rotate: false\n  xy: 867, 1981\n  size: 414, 357\n  orig: 650, 650\n  offset: 63, 154\n  index: -1\n00491\n  rotate: false\n  xy: 1283, 1977\n  size: 408, 361\n  orig: 650, 650\n  offset: 64, 147\n  index: -1\n00492\n  rotate: false\n  xy: 2, 1976\n  size: 444, 362\n  orig: 650, 650\n  offset: 77, 145\n  index: -1\n00493\n  rotate: false\n  xy: 2, 492\n  size: 428, 366\n  orig: 650, 650\n  offset: 89, 143\n  index: -1\n00494\n  rotate: false\n  xy: 2, 1606\n  size: 435, 368\n  orig: 650, 650\n  offset: 92, 143\n  index: -1\n00495\n  rotate: false\n  xy: 433, 869\n  size: 406, 367\n  orig: 650, 650\n  offset: 121, 142\n  index: -1\nimage 1\n  rotate: false\n  xy: 2020, 15\n  size: 91, 97\n  orig: 100, 100\n  offset: 8, 0\n  index: -1\nimage 11\n  rotate: false\n  xy: 2094, 253\n  size: 90, 97\n  orig: 100, 100\n  offset: 6, 0\n  index: -1\nimage 13\n  rotate: false\n  xy: 2135, 679\n  size: 90, 95\n  orig: 100, 100\n  offset: 6, 0\n  index: -1\nimage 15\n  rotate: false\n  xy: 2094, 154\n  size: 91, 97\n  orig: 100, 100\n  offset: 5, 0\n  index: -1\nimage 17\n  rotate: false\n  xy: 2113, 55\n  size: 92, 97\n  orig: 100, 100\n  offset: 4, 0\n  index: -1\nimage 19\n  rotate: false\n  xy: 729, 402\n  size: 96, 96\n  orig: 100, 100\n  offset: 0, 0\n  index: -1\nimage 21\n  rotate: false\n  xy: 2227, 688\n  size: 94, 96\n  orig: 100, 100\n  offset: 2, 0\n  index: -1\nimage 23\n  rotate: false\n  xy: 2201, 786\n  size: 94, 97\n  orig: 100, 100\n  offset: 4, 0\n  index: -1\nimage 3\n  rotate: false\n  xy: 1963, 452\n  size: 93, 98\n  orig: 100, 100\n  offset: 4, 0\n  index: -1\nimage 5\n  rotate: false\n  xy: 2058, 452\n  size: 91, 98\n  orig: 100, 100\n  offset: 3, 0\n  index: -1\nimage 7\n  rotate: false\n  xy: 1963, 352\n  size: 93, 98\n  orig: 100, 100\n  offset: 2, 0\n  index: -1\nimage 9\n  rotate: false\n  xy: 2058, 352\n  size: 91, 98\n  orig: 100, 100\n  offset: 4, 0\n  index: -1\nlight (2)\n  rotate: false\n  xy: 1155, 249\n  size: 253, 228\n  orig: 512, 512\n  offset: 130, 103\n  index: -1\nnohu\n  rotate: true\n  xy: 2102, 1823\n  size: 515, 235\n  orig: 517, 239\n  offset: 1, 4\n  index: -1\ntexiao2 (1)\n  rotate: false\n  xy: 421, 480\n  size: 9, 10\n  orig: 210, 195\n  offset: 91, 87\n  index: -1\ntexiao2 (10)\n  rotate: true\n  xy: 2235, 1057\n  size: 157, 97\n  orig: 210, 195\n  offset: 25, 49\n  index: -1\ntexiao2 (11)\n  rotate: false\n  xy: 1984, 776\n  size: 167, 105\n  orig: 210, 195\n  offset: 20, 45\n  index: -1\ntexiao2 (12)\n  rotate: false\n  xy: 1634, 31\n  size: 170, 111\n  orig: 210, 195\n  offset: 22, 42\n  index: -1\ntexiao2 (13)\n  rotate: true\n  xy: 2192, 885\n  size: 170, 106\n  orig: 210, 195\n  offset: 25, 39\n  index: -1\ntexiao2 (14)\n  rotate: false\n  xy: 184, 23\n  size: 176, 98\n  orig: 210, 195\n  offset: 10, 49\n  index: -1\ntexiao2 (15)\n  rotate: false\n  xy: 1969, 614\n  size: 152, 69\n  orig: 210, 195\n  offset: 5, 60\n  index: -1\ntexiao2 (2)\n  rotate: false\n  xy: 851, 1242\n  size: 95, 40\n  orig: 210, 195\n  offset: 50, 79\n  index: -1\ntexiao2 (3)\n  rotate: true\n  xy: 2153, 777\n  size: 104, 46\n  orig: 210, 195\n  offset: 45, 75\n  index: -1\ntexiao2 (4)\n  rotate: true\n  xy: 362, 7\n  size: 114, 52\n  orig: 210, 195\n  offset: 40, 72\n  index: -1\ntexiao2 (5)\n  rotate: false\n  xy: 1969, 552\n  size: 124, 60\n  orig: 210, 195\n  offset: 35, 68\n  index: -1\ntexiao2 (6)\n  rotate: true\n  xy: 1806, 9\n  size: 133, 67\n  orig: 210, 195\n  offset: 30, 64\n  index: -1\ntexiao2 (7)\n  rotate: false\n  xy: 1875, 37\n  size: 143, 75\n  orig: 210, 195\n  offset: 25, 60\n  index: -1\ntexiao2 (8)\n  rotate: true\n  xy: 729, 246\n  size: 154, 82\n  orig: 210, 195\n  offset: 19, 57\n  index: -1\ntexiao2 (9)\n  rotate: false\n  xy: 1969, 685\n  size: 164, 89\n  orig: 210, 195\n  offset: 14, 53\n  index: -1\n", ["taixiu_superwin.png"], {"skins": [{"name": "default", "attachments": {"light": {"image 9": {"width": 100, "height": 100}, "image 23": {"width": 100, "height": 100}, "image 5": {"width": 100, "height": 100}, "image 13": {"width": 100, "height": 100}, "image 21": {"width": 100, "height": 100}, "image 7": {"width": 100, "height": 100}, "image 11": {"width": 100, "height": 100}, "image 1": {"width": 100, "height": 100}, "image 17": {"width": 100, "height": 100}, "image 3": {"width": 100, "height": 100}, "image 15": {"width": 100, "height": 100}, "image 19": {"width": 100, "height": 100}}, "longl": {"00405": {"width": 650, "height": 650}, "00404": {"width": 650, "height": 650}, "00399": {"width": 650, "height": 650}, "00398": {"width": 650, "height": 650}, "00397": {"width": 650, "height": 650}, "00396": {"width": 650, "height": 650}, "00403": {"width": 650, "height": 650}, "00402": {"width": 650, "height": 650}, "00401": {"width": 650, "height": 650}, "00400": {"width": 650, "height": 650}, "00391": {"width": 650, "height": 650}, "00390": {"width": 650, "height": 650}, "00395": {"width": 650, "height": 650}, "00394": {"width": 650, "height": 650}, "00393": {"width": 650, "height": 650}, "00392": {"width": 650, "height": 650}}, "longh": {"00377": {"width": 650, "height": 650}, "00376": {"width": 650, "height": 650}, "00375": {"width": 650, "height": 650}, "00374": {"width": 650, "height": 650}, "00385": {"width": 650, "height": 650}, "00379": {"width": 650, "height": 650}, "00378": {"width": 650, "height": 650}, "00380": {"width": 650, "height": 650}, "00373": {"width": 650, "height": 650}, "00384": {"width": 650, "height": 650}, "00372": {"width": 650, "height": 650}, "00383": {"width": 650, "height": 650}, "00371": {"width": 650, "height": 650}, "00382": {"width": 650, "height": 650}, "00370": {"width": 650, "height": 650}, "00381": {"width": 650, "height": 650}}, "texiao3": {"texiao2 (3)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (2)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (1)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (10)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (13)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (14)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (9)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (11)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (8)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (12)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (7)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (6)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (5)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (15)": {"width": 210, "y": 0.5, "height": 195}, "texiao2 (4)": {"width": 210, "y": 0.5, "height": 195}}, "nohu": {"nohu": {"x": 0.5, "width": 517, "y": 0.5, "height": 239}}, "long": {"00487": {"width": 650, "height": 650}, "00486": {"width": 650, "height": 650}, "00485": {"width": 650, "height": 650}, "00484": {"width": 650, "height": 650}, "00495": {"width": 650, "height": 650}, "00489": {"width": 650, "height": 650}, "00488": {"width": 650, "height": 650}, "00490": {"width": 650, "height": 650}, "00483": {"width": 650, "height": 650}, "00494": {"width": 650, "height": 650}, "00482": {"width": 650, "height": 650}, "00493": {"width": 650, "height": 650}, "00481": {"width": 650, "height": 650}, "00492": {"width": 650, "height": 650}, "00480": {"width": 650, "height": 650}, "00491": {"width": 650, "height": 650}}, "light2": {"light (2)": {"scaleX": 0.4938, "scaleY": 0.4938, "x": -1.87, "width": 512, "y": 3.11, "height": 512}}}}], "skeleton": {"images": "", "x": -549.22, "width": 1210.91, "y": -533.88, "spine": "3.8.75", "audio": "", "hash": "MJZrPhdsLPGFgizZG8w9yt8Fuw0", "height": 1264.63}, "slots": [{"blend": "additive", "name": "texiao3", "bone": "texiao3"}, {"attachment": "image 9", "blend": "additive", "name": "light", "bone": "light"}, {"attachment": "light (2)", "blend": "additive", "name": "light2", "bone": "light2"}, {"attachment": "00495", "blend": "additive", "name": "long", "bone": "long"}, {"attachment": "00383", "blend": "additive", "name": "longh", "bone": "longh"}, {"attachment": "00403", "name": "longl", "bone": "longl"}, {"attachment": "nohu", "name": "nohu", "bone": "nohu"}], "bones": [{"name": "root"}, {"scaleX": 3.6715, "parent": "root", "scaleY": 3.6715, "name": "texiao3", "x": 32.82, "y": 7.16}, {"scaleX": 3.7474, "parent": "root", "scaleY": 3.7474, "name": "light", "x": 7.5, "y": -1.97}, {"parent": "light", "name": "light2"}, {"scaleX": 1.6355, "parent": "root", "scaleY": 1.6355, "name": "long", "x": 6.48, "y": -2.35}, {"scaleX": 1.8629, "parent": "root", "scaleY": -1.8629, "name": "longh", "x": 56.24, "y": 125.3}, {"scaleX": 1.4065, "parent": "root", "scaleY": 1.4065, "name": "longl", "x": -31.5, "y": 153.3}, {"parent": "root", "name": "nohu", "x": 8.89, "y": 9.63}], "animations": {"animation": {"slots": {"light": {"color": [{"color": "ffffffff", "time": 6.4}, {"color": "ffffff00", "time": 7.2333}], "attachment": [{"name": "image 1"}, {"name": "image 3", "time": 0.0667}, {"name": "image 5", "time": 0.1333}, {"name": "image 7", "time": 0.2}, {"name": "image 9", "time": 0.2667}, {"name": "image 11", "time": 0.3333}, {"name": "image 13", "time": 0.4}, {"name": "image 15", "time": 0.4667}, {"name": "image 17", "time": 0.5333}, {"name": "image 19", "time": 0.6}, {"name": "image 21", "time": 0.6667}, {"name": "image 23", "time": 0.7333}, {"name": "image 1", "time": 0.8}, {"name": "image 3", "time": 0.8667}, {"name": "image 5", "time": 0.9333}, {"name": "image 7", "time": 1}, {"name": "image 9", "time": 1.0667}, {"name": "image 11", "time": 1.1333}, {"name": "image 13", "time": 1.2}, {"name": "image 15", "time": 1.2667}, {"name": "image 17", "time": 1.3333}, {"name": "image 19", "time": 1.4}, {"name": "image 21", "time": 1.4667}, {"name": "image 23", "time": 1.5333}, {"name": "image 1", "time": 1.6}, {"name": "image 3", "time": 1.6667}, {"name": "image 5", "time": 1.7333}, {"name": "image 7", "time": 1.8}, {"name": "image 9", "time": 1.8667}, {"name": "image 11", "time": 1.9333}, {"name": "image 13", "time": 2}, {"name": "image 15", "time": 2.0667}, {"name": "image 17", "time": 2.1333}, {"name": "image 19", "time": 2.2}, {"name": "image 21", "time": 2.2667}, {"name": "image 23", "time": 2.3333}, {"name": "image 1", "time": 2.4}, {"name": "image 3", "time": 2.4667}, {"name": "image 5", "time": 2.5333}, {"name": "image 7", "time": 2.6}, {"name": "image 9", "time": 2.6667}, {"name": "image 11", "time": 2.7333}, {"name": "image 13", "time": 2.8}, {"name": "image 15", "time": 2.8667}, {"name": "image 17", "time": 2.9333}, {"name": "image 19", "time": 3}, {"name": "image 21", "time": 3.0667}, {"name": "image 23", "time": 3.1333}, {"name": "image 1", "time": 3.2}, {"name": "image 3", "time": 3.2667}, {"name": "image 5", "time": 3.3333}, {"name": "image 7", "time": 3.4}, {"name": "image 9", "time": 3.4667}, {"name": "image 11", "time": 3.5333}, {"name": "image 13", "time": 3.6}, {"name": "image 15", "time": 3.6667}, {"name": "image 17", "time": 3.7333}, {"name": "image 19", "time": 3.8}, {"name": "image 21", "time": 3.8667}, {"name": "image 23", "time": 3.9333}, {"name": "image 1", "time": 4}, {"name": "image 3", "time": 4.0667}, {"name": "image 5", "time": 4.1333}, {"name": "image 7", "time": 4.2}, {"name": "image 9", "time": 4.2667}, {"name": "image 11", "time": 4.3333}, {"name": "image 13", "time": 4.4}, {"name": "image 15", "time": 4.4667}, {"name": "image 17", "time": 4.5333}, {"name": "image 19", "time": 4.6}, {"name": "image 21", "time": 4.6667}, {"name": "image 23", "time": 4.7333}, {"name": "image 1", "time": 4.8}, {"name": "image 3", "time": 4.8667}, {"name": "image 5", "time": 4.9333}, {"name": "image 7", "time": 5}, {"name": "image 9", "time": 5.0667}, {"name": "image 11", "time": 5.1333}, {"name": "image 13", "time": 5.2}, {"name": "image 15", "time": 5.2667}, {"name": "image 17", "time": 5.3333}, {"name": "image 19", "time": 5.4}, {"name": "image 21", "time": 5.4667}, {"name": "image 23", "time": 5.5333}, {"name": "image 1", "time": 5.6}, {"name": "image 3", "time": 5.6667}, {"name": "image 5", "time": 5.7333}, {"name": "image 7", "time": 5.8}, {"name": "image 9", "time": 5.8667}, {"name": "image 11", "time": 5.9333}, {"name": "image 13", "time": 6}, {"name": "image 15", "time": 6.0667}, {"name": "image 17", "time": 6.1333}, {"name": "image 19", "time": 6.2}, {"name": "image 21", "time": 6.2667}, {"name": "image 23", "time": 6.3333}, {"name": "image 1", "time": 6.4}, {"name": "image 3", "time": 6.4667}, {"name": "image 5", "time": 6.5333}, {"name": "image 7", "time": 6.6}, {"name": "image 9", "time": 6.6667}, {"name": "image 11", "time": 6.7333}, {"name": "image 13", "time": 6.8}, {"name": "image 15", "time": 6.8667}, {"name": "image 17", "time": 6.9333}, {"name": "image 19", "time": 7}, {"name": "image 21", "time": 7.0667}, {"name": "image 23", "time": 7.1333}]}, "longl": {"color": [{"color": "ffffff00"}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"color": "ffffffff", "time": 0.7}, {"color": "ffffff00", "time": 1.1333}], "attachment": [{"name": null}, {"name": "00390", "time": 0.0667}, {"name": "00391", "time": 0.1333}, {"name": "00392", "time": 0.2}, {"name": "00393", "time": 0.2667}, {"name": "00394", "time": 0.3333}, {"name": "00395", "time": 0.4}, {"name": "00396", "time": 0.4667}, {"name": "00397", "time": 0.5333}, {"name": "00398", "time": 0.6}, {"name": "00399", "time": 0.6667}, {"name": "00400", "time": 0.7333}, {"name": "00401", "time": 0.8}, {"name": "00402", "time": 0.8667}, {"name": "00403", "time": 0.9333}, {"name": "00404", "time": 1}, {"name": "00405", "time": 1.0667}, {"name": null, "time": 1.1333}]}, "longh": {"color": [{"color": "ffffff00"}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"color": "ffffffff", "time": 0.8667}, {"color": "ffffff00", "time": 1.1333}], "attachment": [{"name": null}, {"name": "00370", "time": 0.0667}, {"name": "00371", "time": 0.1333}, {"name": "00372", "time": 0.2}, {"name": "00373", "time": 0.2667}, {"name": "00374", "time": 0.3333}, {"name": "00375", "time": 0.4}, {"name": "00376", "time": 0.4667}, {"name": "00377", "time": 0.5333}, {"name": "00378", "time": 0.6}, {"name": "00379", "time": 0.6667}, {"name": "00380", "time": 0.7333}, {"name": "00381", "time": 0.8}, {"name": "00382", "time": 0.8667}, {"name": "00383", "time": 0.9333}, {"name": "00384", "time": 1}, {"name": "00385", "time": 1.0667}, {"name": null, "time": 1.1333}]}, "texiao3": {"attachment": [{"name": "texiao2 (1)", "time": 0.9667}, {"name": "texiao2 (2)", "time": 1}, {"name": "texiao2 (3)", "time": 1.0333}, {"name": "texiao2 (4)", "time": 1.0667}, {"name": "texiao2 (5)", "time": 1.1}, {"name": "texiao2 (6)", "time": 1.1333}, {"name": "texiao2 (7)", "time": 1.1667}, {"name": "texiao2 (8)", "time": 1.2}, {"name": "texiao2 (9)", "time": 1.2333}, {"name": "texiao2 (10)", "time": 1.2667}, {"name": "texiao2 (11)", "time": 1.3}, {"name": "texiao2 (12)", "time": 1.3333}, {"name": "texiao2 (13)", "time": 1.3667}, {"name": "texiao2 (14)", "time": 1.4}, {"name": "texiao2 (15)", "time": 1.4333}, {"name": null, "time": 1.4667}]}, "nohu": {"color": [{"color": "ffffff00"}, {"color": "ffffff47", "time": 1}, {"color": "ffffffff", "curve": "stepped", "time": 1.0333}, {"color": "ffffffff", "time": 2.3333}, {"color": "ffffff00", "time": 2.6667}]}, "long": {"color": [{"color": "ffffffff", "time": 8.2333}, {"color": "ffffff00", "time": 9.4667}], "attachment": [{"name": null}, {"name": "00480", "time": 1}, {"name": "00481", "time": 1.0667}, {"name": "00482", "time": 1.1333}, {"name": "00483", "time": 1.2}, {"name": "00484", "time": 1.2667}, {"name": "00485", "time": 1.3333}, {"name": "00486", "time": 1.4}, {"name": "00487", "time": 1.4667}, {"name": "00488", "time": 1.5333}, {"name": "00489", "time": 1.6}, {"name": "00490", "time": 1.6667}, {"name": "00491", "time": 1.7333}, {"name": "00492", "time": 1.8}, {"name": "00493", "time": 1.8667}, {"name": "00494", "time": 1.9333}, {"name": "00495", "time": 2}, {"name": "00480", "time": 2.0667}, {"name": "00481", "time": 2.1333}, {"name": "00482", "time": 2.2}, {"name": "00483", "time": 2.2667}, {"name": "00484", "time": 2.3333}, {"name": "00485", "time": 2.4}, {"name": "00486", "time": 2.4667}, {"name": "00487", "time": 2.5333}, {"name": "00488", "time": 2.6}, {"name": "00489", "time": 2.6667}, {"name": "00490", "time": 2.7333}, {"name": "00491", "time": 2.8}, {"name": "00492", "time": 2.8667}, {"name": "00493", "time": 2.9333}, {"name": "00494", "time": 3}, {"name": "00495", "time": 3.0667}, {"name": "00480", "time": 3.1333}, {"name": "00481", "time": 3.2}, {"name": "00482", "time": 3.2667}, {"name": "00483", "time": 3.3333}, {"name": "00484", "time": 3.4}, {"name": "00485", "time": 3.4667}, {"name": "00486", "time": 3.5333}, {"name": "00487", "time": 3.6}, {"name": "00488", "time": 3.6667}, {"name": "00489", "time": 3.7333}, {"name": "00490", "time": 3.8}, {"name": "00491", "time": 3.8667}, {"name": "00492", "time": 3.9333}, {"name": "00493", "time": 4}, {"name": "00494", "time": 4.0667}, {"name": "00495", "time": 4.1333}, {"name": "00480", "time": 4.2}, {"name": "00481", "time": 4.2667}, {"name": "00482", "time": 4.3333}, {"name": "00483", "time": 4.4}, {"name": "00484", "time": 4.4667}, {"name": "00485", "time": 4.5333}, {"name": "00486", "time": 4.6}, {"name": "00487", "time": 4.6667}, {"name": "00488", "time": 4.7333}, {"name": "00489", "time": 4.8}, {"name": "00490", "time": 4.8667}, {"name": "00491", "time": 4.9333}, {"name": "00492", "time": 5}, {"name": "00493", "time": 5.0667}, {"name": "00494", "time": 5.1333}, {"name": "00495", "time": 5.2}, {"name": "00480", "time": 5.2667}, {"name": "00481", "time": 5.3333}, {"name": "00482", "time": 5.4}, {"name": "00483", "time": 5.4667}, {"name": "00484", "time": 5.5333}, {"name": "00485", "time": 5.6}, {"name": "00486", "time": 5.6667}, {"name": "00487", "time": 5.7333}, {"name": "00488", "time": 5.8}, {"name": "00489", "time": 5.8667}, {"name": "00490", "time": 5.9333}, {"name": "00491", "time": 6}, {"name": "00492", "time": 6.0667}, {"name": "00493", "time": 6.1333}, {"name": "00494", "time": 6.2}, {"name": "00495", "time": 6.2667}, {"name": "00480", "time": 6.3333}, {"name": "00481", "time": 6.4}, {"name": "00482", "time": 6.4667}, {"name": "00483", "time": 6.5333}, {"name": "00484", "time": 6.6}, {"name": "00485", "time": 6.6667}, {"name": "00486", "time": 6.7333}, {"name": "00487", "time": 6.8}, {"name": "00488", "time": 6.8667}, {"name": "00489", "time": 6.9333}, {"name": "00490", "time": 7}, {"name": "00491", "time": 7.0667}, {"name": "00492", "time": 7.1333}, {"name": "00493", "time": 7.2}, {"name": "00494", "time": 7.2667}, {"name": "00495", "time": 7.3333}, {"name": "00480", "time": 7.4}, {"name": "00481", "time": 7.4667}, {"name": "00482", "time": 7.5333}, {"name": "00483", "time": 7.6}, {"name": "00484", "time": 7.6667}, {"name": "00485", "time": 7.7333}, {"name": "00486", "time": 7.8}, {"name": "00487", "time": 7.8667}, {"name": "00488", "time": 7.9333}, {"name": "00489", "time": 8}, {"name": "00490", "time": 8.0667}, {"name": "00491", "time": 8.1333}, {"name": "00492", "time": 8.2}, {"name": "00493", "time": 8.2667}, {"name": "00494", "time": 8.3333}, {"name": "00495", "time": 8.4}, {"name": "00480", "time": 8.4667}, {"name": "00481", "time": 8.5333}, {"name": "00482", "time": 8.6}, {"name": "00483", "time": 8.6667}, {"name": "00484", "time": 8.7333}, {"name": "00485", "time": 8.8}, {"name": "00486", "time": 8.8667}, {"name": "00487", "time": 8.9333}, {"name": "00488", "time": 9}, {"name": "00489", "time": 9.0667}, {"name": "00490", "time": 9.1333}, {"name": "00491", "time": 9.2}, {"name": "00492", "time": 9.2667}, {"name": "00493", "time": 9.3333}, {"name": "00494", "time": 9.4}, {"name": "00495", "time": 9.4667}]}, "light2": {"color": [{"color": "ffffff00"}, {"color": "ffffffff", "curve": "stepped", "time": 0.6667}, {"color": "ffffffff", "time": 7.0333}, {"color": "ffffff00", "time": 7.9667}]}}, "bones": {"light": {"scale": [{"time": 0.8667}, {"x": 1.794, "y": 1.794, "time": 0.9333}, {"time": 1.3333}]}, "texiao3": {"scale": [{"x": 0.071, "y": 0.071, "time": 0.9667}, {"x": 2.612, "y": 2.612, "time": 1.0667}, {"x": 2.975, "y": 2.975, "time": 1.4667}]}, "nohu": {"scale": [{"curve": "stepped", "x": 0.057, "y": 0.057}, {"x": 0.057, "y": 0.057, "time": 1.0333}, {"time": 1.2}, {"x": 1.098, "y": 1.098, "time": 2.6667}]}, "long": {"scale": [{"x": 0.547, "y": 0.547, "time": 1}, {"time": 1.1667}]}, "light2": {"scale": [{"x": 0.733, "y": 0.733}, {"time": 1}, {"x": 1.18, "y": 1.18, "time": 1.7333}, {"time": 2.4667}, {"x": 1.18, "y": 1.18, "time": 3.2}, {"time": 3.9333}, {"x": 1.18, "y": 1.18, "time": 4.6667}, {"time": 5.4}, {"x": 1.18, "y": 1.18, "time": 6.1333}, {"time": 6.8667}, {"x": 1.18, "y": 1.18, "time": 7.5}]}}}}}, [0]]], 0, 0, [0], [-1], [31]], [[{"name": "tai", "rect": [0, 0, 132, 88], "offset": [0, 0.5], "originalSize": [132, 89], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [32]], [[{"name": "tXU", "rect": [0, 0, 187, 65], "offset": [0, 0], "originalSize": [187, 65], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [33]], [[[17, "nohu", "\nnohu.png\nsize: 1988,1984\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n10000\n  rotate: false\n  xy: 1789, 1359\n  size: 194, 193\n  orig: 256, 256\n  offset: 31, 31\n  index: -1\n10002\n  rotate: false\n  xy: 1623, 91\n  size: 205, 205\n  orig: 256, 256\n  offset: 25, 24\n  index: -1\n10004\n  rotate: false\n  xy: 640, 218\n  size: 239, 239\n  orig: 256, 256\n  offset: 9, 8\n  index: -1\n10006\n  rotate: false\n  xy: 955, 4\n  size: 245, 245\n  orig: 256, 256\n  offset: 6, 5\n  index: -1\n10008\n  rotate: false\n  xy: 251, 394\n  size: 246, 245\n  orig: 256, 256\n  offset: 6, 5\n  index: -1\n10010\n  rotate: false\n  xy: 502, 667\n  size: 247, 246\n  orig: 256, 256\n  offset: 5, 4\n  index: -1\n10012\n  rotate: false\n  xy: 1005, 695\n  size: 247, 247\n  orig: 256, 256\n  offset: 5, 4\n  index: -1\n10014\n  rotate: false\n  xy: 1089, 260\n  size: 245, 245\n  orig: 256, 256\n  offset: 6, 5\n  index: -1\n10016\n  rotate: false\n  xy: 1202, 15\n  size: 243, 243\n  orig: 256, 256\n  offset: 7, 6\n  index: -1\n10018\n  rotate: false\n  xy: 1336, 279\n  size: 237, 238\n  orig: 256, 256\n  offset: 10, 7\n  index: -1\n10020\n  rotate: false\n  xy: 1462, 707\n  size: 233, 235\n  orig: 256, 256\n  offset: 12, 10\n  index: -1\n10022\n  rotate: true\n  xy: 1762, 1137\n  size: 220, 221\n  orig: 256, 256\n  offset: 18, 16\n  index: -1\n10024\n  rotate: false\n  xy: 1000, 507\n  size: 185, 186\n  orig: 256, 256\n  offset: 35, 33\n  index: -1\n10026\n  rotate: true\n  xy: 1832, 617\n  size: 166, 151\n  orig: 256, 256\n  offset: 41, 56\n  index: -1\n10028\n  rotate: false\n  xy: 1832, 473\n  size: 145, 142\n  orig: 256, 256\n  offset: 58, 55\n  index: -1\ndaodan\n  rotate: false\n  xy: 499, 408\n  size: 106, 49\n  orig: 108, 50\n  offset: 1, 0\n  index: -1\nplane\n  rotate: false\n  xy: 2, 1681\n  size: 525, 301\n  orig: 578, 384\n  offset: 39, 66\n  index: -1\nsmoke_07\n  rotate: false\n  xy: 1575, 306\n  size: 173, 213\n  orig: 216, 213\n  offset: 43, 0\n  index: -1\nsmoke_08\n  rotate: false\n  xy: 1808, 1769\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_09\n  rotate: false\n  xy: 1806, 1554\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_10\n  rotate: true\n  xy: 1757, 961\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_11\n  rotate: true\n  xy: 1757, 961\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_12\n  rotate: true\n  xy: 1757, 785\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_13\n  rotate: true\n  xy: 1757, 785\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_14\n  rotate: true\n  xy: 2, 465\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_15\n  rotate: true\n  xy: 2, 465\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_16\n  rotate: true\n  xy: 210, 218\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_17\n  rotate: true\n  xy: 210, 218\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_18\n  rotate: false\n  xy: 251, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_19\n  rotate: false\n  xy: 251, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_20\n  rotate: true\n  xy: 425, 218\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_21\n  rotate: true\n  xy: 425, 218\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_22\n  rotate: true\n  xy: 1617, 532\n  size: 173, 213\n  orig: 216, 213\n  offset: 43, 0\n  index: -1\nsmoke_23\n  rotate: true\n  xy: 1617, 532\n  size: 173, 213\n  orig: 216, 213\n  offset: 43, 0\n  index: -1\nsmoke_24\n  rotate: false\n  xy: 427, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_25\n  rotate: false\n  xy: 427, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_26\n  rotate: false\n  xy: 603, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_27\n  rotate: false\n  xy: 603, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_28\n  rotate: true\n  xy: 1750, 298\n  size: 173, 213\n  orig: 216, 213\n  offset: 43, 0\n  index: -1\nsmoke_29\n  rotate: true\n  xy: 1750, 298\n  size: 173, 213\n  orig: 216, 213\n  offset: 43, 0\n  index: -1\nsmoke_30\n  rotate: false\n  xy: 779, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_31\n  rotate: false\n  xy: 779, 3\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_32\n  rotate: true\n  xy: 1187, 519\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_33\n  rotate: true\n  xy: 1187, 519\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_34\n  rotate: true\n  xy: 1402, 521\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_35\n  rotate: true\n  xy: 1402, 521\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_36\n  rotate: false\n  xy: 1447, 64\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nsmoke_37\n  rotate: false\n  xy: 1447, 64\n  size: 174, 213\n  orig: 216, 213\n  offset: 42, 0\n  index: -1\nthunder_00\n  rotate: true\n  xy: 755, 915\n  size: 206, 248\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_01\n  rotate: true\n  xy: 751, 707\n  size: 206, 247\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_02\n  rotate: true\n  xy: 758, 1331\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_03\n  rotate: true\n  xy: 2, 1265\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_04\n  rotate: true\n  xy: 253, 1265\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_05\n  rotate: true\n  xy: 1554, 1568\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_06\n  rotate: true\n  xy: 1046, 1776\n  size: 206, 252\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_07\n  rotate: true\n  xy: 1300, 1776\n  size: 206, 252\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_08\n  rotate: true\n  xy: 1554, 1776\n  size: 206, 252\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_09\n  rotate: true\n  xy: 529, 1539\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_10\n  rotate: true\n  xy: 1046, 1568\n  size: 206, 252\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_11\n  rotate: true\n  xy: 1300, 1568\n  size: 206, 252\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_12\n  rotate: true\n  xy: 2, 849\n  size: 206, 248\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_13\n  rotate: true\n  xy: 1009, 1152\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_14\n  rotate: true\n  xy: 781, 1539\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_15\n  rotate: true\n  xy: 2, 1473\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_16\n  rotate: true\n  xy: 504, 1123\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_17\n  rotate: true\n  xy: 254, 1473\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_18\n  rotate: true\n  xy: 252, 849\n  size: 206, 248\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_19\n  rotate: true\n  xy: 1257, 944\n  size: 206, 248\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_20\n  rotate: true\n  xy: 755, 1123\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_21\n  rotate: true\n  xy: 751, 499\n  size: 206, 247\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_22\n  rotate: true\n  xy: 1033, 1360\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_23\n  rotate: true\n  xy: 1285, 1360\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_24\n  rotate: true\n  xy: 1537, 1360\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_25\n  rotate: true\n  xy: 2, 1057\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_26\n  rotate: true\n  xy: 253, 1057\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_27\n  rotate: true\n  xy: 2, 641\n  size: 206, 247\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_28\n  rotate: true\n  xy: 251, 641\n  size: 206, 247\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_29\n  rotate: true\n  xy: 1260, 1152\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_30\n  rotate: true\n  xy: 1511, 1152\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_31\n  rotate: false\n  xy: 1254, 697\n  size: 206, 245\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_32\n  rotate: false\n  xy: 881, 251\n  size: 206, 246\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_33\n  rotate: true\n  xy: 500, 459\n  size: 206, 247\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_34\n  rotate: true\n  xy: 1006, 944\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 0\n  index: -1\nthunder_35\n  rotate: false\n  xy: 2, 210\n  size: 206, 247\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_36\n  rotate: true\n  xy: 2, 2\n  size: 206, 247\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_37\n  rotate: true\n  xy: 1507, 944\n  size: 206, 248\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_38\n  rotate: true\n  xy: 504, 915\n  size: 206, 249\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\nthunder_39\n  rotate: true\n  xy: 506, 1331\n  size: 206, 250\n  orig: 206, 252\n  offset: 0, 2\n  index: -1\ntittle\n  rotate: false\n  xy: 529, 1747\n  size: 515, 235\n  orig: 517, 239\n  offset: 1, 4\n  index: -1\n", ["nohu.png"], {"skins": [{"name": "default", "attachments": {"plane": {"plane": {"width": 578, "height": 384}}, "boom": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}, "daodan": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "daodan2": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "daodan3": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "daodan4": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "daodan5": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "daodan6": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "daodan7": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "daodan8": {"daodan": {"rotation": -19.38, "x": 53.91, "width": 108, "y": 1.03, "height": 50}}, "tittle2": {"tittle": {"x": 0.5, "width": 517, "height": 239}}, "tittle": {"tittle": {"x": 0.5, "width": 517, "height": 239}}, "yan": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "yan6": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "yan7": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "yan8": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "boom4": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}, "effect2": {"thunder_12": {"width": 206, "height": 252}, "thunder_34": {"width": 206, "height": 252}, "thunder_13": {"width": 206, "height": 252}, "thunder_35": {"width": 206, "height": 252}, "thunder_14": {"width": 206, "height": 252}, "thunder_36": {"width": 206, "height": 252}, "thunder_15": {"width": 206, "height": 252}, "thunder_37": {"width": 206, "height": 252}, "thunder_30": {"width": 206, "height": 252}, "thunder_31": {"width": 206, "height": 252}, "thunder_10": {"width": 206, "height": 252}, "thunder_32": {"width": 206, "height": 252}, "thunder_11": {"width": 206, "height": 252}, "thunder_33": {"width": 206, "height": 252}, "thunder_09": {"width": 206, "height": 252}, "thunder_05": {"width": 206, "height": 252}, "thunder_27": {"width": 206, "height": 252}, "thunder_06": {"width": 206, "height": 252}, "thunder_28": {"width": 206, "height": 252}, "thunder_07": {"width": 206, "height": 252}, "thunder_29": {"width": 206, "height": 252}, "thunder_08": {"width": 206, "height": 252}, "thunder_01": {"width": 206, "height": 252}, "thunder_23": {"width": 206, "height": 252}, "thunder_02": {"width": 206, "height": 252}, "thunder_24": {"width": 206, "height": 252}, "thunder_03": {"width": 206, "height": 252}, "thunder_25": {"width": 206, "height": 252}, "thunder_04": {"width": 206, "height": 252}, "thunder_26": {"width": 206, "height": 252}, "thunder_20": {"width": 206, "height": 252}, "thunder_21": {"width": 206, "height": 252}, "thunder_00": {"width": 206, "height": 252}, "thunder_22": {"width": 206, "height": 252}, "thunder_16": {"width": 206, "height": 252}, "thunder_38": {"width": 206, "height": 252}, "thunder_17": {"width": 206, "height": 252}, "thunder_39": {"width": 206, "height": 252}, "thunder_18": {"width": 206, "height": 252}, "thunder_19": {"width": 206, "height": 252}}, "boom3": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}, "boom6": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}, "boom5": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}, "yan2": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "boom8": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}, "yan3": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "boom7": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}, "yan4": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "yan5": {"smoke_10": {"width": 216, "y": 0.5, "height": 213}, "smoke_32": {"width": 216, "y": 0.5, "height": 213}, "smoke_31": {"width": 216, "y": 0.5, "height": 213}, "smoke_30": {"width": 216, "y": 0.5, "height": 213}, "smoke_19": {"width": 216, "y": 0.5, "height": 213}, "smoke_14": {"width": 216, "y": 0.5, "height": 213}, "smoke_36": {"width": 216, "y": 0.5, "height": 213}, "smoke_13": {"width": 216, "y": 0.5, "height": 213}, "smoke_35": {"width": 216, "y": 0.5, "height": 213}, "smoke_12": {"width": 216, "y": 0.5, "height": 213}, "smoke_34": {"width": 216, "y": 0.5, "height": 213}, "smoke_11": {"width": 216, "y": 0.5, "height": 213}, "smoke_33": {"width": 216, "y": 0.5, "height": 213}, "smoke_18": {"width": 216, "y": 0.5, "height": 213}, "smoke_17": {"width": 216, "y": 0.5, "height": 213}, "smoke_16": {"width": 216, "y": 0.5, "height": 213}, "smoke_15": {"width": 216, "y": 0.5, "height": 213}, "smoke_37": {"width": 216, "y": 0.5, "height": 213}, "smoke_21": {"width": 216, "y": 0.5, "height": 213}, "smoke_20": {"width": 216, "y": 0.5, "height": 213}, "smoke_09": {"width": 216, "y": 0.5, "height": 213}, "smoke_08": {"width": 216, "y": 0.5, "height": 213}, "smoke_25": {"width": 216, "y": 0.5, "height": 213}, "smoke_24": {"width": 216, "y": 0.5, "height": 213}, "smoke_23": {"width": 216, "y": 0.5, "height": 213}, "smoke_22": {"width": 216, "y": 0.5, "height": 213}, "smoke_07": {"width": 216, "y": 0.5, "height": 213}, "smoke_29": {"width": 216, "y": 0.5, "height": 213}, "smoke_28": {"width": 216, "y": 0.5, "height": 213}, "smoke_27": {"width": 216, "y": 0.5, "height": 213}, "smoke_26": {"width": 216, "y": 0.5, "height": 213}}, "effect": {"thunder_12": {"width": 206, "height": 252}, "thunder_34": {"width": 206, "height": 252}, "thunder_13": {"width": 206, "height": 252}, "thunder_35": {"width": 206, "height": 252}, "thunder_14": {"width": 206, "height": 252}, "thunder_36": {"width": 206, "height": 252}, "thunder_15": {"width": 206, "height": 252}, "thunder_37": {"width": 206, "height": 252}, "thunder_30": {"width": 206, "height": 252}, "thunder_31": {"width": 206, "height": 252}, "thunder_10": {"width": 206, "height": 252}, "thunder_32": {"width": 206, "height": 252}, "thunder_11": {"width": 206, "height": 252}, "thunder_33": {"width": 206, "height": 252}, "thunder_09": {"width": 206, "height": 252}, "thunder_05": {"width": 206, "height": 252}, "thunder_27": {"width": 206, "height": 252}, "thunder_06": {"width": 206, "height": 252}, "thunder_28": {"width": 206, "height": 252}, "thunder_07": {"width": 206, "height": 252}, "thunder_29": {"width": 206, "height": 252}, "thunder_08": {"width": 206, "height": 252}, "thunder_01": {"width": 206, "height": 252}, "thunder_23": {"width": 206, "height": 252}, "thunder_02": {"width": 206, "height": 252}, "thunder_24": {"width": 206, "height": 252}, "thunder_03": {"width": 206, "height": 252}, "thunder_25": {"width": 206, "height": 252}, "thunder_04": {"width": 206, "height": 252}, "thunder_26": {"width": 206, "height": 252}, "thunder_20": {"width": 206, "height": 252}, "thunder_21": {"width": 206, "height": 252}, "thunder_00": {"width": 206, "height": 252}, "thunder_22": {"width": 206, "height": 252}, "thunder_16": {"width": 206, "height": 252}, "thunder_38": {"width": 206, "height": 252}, "thunder_17": {"width": 206, "height": 252}, "thunder_39": {"width": 206, "height": 252}, "thunder_18": {"width": 206, "height": 252}, "thunder_19": {"width": 206, "height": 252}}, "boom2": {"10000": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10002": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10004": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10006": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10008": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10010": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10012": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10014": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10016": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10018": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10020": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10022": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10024": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10026": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}, "10028": {"x": 4.56, "width": 256, "y": -1.77, "height": 256}}}}], "skeleton": {"images": "", "x": -1052.52, "width": 2348.15, "y": -928.24, "spine": "3.8.75", "audio": "G:/go88/新大小/豹子", "hash": "dBVS2Vm4QyaoInLWAkJjg+fAnS8", "height": 1684.44}, "slots": [{"attachment": "da<PERSON>n", "name": "da<PERSON>n", "bone": "da<PERSON>n"}, {"attachment": "da<PERSON>n", "name": "daodan3", "bone": "daodan3"}, {"attachment": "da<PERSON>n", "name": "daodan8", "bone": "daodan8"}, {"attachment": "da<PERSON>n", "name": "daodan4", "bone": "daodan4"}, {"attachment": "da<PERSON>n", "name": "daodan5", "bone": "daodan5"}, {"attachment": "da<PERSON>n", "name": "daodan6", "bone": "daodan6"}, {"attachment": "da<PERSON>n", "name": "daodan7", "bone": "daodan7"}, {"attachment": "da<PERSON>n", "name": "daodan2", "bone": "daodan2"}, {"attachment": "smoke_37", "name": "yan", "bone": "yan"}, {"attachment": "smoke_37", "name": "yan3", "bone": "yan3"}, {"attachment": "smoke_37", "name": "yan8", "bone": "yan8"}, {"attachment": "smoke_37", "name": "yan4", "bone": "yan4"}, {"attachment": "smoke_37", "name": "yan5", "bone": "yan5"}, {"attachment": "smoke_37", "name": "yan6", "bone": "yan6"}, {"attachment": "smoke_37", "name": "yan7", "bone": "yan7"}, {"attachment": "smoke_37", "name": "yan2", "bone": "yan2"}, {"attachment": "10028", "name": "boom", "bone": "daodanall2"}, {"attachment": "10028", "name": "boom3", "bone": "daodanall6"}, {"attachment": "10028", "name": "boom8", "bone": "daodanall16"}, {"attachment": "10028", "name": "boom4", "bone": "daodanall8"}, {"attachment": "10028", "name": "boom5", "bone": "daodanall10"}, {"attachment": "10028", "name": "boom6", "bone": "daodanall12"}, {"attachment": "10028", "name": "boom7", "bone": "daodanall14"}, {"attachment": "10028", "name": "boom2", "bone": "daodanall4"}, {"attachment": "plane", "name": "plane", "bone": "plane"}, {"name": "tittle", "bone": "tittle"}, {"blend": "additive", "name": "tittle2", "bone": "tittle"}, {"attachment": "thunder_39", "blend": "additive", "name": "effect", "bone": "effect"}, {"attachment": "thunder_39", "blend": "additive", "name": "effect2", "bone": "effect2"}], "bones": [{"name": "root"}, {"scaleX": 1.4791, "parent": "root", "scaleY": 1.4791, "name": "all"}, {"parent": "all", "name": "plane"}, {"parent": "all", "rotation": 15.68, "name": "<PERSON><PERSON><PERSON><PERSON>", "x": 94.7, "y": -284.3}, {"scaleX": 1.6771, "parent": "<PERSON><PERSON><PERSON><PERSON>", "scaleY": 1.9504, "rotation": 41.23, "name": "da<PERSON>n", "length": 90.91}, {"scaleX": 2.1572, "parent": "<PERSON><PERSON><PERSON><PERSON>", "scaleY": 2.1572, "name": "daodanall2", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "<PERSON><PERSON><PERSON><PERSON>", "scaleY": 3.3621, "rotation": 110.48, "name": "yan", "x": 414.49, "y": 252.89}, {"scaleX": 0.5751, "parent": "all", "scaleY": 0.5751, "rotation": -8.73, "name": "daodanall3", "x": -119.09, "y": 120.16}, {"scaleX": 1.6771, "parent": "daodanall3", "scaleY": 1.9504, "rotation": 41.23, "name": "daodan2", "length": 90.91}, {"scaleX": 1.2475, "parent": "daodanall3", "scaleY": 1.2475, "name": "daodanall4", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "daodanall3", "scaleY": 3.3621, "rotation": 110.48, "name": "yan2", "x": 414.49, "y": 252.89}, {"scaleX": 0.7906, "parent": "all", "scaleY": 0.7906, "rotation": 25.76, "name": "daodanall5", "x": 335.45, "y": -234.23}, {"scaleX": 1.6771, "parent": "daodanall5", "scaleY": 1.9504, "rotation": 41.23, "name": "daodan3", "length": 90.91}, {"scaleX": 1.2475, "parent": "daodanall5", "scaleY": 1.2475, "name": "daodanall6", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "daodanall5", "scaleY": 3.3621, "rotation": 110.48, "name": "yan3", "x": 414.49, "y": 252.89}, {"scaleX": 1.308, "parent": "all", "scaleY": 1.308, "rotation": -0.56, "name": "daodanall7", "x": -361.76, "y": -268.9}, {"scaleX": 1.6771, "parent": "daodanall7", "scaleY": 1.9504, "rotation": 41.23, "name": "daodan4", "length": 90.91}, {"scaleX": 2.2325, "parent": "daodanall7", "scaleY": 2.2325, "name": "daodanall8", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "daodanall7", "scaleY": 3.3621, "rotation": 110.48, "name": "yan4", "x": 414.49, "y": 252.89}, {"scaleX": 1.0561, "parent": "all", "scaleY": 1.0561, "rotation": -8.13, "name": "daodanall9", "x": -323.24, "y": -66.67}, {"scaleX": 1.6771, "parent": "daodanall9", "scaleY": 1.9504, "rotation": 41.23, "name": "daodan5", "length": 90.91}, {"scaleX": 1.2475, "parent": "daodanall9", "scaleY": 1.2475, "name": "daodanall10", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "daodanall9", "scaleY": 3.3621, "rotation": 110.48, "name": "yan5", "x": 414.49, "y": 252.89}, {"scaleX": 0.5679, "parent": "all", "scaleY": 0.5679, "rotation": -15.49, "name": "daodanall11", "x": -184.57, "y": 176.01}, {"scaleX": 1.6771, "parent": "daodanall11", "scaleY": 1.9504, "rotation": 41.23, "name": "daodan6", "length": 90.91}, {"scaleX": 1.2475, "parent": "daodanall11", "scaleY": 1.2475, "name": "daodanall12", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "daodanall11", "scaleY": 3.3621, "rotation": 110.48, "name": "yan6", "x": 414.49, "y": 252.89}, {"scaleX": 0.7377, "parent": "all", "scaleY": 0.7377, "name": "tittle", "x": 8.34, "y": 141.09}, {"scaleX": 0.5679, "parent": "all", "scaleY": 0.5679, "rotation": 22.78, "name": "daodanall13", "x": 234.89, "y": -172.32}, {"scaleX": 1.6771, "parent": "daodanall13", "scaleY": 1.9504, "rotation": 41.23, "name": "daodan7", "length": 90.91}, {"scaleX": 1.2475, "parent": "daodanall13", "scaleY": 1.2475, "name": "daodanall14", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "daodanall13", "scaleY": 3.3621, "rotation": 110.48, "name": "yan7", "x": 414.49, "y": 252.89}, {"scaleX": 1.0793, "parent": "all", "scaleY": 1.0793, "rotation": 2.64, "name": "daodanall15", "x": -218.93, "y": -278.38}, {"scaleX": 1.6771, "parent": "daodanall15", "scaleY": 1.9504, "rotation": 41.23, "name": "daodan8", "length": 90.91}, {"scaleX": 1.2475, "parent": "daodanall15", "scaleY": 1.2475, "name": "daodanall16", "x": 10.78, "y": 18.5}, {"scaleX": 2.2276, "parent": "daodanall15", "scaleY": 3.3621, "rotation": 110.48, "name": "yan8", "x": 414.49, "y": 252.89}, {"scaleX": 2.7274, "parent": "tittle", "scaleY": 1.8587, "rotation": 90, "name": "effect", "x": -114.49, "y": 10.97}, {"scaleX": 2.7274, "parent": "tittle", "scaleY": -1.8587, "rotation": 90, "name": "effect2", "x": 151.46, "y": 5.02}], "animations": {"idle": {"slots": {"plane": {"color": [{"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.3333}], "attachment": [{"name": null}, {"name": "plane", "time": 0.3333}]}, "boom": {"attachment": [{"name": null}, {"name": "10000", "time": 0.5}, {"name": "10002", "time": 0.5333}, {"name": "10004", "time": 0.5667}, {"name": "10006", "time": 0.6}, {"name": "10008", "time": 0.6333}, {"name": "10010", "time": 0.6667}, {"name": "10012", "time": 0.7}, {"name": "10014", "time": 0.7333}, {"name": "10016", "time": 0.7667}, {"name": "10018", "time": 0.8}, {"name": "10020", "time": 0.8333}, {"name": "10022", "time": 0.8667}, {"name": "10024", "time": 0.9}, {"name": "10026", "time": 0.9333}, {"name": "10028", "time": 0.9667}, {"name": null, "time": 1}, {"name": "10000", "time": 1.8333}, {"name": "10002", "time": 1.8667}, {"name": "10004", "time": 1.9}, {"name": "10006", "time": 1.9333}, {"name": "10008", "time": 1.9667}, {"name": "10010", "time": 2}, {"name": "10012", "time": 2.0333}, {"name": "10014", "time": 2.0667}, {"name": "10016", "time": 2.1}, {"name": "10018", "time": 2.1333}, {"name": "10020", "time": 2.1667}, {"name": "10022", "time": 2.2}, {"name": "10024", "time": 2.2333}, {"name": "10026", "time": 2.2667}, {"name": "10028", "time": 2.3}, {"name": null, "time": 2.3333}]}, "daodan": {"attachment": [{"name": null}, {"name": "da<PERSON>n", "time": 0.1667}, {"name": null, "time": 0.5333}]}, "daodan2": {"attachment": [{"name": null}, {"name": "da<PERSON>n", "time": 0.2333}, {"name": null, "time": 0.6}]}, "daodan3": {"attachment": [{"name": null}, {"name": "da<PERSON>n", "time": 0.5333}, {"name": null, "time": 0.9}]}, "daodan4": {"attachment": [{"name": null}, {"name": "da<PERSON>n", "time": 0.6}, {"name": null, "time": 0.9667}]}, "daodan5": {"attachment": [{"name": null}, {"name": "da<PERSON>n", "time": 0.7333}, {"name": null, "time": 1.1}]}, "daodan6": {"attachment": [{"name": null, "time": 0.3667}]}, "daodan7": {"attachment": [{"name": null, "time": 0.3667}]}, "daodan8": {"attachment": [{"name": null}, {"name": "da<PERSON>n", "time": 0.5333}, {"name": null, "time": 0.9}]}, "tittle2": {"color": [{"color": "ffffff00", "time": 0.9}, {"color": "ffffffa2", "time": 1.0667}, {"color": "ffffffd5", "time": 1.4333}, {"color": "ffffff00", "time": 1.6667}], "attachment": [{"name": "tittle", "time": 0.9}]}, "tittle": {"color": [{"color": "ffffffff", "time": 3.5}, {"color": "ffffff00", "time": 4}], "attachment": [{"name": "tittle", "time": 0.9}]}, "yan": {"color": [{"color": "ffffffff", "time": 0.6333}, {"color": "ffffff00", "time": 1.0667}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.3667}, {"name": "smoke_08", "time": 0.4}, {"name": "smoke_09", "time": 0.4333}, {"name": "smoke_10", "time": 0.4667}, {"name": "smoke_11", "time": 0.5}, {"name": "smoke_12", "time": 0.5333}, {"name": "smoke_13", "time": 0.5667}, {"name": "smoke_14", "time": 0.6}, {"name": "smoke_15", "time": 0.6333}, {"name": "smoke_16", "time": 0.6667}, {"name": "smoke_17", "time": 0.7}, {"name": "smoke_18", "time": 0.7333}, {"name": "smoke_19", "time": 0.7667}]}, "yan6": {"color": [{"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "time": 0.9}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.2}, {"name": "smoke_08", "time": 0.2333}, {"name": "smoke_09", "time": 0.2667}, {"name": "smoke_10", "time": 0.3}, {"name": "smoke_11", "time": 0.3333}, {"name": "smoke_12", "time": 0.3667}, {"name": "smoke_13", "time": 0.4}, {"name": "smoke_14", "time": 0.4333}, {"name": "smoke_15", "time": 0.4667}, {"name": "smoke_16", "time": 0.5}, {"name": "smoke_17", "time": 0.5333}, {"name": "smoke_18", "time": 0.5667}, {"name": "smoke_19", "time": 0.6}]}, "yan7": {"color": [{"color": "ffffffff", "time": 0.4667}, {"color": "ffffff00", "time": 0.9}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.2}, {"name": "smoke_08", "time": 0.2333}, {"name": "smoke_09", "time": 0.2667}, {"name": "smoke_10", "time": 0.3}, {"name": "smoke_11", "time": 0.3333}, {"name": "smoke_12", "time": 0.3667}, {"name": "smoke_13", "time": 0.4}, {"name": "smoke_14", "time": 0.4333}, {"name": "smoke_15", "time": 0.4667}, {"name": "smoke_16", "time": 0.5}, {"name": "smoke_17", "time": 0.5333}, {"name": "smoke_18", "time": 0.5667}, {"name": "smoke_19", "time": 0.6}]}, "yan8": {"color": [{"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.4333}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.7333}, {"name": "smoke_08", "time": 0.7667}, {"name": "smoke_09", "time": 0.8}, {"name": "smoke_10", "time": 0.8333}, {"name": "smoke_11", "time": 0.8667}, {"name": "smoke_12", "time": 0.9}, {"name": "smoke_13", "time": 0.9333}, {"name": "smoke_14", "time": 0.9667}, {"name": "smoke_15", "time": 1}, {"name": "smoke_16", "time": 1.0333}, {"name": "smoke_17", "time": 1.0667}, {"name": "smoke_18", "time": 1.1}, {"name": "smoke_19", "time": 1.1333}]}, "boom4": {"attachment": [{"name": null}, {"name": "10000", "time": 0.9333}, {"name": "10002", "time": 0.9667}, {"name": "10004", "time": 1}, {"name": "10006", "time": 1.0333}, {"name": "10008", "time": 1.0667}, {"name": "10010", "time": 1.1}, {"name": "10012", "time": 1.1333}, {"name": "10014", "time": 1.1667}, {"name": "10016", "time": 1.2}, {"name": "10018", "time": 1.2333}, {"name": "10020", "time": 1.2667}, {"name": "10022", "time": 1.3}, {"name": "10024", "time": 1.3333}, {"name": "10026", "time": 1.3667}, {"name": "10028", "time": 1.4}, {"name": null, "time": 1.4333}, {"name": "10000", "time": 1.6333}, {"name": "10002", "time": 1.6667}, {"name": "10004", "time": 1.7}, {"name": "10006", "time": 1.7333}, {"name": "10008", "time": 1.7667}, {"name": "10010", "time": 1.8}, {"name": "10012", "time": 1.8333}, {"name": "10014", "time": 1.8667}, {"name": "10016", "time": 1.9}, {"name": "10018", "time": 1.9333}, {"name": "10020", "time": 1.9667}, {"name": "10022", "time": 2}, {"name": "10024", "time": 2.0333}, {"name": "10026", "time": 2.0667}, {"name": "10028", "time": 2.1}, {"name": null, "time": 2.1333}]}, "boom3": {"attachment": [{"name": null}, {"name": "10000", "time": 0.8667}, {"name": "10002", "time": 0.9}, {"name": "10004", "time": 0.9333}, {"name": "10006", "time": 0.9667}, {"name": "10008", "time": 1}, {"name": "10010", "time": 1.0333}, {"name": "10012", "time": 1.0667}, {"name": "10014", "time": 1.1}, {"name": "10016", "time": 1.1333}, {"name": "10018", "time": 1.1667}, {"name": "10020", "time": 1.2}, {"name": "10022", "time": 1.2333}, {"name": "10024", "time": 1.2667}, {"name": "10026", "time": 1.3}, {"name": "10028", "time": 1.3333}, {"name": null, "time": 1.3667}, {"name": "10000", "time": 2.2667}, {"name": "10002", "time": 2.3}, {"name": "10004", "time": 2.3333}, {"name": "10006", "time": 2.3667}, {"name": "10008", "time": 2.4}, {"name": "10010", "time": 2.4333}, {"name": "10012", "time": 2.4667}, {"name": "10014", "time": 2.5}, {"name": "10016", "time": 2.5333}, {"name": "10018", "time": 2.5667}, {"name": "10020", "time": 2.6}, {"name": "10022", "time": 2.6333}, {"name": "10024", "time": 2.6667}, {"name": "10026", "time": 2.7}, {"name": "10028", "time": 2.7333}, {"name": null, "time": 2.7667}]}, "effect2": {"color": [{"color": "ffffff8b", "curve": "stepped", "time": 0.9}, {"color": "ffffff8b", "time": 3.6667}, {"color": "ffffff00", "time": 4}], "attachment": [{"name": null}, {"name": "thunder_00", "time": 0.9}, {"name": "thunder_01", "time": 0.9333}, {"name": "thunder_02", "time": 0.9667}, {"name": "thunder_03", "time": 1}, {"name": "thunder_04", "time": 1.0333}, {"name": "thunder_05", "time": 1.0667}, {"name": "thunder_06", "time": 1.1}, {"name": "thunder_07", "time": 1.1333}, {"name": "thunder_08", "time": 1.1667}, {"name": "thunder_09", "time": 1.2}, {"name": "thunder_10", "time": 1.2333}, {"name": "thunder_11", "time": 1.2667}, {"name": "thunder_12", "time": 1.3}, {"name": "thunder_13", "time": 1.3333}, {"name": "thunder_14", "time": 1.3667}, {"name": "thunder_15", "time": 1.4}, {"name": "thunder_16", "time": 1.4333}, {"name": "thunder_17", "time": 1.4667}, {"name": "thunder_18", "time": 1.5}, {"name": "thunder_19", "time": 1.5333}, {"name": "thunder_20", "time": 1.5667}, {"name": "thunder_21", "time": 1.6}, {"name": "thunder_22", "time": 1.6333}, {"name": "thunder_23", "time": 1.6667}, {"name": "thunder_24", "time": 1.7}, {"name": "thunder_25", "time": 1.7333}, {"name": "thunder_26", "time": 1.7667}, {"name": "thunder_27", "time": 1.8}, {"name": "thunder_28", "time": 1.8333}, {"name": "thunder_29", "time": 1.8667}, {"name": "thunder_30", "time": 1.9}, {"name": "thunder_31", "time": 1.9333}, {"name": "thunder_32", "time": 1.9667}, {"name": "thunder_33", "time": 2}, {"name": "thunder_34", "time": 2.0333}, {"name": "thunder_35", "time": 2.0667}, {"name": "thunder_36", "time": 2.1}, {"name": "thunder_37", "time": 2.1333}, {"name": "thunder_38", "time": 2.1667}, {"name": "thunder_39", "time": 2.2}, {"name": "thunder_00", "time": 2.2333}, {"name": "thunder_01", "time": 2.2667}, {"name": "thunder_02", "time": 2.3}, {"name": "thunder_03", "time": 2.3333}, {"name": "thunder_04", "time": 2.3667}, {"name": "thunder_05", "time": 2.4}, {"name": "thunder_06", "time": 2.4333}, {"name": "thunder_07", "time": 2.4667}, {"name": "thunder_08", "time": 2.5}, {"name": "thunder_09", "time": 2.5333}, {"name": "thunder_10", "time": 2.5667}, {"name": "thunder_11", "time": 2.6}, {"name": "thunder_12", "time": 2.6333}, {"name": "thunder_13", "time": 2.6667}, {"name": "thunder_14", "time": 2.7}, {"name": "thunder_15", "time": 2.7333}, {"name": "thunder_16", "time": 2.7667}, {"name": "thunder_17", "time": 2.8}, {"name": "thunder_18", "time": 2.8333}, {"name": "thunder_19", "time": 2.8667}, {"name": "thunder_20", "time": 2.9}, {"name": "thunder_21", "time": 2.9333}, {"name": "thunder_22", "time": 2.9667}, {"name": "thunder_23", "time": 3}, {"name": "thunder_24", "time": 3.0333}, {"name": "thunder_25", "time": 3.0667}, {"name": "thunder_26", "time": 3.1}, {"name": "thunder_27", "time": 3.1333}, {"name": "thunder_28", "time": 3.1667}, {"name": "thunder_29", "time": 3.2}, {"name": "thunder_30", "time": 3.2333}, {"name": "thunder_31", "time": 3.2667}, {"name": "thunder_32", "time": 3.3}, {"name": "thunder_33", "time": 3.3333}, {"name": "thunder_34", "time": 3.3667}, {"name": "thunder_35", "time": 3.4}, {"name": "thunder_36", "time": 3.4333}, {"name": "thunder_37", "time": 3.4667}, {"name": "thunder_38", "time": 3.5}, {"name": "thunder_39", "time": 3.5333}, {"name": "thunder_00", "time": 3.5667}, {"name": "thunder_01", "time": 3.6}, {"name": "thunder_02", "time": 3.6333}, {"name": "thunder_03", "time": 3.6667}, {"name": "thunder_04", "time": 3.7}, {"name": "thunder_05", "time": 3.7333}, {"name": "thunder_06", "time": 3.7667}, {"name": "thunder_07", "time": 3.8}, {"name": "thunder_08", "time": 3.8333}, {"name": "thunder_09", "time": 3.8667}, {"name": "thunder_10", "time": 3.9}, {"name": "thunder_11", "time": 3.9333}, {"name": "thunder_12", "time": 3.9667}, {"name": "thunder_13", "time": 4}]}, "boom6": {"attachment": [{"name": null}, {"name": "10000", "time": 0.3333}, {"name": "10002", "time": 0.3667}, {"name": "10004", "time": 0.4}, {"name": "10006", "time": 0.4333}, {"name": "10008", "time": 0.4667}, {"name": "10010", "time": 0.5}, {"name": "10012", "time": 0.5333}, {"name": "10014", "time": 0.5667}, {"name": "10016", "time": 0.6}, {"name": "10018", "time": 0.6333}, {"name": "10020", "time": 0.6667}, {"name": "10022", "time": 0.7}, {"name": "10024", "time": 0.7333}, {"name": "10026", "time": 0.7667}, {"name": "10028", "time": 0.8}, {"name": null, "time": 0.8333}, {"name": "10000", "time": 2.4667}, {"name": "10002", "time": 2.5}, {"name": "10004", "time": 2.5333}, {"name": "10006", "time": 2.5667}, {"name": "10008", "time": 2.6}, {"name": "10010", "time": 2.6333}, {"name": "10012", "time": 2.6667}, {"name": "10014", "time": 2.7}, {"name": "10016", "time": 2.7333}, {"name": "10018", "time": 2.7667}, {"name": "10020", "time": 2.8}, {"name": "10022", "time": 2.8333}, {"name": "10024", "time": 2.8667}, {"name": "10026", "time": 2.9}, {"name": "10028", "time": 2.9333}, {"name": null, "time": 2.9667}]}, "boom5": {"attachment": [{"name": null}, {"name": "10000", "time": 1.0667}, {"name": "10002", "time": 1.1}, {"name": "10004", "time": 1.1333}, {"name": "10006", "time": 1.1667}, {"name": "10008", "time": 1.2}, {"name": "10010", "time": 1.2333}, {"name": "10012", "time": 1.2667}, {"name": "10014", "time": 1.3}, {"name": "10016", "time": 1.3333}, {"name": "10018", "time": 1.3667}, {"name": "10020", "time": 1.4}, {"name": "10022", "time": 1.4333}, {"name": "10024", "time": 1.4667}, {"name": "10026", "time": 1.5}, {"name": "10028", "time": 1.5333}, {"name": null, "time": 1.5667}, {"name": "10000", "time": 2.1667}, {"name": "10002", "time": 2.2}, {"name": "10004", "time": 2.2333}, {"name": "10006", "time": 2.2667}, {"name": "10008", "time": 2.3}, {"name": "10010", "time": 2.3333}, {"name": "10012", "time": 2.3667}, {"name": "10014", "time": 2.4}, {"name": "10016", "time": 2.4333}, {"name": "10018", "time": 2.4667}, {"name": "10020", "time": 2.5}, {"name": "10022", "time": 2.5333}, {"name": "10024", "time": 2.5667}, {"name": "10026", "time": 2.6}, {"name": "10028", "time": 2.6333}, {"name": null, "time": 2.6667}]}, "yan2": {"color": [{"color": "ffffffff", "time": 0.7}, {"color": "ffffff00", "time": 1.1333}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.4333}, {"name": "smoke_08", "time": 0.4667}, {"name": "smoke_09", "time": 0.5}, {"name": "smoke_10", "time": 0.5333}, {"name": "smoke_11", "time": 0.5667}, {"name": "smoke_12", "time": 0.6}, {"name": "smoke_13", "time": 0.6333}, {"name": "smoke_14", "time": 0.6667}, {"name": "smoke_15", "time": 0.7}, {"name": "smoke_16", "time": 0.7333}, {"name": "smoke_17", "time": 0.7667}, {"name": "smoke_18", "time": 0.8}, {"name": "smoke_19", "time": 0.8333}]}, "boom8": {"attachment": [{"name": null}, {"name": "10000", "time": 0.8667}, {"name": "10002", "time": 0.9}, {"name": "10004", "time": 0.9333}, {"name": "10006", "time": 0.9667}, {"name": "10008", "time": 1}, {"name": "10010", "time": 1.0333}, {"name": "10012", "time": 1.0667}, {"name": "10014", "time": 1.1}, {"name": "10016", "time": 1.1333}, {"name": "10018", "time": 1.1667}, {"name": "10020", "time": 1.2}, {"name": "10022", "time": 1.2333}, {"name": "10024", "time": 1.2667}, {"name": "10026", "time": 1.3}, {"name": "10028", "time": 1.3333}, {"name": null, "time": 1.3667}]}, "yan3": {"color": [{"color": "ffffffff", "time": 1}, {"color": "ffffff00", "time": 1.4333}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.7333}, {"name": "smoke_08", "time": 0.7667}, {"name": "smoke_09", "time": 0.8}, {"name": "smoke_10", "time": 0.8333}, {"name": "smoke_11", "time": 0.8667}, {"name": "smoke_12", "time": 0.9}, {"name": "smoke_13", "time": 0.9333}, {"name": "smoke_14", "time": 0.9667}, {"name": "smoke_15", "time": 1}, {"name": "smoke_16", "time": 1.0333}, {"name": "smoke_17", "time": 1.0667}, {"name": "smoke_18", "time": 1.1}, {"name": "smoke_19", "time": 1.1333}]}, "boom7": {"attachment": [{"name": null}, {"name": "10000", "time": 0.3333}, {"name": "10002", "time": 0.3667}, {"name": "10004", "time": 0.4}, {"name": "10006", "time": 0.4333}, {"name": "10008", "time": 0.4667}, {"name": "10010", "time": 0.5}, {"name": "10012", "time": 0.5333}, {"name": "10014", "time": 0.5667}, {"name": "10016", "time": 0.6}, {"name": "10018", "time": 0.6333}, {"name": "10020", "time": 0.6667}, {"name": "10022", "time": 0.7}, {"name": "10024", "time": 0.7333}, {"name": "10026", "time": 0.7667}, {"name": "10028", "time": 0.8}, {"name": null, "time": 0.8333}, {"name": "10000", "time": 2.6667}, {"name": "10002", "time": 2.7}, {"name": "10004", "time": 2.7333}, {"name": "10006", "time": 2.7667}, {"name": "10008", "time": 2.8}, {"name": "10010", "time": 2.8333}, {"name": "10012", "time": 2.8667}, {"name": "10014", "time": 2.9}, {"name": "10016", "time": 2.9333}, {"name": "10018", "time": 2.9667}, {"name": "10020", "time": 3}, {"name": "10022", "time": 3.0333}, {"name": "10024", "time": 3.0667}, {"name": "10026", "time": 3.1}, {"name": "10028", "time": 3.1333}, {"name": null, "time": 3.1667}]}, "yan4": {"color": [{"color": "ffffffff", "time": 1.0667}, {"color": "ffffff00", "time": 1.5}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.8}, {"name": "smoke_08", "time": 0.8333}, {"name": "smoke_09", "time": 0.8667}, {"name": "smoke_10", "time": 0.9}, {"name": "smoke_11", "time": 0.9333}, {"name": "smoke_12", "time": 0.9667}, {"name": "smoke_13", "time": 1}, {"name": "smoke_14", "time": 1.0333}, {"name": "smoke_15", "time": 1.0667}, {"name": "smoke_16", "time": 1.1}, {"name": "smoke_17", "time": 1.1333}, {"name": "smoke_18", "time": 1.1667}, {"name": "smoke_19", "time": 1.2}]}, "effect": {"color": [{"color": "ffffff9e", "curve": "stepped", "time": 0.9}, {"color": "ffffff9e", "time": 3.6667}, {"color": "ffffff00", "time": 4}], "attachment": [{"name": null}, {"name": "thunder_00", "time": 0.9}, {"name": "thunder_01", "time": 0.9333}, {"name": "thunder_02", "time": 0.9667}, {"name": "thunder_03", "time": 1}, {"name": "thunder_04", "time": 1.0333}, {"name": "thunder_05", "time": 1.0667}, {"name": "thunder_06", "time": 1.1}, {"name": "thunder_07", "time": 1.1333}, {"name": "thunder_08", "time": 1.1667}, {"name": "thunder_09", "time": 1.2}, {"name": "thunder_10", "time": 1.2333}, {"name": "thunder_11", "time": 1.2667}, {"name": "thunder_12", "time": 1.3}, {"name": "thunder_13", "time": 1.3333}, {"name": "thunder_14", "time": 1.3667}, {"name": "thunder_15", "time": 1.4}, {"name": "thunder_16", "time": 1.4333}, {"name": "thunder_17", "time": 1.4667}, {"name": "thunder_18", "time": 1.5}, {"name": "thunder_19", "time": 1.5333}, {"name": "thunder_20", "time": 1.5667}, {"name": "thunder_21", "time": 1.6}, {"name": "thunder_22", "time": 1.6333}, {"name": "thunder_23", "time": 1.6667}, {"name": "thunder_24", "time": 1.7}, {"name": "thunder_25", "time": 1.7333}, {"name": "thunder_26", "time": 1.7667}, {"name": "thunder_27", "time": 1.8}, {"name": "thunder_28", "time": 1.8333}, {"name": "thunder_29", "time": 1.8667}, {"name": "thunder_30", "time": 1.9}, {"name": "thunder_31", "time": 1.9333}, {"name": "thunder_32", "time": 1.9667}, {"name": "thunder_33", "time": 2}, {"name": "thunder_34", "time": 2.0333}, {"name": "thunder_35", "time": 2.0667}, {"name": "thunder_36", "time": 2.1}, {"name": "thunder_37", "time": 2.1333}, {"name": "thunder_38", "time": 2.1667}, {"name": "thunder_39", "time": 2.2}, {"name": "thunder_00", "time": 2.2333}, {"name": "thunder_01", "time": 2.2667}, {"name": "thunder_02", "time": 2.3}, {"name": "thunder_03", "time": 2.3333}, {"name": "thunder_04", "time": 2.3667}, {"name": "thunder_05", "time": 2.4}, {"name": "thunder_06", "time": 2.4333}, {"name": "thunder_07", "time": 2.4667}, {"name": "thunder_08", "time": 2.5}, {"name": "thunder_09", "time": 2.5333}, {"name": "thunder_10", "time": 2.5667}, {"name": "thunder_11", "time": 2.6}, {"name": "thunder_12", "time": 2.6333}, {"name": "thunder_13", "time": 2.6667}, {"name": "thunder_14", "time": 2.7}, {"name": "thunder_15", "time": 2.7333}, {"name": "thunder_16", "time": 2.7667}, {"name": "thunder_17", "time": 2.8}, {"name": "thunder_18", "time": 2.8333}, {"name": "thunder_19", "time": 2.8667}, {"name": "thunder_20", "time": 2.9}, {"name": "thunder_21", "time": 2.9333}, {"name": "thunder_22", "time": 2.9667}, {"name": "thunder_23", "time": 3}, {"name": "thunder_24", "time": 3.0333}, {"name": "thunder_25", "time": 3.0667}, {"name": "thunder_26", "time": 3.1}, {"name": "thunder_27", "time": 3.1333}, {"name": "thunder_28", "time": 3.1667}, {"name": "thunder_29", "time": 3.2}, {"name": "thunder_30", "time": 3.2333}, {"name": "thunder_31", "time": 3.2667}, {"name": "thunder_32", "time": 3.3}, {"name": "thunder_33", "time": 3.3333}, {"name": "thunder_34", "time": 3.3667}, {"name": "thunder_35", "time": 3.4}, {"name": "thunder_36", "time": 3.4333}, {"name": "thunder_37", "time": 3.4667}, {"name": "thunder_38", "time": 3.5}, {"name": "thunder_39", "time": 3.5333}, {"name": "thunder_00", "time": 3.5667}, {"name": "thunder_01", "time": 3.6}, {"name": "thunder_02", "time": 3.6333}, {"name": "thunder_03", "time": 3.6667}, {"name": "thunder_04", "time": 3.7}, {"name": "thunder_05", "time": 3.7333}, {"name": "thunder_06", "time": 3.7667}, {"name": "thunder_07", "time": 3.8}, {"name": "thunder_08", "time": 3.8333}, {"name": "thunder_09", "time": 3.8667}, {"name": "thunder_10", "time": 3.9}, {"name": "thunder_11", "time": 3.9333}, {"name": "thunder_12", "time": 3.9667}, {"name": "thunder_13", "time": 4}]}, "yan5": {"color": [{"color": "ffffffff", "time": 1.2}, {"color": "ffffff00", "time": 1.6333}], "attachment": [{"name": null}, {"name": "smoke_07", "time": 0.9333}, {"name": "smoke_08", "time": 0.9667}, {"name": "smoke_09", "time": 1}, {"name": "smoke_10", "time": 1.0333}, {"name": "smoke_11", "time": 1.0667}, {"name": "smoke_12", "time": 1.1}, {"name": "smoke_13", "time": 1.1333}, {"name": "smoke_14", "time": 1.1667}, {"name": "smoke_15", "time": 1.2}, {"name": "smoke_16", "time": 1.2333}, {"name": "smoke_17", "time": 1.2667}, {"name": "smoke_18", "time": 1.3}, {"name": "smoke_19", "time": 1.3333}]}, "boom2": {"attachment": [{"name": null}, {"name": "10000", "time": 0.5667}, {"name": "10002", "time": 0.6}, {"name": "10004", "time": 0.6333}, {"name": "10006", "time": 0.6667}, {"name": "10008", "time": 0.7}, {"name": "10010", "time": 0.7333}, {"name": "10012", "time": 0.7667}, {"name": "10014", "time": 0.8}, {"name": "10016", "time": 0.8333}, {"name": "10018", "time": 0.8667}, {"name": "10020", "time": 0.9}, {"name": "10022", "time": 0.9333}, {"name": "10024", "time": 0.9667}, {"name": "10026", "time": 1}, {"name": "10028", "time": 1.0333}, {"name": null, "time": 1.0667}, {"name": "10000", "time": 1.5333}, {"name": "10002", "time": 1.5667}, {"name": "10004", "time": 1.6}, {"name": "10006", "time": 1.6333}, {"name": "10008", "time": 1.6667}, {"name": "10010", "time": 1.7}, {"name": "10012", "time": 1.7333}, {"name": "10014", "time": 1.7667}, {"name": "10016", "time": 1.8}, {"name": "10018", "time": 1.8333}, {"name": "10020", "time": 1.8667}, {"name": "10022", "time": 1.9}, {"name": "10024", "time": 1.9333}, {"name": "10026", "time": 1.9667}, {"name": "10028", "time": 2}, {"name": null, "time": 2.0333}]}}, "bones": {"plane": {"scale": [{"curve": "stepped", "x": 0.164, "y": 0.164}, {"x": 0.164, "y": 0.164, "time": 0.3333}, {"x": 0.572, "y": 0.572, "time": 0.6}, {"x": 2.418, "y": 2.418, "time": 1.3333}], "translate": [{"curve": "stepped", "x": 461.73, "y": 389.06}, {"x": 461.73, "y": 389.06, "time": 0.3333}, {"x": 154.08, "y": 169.08, "time": 0.6}, {"x": -643.49, "y": -456.15, "time": 1.3333}]}, "daodanall3": {"translate": [{"x": -29.13, "y": -110.06}]}, "daodanall5": {"translate": [{"x": -304.28, "y": -161.85}]}, "daodanall4": {"scale": [{"x": 2.38, "y": 2.38}]}, "daodanall7": {"rotate": [{"angle": -3.57, "time": 0.6}], "translate": [{"x": -492.02, "y": -339.88, "time": 0.6}]}, "daodanall6": {"scale": [{"x": 2.175, "y": 2.175}]}, "daodanall8": {"translate": [{"curve": "stepped", "x": 236.95, "y": 257.78, "time": 0.9333}, {"x": 236.95, "y": 257.78, "time": 1.6333}]}, "daodan": {"scale": [{"x": 0.597, "y": 0.597, "time": 0.1667}, {"x": 0.994, "y": 0.994, "time": 0.5}], "translate": [{"curve": "stepped", "x": 711.5, "y": 598.75}, {"x": 711.5, "y": 598.75, "time": 0.1667}, {"x": 20.41, "y": 20.41, "time": 0.5}]}, "daodanall": {"translate": [{"x": -168.32, "y": -110.06}]}, "daodan2": {"scale": [{"x": 0.597, "y": 0.597, "time": 0.2333}, {"x": 0.994, "y": 0.994, "time": 0.5667}], "translate": [{"curve": "stepped", "x": 711.5, "y": 598.75}, {"x": 711.5, "y": 598.75, "time": 0.2333}, {"x": 20.41, "y": 20.41, "time": 0.5667}]}, "daodan3": {"scale": [{"x": 0.597, "y": 0.597, "time": 0.5333}, {"x": 0.994, "y": 0.994, "time": 0.8667}], "translate": [{"curve": "stepped", "x": 711.5, "y": 598.75}, {"x": 711.5, "y": 598.75, "time": 0.5333}, {"x": 20.41, "y": 20.41, "time": 0.8667}]}, "daodan4": {"scale": [{"x": 0.597, "y": 0.597, "time": 0.6}, {"x": 0.994, "y": 0.994, "time": 0.9333}], "translate": [{"curve": "stepped", "x": 711.5, "y": 598.75}, {"x": 711.5, "y": 598.75, "time": 0.6}, {"x": 273.62, "y": 224.78, "time": 0.9333}]}, "daodan5": {"rotate": [{"angle": -12.33, "time": 0.7333}, {"angle": -8.54, "time": 1.0667}], "scale": [{"x": 0.597, "y": 0.597, "time": 0.7333}, {"x": 0.994, "y": 0.994, "time": 1.0667}], "translate": [{"x": 711.5, "y": 598.75}, {"x": 190.67, "y": 192.6, "time": 0.7333}, {"x": -108.39, "y": -0.33, "time": 1.0667}]}, "daodan6": {"scale": [{"x": 0.597, "y": 0.597}, {"x": 0.994, "y": 0.994, "time": 0.3333}], "translate": [{"x": 711.5, "y": 598.75}, {"x": 20.41, "y": 20.41, "time": 0.3333}]}, "daodan7": {"scale": [{"x": 0.597, "y": 0.597}, {"x": 0.994, "y": 0.994, "time": 0.3333}], "translate": [{"x": 711.5, "y": 598.75}, {"x": 20.41, "y": 20.41, "time": 0.3333}]}, "daodan8": {"scale": [{"x": 0.597, "y": 0.597, "time": 0.5333}, {"x": 0.994, "y": 0.994, "time": 0.8667}], "translate": [{"curve": "stepped", "x": 711.5, "y": 598.75}, {"x": 711.5, "y": 598.75, "time": 0.5333}, {"x": 20.41, "y": 20.41, "time": 0.8667}]}, "tittle": {"scale": [{"x": 0.16, "y": 0.16, "time": 0.9}, {"time": 0.9667}, {"x": 1.539, "y": 1.539, "time": 1.1}, {"time": 1.2333}, {"x": 1.236, "y": 1.236, "time": 4}], "translate": [{"time": 0.9667}, {"x": -2, "time": 2}]}, "all": {"translate": [{"time": 0.5}, {"y": -25.53, "time": 0.5667}, {"time": 0.6333}, {"y": -25.53, "time": 0.7}, {"time": 0.7667}, {"y": -25.53, "time": 0.8333}, {"time": 0.9}, {"x": -8.48, "y": -55.83, "time": 0.9667}, {"x": 42.42, "y": 56.96, "time": 1.0333}, {"x": -3.64, "y": -38.86, "time": 1.1}, {"x": 3.64, "y": 12.12, "time": 1.1667}, {"y": -25.53, "time": 1.2333}, {"time": 1.3}, {"y": 58.48, "time": 1.3667}, {"x": 34.46, "y": -62.47, "time": 1.4333}, {"x": -15.08, "y": -1.84, "time": 1.5}, {"x": 17.23, "time": 1.5667}, {"y": -25.53, "time": 1.6333}, {"time": 1.7}, {"y": -25.53, "time": 1.7667}, {"time": 1.8333}, {"y": -25.53, "time": 1.9}, {"time": 1.9667}, {"y": -25.53, "time": 2.0333}, {"time": 2.1}, {"y": -25.53, "time": 2.1667}, {"time": 2.2333}, {"y": -25.53, "time": 2.3}, {"time": 2.3667}, {"y": -25.53, "time": 2.4333}, {"time": 2.5}, {"y": -25.53, "time": 2.5667}, {"time": 2.6333}, {"y": -25.53, "time": 2.7}, {"time": 2.7667}]}, "daodanall15": {"translate": [{"x": -304.28, "y": -161.85}]}, "daodanall16": {"scale": [{"x": 2.175, "y": 2.175}]}, "daodanall13": {"translate": [{"x": 139.19}]}, "daodanall14": {"scale": [{"x": 1.931, "y": 1.931}, {"x": 6.009, "y": 6.009, "time": 2.6667}]}, "daodanall11": {"translate": [{"x": 139.19}]}, "daodanall12": {"scale": [{"x": 1.931, "y": 1.931}]}, "daodanall10": {"scale": [{"x": 2.707, "y": 2.707}], "translate": [{"x": -47.43, "y": 35.58}]}, "effect2": {"scale": [{"x": 0.708}]}, "effect": {"scale": [{"x": 0.752}]}}}}}, [0]]], 0, 0, [0], [-1], [34]], [[{"name": "borderSoiCau", "rect": [0, 0, 516, 28], "offset": [0, 0.5], "originalSize": [516, 29], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [35]], [[[17, "logo_tx", "\nlogo_tx.png\nsize: 918,142\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nBeard\n  rotate: false\n  xy: 341, 3\n  size: 45, 43\n  orig: 45, 43\n  offset: 0, 0\n  index: -1\nBeard10\n  rotate: false\n  xy: 458, 3\n  size: 16, 43\n  orig: 16, 43\n  offset: 0, 0\n  index: -1\nBeard2\n  rotate: true\n  xy: 704, 6\n  size: 40, 21\n  orig: 40, 21\n  offset: 0, 0\n  index: -1\nBeard3\n  rotate: true\n  xy: 826, 88\n  size: 52, 20\n  orig: 52, 20\n  offset: 0, 0\n  index: -1\nBeard4\n  rotate: true\n  xy: 848, 90\n  size: 50, 21\n  orig: 50, 21\n  offset: 0, 0\n  index: -1\nBeard5\n  rotate: false\n  xy: 871, 90\n  size: 21, 50\n  orig: 21, 50\n  offset: 0, 0\n  index: -1\nBeard6\n  rotate: false\n  xy: 783, 74\n  size: 17, 66\n  orig: 17, 66\n  offset: 0, 0\n  index: -1\nBeard7\n  rotate: false\n  xy: 802, 81\n  size: 22, 59\n  orig: 22, 59\n  offset: 0, 0\n  index: -1\nBeard8\n  rotate: false\n  xy: 888, 5\n  size: 26, 45\n  orig: 26, 45\n  offset: 0, 0\n  index: -1\nBeard9\n  rotate: false\n  xy: 860, 4\n  size: 26, 46\n  orig: 26, 46\n  offset: 0, 0\n  index: -1\nBread_Mouth\n  rotate: true\n  xy: 164, 7\n  size: 10, 28\n  orig: 11, 29\n  offset: 1, 1\n  index: -1\nBread_Mouth2\n  rotate: true\n  xy: 121, 6\n  size: 11, 21\n  orig: 13, 23\n  offset: 1, 0\n  index: -1\nBread_Mouth3\n  rotate: true\n  xy: 144, 6\n  size: 11, 18\n  orig: 13, 20\n  offset: 1, 1\n  index: -1\nBread_Mouth4\n  rotate: true\n  xy: 92, 6\n  size: 11, 27\n  orig: 11, 28\n  offset: 0, 0\n  index: -1\nBread_Mouth5\n  rotate: true\n  xy: 2, 2\n  size: 15, 36\n  orig: 15, 36\n  offset: 0, 0\n  index: -1\nEye\n  rotate: true\n  xy: 78, 3\n  size: 14, 12\n  orig: 14, 13\n  offset: 0, 1\n  index: -1\nEyebrow\n  rotate: true\n  xy: 40, 3\n  size: 14, 36\n  orig: 14, 36\n  offset: 0, 0\n  index: -1\nFang\n  rotate: false\n  xy: 896, 56\n  size: 20, 21\n  orig: 20, 21\n  offset: 0, 0\n  index: -1\nHead\n  rotate: true\n  xy: 239, 2\n  size: 44, 65\n  orig: 44, 65\n  offset: 0, 0\n  index: -1\nMouth\n  rotate: false\n  xy: 894, 104\n  size: 17, 36\n  orig: 17, 36\n  offset: 0, 0\n  index: -1\nNose\n  rotate: true\n  xy: 896, 79\n  size: 23, 18\n  orig: 23, 18\n  offset: 0, 0\n  index: -1\nRubi\n  rotate: false\n  xy: 818, 52\n  size: 27, 27\n  orig: 29, 29\n  offset: 1, 1\n  index: -1\nbg1\n  rotate: false\n  xy: 2, 19\n  size: 235, 121\n  orig: 235, 122\n  offset: 0, 0\n  index: -1\nc1\n  rotate: false\n  xy: 428, 3\n  size: 28, 43\n  orig: 31, 47\n  offset: 2, 2\n  index: -1\nc2\n  rotate: true\n  xy: 783, 48\n  size: 24, 33\n  orig: 26, 37\n  offset: 1, 2\n  index: -1\nframe_set/E0\n  rotate: false\n  xy: 388, 3\n  size: 38, 43\n  orig: 56, 48\n  offset: 7, 4\n  index: -1\nframe_set/E2\n  rotate: false\n  xy: 727, 7\n  size: 47, 39\n  orig: 56, 48\n  offset: 4, 6\n  index: -1\nframe_set/E4\n  rotate: true\n  xy: 306, 2\n  size: 44, 33\n  orig: 56, 48\n  offset: 7, 7\n  index: -1\nframe_set/E6\n  rotate: false\n  xy: 776, 8\n  size: 46, 38\n  orig: 56, 48\n  offset: 5, 7\n  index: -1\nframe_set/E8\n  rotate: false\n  xy: 596, 5\n  size: 38, 41\n  orig: 56, 48\n  offset: 8, 3\n  index: -1\nimages/bg_vang\n  rotate: false\n  xy: 239, 48\n  size: 542, 92\n  orig: 544, 103\n  offset: 1, 1\n  index: -1\nimages/v1\n  rotate: false\n  xy: 476, 4\n  size: 58, 42\n  orig: 63, 46\n  offset: 3, 3\n  index: -1\nimages/v2\n  rotate: true\n  xy: 824, 3\n  size: 47, 34\n  orig: 51, 37\n  offset: 2, 2\n  index: -1\nimages/v3\n  rotate: true\n  xy: 636, 5\n  size: 41, 32\n  orig: 43, 38\n  offset: 1, 3\n  index: -1\nimages/v4\n  rotate: false\n  xy: 536, 4\n  size: 58, 42\n  orig: 63, 46\n  offset: 2, 3\n  index: -1\nimages/v5\n  rotate: false\n  xy: 847, 52\n  size: 47, 34\n  orig: 51, 37\n  offset: 2, 2\n  index: -1\nimages/v6\n  rotate: true\n  xy: 670, 5\n  size: 41, 32\n  orig: 43, 38\n  offset: 1, 3\n  index: -1\n", ["logo_tx.png"], {"skins": [{"name": "default", "attachments": {"Eyebrow": {"Eyebrow": {"width": 14, "type": "mesh", "hull": 6, "height": 36, "triangles": [4, 5, 1, 3, 4, 2, 5, 0, 1, 4, 1, 2], "uvs": [0.0149, 0, 0.59545, 0, 1, 0.37229, 1, 0.80984, 0.5099, 1, 0, 1], "vertices": [2, 7, 27.49, 8.37, 5e-05, 8, 15.65, 4.86, 0.99995, 1, 8, 13.53, -5.37, 1, 3, 7, 11.29, -9.86, 0.91242, 8, -4.35, -9.1, 0.0771, 6, 21.49, -10.09, 0.01048, 2, 7, -8.39, -10.47, 0.10947, 6, 1.8, -10.3, 0.89053, 1, 6, -6.85, -1.56, 1, 1, 6, -6.94, 7.61, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10]}}, "bg2": {"bg1": {"x": 1.49, "width": 235, "y": 165.44, "height": 122}}, "bg1": {"bg1": {"x": 1.49, "width": 235, "y": 165.44, "height": 122}}, "Beard20": {"Beard": {"width": 45, "type": "mesh", "hull": 37, "height": 43, "triangles": [16, 17, 20, 17, 19, 20, 17, 18, 19, 16, 20, 21, 15, 16, 22, 16, 21, 22, 23, 15, 22, 14, 23, 13, 13, 23, 24, 23, 14, 15, 13, 24, 12, 11, 12, 26, 12, 25, 26, 12, 24, 25, 26, 27, 11, 11, 27, 10, 10, 27, 9, 9, 27, 28, 28, 8, 9, 28, 29, 8, 29, 30, 8, 30, 7, 8, 30, 31, 7, 31, 6, 7, 31, 5, 6, 31, 32, 5, 5, 32, 33, 4, 5, 33, 33, 34, 4, 4, 34, 3, 34, 35, 3, 3, 35, 36, 2, 0, 1, 2, 36, 0, 2, 3, 36], "uvs": [1, 0.05372, 1, 0.16485, 0.86575, 0.10674, 0.7548, 0.1018, 0.66533, 0.15125, 0.63908, 0.25139, 0.65817, 0.36885, 0.688, 0.51721, 0.72612, 0.66943, 0.73566, 0.83633, 0.70465, 0.96615, 0.59967, 1, 0.46725, 1, 0.3563, 0.94513, 0.25489, 0.85612, 0.17139, 0.74608, 0.10219, 0.62739, 0.04612, 0.52601, 0, 0.4135, 0.0664, 0.37394, 0.14395, 0.48026, 0.19405, 0.56434, 0.26921, 0.67684, 0.34675, 0.78317, 0.42191, 0.85488, 0.50542, 0.89815, 0.56388, 0.91175, 0.59967, 0.88455, 0.61398, 0.82273, 0.59967, 0.6991, 0.57342, 0.56434, 0.5424, 0.40855, 0.51974, 0.24535, 0.5436, 0.1143, 0.6116, 0.02528, 0.73447, 0, 0.86928, 0], "vertices": [1, 110, -1.48, 2.74, 1, 1, 110, -2.97, -3.19, 1, 1, 110, 5.23, -1.94, 1, 3, 110, 11.43, -3.21, 0.10215, 111, 2.1, -2.59, 0.88741, 112, -1.55, -6.89, 0.01043, 3, 111, 7.58, -4.43, 0.25554, 112, 3.05, -3.39, 0.73637, 113, -2.67, -6.18, 0.00809, 2, 112, 8.68, -4.34, 0.26602, 113, 2.42, -3.6, 0.73398, 2, 113, 8.97, -3.36, 0.8478, 114, -1.71, -3.39, 0.1522, 1, 114, 6.63, -3.27, 1, 2, 114, 15.27, -3.57, 0.05733, 115, 2.25, -3.88, 0.94267, 2, 115, 11.44, -3.57, 0.88867, 116, -3.07, -2.13, 0.11133, 1, 116, 3.88, -4.54, 1, 2, 116, 8.7, -0.54, 0.12863, 117, 0.49, -2.22, 0.87137, 1, 117, 7.99, -3.11, 1, 2, 117, 14.62, -0.86, 0.00162, 118, 1.67, -2.58, 0.99838, 2, 118, 9.18, -3.53, 0.82819, 119, -4.53, -2.54, 0.17181, 1, 119, 3.14, -3.25, 1, 2, 119, 10.76, -3.03, 0.68365, 120, -0.59, -3.03, 0.31635, 1, 120, 5.82, -2.7, 1, 1, 120, 12.45, -1.56, 1, 1, 120, 12.23, 2.8, 1, 1, 120, 4.92, 3.35, 1, 2, 119, 10.83, 3.25, 0.6417, 120, -0.51, 3.25, 0.3583, 2, 118, 15.9, 3.73, 0.40379, 119, 3.31, 3.5, 0.59621, 1, 118, 8.61, 3.04, 1, 2, 117, 11.49, 4.51, 0.21919, 118, 2.8, 3.53, 0.78081, 3, 116, 6.94, 7.01, 0.00064, 117, 6.49, 2.71, 0.95649, 118, -2.17, 5.44, 0.04286, 3, 115, 14.66, 6.56, 0.00052, 116, 5.75, 3.81, 0.19062, 117, 3.09, 2.35, 0.80887, 3, 115, 13.36, 4.39, 0.07422, 116, 3.39, 2.92, 0.74981, 117, 1.24, 4.08, 0.17598, 3, 115, 10.05, 3.26, 0.78925, 116, 0.09, 4.09, 0.20893, 117, 0.83, 7.55, 0.00182, 2, 114, 15.3, 3.82, 0.03753, 115, 3.2, 3.44, 0.96247, 2, 114, 7.74, 3.67, 0.99972, 115, -4.32, 4.24, 0.00028, 2, 113, 9.77, 3.54, 0.66442, 114, -1.01, 3.53, 0.33558, 2, 112, 11.22, 1.98, 0.14169, 113, 0.72, 2.99, 0.85831, 1, 112, 4.11, 3.76, 1, 2, 111, 9.45, 2.91, 0.64205, 112, -1.96, 2.29, 0.35795, 2, 110, 13.92, 1.94, 0.02036, 111, 2.32, 3.12, 0.97964, 1, 110, 6.46, 3.8, 1], "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 2, 0, 72, 0]}}, "Bread_Mouth9": {"Bread_Mouth2": {"rotation": 85.76, "x": 9.15, "width": 13, "y": 1.16, "height": 23}}, "Eye": {"Eye": {"x": 0.5, "width": 14, "y": -0.18, "height": 13}}, "Bread_Mouth8": {"Bread_Mouth": {"rotation": 101.48, "x": 11.13, "width": 11, "y": 1.16, "height": 29}}, "Bread_Mouth10": {"Bread_Mouth3": {"rotation": 89.2, "x": 6.94, "width": 13, "y": -1.59, "height": 20}}, "Head2": {"Head": {"width": 44, "type": "mesh", "hull": 25, "height": 65, "triangles": [27, 0, 3, 21, 22, 27, 27, 22, 23, 27, 24, 0, 27, 23, 24, 18, 19, 25, 20, 21, 19, 21, 25, 19, 18, 25, 17, 3, 28, 27, 28, 21, 27, 40, 21, 28, 36, 21, 40, 26, 25, 21, 57, 44, 43, 12, 44, 57, 2, 0, 1, 3, 0, 2, 41, 4, 5, 32, 4, 41, 3, 32, 28, 42, 5, 6, 7, 42, 6, 41, 5, 42, 39, 32, 41, 28, 32, 39, 40, 28, 39, 31, 41, 42, 30, 31, 42, 35, 40, 39, 36, 40, 35, 41, 37, 39, 35, 39, 34, 42, 7, 8, 30, 42, 8, 41, 31, 37, 37, 31, 30, 38, 37, 30, 54, 53, 37, 35, 47, 26, 35, 26, 36, 50, 35, 34, 47, 35, 50, 29, 30, 8, 29, 38, 30, 55, 54, 37, 55, 37, 38, 8, 9, 29, 53, 51, 50, 53, 50, 34, 51, 53, 54, 52, 54, 55, 51, 54, 52, 33, 55, 38, 33, 38, 29, 48, 16, 47, 43, 33, 29, 50, 48, 47, 49, 50, 51, 49, 48, 50, 33, 44, 52, 33, 52, 55, 44, 33, 43, 29, 9, 10, 43, 29, 10, 15, 16, 48, 15, 48, 49, 45, 51, 52, 45, 52, 44, 46, 49, 51, 57, 43, 10, 45, 46, 51, 56, 45, 44, 56, 44, 12, 57, 10, 11, 12, 57, 11, 14, 49, 46, 15, 49, 14, 56, 13, 46, 56, 46, 45, 14, 46, 13, 13, 56, 12, 34, 39, 53, 37, 53, 39, 17, 26, 47, 16, 17, 47, 17, 25, 26, 26, 21, 36, 32, 3, 4], "uvs": [0.50065, 0.2344, 0.60234, 0.07681, 0.69387, 0.10717, 0.61251, 0.33416, 0.73455, 0.3891, 0.86472, 0.3891, 1, 0.46284, 1, 0.51466, 1, 0.61731, 1, 0.69249, 1, 0.84859, 1, 0.96135, 0.83421, 0.95701, 0.66743, 1, 0.47217, 0.9758, 0.34404, 0.85725, 0.35014, 0.71701, 0.34404, 0.63576, 0, 0.35962, 0, 0.29223, 0, 0.23383, 0.42946, 0.45649, 0.15692, 0, 0.24234, 0, 0.3054, 0, 0.29726, 0.52211, 0.39489, 0.59007, 0.44573, 0.37031, 0.52709, 0.47151, 0.87801, 0.70695, 0.8719, 0.61008, 0.80479, 0.5537, 0.60343, 0.45827, 0.78648, 0.73731, 0.62377, 0.61586, 0.55055, 0.61008, 0.46919, 0.53056, 0.77021, 0.61876, 0.8414, 0.6708, 0.61157, 0.53924, 0.51191, 0.50888, 0.76818, 0.47129, 0.90445, 0.51755, 0.82513, 0.80088, 0.7397, 0.82257, 0.68682, 0.86739, 0.56275, 0.89775, 0.43258, 0.68522, 0.42852, 0.76763, 0.47123, 0.80955, 0.57902, 0.68522, 0.61157, 0.7257, 0.70716, 0.73582, 0.66648, 0.64184, 0.70309, 0.68088, 0.74784, 0.70257, 0.70822, 0.93026, 0.87201, 0.89024], "vertices": [2, 4, 28.87, 18.58, 0.5, 5, 28.87, 103.58, 0.5, 2, 4, 22.87, 31.66, 0.5, 5, 22.87, 116.66, 0.5, 2, 4, 17.47, 29.14, 0.5, 5, 17.47, 114.14, 0.5, 2, 4, 22.27, 10.3, 0.5, 5, 22.27, 95.3, 0.5, 2, 4, 15.07, 5.74, 0.5, 5, 15.07, 90.74, 0.5, 2, 4, 7.39, 5.74, 0.5, 5, 7.39, 90.74, 0.5, 2, 4, -0.59, -0.38, 0.524, 5, -0.59, 84.62, 0.476, 2, 4, -0.59, -4.68, 0.53, 5, -0.59, 80.32, 0.47, 2, 4, -0.59, -13.2, 0.55, 5, -0.59, 71.8, 0.45, 2, 4, -0.59, -19.44, 0.57, 5, -0.59, 65.56, 0.43, 2, 4, -0.59, -32.39, 0.59, 5, -0.59, 52.61, 0.41, 2, 4, -0.59, -41.75, 0.59, 5, -0.59, 43.25, 0.41, 2, 4, 9.19, -41.39, 0.564, 5, 9.19, 43.61, 0.436, 2, 4, 19.03, -44.96, 0.53, 5, 19.03, 40.04, 0.47, 2, 4, 30.55, -42.95, 0.51, 5, 30.55, 42.05, 0.49, 2, 4, 38.11, -33.11, 0.53, 5, 38.11, 51.89, 0.47, 2, 4, 37.75, -21.47, 0.5, 5, 37.75, 63.53, 0.5, 2, 4, 38.11, -14.73, 0.5, 5, 38.11, 70.27, 0.5, 2, 4, 58.41, 8.19, 0.46, 5, 58.41, 93.19, 0.54, 2, 4, 58.41, 13.78, 0.46, 5, 58.41, 98.78, 0.54, 2, 4, 58.41, 18.63, 0.48, 5, 58.41, 103.63, 0.52, 2, 4, 33.07, 0.15, 0.492, 5, 33.07, 85.15, 0.508, 2, 4, 49.15, 38.04, 0.45, 5, 49.15, 123.04, 0.55, 2, 4, 44.11, 38.04, 0.45, 5, 44.11, 123.04, 0.55, 2, 4, 40.39, 38.04, 0.46, 5, 40.39, 123.04, 0.54, 2, 4, 40.87, -5.3, 0.516, 5, 40.87, 79.7, 0.484, 2, 4, 35.11, -10.94, 0.524, 5, 35.11, 74.06, 0.476, 2, 4, 32.11, 7.3, 0.492, 5, 32.11, 92.3, 0.508, 2, 4, 27.31, -1.1, 0.516, 5, 27.31, 83.9, 0.484, 2, 4, 6.61, -20.64, 0.548, 5, 6.61, 64.36, 0.452, 2, 4, 6.97, -12.6, 0.564, 5, 6.97, 72.4, 0.436, 2, 4, 10.93, -7.92, 0.564, 5, 10.93, 77.08, 0.436, 2, 4, 22.81, 0, 0.516, 5, 22.81, 85, 0.484, 2, 4, 12.01, -23.16, 0.556, 5, 12.01, 61.84, 0.444, 2, 4, 21.61, -13.08, 0.56, 5, 21.61, 71.92, 0.44, 2, 4, 25.93, -12.6, 0.54, 5, 25.93, 72.4, 0.46, 2, 4, 30.73, -6, 0.524, 5, 30.73, 79, 0.476, 2, 4, 12.97, -13.32, 0.564, 5, 12.97, 71.68, 0.436, 2, 4, 8.77, -17.64, 0.564, 5, 8.77, 67.36, 0.436, 2, 4, 22.33, -6.72, 0.532, 5, 22.33, 78.28, 0.468, 2, 4, 28.21, -4.2, 0.524, 5, 28.21, 80.8, 0.476, 2, 4, 13.09, -1.08, 0.55, 5, 13.09, 83.92, 0.45, 2, 4, 5.05, -4.92, 0.54, 5, 5.05, 80.08, 0.46, 2, 4, 9.73, -28.43, 0.56, 5, 9.73, 56.57, 0.44, 2, 4, 14.77, -30.23, 0.548, 5, 14.77, 54.77, 0.452, 2, 4, 17.89, -33.95, 0.54, 5, 17.89, 51.05, 0.46, 2, 4, 25.21, -36.47, 0.54, 5, 25.21, 48.53, 0.46, 2, 4, 32.89, -18.83, 0.54, 5, 32.89, 66.17, 0.46, 2, 4, 33.13, -25.67, 0.528, 5, 33.13, 59.33, 0.472, 2, 4, 30.61, -29.15, 0.54, 5, 30.61, 55.85, 0.46, 2, 4, 24.25, -18.83, 0.55, 5, 24.25, 66.17, 0.45, 2, 4, 22.33, -22.19, 0.538, 5, 22.33, 62.81, 0.462, 2, 4, 16.69, -23.03, 0.54, 5, 16.69, 61.97, 0.46, 2, 4, 19.09, -15.23, 0.548, 5, 19.09, 69.77, 0.452, 2, 4, 16.93, -18.47, 0.548, 5, 16.93, 66.53, 0.452, 2, 4, 14.29, -20.27, 0.556, 5, 14.29, 64.73, 0.444, 2, 4, 16.63, -39.17, 0.53, 5, 16.63, 45.83, 0.47, 2, 4, 6.96, -35.85, 0.54, 5, 6.96, 49.15, 0.46], "edges": [40, 42, 42, 44, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 48, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 44, 46, 46, 48, 36, 38, 38, 40, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22]}}, "Bread_Mouth3": {"Bread_Mouth3": {"rotation": 89.2, "x": 6.94, "width": 13, "y": -1.59, "height": 20}}, "Bread_Mouth2": {"Bread_Mouth2": {"rotation": 85.76, "x": 9.15, "width": 13, "y": 1.16, "height": 23}}, "Bread_Mouth5": {"Bread_Mouth5": {"width": 15, "type": "mesh", "hull": 10, "height": 36, "triangles": [6, 2, 3, 4, 6, 3, 5, 6, 4, 8, 9, 0, 1, 8, 0, 7, 8, 1, 7, 1, 2, 6, 7, 2], "uvs": [0, 0, 0.72059, 0, 1, 0.36558, 1, 0.75225, 0.33393, 1, 0, 1, 0.27593, 0.65155, 0.23726, 0.48641, 0.09226, 0.26489, 0, 0.15211], "vertices": [1, 54, -3.19, -5.92, 1, 1, 54, 0.34, 4.29, 1, 2, 54, 14.15, 3.94, 0.83886, 55, -3.27, 2.55, 0.16114, 2, 55, 10.18, 6.14, 0.4471, 56, -1.92, 5.98, 0.5529, 1, 56, 11.45, 5.24, 1, 1, 56, 15, 1.7, 1, 3, 54, 20.32, -9.69, 0.04355, 55, 9.48, -5.29, 0.19309, 56, 3.21, -4.25, 0.76336, 3, 54, 14.52, -8.29, 0.50163, 55, 3.88, -7.38, 0.39679, 56, -0.58, -8.87, 0.10159, 3, 54, 6.27, -7.74, 0.98769, 55, -3.26, -11.54, 0.01226, 56, -4.67, -16.05, 5e-05, 1, 54, 1.98, -7.71, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18]}}, "Bread_Mouth4": {"Bread_Mouth4": {"rotation": 100.97, "x": 8.56, "width": 11, "y": 0.46, "height": 28}}, "Bread_Mouth7": {"Bread_Mouth4": {"rotation": 95.48, "x": 7.74, "width": 11, "y": 0.8, "height": 28}}, "Bread_Mouth6": {"Bread_Mouth5": {"width": 15, "type": "mesh", "hull": 10, "height": 36, "triangles": [5, 6, 4, 4, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 1, 8, 0, 8, 9, 0], "uvs": [0, 0, 0.72059, 0, 1, 0.36558, 1, 0.75225, 0.33393, 1, 0, 1, 0.27593, 0.65155, 0.23726, 0.48641, 0.09226, 0.26489, 0, 0.15211], "vertices": [1, 100, -3.72, 4.95, 1, 1, 100, 0.26, -5.1, 1, 2, 100, 14.04, -4.14, 0.97271, 101, -3.38, -2.77, 0.02729, 2, 101, 10.3, -5.31, 0.24012, 102, -2.08, -5.1, 0.75988, 1, 102, 11.31, -5.08, 1, 1, 102, 15.04, -1.74, 1, 3, 100, 19.61, 9.75, 0.02206, 101, 8.73, 6.03, 0.42154, 102, 3.59, 4.84, 0.5564, 3, 100, 13.87, 8.09, 0.37297, 101, 2.99, 7.68, 0.59291, 102, 0.06, 9.66, 0.03412, 2, 100, 5.65, 7.18, 0.94416, 101, -4.46, 11.28, 0.05584, 2, 100, 1.37, 6.97, 0.99525, 101, -8.19, 13.38, 0.00475], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18]}}, "Head": {"Head": {"width": 44, "type": "mesh", "hull": 25, "height": 65, "triangles": [21, 25, 19, 27, 24, 0, 27, 22, 23, 27, 23, 24, 18, 19, 25, 2, 0, 1, 20, 21, 19, 21, 22, 27, 13, 56, 12, 14, 46, 13, 56, 13, 46, 56, 46, 45, 45, 46, 59, 15, 49, 14, 14, 49, 46, 12, 57, 11, 57, 10, 11, 56, 44, 12, 12, 44, 57, 57, 44, 43, 56, 45, 44, 46, 49, 59, 57, 43, 10, 45, 58, 44, 45, 59, 58, 15, 48, 49, 15, 16, 48, 43, 29, 10, 29, 9, 10, 59, 51, 58, 59, 49, 51, 58, 52, 44, 44, 33, 43, 33, 52, 55, 33, 44, 52, 49, 48, 50, 49, 50, 51, 50, 48, 47, 43, 33, 29, 58, 51, 52, 48, 16, 47, 33, 38, 29, 33, 55, 38, 51, 54, 52, 52, 54, 55, 51, 53, 54, 53, 50, 34, 53, 51, 50, 16, 17, 47, 8, 9, 29, 55, 37, 38, 55, 54, 37, 29, 38, 30, 29, 30, 8, 47, 35, 50, 50, 35, 34, 17, 26, 47, 35, 26, 36, 35, 47, 26, 54, 53, 37, 38, 37, 30, 37, 53, 39, 18, 25, 17, 17, 25, 26, 37, 31, 30, 41, 31, 37, 30, 42, 8, 42, 7, 8, 35, 39, 34, 41, 37, 39, 34, 39, 53, 36, 40, 35, 35, 40, 39, 30, 31, 42, 26, 21, 36, 26, 25, 21, 31, 41, 42, 40, 28, 39, 28, 32, 39, 39, 32, 41, 36, 21, 40, 41, 5, 42, 7, 42, 6, 42, 5, 6, 40, 21, 28, 21, 27, 28, 28, 60, 32, 28, 27, 60, 32, 4, 41, 41, 4, 5, 32, 3, 4, 32, 60, 3, 3, 60, 0, 60, 27, 0, 3, 0, 2], "uvs": [0.50065, 0.2344, 0.60234, 0.07681, 0.69387, 0.10717, 0.61251, 0.33416, 0.73455, 0.3891, 0.86472, 0.3891, 1, 0.46284, 1, 0.51466, 1, 0.61731, 1, 0.69249, 1, 0.84859, 1, 0.96135, 0.83421, 0.95701, 0.66743, 1, 0.47217, 0.9758, 0.34404, 0.85725, 0.35014, 0.71701, 0.34404, 0.63576, 0, 0.35962, 0, 0.29223, 0, 0.23383, 0.42946, 0.45649, 0.15692, 0, 0.24234, 0, 0.3054, 0, 0.29726, 0.52211, 0.39489, 0.59007, 0.44573, 0.37031, 0.52709, 0.47151, 0.87801, 0.70695, 0.8719, 0.61008, 0.80479, 0.5537, 0.60343, 0.45827, 0.78648, 0.73731, 0.62377, 0.61586, 0.55055, 0.61008, 0.46919, 0.53056, 0.77021, 0.61876, 0.8414, 0.6708, 0.61157, 0.53924, 0.51191, 0.50888, 0.76818, 0.47129, 0.90445, 0.51755, 0.82513, 0.80088, 0.7397, 0.82257, 0.68682, 0.86739, 0.56275, 0.89775, 0.43258, 0.68522, 0.42852, 0.76763, 0.47123, 0.80955, 0.57902, 0.68522, 0.61157, 0.7257, 0.70716, 0.73582, 0.66648, 0.64184, 0.70309, 0.68088, 0.74784, 0.70257, 0.70822, 0.93026, 0.87201, 0.89024, 0.69262, 0.78202, 0.5988, 0.82848, 0.56384, 0.3732], "vertices": [2, 4, -27.12, 18.58, 0.508, 5, -27.12, 103.58, 0.492, 2, 4, -21.12, 31.66, 0.476, 5, -21.12, 116.66, 0.524, 2, 4, -15.72, 29.14, 0.492, 5, -15.72, 114.14, 0.508, 2, 4, -20.52, 10.3, 0.5, 5, -20.52, 95.3, 0.5, 2, 4, -13.32, 5.74, 0.508, 5, -13.32, 90.74, 0.492, 2, 4, -5.64, 5.74, 0.5, 5, -5.64, 90.74, 0.5, 2, 4, 2.34, -0.38, 0.524, 5, 2.34, 84.62, 0.476, 2, 4, 2.34, -4.68, 0.532, 5, 2.34, 80.32, 0.468, 2, 4, 2.34, -13.2, 0.55, 5, 2.34, 71.8, 0.45, 2, 4, 2.34, -19.44, 0.57, 5, 2.34, 65.56, 0.43, 2, 4, 2.34, -32.39, 0.59, 5, 2.34, 52.61, 0.41, 2, 4, 2.34, -41.75, 0.59, 5, 2.34, 43.25, 0.41, 2, 4, -7.44, -41.39, 0.564, 5, -7.44, 43.61, 0.436, 2, 4, -17.28, -44.96, 0.524, 5, -17.28, 40.04, 0.476, 2, 4, -28.8, -42.95, 0.508, 5, -28.8, 42.05, 0.492, 2, 4, -36.36, -33.11, 0.524, 5, -36.36, 51.89, 0.476, 2, 4, -36, -21.47, 0.548, 5, -36, 63.53, 0.452, 2, 4, -36.36, -14.73, 0.548, 5, -36.36, 70.27, 0.452, 2, 4, -56.66, 8.19, 0.46, 5, -56.66, 93.19, 0.54, 2, 4, -56.66, 13.78, 0.46, 5, -56.66, 98.78, 0.54, 2, 4, -56.66, 18.63, 0.484, 5, -56.66, 103.63, 0.516, 2, 4, -31.32, 0.15, 0.508, 5, -31.32, 85.15, 0.492, 2, 4, -47.4, 38.04, 0.468, 5, -47.4, 123.04, 0.532, 2, 4, -42.36, 38.04, 0.452, 5, -42.36, 123.04, 0.548, 2, 4, -38.64, 38.04, 0.452, 5, -38.64, 123.04, 0.548, 2, 4, -39.12, -5.3, 0.524, 5, -39.12, 79.7, 0.476, 2, 4, -33.36, -10.94, 0.54, 5, -33.36, 74.06, 0.46, 2, 4, -30.36, 7.3, 0.492, 5, -30.36, 92.3, 0.508, 2, 4, -25.56, -1.1, 0.516, 5, -25.56, 83.9, 0.484, 2, 4, -4.86, -20.64, 0.548, 5, -4.86, 64.36, 0.452, 2, 4, -5.22, -12.6, 0.564, 5, -5.22, 72.4, 0.436, 2, 4, -9.18, -7.92, 0.564, 5, -9.18, 77.08, 0.436, 2, 4, -21.06, 0, 0.524, 5, -21.06, 85, 0.476, 2, 4, -10.26, -23.16, 0.556, 5, -10.26, 61.84, 0.444, 2, 4, -19.86, -13.08, 0.556, 5, -19.86, 71.92, 0.444, 2, 4, -24.18, -12.6, 0.54, 5, -24.18, 72.4, 0.46, 2, 4, -28.98, -6, 0.532, 5, -28.98, 79, 0.468, 2, 4, -11.22, -13.32, 0.564, 5, -11.22, 71.68, 0.436, 2, 4, -7.02, -17.64, 0.564, 5, -7.02, 67.36, 0.436, 2, 4, -20.58, -6.72, 0.532, 5, -20.58, 78.28, 0.468, 2, 4, -26.46, -4.2, 0.532, 5, -26.46, 80.8, 0.468, 2, 4, -11.34, -1.08, 0.556, 5, -11.34, 83.92, 0.444, 2, 4, -3.3, -4.92, 0.548, 5, -3.3, 80.08, 0.452, 2, 4, -7.98, -28.43, 0.564, 5, -7.98, 56.57, 0.436, 2, 4, -13.02, -30.23, 0.548, 5, -13.02, 54.77, 0.452, 2, 4, -16.14, -33.95, 0.54, 5, -16.14, 51.05, 0.46, 2, 4, -23.46, -36.47, 0.54, 5, -23.46, 48.53, 0.46, 2, 4, -31.14, -18.83, 0.556, 5, -31.14, 66.17, 0.444, 2, 4, -31.38, -25.67, 0.556, 5, -31.38, 59.33, 0.444, 2, 4, -28.86, -29.15, 0.54, 5, -28.86, 55.85, 0.46, 2, 4, -22.5, -18.83, 0.548, 5, -22.5, 66.17, 0.452, 2, 4, -20.58, -22.19, 0.54, 5, -20.58, 62.81, 0.46, 2, 4, -14.94, -23.03, 0.54, 5, -14.94, 61.97, 0.46, 2, 4, -17.34, -15.23, 0.564, 5, -17.34, 69.77, 0.436, 2, 4, -15.18, -18.47, 0.548, 5, -15.18, 66.53, 0.452, 2, 4, -12.54, -20.27, 0.556, 5, -12.54, 64.73, 0.444, 2, 4, -14.88, -39.17, 0.532, 5, -14.88, 45.83, 0.468, 2, 4, -5.21, -35.85, 0.54, 5, -5.21, 49.15, 0.46, 2, 4, -15.8, -26.87, 0.53939, 5, -15.8, 58.13, 0.46061, 2, 4, -21.33, -30.72, 0.55291, 5, -21.33, 54.28, 0.44709, 2, 4, -23.39, 7.06, 0.5186, 5, -23.39, 92.06, 0.4814], "edges": [40, 42, 42, 44, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 48, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 44, 46, 46, 48, 36, 38, 38, 40, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22]}}, "c1": {"c1": {"x": -2.43, "width": 31, "y": -1.28, "height": 47}}, "c2": {"c2": {"x": 1.05, "width": 26, "y": -1.02, "height": 37}}, "c3": {"c2": {"x": 1.05, "width": 26, "y": -1.02, "height": 37}}, "frame_set/E3": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "frame_set/E2": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "frame_set/E1": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "frame_set/E0": {"frame_set/E4": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E0": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}}, "images/bg_vang2": {"images/bg_vang": {"x": -5.98, "width": 544, "y": 22.09, "height": 103}}, "images/bg_vang": {"images/bg_vang": {"x": -5.98, "width": 544, "y": 22.09, "height": 103}}, "images/v4": {"images/v4": {"x": -3.26, "width": 63, "y": 1.19, "height": 46}}, "Beard": {"Beard": {"width": 45, "type": "mesh", "hull": 37, "height": 43, "triangles": [17, 18, 19, 17, 19, 20, 16, 17, 20, 16, 20, 21, 16, 21, 22, 15, 16, 22, 23, 14, 15, 23, 15, 22, 13, 23, 24, 14, 23, 13, 12, 24, 25, 13, 24, 12, 12, 25, 26, 11, 12, 26, 10, 27, 9, 11, 27, 10, 26, 27, 11, 29, 30, 8, 28, 29, 8, 28, 8, 9, 9, 27, 28, 30, 31, 7, 30, 7, 8, 31, 32, 5, 31, 5, 6, 31, 6, 7, 33, 34, 4, 5, 32, 33, 4, 5, 33, 3, 35, 36, 34, 35, 3, 4, 34, 3, 2, 3, 36, 2, 36, 0, 2, 0, 1], "uvs": [1, 0.05372, 1, 0.16485, 0.86575, 0.10674, 0.7548, 0.1018, 0.66533, 0.15125, 0.63908, 0.25139, 0.65817, 0.36885, 0.688, 0.51721, 0.72612, 0.66943, 0.73566, 0.83633, 0.70465, 0.96615, 0.59967, 1, 0.46725, 1, 0.3563, 0.94513, 0.25489, 0.85612, 0.17139, 0.74608, 0.10219, 0.62739, 0.04612, 0.52601, 0, 0.4135, 0.0664, 0.37394, 0.14395, 0.48026, 0.19405, 0.56434, 0.26921, 0.67684, 0.34675, 0.78317, 0.42191, 0.85488, 0.50542, 0.89815, 0.56388, 0.91175, 0.59967, 0.88455, 0.61398, 0.82273, 0.59967, 0.6991, 0.57342, 0.56434, 0.5424, 0.40855, 0.51974, 0.24535, 0.5436, 0.1143, 0.6116, 0.02528, 0.73447, 0, 0.86928, 0], "vertices": [1, 13, -1.44, -2.77, 1, 1, 13, -3.01, 3.14, 1, 1, 13, 5.2, 2.02, 1, 3, 13, 11.38, 3.38, 0.09514, 14, 2.4, 2.53, 0.904, 15, -1.64, 6.56, 0.00086, 2, 14, 8.02, 3.87, 0.25718, 15, 2.75, 2.8, 0.74282, 2, 15, 8.42, 3.41, 0.40932, 16, 1.38, 3.15, 0.59068, 2, 16, 7.93, 3.13, 0.957, 17, -2.64, 3.26, 0.043, 1, 17, 5.69, 3.16, 1, 2, 17, 14.33, 3.48, 0.18925, 18, 1.14, 3.74, 0.81075, 2, 18, 10.34, 3.9, 0.97528, 19, -4.27, 1.34, 0.02472, 1, 19, 2.28, 4.69, 1, 2, 19, 7.61, 1.39, 0.4496, 20, -0.79, 1.6, 0.5504, 1, 20, 6.47, 3.69, 1, 2, 20, 13.38, 2.53, 0.24129, 21, 0.3, 3.04, 0.75871, 1, 21, 7.86, 3.56, 1, 2, 21, 15.49, 2.54, 0.73289, 22, 1.51, 3.28, 0.26711, 3, 21, 22.87, 0.61, 0.00346, 22, 9.13, 3.23, 0.88951, 23, -1.96, 3.4, 0.10702, 1, 23, 4.43, 2.7, 1, 1, 23, 10.98, 1.19, 1, 1, 23, 10.52, -3.15, 1, 2, 22, 14.79, -3.03, 0.0062, 23, 3.19, -3.29, 0.9938, 2, 22, 9.35, -3.04, 0.91274, 23, -2.23, -2.88, 0.08726, 1, 22, 1.84, -3.46, 1, 2, 21, 6.91, -2.97, 0.91488, 22, -5.46, -4.17, 0.08512, 2, 20, 11.16, -3.27, 0.40685, 21, 1.08, -3.13, 0.59315, 2, 19, 6.91, -6.34, 0.005, 20, 5.92, -2.3, 0.995, 3, 18, 14.08, -6.05, 0.00504, 19, 5.29, -3.33, 0.36704, 20, 2.51, -2.5, 0.62792, 3, 18, 12.67, -3.95, 0.16143, 19, 2.82, -2.77, 0.76288, 20, 0.97, -4.5, 0.07569, 2, 18, 9.31, -3, 0.88319, 19, -0.28, -4.39, 0.11681, 2, 17, 14.37, -3.91, 0.13783, 18, 2.48, -3.53, 0.86217, 1, 17, 6.81, -3.77, 1, 2, 16, 8.97, -3.74, 0.8549, 17, -1.94, -3.65, 0.1451, 2, 15, 10.59, -3.05, 0.33438, 16, -0.09, -3.5, 0.66562, 1, 15, 3.38, -4.4, 1, 2, 14, 9.23, -3.61, 0.73392, 15, -2.59, -2.58, 0.26608, 2, 13, 13.94, -1.73, 0.01753, 14, 2.1, -3.18, 0.98247, 1, 13, 6.52, -3.71, 1], "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 2, 0, 72, 0]}}, "images/v3": {"images/v3": {"x": 0.26, "width": 43, "y": -0.13, "height": 38}}, "images/v6": {"images/v6": {"x": -0.93, "width": 43, "y": -2.28, "height": 38}}, "images/v5": {"images/v5": {"x": 5.1, "width": 51, "y": -0.31, "height": 37}}, "images/v2": {"images/v2": {"x": -7.2, "width": 51, "y": -0.31, "height": 37}}, "images/v1": {"images/v1": {"x": 6.16, "width": 63, "y": -1.67, "height": 46}}, "Rubi": {"Rubi": {"x": -0.07, "width": 29, "y": 0.33, "height": 29}}, "Mouth2": {"Mouth": {"width": 17, "type": "mesh", "hull": 14, "height": 36, "triangles": [14, 27, 26, 27, 8, 9, 6, 17, 5, 5, 18, 4, 5, 17, 18, 6, 7, 16, 7, 15, 16, 17, 16, 28, 17, 6, 16, 4, 18, 25, 3, 4, 2, 25, 18, 29, 4, 25, 2, 17, 23, 18, 17, 28, 23, 25, 1, 2, 23, 24, 18, 18, 24, 29, 23, 28, 24, 16, 15, 28, 25, 29, 1, 28, 19, 24, 19, 28, 14, 24, 19, 29, 1, 29, 20, 7, 8, 15, 29, 19, 20, 20, 26, 1, 26, 0, 1, 28, 15, 14, 15, 8, 14, 19, 14, 20, 20, 14, 26, 26, 22, 0, 22, 13, 0, 21, 12, 22, 9, 11, 21, 21, 11, 12, 22, 12, 13, 9, 10, 11, 8, 27, 14, 27, 22, 26, 27, 9, 21, 27, 21, 22], "uvs": [1, 0.19466, 1, 0.73534, 1, 0.86566, 1, 0.94884, 0.85238, 0.94884, 0.71295, 1, 0.50962, 1, 0.10296, 0.75752, 0.132, 0.47748, 0, 0.1808, 0, 0, 0.34695, 0, 0.72457, 0, 1, 0, 0.376, 0.49412, 0.32372, 0.70762, 0.42829, 0.82407, 0.59676, 0.89061, 0.75943, 0.86289, 0.68972, 0.65216, 0.72457, 0.58562, 0.34695, 0.19189, 0.76524, 0.18634, 0.60286, 0.83097, 0.72852, 0.7772, 0.92352, 0.81029, 0.76977, 0.41023, 0.34996, 0.34114, 0.55029, 0.78341, 0.85029, 0.76627], "vertices": [3, 4, -1.74, -53.71, 0.47242, 5, -1.74, 31.29, 0.42528, 109, -1.74, 36.29, 0.1023, 3, 4, -1.74, -81.72, 0.458, 5, -1.74, 3.28, 0.32108, 109, -1.74, 8.28, 0.22092, 3, 4, -1.74, -88.35, 0.45193, 5, -1.74, -3.35, 0.30223, 109, -1.74, 1.65, 0.24585, 3, 4, -1.74, -92.44, 0.43803, 5, -1.74, -7.44, 0.29951, 109, -1.74, -2.44, 0.26246, 3, 4, 1.36, -92, 0.44051, 5, 1.36, -7, 0.30225, 109, 1.36, -2, 0.25723, 3, 4, 4.29, -93.37, 0.42359, 5, 4.29, -8.37, 0.29487, 109, 4.29, -3.37, 0.28154, 3, 4, 8.56, -93.81, 0.43044, 5, 8.56, -8.81, 0.26802, 109, 8.56, -3.81, 0.30154, 3, 4, 17.1, -82.7, 0.426, 5, 17.1, 2.3, 0.32662, 109, 17.1, 7.3, 0.24738, 3, 4, 16.49, -69.05, 0.414, 5, 16.49, 15.95, 0.33892, 109, 16.49, 20.95, 0.24708, 2, 4, 19.26, -52.75, 0.5, 5, 19.26, 32.25, 0.5, 2, 4, 19.26, -43.52, 0.5, 5, 19.26, 41.48, 0.5, 2, 4, 11.98, -42.54, 0.5, 5, 11.98, 42.46, 0.5, 2, 4, 4.05, -43.86, 0.5, 5, 4.05, 41.14, 0.5, 2, 4, -1.74, -43.64, 0.508, 5, -1.74, 41.36, 0.492, 3, 4, 11.37, -68.46, 0.43516, 5, 11.37, 16.54, 0.37727, 109, 11.37, 21.54, 0.18757, 3, 4, 12.47, -80.06, 0.43, 5, 12.47, 4.94, 0.35369, 109, 12.47, 9.94, 0.21631, 3, 4, 10.27, -86.07, 0.43761, 5, 10.27, -1.07, 0.32116, 109, 10.27, 3.93, 0.24123, 3, 4, 6.73, -89.44, 0.43906, 5, 6.73, -4.44, 0.3034, 109, 6.73, 0.56, 0.25754, 3, 4, 3.32, -87.33, 0.43263, 5, 3.32, -2.33, 0.32144, 109, 3.32, 2.67, 0.24593, 3, 4, 4.78, -78.06, 0.47242, 5, 4.78, 6.94, 0.35927, 109, 4.78, 11.94, 0.16831, 3, 4, 4.05, -73.81, 0.40887, 5, 4.05, 11.19, 0.34649, 109, 4.05, 16.19, 0.24464, 3, 4, 11.98, -52.96, 0.444, 5, 11.98, 32.04, 0.432, 109, 11.98, 37.04, 0.124, 3, 4, 3.19, -51.84, 0.432, 5, 3.19, 33.16, 0.432, 109, 3.19, 38.16, 0.136, 3, 4, 6.6, -85.49, 0.43605, 5, 6.6, -0.49, 0.32734, 109, 6.6, 4.51, 0.23662, 3, 4, 3.96, -83.14, 0.45595, 5, 3.96, 1.86, 0.33943, 109, 3.96, 6.86, 0.20461, 3, 4, -0.13, -84.95, 0.45904, 5, -0.13, 0.05, 0.33204, 109, -0.13, 5.05, 0.20892, 3, 4, 3.1, -64.55, 0.44387, 5, 3.1, 20.45, 0.40922, 109, 3.1, 25.45, 0.14691, 3, 4, 11.91, -60.19, 0.384, 5, 11.91, 24.81, 0.35926, 109, 11.91, 29.81, 0.25674, 3, 4, 7.71, -83.88, 0.44392, 5, 7.71, 1.12, 0.32315, 109, 7.71, 6.12, 0.23292, 3, 4, 1.41, -82.73, 0.446, 5, 1.41, 2.27, 0.33338, 109, 1.41, 7.27, 0.22061], "edges": [18, 20, 18, 16, 16, 14, 14, 12, 20, 22, 10, 12, 10, 8, 4, 6, 8, 6, 38, 40, 4, 2, 40, 2, 38, 28, 28, 30, 22, 24, 24, 26, 2, 0, 0, 26]}}, "Fang2": {"Fang": {"scaleX": -1, "rotation": 89.2, "x": 11.02, "width": 20, "y": 1.29, "height": 21}}, "Mouth": {"Mouth": {"width": 17, "type": "mesh", "hull": 14, "height": 36, "triangles": [9, 10, 11, 22, 12, 13, 21, 11, 12, 9, 11, 21, 21, 12, 22, 22, 13, 0, 27, 21, 22, 0, 20, 27, 19, 27, 20, 15, 8, 14, 28, 15, 14, 0, 27, 22, 1, 20, 0, 29, 19, 20, 1, 29, 20, 7, 8, 15, 19, 28, 14, 24, 19, 29, 28, 19, 24, 25, 29, 1, 16, 15, 28, 23, 28, 24, 16, 28, 23, 18, 24, 29, 18, 29, 25, 23, 24, 18, 25, 1, 2, 17, 16, 23, 17, 23, 18, 4, 25, 2, 4, 18, 25, 3, 4, 2, 6, 16, 17, 7, 15, 16, 6, 7, 16, 5, 17, 18, 5, 18, 4, 6, 17, 5, 26, 21, 27, 8, 9, 26, 14, 26, 27, 26, 9, 21, 8, 26, 14, 14, 27, 19], "uvs": [1, 0.19466, 1, 0.73534, 1, 0.86566, 1, 0.94884, 0.85238, 0.94884, 0.71295, 1, 0.50962, 1, 0.10296, 0.75752, 0.132, 0.47748, 0, 0.1808, 0, 0, 0.34695, 0, 0.72457, 0, 1, 0, 0.376, 0.49412, 0.32372, 0.70762, 0.42829, 0.82407, 0.59676, 0.89061, 0.75943, 0.86289, 0.68972, 0.65216, 0.72457, 0.58562, 0.34695, 0.19189, 0.76524, 0.18634, 0.60286, 0.83097, 0.72852, 0.7772, 0.92352, 0.81029, 0.3227, 0.36013, 0.68741, 0.4459, 0.58912, 0.77631, 0.86769, 0.75164], "vertices": [2, 4, 0.84, -53.67, 0.53198, 5, 0.84, 31.33, 0.46802, 3, 4, 0.84, -81.28, 0.46542, 5, 0.84, 3.72, 0.32258, 109, 0.84, 8.72, 0.212, 3, 4, 0.84, -87.9, 0.466, 5, 0.84, -2.9, 0.30508, 109, 0.84, 2.1, 0.22892, 3, 4, 0.84, -91.56, 0.45338, 5, 0.84, -6.56, 0.31312, 109, 0.84, -1.56, 0.2335, 3, 4, -2.26, -91.56, 0.44248, 5, -2.26, -6.56, 0.30731, 109, -2.26, -1.56, 0.25021, 3, 4, -5.19, -93.37, 0.4358, 5, -5.19, -8.37, 0.30054, 109, -5.19, -3.37, 0.26366, 3, 4, -9.46, -93.37, 0.41603, 5, -9.46, -8.37, 0.34786, 109, -9.46, -3.37, 0.23611, 3, 4, -18, -80.05, 0.45, 5, -18, 4.95, 0.36046, 109, -18, 9.95, 0.18954, 3, 4, -17.39, -66.85, 0.44, 5, -17.39, 18.15, 0.40533, 109, -17.39, 23.15, 0.15467, 3, 4, -20.16, -51.86, 0.454, 5, -20.16, 33.14, 0.46091, 109, -20.16, 38.14, 0.08509, 2, 4, -20.16, -43.49, 0.5, 5, -20.16, 41.51, 0.5, 2, 4, -12.88, -44.18, 0.5, 5, -12.88, 40.82, 0.5, 2, 4, -4.95, -42.98, 0.5, 5, -4.95, 42.02, 0.5, 2, 4, 0.84, -43.42, 0.508, 5, 0.84, 41.58, 0.492, 3, 4, -12.27, -68.9, 0.44465, 5, -12.27, 16.1, 0.32928, 109, -12.27, 21.1, 0.22607, 3, 4, -13.37, -79.62, 0.45913, 5, -13.37, 5.38, 0.36087, 109, -13.37, 10.38, 0.18, 3, 4, -11.17, -85.19, 0.4511, 5, -11.17, -0.19, 0.35967, 109, -11.17, 4.81, 0.18923, 3, 4, -7.63, -88.55, 0.43761, 5, -7.63, -3.55, 0.32116, 109, -7.63, 1.45, 0.24123, 3, 4, -4.22, -87.33, 0.44829, 5, -4.22, -2.33, 0.31529, 109, -4.22, 2.67, 0.23643, 3, 4, -5.68, -76.3, 0.498, 5, -5.68, 8.7, 0.38138, 109, -5.68, 13.7, 0.12062, 3, 4, -4.95, -73.37, 0.45268, 5, -4.95, 11.63, 0.30896, 109, -4.95, 16.63, 0.23836, 3, 4, -12.88, -52.3, 0.474, 5, -12.88, 32.7, 0.43036, 109, -12.88, 37.7, 0.09564, 3, 4, -4.09, -52.94, 0.474, 5, -4.09, 32.06, 0.43036, 109, -4.09, 37.06, 0.09564, 3, 4, -7.5, -85.05, 0.45479, 5, -7.5, -0.05, 0.32833, 109, -7.5, 4.95, 0.21688, 3, 4, -4.86, -82.68, 0.46112, 5, -4.86, 2.32, 0.33111, 109, -4.86, 7.32, 0.20777, 3, 4, -0.77, -85.46, 0.45898, 5, -0.77, -0.46, 0.32041, 109, -0.77, 4.54, 0.22062, 3, 4, -13.39, -60.81, 0.424, 5, -13.39, 24.19, 0.40533, 109, -13.39, 29.19, 0.17067, 3, 4, -5.73, -66.12, 0.45193, 5, -5.73, 18.88, 0.32447, 109, -5.73, 23.88, 0.2236, 3, 4, -7.79, -82.48, 0.46304, 5, -7.79, 2.52, 0.32404, 109, -7.79, 7.52, 0.21292, 3, 4, -1.94, -82.28, 0.46591, 5, -1.94, 2.72, 0.33414, 109, -1.94, 7.72, 0.19995], "edges": [18, 20, 18, 16, 16, 14, 14, 12, 20, 22, 10, 12, 10, 8, 4, 6, 8, 6, 38, 40, 4, 2, 40, 2, 38, 28, 28, 30, 22, 24, 24, 26, 2, 0, 0, 26]}}, "Nose": {"Nose": {"width": 23, "type": "mesh", "hull": 11, "height": 18, "triangles": [18, 8, 9, 7, 8, 18, 10, 18, 9, 11, 18, 19, 21, 6, 7, 22, 6, 21, 23, 22, 14, 23, 14, 15, 5, 6, 22, 5, 22, 23, 12, 20, 3, 4, 12, 3, 0, 1, 20, 3, 20, 1, 1, 2, 3, 4, 15, 16, 4, 16, 12, 4, 23, 15, 5, 23, 4, 15, 14, 17, 15, 17, 16, 16, 17, 19, 20, 16, 19, 12, 16, 20, 22, 21, 13, 22, 13, 14, 14, 13, 11, 14, 11, 17, 13, 21, 7, 11, 13, 7, 17, 11, 19, 19, 10, 0, 19, 18, 10, 20, 19, 0, 11, 7, 18], "uvs": [1, 0.2328, 1, 1, 0.90724, 1, 0.82951, 0.90029, 0.50634, 0.95529, 0, 0.75529, 0, 0.3953, 0.31202, 0.2878, 0.39793, 0, 0.54111, 0, 0.79474, 0, 0.48179, 0.4628, 0.7477, 0.79529, 0.29975, 0.5028, 0.33043, 0.61279, 0.49611, 0.69529, 0.6577, 0.70529, 0.55747, 0.5753, 0.48588, 0.2553, 0.63724, 0.4803, 0.80906, 0.67029, 0.17089, 0.4278, 0.14634, 0.5578, 0.25271, 0.67779], "vertices": [2, 5, 0.83, 54.01, 0.436, 4, 0.83, -30.99, 0.564, 2, 5, 0.83, 40.2, 0.428, 4, 0.83, -44.8, 0.572, 2, 5, -1.21, 40.2, 0.42, 4, -1.21, -44.8, 0.58, 2, 5, -2.92, 42, 0.428, 4, -2.92, -43, 0.572, 2, 5, -10.03, 41.01, 0.436, 4, -10.03, -43.99, 0.564, 2, 5, -21.17, 44.61, 0.436, 4, -21.17, -40.39, 0.564, 2, 5, -21.17, 51.09, 0.436, 4, -21.17, -33.91, 0.564, 2, 5, -14.3, 53.02, 0.436, 4, -14.3, -31.98, 0.564, 2, 5, -12.41, 58.2, 0.436, 4, -12.41, -26.8, 0.564, 2, 5, -9.26, 58.2, 0.436, 4, -9.26, -26.8, 0.564, 2, 5, -3.68, 58.2, 0.436, 4, -3.68, -26.8, 0.564, 2, 5, -10.57, 49.87, 0.42, 4, -10.57, -35.13, 0.58, 2, 5, -4.72, 43.89, 0.42, 4, -4.72, -41.11, 0.58, 2, 5, -14.57, 49.15, 0.412, 4, -14.57, -35.85, 0.588, 2, 5, -13.9, 47.17, 0.412, 4, -13.9, -37.83, 0.588, 2, 5, -10.25, 45.69, 0.42, 4, -10.25, -39.31, 0.58, 2, 5, -6.7, 45.51, 0.412, 4, -6.7, -39.49, 0.588, 2, 5, -8.9, 47.85, 0.412, 4, -8.9, -37.15, 0.588, 2, 5, -10.48, 53.61, 0.42, 4, -10.48, -31.39, 0.58, 2, 5, -7.15, 49.56, 0.42, 4, -7.15, -35.44, 0.58, 2, 5, -3.37, 46.14, 0.412, 4, -3.37, -38.86, 0.588, 2, 5, -17.41, 50.5, 0.428, 4, -17.41, -34.5, 0.572, 2, 5, -17.95, 48.16, 0.428, 4, -17.95, -36.84, 0.572, 2, 5, -15.61, 46, 0.42, 4, -15.61, -39, 0.58], "edges": [12, 14, 14, 16, 2, 0, 20, 0, 10, 12, 10, 8, 8, 6, 2, 4, 6, 4, 16, 18, 18, 20]}}, "Eye2": {"Eye": {"scaleX": -1, "x": 0.5, "width": 14, "y": -0.18, "height": 13}}, "Nose2": {"Nose": {"width": 23, "type": "mesh", "hull": 11, "height": 18, "triangles": [0, 1, 20, 3, 20, 1, 4, 12, 3, 5, 23, 4, 4, 23, 15, 23, 14, 15, 4, 16, 12, 4, 15, 16, 12, 20, 3, 1, 2, 3, 12, 16, 20, 5, 22, 23, 5, 6, 22, 15, 17, 16, 20, 16, 19, 15, 14, 17, 23, 22, 14, 16, 17, 19, 20, 19, 0, 22, 13, 14, 14, 11, 17, 14, 13, 11, 17, 11, 19, 22, 21, 13, 22, 6, 21, 11, 13, 7, 11, 18, 19, 19, 18, 10, 19, 10, 0, 10, 18, 9, 13, 21, 7, 11, 7, 18, 21, 6, 7, 7, 8, 18, 18, 8, 9], "uvs": [1, 0.2328, 1, 1, 0.90724, 1, 0.82951, 0.90029, 0.50634, 0.95529, 0, 0.75529, 0, 0.3953, 0.31202, 0.2878, 0.39793, 0, 0.54111, 0, 0.79474, 0, 0.48179, 0.4628, 0.7477, 0.79529, 0.29975, 0.5028, 0.33043, 0.61279, 0.49611, 0.69529, 0.6577, 0.70529, 0.55747, 0.5753, 0.48588, 0.2553, 0.63724, 0.4803, 0.80906, 0.67029, 0.17089, 0.4278, 0.14634, 0.5578, 0.25271, 0.67779], "vertices": [2, 4, -1.46, -30.99, 0.572, 5, -1.46, 54.01, 0.428, 2, 4, -1.46, -44.8, 0.572, 5, -1.46, 40.2, 0.428, 2, 4, 0.58, -44.8, 0.572, 5, 0.58, 40.2, 0.428, 2, 4, 2.29, -43, 0.58, 5, 2.29, 42, 0.42, 2, 4, 9.4, -43.99, 0.572, 5, 9.4, 41.01, 0.428, 2, 4, 20.54, -40.39, 0.572, 5, 20.54, 44.61, 0.428, 2, 4, 20.54, -33.91, 0.572, 5, 20.54, 51.09, 0.428, 2, 4, 13.68, -31.98, 0.588, 5, 13.68, 53.02, 0.412, 2, 4, 11.79, -26.8, 0.572, 5, 11.79, 58.2, 0.428, 2, 4, 8.64, -26.8, 0.572, 5, 8.64, 58.2, 0.428, 2, 4, 3.06, -26.8, 0.572, 5, 3.06, 58.2, 0.428, 2, 4, 9.94, -35.13, 0.604, 5, 9.94, 49.87, 0.396, 2, 4, 4.09, -41.11, 0.588, 5, 4.09, 43.89, 0.412, 2, 4, 13.95, -35.85, 0.588, 5, 13.95, 49.15, 0.412, 2, 4, 13.27, -37.83, 0.596, 5, 13.27, 47.17, 0.404, 2, 4, 9.63, -39.31, 0.596, 5, 9.63, 45.69, 0.404, 2, 4, 6.07, -39.49, 0.604, 5, 6.07, 45.51, 0.396, 2, 4, 8.28, -37.15, 0.604, 5, 8.28, 47.85, 0.396, 2, 4, 9.85, -31.39, 0.588, 5, 9.85, 53.61, 0.412, 2, 4, 6.52, -35.44, 0.588, 5, 6.52, 49.56, 0.412, 2, 4, 2.74, -38.86, 0.572, 5, 2.74, 46.14, 0.428, 2, 4, 16.78, -34.5, 0.58, 5, 16.78, 50.5, 0.42, 2, 4, 17.32, -36.84, 0.58, 5, 17.32, 48.16, 0.42, 2, 4, 14.98, -39, 0.596, 5, 14.98, 46, 0.404], "edges": [12, 14, 14, 16, 2, 0, 20, 0, 10, 12, 10, 8, 8, 6, 2, 4, 6, 4, 16, 18, 18, 20]}}, "Beard10": {"Beard10": {"width": 16, "type": "mesh", "hull": 9, "height": 43, "triangles": [0, 7, 8, 6, 7, 0, 0, 4, 6, 4, 5, 6, 3, 4, 0, 1, 3, 0, 3, 1, 2], "uvs": [0.7557, 0.38983, 1, 0.66263, 1, 1, 0.5056, 1, 0.33191, 0.78583, 0, 0.60396, 0, 0.36049, 0.56118, 0, 0.81128, 0], "vertices": [3, 50, 24.09, -5.51, 0.00103, 51, 11.33, -6.73, 0.50207, 52, 2.29, -6.8, 0.4969, 3, 50, 11.14, -7.65, 0.65738, 51, -1.77, -7.51, 0.34258, 52, -7.16, -15.91, 5e-05, 1, 50, -3.75, -4.67, 1, 1, 50, -1.91, 4.54, 1, 2, 50, 8.19, 5.88, 0.86453, 51, -3.29, 6.26, 0.13547, 3, 50, 17.45, 10.46, 0.06726, 51, 6.4, 9.85, 0.9309, 52, -12.22, 2.6, 0.00184, 2, 51, 16.86, 6.59, 0.20796, 52, -2.15, 6.91, 0.79204, 1, 52, 16.96, 3.5, 1, 1, 52, 18.83, -0.86, 1], "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 4, 2, 0, 2, 14, 16, 0, 16]}}, "Beard11": {"Beard10": {"width": 16, "type": "mesh", "hull": 9, "height": 43, "triangles": [0, 7, 8, 6, 7, 0, 4, 5, 6, 0, 4, 6, 3, 4, 0, 1, 3, 0, 3, 1, 2], "uvs": [0.7557, 0.38983, 1, 0.66263, 1, 1, 0.5056, 1, 0.33191, 0.78583, 0, 0.60396, 0, 0.36049, 0.56118, 0, 0.81128, 0], "vertices": [3, 64, 24.3, 4.47, 0.00172, 65, 11.31, 6.36, 0.72462, 66, 2.65, 6.45, 0.27366, 2, 64, 11.46, 7.17, 0.8494, 65, -1.8, 6.97, 0.1506, 1, 64, -3.54, 4.83, 1, 2, 64, -2.1, -4.45, 0.99976, 65, -13.32, -6.67, 0.00024, 3, 64, 7.93, -6.23, 0.60869, 65, -3.13, -6.82, 0.37724, 66, -16.88, 7.37, 0.01407, 3, 64, 16.99, -11.2, 0.00207, 65, 6.61, -10.28, 0.70694, 66, -12.46, -1.97, 0.29099, 2, 65, 17.02, -6.88, 0.00812, 66, -2.7, -6.94, 0.99188, 1, 66, 16.6, -4.82, 1, 1, 66, 18.76, -0.58, 1], "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 4, 2, 0, 2, 14, 16, 0, 16]}}, "Beard12": {"Beard9": {"width": 26, "type": "mesh", "hull": 10, "height": 46, "triangles": [7, 8, 4, 6, 7, 4, 5, 6, 4, 9, 0, 3, 8, 9, 3, 8, 3, 4, 2, 0, 1, 3, 0, 2], "uvs": [0.55322, 0, 1, 0, 0.74222, 0.24908, 0.65822, 0.41921, 0.66522, 0.58539, 0.88222, 0.90586, 0.65822, 1, 0.33622, 1, 0, 0.60517, 0, 0.30843], "vertices": [1, 69, 9.43, -6.4, 1, 1, 69, 18.43, 0.95, 1, 2, 69, 5.99, 5.59, 0.78966, 68, 14.83, 8.19, 0.21034, 3, 69, -0.65, 10.27, 0.08657, 68, 6.86, 6.64, 0.90616, 67, 19.77, 8.9, 0.00727, 2, 68, -0.75, 7.42, 0.39947, 67, 12.54, 6.42, 0.60053, 1, 67, -3.24, 6.61, 1, 1, 67, -5.29, -0.35, 1, 1, 67, -2.39, -8.2, 1, 2, 68, -3.02, -9.75, 0.35528, 67, 17.67, -10.12, 0.64472, 3, 69, -10.68, -4.5, 0.00256, 68, 10.58, -10.83, 0.99254, 67, 30.48, -5.39, 0.0049], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18]}}, "Beard13": {"Beard8": {"width": 26, "type": "mesh", "hull": 11, "height": 45, "triangles": [6, 7, 5, 7, 4, 5, 8, 10, 4, 8, 9, 10, 7, 8, 4, 4, 10, 3, 10, 0, 3, 3, 0, 2, 2, 0, 1], "uvs": [0.72434, 0, 1, 0, 0.99665, 0.13717, 0.76519, 0.3063, 0.74476, 0.47544, 0.85369, 0.80977, 0.84007, 1, 0.40438, 1, 0, 0.57377, 0, 0.29057, 0.35672, 0.10177], "vertices": [1, 91, 14.45, -5.16, 1, 1, 91, 20.39, -1.14, 1, 2, 91, 16.86, 3.92, 0.99955, 90, 22.01, 16.01, 0.00045, 3, 91, 7.61, 6.85, 0.72728, 90, 14.26, 10.17, 0.26762, 89, 17.42, 16.71, 0.0051, 3, 91, 2.9, 12.86, 0.12546, 90, 6.63, 9.82, 0.69065, 89, 11.55, 11.83, 0.18389, 2, 90, -8.34, 13, 0.01075, 89, -2.31, 5.34, 0.98925, 1, 89, -9.05, 0.05, 1, 1, 89, -2.43, -9.14, 1, 2, 90, 1.76, -9.44, 0.96252, 89, 19.28, -6.47, 0.03748, 2, 91, -8.47, -4.88, 0.00077, 90, 14.5, -9.74, 0.99923, 2, 91, 3.97, -6.72, 0.99967, 90, 23.21, -0.66, 0.00033], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20]}}, "Beard14": {"Beard7": {"width": 22, "type": "mesh", "hull": 11, "height": 59, "triangles": [0, 2, 10, 1, 2, 0, 3, 10, 2, 8, 9, 10, 8, 10, 3, 7, 8, 3, 7, 3, 4, 6, 7, 4, 5, 6, 4], "uvs": [1, 0, 1, 0.15434, 0.73464, 0.32773, 0.70464, 0.4368, 0.75714, 0.55706, 1, 0.81994, 0.89964, 1, 0.47964, 1, 0, 0.63816, 0, 0.32773, 0.35964, 0.15714], "vertices": [1, 28, 47.27, -25.24, 1, 1, 28, 38.72, -22.1, 1, 1, 28, 31.13, -13.1, 1, 1, 28, 25.31, -10.27, 1, 1, 28, 18.25, -8.91, 1, 1, 28, 1.85, -8.6, 1, 1, 28, -7.37, -2.87, 1, 1, 28, -4.19, 5.81, 1, 1, 28, 19.49, 8.37, 1, 1, 28, 36.68, 2.07, 1, 1, 28, 43.41, -8.82, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20]}}, "Beard8": {"Beard8": {"width": 26, "type": "mesh", "hull": 11, "height": 45, "triangles": [7, 8, 4, 7, 4, 5, 6, 7, 5, 8, 9, 10, 8, 10, 4, 2, 0, 1, 3, 0, 2, 10, 0, 3, 4, 10, 3], "uvs": [0.72434, 0, 1, 0, 0.99665, 0.13717, 0.76519, 0.3063, 0.74476, 0.47544, 0.85369, 0.80977, 0.84007, 1, 0.40438, 1, 0, 0.57377, 0, 0.29057, 0.35672, 0.10177], "vertices": [1, 27, 15.56, 3.44, 1, 1, 27, 21.04, -1.19, 1, 2, 27, 16.99, -5.85, 0.9976, 26, 22.75, -16.22, 0.0024, 3, 27, 7.48, -7.77, 0.68385, 26, 14.72, -10.78, 0.28124, 25, 25.13, -16.16, 0.03491, 3, 27, 2.16, -13.24, 0.11004, 26, 7.09, -10.81, 0.55855, 25, 18.27, -12.83, 0.33141, 2, 26, -7.71, -14.75, 0.00418, 25, 3.26, -9.84, 0.99582, 1, 25, -4.55, -6.32, 1, 1, 25, -0.33, 4.19, 1, 2, 26, 1.25, 8.17, 0.87368, 25, 21.39, 6.79, 0.12632, 2, 27, -7.26, 5.61, 0.27972, 26, 13.96, 9.11, 0.72028, 1, 27, 5.31, 6.11, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20]}}, "Beard15": {"Beard6": {"width": 17, "type": "mesh", "hull": 9, "height": 66, "triangles": [4, 5, 3, 5, 6, 2, 6, 7, 2, 3, 5, 2, 7, 8, 2, 0, 2, 8, 1, 2, 0], "uvs": [1, 0, 1, 0.16578, 0.81606, 0.36942, 0.89512, 0.5459, 1, 0.82421, 0.68429, 1, 0.2363, 1, 0, 0.72578, 0, 0.27439], "vertices": [1, 81, 16.99, -1.16, 1, 3, 81, 7.48, 4.26, 0.99923, 79, 29.28, 15.4, 6e-05, 80, 21.01, 5, 0.00071, 4, 81, -5.74, 8.2, 0.06261, 79, 16.46, 10.3, 0.40489, 80, 7.45, 7.58, 0.53199, 78, 30.31, 12.23, 0.0005, 3, 79, 4.74, 9.88, 0.77794, 80, -2.65, 13.52, 0.00017, 78, 18.73, 10.39, 0.22189, 1, 78, 0.56, 7.15, 1, 1, 78, -9.16, -1.15, 1, 1, 78, -7.11, -8.48, 1, 2, 79, -4.72, -6.94, 0.02277, 78, 11.4, -7.47, 0.97723, 2, 81, -7.16, -6.96, 0.01988, 80, 7.57, -7.65, 0.98012], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16]}}, "Beard9": {"Beard9": {"width": 26, "type": "mesh", "hull": 10, "height": 46, "triangles": [7, 8, 4, 6, 7, 4, 5, 6, 4, 9, 0, 3, 8, 9, 3, 8, 3, 4, 2, 0, 1, 3, 0, 2], "uvs": [0.55322, 0, 1, 0, 0.74222, 0.24908, 0.65822, 0.41921, 0.66522, 0.58539, 0.88222, 0.90586, 0.65822, 1, 0.33622, 1, 0, 0.60517, 0, 0.30843], "vertices": [1, 49, 10.57, 6.75, 1, 1, 49, 18.84, -1.41, 1, 2, 49, 6.02, -4.86, 0.87237, 48, 15.89, -7.65, 0.12763, 3, 49, -1.03, -8.9, 0.10416, 48, 7.96, -5.87, 0.86755, 47, 20.27, -9.07, 0.02829, 3, 49, -6.27, -14.47, 4e-05, 48, 0.34, -6.45, 0.35912, 47, 13.39, -5.75, 0.64084, 1, 47, -2.31, -4.05, 1, 1, 47, -3.51, 3.11, 1, 1, 47, 0.31, 10.56, 1, 2, 48, -1.47, 10.77, 0.61022, 47, 20.46, 10.06, 0.38978, 3, 49, -9.64, 6.75, 0.02159, 48, 12.16, 11.48, 0.9782, 47, 32.61, 3.84, 0.0002], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18]}}, "Beard16": {"Beard5": {"width": 21, "type": "mesh", "hull": 10, "height": 50, "triangles": [6, 4, 5, 6, 3, 4, 3, 7, 2, 3, 6, 7, 7, 8, 2, 2, 8, 1, 8, 9, 1, 1, 9, 0], "uvs": [0.75898, 0, 0.64298, 0.31206, 0.63331, 0.4298, 0.68165, 0.5516, 1, 0.81956, 1, 1, 0.35298, 1, 0, 0.63686, 0, 0.36078, 0.49798, 0], "vertices": [1, 84, 26.83, -0.93, 1, 1, 84, 11.86, 4.11, 1, 2, 84, 6.55, 6.65, 0.95062, 83, 14.93, 9.52, 0.04938, 3, 84, 1.61, 10.36, 0.39867, 83, 9.04, 7.65, 0.57746, 82, 17.24, 10.41, 0.02386, 2, 83, -5.93, 7.51, 0.00606, 82, 3.35, 4.84, 0.99394, 1, 82, -2.65, -1.9, 1, 2, 83, -7.78, -8.7, 0.01339, 82, 7.5, -10.93, 0.98661, 2, 83, 11.76, -7.03, 0.99994, 82, 25.11, -2.28, 6e-05, 2, 84, 3.47, -6.74, 0.49007, 83, 24.05, -0.75, 0.50993, 1, 84, 24.3, -5.79, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18]}}, "Fang": {"Fang": {"rotation": 89.2, "x": 10.96, "width": 20, "y": -2.51, "height": 21}}, "Beard17": {"Beard4": {"width": 50, "type": "mesh", "hull": 11, "height": 21, "triangles": [6, 7, 2, 2, 7, 8, 2, 8, 9, 6, 2, 5, 2, 3, 5, 3, 4, 5, 0, 1, 10, 2, 9, 1, 9, 10, 1], "uvs": [0, 0, 0.33896, 0.24836, 0.55016, 0.21693, 0.85376, 0, 1, 0, 1, 0.46836, 0.82736, 0.89265, 0.53696, 1, 0.30266, 0.99479, 0.14426, 0.67265, 0, 0.20908], "vertices": [2, 76, 18.83, -1.59, 0.00052, 77, 6.75, 1.09, 0.99948, 3, 76, 4.03, 8.16, 0.32921, 74, 23.45, 12.86, 0.00387, 75, 13.22, 8.2, 0.66692, 3, 76, -2.18, 16.73, 0.00029, 74, 13.64, 8.91, 0.4155, 75, 2.64, 8.57, 0.58421, 1, 74, -2.03, 6.47, 1, 1, 74, -8.62, 3.32, 1, 1, 74, -4.38, -5.56, 1, 2, 74, 7.25, -9.87, 0.99994, 75, -10.83, -5.99, 6e-05, 2, 74, 21.32, -5.65, 0.0809, 75, 3.75, -7.85, 0.9191, 2, 76, -6.91, -3.21, 0.00649, 75, 15.45, -7.42, 0.99351, 3, 76, 3.35, -5.02, 0.94146, 77, -7.09, -6.66, 0.05004, 75, 23.19, -0.45, 0.0085, 1, 77, 4.32, -2.57, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20]}}, "Beard18": {"Beard3": {"width": 52, "type": "mesh", "hull": 15, "height": 20, "triangles": [5, 10, 3, 7, 9, 6, 7, 8, 9, 9, 10, 6, 10, 5, 6, 5, 3, 4, 1, 2, 11, 11, 2, 10, 2, 3, 10, 12, 13, 11, 11, 13, 1, 13, 14, 1, 14, 0, 1], "uvs": [0, 0.51115, 0.16672, 0.54215, 0.29489, 0.4104, 0.48268, 0.1314, 0.71518, 0, 0.94172, 0.1934, 1, 0.55765, 1, 1, 0.89403, 1, 0.75989, 0.77465, 0.64662, 0.7359, 0.47076, 0.8754, 0.28595, 1, 0.0773, 0.91415, 0, 0.74365], "vertices": [1, 73, 17.38, 2.31, 1, 2, 73, 8.71, 2.88, 0.98541, 72, 20.46, 7.4, 0.01459, 2, 73, 2.47, 6.4, 0.40178, 72, 13.33, 6.64, 0.59822, 2, 72, 2.09, 7.06, 0.88201, 71, 22.05, 5.03, 0.11799, 1, 71, 10.11, 8.25, 1, 1, 71, -1.85, 4.97, 1, 1, 71, -5.24, -2.15, 1, 1, 71, -5.67, -10.99, 1, 1, 71, -0.17, -11.26, 1, 2, 72, -4.72, -11.02, 0.00775, 71, 7.02, -7.11, 0.99225, 2, 72, 0.14, -7.6, 0.2592, 71, 12.94, -6.62, 0.7408, 2, 72, 9.54, -5.84, 0.97971, 71, 21.94, -9.86, 0.02029, 2, 73, 1.32, -5.35, 0.85893, 72, 19.21, -3.59, 0.14107, 1, 73, 12.3, -5.13, 1, 1, 73, 16.75, -2.3, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28]}}, "Beard4": {"Beard4": {"width": 50, "type": "mesh", "hull": 11, "height": 21, "triangles": [6, 2, 5, 2, 3, 5, 3, 4, 5, 2, 9, 1, 2, 8, 9, 9, 10, 1, 6, 7, 2, 2, 7, 8, 0, 1, 10], "uvs": [0, 0, 0.33896, 0.24836, 0.55016, 0.21693, 0.85376, 0, 1, 0, 1, 0.46836, 0.82736, 0.89265, 0.53696, 1, 0.30266, 0.99479, 0.14426, 0.67265, 0, 0.20908], "vertices": [1, 42, 11.95, -0.2, 1, 3, 42, -4.56, -6.64, 0.01764, 40, 17.36, -8.83, 0.18767, 41, 6.64, -6.04, 0.79469, 3, 40, 6.78, -8.44, 0.87911, 41, -2.02, -12.12, 0.05006, 39, 15.33, -10.74, 0.07083, 2, 40, -8.77, -11.47, 0.00705, 39, 0.46, -5.25, 0.99295, 1, 39, -5.39, -0.86, 1, 1, 39, 0.52, 7, 1, 2, 40, -5.6, 7.05, 0.15495, 39, 12.78, 8.94, 0.84505, 1, 40, 9.07, 7.86, 1, 2, 40, 20.71, 6.59, 0.33855, 41, 0.01, 8.28, 0.66145, 2, 42, -2.48, 6.39, 0.1642, 41, 10.3, 6.64, 0.8358, 2, 42, 9.22, 3.24, 0.9996, 41, 21.53, 2.1, 0.0004], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20]}}, "Beard19": {"Beard2": {"width": 40, "type": "mesh", "hull": 13, "height": 21, "triangles": [13, 8, 14, 6, 7, 5, 13, 7, 8, 13, 5, 7, 13, 4, 5, 10, 11, 15, 15, 14, 10, 14, 9, 10, 8, 9, 14, 3, 13, 14, 2, 3, 14, 4, 13, 3, 15, 11, 12, 15, 12, 0, 15, 0, 1, 2, 15, 1, 14, 15, 2], "uvs": [1, 0.24565, 1, 0.46609, 0.88512, 0.68654, 0.65978, 0.88631, 0.43801, 0.8932, 0.36468, 1, 0, 1, 0, 0.77265, 0.18763, 0.6142, 0.29493, 0.32143, 0.46841, 0.07343, 0.60791, 0, 0.81001, 0, 0.34322, 0.69687, 0.51134, 0.43165, 0.71522, 0.25254], "vertices": [1, 97, -6.24, -3.22, 1, 2, 97, -4.48, -8.9, 0.99287, 98, -14.33, -12.77, 0.00713, 2, 97, 2.9, -12.8, 0.88332, 98, -6.22, -14.78, 0.11668, 2, 97, 15.2, -14.6, 0.2983, 98, 6.11, -13.53, 0.7017, 3, 97, 25.93, -11.55, 0.00548, 98, 15.79, -8, 0.70067, 99, -1.67, -7.87, 0.29386, 2, 98, 20.31, -8.28, 0.23976, 99, 2.56, -9.42, 0.76024, 1, 99, 20.52, -3.5, 1, 1, 99, 18.65, 2.34, 1, 2, 98, 21.91, 5.6, 0.00658, 99, 8.06, 3.44, 0.99342, 2, 98, 14.08, 9.05, 0.68412, 99, 1.59, 9, 0.31588, 2, 98, 3.51, 10.34, 0.99907, 99, -8.18, 13.29, 0.00093, 2, 97, 10.97, 9.08, 0.20429, 98, -3.69, 8.47, 0.79571, 2, 97, 1.21, 6.05, 0.97663, 98, -12.39, 3.14, 0.02337, 2, 98, 16.54, -0.81, 0.05319, 99, 1.08, -1.17, 0.94681, 2, 98, 6.42, 0.85, 0.99998, 99, -8.12, 3.33, 2e-05, 1, 97, 7.95, 1, 1], "edges": [22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 22, 24, 2, 0, 24, 0]}}, "Beard5": {"Beard5": {"width": 21, "type": "mesh", "hull": 10, "height": 50, "triangles": [6, 3, 4, 6, 4, 5, 7, 8, 2, 3, 6, 7, 3, 7, 2, 1, 9, 0, 8, 9, 1, 2, 8, 1], "uvs": [0.75898, 0, 0.64298, 0.31206, 0.63331, 0.4298, 0.68165, 0.5516, 1, 0.81956, 1, 1, 0.35298, 1, 0, 0.63686, 0, 0.36078, 0.49798, 0], "vertices": [1, 38, 25.36, -2.76, 1, 2, 38, 9.91, -6.02, 0.80769, 37, 20.6, -10.53, 0.19231, 2, 38, 4.33, -7.92, 0.20034, 37, 15.05, -8.57, 0.79966, 2, 37, 8.93, -7.71, 0.97126, 36, 16.69, -10.64, 0.02874, 2, 37, -5.85, -10.06, 0.06859, 36, 2.2, -6.87, 0.93141, 1, 36, -4.59, -0.94, 1, 2, 37, -10.38, 5.61, 0.00206, 36, 4.34, 9.3, 0.99794, 2, 38, -10.06, 0.84, 0.20994, 37, 9.17, 7.23, 0.79006, 1, 38, 2.84, 5.74, 1, 1, 38, 23.42, 2.36, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18]}}, "Bread_Mouth": {"Bread_Mouth": {"rotation": 101.48, "x": 12.08, "width": 11, "y": 0.67, "height": 29}}, "Beard6": {"Beard6": {"width": 17, "type": "mesh", "hull": 9, "height": 66, "triangles": [6, 7, 2, 5, 6, 2, 4, 5, 3, 0, 2, 8, 7, 8, 2, 3, 5, 2, 1, 2, 0], "uvs": [1, 0, 1, 0.16578, 0.81606, 0.36942, 0.89512, 0.5459, 1, 0.82421, 0.68429, 1, 0.2363, 1, 0, 0.72578, 0, 0.27439], "vertices": [1, 35, 17.53, 1.89, 1, 1, 35, 8.6, -4.43, 1, 2, 35, -4.18, -9.65, 0.06901, 34, 6.86, -7.72, 0.93099, 3, 34, -3.97, -12.22, 0.48415, 32, 22.68, -9.86, 0.15165, 33, 6.62, -10.44, 0.3642, 3, 34, -21.13, -18.98, 0.00337, 32, 4.4, -12.37, 0.99661, 33, -11.81, -11.44, 2e-05, 1, 32, -7.4, -7.46, 1, 1, 32, -7.7, 0.15, 1, 2, 32, 10.22, 4.88, 0.97997, 33, -4.6, 5.27, 0.02003, 3, 35, -7.07, 5.3, 0.02713, 34, 9.07, 7.34, 0.84511, 33, 25.17, 4.01, 0.12776], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16]}}, "Beard7": {"Beard7": {"width": 22, "type": "mesh", "hull": 11, "height": 59, "triangles": [0, 2, 10, 1, 2, 0, 3, 10, 2, 8, 9, 10, 8, 10, 3, 7, 8, 3, 7, 3, 4, 6, 7, 4, 5, 6, 4], "uvs": [1, 0, 1, 0.15434, 0.73464, 0.32773, 0.70464, 0.4368, 0.75714, 0.55706, 1, 0.81994, 0.89964, 1, 0.47964, 1, 0, 0.63816, 0, 0.32773, 0.35964, 0.15714], "vertices": [1, 28, 47.27, -25.24, 1, 1, 28, 38.72, -22.1, 1, 1, 28, 31.13, -13.1, 1, 1, 28, 25.31, -10.27, 1, 1, 28, 18.25, -8.91, 1, 1, 28, 1.85, -8.6, 1, 1, 28, -7.37, -2.87, 1, 1, 28, -4.19, 5.81, 1, 1, 28, 19.49, 8.37, 1, 1, 28, 36.68, 2.07, 1, 1, 28, 43.41, -8.82, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20]}}, "Beard2": {"Beard2": {"width": 40, "type": "mesh", "hull": 13, "height": 21, "triangles": [13, 4, 5, 13, 5, 7, 13, 7, 8, 6, 7, 5, 13, 8, 14, 4, 13, 3, 2, 3, 14, 3, 13, 14, 8, 9, 14, 14, 9, 10, 14, 15, 2, 2, 15, 1, 15, 0, 1, 15, 14, 10, 10, 11, 15, 15, 12, 0, 15, 11, 12], "uvs": [1, 0.24565, 1, 0.46609, 0.88512, 0.68654, 0.65978, 0.88631, 0.43801, 0.8932, 0.36468, 1, 0, 1, 0, 0.77265, 0.18763, 0.6142, 0.29493, 0.32143, 0.46841, 0.07343, 0.60791, 0, 0.81001, 0, 0.34322, 0.69687, 0.51134, 0.43165, 0.71522, 0.25254], "vertices": [2, 10, -8.03, 5.17, 0.94846, 11, -18.39, 10.44, 0.05154, 2, 10, -6.02, 10.77, 0.86737, 11, -14.95, 15.3, 0.13263, 2, 10, 1.61, 14.35, 0.56962, 11, -6.63, 16.71, 0.43038, 3, 10, 14.46, 15.47, 0.02785, 11, 6.05, 14.33, 0.97201, 12, -12.79, 11.19, 0.00013, 2, 11, 15.57, 7.82, 0.57037, 12, -1.82, 7.61, 0.42963, 2, 11, 20.34, 7.97, 0.12815, 12, 2.72, 9.1, 0.87185, 1, 12, 20.65, 2.93, 1, 1, 12, 18.65, -2.88, 1, 3, 10, 35.09, 0.27, 0.00064, 11, 21.84, -5.85, 0.02287, 12, 8.04, -3.75, 0.97649, 3, 10, 27.17, -5.29, 0.12228, 11, 12.71, -9.08, 0.6045, 12, 0.19, -9.4, 0.27322, 3, 10, 16.42, -8.54, 0.88359, 11, 1.48, -9.33, 0.11615, 12, -10.52, -12.8, 0.00027, 1, 10, 8.92, -7.96, 1, 1, 10, -0.97, -4.41, 1, 2, 11, 16.52, 0.64, 0.0426, 12, 1.11, 1, 0.9574, 3, 10, 17.58, 1.31, 0.00464, 11, 5.25, -0.15, 0.99535, 12, -9.48, -2.93, 1e-05, 2, 10, 5.97, 0.34, 0.98978, 11, -6.2, 2.04, 0.01022], "edges": [22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 22, 24, 2, 0, 24, 0]}}, "Eyebrow2": {"Eyebrow": {"width": 14, "type": "mesh", "hull": 6, "height": 36, "triangles": [3, 4, 2, 4, 5, 1, 4, 1, 2, 5, 0, 1], "uvs": [0.0149, 0, 0.59545, 0, 1, 0.37229, 1, 0.80984, 0.5099, 1, 0, 1], "vertices": [1, 95, 16.7, -3.51, 1, 1, 95, 14.18, 6.63, 1, 2, 95, -3.84, 9.66, 0.30992, 94, 11.07, 10.13, 0.69008, 2, 94, -8.54, 8.33, 0.01224, 93, 3.85, 9.97, 0.98776, 1, 93, -5.74, 2.29, 1, 1, 93, -6.89, -6.82, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10]}}, "Beard3": {"Beard3": {"width": 52, "type": "mesh", "hull": 15, "height": 20, "triangles": [5, 3, 4, 10, 5, 6, 9, 10, 6, 7, 8, 9, 7, 9, 6, 5, 10, 3, 2, 3, 10, 11, 2, 10, 14, 0, 1, 1, 2, 11, 13, 14, 1, 11, 13, 1, 12, 13, 11], "uvs": [0, 0.51115, 0.16672, 0.54215, 0.29489, 0.4104, 0.48268, 0.1314, 0.71518, 0, 0.94172, 0.1934, 1, 0.55765, 1, 1, 0.89403, 1, 0.75989, 0.77465, 0.64662, 0.7359, 0.47076, 0.8754, 0.28595, 1, 0.0773, 0.91415, 0, 0.74365], "vertices": [1, 45, 19.25, -3.46, 1, 1, 45, 10.59, -2.8, 1, 2, 45, 3.91, -5.41, 0.71099, 44, 16.52, -6.59, 0.28901, 2, 45, -5.88, -10.95, 0.00081, 44, 5.29, -7.18, 0.99919, 2, 44, -6.69, -4.1, 0.04339, 43, 9.33, -7.31, 0.95661, 1, 43, -2.96, -5.63, 1, 1, 43, -7.26, 0.99, 1, 1, 43, -8.86, 9.69, 1, 1, 43, -3.44, 10.68, 1, 2, 44, -1.81, 10.79, 0.0662, 43, 4.24, 7.51, 0.9338, 2, 44, 3.11, 7.45, 0.53738, 43, 10.17, 7.81, 0.46262, 3, 45, -5.19, 3.93, 0.07144, 44, 12.53, 5.83, 0.9259, 43, 18.66, 12.21, 0.00266, 1, 45, 4.43, 6.38, 1, 1, 45, 15.27, 4.62, 1, 1, 45, 19.27, 1.19, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28]}}}}], "skeleton": {"images": "C:/Users/<USER>/Desktop/ic_tx/6. zon club/btn rut tien/", "x": -277.95, "width": 544, "y": 1.06, "spine": "3.8.99", "audio": "", "hash": "04887fq6zAug8WIiCbMMBFIYbus", "height": 166.58}, "slots": [{"attachment": "images/bg_vang", "name": "images/bg_vang", "bone": "images/bg_vang"}, {"attachment": "images/bg_vang", "blend": "additive", "name": "images/bg_vang2", "bone": "images/bg_vang2"}, {"attachment": "images/v1", "name": "images/v1", "bone": "images/v1"}, {"attachment": "images/v2", "name": "images/v2", "bone": "images/v2"}, {"attachment": "images/v3", "name": "images/v3", "bone": "images/v3"}, {"attachment": "images/v4", "name": "images/v4", "bone": "images/v4"}, {"attachment": "images/v5", "name": "images/v5", "bone": "images/v5"}, {"attachment": "images/v6", "name": "images/v6", "bone": "images/v6"}, {"attachment": "bg1", "name": "bg1", "bone": "bg1"}, {"attachment": "bg1", "blend": "additive", "name": "bg2", "bone": "bg2"}, {"attachment": "c1", "name": "c1", "bone": "c1"}, {"attachment": "c2", "name": "c2", "bone": "c2"}, {"attachment": "c2", "name": "c3", "bone": "c3"}, {"attachment": "Beard10", "name": "Beard10", "bone": "Beard40"}, {"attachment": "Beard10", "name": "Beard11", "bone": "Beard43"}, {"attachment": "Beard9", "name": "Beard9", "bone": "Beard16"}, {"attachment": "Beard9", "name": "Beard12", "bone": "Beard46"}, {"attachment": "Beard8", "name": "Beard8", "bone": "Beard17"}, {"attachment": "Beard8", "name": "Beard13", "bone": "Beard68"}, {"attachment": "Beard7", "name": "Beard7", "bone": "Beard20"}, {"name": "Beard21", "bone": "Beard20"}, {"attachment": "Beard7", "name": "Beard14", "bone": "Beard64"}, {"attachment": "Beard6", "name": "Beard6", "bone": "Beard24"}, {"name": "Beard22", "bone": "Beard24"}, {"attachment": "Beard6", "name": "Beard15", "bone": "Beard57"}, {"attachment": "Beard5", "name": "Beard5", "bone": "Beard28"}, {"attachment": "Beard5", "name": "Beard16", "bone": "Beard61"}, {"attachment": "Beard4", "name": "Beard4", "bone": "Beard31"}, {"attachment": "Beard4", "name": "Beard17", "bone": "Beard53"}, {"attachment": "Beard3", "name": "Beard3", "bone": "Beard35"}, {"attachment": "Beard3", "name": "Beard18", "bone": "Beard50"}, {"attachment": "Bread_Mouth5", "name": "Bread_Mouth5", "bone": "Bread_Mouth5"}, {"attachment": "Bread_Mouth5", "name": "Bread_Mouth6", "bone": "Bread_Mouth8"}, {"attachment": "Bread_Mouth4", "name": "Bread_Mouth4", "bone": "Bread_Mouth4"}, {"attachment": "Bread_Mouth4", "name": "Bread_Mouth7", "bone": "Bread_Mouth13"}, {"attachment": "Bread_Mouth3", "name": "Bread_Mouth3", "bone": "Bread_Mouth10"}, {"attachment": "Bread_Mouth3", "name": "Bread_Mouth10", "bone": "Bread_Mouth15"}, {"attachment": "Bread_Mouth2", "name": "Bread_Mouth2", "bone": "Bread_Mouth3"}, {"attachment": "Bread_Mouth2", "name": "Bread_Mouth9", "bone": "Bread_Mouth14"}, {"attachment": "Bread_Mouth", "name": "Bread_Mouth", "bone": "Bread_Mouth"}, {"attachment": "Bread_Mouth", "name": "Bread_Mouth8", "bone": "Bread_Mouth2"}, {"attachment": "Mouth", "name": "Mouth", "bone": "Mouth"}, {"attachment": "Mouth", "name": "Mouth2", "bone": "Mouth2"}, {"attachment": "Rubi", "name": "Rubi", "bone": "Rubi"}, {"attachment": "<PERSON>", "name": "<PERSON>", "bone": "<PERSON>"}, {"attachment": "<PERSON>", "name": "Fang2", "bone": "Fang2"}, {"attachment": "Head", "name": "Head", "bone": "Dragon"}, {"attachment": "Head", "name": "Head2", "bone": "Dragon"}, {"attachment": "Eye", "name": "Eye", "bone": "Eye"}, {"attachment": "Eye", "name": "Eye2", "bone": "Eye2"}, {"attachment": "Eyebrow", "name": "Eyebrow", "bone": "Eyebrow"}, {"attachment": "Eyebrow", "name": "Eyebrow2", "bone": "Eyebrow4"}, {"attachment": "Beard2", "name": "Beard2", "bone": "Beard2"}, {"attachment": "Beard2", "name": "Beard19", "bone": "Beard71"}, {"attachment": "<PERSON>", "name": "<PERSON>", "bone": "<PERSON>"}, {"attachment": "<PERSON>", "name": "Beard20", "bone": "Beard70"}, {"attachment": "Nose", "name": "Nose", "bone": "Dragon"}, {"attachment": "Nose", "name": "Nose2", "bone": "Dragon"}, {"name": "frame_set/E0", "bone": "Fx_Rubi"}, {"name": "frame_set/E3", "bone": "Fx_Rubi4"}, {"name": "frame_set/E1", "bone": "Fx_Rubi2"}, {"name": "frame_set/E2", "bone": "Fx_Rubi3"}], "transform": [{"scaleMix": 0, "rotateMix": 0, "name": "3D_B", "y": -200, "translateMix": -0.25, "target": "3D", "shearMix": 0, "bones": ["3D_B"]}, {"scaleMix": 0, "rotation": 142.52, "rotateMix": 0, "name": "3D_Br_T", "x": -4.41, "y": -6.36, "translateMix": -0.05, "shearY": -360, "order": 7, "target": "3D_T", "shearMix": 0, "bones": ["Beard16"]}, {"scaleMix": 0, "rotation": 101.31, "rotateMix": 0, "name": "3D_Br_T2", "x": -3.98, "y": -4.2, "translateMix": -0.04, "shearY": -360, "order": 8, "target": "3D_T", "shearMix": 0, "bones": ["Beard40"]}, {"scaleMix": 0, "rotation": 77.95, "rotateMix": 0, "name": "3D_Br_T3", "x": 4.89, "y": -4.42, "translateMix": -0.04, "order": 15, "target": "3D_T", "shearMix": 0, "bones": ["Beard43"]}, {"scaleMix": 0, "rotation": 44.33, "rotateMix": 0, "name": "3D_Br_T4", "x": 6.25, "y": -7.01, "translateMix": -0.05, "order": 16, "target": "3D_T", "shearMix": 0, "bones": ["Beard46"]}, {"scaleMix": 0, "rotation": 144.57, "rotateMix": 0, "name": "3D_EyeBrowL", "x": -7.68, "y": -18.26, "translateMix": 0.12, "shearY": -360, "order": 2, "target": "3D_T", "shearMix": 0, "bones": ["Eyebrow"]}, {"scaleMix": 0, "rotation": 37.48, "rotateMix": 0, "name": "3D_EyeBrowR", "x": 8.32, "y": -18, "translateMix": 0.12, "order": 3, "target": "3D_T", "shearMix": 0, "bones": ["Eyebrow4"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_EyeL", "x": -16.16, "y": -17.28, "translateMix": 0.12, "order": 5, "target": "3D_T", "shearMix": 0, "bones": ["Eye"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_EyeR", "x": 19.17, "y": -17.28, "translateMix": 0.12, "order": 4, "target": "3D_T", "shearMix": 0, "bones": ["Eye2"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_MouthL", "x": -13.42, "y": 19.24, "translateMix": -0.05, "order": 20, "target": "3D_B", "shearMix": 0, "bones": ["Tong_MouthL"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_MouthR", "x": 11.85, "y": 19.24, "translateMix": -0.05, "order": 21, "target": "3D_B", "shearMix": 0, "bones": ["Tong_MouthR"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_Nose", "x": 1.02, "y": -33.49, "translateMix": 0.08, "order": 6, "target": "3D_T", "shearMix": 0, "bones": ["Bread_Mouth11"]}, {"scaleMix": 0, "rotation": -15.94, "rotateMix": 0, "name": "3D_Rau1", "x": 4.7, "y": -38.43, "translateMix": 0.13, "order": 17, "target": "3D_T", "shearMix": 0, "bones": ["Beard71"]}, {"scaleMix": 0, "rotation": -160.27, "rotateMix": 0, "name": "3D_Rau2", "x": -6.71, "y": -37.44, "translateMix": 0.13, "order": 18, "target": "3D_T", "shearMix": 0, "bones": ["Beard2"]}, {"scaleMix": 0, "rotation": -85.76, "rotateMix": 0, "name": "3D_RauB1", "x": -12, "y": 5.58, "translateMix": -0.12, "order": 11, "target": "3D_B", "shearMix": 0, "bones": ["Bread_Mouth3"]}, {"scaleMix": 0, "rotation": -89.2, "rotateMix": 0, "name": "3D_RauB2", "x": -2.67, "y": 1, "translateMix": -0.15, "order": 12, "target": "3D_B", "shearMix": 0, "bones": ["Bread_Mouth15"]}, {"scaleMix": 0, "rotation": -84.91, "rotateMix": 0, "target": "3D_B", "shearMix": 0, "scaleX": -2, "name": "3D_RauB3", "x": 2.65, "y": 1, "translateMix": -0.15, "shearY": -180, "order": 13, "bones": ["Bread_Mouth10"]}, {"scaleMix": 0, "rotation": -86.07, "rotateMix": 0, "target": "3D_B", "shearMix": 0, "scaleX": -2, "name": "3D_RauB4", "x": 10.06, "y": 5.58, "translateMix": -0.12, "shearY": -180, "order": 14, "bones": ["Bread_Mouth14"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_RauL2", "x": -19.48, "y": -29.06, "translateMix": -0.05, "order": 22, "target": "3D_T", "shearMix": 0, "bones": ["Beard15"]}, {"scaleMix": 0, "rotation": 179.05, "rotateMix": 0, "name": "3D_RauR2", "x": 19.27, "y": -29.06, "translateMix": -0.05, "shearY": -360, "order": 23, "target": "3D_T", "shearMix": 0, "bones": ["Beard49"]}, {"scaleMix": 0, "rotation": 165.11, "rotateMix": 0, "name": "3D_RiaL", "x": -18.77, "y": 50.78, "translateMix": -0.1, "shearY": -360, "order": 10, "target": "3D_B", "shearMix": 0, "bones": ["<PERSON>"]}, {"scaleMix": 0, "rotation": 14.2, "rotateMix": 0, "name": "3D_RiaR", "x": 15.13, "y": 50.78, "translateMix": -0.1, "order": 9, "target": "3D_B", "shearMix": 0, "bones": ["Beard74"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_Rubi", "x": -0.09, "y": 24.21, "translateMix": -0.08, "order": 19, "target": "3D_B", "shearMix": 0, "bones": ["Rubi"]}, {"scaleMix": 0, "rotateMix": 0, "name": "3D_T", "y": -115, "translateMix": 0.25, "order": 1, "target": "3D", "shearMix": 0, "bones": ["3D_T"]}], "bones": [{"name": "root", "x": -0.54, "y": -38.11}, {"parent": "root", "name": "All", "x": 1, "y": 125.4}, {"scaleX": 1.1, "parent": "All", "scaleY": 1.1, "name": "Dragon"}, {"parent": "Dragon", "color": "abe323ff", "name": "3D", "y": 150}, {"parent": "Dragon", "color": "4a00ffff", "name": "3D_T", "y": 35}, {"parent": "Dragon", "color": "4a00ffff", "name": "3D_B", "y": -50}, {"parent": "Dragon", "color": "ff00c6ff", "rotation": 144.57, "name": "Eyebrow", "length": 10.4, "x": -7.68, "y": 16.74}, {"parent": "Eyebrow", "color": "ff00c6ff", "rotation": -1.16, "name": "Eyebrow2", "length": 13.4, "x": 10.4}, {"parent": "Eyebrow2", "color": "ff00c6ff", "rotation": 13.48, "name": "Eyebrow3", "length": 14.53, "x": 13.4}, {"parent": "Dragon", "name": "Eye", "x": -16.16, "y": 17.72}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -160.27, "name": "Beard2", "length": 12.61, "x": -6.71, "y": -2.44}, {"parent": "Beard2", "color": "ff0000ff", "rotation": 15.58, "name": "Beard3", "length": 15.18, "x": 12.49, "y": 0.04}, {"parent": "Beard3", "color": "ff0000ff", "rotation": -16.31, "name": "Beard4", "length": 16.64, "x": 15.18}, {"parent": "Dragon", "color": "ff4509ff", "rotation": 165.11, "name": "<PERSON>", "length": 10.54, "x": -18.77, "y": 0.78}, {"parent": "<PERSON>", "color": "ff4509ff", "rotation": 29.54, "name": "Beard5", "length": 8.67, "x": 10.54}, {"parent": "Beard5", "color": "ff4509ff", "rotation": 54.01, "name": "Beard6", "length": 8.86, "x": 8.67}, {"parent": "Beard6", "color": "ff4509ff", "rotation": 31.03, "name": "Beard7", "length": 10.73, "x": 8.86}, {"parent": "Beard7", "color": "ff4509ff", "rotation": 2.78, "name": "Beard8", "length": 12.55, "x": 10.73}, {"parent": "Beard8", "color": "ff4509ff", "rotation": -10.1, "name": "Beard9", "length": 12.52, "x": 12.55}, {"parent": "Beard9", "color": "ff4509ff", "rotation": -43.33, "name": "Beard10", "length": 6.49, "x": 12.52}, {"parent": "Beard10", "color": "ff4509ff", "rotation": -65.06, "name": "Beard11", "length": 11.68, "x": 6.49}, {"parent": "Beard11", "color": "ff4509ff", "rotation": -28.17, "name": "Beard12", "length": 13.14, "x": 11.68}, {"parent": "Beard12", "color": "ff4509ff", "rotation": -14.27, "name": "Beard13", "length": 11.35, "x": 13.22, "y": -0.27}, {"parent": "Beard13", "color": "ff4509ff", "rotation": 4.51, "name": "Beard14", "length": 10.53, "x": 11.35}, {"parent": "Dragon", "color": "2bff00ff", "name": "Beard15", "x": -19.48, "y": 5.94}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 137.68, "name": "Beard17", "length": 16.67, "x": 1.17, "y": 17.03}, {"parent": "Beard17", "color": "2bff00ff", "rotation": -26.14, "name": "Beard18", "length": 15.03, "x": 16.67}, {"parent": "Beard18", "color": "2bff00ff", "rotation": -45.57, "name": "Beard19", "length": 16.72, "x": 15.03}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 148.3, "name": "Beard20", "length": 14.71, "x": -1.22, "y": 7.83}, {"parent": "Beard20", "color": "2bff00ff", "rotation": -13.3, "name": "Beard21", "length": 17.17, "x": 14.71}, {"parent": "Beard21", "color": "2bff00ff", "rotation": -33.86, "name": "Beard22", "length": 12.38, "x": 17.17}, {"parent": "Beard22", "color": "2bff00ff", "rotation": -11.14, "name": "Beard23", "length": 12.51, "x": 12.05, "y": -0.69}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 142.85, "name": "Beard24", "length": 15.24, "x": -1.04, "y": -7.44}, {"parent": "Beard24", "color": "2bff00ff", "rotation": 4.68, "name": "Beard25", "length": 14.39, "x": 15.24}, {"parent": "Beard25", "color": "2bff00ff", "rotation": -18.4, "name": "Beard26", "length": 13.99, "x": 14.24, "y": -0.1}, {"parent": "Beard26", "color": "2bff00ff", "rotation": -19.33, "name": "Beard27", "length": 14.67, "x": 13.99}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 178.18, "name": "Beard28", "length": 11.6, "x": -0.48, "y": -5.97}, {"parent": "Beard28", "color": "2bff00ff", "rotation": -23.62, "name": "Beard29", "length": 16.71, "x": 11.6}, {"parent": "Beard29", "color": "2bff00ff", "rotation": -38.26, "name": "Beard30", "length": 17.86, "x": 16.55, "y": 0.33}, {"parent": "Beard15", "color": "2bff00ff", "rotation": -154.11, "name": "Beard31", "length": 13.91, "x": 1.36, "y": -5.6}, {"parent": "Beard31", "color": "2bff00ff", "rotation": -31.26, "name": "Beard32", "length": 15.71, "x": 13.91}, {"parent": "Beard32", "color": "2bff00ff", "rotation": -37.13, "name": "Beard33", "length": 11.98, "x": 15.71}, {"parent": "Beard33", "color": "2bff00ff", "rotation": -6.97, "name": "Beard34", "length": 13.31, "x": 11.98}, {"parent": "Beard15", "color": "2bff00ff", "rotation": 151.11, "name": "Beard35", "length": 12.19, "x": 0.62, "y": -22.9}, {"parent": "Beard35", "color": "2bff00ff", "rotation": 37.11, "name": "Beard36", "length": 15.43, "x": 12.19}, {"parent": "Beard36", "color": "2bff00ff", "rotation": -26.46, "name": "Beard37", "length": 16.47, "x": 15.43}, {"parent": "Dragon", "name": "Rubi", "x": -0.09, "y": -25.79}, {"parent": "Dragon", "color": "0159ffff", "rotation": 142.52, "name": "Beard16", "length": 16.33, "x": -4.41, "y": 28.64}, {"parent": "Beard16", "color": "0159ffff", "rotation": -30.1, "name": "Beard38", "length": 14.72, "x": 16.33}, {"parent": "Beard38", "color": "0159ffff", "rotation": -42.41, "name": "Beard39", "length": 15.17, "x": 14.72}, {"parent": "Dragon", "color": "0159ffff", "rotation": 101.31, "name": "Beard40", "length": 12.12, "x": -3.98, "y": 30.8}, {"parent": "Beard40", "color": "0159ffff", "rotation": 5.99, "name": "Beard41", "length": 13.8, "x": 12.12}, {"parent": "Beard41", "color": "0159ffff", "rotation": -40.5, "name": "Beard42", "length": 14.81, "x": 14.01, "y": -0.06}, {"parent": "Dragon", "name": "Tong_MouthL", "x": -13.42, "y": -30.76}, {"parent": "Tong_MouthL", "color": "ff0000ff", "rotation": -110.71, "name": "Bread_Mouth5", "length": 15.43, "x": -2.45, "y": 18.55}, {"parent": "Bread_Mouth5", "color": "ff0000ff", "rotation": -34.02, "name": "Bread_Mouth6", "length": 8.84, "x": 15.43}, {"parent": "Bread_Mouth6", "color": "ff0000ff", "rotation": -30.16, "name": "Bread_Mouth7", "length": 11.84, "x": 8.84}, {"parent": "Tong_MouthL", "color": "ff0000ff", "rotation": -122.24, "name": "Bread_Mouth4", "length": 13.53, "x": -4.07, "y": 2.29}, {"parent": "Tong_MouthL", "color": "ff0000ff", "rotation": -101.48, "name": "Bread_Mouth", "length": 11.49, "x": -2.45, "y": -0.46}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -85.76, "name": "Bread_Mouth3", "length": 9.53, "x": -12, "y": -44.42}, {"parent": "Dragon", "name": "Tong_MouthR", "x": 11.85, "y": -30.76}, {"scaleX": -1, "parent": "Dragon", "color": "ff0000ff", "rotation": 95.09, "name": "Bread_Mouth10", "length": 12.67, "x": 2.65, "y": -49}, {"parent": "Dragon", "name": "Bread_Mouth11", "x": 1.02, "y": 1.51}, {"parent": "Bread_Mouth11", "rotation": -89.2, "name": "<PERSON>", "x": -9.51, "y": -8.98}, {"parent": "Dragon", "color": "0159ffff", "rotation": 77.95, "name": "Beard43", "length": 12.12, "x": 4.89, "y": 30.58}, {"parent": "Beard43", "color": "0159ffff", "rotation": -9.23, "name": "Beard44", "length": 13.8, "x": 12.12}, {"parent": "Beard44", "color": "0159ffff", "rotation": 45.1, "name": "Beard45", "length": 14.81, "x": 14.01, "y": -0.06}, {"parent": "Dragon", "color": "0159ffff", "rotation": 44.33, "name": "Beard46", "length": 16.33, "x": 6.25, "y": 27.99}, {"parent": "Beard46", "color": "0159ffff", "rotation": 24.79, "name": "Beard47", "length": 14.72, "x": 16.33}, {"parent": "Beard47", "color": "0159ffff", "rotation": 46.24, "name": "Beard48", "length": 15.17, "x": 14.72}, {"parent": "Dragon", "color": "2bff00ff", "rotation": 179.05, "name": "Beard49", "x": 19.27, "y": 5.94}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -157.73, "name": "Beard50", "length": 16.67, "x": 1.81, "y": 23.79}, {"parent": "Beard50", "color": "2bff00ff", "rotation": -30.45, "name": "Beard51", "length": 15.03, "x": 16.67}, {"parent": "Beard51", "color": "2bff00ff", "rotation": 35.45, "name": "Beard52", "length": 16.72, "x": 15.03}, {"parent": "Beard49", "color": "2bff00ff", "rotation": 166.46, "name": "Beard53", "length": 14.71, "x": -1.22, "y": 7.83}, {"parent": "Beard53", "color": "2bff00ff", "rotation": 24, "name": "Beard54", "length": 17.17, "x": 14.71}, {"parent": "Beard54", "color": "2bff00ff", "rotation": 52.02, "name": "Beard55", "length": 12.38, "x": 17.17}, {"parent": "Beard55", "color": "2bff00ff", "rotation": -16.75, "name": "Beard56", "length": 12.51, "x": 12.05, "y": -0.69}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -156.72, "name": "Beard57", "length": 15.24, "x": -0.96, "y": 2.81}, {"parent": "Beard57", "color": "2bff00ff", "rotation": 7.03, "name": "Beard58", "length": 14.39, "x": 15.24}, {"parent": "Beard58", "color": "2bff00ff", "rotation": 32.49, "name": "Beard59", "length": 13.99, "x": 14.24, "y": -0.1}, {"parent": "Beard59", "color": "2bff00ff", "rotation": 5.83, "name": "Beard60", "length": 14.67, "x": 13.99}, {"parent": "Beard49", "color": "2bff00ff", "rotation": 175.52, "name": "Beard61", "length": 11.6, "x": 0.11, "y": 3.54}, {"parent": "Beard61", "color": "2bff00ff", "rotation": 21.28, "name": "Beard62", "length": 16.71, "x": 11.6}, {"parent": "Beard62", "color": "2bff00ff", "rotation": 54.57, "name": "Beard63", "length": 17.86, "x": 16.55, "y": 0.33}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -143.44, "name": "Beard64", "length": 13.91, "x": 1.36, "y": -5.6}, {"parent": "Beard64", "color": "2bff00ff", "rotation": 1.71, "name": "Beard65", "length": 15.71, "x": 13.91}, {"parent": "Beard65", "color": "2bff00ff", "rotation": 26.8, "name": "Beard66", "length": 11.98, "x": 15.71}, {"parent": "Beard66", "color": "2bff00ff", "rotation": 30.98, "name": "Beard67", "length": 13.31, "x": 11.98}, {"parent": "Beard49", "color": "2bff00ff", "rotation": -150.58, "name": "Beard68", "length": 12.19, "x": 0.62, "y": -22.9}, {"parent": "Beard68", "color": "2bff00ff", "rotation": 37.11, "name": "Beard69", "length": 15.43, "x": 12.19}, {"parent": "Beard69", "color": "2bff00ff", "rotation": 54.57, "name": "Beard70", "length": 16.47, "x": 15.43}, {"parent": "Dragon", "name": "Eye2", "x": 19.17, "y": 17.72}, {"parent": "Dragon", "color": "ff00c6ff", "rotation": 37.48, "name": "Eyebrow4", "length": 10.4, "x": 8.32, "y": 17}, {"parent": "Eyebrow4", "color": "ff00c6ff", "rotation": -12.42, "name": "Eyebrow5", "length": 13.4, "x": 10.4}, {"parent": "Eyebrow5", "color": "ff00c6ff", "rotation": -8.71, "name": "Eyebrow6", "length": 14.53, "x": 13.4}, {"parent": "Bread_Mouth11", "rotation": -89.2, "name": "Fang2", "x": 7.66, "y": -8.98}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -15.94, "name": "Beard71", "length": 12.61, "x": 4.7, "y": -3.43}, {"parent": "Beard71", "color": "ff0000ff", "rotation": -12.73, "name": "Beard72", "length": 15.18, "x": 13.06, "y": 0.11}, {"parent": "Beard72", "color": "ff0000ff", "rotation": 12.25, "name": "Beard73", "length": 16.64, "x": 16.32, "y": -0.07}, {"parent": "Tong_MouthR", "color": "ff0000ff", "rotation": -71.8, "name": "Bread_Mouth8", "length": 15.43, "x": 3.01, "y": 18.55}, {"parent": "Bread_Mouth8", "color": "ff0000ff", "rotation": 32.15, "name": "Bread_Mouth9", "length": 8.84, "x": 15.43}, {"parent": "Bread_Mouth9", "color": "ff0000ff", "rotation": 37.63, "name": "Bread_Mouth12", "length": 11.84, "x": 8.84}, {"scaleX": -1, "parent": "Tong_MouthR", "color": "ff0000ff", "rotation": 117.77, "name": "Bread_Mouth13", "length": 13.53, "x": 2.38, "y": 2.68}, {"scaleX": -1, "parent": "Tong_MouthR", "color": "ff0000ff", "rotation": 105.52, "name": "Bread_Mouth2", "length": 11.49, "x": 1.24, "y": -0.46}, {"scaleX": -1, "parent": "Dragon", "color": "ff0000ff", "rotation": 93.93, "name": "Bread_Mouth14", "length": 9.53, "x": 10.06, "y": -44.42}, {"parent": "Dragon", "color": "ff0000ff", "rotation": -89.2, "name": "Bread_Mouth15", "length": 12.67, "x": -2.67, "y": -49}, {"parent": "Dragon", "color": "1a00ffff", "name": "Mouth", "x": -20.1, "y": -10.78}, {"parent": "Dragon", "color": "1a00ffff", "name": "Mouth2", "x": 17.42, "y": -10.14}, {"parent": "Dragon", "color": "00ff0aff", "name": "Mouth_B", "y": -55}, {"parent": "Dragon", "color": "ff4509ff", "rotation": 14.2, "name": "Beard74", "length": 10.54, "x": 15.13, "y": 0.78}, {"parent": "Beard74", "color": "ff4509ff", "rotation": -23.55, "name": "Beard75", "length": 8.67, "x": 10.54}, {"parent": "Beard75", "color": "ff4509ff", "rotation": -55.78, "name": "Beard76", "length": 8.86, "x": 8.67}, {"parent": "Beard76", "color": "ff4509ff", "rotation": -36.39, "name": "Beard77", "length": 10.73, "x": 8.86}, {"parent": "Beard77", "color": "ff4509ff", "rotation": -0.89, "name": "Beard78", "length": 12.55, "x": 10.73}, {"parent": "Beard78", "color": "ff4509ff", "rotation": 7.22, "name": "Beard79", "length": 12.52, "x": 12.55}, {"parent": "Beard79", "color": "ff4509ff", "rotation": 38.43, "name": "Beard80", "length": 6.49, "x": 12.52}, {"parent": "Beard80", "color": "ff4509ff", "rotation": 63.68, "name": "Beard81", "length": 11.68, "x": 6.49}, {"parent": "Beard81", "color": "ff4509ff", "rotation": 40.76, "name": "Beard82", "length": 13.14, "x": 11.68}, {"parent": "Beard82", "color": "ff4509ff", "rotation": 9.64, "name": "Beard83", "length": 11.35, "x": 13.22, "y": -0.27}, {"parent": "Beard83", "color": "ff4509ff", "rotation": 0.08, "name": "Beard84", "length": 10.53, "x": 11.35}, {"parent": "Rubi", "name": "Fx_Rubi", "x": 0.09, "y": 0.29}, {"parent": "Eye", "name": "Fx_Rubi2", "x": -0.16, "y": 0.3}, {"scaleX": -1, "parent": "Eye2", "name": "Fx_Rubi3", "x": 0.55, "y": -0.25}, {"parent": "Rubi", "rotation": 90.66, "name": "Fx_Rubi4", "x": 0.09, "y": 0.29}, {"parent": "root", "rotation": -3.98, "name": "c1", "x": 60.41, "y": 168.65}, {"parent": "root", "name": "c2", "x": -83.41, "y": 150.7}, {"parent": "root", "name": "c3", "x": -5.11, "y": 162.3}, {"parent": "root", "name": "bg1", "x": -0.21, "y": -65.27}, {"parent": "root", "name": "bg2", "x": -0.21, "y": -65.27}, {"parent": "root", "name": "images/v1", "x": -206.06, "y": 74.37}, {"parent": "root", "name": "images/v2", "x": -131.7, "y": 71.51}, {"parent": "root", "name": "images/v3", "x": -163.16, "y": 105.83}, {"parent": "root", "name": "images/v5", "x": 140, "y": 71.51}, {"parent": "root", "name": "images/v6", "x": 170.03, "y": 107.97}, {"parent": "bg1", "name": "images/v4", "x": 209.56, "y": 136.78}, {"parent": "root", "color": "0dff00ff", "name": "images/bg_vang", "x": 0.57, "y": 79.11}, {"parent": "root", "color": "0dff00ff", "name": "images/bg_vang2", "x": 0.57, "y": 79.11}], "animations": {"animation": {"slots": {"frame_set/E3": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E0", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "frame_set/E2", "time": 0.1667}, {"name": null, "time": 0.2333}, {"name": "frame_set/E4", "time": 0.3333}, {"name": null, "time": 0.3667}, {"name": "frame_set/E6", "time": 0.4667}, {"name": null, "time": 0.5}, {"name": "frame_set/E8", "time": 0.6333}, {"name": null, "time": 0.6667}, {"name": "frame_set/E0", "time": 0.7}, {"name": null, "time": 0.7333}, {"name": "frame_set/E2", "time": 0.8333}, {"name": null, "time": 0.8667}, {"name": "frame_set/E4", "time": 1}, {"name": null, "time": 1.0333}, {"name": "frame_set/E6", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "frame_set/E8", "time": 1.2667}, {"name": null, "time": 1.3}, {"name": "frame_set/E0", "time": 1.3667}, {"name": null, "time": 1.4}, {"name": "frame_set/E2", "time": 1.5}, {"name": null, "time": 1.5333}, {"name": "frame_set/E4", "time": 1.6333}, {"name": null, "time": 1.6667}, {"name": "frame_set/E6", "time": 1.8}, {"name": null, "time": 1.8333}, {"name": "frame_set/E8", "time": 1.9333}, {"name": null, "time": 1.9667}]}, "frame_set/E2": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E2", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "frame_set/E4", "time": 0.1667}, {"name": null, "time": 0.2333}, {"name": "frame_set/E6", "time": 0.3333}, {"name": null, "time": 0.3667}, {"name": "frame_set/E8", "time": 0.4667}, {"name": null, "time": 0.5}, {"name": "frame_set/E0", "time": 0.5333}, {"name": null, "time": 0.6}, {"name": "frame_set/E2", "time": 0.7}, {"name": null, "time": 0.7333}, {"name": "frame_set/E4", "time": 0.8333}, {"name": null, "time": 0.8667}, {"name": "frame_set/E6", "time": 1}, {"name": null, "time": 1.0333}, {"name": "frame_set/E8", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "frame_set/E0", "time": 1.2}, {"name": null, "time": 1.2333}, {"name": "frame_set/E2", "time": 1.3667}, {"name": null, "time": 1.4}, {"name": "frame_set/E4", "time": 1.5}, {"name": null, "time": 1.5333}, {"name": "frame_set/E6", "time": 1.6333}, {"name": null, "time": 1.6667}, {"name": "frame_set/E8", "time": 1.8}, {"name": null, "time": 1.8333}, {"name": "frame_set/E0", "time": 1.8667}, {"name": null, "time": 1.9}]}, "frame_set/E1": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E0", "time": 0.0333}, {"name": null, "time": 0.0667}, {"name": "frame_set/E2", "time": 0.1667}, {"name": null, "time": 0.2333}, {"name": "frame_set/E4", "time": 0.3333}, {"name": null, "time": 0.3667}, {"name": "frame_set/E6", "time": 0.4667}, {"name": null, "time": 0.5}, {"name": "frame_set/E8", "time": 0.6333}, {"name": null, "time": 0.6667}, {"name": "frame_set/E0", "time": 0.7}, {"name": null, "time": 0.7333}, {"name": "frame_set/E2", "time": 0.8333}, {"name": null, "time": 0.8667}, {"name": "frame_set/E4", "time": 1}, {"name": null, "time": 1.0333}, {"name": "frame_set/E6", "time": 1.1333}, {"name": null, "time": 1.1667}, {"name": "frame_set/E8", "time": 1.2667}, {"name": null, "time": 1.3}, {"name": "frame_set/E0", "time": 1.3667}, {"name": null, "time": 1.4}, {"name": "frame_set/E2", "time": 1.5}, {"name": null, "time": 1.5333}, {"name": "frame_set/E4", "time": 1.6333}, {"name": null, "time": 1.6667}, {"name": "frame_set/E6", "time": 1.8}, {"name": null, "time": 1.8333}, {"name": "frame_set/E8", "time": 1.9333}, {"name": null, "time": 1.9667}]}, "frame_set/E0": {"color": [{"c3": 0.75, "color": "ffffff00", "curve": 0.25}, {"color": "ffffffff", "curve": "stepped", "time": 0.1333}, {"c3": 0.75, "color": "ffffffff", "curve": 0.25, "time": 1.8}, {"color": "ffffff00", "time": 2}], "attachment": [{"name": "frame_set/E0", "time": 0.0667}, {"name": null, "time": 0.1}, {"name": "frame_set/E2", "time": 0.2333}, {"name": null, "time": 0.2667}, {"name": "frame_set/E4", "time": 0.3667}, {"name": null, "time": 0.4}, {"name": "frame_set/E6", "time": 0.5}, {"name": null, "time": 0.5333}, {"name": "frame_set/E8", "time": 0.6667}, {"name": null, "time": 0.7}, {"name": "frame_set/E0", "time": 0.7333}, {"name": null, "time": 0.7667}, {"name": "frame_set/E2", "time": 0.8667}, {"name": null, "time": 0.9}, {"name": "frame_set/E4", "time": 1.0333}, {"name": null, "time": 1.0667}, {"name": "frame_set/E6", "time": 1.1667}, {"name": null, "time": 1.2}, {"name": "frame_set/E8", "time": 1.3}, {"name": null, "time": 1.3667}, {"name": "frame_set/E0", "time": 1.4}, {"name": null, "time": 1.4333}, {"name": "frame_set/E2", "time": 1.5333}, {"name": null, "time": 1.5667}, {"name": "frame_set/E4", "time": 1.6667}, {"name": null, "time": 1.7}, {"name": "frame_set/E6", "time": 1.8333}, {"name": null, "time": 1.8667}, {"name": "frame_set/E8", "time": 1.9667}, {"name": null, "time": 2}]}, "images/bg_vang2": {"color": [{"color": "ffffff00"}, {"color": "ffffff7b", "time": 0.6667}, {"color": "ffffff00", "time": 0.8333}, {"color": "ffffffd2", "time": 1.1667}]}, "bg2": {"color": [{"c3": 0.742, "color": "ffffff1b", "curve": 0.381, "c2": 0.55}, {"c3": 0.75, "color": "ffffff00", "curve": 0.25, "time": 0.2}, {"c3": 0.75, "color": "ffffff4b", "curve": 0.25, "time": 0.7}, {"c3": 0.75, "color": "ffffff00", "curve": 0.25, "time": 1.2}, {"c3": 0.637, "c4": 0.56, "color": "ffffff4b", "curve": 0.245, "time": 1.7}, {"color": "ffffff1b", "time": 2}]}, "c1": {"color": [{"color": "ffffffff", "time": 0.6}, {"color": "ffffff00", "curve": "stepped", "time": 0.7667}, {"color": "ffffff00", "time": 0.8}, {"color": "ffffffff", "time": 0.9667}]}, "c2": {"color": [{"color": "ffffff00"}, {"color": "ffffffff", "curve": "stepped", "time": 0.1667}, {"color": "ffffffff", "time": 1.8}, {"color": "ffffff00", "time": 1.9667}]}}, "bones": {"Beard20": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.06, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -8.23, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.75, "time": 1.2}, {"angle": -7.06, "time": 2}]}, "Beard21": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.72, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -8.77, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 4.96, "time": 1.4}, {"angle": -3.72, "time": 2}]}, "Eye": {"rotate": [{"c3": 0.75, "curve": 0.25, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "angle": -18.12, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "angle": -15.3, "time": 1.6333}, {"time": 2}], "scale": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "x": 1.2, "y": 1.2, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.85, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.637, "time": 1.6333}, {"time": 2}], "translate": [{"c3": 0.75, "curve": 0.25, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "x": 0.54, "y": -1.99, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": -0.91, "y": -2.49, "time": 1.6333}, {"time": 2}]}, "Beard22": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -9.43, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -13.91, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": -6.83, "time": 1.6}, {"angle": -9.43, "time": 2}]}, "Bread_Mouth9": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 5.69, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 6.88, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -2.3, "time": 1.2}, {"angle": 5.69, "time": 2}]}, "Beard23": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 15.16, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -7.39, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 18.53, "time": 1.8}, {"angle": 15.16, "time": 2}]}, "Bread_Mouth8": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 9.64}, {"c3": 0.75, "curve": 0.25, "angle": 3.4, "time": 1}, {"angle": 9.64, "time": 2}]}, "Beard24": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.82, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -5.62, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.53, "time": 1.2}, {"angle": -4.82, "time": 2}]}, "Beard25": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.66, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -5.43, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 2.1, "time": 1.4}, {"angle": -2.66, "time": 2}]}, "Beard26": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -1.62, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -8.54, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 2.42, "time": 1.6}, {"angle": -1.62, "time": 2}]}, "Beard27": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 2.46, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -19.59, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 5.75, "time": 1.8}, {"angle": 2.46, "time": 2}]}, "Beard28": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.19, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.98, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 1.11, "time": 1.2}, {"angle": -4.19, "time": 2}]}, "Beard29": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.9, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.67, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 2.58, "time": 1.4}, {"angle": -3.9, "time": 2}]}, "Bread_Mouth2": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 9.47}, {"c3": 0.75, "curve": 0.25, "angle": 2.51, "time": 1}, {"angle": 9.47, "time": 2}]}, "Bread_Mouth5": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -6.33}, {"c3": 0.75, "curve": 0.25, "angle": 0.57, "time": 1}, {"angle": -6.33, "time": 2}]}, "Bread_Mouth4": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -5.27}, {"c3": 0.75, "curve": 0.25, "angle": 3.39, "time": 1}, {"angle": -5.27, "time": 2}]}, "Bread_Mouth7": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.62, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -6.62, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 4.27, "time": 1.4}, {"angle": -2.62, "time": 2}]}, "Bread_Mouth6": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.52, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -5.62, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.88, "time": 1.2}, {"angle": -4.52, "time": 2}]}, "Bread_Mouth12": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 4.85, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 10.45, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -4.77, "time": 1.4}, {"angle": 4.85, "time": 2}]}, "Beard30": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": 2.42, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -6.67, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 7.71, "time": 1.6}, {"angle": 2.42, "time": 2}]}, "c1": {"rotate": [{"angle": 142.76}, {"curve": "stepped", "time": 0.7667}, {"time": 0.8}, {"angle": 180, "time": 1.8}, {"angle": 142.76, "time": 2}], "translate": [{"y": 36.78}, {"y": -50.71, "time": 0.7667}, {"y": 173.71, "time": 0.8}, {"y": 36.78, "time": 2}]}, "Beard31": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -3.5, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.1, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.52, "time": 1.2}, {"angle": -3.5, "time": 2}]}, "c2": {"rotate": [{}, {"angle": 180, "time": 1}, {"time": 1.9667}], "translate": [{"y": 173.71}, {"y": -50.71, "time": 1.9667}, {"y": 173.71, "time": 2}]}, "Beard32": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.99, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -6.06, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -0.43, "time": 1.4}, {"angle": -3.99, "time": 2}]}, "Bread_Mouth13": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 13.37}, {"c3": 0.75, "curve": 0.25, "angle": 5.58, "time": 1}, {"angle": 13.37, "time": 2}]}, "Beard33": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -0.99, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -5.66, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 1.72, "time": 1.6}, {"angle": -0.99, "time": 2}]}, "Beard34": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 4.26, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -7.48, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 6.02, "time": 1.8}, {"angle": 4.26, "time": 2}]}, "Beard35": {"rotate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "angle": -0.15, "time": 1}, {"angle": -1.98, "time": 2}]}, "Beard36": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -3.3, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.17, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.5, "time": 1.2}, {"angle": -3.3, "time": 2}]}, "Beard37": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -6.23, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -9.62, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -0.39, "time": 1.4}, {"angle": -6.23, "time": 2}]}, "Beard38": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.78, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -9.4, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 3.05, "time": 1.2}, {"angle": -7.78, "time": 2}]}, "Beard39": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.81, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -11.47, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 9.35, "time": 1.4}, {"angle": -3.81, "time": 2}]}, "Beard80": {"rotate": [{"c3": 0.71, "curve": 0.371, "angle": -4.89, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -6.22, "time": 0.1667}, {"c3": 0.689, "c4": 0.75, "curve": 0.243, "angle": 7.51, "time": 1.1667}, {"angle": -4.89, "time": 2}]}, "Beard81": {"rotate": [{"c3": 0.735, "curve": 0.382, "angle": -3.99, "c2": 0.57}, {"c3": 0.75, "curve": 0.25, "angle": -7.71, "time": 0.3333}, {"c3": 0.649, "c4": 0.6, "curve": 0.243, "angle": 5.38, "time": 1.3333}, {"angle": -3.99, "time": 2}]}, "Beard82": {"rotate": [{"c3": 0.757, "curve": 0.351, "angle": 2.9, "c2": 0.4}, {"c3": 0.75, "curve": 0.25, "angle": -5.62, "time": 0.6667}, {"c3": 0.618, "c4": 0.43, "curve": 0.265, "angle": 6.28, "time": 1.6667}, {"angle": 2.9, "time": 2}]}, "Beard83": {"rotate": [{"c3": 0.758, "curve": 0.329, "angle": 9.38, "c2": 0.32}, {"c3": 0.75, "curve": 0.25, "angle": -0.67, "time": 0.7667}, {"c3": 0.622, "c4": 0.39, "curve": 0.278, "angle": 11.38, "time": 1.7667}, {"angle": 9.38, "time": 2}]}, "Beard84": {"rotate": [{"c3": 0.755, "curve": 0.289, "angle": 19.69, "c2": 0.17}, {"c3": 0.75, "curve": 0.25, "angle": 11.83, "time": 0.9}, {"c3": 0.64, "c4": 0.36, "curve": 0.305, "angle": 20.04, "time": 1.9}, {"angle": 19.69, "time": 2}]}, "Eye2": {"rotate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "angle": 11.7, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "angle": 14.38, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "angle": 18.42, "time": 1.6333}, {"time": 2}], "scale": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "x": 1.2, "y": 1.2, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.85, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": 0.85, "y": 0.709, "time": 1.6333}, {"time": 2}], "translate": [{"c3": 0.75, "curve": 0.25, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "y": -1.63, "time": 0.7333}, {"c3": 0.75, "curve": 0.25, "x": -2.75, "y": -1.63, "time": 1.6333}, {"time": 2}]}, "Beard10": {"rotate": [{"c3": 0.752, "curve": 0.264, "angle": 3.77, "c2": 0.06}, {"c3": 0.75, "curve": 0.25, "angle": -6, "time": 0.9667}, {"c3": 0.656, "c4": 0.34, "curve": 0.323, "angle": 3.89, "time": 1.9667}, {"angle": 3.77, "time": 2}]}, "Beard11": {"rotate": [{"c3": 0.722, "curve": 0.378, "angle": 7.24, "c2": 0.61}, {"c3": 0.75, "curve": 0.25, "angle": 9.28, "time": 0.2333}, {"c3": 0.671, "c4": 0.68, "curve": 0.242, "angle": -2.98, "time": 1.2333}, {"angle": 7.24, "time": 2}]}, "3D": {"translate": [{"c4": 0.8, "curve": 0.25}, {"c3": 0.75, "curve": 0, "y": 108.4, "time": 0.3667, "c2": 0.13}, {"c3": 0.75, "curve": 0, "x": -216.88, "time": 0.7333, "c2": 0.13}, {"c3": 0.75, "curve": 0.25, "x": 264.84, "time": 1.6333}, {"time": 2}]}, "Beard12": {"rotate": [{"c3": 0.739, "curve": 0.382, "angle": 5.36, "c2": 0.56}, {"c3": 0.75, "curve": 0.25, "angle": 8.9, "time": 0.3667}, {"c3": 0.643, "c4": 0.58, "curve": 0.244, "angle": -1.98, "time": 1.3667}, {"angle": 5.36, "time": 2}]}, "Beard13": {"rotate": [{"c3": 0.752, "curve": 0.372, "angle": -9.58, "c2": 0.48}, {"c3": 0.75, "curve": 0.25, "angle": -5.11, "time": 0.5333}, {"c3": 0.622, "c4": 0.48, "curve": 0.252, "angle": -13.33, "time": 1.5333}, {"angle": -9.58, "time": 2}]}, "Beard14": {"rotate": [{"c3": 0.758, "curve": 0.329, "angle": -23.37, "c2": 0.32}, {"c3": 0.75, "curve": 0.25, "angle": -36.42, "time": 0.7667}, {"c3": 0.622, "c4": 0.39, "curve": 0.278, "angle": -20.77, "time": 1.7667}, {"angle": -23.37, "time": 2}]}, "Beard8": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -2.01, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -14.35, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 5.18, "time": 1.6}, {"angle": -2.01, "time": 2}]}, "Beard9": {"rotate": [{"c3": 0.758, "curve": 0.329, "angle": 0.78, "c2": 0.32}, {"c3": 0.75, "curve": 0.25, "angle": -7.64, "time": 0.7667}, {"c3": 0.622, "c4": 0.39, "curve": 0.278, "angle": 2.46, "time": 1.7667}, {"angle": 0.78, "time": 2}]}, "Beard16": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -4.6}, {"c3": 0.75, "curve": 0.25, "angle": 0.21, "time": 1}, {"angle": -4.6, "time": 2}]}, "Beard17": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -8.32}, {"c3": 0.75, "curve": 0.25, "angle": 4.94, "time": 1}, {"angle": -8.32, "time": 2}]}, "Beard18": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -11.38, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -14.82, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 11.64, "time": 1.2}, {"angle": -11.38, "time": 2}]}, "Beard19": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -0.93, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -15.8, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 24.64, "time": 1.4}, {"angle": -0.93, "time": 2}]}, "Beard4": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.56, "c2": 0.55}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.68, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -8.13, "time": 1.4}, {"angle": -2.56, "time": 2}]}, "Beard5": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -21.12}, {"c3": 0.75, "curve": 0.25, "angle": -1.17, "time": 1}, {"angle": -21.12, "time": 2}]}, "Beard6": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -9.92, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -11.71, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.07, "time": 1.2}, {"angle": -9.92, "time": 2}]}, "Beard7": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -5.78, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -13.44, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 7.39, "time": 1.4}, {"angle": -5.78, "time": 2}]}, "Beard2": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -4.24}, {"c3": 0.75, "curve": 0.25, "angle": 2.69, "time": 1}, {"angle": -4.24, "time": 2}]}, "Beard3": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -2.68, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -3.7, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 4.15, "time": 1.2}, {"angle": -2.68, "time": 2}]}, "Beard60": {"rotate": [{"c3": 0.752, "curve": 0.372, "angle": -7.94, "c2": 0.48}, {"c3": 0.75, "curve": 0.25, "angle": -16.77, "time": 0.5333}, {"c3": 0.622, "c4": 0.48, "curve": 0.252, "angle": -0.53, "time": 1.5333}, {"angle": -7.94, "time": 2}]}, "Beard61": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -4.24, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -5.36, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 3.22, "time": 1.2}, {"angle": -4.24, "time": 2}]}, "Beard62": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.61, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.42, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 5.64, "time": 1.4}, {"angle": -2.61, "time": 2}]}, "Eyebrow": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -5.85}, {"c3": 0.75, "curve": 0.25, "angle": 1.15, "time": 1}, {"angle": -5.85, "time": 2}]}, "Beard63": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -3.82, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.78, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 0.81, "time": 1.6}, {"angle": -3.82, "time": 2}]}, "Beard64": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -2.81, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -4.56, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 8.89, "time": 1.2}, {"angle": -2.81, "time": 2}]}, "Beard65": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.49, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -8.27, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 4.72, "time": 1.4}, {"angle": -3.49, "time": 2}]}, "Beard66": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -3.78, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.03, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 0.44, "time": 1.6}, {"angle": -3.78, "time": 2}]}, "Beard67": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": -2.52, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -12.08, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": -1.09, "time": 1.8}, {"angle": -2.52, "time": 2}]}, "Beard68": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -10.22}, {"c3": 0.75, "curve": 0.25, "angle": -1.98, "time": 1}, {"angle": -10.22, "time": 2}]}, "Beard69": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -9.77, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -11.12, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -0.78, "time": 1.2}, {"angle": -9.77, "time": 2}]}, "Tong_MouthL": {"scale": [{"x": 0.992, "y": 0.992, "time": 0.3667}]}, "Fx_Rubi3": {"rotate": [{"angle": 19.44}], "scale": [{"x": 0.48, "y": 0.349}], "translate": [{"x": 1.42, "y": -0.47}]}, "Fx_Rubi4": {"rotate": [{"angle": -15.1}]}, "Beard70": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -24.5, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -37.97, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -1.37, "time": 1.4}, {"angle": -24.5, "time": 2}]}, "Beard71": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 5.83}, {"c3": 0.75, "curve": 0.25, "angle": 0.24, "time": 1}, {"angle": 5.83, "time": 2}]}, "Beard72": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 6.43, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 7.67, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -1.85, "time": 1.2}, {"angle": 6.43, "time": 2}]}, "Beard73": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 4.24, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 9.31, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -4.47, "time": 1.4}, {"angle": 4.24, "time": 2}]}, "Beard74": {"rotate": [{"angle": 18.98}, {"angle": 1.19, "time": 1}, {"angle": 18.98, "time": 2}]}, "Beard75": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 6.76, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 7.92, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -1.01, "time": 1.2}, {"angle": 6.76, "time": 2}]}, "Fx_Rubi2": {"rotate": [{"angle": 146.76}], "scale": [{"x": 0.48, "y": 0.349}], "translate": [{"x": -2.4, "y": -0.66}]}, "Beard76": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 5.32, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 11.7, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -5.64, "time": 1.4}, {"angle": 5.32, "time": 2}]}, "Beard77": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": 8.38, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": 13.35, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 5.49, "time": 1.6}, {"angle": 8.38, "time": 2}]}, "Beard78": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": -4.36, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": 9.81, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": -6.48, "time": 1.8}, {"angle": -4.36, "time": 2}]}, "Beard79": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 0.56}, {"c3": 0.75, "curve": 0.25, "angle": 12.87, "time": 1}, {"angle": 0.56, "time": 2}]}, "images/v4": {"scale": [{"c3": 0.755, "curve": 0.363, "x": 1.095, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "x": 1.15, "time": 1.6}, {"x": 1.095, "time": 2}], "translate": [{"c3": 0.755, "curve": 0.363, "x": 1.81, "y": 2.26, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "x": 2.86, "y": 3.58, "time": 1.6}, {"x": 1.81, "y": 2.26, "time": 2}]}, "Beard": {"rotate": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "angle": -4.86, "time": 0.3667}, {"c3": 0.75, "curve": 0.25, "angle": 1.02, "time": 0.7}, {"c3": 0.75, "curve": 0.25, "angle": -3.25, "time": 1}, {"c3": 0.75, "curve": 0.25, "angle": 1.8, "time": 1.3}, {"c3": 0.75, "curve": 0.25, "angle": -4.25, "time": 1.6333}, {"time": 2}]}, "images/v3": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -5.47, "c2": 0.62}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -9.17, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 19.21, "time": 1.2}, {"angle": -5.47, "time": 2}], "translate": [{"c3": 0.716, "curve": 0.375, "y": -6.44, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "y": -8.58, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "y": 7.86, "time": 1.2}, {"y": -6.44, "time": 2}]}, "images/v6": {"scale": [{"c3": 0.742, "curve": 0.381, "x": 1.058, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "x": 1.157, "time": 1.4}, {"x": 1.058, "time": 2}], "translate": [{"c3": 0.742, "curve": 0.381, "x": 1.17, "y": 4.52, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "x": 1.43, "y": 7.15, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "x": 0.71, "time": 1.4}, {"x": 1.17, "y": 4.52, "time": 2}]}, "images/v5": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 21.74, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 26.51, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -10.13, "time": 1.2}, {"angle": 21.74, "time": 2}], "scale": [{"c3": 0.716, "curve": 0.375, "y": 1.016, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "y": 1.126, "time": 1.2}, {"y": 1.016, "time": 2}], "translate": [{"c3": 0.716, "curve": 0.375, "y": 0.93, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "y": 7.15, "time": 1.2}, {"y": 0.93, "time": 2}]}, "images/v2": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -10.15, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -25.51, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 16.24, "time": 1.4}, {"angle": -10.15, "time": 2}]}, "images/v1": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 14.76}, {"c3": 0.75, "curve": 0.25, "angle": -15.63, "time": 1}, {"angle": 14.76, "time": 2}], "scale": [{"c3": 0.75, "curve": 0.25}, {"c3": 0.75, "curve": 0.25, "x": 0.805, "y": 0.805, "time": 1}, {"time": 2}]}, "Beard40": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -7.8}, {"c3": 0.75, "curve": 0.25, "angle": 1.46, "time": 1}, {"angle": -7.8, "time": 2}]}, "Beard41": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.1, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -8.67, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 3.38, "time": 1.2}, {"angle": -7.1, "time": 2}]}, "Beard42": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.54, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -9.56, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 6.8, "time": 1.4}, {"angle": -3.54, "time": 2}]}, "Beard43": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 7.21}, {"c3": 0.75, "curve": 0.25, "angle": 0.11, "time": 1}, {"angle": 7.21, "time": 2}]}, "Beard44": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -13.13, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -15.83, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 4.87, "time": 1.2}, {"angle": -13.13, "time": 2}]}, "Beard45": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -3.65, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -10.48, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 8.07, "time": 1.4}, {"angle": -3.65, "time": 2}]}, "Beard46": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -7.57}, {"c3": 0.75, "curve": 0.25, "angle": 4.14, "time": 1}, {"angle": -7.57, "time": 2}]}, "Beard47": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -7.32, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -9.29, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 5.89, "time": 1.2}, {"angle": -7.32, "time": 2}]}, "Beard48": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -7.2, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -12.45, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 1.82, "time": 1.4}, {"angle": -7.2, "time": 2}]}, "Beard50": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.14, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.14, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 0.49, "time": 1.2}, {"angle": -6.14, "time": 2}]}, "Beard51": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -1.48, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -5.71, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 5.81, "time": 1.4}, {"angle": -1.48, "time": 2}]}, "Beard52": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -1.38, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.38, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 4.44, "time": 1.6}, {"angle": -1.38, "time": 2}]}, "Beard53": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.29, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.59, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.43, "time": 1.2}, {"angle": -6.29, "time": 2}]}, "Beard54": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -4.61, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.5, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 0.37, "time": 1.4}, {"angle": -4.61, "time": 2}]}, "Beard55": {"rotate": [{"c3": 0.755, "curve": 0.363, "angle": -1.29, "c2": 0.44}, {"c3": 0.75, "curve": 0.25, "angle": -11.86, "time": 0.6}, {"c3": 0.619, "c4": 0.45, "curve": 0.258, "angle": 4.86, "time": 1.6}, {"angle": -1.29, "time": 2}]}, "Beard56": {"rotate": [{"c3": 0.757, "curve": 0.32, "angle": 14.51, "c2": 0.29}, {"c3": 0.75, "curve": 0.25, "angle": -19.39, "time": 0.8}, {"c3": 0.625, "c4": 0.38, "curve": 0.284, "angle": 19.58, "time": 1.8}, {"angle": 14.51, "time": 2}]}, "Beard57": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.42, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.53, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 1.04, "time": 1.2}, {"angle": -6.42, "time": 2}]}, "Beard58": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -2.17, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -7.68, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 7.31, "time": 1.4}, {"angle": -2.17, "time": 2}]}, "Beard59": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -9.25}, {"c3": 0.75, "curve": 0.25, "angle": 6.6, "time": 1}, {"angle": -7.68, "time": 2}]}, "Bread_Mouth": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": -6.3}, {"c3": 0.75, "curve": 0.25, "angle": -0.23, "time": 1}, {"angle": -6.3, "time": 2}]}, "Eyebrow6": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": 4.53, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": 9.93, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": -4.75, "time": 1.4}, {"angle": 4.53, "time": 2}]}, "Eyebrow5": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": 5.51, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": 6.7, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": -2.5, "time": 1.2}, {"angle": 5.51, "time": 2}]}, "Eyebrow4": {"rotate": [{"c3": 0.75, "curve": 0.25, "angle": 6.4}, {"c3": 0.75, "curve": 0.25, "angle": -1.14, "time": 1}, {"angle": 6.4, "time": 2}]}, "Eyebrow3": {"rotate": [{"c3": 0.742, "curve": 0.381, "angle": -4.69, "c2": 0.55}, {"c3": 0.75, "curve": 0.25, "angle": -10.41, "time": 0.4}, {"c3": 0.637, "c4": 0.56, "curve": 0.245, "angle": 5.14, "time": 1.4}, {"angle": -4.69, "time": 2}]}, "Eyebrow2": {"rotate": [{"c3": 0.716, "curve": 0.375, "angle": -6.17, "c2": 0.62}, {"c3": 0.75, "curve": 0.25, "angle": -7.41, "time": 0.2}, {"c3": 0.68, "c4": 0.71, "curve": 0.243, "angle": 2.15, "time": 1.2}, {"angle": -6.17, "time": 2}]}}}}}, [0]]], 0, 0, [0], [-1], [36]], [[{"name": "x", "rect": [0, 0, 65, 65], "offset": [0, 0], "originalSize": [65, 65], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [37]], [[{"name": "nan", "rect": [1, 0, 64, 64], "offset": [0.5, 0], "originalSize": [65, 64], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [38]], [[[17, "TXspineblue", "\nTXspineblue.png\nsize: 964,591\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nas\n  rotate: false\n  xy: 2, 14\n  size: 570, 575\n  orig: 573, 577\n  offset: 2, 1\n  index: -1\nas copy\n  rotate: false\n  xy: 797, 133\n  size: 154, 214\n  orig: 155, 214\n  offset: 0, 0\n  index: -1\nparticle\n  rotate: false\n  xy: 797, 2\n  size: 126, 129\n  orig: 152, 152\n  offset: 13, 15\n  index: -1\ntai\n  rotate: true\n  xy: 574, 18\n  size: 329, 221\n  orig: 331, 223\n  offset: 1, 1\n  index: -1\nxiu\n  rotate: false\n  xy: 574, 349\n  size: 388, 240\n  orig: 390, 242\n  offset: 1, 1\n  index: -1\n", ["TXspineblue.png"], {"skeleton": {"hash": "9eTLZP0TARmNiJxIM5/qm4gpMds", "spine": "3.8.75", "x": -297.81, "y": -271.94, "width": 573, "height": 606.49, "images": "C:/Users/<USER>/UI GAME/out/play.hitclub.win/spines/12_TXspine2", "audio": "/Users/<USER>/Duc/Sgame_Web"}, "bones": [{"name": "root"}, {"name": "tai", "parent": "root", "length": 100, "rotation": 41.69, "x": -7.23, "y": -56.59}, {"name": "as", "parent": "root", "x": -7.23, "y": 13.38}, {"name": "star", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "as copy", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "as copy2", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49, "scaleX": 0.597, "scaleY": 0.881}, {"name": "as copy3", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49, "scaleX": 0.816, "scaleY": 0.839}, {"name": "as copy5", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "as copy4", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49, "scaleX": 0.816, "scaleY": 0.839}, {"name": "as copy6", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "as copy7", "parent": "root", "length": 165.42, "rotation": 59.61, "x": 61.37, "y": 135.49}, {"name": "tai2", "parent": "tai", "x": 31.46, "y": 63.09}, {"name": "star2", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star3", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star4", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star5", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star6", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star7", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star8", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star9", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star10", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star11", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}, {"name": "star12", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star13", "parent": "root", "x": -6.25, "y": 19.69}, {"name": "star14", "parent": "root", "x": -6.25, "y": 19.69, "scaleX": 0.68, "scaleY": 0.68}], "slots": [{"name": "as", "bone": "as", "attachment": "as"}, {"name": "particle", "bone": "root"}, {"name": "as copy", "bone": "as copy", "attachment": "as copy"}, {"name": "as copy7", "bone": "as copy7", "attachment": "as copy"}, {"name": "as copy6", "bone": "as copy6", "attachment": "as copy"}, {"name": "as copy2", "bone": "as copy2", "attachment": "as copy"}, {"name": "as copy3", "bone": "as copy3", "attachment": "as copy"}, {"name": "as copy4", "bone": "as copy4", "attachment": "as copy"}, {"name": "as copy5", "bone": "as copy5", "attachment": "as copy"}, {"name": "star", "bone": "star", "attachment": "particle"}, {"name": "star2", "bone": "star2", "attachment": "particle"}, {"name": "star3", "bone": "star3", "attachment": "particle"}, {"name": "star4", "bone": "star4", "attachment": "particle"}, {"name": "star5", "bone": "star5", "attachment": "particle"}, {"name": "star6", "bone": "star6", "attachment": "particle"}, {"name": "star7", "bone": "star7", "attachment": "particle"}, {"name": "star8", "bone": "star8", "attachment": "particle"}, {"name": "star9", "bone": "star9", "attachment": "particle"}, {"name": "star10", "bone": "star10", "attachment": "particle"}, {"name": "star11", "bone": "star11", "attachment": "particle"}, {"name": "star12", "bone": "star12", "attachment": "particle"}, {"name": "star13", "bone": "star13", "attachment": "particle"}, {"name": "star14", "bone": "star14", "attachment": "particle"}, {"name": "tai", "bone": "tai"}, {"name": "tai2", "bone": "tai2"}, {"name": "<PERSON><PERSON>", "bone": "root"}], "skins": [{"name": "default", "attachments": {"star4": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star13": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "tai2": {"tai": {"x": 9.96, "y": 30.12, "rotation": -41.69, "width": 331, "height": 223}, "xiu": {"x": 32.74, "y": 15.86, "rotation": -41.69, "width": 390, "height": 242}}, "tai": {"tai": {"x": 41.42, "y": 93.22, "rotation": -41.69, "width": 331, "height": 223}, "xiu": {"x": 64.2, "y": 78.95, "rotation": -41.69, "width": 390, "height": 242}}, "Xiu": {"xiu2": {"path": "xiu", "x": 630.51, "y": 893.81, "width": 390, "height": 242}}, "star2": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star3": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star12": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "as copy": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "star6": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star7": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star8": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star9": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star10": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "as copy2": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy3": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy4": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as": {"as": {"x": -4.08, "y": 3.18, "width": 573, "height": 577}}, "as copy6": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "as copy7": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "star14": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "star5": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}, "as copy5": {"as copy": {"x": 111.98, "y": -8.97, "rotation": -59.61, "width": 155, "height": 214}}, "star11": {"particle": {"x": -0.77, "y": 2.65, "width": 152, "height": 152}}}}], "animations": {"tai": {"slots": {"star11": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star3": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "tai": {"attachment": [{"name": "tai"}]}, "star": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "star14": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "star4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "star6": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "as copy7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffff00"}]}, "star8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "as copy5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "star5": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "tai2": {"color": [{"color": "ffffff56"}, {"time": 0.4, "color": "ffffff2a"}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"name": "tai"}]}, "star10": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star13": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star9": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "as copy3": {"color": [{"color": "ffffff8d", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff8d"}]}, "as copy6": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffff00"}]}, "as copy": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "as copy2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffff00"}]}, "as copy4": {"color": [{"color": "ffffff8d", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff8d"}]}, "star2": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star7": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}}, "bones": {"as copy": {"translate": [{"x": -18.99, "y": -32.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.831}]}, "as copy2": {"rotate": [{"angle": 47.89}], "translate": [{"x": -107.94, "y": -34.49, "curve": "stepped"}, {"time": 0.2667, "x": -107.94, "y": -34.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -107.94, "y": -34.49}], "scale": [{"x": 1.132, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.132}]}, "as copy3": {"rotate": [{"angle": -43.88}], "translate": [{"x": -22.91, "y": -106.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"x": 1.226, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.831, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.226}]}, "as copy4": {"rotate": [{"angle": -130.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -43.88}], "translate": [{"x": -38.63, "y": -211.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"x": 1.6, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.831, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.226}]}, "as copy5": {"translate": [{"x": -18.99, "y": -32.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.831}]}, "as copy6": {"rotate": [{"angle": 107.06}], "translate": [{"x": -114.3, "y": -110.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -126.07, "y": -109.54, "curve": "stepped"}, {"time": 0.3333, "x": -126.07, "y": -109.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -114.3, "y": -110.55}], "scale": [{"x": 1.347, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 1.528, "curve": "stepped"}, {"time": 0.3333, "x": 1.528, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.347}]}, "as copy7": {"rotate": [{"angle": 161.64}], "translate": [{"x": -73.04, "y": -123.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -96.36, "y": -157.36, "curve": "stepped"}, {"time": 0.5, "x": -96.36, "y": -157.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -73.04, "y": -123.14}], "scale": [{"x": 0.863, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.831, "curve": "stepped"}, {"time": 0.5, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.863}]}, "star": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": -416.48, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star2": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": -202.68, "y": 139.77}, {"time": 0.1333, "x": -304.03, "y": 209.66, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": -202.68, "y": 139.77}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star3": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": -233.18, "y": -134.69}, {"time": 0.1333, "x": -349.77, "y": -202.04, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": -233.18, "y": -134.69}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star4": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": -284.97, "y": -264.93, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": -284.97, "y": -264.93}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star5": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": -67.68, "y": -289.71, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star6": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": -149.32, "y": 207.12}, {"time": 0.1333, "x": -223.97, "y": 310.68, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": -149.32, "y": 207.12}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star7": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": -90.55, "y": 318.3, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star8": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": 170.57, "y": -270.65, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 170.57, "y": -270.65}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star9": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": 210.28, "y": -181.71}, {"time": 0.1333, "x": 315.42, "y": -272.56, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 210.28, "y": -181.71}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star10": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": 228.07, "y": 171.54}, {"time": 0.1333, "x": 342.11, "y": 257.31, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 228.07, "y": 171.54}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star11": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": 385.95, "y": 173.45, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star12": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": 178.19, "y": 352.61, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 178.19, "y": 352.61}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star13": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": 262.38, "y": -11.44}, {"time": 0.1333, "x": 393.57, "y": -17.15, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 262.38, "y": -11.44}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star14": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": 401.19, "y": -144.86, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 401.19, "y": -144.86}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "tai": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "tai2": {"scale": [{}, {"time": 0.4, "x": 1.16, "y": 1.16}, {"time": 0.8333, "x": 1.329, "y": 1.329}]}, "as": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.853, "y": 0.853}, {"time": 0.8333}]}}, "drawOrder": [{"time": 0.7333, "offsets": [{"slot": "tai", "offset": 1}]}]}, "xiu": {"slots": {"star11": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star3": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "tai": {"attachment": [{"name": "xiu"}]}, "star": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "star12": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "star14": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "star4": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "star6": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "as copy7": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "color": "ffffff00"}]}, "star8": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00"}]}, "as copy5": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "star5": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}, "tai2": {"color": [{"color": "ffffff56"}, {"time": 0.4, "color": "ffffff2a"}, {"time": 0.8333, "color": "ffffff00"}], "attachment": [{"name": "xiu"}]}, "star10": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star13": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star9": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "as copy3": {"color": [{"color": "ffffff8d", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff8d"}]}, "as copy6": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.6, "color": "ffffff00"}]}, "as copy": {"color": [{"color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.0333, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "color": "ffffff00"}]}, "as copy2": {"color": [{"color": "ffffff00", "curve": "stepped"}, {"time": 0.2667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.3, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "color": "ffffff00"}]}, "as copy4": {"color": [{"color": "ffffff8d", "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00", "curve": 0.25, "c3": 0.75}, {"time": 0.7, "color": "ffffffff", "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "color": "ffffff8d"}]}, "star2": {"color": [{"color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}]}, "star7": {"color": [{"time": 0.2667, "color": "ffffffff"}, {"time": 0.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}]}}, "bones": {"as copy": {"translate": [{"x": -18.99, "y": -32.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.831}]}, "as copy2": {"rotate": [{"angle": 47.89}], "translate": [{"x": -107.94, "y": -34.49, "curve": "stepped"}, {"time": 0.2667, "x": -107.94, "y": -34.49, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -107.94, "y": -34.49}], "scale": [{"x": 1.132, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.132}]}, "as copy3": {"rotate": [{"angle": -43.88}], "translate": [{"x": -22.91, "y": -106.28, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"x": 1.226, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.831, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.226}]}, "as copy4": {"rotate": [{"angle": -130.35, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "angle": -43.88}], "translate": [{"x": -38.63, "y": -211.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 21.38, "y": -97.97, "curve": "stepped"}, {"time": 0.6667, "x": 21.38, "y": -97.97, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -22.91, "y": -106.28}], "scale": [{"x": 1.6, "y": 1.24, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 1.831, "curve": "stepped"}, {"time": 0.6667, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.226}]}, "as copy5": {"translate": [{"x": -18.99, "y": -32.37, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": -18.99, "y": -32.37}], "scale": [{"x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.2667, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.5333, "x": 1.831}]}, "as copy6": {"rotate": [{"angle": 107.06}], "translate": [{"x": -114.3, "y": -110.55, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -126.07, "y": -109.54, "curve": "stepped"}, {"time": 0.3333, "x": -126.07, "y": -109.54, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -114.3, "y": -110.55}], "scale": [{"x": 1.347, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 1.528, "curve": "stepped"}, {"time": 0.3333, "x": 1.528, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 1.347}]}, "as copy7": {"rotate": [{"angle": 161.64}], "translate": [{"x": -73.04, "y": -123.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -96.36, "y": -157.36, "curve": "stepped"}, {"time": 0.5, "x": -96.36, "y": -157.36, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -67.21, "y": -114.59, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": -73.04, "y": -123.14}], "scale": [{"x": 0.863, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": 1.831, "curve": "stepped"}, {"time": 0.5, "x": 1.831, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 0.621, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "x": 0.863}]}, "star": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": -416.48, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star2": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": -202.68, "y": 139.77}, {"time": 0.1333, "x": -304.03, "y": 209.66, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": -202.68, "y": 139.77}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star3": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": -233.18, "y": -134.69}, {"time": 0.1333, "x": -349.77, "y": -202.04, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": -233.18, "y": -134.69}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star4": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": -284.97, "y": -264.93, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": -284.97, "y": -264.93}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star5": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": -67.68, "y": -289.71, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star6": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": -149.32, "y": 207.12}, {"time": 0.1333, "x": -223.97, "y": 310.68, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": -149.32, "y": 207.12}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star7": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": -90.55, "y": 318.3, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star8": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": 170.57, "y": -270.65, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 170.57, "y": -270.65}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star9": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": 210.28, "y": -181.71}, {"time": 0.1333, "x": 315.42, "y": -272.56, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 210.28, "y": -181.71}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star10": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": 228.07, "y": 171.54}, {"time": 0.1333, "x": 342.11, "y": 257.31, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 228.07, "y": 171.54}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star11": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "translate": [{}, {"time": 0.4, "x": 385.95, "y": 173.45, "curve": "stepped"}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.8333}]}, "star12": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": 178.19, "y": 352.61, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 178.19, "y": 352.61}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "star13": {"rotate": [{"angle": 120}, {"time": 0.2667, "angle": -120}, {"time": 0.5333}, {"time": 0.8333, "angle": 120}], "translate": [{"x": 262.38, "y": -11.44}, {"time": 0.1333, "x": 393.57, "y": -17.15, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 262.38, "y": -11.44}], "scale": [{"x": 0.683, "y": 0.683}, {"time": 0.1333, "x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.5333}, {"time": 0.8333, "x": 0.683, "y": 0.683}]}, "star14": {"rotate": [{"angle": -84}, {"time": 0.2}, {"time": 0.4667, "angle": 120}, {"time": 0.7333, "angle": -120}, {"time": 0.8333, "angle": -84}], "translate": [{"x": 401.19, "y": -144.86, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 401.19, "y": -144.86}], "scale": [{"x": 0.525, "y": 0.525, "curve": "stepped"}, {"time": 0.2}, {"time": 0.6, "x": 0.525, "y": 0.525}]}, "tai": {"scale": [{}, {"time": 0.4, "x": 1.087, "y": 1.087, "curve": 0.25, "c3": 0.75}, {"time": 0.8333}]}, "tai2": {"scale": [{}, {"time": 0.4, "x": 1.16, "y": 1.16}, {"time": 0.8333, "x": 1.329, "y": 1.329}]}, "as": {"rotate": [{}, {"time": 0.2667, "angle": 120}, {"time": 0.5333, "angle": -120}, {"time": 0.8333}], "scale": [{}, {"time": 0.4, "x": 0.853, "y": 0.853}, {"time": 0.8333}]}}, "drawOrder": [{"time": 0.7333, "offsets": [{"slot": "tai", "offset": 1}]}]}}}, [0]]], 0, 0, [0], [-1], [39]], [[{"name": "tTI", "rect": [0, 0, 187, 65], "offset": [0, 0], "originalSize": [187, 65], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [40]], [[{"name": "info", "rect": [0, 0, 65, 65], "offset": [0, 0], "originalSize": [65, 65], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [41]], [[{"name": "xiu", "rect": [0, 0, 156, 97], "offset": [0, 0], "originalSize": [156, 97], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [42]], [[{"name": "button_thongke", "rect": [2, 3, 122, 120], "offset": [0, 0], "originalSize": [126, 126], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [43]], [[{"name": "chat", "rect": [1, 0, 64, 65], "offset": [0.5, 0], "originalSize": [65, 65], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [44]], [[[65, "taixiuView"], [66, "taixiuView", [-46, -47, -48], [[[31, -2, [226, 227], 225], [87, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3], [88, -18, -17, -16], [89, -41, -40, -39, -38, -37, [-34, -35, -36], -33, -32, -31, -30, [-29], [-28], -27, -26, -25, -24, -23, -22, -21, -20, -19], [90, -42], [91, -43], -44, [92, -45, 228, 229, 230, 231, 232, 233]], 4, 4, 4, 4, 4, 4, 1, 4], [93, -1], [5, 860, 500], [-128, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [70, "betInputView", [-63, -64], [[94, -60, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49], [32, -61, [153, 154]], [50, -62, 155]], [0, "f1/Ciwkr9DrYMuBCRoQX+r", 1], [5, 680, 230], [0, 0.5, 1], [24, -225, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "taiXiuMain", 1, [-65, -66, -67, -68, -69, 2, -70, -71, -72, -73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87], [0, "f2PA3AISdOeICO/OiRMTCK", 1], [5, 900, 400], [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "session<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", 3, [-106, -107, -108, -109, -110, -111, -112, -113, -114, -115, -116, -117, -118, -119, -120, -121], [[35, 1, 1, 18.1, -88, [5, 703.4000000000001, 40]], [100, -105, [-89, -90, -91, -92, -93, -94, -95, -96, -97, -98, -99, -100, -101, -102, -103, -104], [63, 64]]], [0, "84zLbYIDROxZQyE4m7nBO6", 1], [5, 703.4000000000001, 40], [0, -138, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [71, "buttonView", 3, [-127, -128, -129, -130, -131, -132, -133, -134, -135], [[101, [null, null], -126, -125, -124, -123, [40, 41], -122, [42, 43]]], [0, "96LGW41hVLrYfxg1hiEyaV", 1]], [21, "layout", [-137, -138, -139, -140, -141, -142, -143, -144, -145, -146, -147, -148, -149, -150], [[95, false, 1, 1, 2, -136, [5, 432, 60]]], [0, "0fB3L+O+1PMqnG8q3wxOcO", 1], [5, 432, 60], [-3, 85.2, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [72, "layout-free", false, [-152, -153, -154, -155, -156, -157, -158, -159, -160, -161, -162, -163], [[96, false, 1, 3, 25.9, 15, -151, [5, 596, 107]]], [0, "86cL5cJoFDLYbbHeMalb7J", 1], [5, 596, 107], [2, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "bg-main", 3, [-165, -166, -167, -168, -169, -170, -171, -172, -173, -174, -175], [[13, false, -164, [87], 88]], [0, "515YzdXMNISIVz9dU7CcI4", 1], [5, 893, 409]], [21, "layout-value", [-177, -178, -179, -180, -181, -182, -183, -184, -185, -186], [[97, 1, 1, 18, 15, -176, [5, 886, 86]]], [0, "d1JnSaiIJGoaZceGkpwCXM", 1], [5, 886, 86], [0, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "layout-infoBet", 3, [-187, -188, -189, -190, -191, -192, -193, -194, -195, -196], [0, "5d3NAB5TxG6LVDHjiwA47D", 1], [5, 200, 150], [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "tornado", false, 3, [-199, -200, -201, -202, -203, -204], [[7, 2, false, -197, [162]], [31, -198, [164, 165, 166], 163]], [0, "91eBbots5A2IiMmRs3W6lP", 1], [5, 144, 147], [32, -1, 0, 0, 0, 0, 1, 2.07, 2.07, 1]], [67, "layout-info", 3, [-205, -206, -207, -208, -209, -210, -211, -212], [0, "f7utxE0mtBeY7DdIIgeNtM", 1], [5, 200, 150]], [21, "chatView", [-218, -219, -220, -221], [[106, -217, -216, -215, -214, -213]], [0, "59rGikyehL15ZugGeK2QZt", 1], [5, 360, 420], [0, 14, 0, 0, 0, 0, 1, 1, 1, 1]], [74, "eventView", false, 3, [-241, -242, -243], [[107, -240, [-237, -238, -239], -236, -235, -234, -233, -232, -231, [-224, -225, -226, -227, -228, -229, -230], [66, 67], -223, -222]], [0, "287m9/ONxGz7PX97Bankj0", 1]], [26, "result-sprite", 3, [-244, -245, -246, -247, -248], [0, "69OILfZQJMUIpeEueV6mFO", 1]], [42, "scrollview", 13, [-252, -253], [[-249, [54, -250], -251], 1, 4, 1], [0, "feJr3qVLpBQ5ldigMpP7n0", 1], [5, 322, 300], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "item", [-260, -261], [[35, 1, 2, 5, -254, [5, 321, 60.72]], [108, -259, -258, -257, -256, -255]], [0, "f87v/ZvNdK6JU2FRvPLhmN", 1], [5, 321, 60.72]], [76, "bg-input", false, 2, [9, 7, -263], [[109, -262]], [0, "66VWM5sqRKZYPtSGsZZWdM", 1], [5, 880, 190], [0, 0.5, 1], [-13, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "layout-button", 18, [-265, -266, -267, -268], [[35, 1, 1, -45, -264, [5, 760, 100]]], [0, "dfYO7d9A5CMouMLksv74zK", 1], [5, 760, 100], [0, -135, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "result-<PERSON><PERSON>", false, 15, [-269, -270, -271, -272], [0, "692uQjG7tDlrb9DKjUf2ko", 1], [0, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bat", 15, [[36, 0, false, -273, [198], 199], [110, -275, [4, **********], -274], [117, -277, -276]], [0, "1eoeJVUctL5bEH5s8XGDrp", 1], [5, 250, 250], [0, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [77, "resultEffectView", 0, 3, [-281, -282, -283, -284], [[119, -280, -279, -278]], [0, "e1VOoT8LdIVqhSNlGMfrsT", 1]], [44, "light_off", 160, 1, [[22, 0, -285, [0], 1], [32, -286, [2, 3]], [120, -287]], [0, "ffU/DJmmJLVYXV76BG1uXo", 1], [5, 4000, 4000], [-1.1368683772161603e-13, 3.410605131648481e-13, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "btnEvent", false, 14, [[39, -289, [[5, "7db83HkR/JO7aKBhmpOyOhT", "openEventClicked", 14]], [4, 4294967295], [4, 4294967295], -288], [40, "animation", 0, false, "animation", -290, [8]]], [0, "01TZUo02RINaMFZ/oa67Vq", 1], [5, 138.98, 128.22], [-412, 175, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [10, "bg_popup_chat", 13, [-293, -294], [[102, 1, 0, false, -291, [30], 31], [54, -292]], [0, "70kDG7KvpFJIwiIR89/vwM", 1], [5, 374, 468], [0, -15, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [46, "editbox-chat", 13, [-296, -297, -298], [-295], [0, "d3G70mk45McYI1QLXe0l84", 1], [5, 200, 47.5], [-55.3, -176.3, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "item-horizontal", 17, [-300, -301], [[98, 1, 5, -299, [5, 295, 33]]], [0, "e09E6Vr0BFpY3SDMXL/QxO", 1], [5, 295, 33], [0, -13.86, 0, 0, 0, 0, 1, 1.066, 1.066, 1.066]], [1, "btnOpenInputTai", 10, [[4, -302, [145], 146], [39, -304, [[5, "347b1xdyqpC24YIVRrVdPlw", "openInputBetTaiClicked", 2]], [4, 4294967295], [4, 4294967295], -303]], [0, "384IYjVFZJ778nnfcCWUAT", 1], [5, 187, 65], [-246, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "editboxDatXiu", false, 0, 10, [-306, -307, -308], [-305], [0, "f4fCNOM+dM/bdIaVDF/hyn", 1], [5, 160, 40], [316, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "editboxDatTai", false, 0, 10, [-310, -311, -312], [-309], [0, "bfmT2f9jJI8LNedES0L1FN", 1], [5, 174, 64], [-250, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnOpenInputXiu", 10, [[4, -313, [143], 144], [39, -315, [[5, "347b1xdyqpC24YIVRrVdPlw", "openInputBetXiuClicked", 2]], [4, 4294967295], [4, 4294967295], -314]], [0, "19CYvRd4JLc5h9AOE8ho4u", 1], [5, 187, 65], [246, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "bg-timer", false, 3, [-317], [[13, false, -316, [202], 203]], [0, "0f6Nbu03pAqKC0bjfSTv3Y", 1], [5, 51, 51], [0, 129, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [10, "TX JP", 3, [-319, -320], [[23, "default", "animation", 0, false, "animation", -318, [223], 224]], [0, "0c49nBlMdF37HUXtWucyWU", 1], [5, 1210.91, 1264.63], [-1.643, -1.14, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [19, "trie<PERSON><PERSON><PERSON>", false, 3, [-322, -323], [[111, -321, [[5, "7db83HkR/JO7aKBhmpOyOhT", "openEventClicked", 14]], [4, **********]]], [0, "52+LSTWR1Cq4ZdGKyHRisK", 1], [5, 800, 200], [-97, 291, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "bg_dayTT", [-325, -326], [[8, 0, false, -324]], [0, "a3EB+Uq39BN4d/OwLSwDLW", 1], [5, 883, 109], [0, 75, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "event-label", false, 14, [-327, -328], [0, "76I+YCaJVMaaQ4jX3518xF", 1], [24, -259, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnTop", 5, [-331], [[13, false, -329, [15], 16], [3, 1.1, 3, -330, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "topClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "46+Yc5MblJ86YaTNxGAdIs", 1], [5, 65, 65], [401.427, 122.412, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "btnSend", 13, [-333], [-332], [0, "177h3AgtBNyZrjOAqB5i8a", 1], [5, 80, 44], [108.4, -182.7, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "dot", 4, [-336], [[-334, [86, true, -335]], 1, 4], [0, "a1X8s7Z7xMyq3IXDO9qK39", 1], [5, 30, 30], [336.7, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnAllin", 19, [-340], [[4, -337, [124], 125], [112, 3, -339, [[5, "347b1xdyqpC24YIVRrVdPlw", "allInClick", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -338, 126, 127, 128]], [0, "90ZgdbrpBMirFThZHyolZS", 1], [5, 256, 61], [-252, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [78, "Background", false, 40, [[103, 1, -341, [122], 123], [123, 0, 45, -53, -53, -0.5, -0.5, 100, 40, -342]], [0, "f3FRrARztHGqOXh9GOEQcW", 1], [4, 4293322470], [5, 256, 61]], [79, "bgNotify", false, 0, 3, [-345], [[22, 0, -343, [147], 148], [31, -344, [150, 151], 149]], [0, "90z6E5B15GGoEniPkdWcZP", 1], [5, 560, 50], [1.528, 126.534, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg_timew", false, 15, [[37, false, 0, -346, [193]]], [0, "226d3UbMRMy46bsEli4ix9", 1], [5, 48, 48], [101, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [68, "bgresult", 0, 3, [-349], [[-347, [22, 0, -348, [216], 217]], 1, 4], [0, "6fWnomkaNLZYAGgmbHenLa", 1], [5, 560, 50], [0, 44, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Trieuhoi", 34, [-350], [0, "63b0PQCBhKgINCV0xM3JfR", 1], [5, 347.67, 76.39], [50.099999999999994, -62.7, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "offset-event-sprite", 14, [-351], [0, "e2ur+jx9hOFYs1nNI388po", 1], [0, -16.700000000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "event-PH-cord", false, 46, [35, 6], [0, "91kGS1jSRBhL6u5ZFgZTQP", 1], [24, -325, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e_black copy", 6, [[8, 2, false, -352], [14, false, -353, [4, **********]]], [0, "76Y0XWFI5CR59+jqJWTVvW", 1], [5, 60, 60], [-251.2, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e_black copy", 6, [[8, 2, false, -354], [14, false, -355, [4, **********]]], [0, "5b7k0ulTFNTKxfeKtwBYAP", 1], [5, 60, 60], [-168.1, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e_black copy", 6, [[8, 2, false, -356], [14, false, -357, [4, **********]]], [0, "a5BwfdQ/BJHq4NIihOdq1C", 1], [5, 60, 60], [-84.5, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e_black copy", 6, [[8, 2, false, -358], [14, false, -359, [4, **********]]], [0, "40L4+KH3NP9oWDwZKKdHXb", 1], [5, 60, 60], [-1.6, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e_black copy", 6, [[8, 2, false, -360], [14, false, -361, [4, **********]]], [0, "38m5jTWe9HfoM42sa8SWG6", 1], [5, 60, 59], [82.2, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e_black copy", 6, [[8, 2, false, -362], [14, false, -363, [4, **********]]], [0, "6f502tOYxPTpjpj0m4M8pR", 1], [5, 60, 60], [166.5, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e_black copy", 6, [[8, 2, false, -364], [14, false, -365, [4, **********]]], [0, "25/cO9GTtGIoF80mTD/QOQ", 1], [5, 60, 60], [249, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnEvent", 5, [[-366, [3, 1.1, 3, -367, [[5, "79a43qxc5JLRZdcLtAklZTY", "sessionDetailClicked", 4]], [4, 4294967295], [4, 4294967295]]], 1, 4], [0, "90wpAWK3BNfZsBllWpFoqg", 1], [5, 65, 65], [-408.273, -83.111, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 5, [[13, false, -368, [11], 12], [3, 1.1, 3, -369, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "helpClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "30/RiPZ2RAXJY+GyzdbF4q", 1], [5, 65, 65], [-431.328, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHistory", 5, [[13, false, -370, [17], 18], [3, 1.1, 3, -371, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "historyClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "5cEqbomeZC2LKYwCJOzqiA", 1], [5, 65, 65], [-319.625, 185, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnNan", 5, [-372, -373], [0, "75qOvftIFOW4kS5SL8dbj1", 1], [5, 65, 65], [419, -83, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 5, [[13, false, -374, [20], 21], [3, 1.1, 3, -375, [[5, "1d8f7wrbA9IfIfaq0FoLVi8", "closeClicked", 1]], [4, 4294967295], [4, 4294967295]]], [0, "60ZBR65J1Gwp2EFOIOuc+p", 1], [5, 65, 65], [314.416, 189.877, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnChat", 5, [[-376, [3, 1.1, 3, -377, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "chatClicked", 5]], [4, 4294967295], [4, 4294967295]]], 1, 4], [0, "8bkmx9EdZPZbtY/qQZFREU", 1], [5, 64, 65], [438.228, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnGraph", 5, [[13, false, -378, [23], 24], [3, 1.1, 3, -379, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "graphClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "55dZGZPxxMAZGh2sNO4TtR", 1], [5, 122, 120], [-399.504, 117.648, 0, 0, 0, 0, 1, 0.814, 0.814, 1]], [1, "close", 25, [[4, -380, [27], 28], [113, 1.1, 3, -381, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "chatClicked", 5]], 29]], [0, "5bWaRypTJB97nj51DuP77i", 1], [5, 96, 50], [156, 212, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [10, "view", 16, [-383], [[124, -382, [38]]], [0, "ec/6ZA3WFGJ766Dm4h76bM", 1], [5, 325, 300], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content", 63, [[99, 1, 2, -384, [5, 300, 0]]], [0, "2aRhuuAc5GTZZjgTJQiRVc", 1], [5, 300, 0], [0, 141, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "dot-outline", 39, [[9, 0, -385, [59]], [33, true, -386, [61], 60]], [0, "55luKeYlpDlp2x5srtni7u", 1], [5, 36, 44]], [18, "lblJackpot", 8, [[-387, [125, -389, -388]], 1, 4], [0, "eeethtnIhAw57mxffMyz13", 1], [5, 155.45, 0], [-4.828, 174.304, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "99,999,999", 9.5, 0, false, 2, 1, 1, 66, [78]], [1, "btnValue", 9, [[4, -390, [91], 92], [3, 1.1, 3, -391, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "1000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "78bkHfEhRIGK1LxcEEEkQ2", 1], [5, 95, 58], [-395.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "btnValue", false, 9, [[9, 0, -392, [93]], [3, 1.1, 3, -393, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "5000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "bbqrDXsAFEPIcYHdzcn4fT", 1], [5, 118, 54]], [1, "btnValue", 9, [[4, -394, [94], 95], [3, 1.1, 3, -395, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "10000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "1b4w/c4oFME6Db63LqkXr0", 1], [5, 95, 58], [-282.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 9, [[4, -396, [96], 97], [3, 1.1, 3, -397, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "50000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "39Bq5L749I/rqCKwljCB3M", 1], [5, 95, 57], [-169.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 9, [[4, -398, [98], 99], [3, 1.1, 3, -399, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "100000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "c3/hrjI2xFMaFCUiGow2Z9", 1], [5, 95, 56], [-56.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "btnValue", false, 9, [[37, false, 0, -400, [100]], [3, 1.1, 3, -401, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "200000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "d55TPF3ChAY4HV8HosJ6lk", 1], [5, 118, 54]], [1, "btnValue", 9, [[4, -402, [101], 102], [3, 1.1, 3, -403, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "500000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "e5tAnMvflDQ6Zokl451Q7L", 1], [5, 95, 58], [56.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 9, [[4, -404, [103], 104], [3, 1.1, 3, -405, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "1000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "d36DLZwWJLBbEOWDAjjtGw", 1], [5, 95, 58], [169.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 9, [[4, -406, [105], 106], [3, 1.1, 3, -407, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "10000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "b5pUIbFQlDgKOjH4A4xEBJ", 1], [5, 95, 58], [282.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 9, [[4, -408, [107], 108], [3, 1.1, 3, -409, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "50000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "dakYzp9blBErbuGOp461U2", 1], [5, 95, 58], [395.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -410, [109]], [3, 1.1, 3, -411, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "1", 2]], [4, 4294967295], [4, 4294967295]]], [0, "eeiBwe0RhPUrfHJoIuKOXp", 1], [5, 78, 47], [-259.5, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -412, [110]], [3, 1.1, 3, -413, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "2", 2]], [4, 4294967295], [4, 4294967295]]], [0, "9cudEVK5pNY5k7Ymr8cKxe", 1], [5, 78, 47], [-156.6, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -414, [111]], [3, 1.1, 3, -415, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "3", 2]], [4, 4294967295], [4, 4294967295]]], [0, "60piKM4pJAkZVJRczHb46G", 1], [5, 78, 47], [-53.699999999999996, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -416, [112]], [3, 1.1, 3, -417, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "4", 2]], [4, 4294967295], [4, 4294967295]]], [0, "78IzivSFVDdatAgURbUzpj", 1], [5, 78, 47], [49.2, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -418, [113]], [3, 1.1, 3, -419, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "5", 2]], [4, 4294967295], [4, 4294967295]]], [0, "41SCJX7+9Oy7lgsNsyzJ8q", 1], [5, 78, 47], [152.1, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue000", 7, [[9, 0, -420, [114]], [3, 1.1, 3, -421, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "30FsS6HuxI142OfsKY2zi6", 1], [5, 78, 47], [255, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -422, [115]], [3, 1.1, 3, -423, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "6", 2]], [4, 4294967295], [4, 4294967295]]], [0, "4avwFYr4hJVYYWuaxGnmFa", 1], [5, 78, 47], [-259.5, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -424, [116]], [3, 1.1, 3, -425, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "7", 2]], [4, 4294967295], [4, 4294967295]]], [0, "30Q5t8+iNBZ68WnUOjdyuJ", 1], [5, 78, 47], [-156.6, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -426, [117]], [3, 1.1, 3, -427, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "8", 2]], [4, 4294967295], [4, 4294967295]]], [0, "6fXg09XO5PIb/m6i3MYk4e", 1], [5, 78, 47], [-53.699999999999996, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -428, [118]], [3, 1.1, 3, -429, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "9", 2]], [4, 4294967295], [4, 4294967295]]], [0, "a88b8I7FdN7J37O8MtoVrB", 1], [5, 78, 47], [49.2, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[9, 0, -430, [119]], [3, 1.1, 3, -431, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "0", 2]], [4, 4294967295], [4, 4294967295]]], [0, "a2trlw+IBNlK1tiwFhfr21", 1], [5, 78, 47], [152.1, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnDelete", 7, [[9, 0, -432, [120]], [3, 1.1, 3, -433, [[5, "347b1xdyqpC24YIVRrVdPlw", "deleteClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "90xqiS7H5C6o43XYGl93sP", 1], [5, 78, 47], [255, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "btnOther", false, 19, [[37, false, 0, -434, [121]], [114, false, 1.1, 3, -435, [[5, "347b1xdyqpC24YIVRrVdPlw", "otherClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "4dy4pAjzBHoLEKyn8rWyPs", 1], [5, 150, 60], [-240, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnConfirm", 19, [[4, -436, [129], 130], [3, 1.1, 3, -437, [[5, "347b1xdyqpC24YIVRrVdPlw", "confirmClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "ecOle4eUxO363N/tVHUaPN", 1], [5, 347, 72], [4.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCancel", 19, [[4, -438, [131], 132], [3, 1.1, 3, -439, [[5, "347b1xdyqpC24YIVRrVdPlw", "cancelClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "93Itmh0PNAu7XVEy69Ofev", 1], [5, 247, 60], [256.5, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [57, 21, 0, false, false, 1, 1, [141]], [57, 21, 0, false, false, 1, 1, [142]], [126, "<PERSON>n mời nặn", 18, 0, false, -1, 1, 1, [152]], [12, "taiWin", false, 3, [[24, "default", "tai", 0, "tai", -440, [167], 168]], [0, "c3gFqZcRVHrKfLpm8Bv2Kj", 1], [5, 573, 606.49], [-237.193, 69, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [12, "x<PERSON><PERSON><PERSON>", false, 3, [[24, "default", "xiu", 0, "xiu", -441, [169], 170]], [0, "abgsXJJ0JLLbdIAtRNcNRB", 1], [5, 573, 606.49], [262, 66, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [1, "tai", 3, [[36, 2, false, -442, [171], 172]], [0, "0aZUoukDlEo7mReWGR3Ryk", 1], [5, 132, 89], [-252.7, 84.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "xiu", 3, [[36, 2, false, -443, [173], 174]], [0, "08HEJaNexPCa/mbvum/mUp", 1], [5, 156, 97], [258.2, 84.9, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sprite", 12, [-445], [[4, -444, [181], 182]], [0, "cb7hSH+PRPkZBNPoox6rEh", 1], [5, 74, 29], [-138, 122, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sprite", 12, [-447], [[4, -446, [184], 185]], [0, "5cs0mruTpLVqyl9sGCh+MJ", 1], [5, 74, 28], [138, 122, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "cancua", false, 12, [-449], [[51, -448, [189]]], [0, "3eLcGYTWNKlrn1k47Sdvco", 1], [5, 496, 45], [35, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "newSesion", false, 12, [-451], [[51, -450, [192]]], [0, "5aAL6eo35Ee6zzy9zFVhwV", 1], [5, 496, 45], [35, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbTaiWin", 22, [[-452, [33, true, -453, [208], 207]], 1, 4], [0, "be5kRNiA9Pp4rj60rKQK8D", 1], [5, 226.91, 0], [-6.063, -15.856, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "lbXiuWin", 22, [[-454, [33, true, -455, [212], 211]], 1, 4], [0, "13oUZjYE9G1Zc9r+UBkbUM", 1], [5, 225.59, 0], [-6.063, -15.856, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "light_off copy", 150, 1, [[22, 0, -456, [4], 5]], [0, "fd8UAxPlBHWbpF4M8BG7Db", 1], [5, 4000, 4000], [-1.1368683772161603e-13, 3.410605131648481e-13, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "Sukien-RongThan-1", 34, [-457], [0, "cen2aNEuRD/6q1uQDsSmm5", 1], [5, 416.1, 328.98], [-230.9, -90.7, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "Idle", 0, false, "Idle", 107, [6]], [40, "Idle", 0, false, "Idle", 45, [7]], [2, "daythang-btn", 35, [-458], [0, "bfX6AOedVL9YqMUGxO9Rk0", 1], [5, 140, 45], [-336.4, 5.8, 0, 0, 0, 0, 1, 1, 1, 1]], [52, 110], [2, "daythua-btn", 35, [-459], [0, "6el5S0YttGFKXfPn3ttMmW", 1], [5, 140, 45], [336.6, 5.6, 0, 0, 0, 0, 1, 1, 1, 1]], [52, 112], [11, "e_black", false, 6, [-460], [0, "f1lmh6HnNJ6IF2WZMRkT8t", 1], [5, 60, 60], [-251.2, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 114], [11, "e_black", false, 6, [-461], [0, "0fsZd9grtLsY+fUAzkst+i", 1], [5, 60, 60], [-168.1, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 116], [11, "e_black", false, 6, [-462], [0, "eemO+dmKJIJ5C4ZZF5dDlG", 1], [5, 60, 60], [-84.5, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 118], [11, "e_black", false, 6, [-463], [0, "9bZi8eHxNDPLUbQntHz0Uk", 1], [5, 60, 60], [-1.6, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 120], [11, "e_black", false, 6, [-464], [0, "66il4G+ktAYIyaO5RtP5Bw", 1], [5, 60, 59], [82.2, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 122], [11, "e_black", false, 6, [-465], [0, "4bznNdjCxM5bRAiQkrTvWH", 1], [5, 60, 60], [166.5, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 124], [11, "e_black", false, 6, [-466], [0, "efYVf9aFpAn77c3ViDGjt6", 1], [5, 60, 60], [249, -4.2, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 2, false, 126], [16, "lbCountCordLostVal", 36, [-467], [0, "7aIPzVLiJNto1nz9uC9h+v", 1], [4, 4281611483], [5, 12.24, 63], [0, 0, 0.5], [368.6, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "0", 22, 50, false, 1, 128, [9]], [16, "lbCountCordWinVal", 36, [-468], [0, "a9FhdZVjtN9ovdR47dI4Tu", 1], [4, 4281282534], [5, 12.24, 63], [0, 0, 0.5], [-296, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "0", 22, 50, false, 1, 130, [10]], [38, false, 55, [65]], [1, "Gift", 37, [[4, -469, [13], 14]], [0, "ffmijmlq9JBZS3PfDoVlUs", 1], [5, 49, 49], [35, 20, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [38, false, 58, [19]], [3, 1.1, 3, 58, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "nanClicked", 5]], [4, 4294967295], [4, 4294967295]], [38, false, 60, [22]], [12, "bt_big", false, 5, [[104, 0, -470]], [0, "d3ysmLEMVLOq3lx4aKH0dJ", 1], [5, 75, 75], [300, 195, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "offset-chat", 3, [13], [0, "d1Thc7nc1OL6ve2EbJVZ1G", 1], [707, 5, 0, 0, 0, 0, 1, 1.2, 1.35, 1]], [1, "boder", 25, [[4, -471, [25], 26]], [0, "82g7MaEBdI24AYTaSIv8bk", 1], [5, 404, 494], [0, 24, 0, 0, 0, 0, 1, 0.91, 0.68, 1]], [1, "sprite", 38, [[4, -472, [32], 33]], [0, "f5QxZPRJdC/JlXuLIQfFp7", 1], [5, 93, 53], [4, 5.5, 0, 0, 0, 0, 1, 1, 1, 1]], [115, 1.1, false, 3, 38, [[5, "c9ea2HJ+4FBwJf8JdBQBbUQ", "sendChatClicked", 13]], [4, 4294967295], [4, 4294967295], 38], [29, "BACKGROUND_SPRITE", 26, [-473], [0, "2dsk/T0mhFTKNbhUUz6/AO", 1], [5, 220, 47.5]], [105, 1, 0, 142, [34]], [82, "TEXT_LABEL", false, 26, [-474], [0, "e01KBllTZHTYQdkrqZ9I5U", 1], [5, 197.3, 47.5], [0, 0, 1], [-97.65, 23.75, 0, 0, 0, 0, 1, 1, 1, 1]], [127, 18, 42, false, false, 1, 1, 144], [16, "PLACEHOLDER_LABEL", 26, [-475], [0, "64paPc+ZhOdqychdYt0s3x", 1], [4, 4290493371], [5, 197.3, 47.5], [0, 0, 1], [-96.728, 27.849, 0, 0, 0, 0, 1, 1, 1, 1]], [128, "<PERSON><PERSON><PERSON><PERSON> nội dung chat", 18, 47.5, false, false, 1, 1, 146, [35]], [138, 2, 250, 6, 26, [[121, "c9ea2HJ+4FBwJf8JdBQBbUQ", "editingReturn"]], 145, 147, 143], [26, "temp", 16, [17], [0, "85WIWEROlOp7NmX1iBHQRc", 1]], [16, "rtChat", 17, [-476], [0, "10qEssX8VOCLfN0JCbuHk5", 1], [4, 4291743438], [5, 321, 27.72], [0, 0, 0.5], [-161, 16.5, 0, 0, 0, 0, 1, 1, 1, 1]], [139, false, "", 17, 321, 22, false, 150], [16, "lbNickName", 27, [-477], [0, "0c+S06bqNPzZcyGrLqJOtU", 1], [4, 4278224383], [5, 0, 20], [0, 0, 0.5], [-147.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [129, 16, 20, false, false, 1, 152, [36]], [16, "lbMessage", 27, [-478], [0, "5aW9StTIxP6ZkyDmBzNdTd", 1], [4, 4294440951], [5, 226, 23], [0, 0, 0.5], [-142.5, 0.29, 0, 0, 0, 0, 1, 1, 1, 1]], [130, 16, 20, false, false, 1, 1, 154, [37]], [140, false, 16, 64], [141, 15, 400, 16, 17, 156], [131, "Label", 1, 1, [39]], [11, "New Label", false, 3, [158], [0, "77ebTB155FqIcjJIidMyJf", 1], [5, 97.87, 50.4], [-237.193, 69, 0, 0, 0, 0, 1, 0.4, 0.4, 0.4]], [11, "dot", false, 4, [-479], [0, "b7dQZYwm9HYoqpwFp86Xk1", 1], [5, 30, 30], [-337.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 160, [44]], [2, "dot", 4, [-480], [0, "3bvI7x+YRJH5DcDv/3AVs7", 1], [5, 30, 30], [-336.70000000000005, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 162, [45]], [2, "dot", 4, [-481], [0, "28hSBOqF5MhqqFe8gDY3vM", 1], [5, 30, 30], [-288.6, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 164, [46]], [2, "dot", 4, [-482], [0, "93vmqdFt9FEIuCMd9Sr0s/", 1], [5, 30, 30], [-240.50000000000003, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 166, [47]], [2, "dot", 4, [-483], [0, "b8KABM67hD45uhCjuGDPP8", 1], [5, 30, 30], [-192.40000000000003, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 168, [48]], [2, "dot", 4, [-484], [0, "9b3eXFoUlAv6y1/FPJSoJt", 1], [5, 30, 30], [-144.30000000000004, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 170, [49]], [2, "dot", 4, [-485], [0, "ddu25zBJlAdYqgKvASDRqL", 1], [5, 30, 30], [-96.20000000000005, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 172, [50]], [2, "dot", 4, [-486], [0, "3cYkRedvNC4JNOfJSey88c", 1], [5, 30, 30], [-48.100000000000044, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 174, [51]], [2, "dot", 4, [-487], [0, "c3dJ6j8y1DWKadY+vEf9U0", 1], [5, 30, 30], [-4.263256414560601e-14, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 176, [52]], [2, "dot", 4, [-488], [0, "bf2IH0oI5MAYHmOgxtSF8l", 1], [5, 30, 30], [48.09999999999996, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 178, [53]], [2, "dot", 4, [-489], [0, "98EfLMCU1ACauJTPPOE8Hf", 1], [5, 30, 30], [96.19999999999996, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 180, [54]], [2, "dot", 4, [-490], [0, "e7ISrWTwtBnoXTIDjhGfv0", 1], [5, 30, 30], [144.29999999999995, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 182, [55]], [2, "dot", 4, [-491], [0, "60lAZWl+RKbr3nA8v+0KV/", 1], [5, 30, 30], [192.39999999999995, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 184, [56]], [2, "dot", 4, [-492], [0, "20U2Ra1ElP5baMw/yCvaWP", 1], [5, 30, 30], [240.49999999999994, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 186, [57]], [2, "dot", 4, [-493], [0, "7cxq3f2iJBBb1CK0TJnxD0", 1], [5, 30, 30], [288.59999999999997, -7.104, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 0, false, 188, [58]], [7, 0, false, 39, [62]], [1, "taixiu_superwin", 8, [[23, "default", "animation", 0, false, "animation", -494, [68], 69]], [0, "7bz7eSIflNh79At9I/tdQW", 1], [5, 1210.91, 1264.63], [-4.966, 283.128, 0, 0, 0, 0, 1, 0.35, 0.35, 1]], [26, "jackpot", 8, [-495], [0, "14T62kFbBDsKuLqzKlqHfC", 1]], [1, "dragon", 192, [[24, "default", "sanhrongdonghoa", 0, "sanhrongdonghoa", -496, [70], 71]], [0, "3ePz3LhrFJsLe16CP/xWdx", 1], [5, 1281.86, 1171.98], [-59.209, 317.943, 0, 0, 0, 0, 1, 0.15, 0.15, 1]], [1, "logo_tx", 8, [[24, "default", "animation", 0, "animation", -497, [72], 73]], [0, "47s8t6xshB/ZC27y2vYL1A", 1], [5, 544, 166.58], [3.725, 190.794, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "jackpot", 8, [[4, -498, [74], 75]], [0, "403YFrn6tIZqWnh1RojfFT", 1], [5, 290, 52], [-2.821, 178.144, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "Jackpot", false, 8, [[23, "default", "<PERSON> no hu", 0, false, "<PERSON> no hu", -499, [76], 77]], [0, "84jrNfhYlB8aXqkCfSTB3W", 1], [5, 1808, 1730.99], [0, -210, 0, 0, 0, 0, 1, 0.41, 0.4, 1]], [1, "icon_x3", 8, [[4, -500, [79], 80]], [0, "fbF3UIxEVDi4jVuahEvJBa", 1], [5, 92, 72], [107.654, 192.751, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnJackpotHis", 8, [[116, 1.1, -501, [[5, "d2f78nGJTNGm5ZkZaPiJU41", "jackpot<PERSON><PERSON><PERSON>Clicked", 5]]]], [0, "68S7CcEmFE85RMdeHfQk2E", 1], [5, 220, 50], [0, 184, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "rectangle5", 8, [[4, -502, [81], 82]], [0, "10GagSVehMw54Q4NGIqo+U", 1], [5, 326, 39], [-210, 14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "rectangle5", 8, [[4, -503, [83], 84]], [0, "dfNdEKdWBFpJia75z+74AZ", 1], [5, 326, 39], [210, 14, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "New Node", false, 8, [[122, "animation", 0, false, "animation", -504, [85], 86]], [0, "bbdxPhsGlEl4LixvyscGZX", 1], [5, 475.42, 447], [0, 177.388, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [1, "time_bg", 3, [[4, -505, [89], 90]], [0, "239dvsbx5ODZrSlwPIiUVI", 1], [5, 252, 252], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "audioChonSo", 2, [-506], [0, "1cUvVpNzBO+aOpgd7T9Yd2", 1]], [34, 203], [29, "BACKGROUND_SPRITE", 29, [-507], [0, "15iWADZV5NcL5xtXczQumv", 1], [5, 160, 40]], [53, 1, 0, 205], [48, "TEXT_LABEL", false, 0, 29, [-508], [0, "83BdZnRe1EnpJu4zMZqsnH", 1], [4, 16777215], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [59, 20, false, 1, 1, 207], [49, "PLACEHOLDER_LABEL", 0, 29, [-509], [0, "baP0ryPclNartQqiXPDp7z", 1], [4, 12303291], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [132, "Enter text here...", 20, false, 1, 1, 209, [133]], [64, 1, 9, 5, 29, [[5, "347b1xdyqpC24YIVRrVdPlw", "onTextXiuChange", 2]], [[5, "347b1xdyqpC24YIVRrVdPlw", "confirmClicked", 2]], 208, 210, 206], [29, "BACKGROUND_SPRITE", 30, [-510], [0, "8bnrzkqqdJGJY+3RAcbpi4", 1], [5, 174, 64]], [53, 1, 0, 212], [48, "TEXT_LABEL", false, 0, 30, [-511], [0, "bfiUqwD21DwaNDuCveWnhb", 1], [4, 16777215], [5, 172, 64], [0, 0, 1], [-85, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [59, 20, false, 1, 1, 214], [49, "PLACEHOLDER_LABEL", 0, 30, [-512], [0, "11F9nilixI0L6T1H0Ksgum", 1], [4, 12303291], [5, 172, 64], [0, 0, 1], [-85, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [133, "Enter text here...", 20, 64, false, 1, 1, 216, [134]], [64, 1, 9, 5, 30, [[5, "347b1xdyqpC24YIVRrVdPlw", "onTextTaiChange", 2]], [[5, "347b1xdyqpC24YIVRrVdPlw", "confirmClicked", 2]], 215, 217, 213], [1, "bgLbTai", 10, [[4, -513, [135], 136]], [0, "48Y7nvAqhM4qV0GTiUYMK+", 1], [5, 175, 55], [-247, -18.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgLbTai copy", 10, [[4, -514, [137], 138]], [0, "efxjyd7pxOGpP55bS/WtNF", 1], [5, 175, 55], [246, -18, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbInputBetTai", 10, [-515], [0, "a3h3SsRYtCYZaRR4GzllwJ", 1], [5, 13.5, 30], [-246, -12, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "0", 24, 30, false, false, 1, 1, 221, [139]], [2, "lbInputBetXiu", 10, [-516], [0, "9ephdnlrNMI7arxshZ5jDT", 1], [5, 13.5, 30], [248, -11, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "0", 24, 30, false, false, 1, 1, 223, [140]], [2, "lbBetTai", 10, [93], [0, "00VLJ6PTRN563H1yM8HKn/", 1], [5, 0, 26.46], [-245, -72.6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbBetXiu", 10, [94], [0, "75ku7w8N5KEJ44oQg/IIgz", 1], [5, 0, 26.46], [249, -71, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "txtNotify", 42, [95], [0, "d2N3JF/flAXq/mYOoXBWoG", 1], [5, 164.38, 0], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lua1", false, 11, [[142, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -517, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 156]], [0, "1f6OBAh1ZBYqZUU7xCjkxi", 1], [21.4, 77.6, 0, 0, 0, 0.6946583704589973, 0.7193398003386512, 0.3, 0.2, 1], [1, 0, 0, 88]], [15, "lua1 copy", false, 11, [[20, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -518, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 157]], [0, "8c/9i5SJlK2ounokFfwrbV", 1], [-18.1, 78.2, 0, 0, 0, 0.8290375725550417, 0.5591929034707468, 0.2, 0.2, 1], [1, 0, 0, 112]], [15, "lua1 copy", false, 11, [[20, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -519, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 158]], [0, "cewttrDrRNgKBkEjDoiCcl", 1], [-53.7, 57.1, 0, 0, 0, 0.9426414910921784, 0.3338068592337709, 0.2, 0.2, 1], [1, 0, 0, 141]], [15, "lua1 copy", false, 11, [[20, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -520, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 159]], [0, "08yipo9GdNu4TINT9VT4Ts", 1], [-20.9, -75.8, 0, 0, 0, -0.7431448254773942, 0.6691306063588582, 0.3, 0.2, 1], [1, 0, 0, -96]], [15, "lua1 copy", false, 11, [[20, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -521, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 160]], [0, "b54NYssnxEpoXhnovh05zu", 1], [20.4, -75.3, 0, 0, 0, -0.5591929034707469, 0.8290375725550416, 0.2, 0.2, 1], [1, 0, 0, -68]], [15, "lua1 copy", false, 11, [[20, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -522, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 161]], [0, "e4F/EbckZCAqiN+k8UKtJx", 1], [52.2, -58.4, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 0.2, 0.2, 1], [1, 0, 0, -45]], [1, "bg_soicau", 3, [[4, -523, [175], 176]], [0, "d8gM0bCopGt7Bv0Vcelw1E", 1], [5, 516, 28], [0, -143.6, 0, 0, 0, 0, 1, 1, 1, 1]], [83, "lbBigTimer", 12, [-524], [0, "aaVxHxrMlISLsP7U0bQf+c", 1], [-1, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [134, 36, 0, false, 1, 235, [177]], [2, "lbTotalBetTai", 12, [-525], [0, "aafQ7FPg5C0KnRPaCPXtbL", 1], [5, 20.63, 0], [-252, 9.6, 0, 0, 0, 0, 1, 1, 1, 1]], [61, "0", 12, 0, false, false, 2, 1, 1, 237, [178]], [2, "lbTotalBetXiu", 12, [-526], [0, "92n0z7S3ZEsb4k749KcWSf", 1], [5, 20.63, 0], [252, 9.6, 0, 0, 0, 0, 1, 1, 1, 1]], [61, "0", 12, 0, false, false, 2, 1, 1, 239, [179]], [2, "lbUserTai", 100, [-527], [0, "27Ux5EnV1MpI75T6A3tG7K", 1], [5, 10.13, 30], [0, 3.832, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "0", 18, 30, false, 1, 1, 241, [180]], [2, "lbUserXiu", 101, [-528], [0, "f2nlLCI5VNRJPSrDs+roGU", 1], [5, 10.13, 33], [0, 3.798, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "0", 18, 33, false, 1, 1, 243, [183]], [84, "lbSessionID", 12, [-529], [0, "48ttkS3jJLJZwcwl7CO7E/", 1], [5, 150, 0], [0, 0, 0.5], [144, 201, 0, 0, 0, 0, 1, 1, 1, 1]], [135, 22, 0, false, false, 1, 245, [186]], [28, "Label", 102, [[63, "<PERSON><PERSON><PERSON> ti<PERSON>n cân cửa", 20, false, 1, -530, [187], 188]], [0, "15ADNjiVdJjoYNeVImdLVv", 1], [5, 199.38, 40]], [28, "Label", 103, [[63, "<PERSON><PERSON><PERSON> đ<PERSON>u phiên mới", 20, false, 1, -531, [190], 191]], [0, "53lAFKhUdIrZlF1tceutla", 1], [5, 218.75, 25]], [2, "Dice1", 20, [-532], [0, "99RL4o1dFBs6oWJnpNYU1S", 1], [5, 322.37, 372.36], [-52.378, -45.566, 0, 0, 0, 0, 1, 0.31, 0.31, 1]], [55, "default", 1, 0, 1, 249, [194]], [2, "Dice2", 20, [-533], [0, "3fUqgMiQFJ/aLZLHa/McrJ", 1], [5, 322.37, 372.36], [49.879, -47.434, 0, 0, 0, 0, 1, 0.31, 0.31, 1]], [55, "default", 2, 0, 2, 251, [195]], [2, "Dice3", 20, [-534], [0, "adxqF4E0lLsb9UxqIP4i+r", 1], [5, 322.37, 372.36], [-1, -112.5, 0, 0, 0, 0, 1, 0.31, 0.31, 1]], [56, "default", 3, 0, false, 3, 253, [196]], [2, "effect", 20, [-535], [0, "2cl2CEdzZECrSQv0uK1AW7", 1], [5, 1347.04, 1545.94], [0, -91, 0, 0, 0, 0, 1, 0.25, 0.3, 1]], [56, "default", "effect", 0, false, "effect", 255, [197]], [118, null, 1, 135], [30, "rollDice", 15, [-536], [0, "edFQYe3spBboDvXVA3lhWx", 1]], [34, 258], [30, "winSound", 15, [-537], [0, "77znS6BGBImKz+a5Ni5GJP", 1]], [34, 260], [69, "result-label", false, 3, [-538], [0, "d8ZAAuaadHK4V5IEuqFD9n", 1]], [11, "lbTotalDice", false, 262, [-539], [0, "741Pr92TBKt7OXr1HLV33b", 1], [5, 0, 50.4], [115.8, 99.4, 0, 0, 0, 0, 1, 1, 1, 1]], [136, false, 28, false, 1, 1, 263, [200]], [85, "lbTimer", false, 32, [-540], [0, "b0QOgdpgRCTYViVi21Yemz", 1], [4, 4294769916], [5, 41.44, 48], [-1, -3, 0, 0, 0, 0, 1, 1, 1, 1]], [137, 12.5, 48, false, 1, 1, 265, [201]], [1, "Popup", 22, [[4, -541, [204], 205]], [0, "08a+NH+TtHC6qRqH0rDnnr", 1], [5, 860, 50], [1.516, -46.987, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "999,999 999", 21, 0, false, -1, 1, 1, 104, [206]], [80, "Win", 22, [[50, -542, 209]], [0, "cfcXXFPLVOQJ2bQ+Sw/rEk", 1]], [25, "999,999,999", 21, 0, false, -1, 1, 1, 105, [210]], [2, "txtresult", 44, [-543], [0, "5cMB6OMhVBuJGYji1q/RjK", 1], [5, 164.38, 0], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "<PERSON>n mời nặn", 18, 0, false, -1, 1, 1, 271, [213]], [32, 44, [214, 215]], [81, "particleBigWin", 33, [[143, 100, 1.5, 0.5, 58, 40, -1, 51, 500, 1, 850, 383, 0, 50, {"custom": false}, -544, [218], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 101, 73], [0, 0, -2400], 219, 220]], [0, "d1rBaK01lIPbFo/H/Yka+V", 1], [5.75, 309.83, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nohu", 33, [[23, "default", "idle", 0, false, "idle", -545, [221], 222]], [0, "88canO80tFHI4Is3xjoGO6", 1], [5, 2348.15, 1684.44], [-2.18143, 25.10286, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 13, 1, 0, 0, 1, 0, 11, 67, 0, 14, 3, 0, 10, 95, 0, 15, 244, 0, 16, 242, 0, 17, 240, 0, 18, 238, 0, 19, 266, 0, 20, 236, 0, 21, 246, 0, 12, 32, 0, 22, 11, 0, 0, 1, 0, 23, 94, 0, 24, 93, 0, 0, 1, 0, 25, 272, 0, 26, 273, 0, 27, 94, 0, 28, 93, 0, 11, 33, 0, 29, 259, 0, 30, 99, 0, 31, 98, 0, 32, 261, 0, -1, 97, 0, -1, 96, 0, 33, 264, 0, 34, 43, 0, 35, 256, 0, 36, 15, 0, -1, 250, 0, -2, 252, 0, -3, 254, 0, 12, 32, 0, 37, 21, 0, 38, 43, 0, 39, 20, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -7, 257, 0, 0, 1, 0, -1, 23, 0, -2, 106, 0, -3, 3, 0, 40, 211, 0, 41, 218, 0, 42, 204, 0, 10, 95, 0, 43, 224, 0, 44, 222, 0, 45, 31, 0, 46, 28, 0, 47, 7, 0, 48, 9, 0, 49, 18, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 203, 0, -2, 18, 0, -1, 34, 0, -2, 8, 0, -3, 202, 0, -4, 138, 0, -5, 14, 0, -7, 11, 0, -8, 159, 0, -9, 96, 0, -10, 97, 0, -11, 98, 0, -12, 99, 0, -13, 234, 0, -14, 4, 0, -15, 5, 0, -16, 12, 0, -17, 10, 0, -18, 15, 0, -19, 262, 0, -20, 32, 0, -21, 22, 0, -22, 42, 0, -23, 44, 0, -24, 33, 0, 0, 4, 0, -1, 190, 0, -2, 189, 0, -3, 187, 0, -4, 185, 0, -5, 183, 0, -6, 181, 0, -7, 179, 0, -8, 177, 0, -9, 175, 0, -10, 173, 0, -11, 171, 0, -12, 169, 0, -13, 167, 0, -14, 165, 0, -15, 163, 0, -16, 161, 0, 0, 4, 0, -1, 160, 0, -2, 162, 0, -3, 164, 0, -4, 166, 0, -5, 168, 0, -6, 170, 0, -7, 172, 0, -8, 174, 0, -9, 176, 0, -10, 178, 0, -11, 180, 0, -12, 182, 0, -13, 184, 0, -14, 186, 0, -15, 188, 0, -16, 39, 0, 50, 136, 0, 51, 134, 0, 52, 13, 0, 53, 23, 0, 0, 5, 0, -1, 56, 0, -2, 37, 0, -3, 57, 0, -4, 58, 0, -5, 59, 0, -6, 60, 0, -7, 61, 0, -8, 55, 0, -9, 137, 0, 0, 6, 0, -1, 48, 0, -2, 49, 0, -3, 50, 0, -4, 51, 0, -5, 52, 0, -6, 53, 0, -7, 54, 0, -8, 114, 0, -9, 116, 0, -10, 118, 0, -11, 120, 0, -12, 122, 0, -13, 124, 0, -14, 126, 0, 0, 7, 0, -1, 78, 0, -2, 79, 0, -3, 80, 0, -4, 81, 0, -5, 82, 0, -6, 83, 0, -7, 84, 0, -8, 85, 0, -9, 86, 0, -10, 87, 0, -11, 88, 0, -12, 89, 0, 0, 8, 0, -1, 191, 0, -2, 192, 0, -3, 194, 0, -4, 195, 0, -5, 196, 0, -6, 66, 0, -7, 197, 0, -8, 198, 0, -9, 199, 0, -10, 200, 0, -11, 201, 0, 0, 9, 0, -1, 68, 0, -2, 69, 0, -3, 70, 0, -4, 71, 0, -5, 72, 0, -6, 73, 0, -7, 74, 0, -8, 75, 0, -9, 76, 0, -10, 77, 0, -1, 29, 0, -2, 30, 0, -3, 219, 0, -4, 220, 0, -5, 221, 0, -6, 223, 0, -7, 225, 0, -8, 226, 0, -9, 28, 0, -10, 31, 0, 0, 11, 0, 0, 11, 0, -1, 228, 0, -2, 229, 0, -3, 230, 0, -4, 231, 0, -5, 232, 0, -6, 233, 0, -1, 235, 0, -2, 237, 0, -3, 239, 0, -4, 100, 0, -5, 101, 0, -6, 245, 0, -7, 102, 0, -8, 103, 0, 10, 158, 0, 54, 141, 0, 55, 148, 0, 56, 157, 0, 0, 13, 0, -1, 25, 0, -2, 38, 0, -3, 26, 0, -4, 16, 0, 57, 111, 0, 58, 113, 0, -1, 115, 0, -2, 117, 0, -3, 119, 0, -4, 121, 0, -5, 123, 0, -6, 125, 0, -7, 127, 0, 59, 129, 0, 60, 131, 0, 61, 108, 0, 62, 109, 0, 63, 132, 0, 64, 24, 0, -1, 46, 0, -2, 45, 0, -3, 36, 0, 0, 14, 0, -1, 46, 0, -2, 24, 0, -3, 36, 0, -1, 43, 0, -2, 20, 0, -3, 21, 0, -4, 258, 0, -5, 260, 0, -1, 156, 0, 0, 16, 0, -3, 157, 0, -1, 149, 0, -2, 63, 0, 0, 17, 0, 65, 151, 0, 66, 155, 0, 67, 153, 0, 68, 27, 0, 0, 17, 0, -1, 150, 0, -2, 27, 0, 0, 18, 0, -3, 19, 0, 0, 19, 0, -1, 90, 0, -2, 40, 0, -3, 91, 0, -4, 92, 0, -1, 249, 0, -2, 251, 0, -3, 253, 0, -4, 255, 0, 0, 21, 0, 8, 21, 0, 0, 21, 0, 69, 257, 0, 0, 21, 0, 70, 270, 0, 71, 268, 0, 0, 22, 0, -1, 267, 0, -2, 104, 0, -3, 269, 0, -4, 105, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 8, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, -1, 139, 0, -2, 62, 0, -1, 148, 0, -1, 142, 0, -2, 144, 0, -3, 146, 0, 0, 27, 0, -1, 152, 0, -2, 154, 0, 0, 28, 0, 8, 28, 0, 0, 28, 0, -1, 211, 0, -1, 205, 0, -2, 207, 0, -3, 209, 0, -1, 218, 0, -1, 212, 0, -2, 214, 0, -3, 216, 0, 0, 31, 0, 8, 31, 0, 0, 31, 0, 0, 32, 0, -1, 265, 0, 0, 33, 0, -1, 274, 0, -2, 275, 0, 0, 34, 0, -1, 107, 0, -2, 45, 0, 0, 35, 0, -1, 110, 0, -2, 112, 0, -1, 128, 0, -2, 130, 0, 0, 37, 0, 0, 37, 0, -1, 133, 0, -1, 141, 0, -1, 140, 0, -1, 190, 0, 0, 39, 0, -1, 65, 0, 0, 40, 0, 8, 41, 0, 0, 40, 0, -1, 41, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, -1, 227, 0, 0, 43, 0, -1, 273, 0, 0, 44, 0, -1, 271, 0, -1, 109, 0, -1, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, -1, 132, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, -1, 134, 0, -2, 135, 0, 0, 59, 0, 0, 59, 0, -1, 136, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, -1, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, -1, 67, 0, 72, 67, 0, 0, 66, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, -1, 241, 0, 0, 101, 0, -1, 243, 0, 0, 102, 0, -1, 247, 0, 0, 103, 0, -1, 248, 0, -1, 268, 0, 0, 104, 0, -1, 270, 0, 0, 105, 0, 0, 106, 0, -1, 108, 0, -1, 111, 0, -1, 113, 0, -1, 115, 0, -1, 117, 0, -1, 119, 0, -1, 121, 0, -1, 123, 0, -1, 125, 0, -1, 127, 0, -1, 129, 0, -1, 131, 0, 0, 133, 0, 0, 137, 0, 0, 139, 0, 0, 140, 0, -1, 143, 0, -1, 145, 0, -1, 147, 0, -1, 151, 0, -1, 153, 0, -1, 155, 0, -1, 161, 0, -1, 163, 0, -1, 165, 0, -1, 167, 0, -1, 169, 0, -1, 171, 0, -1, 173, 0, -1, 175, 0, -1, 177, 0, -1, 179, 0, -1, 181, 0, -1, 183, 0, -1, 185, 0, -1, 187, 0, -1, 189, 0, 0, 191, 0, -1, 193, 0, 0, 193, 0, 0, 194, 0, 0, 195, 0, 0, 196, 0, 0, 197, 0, 0, 198, 0, 0, 199, 0, 0, 200, 0, 0, 201, 0, 0, 202, 0, -1, 204, 0, -1, 206, 0, -1, 208, 0, -1, 210, 0, -1, 213, 0, -1, 215, 0, -1, 217, 0, 0, 219, 0, 0, 220, 0, -1, 222, 0, -1, 224, 0, 0, 228, 0, 0, 229, 0, 0, 230, 0, 0, 231, 0, 0, 232, 0, 0, 233, 0, 0, 234, 0, -1, 236, 0, -1, 238, 0, -1, 240, 0, -1, 242, 0, -1, 244, 0, -1, 246, 0, 0, 247, 0, 0, 248, 0, -1, 250, 0, -1, 252, 0, -1, 254, 0, -1, 256, 0, -1, 259, 0, -1, 261, 0, -1, 263, 0, -1, 264, 0, -1, 266, 0, 0, 267, 0, 0, 269, 0, -1, 272, 0, 0, 274, 0, 0, 275, 0, 73, 1, 2, 5, 3, 6, 5, 47, 7, 5, 18, 9, 5, 18, 13, 5, 138, 17, 5, 149, 35, 5, 47, 93, 0, 225, 94, 0, 226, 95, 0, 227, 158, 0, 159, 545], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 134, 136, 143, 147, 151, 153, 155, 161, 163, 165, 167, 169, 171, 173, 175, 177, 179, 181, 183, 185, 187, 189, 190, 132, 67, 204, 222, 224, 93, 94, 95, 236, 238, 240, 242, 244, 246, 250, 252, 254, 256, 259, 261, 264, 266, 268, 270, 272, 273], [-1, 1, -1, -2, -1, 1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, 74, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -2, -1, -2, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 6, -1, -1, -1, -2, -1, -1, -2, -1, 4, -1, 4, -1, 4, -1, 1, -1, 4, -1, -1, 1, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, 75, 76, 77, -1, 1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 6, -1, -2, -1, -1, -2, 9, 7, 7, 7, 7, 7, 7, -1, 6, -1, -2, -3, -1, 4, -1, 4, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 2, -1, -1, 2, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, 6, -1, 9, -1, 6, -1, -1, -1, -2, -1, 1, -1, 7, 1, -1, 4, -1, 4, 6, -1, -2, 78, 79, 80, 81, 82, 83, 1, 1, 1, 2, 84, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 9, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 4, 4, 4, 4, 9, 9, 2, 2, 2, 2, 2, 6], [0, 12, 45, 46, 0, 12, 1, 1, 1, 0, 0, 0, 47, 0, 48, 0, 49, 0, 50, 0, 0, 51, 0, 0, 52, 0, 53, 0, 54, 0, 0, 55, 0, 56, 0, 0, 0, 0, 0, 0, 57, 13, 8, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 0, 15, 16, 0, 17, 17, 1, 18, 1, 58, 1, 59, 0, 60, 1, 61, 0, 0, 62, 0, 19, 0, 19, 1, 63, 0, 64, 0, 65, 0, 66, 0, 0, 67, 0, 68, 0, 69, 0, 0, 70, 0, 71, 0, 72, 0, 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 0, 20, 74, 75, 76, 0, 77, 0, 78, 0, 0, 0, 21, 0, 21, 0, 0, 0, 0, 0, 79, 0, 80, 0, 9, 6, 6, 22, 0, 81, 82, 83, 3, 3, 3, 3, 3, 3, 0, 23, 23, 84, 85, 1, 24, 1, 24, 0, 86, 0, 87, 0, 88, 0, 0, 0, 0, 0, 89, 0, 0, 90, 0, 0, 25, 0, 0, 25, 0, 0, 1, 1, 1, 1, 0, 91, 0, 0, 0, 92, 0, 9, 0, 7, 7, 93, 0, 7, 7, 0, 6, 22, 0, 9, 0, 94, 95, 1, 96, 1, 18, 26, 26, 97, 98, 99, 100, 101, 102, 103, 13, 8, 104, 10, 105, 10, 10, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 15, 16, 106, 4, 107, 5, 5, 27, 27, 28, 4, 4, 4, 5, 5, 5, 11, 11, 11, 108, 109, 110, 111, 4, 29, 29, 28, 6]], [[[17, "EffectLogo", "\nEffectLogo.png\nsize: 777,775\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\n1\n  rotate: false\n  xy: 2, 15\n  size: 430, 309\n  orig: 430, 309\n  offset: 0, 0\n  index: -1\n2\n  rotate: true\n  xy: 466, 343\n  size: 430, 309\n  orig: 430, 309\n  offset: 0, 0\n  index: -1\nBlur\n  rotate: true\n  xy: 434, 146\n  size: 178, 134\n  orig: 178, 134\n  offset: 0, 0\n  index: -1\nLight\n  rotate: false\n  xy: 434, 24\n  size: 131, 120\n  orig: 131, 120\n  offset: 0, 0\n  index: -1\nefWin\n  rotate: false\n  xy: 2, 326\n  size: 462, 447\n  orig: 462, 447\n  offset: 0, 0\n  index: -1\n", ["EffectLogo.png"], {"skeleton": {"hash": "AyWi4pivKb8xR3EOWJxFzP14t74", "spine": "3.7.93", "width": 475.42, "height": 447, "images": "", "audio": ""}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "root"}, {"name": "bone3", "parent": "root"}, {"name": "bone4", "parent": "root"}, {"name": "bone5", "parent": "root", "x": -85.21, "y": 45.95}, {"name": "MASK", "parent": "root", "x": -270.11, "y": -26.42}, {"name": "bone6", "parent": "root", "x": -178.92, "y": 16.17}, {"name": "bone7", "parent": "root", "x": 147.26, "y": -71.55}, {"name": "bone8", "parent": "root", "x": -30.97, "y": -67.66}], "slots": [{"name": "efWin", "bone": "bone4", "attachment": "efWin"}, {"name": "1", "bone": "bone", "attachment": "1"}, {"name": "2", "bone": "bone2", "attachment": "2"}, {"name": "Light", "bone": "bone5", "attachment": "Light"}, {"name": "Light2", "bone": "bone6", "attachment": "Light"}, {"name": "Light3", "bone": "bone7", "attachment": "Light"}, {"name": "Light4", "bone": "bone8", "attachment": "Light"}, {"name": "MASK", "bone": "MASK", "attachment": "MASK"}, {"name": "Blur", "bone": "bone3", "attachment": "Blur"}], "skins": {"default": {"1": {"1": {"width": 430, "height": 309}}, "2": {"2": {"width": 430, "height": 309}}, "Blur": {"Blur": {"x": -247.83, "y": -5.62, "width": 178, "height": 134}}, "Light": {"Light": {"width": 131, "height": 120}}, "Light2": {"Light": {"width": 131, "height": 120}}, "Light3": {"Light": {"width": 131, "height": 120}}, "Light4": {"Light": {"width": 131, "height": 120}}, "MASK": {"MASK": {"type": "clipping", "end": "MASK", "vertexCount": 166, "color": "ce3a3a00", "vertices": [223.14, 55.8, 224.51, 47.62, 223.56, 43.56, 225.71, 41.25, 228.73, 39.73, 222.4, 36.91, 215.02, 31.37, 213.93, 31.4, 207.14, 22.76, 204.53, 14.54, 202.78, 1.89, 201.15, 5.26, 189.27, 20.97, 186.35, 22.17, 178.32, 12.6, 166.93, 12.5, 165.11, 11.6, 159.81, 2.09, 161.22, -0.88, 163.77, -4.4, 166.24, -4.53, 160.49, -15.1, 149.55, -14.67, 143.35, -4.95, 143.34, 4.11, 145.5, 11.81, 149.83, 20.72, 152.24, 24.54, 156.06, 28.5, 159.8, 32.41, 165.95, 35.87, 171.29, 37.18, 176.85, 38.15, 183.29, 36.99, 189.88, 33.87, 193.11, 29.57, 195.79, 24.41, 199.06, 23.14, 203.77, 23.32, 206.27, 25.3, 209.24, 27.24, 212.58, 31.88, 213.4, 35.63, 213.5, 40.6, 212.28, 46.71, 208.03, 53.18, 204.15, 55.76, 198.41, 57.42, 191.39, 58.9, 184.73, 59.46, 177.52, 59.27, 169.75, 58.72, 159.96, 56.8, 151.41, 54.36, 144.23, 51.01, 136.08, 44.78, 126.11, 33.86, 118.53, 22.52, 113.8, 7.45, 113.19, -5.53, 116.85, -18.1, 122.13, -26.14, 128.52, -32.56, 136.34, -35.38, 147.52, -37.11, 159.02, -38.05, 181.38, -34.27, 183.97, -32.18, 194.98, -9.14, 195.61, -5.5, 201.35, -4.79, 203.35, -3.77, 204.08, -8.81, 203.78, -14.19, 207.52, -23, 212.81, -29.79, 215.58, -29.95, 221.66, -34.39, 228.71, -38.33, 232.33, -40.06, 242.43, -41.19, 250.18, -39.87, 258.97, -36.43, 264.33, -33.39, 268.96, -29.8, 271.25, -28.75, 275.56, -22.92, 279.37, -15.51, 279.41, -12.71, 279.12, -9.87, 280.53, -3.22, 280.77, 5.65, 280.39, 11.64, 278.23, 18.68, 291.37, 21.38, 292.34, 17.5, 295.9, 13.57, 289.02, 8.18, 284.28, -18.99, 285.79, -24.24, 293.71, -35.84, 333.61, -36.21, 347.13, -25.44, 353.28, 3.11, 351.76, 7.2, 355.69, 7.32, 350.76, -21.45, 360.48, -36.06, 400.92, -35.84, 414.68, -25.16, 420.23, 1.44, 418.94, 7.53, 415.79, 11.78, 422.08, 16.76, 427.44, 43.91, 425.78, 49.27, 417.64, 61.27, 377.88, 61.09, 364.38, 50.19, 358.84, 21.57, 359.96, 17.14, 362.25, 13.85, 355.6, 7.62, 351.25, 7.53, 348.1, 11.82, 355.32, 17.5, 360.5, 45.21, 359.21, 48.9, 351.07, 60.53, 310.31, 60.35, 296.99, 49.64, 291.26, 21.57, 284.28, 20.19, 278.13, 18.98, 270.95, 30.89, 273.97, 32.79, 274.95, 35.93, 283.86, 45.39, 282.77, 45.92, 275.24, 45.36, 273.26, 45.57, 272.89, 48.01, 274.89, 54.7, 273.53, 55.16, 268.71, 51.3, 266.01, 49.47, 262.27, 49.3, 259.9, 52.92, 258.31, 57.31, 257.39, 62.79, 257.07, 67.04, 256.17, 67.92, 254.59, 67.74, 251.75, 63.13, 249.53, 60.32, 246.15, 57.57, 243.31, 55.89, 240.63, 57.35, 238.14, 59.44, 236.21, 61.08, 234.72, 63.19, 233.22, 62.86, 231.42, 57.75, 229.72, 55.6, 227.44, 55.61, 224.18, 57.21]}}, "efWin": {"efWin": {"width": 462, "height": 447}}}}, "animations": {"animation": {"slots": {"2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}]}, "Light": {"color": [{"time": 0, "color": "ffffffab"}, {"time": 0.3667, "color": "ffffffff"}]}, "Light2": {"color": [{"time": 0.5667, "color": "ffffffab"}, {"time": 0.9333, "color": "ffffffff"}]}, "Light3": {"color": [{"time": 1.3, "color": "ffffffab"}, {"time": 1.6667, "color": "ffffffff"}]}, "Light4": {"color": [{"time": 1.9, "color": "ffffffab"}, {"time": 2.2667, "color": "ffffffff"}]}}, "bones": {"bone4": {"rotate": [{"time": 0, "angle": 0}, {"time": 3, "angle": 90.3}], "scale": [{"time": 0, "x": 0.6, "y": 0.6}, {"time": 0.5, "x": 1, "y": 1}, {"time": 0.9333, "x": 0.8, "y": 0.8}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.9, "x": 0.8, "y": 0.8}, {"time": 2.5, "x": 1, "y": 1}, {"time": 3, "x": 0.6, "y": 0.6}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.7667, "angle": 96}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3667, "x": 1.1, "y": 1.1}, {"time": 0.7667, "x": 0, "y": 0}]}, "bone3": {"translate": [{"time": 0.6333, "x": 0, "y": 0}, {"time": 1.3667, "x": 504.79, "y": 0}]}, "bone6": {"rotate": [{"time": 0.5667, "angle": 0}, {"time": 1.3333, "angle": 96}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0}, {"time": 0.9333, "x": 1.1, "y": 1.1}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 1.3, "angle": 0}, {"time": 2.0667, "angle": 96}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0}, {"time": 1.6667, "x": 1.1, "y": 1.1}, {"time": 2.0667, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 1.9, "angle": 0}, {"time": 2.6667, "angle": 96}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9, "x": 0, "y": 0}, {"time": 2.2667, "x": 1.1, "y": 1.1}, {"time": 2.6667, "x": 0, "y": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [112]], [[{"name": "icon_x3", "rect": [0, 0, 92, 72], "offset": [0, 0], "originalSize": [92, 72], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [113]], [[{"name": "da<PERSON>oi", "rect": [3, 4, 65, 65], "offset": [0, -1], "originalSize": [71, 71], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [114]], [[{"name": "table", "rect": [0, 0, 893, 409], "offset": [0, 0], "originalSize": [893, 409], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [115]], [[{"name": "to", "rect": [1, 1, 245, 245], "offset": [0, 0], "originalSize": [247, 247], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [116]], [[{"name": "jackpot", "rect": [2, 2, 290, 52], "offset": [0, -1], "originalSize": [294, 54], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [117]], [[{"name": "nanDisableBlack", "rect": [0, 0, 65, 65], "offset": [0, 0], "originalSize": [65, 65], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [118]]]]