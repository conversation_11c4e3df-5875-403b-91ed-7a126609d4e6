[1, ["ecpdLyjvZBwrvm+cedCcQy", "5dsIbAWRxKLp5H1jMaR4Q8", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "0aXAuciR5NBKTqKVUlx9XA", "4b4clu0YdGvKu9WhXWkE9e", "94qTDfxx1Jdo+HoT1Ml9Vp", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_N$target", "_parent", "_defaultClip", "root", "slotsHistoryListView", "lbWin", "lbBet", "lbTime", "lbSessionID", "data"], [["cc.Node", ["_name", "_opacity", "_skewX", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 9, 1, 5, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "_N$normalColor", "clickEvents", "_N$target", "_N$pressedColor", "_N$disabledColor"], 1, 1, 5, 9, 1, 5, 5], ["cc.Label", ["_string", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_fontSize", "_enableWrapText", "_N$overflow", "_spacingX", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 12, 4, 5, 7], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "lbWin", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 1, 5, 5], ["cc.Layout", ["_enabled", "_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["94f0dcGWSRC9ovLsmjATU3F", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["c890fFIrOZAObefUnoFLaiq", ["node", "slotsHistoryListView"], 3, 1, 1]], [[5, 0, 1, 2], [0, 0, 5, 4, 3, 6, 7, 2], [3, 0, 5, 1, 6, 2, 3, 4, 7, 9, 10, 11, 9], [3, 0, 5, 1, 6, 2, 3, 4, 7, 9, 10, 9], [0, 0, 5, 8, 4, 3, 6, 7, 2], [4, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 6, 4, 5, 2], [1, 0, 3, 4, 5, 2], [8, 0, 1, 2, 3], [6, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 8, 3, 6, 7, 2], [0, 0, 5, 8, 3, 7, 2], [0, 0, 8, 4, 3, 6, 2], [0, 0, 2, 5, 4, 3, 9, 6, 7, 3], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 10, 7, 2], [7, 0, 1, 2, 3, 4, 5, 2], [1, 1, 0, 3, 4, 3], [1, 0, 2, 3, 4, 5, 3], [1, 1, 0, 2, 3, 4, 4], [2, 2, 3, 1], [2, 0, 1, 2, 4, 6, 7, 5, 3], [2, 0, 1, 2, 4, 3, 5, 3], [5, 1, 1], [3, 0, 1, 2, 8, 3, 4, 9, 10, 11, 7], [9, 0, 1, 2, 3, 4, 5, 6, 1], [10, 0, 1, 2, 3, 4, 5, 5], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 3, 4, 5, 4], [14, 0, 1], [15, 0, 1, 2, 1], [16, 0, 1, 1]], [[9, "cb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [10, "cb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", [-5, -6, -7, -8, -9], [[33, -2, [28, 29], 27], [34, -4, -3]], [25, -1]], [14, "<PERSON><PERSON>", [-15, -16, -17, -18, -19], [[27, -14, -13, -12, -11, -10, [4, 4278246399], [4, 4294829568]]], [0, "beJegR6bxLHJtBQZypQhNr", 1], [5, 780, 37]], [4, "spriteBGTitle", 1, [-21, -22, -23, -24, -25], [[21, 1, 0, false, -20, [19]]], [0, "2cXqrYAkZNV4RG/bPID05n", 1], [5, 940, 38], [-10, 170, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scrollview", [-29, -30], [[-26, -27, [32, -28]], 1, 1, 4], [0, "771/3CHz9C3Knd3ggWgEPw", 1], [5, 804, 370], [20, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nen popup copy", 1, [-32, -33], [[7, 0, -31, [5], 6]], [0, "0fWADY9bpF2qX8c967+4wO", 1], [5, 1000, 554], [-9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 1, [[20, 2, false, -34, [7], 8], [23, 0.9, 3, -36, [[8, "c890fFIrOZAObefUnoFLaiq", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -35]], [0, "27NJiHzo5EHoie0wRndIXO", 1], [5, 83, 75], [439.377, 227.097, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbDesc", 10, 2, [[2, "<PERSON>em chi tiết", 20, 48, false, false, 1, 1, 1, -37, [24], 25], [24, 1.1, 3, -39, [[8, "98b467lg1tGSIOSS7iaiI4s", "openDetailClicked", 2]], [4, 4292269782], -38]], [0, "fbB6yOqUtCcoa38D1e+lEc", 1], [4, 4278246399], [5, 150, 30], [319, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "black", 100, 1, [[7, 0, -40, [0], 1], [22, -41, [4, 4292269782]]], [0, "c6jnBvuFNN96Tw4YnpA2Z/", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "view", 4, [-43], [[29, 0, -42, [26]]], [0, "c7K/0qF/lHwIhA4rfTxTjJ", 1], [5, 803, 370]], [17, "content", 9, [[28, false, 1, 2, 10, -44, [5, 1190, 75]]], [0, "d0QHbTB4lPwYfAabko8NgJ", 1], [5, 803, 75], [0, 0.5, 1], [0, 182, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tile_lichsu", 5, [[26, "LỊCH SỬ ", 50, false, 2, 1, 1, -45, [2], 3]], [0, "33zZalE51Leoz2t6zhpam7", 1], [5, 160.61, 63], [15.496, 228.377, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lb_mail_bg", 5, [[19, 1, 0, -46, [4]]], [0, "a5FKeP2cZJz4pG3iauJvj/", 1], [5, 935, 421], [-1, -33, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbSessionID", 3, [[2, "PHIÊN", 10, 30, false, false, 1, 1, 1, -47, [9], 10]], [0, "d9itxtVmFOcqfJFORRyYV/", 1], [5, 100, 30], [-318, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 3, [[2, "THỜI GIAN", 10, 30, false, false, 1, 1, 1, -48, [11], 12]], [0, "e4I1L/QXNAs5UJmulTbTc6", 1], [5, 100, 30], [-148, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 3, [[2, "CƯỢC", 10, 30, false, false, 1, 1, 1, -49, [13], 14]], [0, "47K7OUO0lIl6bhXQqKdoAQ", 1], [5, 100, 30], [22, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 3, [[2, "THẮNG", 10, 30, false, false, 1, 1, 1, -50, [15], 16]], [0, "7axeC1fMVHl7KAkvsjENsG", 1], [5, 100, 30], [165, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 3, [[2, "CHI TIẾT", 10, 30, false, false, 1, 1, 1, -51, [17], 18]], [0, "1eWpTz7YZGv69Cllip6CLY", 1], [5, 100, 30], [319, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg<PERSON><PERSON>nt", 1, [4], [0, "acEr/7v65LYKSkuwbdI8ec", 1], [5, 780, 370], [-24, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "temp", 4, [2], [0, "17rZcmypJElbDhu8sMZNGA", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSessionID", 2, [-52], [0, "2dFlliM0xH5JD15NSAcc7q", 1], [5, 150, 30], [-318, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "#66553620", 20, 48, false, false, 1, 1, 1, 20, [20]], [5, "lbTime", 2, [-53], [0, "7fTOunVslLwqAdNcKCZ82X", 1], [5, 200, 30], [-148, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "17:03 23-03-2019", 20, 48, false, false, 1, 1, 1, 22, [21]], [6, "lbBet", 2, [-54], [0, "4aZKGGCD5IXonW2wsd/BNw", 1], [4, 4278255615], [5, 200, 30], [22, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "250.000", 20, 48, false, false, 1, 1, 1, 24, [22]], [6, "lbWin", 2, [-55], [0, "3abt3t0GVBQLPGy3Fg/Aah", 1], [4, 4278255615], [5, 200, 30], [165, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "50.000.000", 20, 48, false, false, 1, 1, 1, 26, [23]], [30, false, 0.75, 0.23, null, null, 4, 10], [31, 15, 10, 400, 4, 2, 28]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 29, 0, 0, 1, 0, -1, 8, 0, -2, 5, 0, -3, 6, 0, -4, 3, 0, -5, 18, 0, 8, 27, 0, 9, 25, 0, 10, 23, 0, 11, 21, 0, 0, 2, 0, -1, 20, 0, -2, 22, 0, -3, 24, 0, -4, 26, 0, -5, 7, 0, 0, 3, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 16, 0, -5, 17, 0, -1, 28, 0, -2, 29, 0, 0, 4, 0, -1, 19, 0, -2, 9, 0, 0, 5, 0, -1, 11, 0, -2, 12, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, 0, 7, 0, 3, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, -1, 21, 0, -1, 23, 0, -1, 25, 0, -1, 27, 0, 12, 1, 2, 4, 19, 4, 4, 18, 55], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 23, 25, 27], [-1, 2, -1, 1, -1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 1, -1, 5, -1, -2, 1, 1, 1, 1], [0, 4, 0, 5, 0, 0, 6, 0, 7, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 0, 2, 0, 3, 3, 8, 2, 2, 2, 2]]