[1, ["53Poe1kl9BdZ37l1tukGpX"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "ani_jackpot", "\nani_jackpot.png\nsize: 454,167\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nbb\n  rotate: false\n  xy: 400, 53\n  size: 17, 17\n  orig: 17, 17\n  offset: 0, 0\n  index: -1\nitem_jackpot\n  rotate: true\n  xy: 2, 2\n  size: 163, 189\n  orig: 163, 189\n  offset: 0, 0\n  index: -1\njp_face\n  rotate: true\n  xy: 313, 4\n  size: 66, 85\n  orig: 66, 85\n  offset: 0, 0\n  index: -1\njp_t1\n  rotate: true\n  xy: 193, 10\n  size: 91, 118\n  orig: 91, 118\n  offset: 0, 0\n  index: -1\njp_t2\n  rotate: false\n  xy: 354, 72\n  size: 98, 93\n  orig: 98, 93\n  offset: 0, 0\n  index: -1\ntext_jp\n  rotate: false\n  xy: 193, 103\n  size: 159, 62\n  orig: 159, 62\n  offset: 0, 0\n  index: -1\n", ["ani_jackpot.png"], {"skeleton": {"hash": "R/d/rf/S+noaj5rLikw04rM3YKg", "spine": "3.6.53", "width": 163, "height": 191.08}, "bones": [{"name": "root"}, {"name": "jp_face", "parent": "root", "length": 42.04, "rotation": 94.4, "x": 1.54, "y": -53.66}, {"name": "bb", "parent": "jp_face", "x": 27.23, "y": -33.88}, {"name": "bb2", "parent": "jp_face", "x": 21.84, "y": 54.86}, {"name": "bb3", "parent": "jp_face", "x": 40.11, "y": 43.9}, {"name": "bb4", "parent": "jp_face", "x": 51.46, "y": 61.91, "scaleX": 0.871, "scaleY": 0.871}, {"name": "jp_face3", "parent": "jp_face", "length": 59.26, "rotation": -22.44, "x": 45, "y": 0.21}, {"name": "jp_t1", "parent": "jp_face3", "length": 32.88, "rotation": 20.85, "x": 48.03, "y": -34.14}, {"name": "jp_t2", "parent": "jp_t1", "length": 20.62, "rotation": -17.28, "x": 32.88}, {"name": "jp_t3", "parent": "jp_t2", "length": 16.6, "rotation": -57.45, "x": 21.01, "y": -0.23}, {"name": "jp_t4", "parent": "jp_face3", "length": 25.83, "rotation": -172.01, "x": 16.23, "y": 35.43}, {"name": "text_jp", "parent": "jp_face", "length": 74.58, "rotation": -82.94, "x": -9.26, "y": 29.57}], "slots": [{"name": "item_jackpot", "bone": "jp_face", "attachment": "item_jackpot"}, {"name": "item_jackpot2", "bone": "jp_face", "color": "ffffff00", "attachment": "item_jackpot", "blend": "additive"}, {"name": "jp_t1", "bone": "jp_t1", "attachment": "jp_t1"}, {"name": "jp_face", "bone": "jp_face3", "attachment": "jp_face"}, {"name": "jp_t2", "bone": "jp_t4", "attachment": "jp_t2"}, {"name": "bb", "bone": "bb", "color": "ffffff00", "attachment": "bb"}, {"name": "bb2", "bone": "bb2", "color": "ffffff00"}, {"name": "bb3", "bone": "bb3", "color": "ffffff00", "attachment": "bb"}, {"name": "bb4", "bone": "bb4", "color": "ffffff00", "attachment": "bb"}, {"name": "text_jp", "bone": "text_jp", "attachment": "text_jp"}], "skins": {"default": {"bb": {"bb": {"x": -0.53, "y": -0.36, "rotation": -94.4, "width": 17, "height": 17}}, "bb2": {"bb": {"x": -0.53, "y": -0.36, "rotation": -94.4, "width": 17, "height": 17}}, "bb3": {"bb": {"x": -0.53, "y": -0.36, "rotation": -94.4, "width": 17, "height": 17}}, "bb4": {"bb": {"x": -0.53, "y": -0.36, "rotation": -94.4, "width": 17, "height": 17}}, "item_jackpot": {"item_jackpot": {"x": 53.62, "y": -2.58, "rotation": -94.4, "width": 163, "height": 189}}, "item_jackpot2": {"item_jackpot": {"x": 53.62, "y": -2.58, "rotation": -94.4, "width": 163, "height": 189}}, "jp_face": {"jp_face": {"x": 22.6, "y": 3.19, "rotation": -71.96, "width": 66, "height": 85}}, "jp_t1": {"jp_t1": {"type": "mesh", "hull": 4, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-49.41, -33.47, -44.95, 57.42, 72.9, 51.64, 68.45, -39.25]}}, "jp_t2": {"jp_t2": {"x": -10.33, "y": 7.02, "rotation": 100.05, "width": 98, "height": 93}}, "text_jp": {"text_jp": {"x": 28.86, "y": 3.68, "rotation": -11.46, "width": 159, "height": 62}}}}, "animations": {"animation": {"slots": {"bb": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}]}, "bb2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}]}, "bb3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}]}, "bb4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}, {"time": 0.5, "name": "bb"}]}, "item_jackpot2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffff1b"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "item_jackpot"}, {"time": 0.6667, "name": "item_jackpot"}, {"time": 1.3333, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_face": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_face3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 6.41}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.91, "y": -2.18}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.086, "y": 1}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": -1.28}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": -1.54, "y": 6.74}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 0.987, "y": 1.161}, {"time": 1.3333, "x": 1, "y": 1}]}, "text_jp": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 7.33, "y": -3.33}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "bb": {"translate": [{"time": 0, "x": -25.02, "y": 3.91}, {"time": 0.3333, "x": -7.69, "y": 0.09}, {"time": 0.6667, "x": 0.56, "y": -5.38}, {"time": 1, "x": 12.19, "y": -9.38}, {"time": 1.3333, "x": 27.75, "y": -7.1}], "scale": [{"time": 0, "x": 0.333, "y": 0.333}, {"time": 0.6667, "x": 0.679, "y": 0.679}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}, "bb2": {"translate": [{"time": 0, "x": -25.02, "y": 3.91}, {"time": 0.3333, "x": -7.69, "y": 0.09}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1, "x": 12.19, "y": -9.38}, {"time": 1.3333, "x": 27.75, "y": -7.1}], "scale": [{"time": 0, "x": 0.333, "y": 0.333}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}, "bb3": {"translate": [{"time": 0, "x": 9.45, "y": 6.31}, {"time": 0.3333, "x": 18.04, "y": -2.08}, {"time": 0.6667, "x": 37.59, "y": 3.48}, {"time": 1, "x": 53.19, "y": -8.81}, {"time": 1.3333, "x": 78.89, "y": -14.51}], "scale": [{"time": 0, "x": 0.333, "y": 0.333}, {"time": 0.6667, "x": 0.641, "y": 0.641}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}, "bb4": {"translate": [{"time": 0, "x": -25.02, "y": 3.91}, {"time": 0.5, "x": -15.85, "y": -22.49}, {"time": 1, "x": 3.58, "y": 0.46}, {"time": 1.3333, "x": 30.67, "y": -8.95}], "scale": [{"time": 0, "x": 0.333, "y": 0.333, "curve": "stepped"}, {"time": 0.5, "x": 0.333, "y": 0.333}, {"time": 1, "x": 0.659, "y": 0.659}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}}}, "win": {"slots": {"bb": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}]}, "bb2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}]}, "bb3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}]}, "bb4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "bb"}, {"time": 0.5, "name": "bb"}]}, "item_jackpot2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffff41"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffff2a"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "item_jackpot"}, {"time": 0.3333, "name": "item_jackpot"}, {"time": 0.6667, "name": "item_jackpot"}, {"time": 1, "name": "item_jackpot"}, {"time": 1.3333, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t4": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6667, "angle": 351.31}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": -3.4, "y": 6.44}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.6667, "x": 0.946, "y": 1.04}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_face": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 0, "y": 15.71}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1, "x": 0, "y": 10.47}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_face3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "text_jp": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6667, "x": 7.33, "y": -3.33}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}]}, "jp_t1": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.4, "angle": 6.41}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4, "x": 1.91, "y": -2.18}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 1.086, "y": 1}, {"time": 1.3333, "x": 1, "y": 1}]}, "bb3": {"translate": [{"time": 0, "x": 9.45, "y": 6.31}, {"time": 0.3333, "x": 18.04, "y": -2.08}, {"time": 0.6667, "x": 37.59, "y": 3.48}, {"time": 1, "x": 53.19, "y": -8.81}, {"time": 1.3333, "x": 78.89, "y": -14.51}], "scale": [{"time": 0, "x": 0.333, "y": 0.333}, {"time": 0.6667, "x": 0.641, "y": 0.641}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}, "bb4": {"translate": [{"time": 0, "x": -25.02, "y": 3.91}, {"time": 0.5, "x": -15.85, "y": -22.49}, {"time": 1, "x": 3.58, "y": 0.46}, {"time": 1.3333, "x": 30.67, "y": -8.95}], "scale": [{"time": 0, "x": 0.333, "y": 0.333, "curve": "stepped"}, {"time": 0.5, "x": 0.333, "y": 0.333}, {"time": 1, "x": 0.659, "y": 0.659}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}, "bb": {"translate": [{"time": 0, "x": -25.02, "y": 3.91}, {"time": 0.3333, "x": -7.69, "y": 0.09}, {"time": 0.6667, "x": 0.56, "y": -5.38}, {"time": 1, "x": 12.19, "y": -9.38}, {"time": 1.3333, "x": 27.75, "y": -7.1}], "scale": [{"time": 0, "x": 0.333, "y": 0.333}, {"time": 0.6667, "x": 0.679, "y": 0.679}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}, "bb2": {"translate": [{"time": 0, "x": -25.02, "y": 3.91}, {"time": 0.3333, "x": -7.69, "y": 0.09}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 1, "x": 12.19, "y": -9.38}, {"time": 1.3333, "x": 27.75, "y": -7.1}], "scale": [{"time": 0, "x": 0.333, "y": 0.333}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1.3333, "x": 0.425, "y": 0.425}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]