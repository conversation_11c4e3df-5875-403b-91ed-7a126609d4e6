[1, ["05QyQLkglB6YPEjcHT7gYS", "dbUhEgNelEXY1H4X7aYon1", "2cz4Jm8iFLrZO71JMhbskJ", "35V5IS5j5OwIsvFGTlAT0i", "0479rDjt5M+YIfcIM3llco", "4a6S6b1WhFrJihqf7U5fs3", "faA0C2p+1DjbSL8UljwKVj", "1eAh+E1i5BZLPxZYsbDWPO", "b03N7HPApNLI6Gbhns57kW", "d0sTIo61xEhL4WBNiRi6U/", "7fiZmSlW5DMKkFk3TIkTTH", "29nz+h18NDioixfDMvP4t1", "88VEhTFthOx49+rKcgtRfx", "e75WpzC9FG8IZtxjrV+vFt", "53H3OqaYBLgr7NQ8146W5n", "c1u2T+yLBGpLEvF4fNZkn1", "1cdAvOgKdJZJ1z2Nghf/TQ", "6aiTsODp9Jwp27JTX70ALu", "deP5pWqTNE8bOMGALD1V0w", "05e7Y4it1FcpKDIc2ipITv", "d0Ygv6W5pAQK50GYiCE3eV", "3emq9ptj5N+ba5YFejdAqa", "17JQBvSytHKYq/UGYW2/JY", "29BGPcJ4tMnpIoh8LO05DM", "64WmlSKKNLVJkrO1+J6pxY", "c0SFMT4kpCk41VYL1c+59r", "b2YUy8nmdKJ7QDIybJ6DKF", "7dkL41E3dN+r3haIGCgVw9", "3a7sIT5PRKFoH2AGouF8Lw", "a7bjo7Ws1Ijbm0JEKzOfgp", "7eTEnW0A9Hy6V3Nfe12txs", "594GQWWidBL6vegyWuSjbI"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "wrapMode", "curveData"], -1, 11]], [[1, 0, 1, 2, 3, 4, 5]], [[[{"name": "24_00003", "rect": [2, 0, 149, 354], "offset": [-4.5, 1], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "fish22", 1.0666666666666667, 15, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.06666666666666667}, "value", 6, 1], [{"frame": 0.13333333333333333}, "value", 6, 2], [{"frame": 0.2}, "value", 6, 3], [{"frame": 0.26666666666666666}, "value", 6, 4], [{"frame": 0.3333333333333333}, "value", 6, 5], [{"frame": 0.4}, "value", 6, 6], [{"frame": 0.4666666666666667}, "value", 6, 7], [{"frame": 0.5333333333333333}, "value", 6, 8], [{"frame": 0.6}, "value", 6, 9], [{"frame": 0.6666666666666666}, "value", 6, 10], [{"frame": 0.7333333333333333}, "value", 6, 11], [{"frame": 0.8}, "value", 6, 12], [{"frame": 0.8666666666666667}, "value", 6, 13], [{"frame": 0.9333333333333333}, "value", 6, 14], [{"frame": 1}, "value", 6, 15]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]], [[{"name": "24_00015", "rect": [0, 0, 160, 353], "offset": [-1, 1.5], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [17]], [[{"name": "24_00006", "rect": [5, 0, 133, 354], "offset": [-9.5, 1], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [18]], [[{"name": "24_00010", "rect": [5, 0, 133, 351], "offset": [-9.5, 2.5], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [19]], [[{"name": "24_00001", "rect": [0, 0, 160, 352], "offset": [-1, 2], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [20]], [[{"name": "24_00002", "rect": [1, 0, 155, 353], "offset": [-2.5, 1.5], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [21]], [[{"name": "24_00004", "rect": [3, 0, 142, 354], "offset": [-7, 1], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [22]], [[{"name": "24_00013", "rect": [2, 0, 149, 355], "offset": [-4.5, 0.5], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [23]], [[{"name": "24_00009", "rect": [6, 0, 129, 350], "offset": [-10.5, 3], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [24]], [[{"name": "24_00011", "rect": [5, 0, 136, 352], "offset": [-8, 2], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [25]], [[{"name": "24_00007", "rect": [6, 0, 129, 352], "offset": [-10.5, 2], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [26]], [[{"name": "24_00014", "rect": [1, 0, 154, 355], "offset": [-3, 0.5], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [27]], [[{"name": "24_00008", "rect": [6, 0, 128, 348], "offset": [-11, 4], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [28]], [[{"name": "24_00000", "rect": [0, 0, 162, 350], "offset": [0, 3], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [29]], [[{"name": "24_00012", "rect": [3, 0, 142, 352], "offset": [-7, 2], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [30]], [[{"name": "24_00005", "rect": [4, 0, 137, 355], "offset": [-8.5, 0.5], "originalSize": [162, 356], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [31]]]]