[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "d82n49/IVAvIEqsa0xvvk0", "017Jn3Zv1Ft7hygdjpaSoK", "a9VpD0DP5LJYQPXITZq+uj", "ddfdBtdMxOaY5yV5m1g5/e", "e6tW9cddpJzJGEf+rPpeFE", "fdNoodJKVLj4dF1TLppv2g", "e2fhlgUYxHPq5uMXIQ8k6C", "72HFa4Z7xBxYeqensc5bOJ", "24xd2Xl+xHVZeWwPN10Wzf", "caUKdVdJZJ3ZmlffmMO1Qe", "65rjRcORdDtLdCkQC3pBBz", "2fJXNQqKlFTahBD/6BNnad", "f64vlRucZB84F8VvN+09t8", "43j3tIST1PdZpzrpYe2gGd", "16TB0wJLVNmL/IjOjgJOxA", "179E9vi6pOArac0zC0jqyh", "07akvZ2PZARZ0yAWY3KXMn", "2cWB/vWPRHja3uQTinHH30", "97DM/PPnlBB4uUR3lxJ+1v"], ["node", "_N$file", "_spriteFrame", "_N$target", "_parent", "root", "lbCurrentOpenDate", "layoutOpenDate", "animListOpenDate", "topList<PERSON>iew", "spriteTop", "lbTotalWin", "lbNickName", "lbRank", "lbBet", "lbTime", "lbSessionID", "data", "_defaultClip", "itemOpenDate"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_children", "_color"], 2, 1, 2, 4, 5, 7, 5, 2, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_enableWrapText", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$normalColor", "_N$target", "_N$pressedColor", "_N$disabledColor"], 1, 1, 9, 5, 1, 5, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingTop", "_N$paddingBottom", "_N$spacingX", "node", "_layoutSize"], -3, 1, 5], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 0], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cbe4bGTJl9GDqP84v8sP/jy", ["node", "topList<PERSON>iew", "animListOpenDate", "layoutOpenDate", "lbCurrentOpenDate", "itemOpenDate"], 3, 1, 1, 1, 1, 1, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["aaa06RFncdFZZV5TQrggruE", ["node", "lbRank", "lbNickName", "lbTotalWin", "spriteTop", "spTop"], 3, 1, 1, 1, 1, 1, 3], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 5, 5], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["1801f0i6epEKZsCs8TxYi+A", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[11, 0, 1, 2], [0, 0, 5, 3, 2, 4, 7, 2], [1, 0, 3, 4, 5, 2], [3, 0, 1, 3, 4, 2, 5, 8, 9, 10, 7], [2, 0, 1, 2, 3, 4, 5, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [14, 0, 1, 2, 3], [3, 0, 1, 3, 4, 2, 6, 5, 8, 9, 8], [7, 0, 1, 2, 4], [0, 0, 1, 5, 3, 2, 4, 7, 3], [2, 0, 1, 2, 3, 8, 4, 5, 2], [1, 2, 0, 3, 4, 5, 3], [4, 2, 4, 1], [3, 0, 1, 3, 4, 2, 6, 5, 7, 8, 9, 9], [3, 0, 1, 3, 4, 2, 5, 8, 9, 7], [8, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 6, 3, 2, 4, 8, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 2], [0, 0, 5, 6, 2, 2], [0, 0, 5, 6, 3, 2, 4, 8, 7, 2], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 8, 7, 2], [0, 0, 5, 6, 2, 7, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 1, 7, 2, 3, 4, 6, 5, 2], [2, 0, 1, 2, 3, 4, 6, 2], [2, 0, 1, 2, 3, 4, 6, 5, 2], [6, 0, 1, 2, 1], [6, 0, 1, 1], [10, 0, 1, 2, 3, 4, 5, 1], [1, 2, 0, 1, 3, 4, 5, 4], [1, 1, 3, 4, 5, 2], [1, 0, 3, 4, 2], [1, 0, 1, 3, 4, 5, 3], [5, 0, 1, 3, 4, 2, 6, 7, 6], [5, 0, 1, 5, 6, 7, 4], [5, 0, 1, 2, 6, 7, 4], [12, 0, 1, 2, 3, 4, 5, 1], [13, 0, 1], [4, 1, 0, 2, 3, 4, 5, 3], [4, 0, 2, 3, 6, 7, 5, 2], [4, 2, 3, 4, 5, 1], [15, 0, 1, 2, 3, 4, 5, 1], [16, 0, 1, 1], [3, 0, 1, 2, 8, 9, 10, 4], [17, 0, 1, 2, 3, 4, 5, 6, 6], [18, 0, 1, 2, 3, 4, 5, 4]], [[[[8, "hideDropdownMenu", 0.08333333333333333, {"props": {"scaleY": [{"frame": 0, "value": 1}, {"frame": 0.08333333333333333, "value": 0}], "opacity": [{"frame": 0, "value": 0}]}, "paths": {"btnCancel": {"props": {"active": [{"frame": 0, "value": false}]}}}}]], 0, 0, [], [], []], [[[8, "showDropdownMenu", 0.2, {"props": {"scaleY": [{"frame": 0, "value": 0}, {"frame": 0.13333333333333333, "value": 1.1}, {"frame": 0.2, "value": 1}], "opacity": [{"frame": 0, "value": 255}]}, "paths": {"btnCancel": {"props": {"active": [{"frame": 0, "value": true}]}}}}]], 0, 0, [], [], []], [[[15, "LoDeTopView"], [16, "LoDeTopView", [-8, -9, -10, -11, -12, -13, -14, -15, -16, -17], [[29, -2, [47, 48], 46], [31, -7, -6, -5, -4, -3, 49]], [0, "c2QKeOmihDC7QgdmUxCkJo", -1]], [17, "wrapOpenDate", [-20, -21, -22, -23, -24], [[11, 1, 0, -18, [42], 43], [36, 1, 2, 10, 10, 10, -19, [5, 200, 197.5]]], [0, "c32+0ZC6NGT4JobbwRAME0", 1], [5, 200, 197.5], [0, 0.5, 1]], [18, "<PERSON><PERSON>", [-30, -31, -32, -33], [[39, -29, -28, -27, -26, -25, [21, 22, 23]]], [0, "ea7vNbVVFHTpZ33ZWmC6JH", 1], [5, 994, 50]], [25, "scrollview", 1, [-37, -38], [[-34, [40, -35], -36], 1, 4, 1], [0, "15sN1bqapB74f/eNrJcfCf", 1], [5, 1020, 470], [2, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnOpenDate", 1, [-42, -43], [[11, 1, 0, -39, [28], 29], [41, 1.1, 3, -41, [[6, "cbe4bGTJl9GDqP84v8sP/jy", "openListOpenDate", 1]], [4, 4292269782], -40]], [0, "81qnwA5cNH15KDCIit6SaC", 1], [5, 200, 54], [-408.7, 265, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "title", 1, [-48, -49, -50], [[44, -47, -46, -45, -44, [4, 4278246399], [4, 4294829568]]], [0, "4fyStNkNlL0JsV3MeBtcbr", 1], [5, 994, 50], [0, 214, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnClose", 1, [-53], [[42, 3, -52, [[6, "cbe4bGTJl9GDqP84v8sP/jy", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -51]], [0, "29LQDI2nxF+IAK1kavHaMS", 1], [5, 80, 80], [522, 291, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "layoutOpenDate", 1, [-55, 2], [-54], [0, "60d7GEHFxBhYwXuAJp5ZrB", 1], [5, 200, 0], [0, 0.5, 1], [-407.9, 244, 0, 0, 0, 0, 1, 1, 0, 1]], [9, "black", 0, 8, [[2, 0, -56, [30], 31], [43, -58, [[6, "cbe4bGTJl9GDqP84v8sP/jy", "hideListOpenDate", 1]], [4, 4292269782], -57]], [0, "71r9c3FrdFRZJu6mP0eEDa", 1], [5, 9999, 9999], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "black", 100, 1, [[2, 0, -59, [0], 1], [12, -60, [4, 4292269782]]], [0, "deNB8kZDhA6q0r2Hfq4iTI", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "nen popup", 1, [[32, 1, 0, false, -61, [2], 3], [12, -62, [4, 4292269782]]], [0, "4cfiJBfKFBu6WQpHXgIBjJ", 1], [5, 1084, 618]], [20, "line", 1, [-63, -64], [0, "04SHlUvSBAUYlcQdduKBn3", 1]], [21, "layout-nick<PERSON><PERSON>", 3, [-66], [[37, 1, 1, 5, -65, [5, 114, 50]]], [0, "0c9O9TRcpNb4p20zjV4W43", 1], [5, 114, 50], [0, 0, 0.5], [-92, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "view", 4, [-68], [[45, -67, [24]]], [0, "d4lQKQB7VG2YTBRN1Ik7SK", 1], [5, 1020, 470]], [23, "content", 14, [[38, 1, 2, 10, -69, [5, 1000, 0]]], [0, "2cYLTqV+1NJJew5ZOW+VHk", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_content", 1, [[2, 0, -70, [4], 5]], [0, "c7OY45vB5IqY2bJ5zO5qsu", 1], [5, 1025, 539], [3, -24, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title_BXH", 1, [[46, "BẢNG XẾP HẠNG", 22, false, -71, [6], 7]], [0, "8a8yvmLndIYKgbuoHnkZaO", 1], [5, 299.06, 27.5], [0, 303.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 7, [[33, false, -72, [8], 9]], [0, "e4o1QwKS9C4JzsdDK9Z9kd", 1], [5, 131, 67], [-32, -9, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "1", 12, [[2, 0, -73, [10], 11]], [0, "8epI/vYP9OCK1qEVqVSdp3", 1], [5, 24, 550], [-222, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 12, [[2, 0, -74, [12], 13]], [0, "c7aLiRE0FCjZh36olyYF/Z", 1], [5, 24, 550], [156, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRank", 6, [-75], [0, "49suriKQZAD4yT65B1BvdC", 1], [5, 77, 33], [-367, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "HẠNG", 22, 48, false, false, 1, 1, 21, [14]], [4, "lbNickname", 6, [-76], [0, "e5Q8qhOJpOhobQDvjycTQI", 1], [5, 202.81, 33], [-27, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "TÊN NHÂN VẬT", 22, 48, false, false, 1, 1, 23, [15]], [4, "lbBet", 6, [-77], [0, "4c/EgSIEhCz7BBcnYapulC", 1], [5, 181.5, 33], [333, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "TỔNG THẮNG", 22, 48, false, false, 1, 1, 25, [16]], [24, "temp", 4, [3], [0, "cb3X7GCWRGrqb53+t6wIEu", 1], [0, 20000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "top", 3, [-78], [0, "8f1c4IzOhNAozBr+tb57OW", 1], [5, 53, 43], [-364, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [34, 2, 28, [17]], [10, "lbRank", 3, [-79], [0, "e20hduBOtLnb7cpy1GjrH8", 1], [4, 4284344318], [5, 150, 30], [-367, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "1", 24, 48, false, false, 1, 1, 1, 30, [18]], [27, "lbNickName", 13, [-80], [0, "c7KbycR0tHL7Vu2DtKZknm", 1], [5, 114, 28.8], [0, 0, 0.5]], [14, "<PERSON><PERSON><PERSON>", 24, 48, false, false, 1, 32, [19]], [10, "lbTotalWin", 3, [-81], [0, "8dylTn7NBJMbEkA4k9Tehi", 1], [4, 4284344318], [5, 200, 30], [333, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "350.000.000", 24, 48, false, false, 1, 1, 1, 34, [20]], [47, false, 0.75, 0.23, null, null, 4, 15], [48, 20, 10, 400, 4, 3, 36], [1, "arrow", 5, [[35, 2, false, -82, [25], 26]], [0, "62tw9TcjBNQ4A9l2Pkk3Qu", 1], [5, 33, 25], [71, -2, 0, 0, 0, 0, 1, 1.2, 1.2, 1]], [28, "lbDate", 5, [-83], [0, "28p4FMWAlN9JPPcEeS5vuO", 1], [5, 129.94, 28.13], [0, 0, 0.5], [-84, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "22/02/2020", 18, 50, false, false, 1, 39, [27]], [1, "lbDate", 2, [[3, "22/02/2020", 22, 50, false, false, 1, -84, [32], 33]], [0, "71g/1ZhKhMJb6t5j6Dpl6C", 1], [5, 120.45, 27.5], [0, -23.75, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDate", 2, [[3, "22/02/2020", 22, 50, false, false, 1, -85, [34], 35]], [0, "ccHdhc1bhL1bXDuLNCi+b/", 1], [5, 120.45, 27.5], [0, -61.25, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDate", 2, [[3, "22/02/2020", 22, 50, false, false, 1, -86, [36], 37]], [0, "5aIB76NelHP5IcjXj9oVeQ", 1], [5, 120.45, 27.5], [0, -98.75, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDate", 2, [[3, "22/02/2020", 22, 50, false, false, 1, -87, [38], 39]], [0, "ef6nmlkVBCo5+qWQJZKcvl", 1], [5, 120.45, 27.5], [0, -136.25, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDate", 2, [[3, "22/02/2020", 22, 50, false, false, 1, -88, [40], 41]], [0, "6e7rrX27VHx6g5O1SpdABa", 1], [5, 120.45, 27.5], [0, -173.75, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 8, [44, 45]]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 40, 0, 7, 2, 0, 8, 46, 0, 9, 37, 0, 0, 1, 0, -1, 10, 0, -2, 11, 0, -3, 16, 0, -4, 17, 0, -5, 7, 0, -6, 12, 0, -7, 6, 0, -8, 4, 0, -9, 5, 0, -10, 8, 0, 0, 2, 0, 0, 2, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, -4, 44, 0, -5, 45, 0, 10, 29, 0, 11, 35, 0, 12, 33, 0, 13, 31, 0, 0, 3, 0, -1, 28, 0, -2, 30, 0, -3, 13, 0, -4, 34, 0, -1, 36, 0, 0, 4, 0, -3, 37, 0, -1, 27, 0, -2, 14, 0, 0, 5, 0, 3, 5, 0, 0, 5, 0, -1, 38, 0, -2, 39, 0, 14, 26, 0, 15, 24, 0, 16, 22, 0, 0, 6, 0, -1, 21, 0, -2, 23, 0, -3, 25, 0, 3, 7, 0, 0, 7, 0, -1, 18, 0, -1, 46, 0, -1, 9, 0, 0, 9, 0, 3, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, -1, 19, 0, -2, 20, 0, 0, 13, 0, -1, 32, 0, 0, 14, 0, -1, 15, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, -1, 22, 0, -1, 24, 0, -1, 26, 0, -1, 29, 0, -1, 31, 0, -1, 33, 0, -1, 35, 0, 0, 38, 0, -1, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 17, 1, 2, 4, 8, 3, 4, 27, 88], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 24, 26, 29, 31, 33, 35, 40], [-1, 2, -1, 2, -1, 2, -1, 1, -1, 2, -1, 2, -1, 2, -1, -1, -1, -1, -1, -1, -1, -1, -2, -3, -1, -1, 2, -1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -2, 18, -1, -2, 19, 1, 1, 1, 2, 1, 1, 1, 1], [0, 4, 0, 8, 0, 9, 0, 10, 0, 11, 0, 5, 0, 5, 0, 0, 0, 0, 0, 0, 0, 6, 12, 13, 0, 0, 14, 0, 0, 15, 0, 4, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 16, 17, 18, 7, 7, 19, 20, 2, 2, 2, 6, 3, 3, 3, 2]]]]