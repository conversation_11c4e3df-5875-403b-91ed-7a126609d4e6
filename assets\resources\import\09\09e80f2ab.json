[1, ["45NbSy6XJGdpkHDVfMIjWY", "ceMAlV+r9KkqmFvYs5knl7", "a3h5+LMB9Htor8Nhw/Vgok", "eaymZSHXlPvYfz05tLwJR5", "00GzQ+cC1GNrkOPv2XRHdT", "37Hb7lCoJJZZl7NWDSmbY+", "04khex/qFMeobr+Yiidz3l", "b9BYiHZilHObbc5/WgM86X", "f7r0j7jDFKgbILXLvKxUOv", "01E+wfmClOsYV0LOyvHort", "00TUmGnwxO4LCjaH9wTcfY", "03TMqp+IdMhrGUFdwU/ofS", "40TeIhb1BEDLB7dwLS5t91", "75VUgdrQJOjaofwaH412r6", "f58DFXnONH9KlP+54+1kpf", "c9k4JW705FBpNw3Tv8lTOa", "76fNNleNtIWbQskS5wVBM/", "51nwLqorFP16rNoYa6T5O/", "bfYOGHyLBIQZ88aPUcW40F", "07V/js+odOXKseAMqRv48a", "60i1hgYsNMpLapbETES3Sl", "1erTgiTQ5Ob4XEasEgcjG/", "dcACjU0slAxpppuYHVhkfx", "ccOJlCrchGcqw5pyTKQBDC", "d3WF6oOtxFF7aJ7ZMfX9LD", "23Odklq7VOXaF//xqAH9Ic", "2cI3YSoMtH8Jl9ZyFumvX7", "ccd1E2Dv9N675CJoWwDERo", "98km9OdUhOBbqgqf1DUpKv", "2bAZFJG01BpoJ2iyR3Fqa6", "f5n9GDOFpKOYBdBHKAQJ2b", "bfSa3KCIhKpLCt95Z5ZAQt", "812Svq+s9Ino6PbZ5GXTCE", "98zkA4g15B64K4PEWtDKvp", "34ICro/GZD64V7YlsXKBR+", "e1UfXvmaJOuKD+T4mMHYXK", "67iWCdJElNr5HrEnfHMlmW", "e18Rr8+/VNq5dRlU5JB7rM"], ["_textureSetter", "1", "3", "4", "7", "8", "9", "cheo", "line1", "line10", "line11", "line12", "line13", "line14", "line15", "line16", "line17", "line18", "line19", "line2", "line20", "line3", "line4", "line5", "line6", "line7", "line8", "line9", "phay", "txtDauphay", "txtNumber0", "txtNumber1", "txtNumber2", "txtNumber3", "txtNumber4", "txtNumber6", "txtNumber7", "txtNumber8"], ["cc.SpriteFrame", ["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[1, 0, 1, 2]], [[[{"name": "7", "rect": [375, 626, 21, 28], "offset": [0, 0], "originalSize": [21, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "9", "rect": [327, 626, 22, 28], "offset": [0, 0], "originalSize": [22, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtDauphay", "rect": [299, 626, 26, 28], "offset": [0, 0], "originalSize": [26, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber4", "rect": [705, 192, 33, 37], "offset": [0, 0], "originalSize": [33, 37], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "8", "rect": [421, 626, 21, 28], "offset": [0, 0], "originalSize": [21, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "line.plist", [{}, "1", 6, 0, "3", 6, 1, "4", 6, 2, "7", 6, 3, "8", 6, 4, "9", 6, 5, "cheo", 6, 6, "line1", 6, 7, "line10", 6, 8, "line11", 6, 9, "line12", 6, 10, "line13", 6, 11, "line14", 6, 12, "line15", 6, 13, "line16", 6, 14, "line17", 6, 15, "line18", 6, 16, "line19", 6, 17, "line2", 6, 18, "line20", 6, 19, "line3", 6, 20, "line4", 6, 21, "line5", 6, 22, "line6", 6, 23, "line7", 6, 24, "line8", 6, 25, "line9", 6, 26, "phay", 6, 27, "txtDauphay", 6, 28, "txtNumber0", 6, 29, "txtNumber1", 6, 30, "txtNumber2", 6, 31, "txtNumber3", 6, 32, "txtNumber4", 6, 33, "txtNumber6", 6, 34, "txtNumber7", 6, 35, "txtNumber8", 6, 36]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37]], [[{"name": "txtNumber7", "rect": [740, 192, 32, 38], "offset": [0, 0], "originalSize": [32, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber2", "rect": [530, 192, 33, 39], "offset": [0, 0], "originalSize": [33, 39], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "phay", "rect": [482, 626, 11, 12], "offset": [0, 0], "originalSize": [11, 13], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber3", "rect": [565, 192, 33, 39], "offset": [0, 0], "originalSize": [33, 39], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "3", "rect": [398, 626, 21, 28], "offset": [0, 0], "originalSize": [21, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "cheo", "rect": [444, 626, 19, 30], "offset": [0, 0], "originalSize": [19, 30], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber1", "rect": [269, 626, 28, 36], "offset": [0, 0], "originalSize": [28, 37], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "1", "rect": [465, 626, 15, 28], "offset": [0, 0], "originalSize": [15, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber6", "rect": [670, 192, 33, 38], "offset": [0, 0], "originalSize": [33, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber8", "rect": [635, 192, 33, 38], "offset": [0, 0], "originalSize": [33, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "4", "rect": [351, 626, 22, 28], "offset": [0, 0], "originalSize": [22, 28], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber0", "rect": [600, 192, 33, 38], "offset": [0, 0], "originalSize": [33, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]