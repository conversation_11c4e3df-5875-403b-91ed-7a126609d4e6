[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "e77GDaOr5CoKUPIDgw1gG8", "72OgFAcuRC179Md6oLumfd", "e1NJ8c5BZHI5JcP4RUdG0s", "9d+PxZkqRKJ7i9c7u2FTVj", "a4LbrG+m1FiJQK9CcpaToD", "2cWB/vWPRHja3uQTinHH30", "583IknWfJFnK+EoF1BvUbU", "a0JSDQuixHebMGajdKRcDx"], ["node", "_spriteFrame", "_N$file", "_parent", "_textureSetter", "root", "groupUserListView", "lbBet", "lbTime", "lbSTT", "lbBalance", "lbNickName", "lbSId", "avatar", "_N$target", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$overflow", "_N$verticalAlign", "node", "_materials"], -5, 1, 3], "cc.SpriteFrame", ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_enabled", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["200e4GvazdNvpuWkER+Upwo", ["node", "groupUserListView"], 3, 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 5, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["f9dcdm7m3JOIpbNPPBVMofg", ["node", "avatar", "lbSId", "lbNickName", "lbBalance", "lbSTT"], 3, 1, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["fde5fYTdUdLFaHQ7QSWDYdb", ["node"], 3, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["e234dp5HkFCtoYRJlmU00AW", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[6, 0, 1, 2], [2, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 6, 8, 9, 8], [3, 0, 1, 2, 3, 4, 7, 8, 9, 7], [0, 0, 6, 8, 4, 3, 5, 7, 2], [0, 0, 6, 4, 3, 5, 2], [0, 0, 6, 4, 3, 5, 7, 2], [1, 2, 0, 1, 4, 5, 6, 4], [8, 1, 6, 1], [9, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 5, 7, 2], [0, 0, 8, 4, 3, 5, 2], [0, 0, 1, 6, 4, 3, 5, 7, 3], [0, 0, 6, 8, 4, 3, 5, 2], [0, 0, 6, 4, 3, 5, 9, 7, 2], [0, 0, 2, 6, 4, 3, 5, 7, 3], [0, 0, 6, 8, 3, 7, 2], [5, 0, 1, 6, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 6, 4, 7, 5, 2], [2, 0, 1, 2, 3, 4, 7, 5, 2], [2, 0, 1, 2, 3, 6, 4, 5, 2], [10, 0, 1, 2, 1], [11, 0, 1, 1], [6, 1, 1], [1, 3, 2, 0, 1, 4, 5, 6, 5], [1, 0, 4, 5, 6, 2], [1, 0, 4, 5, 2], [1, 4, 5, 6, 1], [1, 0, 1, 4, 5, 6, 3], [12, 0, 1, 2, 3, 4, 1], [13, 0, 1], [7, 2, 0, 1, 3, 5, 6, 5], [7, 0, 1, 4, 5, 6, 4], [14, 0, 1, 2, 3, 4, 5, 1], [8, 0, 1, 2, 3, 4, 5, 2], [15, 0, 1, 2, 3], [16, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 5, 7, 6, 8, 9, 9], [17, 0, 1], [18, 0, 1, 2, 3, 4, 5, 6, 6], [19, 0, 1, 2, 3, 4, 5, 4]], [[[[9, "BacaratGroupUserView"], [10, "BacaratGroupUserView", [-5, -6, -7, -8, -9, -10, -11], [[23, -2, [22, 23], 21], [24, -4, -3]], [25, -1]], [4, "title", 1, [-16, -17, -18], [[26, false, 1, 0, false, -12, [12], 13], [31, -15, -14, -13, [4, 4278246399], [4, 4294829568]]], [0, "00vi8H5eZN9Lvt2hAd7tDq", 1], [5, 994, 50], [0, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scrollview", 1, [-22, -23], [[-19, [32, -20], -21], 1, 4, 1], [0, "a73fFMFsdFhZJ94Z+7NFiM", 1], [5, 1000, 410], [0, -68.473, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "layout-nick<PERSON><PERSON>", [-25, -26, -27, -28], [[33, false, 1, 1, 5, -24, [5, 327.4, 50]]], [0, "3b9ZYMt5JBka7FvTXKnpy2", 1], [5, 327.4, 50], [-239, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "<PERSON><PERSON>", [4, -35], [[35, -34, -33, -32, -31, -30, -29]], [0, "72ZRQYMbVF4au3CrAXrveN", 1], [5, 994, 50]], [4, "btnClose", 1, [-38], [[36, 3, -37, [[37, "200e4GvazdNvpuWkER+Upwo", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -36]], [0, "31Q6Iqa4FEhIleXhAVgyVI", 1], [5, 80, 80], [496.99, 217.452, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[27, 0, -39, [0], 1], [8, -40, [4, 4292269782]]], [0, "aaZl8K9Z5EU79oKd740Jtf", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nen popup", 1, [[7, 1, 0, false, -41, [2], 3], [8, -42, [4, 4292269782]]], [0, "8anusNH4tP+LB9XaAuZvtg", 1], [5, 1084, 618]], [19, "avatar", 4, [[-43, [7, 1, 0, false, -44, [15], 16]], 1, 4], [0, "27LRvIq4JKaaYEI+cJbJwN", 1], [5, 40, 40], [152, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "view", 3, [-46], [[38, 0, -45, [20]]], [0, "91H4Q2lzZJSLTwXLWjUgD+", 1], [5, 1000, 410]], [15, "content", 10, [[34, 1, 2, 10, -47, [5, 1000, 0]]], [0, "c0/XNvFx5JN459/0hCGfqv", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "bg_content", 1, [[28, 0, -48, [4]]], [0, "bdV+5SXBNGQalKoaYfebAF", 1], [5, 1020, 520], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "title_BXH", false, 1, [[29, -49, [5], 6]], [0, "5bKwzSqixBRay8dizuBTp/", 1], [5, 445, 117], [0, 296, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "sprite", 6, [[30, 0, false, -50, [7], 8]], [0, "d89/aQ/gRCWIPxI4QHJEjS", 1], [5, 50, 50]], [6, "lbSTT", 2, [[2, "SỐ THỨ TỰ", 20, 48, false, false, 1, 1, -51, [9]]], [0, "62UyPVWQhCrJrsORxlnY0S", 1], [5, 200, 38], [-371, -21.672, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickname", 2, [-52], [0, "ebgFbJfPBBQ6mxGmjW1JAz", 1], [5, 200, 38], [6, -21.672, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "TÊN NHÂN VẬT", 20, 48, false, false, 1, 1, 16, [10]], [1, "lbBet", 2, [-53], [0, "6aM9Ju8qZO4KgEBF1vXIlR", 1], [5, 200, 38], [346, -21.672, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "SỐ DƯ", 20, 48, false, false, 1, 1, 18, [11]], [17, "temp", 3, [5], [0, "2eopbqHOhNN7zxakIBJ7ZY", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbSTT", 4, [-54], [0, "eeex0q8WxCA4Vn9hfJDvvv", 1], [5, 13.8, 28.8], [-136, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "2", 24, 48, false, false, 1, 21, [14]], [40, 9], [20, "lbSID", 4, [-55], [0, "84Vi3dL1pGzYFlZ28/CwGy", 1], [4, 4279026733], [5, 44.4, 28.8], [0, 0, 0.5], [177, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "[TQ]", 24, 48, false, false, 1, 24, [17]], [21, "lbNickName", 4, [-56], [0, "4dTiM+c0lPtaKFD4IUs9a7", 1], [5, 114, 28.8], [0, 0, 0.5], [226, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "<PERSON><PERSON><PERSON>", 24, 48, false, false, 1, 26, [18]], [22, "lbBalance", 5, [-57], [0, "3b2KSz0TBHaZgswa3ajtH0", 1], [4, 4284344318], [5, 200, 30], [347, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "350.000.000", 24, 48, false, false, 1, 1, 1, 28, [19]], [41, false, 0.75, 0.23, null, null, 3, 11], [42, 20, 10, 400, 3, 5, 30]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 31, 0, 0, 1, 0, -1, 7, 0, -2, 8, 0, -3, 12, 0, -4, 13, 0, -5, 6, 0, -6, 2, 0, -7, 3, 0, 0, 2, 0, 7, 19, 0, 8, 17, 0, 0, 2, 0, -1, 15, 0, -2, 16, 0, -3, 18, 0, -1, 30, 0, 0, 3, 0, -3, 31, 0, -1, 20, 0, -2, 10, 0, 0, 4, 0, -1, 21, 0, -2, 9, 0, -3, 24, 0, -4, 26, 0, 9, 22, 0, 10, 29, 0, 11, 27, 0, 12, 25, 0, 13, 23, 0, 0, 5, 0, -2, 28, 0, 14, 6, 0, 0, 6, 0, -1, 14, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 23, 0, 0, 9, 0, 0, 10, 0, -1, 11, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, -1, 17, 0, -1, 19, 0, -1, 22, 0, -1, 25, 0, -1, 27, 0, -1, 29, 0, 15, 1, 4, 3, 5, 5, 3, 20, 57], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 25, 27, 29], [-1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, 16, -1, -2, 2, 2, 2, 2], [0, 3, 0, 4, 0, 0, 5, 0, 6, 0, 0, 0, 0, 7, 0, 0, 8, 0, 0, 0, 0, 2, 2, 9, 1, 1, 1, 1]], [[{"name": "title_pInfo", "rect": [1, 0, 445, 117], "offset": [0.5, 0], "originalSize": [446, 117], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [10]], [[{"name": "popup_statistic", "rect": [0, 0, 1156, 642], "offset": [0, 0], "originalSize": [1156, 642], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [11]]]]