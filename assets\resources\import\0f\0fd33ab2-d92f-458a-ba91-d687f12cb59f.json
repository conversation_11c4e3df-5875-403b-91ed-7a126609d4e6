[1, ["e01/msDqhOEa5a/+KhG2Pe"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "MiniPoker-No<PERSON><PERSON>", "\nMiniPoker-NoHu.png\nsize: 512,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nMiniPoker/Coin/1\n  rotate: false\n  xy: 427, 405\n  size: 50, 44\n  orig: 50, 44\n  offset: 0, 0\n  index: -1\nMiniPoker/Coin/2\n  rotate: false\n  xy: 427, 451\n  size: 50, 51\n  orig: 50, 51\n  offset: 0, 0\n  index: -1\nMiniPoker/Coin/3\n  rotate: false\n  xy: 304, 212\n  size: 50, 43\n  orig: 50, 43\n  offset: 0, 0\n  index: -1\nMiniPoker/Coin/4\n  rotate: false\n  xy: 304, 257\n  size: 50, 45\n  orig: 50, 45\n  offset: 0, 0\n  index: -1\nMiniPoker/Coin/5\n  rotate: true\n  xy: 479, 452\n  size: 50, 19\n  orig: 50, 19\n  offset: 0, 0\n  index: -1\nMiniPoker/Coin/6\n  rotate: false\n  xy: 427, 366\n  size: 50, 37\n  orig: 50, 37\n  offset: 0, 0\n  index: -1\nMiniPoker/Coin/7\n  rotate: false\n  xy: 356, 264\n  size: 50, 38\n  orig: 50, 38\n  offset: 0, 0\n  index: -1\nMiniPoker/Coin/8\n  rotate: false\n  xy: 304, 160\n  size: 32, 50\n  orig: 32, 50\n  offset: 0, 0\n  index: -1\nMiniPoker/Minipoker/images/nohu-light\n  rotate: false\n  xy: 2, 2\n  size: 300, 300\n  orig: 300, 300\n  offset: 0, 0\n  index: -1\nMiniPoker/Minipoker/images/nohu-text\n  rotate: false\n  xy: 2, 304\n  size: 423, 198\n  orig: 423, 198\n  offset: 0, 0\n  index: -1\n", ["MiniPoker-NoHu.png"], {"skeleton": {"hash": "gsADZaYg7GMQc3efQqacvtBSiAc", "spine": "3.6.53", "width": 1280, "height": 720, "images": "../../"}, "bones": [{"name": "root"}, {"name": "bone3", "parent": "root", "length": 82.68, "rotation": -0.47, "x": -4.08, "y": 1.91, "scaleX": 0.448, "scaleY": 0.448}, {"name": "Coin-1", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.1}, {"name": "Coin-2", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.23, "y": 0.04}, {"name": "Coin-3", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.25}, {"name": "Coin-4", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-5", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": 0.11}, {"name": "Coin-6", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": -0.04}, {"name": "Coin-7", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.6, "y": -0.67}, {"name": "Coin-8", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.19, "y": -0.19}, {"name": "Coin-9", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.19, "y": -0.19}, {"name": "Coin-10", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.6, "y": -0.67}, {"name": "Coin-11", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": -0.04}, {"name": "Coin-12", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": 0.11}, {"name": "Coin-13", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-14", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-15", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.25}, {"name": "Coin-16", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.23, "y": 0.04}, {"name": "Coin-17", "parent": "bone3", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.1}, {"name": "bone4", "parent": "root", "length": 82.68, "rotation": -0.47, "x": -4.08, "y": 1.91, "scaleX": -0.71, "scaleY": 0.71}, {"name": "Coin-18", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.1}, {"name": "Coin-19", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.23, "y": 0.04}, {"name": "Coin-20", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.25}, {"name": "Coin-21", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-22", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": 0.11}, {"name": "Coin-23", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": -0.04}, {"name": "Coin-24", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.6, "y": -0.67}, {"name": "Coin-25", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.19, "y": -0.19}, {"name": "Coin-26", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.19, "y": -0.19}, {"name": "Coin-27", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.6, "y": -0.67}, {"name": "Coin-28", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": -0.04}, {"name": "Coin-29", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": 0.11}, {"name": "Coin-30", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-31", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-32", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.25}, {"name": "Coin-33", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.23, "y": 0.04}, {"name": "Coin-34", "parent": "bone4", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.1}, {"name": "bone5", "parent": "root", "length": 82.68, "rotation": -0.47, "x": -4.08, "y": 1.91, "scaleX": 0.355, "scaleY": 0.355}, {"name": "Coin-35", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.1}, {"name": "Coin-36", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.23, "y": 0.04}, {"name": "Coin-37", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.25}, {"name": "Coin-38", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-39", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": 0.11}, {"name": "Coin-40", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": -0.04}, {"name": "Coin-41", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.6, "y": -0.67}, {"name": "Coin-42", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.19, "y": -0.19}, {"name": "Coin-43", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.19, "y": -0.19}, {"name": "Coin-44", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.6, "y": -0.67}, {"name": "Coin-45", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": -0.04}, {"name": "Coin-46", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 1.01, "y": 0.11}, {"name": "Coin-47", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-48", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": -0.18, "y": -0.2}, {"name": "Coin-49", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.25}, {"name": "Coin-50", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.23, "y": 0.04}, {"name": "Coin-51", "parent": "bone5", "length": 56.84, "rotation": 0.05, "x": 0.65, "y": -0.1}, {"name": "bone", "parent": "root", "length": 127.32, "rotation": -0.45, "x": -4.36, "y": -4.94}, {"name": "bone2", "parent": "root", "length": 171.13, "rotation": -0.62, "x": -4.32, "y": 0.61, "scaleX": 0.458, "scaleY": 0.458}], "slots": [{"name": "MiniPoker/Minipoker/images/nohu-light", "bone": "bone2", "attachment": "MiniPoker/Minipoker/images/nohu-light"}, {"name": "MiniPoker/Coin/1", "bone": "Coin-3", "attachment": "MiniPoker/Coin/1"}, {"name": "MiniPoker/Coin/37", "bone": "Coin-37", "attachment": "MiniPoker/Coin/1"}, {"name": "MiniPoker/Coin/20", "bone": "Coin-20", "attachment": "MiniPoker/Coin/1"}, {"name": "MiniPoker/Coin/15", "bone": "Coin-15", "attachment": "MiniPoker/Coin/1"}, {"name": "MiniPoker/Coin/49", "bone": "Coin-49", "attachment": "MiniPoker/Coin/1"}, {"name": "MiniPoker/Coin/32", "bone": "Coin-32", "attachment": "MiniPoker/Coin/1"}, {"name": "MiniPoker/Coin/2", "bone": "Coin-8", "attachment": "MiniPoker/Coin/2"}, {"name": "MiniPoker/Coin/42", "bone": "Coin-42", "attachment": "MiniPoker/Coin/2"}, {"name": "MiniPoker/Coin/25", "bone": "Coin-25", "attachment": "MiniPoker/Coin/2"}, {"name": "MiniPoker/Coin/9", "bone": "Coin-9", "attachment": "MiniPoker/Coin/2"}, {"name": "MiniPoker/Coin/43", "bone": "Coin-43", "attachment": "MiniPoker/Coin/2"}, {"name": "MiniPoker/Coin/26", "bone": "Coin-26", "attachment": "MiniPoker/Coin/2"}, {"name": "MiniPoker/Coin/3", "bone": "Coin-6", "attachment": "MiniPoker/Coin/3"}, {"name": "MiniPoker/Coin/40", "bone": "Coin-40", "attachment": "MiniPoker/Coin/3"}, {"name": "MiniPoker/Coin/23", "bone": "Coin-23", "attachment": "MiniPoker/Coin/3"}, {"name": "MiniPoker/Coin/11", "bone": "Coin-11", "attachment": "MiniPoker/Coin/3"}, {"name": "MiniPoker/Coin/45", "bone": "Coin-45", "attachment": "MiniPoker/Coin/3"}, {"name": "MiniPoker/Coin/28", "bone": "Coin-28", "attachment": "MiniPoker/Coin/3"}, {"name": "MiniPoker/Coin/4", "bone": "Coin-7", "attachment": "MiniPoker/Coin/4"}, {"name": "MiniPoker/Coin/41", "bone": "Coin-41", "attachment": "MiniPoker/Coin/4"}, {"name": "MiniPoker/Coin/24", "bone": "Coin-24", "attachment": "MiniPoker/Coin/4"}, {"name": "MiniPoker/Coin/10", "bone": "Coin-10", "attachment": "MiniPoker/Coin/4"}, {"name": "MiniPoker/Coin/44", "bone": "Coin-44", "attachment": "MiniPoker/Coin/4"}, {"name": "MiniPoker/Coin/27", "bone": "Coin-27", "attachment": "MiniPoker/Coin/4"}, {"name": "MiniPoker/Coin/5", "bone": "Coin-1", "attachment": "MiniPoker/Coin/5"}, {"name": "MiniPoker/Coin/35", "bone": "Coin-35", "attachment": "MiniPoker/Coin/5"}, {"name": "MiniPoker/Coin/18", "bone": "Coin-18", "attachment": "MiniPoker/Coin/5"}, {"name": "MiniPoker/Coin/17", "bone": "Coin-17", "attachment": "MiniPoker/Coin/5"}, {"name": "MiniPoker/Coin/51", "bone": "Coin-51", "attachment": "MiniPoker/Coin/5"}, {"name": "MiniPoker/Coin/34", "bone": "Coin-34", "attachment": "MiniPoker/Coin/5"}, {"name": "MiniPoker/Coin/6", "bone": "Coin-5", "attachment": "MiniPoker/Coin/6"}, {"name": "MiniPoker/Coin/39", "bone": "Coin-39", "attachment": "MiniPoker/Coin/6"}, {"name": "MiniPoker/Coin/22", "bone": "Coin-22", "attachment": "MiniPoker/Coin/6"}, {"name": "MiniPoker/Coin/12", "bone": "Coin-12", "attachment": "MiniPoker/Coin/6"}, {"name": "MiniPoker/Coin/46", "bone": "Coin-46", "attachment": "MiniPoker/Coin/6"}, {"name": "MiniPoker/Coin/29", "bone": "Coin-29", "attachment": "MiniPoker/Coin/6"}, {"name": "MiniPoker/Coin/7", "bone": "Coin-2", "attachment": "MiniPoker/Coin/7"}, {"name": "MiniPoker/Coin/36", "bone": "Coin-36", "attachment": "MiniPoker/Coin/7"}, {"name": "MiniPoker/Coin/19", "bone": "Coin-19", "attachment": "MiniPoker/Coin/7"}, {"name": "MiniPoker/Coin/16", "bone": "Coin-16", "attachment": "MiniPoker/Coin/7"}, {"name": "MiniPoker/Coin/50", "bone": "Coin-50", "attachment": "MiniPoker/Coin/7"}, {"name": "MiniPoker/Coin/33", "bone": "Coin-33", "attachment": "MiniPoker/Coin/7"}, {"name": "MiniPoker/Coin/8", "bone": "Coin-4", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/38", "bone": "Coin-38", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/21", "bone": "Coin-21", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/14", "bone": "Coin-14", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/48", "bone": "Coin-48", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/31", "bone": "Coin-31", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/13", "bone": "Coin-13", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/47", "bone": "Coin-47", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Coin/30", "bone": "Coin-30", "attachment": "MiniPoker/Coin/8"}, {"name": "MiniPoker/Minipoker/images/nohu-text", "bone": "bone", "attachment": "MiniPoker/Minipoker/images/nohu-text"}], "skins": {"default": {"MiniPoker/Coin/1": {"MiniPoker/Coin/1": {"x": 1.27, "y": -1.81, "rotation": -0.19, "width": 50, "height": 44}}, "MiniPoker/Coin/10": {"MiniPoker/Coin/4": {"x": 1.54, "y": -2.47, "rotation": -0.19, "width": 50, "height": 45}}, "MiniPoker/Coin/11": {"MiniPoker/Coin/3": {"x": 2.06, "y": -1.89, "rotation": -0.19, "width": 50, "height": 43}}, "MiniPoker/Coin/12": {"MiniPoker/Coin/6": {"x": -0.4, "y": -2.38, "rotation": -0.19, "width": 50, "height": 37}}, "MiniPoker/Coin/13": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/14": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/15": {"MiniPoker/Coin/1": {"x": 1.27, "y": -1.81, "rotation": -0.19, "width": 50, "height": 44}}, "MiniPoker/Coin/16": {"MiniPoker/Coin/7": {"x": -1.12, "y": -3.03, "rotation": -0.19, "width": 50, "height": 38}}, "MiniPoker/Coin/17": {"MiniPoker/Coin/5": {"x": -0.57, "y": -2.47, "rotation": -0.19, "width": 50, "height": 19}}, "MiniPoker/Coin/18": {"MiniPoker/Coin/5": {"x": -0.57, "y": -2.47, "rotation": -0.19, "width": 50, "height": 19}}, "MiniPoker/Coin/19": {"MiniPoker/Coin/7": {"x": -1.12, "y": -3.03, "rotation": -0.19, "width": 50, "height": 38}}, "MiniPoker/Coin/2": {"MiniPoker/Coin/2": {"x": -0.21, "y": -0.56, "rotation": -0.19, "width": 50, "height": 51}}, "MiniPoker/Coin/20": {"MiniPoker/Coin/1": {"x": 1.27, "y": -1.81, "rotation": -0.19, "width": 50, "height": 44}}, "MiniPoker/Coin/21": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/22": {"MiniPoker/Coin/6": {"x": -0.4, "y": -2.38, "rotation": -0.19, "width": 50, "height": 37}}, "MiniPoker/Coin/23": {"MiniPoker/Coin/3": {"x": 2.06, "y": -1.89, "rotation": -0.19, "width": 50, "height": 43}}, "MiniPoker/Coin/24": {"MiniPoker/Coin/4": {"x": 1.54, "y": -2.47, "rotation": -0.19, "width": 50, "height": 45}}, "MiniPoker/Coin/25": {"MiniPoker/Coin/2": {"x": -0.21, "y": -0.56, "rotation": -0.19, "width": 50, "height": 51}}, "MiniPoker/Coin/26": {"MiniPoker/Coin/2": {"x": -0.21, "y": -0.56, "rotation": -0.19, "width": 50, "height": 51}}, "MiniPoker/Coin/27": {"MiniPoker/Coin/4": {"x": 1.54, "y": -2.47, "rotation": -0.19, "width": 50, "height": 45}}, "MiniPoker/Coin/28": {"MiniPoker/Coin/3": {"x": 2.06, "y": -1.89, "rotation": -0.19, "width": 50, "height": 43}}, "MiniPoker/Coin/29": {"MiniPoker/Coin/6": {"x": -0.4, "y": -2.38, "rotation": -0.19, "width": 50, "height": 37}}, "MiniPoker/Coin/3": {"MiniPoker/Coin/3": {"x": 2.06, "y": -1.89, "rotation": -0.19, "width": 50, "height": 43}}, "MiniPoker/Coin/30": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/31": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/32": {"MiniPoker/Coin/1": {"x": 1.27, "y": -1.81, "rotation": -0.19, "width": 50, "height": 44}}, "MiniPoker/Coin/33": {"MiniPoker/Coin/7": {"x": -1.12, "y": -3.03, "rotation": -0.19, "width": 50, "height": 38}}, "MiniPoker/Coin/34": {"MiniPoker/Coin/5": {"x": -0.57, "y": -2.47, "rotation": -0.19, "width": 50, "height": 19}}, "MiniPoker/Coin/35": {"MiniPoker/Coin/5": {"x": -0.57, "y": -2.47, "rotation": -0.19, "width": 50, "height": 19}}, "MiniPoker/Coin/36": {"MiniPoker/Coin/7": {"x": -1.12, "y": -3.03, "rotation": -0.19, "width": 50, "height": 38}}, "MiniPoker/Coin/37": {"MiniPoker/Coin/1": {"x": 1.27, "y": -1.81, "rotation": -0.19, "width": 50, "height": 44}}, "MiniPoker/Coin/38": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/39": {"MiniPoker/Coin/6": {"x": -0.4, "y": -2.38, "rotation": -0.19, "width": 50, "height": 37}}, "MiniPoker/Coin/4": {"MiniPoker/Coin/4": {"x": 1.54, "y": -2.47, "rotation": -0.19, "width": 50, "height": 45}}, "MiniPoker/Coin/40": {"MiniPoker/Coin/3": {"x": 2.06, "y": -1.89, "rotation": -0.19, "width": 50, "height": 43}}, "MiniPoker/Coin/41": {"MiniPoker/Coin/4": {"x": 1.54, "y": -2.47, "rotation": -0.19, "width": 50, "height": 45}}, "MiniPoker/Coin/42": {"MiniPoker/Coin/2": {"x": -0.21, "y": -0.56, "rotation": -0.19, "width": 50, "height": 51}}, "MiniPoker/Coin/43": {"MiniPoker/Coin/2": {"x": -0.21, "y": -0.56, "rotation": -0.19, "width": 50, "height": 51}}, "MiniPoker/Coin/44": {"MiniPoker/Coin/4": {"x": 1.54, "y": -2.47, "rotation": -0.19, "width": 50, "height": 45}}, "MiniPoker/Coin/45": {"MiniPoker/Coin/3": {"x": 2.06, "y": -1.89, "rotation": -0.19, "width": 50, "height": 43}}, "MiniPoker/Coin/46": {"MiniPoker/Coin/6": {"x": -0.4, "y": -2.38, "rotation": -0.19, "width": 50, "height": 37}}, "MiniPoker/Coin/47": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/48": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/49": {"MiniPoker/Coin/1": {"x": 1.27, "y": -1.81, "rotation": -0.19, "width": 50, "height": 44}}, "MiniPoker/Coin/5": {"MiniPoker/Coin/5": {"x": -0.57, "y": -2.47, "rotation": -0.19, "width": 50, "height": 19}}, "MiniPoker/Coin/50": {"MiniPoker/Coin/7": {"x": -1.12, "y": -3.03, "rotation": -0.19, "width": 50, "height": 38}}, "MiniPoker/Coin/51": {"MiniPoker/Coin/5": {"x": -0.57, "y": -2.47, "rotation": -0.19, "width": 50, "height": 19}}, "MiniPoker/Coin/6": {"MiniPoker/Coin/6": {"x": -0.4, "y": -2.38, "rotation": -0.19, "width": 50, "height": 37}}, "MiniPoker/Coin/7": {"MiniPoker/Coin/7": {"x": -1.12, "y": -3.03, "rotation": -0.19, "width": 50, "height": 38}}, "MiniPoker/Coin/8": {"MiniPoker/Coin/8": {"x": -3.26, "y": 0.41, "rotation": -0.19, "width": 32, "height": 50}}, "MiniPoker/Coin/9": {"MiniPoker/Coin/2": {"x": -0.21, "y": -0.56, "rotation": -0.19, "width": 50, "height": 51}}, "MiniPoker/Minipoker/images/nohu-light": {"MiniPoker/Minipoker/images/nohu-light": {"x": -24.73, "y": 75.04, "scaleX": 3.152, "scaleY": 3.152, "rotation": 0.62, "width": 300, "height": 300}}, "MiniPoker/Minipoker/images/nohu-text": {"MiniPoker/Minipoker/images/nohu-text": {"x": 0.47, "y": 22.39, "rotation": 0.45, "width": 423, "height": 198}}}}, "animations": {"Idle": {"slots": {"MiniPoker/Coin/1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/4": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8, "color": "ffffff00"}, {"time": 2.8667, "color": "ffffffff"}]}, "MiniPoker/Coin/5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "MiniPoker/Coin/6": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00"}, {"time": 2.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/7": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/8": {"color": [{"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "MiniPoker/Coin/9": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/10": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00"}, {"time": 2.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/11": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/12": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00"}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8667, "color": "ffffff00"}, {"time": 2.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/13": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/14": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff0b"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff0b"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff0b"}, {"time": 1.8, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffff0b"}, {"time": 2.4667, "color": "ffffff00"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff"}, {"time": 3.1, "color": "ffffff0b"}, {"time": 3.1333, "color": "ffffff00"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/15": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7667, "color": "ffffff00"}, {"time": 2.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/16": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/17": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/18": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "MiniPoker/Coin/19": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/20": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/21": {"color": [{"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "MiniPoker/Coin/22": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00"}, {"time": 2.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/23": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/24": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8, "color": "ffffff00"}, {"time": 2.8667, "color": "ffffffff"}]}, "MiniPoker/Coin/25": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/26": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/27": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00"}, {"time": 2.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/28": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/29": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00"}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8667, "color": "ffffff00"}, {"time": 2.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/30": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/31": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff0b"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff0b"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff0b"}, {"time": 1.8, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffff0b"}, {"time": 2.4667, "color": "ffffff00"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff"}, {"time": 3.1, "color": "ffffff0b"}, {"time": 3.1333, "color": "ffffff00"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/32": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7667, "color": "ffffff00"}, {"time": 2.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/33": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/34": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/35": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 0.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "MiniPoker/Coin/36": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/37": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff"}, {"time": 1.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 1.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.1, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/38": {"color": [{"time": 0.6, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.2667, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}]}, "MiniPoker/Coin/39": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00"}, {"time": 2.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/40": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/41": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1333, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1333, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8, "color": "ffffff00"}, {"time": 2.8667, "color": "ffffffff"}]}, "MiniPoker/Coin/42": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/43": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/44": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.2, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2333, "color": "ffffff00"}, {"time": 0.3, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9, "color": "ffffff00"}, {"time": 0.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5667, "color": "ffffff00"}, {"time": 1.6333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1333, "color": "ffffffff"}, {"time": 2.2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2333, "color": "ffffff00"}, {"time": 2.3, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8, "color": "ffffffff"}, {"time": 2.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9, "color": "ffffff00"}, {"time": 2.9667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/45": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/46": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1, "color": "ffffffff"}, {"time": 0.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.2, "color": "ffffff00"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.1, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00"}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff"}, {"time": 2.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8667, "color": "ffffff00"}, {"time": 2.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/47": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.0667, "color": "ffffffff"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 0.8, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.5, "color": "ffffff00"}, {"time": 1.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00"}, {"time": 2.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7333, "color": "ffffffff"}, {"time": 2.8, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8333, "color": "ffffff00"}, {"time": 2.9, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/48": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3667, "color": "ffffffff"}, {"time": 0.4333, "color": "ffffff0b"}, {"time": 0.4667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff0b"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7, "color": "ffffffff"}, {"time": 1.7667, "color": "ffffff0b"}, {"time": 1.8, "color": "ffffff00"}, {"time": 1.8667, "color": "ffffff00"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffff0b"}, {"time": 2.4667, "color": "ffffff00"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3.0333, "color": "ffffffff"}, {"time": 3.1, "color": "ffffff0b"}, {"time": 3.1333, "color": "ffffff00"}, {"time": 3.2, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/49": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7667, "color": "ffffff00"}, {"time": 2.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/50": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.0333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3667, "color": "ffffff00"}, {"time": 2.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.9333, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00"}, {"time": 3.1, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Coin/51": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 0.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5333, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 1.7, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.3, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.8667, "color": "ffffffff"}, {"time": 2.9333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.9667, "color": "ffffff00"}, {"time": 3.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.3333, "color": "ffffffff"}]}, "MiniPoker/Minipoker/images/nohu-light": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffff00", "curve": "stepped"}, {"time": 0.4333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8333, "color": "ffffff00", "curve": [0.382, 0.57, 0.735, 1]}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.7, "color": "ffffff00", "curve": [0.382, 0.57, 0.735, 1]}, {"time": 1.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5667, "color": "ffffff00", "curve": [0.382, 0.57, 0.735, 1]}, {"time": 2.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 3.2, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "MiniPoker/Minipoker/images/nohu-light"}, {"time": 0.8667, "name": "MiniPoker/Minipoker/images/nohu-light"}, {"time": 1.7333, "name": "MiniPoker/Minipoker/images/nohu-light"}, {"time": 2.6, "name": "MiniPoker/Minipoker/images/nohu-light"}]}}, "bones": {"bone3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.6667, "angle": 180.47, "curve": [0.343, 0.36, 0.686, 0.73]}, {"time": 2.5, "angle": 92.91, "curve": [0.379, 0.6, 0.724, 1]}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}]}, "Coin-8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 146.57, "y": -286.09}, {"time": 0.2667, "x": 266.5, "y": -520.16}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 146.57, "y": -286.09}, {"time": 0.9333, "x": 266.5, "y": -520.16}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 146.57, "y": -286.09}, {"time": 1.6, "x": 266.5, "y": -520.16}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 146.57, "y": -286.09}, {"time": 2.2667, "x": 266.5, "y": -520.16}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 146.57, "y": -286.09}, {"time": 2.9333, "x": 266.5, "y": -520.16}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 146.57, "y": -286.09}], "scale": [{"time": 0, "x": 1.467, "y": 1.467}, {"time": 0.2667, "x": 2.667, "y": 2.667}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.467, "y": 1.467}, {"time": 0.9333, "x": 2.667, "y": 2.667}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.467, "y": 1.467}, {"time": 1.6, "x": 2.667, "y": 2.667}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 1.467, "y": 1.467}, {"time": 2.2667, "x": 2.667, "y": 2.667}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.467, "y": 1.467}, {"time": 2.9333, "x": 2.667, "y": 2.667}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.467, "y": 1.467}], "shear": [{"time": 0, "x": 24.56, "y": 0}, {"time": 0.2667, "x": 44.65, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 24.56, "y": 0}, {"time": 0.9333, "x": 44.65, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 24.56, "y": 0}, {"time": 1.6, "x": 44.65, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 24.56, "y": 0}, {"time": 2.2667, "x": 44.65, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 24.56, "y": 0}, {"time": 2.9333, "x": 44.65, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 24.56, "y": 0}]}, "Coin-7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -128.05, "y": 339.43}, {"time": 0.1, "x": -160.06, "y": 424.29}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": -128.05, "y": 339.43}, {"time": 0.7667, "x": -160.06, "y": 424.29}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": -128.05, "y": 339.43}, {"time": 1.4333, "x": -160.06, "y": 424.29}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -128.05, "y": 339.43}, {"time": 2.1, "x": -160.06, "y": 424.29}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": -128.05, "y": 339.43}, {"time": 2.7667, "x": -160.06, "y": 424.29}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": -128.05, "y": 339.43}], "scale": [{"time": 0, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 0.0333, "x": 2.589, "y": 2.589}, {"time": 0.1, "x": 3.236, "y": 3.236}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 0.7, "x": 2.589, "y": 2.589}, {"time": 0.7667, "x": 3.236, "y": 3.236}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 1.3667, "x": 2.589, "y": 2.589}, {"time": 1.4333, "x": 3.236, "y": 3.236}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 2.0333, "x": 2.589, "y": 2.589}, {"time": 2.1, "x": 3.236, "y": 3.236}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 2.7, "x": 2.589, "y": 2.589}, {"time": 2.7667, "x": 3.236, "y": 3.236}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.589, "y": 2.589}], "shear": [{"time": 0, "x": -34.78, "y": 0}, {"time": 0.1, "x": -43.48, "y": 0}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": -34.78, "y": 0}, {"time": 0.7667, "x": -43.48, "y": 0}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": -34.78, "y": 0}, {"time": 1.4333, "x": -43.48, "y": 0}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -34.78, "y": 0}, {"time": 2.1, "x": -43.48, "y": 0}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": -34.78, "y": 0}, {"time": 2.7667, "x": -43.48, "y": 0}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": -34.78, "y": 0}]}, "Coin-6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 303, "y": -117.82}, {"time": 0.3333, "x": 673.33, "y": -261.82}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 303, "y": -117.82}, {"time": 1, "x": 673.33, "y": -261.82}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 303, "y": -117.82}, {"time": 1.6667, "x": 673.33, "y": -261.82}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 303, "y": -117.82}, {"time": 2.3333, "x": 673.33, "y": -261.82}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 303, "y": -117.82}, {"time": 3, "x": 673.33, "y": -261.82}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 303, "y": -117.82}], "scale": [{"time": 0, "x": 1.396, "y": 1.396}, {"time": 0.3333, "x": 3.101, "y": 3.101}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.396, "y": 1.396}, {"time": 1, "x": 3.101, "y": 3.101}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.396, "y": 1.396}, {"time": 1.6667, "x": 3.101, "y": 3.101}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 1.396, "y": 1.396}, {"time": 2.3333, "x": 3.101, "y": 3.101}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.396, "y": 1.396}, {"time": 3, "x": 3.101, "y": 3.101}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.396, "y": 1.396}], "shear": [{"time": 0, "x": -20.43, "y": 0}, {"time": 0.3333, "x": -45.41, "y": 0}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": -20.43, "y": 0}, {"time": 1, "x": -45.41, "y": 0}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": -20.43, "y": 0}, {"time": 1.6667, "x": -45.41, "y": 0}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -20.43, "y": 0}, {"time": 2.3333, "x": -45.41, "y": 0}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": -20.43, "y": 0}, {"time": 3, "x": -45.41, "y": 0}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": -20.43, "y": 0}]}, "Coin-5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 400.06, "y": 268.74}, {"time": 0.2, "x": 615.47, "y": 413.44}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 400.06, "y": 268.74}, {"time": 0.8667, "x": 615.47, "y": 413.44}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 400.06, "y": 268.74}, {"time": 1.5333, "x": 615.47, "y": 413.44}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 400.06, "y": 268.74}, {"time": 2.2, "x": 615.47, "y": 413.44}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 400.06, "y": 268.74}, {"time": 2.8667, "x": 615.47, "y": 413.44}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 400.06, "y": 268.74}], "scale": [{"time": 0, "x": 2.095, "y": 2.095}, {"time": 0.2, "x": 3.224, "y": 3.224}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.095, "y": 2.095}, {"time": 0.8667, "x": 3.224, "y": 3.224}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.095, "y": 2.095}, {"time": 1.5333, "x": 3.224, "y": 3.224}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 2.095, "y": 2.095}, {"time": 2.2, "x": 3.224, "y": 3.224}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.095, "y": 2.095}, {"time": 2.8667, "x": 3.224, "y": 3.224}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.095, "y": 2.095}], "shear": [{"time": 0, "x": 14.02, "y": 0}, {"time": 0.2, "x": 21.58, "y": 0}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 14.02, "y": 0}, {"time": 0.8667, "x": 21.58, "y": 0}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 14.02, "y": 0}, {"time": 1.5333, "x": 21.58, "y": 0}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 14.02, "y": 0}, {"time": 2.2, "x": 21.58, "y": 0}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 14.02, "y": 0}, {"time": 2.8667, "x": 21.58, "y": 0}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 14.02, "y": 0}]}, "Coin-4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}, {"time": 3.3333, "angle": 1}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 724.9, "y": 141.47}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 3.111, "y": 3.111}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 29.19, "y": 0}]}, "Coin-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.5333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -141.93, "y": 71.3}, {"time": 0.5, "x": -709.66, "y": 356.48}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": -141.93, "y": 71.3}, {"time": 1.1667, "x": -709.66, "y": 356.48}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": -141.93, "y": 71.3}, {"time": 1.8333, "x": -709.66, "y": 356.48}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": -141.93, "y": 71.3}, {"time": 2.5, "x": -709.66, "y": 356.48}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": -141.93, "y": 71.3}, {"time": 3.1667, "x": -709.66, "y": 356.48}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": -141.93, "y": 71.3}], "scale": [{"time": 0, "x": 0.553, "y": 0.553}, {"time": 0.5, "x": 2.766, "y": 2.766}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.553, "y": 0.553}, {"time": 1.1667, "x": 2.766, "y": 2.766}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.553, "y": 0.553}, {"time": 1.8333, "x": 2.766, "y": 2.766}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0.553, "y": 0.553}, {"time": 2.5, "x": 2.766, "y": 2.766}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.553, "y": 0.553}, {"time": 3.1667, "x": 2.766, "y": 2.766}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.553, "y": 0.553}], "shear": [{"time": 0, "x": 0, "y": -7.47}, {"time": 0.5, "x": 0, "y": -37.34}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": -7.47}, {"time": 1.1667, "x": 0, "y": -37.34}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0, "y": -7.47}, {"time": 1.8333, "x": 0, "y": -37.34}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": -7.47}, {"time": 2.5, "x": 0, "y": -37.34}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0, "y": -7.47}, {"time": 3.1667, "x": 0, "y": -37.34}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0, "y": -7.47}]}, "Coin-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.5333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -143.32, "y": -66.61}, {"time": 0.5, "x": -716.62, "y": -333.07}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": -143.32, "y": -66.61}, {"time": 1.1667, "x": -716.62, "y": -333.07}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": -143.32, "y": -66.61}, {"time": 1.8333, "x": -716.62, "y": -333.07}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": -143.32, "y": -66.61}, {"time": 2.5, "x": -716.62, "y": -333.07}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": -143.32, "y": -66.61}, {"time": 3.1667, "x": -716.62, "y": -333.07}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": -143.32, "y": -66.61}], "scale": [{"time": 0, "x": 0.516, "y": 0.516}, {"time": 0.5, "x": 2.58, "y": 2.58}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.516, "y": 0.516}, {"time": 1.1667, "x": 2.58, "y": 2.58}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.516, "y": 0.516}, {"time": 1.8333, "x": 2.58, "y": 2.58}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0.516, "y": 0.516}, {"time": 2.5, "x": 2.58, "y": 2.58}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.516, "y": 0.516}, {"time": 3.1667, "x": 2.58, "y": 2.58}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.516, "y": 0.516}], "shear": [{"time": 0, "x": 8.13, "y": 0}, {"time": 0.5, "x": 40.63, "y": 0}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 8.13, "y": 0}, {"time": 1.1667, "x": 40.63, "y": 0}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 8.13, "y": 0}, {"time": 1.8333, "x": 40.63, "y": 0}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 8.13, "y": 0}, {"time": 2.5, "x": 40.63, "y": 0}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 8.13, "y": 0}, {"time": 3.1667, "x": 40.63, "y": 0}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 8.13, "y": 0}]}, "Coin-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": -274.9, "y": -505.13}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 3.95, "y": 3.95}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 23.92, "y": 0}]}, "Coin-9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -548.84, "y": -4.5}, {"time": 0.1333, "x": -731.78, "y": -6}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": -548.84, "y": -4.5}, {"time": 0.8, "x": -731.78, "y": -6}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": -548.84, "y": -4.5}, {"time": 1.4667, "x": -731.78, "y": -6}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": -548.84, "y": -4.5}, {"time": 2.1333, "x": -731.78, "y": -6}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": -548.84, "y": -4.5}, {"time": 2.8, "x": -731.78, "y": -6}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": -548.84, "y": -4.5}], "scale": [{"time": 0, "x": 2.001, "y": 2.001}, {"time": 0.1333, "x": 2.667, "y": 2.667}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.001, "y": 2.001}, {"time": 0.8, "x": 2.667, "y": 2.667}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.001, "y": 2.001}, {"time": 1.4667, "x": 2.667, "y": 2.667}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 2.001, "y": 2.001}, {"time": 2.1333, "x": 2.667, "y": 2.667}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.001, "y": 2.001}, {"time": 2.8, "x": 2.667, "y": 2.667}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.001, "y": 2.001}], "shear": [{"time": 0, "x": 33.49, "y": 0}, {"time": 0.1333, "x": 44.65, "y": 0}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 33.49, "y": 0}, {"time": 0.8, "x": 44.65, "y": 0}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 33.49, "y": 0}, {"time": 1.4667, "x": 44.65, "y": 0}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 33.49, "y": 0}, {"time": 2.1333, "x": 44.65, "y": 0}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 33.49, "y": 0}, {"time": 2.8, "x": 44.65, "y": 0}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 33.49, "y": 0}]}, "Coin-10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 442.76, "y": -12.89}, {"time": 0.2, "x": 681.17, "y": -19.83}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 442.76, "y": -12.89}, {"time": 0.8667, "x": 681.17, "y": -19.83}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 442.76, "y": -12.89}, {"time": 1.5333, "x": 681.17, "y": -19.83}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 442.76, "y": -12.89}, {"time": 2.2, "x": 681.17, "y": -19.83}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 442.76, "y": -12.89}, {"time": 2.8667, "x": 681.17, "y": -19.83}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 442.76, "y": -12.89}], "scale": [{"time": 0, "x": 2.104, "y": 2.104}, {"time": 0.2, "x": 3.236, "y": 3.236}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.104, "y": 2.104}, {"time": 0.8667, "x": 3.236, "y": 3.236}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.104, "y": 2.104}, {"time": 1.5333, "x": 3.236, "y": 3.236}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 2.104, "y": 2.104}, {"time": 2.2, "x": 3.236, "y": 3.236}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.104, "y": 2.104}, {"time": 2.8667, "x": 3.236, "y": 3.236}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.104, "y": 2.104}], "shear": [{"time": 0, "x": -28.26, "y": 0}, {"time": 0.2, "x": -43.48, "y": 0}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": -28.26, "y": 0}, {"time": 0.8667, "x": -43.48, "y": 0}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": -28.26, "y": 0}, {"time": 1.5333, "x": -43.48, "y": 0}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": -28.26, "y": 0}, {"time": 2.2, "x": -43.48, "y": 0}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": -28.26, "y": 0}, {"time": 2.8667, "x": -43.48, "y": 0}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": -28.26, "y": 0}]}, "Coin-11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 6.91, "y": -274.8}, {"time": 0.2667, "x": 12.57, "y": -499.64}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 6.91, "y": -274.8}, {"time": 0.9333, "x": 12.57, "y": -499.64}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 6.91, "y": -274.8}, {"time": 1.6, "x": 12.57, "y": -499.64}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 6.91, "y": -274.8}, {"time": 2.2667, "x": 12.57, "y": -499.64}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 6.91, "y": -274.8}, {"time": 2.9333, "x": 12.57, "y": -499.64}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 6.91, "y": -274.8}], "scale": [{"time": 0, "x": 1.706, "y": 1.706}, {"time": 0.2667, "x": 3.101, "y": 3.101}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.706, "y": 1.706}, {"time": 0.9333, "x": 3.101, "y": 3.101}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.706, "y": 1.706}, {"time": 1.6, "x": 3.101, "y": 3.101}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 1.706, "y": 1.706}, {"time": 2.2667, "x": 3.101, "y": 3.101}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.706, "y": 1.706}, {"time": 2.9333, "x": 3.101, "y": 3.101}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.706, "y": 1.706}], "shear": [{"time": 0, "x": -24.97, "y": 0}, {"time": 0.2667, "x": -45.41, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": -24.97, "y": 0}, {"time": 0.9333, "x": -45.41, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": -24.97, "y": 0}, {"time": 1.6, "x": -45.41, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": -24.97, "y": 0}, {"time": 2.2667, "x": -45.41, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": -24.97, "y": 0}, {"time": 2.9333, "x": -45.41, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": -24.97, "y": 0}]}, "Coin-12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 2.44, "y": 281.06}, {"time": 0.1667, "x": 3.48, "y": 401.52}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.44, "y": 281.06}, {"time": 0.8333, "x": 3.48, "y": 401.52}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.44, "y": 281.06}, {"time": 1.5, "x": 3.48, "y": 401.52}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 2.44, "y": 281.06}, {"time": 2.1667, "x": 3.48, "y": 401.52}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.44, "y": 281.06}, {"time": 2.8333, "x": 3.48, "y": 401.52}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.44, "y": 281.06}], "scale": [{"time": 0, "x": 2.257, "y": 2.257}, {"time": 0.1667, "x": 3.224, "y": 3.224}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.257, "y": 2.257}, {"time": 0.8333, "x": 3.224, "y": 3.224}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.257, "y": 2.257}, {"time": 1.5, "x": 3.224, "y": 3.224}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 2.257, "y": 2.257}, {"time": 2.1667, "x": 3.224, "y": 3.224}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.257, "y": 2.257}, {"time": 2.8333, "x": 3.224, "y": 3.224}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.257, "y": 2.257}], "shear": [{"time": 0, "x": 15.1, "y": 0}, {"time": 0.1667, "x": 21.58, "y": 0}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 15.1, "y": 0}, {"time": 0.8333, "x": 21.58, "y": 0}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 15.1, "y": 0}, {"time": 1.5, "x": 21.58, "y": 0}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 15.1, "y": 0}, {"time": 2.1667, "x": 21.58, "y": 0}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 15.1, "y": 0}, {"time": 2.8333, "x": 21.58, "y": 0}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 15.1, "y": 0}]}, "Coin-13": {"rotate": [{"time": 0, "angle": 0.75}, {"time": 0.1333, "angle": 1}, {"time": 0.1667, "angle": 0}, {"time": 0.6667, "angle": 0.75}, {"time": 0.8, "angle": 1}, {"time": 0.8333, "angle": 0}, {"time": 1.3333, "angle": 0.75}, {"time": 1.4667, "angle": 1}, {"time": 1.5, "angle": 0}, {"time": 2, "angle": 0.75}, {"time": 2.1333, "angle": 1}, {"time": 2.1667, "angle": 0}, {"time": 2.6667, "angle": 0.75}, {"time": 2.8, "angle": 1}, {"time": 2.8333, "angle": 0}, {"time": 3.3333, "angle": 0.75}], "translate": [{"time": 0, "x": 490.23, "y": -322.34}, {"time": 0.1333, "x": 653.64, "y": -429.78}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 490.23, "y": -322.34}, {"time": 0.8, "x": 653.64, "y": -429.78}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 490.23, "y": -322.34}, {"time": 1.4667, "x": 653.64, "y": -429.78}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 490.23, "y": -322.34}, {"time": 2.1333, "x": 653.64, "y": -429.78}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 490.23, "y": -322.34}, {"time": 2.8, "x": 653.64, "y": -429.78}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 490.23, "y": -322.34}], "scale": [{"time": 0, "x": 2.333, "y": 2.333}, {"time": 0.1333, "x": 3.111, "y": 3.111}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.333, "y": 2.333}, {"time": 0.8, "x": 3.111, "y": 3.111}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.333, "y": 2.333}, {"time": 1.4667, "x": 3.111, "y": 3.111}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 2.333, "y": 2.333}, {"time": 2.1333, "x": 3.111, "y": 3.111}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.333, "y": 2.333}, {"time": 2.8, "x": 3.111, "y": 3.111}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.333, "y": 2.333}], "shear": [{"time": 0, "x": 21.89, "y": 0}, {"time": 0.1333, "x": 29.19, "y": 0}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.89, "y": 0}, {"time": 0.8, "x": 29.19, "y": 0}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 21.89, "y": 0}, {"time": 1.4667, "x": 29.19, "y": 0}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 21.89, "y": 0}, {"time": 2.1333, "x": 29.19, "y": 0}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 21.89, "y": 0}, {"time": 2.8, "x": 29.19, "y": 0}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 21.89, "y": 0}]}, "Coin-14": {"rotate": [{"time": 0, "angle": 0.3}, {"time": 0.4333, "angle": 1}, {"time": 0.4667, "angle": 0}, {"time": 0.6667, "angle": 0.3}, {"time": 1.1, "angle": 1}, {"time": 1.1333, "angle": 0}, {"time": 1.3333, "angle": 0.3}, {"time": 1.7667, "angle": 1}, {"time": 1.8, "angle": 0}, {"time": 2, "angle": 0.3}, {"time": 2.4333, "angle": 1}, {"time": 2.4667, "angle": 0}, {"time": 2.6667, "angle": 0.3}, {"time": 3.1, "angle": 1}, {"time": 3.1333, "angle": 0}, {"time": 3.3333, "angle": 0.3}], "translate": [{"time": 0, "x": 80.81, "y": 124.16}, {"time": 0.4333, "x": 269.35, "y": 413.86}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 80.81, "y": 124.16}, {"time": 1.1, "x": 269.35, "y": 413.86}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 80.81, "y": 124.16}, {"time": 1.7667, "x": 269.35, "y": 413.86}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 80.81, "y": 124.16}, {"time": 2.4333, "x": 269.35, "y": 413.86}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 80.81, "y": 124.16}, {"time": 3.1, "x": 269.35, "y": 413.86}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 80.81, "y": 124.16}], "scale": [{"time": 0, "x": 0.933, "y": 0.933}, {"time": 0.4333, "x": 3.111, "y": 3.111}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.933, "y": 0.933}, {"time": 1.1, "x": 3.111, "y": 3.111}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.933, "y": 0.933}, {"time": 1.7667, "x": 3.111, "y": 3.111}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 0.933, "y": 0.933}, {"time": 2.4333, "x": 3.111, "y": 3.111}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.933, "y": 0.933}, {"time": 3.1, "x": 3.111, "y": 3.111}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.933, "y": 0.933}], "shear": [{"time": 0, "x": 8.76, "y": 0}, {"time": 0.4333, "x": 29.19, "y": 0}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 8.76, "y": 0}, {"time": 1.1, "x": 29.19, "y": 0}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 8.76, "y": 0}, {"time": 1.7667, "x": 29.19, "y": 0}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 8.76, "y": 0}, {"time": 2.4333, "x": 29.19, "y": 0}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 8.76, "y": 0}, {"time": 3.1, "x": 29.19, "y": 0}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 8.76, "y": 0}]}, "Coin-15": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.7667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.7667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 427.51, "y": -389.38}, {"time": 0.0667, "x": 502.95, "y": -458.09}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 427.51, "y": -389.38}, {"time": 0.7333, "x": 502.95, "y": -458.09}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 427.51, "y": -389.38}, {"time": 1.4, "x": 502.95, "y": -458.09}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 427.51, "y": -389.38}, {"time": 2.0667, "x": 502.95, "y": -458.09}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 427.51, "y": -389.38}, {"time": 2.7333, "x": 502.95, "y": -458.09}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 427.51, "y": -389.38}], "scale": [{"time": 0, "x": 2.351, "y": 2.351}, {"time": 0.0667, "x": 2.766, "y": 2.766}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.351, "y": 2.351}, {"time": 0.7333, "x": 2.766, "y": 2.766}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.351, "y": 2.351}, {"time": 1.4, "x": 2.766, "y": 2.766}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 2.351, "y": 2.351}, {"time": 2.0667, "x": 2.766, "y": 2.766}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.351, "y": 2.351}, {"time": 2.7333, "x": 2.766, "y": 2.766}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.351, "y": 2.351}], "shear": [{"time": 0, "x": 0, "y": -31.74}, {"time": 0.0667, "x": 0, "y": -37.34}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": -31.74}, {"time": 0.7333, "x": 0, "y": -37.34}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 0, "y": -31.74}, {"time": 1.4, "x": 0, "y": -37.34}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": -31.74}, {"time": 2.0667, "x": 0, "y": -37.34}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 0, "y": -31.74}, {"time": 2.7333, "x": 0, "y": -37.34}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 0, "y": -31.74}]}, "Coin-16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -258.84, "y": -207.35}, {"time": 0.3333, "x": -575.2, "y": -460.77}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": -258.84, "y": -207.35}, {"time": 1, "x": -575.2, "y": -460.77}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": -258.84, "y": -207.35}, {"time": 1.6667, "x": -575.2, "y": -460.77}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -258.84, "y": -207.35}, {"time": 2.3333, "x": -575.2, "y": -460.77}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": -258.84, "y": -207.35}, {"time": 3, "x": -575.2, "y": -460.77}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": -258.84, "y": -207.35}], "scale": [{"time": 0, "x": 1.161, "y": 1.161}, {"time": 0.3333, "x": 2.58, "y": 2.58}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.161, "y": 1.161}, {"time": 1, "x": 2.58, "y": 2.58}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.161, "y": 1.161}, {"time": 1.6667, "x": 2.58, "y": 2.58}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 1.161, "y": 1.161}, {"time": 2.3333, "x": 2.58, "y": 2.58}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.161, "y": 1.161}, {"time": 3, "x": 2.58, "y": 2.58}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.161, "y": 1.161}], "shear": [{"time": 0, "x": 18.28, "y": 0}, {"time": 0.3333, "x": 40.63, "y": 0}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 18.28, "y": 0}, {"time": 1, "x": 40.63, "y": 0}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 18.28, "y": 0}, {"time": 1.6667, "x": 40.63, "y": 0}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 18.28, "y": 0}, {"time": 2.3333, "x": 40.63, "y": 0}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 18.28, "y": 0}, {"time": 3, "x": 40.63, "y": 0}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 18.28, "y": 0}]}, "Coin-17": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -242.57, "y": 216.27}, {"time": 0.2667, "x": -441.04, "y": 393.23}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": -242.57, "y": 216.27}, {"time": 0.9333, "x": -441.04, "y": 393.23}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": -242.57, "y": 216.27}, {"time": 1.6, "x": -441.04, "y": 393.23}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": -242.57, "y": 216.27}, {"time": 2.2667, "x": -441.04, "y": 393.23}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": -242.57, "y": 216.27}, {"time": 2.9333, "x": -441.04, "y": 393.23}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": -242.57, "y": 216.27}], "scale": [{"time": 0, "x": 2.172, "y": 2.172}, {"time": 0.2667, "x": 3.95, "y": 3.95}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.172, "y": 2.172}, {"time": 0.9333, "x": 3.95, "y": 3.95}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.172, "y": 2.172}, {"time": 1.6, "x": 3.95, "y": 3.95}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 2.172, "y": 2.172}, {"time": 2.2667, "x": 3.95, "y": 3.95}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.172, "y": 2.172}, {"time": 2.9333, "x": 3.95, "y": 3.95}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.172, "y": 2.172}], "shear": [{"time": 0, "x": 13.16, "y": 0}, {"time": 0.2667, "x": 23.92, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 13.16, "y": 0}, {"time": 0.9333, "x": 23.92, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 13.16, "y": 0}, {"time": 1.6, "x": 23.92, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 13.16, "y": 0}, {"time": 2.2667, "x": 23.92, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 13.16, "y": 0}, {"time": 2.9333, "x": 23.92, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 13.16, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 2.6, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 2.987, "y": 2.987, "curve": "stepped"}, {"time": 0.4, "x": 2.987, "y": 2.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 2.987, "y": 2.987, "curve": "stepped"}, {"time": 0.8333, "x": 2.987, "y": 2.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 2.987, "y": 2.987, "curve": "stepped"}, {"time": 1.2667, "x": 2.987, "y": 2.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 2.987, "y": 2.987, "curve": "stepped"}, {"time": 1.7, "x": 2.987, "y": 2.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "x": 2.987, "y": 2.987, "curve": "stepped"}, {"time": 2.1333, "x": 2.987, "y": 2.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 2.987, "y": 2.987, "curve": "stepped"}, {"time": 2.5667, "x": 2.987, "y": 2.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.9333, "x": 2.987, "y": 2.987, "curve": "stepped"}, {"time": 3, "x": 2.987, "y": 2.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0333, "x": 0.014, "y": 0.014, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 2.987, "y": 2.987}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "Coin-18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": -274.9, "y": -505.13}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 3.95, "y": 3.95}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 23.92, "y": 0}]}, "Coin-19": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.5333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -143.32, "y": -66.61}, {"time": 0.5, "x": -716.62, "y": -333.07}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": -143.32, "y": -66.61}, {"time": 1.1667, "x": -716.62, "y": -333.07}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": -143.32, "y": -66.61}, {"time": 1.8333, "x": -716.62, "y": -333.07}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": -143.32, "y": -66.61}, {"time": 2.5, "x": -716.62, "y": -333.07}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": -143.32, "y": -66.61}, {"time": 3.1667, "x": -716.62, "y": -333.07}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": -143.32, "y": -66.61}], "scale": [{"time": 0, "x": 0.516, "y": 0.516}, {"time": 0.5, "x": 2.58, "y": 2.58}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.516, "y": 0.516}, {"time": 1.1667, "x": 2.58, "y": 2.58}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.516, "y": 0.516}, {"time": 1.8333, "x": 2.58, "y": 2.58}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0.516, "y": 0.516}, {"time": 2.5, "x": 2.58, "y": 2.58}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.516, "y": 0.516}, {"time": 3.1667, "x": 2.58, "y": 2.58}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.516, "y": 0.516}], "shear": [{"time": 0, "x": 8.13, "y": 0}, {"time": 0.5, "x": 40.63, "y": 0}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 8.13, "y": 0}, {"time": 1.1667, "x": 40.63, "y": 0}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 8.13, "y": 0}, {"time": 1.8333, "x": 40.63, "y": 0}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 8.13, "y": 0}, {"time": 2.5, "x": 40.63, "y": 0}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 8.13, "y": 0}, {"time": 3.1667, "x": 40.63, "y": 0}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 8.13, "y": 0}]}, "Coin-20": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.5333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -141.93, "y": 71.3}, {"time": 0.5, "x": -709.66, "y": 356.48}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": -141.93, "y": 71.3}, {"time": 1.1667, "x": -709.66, "y": 356.48}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": -141.93, "y": 71.3}, {"time": 1.8333, "x": -709.66, "y": 356.48}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": -141.93, "y": 71.3}, {"time": 2.5, "x": -709.66, "y": 356.48}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": -141.93, "y": 71.3}, {"time": 3.1667, "x": -709.66, "y": 356.48}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": -141.93, "y": 71.3}], "scale": [{"time": 0, "x": 0.553, "y": 0.553}, {"time": 0.5, "x": 2.766, "y": 2.766}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.553, "y": 0.553}, {"time": 1.1667, "x": 2.766, "y": 2.766}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.553, "y": 0.553}, {"time": 1.8333, "x": 2.766, "y": 2.766}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0.553, "y": 0.553}, {"time": 2.5, "x": 2.766, "y": 2.766}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.553, "y": 0.553}, {"time": 3.1667, "x": 2.766, "y": 2.766}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.553, "y": 0.553}], "shear": [{"time": 0, "x": 0, "y": -7.47}, {"time": 0.5, "x": 0, "y": -37.34}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": -7.47}, {"time": 1.1667, "x": 0, "y": -37.34}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0, "y": -7.47}, {"time": 1.8333, "x": 0, "y": -37.34}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": -7.47}, {"time": 2.5, "x": 0, "y": -37.34}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0, "y": -7.47}, {"time": 3.1667, "x": 0, "y": -37.34}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0, "y": -7.47}]}, "Coin-21": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}, {"time": 3.3333, "angle": 1}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 724.9, "y": 141.47}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 3.111, "y": 3.111}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 29.19, "y": 0}]}, "Coin-22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 400.06, "y": 268.74}, {"time": 0.2, "x": 615.47, "y": 413.44}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 400.06, "y": 268.74}, {"time": 0.8667, "x": 615.47, "y": 413.44}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 400.06, "y": 268.74}, {"time": 1.5333, "x": 615.47, "y": 413.44}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 400.06, "y": 268.74}, {"time": 2.2, "x": 615.47, "y": 413.44}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 400.06, "y": 268.74}, {"time": 2.8667, "x": 615.47, "y": 413.44}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 400.06, "y": 268.74}], "scale": [{"time": 0, "x": 2.095, "y": 2.095}, {"time": 0.2, "x": 3.224, "y": 3.224}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.095, "y": 2.095}, {"time": 0.8667, "x": 3.224, "y": 3.224}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.095, "y": 2.095}, {"time": 1.5333, "x": 3.224, "y": 3.224}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 2.095, "y": 2.095}, {"time": 2.2, "x": 3.224, "y": 3.224}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.095, "y": 2.095}, {"time": 2.8667, "x": 3.224, "y": 3.224}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.095, "y": 2.095}], "shear": [{"time": 0, "x": 14.02, "y": 0}, {"time": 0.2, "x": 21.58, "y": 0}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 14.02, "y": 0}, {"time": 0.8667, "x": 21.58, "y": 0}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 14.02, "y": 0}, {"time": 1.5333, "x": 21.58, "y": 0}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 14.02, "y": 0}, {"time": 2.2, "x": 21.58, "y": 0}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 14.02, "y": 0}, {"time": 2.8667, "x": 21.58, "y": 0}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 14.02, "y": 0}]}, "Coin-23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 303, "y": -117.82}, {"time": 0.3333, "x": 673.33, "y": -261.82}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 303, "y": -117.82}, {"time": 1, "x": 673.33, "y": -261.82}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 303, "y": -117.82}, {"time": 1.6667, "x": 673.33, "y": -261.82}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 303, "y": -117.82}, {"time": 2.3333, "x": 673.33, "y": -261.82}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 303, "y": -117.82}, {"time": 3, "x": 673.33, "y": -261.82}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 303, "y": -117.82}], "scale": [{"time": 0, "x": 1.396, "y": 1.396}, {"time": 0.3333, "x": 3.101, "y": 3.101}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.396, "y": 1.396}, {"time": 1, "x": 3.101, "y": 3.101}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.396, "y": 1.396}, {"time": 1.6667, "x": 3.101, "y": 3.101}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 1.396, "y": 1.396}, {"time": 2.3333, "x": 3.101, "y": 3.101}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.396, "y": 1.396}, {"time": 3, "x": 3.101, "y": 3.101}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.396, "y": 1.396}], "shear": [{"time": 0, "x": -20.43, "y": 0}, {"time": 0.3333, "x": -45.41, "y": 0}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": -20.43, "y": 0}, {"time": 1, "x": -45.41, "y": 0}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": -20.43, "y": 0}, {"time": 1.6667, "x": -45.41, "y": 0}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -20.43, "y": 0}, {"time": 2.3333, "x": -45.41, "y": 0}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": -20.43, "y": 0}, {"time": 3, "x": -45.41, "y": 0}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": -20.43, "y": 0}]}, "Coin-24": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -128.05, "y": 339.43}, {"time": 0.1, "x": -160.06, "y": 424.29}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": -128.05, "y": 339.43}, {"time": 0.7667, "x": -160.06, "y": 424.29}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": -128.05, "y": 339.43}, {"time": 1.4333, "x": -160.06, "y": 424.29}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -128.05, "y": 339.43}, {"time": 2.1, "x": -160.06, "y": 424.29}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": -128.05, "y": 339.43}, {"time": 2.7667, "x": -160.06, "y": 424.29}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": -128.05, "y": 339.43}], "scale": [{"time": 0, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 0.0333, "x": 2.589, "y": 2.589}, {"time": 0.1, "x": 3.236, "y": 3.236}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 0.7, "x": 2.589, "y": 2.589}, {"time": 0.7667, "x": 3.236, "y": 3.236}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 1.3667, "x": 2.589, "y": 2.589}, {"time": 1.4333, "x": 3.236, "y": 3.236}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 2.0333, "x": 2.589, "y": 2.589}, {"time": 2.1, "x": 3.236, "y": 3.236}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 2.7, "x": 2.589, "y": 2.589}, {"time": 2.7667, "x": 3.236, "y": 3.236}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.589, "y": 2.589}], "shear": [{"time": 0, "x": -34.78, "y": 0}, {"time": 0.1, "x": -43.48, "y": 0}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": -34.78, "y": 0}, {"time": 0.7667, "x": -43.48, "y": 0}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": -34.78, "y": 0}, {"time": 1.4333, "x": -43.48, "y": 0}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -34.78, "y": 0}, {"time": 2.1, "x": -43.48, "y": 0}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": -34.78, "y": 0}, {"time": 2.7667, "x": -43.48, "y": 0}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": -34.78, "y": 0}]}, "Coin-25": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 146.57, "y": -286.09}, {"time": 0.2667, "x": 266.5, "y": -520.16}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 146.57, "y": -286.09}, {"time": 0.9333, "x": 266.5, "y": -520.16}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 146.57, "y": -286.09}, {"time": 1.6, "x": 266.5, "y": -520.16}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 146.57, "y": -286.09}, {"time": 2.2667, "x": 266.5, "y": -520.16}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 146.57, "y": -286.09}, {"time": 2.9333, "x": 266.5, "y": -520.16}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 146.57, "y": -286.09}], "scale": [{"time": 0, "x": 1.467, "y": 1.467}, {"time": 0.2667, "x": 2.667, "y": 2.667}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.467, "y": 1.467}, {"time": 0.9333, "x": 2.667, "y": 2.667}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.467, "y": 1.467}, {"time": 1.6, "x": 2.667, "y": 2.667}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 1.467, "y": 1.467}, {"time": 2.2667, "x": 2.667, "y": 2.667}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.467, "y": 1.467}, {"time": 2.9333, "x": 2.667, "y": 2.667}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.467, "y": 1.467}], "shear": [{"time": 0, "x": 24.56, "y": 0}, {"time": 0.2667, "x": 44.65, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 24.56, "y": 0}, {"time": 0.9333, "x": 44.65, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 24.56, "y": 0}, {"time": 1.6, "x": 44.65, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 24.56, "y": 0}, {"time": 2.2667, "x": 44.65, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 24.56, "y": 0}, {"time": 2.9333, "x": 44.65, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 24.56, "y": 0}]}, "Coin-26": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -548.84, "y": -4.5}, {"time": 0.1333, "x": -731.78, "y": -6}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": -548.84, "y": -4.5}, {"time": 0.8, "x": -731.78, "y": -6}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": -548.84, "y": -4.5}, {"time": 1.4667, "x": -731.78, "y": -6}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": -548.84, "y": -4.5}, {"time": 2.1333, "x": -731.78, "y": -6}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": -548.84, "y": -4.5}, {"time": 2.8, "x": -731.78, "y": -6}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": -548.84, "y": -4.5}], "scale": [{"time": 0, "x": 2.001, "y": 2.001}, {"time": 0.1333, "x": 2.667, "y": 2.667}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.001, "y": 2.001}, {"time": 0.8, "x": 2.667, "y": 2.667}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.001, "y": 2.001}, {"time": 1.4667, "x": 2.667, "y": 2.667}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 2.001, "y": 2.001}, {"time": 2.1333, "x": 2.667, "y": 2.667}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.001, "y": 2.001}, {"time": 2.8, "x": 2.667, "y": 2.667}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.001, "y": 2.001}], "shear": [{"time": 0, "x": 33.49, "y": 0}, {"time": 0.1333, "x": 44.65, "y": 0}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 33.49, "y": 0}, {"time": 0.8, "x": 44.65, "y": 0}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 33.49, "y": 0}, {"time": 1.4667, "x": 44.65, "y": 0}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 33.49, "y": 0}, {"time": 2.1333, "x": 44.65, "y": 0}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 33.49, "y": 0}, {"time": 2.8, "x": 44.65, "y": 0}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 33.49, "y": 0}]}, "Coin-27": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 442.76, "y": -12.89}, {"time": 0.2, "x": 681.17, "y": -19.83}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 442.76, "y": -12.89}, {"time": 0.8667, "x": 681.17, "y": -19.83}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 442.76, "y": -12.89}, {"time": 1.5333, "x": 681.17, "y": -19.83}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 442.76, "y": -12.89}, {"time": 2.2, "x": 681.17, "y": -19.83}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 442.76, "y": -12.89}, {"time": 2.8667, "x": 681.17, "y": -19.83}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 442.76, "y": -12.89}], "scale": [{"time": 0, "x": 2.104, "y": 2.104}, {"time": 0.2, "x": 3.236, "y": 3.236}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.104, "y": 2.104}, {"time": 0.8667, "x": 3.236, "y": 3.236}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.104, "y": 2.104}, {"time": 1.5333, "x": 3.236, "y": 3.236}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 2.104, "y": 2.104}, {"time": 2.2, "x": 3.236, "y": 3.236}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.104, "y": 2.104}, {"time": 2.8667, "x": 3.236, "y": 3.236}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.104, "y": 2.104}], "shear": [{"time": 0, "x": -28.26, "y": 0}, {"time": 0.2, "x": -43.48, "y": 0}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": -28.26, "y": 0}, {"time": 0.8667, "x": -43.48, "y": 0}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": -28.26, "y": 0}, {"time": 1.5333, "x": -43.48, "y": 0}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": -28.26, "y": 0}, {"time": 2.2, "x": -43.48, "y": 0}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": -28.26, "y": 0}, {"time": 2.8667, "x": -43.48, "y": 0}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": -28.26, "y": 0}]}, "Coin-28": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 6.91, "y": -274.8}, {"time": 0.2667, "x": 12.57, "y": -499.64}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 6.91, "y": -274.8}, {"time": 0.9333, "x": 12.57, "y": -499.64}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 6.91, "y": -274.8}, {"time": 1.6, "x": 12.57, "y": -499.64}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 6.91, "y": -274.8}, {"time": 2.2667, "x": 12.57, "y": -499.64}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 6.91, "y": -274.8}, {"time": 2.9333, "x": 12.57, "y": -499.64}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 6.91, "y": -274.8}], "scale": [{"time": 0, "x": 1.706, "y": 1.706}, {"time": 0.2667, "x": 3.101, "y": 3.101}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.706, "y": 1.706}, {"time": 0.9333, "x": 3.101, "y": 3.101}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.706, "y": 1.706}, {"time": 1.6, "x": 3.101, "y": 3.101}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 1.706, "y": 1.706}, {"time": 2.2667, "x": 3.101, "y": 3.101}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.706, "y": 1.706}, {"time": 2.9333, "x": 3.101, "y": 3.101}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.706, "y": 1.706}], "shear": [{"time": 0, "x": -24.97, "y": 0}, {"time": 0.2667, "x": -45.41, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": -24.97, "y": 0}, {"time": 0.9333, "x": -45.41, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": -24.97, "y": 0}, {"time": 1.6, "x": -45.41, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": -24.97, "y": 0}, {"time": 2.2667, "x": -45.41, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": -24.97, "y": 0}, {"time": 2.9333, "x": -45.41, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": -24.97, "y": 0}]}, "Coin-29": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 2.44, "y": 281.06}, {"time": 0.1667, "x": 3.48, "y": 401.52}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.44, "y": 281.06}, {"time": 0.8333, "x": 3.48, "y": 401.52}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.44, "y": 281.06}, {"time": 1.5, "x": 3.48, "y": 401.52}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 2.44, "y": 281.06}, {"time": 2.1667, "x": 3.48, "y": 401.52}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.44, "y": 281.06}, {"time": 2.8333, "x": 3.48, "y": 401.52}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.44, "y": 281.06}], "scale": [{"time": 0, "x": 2.257, "y": 2.257}, {"time": 0.1667, "x": 3.224, "y": 3.224}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.257, "y": 2.257}, {"time": 0.8333, "x": 3.224, "y": 3.224}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.257, "y": 2.257}, {"time": 1.5, "x": 3.224, "y": 3.224}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 2.257, "y": 2.257}, {"time": 2.1667, "x": 3.224, "y": 3.224}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.257, "y": 2.257}, {"time": 2.8333, "x": 3.224, "y": 3.224}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.257, "y": 2.257}], "shear": [{"time": 0, "x": 15.1, "y": 0}, {"time": 0.1667, "x": 21.58, "y": 0}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 15.1, "y": 0}, {"time": 0.8333, "x": 21.58, "y": 0}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 15.1, "y": 0}, {"time": 1.5, "x": 21.58, "y": 0}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 15.1, "y": 0}, {"time": 2.1667, "x": 21.58, "y": 0}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 15.1, "y": 0}, {"time": 2.8333, "x": 21.58, "y": 0}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 15.1, "y": 0}]}, "Coin-30": {"rotate": [{"time": 0, "angle": 0.75}, {"time": 0.1333, "angle": 1}, {"time": 0.1667, "angle": 0}, {"time": 0.6667, "angle": 0.75}, {"time": 0.8, "angle": 1}, {"time": 0.8333, "angle": 0}, {"time": 1.3333, "angle": 0.75}, {"time": 1.4667, "angle": 1}, {"time": 1.5, "angle": 0}, {"time": 2, "angle": 0.75}, {"time": 2.1333, "angle": 1}, {"time": 2.1667, "angle": 0}, {"time": 2.6667, "angle": 0.75}, {"time": 2.8, "angle": 1}, {"time": 2.8333, "angle": 0}, {"time": 3.3333, "angle": 0.75}], "translate": [{"time": 0, "x": 490.23, "y": -322.34}, {"time": 0.1333, "x": 653.64, "y": -429.78}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 490.23, "y": -322.34}, {"time": 0.8, "x": 653.64, "y": -429.78}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 490.23, "y": -322.34}, {"time": 1.4667, "x": 653.64, "y": -429.78}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 490.23, "y": -322.34}, {"time": 2.1333, "x": 653.64, "y": -429.78}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 490.23, "y": -322.34}, {"time": 2.8, "x": 653.64, "y": -429.78}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 490.23, "y": -322.34}], "scale": [{"time": 0, "x": 2.333, "y": 2.333}, {"time": 0.1333, "x": 3.111, "y": 3.111}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.333, "y": 2.333}, {"time": 0.8, "x": 3.111, "y": 3.111}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.333, "y": 2.333}, {"time": 1.4667, "x": 3.111, "y": 3.111}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 2.333, "y": 2.333}, {"time": 2.1333, "x": 3.111, "y": 3.111}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.333, "y": 2.333}, {"time": 2.8, "x": 3.111, "y": 3.111}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.333, "y": 2.333}], "shear": [{"time": 0, "x": 21.89, "y": 0}, {"time": 0.1333, "x": 29.19, "y": 0}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.89, "y": 0}, {"time": 0.8, "x": 29.19, "y": 0}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 21.89, "y": 0}, {"time": 1.4667, "x": 29.19, "y": 0}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 21.89, "y": 0}, {"time": 2.1333, "x": 29.19, "y": 0}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 21.89, "y": 0}, {"time": 2.8, "x": 29.19, "y": 0}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 21.89, "y": 0}]}, "Coin-31": {"rotate": [{"time": 0, "angle": 0.3}, {"time": 0.4333, "angle": 1}, {"time": 0.4667, "angle": 0}, {"time": 0.6667, "angle": 0.3}, {"time": 1.1, "angle": 1}, {"time": 1.1333, "angle": 0}, {"time": 1.3333, "angle": 0.3}, {"time": 1.7667, "angle": 1}, {"time": 1.8, "angle": 0}, {"time": 2, "angle": 0.3}, {"time": 2.4333, "angle": 1}, {"time": 2.4667, "angle": 0}, {"time": 2.6667, "angle": 0.3}, {"time": 3.1, "angle": 1}, {"time": 3.1333, "angle": 0}, {"time": 3.3333, "angle": 0.3}], "translate": [{"time": 0, "x": 80.81, "y": 124.16}, {"time": 0.4333, "x": 269.35, "y": 413.86}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 80.81, "y": 124.16}, {"time": 1.1, "x": 269.35, "y": 413.86}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 80.81, "y": 124.16}, {"time": 1.7667, "x": 269.35, "y": 413.86}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 80.81, "y": 124.16}, {"time": 2.4333, "x": 269.35, "y": 413.86}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 80.81, "y": 124.16}, {"time": 3.1, "x": 269.35, "y": 413.86}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 80.81, "y": 124.16}], "scale": [{"time": 0, "x": 0.933, "y": 0.933}, {"time": 0.4333, "x": 3.111, "y": 3.111}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.933, "y": 0.933}, {"time": 1.1, "x": 3.111, "y": 3.111}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.933, "y": 0.933}, {"time": 1.7667, "x": 3.111, "y": 3.111}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 0.933, "y": 0.933}, {"time": 2.4333, "x": 3.111, "y": 3.111}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.933, "y": 0.933}, {"time": 3.1, "x": 3.111, "y": 3.111}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.933, "y": 0.933}], "shear": [{"time": 0, "x": 8.76, "y": 0}, {"time": 0.4333, "x": 29.19, "y": 0}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 8.76, "y": 0}, {"time": 1.1, "x": 29.19, "y": 0}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 8.76, "y": 0}, {"time": 1.7667, "x": 29.19, "y": 0}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 8.76, "y": 0}, {"time": 2.4333, "x": 29.19, "y": 0}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 8.76, "y": 0}, {"time": 3.1, "x": 29.19, "y": 0}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 8.76, "y": 0}]}, "Coin-32": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.7667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.7667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 427.51, "y": -389.38}, {"time": 0.0667, "x": 502.95, "y": -458.09}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 427.51, "y": -389.38}, {"time": 0.7333, "x": 502.95, "y": -458.09}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 427.51, "y": -389.38}, {"time": 1.4, "x": 502.95, "y": -458.09}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 427.51, "y": -389.38}, {"time": 2.0667, "x": 502.95, "y": -458.09}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 427.51, "y": -389.38}, {"time": 2.7333, "x": 502.95, "y": -458.09}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 427.51, "y": -389.38}], "scale": [{"time": 0, "x": 2.351, "y": 2.351}, {"time": 0.0667, "x": 2.766, "y": 2.766}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.351, "y": 2.351}, {"time": 0.7333, "x": 2.766, "y": 2.766}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.351, "y": 2.351}, {"time": 1.4, "x": 2.766, "y": 2.766}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 2.351, "y": 2.351}, {"time": 2.0667, "x": 2.766, "y": 2.766}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.351, "y": 2.351}, {"time": 2.7333, "x": 2.766, "y": 2.766}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.351, "y": 2.351}], "shear": [{"time": 0, "x": 0, "y": -31.74}, {"time": 0.0667, "x": 0, "y": -37.34}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": -31.74}, {"time": 0.7333, "x": 0, "y": -37.34}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 0, "y": -31.74}, {"time": 1.4, "x": 0, "y": -37.34}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": -31.74}, {"time": 2.0667, "x": 0, "y": -37.34}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 0, "y": -31.74}, {"time": 2.7333, "x": 0, "y": -37.34}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 0, "y": -31.74}]}, "Coin-33": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -258.84, "y": -207.35}, {"time": 0.3333, "x": -575.2, "y": -460.77}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": -258.84, "y": -207.35}, {"time": 1, "x": -575.2, "y": -460.77}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": -258.84, "y": -207.35}, {"time": 1.6667, "x": -575.2, "y": -460.77}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -258.84, "y": -207.35}, {"time": 2.3333, "x": -575.2, "y": -460.77}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": -258.84, "y": -207.35}, {"time": 3, "x": -575.2, "y": -460.77}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": -258.84, "y": -207.35}], "scale": [{"time": 0, "x": 1.161, "y": 1.161}, {"time": 0.3333, "x": 2.58, "y": 2.58}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.161, "y": 1.161}, {"time": 1, "x": 2.58, "y": 2.58}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.161, "y": 1.161}, {"time": 1.6667, "x": 2.58, "y": 2.58}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 1.161, "y": 1.161}, {"time": 2.3333, "x": 2.58, "y": 2.58}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.161, "y": 1.161}, {"time": 3, "x": 2.58, "y": 2.58}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.161, "y": 1.161}], "shear": [{"time": 0, "x": 18.28, "y": 0}, {"time": 0.3333, "x": 40.63, "y": 0}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 18.28, "y": 0}, {"time": 1, "x": 40.63, "y": 0}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 18.28, "y": 0}, {"time": 1.6667, "x": 40.63, "y": 0}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 18.28, "y": 0}, {"time": 2.3333, "x": 40.63, "y": 0}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 18.28, "y": 0}, {"time": 3, "x": 40.63, "y": 0}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 18.28, "y": 0}]}, "Coin-34": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -242.57, "y": 216.27}, {"time": 0.2667, "x": -441.04, "y": 393.23}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": -242.57, "y": 216.27}, {"time": 0.9333, "x": -441.04, "y": 393.23}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": -242.57, "y": 216.27}, {"time": 1.6, "x": -441.04, "y": 393.23}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": -242.57, "y": 216.27}, {"time": 2.2667, "x": -441.04, "y": 393.23}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": -242.57, "y": 216.27}, {"time": 2.9333, "x": -441.04, "y": 393.23}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": -242.57, "y": 216.27}], "scale": [{"time": 0, "x": 2.172, "y": 2.172}, {"time": 0.2667, "x": 3.95, "y": 3.95}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.172, "y": 2.172}, {"time": 0.9333, "x": 3.95, "y": 3.95}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.172, "y": 2.172}, {"time": 1.6, "x": 3.95, "y": 3.95}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 2.172, "y": 2.172}, {"time": 2.2667, "x": 3.95, "y": 3.95}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.172, "y": 2.172}, {"time": 2.9333, "x": 3.95, "y": 3.95}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.172, "y": 2.172}], "shear": [{"time": 0, "x": 13.16, "y": 0}, {"time": 0.2667, "x": 23.92, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 13.16, "y": 0}, {"time": 0.9333, "x": 23.92, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 13.16, "y": 0}, {"time": 1.6, "x": 23.92, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 13.16, "y": 0}, {"time": 2.2667, "x": 23.92, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 13.16, "y": 0}, {"time": 2.9333, "x": 23.92, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 13.16, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "Coin-35": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": -274.9, "y": -505.13}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 3.95, "y": 3.95}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 23.92, "y": 0}]}, "Coin-36": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.5333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -143.32, "y": -66.61}, {"time": 0.5, "x": -716.62, "y": -333.07}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": -143.32, "y": -66.61}, {"time": 1.1667, "x": -716.62, "y": -333.07}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": -143.32, "y": -66.61}, {"time": 1.8333, "x": -716.62, "y": -333.07}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": -143.32, "y": -66.61}, {"time": 2.5, "x": -716.62, "y": -333.07}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": -143.32, "y": -66.61}, {"time": 3.1667, "x": -716.62, "y": -333.07}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": -143.32, "y": -66.61}], "scale": [{"time": 0, "x": 0.516, "y": 0.516}, {"time": 0.5, "x": 2.58, "y": 2.58}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.516, "y": 0.516}, {"time": 1.1667, "x": 2.58, "y": 2.58}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.516, "y": 0.516}, {"time": 1.8333, "x": 2.58, "y": 2.58}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0.516, "y": 0.516}, {"time": 2.5, "x": 2.58, "y": 2.58}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.516, "y": 0.516}, {"time": 3.1667, "x": 2.58, "y": 2.58}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.516, "y": 0.516}], "shear": [{"time": 0, "x": 8.13, "y": 0}, {"time": 0.5, "x": 40.63, "y": 0}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 8.13, "y": 0}, {"time": 1.1667, "x": 40.63, "y": 0}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 8.13, "y": 0}, {"time": 1.8333, "x": 40.63, "y": 0}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 8.13, "y": 0}, {"time": 2.5, "x": 40.63, "y": 0}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 8.13, "y": 0}, {"time": 3.1667, "x": 40.63, "y": 0}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 8.13, "y": 0}]}, "Coin-37": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.8667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.5333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -141.93, "y": 71.3}, {"time": 0.5, "x": -709.66, "y": 356.48}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": -141.93, "y": 71.3}, {"time": 1.1667, "x": -709.66, "y": 356.48}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": -141.93, "y": 71.3}, {"time": 1.8333, "x": -709.66, "y": 356.48}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": -141.93, "y": 71.3}, {"time": 2.5, "x": -709.66, "y": 356.48}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": -141.93, "y": 71.3}, {"time": 3.1667, "x": -709.66, "y": 356.48}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": -141.93, "y": 71.3}], "scale": [{"time": 0, "x": 0.553, "y": 0.553}, {"time": 0.5, "x": 2.766, "y": 2.766}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.553, "y": 0.553}, {"time": 1.1667, "x": 2.766, "y": 2.766}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.553, "y": 0.553}, {"time": 1.8333, "x": 2.766, "y": 2.766}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0.553, "y": 0.553}, {"time": 2.5, "x": 2.766, "y": 2.766}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.553, "y": 0.553}, {"time": 3.1667, "x": 2.766, "y": 2.766}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.553, "y": 0.553}], "shear": [{"time": 0, "x": 0, "y": -7.47}, {"time": 0.5, "x": 0, "y": -37.34}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": -7.47}, {"time": 1.1667, "x": 0, "y": -37.34}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.3333, "x": 0, "y": -7.47}, {"time": 1.8333, "x": 0, "y": -37.34}, {"time": 1.8667, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": -7.47}, {"time": 2.5, "x": 0, "y": -37.34}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0, "y": -7.47}, {"time": 3.1667, "x": 0, "y": -37.34}, {"time": 3.2, "x": 0, "y": 0}, {"time": 3.3333, "x": 0, "y": -7.47}]}, "Coin-38": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}, {"time": 3.3333, "angle": 1}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 724.9, "y": 141.47}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 3.111, "y": 3.111}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3.3333, "x": 29.19, "y": 0}]}, "Coin-39": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 400.06, "y": 268.74}, {"time": 0.2, "x": 615.47, "y": 413.44}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 400.06, "y": 268.74}, {"time": 0.8667, "x": 615.47, "y": 413.44}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 400.06, "y": 268.74}, {"time": 1.5333, "x": 615.47, "y": 413.44}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 400.06, "y": 268.74}, {"time": 2.2, "x": 615.47, "y": 413.44}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 400.06, "y": 268.74}, {"time": 2.8667, "x": 615.47, "y": 413.44}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 400.06, "y": 268.74}], "scale": [{"time": 0, "x": 2.095, "y": 2.095}, {"time": 0.2, "x": 3.224, "y": 3.224}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.095, "y": 2.095}, {"time": 0.8667, "x": 3.224, "y": 3.224}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.095, "y": 2.095}, {"time": 1.5333, "x": 3.224, "y": 3.224}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 2.095, "y": 2.095}, {"time": 2.2, "x": 3.224, "y": 3.224}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.095, "y": 2.095}, {"time": 2.8667, "x": 3.224, "y": 3.224}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.095, "y": 2.095}], "shear": [{"time": 0, "x": 14.02, "y": 0}, {"time": 0.2, "x": 21.58, "y": 0}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 14.02, "y": 0}, {"time": 0.8667, "x": 21.58, "y": 0}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 14.02, "y": 0}, {"time": 1.5333, "x": 21.58, "y": 0}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 14.02, "y": 0}, {"time": 2.2, "x": 21.58, "y": 0}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 14.02, "y": 0}, {"time": 2.8667, "x": 21.58, "y": 0}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 14.02, "y": 0}]}, "Coin-40": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 303, "y": -117.82}, {"time": 0.3333, "x": 673.33, "y": -261.82}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 303, "y": -117.82}, {"time": 1, "x": 673.33, "y": -261.82}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 303, "y": -117.82}, {"time": 1.6667, "x": 673.33, "y": -261.82}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 303, "y": -117.82}, {"time": 2.3333, "x": 673.33, "y": -261.82}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 303, "y": -117.82}, {"time": 3, "x": 673.33, "y": -261.82}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 303, "y": -117.82}], "scale": [{"time": 0, "x": 1.396, "y": 1.396}, {"time": 0.3333, "x": 3.101, "y": 3.101}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.396, "y": 1.396}, {"time": 1, "x": 3.101, "y": 3.101}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.396, "y": 1.396}, {"time": 1.6667, "x": 3.101, "y": 3.101}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 1.396, "y": 1.396}, {"time": 2.3333, "x": 3.101, "y": 3.101}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.396, "y": 1.396}, {"time": 3, "x": 3.101, "y": 3.101}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.396, "y": 1.396}], "shear": [{"time": 0, "x": -20.43, "y": 0}, {"time": 0.3333, "x": -45.41, "y": 0}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": -20.43, "y": 0}, {"time": 1, "x": -45.41, "y": 0}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": -20.43, "y": 0}, {"time": 1.6667, "x": -45.41, "y": 0}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -20.43, "y": 0}, {"time": 2.3333, "x": -45.41, "y": 0}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": -20.43, "y": 0}, {"time": 3, "x": -45.41, "y": 0}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": -20.43, "y": 0}]}, "Coin-41": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -128.05, "y": 339.43}, {"time": 0.1, "x": -160.06, "y": 424.29}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": -128.05, "y": 339.43}, {"time": 0.7667, "x": -160.06, "y": 424.29}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": -128.05, "y": 339.43}, {"time": 1.4333, "x": -160.06, "y": 424.29}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -128.05, "y": 339.43}, {"time": 2.1, "x": -160.06, "y": 424.29}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": -128.05, "y": 339.43}, {"time": 2.7667, "x": -160.06, "y": 424.29}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": -128.05, "y": 339.43}], "scale": [{"time": 0, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 0.0333, "x": 2.589, "y": 2.589}, {"time": 0.1, "x": 3.236, "y": 3.236}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 0.7, "x": 2.589, "y": 2.589}, {"time": 0.7667, "x": 3.236, "y": 3.236}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 1.3667, "x": 2.589, "y": 2.589}, {"time": 1.4333, "x": 3.236, "y": 3.236}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 2.0333, "x": 2.589, "y": 2.589}, {"time": 2.1, "x": 3.236, "y": 3.236}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.589, "y": 2.589, "curve": "stepped"}, {"time": 2.7, "x": 2.589, "y": 2.589}, {"time": 2.7667, "x": 3.236, "y": 3.236}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.589, "y": 2.589}], "shear": [{"time": 0, "x": -34.78, "y": 0}, {"time": 0.1, "x": -43.48, "y": 0}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.6667, "x": -34.78, "y": 0}, {"time": 0.7667, "x": -43.48, "y": 0}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.3333, "x": -34.78, "y": 0}, {"time": 1.4333, "x": -43.48, "y": 0}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 2, "x": -34.78, "y": 0}, {"time": 2.1, "x": -43.48, "y": 0}, {"time": 2.1333, "x": 0, "y": 0}, {"time": 2.6667, "x": -34.78, "y": 0}, {"time": 2.7667, "x": -43.48, "y": 0}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3.3333, "x": -34.78, "y": 0}]}, "Coin-42": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 146.57, "y": -286.09}, {"time": 0.2667, "x": 266.5, "y": -520.16}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 146.57, "y": -286.09}, {"time": 0.9333, "x": 266.5, "y": -520.16}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 146.57, "y": -286.09}, {"time": 1.6, "x": 266.5, "y": -520.16}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 146.57, "y": -286.09}, {"time": 2.2667, "x": 266.5, "y": -520.16}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 146.57, "y": -286.09}, {"time": 2.9333, "x": 266.5, "y": -520.16}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 146.57, "y": -286.09}], "scale": [{"time": 0, "x": 1.467, "y": 1.467}, {"time": 0.2667, "x": 2.667, "y": 2.667}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.467, "y": 1.467}, {"time": 0.9333, "x": 2.667, "y": 2.667}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.467, "y": 1.467}, {"time": 1.6, "x": 2.667, "y": 2.667}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 1.467, "y": 1.467}, {"time": 2.2667, "x": 2.667, "y": 2.667}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.467, "y": 1.467}, {"time": 2.9333, "x": 2.667, "y": 2.667}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.467, "y": 1.467}], "shear": [{"time": 0, "x": 24.56, "y": 0}, {"time": 0.2667, "x": 44.65, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 24.56, "y": 0}, {"time": 0.9333, "x": 44.65, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 24.56, "y": 0}, {"time": 1.6, "x": 44.65, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 24.56, "y": 0}, {"time": 2.2667, "x": 44.65, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 24.56, "y": 0}, {"time": 2.9333, "x": 44.65, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 24.56, "y": 0}]}, "Coin-43": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -548.84, "y": -4.5}, {"time": 0.1333, "x": -731.78, "y": -6}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": -548.84, "y": -4.5}, {"time": 0.8, "x": -731.78, "y": -6}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": -548.84, "y": -4.5}, {"time": 1.4667, "x": -731.78, "y": -6}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": -548.84, "y": -4.5}, {"time": 2.1333, "x": -731.78, "y": -6}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": -548.84, "y": -4.5}, {"time": 2.8, "x": -731.78, "y": -6}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": -548.84, "y": -4.5}], "scale": [{"time": 0, "x": 2.001, "y": 2.001}, {"time": 0.1333, "x": 2.667, "y": 2.667}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.001, "y": 2.001}, {"time": 0.8, "x": 2.667, "y": 2.667}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.001, "y": 2.001}, {"time": 1.4667, "x": 2.667, "y": 2.667}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 2.001, "y": 2.001}, {"time": 2.1333, "x": 2.667, "y": 2.667}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.001, "y": 2.001}, {"time": 2.8, "x": 2.667, "y": 2.667}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.001, "y": 2.001}], "shear": [{"time": 0, "x": 33.49, "y": 0}, {"time": 0.1333, "x": 44.65, "y": 0}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 33.49, "y": 0}, {"time": 0.8, "x": 44.65, "y": 0}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 33.49, "y": 0}, {"time": 1.4667, "x": 44.65, "y": 0}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 33.49, "y": 0}, {"time": 2.1333, "x": 44.65, "y": 0}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 33.49, "y": 0}, {"time": 2.8, "x": 44.65, "y": 0}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 33.49, "y": 0}]}, "Coin-44": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 442.76, "y": -12.89}, {"time": 0.2, "x": 681.17, "y": -19.83}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 442.76, "y": -12.89}, {"time": 0.8667, "x": 681.17, "y": -19.83}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 442.76, "y": -12.89}, {"time": 1.5333, "x": 681.17, "y": -19.83}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 442.76, "y": -12.89}, {"time": 2.2, "x": 681.17, "y": -19.83}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 442.76, "y": -12.89}, {"time": 2.8667, "x": 681.17, "y": -19.83}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 442.76, "y": -12.89}], "scale": [{"time": 0, "x": 2.104, "y": 2.104}, {"time": 0.2, "x": 3.236, "y": 3.236}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.104, "y": 2.104}, {"time": 0.8667, "x": 3.236, "y": 3.236}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.104, "y": 2.104}, {"time": 1.5333, "x": 3.236, "y": 3.236}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": 2.104, "y": 2.104}, {"time": 2.2, "x": 3.236, "y": 3.236}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.104, "y": 2.104}, {"time": 2.8667, "x": 3.236, "y": 3.236}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.104, "y": 2.104}], "shear": [{"time": 0, "x": -28.26, "y": 0}, {"time": 0.2, "x": -43.48, "y": 0}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.6667, "x": -28.26, "y": 0}, {"time": 0.8667, "x": -43.48, "y": 0}, {"time": 0.9, "x": 0, "y": 0}, {"time": 1.3333, "x": -28.26, "y": 0}, {"time": 1.5333, "x": -43.48, "y": 0}, {"time": 1.5667, "x": 0, "y": 0}, {"time": 2, "x": -28.26, "y": 0}, {"time": 2.2, "x": -43.48, "y": 0}, {"time": 2.2333, "x": 0, "y": 0}, {"time": 2.6667, "x": -28.26, "y": 0}, {"time": 2.8667, "x": -43.48, "y": 0}, {"time": 2.9, "x": 0, "y": 0}, {"time": 3.3333, "x": -28.26, "y": 0}]}, "Coin-45": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 6.91, "y": -274.8}, {"time": 0.2667, "x": 12.57, "y": -499.64}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 6.91, "y": -274.8}, {"time": 0.9333, "x": 12.57, "y": -499.64}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 6.91, "y": -274.8}, {"time": 1.6, "x": 12.57, "y": -499.64}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 6.91, "y": -274.8}, {"time": 2.2667, "x": 12.57, "y": -499.64}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 6.91, "y": -274.8}, {"time": 2.9333, "x": 12.57, "y": -499.64}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 6.91, "y": -274.8}], "scale": [{"time": 0, "x": 1.706, "y": 1.706}, {"time": 0.2667, "x": 3.101, "y": 3.101}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.706, "y": 1.706}, {"time": 0.9333, "x": 3.101, "y": 3.101}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.706, "y": 1.706}, {"time": 1.6, "x": 3.101, "y": 3.101}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 1.706, "y": 1.706}, {"time": 2.2667, "x": 3.101, "y": 3.101}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.706, "y": 1.706}, {"time": 2.9333, "x": 3.101, "y": 3.101}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.706, "y": 1.706}], "shear": [{"time": 0, "x": -24.97, "y": 0}, {"time": 0.2667, "x": -45.41, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": -24.97, "y": 0}, {"time": 0.9333, "x": -45.41, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": -24.97, "y": 0}, {"time": 1.6, "x": -45.41, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": -24.97, "y": 0}, {"time": 2.2667, "x": -45.41, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": -24.97, "y": 0}, {"time": 2.9333, "x": -45.41, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": -24.97, "y": 0}]}, "Coin-46": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.8667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 2.44, "y": 281.06}, {"time": 0.1667, "x": 3.48, "y": 401.52}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.44, "y": 281.06}, {"time": 0.8333, "x": 3.48, "y": 401.52}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.44, "y": 281.06}, {"time": 1.5, "x": 3.48, "y": 401.52}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 2.44, "y": 281.06}, {"time": 2.1667, "x": 3.48, "y": 401.52}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.44, "y": 281.06}, {"time": 2.8333, "x": 3.48, "y": 401.52}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.44, "y": 281.06}], "scale": [{"time": 0, "x": 2.257, "y": 2.257}, {"time": 0.1667, "x": 3.224, "y": 3.224}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.257, "y": 2.257}, {"time": 0.8333, "x": 3.224, "y": 3.224}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.257, "y": 2.257}, {"time": 1.5, "x": 3.224, "y": 3.224}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 2.257, "y": 2.257}, {"time": 2.1667, "x": 3.224, "y": 3.224}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.257, "y": 2.257}, {"time": 2.8333, "x": 3.224, "y": 3.224}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.257, "y": 2.257}], "shear": [{"time": 0, "x": 15.1, "y": 0}, {"time": 0.1667, "x": 21.58, "y": 0}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.6667, "x": 15.1, "y": 0}, {"time": 0.8333, "x": 21.58, "y": 0}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.3333, "x": 15.1, "y": 0}, {"time": 1.5, "x": 21.58, "y": 0}, {"time": 1.5333, "x": 0, "y": 0}, {"time": 2, "x": 15.1, "y": 0}, {"time": 2.1667, "x": 21.58, "y": 0}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.6667, "x": 15.1, "y": 0}, {"time": 2.8333, "x": 21.58, "y": 0}, {"time": 2.8667, "x": 0, "y": 0}, {"time": 3.3333, "x": 15.1, "y": 0}]}, "Coin-47": {"rotate": [{"time": 0, "angle": 0.75}, {"time": 0.1333, "angle": 1}, {"time": 0.1667, "angle": 0}, {"time": 0.6667, "angle": 0.75}, {"time": 0.8, "angle": 1}, {"time": 0.8333, "angle": 0}, {"time": 1.3333, "angle": 0.75}, {"time": 1.4667, "angle": 1}, {"time": 1.5, "angle": 0}, {"time": 2, "angle": 0.75}, {"time": 2.1333, "angle": 1}, {"time": 2.1667, "angle": 0}, {"time": 2.6667, "angle": 0.75}, {"time": 2.8, "angle": 1}, {"time": 2.8333, "angle": 0}, {"time": 3.3333, "angle": 0.75}], "translate": [{"time": 0, "x": 490.23, "y": -322.34}, {"time": 0.1333, "x": 653.64, "y": -429.78}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 490.23, "y": -322.34}, {"time": 0.8, "x": 653.64, "y": -429.78}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 490.23, "y": -322.34}, {"time": 1.4667, "x": 653.64, "y": -429.78}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 490.23, "y": -322.34}, {"time": 2.1333, "x": 653.64, "y": -429.78}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 490.23, "y": -322.34}, {"time": 2.8, "x": 653.64, "y": -429.78}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 490.23, "y": -322.34}], "scale": [{"time": 0, "x": 2.333, "y": 2.333}, {"time": 0.1333, "x": 3.111, "y": 3.111}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.333, "y": 2.333}, {"time": 0.8, "x": 3.111, "y": 3.111}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.333, "y": 2.333}, {"time": 1.4667, "x": 3.111, "y": 3.111}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 2.333, "y": 2.333}, {"time": 2.1333, "x": 3.111, "y": 3.111}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.333, "y": 2.333}, {"time": 2.8, "x": 3.111, "y": 3.111}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.333, "y": 2.333}], "shear": [{"time": 0, "x": 21.89, "y": 0}, {"time": 0.1333, "x": 29.19, "y": 0}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.6667, "x": 21.89, "y": 0}, {"time": 0.8, "x": 29.19, "y": 0}, {"time": 0.8333, "x": 0, "y": 0}, {"time": 1.3333, "x": 21.89, "y": 0}, {"time": 1.4667, "x": 29.19, "y": 0}, {"time": 1.5, "x": 0, "y": 0}, {"time": 2, "x": 21.89, "y": 0}, {"time": 2.1333, "x": 29.19, "y": 0}, {"time": 2.1667, "x": 0, "y": 0}, {"time": 2.6667, "x": 21.89, "y": 0}, {"time": 2.8, "x": 29.19, "y": 0}, {"time": 2.8333, "x": 0, "y": 0}, {"time": 3.3333, "x": 21.89, "y": 0}]}, "Coin-48": {"rotate": [{"time": 0, "angle": 0.3}, {"time": 0.4333, "angle": 1}, {"time": 0.4667, "angle": 0}, {"time": 0.6667, "angle": 0.3}, {"time": 1.1, "angle": 1}, {"time": 1.1333, "angle": 0}, {"time": 1.3333, "angle": 0.3}, {"time": 1.7667, "angle": 1}, {"time": 1.8, "angle": 0}, {"time": 2, "angle": 0.3}, {"time": 2.4333, "angle": 1}, {"time": 2.4667, "angle": 0}, {"time": 2.6667, "angle": 0.3}, {"time": 3.1, "angle": 1}, {"time": 3.1333, "angle": 0}, {"time": 3.3333, "angle": 0.3}], "translate": [{"time": 0, "x": 80.81, "y": 124.16}, {"time": 0.4333, "x": 269.35, "y": 413.86}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 80.81, "y": 124.16}, {"time": 1.1, "x": 269.35, "y": 413.86}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 80.81, "y": 124.16}, {"time": 1.7667, "x": 269.35, "y": 413.86}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 80.81, "y": 124.16}, {"time": 2.4333, "x": 269.35, "y": 413.86}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 80.81, "y": 124.16}, {"time": 3.1, "x": 269.35, "y": 413.86}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 80.81, "y": 124.16}], "scale": [{"time": 0, "x": 0.933, "y": 0.933}, {"time": 0.4333, "x": 3.111, "y": 3.111}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 0.933, "y": 0.933}, {"time": 1.1, "x": 3.111, "y": 3.111}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 0.933, "y": 0.933}, {"time": 1.7667, "x": 3.111, "y": 3.111}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 0.933, "y": 0.933}, {"time": 2.4333, "x": 3.111, "y": 3.111}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.933, "y": 0.933}, {"time": 3.1, "x": 3.111, "y": 3.111}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 0.933, "y": 0.933}], "shear": [{"time": 0, "x": 8.76, "y": 0}, {"time": 0.4333, "x": 29.19, "y": 0}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 0.6667, "x": 8.76, "y": 0}, {"time": 1.1, "x": 29.19, "y": 0}, {"time": 1.1333, "x": 0, "y": 0}, {"time": 1.3333, "x": 8.76, "y": 0}, {"time": 1.7667, "x": 29.19, "y": 0}, {"time": 1.8, "x": 0, "y": 0}, {"time": 2, "x": 8.76, "y": 0}, {"time": 2.4333, "x": 29.19, "y": 0}, {"time": 2.4667, "x": 0, "y": 0}, {"time": 2.6667, "x": 8.76, "y": 0}, {"time": 3.1, "x": 29.19, "y": 0}, {"time": 3.1333, "x": 0, "y": 0}, {"time": 3.3333, "x": 8.76, "y": 0}]}, "Coin-49": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.7667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.1, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.7667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 427.51, "y": -389.38}, {"time": 0.0667, "x": 502.95, "y": -458.09}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 427.51, "y": -389.38}, {"time": 0.7333, "x": 502.95, "y": -458.09}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 427.51, "y": -389.38}, {"time": 1.4, "x": 502.95, "y": -458.09}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 427.51, "y": -389.38}, {"time": 2.0667, "x": 502.95, "y": -458.09}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 427.51, "y": -389.38}, {"time": 2.7333, "x": 502.95, "y": -458.09}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 427.51, "y": -389.38}], "scale": [{"time": 0, "x": 2.351, "y": 2.351}, {"time": 0.0667, "x": 2.766, "y": 2.766}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.351, "y": 2.351}, {"time": 0.7333, "x": 2.766, "y": 2.766}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.351, "y": 2.351}, {"time": 1.4, "x": 2.766, "y": 2.766}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 2.351, "y": 2.351}, {"time": 2.0667, "x": 2.766, "y": 2.766}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.351, "y": 2.351}, {"time": 2.7333, "x": 2.766, "y": 2.766}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.351, "y": 2.351}], "shear": [{"time": 0, "x": 0, "y": -31.74}, {"time": 0.0667, "x": 0, "y": -37.34}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.6667, "x": 0, "y": -31.74}, {"time": 0.7333, "x": 0, "y": -37.34}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 1.3333, "x": 0, "y": -31.74}, {"time": 1.4, "x": 0, "y": -37.34}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 2, "x": 0, "y": -31.74}, {"time": 2.0667, "x": 0, "y": -37.34}, {"time": 2.1, "x": 0, "y": 0}, {"time": 2.6667, "x": 0, "y": -31.74}, {"time": 2.7333, "x": 0, "y": -37.34}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3.3333, "x": 0, "y": -31.74}]}, "Coin-50": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3667, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.0333, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.0333, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -258.84, "y": -207.35}, {"time": 0.3333, "x": -575.2, "y": -460.77}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": -258.84, "y": -207.35}, {"time": 1, "x": -575.2, "y": -460.77}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": -258.84, "y": -207.35}, {"time": 1.6667, "x": -575.2, "y": -460.77}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": -258.84, "y": -207.35}, {"time": 2.3333, "x": -575.2, "y": -460.77}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": -258.84, "y": -207.35}, {"time": 3, "x": -575.2, "y": -460.77}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": -258.84, "y": -207.35}], "scale": [{"time": 0, "x": 1.161, "y": 1.161}, {"time": 0.3333, "x": 2.58, "y": 2.58}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 1.161, "y": 1.161}, {"time": 1, "x": 2.58, "y": 2.58}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 1.161, "y": 1.161}, {"time": 1.6667, "x": 2.58, "y": 2.58}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 1.161, "y": 1.161}, {"time": 2.3333, "x": 2.58, "y": 2.58}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 1.161, "y": 1.161}, {"time": 3, "x": 2.58, "y": 2.58}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 1.161, "y": 1.161}], "shear": [{"time": 0, "x": 18.28, "y": 0}, {"time": 0.3333, "x": 40.63, "y": 0}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.6667, "x": 18.28, "y": 0}, {"time": 1, "x": 40.63, "y": 0}, {"time": 1.0333, "x": 0, "y": 0}, {"time": 1.3333, "x": 18.28, "y": 0}, {"time": 1.6667, "x": 40.63, "y": 0}, {"time": 1.7, "x": 0, "y": 0}, {"time": 2, "x": 18.28, "y": 0}, {"time": 2.3333, "x": 40.63, "y": 0}, {"time": 2.3667, "x": 0, "y": 0}, {"time": 2.6667, "x": 18.28, "y": 0}, {"time": 3, "x": 40.63, "y": 0}, {"time": 3.0333, "x": 0, "y": 0}, {"time": 3.3333, "x": 18.28, "y": 0}]}, "Coin-51": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": -242.57, "y": 216.27}, {"time": 0.2667, "x": -441.04, "y": 393.23}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": -242.57, "y": 216.27}, {"time": 0.9333, "x": -441.04, "y": 393.23}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": -242.57, "y": 216.27}, {"time": 1.6, "x": -441.04, "y": 393.23}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": -242.57, "y": 216.27}, {"time": 2.2667, "x": -441.04, "y": 393.23}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": -242.57, "y": 216.27}, {"time": 2.9333, "x": -441.04, "y": 393.23}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": -242.57, "y": 216.27}], "scale": [{"time": 0, "x": 2.172, "y": 2.172}, {"time": 0.2667, "x": 3.95, "y": 3.95}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 2.172, "y": 2.172}, {"time": 0.9333, "x": 3.95, "y": 3.95}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 2.172, "y": 2.172}, {"time": 1.6, "x": 3.95, "y": 3.95}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 2.172, "y": 2.172}, {"time": 2.2667, "x": 3.95, "y": 3.95}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 2.172, "y": 2.172}, {"time": 2.9333, "x": 3.95, "y": 3.95}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 2.172, "y": 2.172}], "shear": [{"time": 0, "x": 13.16, "y": 0}, {"time": 0.2667, "x": 23.92, "y": 0}, {"time": 0.3, "x": 0, "y": 0}, {"time": 0.6667, "x": 13.16, "y": 0}, {"time": 0.9333, "x": 23.92, "y": 0}, {"time": 0.9667, "x": 0, "y": 0}, {"time": 1.3333, "x": 13.16, "y": 0}, {"time": 1.6, "x": 23.92, "y": 0}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2, "x": 13.16, "y": 0}, {"time": 2.2667, "x": 23.92, "y": 0}, {"time": 2.3, "x": 0, "y": 0}, {"time": 2.6667, "x": 13.16, "y": 0}, {"time": 2.9333, "x": 23.92, "y": 0}, {"time": 2.9667, "x": 0, "y": 0}, {"time": 3.3333, "x": 13.16, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.2, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": "stepped"}, {"time": 1.8, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.2, "angle": 0, "curve": "stepped"}, {"time": 2.4, "angle": 0, "curve": "stepped"}, {"time": 2.6, "angle": 0, "curve": "stepped"}, {"time": 2.8, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0, "curve": "stepped"}, {"time": 3.2, "angle": 0, "curve": "stepped"}, {"time": 3.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.1, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.3, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.5, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.7, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.9, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.1, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.3, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.5, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.7, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.9, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.1, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.3, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.4, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.5, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.6, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.7, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 2.8, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 2.9, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 3.1, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3.2, "x": 1, "y": 1, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 3.3, "x": 0.73, "y": 0.73, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 3.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]