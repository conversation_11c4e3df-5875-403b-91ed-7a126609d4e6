[1, ["c65RwHrHdJuoqOTMFfLxjX", "1by5GwKBZKNptwmjzvMWNh"], ["spriteFrame", "_textureSetter"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[[0, "font_title_popup", 32, {"commonHeight": 80, "fontSize": 32, "atlasName": "C:\\Users\\<USER>\\Downloads\\Telegra", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 96, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 12, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 0, "yOffset": 49, "xAdvance": 10, "rect": {"x": 342, "y": 284, "width": 6, "height": 23}}, "34": {"xOffset": 0, "yOffset": 49, "xAdvance": 17, "rect": {"x": 283, "y": 20, "width": 13, "height": 10}}, "35": {"xOffset": 0, "yOffset": 51, "xAdvance": 22, "rect": {"x": 245, "y": 55, "width": 18, "height": 20}}, "36": {"xOffset": 0, "yOffset": 47, "xAdvance": 19, "rect": {"x": 299, "y": 196, "width": 15, "height": 28}}, "37": {"xOffset": 0, "yOffset": 49, "xAdvance": 28, "rect": {"x": 26, "y": 159, "width": 24, "height": 24}}, "38": {"xOffset": 0, "yOffset": 49, "xAdvance": 25, "rect": {"x": 100, "y": 301, "width": 21, "height": 24}}, "39": {"xOffset": 0, "yOffset": 49, "xAdvance": 11, "rect": {"x": 340, "y": 196, "width": 7, "height": 11}}, "40": {"xOffset": 0, "yOffset": 49, "xAdvance": 16, "rect": {"x": 329, "y": 0, "width": 12, "height": 31}}, "41": {"xOffset": 0, "yOffset": 49, "xAdvance": 17, "rect": {"x": 315, "y": 87, "width": 13, "height": 31}}, "42": {"xOffset": 0, "yOffset": 49, "xAdvance": 18, "rect": {"x": 299, "y": 270, "width": 14, "height": 14}}, "43": {"xOffset": 0, "yOffset": 56, "xAdvance": 20, "rect": {"x": 282, "y": 295, "width": 16, "height": 16}}, "44": {"xOffset": 0, "yOffset": 66, "xAdvance": 10, "rect": {"x": 307, "y": 309, "width": 6, "height": 11}}, "45": {"xOffset": 0, "yOffset": 60, "xAdvance": 13, "rect": {"x": 314, "y": 345, "width": 9, "height": 6}}, "46": {"xOffset": 0, "yOffset": 66, "xAdvance": 10, "rect": {"x": 324, "y": 345, "width": 6, "height": 6}}, "47": {"xOffset": 0, "yOffset": 49, "xAdvance": 18, "rect": {"x": 299, "y": 243, "width": 14, "height": 26}}, "48": {"xOffset": 0, "yOffset": 56, "xAdvance": 21, "rect": {"x": 264, "y": 110, "width": 17, "height": 24}}, "49": {"xOffset": 0, "yOffset": 56, "xAdvance": 14, "rect": {"x": 329, "y": 128, "width": 10, "height": 23}}, "50": {"xOffset": 0, "yOffset": 56, "xAdvance": 21, "rect": {"x": 264, "y": 86, "width": 17, "height": 23}}, "51": {"xOffset": 0, "yOffset": 56, "xAdvance": 21, "rect": {"x": 264, "y": 33, "width": 17, "height": 24}}, "52": {"xOffset": 0, "yOffset": 56, "xAdvance": 23, "rect": {"x": 204, "y": 328, "width": 19, "height": 23}}, "53": {"xOffset": 0, "yOffset": 56, "xAdvance": 22, "rect": {"x": 245, "y": 76, "width": 18, "height": 24}}, "54": {"xOffset": 0, "yOffset": 56, "xAdvance": 22, "rect": {"x": 244, "y": 256, "width": 18, "height": 24}}, "55": {"xOffset": 0, "yOffset": 56, "xAdvance": 20, "rect": {"x": 282, "y": 119, "width": 16, "height": 24}}, "56": {"xOffset": 0, "yOffset": 56, "xAdvance": 22, "rect": {"x": 244, "y": 205, "width": 18, "height": 24}}, "57": {"xOffset": 0, "yOffset": 56, "xAdvance": 23, "rect": {"x": 224, "y": 298, "width": 19, "height": 24}}, "58": {"xOffset": 0, "yOffset": 55, "xAdvance": 11, "rect": {"x": 299, "y": 309, "width": 7, "height": 17}}, "59": {"xOffset": 0, "yOffset": 55, "xAdvance": 11, "rect": {"x": 340, "y": 125, "width": 7, "height": 22}}, "60": {"xOffset": 0, "yOffset": 55, "xAdvance": 21, "rect": {"x": 264, "y": 186, "width": 17, "height": 18}}, "61": {"xOffset": 0, "yOffset": 57, "xAdvance": 20, "rect": {"x": 282, "y": 312, "width": 16, "height": 14}}, "62": {"xOffset": 0, "yOffset": 55, "xAdvance": 20, "rect": {"x": 282, "y": 251, "width": 16, "height": 18}}, "63": {"xOffset": 0, "yOffset": 49, "xAdvance": 20, "rect": {"x": 282, "y": 144, "width": 16, "height": 23}}, "64": {"xOffset": 0, "yOffset": 49, "xAdvance": 27, "rect": {"x": 100, "y": 0, "width": 23, "height": 24}}, "65": {"xOffset": 0, "yOffset": 57, "xAdvance": 27, "rect": {"x": 52, "y": 111, "width": 23, "height": 23}}, "66": {"xOffset": 0, "yOffset": 57, "xAdvance": 22, "rect": {"x": 244, "y": 281, "width": 18, "height": 23}}, "67": {"xOffset": 0, "yOffset": 56, "xAdvance": 28, "rect": {"x": 26, "y": 208, "width": 24, "height": 24}}, "68": {"xOffset": 0, "yOffset": 57, "xAdvance": 25, "rect": {"x": 100, "y": 326, "width": 21, "height": 23}}, "69": {"xOffset": 0, "yOffset": 57, "xAdvance": 17, "rect": {"x": 314, "y": 320, "width": 13, "height": 23}}, "70": {"xOffset": 0, "yOffset": 57, "xAdvance": 17, "rect": {"x": 315, "y": 63, "width": 13, "height": 23}}, "71": {"xOffset": 0, "yOffset": 56, "xAdvance": 29, "rect": {"x": 0, "y": 119, "width": 25, "height": 25}}, "72": {"xOffset": 0, "yOffset": 57, "xAdvance": 23, "rect": {"x": 225, "y": 56, "width": 19, "height": 23}}, "73": {"xOffset": 0, "yOffset": 57, "xAdvance": 11, "rect": {"x": 340, "y": 148, "width": 7, "height": 23}}, "74": {"xOffset": 0, "yOffset": 57, "xAdvance": 20, "rect": {"x": 282, "y": 95, "width": 16, "height": 23}}, "75": {"xOffset": 0, "yOffset": 57, "xAdvance": 23, "rect": {"x": 225, "y": 106, "width": 19, "height": 23}}, "76": {"xOffset": 0, "yOffset": 57, "xAdvance": 18, "rect": {"x": 300, "y": 68, "width": 14, "height": 23}}, "77": {"xOffset": 0, "yOffset": 57, "xAdvance": 28, "rect": {"x": 26, "y": 184, "width": 24, "height": 23}}, "78": {"xOffset": 0, "yOffset": 57, "xAdvance": 24, "rect": {"x": 143, "y": 299, "width": 20, "height": 23}}, "79": {"xOffset": 0, "yOffset": 56, "xAdvance": 29, "rect": {"x": 0, "y": 183, "width": 25, "height": 24}}, "80": {"xOffset": 0, "yOffset": 57, "xAdvance": 22, "rect": {"x": 263, "y": 292, "width": 18, "height": 23}}, "81": {"xOffset": 0, "yOffset": 56, "xAdvance": 29, "rect": {"x": 0, "y": 93, "width": 25, "height": 25}}, "82": {"xOffset": 0, "yOffset": 57, "xAdvance": 22, "rect": {"x": 263, "y": 237, "width": 18, "height": 23}}, "83": {"xOffset": 0, "yOffset": 56, "xAdvance": 21, "rect": {"x": 264, "y": 135, "width": 17, "height": 24}}, "84": {"xOffset": 0, "yOffset": 57, "xAdvance": 19, "rect": {"x": 298, "y": 327, "width": 15, "height": 23}}, "85": {"xOffset": 0, "yOffset": 57, "xAdvance": 22, "rect": {"x": 245, "y": 101, "width": 18, "height": 23}}, "86": {"xOffset": 0, "yOffset": 57, "xAdvance": 25, "rect": {"x": 30, "y": 0, "width": 21, "height": 23}}, "87": {"xOffset": 0, "yOffset": 57, "xAdvance": 33, "rect": {"x": 0, "y": 0, "width": 29, "height": 23}}, "88": {"xOffset": 0, "yOffset": 57, "xAdvance": 26, "rect": {"x": 100, "y": 149, "width": 22, "height": 23}}, "89": {"xOffset": 0, "yOffset": 57, "xAdvance": 24, "rect": {"x": 143, "y": 323, "width": 20, "height": 23}}, "90": {"xOffset": 0, "yOffset": 57, "xAdvance": 19, "rect": {"x": 282, "y": 327, "width": 15, "height": 23}}, "91": {"xOffset": 0, "yOffset": 49, "xAdvance": 14, "rect": {"x": 329, "y": 64, "width": 10, "height": 31}}, "92": {"xOffset": 0, "yOffset": 49, "xAdvance": 19, "rect": {"x": 299, "y": 169, "width": 15, "height": 26}}, "93": {"xOffset": 0, "yOffset": 49, "xAdvance": 14, "rect": {"x": 329, "y": 32, "width": 10, "height": 31}}, "94": {"xOffset": 0, "yOffset": 49, "xAdvance": 20, "rect": {"x": 164, "y": 336, "width": 16, "height": 12}}, "95": {"xOffset": 0, "yOffset": 73, "xAdvance": 20, "rect": {"x": 26, "y": 348, "width": 16, "height": 3}}, "96": {"xOffset": 0, "yOffset": 49, "xAdvance": 10, "rect": {"x": 340, "y": 272, "width": 6, "height": 11}}, "97": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 165, "y": 20, "width": 19, "height": 19}}, "98": {"xOffset": 0, "yOffset": 57, "xAdvance": 23, "rect": {"x": 205, "y": 26, "width": 19, "height": 24}}, "99": {"xOffset": 0, "yOffset": 62, "xAdvance": 22, "rect": {"x": 205, "y": 132, "width": 18, "height": 19}}, "100": {"xOffset": 0, "yOffset": 56, "xAdvance": 23, "rect": {"x": 144, "y": 243, "width": 19, "height": 24}}, "101": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 244, "y": 156, "width": 19, "height": 19}}, "102": {"xOffset": 0, "yOffset": 56, "xAdvance": 14, "rect": {"x": 329, "y": 180, "width": 10, "height": 23}}, "103": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 164, "y": 309, "width": 19, "height": 26}}, "104": {"xOffset": 0, "yOffset": 56, "xAdvance": 21, "rect": {"x": 282, "y": 33, "width": 17, "height": 23}}, "105": {"xOffset": 0, "yOffset": 56, "xAdvance": 10, "rect": {"x": 340, "y": 321, "width": 6, "height": 23}}, "106": {"xOffset": 0, "yOffset": 56, "xAdvance": 13, "rect": {"x": 340, "y": 61, "width": 9, "height": 29}}, "107": {"xOffset": 0, "yOffset": 56, "xAdvance": 20, "rect": {"x": 282, "y": 270, "width": 16, "height": 24}}, "108": {"xOffset": 0, "yOffset": 56, "xAdvance": 11, "rect": {"x": 340, "y": 172, "width": 7, "height": 23}}, "109": {"xOffset": 0, "yOffset": 62, "xAdvance": 30, "rect": {"x": 0, "y": 24, "width": 26, "height": 18}}, "110": {"xOffset": 0, "yOffset": 62, "xAdvance": 21, "rect": {"x": 282, "y": 76, "width": 17, "height": 18}}, "111": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 165, "y": 0, "width": 19, "height": 19}}, "112": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 204, "y": 216, "width": 19, "height": 24}}, "113": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 205, "y": 107, "width": 19, "height": 24}}, "114": {"xOffset": 0, "yOffset": 61, "xAdvance": 14, "rect": {"x": 329, "y": 231, "width": 10, "height": 18}}, "115": {"xOffset": 0, "yOffset": 61, "xAdvance": 19, "rect": {"x": 283, "y": 0, "width": 15, "height": 19}}, "116": {"xOffset": 0, "yOffset": 56, "xAdvance": 15, "rect": {"x": 328, "y": 321, "width": 11, "height": 23}}, "117": {"xOffset": 0, "yOffset": 62, "xAdvance": 21, "rect": {"x": 282, "y": 57, "width": 17, "height": 18}}, "118": {"xOffset": 0, "yOffset": 62, "xAdvance": 22, "rect": {"x": 185, "y": 152, "width": 18, "height": 18}}, "119": {"xOffset": 0, "yOffset": 62, "xAdvance": 30, "rect": {"x": 0, "y": 43, "width": 26, "height": 17}}, "120": {"xOffset": 0, "yOffset": 62, "xAdvance": 24, "rect": {"x": 26, "y": 329, "width": 20, "height": 18}}, "121": {"xOffset": 0, "yOffset": 62, "xAdvance": 22, "rect": {"x": 245, "y": 31, "width": 18, "height": 23}}, "122": {"xOffset": 0, "yOffset": 62, "xAdvance": 18, "rect": {"x": 299, "y": 225, "width": 14, "height": 17}}, "123": {"xOffset": 0, "yOffset": 49, "xAdvance": 17, "rect": {"x": 315, "y": 189, "width": 13, "height": 31}}, "125": {"xOffset": 0, "yOffset": 49, "xAdvance": 17, "rect": {"x": 315, "y": 157, "width": 13, "height": 31}}, "126": {"xOffset": 0, "yOffset": 60, "xAdvance": 19, "rect": {"x": 244, "y": 338, "width": 15, "height": 9}}, "192": {"xOffset": 0, "yOffset": 49, "xAdvance": 27, "rect": {"x": 76, "y": 286, "width": 23, "height": 31}}, "193": {"xOffset": 0, "yOffset": 49, "xAdvance": 27, "rect": {"x": 76, "y": 254, "width": 23, "height": 31}}, "194": {"xOffset": 0, "yOffset": 50, "xAdvance": 27, "rect": {"x": 76, "y": 223, "width": 23, "height": 30}}, "195": {"xOffset": 0, "yOffset": 51, "xAdvance": 27, "rect": {"x": 100, "y": 25, "width": 23, "height": 29}}, "200": {"xOffset": 0, "yOffset": 48, "xAdvance": 17, "rect": {"x": 315, "y": 31, "width": 13, "height": 31}}, "201": {"xOffset": 0, "yOffset": 48, "xAdvance": 18, "rect": {"x": 314, "y": 225, "width": 14, "height": 31}}, "202": {"xOffset": 0, "yOffset": 49, "xAdvance": 18, "rect": {"x": 299, "y": 0, "width": 14, "height": 30}}, "204": {"xOffset": 0, "yOffset": 49, "xAdvance": 14, "rect": {"x": 329, "y": 96, "width": 10, "height": 31}}, "205": {"xOffset": 0, "yOffset": 49, "xAdvance": 14, "rect": {"x": 329, "y": 250, "width": 10, "height": 31}}, "210": {"xOffset": 0, "yOffset": 49, "xAdvance": 29, "rect": {"x": 26, "y": 127, "width": 25, "height": 31}}, "211": {"xOffset": 0, "yOffset": 49, "xAdvance": 29, "rect": {"x": 0, "y": 61, "width": 25, "height": 31}}, "212": {"xOffset": 0, "yOffset": 50, "xAdvance": 29, "rect": {"x": 26, "y": 96, "width": 25, "height": 30}}, "213": {"xOffset": 0, "yOffset": 51, "xAdvance": 28, "rect": {"x": 51, "y": 159, "width": 24, "height": 29}}, "217": {"xOffset": 0, "yOffset": 48, "xAdvance": 22, "rect": {"x": 244, "y": 305, "width": 18, "height": 32}}, "218": {"xOffset": 0, "yOffset": 48, "xAdvance": 23, "rect": {"x": 204, "y": 183, "width": 19, "height": 32}}, "221": {"xOffset": 0, "yOffset": 50, "xAdvance": 24, "rect": {"x": 123, "y": 119, "width": 20, "height": 30}}, "224": {"xOffset": 0, "yOffset": 54, "xAdvance": 23, "rect": {"x": 165, "y": 40, "width": 19, "height": 26}}, "225": {"xOffset": 0, "yOffset": 54, "xAdvance": 23, "rect": {"x": 204, "y": 274, "width": 19, "height": 26}}, "226": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 165, "y": 149, "width": 19, "height": 25}}, "227": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 165, "y": 96, "width": 19, "height": 25}}, "232": {"xOffset": 0, "yOffset": 53, "xAdvance": 23, "rect": {"x": 225, "y": 0, "width": 19, "height": 27}}, "233": {"xOffset": 0, "yOffset": 53, "xAdvance": 23, "rect": {"x": 225, "y": 28, "width": 19, "height": 27}}, "234": {"xOffset": 0, "yOffset": 54, "xAdvance": 23, "rect": {"x": 224, "y": 323, "width": 19, "height": 26}}, "236": {"xOffset": 0, "yOffset": 54, "xAdvance": 14, "rect": {"x": 329, "y": 204, "width": 10, "height": 26}}, "237": {"xOffset": 0, "yOffset": 53, "xAdvance": 14, "rect": {"x": 329, "y": 152, "width": 10, "height": 27}}, "242": {"xOffset": 0, "yOffset": 54, "xAdvance": 23, "rect": {"x": 205, "y": 80, "width": 19, "height": 26}}, "243": {"xOffset": 0, "yOffset": 53, "xAdvance": 23, "rect": {"x": 145, "y": 64, "width": 19, "height": 27}}, "244": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 185, "y": 92, "width": 19, "height": 25}}, "245": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 205, "y": 0, "width": 19, "height": 25}}, "249": {"xOffset": 0, "yOffset": 53, "xAdvance": 21, "rect": {"x": 264, "y": 58, "width": 17, "height": 27}}, "250": {"xOffset": 0, "yOffset": 53, "xAdvance": 20, "rect": {"x": 282, "y": 168, "width": 16, "height": 27}}, "253": {"xOffset": 0, "yOffset": 54, "xAdvance": 22, "rect": {"x": 263, "y": 205, "width": 18, "height": 31}}, "259": {"xOffset": 0, "yOffset": 56, "xAdvance": 23, "rect": {"x": 184, "y": 175, "width": 19, "height": 24}}, "272": {"xOffset": 0, "yOffset": 57, "xAdvance": 27, "rect": {"x": 52, "y": 135, "width": 23, "height": 23}}, "273": {"xOffset": 0, "yOffset": 57, "xAdvance": 24, "rect": {"x": 122, "y": 270, "width": 20, "height": 23}}, "296": {"xOffset": 0, "yOffset": 50, "xAdvance": 17, "rect": {"x": 328, "y": 290, "width": 13, "height": 30}}, "297": {"xOffset": 0, "yOffset": 57, "xAdvance": 18, "rect": {"x": 299, "y": 285, "width": 14, "height": 23}}, "360": {"xOffset": 0, "yOffset": 50, "xAdvance": 22, "rect": {"x": 245, "y": 0, "width": 18, "height": 30}}, "361": {"xOffset": 0, "yOffset": 55, "xAdvance": 21, "rect": {"x": 264, "y": 160, "width": 17, "height": 25}}, "416": {"xOffset": 0, "yOffset": 53, "xAdvance": 28, "rect": {"x": 26, "y": 301, "width": 24, "height": 27}}, "417": {"xOffset": 0, "yOffset": 58, "xAdvance": 23, "rect": {"x": 145, "y": 92, "width": 19, "height": 22}}, "431": {"xOffset": 0, "yOffset": 53, "xAdvance": 25, "rect": {"x": 122, "y": 173, "width": 21, "height": 27}}, "432": {"xOffset": 0, "yOffset": 58, "xAdvance": 24, "rect": {"x": 123, "y": 150, "width": 20, "height": 22}}, "7840": {"xOffset": 0, "yOffset": 57, "xAdvance": 27, "rect": {"x": 100, "y": 55, "width": 23, "height": 30}}, "7841": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 165, "y": 122, "width": 19, "height": 26}}, "7842": {"xOffset": 0, "yOffset": 48, "xAdvance": 27, "rect": {"x": 100, "y": 86, "width": 23, "height": 32}}, "7843": {"xOffset": 0, "yOffset": 52, "xAdvance": 23, "rect": {"x": 165, "y": 67, "width": 19, "height": 28}}, "7844": {"xOffset": 0, "yOffset": 45, "xAdvance": 28, "rect": {"x": 26, "y": 233, "width": 24, "height": 35}}, "7845": {"xOffset": 0, "yOffset": 50, "xAdvance": 28, "rect": {"x": 51, "y": 257, "width": 24, "height": 30}}, "7846": {"xOffset": 0, "yOffset": 44, "xAdvance": 27, "rect": {"x": 76, "y": 149, "width": 23, "height": 36}}, "7847": {"xOffset": 0, "yOffset": 48, "xAdvance": 23, "rect": {"x": 184, "y": 200, "width": 19, "height": 32}}, "7848": {"xOffset": 0, "yOffset": 43, "xAdvance": 27, "rect": {"x": 76, "y": 111, "width": 23, "height": 37}}, "7849": {"xOffset": 0, "yOffset": 48, "xAdvance": 25, "rect": {"x": 100, "y": 205, "width": 21, "height": 32}}, "7850": {"xOffset": 0, "yOffset": 45, "xAdvance": 27, "rect": {"x": 76, "y": 75, "width": 23, "height": 35}}, "7851": {"xOffset": 0, "yOffset": 50, "xAdvance": 23, "rect": {"x": 204, "y": 152, "width": 19, "height": 30}}, "7852": {"xOffset": 0, "yOffset": 50, "xAdvance": 27, "rect": {"x": 76, "y": 37, "width": 23, "height": 37}}, "7853": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 164, "y": 276, "width": 19, "height": 32}}, "7854": {"xOffset": 0, "yOffset": 44, "xAdvance": 27, "rect": {"x": 52, "y": 38, "width": 23, "height": 36}}, "7855": {"xOffset": 0, "yOffset": 49, "xAdvance": 23, "rect": {"x": 164, "y": 178, "width": 19, "height": 31}}, "7856": {"xOffset": 0, "yOffset": 44, "xAdvance": 27, "rect": {"x": 76, "y": 0, "width": 23, "height": 36}}, "7857": {"xOffset": 0, "yOffset": 49, "xAdvance": 23, "rect": {"x": 144, "y": 211, "width": 19, "height": 31}}, "7858": {"xOffset": 0, "yOffset": 43, "xAdvance": 27, "rect": {"x": 52, "y": 0, "width": 23, "height": 37}}, "7859": {"xOffset": 0, "yOffset": 48, "xAdvance": 23, "rect": {"x": 145, "y": 0, "width": 19, "height": 32}}, "7860": {"xOffset": 0, "yOffset": 45, "xAdvance": 27, "rect": {"x": 52, "y": 75, "width": 23, "height": 35}}, "7861": {"xOffset": 0, "yOffset": 51, "xAdvance": 23, "rect": {"x": 185, "y": 30, "width": 19, "height": 29}}, "7862": {"xOffset": 0, "yOffset": 51, "xAdvance": 27, "rect": {"x": 76, "y": 186, "width": 23, "height": 36}}, "7863": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 204, "y": 241, "width": 19, "height": 32}}, "7864": {"xOffset": 0, "yOffset": 56, "xAdvance": 18, "rect": {"x": 314, "y": 0, "width": 14, "height": 30}}, "7865": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 244, "y": 130, "width": 19, "height": 25}}, "7866": {"xOffset": 0, "yOffset": 47, "xAdvance": 18, "rect": {"x": 314, "y": 257, "width": 14, "height": 32}}, "7867": {"xOffset": 0, "yOffset": 52, "xAdvance": 23, "rect": {"x": 244, "y": 176, "width": 19, "height": 28}}, "7868": {"xOffset": 0, "yOffset": 50, "xAdvance": 17, "rect": {"x": 314, "y": 290, "width": 13, "height": 29}}, "7869": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 225, "y": 80, "width": 19, "height": 25}}, "7870": {"xOffset": 0, "yOffset": 45, "xAdvance": 23, "rect": {"x": 224, "y": 132, "width": 19, "height": 34}}, "7871": {"xOffset": 0, "yOffset": 50, "xAdvance": 27, "rect": {"x": 76, "y": 318, "width": 23, "height": 30}}, "7872": {"xOffset": 0, "yOffset": 43, "xAdvance": 18, "rect": {"x": 300, "y": 31, "width": 14, "height": 36}}, "7873": {"xOffset": 0, "yOffset": 48, "xAdvance": 23, "rect": {"x": 224, "y": 265, "width": 19, "height": 32}}, "7874": {"xOffset": 0, "yOffset": 42, "xAdvance": 19, "rect": {"x": 299, "y": 95, "width": 15, "height": 37}}, "7875": {"xOffset": 0, "yOffset": 47, "xAdvance": 23, "rect": {"x": 224, "y": 231, "width": 19, "height": 33}}, "7876": {"xOffset": 0, "yOffset": 44, "xAdvance": 19, "rect": {"x": 299, "y": 133, "width": 15, "height": 35}}, "7877": {"xOffset": 0, "yOffset": 50, "xAdvance": 23, "rect": {"x": 224, "y": 200, "width": 19, "height": 30}}, "7878": {"xOffset": 0, "yOffset": 49, "xAdvance": 17, "rect": {"x": 315, "y": 119, "width": 13, "height": 37}}, "7879": {"xOffset": 0, "yOffset": 54, "xAdvance": 23, "rect": {"x": 224, "y": 167, "width": 19, "height": 32}}, "7880": {"xOffset": 0, "yOffset": 47, "xAdvance": 13, "rect": {"x": 340, "y": 91, "width": 9, "height": 33}}, "7881": {"xOffset": 0, "yOffset": 52, "xAdvance": 13, "rect": {"x": 340, "y": 32, "width": 9, "height": 28}}, "7882": {"xOffset": 0, "yOffset": 56, "xAdvance": 11, "rect": {"x": 340, "y": 240, "width": 7, "height": 31}}, "7883": {"xOffset": 0, "yOffset": 56, "xAdvance": 11, "rect": {"x": 340, "y": 208, "width": 7, "height": 31}}, "7884": {"xOffset": 0, "yOffset": 56, "xAdvance": 28, "rect": {"x": 26, "y": 269, "width": 24, "height": 31}}, "7885": {"xOffset": 0, "yOffset": 61, "xAdvance": 23, "rect": {"x": 204, "y": 301, "width": 19, "height": 26}}, "7886": {"xOffset": 0, "yOffset": 47, "xAdvance": 29, "rect": {"x": 0, "y": 208, "width": 25, "height": 33}}, "7887": {"xOffset": 0, "yOffset": 52, "xAdvance": 23, "rect": {"x": 205, "y": 51, "width": 19, "height": 28}}, "7888": {"xOffset": 0, "yOffset": 46, "xAdvance": 29, "rect": {"x": 26, "y": 61, "width": 25, "height": 34}}, "7889": {"xOffset": 0, "yOffset": 51, "xAdvance": 26, "rect": {"x": 100, "y": 119, "width": 22, "height": 29}}, "7890": {"xOffset": 0, "yOffset": 44, "xAdvance": 29, "rect": {"x": 0, "y": 280, "width": 25, "height": 36}}, "7891": {"xOffset": 0, "yOffset": 48, "xAdvance": 23, "rect": {"x": 164, "y": 210, "width": 19, "height": 32}}, "7892": {"xOffset": 0, "yOffset": 43, "xAdvance": 29, "rect": {"x": 0, "y": 242, "width": 25, "height": 37}}, "7893": {"xOffset": 0, "yOffset": 48, "xAdvance": 23, "rect": {"x": 164, "y": 243, "width": 19, "height": 32}}, "7894": {"xOffset": 0, "yOffset": 46, "xAdvance": 29, "rect": {"x": 0, "y": 317, "width": 25, "height": 34}}, "7895": {"xOffset": 0, "yOffset": 50, "xAdvance": 23, "rect": {"x": 145, "y": 33, "width": 19, "height": 30}}, "7896": {"xOffset": 0, "yOffset": 50, "xAdvance": 29, "rect": {"x": 0, "y": 145, "width": 25, "height": 37}}, "7897": {"xOffset": 0, "yOffset": 55, "xAdvance": 23, "rect": {"x": 144, "y": 178, "width": 19, "height": 32}}, "7898": {"xOffset": 0, "yOffset": 49, "xAdvance": 28, "rect": {"x": 27, "y": 24, "width": 24, "height": 31}}, "7899": {"xOffset": 0, "yOffset": 53, "xAdvance": 23, "rect": {"x": 184, "y": 233, "width": 19, "height": 27}}, "7900": {"xOffset": 0, "yOffset": 49, "xAdvance": 28, "rect": {"x": 51, "y": 318, "width": 24, "height": 31}}, "7901": {"xOffset": 0, "yOffset": 54, "xAdvance": 23, "rect": {"x": 184, "y": 261, "width": 19, "height": 26}}, "7902": {"xOffset": 0, "yOffset": 48, "xAdvance": 28, "rect": {"x": 51, "y": 224, "width": 24, "height": 32}}, "7903": {"xOffset": 0, "yOffset": 52, "xAdvance": 23, "rect": {"x": 184, "y": 288, "width": 19, "height": 28}}, "7904": {"xOffset": 0, "yOffset": 51, "xAdvance": 28, "rect": {"x": 51, "y": 288, "width": 24, "height": 29}}, "7905": {"xOffset": 0, "yOffset": 54, "xAdvance": 23, "rect": {"x": 184, "y": 317, "width": 19, "height": 26}}, "7906": {"xOffset": 0, "yOffset": 53, "xAdvance": 28, "rect": {"x": 51, "y": 189, "width": 24, "height": 34}}, "7907": {"xOffset": 0, "yOffset": 58, "xAdvance": 23, "rect": {"x": 185, "y": 0, "width": 19, "height": 29}}, "7908": {"xOffset": 0, "yOffset": 56, "xAdvance": 23, "rect": {"x": 185, "y": 60, "width": 19, "height": 31}}, "7909": {"xOffset": 0, "yOffset": 61, "xAdvance": 20, "rect": {"x": 282, "y": 225, "width": 16, "height": 25}}, "7910": {"xOffset": 0, "yOffset": 47, "xAdvance": 23, "rect": {"x": 185, "y": 118, "width": 19, "height": 33}}, "7911": {"xOffset": 0, "yOffset": 52, "xAdvance": 20, "rect": {"x": 282, "y": 196, "width": 16, "height": 28}}, "7912": {"xOffset": 0, "yOffset": 49, "xAdvance": 25, "rect": {"x": 100, "y": 173, "width": 21, "height": 31}}, "7913": {"xOffset": 0, "yOffset": 54, "xAdvance": 24, "rect": {"x": 124, "y": 64, "width": 20, "height": 26}}, "7914": {"xOffset": 0, "yOffset": 49, "xAdvance": 25, "rect": {"x": 100, "y": 269, "width": 21, "height": 31}}, "7915": {"xOffset": 0, "yOffset": 54, "xAdvance": 24, "rect": {"x": 124, "y": 91, "width": 20, "height": 26}}, "7916": {"xOffset": 0, "yOffset": 47, "xAdvance": 25, "rect": {"x": 122, "y": 236, "width": 21, "height": 33}}, "7917": {"xOffset": 0, "yOffset": 52, "xAdvance": 24, "rect": {"x": 143, "y": 270, "width": 20, "height": 28}}, "7918": {"xOffset": 0, "yOffset": 50, "xAdvance": 25, "rect": {"x": 100, "y": 238, "width": 21, "height": 30}}, "7919": {"xOffset": 0, "yOffset": 55, "xAdvance": 24, "rect": {"x": 122, "y": 326, "width": 20, "height": 25}}, "7920": {"xOffset": 0, "yOffset": 53, "xAdvance": 25, "rect": {"x": 122, "y": 201, "width": 21, "height": 34}}, "7921": {"xOffset": 0, "yOffset": 58, "xAdvance": 24, "rect": {"x": 144, "y": 148, "width": 20, "height": 29}}, "7922": {"xOffset": 0, "yOffset": 50, "xAdvance": 24, "rect": {"x": 124, "y": 0, "width": 20, "height": 30}}, "7923": {"xOffset": 0, "yOffset": 54, "xAdvance": 22, "rect": {"x": 264, "y": 0, "width": 18, "height": 32}}, "7924": {"xOffset": 0, "yOffset": 57, "xAdvance": 24, "rect": {"x": 122, "y": 294, "width": 20, "height": 31}}, "7925": {"xOffset": 0, "yOffset": 63, "xAdvance": 22, "rect": {"x": 244, "y": 230, "width": 18, "height": 25}}, "7926": {"xOffset": 0, "yOffset": 48, "xAdvance": 24, "rect": {"x": 124, "y": 31, "width": 20, "height": 32}}, "7927": {"xOffset": 0, "yOffset": 53, "xAdvance": 22, "rect": {"x": 263, "y": 316, "width": 18, "height": 33}}, "7928": {"xOffset": 0, "yOffset": 51, "xAdvance": 24, "rect": {"x": 144, "y": 118, "width": 20, "height": 29}}, "7929": {"xOffset": 0, "yOffset": 56, "xAdvance": 22, "rect": {"x": 263, "y": 261, "width": 18, "height": 30}}}, "kerningDict": {}}]], 0, 0, [0], [0], [0]], [[{"name": "font_title_popup", "rect": [0, 0, 349, 351], "offset": [-0.5, 0.5], "originalSize": [350, 352], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]]]]