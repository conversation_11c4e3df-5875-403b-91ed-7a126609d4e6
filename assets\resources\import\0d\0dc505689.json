[1, ["adw94Z+hpN57wutNivq8Q5", "017Jn3Zv1Ft7hygdjpaSoK", "4c8fiezORAEI8BkChEFsdB", "69AZWE8jdPuKvcZ7bYak9a", "fdNoodJKVLj4dF1TLppv2g", "edmIjE8E1DiqFJrJ0cKobw", "e29lCfmCdBlah37brFmpuP", "405TN4bh5FLprZsmk8hB/6", "a9VpD0DP5LJYQPXITZq+uj", "ffw9P0ZiFLRb96S+X/Fgwm", "1718UlgrFOyq8Mn4wIYH7p", "1cIgMKIypJ7Jy+JHaSTGQS", "beL2Don21Kyq3/VR7C09IC", "74qCZlsOtBcqB3v53xd5qm", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_parent", "_textureSetter", "lbDesc", "lbWin", "lbRoom", "lbNickName", "lbSID", "lbTime", "lbSessionID", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "tqJackpotView", "tqBigWinView", "nodeBigWin", "nodeJackpot", "btnBigWin", "btnJackpot", "_N$target", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_children"], 1, 1, 2, 4, 5, 7, 5, 2], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_spriteFrame"], 0, 1, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_N$overflow", "node", "_N$file"], -5, 1, 6], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "_N$interactable", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_children", "_parent"], 2, 12, 4, 5, 7, 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["9fd49dp2txHZreHKIS/viSD", ["node", "btnJackpot", "btnBigWin", "nodeJackpot", "nodeBigWin", "tqBigWinView", "tqJackpotView"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["84059QTVoRBqaXAI7L430Pw", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["eaeaccFFZBJmIir1ejKu6tF", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["dc587IfjtFNWp44MIbM/17K", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["54b61U3BbtM3oWfMzCCVwGr", ["node", "tqTopJackpotListView"], 3, 1, 1], ["30f11N4VrZGZYrAQOBK1ywd", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["06b7cDS/dpE4oCEQcJLoydM", ["node", "tqTopBigWinListView"], 3, 1, 1]], [[11, 0, 1, 2], [0, 0, 5, 3, 2, 4, 7, 2], [3, 0, 1, 2, 6, 3, 4, 5, 7, 8, 9, 9], [3, 0, 1, 2, 6, 3, 4, 5, 7, 8, 9], [1, 0, 2, 3, 4, 5, 6, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [1, 0, 2, 3, 4, 7, 5, 6, 2], [3, 0, 1, 2, 6, 3, 4, 5, 8, 8], [15, 0, 1, 2, 3], [2, 0, 1, 3, 4, 3], [0, 0, 6, 3, 2, 4, 2], [0, 0, 6, 2, 7, 2], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 8, 7, 2], [0, 0, 5, 6, 2, 7, 2], [6, 0, 5, 1, 2, 3, 4, 2], [6, 0, 6, 1, 2, 3, 4, 2], [1, 0, 1, 2, 8, 3, 4, 3], [1, 0, 1, 2, 3, 4, 7, 5, 6, 3], [14, 0, 1], [5, 0, 2, 3, 4, 5, 6, 2], [2, 3, 4, 1], [2, 2, 0, 1, 3, 4, 4], [7, 0, 1, 2, 5, 6, 4], [7, 3, 0, 1, 4, 5, 6, 5], [16, 0, 1, 2], [3, 0, 1, 2, 3, 4, 5, 8, 9, 7], [17, 0, 1, 2, 3, 4, 5, 6, 6], [8, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [0, 0, 5, 3, 2, 4, 2], [9, 0, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 6, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 1], [5, 2, 7, 1], [5, 1, 0, 2, 3, 4, 5, 6, 3], [2, 0, 3, 4, 2], [18, 0, 1, 2, 3, 4, 5, 4], [19, 0, 1, 1], [20, 0, 1, 2, 3, 4, 5, 4], [21, 0, 1, 1]], [[[{"name": "bxh", "rect": [0, 0, 290, 45], "offset": [0, 0], "originalSize": [290, 45], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [5]], [[{"name": "tab_off", "rect": [0, 0, 135, 50], "offset": [0, 0], "originalSize": [135, 50], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [6]], [[{"name": "tab_on", "rect": [0, 0, 135, 50], "offset": [0, 0], "originalSize": [135, 50], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [4], [7]], [[[28, "tqTopView"], [29, "tqTopView", [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20], [[32, -2, [23, 24], 22], [33, -9, -8, -7, -6, -5, -4, -3]], [0, "59gqDY1VFPTrAy0p7wUC48", -1]], [10, "item", [-29, -30, -31, -32, -33, -34], [[34, -28, -27, -26, -25, -24, -23, -22, -21]], [0, "2bMad3GcdERYwx61vBmWF7", 1], [5, 994, 50]], [10, "item", [-43, -44, -45, -46, -47, -48], [[35, -42, -41, -40, -39, -38, -37, -36, -35]], [0, "47r/J5FlZI/YOOoqacH03D", 1], [5, 994, 50]], [11, "title", [-49, -50, -51, -52, -53, -54], [0, "9bsMzl44NOMJJYw0cyPj2I", 1], [0, 142, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "title", [-55, -56, -57, -58, -59, -60], [0, "87EBNcvB1Gibk5uPsRIHFU", 1], [0, 142, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", [-64, -65], [[-61, -62, [19, -63]], 1, 1, 4], [0, "6aV08kzntMD5Y9kalVwlrO", 1], [5, 1050, 400], [0, -93, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", [-69, -70], [[-66, -67, [19, -68]], 1, 1, 4], [0, "23Ly3hi61Ko5tC37dyE1mL", 1], [5, 1050, 400], [0, -93, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "jackpotView", false, 1, [4, 6], [-71], [0, "cewX+V9qRA879Q6zaxYK7D", 1]], [17, "<PERSON><PERSON><PERSON><PERSON><PERSON>", false, 1, [5, 7], [-72], [0, "0fA2sxet5J97MIx8OnKv+O", 1]], [5, "btnClose", 1, [-75], [[20, 3, -74, [[8, "9fd49dp2txHZreHKIS/viSD", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -73]], [0, "92KKAapbtMGYURsbZktsrQ", 1], [5, 80, 80], [546, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnJackpot", 1, [[[9, 2, false, -76, 6], -77], 4, 1], [0, "1ebhoZxDZJxpBi1P0oHwEl", 1], [5, 135, 50], [-75, 202, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnBigWin", 1, [[[9, 2, false, -78, 7], -79], 4, 1], [0, "21MMbZJ1dHkZ7RN4SCv2uU", 1], [5, 135, 50], [75, 202, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 2, [-81, -82], [[23, 1, 1, 5, -80, [5, 137, 50]]], [0, "fcf37cKWxLKJQS/eq/0Xdu", 1], [5, 137, 50], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 3, [-84, -85], [[23, 1, 1, 5, -83, [5, 137, 50]]], [0, "4eT6inu3RGdLh7B1d7XG53", 1], [5, 137, 50], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "black", 100, 1, [[38, 0, -86, 0], [36, -87, [4, 4292269782]]], [0, "798uBkQlJMAr72o4V/vflR", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "colum", 1, [-89], [[21, -88, 3]], [0, "72/ngILUtPkaKRFdzeo57a", 1], [5, 1137, 75], [0, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "view", 6, [-91], [[25, 0, -90]], [0, "c9b/BxzSpJkIFqGs6ErE+a", 1], [5, 1050, 400]], [13, "content", 17, [[24, false, 1, 2, 10, -92, [5, 1000, -10]]], [0, "c3gCGZ451H5YV8MXUxCs3Q", 1], [5, 1000, -10], [0, 0.5, 1], [0, 188, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "view", 7, [-94], [[25, 0, -93]], [0, "ffhF7E0q1Bv5CNLzcE7LYq", 1], [5, 1050, 400]], [13, "content", 19, [[24, false, 1, 2, 10, -95, [5, 1000, -10]]], [0, "0cwJLXOzZMpLuscsgMmN6g", 1], [5, 1000, -10], [0, 0.5, 1], [0, 188, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[22, 1, 0, false, -96, 1]], [0, "92oZJPHVZOYpump75TAHtr", 1], [5, 1084, 580], [0, -18, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 16, [[21, -97, 2]], [0, "b7Oph+31pNjKyIQ+W5iKDD", 1], [5, 290, 45], [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 1, [[22, 1, 0, false, -98, 4]], [0, "89TiV+GmhOhoWBV868kDGy", 1], [5, 994, 50], [0, 142, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "sprite", 10, [[9, 2, false, -99, 5]], [0, "530PbxKytOgq75Dq+E1atC", 1], [5, 66, 67]], [37, false, 2, 11, [[8, "9fd49dp2txHZreHKIS/viSD", "jackpotTabClicked", 1]], [4, 4294967295], [4, 4294967295], 11], [20, 2, 12, [[8, "9fd49dp2txHZreHKIS/viSD", "bigWinTabClicked", 1]], [4, 4294967295], [4, 4294967295], 12], [1, "lbJackpot", 1, [[26, "NỔ HŨ", 21, 52, false, 1, 1, -100, 8]], [0, "d4EYBEqpFDgrVHvEntGjjH", 1], [5, 65.63, 27.3], [-75, 202, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBigWin", 1, [[26, "THẮNG LỚN", 21, 52, false, 1, 1, -101, 9]], [0, "d5M8XcyxhFtK84lProDg4N", 1], [5, 118.13, 27.3], [75, 202, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNo", 4, [[2, "STT", 22, 50, false, false, 1, 1, 1, -102, 10]], [0, "a0pG0+41NHmpIRIvwhUjhq", 1], [5, 150, 30], [-440, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 4, [[2, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, -103, 11]], [0, "f3cUrM3PhGwo8gIrN6AWhz", 1], [5, 200, 38], [-308, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 4, [[2, "TÀI KHOẢN", 22, 50, false, false, 1, 1, 1, -104, 12]], [0, "daYW1Fr6xHQLmDxBi7VaOf", 1], [5, 200, 38], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRoom", 4, [[2, "PHÒNG", 22, 50, false, false, 1, 1, 1, -105, 13]], [0, "32mvJqxrNLX7rL5UAlt8OM", 1], [5, 200, 38], [104, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 4, [[2, "THẮNG", 22, 50, false, false, 1, 1, 1, -106, 14]], [0, "6bVQLb9zpJEa/ATbHbS9qF", 1], [5, 200, 38], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 4, [[2, "LOẠI", 22, 50, false, false, 1, 1, 1, -107, 15]], [0, "fex/uX9gFNgprVlCk80v2h", 1], [5, 200, 38], [393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "temp", 6, [2], [0, "4aT/cDCUJNPamOjGJw4+d1", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbNo", 2, [-108], [0, "4bWSEbv1ZNcJHTjgAbt40L", 1], [5, 150, 30], [-440, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "STT", 22, 50, false, false, 1, 1, 1, 36], [4, "lbTime", 2, [-109], [0, "b6fwE4E7ZHRr1xGU7a10v5", 1], [5, 200, 38], [-308, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, 38], [18, "lbSID", false, 13, [-110], [0, "328ExtlppEf4sQCUsmQdGH", 1], [4, 4279026733], [5, 37, 24], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "[TQ]", 20, 48, false, false, 1, 1, 40], [4, "lbNickName", 13, [-111], [0, "6dyEbWTM9G/atQwr+G2m1h", 1], [5, 95, 24], [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "<PERSON><PERSON><PERSON>", 20, 48, false, false, 1, 1, 42], [6, "lbRoom", 2, [-112], [0, "6a08/iQKBKLZLpoHmwhN5q", 1], [4, 4278315513], [5, 200, 38], [104, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "PHÒNG", 22, 50, false, false, 1, 1, 1, 44], [6, "lbWin", 2, [-113], [0, "7f2RyAwWlL34qm8/L1EwBc", 1], [4, 4278315513], [5, 200, 38], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "THẮNG", 22, 50, false, false, 1, 1, 1, 46], [4, "lbDesc", 2, [-114], [0, "fc2iF2OhNNIZXWrlS3cLxD", 1], [5, 200, 38], [393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "LOẠI", 22, 50, false, false, 1, 1, 1, 48], [27, false, 0.75, 0.23, null, null, 6, 18], [39, 20, 10, 400, 6, 2, 50], [40, 8, 51], [1, "lbNo", 5, [[2, "STT", 22, 50, false, false, 1, 1, 1, -115, 16]], [0, "3aAwvyASNITqRWTCbMDkBV", 1], [5, 150, 30], [-440, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 5, [[2, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, -116, 17]], [0, "8f8O15GOlJOYlPS/lBFc/V", 1], [5, 200, 38], [-308, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNickName", 5, [[2, "TÀI KHOẢN", 22, 50, false, false, 1, 1, 1, -117, 18]], [0, "eec3vXczxI6qiVbtKNOENY", 1], [5, 200, 38], [-72, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRoom", 5, [[2, "PHÒNG", 22, 50, false, false, 1, 1, 1, -118, 19]], [0, "04JGB6/gpGeLAGKmvtnoSc", 1], [5, 200, 38], [104, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 5, [[2, "THẮNG", 22, 50, false, false, 1, 1, 1, -119, 20]], [0, "60T0vwS3RIqYBqEhktt+lI", 1], [5, 200, 38], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 5, [[2, "LOẠI", 22, 50, false, false, 1, 1, 1, -120, 21]], [0, "6cxz2wUt5KMKbMSzN30zsH", 1], [5, 200, 38], [393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "temp", 7, [3], [0, "b50V9/D/ZIF6Qu/aJ9ytjt", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbNo", 3, [-121], [0, "40HIhstY5JQbMUG+qZCyN7", 1], [5, 150, 30], [-440, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "STT", 22, 50, false, false, 1, 1, 1, 60], [4, "lbTime", 3, [-122], [0, "67pe4ppXdCEYajXvNuv9Vq", 1], [5, 200, 38], [-308, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "THỜI GIAN", 22, 50, false, false, 1, 1, 1, 62], [18, "lbSID", false, 14, [-123], [0, "50lKQgwQpOcpZHUID2rDXh", 1], [4, 4279026733], [5, 37, 24], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "[TQ]", 20, 48, false, false, 1, 1, 64], [4, "lbNickName", 14, [-124], [0, "eaaCLQJGFMfKZ6ABo3lBsV", 1], [5, 95, 24], [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "<PERSON><PERSON><PERSON>", 20, 48, false, false, 1, 1, 66], [6, "lbRoom", 3, [-125], [0, "09tE1W5tlFCbWZO532RxGc", 1], [4, 4278315513], [5, 200, 38], [104, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "PHÒNG", 22, 50, false, false, 1, 1, 1, 68], [6, "lbWin", 3, [-126], [0, "47943su6JC/LtMpu3hTQ5W", 1], [4, 4278315513], [5, 200, 38], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "THẮNG", 22, 50, false, false, 1, 1, 1, 70], [4, "lbDesc", 3, [-127], [0, "7fyengyYRHZ7kOwIQ+iH0V", 1], [5, 200, 38], [393, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "LOẠI", 22, 50, false, false, 1, 1, 1, 72], [27, false, 0.75, 0.23, null, null, 7, 20], [41, 20, 10, 400, 7, 3, 74], [42, 9, 75]], 0, [0, 16, 1, 0, 0, 1, 0, 17, 52, 0, 18, 76, 0, 19, 9, 0, 20, 8, 0, 21, 26, 0, 22, 25, 0, 0, 1, 0, -1, 15, 0, -2, 21, 0, -3, 16, 0, -4, 23, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, -8, 27, 0, -9, 28, 0, -10, 8, 0, -11, 9, 0, 5, 49, 0, 6, 47, 0, 7, 45, 0, 8, 43, 0, 9, 41, 0, 10, 39, 0, 11, 37, 0, 0, 2, 0, -1, 36, 0, -2, 38, 0, -3, 13, 0, -4, 44, 0, -5, 46, 0, -6, 48, 0, 5, 73, 0, 6, 71, 0, 7, 69, 0, 8, 67, 0, 9, 65, 0, 10, 63, 0, 11, 61, 0, 0, 3, 0, -1, 60, 0, -2, 62, 0, -3, 14, 0, -4, 68, 0, -5, 70, 0, -6, 72, 0, -1, 29, 0, -2, 30, 0, -3, 31, 0, -4, 32, 0, -5, 33, 0, -6, 34, 0, -1, 53, 0, -2, 54, 0, -3, 55, 0, -4, 56, 0, -5, 57, 0, -6, 58, 0, -1, 50, 0, -2, 51, 0, 0, 6, 0, -1, 35, 0, -2, 17, 0, -1, 74, 0, -2, 75, 0, 0, 7, 0, -1, 59, 0, -2, 19, 0, -1, 52, 0, -1, 76, 0, 23, 10, 0, 0, 10, 0, -1, 24, 0, 0, 11, 0, -2, 25, 0, 0, 12, 0, -2, 26, 0, 0, 13, 0, -1, 40, 0, -2, 42, 0, 0, 14, 0, -1, 64, 0, -2, 66, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 22, 0, 0, 17, 0, -1, 18, 0, 0, 18, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, -1, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, -1, 45, 0, -1, 47, 0, -1, 49, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, -1, 61, 0, -1, 63, 0, -1, 65, 0, -1, 67, 0, -1, 69, 0, -1, 71, 0, -1, 73, 0, 24, 1, 2, 3, 35, 3, 3, 59, 4, 3, 8, 5, 3, 9, 6, 3, 8, 7, 3, 9, 127], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 25, 25, 25, 26, 26, 26, 26, 37, 39, 41, 43, 45, 47, 49, 61, 63, 65, 67, 69, 71, 73], [2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25, -1, -2, 12, 13, 14, 15, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [8, 9, 10, 11, 12, 13, 3, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 4, 14, 2, 2, 2, 3, 2, 2, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]]]]