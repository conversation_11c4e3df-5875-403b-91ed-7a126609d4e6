[1, ["36MXrND21PFq2egPmFLFxn"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "phong3", "\nphong3.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nGroup 11\n  rotate: false\n  xy: 368, 741\n  size: 268, 67\n  orig: 268, 67\n  offset: 0, 0\n  index: -1\nLayer 33\n  rotate: false\n  xy: 2, 512\n  size: 330, 184\n  orig: 330, 184\n  offset: 0, 0\n  index: -1\nLayer 596\n  rotate: false\n  xy: 2, 26\n  size: 144, 80\n  orig: 144, 80\n  offset: 0, 0\n  index: -1\nLayer 597\n  rotate: false\n  xy: 2, 698\n  size: 364, 312\n  orig: 364, 312\n  offset: 0, 0\n  index: -1\nLayer 598\n  rotate: false\n  xy: 810, 313\n  size: 92, 91\n  orig: 92, 91\n  offset: 0, 0\n  index: -1\nbonus_flare\n  rotate: false\n  xy: 840, 712\n  size: 99, 96\n  orig: 99, 96\n  offset: 0, 0\n  index: -1\ncammap_00000\n  rotate: false\n  xy: 368, 810\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00001\n  rotate: false\n  xy: 2, 310\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00002\n  rotate: false\n  xy: 570, 810\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00003\n  rotate: false\n  xy: 2, 108\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00004\n  rotate: false\n  xy: 772, 810\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00005\n  rotate: false\n  xy: 204, 310\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00006\n  rotate: false\n  xy: 204, 108\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00007\n  rotate: false\n  xy: 638, 608\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00008\n  rotate: false\n  xy: 368, 539\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00009\n  rotate: false\n  xy: 406, 337\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00010\n  rotate: false\n  xy: 406, 135\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00011\n  rotate: false\n  xy: 608, 406\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00012\n  rotate: false\n  xy: 608, 204\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00013\n  rotate: false\n  xy: 608, 2\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\ncammap_00014\n  rotate: false\n  xy: 810, 406\n  size: 200, 200\n  orig: 200, 200\n  offset: 0, 0\n  index: -1\n", ["phong3.png"], {"skeleton": {"hash": "tR68AOrMgLnweY2b91glilQUooY", "spine": "3.7.94", "width": 414.57, "height": 396, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/ca 2m/chon phong/phong3"}, "bones": [{"name": "root", "x": 14.1, "y": 164.14}, {"name": "Layer 597", "parent": "root", "x": 1.73, "y": -65.82}, {"name": "Layer 599", "parent": "Layer 597", "length": 65.01, "rotation": 93.18, "x": 1.2, "y": 25.24}, {"name": "Layer 598", "parent": "Layer 599", "length": 105.46, "rotation": 1.07, "x": 77.11, "y": 1.13}, {"name": "cammap_00000", "parent": "Layer 598", "rotation": -120.7, "x": -12.65, "y": -41.73}, {"name": "Layer 601", "parent": "Layer 598", "length": 43.44, "rotation": 100.17, "x": -26.4, "y": -144.48}, {"name": "Layer 596", "parent": "root", "length": 39.6, "rotation": -135.94, "x": 68.67, "y": 4.63}, {"name": "Layer 602", "parent": "Layer 596", "length": 42.79, "rotation": -21.35, "x": 47.39, "y": 0.13}, {"name": "Layer 600", "parent": "Layer 602", "length": 38.95, "rotation": -30.84, "x": 50.77, "y": -2.34}, {"name": "bonus_flare", "parent": "root", "x": -106.33, "y": -36.59}], "slots": [{"name": "Layer 597", "bone": "Layer 598", "attachment": "Layer 597"}, {"name": "Layer 596", "bone": "Layer 600", "attachment": "Layer 596"}, {"name": "Layer 33", "bone": "root", "attachment": "Layer 33"}, {"name": "Layer 598", "bone": "Layer 601", "attachment": "Layer 598"}, {"name": "Group 11", "bone": "root", "attachment": "Group 11"}, {"name": "bonus_flare", "bone": "bonus_flare", "attachment": "bonus_flare", "blend": "additive"}, {"name": "cammap_00000", "bone": "cammap_00000", "attachment": "cammap_00014"}], "skins": {"default": {"Group 11": {"Group 11": {"x": -4.76, "y": -108.62, "width": 268, "height": 67}}, "Layer 33": {"Layer 33": {"x": -13.76, "y": -75.12, "width": 330, "height": 184}}, "Layer 596": {"Layer 596": {"type": "mesh", "hull": 39, "width": 144, "height": 80, "uvs": [1, 0.27187, 0.95384, 0.38322, 0.89926, 0.47164, 0.83012, 0.46509, 0.79555, 0.51749, 0.80828, 0.58627, 0.74824, 0.71072, 0.67547, 0.80569, 0.62634, 0.84827, 0.57722, 0.80242, 0.51717, 0.85154, 0.49898, 0.94652, 0.39527, 0.98582, 0.33159, 1, 0.26973, 1, 0.25336, 0.95634, 0.18968, 0.93997, 0.08961, 1, 0.06413, 0.88102, 0.04412, 0.77949, 0, 0.66487, 0, 0.57317, 0.02956, 0.56007, 0.08597, 0.56007, 0.18422, 0.66159, 0.2388, 0.71399, 0.2952, 0.71399, 0.36616, 0.70089, 0.43712, 0.66814, 0.48624, 0.65832, 0.53537, 0.60919, 0.56994, 0.51422, 0.62816, 0.42579, 0.72277, 0.38977, 0.77371, 0.30789, 0.76462, 0.22929, 0.79919, 0.12777, 0.87924, 0.02624, 1, 0], "triangles": [37, 38, 0, 36, 37, 0, 35, 36, 0, 34, 35, 0, 1, 34, 0, 3, 34, 1, 33, 34, 3, 2, 3, 1, 4, 33, 3, 20, 21, 22, 4, 32, 33, 4, 31, 32, 31, 4, 5, 6, 31, 5, 30, 31, 6, 23, 20, 22, 24, 20, 23, 24, 19, 20, 9, 30, 6, 29, 30, 9, 7, 9, 6, 8, 9, 7, 10, 29, 9, 28, 29, 10, 27, 28, 10, 25, 18, 19, 25, 19, 24, 16, 18, 25, 27, 12, 26, 15, 25, 26, 16, 25, 15, 27, 10, 12, 11, 12, 10, 15, 26, 12, 17, 18, 16, 13, 14, 15, 12, 13, 15], "vertices": [1, 6, -4.52, 13.41, 1, 1, 6, 6.45, 15.19, 1, 1, 6, 17.02, 14.81, 1, 1, 6, 23.81, 7.51, 1, 2, 6, 30.3, 7.06, 0.98929, 7, -18.44, 0.23, 0.01071, 2, 6, 32.81, 12.28, 0.92338, 7, -18, 6.02, 0.07662, 2, 6, 45.95, 13.43, 0.32769, 7, -6.18, 11.86, 0.67231, 2, 6, 58.76, 11.6, 0.00217, 7, 6.42, 14.82, 0.99783, 1, 7, 14.26, 15.23, 1, 1, 7, 19.37, 9.12, 1, 1, 7, 28.86, 9.41, 1, 2, 7, 34.21, 15.4, 0.99739, 8, -23.31, 6.75, 0.00261, 2, 7, 49.2, 12.54, 0.56347, 8, -8.97, 11.97, 0.43653, 2, 7, 58.1, 10.04, 0.11369, 8, -0.06, 14.39, 0.88631, 2, 7, 66.32, 6.6, 0.00161, 8, 8.76, 15.65, 0.99839, 1, 8, 11.59, 12.53, 1, 1, 8, 20.85, 12.53, 1, 1, 8, 34.44, 19.32, 1, 1, 8, 39.42, 10.42, 1, 1, 8, 43.42, 2.78, 1, 1, 8, 51, -5.4, 1, 1, 8, 52.04, -12.66, 1, 1, 8, 47.97, -14.3, 1, 1, 8, 39.93, -15.45, 1, 1, 8, 24.78, -9.41, 1, 1, 8, 16.41, -6.37, 1, 2, 7, 54.1, -13.09, 0.01046, 8, 8.37, -7.52, 0.98954, 2, 7, 44.27, -10.11, 0.51464, 8, -1.6, -10, 0.48536, 2, 7, 33.83, -8.58, 0.9888, 8, -11.35, -14.04, 0.0112, 1, 7, 27, -6.57, 1, 2, 6, 62.33, -13.73, 0.00278, 7, 18.96, -7.47, 0.99722, 2, 6, 53.47, -15.73, 0.08876, 7, 11.43, -12.55, 0.91124, 2, 6, 42.52, -14.98, 0.47263, 7, 0.97, -15.84, 0.52737, 2, 6, 30.73, -7.57, 0.9902, 7, -12.71, -13.24, 0.0098, 1, 6, 20.9, -7.18, 1, 1, 6, 17.47, -12.61, 1, 1, 6, 8.25, -14.98, 1, 1, 6, -5.69, -12.8, 1, 1, 6, -19.64, -2.22, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 0, 76]}}, "Layer 597": {"Layer 597": {"type": "mesh", "hull": 70, "width": 364, "height": 312, "uvs": [0.38267, 0.71549, 0.3584, 0.73572, 0.3116, 0.72965, 0.25266, 0.73976, 0.24919, 0.8065, 0.20065, 0.80448, 0.15038, 0.83482, 0.09144, 0.87122, 0.03597, 0.8328, 0.00476, 0.75999, 0, 0.671, 0, 0.62651, 0.00996, 0.58201, 0.07064, 0.54966, 0.11571, 0.54561, 0.18852, 0.55774, 0.23185, 0.58606, 0.20412, 0.64066, 0.21799, 0.69729, 0.26132, 0.63662, 0.29946, 0.60224, 0.25959, 0.54966, 0.19372, 0.51123, 0.11398, 0.49707, 0.07931, 0.48494, 0.03943, 0.43438, 0.0585, 0.3191, 0.15038, 0.20584, 0.23706, 0.19168, 0.29253, 0.25438, 0.36187, 0.19573, 0.40867, 0.17348, 0.48148, 0.15124, 0.51442, 0.10472, 0.54215, 0.06023, 0.60456, 0.00562, 0.69817, 0, 0.78485, 0, 0.81085, 0.04405, 0.76578, 0.12292, 0.72244, 0.24022, 0.79871, 0.17955, 0.84552, 0.2018, 0.83165, 0.3191, 0.78831, 0.37977, 0.79698, 0.43842, 0.80045, 0.48696, 0.88539, 0.49303, 0.97207, 0.54561, 0.9946, 0.60831, 1, 0.68516, 0.979, 0.72561, 0.92526, 0.66696, 0.84899, 0.63662, 0.80045, 0.63662, 0.80565, 0.76201, 0.78138, 0.81662, 0.76058, 0.88538, 0.75018, 0.92785, 0.73631, 0.99864, 0.72244, 1, 0.62883, 1, 0.50748, 1, 0.42427, 1, 0.40694, 0.94807, 0.40001, 0.91572, 0.45548, 0.89347, 0.3844, 0.85302, 0.41387, 0.81864, 0.39827, 0.75594, 0.51056, 0.88736, 0.60094, 0.87949, 0.7075, 0.87634, 0.48628, 0.78664, 0.61173, 0.7709, 0.72369, 0.75831, 0.42154, 0.70009, 0.5254, 0.67805, 0.6468, 0.65917, 0.73448, 0.64028, 0.37163, 0.60252, 0.49573, 0.59307, 0.62792, 0.5616, 0.74392, 0.5553, 0.27855, 0.51439, 0.44717, 0.50809, 0.53215, 0.47504, 0.61983, 0.46088, 0.68457, 0.39793, 0.14231, 0.39793, 0.25427, 0.4121, 0.34465, 0.40737, 0.4647, 0.40737, 0.59285, 0.346, 0.64006, 0.26417, 0.25562, 0.30194, 0.45931, 0.25472, 0.58745, 0.20122, 0.3487, 0.30508, 0.62657, 0.12411, 0.69401, 0.05329], "triangles": [100, 35, 36, 100, 37, 38, 37, 100, 36, 39, 100, 38, 99, 35, 100, 99, 100, 39, 34, 35, 99, 33, 34, 99, 97, 33, 99, 32, 33, 97, 40, 99, 39, 94, 97, 99, 96, 31, 32, 96, 32, 97, 40, 94, 99, 95, 28, 29, 98, 29, 30, 95, 29, 98, 30, 31, 96, 42, 40, 41, 43, 40, 42, 93, 97, 94, 96, 97, 93, 44, 40, 43, 88, 94, 40, 88, 40, 44, 93, 94, 88, 89, 26, 27, 92, 96, 93, 91, 95, 98, 98, 30, 96, 98, 96, 92, 95, 27, 28, 90, 95, 91, 95, 89, 27, 90, 89, 95, 25, 26, 89, 88, 44, 45, 87, 93, 88, 86, 92, 93, 87, 86, 93, 24, 25, 89, 23, 24, 89, 92, 91, 98, 85, 92, 86, 85, 91, 92, 22, 89, 90, 23, 89, 22, 84, 90, 91, 84, 91, 85, 22, 90, 84, 21, 22, 84, 46, 83, 88, 46, 88, 45, 87, 88, 83, 82, 87, 83, 86, 87, 82, 81, 85, 86, 81, 86, 82, 80, 20, 84, 85, 80, 84, 21, 84, 20, 81, 80, 85, 53, 54, 46, 83, 46, 54, 47, 53, 46, 48, 53, 47, 48, 52, 53, 79, 82, 83, 79, 83, 54, 17, 15, 16, 78, 82, 79, 77, 81, 82, 49, 52, 48, 13, 10, 11, 78, 77, 82, 52, 49, 50, 76, 80, 81, 76, 81, 77, 0, 80, 76, 51, 52, 50, 0, 2, 20, 0, 20, 80, 19, 20, 2, 3, 18, 19, 1, 2, 0, 2, 3, 19, 69, 0, 76, 75, 78, 79, 13, 9, 10, 79, 54, 55, 75, 79, 55, 74, 77, 78, 74, 78, 75, 73, 76, 77, 73, 77, 74, 69, 76, 73, 15, 17, 14, 18, 8, 17, 5, 18, 3, 4, 5, 3, 56, 75, 55, 68, 69, 73, 11, 12, 13, 14, 8, 9, 14, 9, 13, 14, 17, 8, 8, 18, 6, 5, 6, 18, 7, 8, 6, 72, 74, 75, 72, 75, 56, 71, 73, 74, 71, 74, 72, 57, 72, 56, 70, 73, 71, 66, 68, 73, 70, 66, 73, 67, 68, 66, 58, 72, 57, 64, 65, 66, 72, 60, 61, 62, 63, 64, 62, 66, 70, 62, 64, 66, 61, 71, 72, 72, 58, 60, 62, 70, 71, 61, 62, 71, 59, 60, 58], "vertices": [3, 1, -61.2, 71.47, 0.00036, 2, 49.62, 59.74, 0.4517, 3, -26.4, 59.11, 0.54794, 2, 2, 43.81, 68.91, 0.34897, 3, -32.03, 68.38, 0.65103, 2, 2, 46.64, 85.81, 0.26435, 3, -28.88, 85.23, 0.73565, 2, 2, 44.68, 107.41, 0.19984, 3, -30.44, 106.86, 0.80016, 2, 2, 23.96, 109.83, 0.1851, 3, -51.11, 109.66, 0.8149, 2, 2, 25.57, 127.43, 0.17951, 3, -49.18, 127.24, 0.82049, 2, 2, 17.13, 146.23, 0.17124, 3, -57.26, 146.19, 0.82876, 2, 2, 6.98, 168.28, 0.16766, 3, -67, 168.42, 0.83234, 2, 2, 20.07, 187.77, 0.16637, 3, -53.54, 187.67, 0.83363, 2, 2, 43.39, 197.85, 0.16535, 3, -30.05, 197.32, 0.83465, 2, 2, 71.2, 198.05, 0.16462, 3, -2.23, 196.99, 0.83538, 2, 2, 85.06, 197.28, 0.16441, 3, 11.61, 195.96, 0.83559, 2, 2, 98.72, 192.88, 0.16438, 3, 25.19, 191.31, 0.83562, 2, 2, 107.58, 170.27, 0.16501, 3, 33.62, 168.54, 0.83499, 2, 2, 107.93, 153.82, 0.16593, 3, 33.66, 152.09, 0.83407, 2, 2, 102.68, 127.57, 0.168, 3, 27.92, 125.94, 0.832, 2, 2, 92.98, 112.31, 0.16878, 3, 17.94, 110.86, 0.83122, 2, 2, 76.53, 123.34, 0.17204, 3, 1.7, 122.19, 0.82796, 2, 2, 58.61, 119.28, 0.18846, 3, -16.29, 118.47, 0.81154, 2, 2, 76.64, 102.48, 0.20542, 3, 1.42, 101.33, 0.79458, 2, 2, 86.58, 88.02, 0.15417, 3, 11.09, 86.69, 0.84583, 2, 2, 103.76, 101.6, 0.04652, 3, 28.52, 99.95, 0.95348, 2, 2, 117.06, 124.88, 0.00731, 3, 42.25, 122.98, 0.99269, 2, 2, 123.08, 153.61, 0.00023, 3, 48.81, 151.6, 0.99977, 1, 3, 53.52, 163.9, 1, 1, 3, 70.33, 177.2, 1, 1, 3, 105.68, 167.62, 1, 1, 3, 138.44, 131.65, 1, 1, 3, 140.51, 99.86, 1, 1, 3, 119.51, 81.17, 1, 1, 3, 135.89, 54.65, 1, 1, 3, 141.55, 37.14, 1, 1, 3, 146.5, 10.2, 1, 1, 3, 160.09, -2.83, 1, 1, 3, 173.19, -13.93, 1, 1, 3, 188.49, -37.85, 1, 1, 3, 187.72, -71.96, 1, 1, 3, 185.38, -103.42, 1, 1, 3, 170.97, -111.84, 1, 1, 3, 147.65, -93.66, 1, 2, 2, 190.81, -71.97, 0.00539, 3, 112.32, -75.21, 0.99461, 2, 2, 208.17, -100.74, 1e-05, 3, 129.14, -104.3, 0.99999, 2, 2, 200.3, -117.37, 0.00016, 3, 120.96, -120.78, 0.99984, 2, 2, 164.04, -110.3, 0.02145, 3, 84.83, -113.03, 0.97855, 2, 2, 146.01, -93.5, 0.08218, 3, 67.12, -95.9, 0.91782, 2, 2, 127.56, -95.63, 0.19011, 3, 48.64, -97.69, 0.80989, 2, 2, 112.37, -96.05, 0.31772, 3, 33.45, -97.83, 0.68228, 2, 2, 108.77, -126.82, 0.43096, 3, 29.27, -128.52, 0.56904, 2, 2, 90.64, -157.41, 0.45431, 3, 10.57, -158.77, 0.54569, 2, 2, 70.65, -164.52, 0.45886, 3, -9.55, -165.5, 0.54114, 2, 2, 46.6, -165.15, 0.4614, 3, -33.6, -165.68, 0.5386, 2, 2, 34.42, -156.82, 0.46171, 3, -45.62, -157.13, 0.53829, 2, 2, 53.78, -138.3, 0.46028, 3, -25.93, -138.97, 0.53972, 3, 1, 108.54, 96.08, 0.00036, 2, 64.77, -111.1, 0.47027, 3, -14.43, -111.99, 0.52938, 3, 1, 90.87, 96.08, 0.00536, 2, 65.75, -93.46, 0.54579, 3, -13.12, -94.37, 0.44885, 3, 1, 92.77, 56.95, 0.06703, 2, 26.58, -93.18, 0.78734, 3, -52.28, -93.36, 0.14563, 3, 1, 83.93, 39.92, 0.13594, 2, 10.06, -83.42, 0.79043, 3, -68.61, -83.29, 0.07363, 3, 1, 76.36, 18.46, 0.30232, 2, -10.94, -74.67, 0.6804, 3, -89.44, -74.15, 0.01728, 3, 1, 72.58, 5.21, 0.42642, 2, -23.96, -70.15, 0.56931, 3, -102.38, -69.39, 0.00427, 2, 1, 67.53, -16.87, 0.55278, 2, -45.73, -63.89, 0.44722, 2, 1, 62.48, -17.3, 0.55919, 2, -45.87, -58.82, 0.44081, 2, 1, 28.41, -17.3, 0.79694, 2, -43.98, -24.8, 0.20306, 2, 1, -15.76, -17.3, 0.98382, 2, -41.53, 19.3, 0.01618, 2, 1, -46.05, -17.3, 0.90926, 2, -39.85, 49.54, 0.09074, 2, 1, -52.36, -1.1, 0.84111, 2, -23.33, 54.94, 0.15889, 2, 1, -54.89, 9, 0.81196, 2, -13.11, 56.9, 0.18804, 3, 1, -34.69, 15.94, 0.49113, 2, -7.3, 36.36, 0.50354, 3, -83.73, 36.79, 0.00532, 3, 1, -60.57, 28.56, 0.14014, 2, 6.74, 61.49, 0.8246, 3, -69.23, 61.66, 0.03526, 3, 1, -49.84, 39.29, 0.09902, 2, 16.85, 50.18, 0.83168, 3, -59.33, 50.17, 0.0693, 3, 1, -55.52, 58.85, 0.0133, 2, 36.7, 54.77, 0.70388, 3, -39.4, 54.38, 0.28282, 3, 1, -14.64, 17.85, 0.37699, 2, -6.51, 16.23, 0.62236, 3, -83.32, 16.65, 0.00065, 2, 1, 18.25, 20.3, 0.26799, 2, -5.88, -16.75, 0.73201, 3, 1, 57.04, 21.28, 0.31116, 2, -7.05, -55.53, 0.67817, 3, -85.2, -55.09, 0.01067, 3, 1, -23.48, 49.27, 0.02218, 2, 25.36, 23.31, 0.92867, 3, -51.33, 23.14, 0.04916, 3, 1, 22.18, 54.18, 0.0126, 2, 27.73, -22.55, 0.97852, 3, -49.81, -22.76, 0.00888, 3, 1, 62.93, 58.11, 0.0674, 2, 29.39, -63.46, 0.82038, 3, -48.91, -63.69, 0.11222, 3, 1, -47.05, 76.28, 0.00044, 2, 53.63, 45.35, 0.55778, 3, -22.65, 44.64, 0.44178, 2, 2, 58.4, 7.22, 0.92265, 3, -18.6, 6.43, 0.07735, 3, 1, 34.95, 89.04, 0.00137, 2, 61.83, -37.23, 0.70024, 3, -15.99, -38.07, 0.29839, 3, 1, 66.86, 94.93, 0.00621, 2, 65.94, -69.43, 0.59959, 3, -12.48, -70.34, 0.3942, 2, 2, 85.03, 61.8, 0.17995, 3, 9.05, 60.5, 0.82005, 2, 2, 85.47, 16.53, 0.05526, 3, 8.65, 15.24, 0.94474, 2, 2, 92.61, -32.06, 0.23142, 3, 14.87, -33.47, 0.76858, 3, 1, 70.3, 121.45, 0.00014, 2, 92.22, -74.33, 0.38594, 3, 13.7, -75.73, 0.61391, 2, 2, 114.37, 94.1, 0.03651, 3, 38.98, 92.25, 0.96349, 2, 2, 112.92, 32.71, 0.01261, 3, 36.4, 30.9, 0.98739, 2, 2, 121.5, 1.25, 0.00022, 3, 44.39, -0.71, 0.99978, 2, 2, 124.14, -30.86, 0.05552, 3, 46.43, -32.86, 0.94448, 2, 2, 142.45, -55.48, 0.06616, 3, 64.27, -57.82, 0.93384, 2, 2, 153.4, 141.6, 1e-05, 3, 78.89, 139.02, 0.99999, 2, 2, 146.72, 101.15, 0.00329, 3, 71.47, 98.7, 0.99671, 2, 2, 146.37, 68.22, 0.00317, 3, 70.5, 65.79, 0.99683, 1, 3, 67.26, 22.21, 1, 2, 2, 160.48, -23.04, 0.00471, 3, 82.9, -25.73, 0.99529, 2, 2, 185.02, -41.62, 0.00281, 3, 107.09, -44.76, 0.99719, 1, 3, 105.71, 95.67, 1, 1, 3, 114.9, 20.64, 1, 1, 3, 128.1, -27.11, 1, 1, 3, 102.22, 61.95, 1, 1, 3, 151.03, -43.1, 1, 1, 3, 171.25, -69.22, 1], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 0, 138]}}, "Layer 598": {"Layer 598": {"type": "mesh", "hull": 4, "width": 92, "height": 91, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-7.36, 54.39, 81.74, 31.48, 59.08, -56.66, -30.02, -33.74], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "bonus_flare": {"bonus_flare": {"x": -1.93, "y": 0.34, "width": 99, "height": 96}}, "cammap_00000": {"cammap_00000": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00001": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00002": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00003": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00004": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00005": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00006": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00007": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00008": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00009": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00010": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00011": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00012": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00013": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}, "cammap_00014": {"x": 10.28, "y": 72.86, "width": 200, "height": 200}}}}, "animations": {"animation": {"slots": {"cammap_00000": {"attachment": [{"time": 0, "name": "cammap_00000"}, {"time": 0.0667, "name": "cammap_00001"}, {"time": 0.1667, "name": "cammap_00002"}, {"time": 0.2333, "name": "cammap_00003"}, {"time": 0.3, "name": "cammap_00004"}, {"time": 0.3667, "name": "cammap_00005"}, {"time": 0.4333, "name": "cammap_00006"}, {"time": 0.5, "name": "cammap_00007"}, {"time": 0.5333, "name": "cammap_00008"}, {"time": 0.6333, "name": "cammap_00009"}, {"time": 0.7, "name": "cammap_00010"}, {"time": 0.7667, "name": "cammap_00011"}, {"time": 0.8333, "name": "cammap_00012"}, {"time": 0.9, "name": "cammap_00013"}, {"time": 0.9667, "name": "cammap_00014"}, {"time": 1.0333, "name": "cammap_00000"}, {"time": 1.1, "name": "cammap_00001"}, {"time": 1.2, "name": "cammap_00002"}, {"time": 1.2667, "name": "cammap_00003"}, {"time": 1.3333, "name": "cammap_00004"}, {"time": 1.4, "name": "cammap_00005"}, {"time": 1.4667, "name": "cammap_00006"}, {"time": 1.5333, "name": "cammap_00007"}, {"time": 1.5667, "name": "cammap_00008"}, {"time": 1.6667, "name": "cammap_00009"}, {"time": 1.7333, "name": "cammap_00010"}, {"time": 1.8, "name": "cammap_00011"}, {"time": 1.8667, "name": "cammap_00012"}, {"time": 1.9333, "name": "cammap_00013"}, {"time": 2, "name": "cammap_00014"}]}}, "bones": {"Layer 599": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0, "y": -11.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Layer 598": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Layer 601": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.239, "y": 1.239, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Layer 602": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 7.44, "y": 5.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Layer 596": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0, "y": -4.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 0, "y": 0}]}, "Layer 600": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 3.22, "y": 5.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "bonus_flare": {"rotate": [{"time": 0, "angle": 0}, {"time": 1, "angle": 180}, {"time": 2, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 1, "x": 1, "y": 1}, {"time": 2, "x": 0, "y": 0}]}}, "deform": {"default": {"Layer 597": {"Layer 597": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 286, "vertices": [-0.7655, 4.02827, 4.06456, 0.54086, 4.09995, 6e-05, -0.7655, 4.02827, 4.06456, 0.54086, 4.09995, 6e-05, 0, 0, 0, 0, 0, 0, -0.7655, 4.02827, 4.06456, 0.54086, 4.09995, 6e-05, 4.06456, 0.54086, 4.09995, 6e-05, -0.7655, 4.02827, 4.06456, 0.54086, 4.09995, 6e-05, 0, 0, 0, 0, 0, 0, 4.06456, 0.54086, 4.09995, 6e-05, 4.06456, 0.54086, 4.09995, 6e-05, 4.06456, 0.54086, 4.09995], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}}}, [0]]], 0, 0, [0], [-1], [0]]