[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "4bkoos9CpCK7vPZ8iOblYA", "d9FZLNPidOEojqaHsmakXc", "2f5uiDmuFP5YppIV/NENKT", "6fgBCSDDdPMInvyNlggls2", "89ksg3sNlBNIcaQxAIMzu4", "56n+v1pyROualSktxFR0r1", "fdNoodJKVLj4dF1TLppv2g", "e5ovyOestOw7K06CKpU8KY", "2fuBPvbetNmpyJQPn8doRE", "3d/5tJqAhHLpBfHHHyX2pF", "8fiVdjvORK4LcsO59YwgPl", "06m3XlLwpMCKFPsYe67/rz", "b74P/3TO9Pa6F1F88iawID", "70Aic4HTxN3plZoOg2GiOO", "61AiHZqM5FP4Pk4FPtfKiJ", "c26lgLUC9GUoretPN6S8gr", "25A0CGAWRCSZTV4wGU8GfV", "12AmNLiFlAKJh+cPF55yRq", "9ex7uOpIlJ07iZH5hO9Ott", "a9VpD0DP5LJYQPXITZq+uj", "62TTC6G+hHCamgiYceJ0w6", "e8zRISlSpJaY0/oMNgi0gY", "7a/QZLET9IDreTiBfRn2PD", "e1NJ8c5BZHI5JcP4RUdG0s", "7cJTLBFK9OuI73yN8QqVoe", "74HyseAK5EoZUJoxVzNBs6", "77wAbDnElABbQH4hahkycC", "52nxqPOeRFrazczIYX5Wwp", "c3KmmcKOhLgpJ8+jckYnkd", "1fT9G5zwhBsITxiLbNVqvL", "2cWB/vWPRHja3uQTinHH30", "12swa4CfxKq5wo6rWlUbAo", "5f9ZjEFtJDvIWof3T2U5ML", "395WgpiPRPA6B+FWxKzbWv", "73o7jEg2dIi698QZi+98wE", "6bPprqvXdBQ7UH8AVQhyQl", "7aPlP2c8hIrZhYTaf4f0Rc"], ["node", "_N$file", "_spriteFrame", "_textureSetter", "_parent", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "lbTotalHo", "lbTotalHoa", "lbTotalRong", "btnBack", "btnNext", "pageView", "dragonTigerGraphCard3View", "dragonTigerGraphCatCauView", "dragonTigerGraph100View", "_N$target", "_N$content", "data", "lbResult", "lbSessionID", "toggleHo", "checkMark", "_defaultClip", "spriteFrame"], [["cc.Node", ["_name", "_opacity", "_prefab", "_contentSize", "_components", "_trs", "_parent", "_children", "_anchorPoint", "_color"], 1, 4, 5, 9, 7, 1, 2, 5, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_N$verticalAlign", "_enableWrapText", "_N$horizontalAlign", "_N$overflow", "_materials", "node", "_N$file"], -5, 3, 1, 6], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint", "_color"], 2, 1, 2, 4, 5, 7, 12, 5, 5], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_anchorPoint"], 2, 12, 4, 5, 7, 1, 5], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingY", "_N$spacingX", "_N$startAxis", "_N$paddingLeft", "_N$paddingRight", "_N$paddingTop", "_N$paddingBottom", "_N$verticalDirection", "_N$horizontalDirection", "node", "_layoutSize"], -8, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Graphics", ["_lineWidth", "node", "_materials", "_strokeColor", "_fillColor"], 2, 1, 3, 5, 5], ["cc.Toggle", ["_N$transition", "node", "_N$normalColor", "_N$target", "checkEvents", "checkMark"], 2, 1, 5, 1, 9, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 2, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["1eae07h3zJF8KMHovK5Vllm", ["node", "dragonTigerGraph100View", "dragonTigerGraphCatCauView", "dragonTigerGraphCard3View", "pageView", "btnNext", "btnBack", "lbTotalRong", "lbTotalHoa", "lbTotalHo"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["vertical", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["53897ifwUhMioozuesz3BM9", ["node", "nodeParent", "nodeRongTemp", "nodeHoaTemp", "nodeHoTemp"], 3, 1, 1, 1, 1, 1], ["07c7dcppWpEOa/f1ADn5FlO", ["node", "nodeParent", "nodeRongTemp", "nodeHoaTemp", "nodeHoTemp"], 3, 1, 1, 1, 1, 1], ["2b5f5d+/A5CvbS1vm4zE11T", ["node", "nodeGraphics1", "nodeGraphics2", "nodeGraphics3", "toggleRong", "colorRong", "colorHo", "colorHoa"], 3, 1, 1, 1, 1, 1, 5, 5, 5], ["<PERSON>.<PERSON>", ["bounceDuration", "node", "_N$content", "pageEvents"], 2, 1, 1, 9], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[8, 0, 1, 2], [0, 0, 6, 4, 2, 3, 5, 2], [2, 0, 1, 3, 2, 6, 4, 9, 8, 10, 7], [3, 0, 1, 3, 4, 5, 3], [0, 0, 6, 4, 2, 3, 2], [15, 0, 1, 2, 3], [0, 0, 6, 7, 4, 2, 3, 5, 2], [0, 0, 7, 2, 3, 5, 2], [4, 0, 1, 2, 3, 4, 7, 5, 2], [3, 2, 0, 3, 4, 5, 3], [3, 3, 4, 5, 1], [19, 0, 1, 2, 2], [0, 0, 7, 4, 2, 3, 8, 5, 2], [0, 0, 6, 7, 4, 2, 3, 2], [0, 0, 6, 4, 2, 3, 8, 2], [0, 0, 6, 4, 2, 9, 3, 8, 5, 2], [4, 0, 1, 2, 3, 4, 2], [5, 0, 5, 1, 2, 3, 4, 2], [5, 0, 5, 1, 2, 3, 6, 4, 2], [12, 0, 1, 2, 3, 4, 5, 6, 2], [6, 1, 0, 2, 11, 12, 4], [9, 0, 1, 2, 3, 2], [3, 0, 1, 3, 4, 3], [7, 2, 7, 1], [7, 1, 0, 2, 3, 4, 5, 6, 3], [16, 0, 1, 2, 3, 4, 5, 6, 7, 7], [2, 0, 1, 3, 5, 2, 4, 7, 9, 8, 8], [2, 0, 1, 3, 2, 4, 9, 8, 10, 6], [11, 0, 2], [0, 0, 7, 4, 2, 2], [0, 0, 7, 4, 2, 3, 5, 2], [0, 0, 6, 2, 3, 2], [0, 0, 6, 7, 2, 5, 2], [0, 0, 1, 6, 4, 2, 3, 5, 3], [0, 0, 2, 3, 8, 5, 2], [0, 0, 6, 7, 4, 2, 3, 8, 5, 2], [0, 0, 6, 4, 2, 3, 8, 5, 2], [0, 0, 6, 7, 2, 8, 5, 2], [4, 0, 1, 6, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 8, 4, 5, 2], [5, 0, 1, 2, 3, 4, 2], [13, 0, 1, 2, 1], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [8, 1, 1], [6, 1, 0, 3, 11, 12, 4], [6, 0, 4, 5, 6, 7, 8, 3, 2, 9, 10, 11, 12, 11], [9, 0, 1, 2, 3, 4, 2], [3, 0, 3, 4, 5, 2], [3, 2, 0, 1, 3, 4, 5, 4], [7, 0, 2, 3, 4, 5, 6, 2], [17, 0, 1], [18, 0, 1, 2, 3, 4, 5, 6, 6], [2, 0, 1, 2, 9, 8, 10, 4], [2, 0, 1, 3, 5, 2, 6, 4, 7, 9, 8, 9], [2, 0, 1, 3, 5, 2, 6, 4, 8, 8], [2, 0, 1, 3, 5, 2, 6, 4, 9, 8, 8], [20, 0, 1, 2, 3, 4, 1], [21, 0, 1, 2, 3, 4, 1], [10, 0, 1, 2, 3, 5, 4, 2], [10, 0, 1, 2, 3, 4, 2], [22, 0, 1, 2, 3, 4, 5, 6, 7, 1], [23, 0, 1, 2, 3, 2], [24, 0, 1, 2, 4]], [[[{"name": "btn_next_disable", "rect": [1, 1, 40, 54], "offset": [0, 0], "originalSize": [42, 56], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [10]], [[{"name": "txt_ho", "rect": [0, 0, 83, 67], "offset": [0, 0], "originalSize": [83, 67], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [11]], [[{"name": "sq", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [2, 2, 2, 2]}], [1], 0, [0], [3], [12]], [[{"name": "so_cnh@export", "rect": [0, 0, 140, 141], "offset": [-0.5, 0.5], "originalSize": [141, 142], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [13]], [[{"name": "btn_next", "rect": [1, 1, 40, 54], "offset": [0, 0], "originalSize": [42, 56], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [14]], [[{"name": "txt_rong", "rect": [0, 0, 117, 67], "offset": [0, 0], "originalSize": [117, 67], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [15]], [[{"name": "checkBox", "rect": [0, 0, 29, 31], "offset": [0, 0], "originalSize": [29, 31], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [16]], [[{"name": "btn_pre_disable", "rect": [1, 0, 41, 55], "offset": [0.5, 0], "originalSize": [42, 55], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [17]], [[{"name": "p_Hoa", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [18]], [[{"name": "p_Ho", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [19]], [[{"name": "p_Rong", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [20]], [[[28, "dragonTigerGraphView"], [29, "dragonTigerGraphView", [-13, -14, -15, -16, -17, -18, -19], [[42, -2, [79, 80], 78], [43, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [44, -1]], [30, "layout-numberTop", [-21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33], [[20, 1, 2, 12.7, -20, [5, 30, 451.4]]], [0, "f4fe2olC9Nz6Tnk9lGuubE", 1], [5, 30, 451.4], [-374, -43.9, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "page_2", [-34, -35, -36, -37, -38, 2, -39], [0, "180if/3X9I26r2zvXfL42M", 1], [5, 850, 600], [1275, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "title", [-40, -41, -42, -43, -44, -45], [0, "a62NBM9cRBUKndcneEEfdk", 1], [5, 800, 56], [0, 230, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "page_1", [4, -46, -47, -48, -49], [0, "3bmetvDZZCJYF7Pzous+gQ", 1], [5, 850, 600], [425, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "layout-parentDice1", [-51, -52], [[47, 1, -50, [38], [4, 4294967295], [4, 4294901503]]], [0, "236vpd2ytI4q9cgtfXteHL", 1], [5, 4000, 520], [0, 1, 0.5], [403.1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "pageview", 1, [[[31, "background", -54, [0, "aaGhfifCVNAbJqusxqTjty", 1], [5, 400, 350]], -55, -56], 4, 1, 1], [-53], [0, "f141bbdD5Berpy4/Chtz9K", 1], [5, 1000, 600], [0, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "content", [5, 3], [[20, 1, 1, 15, -57, [5, 1700, 550]]], [0, "8bQGt5xtNPOqg+pqO6p4Qw", 1], [5, 1700, 550], [0, 0, 0.5], [-425, 24, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "dragonTigerGraphCard3View", [[[9, 2, 0, -58, [40], 41], -59], 4, 1], [0, "a2kaqcxCNGs6LgQbe4tHTX", 1], [5, 802, 480], [0, 300, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "btnBackSession", 1, [[[3, 2, false, -60, [7], 8], -61], 4, 1], [0, "d4KwTQgstMYLzPxxVvTr3P", 1], [5, 42, 56], [-475, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "btnNextSession", 1, [[[3, 2, false, -62, [9], 10], -63], 4, 1], [0, "34dpTSOlNAdYmcP0GDboYj", 1], [5, 42, 55], [475, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-66], [[50, 3, -65, [[5, "1eae07h3zJF8KMHovK5Vllm", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -64]], [0, "d555BWhqdOwqXaJVVgCSqx", 1], [5, 80, 80], [506.226, 315.382, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [32, "temp", 7, [-67, -68, -69], [0, "30IMlsJH1EyITqqnRU+nU3", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "dot_Rong", 13, [[3, 0, false, -70, [13], 14]], [0, "d6NlaknBdGvIXf4PumChbu", 1], [5, 23, 24]], [1, "dot_Hoa", 13, [[3, 0, false, -71, [15], 16]], [0, "0bIVe6rz1MRIi7AYH0jVPv", 1], [5, 23, 24], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dot_Ho", 13, [[3, 0, false, -72, [17], 18]], [0, "c4FcXWcZVFOrRcQlmXXqni", 1], [5, 23, 24], [0, -75.7, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "scrollview", 5, [-76], [[25, false, 0.1, false, 0.23, null, null, -74, -73], [51, -75]], [0, "40Q0dqGURD3aWi7Q2DZHUV", 1], [5, 802, 242], [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "toggle-rong", 3, [-78, -79], [-77], [0, "2c5l/9HBZBfrBYEiUF+SL+", 1], [5, 28, 28], [-123, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "toggle-ho", 3, [-81, -82], [-80], [0, "7bMxkmc4ZCAJjpN+3DY8Tt", 1], [5, 28, 28], [82, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "layout-session", 3, [-84, -85], [[45, 1, 1, 5, -83, [5, 459.65, 50]]], [0, "6ewYJJEjlK+Z0fgmwahsUl", 1], [5, 459.65, 50], [0, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "black", 100, 1, [[48, 0, -86, [0], 1], [23, -87, [4, 4292269782]]], [0, "6fMEV9KgxOi6M7CBjegh34", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nen popup", 1, [[49, 1, 0, false, -88, [2], 3], [23, -89, [4, 4292269782]]], [0, "49EMnkmKhL6py+EaO6hM+w", 1], [5, 1084, 700]], [6, "title_soiCau", 1, [-91], [[52, "default", "animation", 0, false, "animation", -90, [6]]], [0, "51TmR210JKWbev/BL0lEVo", 1], [5, 270, 89.04], [0, 344.974, 0, 0, 0, 0, 1, 1.5, 1, 1]], [13, "view", 7, [8], [[11, 0, -92, [77]]], [0, "fcP4Uw5XFF7oY+K8DBI+PP", 1], [5, 850, 600]], [18, "dragonTigerGraphCatCauView", 5, [[[9, 2, 0, -93, [28], 29], -94], 4, 1], [0, "01P4RtrWxIwKordymiIfba", 1], [5, 802, 242], [0, 1, 0.5], [400, 70, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "layout-parentCatCau", [0, "7ahe1YbOBCqb5ydqyynyTp", 1], [5, 2000, 242], [0, 1, 0.5], [2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "view", 17, [26], [[11, 0, -95, [30]]], [0, "6b0POiuihHHr6aC5qXeG3x", 1], [5, 802, 242], [0, 1, 0.5], [398, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "dragonTigerGraph100View", 5, [[[9, 2, 0, -96, [31], 32], -97], 4, 1], [0, "2dlu4wPfJDgbYzCpyHfc7Y", 1], [5, 802, 201], [0, 1, 0.5], [398, -170, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "layout-parent100", 5, [[46, 3, 1, 12, 12, 25, 10, 17, 16, 0, 1, -98, [5, 804.9, 230]]], [0, "8aXleEb1NNQ4u6gAdLeQeL", 1], [5, 804.9, 230], [0, 1, 0.5], [400, -158, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Background", 18, [[3, 2, false, -99, [33], 34]], [0, "f4AdEbn3VCcKZCCZ2ZKtY1", 1], [5, 29, 31]], [37, "offset-graph", 3, [9, -100], [0, "8c9uMQpw1GUrJ9bHtNfGyJ", 1], [0, 0.5, 1], [0, -307, 0, 0, 0, 0, 1, 0.88, 0.88, 1]], [6, "scrollview-dice3", 31, [-102], [[25, false, 0.1, false, 0.23, null, null, -101, 6]], [0, "b9703nnnVPJZGlBUBleRCp", 1], [5, 800, 520], [0, 300, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "view", 32, [6], [[11, 0, -103, [39]]], [0, "02MWGQi+dFuI5F+CiNKuig", 1], [5, 800, 520]], [14, "layout-parentDice2", 6, [[21, 1, -104, [36], [4, 4294967295]]], [0, "63neosd+BKIYIZTOQOBmQW", 1], [5, 4000, 480], [0, 0, 0.5]], [14, "layout-parentDice3", 6, [[21, 1, -105, [37], [4, 4294967295]]], [0, "31UUgVtB5DvLEMkooBsjEj", 1], [5, 4000, 520], [0, 1, 0.5]], [4, "Background", 19, [[3, 2, false, -106, [42], 43]], [0, "2b1GW3CXJHHaUVrwGIVrBw", 1], [5, 29, 31]], [1, "New Node", 23, [[53, "SOI CẦU", 25, false, -107, [4], 5]], [0, "2amZSpJWpBhqJBxUMiuW7A", 1], [5, 171.09, 31.25], [0, 7.505, 0, 0, 0, 0, 1, 0.7, 1, 1]], [24, 1.1, 2, 10, [[5, "1eae07h3zJF8KMHovK5Vllm", "backPageClicked", 1]], [4, 4294967295], [4, 2533359615], 10], [24, 1.1, 2, 11, [[5, "1eae07h3zJF8KMHovK5Vllm", "nextPageClicked", 1]], [4, 4294967295], [4, 2533359615], 11], [4, "sprite", 12, [[3, 0, false, -108, [11], 12]], [0, "7f+tX+CLJKDJ4aWcBErQ18", 1], [5, 70, 70]], [1, "<PERSON><PERSON>", 4, [[10, -109, [19], 20]], [0, "62nEsG205Gza5NKibDKJNB", 1], [5, 117, 67], [-338, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Hoa", 4, [[10, -110, [21], 22]], [0, "363MGTrWJK3opdKUxCnfRe", 1], [5, 93, 54]], [1, "<PERSON>", 4, [[10, -111, [23], 24]], [0, "61b0fK/b5J0Is7A/sfGUxJ", 1], [5, 83, 67], [352, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbTotalRong", 4, [-112], [0, "5f8M+zj6FG8oceBizVYaBU", 1], [5, 50, 40], [0, 0, 0.5], [-270, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "4", 30, 50, false, false, 1, 1, 44, [25]], [8, "lbTotalHoa", 4, [-113], [0, "01zmk/U/pE9ope5t+wIwb8", 1], [5, 50, 40], [0, 0, 0.5], [60, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "5", 30, 50, false, false, 1, 1, 46, [26]], [8, "lbTotalHo", 4, [-114], [0, "48HuBWQO9J6IGudPomap/8", 1], [5, 50, 40], [0, 1, 0.5], [295, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "4", 30, 50, false, false, 2, 1, 1, 48, [27]], [57, 25, 26, 14, 15, 16], [58, 28, 29, 14, 15, 16], [16, "checkmark", 18, [-115], [0, "37jBKn3GVHxKlxJwAq8Mwu", 1], [5, 18, 16]], [22, 2, false, 52, [35]], [59, 3, 18, [4, 4292269782], 30, 53, [[5, "2b5f5d+/A5CvbS1vm4zE11T", "toggleDrawRongClicked", 9]]], [61, 9, 6, 34, 35, 54, [4, 4278255612], [4, 4279369983], [4, 4288135958]], [60, 3, 19, [4, 4292269782], 36, [[5, "2b5f5d+/A5CvbS1vm4zE11T", "toggleDrawHoClicked", 9]]], [16, "checkmark", 19, [-116], [0, "78wAT0KYVJiZahTQE4lms5", 1], [5, 18, 16]], [22, 2, false, 57, [44]], [55, "<PERSON><PERSON><PERSON><PERSON><PERSON> (#1234569857645)", 21, 50, false, false, 1, 1, [46]], [39, "lbSession", 20, [59], [0, "93aCZmwGZMta2g7HSTK3Pl", 1], [5, 373.8, 26.25], [-42.92499999999998, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "lbResult", 20, [-117], [0, "41oK/qPLFL9ZGzEvWfKISS", 1], [4, 4278705885], [5, 80.85, 26.25], [189.40000000000003, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "Rồng 14", 21, 50, false, false, 1, 1, 61, [45]], [15, "label", 3, [[27, "R<PERSON><PERSON>", 20, 52, false, 1, -118, [47], 48]], [0, "ebSGbVEIhHPK8ve4wS9jrQ", 1], [4, 4278254847], [5, 48, 26], [0, 0, 0.5], [-99.6, 222.2, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "label", 3, [[27, "<PERSON><PERSON>", 20, 52, false, 1, -119, [49], 50]], [0, "90kQOz9yZHpaOqr6Dtg6Uz", 1], [4, 4278584287], [5, 28.5, 26], [0, 0, 0.5], [106, 222, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "13", 20, 46, false, 1, 1, -120, [51], 52]], [0, "84Thec2flG7JpiVsEu/+cJ", 1], [5, 23.5, 23], [0, 214.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "12", 20, 46, false, 1, 1, -121, [53], 54]], [0, "c0a1wG07VLE7ie/ghl5bCR", 1], [5, 23.5, 23], [0, 178.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "11", 20, 46, false, 1, 1, -122, [55], 56]], [0, "65CE4FSAxBw7UPySpPEgSB", 1], [5, 20.5, 23], [0, 142.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "10", 20, 46, false, 1, 1, -123, [57], 58]], [0, "26/jQtaupA5JEiZ+QnYPvk", 1], [5, 23.5, 23], [0, 107.10000000000001, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "9", 20, 46, false, 1, 1, -124, [59], 60]], [0, "9fynMn9+BLTYSMA5XYePBJ", 1], [5, 12, 23], [0, 71.4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "8", 20, 46, false, 1, 1, -125, [61], 62]], [0, "fcrxGBrk5LTr9+8CIRSMGV", 1], [5, 12, 23], [0, 35.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "7", 20, 46, false, 1, 1, -126, [63], 64]], [0, "f9bfCH7f5MIo1Kg8+rATbL", 1], [5, 12, 23], [0, 3.552713678800501e-15, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "6", 20, 46, false, 1, 1, -127, [65], 66]], [0, "272TTsUn5PN7Uz5tehgWvQ", 1], [5, 12, 23], [0, -35.699999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "5", 20, 46, false, 1, 1, -128, [67], 68]], [0, "04Sew287NLJZAL0zVP07/b", 1], [5, 12.5, 23], [0, -71.39999999999999, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "4", 20, 46, false, 1, 1, -129, [69], 70]], [0, "6a2/fSofBE/Iv027puoo3S", 1], [5, 13, 23], [0, -107.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "3", 20, 46, false, 1, 1, -130, [71], 72]], [0, "b3Y+Mbz1dECKQA+xA4KKw4", 1], [5, 12, 23], [0, -142.79999999999998, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "2", 20, 46, false, 1, 1, -131, [73], 74]], [0, "78At2fcThIeIAYb+G3pKsH", 1], [5, 12, 23], [0, -178.49999999999997, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 2, [[2, "1", 20, 46, false, 1, 1, -132, [75], 76]], [0, "ecssIA10ZAfpDqZ5yVm5yK", 1], [5, 9, 23], [0, -214.19999999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [62, 0.5, 7, 8, [[5, "ef32bQW2e5LaKiYTRZis+WJ", "pageEvent", 1]]]], 0, [0, 9, 1, 0, 0, 1, 0, 10, 49, 0, 11, 47, 0, 12, 45, 0, 13, 38, 0, 14, 39, 0, 15, 78, 0, 16, 55, 0, 17, 50, 0, 18, 51, 0, 0, 1, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, -7, 7, 0, 0, 2, 0, -1, 65, 0, -2, 66, 0, -3, 67, 0, -4, 68, 0, -5, 69, 0, -6, 70, 0, -7, 71, 0, -8, 72, 0, -9, 73, 0, -10, 74, 0, -11, 75, 0, -12, 76, 0, -13, 77, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, -4, 63, 0, -5, 64, 0, -7, 31, 0, -1, 41, 0, -2, 42, 0, -3, 43, 0, -4, 44, 0, -5, 46, 0, -6, 48, 0, -2, 25, 0, -3, 28, 0, -4, 29, 0, -5, 17, 0, 0, 6, 0, -1, 34, 0, -2, 35, 0, -1, 78, 0, 4, 7, 0, -2, 13, 0, -3, 24, 0, 0, 8, 0, 0, 9, 0, -2, 55, 0, 0, 10, 0, -2, 38, 0, 0, 11, 0, -2, 39, 0, 19, 12, 0, 0, 12, 0, -1, 40, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 20, 26, 0, 0, 17, 0, 0, 17, 0, -1, 27, 0, -1, 54, 0, -1, 30, 0, -2, 52, 0, -1, 56, 0, -1, 36, 0, -2, 57, 0, 0, 20, 0, -1, 60, 0, -2, 61, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, -1, 37, 0, 0, 24, 0, 0, 25, 0, -2, 50, 0, 0, 27, 0, 0, 28, 0, -2, 51, 0, 0, 29, 0, 0, 30, 0, -2, 32, 0, 0, 32, 0, -1, 33, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, -1, 45, 0, -1, 47, 0, -1, 49, 0, -1, 53, 0, -1, 58, 0, -1, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 21, 1, 2, 4, 3, 3, 4, 8, 4, 4, 5, 5, 4, 8, 6, 4, 33, 8, 4, 24, 9, 4, 31, 26, 4, 27, 55, 22, 62, 55, 23, 59, 55, 24, 56, 56, 25, 58, 59, 0, 60, 132], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 38, 38, 38, 39, 39, 39, 39, 45, 47, 49, 53, 58, 62, 59], [-1, 2, -1, 2, -1, 1, -1, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, -1, -1, -1, 2, -1, -1, 2, -1, 2, -1, -1, -1, -1, -1, -1, 2, -1, 2, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 26, -1, -2, 5, 6, 7, 8, 5, 6, 7, 8, 1, 1, 1, 2, 2, 1, 1], [0, 21, 0, 22, 0, 23, 24, 0, 2, 0, 3, 0, 25, 0, 26, 0, 27, 0, 28, 0, 29, 0, 30, 0, 31, 0, 0, 0, 0, 4, 0, 0, 4, 0, 7, 0, 5, 5, 5, 0, 0, 4, 0, 7, 0, 0, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 8, 8, 32, 2, 2, 2, 33, 3, 3, 3, 34, 6, 6, 6, 9, 9, 1, 1]], [[[63, "so_cnh@export", 32, {"commonHeight": 50, "fontSize": 32, "atlasName": "<EMAIL>", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 0, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 8, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "46": {"xOffset": 0, "yOffset": 27, "xAdvance": 10, "rect": {"x": 0, "y": 98, "width": 18, "height": 22}}, "48": {"xOffset": 0, "yOffset": 2, "xAdvance": 28, "rect": {"x": 0, "y": 0, "width": 36, "height": 48}}, "49": {"xOffset": 0, "yOffset": 3, "xAdvance": 16, "rect": {"x": 108, "y": 47, "width": 24, "height": 46}}, "50": {"xOffset": 0, "yOffset": 3, "xAdvance": 24, "rect": {"x": 108, "y": 0, "width": 32, "height": 46}}, "51": {"xOffset": 0, "yOffset": 3, "xAdvance": 24, "rect": {"x": 106, "y": 94, "width": 32, "height": 47}}, "52": {"xOffset": 0, "yOffset": 3, "xAdvance": 26, "rect": {"x": 73, "y": 47, "width": 34, "height": 46}}, "53": {"xOffset": 0, "yOffset": 3, "xAdvance": 24, "rect": {"x": 73, "y": 94, "width": 32, "height": 47}}, "54": {"xOffset": 0, "yOffset": 2, "xAdvance": 27, "rect": {"x": 37, "y": 49, "width": 35, "height": 48}}, "55": {"xOffset": 0, "yOffset": 3, "xAdvance": 26, "rect": {"x": 73, "y": 0, "width": 34, "height": 46}}, "56": {"xOffset": 0, "yOffset": 2, "xAdvance": 27, "rect": {"x": 37, "y": 0, "width": 35, "height": 48}}, "57": {"xOffset": 0, "yOffset": 2, "xAdvance": 28, "rect": {"x": 0, "y": 49, "width": 36, "height": 48}}}, "kerningDict": {}}]], 0, 0, [0], [27], [35]], [[{"name": "txt_hoa", "rect": [0, 0, 93, 54], "offset": [0, 0], "originalSize": [93, 54], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [36]], [[{"name": "btn_pre", "rect": [1, 0, 41, 55], "offset": [0.5, 0], "originalSize": [42, 55], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [37]], [[{"name": "check", "rect": [0, 0, 18, 16], "offset": [0, 0], "originalSize": [18, 16], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [3], [38]]]]