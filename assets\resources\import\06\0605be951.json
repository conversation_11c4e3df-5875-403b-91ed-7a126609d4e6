[1, ["cbJwGkF/xLAY3gtQwvgKSM", "26Z5aR+ERCHYzPBg8HAM+I"], ["spriteFrame", "_textureSetter"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[[0, "Font-export", 32, {"commonHeight": 89, "fontSize": 32, "atlasName": "Font-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 160, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 20, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 0, "yOffset": 48, "xAdvance": 18, "rect": {"x": 569, "y": 215, "width": 17, "height": 41}}, "35": {"xOffset": 0, "yOffset": 48, "xAdvance": 34, "rect": {"x": 336, "y": 263, "width": 33, "height": 41}}, "36": {"xOffset": 0, "yOffset": 43, "xAdvance": 36, "rect": {"x": 301, "y": 88, "width": 35, "height": 51}}, "37": {"xOffset": 0, "yOffset": 47, "xAdvance": 42, "rect": {"x": 0, "y": 268, "width": 41, "height": 43}}, "38": {"xOffset": 0, "yOffset": 48, "xAdvance": 41, "rect": {"x": 41, "y": 481, "width": 40, "height": 42}}, "39": {"xOffset": 0, "yOffset": 47, "xAdvance": 18, "rect": {"x": 204, "y": 559, "width": 17, "height": 23}}, "40": {"xOffset": 0, "yOffset": 48, "xAdvance": 19, "rect": {"x": 567, "y": 532, "width": 18, "height": 46}}, "41": {"xOffset": 0, "yOffset": 48, "xAdvance": 19, "rect": {"x": 551, "y": 307, "width": 18, "height": 46}}, "42": {"xOffset": 0, "yOffset": 48, "xAdvance": 26, "rect": {"x": 491, "y": 564, "width": 25, "height": 22}}, "43": {"xOffset": 0, "yOffset": 58, "xAdvance": 35, "rect": {"x": 267, "y": 290, "width": 34, "height": 31}}, "44": {"xOffset": 0, "yOffset": 74, "xAdvance": 18, "rect": {"x": 187, "y": 562, "width": 17, "height": 23}}, "45": {"xOffset": 0, "yOffset": 67, "xAdvance": 21, "rect": {"x": 493, "y": 413, "width": 20, "height": 13}}, "46": {"xOffset": 0, "yOffset": 74, "xAdvance": 17, "rect": {"x": 42, "y": 247, "width": 16, "height": 15}}, "47": {"xOffset": 0, "yOffset": 47, "xAdvance": 31, "rect": {"x": 493, "y": 153, "width": 30, "height": 43}}, "48": {"xOffset": 0, "yOffset": 48, "xAdvance": 34, "rect": {"x": 335, "y": 533, "width": 33, "height": 42}}, "49": {"xOffset": 0, "yOffset": 48, "xAdvance": 28, "rect": {"x": 522, "y": 413, "width": 27, "height": 41}}, "50": {"xOffset": 0, "yOffset": 47, "xAdvance": 34, "rect": {"x": 335, "y": 180, "width": 33, "height": 42}}, "51": {"xOffset": 0, "yOffset": 48, "xAdvance": 35, "rect": {"x": 301, "y": 375, "width": 34, "height": 41}}, "52": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 266, "y": 489, "width": 35, "height": 41}}, "53": {"xOffset": 0, "yOffset": 48, "xAdvance": 34, "rect": {"x": 335, "y": 222, "width": 33, "height": 41}}, "54": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 266, "y": 530, "width": 35, "height": 41}}, "55": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 400, "y": 233, "width": 31, "height": 41}}, "56": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 267, "y": 166, "width": 35, "height": 42}}, "57": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 267, "y": 249, "width": 35, "height": 41}}, "58": {"xOffset": 0, "yOffset": 57, "xAdvance": 17, "rect": {"x": 551, "y": 92, "width": 16, "height": 31}}, "59": {"xOffset": 0, "yOffset": 58, "xAdvance": 18, "rect": {"x": 568, "y": 267, "width": 17, "height": 39}}, "63": {"xOffset": 0, "yOffset": 47, "xAdvance": 31, "rect": {"x": 492, "y": 522, "width": 30, "height": 42}}, "64": {"xOffset": 0, "yOffset": 47, "xAdvance": 48, "rect": {"x": 0, "y": 41, "width": 47, "height": 43}}, "65": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 81, "y": 427, "width": 37, "height": 41}}, "66": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 266, "y": 370, "width": 35, "height": 41}}, "67": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 266, "y": 411, "width": 35, "height": 41}}, "68": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 157, "y": 351, "width": 35, "height": 41}}, "69": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 462, "y": 285, "width": 31, "height": 41}}, "70": {"xOffset": 0, "yOffset": 48, "xAdvance": 31, "rect": {"x": 493, "y": 237, "width": 30, "height": 41}}, "71": {"xOffset": 0, "yOffset": 48, "xAdvance": 37, "rect": {"x": 230, "y": 440, "width": 36, "height": 41}}, "72": {"xOffset": 0, "yOffset": 48, "xAdvance": 35, "rect": {"x": 301, "y": 457, "width": 34, "height": 41}}, "73": {"xOffset": 0, "yOffset": 48, "xAdvance": 18, "rect": {"x": 568, "y": 484, "width": 17, "height": 41}}, "74": {"xOffset": 0, "yOffset": 48, "xAdvance": 24, "rect": {"x": 547, "y": 123, "width": 23, "height": 42}}, "75": {"xOffset": 0, "yOffset": 48, "xAdvance": 37, "rect": {"x": 230, "y": 399, "width": 36, "height": 41}}, "76": {"xOffset": 0, "yOffset": 48, "xAdvance": 31, "rect": {"x": 493, "y": 196, "width": 30, "height": 41}}, "77": {"xOffset": 0, "yOffset": 48, "xAdvance": 44, "rect": {"x": 0, "y": 196, "width": 43, "height": 41}}, "78": {"xOffset": 0, "yOffset": 48, "xAdvance": 35, "rect": {"x": 301, "y": 498, "width": 34, "height": 41}}, "79": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 157, "y": 56, "width": 37, "height": 41}}, "80": {"xOffset": 0, "yOffset": 48, "xAdvance": 35, "rect": {"x": 302, "y": 139, "width": 34, "height": 41}}, "81": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 88, "y": 0, "width": 37, "height": 48}}, "82": {"xOffset": 0, "yOffset": 48, "xAdvance": 35, "rect": {"x": 301, "y": 539, "width": 34, "height": 41}}, "83": {"xOffset": 0, "yOffset": 47, "xAdvance": 36, "rect": {"x": 266, "y": 123, "width": 35, "height": 43}}, "84": {"xOffset": 0, "yOffset": 48, "xAdvance": 35, "rect": {"x": 301, "y": 416, "width": 34, "height": 41}}, "85": {"xOffset": 0, "yOffset": 48, "xAdvance": 34, "rect": {"x": 335, "y": 0, "width": 33, "height": 41}}, "86": {"xOffset": 0, "yOffset": 48, "xAdvance": 37, "rect": {"x": 231, "y": 82, "width": 36, "height": 41}}, "87": {"xOffset": 0, "yOffset": 48, "xAdvance": 50, "rect": {"x": 0, "y": 0, "width": 49, "height": 41}}, "88": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 194, "y": 56, "width": 37, "height": 41}}, "89": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 194, "y": 300, "width": 37, "height": 41}}, "90": {"xOffset": 0, "yOffset": 48, "xAdvance": 33, "rect": {"x": 336, "y": 129, "width": 32, "height": 41}}, "91": {"xOffset": 0, "yOffset": 47, "xAdvance": 20, "rect": {"x": 551, "y": 0, "width": 19, "height": 46}}, "92": {"xOffset": 0, "yOffset": 47, "xAdvance": 31, "rect": {"x": 493, "y": 278, "width": 30, "height": 43}}, "93": {"xOffset": 0, "yOffset": 47, "xAdvance": 20, "rect": {"x": 551, "y": 46, "width": 19, "height": 46}}, "94": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 368, "y": 554, "width": 31, "height": 24}}, "95": {"xOffset": 0, "yOffset": 87, "xAdvance": 34, "rect": {"x": 267, "y": 321, "width": 33, "height": 8}}, "97": {"xOffset": 0, "yOffset": 58, "xAdvance": 32, "rect": {"x": 267, "y": 88, "width": 31, "height": 31}}, "98": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 399, "y": 152, "width": 31, "height": 41}}, "99": {"xOffset": 0, "yOffset": 58, "xAdvance": 31, "rect": {"x": 462, "y": 426, "width": 30, "height": 31}}, "100": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 368, "y": 152, "width": 31, "height": 41}}, "101": {"xOffset": 0, "yOffset": 58, "xAdvance": 32, "rect": {"x": 120, "y": 392, "width": 31, "height": 31}}, "102": {"xOffset": 0, "yOffset": 48, "xAdvance": 25, "rect": {"x": 523, "y": 168, "width": 24, "height": 41}}, "103": {"xOffset": 0, "yOffset": 56, "xAdvance": 35, "rect": {"x": 301, "y": 335, "width": 34, "height": 40}}, "104": {"xOffset": 0, "yOffset": 48, "xAdvance": 30, "rect": {"x": 522, "y": 0, "width": 29, "height": 41}}, "105": {"xOffset": 0, "yOffset": 48, "xAdvance": 16, "rect": {"x": 569, "y": 306, "width": 15, "height": 41}}, "106": {"xOffset": 0, "yOffset": 47, "xAdvance": 20, "rect": {"x": 549, "y": 484, "width": 19, "height": 48}}, "107": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 461, "y": 461, "width": 31, "height": 41}}, "108": {"xOffset": 0, "yOffset": 48, "xAdvance": 16, "rect": {"x": 569, "y": 347, "width": 15, "height": 41}}, "109": {"xOffset": 0, "yOffset": 58, "xAdvance": 43, "rect": {"x": 0, "y": 237, "width": 42, "height": 31}}, "110": {"xOffset": 0, "yOffset": 58, "xAdvance": 30, "rect": {"x": 522, "y": 352, "width": 29, "height": 31}}, "111": {"xOffset": 0, "yOffset": 58, "xAdvance": 33, "rect": {"x": 47, "y": 49, "width": 32, "height": 32}}, "112": {"xOffset": 0, "yOffset": 57, "xAdvance": 32, "rect": {"x": 400, "y": 322, "width": 31, "height": 38}}, "113": {"xOffset": 0, "yOffset": 57, "xAdvance": 32, "rect": {"x": 368, "y": 516, "width": 31, "height": 38}}, "114": {"xOffset": 0, "yOffset": 57, "xAdvance": 25, "rect": {"x": 523, "y": 209, "width": 24, "height": 31}}, "115": {"xOffset": 0, "yOffset": 57, "xAdvance": 30, "rect": {"x": 522, "y": 321, "width": 29, "height": 31}}, "116": {"xOffset": 0, "yOffset": 49, "xAdvance": 25, "rect": {"x": 522, "y": 546, "width": 24, "height": 40}}, "117": {"xOffset": 0, "yOffset": 57, "xAdvance": 30, "rect": {"x": 400, "y": 481, "width": 29, "height": 32}}, "118": {"xOffset": 0, "yOffset": 57, "xAdvance": 31, "rect": {"x": 302, "y": 51, "width": 30, "height": 31}}, "119": {"xOffset": 0, "yOffset": 57, "xAdvance": 42, "rect": {"x": 0, "y": 458, "width": 41, "height": 31}}, "120": {"xOffset": 0, "yOffset": 57, "xAdvance": 33, "rect": {"x": 336, "y": 304, "width": 32, "height": 31}}, "121": {"xOffset": 0, "yOffset": 57, "xAdvance": 31, "rect": {"x": 461, "y": 547, "width": 30, "height": 39}}, "122": {"xOffset": 0, "yOffset": 58, "xAdvance": 28, "rect": {"x": 522, "y": 383, "width": 27, "height": 30}}, "123": {"xOffset": 0, "yOffset": 47, "xAdvance": 25, "rect": {"x": 523, "y": 240, "width": 24, "height": 45}}, "124": {"xOffset": 0, "yOffset": 47, "xAdvance": 15, "rect": {"x": 569, "y": 438, "width": 14, "height": 43}}, "125": {"xOffset": 0, "yOffset": 47, "xAdvance": 25, "rect": {"x": 523, "y": 123, "width": 24, "height": 45}}, "192": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 193, "y": 508, "width": 37, "height": 51}}, "193": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 120, "y": 99, "width": 37, "height": 51}}, "194": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 81, "y": 468, "width": 37, "height": 51}}, "195": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 120, "y": 48, "width": 37, "height": 51}}, "200": {"xOffset": 0, "yOffset": 38, "xAdvance": 32, "rect": {"x": 462, "y": 0, "width": 31, "height": 51}}, "201": {"xOffset": 0, "yOffset": 38, "xAdvance": 32, "rect": {"x": 462, "y": 194, "width": 31, "height": 51}}, "202": {"xOffset": 0, "yOffset": 38, "xAdvance": 32, "rect": {"x": 431, "y": 223, "width": 31, "height": 51}}, "204": {"xOffset": 0, "yOffset": 38, "xAdvance": 23, "rect": {"x": 547, "y": 216, "width": 22, "height": 51}}, "205": {"xOffset": 0, "yOffset": 38, "xAdvance": 23, "rect": {"x": 547, "y": 165, "width": 22, "height": 51}}, "210": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 192, "y": 351, "width": 37, "height": 51}}, "211": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 157, "y": 97, "width": 37, "height": 51}}, "212": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 194, "y": 249, "width": 37, "height": 51}}, "213": {"xOffset": 0, "yOffset": 39, "xAdvance": 38, "rect": {"x": 193, "y": 458, "width": 37, "height": 50}}, "217": {"xOffset": 0, "yOffset": 38, "xAdvance": 34, "rect": {"x": 302, "y": 0, "width": 33, "height": 51}}, "218": {"xOffset": 0, "yOffset": 38, "xAdvance": 34, "rect": {"x": 302, "y": 227, "width": 33, "height": 51}}, "221": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 157, "y": 200, "width": 37, "height": 51}}, "224": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 461, "y": 98, "width": 31, "height": 40}}, "225": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 431, "y": 421, "width": 31, "height": 40}}, "226": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 399, "y": 193, "width": 31, "height": 40}}, "227": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 462, "y": 326, "width": 31, "height": 41}}, "232": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 400, "y": 0, "width": 31, "height": 40}}, "233": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 400, "y": 40, "width": 31, "height": 40}}, "234": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 400, "y": 441, "width": 31, "height": 40}}, "236": {"xOffset": 0, "yOffset": 49, "xAdvance": 16, "rect": {"x": 547, "y": 267, "width": 21, "height": 40}}, "237": {"xOffset": 0, "yOffset": 49, "xAdvance": 16, "rect": {"x": 546, "y": 546, "width": 21, "height": 40}}, "242": {"xOffset": 0, "yOffset": 49, "xAdvance": 33, "rect": {"x": 368, "y": 426, "width": 32, "height": 41}}, "243": {"xOffset": 0, "yOffset": 49, "xAdvance": 33, "rect": {"x": 368, "y": 304, "width": 32, "height": 41}}, "244": {"xOffset": 0, "yOffset": 48, "xAdvance": 33, "rect": {"x": 368, "y": 345, "width": 32, "height": 41}}, "245": {"xOffset": 0, "yOffset": 47, "xAdvance": 33, "rect": {"x": 336, "y": 87, "width": 32, "height": 42}}, "249": {"xOffset": 0, "yOffset": 48, "xAdvance": 30, "rect": {"x": 522, "y": 41, "width": 29, "height": 41}}, "250": {"xOffset": 0, "yOffset": 48, "xAdvance": 30, "rect": {"x": 522, "y": 82, "width": 29, "height": 41}}, "253": {"xOffset": 0, "yOffset": 48, "xAdvance": 31, "rect": {"x": 492, "y": 426, "width": 30, "height": 48}}, "258": {"xOffset": 0, "yOffset": 40, "xAdvance": 38, "rect": {"x": 157, "y": 251, "width": 37, "height": 49}}, "259": {"xOffset": 0, "yOffset": 51, "xAdvance": 32, "rect": {"x": 430, "y": 185, "width": 31, "height": 38}}, "272": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 119, "y": 542, "width": 37, "height": 41}}, "273": {"xOffset": 0, "yOffset": 48, "xAdvance": 34, "rect": {"x": 335, "y": 492, "width": 33, "height": 41}}, "296": {"xOffset": 0, "yOffset": 38, "xAdvance": 23, "rect": {"x": 522, "y": 454, "width": 27, "height": 51}}, "297": {"xOffset": 0, "yOffset": 48, "xAdvance": 28, "rect": {"x": 522, "y": 505, "width": 27, "height": 41}}, "360": {"xOffset": 0, "yOffset": 38, "xAdvance": 34, "rect": {"x": 335, "y": 391, "width": 33, "height": 51}}, "361": {"xOffset": 0, "yOffset": 47, "xAdvance": 30, "rect": {"x": 493, "y": 367, "width": 29, "height": 42}}, "416": {"xOffset": 0, "yOffset": 42, "xAdvance": 41, "rect": {"x": 40, "y": 534, "width": 40, "height": 47}}, "417": {"xOffset": 0, "yOffset": 52, "xAdvance": 36, "rect": {"x": 266, "y": 452, "width": 35, "height": 37}}, "431": {"xOffset": 0, "yOffset": 40, "xAdvance": 34, "rect": {"x": 49, "y": 0, "width": 39, "height": 49}}, "432": {"xOffset": 0, "yOffset": 50, "xAdvance": 37, "rect": {"x": 230, "y": 147, "width": 36, "height": 39}}, "7840": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 120, "y": 342, "width": 37, "height": 50}}, "7841": {"xOffset": 0, "yOffset": 58, "xAdvance": 32, "rect": {"x": 462, "y": 245, "width": 31, "height": 40}}, "7842": {"xOffset": 0, "yOffset": 33, "xAdvance": 38, "rect": {"x": 157, "y": 0, "width": 37, "height": 56}}, "7843": {"xOffset": 0, "yOffset": 44, "xAdvance": 32, "rect": {"x": 461, "y": 502, "width": 31, "height": 45}}, "7844": {"xOffset": 0, "yOffset": 33, "xAdvance": 38, "rect": {"x": 0, "y": 84, "width": 44, "height": 56}}, "7845": {"xOffset": 0, "yOffset": 44, "xAdvance": 32, "rect": {"x": 0, "y": 489, "width": 41, "height": 45}}, "7846": {"xOffset": 0, "yOffset": 32, "xAdvance": 38, "rect": {"x": 119, "y": 485, "width": 37, "height": 57}}, "7847": {"xOffset": 0, "yOffset": 43, "xAdvance": 34, "rect": {"x": 335, "y": 41, "width": 33, "height": 46}}, "7848": {"xOffset": 0, "yOffset": 28, "xAdvance": 38, "rect": {"x": 81, "y": 308, "width": 39, "height": 61}}, "7849": {"xOffset": 0, "yOffset": 39, "xAdvance": 32, "rect": {"x": 156, "y": 150, "width": 37, "height": 50}}, "7850": {"xOffset": 0, "yOffset": 30, "xAdvance": 38, "rect": {"x": 119, "y": 166, "width": 37, "height": 59}}, "7851": {"xOffset": 0, "yOffset": 41, "xAdvance": 32, "rect": {"x": 400, "y": 274, "width": 31, "height": 48}}, "7852": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 155, "y": 392, "width": 37, "height": 60}}, "7853": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 369, "y": 239, "width": 31, "height": 49}}, "7854": {"xOffset": 0, "yOffset": 31, "xAdvance": 38, "rect": {"x": 229, "y": 341, "width": 37, "height": 58}}, "7855": {"xOffset": 0, "yOffset": 42, "xAdvance": 32, "rect": {"x": 431, "y": 51, "width": 31, "height": 47}}, "7856": {"xOffset": 0, "yOffset": 31, "xAdvance": 38, "rect": {"x": 82, "y": 186, "width": 37, "height": 58}}, "7857": {"xOffset": 0, "yOffset": 42, "xAdvance": 32, "rect": {"x": 430, "y": 531, "width": 31, "height": 47}}, "7858": {"xOffset": 0, "yOffset": 29, "xAdvance": 38, "rect": {"x": 83, "y": 49, "width": 37, "height": 60}}, "7859": {"xOffset": 0, "yOffset": 39, "xAdvance": 32, "rect": {"x": 430, "y": 481, "width": 31, "height": 50}}, "7860": {"xOffset": 0, "yOffset": 32, "xAdvance": 38, "rect": {"x": 83, "y": 109, "width": 37, "height": 57}}, "7861": {"xOffset": 0, "yOffset": 43, "xAdvance": 32, "rect": {"x": 368, "y": 193, "width": 31, "height": 46}}, "7862": {"xOffset": 0, "yOffset": 40, "xAdvance": 38, "rect": {"x": 118, "y": 427, "width": 37, "height": 58}}, "7863": {"xOffset": 0, "yOffset": 50, "xAdvance": 32, "rect": {"x": 399, "y": 516, "width": 31, "height": 48}}, "7864": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 431, "y": 274, "width": 31, "height": 50}}, "7865": {"xOffset": 0, "yOffset": 58, "xAdvance": 32, "rect": {"x": 400, "y": 401, "width": 31, "height": 40}}, "7866": {"xOffset": 0, "yOffset": 33, "xAdvance": 32, "rect": {"x": 461, "y": 138, "width": 31, "height": 56}}, "7867": {"xOffset": 0, "yOffset": 44, "xAdvance": 32, "rect": {"x": 400, "y": 80, "width": 31, "height": 45}}, "7868": {"xOffset": 0, "yOffset": 38, "xAdvance": 32, "rect": {"x": 431, "y": 0, "width": 31, "height": 51}}, "7869": {"xOffset": 0, "yOffset": 48, "xAdvance": 32, "rect": {"x": 400, "y": 360, "width": 31, "height": 41}}, "7870": {"xOffset": 0, "yOffset": 33, "xAdvance": 32, "rect": {"x": 0, "y": 356, "width": 41, "height": 56}}, "7871": {"xOffset": 0, "yOffset": 44, "xAdvance": 32, "rect": {"x": 0, "y": 311, "width": 41, "height": 45}}, "7872": {"xOffset": 0, "yOffset": 32, "xAdvance": 33, "rect": {"x": 368, "y": 0, "width": 32, "height": 57}}, "7873": {"xOffset": 0, "yOffset": 43, "xAdvance": 33, "rect": {"x": 125, "y": 0, "width": 32, "height": 46}}, "7874": {"xOffset": 0, "yOffset": 28, "xAdvance": 32, "rect": {"x": 230, "y": 481, "width": 36, "height": 61}}, "7875": {"xOffset": 0, "yOffset": 39, "xAdvance": 32, "rect": {"x": 231, "y": 186, "width": 36, "height": 50}}, "7876": {"xOffset": 0, "yOffset": 30, "xAdvance": 32, "rect": {"x": 462, "y": 367, "width": 31, "height": 59}}, "7877": {"xOffset": 0, "yOffset": 41, "xAdvance": 32, "rect": {"x": 431, "y": 324, "width": 31, "height": 48}}, "7878": {"xOffset": 0, "yOffset": 38, "xAdvance": 32, "rect": {"x": 430, "y": 125, "width": 31, "height": 60}}, "7879": {"xOffset": 0, "yOffset": 49, "xAdvance": 32, "rect": {"x": 431, "y": 372, "width": 31, "height": 49}}, "7880": {"xOffset": 0, "yOffset": 33, "xAdvance": 21, "rect": {"x": 549, "y": 428, "width": 20, "height": 56}}, "7881": {"xOffset": 0, "yOffset": 44, "xAdvance": 21, "rect": {"x": 549, "y": 383, "width": 20, "height": 45}}, "7882": {"xOffset": 0, "yOffset": 48, "xAdvance": 18, "rect": {"x": 569, "y": 165, "width": 17, "height": 50}}, "7883": {"xOffset": 0, "yOffset": 48, "xAdvance": 16, "rect": {"x": 569, "y": 388, "width": 15, "height": 50}}, "7884": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 193, "y": 148, "width": 37, "height": 50}}, "7885": {"xOffset": 0, "yOffset": 57, "xAdvance": 33, "rect": {"x": 368, "y": 386, "width": 32, "height": 40}}, "7886": {"xOffset": 0, "yOffset": 33, "xAdvance": 38, "rect": {"x": 193, "y": 402, "width": 37, "height": 56}}, "7887": {"xOffset": 0, "yOffset": 43, "xAdvance": 33, "rect": {"x": 368, "y": 106, "width": 32, "height": 46}}, "7888": {"xOffset": 0, "yOffset": 33, "xAdvance": 38, "rect": {"x": 0, "y": 140, "width": 44, "height": 56}}, "7889": {"xOffset": 0, "yOffset": 43, "xAdvance": 33, "rect": {"x": 0, "y": 412, "width": 41, "height": 46}}, "7890": {"xOffset": 0, "yOffset": 32, "xAdvance": 38, "rect": {"x": 120, "y": 225, "width": 37, "height": 57}}, "7891": {"xOffset": 0, "yOffset": 42, "xAdvance": 33, "rect": {"x": 302, "y": 180, "width": 33, "height": 47}}, "7892": {"xOffset": 0, "yOffset": 28, "xAdvance": 38, "rect": {"x": 81, "y": 247, "width": 39, "height": 61}}, "7893": {"xOffset": 0, "yOffset": 38, "xAdvance": 33, "rect": {"x": 156, "y": 511, "width": 37, "height": 51}}, "7894": {"xOffset": 0, "yOffset": 30, "xAdvance": 38, "rect": {"x": 156, "y": 452, "width": 37, "height": 59}}, "7895": {"xOffset": 0, "yOffset": 40, "xAdvance": 33, "rect": {"x": 368, "y": 467, "width": 32, "height": 49}}, "7896": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 120, "y": 282, "width": 37, "height": 60}}, "7897": {"xOffset": 0, "yOffset": 48, "xAdvance": 33, "rect": {"x": 368, "y": 57, "width": 32, "height": 49}}, "7898": {"xOffset": 0, "yOffset": 38, "xAdvance": 41, "rect": {"x": 41, "y": 374, "width": 40, "height": 51}}, "7899": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 267, "y": 208, "width": 35, "height": 41}}, "7900": {"xOffset": 0, "yOffset": 38, "xAdvance": 41, "rect": {"x": 0, "y": 534, "width": 40, "height": 51}}, "7901": {"xOffset": 0, "yOffset": 48, "xAdvance": 36, "rect": {"x": 266, "y": 329, "width": 35, "height": 41}}, "7902": {"xOffset": 0, "yOffset": 33, "xAdvance": 41, "rect": {"x": 41, "y": 268, "width": 40, "height": 56}}, "7903": {"xOffset": 0, "yOffset": 43, "xAdvance": 36, "rect": {"x": 267, "y": 0, "width": 35, "height": 46}}, "7904": {"xOffset": 0, "yOffset": 39, "xAdvance": 41, "rect": {"x": 41, "y": 324, "width": 40, "height": 50}}, "7905": {"xOffset": 0, "yOffset": 47, "xAdvance": 36, "rect": {"x": 267, "y": 46, "width": 35, "height": 42}}, "7906": {"xOffset": 0, "yOffset": 42, "xAdvance": 41, "rect": {"x": 41, "y": 425, "width": 40, "height": 56}}, "7907": {"xOffset": 0, "yOffset": 52, "xAdvance": 36, "rect": {"x": 301, "y": 290, "width": 35, "height": 45}}, "7908": {"xOffset": 0, "yOffset": 48, "xAdvance": 34, "rect": {"x": 335, "y": 442, "width": 33, "height": 50}}, "7909": {"xOffset": 0, "yOffset": 57, "xAdvance": 30, "rect": {"x": 493, "y": 0, "width": 29, "height": 40}}, "7910": {"xOffset": 0, "yOffset": 33, "xAdvance": 34, "rect": {"x": 335, "y": 335, "width": 33, "height": 56}}, "7911": {"xOffset": 0, "yOffset": 43, "xAdvance": 30, "rect": {"x": 493, "y": 321, "width": 29, "height": 46}}, "7912": {"xOffset": 0, "yOffset": 38, "xAdvance": 34, "rect": {"x": 43, "y": 196, "width": 39, "height": 51}}, "7913": {"xOffset": 0, "yOffset": 48, "xAdvance": 37, "rect": {"x": 231, "y": 41, "width": 36, "height": 41}}, "7914": {"xOffset": 0, "yOffset": 38, "xAdvance": 34, "rect": {"x": 44, "y": 84, "width": 39, "height": 51}}, "7915": {"xOffset": 0, "yOffset": 48, "xAdvance": 37, "rect": {"x": 231, "y": 0, "width": 36, "height": 41}}, "7916": {"xOffset": 0, "yOffset": 33, "xAdvance": 34, "rect": {"x": 80, "y": 523, "width": 39, "height": 56}}, "7917": {"xOffset": 0, "yOffset": 43, "xAdvance": 37, "rect": {"x": 231, "y": 283, "width": 36, "height": 46}}, "7918": {"xOffset": 0, "yOffset": 38, "xAdvance": 34, "rect": {"x": 44, "y": 135, "width": 39, "height": 51}}, "7919": {"xOffset": 0, "yOffset": 47, "xAdvance": 37, "rect": {"x": 230, "y": 542, "width": 36, "height": 42}}, "7920": {"xOffset": 0, "yOffset": 40, "xAdvance": 34, "rect": {"x": 81, "y": 369, "width": 39, "height": 58}}, "7921": {"xOffset": 0, "yOffset": 50, "xAdvance": 37, "rect": {"x": 231, "y": 236, "width": 36, "height": 47}}, "7922": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 157, "y": 300, "width": 37, "height": 51}}, "7923": {"xOffset": 0, "yOffset": 48, "xAdvance": 31, "rect": {"x": 492, "y": 474, "width": 30, "height": 48}}, "7924": {"xOffset": 0, "yOffset": 48, "xAdvance": 38, "rect": {"x": 194, "y": 97, "width": 37, "height": 50}}, "7925": {"xOffset": 0, "yOffset": 57, "xAdvance": 31, "rect": {"x": 462, "y": 51, "width": 30, "height": 40}}, "7926": {"xOffset": 0, "yOffset": 33, "xAdvance": 38, "rect": {"x": 194, "y": 0, "width": 37, "height": 56}}, "7927": {"xOffset": 0, "yOffset": 43, "xAdvance": 31, "rect": {"x": 492, "y": 51, "width": 30, "height": 53}}, "7928": {"xOffset": 0, "yOffset": 38, "xAdvance": 38, "rect": {"x": 194, "y": 198, "width": 37, "height": 51}}, "7929": {"xOffset": 0, "yOffset": 47, "xAdvance": 31, "rect": {"x": 492, "y": 104, "width": 30, "height": 49}}, "8221": {"xOffset": 0, "yOffset": 47, "xAdvance": 32, "rect": {"x": 156, "y": 562, "width": 31, "height": 23}}}, "kerningDict": {}}]], 0, 0, [0], [0], [0]], [[{"name": "Font-export", "rect": [0, 0, 586, 586], "offset": [0, 0], "originalSize": [586, 586], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]]]]