[1, ["9awKuJe0ZAdYuaoLZYOaoE", "30B8EnmAhDhLvCN0ZgFPB+", "c2y1iy2ndM4bznX6DeLCXn", "20Lx8fkttP3LAsaq8i+QcY", "5b4hxm6C5Ac4ld/9Eul9gs", "5dsIbAWRxKLp5H1jMaR4Q8", "29BWTcHGhFrbTgI6KErCc/", "46CqH2yMFIS58lWEg++Z8b", "41eaKXH4ZP9rfp4B1CC1U7", "a3dONXYTxJlLUKH3OVShu8", "7e5Vd+AgNHI4kVs9cX5Ie1", "b2nGL4baNL9q086seD0PG6", "a9VpD0DP5LJYQPXITZq+uj", "19sdEij6JGJZkTbl/z3AH2", "a911dPIMpLobb8/U+XIQ+A", "62PalroPdDU6npLsAvqydr", "1ewN07T/1NILwQPmAp2HOd", "2eQ9UTZHtPoreQUCmClqDV", "072xiD7jJOjaYjO+ccfHNc", "ed3GRVC65MHLDn2FppzNLV", "18obhJevpCdp7jMA6C5h2U", "9e+P7XlIpLsKG4N46G1+PJ", "a3tNFU+qRJRathKRC2MSGp"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "_defaultClip", "_N$normalSprite", "root", "btnContinue", "btnClose", "lbiX2", "lbiLastWin", "data", "_parent", "sfResultPicked", "sfResultMiss"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs"], 1, 9, 4, 5, 1, 2, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_color"], 2, 1, 12, 4, 5, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_spriteFrame"], 1, 1, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "_N$enableAutoGrayEffect", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 0, 1, 9, 5, 5, 1, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_spacingX", "_enableWrapText", "_N$horizontalAlign", "_N$verticalAlign", "node", "_N$file"], -5, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["6a218DlE3xKlahML33v0tzu", ["node", "lbiLastWin", "lbiX2", "btnClose", "btnContinue", "btnPicks", "sfResultPicked", "sfResultMiss"], 3, 1, 1, 1, 1, 1, 2, 6, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["47b4eqoA/hFw4ocENL+7owM", ["node"], 3, 1], ["d857dgwT55H868B0MQw1E2w", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1]], [[9, 0, 1, 2], [3, 0, 1, 2, 3, 3], [1, 0, 5, 6, 2, 3, 4, 7, 2], [3, 2, 3, 1], [1, 0, 5, 2, 3, 4, 2], [2, 0, 1, 6, 2, 3, 4, 5, 2], [10, 0, 1], [14, 0, 1, 2, 1], [4, 0, 1, 3, 4, 5, 6, 7, 3], [6, 0, 1, 2, 3, 4], [2, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 7, 4, 5, 2], [5, 0, 1, 2, 5, 3, 6, 7, 8, 9, 8], [4, 0, 2, 1, 3, 4, 5, 6, 7, 4], [6, 0, 1, 3, 3], [7, 0, 2], [1, 0, 6, 2, 3, 4, 2], [1, 0, 2, 3, 4, 7, 2], [1, 0, 1, 5, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 6, 7, 1], [5, 0, 1, 2, 3, 4, 8, 9, 6], [11, 0, 1], [12, 0, 1], [13, 0, 1, 2, 3, 4, 5, 5], [3, 0, 2, 3, 2], [4, 3, 8, 1], [15, 0, 1]], [[[{"name": "btn_chơi", "rect": [1, 0, 146, 156], "offset": [0, 0.5], "originalSize": [148, 157], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [6]], [[{"name": "bg", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [7]], [[{"name": "sheld", "rect": [0, 0, 700, 180], "offset": [0, 0], "originalSize": [700, 180], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [8]], [[{"name": "thung", "rect": [0, 3, 194, 238], "offset": [0, 2.5], "originalSize": [194, 249], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [9]], [[{"name": "btn_dung", "rect": [1, 0, 146, 156], "offset": [0, 0.5], "originalSize": [148, 157], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [10]], [[{"name": "bom", "rect": [0, 0, 88, 120], "offset": [0, 0], "originalSize": [88, 120], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [11]], [[[15, "cbX2GameView"], [16, "cbX2GameView", [-10, -11, -12, -13, -14, -15, -16], [[19, -9, -8, -7, -6, -5, [-2, -3, -4], 24, 25]], [0, "af/NvnMZpBp5jm5t/OhwxP", -1], [5, 1280, 720]], [17, "lbBalance", [[20, "0", 21, 70, false, 2, -17, 2], [6, -18], [21, -19], [22, -20]], [0, "30nP5PDRdFdKUD9VckL6+a", 1], [5, 30.19, 45.94], [0, -22, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "layout", 1, [-22, -23, -24], [[23, 1, 1, 70, 20, -21, [5, 722, 250]]], [0, "3egqFujgdA5KF4X/i6HsgO", 1], [5, 722, 250], [0, -86, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "button", 3, [-27], [[[1, 2, false, -25, 8], -26], 4, 1], [0, "c3k9K2iFlI3Jo/IE21lF/h", 1], [5, 194, 249], [-264, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "button copy", 3, [-30], [[[1, 2, false, -28, 13], -29], 4, 1], [0, "5deX4uLMxPGIdUFCtcB9yh", 1], [5, 194, 249], [0, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "button copy", 3, [-33], [[[1, 2, false, -31, 18], -32], 4, 1], [0, "feqtVyJDNCM5DnBaENOsYb", 1], [5, 194, 249], [264, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "result", 4, [-36], [[1, 2, false, -34, 5], [7, -35, [7], 6]], [0, "79cF4tiUJDCZfq502wdw6m", 1], [5, 88, 120], [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "result", 5, [-39], [[1, 2, false, -37, 10], [7, -38, [12], 11]], [0, "68PMkYSytEVb7AflqtGG0o", 1], [5, 88, 120], [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "result", 6, [-42], [[1, 2, false, -40, 15], [7, -41, [17], 16]], [0, "344NsMRzBJcp51hYQJpIVa", 1], [5, 88, 120], [0, 151, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "sheld", 1, [-44, -45], [[3, -43, 21]], [0, "eagEzjUJpAm5w4RudTbQep", 1], [5, 700, 180], [10, -270, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnStop", 1, [[[1, 2, false, -46, 22], -47], 4, 1], [0, "59cwnW/xJOZZqOKvOhMPrd", 1], [5, 148, 157], [-436, -263, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnQuickPlay", 1, [[[1, 2, false, -48, 23], -49], 4, 1], [0, "faAQjtoJNCpr68f0hbNE4w", 1], [5, 148, 157], [446, -262, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "black", 1, 1, [[24, 0, -50, 0], [25, -51, [4, 4292269782]]], [0, "8boV/cHa1F+7naKgg3D87A", 1], [5, 3000, 3000]], [4, "bg", 1, [[3, -52, 1], [26, -53]], [0, "e3+OFglB5I9Ka67U4KIpma", 1], [5, 1280, 720]], [2, "Title_x2", 1, [2], [[3, -54, 3]], [0, "e5z9wl/+RNh5s4zOgoi1nY", 1], [5, 355, 142], [0, 284, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbWin", 10, [[[12, "0", 36, 36, false, false, 1, 1, -55, 19], -56], 4, 1], [0, "f325LqWJBHVIT68U0WOyHM", 1], [4, 4290495701], [5, 20.97, 36], [-221, -36, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbX2", 10, [[[12, "0", 36, 36, false, false, 1, 1, -57, 20], -58], 4, 1], [0, "44c9fmbLdCnZOXb4hkQz3A", 1], [4, 4290495701], [5, 20.97, 36], [216, -36, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "x2", 7, [[3, -59, 4]], [0, "35D0ebyX1KXYJcC/sleB3N", 1], [5, 98, 70]], [8, 1.05, 3, 4, [[9, "6a218DlE3xKlahML33v0tzu", "pickClicked", "0", 1]], [4, 4294967295], [4, 4294967295], 4], [4, "x2", 8, [[3, -60, 9]], [0, "257h/uxwVCspow5HN3Grhg", 1], [5, 98, 70]], [8, 1.05, 3, 5, [[9, "6a218DlE3xKlahML33v0tzu", "pickClicked", "1", 1]], [4, 4294967295], [4, 4294967295], 5], [4, "x2", 9, [[3, -61, 14]], [0, "0fM/VBvshOq4cCYndL2RI5", 1], [5, 98, 70]], [8, 1.05, 3, 6, [[9, "6a218DlE3xKlahML33v0tzu", "pickClicked", "2", 1]], [4, 4294967295], [4, 4294967295], 6], [6, 16], [6, 17], [13, 0.9, true, 3, 11, [[14, "6a218DlE3xKlahML33v0tzu", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], 11], [13, 0.9, true, 3, 12, [[14, "6a218DlE3xKlahML33v0tzu", "continueClicked", 1]], [4, 4294967295], [4, 4294967295], 12]], 0, [0, 6, 1, 0, -1, 19, 0, -2, 21, 0, -3, 23, 0, 7, 27, 0, 8, 26, 0, 9, 25, 0, 10, 24, 0, 0, 1, 0, -1, 13, 0, -2, 14, 0, -3, 15, 0, -4, 3, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, 0, 4, 0, -2, 19, 0, -1, 7, 0, 0, 5, 0, -2, 21, 0, -1, 8, 0, 0, 6, 0, -2, 23, 0, -1, 9, 0, 0, 7, 0, 0, 7, 0, -1, 18, 0, 0, 8, 0, 0, 8, 0, -1, 20, 0, 0, 9, 0, 0, 9, 0, -1, 22, 0, 0, 10, 0, -1, 16, 0, -2, 17, 0, 0, 11, 0, -2, 26, 0, 0, 12, 0, -2, 27, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, -2, 24, 0, 0, 17, 0, -2, 25, 0, 0, 18, 0, 0, 20, 0, 0, 22, 0, 11, 1, 2, 12, 15, 61], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 21, 23], [1, 1, 3, 1, 1, 1, 4, -1, 1, 1, 1, 4, -1, 1, 1, 1, 4, -1, 1, 3, 3, 1, 1, 1, 13, 14, 5, 5, 5], [12, 13, 14, 15, 2, 1, 0, 0, 3, 2, 1, 0, 0, 3, 2, 1, 0, 0, 3, 5, 5, 16, 17, 18, 19, 1, 4, 4, 4]], [[{"name": "Title_x2", "rect": [0, 0, 354, 142], "offset": [-0.5, 0], "originalSize": [355, 142], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [20]], [[{"name": "x2", "rect": [0, 0, 98, 70], "offset": [0, 0], "originalSize": [98, 70], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [21]], [[{"name": "fx", "rect": [4, 3, 352, 263], "offset": [2, -1.5], "originalSize": [356, 266], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [22]]]]