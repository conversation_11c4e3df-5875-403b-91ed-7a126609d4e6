[1, ["ecpdLyjvZBwrvm+cedCcQy", "cee4idYE5Do6G4/8TuO0wI", "5b34qajzVN2qg5Xlrq/tD/", "017Jn3Zv1Ft7hygdjpaSoK", "52/DnpWZ5HcaojGJ0XF9tj", "e2xSxof3xCnIoaUKvS6jbh", "2fgX2IKNJKn6CAfXOA/lGV", "16qkgkWLhFop/AtltIhLsG", "09OY9g2PhGgrx/xlk2nfMg", "b9niFylbhKnYQbHWywpWj6", "39H+s3tRdCEbSOJ16AJJ4f", "abfudYt6ZJ6rxHlVLRlIpZ", "a9VpD0DP5LJYQPXITZq+uj", "dfQo5p6SVOhImfY9X1/rY7", "27hqkVyjZCTbSHGNw8t10L", "6dLfwxYIRO4bY7xceFzLOF", "55mPnss5tIDbuWkBAOhSNQ", "8592FjhdxLxpGbbmafr/g8", "11d/8yZjZJMKnZFyxuN9gz", "ceA/axcHlA/r0iLXRAIjR+", "5e+wb5QOFNML3qwBEtYvOL", "bfws0w5+9EeYfI++woq+Hd", "7a/QZLET9IDreTiBfRn2PD", "48zC+EdUNCPbJDOCAb7BLt", "2dlD+Py99GMJUKNgyvmcdp", "fdNoodJKVLj4dF1TLppv2g", "adw94Z+hpN57wutNivq8Q5", "fejefp3nBBkqMgDkJLN18X", "666PEUMb9CH7cdf1ir8SMc", "dduOXAqPtEOZlLjBszo4Aq", "61EjIZbuhCKYjy9Tksf+TI", "9fAU1h4RRFtLazpYTFVeHC", "a2igG1YptHs5ZbTy5qSy7X", "ffT852tP1HEKBgTURfbnmx", "c6fla9nqJIrq6znt/jBHFf", "23PB9HLbJCu4q9vy9jvsOO", "cf01jUdddC1o54OxmxNML9", "5e0whbWz1CpoVcJhIkOlaB", "e1jcByDN1CxaS+hXS46HOw", "ddkZq5+oROabUi9Pd8hKgq", "33/FRxjr9Cl4kVHg82Gme3", "e1YfJ1yv9NX4kuVnHdeKPL", "44o1g1FB1IuIRL75YKJK4i", "09iakVrJVHF40qqzmz4teA", "6448AhxfpMQYYkmEe5cdTD", "5as6cPpbZJWYVwXmL20/Cq", "1cpQrJ2/1LHLZQtanVHhMs", "9eK+84OVVJeZVEhwKSiaGy", "69/0P/L2tBDrLXkOJmeEYH", "6cI0f7lXBFpb2sSIKUzTtQ", "ccKL7TXD9BBqbbYYU9PN5X", "d8K9BuleJFiLrdaxRFWQ5L", "e3Tv9xdgxI7ZoSwSNJLjZq", "12cPOCAIdHJY3aY+76ojgU", "0aXAuciR5NBKTqKVUlx9XA", "4ebDRhVh5NWIcV4CXKdy8I", "8aKeCKJvFK95Qz7ZtdrexS", "4crKH31hpH7KHVDqNMPJgH", "28Cc1cCcZOaqwCB7zjPtNc", "99Tm/wEUtOq69zsYudgteE", "6e4V2043lA0JoBaxoZjN3F", "f7r0j7jDFKgbILXLvKxUOv", "07V/js+odOXKseAMqRv48a", "1erTgiTQ5Ob4XEasEgcjG/", "dcACjU0slAxpppuYHVhkfx", "ccOJlCrchGcqw5pyTKQBDC", "d3WF6oOtxFF7aJ7ZMfX9LD", "23Odklq7VOXaF//xqAH9Ic", "2cI3YSoMtH8Jl9ZyFumvX7", "ccd1E2Dv9N675CJoWwDERo", "01E+wfmClOsYV0LOyvHort", "00TUmGnwxO4LCjaH9wTcfY", "03TMqp+IdMhrGUFdwU/ofS", "40TeIhb1BEDLB7dwLS5t91", "75VUgdrQJOjaofwaH412r6", "f58DFXnONH9KlP+54+1kpf", "c9k4JW705FBpNw3Tv8lTOa", "76fNNleNtIWbQskS5wVBM/", "51nwLqorFP16rNoYa6T5O/", "bfYOGHyLBIQZ88aPUcW40F", "60i1hgYsNMpLapbETES3Sl", "a9Z6PjFIpGOK21hJdG5iCC", "98R9YhzctB1aInFJ50vMnv", "99ZjG2UE9AqYyEvWJzPJM/", "04Av3VXFtHXordj9C3rjOy", "2cWB/vWPRHja3uQTinHH30", "9fsZYWMbhPDbl0gJfQDbz6", "eeGpL/QIZE4Kqhy65qdOra", "72faeWHsRKw6VbuFlr3BKj", "2eHnjxB9VAaYQR28+5AF/C", "4cBE186hZGQL5lW81/6nPY", "3fil29RtFJYoO8sR2395ui", "bd+d6w5TtAMZ1AekuStf3m", "7daD2RAvNJhpw/E/YOCH/Q", "31oYtbcSNJLrT4TsI5RK5i", "7eWP6788RO2Y9snPw/Lhfr", "1eHbidsOxKVZ6Dh6HYMs+v", "71WoB30WRPEJoAiPqv1xCl", "c37OpLUqdDPb20i7rkMisz", "823AkgqbNMgZe4i4GjD5ZL", "cd36p8PEtISZhqp8krREEf", "f4S4i8O1RNW7sL0mgtYQp9", "f5EDvjhR9D6JEUO7XFUgpA", "a7ZIP+aGpJdJPFaAE8axIJ"], ["node", "_spriteFrame", "_N$file", "_defaultClip", "_parent", "_N$target", "_file", "_N$skeletonData", "_textureSetter", "spriteFrame", "root", "nodeScale", "btnScale", "btnSelectLines", "btnSpin", "btnAutoSpin", "btnFastSpin", "btn10000", "btn5000", "btn1000", "btn100", "seven77Image", "lbTotalLines", "lbSessionID", "target", "lbiNormalWin", "lbiBigWin", "lbiJackpotWin", "particleNormalWin", "particleBigWin", "particleJackpot", "nodeNormalWin", "spriteXHu", "lbiJackpot", "lbFreeSpin", "data", "prefabBetLines", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_anchorPoint", "_color"], 0, 9, 4, 5, 2, 1, 7, 5, 5], ["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_trs", "_parent", "_components", "_children", "_anchorPoint"], 1, 4, 5, 7, 1, 12, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_enabled", "node", "_spriteFrame", "_materials"], 0, 1, 6, 3], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_string", "_lineHeight", "_N$horizontalAlign", "_N$overflow", "_enableWrapText", "_spacingX", "_enabled", "node", "_materials", "_N$file"], -7, 1, 3, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "_N$enableAutoGrayEffect", "node", "clickEvents", "_N$target", "_N$pressedColor", "_N$disabledColor", "_N$normalColor"], 0, 1, 9, 1, 5, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_trs", "_contentSize", "_anchorPoint", "_color"], 2, 1, 2, 4, 7, 5, 5, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_N$paddingTop", "_N$paddingBottom", "_enabled", "_N$spacingX", "node", "_layoutSize"], -4, 1, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.ParticleSystem", ["emissionRate", "life", "lifeVar", "angleVar", "startSize", "endSize", "startSpinVar", "endSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccelVar", "_custom", "totalParticles", "duration", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity"], -13, 1, 3, 8, 8, 8, 8, 5, 5], "cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "speed", "wrapMode", "events", "curveData"], -3], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0], ["cc.Prefab", ["_name"], 2], ["f74d33qIsFLnqhF+m+kFzXV", ["node"], 3, 1], ["8457bzXi+RCFrWEyccPy/PF", ["_enabled", "touchParent", "node"], 1, 1], ["12e663XDxFDpa0pUqXSDybJ", ["node", "nodeScale"], 3, 1, 1], ["06c65drxJVBh4ZpP6QSPpC5", ["node", "seven77Image", "btn100", "btn1000", "btn5000", "btn10000", "btnFastSpin", "btnAutoSpin", "btnSpin", "btnSelectLines", "btnScale", "spriteScaleIcon"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], ["7982e/VnphPwLLDmzu5GQwy", ["node", "spriteIcons"], 3, 1, 3], ["c475c5xRx5H4opGuVyHeh4H", ["node", "prefabBetLines", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail"], 3, 1, 6, 6, 6, 6, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["33a0eluM/lEyIXB0s4cQkT5", ["node", "nodeLines"], 3, 1, 2], ["93474vzV6hKp6SXG2oP4rCl", ["node", "seven77SpinColumnViews", "lbSessionID", "lbTotalLines"], 3, 1, 2, 1, 1], ["89dcc+VbqtD+qT4A9ri2qvX", ["node", "nodeJackpot", "nodeBigWin", "nodeNormalWin", "particleJackpot", "particleBigWin", "particleNormalWin", "lbiJackpotWin", "lbiBigWin", "lbiNormalWin"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["5c1fa7YnkBEMqpU9n30ksJn", ["gameId", "node", "nodeX", "spriteXHu", "lbRemainJackpots"], 2, 1, 1, 1, 2], ["16440wNqsRBr5tuG4ljvj+C", ["node", "lbiJackpot"], 3, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["860a7tSvnpF8L9qU6baMpvw", ["node", "lbFreeSpin"], 3, 1, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["e18e3vBV+RCUKwxLehv3E94", ["node", "spriteIcons"], 3, 1, 2], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["3182coZiuxBF5lAr+jPTUf9", ["node", "sfChips", "sfFastSpins", "sfAutoSpins", "sfSpins"], 3, 1, 3, 3, 3, 3]], [[20, 0, 1, 2], [2, 0, 1, 3, 5, 4, 3], [0, 0, 7, 3, 4, 5, 8, 2], [2, 3, 5, 4, 1], [0, 0, 1, 7, 3, 4, 5, 8, 3], [0, 0, 7, 6, 3, 4, 5, 8, 2], [5, 0, 1, 2, 3, 5, 4, 2], [7, 1, 2, 3, 1], [8, 0, 1, 3, 3], [2, 3, 5, 1], [4, 0, 1, 3, 4, 6, 7, 5, 3], [1, 0, 5, 6, 2, 3, 4, 2], [28, 0, 1], [7, 0, 1, 2, 3, 2], [3, 3, 0, 4, 1, 5, 2, 10, 7], [11, 0, 1, 2, 3, 4, 5, 7], [0, 0, 7, 6, 3, 4, 10, 5, 9, 8, 2], [5, 0, 1, 2, 3, 5, 6, 4, 2], [6, 0, 1, 2, 7, 8, 4], [8, 0, 1, 2, 3, 4], [2, 0, 3, 4, 2], [3, 3, 0, 4, 1, 5, 2, 10, 12, 7], [3, 3, 0, 4, 1, 5, 2, 10, 11, 12, 7], [3, 3, 0, 1, 5, 2, 10, 11, 12, 6], [1, 0, 7, 6, 2, 3, 4, 2], [1, 0, 5, 7, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 2], [29, 0, 1, 1], [1, 0, 5, 6, 2, 3, 8, 4, 2], [0, 0, 7, 6, 3, 4, 5, 9, 8, 2], [0, 0, 1, 6, 3, 4, 8, 3], [0, 0, 1, 7, 6, 3, 4, 5, 8, 3], [0, 0, 7, 3, 4, 5, 2], [0, 0, 7, 3, 4, 8, 2], [0, 0, 1, 2, 7, 3, 4, 5, 4], [4, 3, 4, 8, 1], [9, 14, 15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 17], [30, 0, 1, 2, 3, 4, 5, 6, 5], [12, 0, 1, 2, 4], [13, 0, 2], [1, 0, 7, 6, 2, 3, 8, 4, 2], [1, 0, 5, 7, 6, 2, 3, 4, 2], [1, 0, 5, 7, 2, 4, 2], [1, 0, 1, 5, 7, 2, 3, 4, 3], [1, 0, 1, 5, 6, 2, 3, 4, 3], [1, 0, 5, 6, 2, 3, 2], [0, 0, 6, 3, 4, 8, 2], [0, 0, 6, 3, 4, 5, 2], [0, 0, 1, 6, 3, 4, 5, 9, 3], [0, 0, 6, 3, 4, 2], [0, 0, 1, 7, 6, 3, 4, 8, 3], [0, 0, 6, 3, 4, 5, 8, 2], [0, 0, 7, 6, 3, 4, 5, 2], [0, 0, 2, 7, 3, 4, 5, 3], [5, 0, 1, 2, 3, 7, 5, 6, 4, 2], [14, 0, 1], [15, 0, 1, 2, 3], [16, 0, 1, 1], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [18, 0, 1, 1], [19, 0, 1, 2, 3, 4, 5, 1], [21, 0, 1, 1], [22, 0, 1, 2, 3, 1], [6, 0, 1, 3, 4, 2, 7, 8, 6], [6, 5, 0, 1, 2, 7, 8, 5], [6, 0, 1, 6, 7, 8, 4], [4, 3, 4, 8, 5, 1], [4, 3, 4, 6, 7, 5, 1], [4, 0, 2, 1, 3, 4, 6, 7, 5, 4], [2, 2, 0, 1, 3, 5, 4, 4], [2, 3, 4, 1], [2, 0, 3, 5, 4, 2], [2, 0, 1, 3, 3], [23, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [24, 0, 1, 2, 3, 4, 2], [25, 0, 1, 1], [3, 0, 4, 1, 8, 2, 6, 10, 11, 12, 7], [3, 3, 0, 4, 7, 1, 5, 2, 10, 11, 8], [3, 0, 4, 7, 1, 2, 6, 10, 11, 7], [3, 9, 3, 0, 1, 5, 2, 10, 11, 12, 7], [26, 0, 1, 2, 2], [27, 0, 1, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 23, 15], [31, 0, 1, 2, 3, 4, 1]], [[[[15, "columnStop", 2.4166666666666665, 4, "0", [{"frame": 2.4166666666666665, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 190}, {"frame": 1.3333333333333333, "value": -190}, {"frame": 1.35, "value": 190}, {"frame": 1.8333333333333333, "value": 130}, {"frame": 2.4166666666666665, "value": 190}], "active": [{"frame": 0, "value": true}, {"frame": 1.3333333333333333, "value": false}, {"frame": 1.35, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 95}, {"frame": 1, "value": -190}, {"frame": 1.0166666666666666, "value": 190}, {"frame": 1.35, "value": 95}, {"frame": 1.8333333333333333, "value": 35}, {"frame": 2.4166666666666665, "value": 95}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, 0]}, {"frame": 0.6666666666666666, "value": [0, -190]}, {"frame": 0.6833333333333333, "value": [0, 190]}, {"frame": 1.35, "value": [0, 0]}, {"frame": 1.8333333333333333, "value": [0, -60]}, {"frame": 2.4166666666666665, "value": [0, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -95]}, {"frame": 0.3333333333333333, "value": [0, -190]}, {"frame": 0.35, "value": [0, 190]}, {"frame": 1.35, "value": [0, -95]}, {"frame": 1.8333333333333333, "value": [0, -155]}, {"frame": 2.4166666666666665, "value": [0, -95]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[[15, "columnSpin2", 0.6833333333333333, 8, 2, [{"frame": 0.16666666666666666, "func": "3", "params": []}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 190}, {"frame": 0.6666666666666666, "value": -190}, {"frame": 0.6833333333333333, "value": 190}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 95}, {"frame": 0.5, "value": -190}, {"frame": 0.5166666666666667, "value": 190}, {"frame": 0.6833333333333333, "value": 95}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -190}, {"frame": 0.35, "value": 190}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -95]}, {"frame": 0.16666666666666666, "value": [0, -190]}, {"frame": 0.18333333333333332, "value": [0, 190]}, {"frame": 0.6833333333333333, "value": [0, -95]}]}}}}]], 0, 0, [], [], []], [[[15, "columnSpin", 0.6833333333333333, 4, 2, [{"frame": 0.16666666666666666, "func": "randomIcon", "params": ["3"]}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 190}, {"frame": 0.6666666666666666, "value": -190}, {"frame": 0.6833333333333333, "value": 190}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 95}, {"frame": 0.5, "value": -190}, {"frame": 0.5166666666666667, "value": 190}, {"frame": 0.6833333333333333, "value": 95}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -190}, {"frame": 0.35, "value": 190}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -95]}, {"frame": 0.16666666666666666, "value": [0, -190]}, {"frame": 0.18333333333333332, "value": [0, 190]}, {"frame": 0.6833333333333333, "value": [0, -95]}]}}}}]], 0, 0, [], [], []], [[{"name": "gold-dis", "rect": [0, 0, 66, 63], "offset": [0, 0], "originalSize": [66, 63], "capInsets": [0, 0, 0, 0]}], [10], 0, [0], [8], [28]], [[[38, "Font_HelveticaNeue_Effect-export", 32, {"commonHeight": 39, "fontSize": 32, "atlasName": "Font_HelveticaNeue_Effect-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 160, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 12, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 410, "y": 84, "width": 11, "height": 27}}, "35": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 234, "y": 364, "width": 23, "height": 27}}, "36": {"xOffset": 0, "yOffset": 9, "xAdvance": 25, "rect": {"x": 209, "y": 0, "width": 24, "height": 33}}, "37": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 28, "width": 34, "height": 29}}, "38": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 154, "y": 274, "width": 27, "height": 27}}, "39": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 318, "y": 404, "width": 11, "height": 13}}, "40": {"xOffset": 0, "yOffset": 11, "xAdvance": 15, "rect": {"x": 394, "y": 52, "width": 14, "height": 34}}, "41": {"xOffset": 0, "yOffset": 11, "xAdvance": 15, "rect": {"x": 395, "y": 87, "width": 14, "height": 34}}, "42": {"xOffset": 0, "yOffset": 12, "xAdvance": 18, "rect": {"x": 183, "y": 247, "width": 17, "height": 14}}, "43": {"xOffset": 0, "yOffset": 18, "xAdvance": 25, "rect": {"x": 208, "y": 397, "width": 24, "height": 21}}, "44": {"xOffset": 0, "yOffset": 30, "xAdvance": 12, "rect": {"x": 53, "y": 240, "width": 11, "height": 13}}, "45": {"xOffset": 0, "yOffset": 24, "xAdvance": 18, "rect": {"x": 35, "y": 249, "width": 17, "height": 8}}, "46": {"xOffset": 0, "yOffset": 30, "xAdvance": 12, "rect": {"x": 306, "y": 413, "width": 11, "height": 9}}, "47": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 377, "y": 292, "width": 19, "height": 28}}, "48": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 259, "y": 232, "width": 23, "height": 27}}, "49": {"xOffset": 0, "yOffset": 12, "xAdvance": 17, "rect": {"x": 377, "y": 29, "width": 16, "height": 27}}, "50": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 171, "width": 23, "height": 27}}, "51": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 265, "width": 22, "height": 27}}, "52": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 98, "width": 23, "height": 27}}, "53": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 321, "width": 22, "height": 27}}, "54": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 362, "width": 23, "height": 27}}, "55": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 349, "width": 22, "height": 27}}, "56": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 300, "width": 23, "height": 27}}, "57": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 293, "width": 22, "height": 27}}, "58": {"xOffset": 0, "yOffset": 18, "xAdvance": 12, "rect": {"x": 397, "y": 327, "width": 11, "height": 21}}, "59": {"xOffset": 0, "yOffset": 18, "xAdvance": 12, "rect": {"x": 410, "y": 112, "width": 11, "height": 25}}, "63": {"xOffset": 0, "yOffset": 11, "xAdvance": 23, "rect": {"x": 354, "y": 57, "width": 22, "height": 28}}, "64": {"xOffset": 0, "yOffset": 11, "xAdvance": 31, "rect": {"x": 33, "y": 356, "width": 30, "height": 29}}, "65": {"xOffset": 0, "yOffset": 12, "xAdvance": 30, "rect": {"x": 65, "y": 114, "width": 29, "height": 27}}, "66": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 391, "width": 26, "height": 27}}, "67": {"xOffset": 0, "yOffset": 11, "xAdvance": 29, "rect": {"x": 125, "y": 100, "width": 28, "height": 28}}, "68": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 154, "y": 61, "width": 27, "height": 27}}, "69": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 271, "width": 23, "height": 27}}, "70": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 331, "y": 0, "width": 23, "height": 27}}, "71": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 95, "y": 324, "width": 29, "height": 28}}, "72": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 190, "width": 26, "height": 27}}, "73": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 387, "width": 10, "height": 27}}, "74": {"xOffset": 0, "yOffset": 12, "xAdvance": 22, "rect": {"x": 377, "y": 213, "width": 21, "height": 27}}, "75": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 155, "y": 236, "width": 27, "height": 27}}, "76": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 330, "y": 126, "width": 23, "height": 27}}, "77": {"xOffset": 0, "yOffset": 12, "xAdvance": 32, "rect": {"x": 33, "y": 328, "width": 31, "height": 27}}, "78": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 36, "width": 26, "height": 27}}, "79": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 94, "y": 393, "width": 29, "height": 28}}, "80": {"xOffset": 0, "yOffset": 12, "xAdvance": 26, "rect": {"x": 209, "y": 275, "width": 25, "height": 27}}, "81": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 64, "y": 356, "width": 29, "height": 30}}, "82": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 363, "width": 26, "height": 27}}, "83": {"xOffset": 0, "yOffset": 11, "xAdvance": 27, "rect": {"x": 183, "y": 218, "width": 26, "height": 28}}, "84": {"xOffset": 0, "yOffset": 12, "xAdvance": 26, "rect": {"x": 209, "y": 247, "width": 25, "height": 27}}, "85": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 162, "width": 26, "height": 27}}, "86": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 126, "y": 274, "width": 27, "height": 27}}, "87": {"xOffset": 0, "yOffset": 12, "xAdvance": 38, "rect": {"x": 0, "y": 0, "width": 37, "height": 27}}, "88": {"xOffset": 0, "yOffset": 12, "xAdvance": 28, "rect": {"x": 153, "y": 302, "width": 27, "height": 27}}, "89": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 125, "y": 72, "width": 28, "height": 27}}, "90": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 182, "y": 64, "width": 26, "height": 27}}, "91": {"xOffset": 0, "yOffset": 11, "xAdvance": 14, "rect": {"x": 399, "y": 152, "width": 13, "height": 34}}, "92": {"xOffset": 0, "yOffset": 11, "xAdvance": 20, "rect": {"x": 377, "y": 321, "width": 19, "height": 28}}, "93": {"xOffset": 0, "yOffset": 11, "xAdvance": 14, "rect": {"x": 396, "y": 384, "width": 13, "height": 34}}, "94": {"xOffset": 0, "yOffset": 12, "xAdvance": 25, "rect": {"x": 126, "y": 302, "width": 24, "height": 19}}, "95": {"xOffset": 0, "yOffset": 35, "xAdvance": 24, "rect": {"x": 33, "y": 387, "width": 23, "height": 8}}, "97": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 209, "y": 303, "width": 23, "height": 22}}, "98": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 258, "y": 390, "width": 23, "height": 27}}, "99": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 307, "y": 63, "width": 22, "height": 22}}, "100": {"xOffset": 0, "yOffset": 12, "xAdvance": 24, "rect": {"x": 307, "y": 0, "width": 23, "height": 27}}, "101": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 354, "y": 115, "width": 22, "height": 22}}, "102": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 377, "y": 92, "width": 16, "height": 28}}, "103": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 354, "y": 86, "width": 22, "height": 28}}, "104": {"xOffset": 0, "yOffset": 12, "xAdvance": 22, "rect": {"x": 377, "y": 241, "width": 21, "height": 27}}, "105": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 399, "y": 0, "width": 10, "height": 27}}, "106": {"xOffset": 0, "yOffset": 12, "xAdvance": 14, "rect": {"x": 396, "y": 350, "width": 13, "height": 33}}, "107": {"xOffset": 0, "yOffset": 12, "xAdvance": 23, "rect": {"x": 354, "y": 377, "width": 22, "height": 27}}, "108": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 359, "width": 10, "height": 27}}, "109": {"xOffset": 0, "yOffset": 17, "xAdvance": 32, "rect": {"x": 0, "y": 396, "width": 31, "height": 22}}, "110": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 241, "width": 21, "height": 22}}, "111": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 282, "y": 394, "width": 23, "height": 22}}, "112": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 307, "y": 213, "width": 23, "height": 28}}, "113": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 307, "y": 242, "width": 23, "height": 28}}, "114": {"xOffset": 0, "yOffset": 17, "xAdvance": 16, "rect": {"x": 394, "y": 29, "width": 15, "height": 22}}, "115": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 377, "y": 269, "width": 21, "height": 22}}, "116": {"xOffset": 0, "yOffset": 13, "xAdvance": 17, "rect": {"x": 378, "y": 121, "width": 16, "height": 26}}, "117": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 30, "width": 21, "height": 22}}, "118": {"xOffset": 0, "yOffset": 17, "xAdvance": 23, "rect": {"x": 355, "y": 138, "width": 22, "height": 22}}, "119": {"xOffset": 0, "yOffset": 17, "xAdvance": 32, "rect": {"x": 32, "y": 396, "width": 31, "height": 22}}, "120": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 283, "y": 35, "width": 23, "height": 22}}, "121": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 283, "y": 58, "width": 23, "height": 28}}, "122": {"xOffset": 0, "yOffset": 18, "xAdvance": 23, "rect": {"x": 355, "y": 161, "width": 22, "height": 21}}, "123": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 377, "y": 57, "width": 16, "height": 34}}, "124": {"xOffset": 0, "yOffset": 9, "xAdvance": 10, "rect": {"x": 411, "y": 284, "width": 9, "height": 36}}, "125": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 378, "y": 148, "width": 16, "height": 34}}, "192": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 35, "y": 135, "width": 29, "height": 34}}, "193": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 35, "y": 100, "width": 29, "height": 34}}, "194": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 0, "width": 29, "height": 34}}, "195": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 35, "y": 206, "width": 29, "height": 33}}, "200": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 0, "width": 24, "height": 34}}, "201": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 35, "width": 24, "height": 34}}, "202": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 209, "y": 169, "width": 24, "height": 34}}, "204": {"xOffset": 0, "yOffset": 5, "xAdvance": 14, "rect": {"x": 397, "y": 292, "width": 13, "height": 34}}, "205": {"xOffset": 0, "yOffset": 4, "xAdvance": 14, "rect": {"x": 399, "y": 217, "width": 13, "height": 35}}, "210": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 65, "y": 142, "width": 29, "height": 34}}, "211": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 96, "y": 287, "width": 29, "height": 34}}, "212": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 214, "width": 29, "height": 34}}, "213": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 95, "y": 147, "width": 29, "height": 33}}, "217": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 182, "y": 127, "width": 26, "height": 34}}, "218": {"xOffset": 0, "yOffset": 5, "xAdvance": 27, "rect": {"x": 182, "y": 92, "width": 26, "height": 34}}, "221": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 125, "y": 165, "width": 28, "height": 34}}, "224": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 173, "width": 23, "height": 29}}, "225": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 334, "width": 23, "height": 29}}, "226": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 234, "y": 392, "width": 23, "height": 29}}, "227": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 235, "y": 271, "width": 23, "height": 28}}, "232": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 259, "y": 260, "width": 23, "height": 29}}, "233": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 259, "y": 0, "width": 23, "height": 29}}, "234": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 282, "y": 117, "width": 23, "height": 29}}, "236": {"xOffset": 0, "yOffset": 10, "xAdvance": 14, "rect": {"x": 399, "y": 187, "width": 13, "height": 29}}, "237": {"xOffset": 0, "yOffset": 10, "xAdvance": 15, "rect": {"x": 395, "y": 122, "width": 14, "height": 29}}, "242": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 283, "y": 210, "width": 23, "height": 29}}, "243": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 283, "y": 180, "width": 23, "height": 29}}, "244": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 306, "y": 271, "width": 23, "height": 29}}, "245": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 306, "y": 87, "width": 23, "height": 28}}, "249": {"xOffset": 0, "yOffset": 10, "xAdvance": 22, "rect": {"x": 377, "y": 183, "width": 21, "height": 29}}, "250": {"xOffset": 0, "yOffset": 10, "xAdvance": 22, "rect": {"x": 355, "y": 0, "width": 21, "height": 29}}, "253": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 306, "y": 143, "width": 23, "height": 35}}, "258": {"xOffset": 0, "yOffset": 6, "xAdvance": 30, "rect": {"x": 64, "y": 387, "width": 29, "height": 33}}, "259": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 234, "y": 144, "width": 23, "height": 28}}, "272": {"xOffset": 0, "yOffset": 12, "xAdvance": 30, "rect": {"x": 65, "y": 328, "width": 29, "height": 27}}, "273": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 38, "y": 0, "width": 26, "height": 27}}, "296": {"xOffset": 0, "yOffset": 6, "xAdvance": 19, "rect": {"x": 377, "y": 350, "width": 18, "height": 33}}, "297": {"xOffset": 0, "yOffset": 11, "xAdvance": 19, "rect": {"x": 377, "y": 384, "width": 18, "height": 28}}, "360": {"xOffset": 0, "yOffset": 6, "xAdvance": 27, "rect": {"x": 182, "y": 264, "width": 26, "height": 33}}, "361": {"xOffset": 0, "yOffset": 11, "xAdvance": 22, "rect": {"x": 377, "y": 0, "width": 21, "height": 28}}, "416": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 58, "width": 34, "height": 28}}, "417": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 125, "y": 322, "width": 27, "height": 23}}, "431": {"xOffset": 0, "yOffset": 10, "xAdvance": 33, "rect": {"x": 0, "y": 260, "width": 32, "height": 29}}, "432": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 153, "y": 389, "width": 27, "height": 23}}, "7840": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 124, "y": 353, "width": 28, "height": 31}}, "7841": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 258, "y": 144, "width": 23, "height": 26}}, "7842": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 170, "width": 29, "height": 35}}, "7843": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 234, "y": 303, "width": 23, "height": 30}}, "7844": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 28, "width": 29, "height": 35}}, "7845": {"xOffset": 0, "yOffset": 7, "xAdvance": 25, "rect": {"x": 209, "y": 34, "width": 24, "height": 32}}, "7846": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 35, "y": 64, "width": 29, "height": 35}}, "7847": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 235, "y": 203, "width": 23, "height": 32}}, "7848": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 96, "y": 249, "width": 29, "height": 37}}, "7849": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 235, "y": 236, "width": 23, "height": 34}}, "7850": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 94, "y": 356, "width": 29, "height": 36}}, "7851": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 283, "y": 0, "width": 23, "height": 34}}, "7852": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 125, "y": 200, "width": 28, "height": 38}}, "7853": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 258, "y": 328, "width": 23, "height": 33}}, "7854": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 0, "width": 29, "height": 37}}, "7855": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 234, "y": 109, "width": 23, "height": 34}}, "7856": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 38, "width": 29, "height": 37}}, "7857": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 258, "y": 109, "width": 23, "height": 34}}, "7858": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 65, "y": 76, "width": 29, "height": 37}}, "7859": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 330, "y": 63, "width": 23, "height": 34}}, "7860": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 95, "y": 74, "width": 29, "height": 36}}, "7861": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 307, "y": 28, "width": 23, "height": 34}}, "7862": {"xOffset": 0, "yOffset": 6, "xAdvance": 29, "rect": {"x": 125, "y": 34, "width": 28, "height": 37}}, "7863": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 259, "y": 199, "width": 23, "height": 32}}, "7864": {"xOffset": 0, "yOffset": 12, "xAdvance": 25, "rect": {"x": 209, "y": 137, "width": 24, "height": 31}}, "7865": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 282, "y": 90, "width": 23, "height": 26}}, "7866": {"xOffset": 0, "yOffset": 4, "xAdvance": 25, "rect": {"x": 209, "y": 67, "width": 24, "height": 35}}, "7867": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 259, "y": 30, "width": 23, "height": 30}}, "7868": {"xOffset": 0, "yOffset": 6, "xAdvance": 25, "rect": {"x": 209, "y": 103, "width": 24, "height": 33}}, "7869": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 259, "y": 61, "width": 23, "height": 28}}, "7870": {"xOffset": 0, "yOffset": 4, "xAdvance": 25, "rect": {"x": 210, "y": 204, "width": 24, "height": 35}}, "7871": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 208, "y": 331, "width": 25, "height": 32}}, "7872": {"xOffset": 0, "yOffset": 4, "xAdvance": 24, "rect": {"x": 331, "y": 191, "width": 23, "height": 35}}, "7873": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 282, "y": 147, "width": 23, "height": 32}}, "7874": {"xOffset": 0, "yOffset": 2, "xAdvance": 24, "rect": {"x": 331, "y": 227, "width": 23, "height": 37}}, "7875": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 282, "y": 290, "width": 23, "height": 34}}, "7876": {"xOffset": 0, "yOffset": 3, "xAdvance": 24, "rect": {"x": 331, "y": 154, "width": 23, "height": 36}}, "7877": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 282, "y": 325, "width": 23, "height": 34}}, "7878": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 234, "y": 70, "width": 24, "height": 38}}, "7879": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 282, "y": 360, "width": 23, "height": 33}}, "7880": {"xOffset": 0, "yOffset": 4, "xAdvance": 13, "rect": {"x": 410, "y": 0, "width": 12, "height": 35}}, "7881": {"xOffset": 0, "yOffset": 9, "xAdvance": 13, "rect": {"x": 399, "y": 253, "width": 12, "height": 30}}, "7882": {"xOffset": 0, "yOffset": 12, "xAdvance": 12, "rect": {"x": 409, "y": 52, "width": 11, "height": 31}}, "7883": {"xOffset": 0, "yOffset": 12, "xAdvance": 11, "rect": {"x": 410, "y": 327, "width": 10, "height": 31}}, "7884": {"xOffset": 0, "yOffset": 11, "xAdvance": 30, "rect": {"x": 95, "y": 181, "width": 29, "height": 32}}, "7885": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 306, "y": 116, "width": 23, "height": 26}}, "7886": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 95, "y": 111, "width": 29, "height": 35}}, "7887": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 283, "y": 240, "width": 23, "height": 30}}, "7888": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 65, "y": 177, "width": 29, "height": 35}}, "7889": {"xOffset": 0, "yOffset": 7, "xAdvance": 26, "rect": {"x": 208, "y": 364, "width": 25, "height": 32}}, "7890": {"xOffset": 0, "yOffset": 4, "xAdvance": 30, "rect": {"x": 65, "y": 213, "width": 29, "height": 35}}, "7891": {"xOffset": 0, "yOffset": 7, "xAdvance": 24, "rect": {"x": 306, "y": 301, "width": 23, "height": 32}}, "7892": {"xOffset": 0, "yOffset": 2, "xAdvance": 30, "rect": {"x": 66, "y": 249, "width": 29, "height": 37}}, "7893": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 306, "y": 334, "width": 23, "height": 34}}, "7894": {"xOffset": 0, "yOffset": 3, "xAdvance": 30, "rect": {"x": 66, "y": 287, "width": 29, "height": 36}}, "7895": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 306, "y": 369, "width": 23, "height": 34}}, "7896": {"xOffset": 0, "yOffset": 5, "xAdvance": 30, "rect": {"x": 95, "y": 35, "width": 29, "height": 38}}, "7897": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 307, "y": 179, "width": 23, "height": 33}}, "7898": {"xOffset": 0, "yOffset": 5, "xAdvance": 35, "rect": {"x": 0, "y": 122, "width": 34, "height": 34}}, "7899": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 146, "width": 27, "height": 29}}, "7900": {"xOffset": 0, "yOffset": 5, "xAdvance": 35, "rect": {"x": 0, "y": 87, "width": 34, "height": 34}}, "7901": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 0, "width": 27, "height": 29}}, "7902": {"xOffset": 0, "yOffset": 4, "xAdvance": 35, "rect": {"x": 0, "y": 224, "width": 34, "height": 35}}, "7903": {"xOffset": 0, "yOffset": 9, "xAdvance": 28, "rect": {"x": 154, "y": 30, "width": 27, "height": 30}}, "7904": {"xOffset": 0, "yOffset": 6, "xAdvance": 35, "rect": {"x": 0, "y": 157, "width": 34, "height": 33}}, "7905": {"xOffset": 0, "yOffset": 11, "xAdvance": 28, "rect": {"x": 154, "y": 89, "width": 27, "height": 28}}, "7906": {"xOffset": 0, "yOffset": 11, "xAdvance": 35, "rect": {"x": 0, "y": 191, "width": 34, "height": 32}}, "7907": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 154, "y": 118, "width": 27, "height": 27}}, "7908": {"xOffset": 0, "yOffset": 12, "xAdvance": 27, "rect": {"x": 181, "y": 331, "width": 26, "height": 31}}, "7909": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 355, "y": 214, "width": 21, "height": 26}}, "7910": {"xOffset": 0, "yOffset": 4, "xAdvance": 27, "rect": {"x": 182, "y": 0, "width": 26, "height": 35}}, "7911": {"xOffset": 0, "yOffset": 9, "xAdvance": 22, "rect": {"x": 355, "y": 183, "width": 21, "height": 30}}, "7912": {"xOffset": 0, "yOffset": 5, "xAdvance": 33, "rect": {"x": 0, "y": 290, "width": 32, "height": 34}}, "7913": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 176, "width": 27, "height": 29}}, "7914": {"xOffset": 0, "yOffset": 5, "xAdvance": 33, "rect": {"x": 0, "y": 325, "width": 32, "height": 34}}, "7915": {"xOffset": 0, "yOffset": 10, "xAdvance": 28, "rect": {"x": 154, "y": 206, "width": 27, "height": 29}}, "7916": {"xOffset": 0, "yOffset": 4, "xAdvance": 33, "rect": {"x": 0, "y": 360, "width": 32, "height": 35}}, "7917": {"xOffset": 0, "yOffset": 9, "xAdvance": 28, "rect": {"x": 153, "y": 330, "width": 27, "height": 30}}, "7918": {"xOffset": 0, "yOffset": 6, "xAdvance": 33, "rect": {"x": 33, "y": 260, "width": 32, "height": 33}}, "7919": {"xOffset": 0, "yOffset": 11, "xAdvance": 28, "rect": {"x": 181, "y": 302, "width": 27, "height": 28}}, "7920": {"xOffset": 0, "yOffset": 10, "xAdvance": 33, "rect": {"x": 33, "y": 294, "width": 32, "height": 33}}, "7921": {"xOffset": 0, "yOffset": 16, "xAdvance": 28, "rect": {"x": 153, "y": 361, "width": 27, "height": 27}}, "7922": {"xOffset": 0, "yOffset": 5, "xAdvance": 29, "rect": {"x": 126, "y": 239, "width": 28, "height": 34}}, "7923": {"xOffset": 0, "yOffset": 10, "xAdvance": 24, "rect": {"x": 330, "y": 299, "width": 23, "height": 35}}, "7924": {"xOffset": 0, "yOffset": 12, "xAdvance": 29, "rect": {"x": 124, "y": 385, "width": 28, "height": 31}}, "7925": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 331, "y": 28, "width": 23, "height": 28}}, "7926": {"xOffset": 0, "yOffset": 4, "xAdvance": 29, "rect": {"x": 125, "y": 129, "width": 28, "height": 35}}, "7927": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 330, "y": 335, "width": 23, "height": 36}}, "7928": {"xOffset": 0, "yOffset": 6, "xAdvance": 29, "rect": {"x": 125, "y": 0, "width": 28, "height": 33}}, "7929": {"xOffset": 0, "yOffset": 11, "xAdvance": 24, "rect": {"x": 330, "y": 372, "width": 23, "height": 34}}, "8221": {"xOffset": 0, "yOffset": 11, "xAdvance": 17, "rect": {"x": 259, "y": 90, "width": 16, "height": 14}}}, "kerningDict": {}}]], 0, 0, [0], [9], [29]], [[[39, "777<PERSON>iew"], [40, "777<PERSON>iew", [-21, -22], [[[7, -2, [210, 211], 209], [55, -3], [56, false, null, -4], [57, -6, -5], [58, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, [224, 225]], [59, -18, [226, 227, 228, 229, 230, 231]], -19, [60, -20, 232, 233, 234, 235, 236]], 4, 4, 4, 4, 4, 4, 1, 4], [0, "53f2op79ZJvqy9oBEpuPVD", -1], [5, 540, 450], [0, 0.5, 0.54], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "payLineView", [-44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61, -62, -63], [[61, -43, [-23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42]]], [0, "0aFSTvBI9K/6LNmO17Va0n", 1], [-75, -23, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "spinView", [-70, -71, -72, -73, -74, -75, -76, -77, 2, -78, -79, -80, -81], [[62, -69, [-66, -67, -68], -65, -64]], [0, "75a78PuQJDtZ6vC7azTWFA", 1], [5, 550, 308]], [5, "lineLeft", 3, [-83, -84, -85, -86, -87, -88, -89, -90, -91, -92], [[18, 1, 2, 8, -82, [5, 30, 332]]], [0, "eekG1Gl3pIi6dPXVZZeu8o", 1], [5, 30, 332], [-189, -51, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lineRight", 3, [-94, -95, -96, -97, -98, -99, -100, -101, -102, -103], [[18, 1, 2, 8, -93, [5, 30, 332]]], [0, "849Hf2eShGHrY1bxQjjMmj", 1], [5, 30, 332], [189, -51, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "offset-xHu", false, [-107, -108, -109], [[66, -106, [[8, "5c1fa7YnkBEMqpU9n30ksJn", "openEventClicked", -105]], [4, 4292269782], -104]], [0, "03HPTumNlE05kVu0qouc6Y", 1], [5, 190, 190], [0, 0.5, 0.9]], [29, "layout", 6, [-112, -113, -114, -115], [[20, 0, -110, 6], [63, 1, 2, 25, 25, 15, -111, [5, 190, 195]]], [0, "98Izjc85tBQqhogRKowfdd", 1], [5, 190, 195], [0, 0.5, 1], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnLeft", 3, [-117, -118, -119, -120, -121], [[18, 1, 2, 20, -116, [5, 150, 296]]], [0, "6bEiQ24lFOXqo4vYtpIcux", 1], [5, 150, 296], [-301, -54, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "1", [-124, -125, -126, -127], [[[7, -122, [86, 87, 88, 89], 85], -123], 4, 1], [0, "06imgarAlKf4GuRE63j4A+", 1], [5, 72, 300], [-94, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "2", [-130, -131, -132, -133], [[[7, -128, [101, 102, 103, 104], 100], -129], 4, 1], [0, "21pKrxkCxOGaqutedefLbp", 1], [5, 72, 300], [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "3", [-136, -137, -138, -139], [[[7, -134, [116, 117, 118, 119], 115], -135], 4, 1], [0, "1af5K/1T5Pr7F2ttG7yGQo", 1], [5, 72, 300], [94, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "offset-jackpot", false, [-141, -142, -143, -144], [[13, true, -140, [194], 193]], [0, "69wekR2KhPGLNTxosINO8R", 1], [0, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "offset-bigWin", false, [-146, -147, -148, -149], [[13, true, -145, [202], 201]], [0, "c3xgY5NLVG1KzaHGMCwlBD", 1], [0, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "btnBetLines", 8, [-152, -153], [[[3, -150, [55], 56], -151], 4, 1], [0, "21Yrd8KX1LgKNSyPePebPk", 1], [5, 145, 59], [0, 118.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "button", 3, [-155, -156, -157, -158], [[64, false, 1, 2, 30, -154, [5, 70, 341]]], [0, "5aGdFGXExJXbGdyIqoDHud", 1], [5, 70, 341], [-447, -27, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "effect<PERSON>iew", [12, 13, -167], [[73, -166, 12, 13, -165, -164, -163, -162, -161, -160, -159]], [0, "1ciYa6ey5P0oA0Td+4YMRB", 1]], [50, "offset-normalWin", false, 16, [-169, -170, -171], [[13, true, -168, [208], 207]], [0, "b6c1r+i69J1KmkEV3OdD4h", 1], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "xHuView", 1, [6], [[74, 7, -177, 6, -176, [-172, -173, -174, -175]]], [0, "66zhxAmpZBEL36os60lnmX", 1], [5, 190, 190], [0, 0.5, 0.7], [-383, 147, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "offset-scale", 1, [3, -178, 16], [0, "75bPqCCeRPOJkxe0olHgqo", 1], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [51, "slots", [9, 10, 11], [[65, 1, 1, 22, -179, [5, 260, 310]]], [0, "12c0LmzNRM5oDEVPLqhJV+", 1], [5, 260, 310], [0, 0, 0, 0, 0, 0, 1, 1.08, 1.08, 1]], [5, "btnRight", 3, [-181, -182, -183], [[18, 1, 2, 30, -180, [5, 150, 296]]], [0, "37yUr8kclOepPOYCxDsbLj", 1], [5, 150, 296], [304.4, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "lbChipValue", false, 19, [-184, -185, -186, -187], [0, "e6DU5MGLBGM7KwyoZZDxU5", 1], [5, 60, 139], [-302, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btn100", 8, [[[1, 2, false, -188, [57], 58], -189], 4, 1], [0, "3e3t6kwndGdLEWSasyK1Wb", 1], [5, 145, 59], [0, 39.5, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btn1000", 8, [[[1, 2, false, -190, [59], 60], -191], 4, 1], [0, "93OJghcutAUIsnLWYxGoLo", 1], [5, 145, 59], [0, -39.5, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "btn5000", false, 8, [[[69, false, 2, false, -192, [61], 62], -193], 4, 1], [0, "28TdYgsuBOJ57tRZ4TRy/o", 1], [5, 66, 63], [0, -43, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btn10000", 8, [[[1, 2, false, -194, [63], 64], -195], 4, 1], [0, "53tJ4XWaFOtaNsQ5dHqOa+", 1], [5, 145, 59], [0, -118.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 3, [[1, 2, false, -196, [65], 66], [10, 1.1, 3, -198, [[8, "12e663XDxFDpa0pUqXSDybJ", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -197]], [0, "bd+/G3i7JCb4hsbUGndycz", 1], [5, 73, 73], [391, 157, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnHelp", 15, [[1, 2, false, -199, [67], 68], [10, 1.1, 3, -201, [[8, "06c65drxJVBh4ZpP6QSPpC5", "helpClicked", 1]], [4, 4294967295], [4, 4294967295], -200]], [0, "1eexqsS/xKUKApau9vQBKR", 1], [5, 63, 63], [23, 139, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnTop", 15, [[1, 2, false, -202, [69], 70], [10, 1.1, 3, -204, [[8, "06c65drxJVBh4ZpP6QSPpC5", "topClicked", 1]], [4, 4294967295], [4, 4294967295], -203]], [0, "1e+piSLt5D2YUG0XDCAs7G", 1], [5, 63, 63], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnHistory", 15, [[1, 2, false, -205, [71], 72], [10, 1.1, 3, -207, [[8, "06c65drxJVBh4ZpP6QSPpC5", "historyClicked", 1]], [4, 4294967295], [4, 4294967295], -206]], [0, "24uRUmOk9K3ob4zmSuxS5V", 1], [5, 63, 63], [0, -47, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btnScale", 15, [[[1, 0, false, -208, [73], 74], -209], 4, 1], [0, "67wmCHomxOOpTw3xt1+BUb", 1], [5, 63, 62], [4, -139.5, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btnAutoSpin", 21, [[[1, 2, false, -210, [121], 122], -211], 4, 1], [0, "23P59JOD1IT7iJBn3BlrVH", 1], [5, 145, 78], [0, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btnSpin", 21, [[[1, 2, false, -212, [123], 124], -213], 4, 1], [0, "d78N6+6GlOZ7npaeE+20js", 1], [5, 145, 79], [0, 0.5, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btnFastSpin", 21, [[[1, 2, false, -214, [125], 126], -215], 4, 1], [0, "44mC9TPltOcLLcq6rPWwOB", 1], [5, 145, 79], [0, -108.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "jackpotView", 3, [-218, -219], [[75, -217, -216]], [0, "09sT9XCL1H/5aGASVMq97m", 1], [5, 210, 36], [0, -246.2, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "logo", 6, [[-220, [13, true, -221, [1], 0]], 1, 4], [0, "5fWCzpm6dPH43zcYVXgnuH", 1], [5, 90, 44], [0, 0.5, 1], [0, 58.1, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbRoom", 7, [-223], [[21, "100:", 20, 50, false, 1, 1, -222, 2]], [0, "a9ClYztUpOeb5RkzTBBen9", 1], [4, 4278255615], [5, 39.5, 25], [0, 0, 0.5], [-73.5, -37.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbRoom", 7, [-225], [[21, "1K:", 20, 50, false, 1, 1, -224, 3]], [0, "88IIkBBaNDdoMjNca5jiq2", 1], [4, 4278255615], [5, 29.5, 25], [0, 0, 0.5], [-73.5, -77.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbRoom", 7, [-227], [[21, "5K:", 20, 50, false, 1, 1, -226, 4]], [0, "51NebqB2lDNLU5yROwHLEx", 1], [4, 4278255615], [5, 29.5, 25], [0, 0, 0.5], [-73.5, -117.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbRoom", 7, [-229], [[21, "10K:", 20, 50, false, 1, 1, -228, 5]], [0, "3f+BUJ6sVD1JmquibKbDS5", 1], [4, 4278255615], [5, 41, 25], [0, 0, 0.5], [-73.5, -157.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "close", 6, [[20, 0, -230, 7], [35, -231, [[8, "5c1fa7YnkBEMqpU9n30ksJn", "onClickHide", 18]], [4, 4292269782]]], [0, "cdNSi7vo1HX5t4UMnAKuiL", 1], [5, 36, 36], [-88.2, 14.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "boder<PERSON><PERSON>", 3, [-233], [[3, -232, [10], 11]], [0, "d4WJgY+eFGPpnSnCVMk29S", 1], [5, 747, 488], [0, 0, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [5, "mask", 3, [20], [[80, 1, -234, [120]]], [0, "e9K7D8fzlF+7EdE3khIt5l", 1], [5, 310, 310], [0, -51, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot1", 9, [-236], [[7, -235, [78], 77]], [0, "ec7Ffe2kBF7b1gb2fvVH2y", 1], [5, 72, 96], [0, 95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot2", 9, [-238], [[7, -237, [81], 80]], [0, "3b1oQuSKdNTbHDcbkrQIEV", 1], [5, 72, 96], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot3", 9, [-240], [[7, -239, [84], 83]], [0, "5bMV5o9VtPYYxWgvZtOslm", 1], [5, 72, 96], [0, -95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot1", 10, [-242], [[7, -241, [93], 92]], [0, "f6jBUm5qVFg7O1nnXx9YFW", 1], [5, 72, 96], [0, 95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot2", 10, [-244], [[7, -243, [96], 95]], [0, "f5f+s9lc9LB5dR1WedfiIB", 1], [5, 72, 96], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot3", 10, [-246], [[7, -245, [99], 98]], [0, "47D3QY915D7Y5cbC+RfNUt", 1], [5, 72, 96], [0, -95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot1", 11, [-248], [[7, -247, [108], 107]], [0, "daMKmeYdhI8bsWg1JwB3lL", 1], [5, 72, 96], [0, 95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot2", 11, [-250], [[7, -249, [111], 110]], [0, "8dZ61zXZRJ5LCvFAXzf5pD", 1], [5, 72, 96], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [5, "slot3", 11, [-252], [[7, -251, [114], 113]], [0, "c4ocvKhxVP5q/sd4J8INb2", 1], [5, 72, 96], [0, -95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [4, "1", false, 2, [[1, 2, false, -253, [127], 128]], [0, "74gBqs9QtKs4kQtK+7IPwY", 1], [5, 261, 63], [44, 92, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "2", false, 2, [[1, 2, false, -254, [129], 130]], [0, "91nNLhNVVHDaAyjkGRtZ3z", 1], [5, 264, 119], [44, 32.7, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "3", false, 2, [[1, 2, false, -255, [131], 132]], [0, "e8JOKc/ItMhJut7wgApejQ", 1], [5, 268, 172], [44, -28.1, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "4", false, 2, [[1, 2, false, -256, [133], 134]], [0, "d1p1GRjH9KXZVJYNOmtZ8h", 1], [5, 260, 192], [44, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "5", false, 2, [[1, 2, false, -257, [135], 136]], [0, "87Qd6Y13RAxJ4GINPr4ahX", 1], [5, 264, 192], [44, -33, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "6", false, 2, [[1, 2, false, -258, [137], 138]], [0, "2bQ6F/ZclKDown+3j48sMS", 1], [5, 263, 112], [44, 2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "7", false, 2, [[1, 2, false, -259, [139], 140]], [0, "c6DIFDUCJDt7jUmPKhO7kC", 1], [5, 264, 193], [44, -42.9, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "8", false, 2, [[1, 2, false, -260, [141], 142]], [0, "8b6mumhbRB6rE42Km3ArLQ", 1], [5, 259, 191], [44, -44, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "9", false, 2, [[1, 2, false, -261, [143], 144]], [0, "1ahZ0qgctAO45LUh5+/CEZ", 1], [5, 260, 120], [44, -100.3, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "10", false, 2, [[1, 2, false, -262, [145], 146]], [0, "78U2q6jXBHea7ydYdSiEA9", 1], [5, 262, 232], [44, -62, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "11", false, 2, [[1, 2, false, -263, [147], 148]], [0, "d4Ncjag2ZMyaSvDucacCYN", 1], [5, 264, 236], [108, 2.4000000000000004, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "12", false, 2, [[1, 2, false, -264, [149], 150]], [0, "43wz+MrcdPWKILf0CQg5Ea", 1], [5, 266, 119], [103, 37.1, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "13", false, 2, [[1, 2, false, -265, [151], 152]], [0, "b2T4OkMmlC7LzaxMugORyT", 1], [5, 266, 173], [102.1, -29.6, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "14", false, 2, [[1, 2, false, -266, [153], 154]], [0, "4beZGJ5LVN+q1jhBEkI0CH", 1], [5, 263, 107], [103.4, 19.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "15", false, 2, [[1, 2, false, -267, [155], 156]], [0, "07tYHs4OdIALxA4iFTRQxs", 1], [5, 265, 121], [102.9, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "16", false, 2, [[1, 2, false, -268, [157], 158]], [0, "a6TVA+cxJLQ7dmsoYwsp87", 1], [5, 266, 114], [104, 13.3, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "17", false, 2, [[1, 2, false, -269, [159], 160]], [0, "b4ksj44zpBn7C035uiezue", 1], [5, 265, 104], [105.9, -79, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "18", false, 2, [[1, 2, false, -270, [161], 162]], [0, "144L4Aay5EMr534iyyucyt", 1], [5, 267, 174], [102.3, -20.6, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "19", false, 2, [[1, 2, false, -271, [163], 164]], [0, "88++drth9FIJQAaqgC6223", 1], [5, 265, 121], [103.3, -91.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "20", false, 2, [[1, 2, false, -272, [165], 166]], [0, "b0z3/D0qtOap90L+P7gZ40", 1], [5, 267, 231], [101.6, -58.9, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "freeSpinView", 3, [-275], [[81, -274, -273]], [0, "766wh2oFxAq6zZIQeSyX5x", 1], [5, 362, 44]], [31, "spriteBG", false, 73, [-277], [[70, -276, 167]], [0, "d7BlaPdYRHG6Ttmltp5S1y", 1], [5, 362, 44], [-69, -148, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "lbFreeSpin", 74, [[-278, [12, -279]], 1, 4], [0, "a8RlakiE5A4ar0pAUPZ15X", 1], [5, 196.3, 32.5]], [28, "lbJackpot", 35, [[[76, 22, 34, false, -2, 1, 2, -280, [171], 172], -281], 4, 1], [0, "cdTMdhjB9Lk66DJh566SF0", 1], [5, 220, 34], [0, 0, 0.5], [-84.446, 6.452, 0, 0, 0, 0, 1, 1.322, 1.322, 1.322]], [31, "bgWin", false, 3, [-283], [[13, true, -282, [176, 177], 175]], [0, "65D6DaBC1KPYBaGcKttNrQ", 1], [5, 362, 70], [0, -37, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "lbWin", 77, [[22, "100.000", 67, 64, false, 1, 1, -284, [173], 174], [12, -285]], [0, "9bPiSfIZ5OiZ5PPrYajPpA", 1], [5, 262, 64]], [53, "black", 1, 12, [[71, 0, -286, [186], 187], [35, -287, [[8, "89dcc+VbqtD+qT4A9ri2qvX", "continueClicked", 16]], [4, 4292269782]]], [0, "e1jsIpalxFY432hpZc4xzU", 1], [5, 3000, 3000]], [11, "lbWin", 12, [[[22, "100.000", 67, 64, false, 1, 1, -288, [191], 192], -289], 4, 1], [0, "edZ4pr+HlD/rWTlVyl2pDQ", 1], [5, 262, 64], [0, -90, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbWin", 13, [[[22, "100.000", 67, 64, false, 1, 1, -290, [199], 200], -291], 4, 1], [0, "c9uQIiQD1PMKfvGC12LxWW", 1], [5, 262, 64], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbWin", 17, [[[22, "100.000", 67, 64, false, 1, 1, -292, [205], 206], -293], 4, 1], [0, "bfy5pG1odIp6a73oJHUxrA", 1], [5, 262, 64], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [72, 2, false, 36], [17, "lbRemain", 37, [-294], [0, "08dDb6mK5PE4iPEazqh4G8", 1], [5, 102.5, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "CÒN 26 HŨ", 20, 50, false, 1, 1, 84], [17, "lbRemain", 38, [-295], [0, "32pjKUIDJCMYINha9t8Ngp", 1], [5, 102.5, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "CÒN 26 HŨ", 20, 50, false, 1, 1, 86], [17, "lbRemain", 39, [-296], [0, "a2aHFmxsFMgJZYK0uT1Jar", 1], [5, 91, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "CÒN 2 HŨ", 20, 50, false, 1, 1, 88], [17, "lbRemain", 40, [-297], [0, "ebRlPASRtPKpJmvS0Xex1j", 1], [5, 91, 25], [0, 0, 0.5], [44.3, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "CÒN 2 HŨ", 20, 50, false, 1, 1, 90], [2, "bgBoard", 42, [[3, -298, [8], 9]], [0, "0aRdMDknRPDKpldvsSHmPE", 1], [5, 379, 379], [0, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "1", 4, [[3, -299, [12], 13]], [0, "bbn3228QFCFLLr8qNAy3vD", 1], [5, 26, 26], [0, 153, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "2", 4, [[3, -300, [14], 15]], [0, "26uRpPPa9JU5f7je/VwxD5", 1], [5, 26, 26], [0, 119, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "3", 4, [[3, -301, [16], 17]], [0, "34GDwI54FI1aG4pJVTy+Ft", 1], [5, 26, 26], [0, 85, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "4", 4, [[3, -302, [18], 19]], [0, "d7IyJtVuJOtakaZVXmfGRk", 1], [5, 26, 26], [0, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "5", 4, [[3, -303, [20], 21]], [0, "d8qYrZjQBA2psHyuCfNRSa", 1], [5, 26, 26], [0, 17, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "6", 4, [[3, -304, [22], 23]], [0, "8fCAR5HmxLP6Q0PkuELaGi", 1], [5, 26, 26], [0, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "7", 4, [[3, -305, [24], 25]], [0, "82/tVqgkhAZaEYOAf5kSDQ", 1], [5, 26, 26], [0, -51, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "8", 4, [[3, -306, [26], 27]], [0, "61J9JecmtCG4LVLCsPGVXa", 1], [5, 26, 26], [0, -85, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "9", 4, [[3, -307, [28], 29]], [0, "1cPOgtKKdCqoSCtvDJHMj2", 1], [5, 26, 26], [0, -119, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "10", 4, [[3, -308, [30], 31]], [0, "c2ZlLszu5AgbPvr5cFGBQC", 1], [5, 26, 26], [0, -153, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "11", 5, [[3, -309, [32], 33]], [0, "3cNfj+WzNKAq0jhR2CVSxT", 1], [5, 26, 26], [0, 153, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "12", 5, [[3, -310, [34], 35]], [0, "7allWRT09HOIbFL0o2Vue2", 1], [5, 26, 26], [0, 119, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "13", 5, [[3, -311, [36], 37]], [0, "421E+v1EhFM7MXBkT7cW0T", 1], [5, 26, 26], [0, 85, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "14", 5, [[3, -312, [38], 39]], [0, "bb42YZ4IZNraXOvAw8Hs3t", 1], [5, 26, 26], [0, 51, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "15", 5, [[3, -313, [40], 41]], [0, "aaQDNngoBIzZhn4LdAIFoP", 1], [5, 26, 26], [0, 17, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "16", 5, [[3, -314, [42], 43]], [0, "68h+gSDaNJNY8EYbvGtLtG", 1], [5, 26, 26], [0, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "17", 5, [[3, -315, [44], 45]], [0, "bbaHBy/axJypYBI7AA1aIm", 1], [5, 26, 26], [0, -51, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "18", 5, [[3, -316, [46], 47]], [0, "68a+Av39tODbi2aA+IPZTq", 1], [5, 26, 26], [0, -85, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "19", 5, [[3, -317, [48], 49]], [0, "2aJ5fkkuxDeaCD+cwCXPll", 1], [5, 26, 26], [0, -119, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "20", 5, [[3, -318, [50], 51]], [0, "b0xO+3/C5JKYZ4FWnXLb4z", 1], [5, 26, 26], [0, -153, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbTotalLine", 14, [-319], [0, "78AMbNy7pC2oWkS6ohStc8", 1], [5, 29.94, 50], [-34.6, -3.5, 0, 0, 0, 0, 1, 1, 1, 1]], [77, "20", 22, 50, false, false, 1, 1, 113, [52]], [2, "Line", 14, [[23, "Dòng", 22, false, 1, 1, -320, [53], 54]], [0, "22Zr10eANBP7oGrzwFu0zA", 1], [5, 64.69, 50.4], [15, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [67, 14, [[8, "06c65drxJVBh4ZpP6QSPpC5", "betLinesClicked", 1]], [4, 4294967295], [4, 4294967295], 14], [10, 1.1, 3, 23, [[19, "06c65drxJVBh4ZpP6QSPpC5", "roomClicked", "1", 1]], [4, 4294967295], [4, 4294967295], 23], [10, 1.1, 3, 24, [[19, "06c65drxJVBh4ZpP6QSPpC5", "roomClicked", "2", 1]], [4, 4294967295], [4, 4294967295], 24], [10, 1.1, 3, 25, [[19, "06c65drxJVBh4ZpP6QSPpC5", "roomClicked", "3", 1]], [4, 4294967295], [4, 4294967295], 25], [10, 1.1, 3, 26, [[19, "06c65drxJVBh4ZpP6QSPpC5", "roomClicked", "4", 1]], [4, 4294967295], [4, 4294967295], 26], [10, 1.1, 3, 31, [[8, "06c65drxJVBh4ZpP6QSPpC5", "scaleClick", 1]], [4, 4294967295], [4, 4294967295], 31], [25, "slot0", 9, [-321], [0, "a84rAYqxxF7qPPsUqLhuB9", 1], [5, 72, 96], [0, 190, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "candy5", 122, [-322], [0, "ed4H99PtNNfKvWud6/QQZF", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 123, [75]], [6, "candy5", 44, [-323], [0, "94wa2Z2hBGsIaTk918EKiL", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 125, [76]], [6, "candy5", 45, [-324], [0, "90Kw0ELPBBu7c5X100XVPg", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 127, [79]], [6, "candy5", 46, [-325], [0, "c5y0D3++lBZrfJ2QlkwBcp", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 129, [82]], [27, 9, [124, 126, 128, 130]], [25, "slot0", 10, [-326], [0, "29Fq0kcgNMYIJRFZvZNz6A", 1], [5, 72, 96], [0, 190, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "candy5", 132, [-327], [0, "2cWmV9WLtCdIfOvl8Pfy9t", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 133, [90]], [6, "candy5", 47, [-328], [0, "9fg6bZSLlJIrgrHr+MSvr8", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 135, [91]], [6, "candy5", 48, [-329], [0, "7bdjb1ilpAKav5sbpZKze3", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 137, [94]], [6, "candy5", 49, [-330], [0, "dfC935+XJN35o5FGV3kBUd", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 139, [97]], [27, 10, [134, 136, 138, 140]], [25, "slot0", 11, [-331], [0, "79BcwdREFJ8btjjsg7YZWU", 1], [5, 72, 96], [0, 190, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [6, "candy5", 142, [-332], [0, "77F3STrxFKR69UkGXkKnfH", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 143, [105]], [6, "candy5", 50, [-333], [0, "228+lrhbFDU7fkTHNmJU9V", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 145, [106]], [6, "candy5", 51, [-334], [0, "ae3UjwmfVBEJ+0g8cU+RWA", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 147, [109]], [6, "candy5", 52, [-335], [0, "26pfhJmahHZbRZwxqoOLn5", 1], [5, 80, 80], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [9, 149, [112]], [27, 11, [144, 146, 148, 150]], [10, 1.1, 3, 32, [[8, "06c65drxJVBh4ZpP6QSPpC5", "autoSpinClicked", 1]], [4, 4294967295], [4, 4294967295], 32], [68, 1.1, true, 3, 33, [[8, "06c65drxJVBh4ZpP6QSPpC5", "spinClicked", 1]], [4, 4294967295], [4, 4294967295], 33], [10, 1.1, 3, 34, [[8, "06c65drxJVBh4ZpP6QSPpC5", "fastSpinClicked", 1]], [4, 4294967295], [4, 4294967295], 34], [14, "<PERSON>uay miễn phí: 1", 26, 50, false, 1, 1, 75], [54, "lbSessionId", 3, [-336], [0, "daFowmGVBBpaXn0YwB76Bc", 1], [4, 4289243304], [5, 120, 40], [0, 0, 0.5], [139.9, 196.9, 0, 0, 0, 0, 1, 1, 1, 1]], [78, 16, 50, false, false, 1, 1, 156, [168]], [32, "bgJackpot", 35, [[3, -337, [169], 170]], [0, "96ZL83jShKyqXTXR/tXaMz", 1], [5, 241, 54]], [12, 76], [2, "lb100", 22, [[23, "100", 24, false, 1, 1, -338, [178], 179]], [0, "a6ThBhK69GWaSZg9mMwdRx", 1], [5, 36.75, 30], [2, 128.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lb1K", 22, [[23, "1K", 24, false, 1, 1, -339, [180], 181]], [0, "09sqkeAoRFM7NZpkvgEbiV", 1], [5, 26.25, 30], [0, 42.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lb5K", false, 22, [[79, false, "5K", 24, false, 1, 1, -340, [182], 183]], [0, "3cFn6/uQhAEa6IHMB2Gqhs", 1], [5, 30, 30], [0, -43.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lb10K", 22, [[23, "10K", 24, false, 1, 1, -341, [184], 185]], [0, "09DmnDuoZDH7uIrysMpFAn", 1], [5, 39.75, 30], [0, -128.5, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "particleBigWin", 12, [-342], [0, "2el9lp4V9GS6EKXZlBXsZZ", 1], [0, 121, 0, 0, 0, 0, 1, 1, 1, 1]], [82, 100, 1.5, 0.5, 58, 40, -1, 51, 500, 1, 850, 383, 0, 50, {"custom": false}, 164, [188], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 101, 73], [0, 0, -2400]], [33, "noHu_mn", 12, [[37, "NoHu-Idel", 0, false, "NoHu-Idel", -343, [189], 190]], [0, "baknKp44FL3Z96bmJxvi/1", 1], [0, 34.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, 80], [34, "black", false, 1, 13, [[20, 0, -344, 195]], [0, "a4RSTnA4xNW6OcdTT7WUUW", 1], [5, 3000, 3000]], [26, "particleWin", 13, [-345], [0, "378Ut1G4pJk5IreHDz2ofS", 1], [9, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [36, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, {"custom": false}, 169, [196], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [33, "ThagLon_mn", 13, [[37, "ThangSieuLown-idle", 0, false, "ThangSieuLown-idle", -346, [197], 198]], [0, "ebvaIXfUZJDITH4d9WxrJT", 1], [0, 34.2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, 81], [34, "black", false, 1, 17, [[20, 0, -347, 203]], [0, "70KsFaKpdN85J5EaAzF1dZ", 1], [5, 3000, 3000]], [26, "particleWin", 17, [-348], [0, "dbqk3QnHlPh5IMWGUuMPys", 1], [9, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [36, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, {"custom": false}, 174, [204], [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [12, 82], [83, 1, [212, 213, 214, 215, 216, 217], [218, 219], [220, 221], [222, 223]]], 0, [0, 10, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 11, 19, 0, 0, 1, 0, 12, 121, 0, 13, 116, 0, 14, 153, 0, 15, 152, 0, 16, 154, 0, 17, 120, 0, 18, 119, 0, 19, 118, 0, 20, 117, 0, 21, 177, 0, 0, 1, 0, 0, 1, 0, -7, 177, 0, 0, 1, 0, -1, 18, 0, -2, 19, 0, -1, 53, 0, -2, 54, 0, -3, 55, 0, -4, 56, 0, -5, 57, 0, -6, 58, 0, -7, 59, 0, -8, 60, 0, -9, 61, 0, -10, 62, 0, -11, 63, 0, -12, 64, 0, -13, 65, 0, -14, 66, 0, -15, 67, 0, -16, 68, 0, -17, 69, 0, -18, 70, 0, -19, 71, 0, -20, 72, 0, 0, 2, 0, -1, 53, 0, -2, 54, 0, -3, 55, 0, -4, 56, 0, -5, 57, 0, -6, 58, 0, -7, 59, 0, -8, 60, 0, -9, 61, 0, -10, 62, 0, -11, 63, 0, -12, 64, 0, -13, 65, 0, -14, 66, 0, -15, 67, 0, -16, 68, 0, -17, 69, 0, -18, 70, 0, -19, 71, 0, -20, 72, 0, 22, 114, 0, 23, 157, 0, -1, 131, 0, -2, 141, 0, -3, 151, 0, 0, 3, 0, -1, 42, 0, -2, 4, 0, -3, 5, 0, -4, 8, 0, -5, 27, 0, -6, 15, 0, -7, 43, 0, -8, 21, 0, -10, 73, 0, -11, 156, 0, -12, 35, 0, -13, 77, 0, 0, 4, 0, -1, 93, 0, -2, 94, 0, -3, 95, 0, -4, 96, 0, -5, 97, 0, -6, 98, 0, -7, 99, 0, -8, 100, 0, -9, 101, 0, -10, 102, 0, 0, 5, 0, -1, 103, 0, -2, 104, 0, -3, 105, 0, -4, 106, 0, -5, 107, 0, -6, 108, 0, -7, 109, 0, -8, 110, 0, -9, 111, 0, -10, 112, 0, 5, 6, 0, 24, 18, 0, 0, 6, 0, -1, 36, 0, -2, 7, 0, -3, 41, 0, 0, 7, 0, 0, 7, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 40, 0, 0, 8, 0, -1, 14, 0, -2, 23, 0, -3, 24, 0, -4, 25, 0, -5, 26, 0, 0, 9, 0, -2, 131, 0, -1, 122, 0, -2, 44, 0, -3, 45, 0, -4, 46, 0, 0, 10, 0, -2, 141, 0, -1, 132, 0, -2, 47, 0, -3, 48, 0, -4, 49, 0, 0, 11, 0, -2, 151, 0, -1, 142, 0, -2, 50, 0, -3, 51, 0, -4, 52, 0, 0, 12, 0, -1, 79, 0, -2, 164, 0, -3, 166, 0, -4, 80, 0, 0, 13, 0, -1, 168, 0, -2, 169, 0, -3, 171, 0, -4, 81, 0, 0, 14, 0, -2, 116, 0, -1, 113, 0, -2, 115, 0, 0, 15, 0, -1, 28, 0, -2, 29, 0, -3, 30, 0, -4, 31, 0, 25, 176, 0, 26, 172, 0, 27, 167, 0, 28, 175, 0, 29, 170, 0, 30, 165, 0, 31, 17, 0, 0, 16, 0, -3, 17, 0, 0, 17, 0, -1, 173, 0, -2, 174, 0, -3, 82, 0, -1, 85, 0, -2, 87, 0, -3, 89, 0, -4, 91, 0, 32, 83, 0, 0, 18, 0, -2, 22, 0, 0, 20, 0, 0, 21, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -1, 160, 0, -2, 161, 0, -3, 162, 0, -4, 163, 0, 0, 23, 0, -2, 117, 0, 0, 24, 0, -2, 118, 0, 0, 25, 0, -2, 119, 0, 0, 26, 0, -2, 120, 0, 0, 27, 0, 5, 27, 0, 0, 27, 0, 0, 28, 0, 5, 28, 0, 0, 28, 0, 0, 29, 0, 5, 29, 0, 0, 29, 0, 0, 30, 0, 5, 30, 0, 0, 30, 0, 0, 31, 0, -2, 121, 0, 0, 32, 0, -2, 152, 0, 0, 33, 0, -2, 153, 0, 0, 34, 0, -2, 154, 0, 33, 159, 0, 0, 35, 0, -1, 158, 0, -2, 76, 0, -1, 83, 0, 0, 36, 0, 0, 37, 0, -1, 84, 0, 0, 38, 0, -1, 86, 0, 0, 39, 0, -1, 88, 0, 0, 40, 0, -1, 90, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, -1, 92, 0, 0, 43, 0, 0, 44, 0, -1, 125, 0, 0, 45, 0, -1, 127, 0, 0, 46, 0, -1, 129, 0, 0, 47, 0, -1, 135, 0, 0, 48, 0, -1, 137, 0, 0, 49, 0, -1, 139, 0, 0, 50, 0, -1, 145, 0, 0, 51, 0, -1, 147, 0, 0, 52, 0, -1, 149, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 34, 155, 0, 0, 73, 0, -1, 74, 0, 0, 74, 0, -1, 75, 0, -1, 155, 0, 0, 75, 0, 0, 76, 0, -2, 159, 0, 0, 77, 0, -1, 78, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, -2, 167, 0, 0, 81, 0, -2, 172, 0, 0, 82, 0, -2, 176, 0, -1, 85, 0, -1, 87, 0, -1, 89, 0, -1, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, 0, 109, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, -1, 114, 0, 0, 115, 0, -1, 123, 0, -1, 124, 0, -1, 126, 0, -1, 128, 0, -1, 130, 0, -1, 133, 0, -1, 134, 0, -1, 136, 0, -1, 138, 0, -1, 140, 0, -1, 143, 0, -1, 144, 0, -1, 146, 0, -1, 148, 0, -1, 150, 0, -1, 157, 0, 0, 158, 0, 0, 160, 0, 0, 161, 0, 0, 162, 0, 0, 163, 0, -1, 165, 0, 0, 166, 0, 0, 168, 0, -1, 170, 0, 0, 171, 0, 0, 173, 0, -1, 175, 0, 35, 1, 2, 4, 3, 3, 4, 19, 6, 4, 18, 9, 4, 20, 10, 4, 20, 11, 4, 20, 12, 4, 16, 13, 4, 16, 16, 4, 19, 20, 4, 43, 348], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 83, 85, 87, 89, 91, 114, 124, 126, 128, 130, 134, 136, 138, 140, 144, 146, 148, 150, 155, 157, 165, 165, 170, 170, 175, 175], [3, -1, 2, 2, 2, 2, 1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, 3, -1, -2, -3, -4, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, 3, -1, -2, -3, -4, -1, -1, 3, -1, -1, 3, -1, -1, 3, -1, 3, -1, -2, -3, -4, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 1, -1, -1, 1, -1, 2, -1, 2, 3, -1, -2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, -1, 7, -1, 2, 3, -1, 1, -1, -1, 7, -1, 2, 3, -1, 1, -1, -1, 2, 3, -1, 3, -1, -2, -1, -2, -3, -4, -5, -6, -1, -2, -1, -2, -1, -2, -1, -2, -1, -2, -3, -4, -5, -6, 36, 37, 38, 39, 40, 1, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 6, 1, 6, 1, 6, 1], [14, 14, 3, 3, 3, 3, 30, 31, 0, 32, 0, 33, 0, 34, 0, 35, 0, 36, 0, 37, 0, 38, 0, 39, 0, 40, 0, 41, 0, 42, 0, 43, 0, 44, 0, 45, 0, 46, 0, 47, 0, 48, 0, 49, 0, 50, 0, 51, 0, 52, 0, 53, 0, 0, 54, 0, 55, 0, 15, 0, 16, 0, 56, 0, 17, 0, 57, 0, 58, 0, 59, 0, 60, 0, 7, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 4, 4, 8, 9, 10, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 4, 4, 8, 9, 10, 0, 0, 1, 1, 0, 1, 1, 0, 1, 1, 4, 4, 8, 9, 10, 0, 0, 18, 0, 11, 0, 19, 0, 61, 0, 62, 0, 63, 0, 64, 0, 65, 0, 66, 0, 67, 0, 68, 0, 69, 0, 70, 0, 71, 0, 72, 0, 73, 0, 74, 0, 75, 0, 76, 0, 77, 0, 78, 0, 79, 0, 80, 81, 0, 0, 82, 0, 83, 0, 20, 21, 21, 84, 0, 6, 0, 6, 0, 6, 0, 6, 0, 12, 0, 22, 23, 0, 24, 5, 5, 12, 0, 22, 23, 0, 24, 5, 5, 12, 0, 0, 20, 5, 5, 25, 25, 85, 86, 15, 87, 16, 88, 17, 89, 19, 90, 18, 11, 11, 7, 7, 91, 92, 93, 94, 2, 95, 96, 97, 98, 99, 100, 101, 3, 3, 3, 3, 102, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 26, 26, 103, 13, 27, 13, 27, 13]], [[[15, "columnStop2", 1.35, 8, "0", [{"frame": 1.35, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 190}, {"frame": 1.3333333333333333, "value": -190}, {"frame": 1.35, "value": 190}], "active": []}}, "slot1": {"props": {"y": [{"frame": 0, "value": 95}, {"frame": 1, "value": -190}, {"frame": 1.0166666666666666, "value": 190}, {"frame": 1.35, "value": 95}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, 0]}, {"frame": 0.6666666666666666, "value": [0, -190]}, {"frame": 0.6833333333333333, "value": [0, 190]}, {"frame": 1.35, "value": [0, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -95]}, {"frame": 0.3333333333333333, "value": [0, -190]}, {"frame": 0.35, "value": [0, 190]}, {"frame": 1.35, "value": [0, -95]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []]]]