[1, ["ecpdLyjvZBwrvm+cedCcQy", "825TQ2kU9Ktq1Ncj5HdPmn", "59C7fScnNIDJ0+8Pozx5V6", "fdNoodJKVLj4dF1TLppv2g", "82hGaiLH9Baq98xRGT3MMW", "56XeyDpqBLTINZZjgFU7xK", "7efPlf0GJIM4npUupBNNSg", "a9VpD0DP5LJYQPXITZq+uj", "1dLz0IPHdDnYCGwnKmvxEo", "1c2oPmogBDRIXDn3+ViCrb", "66ssBgU3FAw5fYjMXVGS9Q", "7clEMHz1dA9KTrYU+qtEZ1", "1d0ZVkAohDcY3Whb3giQGK", "5doz76MJtMeY9u1D5d6FF8", "c5H1zNoG5FNLT97wLAo1z6", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_N$target", "_N$normalSprite", "_textureSetter", "root", "_N$content", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalSprite", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 6, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["9f9e1IJ4WFMSqhdfSr9lY+W", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["<PERSON>.<PERSON>", ["bounceDuration", "node", "_N$content"], 2, 1, 1], ["34c4fFXj/hAgrtv/y80/0lh", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3]], [[8, 0, 1, 2], [0, 0, 5, 3, 2, 4, 7, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [3, 0, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 3], [0, 0, 5, 6, 2, 4, 7, 2], [0, 0, 5, 3, 2, 4, 2], [2, 0, 2, 3, 4, 5, 6, 7, 2], [4, 0, 1, 3, 3], [4, 0, 1, 2, 3, 4], [3, 2, 3, 4, 1], [5, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 6, 3, 2, 4, 8, 7, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [0, 0, 5, 6, 3, 2, 4, 2], [6, 0, 1, 2, 1], [7, 0, 1], [9, 0, 1, 2, 2], [10, 0, 1], [11, 0, 1, 2, 3, 4, 4], [2, 1, 0, 2, 3, 4, 5, 6, 7, 3], [2, 2, 8, 1], [12, 0, 1, 2, 2]], [[[{"name": "nex1", "rect": [0, 0, 51, 62], "offset": [0, 0], "originalSize": [51, 62], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [4]], [[{"name": "huongdanitem", "rect": [0, 0, 770, 441], "offset": [0, 0], "originalSize": [770, 441], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [5]], [[{"name": "superwin", "rect": [0, 1, 666, 327], "offset": [0, -0.5], "originalSize": [666, 328], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [6]], [[[11, "helpMiniView"], [12, "helpMiniView", [-4, -5, -6, -7, -8, -9, -10, -11], [[16, -2, [26, 27], 25], [17, -3]], [0, "45MDcHq6xPe58DF1KfWLNZ", -1]], [2, "pageview", 1, [-15], [[18, 0.5, -13, -12], [19, -14]], [0, "1fM9UUzSFP5psXgoGL1dSz", 1], [5, 836, 400], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "content", [-17, -18, -19], [[20, 1, 1, 15, -16, [5, 2508, 400]]], [0, "24fEAMoCBK9pGuVC31/I8p", 1], [5, 2508, 400], [0, 0, 0.5], [-418, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 1, [-22], [[7, 3, -21, [[8, "9f9e1IJ4WFMSqhdfSr9lY+W", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -20, 15]], [0, "94vDoz8jpMF5L2jA91gjjH", 1], [5, 80, 80], [481.37, 214.119, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnBack", 1, [-25], [[7, 3, -24, [[9, "34c4fFXj/hAgrtv/y80/0lh", "switchPageClicked", "-1", 2]], [4, 4294967295], [4, 4294967295], -23, 18]], [0, "347Cqt/i5PK57AGAGNk2my", 1], [5, 80, 80], [-423.491, 7.991, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnContinue", 1, [-28], [[21, 1.1, 3, -27, [[8, "9f9e1IJ4WFMSqhdfSr9lY+W", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -26, 21]], [0, "f25o8BWRFHGoXUsAcY4Xld", 1], [5, 180, 80], [0, -239.02, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnNext", 1, [-31], [[7, 3, -30, [[9, "34c4fFXj/hAgrtv/y80/0lh", "switchPageClicked", "1", 2]], [4, 4294967295], [4, 4294967295], -29, 24]], [0, "1dmHUqFSBG0qIyZiSr9Yk8", 1], [5, 80, 80], [120, -238, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "black", 100, 1, [[3, 0, -32, [0], 1], [22, -33, [4, 4292269782]]], [0, "b6CyonVsBL57m5VUwSK64c", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 2, [3], [[23, 0, -34, [12]]], [0, "aeLGAny+9Gy6Kb1DJk4A89", 1], [5, 836, 520]], [1, "nen popup", 1, [[10, -35, [2], 3]], [0, "davmUxNq5CDoMu9R520noL", 1], [5, 1028, 674], [1.983, 21.017, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bảng thưởng", 1, [[10, -36, [4], 5]], [0, "38t0+OOzlIFJWZcDuLg4op", 1], [5, 162, 31], [0, 251.528, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "payTable", 3, [-37], [0, "01cF+U6clLTqSky+Sweprh", 1], [5, 836, 400], [418, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 12, [[3, 2, -38, [6], 7]], [0, "6dJxVuV2BHY5Gq6UP4TSQz", 1], [5, 770, 441], [13.832, -31.218, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "superWin", 3, [-39], [0, "5faiY86+BAcI4COQFbRUJn", 1], [5, 836, 400], [1254, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "sprite", 14, [[3, 2, -40, [8], 9]], [0, "dcLeRkEA5GgLsiLVtD/X3z", 1], [5, 666, 328]], [5, "betlines", 3, [-41], [0, "569pXmoglLub6IOIrx/kzj", 1], [5, 836, 400], [2090, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "sprite", 16, [[3, 2, -42, [10], 11]], [0, "43RuZsjTNOap2HOVHvK6/R", 1], [5, 618, 394]], [1, "sprite", 4, [[4, 2, false, -43, [13], 14]], [0, "b9mCBfpYFFFYr6g+OS+TEZ", 1], [5, 63, 59], [-27.627, 6.043, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 5, [[4, 2, false, -44, [16], 17]], [0, "28QsX+gaZMiIyvWl0/CnZf", 1], [5, 51, 62], [0, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [6, "sprite", 6, [[4, 2, false, -45, [19], 20]], [0, "4a2huZ+01OkZmvQ7mRMNQh", 1], [5, 186, 73]], [1, "sprite", 7, [[4, 2, false, -46, [22], 23]], [0, "40hr3bjEdMibKWA46vIwQ7", 1], [5, 51, 62], [325.992, 241.191, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 10, 0, -3, 11, 0, -4, 2, 0, -5, 4, 0, -6, 5, 0, -7, 6, 0, -8, 7, 0, 6, 3, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, 0, 3, 0, -1, 12, 0, -2, 14, 0, -3, 16, 0, 2, 4, 0, 0, 4, 0, -1, 18, 0, 2, 5, 0, 0, 5, 0, -1, 19, 0, 2, 6, 0, 0, 6, 0, -1, 20, 0, 2, 7, 0, 0, 7, 0, -1, 21, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, 0, 13, 0, -1, 15, 0, 0, 15, 0, -1, 17, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 7, 1, 3, 8, 9, 46], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, 9, -1, -2], [0, 7, 0, 8, 0, 9, 0, 10, 0, 11, 0, 12, 0, 0, 13, 1, 0, 2, 1, 0, 14, 1, 0, 2, 1, 3, 3, 15]]]]