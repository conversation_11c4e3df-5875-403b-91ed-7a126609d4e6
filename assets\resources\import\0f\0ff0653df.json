[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "84sL1PMLRE7bCsBxO5IwzV", "8bB40N/LdMGKgrvO5fBKpm", "dbh0K0Ui1BtbmJ6/AFxXVS", "a3l6ic4lJMhqWRY3K84LO+", "017Jn3Zv1Ft7hygdjpaSoK", "1cDpn7iXVFOJlUGFAdLw99", "62f4+oyDZEa4GHSq2gua7c", "0e9P4V1GdM85UkCubmruRY", "42FqTAlBVHKoGRMN4Pw/kZ", "71NbLtWZVENZ16emRloX5s", "baj4v5trtOyI7JTchbXAiK", "7a/QZLET9IDreTiBfRn2PD", "76XIlclNBJFY6dbyGQN/Jp", "b1FdnLn49AQY9AUBmOdLZD", "72hOUIvdtMLZ0oT88tRKh8", "5ckbco7cROAJ92hw6PR5eT", "2fLK3rZuRMHKzfC0bRnM3/", "6do44g8xpGK7zzhPP3GOTF", "e4nmUQqB1B5oOPip8ewaBd", "adQbfcQ29JB5aSRs90Tsf6", "f9dTdOo0hB9ZCzesk+z1/5", "08vpyWtphHz5eOotQtCgxB", "89t1t1HzJPfIsXsUwt4Hk2", "5cCKXcafNGnK0Jt9MkRbqF", "be4PQo8dtCYp+vwDjyX56P", "0bCEtI7ptOmZHipubmGQ+v", "11ONk2ePRLa7ZuMeQ9nQtF", "4fMAqa1JBAG7TTx9FzOqP9", "96NF2Gu/FBsYEqsfJEuyf0", "f8zmoIsllG8ozQ9xZTcydw", "65STJRaOhGZJTO2AQIt2zc", "64mtbyC+RKQLqTXA21mmM5", "b5JYZRxtJDz5nGPQJj7fgE", "bbrDBQOvlCBYBb6UsTyg3Y", "60lCKF0BFLRJpfGf89TIRK", "d4zGCcnPVNtbt0FMclu24y", "deFzX2fuBDiJAudVaDc1Oo", "554WRGGlZH0YTRJUPl5Rdx", "90XwFT8bVHFITGkNTMggT+", "aaD8ucCqlIW77wPW5kPw+W", "6cH6A0iI5NobNHapuiYy9w", "8bk4OB12VHrqJItHKofG9f", "44F5RqzeBAOYwVwVHHVTHG", "6265JGQBhAA4xZCkfma9xo", "dbrfKp8QRLXo+LAovF6R1r", "02EpDKtlpDCaBTEg+Y4cu2", "107rft5IRHs5zAD6hNodbh", "24+l98WfxK3YoNjF9BLqfl", "5cqfMMaphGDY1i7mqBlPy+", "74KjI2RXFEFJxQh8wshuC1", "a6ZySYg75Lno2EdvKNyB/W", "44CFdkRFVAOJNvaq+Wx5rT", "8f1pqMzfVHcKcwb27Th3NL", "7b+59R6DVD35LfHouZ7XJv", "48rNiLsKJE9JxNBJsPpDup", "44ITAzWyFPA5PB1n9SWdco", "83z2yOsnJPr4ovwF+m5dnP", "a33UZXc/5Pzo5TFjSuAxtE", "ccN4ASiitC95aD9Lut28Yz", "1dLy7PiR9Oere6/9SHKyW0", "a7I9cRgV9HIKJiDta28BvA", "f0jwjWQBZMJoR/9qG8ndYb", "abpLQmo1tJV6dbSUq6/UgU", "4drVsf/TtC+pLe8U+CSoYP", "deaTin3HZDCJ8fs6I4a9Q1", "5flru/uyhBPrLlpwDJVuDL", "16FxOm/kFCfbuRieySZ9mO", "57kqKFRGhAXKrsfkQaVkbb", "24QqTxi+lOpIzDbF7Rl9lo", "33Ssg8kdBEabsK0pHAam8E", "8d4IfWJkVN2L5GPfcsGaxm", "b7SNZp6JZF+bzzVHd+74VX", "60Cjw/qchBi6zTtOycNTE/", "d0l3SkFW5CqKqu6M7Hh2xu", "a5Pxb/df1CmIvZ3VE2Tm9A", "7a3tJazMtEGbqPycbPkbHj", "2ez3r//fJInoIlG7i9pDsI", "c0T2zi605LLoTN9Z3ryrPx", "47xjJCY2FEjoU5RCewX7ay", "a6cxNiBy5BcoIp7fWn8Z/6", "bdvJIkJ3JFga0lMXl8x205", "c9xWHktBNFsY0/dVOwqdx0", "ffmdogqx5K0ZFkjKs9NAFe", "d9NoL6doFJPIbcLTX4TWes", "e8zUIRYL5G2ZmZpvwo2fcL", "dcCTQQjzJI9b2zI8+0Up0D", "4blzOf71JK25bvyete8VyO", "5169CJhrNAmJAIU6SpY390", "a3i9OOwSBDwImAHGdyegcH", "a0fj39KGRMbqTGurBNhSP8", "37whHwSRJK+Lif1i0ZnGUO", "c92KSKE4FIaq4HQkEV8XqL", "d09sx5MfdGV5S9gI8MaI8P", "89FB9kDXRCk6fWkBkmp0WC", "425OyJiKNBEqTIincFeXrD", "b6ncPWds1LN42CD6PDZAIL", "eaOrgifZJI3rZL8jovxyWR", "363oVUjhJPwaDfOLZMXfyb", "a6Ydnifa5O0auqa9VzhHps", "911LLlfY1MRK/td4XQs4O+", "e5KCyRNWNOwbYhKVVpO27z", "be4bdSJlxF1pV2p/b1VRXO", "daEjx/aJpA6KlBIWq74594", "7bmWz3SbpE8YTGsALDvaTR", "edqtZ+KBxLFLEpyMVyK1fa", "f10mlGIaBJ04bgbDT+bUVp", "0bmetR1uNBWqu7H4QFpcox", "15zHi1VSNCDKrfXEL26FgV", "7bnBb+uj1Cz5jhl7DTOCT4", "8494ISLDFPkYi2F4uKxX/z", "28I33u5hxNJ7vQEg89Yzcf", "7fGEq8BPZGtr8pGKZzLel9", "faiQhppi9DlL2cQuNvzivL", "45CtNC6mZISLuITqPqjev+", "57zZN6zpdJSo7WK4MYkiTW", "8eu5lDi55CQbUTy+ZEJDuJ", "a0VIOUnqlECbYXmuQPB42u", "f3Fr4uDTdI1Y3VC+i8z1hr", "92EQr8uldAypNjzGyXF4v/", "3ewe2727lLVoJ0ADZ8W05J", "e7Fj4Fv9dFza13rOJG5SHm", "39f7+X/G1ISouId8kveHFD", "45c/a/PatNa6lfosNdvRPt", "afYrEVl7xB36C5GVg5Ufiq", "23KDdWwRNKJJpUdOSxvwmM", "7904g+IcRAR72oGvrjwQBV", "8fSA+XMDRMgog5+JKqHoFc", "7dFN9M5VhLBbPjJLZpEK6L", "f26343FIdChJbsteFZN2Jb", "f8ikgKlIRA8KZ+2oAI42zr", "7aUwOp12NIV6zj43tN9WL+"], ["node", "_spriteFrame", "_N$file", "_N$target", "_parent", "_N$skeletonData", "_normalMaterial", "_N$normalSprite", "_N$barSprite", "_defaultClip", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_clip", "layoutCardGame", "lbSID", "lbMessage", "_N$font", "root", "nodeParentChat", "nodeMainRoomView", "nodeChooseRoomView", "animationNofity", "nodeRegisterLeave", "spriteBack", "spriteSound", "layoutContentCard", "buttonDanh", "buttonXepBai", "buttonChon", "buttonBoLuot", "layoutButton", "dealer", "lbPlayerStatus", "lbInfo", "lbTableId", "lbRoomID", "btnSendChat", "editBoxChat", "chatListView", "lbTotalWin", "lbNickName", "lbRank", "iconRank", "rtAdmin", "lbName", "chipBet", "moveCard", "musicBackground", "_N$content", "TLMNTopListView", "data", "prefabHelp", "prefabLayoutWrapCard", "spriteCardBack", "prefabChat", "sfAvatarDef", "bmfWin", "bmfLose", "sfBorderCard", "sfCardBack", "prefab", "_textureSetter"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs", "_anchorPoint", "_color"], 0, 4, 1, 9, 5, 2, 7, 5, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_fillType", "_fillStart", "_fillRange", "_srcBlendFactor", "node", "_materials", "_spriteFrame", "_fillCenter"], -5, 1, 3, 6, 5], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_string", "_N$horizontalAlign", "_lineHeight", "_enableWrapText", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_prefab", "_components", "_contentSize", "_trs", "_anchorPoint", "_color", "_children"], 1, 1, 4, 2, 5, 7, 5, 5, 12], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_N$interactable", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_normalMaterial", "_N$normalSprite"], 0, 1, 9, 1, 5, 5, 5, 6, 6], ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 0, 12, 4, 5, 7, 1, 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_anchorPoint", "_color"], 2, 2, 2, 4, 5, 7, 1, 5, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$paddingLeft", "_N$paddingRight", "_N$paddingTop", "_N$paddingBottom", "_N$spacingY", "_N$affectedByScale", "node", "_layoutSize"], -7, 1, 5], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "_animationName", "defaultAnimation", "node", "_N$skeletonData", "_materials"], -1, 1, 6, 3], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$handleTouchEvent", "node", "_N$font"], -3, 1, 6], ["cc.AudioSource", ["preload", "_volume", "_loop", "node"], 0, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["aecb3P72plJupyHv+8j1L7I", ["node", "nodeChooseRoomView", "nodeMainRoomView", "nodeParentChat", "prefabChat"], 3, 1, 1, 1, 1, 6], ["09d764EYGtI06IOUPrt3aKW", ["node", "spGameResult", "colorDark", "colorWhite", "sfBack", "sfSuitCo", "sfSuitRo", "sfSuitTep", "sfSuitBich", "animationNofity", "sfAvatarDef", "bmfWin", "bmfLose", "sfBorderCard", "sfCardBack"], 3, 1, 12, 5, 5, 3, 3, 3, 3, 3, 1, 6, 6, 6, 6, 6], ["ecbe1Xn8BZJ2KneOLuQ5ukd", ["node", "prefab"], 3, 1, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["aa0ebuePJNGjaJ+PGuzW9E2", ["node", "layoutCardGame", "sfSounds", "spriteSound", "spriteBack", "nodeRegisterLeave"], 3, 1, 1, 3, 1, 1, 1], ["c640du6Nt5BRrAkpiwohzN3", ["node", "layoutCardGame", "layoutContentCard", "prefabLayoutWrapCard"], 3, 1, 1, 1, 6], ["0b7c8vAZhtLbqM8oqzmuWlT", ["node", "layoutButton", "buttonBoLuot", "buttonChon", "buttonXepBai", "buttonDanh"], 3, 1, 1, 1, 1, 1, 1], ["53b88trXlpM65FqpX9o6BHx", ["node", "lbSID", "lbRoomID", "lbTableId", "lbInfo", "lbPlayerStatus", "TLMNPlayers", "dealer", "spriteCardBack"], 3, 1, 1, 1, 1, 1, 1, 2, 1, 6], ["6d778KTeFpKA4z4LqE+Fin1", ["node", "prefabHelp"], 3, 1, 6], ["c9ea2HJ+4FBwJf8JdBQBbUQ", ["channelId", "node", "chatListView", "editBoxChat", "btnSendChat"], 2, 1, 1, 1, 1], ["e63a8GDCoBJzrUnbCTfn9Hs", ["node", "iconRank", "lbRank", "lbSID", "lbNickName", "lbTotalWin", "spTop"], 3, 1, 1, 1, 1, 1, 1, 3], ["8be9fmDgjRGuJDpCIp+Bx7T", ["node", "nodeUser", "lbName", "lbMessage", "rtAdmin"], 3, 1, 1, 1, 1, 1], ["87c34hvqC1AcbGs5+NTs4WA", ["node", "musicBackground", "moveCard", "chipBet"], 3, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["2d79eSpNN9AurP/ZR5pfwNJ", ["ordinalValue", "cardNumber", "node"], 1, 1], ["832cdd6gThBUZ3q0q4hjRy5", ["messWinPosY", "node", "nodeMessage", "lbMessage"], 2, 1, 1, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["d7e53janRVDYbu49BC/dgcL", ["node", "TLMNTopListView"], 3, 1, 1], ["fde5fYTdUdLFaHQ7QSWDYdb", ["node"], 3, 1], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["87b0b6j4kBLKKRiRXRQjf/0", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "editingReturn", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 9, 1, 1, 1], ["63e69hqxiNBDZood3oxrSds", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["3e540mOKLZMLI5LC2NNOdwF", ["node", "cardOnHand", "layoutCard"], 3, 1, 1, 1]], [[20, 0, 1, 2], [0, 0, 4, 5, 3, 6, 8, 2], [0, 0, 4, 7, 5, 3, 6, 8, 2], [0, 0, 4, 5, 3, 6, 2], [2, 3, 0, 1, 4, 2, 8, 9, 10, 6], [1, 8, 9, 10, 1], [1, 0, 8, 9, 10, 2], [2, 3, 0, 1, 4, 2, 8, 10, 6], [0, 0, 4, 5, 3, 10, 6, 2], [10, 0, 1, 3, 3], [9, 1, 2, 1], [0, 0, 1, 4, 5, 3, 6, 8, 3], [1, 1, 0, 8, 10, 3], [4, 0, 3, 4, 6, 5, 9, 2], [10, 0, 1, 2, 3, 4], [0, 0, 4, 5, 3, 6, 9, 8, 2], [0, 0, 1, 4, 5, 3, 6, 9, 8, 3], [6, 0, 6, 1, 2, 3, 4, 5, 2], [1, 1, 0, 8, 9, 10, 3], [0, 0, 4, 7, 5, 3, 6, 9, 8, 2], [0, 0, 2, 4, 7, 5, 3, 6, 8, 3], [3, 0, 2, 4, 3, 5, 7, 6, 2], [7, 0, 1, 2, 9, 10, 11, 5], [7, 0, 1, 2, 10, 11, 4], [1, 8, 10, 1], [1, 1, 0, 2, 8, 9, 10, 4], [35, 0, 1], [2, 3, 0, 1, 4, 2, 8, 9, 6], [0, 0, 1, 4, 7, 3, 3], [0, 0, 1, 4, 5, 3, 10, 6, 8, 3], [9, 0, 1, 2, 3, 2], [1, 1, 0, 8, 3], [4, 0, 3, 4, 6, 7, 8, 5, 2], [36, 0, 1, 2, 2], [2, 3, 0, 5, 1, 4, 2, 7, 8, 10, 8], [37, 0, 1], [8, 0, 1, 2, 4, 6, 5, 4], [8, 3, 0, 1, 2, 4, 5, 5], [41, 0, 1, 2, 1], [0, 0, 1, 4, 7, 5, 3, 6, 8, 3], [5, 0, 7, 3, 4, 5, 2], [3, 0, 1, 2, 4, 3, 5, 7, 6, 3], [3, 0, 2, 4, 3, 2], [1, 1, 0, 4, 5, 6, 8, 9, 11, 6], [4, 1, 0, 3, 4, 6, 7, 8, 5, 3], [31, 0, 1, 2, 3], [0, 0, 4, 7, 5, 3, 8, 2], [0, 0, 4, 7, 3, 2], [0, 0, 4, 7, 5, 3, 2], [0, 0, 4, 7, 3, 8, 2], [0, 0, 1, 4, 7, 3, 8, 3], [0, 0, 1, 4, 7, 5, 3, 10, 6, 8, 3], [0, 0, 4, 7, 5, 3, 6, 2], [5, 0, 8, 3, 4, 5, 6, 2], [5, 0, 2, 8, 3, 4, 5, 6, 3], [3, 0, 2, 4, 3, 5, 2], [3, 0, 2, 4, 3, 5, 6, 2], [7, 0, 1, 10, 11, 3], [1, 0, 2, 8, 9, 3], [11, 0, 1, 2, 3, 4, 5, 6, 6], [30, 0, 1], [33, 0, 1], [12, 0, 1, 2, 2], [2, 3, 0, 1, 4, 2, 8, 6], [14, 0, 3, 2], [16, 0, 2], [0, 0, 7, 5, 3, 2], [0, 0, 1, 4, 3, 3], [0, 0, 1, 4, 3, 8, 3], [0, 0, 7, 5, 3, 6, 8, 2], [0, 0, 7, 5, 3, 10, 6, 8, 2], [0, 0, 7, 5, 3, 6, 9, 2], [0, 0, 7, 5, 3, 6, 9, 8, 2], [0, 0, 1, 2, 7, 5, 3, 6, 4], [0, 0, 4, 7, 5, 3, 10, 6, 8, 2], [0, 0, 4, 7, 5, 3, 6, 9, 2], [0, 0, 4, 3, 6, 2], [0, 0, 1, 4, 5, 3, 10, 6, 9, 3], [0, 0, 4, 3, 6, 9, 2], [0, 0, 1, 2, 4, 5, 3, 6, 9, 8, 4], [0, 0, 4, 3, 2], [5, 0, 7, 3, 4, 5, 6, 2], [5, 0, 1, 7, 3, 4, 5, 6, 3], [5, 0, 1, 7, 3, 4, 5, 3], [3, 0, 2, 9, 3, 2], [3, 0, 2, 4, 3, 8, 5, 7, 2], [3, 0, 2, 4, 3, 8, 5, 7, 6, 2], [3, 0, 1, 2, 4, 3, 8, 5, 7, 6, 3], [3, 0, 2, 4, 3, 8, 5, 6, 2], [3, 0, 2, 4, 3, 8, 5, 2], [3, 0, 1, 2, 4, 3, 5, 6, 3], [6, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 2, 3, 4, 7, 5, 2], [6, 0, 6, 1, 2, 3, 8, 4, 7, 5, 2], [17, 0, 1, 2, 3, 4, 1], [18, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1], [19, 0, 1, 1], [21, 0, 1, 2, 3, 4, 5, 1], [22, 0, 1, 2, 3, 1], [23, 0, 1, 2, 3, 4, 5, 1], [24, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [7, 3, 0, 1, 4, 5, 6, 7, 2, 8, 10, 11, 10], [25, 0, 1, 1], [26, 0, 1, 2, 3, 4, 2], [27, 0, 1, 2, 3, 4, 5, 6, 1], [1, 3, 7, 0, 2, 8, 5], [1, 1, 0, 8, 9, 3], [1, 8, 1], [1, 0, 8, 10, 2], [1, 3, 8, 9, 2], [1, 1, 0, 4, 5, 6, 8, 11, 6], [1, 0, 2, 8, 9, 10, 3], [1, 0, 8, 2], [4, 0, 3, 4, 6, 5, 2], [4, 0, 3, 4, 6, 7, 8, 5, 10, 2], [4, 3, 4, 1], [4, 1, 2, 0, 3, 4, 7, 8, 5, 4], [28, 0, 1, 2, 3, 4, 1], [29, 0, 1, 2, 3, 1], [11, 0, 5, 6, 2], [32, 0, 1, 2, 3, 2], [12, 0, 1, 2], [13, 0, 1, 2, 3, 4, 5, 6, 7, 7], [13, 0, 1, 2, 3, 4, 5, 6, 7], [34, 0, 1, 1], [2, 3, 0, 5, 6, 1, 4, 2, 8, 10, 8], [2, 0, 5, 6, 1, 4, 2, 8, 9, 7], [2, 0, 5, 6, 1, 2, 7, 8, 9, 7], [2, 0, 5, 6, 1, 2, 7, 8, 7], [2, 3, 0, 5, 6, 1, 2, 7, 8, 9, 8], [2, 3, 0, 5, 6, 1, 7, 8, 9, 7], [2, 0, 1, 4, 2, 8, 5], [2, 3, 0, 5, 6, 1, 4, 2, 7, 8, 9], [38, 0, 1, 2, 3, 4, 3], [39, 0, 1, 2, 3, 4, 5, 6, 3], [40, 0, 1, 2, 3, 4, 3], [14, 1, 2, 0, 3, 4], [8, 3, 0, 1, 2, 4, 5], [8, 0, 1, 4, 3]], [[[[65, "TLMNSoLoLobby"], [66, "TLMNSoLoLobby", [-9, -10], [[94, -5, -4, -3, -2, 261], [95, -7, [[263, 264, 265, 266, null, null, 267], 6, 6, 6, 6, 0, 0, 6], [4, 4288322204], [4, 4294967295], [270, 271], [273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285], [286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298], [299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311], [312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324], -6, 262, 268, 269, 272, 325], [96, -8, 326]], [0, "a4VcI9aH5P078C4LI3ftlj", -1]], [46, "MainGame", 1, [-36, -37, -38, -39, -40, -41], [[97, -15, -14, [257, 258], -13, -12, -11], [98, -18, -17, -16, 259], [99, -24, -23, -22, -21, -20, -19], [100, -35, -34, -33, -32, -31, -30, [-26, -27, -28, -29], -25, 260]], [0, "8d9aIe3CFLibzFXdQpogD+", 1], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "game", 2, [-42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53], [0, "b8Dl36tXVJB7DR+0RNwb8L", 1]], [53, "btnPos1", [-56, -57, -58, -59, -60, -61, -62, -63, -64, -65], [[[10, -54, [145, 146]], -55], 4, 1], [0, "ca5ehHmWVKDrkMHmf6LIfd", 1], [5, 120, 120], [-539, -309, 0, 0, 0, 0, 1, 1, 1, 1]], [84, "layoutPositionCard", 3, [[-66, -67, -68, -69, -70, -71, -72, -73, [67, "posCardStartSlide", false, -74, [0, "cbamR4WbdHKJFbvKzbpWGz", 1]], [68, "posCardStartSlide copy", false, -75, [0, "fcr4aEJ+FI8rS240tHfRxC", 1], [-40, -145, 0, 0, 0, 0, 1, 1, 1, 1]], -76], 1, 1, 1, 1, 1, 1, 1, 1, 4, 4, 1], [0, "5bvqFaFoJDo4ZOqzT5u0fr", 1]], [54, "btnPos2", 0, [-79, -80, -81, -82, -83, -84, -85, -86, -87, -88], [[[10, -77, [189, 190]], -78], 4, 1], [0, "36PK6ypi9Jhr1JnkvGYj4O", 1], [5, 100, 100], [-553.8, -48.6, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "btnPos3", [-91, -92, -93, -94, -95, -96, -97, -98, -99, -100], [[[10, -89, [215, 216]], -90], 4, 1], [0, "a6M8GYENtBJ55gzCfN5zgE", 1], [5, 90, 90], [262.3, 226, 0, 0, 0, 0, 1, 1, 1, 1]], [54, "btnPos4", 0, [-103, -104, -105, -106, -107, -108, -109, -110, -111, -112], [[[10, -101, [240, 241]], -102], 4, 1], [0, "6cLb+Iwn1Py4bq8D39+dq4", 1], [5, 90, 90], [548.7, -48, 0, 0, 0, 0, 1, 1, 1, 1]], [69, "layoutRoom", [-114, -115, -116, -117, -118, -119, -120, -121, -122], [[101, false, 1, 3, 20, 20, 20, 15, 60, 20, -113, [5, 580, 483]]], [0, "ecqZZvR5xFP5eoC9kMevMr", 1], [5, 580, 483], [33, -63.2, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "Rooms", 1, [-124, -125, 9, -126, -127, -128], [[102, -123, 98]], [0, "9flvMoeUZGcJmMo6E9HBBS", 1]], [2, "chatView", 10, [-133, -134, -135, -136, -137], [[103, "bai", -132, -131, -130, -129]], [0, "fcT+1j/nRMBpBOlmEplTMw", 1], [5, 358, 495], [-451, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [70, "item", [-144, -145, -146, -147, -148, -149], [[104, -143, -142, -141, -140, -139, -138, [90, 91, 92]]], [0, "35ndmI/PdPSape2P6hcRwE", 1], [4, 4278190080], [5, 240, 60], [-2, 1000, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bgSID", 3, [-151, -152, -153, -154, -155, -156], [[18, 1, 0, -150, [117], 118]], [0, "946jU2IbZNnL7eJU+c0HE3", 1], [5, 170, 66], [-409.7, 324, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "layoutButtons", 3, [-158, -159, -160, -161, -162], [[22, 1, 1, 60, true, -157, [5, 780, 60]]], [0, "ebNo2CC/ZK4JgNjPhq8YX2", 1], [5, 780, 60], [0, 0, 0.5], [-158, -310, 0, 0, 0, 0, 1, 1, 1, 1]], [71, "item-horizontal", [-164, -165, -166, -167], [[57, 1, 1, -163, [5, 279.39, 33]]], [0, "4dmOBCAblItKpzDrK69jlK", 1], [5, 279.39, 33], [0, 0, 0.5]], [2, "1k", 9, [-171, -172], [[5, -168, [12], 13], [13, 3, -170, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "1000", 1]], [4, 4292269782], -169, 14]], [0, "24sXg6ofNP04DwVTU5Kz00", 1], [5, 136, 136], [-202, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "2k", 200, 9, [-176, -177], [[5, -173, [19], 20], [13, 3, -175, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "2000", 1]], [4, 4292269782], -174, 21]], [0, "d9ecH9S+FGJ7Zkp2IOfq2X", 1], [5, 136, 136], [-6, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "5k", 9, [-181, -182], [[5, -178, [26], 27], [13, 3, -180, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "5000", 1]], [4, 4292269782], -179, 28]], [0, "38KEsrGstIMaSFX1vfVWse", 1], [5, 136, 136], [190, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "10k", 9, [-186, -187], [[5, -183, [33], 34], [13, 3, -185, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "10000", 1]], [4, 4292269782], -184, 35]], [0, "baUjiWtr1GS5HE8nvQ9QbG", 1], [5, 136, 136], [-202, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "20K", 9, [-191, -192], [[5, -188, [40], 41], [13, 3, -190, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "20000", 1]], [4, 4292269782], -189, 42]], [0, "064J9OToRHIKSnXSIPaPks", 1], [5, 136, 136], [-6, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "50K", 200, 9, [-196, -197], [[5, -193, [47], 48], [13, 3, -195, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "50000", 1]], [4, 4292269782], -194, 49]], [0, "7dzeT5TVVK2oVjSKzDHd3i", 1], [5, 136, 136], [190, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "100K", 200, 9, [-201, -202], [[5, -198, [54], 55], [13, 3, -200, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "100000", 1]], [4, 4292269782], -199, 56]], [0, "5b6gZA2lBGG59UuYWEAznR", 1], [5, 136, 136], [-202, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "200k", 200, 9, [-206, -207], [[5, -203, [61], 62], [13, 3, -205, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "200000", 1]], [4, 4292269782], -204, 63]], [0, "59XDrBc6FAA5YJSP9uXK3R", 1], [5, 136, 136], [-6, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "500K", 200, 9, [-211, -212], [[5, -208, [68], 69], [13, 3, -210, [[14, "aecb3P72plJupyHv+8j1L7I", "setBetRoom", "500000", 1]], [4, 4292269782], -209, 70]], [0, "00Y7Kxq3dLNZ8Rc1Y0QSFt", 1], [5, 136, 136], [190, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [72, "item", [-218, 15], [[23, 1, 2, 5, -213, [5, -5, 33]], [117, -217, 15, -216, -215, -214]], [0, "43UPQai71NWLe3tdZJaHzO", 1], [5, -5, 33], [0, 0, 0.5], [-170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "scrollview", 11, [-221, -222], [-219, -220], [0, "c84apqRtlLJ7w/c42qZyyx", 1], [5, 360, 270], [-7, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [91, "editbox-chat", [-224, -225, -226], [-223], [0, "efnHR4MFtAmKVbDPkTP0Sh", 1], [5, 235, 30], [-43, 4.5, 0, 0, 0, 0, 1, 1, 1, 1]], [92, "scrollviewRanks", [12, -229], [-227, -228], [0, "95RGPJfTZLIKzz70yfmStr", 1], [5, 240, 350], [0, 0.5, 1], [0, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "audioPool", 2, [-234, -235, -236], [[118, -233, -232, -231, -230]], [0, "34CpDceTRNeYOOGCoqYBOo", 1]], [49, "user-pos", 3, [4, 6, 7, 8], [0, "a8vufGX9lOM4bMPJuRTWcp", 1], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "offset-message", false, 0, [-238, -239], [[105, false, 768, 2, false, -237]], [0, "daW2CvibJHEYvjmG0H2nfm", 1], [5, 451, 80]], [39, "scrollview", false, 11, [-243], [[59, false, 0.75, 0.23, null, null, -241, -240], [60, -242]], [0, "2dyhzEQHRMOrxdfvI4HC9H", 1], [5, 360, 270], [0, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "btnSend", 11, [-245], [-244], [0, "3e0TTVdEFAMY3o1QnS8U0P", 1], [5, 80, 70], [108, -200.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 10, [[6, 2, -246, [96], 97], [113, 3, -248, [[9, "6d778KTeFpKA4z4LqE+Fin1", "createHelpView", 10]], [4, 4292269782], -247]], [0, "5eyiqm55RKJYMVSEZmgAQk", 1], [5, 51, 51], [326, 199, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 3, [-251], [[44, 0.9, 3, -250, [[9, "aa0ebuePJNGjaJ+PGuzW9E2", "backClicked", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -249]], [0, "3cnmzpqsdLUosO0Yvsxhnt", 1], [5, 100, 96], [-589, 312, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnSound", 3, [-254], [[44, 0.9, 3, -253, [[9, "aa0ebuePJNGjaJ+PGuzW9E2", "soundClicked", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -252]], [0, "859PX53QhM6ISg8HTD4wTZ", 1], [5, 80, 70], [588, 311.5, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "nodeStatus", false, 3, [-255, -256, -257], [0, "4dehIT1EVAI69DvShvlPOt", 1], [0, 97, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 4, [-259, -260], [[5, -258, [132], 133]], [0, "d97AMsDCRPZb1NjhZb2jvN", 1], [5, 121, 51], [-4.1, -84.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "layoutName", 38, [-262, -263], [[23, 1, 1, 2, -261, [5, 89.8, 20]]], [0, "7d/QMR8wlLvrj+c1/xn19A", 1], [5, 89.8, 20], [4, 13.2, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "cards_user1", false, 5, [-265], [[24, -264, 166]], [0, "40O5HLZiBGs7WTWOvso2ip", 1], [5, 49, 71], [-424, -209, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "layoutCard_user1", 5, [[22, 1, 1, -20, true, -266, [5, 20, 120]], [10, -267, [147]]], [0, "9e3qfBa/5PiIHOFk5tU5RA", 1], [5, 20, 120], [0, 0, 0.5], [-438, -199, 0, 0, 0, 0, 1, 1, 1, 1]], [51, "cards_user2", false, 5, [-269], [[24, -268, 149]], [0, "b5EctEj81HHLcVLd01n4dO", 1], [4, 4288322204], [5, 56, 81], [-437, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "layoutCard_user2", 5, [-271], [[22, 1, 1, -55, true, -270, [5, 55, 120]]], [0, "8fa4D1PopJvYcfFcmqqMzN", 1], [5, 55, 120], [0, 0, 0.5], [-464.3, 3, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [11, "item1 copy", false, 43, [[12, 1, 0, -272, 150], [45, 0, 0, -273], [10, -274, [151, 152]]], [0, "81cmZj+UhI/6QfyVaxRB3j", 1], [5, 107, 153], [45.***************, 0, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [74, "cards_user3", 5, [-276], [[5, -275, [155], 156]], [0, "f2p98GeR5GuqVkoYjpMKfy", 1], [4, 4288322204], [5, 56, 81], [160.7, 275, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "layoutCard_user3", 5, [-278], [[22, 1, 1, -55, true, -277, [5, 55, 120]]], [0, "c9jPmWOLtAQ68ZXv0FKoQW", 1], [5, 55, 120], [0, 1, 0.5], [176, 277, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [11, "item1 copy", false, 46, [[12, 1, 0, -279, 157], [45, 0, 0, -280], [10, -281, [158, 159]]], [0, "00P2gaLN1B2b524ugh72ND", 1], [5, 107, 153], [45.***************, -0.7, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [51, "cards_user4", false, 5, [-283], [[24, -282, 161]], [0, "11ruUoXgtDwbYSmV/kQXxr", 1], [4, 4288322204], [5, 56, 81], [430.5, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "layoutCard_user4", 5, [-285], [[22, 1, 1, -55, true, -284, [5, 55, 120]]], [0, "7fmyE8BE5IX74LA9FFNj6T", 1], [5, 55, 120], [0, 1, 0.5], [460, 5, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [11, "item1 copy", false, 49, [[12, 1, 0, -286, 162], [45, 0, 0, -287], [10, -288, [163, 164]]], [0, "aa5jp5MgxEtq/QciB9MHxw", 1], [5, 107, 153], [-45.475, 0, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [2, "ava_money", 6, [-290, -291], [[5, -289, [176], 177]], [0, "26NMHQbD9EbIxGiPcq4KpE", 1], [5, 121, 51], [-2.4, -69.7, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "layoutName", 51, [-293, -294], [[23, 1, 1, 2, -292, [5, 89.8, 20]]], [0, "7aBRC4drRNf47CyblMdNlt", 1], [5, 89.8, 20], [5.4, 14.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 7, [-296, -297], [[5, -295, [201], 202]], [0, "46JpLuU65Mupykl6ckYwg9", 1], [5, 121, 51], [-2, -70, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "layoutName", 53, [-299, -300], [[23, 1, 1, 2, -298, [5, 89.8, 20]]], [0, "48iouVv5RKyZjP0aGEBYRp", 1], [5, 89.8, 20], [3.9, 9.8, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 8, [-302, -303], [[5, -301, [227], 228]], [0, "22GbSfMPtIs7miIa5Lljiz", 1], [5, 121, 51], [0, -71.9, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "layoutName", 55, [-305, -306], [[23, 1, 1, 2, -304, [5, 89.8, 20]]], [0, "20rxmJEGhNWolTdUUCazjz", 1], [5, 89.8, 20], [4.6, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "bgLeaveRoom", false, 3, [-308], [[12, 1, 0, -307, 243]], [0, "f7MoyWup9Npo1k9lub7Pas", 1], [5, 200, 40], [-538.7, 267, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnChat", 3, [-311], [[44, 0.9, 3, -310, [[9, "aecb3P72plJupyHv+8j1L7I", "chatClicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -309]], [0, "a7avbnCmlALZrQFWzECBJl", 1], [5, 80, 70], [-416.2, -320.9, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "btnBatDau", false, 14, [[114, 3, -313, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onStartGame", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -312, 254], [12, 1, 0, -314, 255]], [0, "e9m/P6+elEgLwXuvRf/8Qg", 1], [5, 150, 63], [95, 302, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "popupSlotsView", 2, [31], [[10, -315, [256]], [120, 0, -317, 31, -316]], [0, "7eZfEEPIhDZL5J/AV9SYek", 1], [5, 444, 220]], [47, "bg", 10, [-318, -319], [0, "1fP1mYv4xJXJalFVT638Dg", 1]], [1, "bg", 61, [[6, 0, -320, [0], 1], [61, -321]], [0, "60GaFT38BPXrsDeD2hleG4", 1], [5, 1561, 720], [0, 5.684341886080802e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icoBack@2x", 61, [[5, -322, [2], 3], [115, -323, [[9, "aecb3P72plJupyHv+8j1L7I", "<PERSON><PERSON><PERSON><PERSON>", 1]]]], [0, "cckDrESLVK2rVA5CyZrttO", 1], [5, 36, 53], [-600.355, 235.964, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "gameName", 10, [-325], [[6, 0, -324, [6], 7]], [0, "61piOxvjxIpoToF2AEYl1c", 1], [5, 403, 40], [39, 198, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_popup_chat", 11, [[25, 1, 0, false, -326, [71], 72], [60, -327]], [0, "f6C0b1MUJFYrm8yNCnEeew", 1], [5, 358, 495], [-9.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "view", 26, [-329], [[62, 0, -328, [77]]], [0, "dc9gRjrmZP+J0OP47s8qmk", 1], [5, 340, 380], [-6, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "content", 66, [[57, 1, 2, -330, [5, 340, 0]]], [0, "02NE1cxZxPZ4DJDP89hVjl", 1], [5, 340, 0], [0, 0.5, 1], [0, 131, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "view", 32, [-332], [[121, 0, -331]], [0, "d0IgGapUlBA51tdfdtGtlt", 1], [5, 360, 270], [0, 0.5, 1], [0, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "rtChat", 68, [[122, false, "", 22, 345, 33, false, -333, 78]], [0, "4czziff0ZG34mvZR3k8epf", 1], [5, 345, 33], [0, 0, 1], [-173, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "textbox_chat", 11, [27], [[18, 1, 0, -334, [81], 82]], [0, "02aRY/2WhDmIRhhqJbtXZI", 1], [5, 340, 60], [-12, -200.7, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "nodeRanks", 10, [-337], [[124, -336, -335]], [0, "79jMf3fsZBX5RSr8qC0+Fg", 1], [490, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "bg_bxh", 71, [28], [[5, -338, [94], 95]], [0, "209t5b2Y1Jb7KUl7D12R/x", 1], [5, 287, 459]], [11, "avatar", false, 12, [[12, 1, 0, -339, 86], [26, -340]], [0, "85bL+WP0VB+6DRHyNCPm8Z", 1], [5, 37, 37], [-44.2, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [93, "lbMoney", 12, [-342], [-341], [0, "a1EzbD+zJIXKrwM1f+oMhZ", 1], [4, 4278255612], [5, 104.5, 40], [0, 0, 0.5], [-85, -8.5, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "view", 28, [-344], [[62, 0, -343, [93]]], [0, "19FaXtTX1FXYHlHAzTQl+A", 1], [5, 240, 350], [0, 0.5, 1]], [3, "bg", 3, [[6, 0, -345, [99], 100], [61, -346]], [0, "e5dpm4UxxA8I/7267ybeaR", 1], [5, 1560, 723]], [2, "table", 3, [-348], [[5, -347, [103], 104]], [0, "e8oQEDbKBJQZ9z968UirVi", 1], [5, 1118, 490], [0, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [81, "lbStatus", 37, [[-349, [10, -350, [120]]], 1, 4], [0, "a8+pwRX19GCahYN9WZkYI3", 1], [5, 178.2, 24], [-2, 34.1, 0, 0, 0, 0, 1, 1, 1, 1]], [82, "lbPlayerStatus", false, 37, [[-351, [10, -352, [121]]], 1, 4], [0, "28Vw6dua9Gq6+5WzAFJg6E", 1], [5, 0, 18], [-2, -115, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Avatar", 4, [[25, 1, 0, false, -353, [122], 123], [26, -354]], [0, "78As/9PEdIibR6JuntJ3Er", 1], [4, 4293322470], [5, 120, 120]], [40, "timeprogress", 4, [[-355, [33, 2, -357, -356]], 1, 4], [0, "a0uPpa4kBMjYqpU/DCAGMV", 1], [5, 100, 100]], [1, "lbChip", 38, [[4, "10.M", 18, false, 1, 1, -358, [130], 131], [35, -359]], [0, "82RFMd8K9J1qLRyMhdMZtn", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbWin", false, 4, [[7, "+100.000", 20, false, 1, 1, -360, 139], [30, true, -361, [141], 140]], [0, "a0KJ+Dt8hAqKLTce4lpPSK", 1], [5, 153.13, 25], [0, 0.5, 0.500000000000002], [0, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "chat", false, 4, [-362, -363], [0, "a6xYkIJidJoq6pZ6SdSNWP", 1]], [2, "bubble", 84, [-365], [[12, 1, 0, -364, 144]], [0, "21X35+05hLwasyAnqIyssX", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [76, "layoutCardGame", 5, [0, "abLmNDf3VP7pUwV6yrtCtw", 1], [5, 200, 100]], [8, "Avatar", 6, [[25, 1, 0, false, -366, [167], 168], [26, -367]], [0, "f25HXb2yVBcqt4FLl1INqz", 1], [4, 4293322470], [5, 90, 90]], [83, "timeprogress", false, 6, [[-368, [33, 2, -370, -369]], 1, 4], [0, "c6cAuPmOpIZrueXBG/gslZ", 1], [5, 90, 90]], [1, "lbChip", 51, [[4, "10.M", 18, false, 1, 1, -371, [174], 175], [35, -372]], [0, "6aJSxD47NOlpVEKJfy/Jhf", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbWin", false, 6, [[7, "+100.000", 20, false, 1, 1, -373, 183], [30, true, -374, [185], 184]], [0, "d3cNckDixFpoYVp81kkiDu", 1], [5, 153.13, 25], [0, 0.5, 0.500000000000002], [0, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "chat", false, 6, [-375, -376], [0, "baUvJU+u9CbbNAweZ2qQay", 1]], [2, "bubble", 91, [-378], [[12, 1, 0, -377, 188]], [0, "d6jC5JBjBIH5hex7JqsmSt", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Avatar", 7, [[25, 1, 0, false, -379, [191], 192], [26, -380]], [0, "06fo3WJ5VGnIrILcPdfbMh", 1], [4, 4293322470], [5, 90, 90]], [40, "timeprogress", 7, [[-381, [33, 2, -383, -382]], 1, 4], [0, "12mjzDRaJHJp7oqe/L3BeA", 1], [5, 90, 90]], [1, "lbChip", 53, [[4, "10.M", 18, false, 1, 1, -384, [199], 200], [35, -385]], [0, "c9PvU1KepC45cc9aHy7iDD", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "lbWin", 7, [[4, "+100.000", 20, false, 1, 1, -386, [208], 209], [30, true, -387, [211], 210]], [0, "3e8FwhqT9NPawLb6LffUR/", 1], [5, 153.13, 40], [0, 0.5, 0.500000000000002], [138.5, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "chat", false, 7, [-388, -389], [0, "ffVwXwWzVMI4WhxGUp4f9P", 1]], [2, "bubble", 97, [-391], [[12, 1, 0, -390, 214]], [0, "2fxgJQe55JiKX+ZeYmfbp4", 1], [5, 192, 65], [106.6, 29.9, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Avatar", 8, [[25, 1, 0, false, -392, [217], 218], [26, -393]], [0, "0eNUrIYTBNZYVaV1R6cvy6", 1], [4, 4293322470], [5, 90, 90]], [40, "timeprogress", 8, [[-394, [33, 2, -396, -395]], 1, 4], [0, "4cOb7uas9OoKumjhfYQrI5", 1], [5, 90, 90]], [1, "lbChip", 55, [[4, "10.M", 18, false, 1, 1, -397, [225], 226], [35, -398]], [0, "88SicwcXNAQYoEYebSl5+/", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbWin", false, 8, [[7, "+100.000", 20, false, 1, 1, -399, 234], [30, true, -400, [236], 235]], [0, "b9YEICT2dFU70egOsEPlSJ", 1], [5, 153.13, 25], [0, 0.5, 0.500000000000002], [0, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "chat", false, 8, [-401, -402], [0, "52cCcfmYhKp7cjeoWDm5rO", 1]], [2, "bubble", 103, [-404], [[12, 1, 0, -403, 239]], [0, "cczudwwM1NtYiiMR6eMZpP", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "btnBoLuot", 14, [-406], [-405], [0, "dfaqp2J0VOH6NWCULtXg1X", 1], [5, 150, 60], [75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 105, [[18, 1, 0, -407, [246], 247]], [0, "52m/oNLL1PWaEiOG2KPImJ", 1], [4, 4293322470], [5, 150, 60]], [17, "btnBoChon", 14, [-409], [-408], [0, "96PY44jtVDpLYp3S2CMzPT", 1], [5, 150, 60], [285, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 107, [[18, 1, 0, -410, [248], 249]], [0, "10yD13oJRLPL2VxINGpiwG", 1], [4, 4293322470], [5, 150, 60]], [17, "btnXepBai", 14, [-412], [-411], [0, "73zDh/wt9BHoxDm/QZZ8et", 1], [5, 150, 60], [495, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 109, [[18, 1, 0, -413, [250], 251]], [0, "6bup6X3HRMG4+7IQmWPNYj", 1], [4, 4293322470], [5, 150, 60]], [17, "btnDanhBai", 14, [-415], [-414], [0, "78d3nzxHdA0LQee97K3c/n", 1], [5, 150, 60], [705, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Background", 111, [[18, 1, 0, -416, [252], 253]], [0, "84eiby0wJNNrtoKYl1gKHz", 1], [4, 4293322470], [5, 150, 60]], [1, "name", 64, [[4, "TIẾN LÊN MIỀN NAM SOLO", 34, false, 1, 1, -417, [4], 5]], [0, "33vuAU1ARG/qOLiUh0gwn1", 1], [5, 420.75, 40], [-12, 4.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 16, [[6, 0, -418, [8], 9]], [0, "f4eKKAHQ9IB47mCBoqSpsN", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 16, [[4, "1K", 25, false, 1, 1, -419, [10], 11]], [0, "c4ZDGbzbJOCZBV16NTH9u6", 1], [5, 46.09, 40]], [1, "chip_demo", 17, [[6, 0, -420, [15], 16]], [0, "c9PBFeRtVGmI8mcpM1rPNK", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 17, [[4, "2K", 25, false, 1, 1, -421, [17], 18]], [0, "1chZ39T8lG2oIe3DEBDneM", 1], [5, 54.69, 40]], [1, "chip_demo", 18, [[6, 0, -422, [22], 23]], [0, "f4R0sxldtLw7mVEC0uHyz1", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 18, [[4, "5K", 25, false, 1, 1, -423, [24], 25]], [0, "e8G2in78VNgYkyOF721pjq", 1], [5, 56.25, 40]], [1, "chip_demo", 19, [[6, 0, -424, [29], 30]], [0, "63TnXskbhNPoDRfrow5j+h", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 19, [[4, "10K", 25, false, 1, 1, -425, [31], 32]], [0, "57LCgZT3RDA5YxbSY4qW8l", 1], [5, 73.44, 40]], [1, "chip_demo", 20, [[6, 0, -426, [36], 37]], [0, "4218YmiBZIBo3rOYCd03ZK", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 20, [[4, "20K", 25, false, 1, 1, -427, [38], 39]], [0, "41fjuee2JB155gjfRFsA3b", 1], [5, 82.03, 40]], [1, "chip_demo", 21, [[6, 0, -428, [43], 44]], [0, "0b7nHbmbtB/bS+PPqeFL6D", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 21, [[4, "50K", 25, false, 1, 1, -429, [45], 46]], [0, "e9tU2XstFO87JZhqVqOBon", 1], [5, 83.59, 40]], [1, "chip_demo", 22, [[6, 0, -430, [50], 51]], [0, "98MUUd15ZCRo+SUu6u0gYE", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 22, [[4, "100K", 25, false, 1, 1, -431, [52], 53]], [0, "adQzyWJ2FFy49SEqAR1T+n", 1], [5, 100.78, 40]], [1, "chip_demo", 23, [[6, 0, -432, [57], 58]], [0, "3ebgpRpAVFYoCLUU7iRdVh", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 23, [[4, "200K", 25, false, 1, 1, -433, [59], 60]], [0, "30IHpmRMZNtqss3ywaqb+b", 1], [5, 109.38, 40]], [1, "chip_demo", 24, [[6, 0, -434, [64], 65]], [0, "1cNUO9BhNID7OyyfOuBExU", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbRoomValue", 24, [[4, "500K", 25, false, 1, 1, -435, [66], 67]], [0, "22Fpzgue9KsafBmbARjovN", 1], [5, 110.94, 40]], [49, "temp", 26, [25], [0, "a5mJQFUcVACLeI7p+5BNk6", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "rtChat", false, 25, [-436], [0, "112VTB+TxJZqjiI44C5JST", 1], [5, 345, 33], [0, 0, 0.5], [0, 16.5, 0, 0, 0, 0, 1, 1, 1, 1]], [123, false, "", 20, 345, 33, false, 133], [77, "lbSID", false, 15, [[125, "[TQ]", 18, 48, false, false, 1, 1, -437, 73]], [0, "d5ZWg0+GtB/qU8dKv5Ub47", 1], [4, 4279026733], [5, 33.3, 21.6], [0, 0, 0.5]], [85, "lbNickName", 15, [-438], [0, "3dxMcguSxO7J8q3ZaAJje9", 1], [4, 4281523194], [5, 95.39, 60.48], [0, 0, 0.5]], [126, 19, 48, false, false, 1, 1, 136, [74]], [11, "V1", false, 15, [[24, -439, 75]], [0, "8d/6LkKepMdL0hD+sxa4os", 1], [5, 30, 28], [133.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbMessage", 15, [-440], [0, "6e6/OouAVJL4KZY99DvVch", 1], [5, 184, 24], [0, 0, 0.5], [95.39, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [127, 19, 48, false, false, 1, 1, 139, [76]], [119, false, 26, 67], [133, 15, 400, 26, 25, 141], [55, "BACKGROUND_SPRITE", 27, [-441], [0, "22g0XDfqNNwJHyKYEVK9I5", 1], [5, 235, 30]], [106, 1, 0, 143, [79]], [41, "TEXT_LABEL", false, 27, [-442], [0, "fbQqqCJ6NAO48YZa2mGZ01", 1], [5, 233, 30], [0, 0, 1], [-115.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [128, 20, 30, false, false, 1, 1, 145], [86, "PLACEHOLDER_LABEL", 27, [-443], [0, "8dCX2zBs1CaZO/b6s8uuvC", 1], [4, 4290493371], [5, 233, 30], [0, 0, 1], [-115.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [129, "<PERSON><PERSON>i dung tin nhắn ...", 20, 30, false, false, 1, 1, 147, [80]], [134, 255, 6, 27, [[9, "c9ea2HJ+4FBwJf8JdBQBbUQ", "editingReturn", 11]], 146, 148, 144], [1, "sprite", 33, [[6, 2, -444, [83], 84]], [0, "ab0u5bM1NCpqD80Wdzu5DL", 1], [5, 82, 46], [-2.6, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [116, 1.1, false, 3, 33, [[9, "c9ea2HJ+4FBwJf8JdBQBbUQ", "sendChatClicked", 11]], [4, 4294967295], [4, 4294967295], 33], [41, "iconRank", false, 12, [-445], [0, "771eSyfFJGwJh0AEYAFolk", 1], [5, 36, 29], [0, 0, 0.5], [-113, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [107, 152], [56, "lbRank", 12, [-446], [0, "41yjfBnWxOt44gxDG+z8TY", 1], [5, 15, 40], [-108.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "0", 25, false, 1, 1, 154, [85]], [87, "lbSID", false, 12, [-447], [0, "83/ZKObnJJB6fZQou+NEd9", 1], [4, 4280264453], [5, 32.73, 17], [0, 0, 0.5], [-86.1, 13.4, 0, 0, 0, 0, 1, 1, 1, 1]], [63, "[TQ] ", 17, false, 1, 1, 156], [21, "lbNickName", 12, [-448], [0, "0eBMoIocNKCrggQbIDxa1G", 1], [5, 170, 17], [0, 0, 0.5], [-85, 13.4, 0, 0, 0, 0, 1, 1, 1, 1]], [130, "900MMMM99009009", 17, 17, false, false, 2, 158, [87]], [16, "chip_demo", false, 74, [[108, 0, -449, 88]], [0, "affaa7BYlPOp3nDAa4Mtp3", 1], [5, 25, 25], [0, 1, 0.5], [-0.9, -3.8, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "90.000.000", 20, false, 1, 1, 74, [89]], [78, "content", 75, [0, "1faWAI1PND5Zc6tc3qAq3W", 1], [5, 240, 350], [0, 0.5, 1]], [59, false, 0.75, 0.23, null, null, 28, 162], [135, 5, 100, 28, 12, 163], [42, "musicBackground", 29, [-450], [0, "50I32yCjpCJJnmYbsHdT7N", 1]], [136, 0.2, true, true, 165], [42, "chipBet", 29, [-451], [0, "579HUyj6VA1YnSHIXBOb6V", 1]], [64, true, 167], [42, "moveCard", 29, [-452], [0, "fc//bIDdVPp5MMlaWvP5m/", 1]], [64, true, 169], [1, "nameBC", 77, [[5, -453, [101], 102]], [0, "d01Blaq+VD5pYVrTM2hHAI", 1], [5, 442, 85], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "table", 3, [[109, false, -454, [105]]], [0, "a348hHXNFGPqodunHlmxDn", 1], [5, 967, 573], [0, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [88, "bgBack", 35, [-455], [0, "f2ja3o5R9Ht4edj2gNtL/l", 1], [4, 4293322470], [5, 36, 53], [0, 11.6, 0, 0, 0, 0, 1, 1, 1, 1]], [58, 2, false, 173, [106]], [89, "bgSound", 36, [-456], [0, "b1hatC1XFFGphi2oUm1Qar", 1], [4, 4293322470], [5, 81, 82]], [58, 2, false, 175, [107]], [15, "lbTable", 13, [[4, "Bàn", 17, false, 1, 1, -457, [108], 109]], [0, "86aquWJh5CBZzweh/A5/nX", 1], [5, 31.02, 40], [0, 0, 0.5], [-64.9, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbTableValue", 13, [-458], [0, "d3ZZynju9AdZIUEkG7A7Sf", 1], [5, 39.1, 40], [0, 0, 0.5], [-9, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [27, ": 012", 17, false, 1, 1, 178, [110]], [15, "lbRoomID", 13, [[4, "Phòng", 17, false, 1, 1, -459, [111], 112]], [0, "c8UjJGDpZLS7IEqXD7iM1x", 1], [5, 50.58, 40], [0, 0, 0.5], [-64.2, 2.9, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbRoomIDValue", 13, [-460], [0, "05uOzuo/hCqJyP28cD27NY", 1], [5, 53.98, 40], [0, 0, 0.5], [-9.1, 3.3, 0, 0, 0, 0, 1, 1, 1, 1]], [27, ": 1.000", 17, false, 1, 1, 181, [113]], [15, "lbSID", 13, [[4, "<PERSON><PERSON><PERSON>", 17, false, 1, 1, -461, [114], 115]], [0, "42slCXMLtJTJhth36w8GYg", 1], [5, 45.05, 40], [0, 0, 0.5], [-64.8, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbSIDValue", 13, [-462], [0, "6eEhBrdjtI+51A/E3A2Ivk", 1], [5, 78.62, 40], [0, 0, 0.5], [-8.7, -17.8, 0, 0, 0, 0, 1, 1, 1, 1]], [27, ": #123456", 17, false, 1, 1, 184, [116]], [1, "bg_noti", 37, [[24, -463, 119]], [0, "82ciH34QJF9awy2C10kQ5U", 1], [5, 274, 49], [0, 32.8, 0, 0, 0, 0, 1, 1, 1, 1]], [63, "CHO PHIEN MOI", 24, false, 1, 1, 78], [131, 18, false, 1, 1, 79], [43, 3, 0, 2, 0.5, 1, 81, [124], [0, 0.5, 0.5]], [3, "ava_sheld", 4, [[6, 0, -464, [125], 126]], [0, "a7muzpgPBCzqqm/ofFn2lH", 1], [5, 120, 120]], [29, "lbSID", false, 39, [[7, "[TQ]", 16, false, 1, 1, -465, 127]], [0, "322pPg0mRKWZUwqAt1l3NL", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbName", 39, [[4, "Academy...", 18, false, 1, 1, -466, [128], 129]], [0, "d6x2U+ZepCIYizBEhLj9jP", 1], [4, 4284012543], [5, 89.8, 40]], [1, "nodeOut", 4, [[5, -467, [134], 135]], [0, "4elj33gWtMHLkm0S9sMVhb", 1], [5, 36, 39], [54.4, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "notify", false, 4, [[31, 1, 0, -468]], [0, "81Q1ibKHZOS4sEhW/NE3O3", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [79, "lbResult", false, 0, 4, [[7, "<PERSON> đôi thông", 25, false, 1, 1, -469, 136]], [0, "bePoet16BKNbzHSXHAU4E9", 1], [5, 144.38, 25], [0, 0, 0.5], [19, -14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 4, [[36, 0, false, "tquy", -470, [137], 138]], [0, "98klPsqr1MgIBvLNG9CqaO", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [3, "emotion", 84, [[37, "1-waaaht", 0, false, "1-waaaht", -471, 142]], [0, "36mkc3vhtK5pRcYrcSqK8l", 1], [5, 123, 110]], [8, "lbChat", 85, [[34, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -472, 143]], [0, "69z0/Dy+1AcYdcwhoWQ9TX", 1], [4, 4278190080], [5, 172.4, 55]], [38, 4, 40, 41], [1, "lbNumberCard", 42, [[7, "13", 30, false, 1, 1, -473, 148]], [0, "4bAKzaWXlGA5xg1vJ9yKwL", 1], [5, 35.25, 30], [-3.3, 2.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNumberCard", 45, [[4, "13", 30, false, 1, 1, -474, [153], 154]], [0, "91sIcZXZhCH6RCjPThh/cX", 1], [5, 35.25, 40], [-2.8, 2.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNumberCard", 48, [[7, "13", 30, false, 1, 1, -475, 160]], [0, "18gD9Xwp1Duo9YWhZIEGpY", 1], [5, 35.25, 30], [-2.3, 2.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbNumberCard", 40, [[7, "13", 18, false, 1, 1, -476, 165]], [0, "00YlL5UxJAwpQ+OmnF0h3a", 1], [5, 21.15, 18], [-1.2, 1.9, 0, 0, 0, 0, 1, 1, 1, 1]], [110, 3, 0, 2, 0.5, 1, 88, [0, 0.5, 0.5]], [3, "ava_sheld", 6, [[6, 0, -477, [169], 170]], [0, "1aXcGgzalLdqSpB3YoRfSK", 1], [5, 90, 90]], [29, "lbSID", false, 52, [[7, "[TQ]", 16, false, 1, 1, -478, 171]], [0, "d0BmCNHvFPTpnVLUIgY0sz", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbName", 52, [[4, "Academy...", 18, false, 1, 1, -479, [172], 173]], [0, "f3J8oUlA1JtKGF5cTyhdKE", 1], [5, 89.8, 40]], [1, "nodeOut", 6, [[5, -480, [178], 179]], [0, "a9aqoU8ZBMU66vQYN5QjGJ", 1], [5, 36, 39], [4.6, 39.6, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbResult", false, 6, [[7, "<PERSON> đôi thông", 23, false, 1, 1, -481, 180]], [0, "3eg0Wcyq5HnJJOW/GWq0uS", 1], [5, 132.82, 23], [0, 0, 0.5], [105, -83.7, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "notify", false, 6, [[31, 1, 0, -482]], [0, "ebiLVo0XVFaqNsHsi/RAUq", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 6, [[36, 0, false, "animation", -483, [181], 182]], [0, "6279F0Pg9ACL16TsMMUITy", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [3, "emotion", 91, [[37, "1-waaaht", 0, false, "1-waaaht", -484, 186]], [0, "b0RdJ/+5ZPFZ/h3eDwCFDN", 1], [5, 123, 110]], [8, "lbChat", 92, [[34, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -485, 187]], [0, "7dOF3r/NRMCaXxrAZ2nw5q", 1], [4, 4278190080], [5, 172.4, 55]], [38, 6, 42, 43], [43, 3, 0, 2, 0.5, 1, 94, [193], [0, 0.5, 0.5]], [3, "ava_sheld", 7, [[6, 0, -486, [194], 195]], [0, "32Ta0JtcZOPKXSZF5wvMUM", 1], [5, 90, 90]], [29, "lbSID", false, 54, [[7, "[TQ]", 16, false, 1, 1, -487, 196]], [0, "69ONUzkrJFH6z47R0B6c5I", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "lbName", 54, [[4, "Academy...", 18, false, 1, 1, -488, [197], 198]], [0, "b1q7cJRXdJDqP4O4rveE/H", 1], [4, 4284012543], [5, 89.8, 40]], [1, "nodeOut", 7, [[5, -489, [203], 204]], [0, "b13UylPkxKR6nhLDxwBLcI", 1], [5, 36, 39], [0.2, 40.5, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbResult", false, 7, [[7, "<PERSON> đôi thông", 23, false, 1, 1, -490, 205]], [0, "aefRCqGCJArqmsq6U9V14I", 1], [5, 132.82, 23], [0, 1, 0.5], [-87, -135, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "notify", false, 7, [[31, 1, 0, -491]], [0, "74XCshJLVGkJ8CJst/vWc7", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 7, [[36, 0, false, "animation", -492, [206], 207]], [0, "7dVy3hjWhGgKU3tZdbJN6r", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [3, "emotion", 97, [[37, "1-waaaht", 0, false, "1-waaaht", -493, 212]], [0, "1c0NFxVyZH8KvR0hLyxDzH", 1], [5, 123, 110]], [8, "lbChat", 98, [[34, "Ah", 30, 50, false, 1, 1, 2, -494, 213]], [0, "a3xRWui6RNw6g3W/XzAx8o", 1], [4, 4278190080], [5, 172.4, 55]], [38, 7, 45, 46], [43, 3, 0, 2, 0.5, 1, 100, [219], [0, 0.5, 0.5]], [3, "ava_sheld", 8, [[6, 0, -495, [220], 221]], [0, "6eR7tqc+9Ke6qEkSoWriSM", 1], [5, 90, 90]], [29, "lbSID", false, 56, [[7, "[TQ]", 16, false, 1, 1, -496, 222]], [0, "262v97R/hIwYS7xer3doQH", 1], [4, 4279026733], [5, 30.8, 16], [-45.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbName", 56, [[4, "Academy...", 18, false, 1, 1, -497, [223], 224]], [0, "855lXWjsFAsK6zMBo06EB6", 1], [5, 89.8, 40]], [1, "nodeOut", 8, [[5, -498, [229], 230]], [0, "2eplsb/6pJ45We+nenAe3/", 1], [5, 36, 39], [2.4, 38, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbResult", false, 8, [[7, "<PERSON> đôi thông", 23, false, 1, 1, -499, 231]], [0, "93WXlFgABAIq+4JVqhxQwt", 1], [5, 132.82, 23], [0, 1, 0.5], [-75, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "notify", false, 8, [[31, 1, 0, -500]], [0, "e9a6LF3t9GsYuQCrvzFs46", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "win", 8, [[36, 0, false, "animation", -501, [232], 233]], [0, "26g5qUQtJJXqXEa/qGuHII", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [3, "emotion", 103, [[37, "1-waaaht", 0, false, "1-waaaht", -502, 237]], [0, "838lrk5dxGL5BFAQbt1Ok3", 1], [5, 123, 110]], [8, "lbChat", 104, [[34, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -503, 238]], [0, "4eB9hH8SREvouXBWc+T5ul", 1], [4, 4278190080], [5, 172.4, 55]], [38, 8, 48, 49], [3, "lbSID", 57, [[7, "<PERSON><PERSON><PERSON> ký rời bàn", 20, false, 1, 1, -504, 242]], [0, "cer47MSotDiLsoqmIQBaO3", 1], [5, 144, 20]], [3, "Background", 58, [[111, 2, false, -505, [244], 245]], [0, "c2ReMuQJ5IlpU5fUxKKZOE", 1], [5, 60, 61]], [32, 2, 105, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onBoLuot", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 106], [32, 2, 107, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onBoChon", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 108], [32, 2, 109, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onSortCard", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 110], [32, 2, 111, [[9, "0b7c8vAZhtLbqM8oqzmuWlT", "onBanhBai", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 112], [90, "dealer_xd", false, 2, [-506], [0, "1e/B6UMjJCDahgb8OqLSFo", 1], [5, 151.57, 182.34], [0, 185, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [137, "animation", 0, false, "animation", 243], [80, "parentChat", 2, [0, "87mGZhGWlHZqcvdXrM9rqd", 1]], [50, "nodeNotify", false, 2, [-507], [0, "40/txBfmpLj7XrDTzNOefL", 1], [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [56, "anim", 246, [-508], [0, "0bCWnhh/NBw5n7o8/JeIRk", 1], [5, 408.34, 328], [0, 80, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [138, 0, false, 247], [3, "bg_tex1", 31, [[112, 0, -509]], [0, "01/MDlA61AyKgGw0HBU3FP", 1], [5, 800, 80]], [55, "lbMessage", 31, [-510], [0, "25ybWcfvtAD4Zwd+otp/FX", 1], [5, 700, 45]], [132, "Dat cuoc thanh cong ", 36, 50, false, false, 1, 1, 2, 250]], 0, [0, 18, 1, 0, 19, 245, 0, 20, 2, 0, 21, 10, 0, 0, 1, 0, 22, 248, 0, 0, 1, 0, 0, 1, 0, -1, 10, 0, -2, 2, 0, 23, 57, 0, 24, 174, 0, 25, 176, 0, 14, 86, 0, 0, 2, 0, 26, 86, 0, 14, 5, 0, 0, 2, 0, 27, 242, 0, 28, 241, 0, 29, 240, 0, 30, 239, 0, 31, 14, 0, 0, 2, 0, 32, 244, 0, -1, 199, 0, -2, 225, 0, -3, 214, 0, -4, 236, 0, 33, 188, 0, 34, 187, 0, 35, 179, 0, 36, 182, 0, 15, 185, 0, 0, 2, 0, -1, 29, 0, -2, 3, 0, -3, 243, 0, -4, 245, 0, -5, 246, 0, -6, 60, 0, -1, 76, 0, -2, 77, 0, -3, 172, 0, -4, 35, 0, -5, 36, 0, -6, 13, 0, -7, 37, 0, -8, 30, 0, -9, 5, 0, -10, 57, 0, -11, 58, 0, -12, 14, 0, 0, 4, 0, -2, 199, 0, -1, 80, 0, -2, 81, 0, -3, 190, 0, -4, 38, 0, -5, 193, 0, -6, 194, 0, -7, 195, 0, -8, 196, 0, -9, 83, 0, -10, 84, 0, -1, 41, 0, -2, 40, 0, -3, 42, 0, -4, 43, 0, -5, 45, 0, -6, 46, 0, -7, 48, 0, -8, 49, 0, 4, 5, 0, 4, 5, 0, -11, 86, 0, 0, 6, 0, -2, 214, 0, -1, 87, 0, -2, 88, 0, -3, 205, 0, -4, 51, 0, -5, 208, 0, -6, 209, 0, -7, 210, 0, -8, 211, 0, -9, 90, 0, -10, 91, 0, 0, 7, 0, -2, 225, 0, -1, 93, 0, -2, 94, 0, -3, 216, 0, -4, 53, 0, -5, 219, 0, -6, 220, 0, -7, 221, 0, -8, 222, 0, -9, 96, 0, -10, 97, 0, 0, 8, 0, -2, 236, 0, -1, 99, 0, -2, 100, 0, -3, 227, 0, -4, 55, 0, -5, 230, 0, -6, 231, 0, -7, 232, 0, -8, 233, 0, -9, 102, 0, -10, 103, 0, 0, 9, 0, -1, 16, 0, -2, 17, 0, -3, 18, 0, -4, 19, 0, -5, 20, 0, -6, 21, 0, -7, 22, 0, -8, 23, 0, -9, 24, 0, 0, 10, 0, -1, 61, 0, -2, 64, 0, -4, 11, 0, -5, 71, 0, -6, 34, 0, 37, 151, 0, 38, 149, 0, 39, 142, 0, 0, 11, 0, -1, 65, 0, -2, 26, 0, -3, 32, 0, -4, 70, 0, -5, 33, 0, 40, 161, 0, 41, 159, 0, 15, 157, 0, 42, 155, 0, 43, 153, 0, 0, 12, 0, -1, 152, 0, -2, 154, 0, -3, 73, 0, -4, 156, 0, -5, 158, 0, -6, 74, 0, 0, 13, 0, -1, 177, 0, -2, 178, 0, -3, 180, 0, -4, 181, 0, -5, 183, 0, -6, 184, 0, 0, 14, 0, -1, 105, 0, -2, 107, 0, -3, 109, 0, -4, 111, 0, -5, 59, 0, 0, 15, 0, -1, 135, 0, -2, 136, 0, -3, 138, 0, -4, 139, 0, 0, 16, 0, 3, 16, 0, 0, 16, 0, -1, 114, 0, -2, 115, 0, 0, 17, 0, 3, 17, 0, 0, 17, 0, -1, 116, 0, -2, 117, 0, 0, 18, 0, 3, 18, 0, 0, 18, 0, -1, 118, 0, -2, 119, 0, 0, 19, 0, 3, 19, 0, 0, 19, 0, -1, 120, 0, -2, 121, 0, 0, 20, 0, 3, 20, 0, 0, 20, 0, -1, 122, 0, -2, 123, 0, 0, 21, 0, 3, 21, 0, 0, 21, 0, -1, 124, 0, -2, 125, 0, 0, 22, 0, 3, 22, 0, 0, 22, 0, -1, 126, 0, -2, 127, 0, 0, 23, 0, 3, 23, 0, 0, 23, 0, -1, 128, 0, -2, 129, 0, 0, 24, 0, 3, 24, 0, 0, 24, 0, -1, 130, 0, -2, 131, 0, 0, 25, 0, 44, 134, 0, 16, 140, 0, 45, 137, 0, 0, 25, 0, -1, 133, 0, -1, 141, 0, -2, 142, 0, -1, 132, 0, -2, 66, 0, -1, 149, 0, -1, 143, 0, -2, 145, 0, -3, 147, 0, -1, 163, 0, -2, 164, 0, -2, 75, 0, 46, 168, 0, 47, 170, 0, 48, 166, 0, 0, 29, 0, -1, 165, 0, -2, 167, 0, -3, 169, 0, 0, 31, 0, -1, 249, 0, -2, 250, 0, 49, 69, 0, 0, 32, 0, 0, 32, 0, -1, 68, 0, -1, 151, 0, -1, 150, 0, 0, 34, 0, 3, 34, 0, 0, 34, 0, 3, 35, 0, 0, 35, 0, -1, 173, 0, 3, 36, 0, 0, 36, 0, -1, 175, 0, -1, 186, 0, -2, 78, 0, -3, 79, 0, 0, 38, 0, -1, 39, 0, -2, 82, 0, 0, 39, 0, -1, 191, 0, -2, 192, 0, 0, 40, 0, -1, 203, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, -1, 200, 0, 0, 43, 0, -1, 44, 0, 0, 44, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, -1, 201, 0, 0, 46, 0, -1, 47, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, -1, 202, 0, 0, 49, 0, -1, 50, 0, 0, 50, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, -1, 52, 0, -2, 89, 0, 0, 52, 0, -1, 206, 0, -2, 207, 0, 0, 53, 0, -1, 54, 0, -2, 95, 0, 0, 54, 0, -1, 217, 0, -2, 218, 0, 0, 55, 0, -1, 56, 0, -2, 101, 0, 0, 56, 0, -1, 228, 0, -2, 229, 0, 0, 57, 0, -1, 237, 0, 3, 58, 0, 0, 58, 0, -1, 238, 0, 3, 59, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 16, 251, 0, 0, 60, 0, -1, 62, 0, -2, 63, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, -1, 113, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, -1, 67, 0, 0, 67, 0, 0, 68, 0, -1, 69, 0, 0, 69, 0, 0, 70, 0, 50, 164, 0, 0, 71, 0, -1, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, -1, 161, 0, -1, 160, 0, 0, 75, 0, -1, 162, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, -1, 171, 0, -1, 187, 0, 0, 78, 0, -1, 188, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, -1, 189, 0, 8, 189, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, -1, 197, 0, -2, 85, 0, 0, 85, 0, -1, 198, 0, 0, 87, 0, 0, 87, 0, -1, 204, 0, 8, 204, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, -1, 212, 0, -2, 92, 0, 0, 92, 0, -1, 213, 0, 0, 93, 0, 0, 93, 0, -1, 215, 0, 8, 215, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, -1, 223, 0, -2, 98, 0, 0, 98, 0, -1, 224, 0, 0, 99, 0, 0, 99, 0, -1, 226, 0, 8, 226, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, -1, 234, 0, -2, 104, 0, 0, 104, 0, -1, 235, 0, -1, 239, 0, -1, 106, 0, 0, 106, 0, -1, 240, 0, -1, 108, 0, 0, 108, 0, -1, 241, 0, -1, 110, 0, 0, 110, 0, -1, 242, 0, -1, 112, 0, 0, 112, 0, 0, 113, 0, 0, 114, 0, 0, 115, 0, 0, 116, 0, 0, 117, 0, 0, 118, 0, 0, 119, 0, 0, 120, 0, 0, 121, 0, 0, 122, 0, 0, 123, 0, 0, 124, 0, 0, 125, 0, 0, 126, 0, 0, 127, 0, 0, 128, 0, 0, 129, 0, 0, 130, 0, 0, 131, 0, -1, 134, 0, 0, 135, 0, -1, 137, 0, 0, 138, 0, -1, 140, 0, -1, 144, 0, -1, 146, 0, -1, 148, 0, 0, 150, 0, -1, 153, 0, -1, 155, 0, -1, 157, 0, -1, 159, 0, 0, 160, 0, -1, 166, 0, -1, 168, 0, -1, 170, 0, 0, 171, 0, 0, 172, 0, -1, 174, 0, -1, 176, 0, 0, 177, 0, -1, 179, 0, 0, 180, 0, -1, 182, 0, 0, 183, 0, -1, 185, 0, 0, 186, 0, 0, 190, 0, 0, 191, 0, 0, 192, 0, 0, 193, 0, 0, 194, 0, 0, 195, 0, 0, 196, 0, 0, 197, 0, 0, 198, 0, 0, 200, 0, 0, 201, 0, 0, 202, 0, 0, 203, 0, 0, 205, 0, 0, 206, 0, 0, 207, 0, 0, 208, 0, 0, 209, 0, 0, 210, 0, 0, 211, 0, 0, 212, 0, 0, 213, 0, 0, 216, 0, 0, 217, 0, 0, 218, 0, 0, 219, 0, 0, 220, 0, 0, 221, 0, 0, 222, 0, 0, 223, 0, 0, 224, 0, 0, 227, 0, 0, 228, 0, 0, 229, 0, 0, 230, 0, 0, 231, 0, 0, 232, 0, 0, 233, 0, 0, 234, 0, 0, 235, 0, 0, 237, 0, 0, 238, 0, -1, 244, 0, -1, 247, 0, -1, 248, 0, 0, 249, 0, -1, 251, 0, 51, 1, 4, 4, 30, 6, 4, 30, 7, 4, 30, 8, 4, 30, 9, 4, 10, 12, 4, 28, 15, 4, 25, 25, 4, 132, 27, 4, 70, 28, 4, 72, 31, 4, 60, 510], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 134, 137, 140, 146, 148, 153, 155, 157, 159, 161, 166, 168, 170, 174, 176, 179, 182, 185, 187, 188, 189, 204, 215, 226, 239, 239, 239, 239, 240, 240, 240, 240, 241, 241, 241, 241, 242, 242, 242, 242, 244, 248, 251], [-1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, -1, 2, -1, 1, 6, -1, 1, 2, -1, 1, -1, -1, 17, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -2, -3, -1, -1, 1, -1, 1, 52, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 1, 1, -1, -1, -1, 1, -1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, 2, 9, -1, 5, 2, 1, -1, -2, -1, 2, 1, 1, -1, -2, -1, 2, -1, 1, 1, -1, -2, 2, 1, 1, -1, -2, 2, 1, -1, 1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, 2, 9, -1, 5, 2, 1, -1, -2, -1, 1, -1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, -1, 2, 9, -1, 5, 2, 1, -1, -2, -1, 1, -1, -1, 1, 2, -1, 2, -1, 2, -1, 1, -1, 1, 2, -1, 5, 2, 9, -1, 5, 2, 1, -1, -2, 2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 7, 1, -1, -1, -2, 53, 54, 55, 56, -1, -2, -3, -4, -7, 57, 58, -1, -2, 59, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, 60, 61, 17, 2, 2, 2, 2, 1, 2, 2, 2, 2, 13, 13, 13, 1, 1, 2, 2, 2, 2, 2, 1, 1, 1, 1, 7, 10, 11, 12, 7, 10, 11, 12, 7, 10, 11, 12, 7, 10, 11, 12, 5, 5, 2], [0, 41, 0, 28, 0, 1, 0, 42, 0, 21, 0, 2, 0, 29, 0, 0, 4, 0, 7, 0, 5, 0, 0, 4, 0, 2, 0, 5, 0, 0, 21, 0, 2, 0, 29, 0, 0, 4, 0, 2, 0, 5, 0, 0, 4, 0, 7, 0, 5, 0, 0, 4, 0, 7, 0, 5, 0, 0, 4, 0, 7, 0, 5, 0, 0, 4, 0, 7, 0, 5, 0, 0, 43, 6, 0, 44, 0, 0, 30, 0, 0, 0, 45, 0, 46, 0, 47, 0, 21, 0, 31, 48, 49, 0, 0, 50, 0, 51, 52, 0, 53, 0, 54, 0, 55, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 32, 56, 33, 33, 0, 9, 0, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 2, 3, 3, 14, 1, 15, 16, 17, 57, 1, 18, 19, 22, 23, 0, 1, 0, 18, 19, 22, 23, 1, 18, 19, 22, 23, 1, 18, 0, 9, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 2, 3, 3, 14, 1, 15, 16, 17, 0, 9, 0, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 0, 2, 3, 3, 14, 1, 15, 16, 17, 0, 9, 0, 0, 10, 1, 0, 1, 0, 1, 0, 11, 0, 12, 6, 13, 8, 2, 3, 3, 14, 1, 15, 16, 17, 1, 32, 0, 58, 0, 24, 0, 25, 0, 26, 0, 27, 34, 34, 59, 35, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 19, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 30, 36, 36, 6, 1, 31, 1, 1, 1, 1, 128, 129, 130, 28, 35, 1, 1, 1, 1, 1, 20, 20, 20, 20, 24, 37, 24, 37, 25, 38, 25, 38, 26, 39, 26, 39, 27, 40, 27, 40, 131, 8, 1]], [[{"name": "name_solo", "rect": [0, 0, 442, 85], "offset": [0, 0], "originalSize": [442, 85], "capInsets": [0, 0, 0, 0]}], [15], 0, [0], [62], [132]]]]