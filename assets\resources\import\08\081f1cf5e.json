[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "f8nzb/0KZEnaqLuNaeP/7J", "a2tBXzjmRHWIetS1zkxuiC", "88AiyvRF1KRI9acSB1VU6E", "8bo+zw+UdH6bz8bdhxNl6r", "fdNoodJKVLj4dF1TLppv2g", "0dxzpfc/tPDqMPlPtRxS6k", "a9VpD0DP5LJYQPXITZq+uj", "f3TtjOS/NBqbNBHzH/oGL3", "9c3Z3RnVlMWaphwA/0G641", "8evSAQFN9NorJL2WFXV+6p", "0aUJBPIClNhZQYtdPrAy0J", "fe4uJbKBJJt5ObdXWt6Ntj", "4bMngtoe1OkrqHUwgEXlX7", "2cWB/vWPRHja3uQTinHH30", "1dFyzOKDRB8IBB/KQR3hOH", "e53/RnuVJMiJ2npNfWToia", "1a9UE8I2NJTIeqJK0kaUz5", "f42eVSMl5PiLZCeIzRpj7i"], ["node", "_N$file", "_spriteFrame", "_textureSetter", "_parent", "root", "lbDesc", "lbWin", "lbRoom", "lbNickName", "lbSID", "lbTime", "lbSessionID", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "jackpotView", "bigWinView", "nodeBigWin", "nodeJackpot", "btnBigWin", "btnJackpot", "_N$target", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 1, 2, 4, 5, 7, 1, 2, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "_N$interactable", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["3c6a1IcT7pG8b7cqDRMjmCv", ["node", "btnJackpot", "btnBigWin", "nodeJackpot", "nodeBigWin", "bigWinView", "jackpotView"], 3, 1, 1, 1, 1, 1, 1, 1], ["6c6718pco1KcbJmkeGVCmhO", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["3566aJpBw5KOJAxbKKG26cU", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["088def4kH5OCoVfmOqRAxDh", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["5f63a+KMdxKmINJ7inujvgg", ["node", "slotsLBJackpotListView"], 3, 1, 1], ["7843dJ1OI1CC4ZbqgEYhC5i", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["3f026hcsZBBlIzyApf7sacO", ["node", "slotsLBBigWinListView"], 3, 1, 1]], [[6, 0, 1, 2], [0, 0, 6, 4, 3, 5, 7, 2], [1, 0, 6, 2, 3, 8, 4, 5, 2], [4, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [0, 0, 6, 8, 4, 3, 5, 7, 2], [1, 0, 6, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [4, 0, 2, 3, 4, 2], [15, 0, 1, 2, 3], [3, 0, 1, 2, 3, 4, 5, 6, 8, 8], [0, 0, 6, 8, 4, 3, 5, 2], [0, 0, 6, 4, 3, 5, 9, 7, 2], [0, 0, 6, 8, 3, 7, 2], [1, 0, 7, 2, 3, 4, 5, 2], [1, 0, 1, 6, 7, 2, 3, 5, 3], [1, 0, 1, 6, 2, 3, 8, 4, 5, 3], [10, 0, 1, 2, 3, 4, 5, 2], [4, 2, 3, 4, 1], [5, 0, 2, 3, 4, 5, 6, 2], [7, 0, 1, 2, 5, 6, 4], [7, 3, 0, 1, 4, 5, 6, 5], [16, 0, 1, 2, 3, 4, 5, 6, 6], [9, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 5, 7, 2], [0, 0, 8, 4, 3, 5, 2], [0, 0, 1, 6, 4, 3, 5, 7, 3], [0, 0, 2, 6, 4, 3, 5, 7, 3], [0, 0, 6, 4, 3, 5, 2], [1, 0, 6, 2, 3, 4, 2], [11, 0, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 6, 1], [6, 1, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 1], [14, 0, 1, 2, 3, 4, 5, 6, 7, 1], [5, 2, 7, 1], [5, 1, 0, 2, 3, 4, 5, 6, 3], [8, 0, 1, 2, 2], [8, 0, 1, 2], [3, 0, 1, 2, 3, 4, 5, 6, 8, 9, 8], [17, 0, 1, 2, 3, 4, 5, 4], [18, 0, 1, 1], [19, 0, 1, 2, 3, 4, 5, 4], [20, 0, 1, 1]], [[[{"name": "THẮNG LỚN", "rect": [0, 0, 133, 28], "offset": [0, 0], "originalSize": [133, 28], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [7]], [[[24, "dbLeaderboardView"], [25, "dbLeaderboardView", [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21], [[32, -2, [42, 43], 41], [33, -9, -8, -7, -6, -5, -4, -3]], [34, -1]], [26, "<PERSON><PERSON>", [-30, -31, -32, -33, -34, -35], [[35, -29, -28, -27, -26, -25, -24, -23, -22]], [0, "42VJ3NiLxI3a0ot1XFLbH2", 1], [5, 780, 37], [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "<PERSON><PERSON>", [-44, -45, -46, -47, -48, -49], [[36, -43, -42, -41, -40, -39, -38, -37, -36]], [0, "714szqKcpHM7z0fAe57pXc", 1], [5, 780, 37]], [5, "spriteBGTitle", 1, [-51, -52, -53, -54, -55, -56], [[3, 0, false, -50, [28], 29]], [0, "d6AMgtUARAFZ0n4sKLZbOj", 1], [5, 780, 38], [-24, 143, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "BG_Panel_Info_half", 1, [-59], [[9, 0, -58, [6], 7]], [0, "a6N11e619GRpvZK0MauVGr", -57], [5, 628, 682], [-268, 37, 0, 0, 0, 0, 1, 0.785, 0.785, 0.785]], [15, "scrollview", [-62, -63], [-60, -61], [0, "26jglnbllPk7/pyvA2BOWJ", 1], [5, 803, 310], [0, -59, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", [-66, -67], [-64, -65], [0, "7fAzR1hPxDjbjO5hPhMWGN", 1], [5, 804, 310], [0, -59, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnJackpot", 1, [[[3, 2, false, -68, [10], 11], -69], 4, 1], [0, "74GSqToTFF1qUkR44Tggnu", 1], [5, 157, 58], [-123.6, 197.2, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnBigWin", 1, [[[3, 2, false, -70, [12], 13], -71], 4, 1], [0, "10bGZq5nRK3JsghawmYtEg", 1], [5, 157, 58], [42.3, 197.2, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "btnClose", 1, [-74], [[20, 3, -73, [[10, "3c6a1IcT7pG8b7cqDRMjmCv", "backClicked", 1]], [4, **********], [4, **********], -72]], [0, "51wos3Z/hLJZ3NhvZ6i6l2", 1], [5, 80, 80], [424.128, 240.109, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "lbJackpotView", false, 1, [6], [-75], [0, "aaOIYmVT5Hwb4naB2vTaRD", 1], [-24, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 2, [-77, -78], [[21, 1, 1, 5, -76, [5, 95, 50]]], [0, "ccgHGsjv9PiYOa12PgllHM", 1], [5, 95, 50], [0.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbBig<PERSON>in<PERSON>iew", false, 1, [7], [-79], [0, "c07UuAtZ1NJaLvV6K8z0Gz", 1], [-24, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-nick<PERSON><PERSON>", 3, [-81, -82], [[21, 1, 1, 5, -80, [5, 137, 50]]], [0, "93swyyB0xCoosmkeMhmH6n", 1], [5, 137, 50], [0.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "black", 100, 1, [[9, 0, -83, [0], 1], [37, -84, [4, 4292269782]]], [0, "4cwykq/MFJ06igN5OdPLEK", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "view", 6, [-86], [[39, 0, -85, [40]]], [0, "fa8OJeEA5PcINzrvbTo9rZ", 1], [5, 803, 310]], [13, "content", 16, [[22, false, 1, 2, 10, -87, [5, 1190, 75]]], [0, "02SEbGkWBO5pmz9vGls6NE", 1], [5, 803, 75], [0, 0.5, 1], [0, 152, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "view", 7, [-89], [[40, 0, -88]], [0, "f0sLtVIydEo7jfhC1ge0Jo", 1], [5, 803, 310]], [13, "content", 18, [[22, false, 1, 2, 10, -90, [5, 1190, 75]]], [0, "c5FN8HSgNDSYGYZphRJ9Tr", 1], [5, 803, 75], [0, 0.5, 1], [0, 152, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "nen popup", false, 1, [[3, 0, false, -91, [2], 3]], [0, "fd01DvVWlMGJ1Ie6voOY2X", 1], [5, 854, 497], [-24, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BG_Panel_Info_half copy", 5, [[9, 0, -92, [4], 5]], [0, "6aQsYvizZAJrzke2DehTMc", 5], [5, 628, 682], [625, 0, 0, 0, 0, 0, 1, -1, 1, -1]], [1, "tex_bangthuong", 1, [[3, 2, false, -93, [8], 9]], [0, "e0FHjTbzxKy6plFr4242BQ", 1], [5, 130, 24], [-37, 260, 0, 0, 0, 0, 1, 1, 1, 1]], [38, false, 2, 8, [[10, "3c6a1IcT7pG8b7cqDRMjmCv", "jackpotTabClicked", 1]], [4, **********], [4, **********], 8], [20, 2, 9, [[10, "3c6a1IcT7pG8b7cqDRMjmCv", "bigWinTabClicked", 1]], [4, **********], [4, **********], 9], [30, "sprite", 10, [[3, 2, false, -94, [14], 15]], [0, "52PT/dXKlLcpidUV31aXp4", 1], [5, 51, 36]], [1, "lbSessionID", 4, [[4, "PHIÊN", 20, 30, false, false, 1, 1, 1, -95, [16], 17]], [0, "2bCerDdM1Fy7ThZl6JkGqX", 1], [5, 100, 30], [-339.6, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 4, [[4, "THỜI GIAN", 20, 30, false, false, 1, 1, 1, -96, [18], 19]], [0, "a6qtf11ldAC72I/TgAZRgd", 1], [5, 100, 30], [-197.7, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbAccount", 4, [[4, "TÀI KHOẢN", 20, 30, false, false, 1, 1, 1, -97, [20], 21]], [0, "15zbgX7WpBQL8SpoGabOqK", 1], [5, 100, 30], [0.9, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRoom", 4, [[4, "PHÒNG", 20, 30, false, false, 1, 1, 1, -98, [22], 23]], [0, "85acepFiBNLq+XI30+BqV/", 1], [5, 100, 30], [141, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 4, [[4, "THẮNG", 20, 30, false, false, 1, 1, 1, -99, [24], 25]], [0, "f2/cCJoehNsYavPedf0b8h", 1], [5, 100, 30], [236, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 4, [[4, "MÔ TẢ", 20, 30, false, false, 1, 1, 1, -100, [26], 27]], [0, "e0xj6fIE1EubEEIYqCBELJ", 1], [5, 100, 30], [335, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbJackpot", 1, [[19, -101, [30], 31]], [0, "88UVZTq/hBipsD+RIk//QW", 1], [5, 73, 28], [-123.6, 200.2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBigWin", 1, [[19, -102, [32], 33]], [0, "7chiYrfhdLCI/IsclqNybJ", 1], [5, 133, 28], [42.3, 200.2, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "temp", 6, [2], [0, "314I+zPJdG8q7XUCW6eJot", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lb\bSessionID", 2, [-103], [0, "4bfiD+jNhP1raMb+PcwMAf", 1], [5, 200, 30], [-339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "#66563620", 20, 48, false, false, 1, 1, 1, 35, [34]], [6, "lbTime", 2, [-104], [0, "45JoBFGRRHQ4HTKxWqVn4N", 1], [5, 220, 30], [-197.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "20:45 21-08-2018", 20, 48, false, false, 1, 1, 1, 37, [35]], [17, "lbSID", false, 12, [-105], [0, "1a9/HRTTJIGZwni8RnAPOG", 1], [4, 4279026733], [5, 37, 24], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "[TQ]", 20, 48, false, false, 1, 1, 39], [31, "lbNickName", 12, [-106], [0, "813b20igdBY7KIZOa2cDqc", 1], [5, 95, 48]], [41, "<PERSON><PERSON><PERSON>", 20, 48, false, false, 1, 1, 41, [36]], [2, "lbRoom", 2, [-107], [0, "94/nyr4qpO5JpeQGbewJPs", 1], [4, 4278255615], [5, 100, 30], [141, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "5.000", 20, 48, false, false, 1, 1, 1, 43, [37]], [2, "lbWin", 2, [-108], [0, "4cOYqz949EqJ812lyayY/+", 1], [4, 4278255615], [5, 200, 30], [236, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "50.000.000", 20, 48, false, false, 1, 1, 1, 45, [38]], [2, "lbDesc", 2, [-109], [0, "14CaV3RE9Kya/gxoKbWGKd", 1], [4, 4278246399], [5, 200, 30], [335, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "NỔ HŨ", 20, 48, false, false, 1, 1, 1, 47, [39]], [23, false, 0.75, 0.23, null, null, 6, 17], [42, 15, 10, 400, 6, 2, 49], [43, 11, 50], [14, "temp", 7, [3], [0, "47tOaGSZxBGLRquSM+5b60", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lb\bSessionID", 3, [-110], [0, "4bLp1TXiBDi6AytjOxmRDw", 1], [5, 200, 30], [-339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "PHIÊN", 20, 48, false, false, 1, 1, 1, 53], [6, "lbTime", 3, [-111], [0, "a9WnXnIk1LOYbx8RiRvhEb", 1], [5, 220, 30], [-197.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "THỜI GIAN", 20, 48, false, false, 1, 1, 1, 55], [17, "lbSID", false, 14, [-112], [0, "d5S2XQSqBNkZFuoTlzcXzH", 1], [4, 4279026733], [5, 37, 24], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "[TQ]", 20, 48, false, false, 1, 1, 57], [6, "lbNickName", 14, [-113], [0, "98tNCe9ZhJ7aFaKJUsmhND", 1], [5, 95, 24], [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "<PERSON><PERSON><PERSON>", 20, 48, false, false, 1, 1, 59], [2, "lbRoom", 3, [-114], [0, "e5GFymW8hEULS06deUOQtT", 1], [4, 4278255615], [5, 100, 30], [141, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "PHÒNG", 20, 48, false, false, 1, 1, 1, 61], [2, "lbWin", 3, [-115], [0, "75TKmFUMdCK5PZ1r3GW6nd", 1], [4, 4278255615], [5, 200, 30], [224.2, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "THẮNG", 20, 48, false, false, 1, 1, 1, 63], [2, "lbDesc", 3, [-116], [0, "05YGp6gKlKBby8K75dqzw4", 1], [4, 4294829568], [5, 200, 30], [335, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "MÔ TẢ", 20, 48, false, false, 1, 1, 1, 65], [23, false, 0.75, 0.23, null, null, 7, 19], [44, 15, 10, 400, 7, 3, 67], [45, 13, 68]], 0, [0, 5, 1, 0, 0, 1, 0, 17, 51, 0, 18, 69, 0, 19, 13, 0, 20, 11, 0, 21, 24, 0, 22, 23, 0, 0, 1, 0, -1, 15, 0, -2, 20, 0, -3, 5, 0, -4, 22, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, -8, 4, 0, -9, 32, 0, -10, 33, 0, -11, 11, 0, -12, 13, 0, 6, 48, 0, 7, 46, 0, 8, 44, 0, 9, 42, 0, 10, 40, 0, 11, 38, 0, 12, 36, 0, 0, 2, 0, -1, 35, 0, -2, 37, 0, -3, 12, 0, -4, 43, 0, -5, 45, 0, -6, 47, 0, 6, 66, 0, 7, 64, 0, 8, 62, 0, 9, 60, 0, 10, 58, 0, 11, 56, 0, 12, 54, 0, 0, 3, 0, -1, 53, 0, -2, 55, 0, -3, 14, 0, -4, 61, 0, -5, 63, 0, -6, 65, 0, 0, 4, 0, -1, 26, 0, -2, 27, 0, -3, 28, 0, -4, 29, 0, -5, 30, 0, -6, 31, 0, 5, 5, 0, 0, 5, 0, -1, 21, 0, -1, 49, 0, -2, 50, 0, -1, 34, 0, -2, 16, 0, -1, 67, 0, -2, 68, 0, -1, 52, 0, -2, 18, 0, 0, 8, 0, -2, 23, 0, 0, 9, 0, -2, 24, 0, 23, 10, 0, 0, 10, 0, -1, 25, 0, -1, 51, 0, 0, 12, 0, -1, 39, 0, -2, 41, 0, -1, 69, 0, 0, 14, 0, -1, 57, 0, -2, 59, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, -1, 17, 0, 0, 17, 0, 0, 18, 0, -1, 19, 0, 0, 19, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 25, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, -1, 36, 0, -1, 38, 0, -1, 40, 0, -1, 42, 0, -1, 44, 0, -1, 46, 0, -1, 48, 0, -1, 54, 0, -1, 56, 0, -1, 58, 0, -1, 60, 0, -1, 62, 0, -1, 64, 0, -1, 66, 0, 24, 1, 2, 4, 34, 3, 4, 52, 6, 4, 11, 7, 4, 13, 116], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 23, 23, 23, 24, 24, 24, 24, 36, 38, 40, 42, 44, 46, 48, 54, 56, 58, 60, 62, 64, 66], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, 2, -1, 2, -1, -1, -1, -1, -1, -1, -1, 25, -1, -2, 13, 14, 15, 16, 13, 14, 15, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, 8, 0, 9, 0, 5, 0, 5, 0, 10, 0, 4, 0, 2, 0, 11, 0, 3, 0, 3, 0, 3, 0, 3, 0, 3, 0, 3, 0, 12, 0, 13, 0, 14, 0, 0, 0, 0, 0, 0, 0, 6, 6, 15, 2, 2, 2, 4, 2, 2, 2, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]], [[{"name": "bnt__on", "rect": [0, 0, 157, 58], "offset": [0, 0], "originalSize": [157, 58], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [16]], [[{"name": "tex_vinh<PERSON>h", "rect": [0, 0, 130, 24], "offset": [0, 0], "originalSize": [130, 24], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [17]], [[{"name": "bnt__off", "rect": [1, 0, 156, 57], "offset": [0.5, 0.5], "originalSize": [157, 58], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [18]], [[{"name": "NỔ HŨ", "rect": [0, 0, 73, 28], "offset": [0, 0], "originalSize": [73, 28], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [19]]]]