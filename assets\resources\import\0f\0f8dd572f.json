[1, ["beFsawVrZB5rDgj2XumKNc", "b6NkzVKYlAHKi8XGtksv2c", "6bpsf6sftIG7oSSrDSgUy/", "24A6Qo1jtLUK4IMY/3xBnS", "334zyzrc9FFIJ9rC7G7B95", "3dh5mrOMlEqZ5HgerRZ5U1", "dbv2rTXBpAuLm3xwJl0sIB", "c5t364ReNAJ4VnBA4OypXR", "5bnGXjFUJCwKkXsBv9B+7s", "1ceDDw4cBIGo5/5dNyW+aw", "73OtDaPBNK05MxhfHXG/f7", "b9cL1vIWJM5pptDsfnnjbU", "98o0laL3xA04lZe3tt+yhX", "2e00CX59pMPYJH4/pMoMiv", "58mIILiaNBuJuhFi46hEC6", "beOZVYYUNIUp/vseR7e4KM", "4dPNcU8CFCl4UqvkvDR9oC", "c6Q9vBEjFFiKg6p886Wabh", "cfZWuIFlFHQog+8zM/0OrN", "adHPXBpYpBwJpUa63VW1fA", "cakwpVCE5P84ZVol4M1FMa", "535GMeplBGK5Sarni7772F", "9d8tEOKtpKrqL2F7pq2m5G", "e7w7fitqNB9aJ2MpBppmAn", "fcNRrTkl9NSJ9ZDNVH7BzR", "95RCYcYw5Ml5I88HVQv0mO", "79LQ9dC+9JLY5mLCkHXVZ/", "4dRFIQaxRJXatepwB3UzN7", "e3g4dr4WBKrLeFggOcyxgo", "fdl1dCpkVAV6RQiIzfoV04", "ecR7+o3uRGEYhIu4XoMTcy", "33c4hhKKpD1qdhmz5XR4Sd", "64RsUNQH9GY5w6n1E6KULV", "e5D0yQ7slGRZVBYiO+VEfv", "bbSWlEymxHy6Q8k7BRgVAu", "ccDbedzg5F4Jv6esdeuHBe", "92IwTJw+FPDq/m0A6C66XY", "41SYL/wh1GDLmEEVr3Ts55", "ec6QY4cHZLeaYgV992xDAW", "87xguutRxGZ53EeW91ezNf", "e9rgRMU71I3I7q1WEbuYsz", "7cM5CzgX9IaJthgUulNmk0", "36DNy4jnBMjr9O0GcKOfJc", "bfr73jycZO/4XSS3ztNf9p", "089KJgBetIep/UZzU54vNA", "d0jxIU8c5OY7xr3Fir6MDd", "ccDHb15zJNGoos6UsEcnu3", "fesducK3JPrqJILCYe68up", "e5G0D7aslCCoxb8e47NfGw", "4bWxY8UidCz7MVd3VVfjhU", "91mwpGJfNDnJr3IxgVpNBe", "b6sarjaCpJ2oT2nY7QbnnK", "acvsWge75Lboog/AeN/FF5", "4d4T0phw1AB5QBI4ZPHjM0", "3aPE3Kq9JG35YcSKaqJhVc", "3328Q8FYBCfIb1q/AN9X1r"], ["_textureSetter", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52", "01", "02", "03", "04", "05", "06", "07", "08", "09", "B", "D", "V"], ["cc.SpriteFrame", ["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[1, 0, 1, 2]], [[[{"name": "12", "rect": [756, 308, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "card3.plist", [{}, "10", 6, 0, "11", 6, 1, "12", 6, 2, "13", 6, 3, "14", 6, 4, "15", 6, 5, "16", 6, 6, "17", 6, 7, "18", 6, 8, "19", 6, 9, "20", 6, 10, "21", 6, 11, "22", 6, 12, "23", 6, 13, "24", 6, 14, "25", 6, 15, "26", 6, 16, "27", 6, 17, "28", 6, 18, "29", 6, 19, "30", 6, 20, "31", 6, 21, "32", 6, 22, "33", 6, 23, "34", 6, 24, "35", 6, 25, "36", 6, 26, "37", 6, 27, "38", 6, 28, "39", 6, 29, "40", 6, 30, "41", 6, 31, "42", 6, 32, "43", 6, 33, "44", 6, 34, "45", 6, 35, "46", 6, 36, "47", 6, 37, "48", 6, 38, "49", 6, 39, "50", 6, 40, "51", 6, 41, "52", 6, 42, "01", 6, 43, "02", 6, 44, "03", 6, 45, "04", 6, 46, "05", 6, 47, "06", 6, 48, "07", 6, 49, "08", 6, 50, "09", 6, 51, "B", 6, 52, "D", 6, 53, "V", 6, 54]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55]], [[{"name": "V", "rect": [0, 0, 117, 162], "offset": [0, 0], "originalSize": [117, 162], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "13", "rect": [756, 154, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "51", "rect": [0, 624, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "D", "rect": [216, 924, 105, 150], "offset": [0, 0], "originalSize": [105, 150], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "25", "rect": [540, 154, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "36", "rect": [324, 308, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "23", "rect": [540, 462, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "11", "rect": [756, 462, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "50", "rect": [0, 778, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "10", "rect": [756, 616, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "24", "rect": [540, 308, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "52", "rect": [0, 470, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "26", "rect": [442, 0, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "37", "rect": [324, 154, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "49", "rect": [108, 163, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "39", "rect": [216, 770, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "38", "rect": [226, 0, 107, 153], "offset": [0, 0], "originalSize": [107, 153], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]