[1, ["c6FGFi9rhGiISrNB32yHx4", "04e3BDB9xACJeMJRWrkBMl"], ["_textureSetter", "spriteFrame"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[{"name": "font-export", "rect": [0, 0, 119, 126], "offset": [-4.5, 1], "originalSize": [128, 128], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "font-export", 48, {"commonHeight": 25, "fontSize": 48, "atlasName": "font-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 180, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 6, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "33": {"xOffset": 0, "yOffset": 6, "xAdvance": 7, "rect": {"x": 108, "y": 107, "width": 5, "height": 19}}, "34": {"xOffset": 0, "yOffset": 6, "xAdvance": 12, "rect": {"x": 35, "y": 120, "width": 10, "height": 6}}, "35": {"xOffset": 0, "yOffset": 6, "xAdvance": 18, "rect": {"x": 19, "y": 69, "width": 16, "height": 19}}, "36": {"xOffset": 0, "yOffset": 3, "xAdvance": 14, "rect": {"x": 84, "y": 65, "width": 12, "height": 25}}, "37": {"xOffset": 0, "yOffset": 6, "xAdvance": 21, "rect": {"x": 0, "y": 48, "width": 19, "height": 19}}, "38": {"xOffset": 0, "yOffset": 6, "xAdvance": 19, "rect": {"x": 0, "y": 69, "width": 17, "height": 20}}, "39": {"xOffset": 0, "yOffset": 6, "xAdvance": 7, "rect": {"x": 109, "y": 78, "width": 5, "height": 6}}, "40": {"xOffset": 0, "yOffset": 6, "xAdvance": 9, "rect": {"x": 108, "y": 0, "width": 7, "height": 24}}, "41": {"xOffset": 0, "yOffset": 6, "xAdvance": 9, "rect": {"x": 109, "y": 52, "width": 7, "height": 24}}, "42": {"xOffset": 0, "yOffset": 6, "xAdvance": 13, "rect": {"x": 76, "y": 112, "width": 11, "height": 10}}, "43": {"xOffset": 0, "yOffset": 9, "xAdvance": 14, "rect": {"x": 49, "y": 113, "width": 12, "height": 12}}, "44": {"xOffset": 0, "yOffset": 21, "xAdvance": 8, "rect": {"x": 99, "y": 52, "width": 6, "height": 7}}, "45": {"xOffset": 0, "yOffset": 16, "xAdvance": 8, "rect": {"x": 99, "y": 47, "width": 6, "height": 3}}, "46": {"xOffset": 0, "yOffset": 21, "xAdvance": 7, "rect": {"x": 109, "y": 86, "width": 5, "height": 4}}, "47": {"xOffset": 0, "yOffset": 6, "xAdvance": 12, "rect": {"x": 86, "y": 22, "width": 10, "height": 19}}, "48": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 53, "y": 91, "width": 14, "height": 20}}, "49": {"xOffset": 0, "yOffset": 6, "xAdvance": 10, "rect": {"x": 98, "y": 0, "width": 8, "height": 19}}, "50": {"xOffset": 0, "yOffset": 6, "xAdvance": 15, "rect": {"x": 70, "y": 44, "width": 13, "height": 19}}, "51": {"xOffset": 0, "yOffset": 6, "xAdvance": 14, "rect": {"x": 85, "y": 43, "width": 12, "height": 20}}, "52": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 54, "y": 48, "width": 14, "height": 19}}, "53": {"xOffset": 0, "yOffset": 6, "xAdvance": 15, "rect": {"x": 57, "y": 0, "width": 13, "height": 20}}, "54": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 39, "y": 26, "width": 14, "height": 20}}, "55": {"xOffset": 0, "yOffset": 6, "xAdvance": 15, "rect": {"x": 69, "y": 69, "width": 13, "height": 19}}, "56": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 53, "y": 69, "width": 14, "height": 20}}, "57": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 41, "y": 0, "width": 14, "height": 20}}, "58": {"xOffset": 0, "yOffset": 11, "xAdvance": 6, "rect": {"x": 115, "y": 92, "width": 4, "height": 14}}, "59": {"xOffset": 0, "yOffset": 11, "xAdvance": 7, "rect": {"x": 101, "y": 107, "width": 5, "height": 17}}, "60": {"xOffset": 0, "yOffset": 9, "xAdvance": 13, "rect": {"x": 84, "y": 92, "width": 11, "height": 13}}, "61": {"xOffset": 0, "yOffset": 10, "xAdvance": 13, "rect": {"x": 63, "y": 113, "width": 11, "height": 10}}, "62": {"xOffset": 0, "yOffset": 9, "xAdvance": 12, "rect": {"x": 97, "y": 92, "width": 10, "height": 13}}, "63": {"xOffset": 0, "yOffset": 6, "xAdvance": 12, "rect": {"x": 89, "y": 107, "width": 10, "height": 19}}, "64": {"xOffset": 0, "yOffset": 8, "xAdvance": 21, "rect": {"x": 0, "y": 26, "width": 19, "height": 20}}, "66": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 37, "y": 69, "width": 14, "height": 20}}, "68": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 55, "y": 22, "width": 14, "height": 20}}, "69": {"xOffset": 0, "yOffset": 6, "xAdvance": 14, "rect": {"x": 72, "y": 0, "width": 12, "height": 20}}, "71": {"xOffset": 0, "yOffset": 6, "xAdvance": 15, "rect": {"x": 69, "y": 90, "width": 13, "height": 20}}, "75": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 38, "y": 48, "width": 14, "height": 19}}, "77": {"xOffset": 0, "yOffset": 6, "xAdvance": 19, "rect": {"x": 0, "y": 101, "width": 17, "height": 19}}, "78": {"xOffset": 0, "yOffset": 6, "xAdvance": 17, "rect": {"x": 21, "y": 48, "width": 15, "height": 19}}, "80": {"xOffset": 0, "yOffset": 6, "xAdvance": 15, "rect": {"x": 71, "y": 22, "width": 13, "height": 19}}, "84": {"xOffset": 0, "yOffset": 6, "xAdvance": 16, "rect": {"x": 37, "y": 91, "width": 14, "height": 19}}, "86": {"xOffset": 0, "yOffset": 6, "xAdvance": 18, "rect": {"x": 21, "y": 26, "width": 16, "height": 20}}, "88": {"xOffset": 0, "yOffset": 6, "xAdvance": 18, "rect": {"x": 19, "y": 90, "width": 16, "height": 19}}, "91": {"xOffset": 0, "yOffset": 6, "xAdvance": 9, "rect": {"x": 99, "y": 21, "width": 7, "height": 24}}, "92": {"xOffset": 0, "yOffset": 6, "xAdvance": 12, "rect": {"x": 86, "y": 0, "width": 10, "height": 20}}, "93": {"xOffset": 0, "yOffset": 6, "xAdvance": 9, "rect": {"x": 108, "y": 26, "width": 7, "height": 24}}, "94": {"xOffset": 0, "yOffset": 6, "xAdvance": 14, "rect": {"x": 35, "y": 113, "width": 12, "height": 5}}, "95": {"xOffset": 0, "yOffset": 26, "xAdvance": 17, "rect": {"x": 0, "y": 122, "width": 15, "height": 2}}, "120": {"xOffset": 0, "yOffset": 11, "xAdvance": 16, "rect": {"x": 19, "y": 112, "width": 14, "height": 14}}, "123": {"xOffset": 0, "yOffset": 6, "xAdvance": 11, "rect": {"x": 98, "y": 65, "width": 9, "height": 24}}, "124": {"xOffset": 0, "yOffset": 6, "xAdvance": 41, "rect": {"x": 0, "y": 0, "width": 39, "height": 24}}, "125": {"xOffset": 0, "yOffset": 14, "xAdvance": 19, "rect": {"x": 0, "y": 91, "width": 17, "height": 8}}}, "kerningDict": {}}]], 0, 0, [0], [1], [1]]]]