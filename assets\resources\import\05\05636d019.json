[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2tBXzjmRHWIetS1zkxuiC", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "1fLu/FjupHJaCNyegu34iq", "a9VpD0DP5LJYQPXITZq+uj", "271R0s38tAlIenzZCkaPff", "1d/Zt4ootFOJaNUdkgJzUb", "27prX/qmpC/K00V6HQEsdU", "2bVoxahyREcJRSYE5h+NAD", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_N$target", "_parent", "_textureSetter", "root", "slotsHistoryListView", "lbWin", "lbBet", "lbTime", "lbSessionID", "data", "_defaultClip"], [["cc.Node", ["_name", "_skewX", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 9, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 2, 2, 4, 5, 7, 1, 2, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor"], 1, 1, 9, 1, 5, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["c890fFIrOZAObefUnoFLaiq", ["node", "slotsHistoryListView"], 3, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "lbWin", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 1, 5, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], 0, 1, 5], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["94f0dcGWSRC9ovLsmjATU3F", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[9, 0, 1, 2], [0, 0, 7, 5, 4, 6, 8, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [0, 0, 7, 9, 5, 4, 6, 8, 2], [2, 0, 5, 1, 2, 3, 4, 2], [2, 0, 5, 1, 2, 7, 3, 4, 2], [11, 0, 1, 2, 3], [1, 0, 1, 3, 4, 5, 3], [6, 0, 2], [0, 0, 9, 5, 4, 2], [0, 0, 9, 5, 4, 6, 2], [0, 0, 1, 7, 5, 4, 10, 6, 8, 3], [0, 0, 2, 7, 5, 4, 6, 8, 3], [0, 0, 7, 5, 4, 6, 11, 8, 2], [0, 0, 3, 7, 5, 4, 6, 8, 3], [0, 0, 7, 5, 4, 6, 2], [0, 0, 7, 9, 4, 6, 8, 2], [0, 0, 7, 9, 4, 8, 2], [2, 0, 6, 1, 2, 3, 4, 2], [7, 0, 1, 2, 1], [8, 0, 1, 1], [10, 0, 1, 2, 3, 4, 5, 6, 1], [3, 0, 1, 2, 3, 6, 7, 4, 3], [3, 0, 1, 2, 3, 5, 4, 3], [3, 2, 5, 1], [1, 0, 3, 4, 5, 2], [1, 3, 4, 5, 1], [1, 2, 0, 1, 3, 4, 5, 4], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 4], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1, 2, 3, 4, 5, 4]], [[[{"name": "title_lsgd", "rect": [0, 0, 247, 43], "offset": [0, 0], "originalSize": [247, 43], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [5], [4]], [[[9, "aquariumHistoryView"], [10, "aquariumHistoryView", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15], [[20, -2, [28, 29], 27], [21, -4, -3]], [0, "bfdrSda91GO5HWgz9Fl5o5", -1]], [11, "<PERSON><PERSON>", [-21, -22, -23, -24, -25], [[22, -20, -19, -18, -17, -16, [4, 4278246399], [4, 4294829568]]], [0, "3e26WdRIFF5Ypm3z6rbItx", 1], [5, 840, 37]], [19, "scrollview", [-28, -29], [-26, -27], [0, "08FH8m/q9GsqvMf1DWXrhg", 1], [5, 840, 411], [0, 17, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btnClose", 1, [-32], [[23, 1.1, 3, -31, [[7, "c890fFIrOZAObefUnoFLaiq", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -30]], [0, "3aWxiHXjZCUKFJyM8V4L4k", 1], [5, 80, 80], [-3.279, -206.004, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "lbDesc", 10, 2, [[2, "<PERSON>em chi tiết", 20, 30, false, false, 1, 1, 1, -33, [24], 25], [24, 1.1, 3, -35, [[7, "98b467lg1tGSIOSS7iaiI4s", "openDetailClicked", 2]], [4, 4292269782], -34]], [0, "31G1y0EiJBB5f+tQaVT5qY", 1], [4, 4278246399], [5, 150, 30], [319, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[26, 0, -36, [0], 1], [25, -37, [4, 4292269782]]], [0, "58KkRgpthJ86PVkaCTRqXl", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "view", 3, [-39], [[29, 0, -38, [26]]], [0, "8bdrm8jddOJItGZZoKaNUv", 1], [5, 840, 422], [0, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "content", 7, [[30, 1, 2, 10, -40, [5, 840, -10]]], [0, "00OiPNdX1KrYzERgMotiXx", 1], [5, 840, -10], [0, 0.5, 1], [0, 221, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[8, 0, false, -41, [2], 3]], [0, "f2SC/tduZDBJEQZWpwDCVq", 1], [5, 643, 377], [0, 46, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [15, "title_lsgd", false, 1, [[27, -42, [4], 5]], [0, "b8o7TyvMtEKJs36CEdrZiB", 1], [5, 247, 43], [2, 293, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "sprite", 4, [[8, 2, false, -43, [6], 7]], [0, "9aDU3eEV5EUJwlXs9t1BSw", 1], [5, 180, 58]], [1, "spriteBGTitle", 1, [[28, 1, 0, false, -44, [8], 9]], [0, "3dtbCkmoNEU7VpaZmhnvtl", 1], [5, 840, 38], [0, 234, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 1, [[2, "THỜI GIAN", 20, 30, false, false, 1, 1, 1, -45, [10], 11]], [0, "02qB8dIC1Kiag4j0ADUgfG", 1], [5, 100, 30], [-155, 233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 1, [[2, "CƯỢC", 20, 30, false, false, 1, 1, 1, -46, [12], 13]], [0, "1cmWPVMW9HZZEv+gBAv65T", 1], [5, 100, 30], [22, 233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 1, [[2, "THẮNG", 20, 30, false, false, 1, 1, 1, -47, [14], 15]], [0, "c0+/L3YYFDr4TdSo/EITxT", 1], [5, 100, 30], [165, 233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 1, [[2, "CHI TIẾT", 20, 30, false, false, 1, 1, 1, -48, [16], 17]], [0, "962PSwGMtLRKpvzvMOLrWe", 1], [5, 100, 30], [319, 233, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbSessionID", 1, [[2, "PHIÊN", 20, 30, false, false, 1, 1, 1, -49, [18], 19]], [0, "4as+9fvwlGPJOOGmtZNbVC", 1], [5, 100, 30], [-345, 233, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "bg<PERSON><PERSON>nt", 1, [3], [0, "4cE6y19FJNvLN9DGW3BvvQ", 1], [5, 803, 398], [0, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "temp", 3, [2], [0, "754IJ1PdxB1okyRtQp1Thh", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbSessionID", 2, [-50], [0, "a8+u1lrypFRrOLMrQGpx16", 1], [5, 150, 30], [-345, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "#66553620", 20, 30, false, false, 1, 1, 1, 20, [20]], [5, "lbTime", 2, [-51], [0, "32+MyAigJG8ZoJrjgVfXGv", 1], [5, 200, 30], [-155, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "17:03 23-03-2019", 20, 30, false, false, 1, 1, 1, 22, [21]], [6, "lbBet", 2, [-52], [0, "faiLNu4HBGWJOYxb9GLZ4J", 1], [4, 4278255615], [5, 200, 30], [22, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "250.000", 20, 30, false, false, 1, 1, 1, 24, [22]], [6, "lbWin", 2, [-53], [0, "81GVh3SRFOp7dkH5n3qm9s", 1], [4, 4278255615], [5, 200, 30], [165, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "50.000.000", 20, 30, false, false, 1, 1, 1, 26, [23]], [31, false, 0.75, 0.23, null, null, 3, 8], [32, 15, 10, 400, 3, 2, 28]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 29, 0, 0, 1, 0, -1, 6, 0, -2, 9, 0, -3, 10, 0, -4, 4, 0, -5, 12, 0, -6, 13, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, -10, 17, 0, -11, 18, 0, 8, 27, 0, 9, 25, 0, 10, 23, 0, 11, 21, 0, 0, 2, 0, -1, 20, 0, -2, 22, 0, -3, 24, 0, -4, 26, 0, -5, 5, 0, -1, 28, 0, -2, 29, 0, -1, 19, 0, -2, 7, 0, 3, 4, 0, 0, 4, 0, -1, 11, 0, 0, 5, 0, 3, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, -1, 21, 0, -1, 23, 0, -1, 25, 0, -1, 27, 0, 12, 1, 2, 4, 19, 3, 4, 18, 53], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 23, 25, 27], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 13, -1, -2, 1, 1, 1, 1], [0, 5, 0, 6, 0, 7, 0, 8, 0, 9, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 2, 0, 3, 3, 10, 2, 2, 2, 2]]]]