[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "1dLz0IPHdDnYCGwnKmvxEo", "83+naGN79I95fMxmWn9xda", "c1iNYlndJPkbHWUAVaO4IP", "18s14wsJ1Nh7EeEYJxncZC", "c9qM75rxFL7qLQhl66DAEl", "c5H1zNoG5FNLT97wLAo1z6", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_N$target", "_defaultClip", "root", "slotsHistoryListView", "lbWin", "lbBet", "lbTime", "lbSessionID", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_skewX", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 9, 1, 5, 7, 2, 5, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor"], 1, 1, 9, 1, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "lbWin", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 1, 5, 5], ["cc.Layout", ["_enabled", "_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["94f0dcGWSRC9ovLsmjATU3F", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["c890fFIrOZAObefUnoFLaiq", ["node", "slotsHistoryListView"], 3, 1, 1]], [[7, 0, 1, 2], [0, 0, 5, 4, 3, 6, 7, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [2, 2, 3, 4, 1], [2, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3], [0, 0, 5, 8, 4, 3, 6, 7, 2], [0, 0, 5, 4, 3, 6, 2], [3, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 6, 4, 5, 2], [5, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 1, 5, 4, 3, 6, 7, 3], [0, 0, 5, 8, 3, 7, 2], [0, 0, 8, 4, 3, 6, 2], [0, 0, 2, 5, 4, 3, 9, 6, 7, 3], [0, 0, 5, 8, 4, 3, 6, 2], [0, 0, 5, 4, 3, 6, 10, 7, 2], [6, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 2, 3, 4, 2], [1, 2, 5, 1], [1, 0, 2, 3, 6, 7, 4, 2], [1, 1, 0, 2, 3, 6, 7, 4, 3], [1, 1, 0, 2, 3, 5, 4, 3], [9, 0, 1, 2, 3, 4, 5, 6, 1], [10, 0, 1, 2, 3, 4, 5, 5], [11, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 3, 4, 5, 4], [14, 0, 1], [15, 0, 1, 2, 1], [16, 0, 1, 1]], [[11, "historyView"], [12, "historyView", [-5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17], [[31, -2, [32, 33], 31], [32, -4, -3]], [0, "e8ZviA26FPfJDALwqCeJHs", -1]], [15, "<PERSON><PERSON>", [-23, -24, -25, -26, -27], [[25, -22, -21, -20, -19, -18, [4, 4278246399], [4, 4294829568]]], [0, "aaV77SzdZO9bjgc38GuoAF", 1], [5, 799, 37]], [19, "scrollview", 1, [-31, -32], [[-28, -29, [30, -30]], 1, 1, 4], [0, "ab/6EEa5lNj4JVfKvnCNOD", 1], [5, 803, 317], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnClose", 1, [-35], [[22, 3, -34, [[6, "c890fFIrOZAObefUnoFLaiq", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -33]], [0, "27NJiHzo5EHoie0wRndIXO", 1], [5, 80, 80], [482.872, 216.66, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnContinue", 1, [-38], [[23, 1.1, 3, -37, [[6, "c890fFIrOZAObefUnoFLaiq", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -36]], [0, "64rlH7ayJA4KEPoIyPTs2H", 1], [5, 180, 80], [0, -235, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbDesc", 10, 2, [[2, "<PERSON>em chi tiết", 20, 50, false, false, 1, 1, 1, -39, [28], 29], [24, 1.1, 3, -41, [[6, "98b467lg1tGSIOSS7iaiI4s", "openDetailClicked", 2]], [4, 4292269782], -40]], [0, "ffhdCON8ZFIpW/xA0Ui5Nq", 1], [4, 4278246399], [5, 150, 30], [319, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[20, 0, -42, [0], 1], [21, -43, [4, 4292269782]]], [0, "c6jnBvuFNN96Tw4YnpA2Z/", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "view", 3, [-45], [[27, 0, -44, [30]]], [0, "e3GgtIKKRE8rZr6hbOFn9f", 1], [5, 803, 306]], [18, "content", 8, [[26, false, 1, 2, 10, -46, [5, 1190, 75]]], [0, "6eTgnnKhpAA6dO0/U/xWks", 1], [5, 803, 75], [0, 0.5, 1], [0, 153, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[4, -47, [2], 3]], [0, "6dEqrMSBRLHY+wbzB9LEl7", 1], [5, 1028, 674], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bảng thưởng", 1, [[4, -48, [4], 5]], [0, "500KNNpz1LM6qeu3HRMZEQ", 1], [5, 93, 35], [0.463, 260.624, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "spriteBGTitle", 1, [[4, -49, [6], 7]], [0, "2cXqrYAkZNV4RG/bPID05n", 1], [5, 803, 38], [0, 164, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg<PERSON><PERSON>nt", 1, [[5, 2, false, -50, [8], 9]], [0, "acEr/7v65LYKSkuwbdI8ec", 1], [5, 803, 317], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "sprite", 4, [[5, 2, false, -51, [10], 11]], [0, "8dE/r4TYZDyoYMtegv3CEE", 1], [5, 63, 59]], [8, "sprite", 5, [[5, 2, false, -52, [12], 13]], [0, "f3GSWI0AJFBJUmPPUyB8KX", 1], [5, 186, 73]], [1, "lbSessionID", 1, [[2, "PHIÊN", 20, 50, false, false, 1, 1, 1, -53, [14], 15]], [0, "c33TppnEBG145dqeCAezWy", 1], [5, 100, 30], [-318, 163, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 1, [[2, "THỜI GIAN", 20, 50, false, false, 1, 1, 1, -54, [16], 17]], [0, "e4SND6QlhIaraNu598vZDx", 1], [5, 100, 30], [-148, 163, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 1, [[2, "CƯỢC", 20, 50, false, false, 1, 1, 1, -55, [18], 19]], [0, "81nXtpnW5AL6bQjR+nstn2", 1], [5, 100, 30], [22, 163, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 1, [[2, "THẮNG", 20, 50, false, false, 1, 1, 1, -56, [20], 21]], [0, "0a53559upL252lOOew1z4N", 1], [5, 100, 30], [165, 163, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 1, [[2, "CHI TIẾT", 20, 50, false, false, 1, 1, 1, -57, [22], 23]], [0, "83WndWKFtI8p7Dg3+zCoIJ", 1], [5, 100, 30], [319, 163, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "temp", 3, [2], [0, "b6kJnNMZhE7KV9bYK4YbFN", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "lbSessionID", 2, [-58], [0, "eeb8Me/EpN5bTjtzidhFwW", 1], [5, 150, 30], [-318, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "#66553620", 20, 50, false, false, 1, 1, 1, 22, [24]], [9, "lbTime", 2, [-59], [0, "82MQM69yFB6LCoXSOOPT06", 1], [5, 200, 30], [-148, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "17:03 23-03-2019", 20, 50, false, false, 1, 1, 1, 24, [25]], [10, "lbBet", 2, [-60], [0, "8c66pj3AxIub3dNkDT5Vya", 1], [4, 4278255615], [5, 200, 30], [22, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "250.000", 20, 50, false, false, 1, 1, 1, 26, [26]], [10, "lbWin", 2, [-61], [0, "8axnaxM65APIoI4dCK76va", 1], [4, 4278255615], [5, 200, 30], [165, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "50.000.000", 20, 50, false, false, 1, 1, 1, 28, [27]], [28, false, 0.75, 0.23, null, null, 3, 9], [29, 15, 10, 400, 3, 2, 30]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 31, 0, 0, 1, 0, -1, 7, 0, -2, 10, 0, -3, 11, 0, -4, 12, 0, -5, 13, 0, -6, 4, 0, -7, 5, 0, -8, 16, 0, -9, 17, 0, -10, 18, 0, -11, 19, 0, -12, 20, 0, -13, 3, 0, 7, 29, 0, 8, 27, 0, 9, 25, 0, 10, 23, 0, 0, 2, 0, -1, 22, 0, -2, 24, 0, -3, 26, 0, -4, 28, 0, -5, 6, 0, -1, 30, 0, -2, 31, 0, 0, 3, 0, -1, 21, 0, -2, 8, 0, 3, 4, 0, 0, 4, 0, -1, 14, 0, 3, 5, 0, 0, 5, 0, -1, 15, 0, 0, 6, 0, 3, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 0, 20, 0, -1, 23, 0, -1, 25, 0, -1, 27, 0, -1, 29, 0, 11, 1, 2, 12, 21, 61], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 25, 27, 29], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 4, -1, -2, 1, 1, 1, 1], [0, 4, 0, 5, 0, 6, 0, 7, 0, 8, 0, 9, 0, 10, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 0, 0, 0, 2, 0, 3, 3, 11, 2, 2, 2, 2]]