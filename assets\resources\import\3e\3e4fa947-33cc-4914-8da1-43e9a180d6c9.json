[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2tBXzjmRHWIetS1zkxuiC", "017Jn3Zv1Ft7hygdjpaSoK", "8bo+zw+UdH6bz8bdhxNl6r", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "f3TtjOS/NBqbNBHzH/oGL3", "40iEs6WntFCKAZKl2VoxhX", "8evSAQFN9NorJL2WFXV+6p", "0aUJBPIClNhZQYtdPrAy0J", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "root", "_parent", "_N$target", "_defaultClip", "slotsHistoryListView", "lbWin", "lbBet", "lbTime", "lbSessionID", "data"], [["cc.Node", ["_name", "_opacity", "_active", "_skewX", "_prefab", "_components", "_parent", "_contentSize", "_trs", "_children", "_color", "_anchorPoint"], -1, 4, 9, 1, 5, 7, 12, 5, 5], ["cc.Node", ["_name", "_children", "_prefab", "_parent", "_components", "_contentSize", "_trs"], 2, 2, 4, 1, 9, 5, 7], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "_N$normalColor", "clickEvents", "_N$target", "_N$pressedColor", "_N$disabledColor"], 1, 1, 5, 9, 1, 5, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "lbWin", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 1, 5, 5], ["cc.Layout", ["_enabled", "_resize", "_N$layoutType", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["94f0dcGWSRC9ovLsmjATU3F", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["c890fFIrOZAObefUnoFLaiq", ["node", "slotsHistoryListView"], 3, 1, 1]], [[5, 0, 1, 2], [0, 0, 6, 5, 4, 7, 8, 2], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 9], [2, 0, 1, 2, 3, 4, 3], [6, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [1, 0, 3, 1, 4, 2, 5, 6, 2], [4, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 6, 4, 5, 2], [2, 2, 3, 4, 1], [9, 0, 1, 2, 3], [7, 0, 2], [0, 0, 9, 5, 4, 2], [0, 0, 1, 6, 5, 4, 7, 8, 3], [0, 0, 2, 6, 5, 4, 7, 8, 3], [0, 0, 6, 4, 7, 8, 2], [0, 0, 6, 5, 4, 7, 2], [0, 0, 3, 6, 5, 4, 10, 7, 8, 3], [0, 0, 6, 5, 4, 7, 11, 8, 2], [1, 0, 3, 1, 2, 6, 2], [1, 0, 1, 4, 2, 5, 2], [1, 0, 3, 1, 4, 2, 5, 2], [8, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 2, 3, 4, 2], [3, 2, 3, 1], [3, 0, 2, 4, 6, 7, 5, 2], [3, 1, 0, 2, 4, 3, 5, 3], [5, 1, 1], [10, 0, 1, 2, 3, 4, 5, 6, 1], [11, 0, 1, 2, 3, 4, 5, 5], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1], [16, 0, 1, 2, 1], [17, 0, 1, 1]], [[10, "db<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], [11, "db<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", [[-5, -6, -7, [14, "bg<PERSON><PERSON>nt", -9, [0, "acEr/7v65LYKSkuwbdI8ec", -8], [5, 780, 370], [-24, -9, 0, 0, 0, 0, 1, 1, 1, 1]], -10, -11, -12, -13], 1, 1, 1, 4, 1, 1, 1, 1], [[33, -2, [32, 33], 31], [34, -4, -3]], [26, -1]], [19, "<PERSON><PERSON>", [-19, -20, -21, -22, -23], [[27, -18, -17, -16, -15, -14, [4, 4278246399], [4, 4294829568]]], [0, "46QjQSz8dFYJmi9bJZ5sZ8", 1], [5, 780, 37]], [5, "spriteBGTitle", 1, [-25, -26, -27, -28, -29], [[3, 0, false, -24, [22], 23]], [0, "2cXqrYAkZNV4RG/bPID05n", 1], [5, 780, 38], [-24, 200, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "scrollview", 1, [-33, -34], [[-30, -31, [32, -32]], 1, 1, 4], [0, "b4NfQltidH65MDosWe8NIf", 1], [5, 804, 370], [-24, -9, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "BG_Panel_Info_half", 1, [-37], [[8, -36, [6], 7]], [0, "56Zxdm5XVNZZZpOBjeiSzj", -35], [5, 628, 641], [-295.684, 44.804, 0, 0, 0, 0, 1, 0.826, 0.826, 0.826]], [5, "btnClose", 1, [-40], [[24, 3, -39, [[9, "c890fFIrOZAObefUnoFLaiq", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -38]], [0, "27NJiHzo5EHoie0wRndIXO", 1], [5, 80, 80], [436.024, 243.359, 0, 0, 0, 0, 1, 1, 1, 0]], [16, "lbDesc", 10, 2, [[2, "<PERSON>em chi tiết", 20, 48, false, false, 1, 1, 1, -41, [28], 29], [25, 1.1, 3, -43, [[9, "98b467lg1tGSIOSS7iaiI4s", "openDetailClicked", 2]], [4, 4292269782], -42]], [0, "6fqK2c+KtK8ZTGpv7kEIz0", 1], [4, 4278246399], [5, 150, 30], [319, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [12, "black", 100, 1, [[22, 0, -44, [0], 1], [23, -45, [4, 4292269782]]], [0, "c6jnBvuFNN96Tw4YnpA2Z/", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "view", 4, [-47], [[29, 0, -46, [30]]], [0, "4aprA7actCa4Med2Aq9tzr", 1], [5, 803, 370]], [17, "content", 9, [[28, false, 1, 2, 10, -48, [5, 1190, 75]]], [0, "97J6kSQ0hP96Lg5m4BYE+P", 1], [5, 803, 75], [0, 0.5, 1], [0, 182, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "nen popup", false, 1, [[3, 0, false, -49, [2], 3]], [0, "38vMh4ACZLQ7mkp+aSMk32", 1], [5, 854, 497], [-24, 35, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "BG_Panel_Info_half copy", 5, [[8, -50, [4], 5]], [0, "6aQsYvizZAJrzke2DehTMc", 5], [5, 628, 641], [624.751, 0, 0, 0, 0, 0, 1, -1, 1, -1]], [1, "tex_bangthuong", 1, [[3, 2, false, -51, [8], 9]], [0, "986dA/Q0tO1ZWp4Kc6JwUv", 1], [5, 96, 38], [-37, 260, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "sprite", 6, [[3, 2, false, -52, [10], 11]], [0, "8dE/r4TYZDyoYMtegv3CEE", 1], [5, 51, 36]], [1, "lbSessionID", 3, [[2, "PHIÊN", 20, 30, false, false, 1, 1, 1, -53, [12], 13]], [0, "d9itxtVmFOcqfJFORRyYV/", 1], [5, 100, 30], [-318, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 3, [[2, "THỜI GIAN", 20, 30, false, false, 1, 1, 1, -54, [14], 15]], [0, "e4I1L/QXNAs5UJmulTbTc6", 1], [5, 100, 30], [-148, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBet", 3, [[2, "CƯỢC", 20, 30, false, false, 1, 1, 1, -55, [16], 17]], [0, "47K7OUO0lIl6bhXQqKdoAQ", 1], [5, 100, 30], [22, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 3, [[2, "THẮNG", 20, 30, false, false, 1, 1, 1, -56, [18], 19]], [0, "7axeC1fMVHl7KAkvsjENsG", 1], [5, 100, 30], [165, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 3, [[2, "CHI TIẾT", 20, 30, false, false, 1, 1, 1, -57, [20], 21]], [0, "1eWpTz7YZGv69Cllip6CLY", 1], [5, 100, 30], [319, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "temp", 4, [2], [0, "85hfUSGxtEyptdPPecL6+N", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lbSessionID", 2, [-58], [0, "aaNCnrpvBM5YcTWaQu9mdl", 1], [5, 150, 30], [-318, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "#66553620", 20, 48, false, false, 1, 1, 1, 21, [24]], [6, "lbTime", 2, [-59], [0, "32RxGGw7RP1qH6Krcb4inP", 1], [5, 200, 30], [-148, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "17:03 23-03-2019", 20, 48, false, false, 1, 1, 1, 23, [25]], [7, "lbBet", 2, [-60], [0, "da9mOA+b9HsZROkuXLIxv1", 1], [4, 4278255615], [5, 200, 30], [22, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "250.000", 20, 48, false, false, 1, 1, 1, 25, [26]], [7, "lbWin", 2, [-61], [0, "e6Lu9dSkhBgoBldEjBGrT8", 1], [4, 4278255615], [5, 200, 30], [165, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "50.000.000", 20, 48, false, false, 1, 1, 1, 27, [27]], [30, false, 0.75, 0.23, null, null, 4, 10], [31, 15, 10, 400, 4, 2, 29]], 0, [0, 3, 1, 0, 0, 1, 0, 7, 30, 0, 0, 1, 0, -1, 8, 0, -2, 11, 0, -3, 5, 0, 3, 1, 0, 4, 1, 0, -5, 13, 0, -6, 6, 0, -7, 3, 0, -8, 4, 0, 8, 28, 0, 9, 26, 0, 10, 24, 0, 11, 22, 0, 0, 2, 0, -1, 21, 0, -2, 23, 0, -3, 25, 0, -4, 27, 0, -5, 7, 0, 0, 3, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -4, 18, 0, -5, 19, 0, -1, 29, 0, -2, 30, 0, 0, 4, 0, -1, 20, 0, -2, 9, 0, 3, 5, 0, 0, 5, 0, -1, 12, 0, 5, 6, 0, 0, 6, 0, -1, 14, 0, 0, 7, 0, 5, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, -1, 22, 0, -1, 24, 0, -1, 26, 0, -1, 28, 0, 12, 1, 2, 4, 20, 61], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 24, 26, 28], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 2, -1, -1, -1, -1, -1, 1, -1, 6, -1, -2, 1, 1, 1, 1], [0, 5, 0, 6, 0, 3, 0, 3, 0, 7, 0, 8, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 9, 0, 0, 0, 0, 0, 2, 0, 4, 4, 10, 2, 2, 2, 2]]