[1, ["ffReE8I2lNwouyQ6+irnLZ", "ecpdLyjvZBwrvm+cedCcQy", "572jbIldFCAZPMmu+lPXXi", "feA1ILgjpJw6wnH1B8jMrQ", "7a/QZLET9IDreTiBfRn2PD", "017Jn3Zv1Ft7hygdjpaSoK", "d6Rumonm5L1oApv5SZjI1p", "adw94Z+hpN57wutNivq8Q5", "e2xSxof3xCnIoaUKvS6jbh", "96OPi4ffhPuKzYxQTvqdlI", "4a9y6Z6FtALIdImOfKyeR/", "514gKu8r1CM7vpCgqT+NtQ", "2aREFpNxhOsYOgDliuwqce", "19hYVeaHdKE5Mj6knGA8M6", "afvVi5OixJvI1Zr3gEVDcL", "9cmHO2XKpGMKplTPp4g9Je", "8cE1DQAZBOYZnZ+bZvUyca", "a9VpD0DP5LJYQPXITZq+uj", "3c5yg9ZL9KYbuq+0I9LrZ6", "dfQo5p6SVOhImfY9X1/rY7", "27hqkVyjZCTbSHGNw8t10L", "72SQYskd1L76r4vUZ2srkU", "ccPtnC6EJHJbEspbF988Np", "c2QLhumlBOgbxfRUdW5Mu+", "abrt+WDZZCx5N8oTcZe7qN", "7f3xlvR2NLdZpIt1cRi+N8", "fdNoodJKVLj4dF1TLppv2g", "04GkowVEJNh4xR/GBrgFva", "fejefp3nBBkqMgDkJLN18X", "a83jnwg7FHG4CXaHeIW8CG", "4f/NkIDVJBaI+aAwVgyIfO", "75rhPsGoJAIa6H4+19amQG", "2dScjtz4NAZ5yZwXjIM3f7", "a059I3zYJEkIx5vX2d7pOM", "7aLmuUtDJE97+PXLoy+DPY", "fbDkgKcntDvq8Fo5xrxzIh", "0be+dped1Mq4m4CpVFsy0Q", "a1i/vr+NNOTK/C8k5FE/Xf", "e8mKvSNc9I3593bFXyVy1W", "61EjIZbuhCKYjy9Tksf+TI", "9fAU1h4RRFtLazpYTFVeHC", "8fHyFNHiRB97qc0DJBUEe2", "806xS2Qf1J7bzC96VdUiiC", "c2UTpR0HdFKqba10HAXIzA", "afcyw2ieZM+q2fMLR7+4fP", "3cjvVN+BJAA6RMQ2i0zQnv", "4fZQEpQ3JMKbubPQkTP6QC", "20noKQomhDY7uxc91vSK7/", "a9ucUIQkJH3Kohs7n7i/mK", "614RXK7dBDv53vvzJ04hW+", "eeR/3iUhhO9oujYqobUcze", "c8Dvb7c39N7IjSdDhrB4Jn", "88Cx5RxChLD7oeUl8U0Ukr", "a56INqbQFLDq/qJ1P3+Scg", "3buiDM1zJE0qcTPzEPyh17", "ea7e/XWwpIz5uMPFbXhaJN", "f2eC7NmkFPspmcthjURtxj", "28t8fCR09DOZDpR1vsM/QC", "2eywyLVMVCXJB+8zup6ui+", "72qUL7TE1MOpbd3RvJKgOt", "23Lko/HBRBJL+NQeNTL0mo", "e0I0af2g9CsogX4fSNCTpK", "3eC1ziMzVM0bcJElUp/EqS", "b2EB1jAM1EfJDIZRgOkiqd", "58vcvDzl9IUrWIdJSZ7lHL", "08MkEdwP1IOZuAI6OsbDl2", "03pfFbmVtPOYGKSysMCLns", "deX86ehrdI7I9aXL3M7Tvx", "cbF9kFIrJPWaAcUyBzsAb/", "a9Z6PjFIpGOK21hJdG5iCC", "8cstGiIPdJVpDWo9WwDS9m", "d9xY2XwalAgqJ44eO6mMwF", "01GYztcvxP+5hISE0QEqM4", "8c4Oq+kIpGC4IhgNBzPDIJ", "1bjwwvPBFALrztq0QtaEfi", "fdKu5o8g1HQoNG7qoCXSm+", "2cWB/vWPRHja3uQTinHH30", "3excSuGd9JLawr7+eWDqOA", "4fKqQV5r9MuKrawwtJQwZF", "50nuFtegFNDqd/iFN6jCIp", "94kaeXaJRC0rbycNyyZmzA", "8e1bPMvANMWberuCFK/aRL", "04yKbHkGFLdo8nX1tCXQTM", "f9Sx0zqB9PR5TxR66LsGZa", "ae+Ozh5fpNk6ZpC0zuSp5D", "34dkQ0DJhDxY4Hi+0wQ3N+", "51aVBYI1lPiL+q6xbeWt3x", "a4+wjb9ltNJ5THbmpg5rf+", "f4S4i8O1RNW7sL0mgtYQp9", "a7ZIP+aGpJdJPFaAE8axIJ", "f9mM8Js2pA0oXDxDwe8exI", "c28vw+1udNAI0bPq6j6EMS", "59syK6iOtAt6Eilf5FgPiW", "86p1edj/FKKbG8b7gQoG7L", "98LOmh3t5EirJNU3v36MVl", "8f8xdjzqlDKpzEDrRW+2KV", "98XqDChH9OQLFrGXLrap6+", "2cmXBZ7dRD167qIPAuXQsY", "8f8/ondt5JqI1N1BgRtsrA", "7aWkmbgTRAeJ6K0R3SFxVg", "4358BX9IdKt4jj4eTblgZ6", "16iCsDh7FIXYbUKWoW04nw", "c1zTnnSFZMIJwMzhJzbvfD", "63eZgSPJ9KBrU0psOvl5p/", "9aOBTt305NS7ry82XF8Aye", "cazvMvUxlMWoBOnqeU2o+b", "25tK3az+1EXKutaFmoMKFN", "4e0I/zoq1ENbDMLLAsGOaS", "85/HrMisBI468CqiwtXByu", "51FURobl1Nr4PWCapm7EWo", "8bgTgOD6ZASLt/4anXWK2q", "29eJ3yNjBNzIgqevzXKn3w", "14W4ubPARAzKMV92VUqdTI", "bd8Dcm2t9HrayEiZ0PT5Zj", "0fIveeK4JCyYTYTTweNDP0", "57MHW02wJHJYzVFaR90A0l", "30FcjpWFhGDq1rriSHp/YG", "652oUU7OZMVLhA+GrSU3Av", "afao0ThIJADLQV3gc+Iqtk", "114LP/+VVOGIm7iOYZ2P+q", "77FbYmOWhKsKtykwt/3pqd", "068F6TEgRFfYkI7EagJ43b", "d8xOBRSrtN7LOYgHykpEyF"], ["node", "_spriteFrame", "_textureSetter", "_defaultClip", "_N$file", "_N$skeletonData", "_parent", "_N$target", "_file", "target", "root", "nodeScale", "btnScale", "btnSpin", "btnAutoSpin", "btnFastSpin", "btn30000", "btn10000", "btn5000", "btn2000", "btn1000", "tqImage", "lbSessionID", "lbiNormalWin", "lbiBigWin", "lbiJackpotWin", "particleNormalWin", "particleBigWin", "particleJackpot", "nodeNormalWin", "spriteXHu", "lbValueFxX2", "lbBom", "lbRank", "lbTime", "lbTimeEnd", "nodeEventUpcoming", "lbiJackpot", "lbFreeSpin", "data", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], 0, 9, 4, 1, 5, 7, 2, 5, 5, 5], ["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_trs", "_children", "_components", "_parent", "_anchorPoint", "_color"], 1, 4, 5, 7, 2, 12, 1, 5, 5], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_color", "_children"], 1, 1, 2, 4, 5, 7, 5, 5, 2], ["cc.Label", ["_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_string", "_lineHeight", "_enableWrapText", "_N$overflow", "node", "_N$file", "_materials"], -5, 1, 6, 3], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_spriteFrame", "_materials"], 1, 1, 6, 3], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "defaultAnimation", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "_N$enableAutoGrayEffect", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor"], 0, 1, 9, 1, 5, 5, 5], ["cc.AnimationClip", ["_name", "_duration", "curveData", "speed", "events", "wrapMode"], -3], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingY", "_enabled", "_N$paddingTop", "_N$paddingBottom", "_N$spacingX", "node", "_layoutSize"], -4, 1, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["duration", "node"], 2, 1], ["cc.ParticleSystem", ["emissionRate", "life", "lifeVar", "angleVar", "startSize", "endSize", "startSpinVar", "endSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccelVar", "_custom", "totalParticles", "duration", "node", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity"], -13, 1, 8, 8, 8, 8, 5, 5], ["cc.Prefab", ["_name"], 2], ["f74d33qIsFLnqhF+m+kFzXV", ["node"], 3, 1], ["8457bzXi+RCFrWEyccPy/PF", ["_enabled", "touchParent", "node"], 1, 1], ["b0757FMPMxCHIRFCGQRoETZ", ["node", "nodeScale"], 3, 1, 1], ["de66bqKXctK6o3sEBjP6Boq", ["node", "tqImage", "btn1000", "btn2000", "btn5000", "btn10000", "btn30000", "btnFastSpin", "btnAutoSpin", "btnSpin", "btnScale", "spriteScaleIcon"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], ["6e759aDfoBKCpKo3EMS6pEW", ["node", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail"], 3, 1, 6, 6, 6, 6], ["1c836bBjXtMJ4Hyx9UQ01Cc", ["node", "skeletonDataIcons", "sfIcons"], 3, 1, 12, 12], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["bf871/g61xP54pQFlByyiSP", ["node", "iconSkeletons"], 3, 1, 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["bea54aO3OpHZKNA2kiR7IaP", ["node", "tqSpinColumnViews", "lbSessionID"], 3, 1, 2, 1], ["9b478wFzXdLvKAGmn0s3ue1", ["node", "nodeJackpot", "nodeBigWin", "nodeNormalWin", "particleJackpot", "particleBigWin", "particleNormalWin", "lbiJackpotWin", "lbiBigWin", "lbiNormalWin"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["5c1fa7YnkBEMqpU9n30ksJn", ["gameId", "node", "nodeX", "spriteXHu", "lbRemainJackpots"], 2, 1, 1, 1, 2], ["834a9/hDm1OpI1i5dT+udqj", ["node", "nodeKB", "nodeEvent", "nodeEventUpcoming", "lbTimeEnd", "lbTime", "lbRank", "lbBom", "lbValueFxX2"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["3048dLIkeJBsYKzB6a0f0Ul", ["node", "lbiJackpot"], 3, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["c4151REJOpB76AFLIkHKQdt", ["node", "lbFreeSpin"], 3, 1, 1], ["36195eCHJlByb3hYOl3uZl5", ["node", "skeletonIcons", "spriteIcons"], 3, 1, 2, 2], ["9c641mQJtBClLlFzv+vjYnM", ["node", "sfChips", "sfVipChips", "sfFastSpins", "sfAutoSpins", "sfSpins"], 3, 1, 3, 3, 3, 3, 3], ["cc.TTFFont", ["_name", "_native"], 1], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[21, 0, 1, 2], [10, 1, 2, 3, 1], [5, 0, 1, 2, 3, 3], [1, 0, 1, 5, 3, 4, 6, 3], [3, 0, 2, 3, 4, 5, 6, 2], [2, 0, 7, 5, 2, 3, 4, 2], [1, 0, 5, 3, 4, 6, 7, 2], [5, 0, 1, 2, 3], [3, 0, 1, 2, 3, 4, 5, 3], [7, 0, 1, 3, 4, 7, 8, 5, 3], [11, 0, 1, 3, 3], [4, 4, 2, 5, 0, 3, 1, 8, 9, 7], [5, 0, 1, 2, 4, 3, 3], [4, 4, 2, 5, 0, 3, 1, 8, 7], [3, 0, 1, 2, 3, 4, 5, 6, 3], [5, 0, 1, 2, 4, 3], [6, 0, 1, 5, 3], [6, 2, 0, 1, 3, 4, 5], [1, 0, 5, 3, 4, 6, 2], [2, 0, 7, 6, 2, 3, 4, 2], [2, 0, 7, 5, 2, 3, 2], [1, 0, 5, 8, 3, 4, 6, 7, 2], [5, 0, 2, 3, 2], [5, 2, 4, 3, 1], [11, 0, 1, 2, 3, 4], [1, 0, 5, 8, 3, 4, 10, 6, 9, 7, 2], [3, 0, 2, 9, 3, 4, 5, 6, 2], [3, 0, 2, 3, 4, 5, 7, 6, 2], [10, 0, 1, 2, 3, 2], [4, 4, 2, 0, 3, 1, 8, 10, 9, 6], [12, 1, 1], [31, 0, 1, 2, 1], [8, 0, 1, 3, 5, 4, 2, 7], [2, 0, 5, 6, 2, 3, 4, 2], [2, 0, 7, 5, 2, 4, 2], [1, 0, 5, 3, 4, 6, 9, 7, 2], [3, 0, 2, 3, 4, 6, 2], [6, 0, 1, 4, 5, 3], [2, 0, 6, 2, 3, 4, 2], [2, 0, 7, 6, 2, 9, 3, 8, 4, 2], [1, 0, 5, 8, 3, 4, 6, 2], [1, 0, 1, 8, 3, 4, 7, 3], [1, 0, 1, 5, 8, 3, 4, 7, 3], [1, 0, 1, 5, 8, 3, 4, 6, 7, 3], [1, 0, 5, 3, 4, 7, 2], [1, 0, 1, 2, 5, 3, 4, 6, 4], [3, 0, 2, 3, 4, 8, 5, 7, 6, 2], [5, 2, 3, 1], [9, 3, 0, 1, 2, 7, 8, 5], [6, 2, 0, 1, 3, 4, 6, 5], [7, 3, 4, 6, 1], [7, 0, 2, 1, 3, 4, 7, 8, 5, 4], [4, 4, 5, 0, 3, 1, 8, 9, 6], [29, 0, 1, 2, 2], [12, 0, 1, 2], [13, 14, 15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 17], [8, 0, 1, 3, 4, 2, 6], [8, 0, 1, 2, 4], [14, 0, 2], [2, 0, 5, 6, 2, 3, 8, 4, 2], [2, 0, 5, 6, 2, 3, 2], [2, 0, 5, 2, 3, 2], [2, 0, 1, 7, 5, 2, 4, 3], [2, 0, 7, 6, 2, 3, 8, 4, 2], [2, 0, 7, 6, 2, 3, 2], [1, 0, 8, 3, 4, 7, 2], [1, 0, 8, 3, 4, 6, 9, 7, 2], [1, 0, 1, 8, 3, 4, 6, 7, 3], [1, 0, 1, 8, 3, 4, 6, 9, 3], [1, 0, 5, 8, 3, 4, 2], [1, 0, 5, 8, 3, 4, 6, 9, 7, 2], [1, 0, 5, 8, 3, 4, 7, 2], [1, 0, 1, 5, 3, 4, 6, 7, 11, 3], [1, 0, 2, 5, 3, 4, 6, 3], [1, 0, 1, 5, 3, 4, 6, 7, 3], [3, 0, 2, 3, 4, 8, 5, 6, 2], [15, 0, 1], [16, 0, 1, 2, 3], [17, 0, 1, 1], [18, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [19, 0, 1, 2, 3, 4, 1], [20, 0, 1, 2, 1], [22, 0, 1, 1], [9, 0, 1, 4, 5, 2, 7, 8, 6], [9, 3, 0, 1, 6, 7, 8, 5], [6, 2, 0, 1, 3, 5, 5], [6, 2, 0, 3, 4, 6, 4], [23, 0, 1], [7, 0, 1, 3, 4, 6, 5, 3], [7, 3, 4, 6, 5, 1], [24, 0, 1, 2, 1], [25, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [26, 0, 1, 2, 3, 4, 2], [27, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [28, 0, 1, 1], [4, 2, 5, 6, 0, 3, 1, 7, 8, 10, 9, 8], [4, 4, 2, 0, 3, 1, 8, 9, 6], [4, 2, 6, 0, 1, 7, 8, 10, 6], [4, 4, 2, 0, 3, 1, 8, 6], [30, 0, 1, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 16, 17, 18, 19, 20, 21, 22, 15], [32, 0, 1, 2, 3, 4, 5, 1], [33, 0, 1, 3], [34, 0, 1, 2, 3, 4, 5]], [[[{"name": "thele-btn", "rect": [4, 3, 76, 73], "offset": [2, -1.5], "originalSize": [80, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [29]], [[{"name": "line9", "rect": [3, 3, 323, 288], "offset": [-1, 0.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [30]], [[{"name": "sieutoc-dung-btn", "rect": [1, 5, 165, 70], "offset": [-0.5, -2], "originalSize": [168, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [31]], [[{"name": "line16", "rect": [3, 3, 322, 286], "offset": [-1.5, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [32]], [[[32, "columnSpin2", 0.6833333333333333, 8, 2, [{"frame": 0.16666666666666666, "func": "3", "params": []}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 220}, {"frame": 0.6666666666666666, "value": -220}, {"frame": 0.6833333333333333, "value": 220}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 110}, {"frame": 0.5, "value": -220}, {"frame": 0.5166666666666667, "value": 220}, {"frame": 0.6833333333333333, "value": 110}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -220}, {"frame": 0.35, "value": 220}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -110]}, {"frame": 0.16666666666666666, "value": [0, -220]}, {"frame": 0.18333333333333332, "value": [0, 220]}, {"frame": 0.6833333333333333, "value": [0, -110]}]}}}}]], 0, 0, [], [], []], [[{"name": "x2Bom", "rect": [0, 0, 289, 72], "offset": [0, 0], "originalSize": [289, 72], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [33]], [[{"name": "line7", "rect": [3, 3, 324, 286], "offset": [-0.5, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [34]], [[{"name": "line5", "rect": [3, 3, 324, 160], "offset": [-0.5, 64.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [35]], [[{"name": "line26", "rect": [3, 119, 323, 170], "offset": [-1, -56.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [36]], [[[32, "columnStop2", 1.35, 8, "0", [{"frame": 1.35, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 220}, {"frame": 1.3333333333333333, "value": -220}, {"frame": 1.35, "value": 220}], "active": []}}, "slot1": {"props": {"y": [{"frame": 0, "value": 110}, {"frame": 1, "value": -220}, {"frame": 1.0166666666666666, "value": 220}, {"frame": 1.35, "value": 110}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, 0]}, {"frame": 0.6666666666666666, "value": [0, -220]}, {"frame": 0.6833333333333333, "value": [0, 220]}, {"frame": 1.35, "value": [0, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -110]}, {"frame": 0.3333333333333333, "value": [0, -220]}, {"frame": 0.35, "value": [0, 220]}, {"frame": 1.35, "value": [0, -110]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "line10", "rect": [3, 3, 323, 163], "offset": [-1, 63], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [37]], [[{"name": "icon5", "rect": [0, 17, 134, 77], "offset": [0, 1.5], "originalSize": [134, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [38]], [[[58, "tqView"], [59, "tqView", [-21, -22], [[[1, -2, [212, 213], 211], [76, -3], [77, false, null, -4], [78, -6, -5], [79, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, [224, 225]], [80, -18, 226, 227, 228, 229], -19, [81, -20, [[230, 231, 232, 233, null, null, null, null, null], 6, 6, 6, 6, 0, 0, 0, 0, 0], [[null, null, null, null, 234, 235, 236, 237, 238], 0, 0, 0, 0, 6, 6, 6, 6, 6]]], 4, 4, 4, 4, 4, 4, 1, 4], [0, "89VqStrTlFpJvaIK/1KJBX", -1], [5, 780, 470], [0, 0.5, 0.49], [-93, 25, 0, 0, 0, 0, 1, 1, 1, 1]], [65, "payLineView", [-33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59], [[82, -32, [-23, -24, -25, -26, -27, -28, -29, -30, -31]]], [0, "38zgzL6eFLPZHXN+kFmy50", 1], [-70, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "offset-scale", 1, [-60, -61, -62, 2, -63, -64, -65, -66, -67], [0, "de3D4NwzlJe4uw1mMPweVX", 1], [0, 0, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [66, "layout", [-70, -71, -72, -73, -74], [[22, 0, -68, 7], [83, 1, 2, 25, 25, 15, -69, [5, 200, 235]]], [0, "e88x68kEZOS6dKV9/PnhqP", 1], [5, 200, 235], [0, 0.5, 1], [0, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "layout-left", 3, [-76, -77, -78, -79, -80, -81], [[48, false, 1, 2, -5, -75, [5, 60, 455]]], [0, "afRWdBJ89IAIWzkPXRCC9/", 1], [5, 60, 455], [-324, -78, 0, 0, 0, 0, 1, 1, 1, 1]], [67, "even_VBom", false, [-87, -88, -89], [[49, "animation", 0, false, "animation", -82, 210], [87, -83], [88, 0.95, 3, -86, [[10, "834a9/hDm1OpI1i5dT+udqj", "openEventClicked", -85]], [4, 4292269782], -84]], [0, "08QYMlCVNIj49S7MYzo+Au", 1], [5, 174.65, 171.88], [151, -34, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "event", 6, [-90, -91, -92, -93, -94, -95], [0, "74ym977KhNS5GGwZAGDUVY", 1], [-7, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [68, "offset-xHu", false, [-99, 4, -100], [[89, -98, [[10, "5c1fa7YnkBEMqpU9n30ksJn", "openEventClicked", -97]], [4, 4292269782], -96]], [0, "e2f96WT2JLZqDljD4V/NY2", 1], [5, 190, 240], [0, 0.5, 0.9]], [34, "lbRoomVal", 3, [-101, -102, -103, -104, -105, -106], [0, "44CLZ3o/hDxbmWjJUxStpC", 1], [-324, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "spinView", 3, [-114, -115, -116, -117, -118], [[90, -113, [-108, -109, -110, -111, -112], -107]], [0, "c8WerTBMtNYK0Gf/VDIVQ1", 1], [5, 550, 308]], [33, "1", [-121, -122, -123, -124], [[[1, -119, [38, 39, 40, 41, 42], 37], -120], 4, 1], [0, "c3f/oq99dM7KkFrYmIvJBj", 1], [5, 72, 300], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "2", [-127, -128, -129, -130], [[[1, -125, [48, 49, 50, 51, 52], 47], -126], 4, 1], [0, "97K6lifTxCqZKCuTimcR5+", 1], [5, 72, 300]], [33, "3", [-133, -134, -135, -136], [[[1, -131, [58, 59, 60, 61, 62], 57], -132], 4, 1], [0, "8f9dhd3nxGcZ6fiSzPA1Yp", 1], [5, 72, 300], [120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "4", [-139, -140, -141, -142], [[[1, -137, [69, 70, 71, 72, 73], 68], -138], 4, 1], [0, "cevGGy/NJHvr130ql7veiN", 1], [5, 72, 300], [-55, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "5", [-145, -146, -147, -148], [[[1, -143, [79, 80, 81, 82, 83], 78], -144], 4, 1], [0, "bbIbJizw5A0abjTnB8o8HN", 1], [5, 72, 300], [55, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "layout-right", 3, [-150, -151, -152, -153, -154], [[48, false, 1, 1, 8, -149, [5, 317, 327]]], [0, "4aWgdeatxFgaNsA0Q+wqnQ", 1], [5, 317, 80], [259.7, 158.3, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "offset-jackpot", false, [-156, -157, -158, -159], [[28, true, -155, [192], 191]], [0, "91NoNhLJJMVIj1GC/4j+rx", 1], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "offset-bigWin", false, [-161, -162, -163, -164], [[28, true, -160, [197], 196]], [0, "58iMRWYBRIKLd285Fwt3ka", 1], [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [69, "effect<PERSON>iew", 3, [17, 18, -173], [[91, -172, 17, 18, -171, -170, -169, -168, -167, -166, -165]], [0, "10q6SFtahJLb0RV95HlEC4", 1]], [42, "offset-normalWin", false, 19, [-175, -176, -177], [[28, true, -174, [201], 200]], [0, "14a7kZ75VDapxw5Ae9ZLcq", 1], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [70, "xHuView", 1, [8], [[92, 12, -184, 8, -183, [-178, -179, -180, -181, -182]]], [0, "b8K/agyLlKkI6ZHgVAkKJ8", 1], [5, 190, 240], [0, 0.5, 0.75], [-443, 111, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "layout-btn", 3, [-186, -187, -188], [[84, false, 1, 1, 2, -185, [5, 454, 66]]], [0, "casMMRgvZMsLlFkD8P2b2y", 1], [5, 454, 66], [0, -250, 0, 0, 0, 0, 1, 1, 1, 1]], [71, "eventView", 3, [6], [[93, -195, 6, 7, -194, -193, -192, -191, -190, -189]], [0, "feE4BSYSpAy7M+wjA0+Nr5", 1], [318, -178, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btn1000", 5, [-197], [-196], [0, "83qftRti9C7Lq4ucjwlyCq", 1], [5, 80, 80], [0, 187.5, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btn2000", 5, [-199], [-198], [0, "a8NgeW05JHr4Wlhxmn5vbR", 1], [5, 80, 80], [0, 112.5, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btn5000", 5, [-201], [-200], [0, "b00IMfRtZBoryNYoIA1UU9", 1], [5, 80, 80], [0, 37.5, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btn10000", 5, [-203], [-202], [0, "26pv7ivxVBJ4EJ6uX2IZzW", 1], [5, 80, 80], [0, -37.5, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "btn30000", 5, [-205], [-204], [0, "0eV+Wn3pVL9rrwWSX65cL6", 1], [5, 80, 80], [0, -112.5, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "btn100K", false, 5, [-208], [[9, 1.1, 3, -207, [[24, "de66bqKXctK6o3sEBjP6Boq", "roomClicked", "7", 1]], [4, 4294967295], [4, 4294967295], -206]], [0, "6dcGgrKrdLCLce3TzIjffj", 1], [5, 80, 80], [0, -187.5, 0, 0, 0, 0, 1, 1, 1, 1]], [61, "slots", [11, 12, 13], [0, "04Wrx5bfVPhJEnlUlZ/0dL", 1], [5, 320, 260]], [21, "jackpotView", 10, [-211, -212], [[94, -210, -209]], [0, "38B40V2n1O8IijWzc+9ID6", 1], [5, 200, 34], [-39.2, 170.2, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnScale", 16, [[[12, 2, false, -213, [172], 173], -214], 4, 1], [0, "ffF7p3EO9Cj5RqHTmOsqzR", 1], [5, 80, 76], [-133, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnHistory", 16, [[12, 2, false, -215, [174], 175], [9, 1.1, 3, -217, [[10, "de66bqKXctK6o3sEBjP6Boq", "historyClicked", 1]], [4, 4294967295], [4, 4294967295], -216]], [0, "82LNFOFgxFkqs/VsTqHbn1", 1], [5, 80, 76], [-39, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnTop", 16, [[12, 2, false, -218, [176], 177], [9, 1.1, 3, -220, [[10, "de66bqKXctK6o3sEBjP6Boq", "topClicked", 1]], [4, 4294967295], [4, 4294967295], -219]], [0, "8fl0sitU5A24H1DS8CblBF", 1], [5, 80, 76], [58, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnHelp", 16, [[12, 2, false, -221, [178], 179], [9, 1.1, 3, -223, [[10, "de66bqKXctK6o3sEBjP6Boq", "helpClicked", 1]], [4, 4294967295], [4, 4294967295], -222]], [0, "e9rxT6zeZA5Ic3cNK1Ol9D", 1], [5, 80, 76], [141, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 16, [[12, 2, false, -224, [180], 181], [9, 1.1, 3, -226, [[10, "b0757FMPMxCHIRFCGQRoETZ", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -225]], [0, "a12cerzsRE6IMradmaoJkh", 1], [5, 77, 75], [142, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnFastSpin", 22, [[[12, 2, false, -227, [182], 183], -228], 4, 1], [0, "74nSD7XThEwohGPn5ksMff", 1], [5, 168, 76], [-180, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnAutoSpin", 22, [[[12, 2, false, -229, [184], 185], -230], 4, 1], [0, "3ds+iZcwBPQY/jfs1VNqcZ", 1], [5, 168, 76], [15.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnSpin", 22, [[[12, 2, false, -231, [186], 187], -232], 4, 1], [0, "64Mk5QLBlIDpLjE8Xqp/QN", 1], [5, 212, 88], [235, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "eventUpcoming", false, 6, [-233, -234], [0, "7c3jqFwWlJE4nqP9ASGUgA", 1], [-7, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "fxX2", false, 6, [-236, -237], [[28, true, -235, [209], 208]], [0, "c3svNf031OtbXFfMGihNKs", 1], [-542, 105, 0, 0, 0, 0, 1, 1, 1, 1]], [63, "logo", 8, [[-238, [28, true, -239, [1], 0]], 1, 4], [0, "cfIr9A4F5I+4zZsHXxxpN2", 1], [5, 90, 44], [0, 0.5, 1], [0, 58.1, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "lbRoom", 4, [-241], [[11, "1K:", 20, 50, false, 1, 1, -240, 2]], [0, "abm0tvgvNPcreScTcgAeZj", 1], [4, 4278255615], [5, 29.5, 25], [0, 0, 0.5], [-80.1, -37.5, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "lbRoom", 4, [-243], [[11, "2K:", 20, 50, false, 1, 1, -242, 3]], [0, "5fiYnEyCpEXb5bi8yqurlE", 1], [4, 4278255615], [5, 29.5, 25], [0, 0, 0.5], [-80.1, -77.5, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "lbRoom", 4, [-245], [[11, "5K:", 20, 50, false, 1, 1, -244, 4]], [0, "c5DhmWYxRLUrHwrA6zVBPT", 1], [4, 4278255615], [5, 29.5, 25], [0, 0, 0.5], [-80.1, -117.5, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "lbRoom", 4, [-247], [[11, "10K:", 20, 50, false, 1, 1, -246, 5]], [0, "f0OMhDSEZAbLyihVMXCMr9", 1], [4, 4278255615], [5, 41, 25], [0, 0, 0.5], [-80.1, -157.5, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "lbRoom", 4, [-249], [[11, "30K:", 20, 50, false, 1, 1, -248, 6]], [0, "6eLXrUFy5Afr74+0jEuwZT", 1], [4, 4278255615], [5, 41, 25], [0, 0, 0.5], [-80.1, -197.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "close", 8, [[22, 0, -250, 8], [50, -251, [[10, "5c1fa7YnkBEMqpU9n30ksJn", "onClickHide", 21]], [4, 4292269782]]], [0, "c0yCvG7FVOsYZ7vcJT75xe", 1], [5, 36, 36], [-94.1, 14.9, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "mask", 10, [30], [[53, 1, -252, [63]]], [0, "37Coxj5lNJhasAaBOmYyR1", 1], [5, 360, 329], [-70, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot0", 11, [-253, -254], [0, "a2SlqvIqJO6ZPdwomdkM+5", 1], [5, 72, 96], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot1", 11, [-255, -256], [0, "9880rwMoVJwL1EHB61JP0b", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 0, false, [34]], [20, "slot2", 11, [-257, -258], [0, "82lXEkChhL2IOHlfTAzNzQ", 1], [5, 72, 96]], [16, 0, false, [35]], [5, "slot3", 11, [-259, -260], [0, "deFKLNx81OvYUs4TBPRu/3", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 0, false, [36]], [5, "slot0", 12, [-261, -262], [0, "2fIp3VqBVFCYs0bUJQDIcw", 1], [5, 72, 96], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot1", 12, [-263, -264], [0, "18NAicGsBAVZ9/JrQmDqoQ", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 0, false, [44]], [20, "slot2", 12, [-265, -266], [0, "0biSlVxPZG761YH8zp92cJ", 1], [5, 72, 96]], [16, 0, false, [45]], [5, "slot3", 12, [-267, -268], [0, "58jkCVtXhHg7W8zIwOdk2P", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 0, false, [46]], [5, "slot0", 13, [-269, -270], [0, "f9J5ExCLdLR43I18s8uzjZ", 1], [5, 72, 96], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot1", 13, [-271, -272], [0, "e3272iwMtAt5gEkiXMNfW8", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 0, false, [54]], [20, "slot2", 13, [-273, -274], [0, "6cSsz08m5PfJHQI+Pv7Idj", 1], [5, 72, 96]], [16, 0, false, [55]], [5, "slot3", 13, [-275, -276], [0, "96LfI3T7BOAZ25uUMVW/1W", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [85, "Idle", 0, false, "Idle", [56]], [21, "mask-kernel", 10, [-278], [[53, 0, -277, [84]]], [0, "1bHyO9VY9OQpT2C4gASMwZ", 1], [5, 222, 276], [246, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "slots", 71, [14, 15], [0, "168+GUYndCEqmPYbMJZgNq", 1], [5, 222, 276]], [5, "slot0", 14, [-279, -280], [0, "940h3vhE9Epb5YrhtYMVhS", 1], [5, 72, 96], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot1", 14, [-281, -282], [0, "23uihHUqdEoqLp84wqv4CP", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "slot2", 14, [-283, -284], [0, "5d4QNOKadEC5GjS/XH9hH1", 1], [5, 72, 96]], [5, "slot3", 14, [-285, -286], [0, "94jWOQgLhN37pORNTiFsOx", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot0", 15, [-287, -288], [0, "44pOna0mFCg6H336+WhzQ9", 1], [5, 72, 96], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "slot1", 15, [-289, -290], [0, "2eaCaSNV5EILTiqkLurYgb", 1], [5, 72, 96], [0, 110, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "slot2", 15, [-291, -292], [0, "b0/VpKIN9MfrKRUpy2Fx4S", 1], [5, 72, 96]], [5, "slot3", 15, [-293, -294], [0, "415uo8nzZILoyKDcz+wcYy", 1], [5, 72, 96], [0, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "lbJackpot", 31, [[[95, 32, 32, false, false, 1, 1, 2, -295, [86], 87], -296], 4, 1], [0, "c3lQC+WKFKN5JCrtXgKSNi", 1], [5, 196, 32], [-1.592, -7.536, 0, 0, 0, 0, 1, 1.022, 1.022, 1.022]], [3, "1", false, 2, [[2, 2, false, -297, 90], [1, -298, [92], 91]], [0, "40Sl8b39FHJqNcm2A9nVrf", 1], [5, 331, 295]], [3, "2", false, 2, [[2, 2, false, -299, 93], [1, -300, [95], 94]], [0, "0cyNuSkPZIMIggS1ztwWI1", 1], [5, 331, 295]], [3, "3", false, 2, [[2, 2, false, -301, 96], [1, -302, [98], 97]], [0, "04feZdE3VDCoi0hJLjaHKl", 1], [5, 331, 295]], [3, "4", false, 2, [[2, 2, false, -303, 99], [1, -304, [101], 100]], [0, "73LCJjXY5EBr53+W8XwF0C", 1], [5, 331, 295]], [3, "5", false, 2, [[2, 2, false, -305, 102], [1, -306, [104], 103]], [0, "96D914+MFFgqeo8EMgOWIH", 1], [5, 331, 295]], [3, "6", false, 2, [[2, 2, false, -307, 105], [1, -308, [107], 106]], [0, "250/yH95BHRZLX0NBx0gzy", 1], [5, 331, 295]], [72, "7", false, 2, [[2, 2, false, -309, 108], [1, -310, [110], 109]], [0, "d2z59MMyhEQJHXpZppMx35", 1], [5, 331, 295], [0, 0, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [3, "8", false, 2, [[2, 2, false, -311, 111], [1, -312, [113], 112]], [0, "97W2Hkny1PsYbNy6kWbemd", 1], [5, 331, 295]], [3, "9", false, 2, [[2, 2, false, -313, 114], [1, -314, [116], 115]], [0, "f40Hxl6IlL87sTfaFEzHIU", 1], [5, 331, 295]], [3, "10", false, 2, [[2, 2, false, -315, 117], [1, -316, [119], 118]], [0, "2dpx2KN1VPo7L9u4pHxFuc", 1], [5, 331, 295]], [3, "11", false, 2, [[2, 2, false, -317, 120], [1, -318, [122], 121]], [0, "272fjyzdZOo67E2CWXBDKE", 1], [5, 331, 295]], [3, "12", false, 2, [[2, 2, false, -319, 123], [1, -320, [125], 124]], [0, "5cF/nON99Nz502WDr4W9ug", 1], [5, 331, 295]], [3, "13", false, 2, [[2, 2, false, -321, 126], [1, -322, [128], 127]], [0, "f6vnL86EZFBKUztPzpLMsD", 1], [5, 331, 295]], [3, "14", false, 2, [[2, 2, false, -323, 129], [1, -324, [131], 130]], [0, "6cWuuams9LNZonj1refnQp", 1], [5, 331, 295]], [3, "15", false, 2, [[2, 2, false, -325, 132], [1, -326, [134], 133]], [0, "f1TbT8dKRBabRw5xVn8Y+G", 1], [5, 331, 295]], [3, "16", false, 2, [[2, 2, false, -327, 135], [1, -328, [137], 136]], [0, "7fS0/IyFRKHIEwy8IEEcDm", 1], [5, 331, 295]], [3, "17", false, 2, [[2, 2, false, -329, 138], [1, -330, [140], 139]], [0, "6410a8dgxOpp81RO9Eyj/3", 1], [5, 331, 295]], [3, "18", false, 2, [[2, 2, false, -331, 141], [1, -332, [143], 142]], [0, "d6kEk1dDZIco969n509w9/", 1], [5, 331, 295]], [3, "19", false, 2, [[2, 2, false, -333, 144], [1, -334, [146], 145]], [0, "20efKGzSdIzJ6pmc/pUFKM", 1], [5, 331, 295]], [3, "20", false, 2, [[2, 2, false, -335, 147], [1, -336, [149], 148]], [0, "e3r0s5ii1D8LSSsj6foVLS", 1], [5, 331, 295]], [3, "21", false, 2, [[2, 2, false, -337, 150], [1, -338, [152], 151]], [0, "bd7CYwwEFFNbDc9TsqgThY", 1], [5, 331, 295]], [3, "22", false, 2, [[2, 2, false, -339, 153], [1, -340, [155], 154]], [0, "712WfsyHtCO5WCRpm9vS1O", 1], [5, 331, 295]], [3, "23", false, 2, [[2, 2, false, -341, 156], [1, -342, [158], 157]], [0, "48S4IXU81Jn5/ARvzY2/ur", 1], [5, 331, 295]], [3, "24", false, 2, [[2, 2, false, -343, 159], [1, -344, [161], 160]], [0, "a3oVPupdFOrJQtO9u8RdGB", 1], [5, 331, 295]], [3, "25", false, 2, [[2, 2, false, -345, 162], [1, -346, [164], 163]], [0, "c81ijaU6lOM4IxZ6b6IyRY", 1], [5, 331, 295]], [3, "26", false, 2, [[2, 2, false, -347, 165], [1, -348, [167], 166]], [0, "cbnEL7O6ZFpZqV4Rm5iz6T", 1], [5, 331, 295]], [3, "27", false, 2, [[2, 2, false, -349, 168], [1, -350, [170], 169]], [0, "77UTI+cfpDpY4PIPpVIkhH", 1], [5, 331, 295]], [40, "freeSpinView", 3, [-353], [[99, -352, -351]], [0, "40k4VgxApDDpw+Gt6s8zDN", 1], [5, 362, 44]], [43, "spriteBG", false, 109, [-355], [[22, 0, -354, 171]], [0, "ebopkH2yNLP6Q1o9xhF0mH", 1], [5, 362, 30], [-69, -182, 0, 0, 0, 0, 1, 1, 1, 1]], [64, "lbFreeSpin", 110, [[-356, [30, -357]], 1, 4], [0, "44gqNr5oxNdrCRzSTWX7Ox", 1], [5, 161.45, 22.5]], [73, "black", 1, 17, [[22, 0, -358, 188], [50, -359, [[10, "9b478wFzXdLvKAGmn0s3ue1", "continueClicked", 19]], [4, 4292269782]]], [0, "77y/+yn9ZFv5mMSGFs3SFh", 1], [5, 3000, 3000]], [38, "lbWin", [[[52, "100.000", 64, false, 1, 1, -360, 190], -361], 4, 1], [0, "373EbI6NdGToJbc7/SYLP0", 1], [5, 325, 80], [0, -89, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "lbWin", [[[52, "100.000", 64, false, 1, 1, -362, 195], -363], 4, 1], [0, "59MQxYb39MjZ3jll/CIK8C", 1], [5, 325, 80], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "lbWin", 20, [[[11, "100.000", 32, 64, false, 1, 1, -364, 199], -365], 4, 1], [0, "15+3GbBrdMwZB6XOJtvhN8", 1], [5, 260, 64], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "lbBom", 7, [[[11, "0", 20, 30, false, 1, 1, -366, 205], -367], 4, 1], [0, "ebDbxyM8lGTaaTKRF58mXo", 1], [4, 4278315513], [5, 12, 15], [0, 0, 0.5], [-15.5, 1.7, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "lbTime", 7, [[-368, [54, 0.5, -369]], 1, 4], [0, "cfbOGmpoBFEoO3TsKssyCs", 1], [4, 4278315513], [5, 48.9, 9], [0, 0, 0.5], [12.7, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 2, false, 42], [27, "lbRemain", 43, [-370], [0, "91ht9aS49G06Pr2SslSUqc", 1], [5, 102.5, 25], [0, 0, 0.5], [56.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 26 HŨ", 20, 50, false, 1, 1, 119], [27, "lbRemain", 44, [-371], [0, "f5JG9YOTJE+LgclU5TwD1t", 1], [5, 102.5, 25], [0, 0, 0.5], [56.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 26 HŨ", 20, 50, false, 1, 1, 121], [27, "lbRemain", 45, [-372], [0, "aeNk6Oh61FLrXEwCNXueTt", 1], [5, 91, 25], [0, 0, 0.5], [56.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 2 HŨ", 20, 50, false, 1, 1, 123], [27, "lbRemain", 46, [-373], [0, "aelr076gBO+7AI3lgpw+Hz", 1], [5, 91, 25], [0, 0, 0.5], [56.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 2 HŨ", 20, 50, false, 1, 1, 125], [27, "lbRemain", 47, [-374], [0, "18iD/IrqpAHZ0Vh3qQSrP7", 1], [5, 91, 25], [0, 0, 0.5], [56.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "CÒN 0 HŨ", 20, 50, false, 1, 1, 127], [18, "phong2-bg", 24, [[23, -375, [9], 10]], [0, "f3FkRtGh5JGaN8VzGYkLrj", 1], [5, 139, 139]], [9, 1.1, 3, 24, [[24, "de66bqKXctK6o3sEBjP6Boq", "roomClicked", "1", 1]], [4, 4294967295], [4, 4294967295], 24], [18, "phong-bg", 25, [[23, -376, [11], 12]], [0, "a0kPS70SBHEIF2rQ2Fcso2", 1], [5, 72, 90]], [9, 1.1, 3, 25, [[24, "de66bqKXctK6o3sEBjP6Boq", "roomClicked", "2", 1]], [4, 4294967295], [4, 4294967295], 25], [18, "phong-bg", 26, [[23, -377, [13], 14]], [0, "e97Ym9F41IO5+FKeB8FIpg", 1], [5, 72, 90]], [9, 1.1, 3, 26, [[24, "de66bqKXctK6o3sEBjP6Boq", "roomClicked", "3", 1]], [4, 4294967295], [4, 4294967295], 26], [18, "phong-bg", 27, [[23, -378, [15], 16]], [0, "1aVyRf8ZNFupnP+wdqPPxk", 1], [5, 72, 90]], [9, 1.1, 3, 27, [[24, "de66bqKXctK6o3sEBjP6Boq", "roomClicked", "4", 1]], [4, 4294967295], [4, 4294967295], 27], [18, "phong-bg", 28, [[23, -379, [17], 18]], [0, "87Mg6edDhCUK+mPj3fmtvK", 1], [5, 72, 90]], [9, 1.1, 3, 28, [[24, "de66bqKXctK6o3sEBjP6Boq", "roomClicked", "5", 1]], [4, 4294967295], [4, 4294967295], 28], [18, "phong-bg", 29, [[47, -380, 19]], [0, "704hSVsGhKAKWIUr6+xWaI", 1], [5, 72, 90]], [74, "lb100K", false, 9, [[96, "100K", 22, false, 1, 1, -381, 20]], [0, "449sCKt+JKA5f+HsYGa8q9", 1], [5, 56.31, 40], [0, -233.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lb30K", 9, [[29, "30K", 22, false, 1, 1, -382, [21], 22]], [0, "4f9M1iTzJDorvwTAT9+jlH", 1], [5, 42.72, 50.4], [0, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lb10K", 9, [[29, "10K", 22, false, 1, 1, -383, [23], 24]], [0, "bchpMR3ihBeIruJrMs9tj3", 1], [5, 42.72, 50.4], [0, -83.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lb5K", 9, [[29, "5K", 22, false, 1, 1, -384, [25], 26]], [0, "91vVCFiX5PqorpOLCk9Ctn", 1], [5, 29.13, 50.4], [0, -8.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lb2K", 9, [[29, "2K", 22, false, 1, 1, -385, [27], 28]], [0, "f6yDsHCiNHdbdO5rsk/b9H", 1], [5, 29.13, 50.4], [0, 66.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "lb1K", 9, [[29, "1K", 22, false, 1, 1, -386, [29], 30]], [0, "ccy6GGWd9KmbhfXdTcXxqN", 1], [5, 29.13, 50.4], [0, 141.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "bg", 10, [[23, -387, [31], 32]], [0, "b4dcHw7ztC4K1smvtZuJa6", 1], [5, 833, 593], [0, 30, 0, 0, 0, 0, 1, 0.93, 0.93, 1]], [4, "skeleton", 50, [-388], [0, "95NbAQ4UpNO4hI1JsAQ2aT", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 0, false, 147, [33]], [8, "icon", false, 50, [-389], [0, "78GvlbbmxFB42pas4jACd/", 1], [5, 134, 114]], [7, 2, false, 149], [4, "skeleton", 51, [52], [0, "bcLzFEM2pAG5hS/bY+QQex", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 51, [-390], [0, "52Flb34j1JUIL17xYrAa1G", 1], [5, 134, 114]], [7, 2, false, 152], [4, "skeleton", 53, [54], [0, "9aUSLcdp5DzbsHyPXTFaQS", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 53, [-391], [0, "17cJwDKt9LSa2kqYtlUlQW", 1], [5, 134, 114]], [7, 2, false, 155], [4, "skeleton", 55, [56], [0, "61SN1jsFBFXaLQP6zIEnLi", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 55, [-392], [0, "b5uaiYCz1AzbsfWsZ55VFm", 1], [5, 134, 114]], [7, 2, false, 158], [31, 11, [148, 52, 54, 56], [150, 153, 156, 159]], [4, "skeleton", 57, [-393], [0, "c1AgXNvmpGc5mcAoIFy1dc", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 0, false, 161, [43]], [8, "icon", false, 57, [-394], [0, "76cQbgADFCFKuhRt5oZ5mh", 1], [5, 134, 114]], [7, 2, false, 163], [4, "skeleton", 58, [59], [0, "37DcT5GA5LlIQ9lJuzOHDv", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 58, [-395], [0, "e0TMiFM5ZF3pEwiOkFX8Uy", 1], [5, 134, 114]], [7, 2, false, 166], [4, "skeleton", 60, [61], [0, "b96W6z5O1Bb5z6A2bpzpua", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 60, [-396], [0, "2eRynFmc5OtaYxbB1k+kN5", 1], [5, 134, 114]], [7, 2, false, 169], [4, "skeleton", 62, [63], [0, "76VHsou3tKYqEh6NUlcEfe", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 62, [-397], [0, "7dezuAR3VNEKNccT9Ts2BV", 1], [5, 134, 114]], [7, 2, false, 172], [31, 12, [162, 59, 61, 63], [164, 167, 170, 173]], [4, "skeleton", 64, [-398], [0, "565CUMqjBGsozVfbjGoigH", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [37, 0, false, 175, [53]], [8, "icon", false, 64, [-399], [0, "ccnCupNq1MGptYxEEV3sDj", 1], [5, 134, 114]], [7, 2, false, 177], [4, "skeleton", 65, [66], [0, "1dFsbk04VE665Mhpe0DQmr", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 65, [-400], [0, "f4ZPnoCwlAO4P3tqJiAMk5", 1], [5, 134, 114]], [7, 2, false, 180], [4, "skeleton", 67, [68], [0, "8erY3bQk5CB455QoZSWpaO", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 67, [-401], [0, "faPxFza6ZLfYAZiTltbB+G", 1], [5, 134, 114]], [7, 2, false, 183], [4, "skeleton", 69, [70], [0, "7caTqMgRtP/ZMV4sDEfOxh", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "icon", false, 69, [-402], [0, "20cmSkDINCS7yV8tTlRxiA", 1], [5, 134, 114]], [7, 2, false, 186], [31, 13, [176, 66, 68, 70], [178, 181, 184, 187]], [14, "skeleton", false, 73, [-403], [0, "0ckqq6ZzdN3ZV1Sr1bpNrl", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 189], [4, "icon", 73, [-404], [0, "7a8KB/WAJJaKezc4490FtV", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 191, [64]], [14, "skeleton", false, 74, [-405], [0, "6e3vDZRPpERb+cpYBRepeJ", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 193], [4, "icon", 74, [-406], [0, "e1Gf173zpAYKTiBs5N728u", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 195, [65]], [14, "skeleton", false, 75, [-407], [0, "2bNpYXye5Ho6ExyzWWc/9y", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 197], [4, "icon", 75, [-408], [0, "a7McNTFjZGUKmn8bYahT9R", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 199, [66]], [14, "skeleton", false, 76, [-409], [0, "22ls/aFWFHP5QPphbktRH2", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 201], [4, "icon", 76, [-410], [0, "65HhKvNJdJHouQyPPtxkIr", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 203, [67]], [31, 14, [190, 194, 198, 202], [192, 196, 200, 204]], [14, "skeleton", false, 77, [-411], [0, "65Meu++8ZHe79HwgjbKE2r", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 206], [4, "icon", 77, [-412], [0, "183CZffcdPzomUdbrpFBgC", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 208, [74]], [14, "skeleton", false, 78, [-413], [0, "41HY0oE71H7Y4/KQ//fEGt", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 210], [4, "icon", 78, [-414], [0, "62f/1qK/dARbgEPn+Zz/30", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 212, [75]], [14, "skeleton", false, 79, [-415], [0, "6ekt9AddxCTZgUh5/sWm6e", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 214], [4, "icon", 79, [-416], [0, "77EVZPf3VPZ57rOlN5u2dd", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 216, [76]], [14, "skeleton", false, 80, [-417], [0, "cd9JxzMYVKd6SLQX76spGx", 1], [5, 134.27, 114.32], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "Idle", 0, false, "Idle", 218], [4, "icon", 80, [-418], [0, "10Z1OTKeZK/aSsJTnI4S2H", 1], [5, 134, 114], [0, -10, 0, 0, 0, 0, 1, 1, 1, 1]], [15, 2, false, 220, [77]], [31, 15, [207, 211, 215, 219], [209, 213, 217, 221]], [46, "lbSessionId", 10, [-419], [0, "74Lgt+5MFM8Yukgsgj7JfD", 1], [4, 4291940817], [5, 100, 40], [0, 0, 0.5], [133.4, 121.6, 0, 0, 0, 0, 1, 1, 1, 1]], [97, 11, false, false, 1, 1, 223, [85]], [30, 81], [6, "tien", 31, [[12, 2, false, -420, [88], 89]], [0, "37oA7AENZDYqbRrZH5lPSs", 1], [5, 67, 69], [-128.5, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [13, "QUAY MIỄN PHÍ: X1", 18, 50, false, 1, 1, 111], [9, 1.1, 3, 32, [[10, "de66bqKXctK6o3sEBjP6Boq", "scaleClick", 1]], [4, 4294967295], [4, 4294967295], 32], [9, 1.1, 3, 37, [[10, "de66bqKXctK6o3sEBjP6Boq", "fastSpinClicked", 1]], [4, 4294967295], [4, 4294967295], 37], [51, 1.1, true, 3, 38, [[10, "de66bqKXctK6o3sEBjP6Boq", "autoSpinClicked", 1]], [4, 4294967295], [4, 4294967295], 38], [51, 1.1, true, 3, 39, [[10, "de66bqKXctK6o3sEBjP6Boq", "spinClicked", 1]], [4, 4294967295], [4, 4294967295], 39], [36, "particleBigWin", 17, [-421], [0, "63hx+TM5dHTK9jj8V+zkHi", 1], [0, 121, 0, 0, 0, 0, 1, 1, 1, 1]], [100, 100, 1.5, 0.5, 58, 40, -1, 51, 500, 1, 850, 383, 0, 50, {"custom": false}, 232, [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 101, 73], [0, 0, -2400]], [44, "noHu_mn", 17, [[86, "NoHu", 0, "NoHu", -422, 189]], [0, "58H2lOuPlAcbCHjrpLxDpb", 1], [21.66667, 82.08333, 0, 0, 0, 0, 1, 0.83333, 0.83333, 0.83333]], [5, "1", 17, [113], [0, "c2U8yIApRCXLDoh/ZF3nDe", 1], [5, 362, 74], [27, 27, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 113], [45, "black", false, 1, 18, [[22, 0, -423, 193]], [0, "72hWik6pJMvKLohRH8U8f7", 1], [5, 3000, 3000]], [36, "particleWin", 18, [-424], [0, "37VwddfilCFIwnvHhvdcxF", 1], [9, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [55, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, {"custom": false}, 238, [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [44, "ThagLon_mn", 18, [[49, "Thang-Lon", 0, false, "Thang-Lon", -425, 194]], [0, "dcDTm+1DVEtIpQUczkhDnA", 1], [21.66667, 57.08333, 0, 0, 0, 0, 1, 0.83333, 0.83333, 0.83333]], [5, "1", 18, [114], [0, "f1ii62mulHnoKDWJQIuMn+", 1], [5, 362, 74], [32, -88, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 114], [45, "black", false, 1, 20, [[22, 0, -426, 198]], [0, "f34P7D5WFMmIyhB3bSuHS9", 1], [5, 3000, 3000]], [36, "particleWin", 20, [-427], [0, "93Eu9Ay05B4b4HGCfqgixE", 1], [9, -29, 0, 0, 0, 0, 1, 1, 1, 1]], [55, 81, 0.2, 54, 1.5, 0.5, 30, 40, -1, 51, 500, 1, 850, 200, 0, 50, {"custom": false}, 244, [4, 4290772991], [4, 0], [4, 4290772991], [4, 0], [0, 30, 20], [0, 0, -2400]], [30, 115], [35, "label", 7, [[11, "Hạng:", 16, 30, false, 1, 1, -428, 202]], [0, "1e80uz3gVNjYMXJ7b+0gss", 1], [5, 43.2, 12], [0, 0, 0.5], [-61.7, 24.1, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "label", 7, [[11, "<PERSON><PERSON><PERSON><PERSON>:", 16, 30, false, 1, 1, -429, 203]], [0, "6c9B0oGHlE2JFJ+pGKdPJq", 1], [5, 42, 12], [0, 0, 0.5], [-61.7, 1.7, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "label", 7, [[11, "<PERSON><PERSON><PERSON> th<PERSON>c sau:", 12, 30, false, 1, 1, -430, 204]], [0, "4beG5uc/VJrbDr/DhuSKPh", 1], [5, 71.7, 9], [0, 0, 0.5], [-61.1, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "lbRank", 7, [-431], [0, "19xFtimGJFiKBBDL+S+SRS", 1], [4, 4278315513], [5, 12, 15], [0, 0, 0.5], [-15.5, 24.1, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "0", 20, 30, false, 1, 1, 250], [54, 0.5, 116], [13, "00:00:00", 12, 30, false, 1, 1, 117], [6, "label", 40, [[11, "<PERSON><PERSON> kiện sắp diễn ra", 15, 30, false, 1, 1, -432, 206]], [0, "89P5fBTCpJZZEH1tTHUEZb", 1], [5, 130.5, 11.25], [0, 15.9, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "lbTime", 40, [-433], [0, "44qaEJnP5NF7HDDlHrY9lx", 1], [4, 4278315513], [5, 90.2, 16.5], [0, -5.2, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "00:15:25", 22, 30, false, 1, 1, 255], [18, "x2Bom", 41, [[47, -434, 207]], [0, "0cHV9k7GpBy61b1dZuSmxl", 1], [5, 289, 72]], [4, "lbValue", 41, [-435], [0, "8cWlaKsqpEh4JzILe73bKv", 1], [5, 187.85, 26], [0, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [98, "10 x2 = 20 BOM", 26, false, 1, 1, 258], [101, 1, [214, 215], [216, 217], [218, 219], [220, 221], [222, 223]]], 0, [0, 10, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 11, 3, 0, 0, 1, 0, 12, 228, 0, 13, 231, 0, 14, 230, 0, 15, 229, 0, 16, 138, 0, 17, 136, 0, 18, 134, 0, 19, 132, 0, 20, 130, 0, 21, 260, 0, 0, 1, 0, 0, 1, 0, -7, 260, 0, 0, 1, 0, -1, 21, 0, -2, 3, 0, -1, 52, 0, -2, 59, 0, -3, 66, 0, -4, 54, 0, -5, 61, 0, -6, 68, 0, -7, 56, 0, -8, 63, 0, -9, 70, 0, 0, 2, 0, -1, 82, 0, -2, 83, 0, -3, 84, 0, -4, 85, 0, -5, 86, 0, -6, 87, 0, -7, 88, 0, -8, 89, 0, -9, 90, 0, -10, 91, 0, -11, 92, 0, -12, 93, 0, -13, 94, 0, -14, 95, 0, -15, 96, 0, -16, 97, 0, -17, 98, 0, -18, 99, 0, -19, 100, 0, -20, 101, 0, -21, 102, 0, -22, 103, 0, -23, 104, 0, -24, 105, 0, -25, 106, 0, -26, 107, 0, -27, 108, 0, -1, 5, 0, -2, 9, 0, -3, 10, 0, -5, 109, 0, -6, 16, 0, -7, 22, 0, -8, 19, 0, -9, 23, 0, 0, 4, 0, 0, 4, 0, -1, 43, 0, -2, 44, 0, -3, 45, 0, -4, 46, 0, -5, 47, 0, 0, 5, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, -4, 27, 0, -5, 28, 0, -6, 29, 0, 0, 6, 0, 0, 6, 0, 7, 23, 0, 9, 23, 0, 0, 6, 0, -1, 7, 0, -2, 40, 0, -3, 41, 0, -1, 247, 0, -2, 248, 0, -3, 249, 0, -4, 250, 0, -5, 116, 0, -6, 117, 0, 7, 8, 0, 9, 21, 0, 0, 8, 0, -1, 42, 0, -3, 48, 0, -1, 140, 0, -2, 141, 0, -3, 142, 0, -4, 143, 0, -5, 144, 0, -6, 145, 0, 22, 224, 0, -1, 160, 0, -2, 174, 0, -3, 188, 0, -4, 205, 0, -5, 222, 0, 0, 10, 0, -1, 146, 0, -2, 49, 0, -3, 71, 0, -4, 223, 0, -5, 31, 0, 0, 11, 0, -2, 160, 0, -1, 50, 0, -2, 51, 0, -3, 53, 0, -4, 55, 0, 0, 12, 0, -2, 174, 0, -1, 57, 0, -2, 58, 0, -3, 60, 0, -4, 62, 0, 0, 13, 0, -2, 188, 0, -1, 64, 0, -2, 65, 0, -3, 67, 0, -4, 69, 0, 0, 14, 0, -2, 205, 0, -1, 73, 0, -2, 74, 0, -3, 75, 0, -4, 76, 0, 0, 15, 0, -2, 222, 0, -1, 77, 0, -2, 78, 0, -3, 79, 0, -4, 80, 0, 0, 16, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, -5, 36, 0, 0, 17, 0, -1, 112, 0, -2, 232, 0, -3, 234, 0, -4, 235, 0, 0, 18, 0, -1, 237, 0, -2, 238, 0, -3, 240, 0, -4, 241, 0, 23, 246, 0, 24, 242, 0, 25, 236, 0, 26, 245, 0, 27, 239, 0, 28, 233, 0, 29, 20, 0, 0, 19, 0, -3, 20, 0, 0, 20, 0, -1, 243, 0, -2, 244, 0, -3, 115, 0, -1, 120, 0, -2, 122, 0, -3, 124, 0, -4, 126, 0, -5, 128, 0, 30, 118, 0, 0, 21, 0, 0, 22, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, 31, 259, 0, 32, 252, 0, 33, 251, 0, 34, 256, 0, 35, 253, 0, 36, 40, 0, 0, 23, 0, -1, 130, 0, -1, 129, 0, -1, 132, 0, -1, 131, 0, -1, 134, 0, -1, 133, 0, -1, 136, 0, -1, 135, 0, -1, 138, 0, -1, 137, 0, 7, 29, 0, 0, 29, 0, -1, 139, 0, 37, 225, 0, 0, 31, 0, -1, 81, 0, -2, 226, 0, 0, 32, 0, -2, 228, 0, 0, 33, 0, 7, 33, 0, 0, 33, 0, 0, 34, 0, 7, 34, 0, 0, 34, 0, 0, 35, 0, 7, 35, 0, 0, 35, 0, 0, 36, 0, 7, 36, 0, 0, 36, 0, 0, 37, 0, -2, 229, 0, 0, 38, 0, -2, 230, 0, 0, 39, 0, -2, 231, 0, -1, 254, 0, -2, 255, 0, 0, 41, 0, -1, 257, 0, -2, 258, 0, -1, 118, 0, 0, 42, 0, 0, 43, 0, -1, 119, 0, 0, 44, 0, -1, 121, 0, 0, 45, 0, -1, 123, 0, 0, 46, 0, -1, 125, 0, 0, 47, 0, -1, 127, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, -1, 147, 0, -2, 149, 0, -1, 151, 0, -2, 152, 0, -1, 154, 0, -2, 155, 0, -1, 157, 0, -2, 158, 0, -1, 161, 0, -2, 163, 0, -1, 165, 0, -2, 166, 0, -1, 168, 0, -2, 169, 0, -1, 171, 0, -2, 172, 0, -1, 175, 0, -2, 177, 0, -1, 179, 0, -2, 180, 0, -1, 182, 0, -2, 183, 0, -1, 185, 0, -2, 186, 0, 0, 71, 0, -1, 72, 0, -1, 189, 0, -2, 191, 0, -1, 193, 0, -2, 195, 0, -1, 197, 0, -2, 199, 0, -1, 201, 0, -2, 203, 0, -1, 206, 0, -2, 208, 0, -1, 210, 0, -2, 212, 0, -1, 214, 0, -2, 216, 0, -1, 218, 0, -2, 220, 0, 0, 81, 0, -2, 225, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 93, 0, 0, 93, 0, 0, 94, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, 0, 97, 0, 0, 97, 0, 0, 98, 0, 0, 98, 0, 0, 99, 0, 0, 99, 0, 0, 100, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, 0, 104, 0, 0, 104, 0, 0, 105, 0, 0, 105, 0, 0, 106, 0, 0, 106, 0, 0, 107, 0, 0, 107, 0, 0, 108, 0, 0, 108, 0, 38, 227, 0, 0, 109, 0, -1, 110, 0, 0, 110, 0, -1, 111, 0, -1, 227, 0, 0, 111, 0, 0, 112, 0, 0, 112, 0, 0, 113, 0, -2, 236, 0, 0, 114, 0, -2, 242, 0, 0, 115, 0, -2, 246, 0, 0, 116, 0, -2, 252, 0, -1, 253, 0, 0, 117, 0, -1, 120, 0, -1, 122, 0, -1, 124, 0, -1, 126, 0, -1, 128, 0, 0, 129, 0, 0, 131, 0, 0, 133, 0, 0, 135, 0, 0, 137, 0, 0, 139, 0, 0, 140, 0, 0, 141, 0, 0, 142, 0, 0, 143, 0, 0, 144, 0, 0, 145, 0, 0, 146, 0, -1, 148, 0, -1, 150, 0, -1, 153, 0, -1, 156, 0, -1, 159, 0, -1, 162, 0, -1, 164, 0, -1, 167, 0, -1, 170, 0, -1, 173, 0, -1, 176, 0, -1, 178, 0, -1, 181, 0, -1, 184, 0, -1, 187, 0, -1, 190, 0, -1, 192, 0, -1, 194, 0, -1, 196, 0, -1, 198, 0, -1, 200, 0, -1, 202, 0, -1, 204, 0, -1, 207, 0, -1, 209, 0, -1, 211, 0, -1, 213, 0, -1, 215, 0, -1, 217, 0, -1, 219, 0, -1, 221, 0, -1, 224, 0, 0, 226, 0, -1, 233, 0, 0, 234, 0, 0, 237, 0, -1, 239, 0, 0, 240, 0, 0, 243, 0, -1, 245, 0, 0, 247, 0, 0, 248, 0, 0, 249, 0, -1, 251, 0, 0, 254, 0, -1, 256, 0, 0, 257, 0, -1, 259, 0, 39, 1, 2, 6, 3, 4, 6, 8, 6, 6, 23, 8, 6, 21, 11, 6, 30, 12, 6, 30, 13, 6, 30, 14, 6, 72, 15, 6, 72, 17, 6, 19, 18, 6, 19, 30, 6, 49, 52, 0, 151, 54, 0, 154, 56, 0, 157, 59, 0, 165, 61, 0, 168, 63, 0, 171, 66, 0, 179, 68, 0, 182, 70, 0, 185, 113, 6, 235, 114, 6, 241, 435], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 118, 120, 122, 124, 126, 128, 148, 150, 52, 153, 54, 156, 56, 159, 162, 164, 59, 167, 61, 170, 63, 173, 176, 178, 66, 181, 68, 184, 70, 187, 190, 192, 194, 196, 198, 200, 202, 204, 207, 209, 211, 213, 215, 217, 219, 221, 224, 227, 233, 233, 239, 239, 245, 245, 251, 253, 256, 259], [3, -1, 4, 4, 4, 4, 4, 1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 1, 4, -1, 4, -1, 4, -1, 4, -1, 4, -1, 4, -1, 1, -1, -1, -1, -1, 3, -1, -2, -3, -4, -5, -1, -1, -1, -1, 3, -1, -2, -3, -4, -5, -1, -1, -1, -1, 3, -1, -2, -3, -4, -5, -1, -1, -1, -1, -1, 3, -1, -2, -3, -4, -5, -1, -1, -1, -1, 3, -1, -2, -3, -4, -5, -1, -1, -1, 4, -1, 1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, 3, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 1, 5, 4, 3, -1, 1, 5, 4, 3, -1, 1, 4, 3, -1, 4, 4, 4, 4, 4, 1, 3, -1, 5, 3, -1, -2, -1, -2, -1, -2, -1, -2, -1, -2, -1, -2, -1, -2, 40, 41, 42, 43, -1, -2, -3, -4, -5, -6, -7, -8, -9, 1, 4, 4, 4, 4, 4, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 4, 4, 8, 1, 8, 1, 8, 1, 4, 4, 4, 4], [20, 20, 5, 5, 5, 5, 5, 39, 40, 1, 15, 1, 9, 1, 9, 1, 9, 1, 9, 9, 10, 1, 10, 1, 10, 1, 10, 1, 10, 1, 10, 1, 41, 4, 4, 4, 4, 6, 6, 11, 12, 13, 14, 4, 4, 4, 4, 6, 6, 11, 12, 13, 14, 4, 4, 4, 4, 6, 6, 11, 12, 13, 14, 1, 1, 1, 1, 1, 6, 6, 11, 12, 13, 14, 1, 1, 1, 1, 6, 6, 11, 12, 13, 14, 1, 1, 1, 42, 1, 43, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 21, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 21, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 1, 22, 1, 70, 1, 71, 1, 72, 1, 73, 1, 23, 1, 24, 1, 16, 17, 25, 18, 8, 8, 17, 25, 18, 8, 8, 17, 18, 8, 8, 7, 7, 7, 7, 7, 74, 8, 8, 75, 26, 26, 76, 15, 9, 15, 9, 27, 23, 27, 24, 16, 16, 22, 77, 78, 79, 80, 81, 82, 83, 84, 3, 2, 85, 2, 86, 87, 88, 5, 5, 5, 5, 5, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 5, 7, 89, 19, 28, 19, 28, 19, 7, 7, 7, 7]], [[{"name": "line24", "rect": [3, 119, 324, 170], "offset": [-0.5, -56.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [90]], [[{"name": "line14", "rect": [3, 119, 324, 46], "offset": [-0.5, 5.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [91]], [[{"name": "line8", "rect": [3, 3, 323, 286], "offset": [-1, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [92]], [[{"name": "scale-btn-2", "rect": [4, 3, 76, 73], "offset": [2, -1.5], "originalSize": [80, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [93]], [[[102, "UTM AvoBold", "UTM AvoBold.ttf"], -1], 0, 0, [], [], []], [[{"name": "line27", "rect": [3, 244, 324, 46], "offset": [-0.5, -119.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [94]], [[{"name": "icon4", "rect": [16, 13, 103, 87], "offset": [0.5, 0.5], "originalSize": [134, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [95]], [[[32, "columnStop", 2.4166666666666665, 4, "0", [{"frame": 2.4166666666666665, "func": "finishSpin", "params": []}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 220}, {"frame": 1.3333333333333333, "value": -220}, {"frame": 1.35, "value": 220}, {"frame": 1.8333333333333333, "value": 160}, {"frame": 2.4166666666666665, "value": 220}], "active": [{"frame": 0, "value": true}, {"frame": 1.3333333333333333, "value": false}, {"frame": 1.35, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 110}, {"frame": 1, "value": -220}, {"frame": 1.0166666666666666, "value": 220}, {"frame": 1.35, "value": 110}, {"frame": 1.8333333333333333, "value": 50}, {"frame": 2.4166666666666665, "value": 110}], "active": [{"frame": 0, "value": true}, {"frame": 1, "value": false}, {"frame": 1.0166666666666666, "value": true}]}}, "slot2": {"props": {"position": [{"frame": 0, "value": [0, 0]}, {"frame": 0.6666666666666666, "value": [0, -220]}, {"frame": 0.6833333333333333, "value": [0, 220]}, {"frame": 1.35, "value": [0, 0]}, {"frame": 1.8333333333333333, "value": [0, -60]}, {"frame": 2.4166666666666665, "value": [0, 0]}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot3": {"props": {"position": [{"frame": 0, "value": [0, -110]}, {"frame": 0.3333333333333333, "value": [0, -220]}, {"frame": 0.35, "value": [0, 220]}, {"frame": 1.35, "value": [0, -110]}, {"frame": 1.8333333333333333, "value": [0, -170]}, {"frame": 2.4166666666666665, "value": [0, -110]}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "line25", "rect": [3, 3, 323, 288], "offset": [-1, 0.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [96]], [[{"name": "line4", "rect": [3, 3, 324, 161], "offset": [-0.5, 64], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [97]], [[{"name": "line3", "rect": [3, 3, 323, 288], "offset": [-1, 0.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [98]], [[{"name": "line23", "rect": [3, 119, 323, 170], "offset": [-1, -56.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [99]], [[{"name": "line17", "rect": [3, 119, 323, 170], "offset": [-1, -56.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [100]], [[{"name": "quay-btn", "rect": [4, 4, 205, 83], "offset": [0.5, -1.5], "originalSize": [212, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [101]], [[{"name": "lichsu-btn", "rect": [4, 3, 76, 73], "offset": [2, -1.5], "originalSize": [80, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [102]], [[{"name": "exit-btn", "rect": [0, 0, 77, 75], "offset": [0, 0], "originalSize": [77, 75], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [103]], [[{"name": "phong-bg", "rect": [35, 27, 72, 90], "offset": [-1.5, 0.5], "originalSize": [145, 145], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [104]], [[{"name": "phong2-bg", "rect": [3, 3, 139, 139], "offset": [0, 0], "originalSize": [145, 145], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [105]], [[{"name": "icon3", "rect": [18, 12, 99, 87], "offset": [0.5, 1.5], "originalSize": [134, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [106]], [[{"name": "line11", "rect": [3, 3, 324, 161], "offset": [-0.5, 64], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [107]], [[{"name": "line21", "rect": [3, 3, 324, 286], "offset": [-0.5, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [108]], [[{"name": "tuquay-btn", "rect": [0, 0, 168, 76], "offset": [0, 0], "originalSize": [168, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [109]], [[{"name": "line1", "rect": [3, 3, 324, 46], "offset": [-0.5, 121.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [110]], [[[56, "columnStart", 0.6666666666666666, 4, [{"frame": 0.6666666666666666, "func": "finishStart", "params": []}], {"props": {"y": []}, "paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 220}, {"frame": 0.016666666666666666, "value": -220}, {"frame": 0.5333333333333333, "value": -140}, {"frame": 0.6666666666666666, "value": -220}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 110}, {"frame": 0.5333333333333333, "value": 190}, {"frame": 0.6666666666666666, "value": 110}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.5333333333333333, "value": 80}, {"frame": 0.6666666666666666, "value": 0}]}}, "slot3": {"props": {"y": [{"frame": 0, "value": -110}, {"frame": 0.5333333333333333, "value": -30}, {"frame": 0.6666666666666666, "value": -110}]}}}}]], 0, 0, [], [], []], [[{"name": "line12", "rect": [3, 3, 323, 286], "offset": [-1, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [111]], [[{"name": "sieutoc-btn", "rect": [2, 3, 165, 72], "offset": [0.5, -1], "originalSize": [168, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [112]], [[{"name": "line22", "rect": [3, 3, 323, 286], "offset": [-1, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [113]], [[{"name": "line20", "rect": [3, 3, 323, 286], "offset": [-1, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [114]], [[{"name": "scale-btn-1", "rect": [4, 3, 76, 73], "offset": [2, -1.5], "originalSize": [80, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [115]], [[[32, "columnSpin", 0.6833333333333333, 4, 2, [{"frame": 0.16666666666666666, "func": "randomIcon", "params": ["3"]}, {"frame": 0.3333333333333333, "func": "randomIcon", "params": [2]}, {"frame": 0.5, "func": "randomIcon", "params": [1]}, {"frame": 0.6666666666666666, "func": "randomIcon", "params": [0]}], {"paths": {"slot0": {"props": {"y": [{"frame": 0, "value": 220}, {"frame": 0.6666666666666666, "value": -220}, {"frame": 0.6833333333333333, "value": 220}], "active": [{"frame": 0, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 0.6833333333333333, "value": true}]}}, "slot1": {"props": {"y": [{"frame": 0, "value": 110}, {"frame": 0.5, "value": -220}, {"frame": 0.5166666666666667, "value": 220}, {"frame": 0.6833333333333333, "value": 110}], "active": [{"frame": 0, "value": true}, {"frame": 0.5, "value": false}, {"frame": 0.5166666666666667, "value": true}]}}, "slot2": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.3333333333333333, "value": -220}, {"frame": 0.35, "value": 220}, {"frame": 0.6833333333333333, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.35, "value": true}]}}, "slot3": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.18333333333333332, "value": true}], "position": [{"frame": 0, "value": [0, -110]}, {"frame": 0.16666666666666666, "value": [0, -220]}, {"frame": 0.18333333333333332, "value": [0, 220]}, {"frame": 0.6833333333333333, "value": [0, -110]}]}}}}]], 0, 0, [], [], []], [[{"name": "vinhdanh-btn", "rect": [4, 3, 76, 73], "offset": [2, -1.5], "originalSize": [80, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [116]], [[{"name": "line19", "rect": [3, 3, 323, 288], "offset": [-1, 0.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [117]], [[{"name": "line18", "rect": [3, 119, 323, 170], "offset": [-1, -56.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [118]], [[{"name": "line15", "rect": [3, 118, 323, 171], "offset": [-1, -56], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [119]], [[{"name": "line6", "rect": [3, 3, 323, 286], "offset": [-1, 1.5], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [120]], [[{"name": "line13", "rect": [3, 3, 323, 163], "offset": [-1, 63], "originalSize": [331, 295], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [121]], [[[103, "even_VBom", "\neven_VBom.png\nsize: 392,391\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nBomb\n  rotate: false\n  xy: 359, 46\n  size: 32, 44\n  orig: 32, 44\n  offset: 0, 0\n  index: -1\nVUA\n  rotate: false\n  xy: 1, 14\n  size: 98, 36\n  orig: 98, 36\n  offset: 0, 0\n  index: -1\nX2\n  rotate: false\n  xy: 103, 165\n  size: 29, 21\n  orig: 29, 21\n  offset: 0, 0\n  index: -1\nbg\n  rotate: false\n  xy: 1, 290\n  size: 149, 100\n  orig: 149, 100\n  offset: 0, 0\n  index: -1\nbom\n  rotate: true\n  xy: 103, 187\n  size: 102, 35\n  orig: 102, 35\n  offset: 0, 0\n  index: -1\ncrown\n  rotate: false\n  xy: 100, 3\n  size: 40, 33\n  orig: 40, 33\n  offset: 0, 0\n  index: -1\nex_no_01\n  rotate: true\n  xy: 296, 35\n  size: 55, 62\n  orig: 57, 64\n  offset: 1, 1\n  index: -1\nex_no_02\n  rotate: false\n  xy: 321, 149\n  size: 61, 68\n  orig: 63, 70\n  offset: 1, 1\n  index: -1\nex_no_03\n  rotate: false\n  xy: 321, 218\n  size: 67, 74\n  orig: 69, 76\n  offset: 1, 1\n  index: -1\nex_no_04\n  rotate: true\n  xy: 102, 37\n  size: 73, 80\n  orig: 75, 82\n  offset: 1, 1\n  index: -1\nex_no_05\n  rotate: true\n  xy: 233, 119\n  size: 80, 87\n  orig: 82, 89\n  offset: 1, 1\n  index: -1\nex_no_06\n  rotate: false\n  xy: 233, 200\n  size: 87, 92\n  orig: 89, 94\n  offset: 1, 1\n  index: -1\nex_no_07\n  rotate: false\n  xy: 139, 192\n  size: 93, 97\n  orig: 95, 99\n  offset: 1, 1\n  index: -1\nex_no_08\n  rotate: true\n  xy: 262, 293\n  size: 97, 103\n  orig: 99, 105\n  offset: 1, 1\n  index: -1\nex_no_09\n  rotate: false\n  xy: 1, 51\n  size: 100, 108\n  orig: 102, 110\n  offset: 1, 1\n  index: -1\nex_no_10\n  rotate: true\n  xy: 151, 293\n  size: 97, 110\n  orig: 99, 112\n  offset: 1, 1\n  index: -1\nex_no_11\n  rotate: true\n  xy: 139, 111\n  size: 80, 83\n  orig: 82, 85\n  offset: 1, 1\n  index: -1\nex_no_12\n  rotate: false\n  xy: 238, 53\n  size: 57, 65\n  orig: 59, 67\n  offset: 1, 1\n  index: -1\nex_no_13\n  rotate: true\n  xy: 321, 91\n  size: 57, 65\n  orig: 59, 67\n  offset: 1, 1\n  index: -1\nex_no_14\n  rotate: false\n  xy: 183, 44\n  size: 54, 66\n  orig: 56, 68\n  offset: 1, 1\n  index: -1\nnv\n  rotate: false\n  xy: 1, 160\n  size: 101, 129\n  orig: 101, 129\n  offset: 0, 0\n  index: -1\n", ["even_VBom.png"], {"skeleton": {"hash": "eR4HaMzOrl7m1yJFRWaTjFvNqrk", "spine": "3.6.53", "width": 174.65, "height": 171.88}, "bones": [{"name": "root"}, {"name": "bom", "parent": "root", "length": 56.87, "rotation": 1.77, "x": -64.54, "y": 18.31}, {"name": "Bomb", "parent": "bom", "length": 19.11, "rotation": 101.4, "x": 30.72, "y": -4.23}, {"name": "VUA", "parent": "root", "length": 52.74, "x": -59.85, "y": 59.33}, {"name": "X2", "parent": "root", "x": 67.04, "y": -22.45}, {"name": "crown", "parent": "Bomb", "length": 19.28, "rotation": 6.36, "x": 19.42, "y": 2.43}, {"name": "ex_no_01", "parent": "root", "x": 64.58, "y": -20.42}, {"name": "nv", "parent": "root", "length": 40.44, "rotation": 89.17, "x": 46.8, "y": 31.79}], "slots": [{"name": "bg", "bone": "root", "attachment": "bg"}, {"name": "nv", "bone": "nv", "attachment": "nv"}, {"name": "VUA", "bone": "VUA", "attachment": "VUA"}, {"name": "bom", "bone": "bom", "attachment": "bom"}, {"name": "Bomb", "bone": "Bomb", "attachment": "Bomb"}, {"name": "crown", "bone": "crown", "attachment": "crown"}, {"name": "ex_no_01", "bone": "ex_no_01"}, {"name": "X2", "bone": "X2"}, {"name": "X3", "bone": "X2", "blend": "additive"}], "skins": {"default": {"Bomb": {"Bomb": {"x": 16.78, "y": -1.35, "rotation": -103.17, "width": 32, "height": 44}}, "VUA": {"VUA": {"x": 25.87, "y": -2.33, "width": 98, "height": 36}}, "X2": {"X2": {"x": 0.85, "y": -0.85, "width": 29, "height": 21}}, "X3": {"X2": {"x": 0.85, "y": -0.85, "width": 29, "height": 21}}, "bg": {"bg": {"x": -7.19, "y": -12.25, "width": 149, "height": 100}}, "bom": {"bom": {"x": 33.72, "y": 4.65, "rotation": -1.77, "width": 102, "height": 35}}, "crown": {"crown": {"x": 4.59, "y": 0.21, "rotation": -109.54, "width": 40, "height": 33}}, "ex_no_01": {"ex_no_01": {"x": 2.25, "y": 1.93, "width": 57, "height": 64}, "ex_no_02": {"x": 2.25, "y": 1.93, "width": 63, "height": 70}, "ex_no_03": {"x": 2.25, "y": 1.93, "width": 69, "height": 76}, "ex_no_04": {"x": 2.25, "y": 1.93, "width": 75, "height": 82}, "ex_no_05": {"x": 2.25, "y": 1.93, "width": 82, "height": 89}, "ex_no_06": {"x": 2.25, "y": 1.93, "width": 89, "height": 94}, "ex_no_07": {"x": 2.25, "y": 1.93, "width": 95, "height": 99}, "ex_no_08": {"x": 2.25, "y": 1.93, "width": 99, "height": 105}, "ex_no_09": {"x": 2.25, "y": 1.93, "width": 102, "height": 110}, "ex_no_10": {"x": 2.25, "y": 1.93, "width": 99, "height": 112}, "ex_no_11": {"x": 2.25, "y": 1.93, "width": 82, "height": 85}, "ex_no_12": {"x": 2.25, "y": 1.93, "width": 59, "height": 67}, "ex_no_13": {"x": 2.25, "y": 1.93, "width": 59, "height": 67}, "ex_no_14": {"x": 2.25, "y": 1.93, "width": 56, "height": 68}}, "nv": {"nv": {"type": "mesh", "hull": 4, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-41.5, -45.48, -42.97, 55.51, 86.02, 57.38, 87.48, -43.61]}}}}, "animations": {"animation": {"slots": {"nv": {"attachment": [{"time": 0, "name": "nv"}, {"time": 1, "name": "nv"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "nv": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 3.02}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": -3.04, "y": 3.79}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "bom": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.107, "y": 1.107}, {"time": 1, "x": 1, "y": 1}]}, "crown": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 9.1}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 0.97, "y": 1.08}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "VUA": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": 2.72, "y": -3.91}, {"time": 0.5, "x": 1.95, "y": 0.52}, {"time": 0.7333, "x": 2.72, "y": -3.91}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "Bomb": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}}, "deform": {"default": {"nv": {"nv": [{"time": 0}, {"time": 0.5, "vertices": [0.64114, -6.84306, 0, 0, -3.68972, 6.13653]}, {"time": 1}]}}}}, "bom": {"slots": {"nv": {"attachment": [{"time": 0, "name": "nv"}, {"time": 1, "name": "nv"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "nv": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 3.02}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": -3.04, "y": 3.79}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "bom": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "crown": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 9.1}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 1.65, "y": 1}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "VUA": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "Bomb": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.2667, "x": 0.23, "y": 7.58}, {"time": 0.5, "x": 0, "y": 0}, {"time": 0.7667, "x": 0.15, "y": 4.73}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}}, "deform": {"default": {"nv": {"nv": [{"time": 0}, {"time": 0.5, "vertices": [0.64114, -6.84306, 0, 0, -3.68972, 6.13653]}, {"time": 1}]}}}}, "bomx2": {"slots": {"X2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "X2"}, {"time": 1.1667, "name": "X2"}]}, "X3": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffff9f"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "X2"}, {"time": 0.5667, "name": "X2"}, {"time": 1.1667, "name": "X2"}]}, "ex_no_01": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.0667, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}, {"time": 0.0667, "name": "ex_no_01"}, {"time": 0.1333, "name": "ex_no_01"}, {"time": 0.2, "name": "ex_no_02"}, {"time": 0.2667, "name": "ex_no_03"}, {"time": 0.3333, "name": "ex_no_04"}, {"time": 0.4333, "name": "ex_no_05"}, {"time": 0.5, "name": "ex_no_06"}, {"time": 0.5667, "name": "ex_no_07"}, {"time": 0.6333, "name": "ex_no_08"}, {"time": 0.7333, "name": "ex_no_09"}, {"time": 0.8, "name": "ex_no_10"}, {"time": 0.8667, "name": "ex_no_11"}, {"time": 0.9667, "name": "ex_no_12"}, {"time": 1.0333, "name": "ex_no_13"}, {"time": 1.1, "name": "ex_no_14"}, {"time": 1.1667, "name": "ex_no_01"}]}, "nv": {"attachment": [{"time": 0, "name": "nv"}, {"time": 0.5, "name": "nv"}, {"time": 1.1667, "name": "nv"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "nv": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 0, "y": 1.63}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "bom": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "crown": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 9.1}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 1.65, "y": 1}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "VUA": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "Bomb": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6, "x": 0.15, "y": 4.73}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1667, "x": 1, "y": 1}]}, "X2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5667, "x": -2.31, "y": 6.4}, {"time": 1.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5667, "x": 1.107, "y": 1.107}, {"time": 1.1667, "x": 1, "y": 1}]}, "ex_no_01": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.6333, "x": 0.88, "y": 4.38, "curve": "stepped"}, {"time": 1.1667, "x": 0.88, "y": 4.38}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.0667, "x": 0.868, "y": 0.868}, {"time": 1.1, "x": 0.734, "y": 0.734}, {"time": 1.1667, "x": 0.868, "y": 0.868}]}}, "deform": {"default": {"nv": {"nv": [{"time": 0, "curve": "stepped"}, {"time": 0.5, "curve": "stepped"}, {"time": 1.1667}]}}}}}}, [0]]], 0, 0, [0], [-1], [122]], [[[57, "payline", 1, {"props": {"opacity": [{"frame": 0, "value": 0}, {"frame": 0.5, "value": 255}, {"frame": 1, "value": 0}]}}]], 0, 0, [], [], []]]]