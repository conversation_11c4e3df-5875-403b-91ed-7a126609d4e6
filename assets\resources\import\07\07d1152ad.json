[1, ["ecpdLyjvZBwrvm+cedCcQy", "33rUXoynpNhotX2utXZJwA", "9awKuJe0ZAdYuaoLZYOaoE", "b4zj4h1fZLcJwL6tBOFAJv", "f0heWTamVJR4DVxmCSN7Tm", "52F6+Hw8xGu5R8bPvFn+98", "65vEveyodD9rx/rQYi+aX7", "5b4hxm6C5Ac4ld/9Eul9gs", "5ad52h43BIl6K9gl88JMXU", "daSxaL6sBOgJqyMx0/MEUA", "24Q5q63KRBqJvfnrk0d6u9", "a9VpD0DP5LJYQPXITZq+uj", "22jiCBYxBA56//kXLWPV3+", "adw94Z+hpN57wutNivq8Q5", "c0ZhbxnCBKzK2XSGqnGZ3U", "33ORapUatKRaYrTfoA3zjX", "c1wU3moYhLD5kcPlTyLZdA", "5drovY8MlOspJiOCZY7Xl4", "8crLuLXvpDerTk3ydTlWD4", "057M9F66RDp5HlAYGvpwit", "b7e5chIlFPx4KTIKpX3nUj", "2fA68pnCdJL5RXkWGOUWJ+", "ccYfy//21BeLNdKaXci4DY", "eai/YyfupMUrD5NVHe6PB+", "b9VxgzQ2tA0a6e7DUW5QEB", "cfh2tePzVJa6B2Nq+t8a+O", "c7e37bgrhPI5E0ENegOu3c", "1bnb87tylNNJVd5gYctDx9", "27QxPVuVpGJY+o4oBbuINZ", "00+cdVH9NORrdse+VpWxw5", "6aszZu989EZKn9niHpmqea", "c9FkWQZf5ElpdGVoej5Wgw", "67StPxJrJAxpNbzmcZqeaS", "d0joctyZ9M84NLTIPm6DQA", "e7aYiVIhNCXYkihZ4Pe57Y", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "78CV4qbb5KtIf110J/vlM5", "a2tBXzjmRHWIetS1zkxuiC", "6a5h44kzdEZLpsX5Jh8O+o", "7eZ13HXl9OgpZMIS5CfqYi", "20pmXJ/O1Is6j1U5cxVHxj", "e2rZi/plBALaLu1F1VlozO", "59zrCpKAtIcr7Ed9fe8Z7m", "46z8VhpUVNeaTSaTKpA+lE", "824NJ+ZOhP3Lma0Ue68XMQ", "8dz2tvJr1PuY/uEwYy/D9U"], ["node", "_spriteFrame", "_defaultClip", "_N$file", "_textureSetter", "_N$normalSprite", "_parent", "root", "btnQuickPlay", "lbiWin", "lbTime", "nodeResult", "nodeMulti", "nodePick", "nodeBonus", "nodeStart", "target", "lbResult", "_N$target", "data", "sfMiss", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_zIndex", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs"], -1, 4, 5, 9, 1, 2, 7], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_spriteFrame", "_materials"], 1, 1, 6, 3], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_lineHeight", "_N$horizontalAlign", "_enableWrapText", "_N$overflow", "_srcBlendFactor", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Node", ["_name", "_zIndex", "_active", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children"], 0, 2, 4, 5, 7, 1, 2], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 1, 1, 9, 5, 5, 1, 5, 6, 6, 6, 6], ["cc.Node", ["_name", "_zIndex", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 5, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["57446/nohhJsIVxYKXCrbP/", ["node", "nodeStart", "nodeBonus", "nodePick", "nodeMulti", "nodeResult", "lbTime", "lbiWin", "btnQuickPlay"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["06d31XDyaxNc5ExI4Dw7pBS", ["node", "btnPicks", "lbiPrizes", "nodeEffects", "spriteBalls", "sfMiss"], 3, 1, 2, 2, 2, 2, 6], ["cfec3/GtxtG3pZDeXJvg8gF", ["node", "btnPicks", "spriteDoors", "spriteResults", "sfMultipliers"], 3, 1, 2, 2, 2, 3], ["09052Zq0oVJl59gzr1ZolUq", ["node"], 3, 1], ["7269ege3OpEV4lKd6fET93f", ["node", "lbResult"], 3, 1, 1], ["fa35bHjksRPqKrWumsBm7ik", ["node"], 3, 1], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1]], [[7, 0, 1, 2], [17, 0, 1, 2, 3, 2], [2, 2, 4, 3, 1], [4, 0, 1, 7, 3, 4, 5, 6, 3], [8, 0, 1, 2, 3, 4], [1, 0, 2, 1, 7, 6, 4, 5, 9, 4], [18, 0, 1], [5, 1, 0, 2, 3, 4, 5, 6, 3], [1, 0, 2, 1, 7, 6, 4, 5, 4], [1, 0, 2, 1, 7, 8, 6, 4, 5, 4], [4, 0, 1, 7, 8, 3, 4, 5, 6, 3], [6, 0, 1, 2, 3, 4, 7, 5, 3], [2, 0, 2, 4, 2], [3, 0, 1, 4, 6, 2, 5, 3, 9, 11, 8], [2, 2, 3, 1], [2, 0, 2, 3, 2], [2, 0, 1, 2, 4, 3, 3], [1, 0, 1, 7, 8, 6, 4, 5, 9, 3], [2, 0, 2, 4, 3, 2], [2, 0, 1, 2, 4, 3], [8, 0, 1, 3, 3], [1, 0, 1, 7, 6, 4, 5, 3], [4, 0, 2, 1, 7, 3, 4, 5, 6, 4], [5, 2, 3, 7, 1], [5, 2, 3, 4, 5, 6, 1], [1, 0, 2, 1, 7, 8, 6, 4, 5, 9, 4], [1, 0, 3, 1, 7, 6, 4, 5, 9, 4], [1, 0, 1, 7, 6, 4, 5, 9, 3], [4, 0, 1, 8, 3, 4, 5, 6, 3], [6, 0, 1, 2, 3, 4, 5, 6, 3], [2, 0, 1, 2, 3], [11, 0, 1], [5, 2, 7, 1], [3, 0, 1, 4, 2, 5, 3, 9, 10, 11, 7], [9, 0, 2], [1, 0, 1, 8, 6, 4, 5, 3], [1, 0, 2, 1, 8, 6, 4, 5, 4], [1, 0, 1, 7, 8, 4, 5, 9, 3], [1, 0, 1, 7, 8, 4, 5, 3], [1, 0, 3, 1, 7, 6, 4, 5, 4], [4, 0, 1, 8, 3, 4, 5, 3], [6, 0, 1, 2, 3, 4, 5, 8, 6, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [7, 1, 1], [2, 0, 1, 2, 3, 3], [12, 0, 1, 2, 3, 4, 5, 1], [5, 0, 2, 4, 5, 6, 8, 9, 10, 11, 2], [13, 0, 1, 2, 3, 4, 1], [14, 0, 1], [15, 0, 1, 1], [16, 0, 1], [3, 0, 1, 4, 6, 2, 5, 3, 9, 10, 11, 8], [3, 0, 1, 4, 6, 2, 5, 3, 7, 9, 10, 11, 9], [3, 8, 0, 1, 6, 2, 3, 9, 10, 7], [3, 0, 1, 4, 2, 5, 3, 9, 10, 7]], [[[{"name": "X3", "rect": [0, 1, 126, 105], "offset": [0, -0.5], "originalSize": [126, 106], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [18]], [[{"name": "thua", "rect": [0, 0, 84, 113], "offset": [0, 0], "originalSize": [84, 113], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [19]], [[{"name": "X2", "rect": [0, 1, 130, 104], "offset": [0, -0.5], "originalSize": [130, 105], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [20]], [[{"name": "x1", "rect": [0, 0, 100, 104], "offset": [-0.5, 0], "originalSize": [101, 104], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [21]], [[{"name": "bg_ngoc2", "rect": [0, 1, 135, 62], "offset": [0, -0.5], "originalSize": [135, 63], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [22]], [[{"name": "bg_tex1", "rect": [0, 0, 217, 47], "offset": [0, 0], "originalSize": [217, 47], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [23]], [[{"name": "logo", "rect": [0, 0, 453, 86], "offset": [0, 0], "originalSize": [453, 86], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [24]], [[{"name": "x4", "rect": [0, 0, 146, 104], "offset": [0, 0], "originalSize": [146, 104], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [25]], [[{"name": "btn_chơiNhanh", "rect": [0, 0, 173, 174], "offset": [0, 0], "originalSize": [173, 174], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [26]], [[[34, "dbBonusGameView"], [35, "dbBonusGameView", 0, [-11, -12, -13], [[42, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [43, -1], [5, 1280, 720]], [36, "bonusGamePickView", false, 0, [-73], [[16, 2, false, -14, [152], 153], [31, -15], [45, -72, [-58, -59, -60, -61, -62, -63, -64, -65, -66, -67, -68, -69, -70, -71], [-44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57], [-30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40, -41, -42, -43], [-16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29], 154]], [0, "1d0Db7f9VCQ4qL/H37vD5G", 1], [5, 1560, 720]], [37, "layout", 0, 2, [-74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87], [0, "afoLOmqQJPWaUvf6uQarIA", 1], [5, 854, 123], [0, 43, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "bonusGame", 0, 1, [2, -88, -89, -90, -91, -92, -93, -94], [0, "500o+npENJyKYRgN0WhyV+", 1], [5, 1280, 720]], [25, "bonusGameMultiView", false, 0, 4, [-108], [[16, 2, false, -95, [165], 166], [31, -96], [32, -97, [4, 4292269782]], [47, -107, [-104, -105, -106], [-101, -102, -103], [-98, -99, -100], [167, 168, 169, 170, 171]]], [0, "53GzVa0jZJQpxxcr+hXPBu", 1], [5, 1560, 720], [0, 0, 0, 0, 0, 0, 1, 0.88, 0.88, 0.88]], [17, "bonusGameStartView", 0, 1, [-113, -114], [[16, 2, false, -109, [6], 7], [48, -110], [23, -112, [[20, "09052Zq0oVJl59gzr1ZolUq", "startClicked", -111]], [4, 4292269782]]], [0, "37SV/MHJBItK4WKTsqseMc", 1], [5, 921, 482], [0, 0, 0, 0, 0, 0, 1, 0.662, 0.662, 0.662]], [25, "bonusGameResultView", false, 0, 4, [-118, -119, -120], [[16, 2, false, -115, [194], 195], [49, -117, -116]], [0, "862Asd/UpOv5WPrEBNZBCn", 1], [5, 921, 482], [0, 0, 0, 0, 0, 0, 1, 0.472, 0.472, 0.472]], [10, "button", 0, 3, [-122, -123, -124, -125], [-121], [0, "b2Gezd4ExEJoJTRiMqSiJM", 1], [5, 100, 100], [517, -114, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-127, -128, -129, -130], [-126], [0, "8clMVqpmVF4IXZOJ8IyOeY", 1], [5, 90, 90], [379, -168, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-132, -133, -134, -135], [-131], [0, "0d909NPtFEr6jpXdOT0H1d", 1], [5, 90, 90], [184, -198, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-137, -138, -139, -140], [-136], [0, "7bQ/RO9WRKS6c98UrQGXLL", 1], [5, 90, 90], [2, -202, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-142, -143, -144, -145], [-141], [0, "c3hkCHuvVLt6mRd2/H6Kpj", 1], [5, 90, 90], [-174, -200, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-147, -148, -149, -150], [-146], [0, "3ehGBwCrNDZpMkkSxg07FI", 1], [5, 90, 90], [-342, -186, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-152, -153, -154, -155], [-151], [0, "1c+0nXBBBCJ6IsgwbtUDJb", 1], [5, 90, 90], [-543, -143, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-157, -158, -159, -160], [-156], [0, "0fm0G/F2lJ9o+6Ao4fmn4C", 1], [5, 100, 100], [-410, -66, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-162, -163, -164, -165], [-161], [0, "90FbddcEFLjZ+sCiITBxPd", 1], [5, 100, 100], [-231, -75, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-167, -168, -169, -170], [-166], [0, "47ngKt1CNJbpqyNWlaC6YO", 1], [5, 100, 100], [-39, -84, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-172, -173, -174, -175], [-171], [0, "25kKGw1dFPIr6DbnajJlgX", 1], [5, 100, 100], [173, -59, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-177, -178, -179, -180], [-176], [0, "a91fF+XB9H+JJyGQsoB43G", 1], [5, 90, 90], [342, -47, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-182, -183, -184, -185], [-181], [0, "6dvdJuVgJNVa6yW2yEfWpp", 1], [5, 90, 90], [-134.6, 17, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "button", 0, 3, [-187, -188, -189, -190], [-186], [0, "37HQRdFaxANKcwS883A/Y0", 1], [5, 90, 90], [47.4, 16, 0, 0, 0, 0, 1, 1, 1, 0]], [28, "button", 0, [-192, -193, -194], [-191], [0, "409j+Pi6RA3bf+/3IgsRVl", 1], [5, 250, 200], [-280, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "button", 0, [-196, -197, -198], [-195], [0, "cesk/zSx9DiZoL89O+2Q+L", 1], [5, 250, 200]], [28, "button", 0, [-200, -201, -202], [-199], [0, "76SLg10eNDPYN7JEh3lsy/", 1], [5, 250, 200], [280, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "layout", 0, 5, [22, 23, 24], [[50, -203]], [0, "fbo3JAklJBbLPwabR0JFld", 1], [5, 790, 440], [0, -55, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "sprite", 0, 4, [-207], [[16, 2, false, -204, [181], 182], [46, 2, -206, [4, 4294967295], [4, 4294967295], -205, 183, 184, 185, 186]], [0, "01yAugVy1PrJ8PsDfs4004", 1], [5, 424, 122], [-443, -270, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "effect", false, 0, 8, [[14, -208, 11], [1, true, -209, [13], 12]], [0, "08hcTmlEZABKDy6vB3KaL5", 1], [5, 226, 227]], [9, "bgValue", false, 0, 8, [-212], [[44, 0, false, -210, 15], [1, true, -211, [17], 16]], [0, "a8aLvWEZBOw7pJ8/DDpvGM", 1], [5, 160, 47]], [8, "effect", false, 0, 9, [[2, -213, [21], 22], [1, true, -214, [24], 23]], [0, "0eUo1zvZNOXJHe8H43hCnB", 1], [5, 226, 227]], [9, "bgValue", false, 0, 9, [-217], [[18, 0, -215, [27], 28], [1, true, -216, [30], 29]], [0, "05sbXwnAhCXoynbXZIRYyP", 1], [5, 160, 47]], [8, "effect", false, 0, 10, [[14, -218, 34], [1, true, -219, [36], 35]], [0, "adk8x3dxpBeK82dbU4U2dI", 1], [5, 226, 227]], [9, "bgValue", false, 0, 10, [-222], [[15, 0, -220, 38], [1, true, -221, [40], 39]], [0, "c1/qvTnIVBAb9CQ3oVVxyO", 1], [5, 160, 47]], [8, "effect", false, 0, 11, [[14, -223, 44], [1, true, -224, [46], 45]], [0, "8eHwOq1hpDsJHQhlo+e6SK", 1], [5, 226, 227]], [9, "bgValue", false, 0, 11, [-227], [[15, 0, -225, 48], [1, true, -226, [50], 49]], [0, "efaLJKLPpIR7xQ0gW5ksYM", 1], [5, 160, 47]], [8, "effect", false, 0, 12, [[14, -228, 54], [1, true, -229, [56], 55]], [0, "acgecbMqhIybVv97I3BIZu", 1], [5, 226, 227]], [9, "bgValue", false, 0, 12, [-232], [[15, 0, -230, 58], [1, true, -231, [60], 59]], [0, "6esEorbKhEcaxxbGQ1K3sc", 1], [5, 160, 47]], [8, "effect", false, 0, 13, [[14, -233, 64], [1, true, -234, [66], 65]], [0, "36BATxar1IdZjDrPSat4in", 1], [5, 226, 227]], [9, "bgValue", false, 0, 13, [-237], [[15, 0, -235, 68], [1, true, -236, [70], 69]], [0, "d17cu8d5hM4qK5TJAAUVrp", 1], [5, 160, 47]], [8, "effect", false, 0, 14, [[2, -238, [74], 75], [1, true, -239, [77], 76]], [0, "7agA6509ZPTZ5V5i1oyueh", 1], [5, 226, 227]], [9, "bgValue", false, 0, 14, [-242], [[15, 0, -240, 79], [1, true, -241, [81], 80]], [0, "60Zf1RkyVNzLmvrhRycZhJ", 1], [5, 160, 47]], [8, "effect", false, 0, 15, [[14, -243, 85], [1, true, -244, [87], 86]], [0, "c6dZVuAi9Gqb0cPXTQgNLn", 1], [5, 226, 227]], [9, "bgValue", false, 0, 15, [-247], [[15, 0, -245, 89], [1, true, -246, [91], 90]], [0, "3flbBxBeFNErJb2eo1ZlII", 1], [5, 160, 47]], [8, "effect", false, 0, 16, [[14, -248, 95], [1, true, -249, [97], 96]], [0, "c1KD8x1HNH3qmUQLO2i3qj", 1], [5, 226, 227]], [9, "bgValue", false, 0, 16, [-252], [[15, 0, -250, 99], [1, true, -251, [101], 100]], [0, "48AFFdhzlAGKz3s8wp2vxi", 1], [5, 160, 47]], [8, "effect", false, 0, 17, [[14, -253, 105], [1, true, -254, [107], 106]], [0, "77VgAU2fBEh7ua/f32JObC", 1], [5, 226, 227]], [9, "bgValue", false, 0, 17, [-257], [[15, 0, -255, 109], [1, true, -256, [111], 110]], [0, "790SJ3XtdLhbxnpcmSRbqW", 1], [5, 160, 47]], [8, "effect", false, 0, 18, [[14, -258, 115], [1, true, -259, [117], 116]], [0, "faTVbkFmJMb70CKhYTXxCr", 1], [5, 226, 227]], [9, "bgValue", false, 0, 18, [-262], [[15, 0, -260, 119], [1, true, -261, [121], 120]], [0, "32Hzep2dxItKOonYt51gLG", 1], [5, 160, 47]], [8, "effect", false, 0, 19, [[14, -263, 125], [1, true, -264, [127], 126]], [0, "f3tyXcUElFdJXycjncYybb", 1], [5, 226, 227]], [9, "bgValue", false, 0, 19, [-267], [[15, 0, -265, 129], [1, true, -266, [131], 130]], [0, "8di4y/lPtCRbgDjwHaHEk9", 1], [5, 160, 47]], [8, "effect", false, 0, 20, [[14, -268, 135], [1, true, -269, [137], 136]], [0, "121Kfw69pM1pLQeiNB5tz+", 1], [5, 226, 227]], [9, "bgValue", false, 0, 20, [-272], [[15, 0, -270, 139], [1, true, -271, [141], 140]], [0, "d7tKdi2dVCSogGHWkYh6Eu", 1], [5, 160, 47]], [8, "effect", false, 0, 21, [[14, -273, 145], [1, true, -274, [147], 146]], [0, "34EZBTU9tBYLCrAdv588Dt", 1], [5, 226, 227]], [9, "bgValue", false, 0, 21, [-277], [[15, 0, -275, 149], [1, true, -276, [151], 150]], [0, "d38HfSHqlDFrYxDlhB42sK", 1], [5, 160, 47]], [29, "btnQuickPlay", 0, 4, [[[16, 2, false, -278, [187], 188], -279], 4, 1], [0, "611BDIjahMd7W5LeQfMWHc", 1], [5, 173, 174], [494, -251, 0, 0, 0, 0, 1, 1, 1, 0]], [39, "black", 0, 0, 1, [[18, 0, -280, [0], 1], [32, -281, [4, 4292269782]]], [0, "08BOhSLxhLXJuh+a/E+i9M", 1], [5, 3000, 3000]], [26, "black", 0, 0, 6, [[18, 0, -282, [2], 3], [23, -283, [[20, "09052Zq0oVJl59gzr1ZolUq", "startClicked", 6]], [4, 4292269782]]], [0, "53lxsVKc5FWpI/OT/oMgFt", 1], [5, 3000, 3000], [0, 0, 0, 0, 0, 0, 1, 1.516, 1.516, 1.516]], [11, "Label", 0, 28, [[[13, "0", 12, 80, false, false, 1, 1, -284, 14], -285], 4, 1], [0, "2bzy2Eu2RG0IMJchRgYQSf", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 30, [[[51, "0", 12, 80, false, false, 1, 1, -286, [25], 26], -287], 4, 1], [0, "d6GQYG0vJKG5jZt+KejbPa", 1], [4, 4287823615], [5, 18.38, 80]], [11, "Label", 0, 32, [[[13, "0", 12, 80, false, false, 1, 1, -288, 37], -289], 4, 1], [0, "63XR2M+sRPwYhXJCrFaEkn", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 34, [[[13, "0", 12, 80, false, false, 1, 1, -290, 47], -291], 4, 1], [0, "30Y0Pkh0lO1JXxlt/H5PG7", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 36, [[[13, "0", 12, 80, false, false, 1, 1, -292, 57], -293], 4, 1], [0, "c6t5deXQtPZLg6Z9oUtMRe", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 38, [[[13, "0", 12, 80, false, false, 1, 1, -294, 67], -295], 4, 1], [0, "f1XjAapWNO76QyhoUzOBZW", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 40, [[[13, "0", 12, 80, false, false, 1, 1, -296, 78], -297], 4, 1], [0, "f6TIqTTulNgJMjv3r77pM9", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 42, [[[13, "0", 12, 80, false, false, 1, 1, -298, 88], -299], 4, 1], [0, "e0N4f9amNLuoeAWxb2Rqfw", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 44, [[[13, "0", 12, 80, false, false, 1, 1, -300, 98], -301], 4, 1], [0, "d4chKa+EFHja88elfhoaZC", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 46, [[[13, "0", 12, 80, false, false, 1, 1, -302, 108], -303], 4, 1], [0, "0cekVn/vdOMaR8J+t88h+y", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 48, [[[13, "0", 12, 80, false, false, 1, 1, -304, 118], -305], 4, 1], [0, "87NuUNQ8dAiJp2ZbNW9bki", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 50, [[[13, "0", 12, 80, false, false, 1, 1, -306, 128], -307], 4, 1], [0, "86M0//vARJKIpkX4j4Mzma", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 52, [[[13, "0", 12, 80, false, false, 1, 1, -308, 138], -309], 4, 1], [0, "a4dWN7t9hAR6vkEBFCcFZf", 1], [4, 4287823615], [5, 18.38, 30]], [11, "Label", 0, 54, [[[13, "0", 12, 80, false, false, 1, 1, -310, 148], -311], 4, 1], [0, "49EW24yRNBDI2pWqGsNrrM", 1], [4, 4287823615], [5, 18.38, 30]], [17, "bg_tex", 0, 4, [-313], [[16, 2, false, -312, [177], 178]], [0, "56HlaWkE5EwrTTerNPdCrI", 1], [5, 656, 52], [0, 191, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "lbTime", 0, 72, [[-314, [6, -315]], 1, 4], [0, "c2gLra5axHso5k7exXa987", 1], [5, 434.27, 50.4], [0, 0, 0.5], [-218, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "lbWin", 0, 26, [[[52, "1,000,000,000", 18, 80, false, false, 1, 1, 2, -316, [179], 180], -317], 4, 1], [0, "8fkCqaEidLKLcFQIciXzVj", 1], [5, 239.38, 50], [0, 8.151, 0, 0, 0, 0, 1, 1.4, 1.4, 1.4]], [26, "black", 0, 0, 7, [[18, 0, -318, [189], 190], [23, -319, [[20, "7269ege3OpEV4lKd6fET93f", "closeClicked", 7]], [4, 4292269782]]], [0, "eaXLAobbZDq4P8eu+XPTa3", 1], [5, 3000, 3000], [0, 0, 0, 0, 0, 0, 1, 1.836, 1.836, 1.836]], [27, "label", 0, 6, [[33, "<PERSON><PERSON><PERSON> mừng bạn đã trúng\nBONUS GAME", 36, 60, false, 1, 1, -320, [4], 5]], [0, "13wjIgf7xBKqm09q4okoHb", 1], [5, 404.1, 120], [0, -10, 0, 0, 0, 0, 1, 1.516, 1.516, 1.516]], [5, "bg_ngoc", false, 0, 8, [[2, -321, [8], 9]], [0, "caDaGgQ75Ogrsn/hRE6xq5", 1], [5, 135, 62], [24, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 8, [-322], [0, "0ajFipCDRBL78OhRof3Jvu", 1], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 78, [10]], [6, 58], [7, 1.05, 3, 8, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "1", 2]], [4, 4294967295], [4, 4294967295], 8], [5, "bg_ngoc", false, 0, 9, [[2, -323, [18], 19]], [0, "183a+jYPVOq5R/BzMCgAAd", 1], [5, 161, 84], [29, -16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 9, [-324], [0, "feZ1HHPjBEyJm5nXKgv8+t", 1], [5, 111, 111], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 83, [20]], [6, 59], [7, 1.05, 3, 9, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "2", 2]], [4, 4294967295], [4, 4294967295], 9], [5, "bg_ngoc", false, 0, 10, [[2, -325, [31], 32]], [0, "0cELAT0LpDloMYs03XFrqJ", 1], [5, 161, 84], [29, -16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 10, [-326], [0, "08VvDWlY5JhZ00OTJr+G4u", 1], [5, 111, 111], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 88, [33]], [6, 60], [7, 1.05, 3, 10, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "3", 2]], [4, 4294967295], [4, 4294967295], 10], [5, "bg_ngoc", false, 0, 11, [[2, -327, [41], 42]], [0, "d0Lj16lmhI8oVpO7BcRcZh", 1], [5, 161, 84], [29, -16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 11, [-328], [0, "1bnrShv1lEdoWwDUNn3SEN", 1], [5, 111, 111], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 93, [43]], [6, 61], [7, 1.05, 3, 11, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "4", 2]], [4, 4294967295], [4, 4294967295], 11], [5, "bg_ngoc", false, 0, 12, [[2, -329, [51], 52]], [0, "93JJ63xSlDv5TTS0knh1+z", 1], [5, 161, 84], [29, -16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 12, [-330], [0, "a2mjwbcvtIxo1l50tkZXsH", 1], [5, 111, 111], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 98, [53]], [6, 62], [7, 1.05, 3, 12, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "5", 2]], [4, 4294967295], [4, 4294967295], 12], [5, "bg_ngoc", false, 0, 13, [[2, -331, [61], 62]], [0, "a4yRmbZTJABK7orkV7ruKn", 1], [5, 161, 84], [29, -16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 13, [-332], [0, "860XE2RAdEFouTFVthRlRn", 1], [5, 111, 111], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 103, [63]], [6, 63], [7, 1.05, 3, 13, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "6", 2]], [4, 4294967295], [4, 4294967295], 13], [5, "bg_ngoc", false, 0, 14, [[2, -333, [71], 72]], [0, "642DU6tKBMjaH3GgDKSfhc", 1], [5, 161, 84], [29, -16, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 14, [-334], [0, "ccTRlfYORA4bSZidmiao+j", 1], [5, 111, 111], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 108, [73]], [6, 64], [7, 1.05, 3, 14, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "7", 2]], [4, 4294967295], [4, 4294967295], 14], [5, "bg_ngoc", false, 0, 15, [[2, -335, [82], 83]], [0, "60cIYC/cxH7qc5H0vaBiFk", 1], [5, 135, 62], [24.5, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 15, [-336], [0, "d5c9WR8ptIq7kM6j4Dm5dl", 1], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 113, [84]], [6, 65], [7, 1.05, 3, 15, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "8", 2]], [4, 4294967295], [4, 4294967295], 15], [5, "bg_ngoc", false, 0, 16, [[2, -337, [92], 93]], [0, "cft4WPgFNFwaEL47RMlRQ2", 1], [5, 135, 62], [24.5, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 16, [-338], [0, "100/4+l2dFIJZv+VIdH2gy", 1], [5, 100, 100], [0, 2, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 118, [94]], [6, 66], [7, 1.05, 3, 16, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "9", 2]], [4, 4294967295], [4, 4294967295], 16], [5, "bg_ngoc", false, 0, 17, [[2, -339, [102], 103]], [0, "77vTRFWw9K449bpAJiAdYC", 1], [5, 135, 62], [24.5, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 17, [-340], [0, "65K1OBGOVOVKmpJsgHCoaB", 1], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 123, [104]], [6, 67], [7, 1.05, 3, 17, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "10", 2]], [4, 4294967295], [4, 4294967295], 17], [5, "bg_ngoc", false, 0, 18, [[2, -341, [112], 113]], [0, "f1Y3N9ajxH5oJI82t2ru0g", 1], [5, 135, 62], [24.5, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 18, [-342], [0, "f5/Kx2xrhLfpIANgt7V+mH", 1], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 128, [114]], [6, 68], [7, 1.05, 3, 18, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "11", 2]], [4, 4294967295], [4, 4294967295], 18], [5, "bg_ngoc", false, 0, 19, [[2, -343, [122], 123]], [0, "1aAtYhrKJNgYjLIIqwbTUQ", 1], [5, 135, 62], [27.6, -15.3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 19, [-344], [0, "11SPZwloRDXLNz8jKCXkaz", 1], [5, 90, 90], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 133, [124]], [6, 69], [7, 1.05, 3, 19, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "12", 2]], [4, 4294967295], [4, 4294967295], 19], [5, "bg_ngoc", false, 0, 20, [[2, -345, [132], 133]], [0, "9bT9bjdqJOCI8eghGx+bAi", 1], [5, 135, 62], [27.6, -15.3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 20, [-346], [0, "64Bf3kC2BPP7Jb6mHps8zW", 1], [5, 90, 90], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 138, [134]], [6, 70], [7, 1.05, 3, 20, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "13", 2]], [4, 4294967295], [4, 4294967295], 20], [5, "bg_ngoc", false, 0, 21, [[2, -347, [142], 143]], [0, "c2SyKcAXtJJrydgMX/K0zC", 1], [5, 135, 62], [27.6, -15.3, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sprite", 0, 21, [-348], [0, "02aRPOKbBD74BCYjAaIsZx", 1], [5, 90, 90], [0, 0, 0, 0, 0, 0, 1, 1.406, 1.406, 1.406]], [12, 0, 143, [144]], [6, 71], [7, 1.05, 3, 21, [[4, "06d31XDyaxNc5ExI4Dw7pBS", "pickClicked", "14", 2]], [4, 4294967295], [4, 4294967295], 21], [21, "bg_nhan", 0, 22, [[2, -349, [155], 156]], [0, "bbLwIsOM1Gfrmh+ALcQDpS", 1], [5, 300, 238]], [3, "spriteBall", 0, 22, [-350], [0, "e4XRUa8h9NEatlZYQrPIBd", 1], [5, 130, 110.1], [0, -6.1, 0, 0, 0, 0, 1, 2.03, 2.03, 2.03]], [19, 0, false, 148, [157]], [22, "spriteX", false, 0, 22, [-351], [0, "55IhIQgQ1I9IWDif5CSvgx", 1], [5, 130, 105], [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 2, false, 150], [24, 22, [[4, "cfec3/GtxtG3pZDeXJvg8gF", "pickClicked", "1", 5]], [4, 4294967295], [4, 4294967295], 22], [21, "bg_nhan", 0, 23, [[2, -352, [158], 159]], [0, "3cq8p1w4FH/7P/HVk9lVxK", 1], [5, 300, 238]], [3, "spriteBall", 0, 23, [-353], [0, "cdy1Jd2ZNKc5FP8FUsmlgT", 1], [5, 130, 110.1], [0, -6.1, 0, 0, 0, 0, 1, 2.03, 2.03, 2.03]], [19, 0, false, 154, [160]], [22, "spriteX", false, 0, 23, [-354], [0, "baeKkp4J5CqIrOzfUqCqaa", 1], [5, 130, 105], [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [30, 2, false, 156], [24, 23, [[4, "cfec3/GtxtG3pZDeXJvg8gF", "pickClicked", "2", 5]], [4, 4294967295], [4, 4294967295], 23], [21, "bg_nhan", 0, 24, [[2, -355, [161], 162]], [0, "0dnaglSk9FYJsLZK7HHY9J", 1], [5, 300, 238]], [3, "spriteBall", 0, 24, [-356], [0, "9dlqOMjWdB1JmZf/19qVvx", 1], [5, 130, 110.1], [0, -6.1, 0, 0, 0, 0, 1, 2.03, 2.03, 2.03]], [19, 0, false, 160, [163]], [22, "spriteX", false, 0, 24, [-357], [0, "a6oQeTw09O74wBRJm59FWx", 1], [5, 130, 105], [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 2, false, 162, [164]], [24, 24, [[4, "cfec3/GtxtG3pZDeXJvg8gF", "pickClicked", "3", 5]], [4, 4294967295], [4, 4294967295], 24], [5, "logo", false, 0, 4, [[2, -358, [172], 173]], [0, "5fze1U+PJFm5RzeSsOARMG", 1], [5, 453, 86], [0, 271, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "tex", false, 0, 4, [[2, -359, [174], 175]], [0, "6eTF0xioZJL49702ix9CX6", 1], [5, 707, 49], [56, -285, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [53, 1, "T<PERSON> động chơi Bonus Game sau 10s", 28, false, false, 1, 73, [176]], [6, 74], [7, 1.05, 3, 55, [[20, "57446/nohhJsIVxYKXCrbP/", "quickPlayClicked", 1]], [4, 4294967295], [4, 4294967295], 55], [27, "label", 0, 7, [[33, "<PERSON><PERSON><PERSON> mừng bạn đã được", 34, 50, false, 1, 1, -360, [191], 192]], [0, "0eRtZuSZ9LZq4Lbcsfbg7Q", 1], [5, 377.4, 50], [0, 29, 0, 0, 0, 0, 1, 1.836, 1.836, 1.836]], [3, "lbWin", 0, 7, [-361], [0, "c0RhW7G2hAxIEAuWwNGXUM", 1], [5, 310.06, 80], [0, -39, 0, 0, 0, 0, 1, 1.836, 1.836, 1.836]], [54, "1,000,000,000", 22, 80, false, 1, 1, 171, [193]]], 0, [0, 7, 1, 0, 8, 169, 0, 9, 168, 0, 10, 167, 0, 11, 7, 0, 12, 5, 0, 13, 2, 0, 14, 4, 0, 15, 6, 0, 0, 1, 0, -1, 56, 0, -2, 6, 0, -3, 4, 0, 0, 2, 0, 0, 2, 0, -1, 79, 0, -2, 84, 0, -3, 89, 0, -4, 94, 0, -5, 99, 0, -6, 104, 0, -7, 109, 0, -8, 114, 0, -9, 119, 0, -10, 124, 0, -11, 129, 0, -12, 134, 0, -13, 139, 0, -14, 144, 0, -1, 27, 0, -2, 29, 0, -3, 31, 0, -4, 33, 0, -5, 35, 0, -6, 37, 0, -7, 39, 0, -8, 41, 0, -9, 43, 0, -10, 45, 0, -11, 47, 0, -12, 49, 0, -13, 51, 0, -14, 53, 0, -1, 80, 0, -2, 85, 0, -3, 90, 0, -4, 95, 0, -5, 100, 0, -6, 105, 0, -7, 110, 0, -8, 115, 0, -9, 120, 0, -10, 125, 0, -11, 130, 0, -12, 135, 0, -13, 140, 0, -14, 145, 0, -1, 81, 0, -2, 86, 0, -3, 91, 0, -4, 96, 0, -5, 101, 0, -6, 106, 0, -7, 111, 0, -8, 116, 0, -9, 121, 0, -10, 126, 0, -11, 131, 0, -12, 136, 0, -13, 141, 0, -14, 146, 0, 0, 2, 0, -1, 3, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, -5, 12, 0, -6, 13, 0, -7, 14, 0, -8, 15, 0, -9, 16, 0, -10, 17, 0, -11, 18, 0, -12, 19, 0, -13, 20, 0, -14, 21, 0, -2, 5, 0, -3, 165, 0, -4, 166, 0, -5, 72, 0, -6, 26, 0, -7, 55, 0, -8, 7, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 151, 0, -2, 157, 0, -3, 163, 0, -1, 149, 0, -2, 155, 0, -3, 161, 0, -1, 152, 0, -2, 158, 0, -3, 164, 0, 0, 5, 0, -1, 25, 0, 0, 6, 0, 0, 6, 0, 16, 6, 0, 0, 6, 0, -1, 57, 0, -2, 76, 0, 0, 7, 0, 17, 172, 0, 0, 7, 0, -1, 75, 0, -2, 170, 0, -3, 171, 0, -1, 81, 0, -1, 77, 0, -2, 78, 0, -3, 27, 0, -4, 28, 0, -1, 86, 0, -1, 82, 0, -2, 83, 0, -3, 29, 0, -4, 30, 0, -1, 91, 0, -1, 87, 0, -2, 88, 0, -3, 31, 0, -4, 32, 0, -1, 96, 0, -1, 92, 0, -2, 93, 0, -3, 33, 0, -4, 34, 0, -1, 101, 0, -1, 97, 0, -2, 98, 0, -3, 35, 0, -4, 36, 0, -1, 106, 0, -1, 102, 0, -2, 103, 0, -3, 37, 0, -4, 38, 0, -1, 111, 0, -1, 107, 0, -2, 108, 0, -3, 39, 0, -4, 40, 0, -1, 116, 0, -1, 112, 0, -2, 113, 0, -3, 41, 0, -4, 42, 0, -1, 121, 0, -1, 117, 0, -2, 118, 0, -3, 43, 0, -4, 44, 0, -1, 126, 0, -1, 122, 0, -2, 123, 0, -3, 45, 0, -4, 46, 0, -1, 131, 0, -1, 127, 0, -2, 128, 0, -3, 47, 0, -4, 48, 0, -1, 136, 0, -1, 132, 0, -2, 133, 0, -3, 49, 0, -4, 50, 0, -1, 141, 0, -1, 137, 0, -2, 138, 0, -3, 51, 0, -4, 52, 0, -1, 146, 0, -1, 142, 0, -2, 143, 0, -3, 53, 0, -4, 54, 0, -1, 152, 0, -1, 147, 0, -2, 148, 0, -3, 150, 0, -1, 158, 0, -1, 153, 0, -2, 154, 0, -3, 156, 0, -1, 164, 0, -1, 159, 0, -2, 160, 0, -3, 162, 0, 0, 25, 0, 0, 26, 0, 18, 26, 0, 0, 26, 0, -1, 74, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, -1, 58, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, -1, 59, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, -1, 60, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, -1, 61, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, -1, 62, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, -1, 63, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, -1, 64, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, -1, 65, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, -1, 66, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, -1, 67, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, -1, 68, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, -1, 69, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, -1, 70, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, -1, 71, 0, 0, 55, 0, -2, 169, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, -2, 80, 0, 0, 59, 0, -2, 85, 0, 0, 60, 0, -2, 90, 0, 0, 61, 0, -2, 95, 0, 0, 62, 0, -2, 100, 0, 0, 63, 0, -2, 105, 0, 0, 64, 0, -2, 110, 0, 0, 65, 0, -2, 115, 0, 0, 66, 0, -2, 120, 0, 0, 67, 0, -2, 125, 0, 0, 68, 0, -2, 130, 0, 0, 69, 0, -2, 135, 0, 0, 70, 0, -2, 140, 0, 0, 71, 0, -2, 145, 0, 0, 72, 0, -1, 73, 0, -1, 167, 0, 0, 73, 0, 0, 74, 0, -2, 168, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, -1, 79, 0, 0, 82, 0, -1, 84, 0, 0, 87, 0, -1, 89, 0, 0, 92, 0, -1, 94, 0, 0, 97, 0, -1, 99, 0, 0, 102, 0, -1, 104, 0, 0, 107, 0, -1, 109, 0, 0, 112, 0, -1, 114, 0, 0, 117, 0, -1, 119, 0, 0, 122, 0, -1, 124, 0, 0, 127, 0, -1, 129, 0, 0, 132, 0, -1, 134, 0, 0, 137, 0, -1, 139, 0, 0, 142, 0, -1, 144, 0, 0, 147, 0, -1, 149, 0, -1, 151, 0, 0, 153, 0, -1, 155, 0, -1, 157, 0, 0, 159, 0, -1, 161, 0, -1, 163, 0, 0, 165, 0, 0, 166, 0, 0, 170, 0, -1, 172, 0, 19, 1, 2, 6, 4, 22, 6, 25, 23, 6, 25, 24, 6, 25, 361], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 81, 84, 86, 89, 91, 94, 96, 99, 101, 104, 106, 109, 111, 114, 116, 119, 121, 124, 126, 129, 131, 134, 136, 139, 141, 144, 146, 149, 151, 155, 157, 161, 163, 167, 169, 172], [-1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, -1, 1, 2, -1, -1, 3, -1, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, -1, 1, 2, -1, 3, 1, 2, -1, -1, 1, 20, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -2, -3, -4, -5, -1, 1, -1, 1, -1, -1, 1, -1, 3, -1, 1, 5, 21, 22, 23, -1, 1, -1, 1, -1, 3, -1, -1, 1, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 5, 1, 1, 1, 1, 1, 1, 3, 5, 3], [0, 11, 0, 11, 0, 13, 0, 14, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 9, 0, 0, 4, 1, 1, 0, 5, 0, 6, 2, 2, 0, 9, 0, 4, 1, 1, 5, 6, 2, 2, 0, 9, 0, 4, 1, 1, 5, 6, 2, 2, 0, 9, 0, 4, 1, 1, 5, 6, 2, 2, 0, 9, 0, 4, 1, 1, 5, 6, 2, 2, 0, 9, 0, 0, 4, 1, 1, 5, 6, 2, 2, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 8, 0, 4, 1, 1, 5, 6, 2, 2, 0, 15, 27, 0, 12, 0, 0, 12, 0, 0, 12, 0, 0, 0, 15, 28, 10, 29, 30, 31, 0, 32, 0, 33, 0, 0, 34, 0, 16, 0, 17, 17, 35, 36, 37, 0, 38, 0, 11, 0, 13, 0, 0, 14, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 7, 3, 10, 3, 10, 3, 10, 39, 40, 16]], [[{"name": "thantai_outdoor_btn_item_0_1", "rect": [0, 0, 245, 198], "offset": [0, 0.5], "originalSize": [245, 199], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [41]], [[{"name": "thantai_bg_slots_fg", "rect": [0, 0, 921, 482], "offset": [0, 0], "originalSize": [921, 482], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [42]], [[{"name": "X5", "rect": [0, 1, 131, 104], "offset": [0, 0], "originalSize": [131, 106], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [43]], [[{"name": "tex", "rect": [0, 0, 707, 49], "offset": [0, 0], "originalSize": [707, 49], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [44]], [[{"name": "bg_ngoc", "rect": [0, 0, 161, 84], "offset": [0, 0], "originalSize": [161, 84], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [45]], [[{"name": "bg_tex", "rect": [0, 0, 656, 52], "offset": [0, 0], "originalSize": [656, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [46]], [[{"name": "effect", "rect": [2, 4, 226, 227], "offset": [1, -1], "originalSize": [228, 233], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [47]]]]