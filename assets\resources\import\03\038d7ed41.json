[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "4d4T0phw1AB5QBI4ZPHjM0", "baHWwsa15Jp4vcC0qqh9Xy", "84sL1PMLRE7bCsBxO5IwzV", "adQbfcQ29JB5aSRs90Tsf6", "e6YVTd7H9JHLmPP0pDDpWX", "35V7uXrJBBxpNmxnEjsOpQ", "1cDpn7iXVFOJlUGFAdLw99", "a3l6ic4lJMhqWRY3K84LO+", "1eysAYWdVGnIUPtkRM2c1e", "ff/TOQPBtI54j+e9GgBkAp", "9fk2JyJ4hG9Ke2KNK4kYDE", "a2tBXzjmRHWIetS1zkxuiC", "71NbLtWZVENZ16emRloX5s", "2eODcjjntIyKShSQa+mRZg", "beCw2Z2hxFyZsQ1gS12Syo", "baj4v5trtOyI7JTchbXAiK", "e1rBsAuLNE+LsXIX11poKi", "99s/4UcvtKmLh4Rk9nlSQx", "76XIlclNBJFY6dbyGQN/Jp", "b1FdnLn49AQY9AUBmOdLZD", "72hOUIvdtMLZ0oT88tRKh8", "5ckbco7cROAJ92hw6PR5eT", "364FKfQsVKeKDVm7e33NQK", "a4LbrG+m1FiJQK9CcpaToD", "c1dXVQWApGw7Q62cAoKV4i", "e4nmUQqB1B5oOPip8ewaBd", "33HOamjjFPGoj/1rwHcNWy", "65STJRaOhGZJTO2AQIt2zc", "46DU9yv2hDKLdCvqpAens+", "e7q6FL+VZEgLJUjVeDLic/", "11ONk2ePRLa7ZuMeQ9nQtF", "9eGy9DOGtPnbUtHULUTdHs", "fc1w62F7pMnKg2495+b9ga", "56Y/mzv4dJHKrdnOBu7W2O", "64mtbyC+RKQLqTXA21mmM5", "fdNoodJKVLj4dF1TLppv2g", "2cWB/vWPRHja3uQTinHH30", "c9UC8UI/RCHLwApeZf7qKU", "91WFQBMZlNF5i3m7o0yZut", "bbrDBQOvlCBYBb6UsTyg3Y", "017Jn3Zv1Ft7hygdjpaSoK", "96NF2Gu/FBsYEqsfJEuyf0", "60lCKF0BFLRJpfGf89TIRK", "82YKFNkBxFYIPl/b4NYcI9", "56G9gPp99C/J9ushMMRhq6", "22VEsJk0JMWrT7qgFs4w4W", "2dyDLTKclIyYrzVcwsynPc", "f9wbr2rfFO/JFEIH/sgFUX", "feNjtyZeZLL7laJtfQR2Tx", "54CYv5FA5Ed5DfO8QGUHyA", "96xfRpRepNtr+F1yZE9aL/", "aeFoKMM4hMfZC5HmqWcwkp", "58qYzEonpM7bc88+s6D5ze", "60riaKxOZNfKcxE67XOQIk", "aaD8ucCqlIW77wPW5kPw+W", "44CFdkRFVAOJNvaq+Wx5rT", "48T6THjOhLpIXinwclqdz+", "52kNPnnERJCqckBlge0SQP", "0e9P4V1GdM85UkCubmruRY", "42FqTAlBVHKoGRMN4Pw/kZ", "4aMB16aS9CLajkSS3URg8B", "33c4hhKKpD1qdhmz5XR4Sd", "64RsUNQH9GY5w6n1E6KULV", "e5D0yQ7slGRZVBYiO+VEfv", "bbSWlEymxHy6Q8k7BRgVAu", "ccDbedzg5F4Jv6esdeuHBe", "92IwTJw+FPDq/m0A6C66XY", "41SYL/wh1GDLmEEVr3Ts55", "ec6QY4cHZLeaYgV992xDAW", "87xguutRxGZ53EeW91ezNf", "cfZWuIFlFHQog+8zM/0OrN", "adHPXBpYpBwJpUa63VW1fA", "cakwpVCE5P84ZVol4M1FMa", "535GMeplBGK5Sarni7772F", "9d8tEOKtpKrqL2F7pq2m5G", "e7w7fitqNB9aJ2MpBppmAn", "fcNRrTkl9NSJ9ZDNVH7BzR", "95RCYcYw5Ml5I88HVQv0mO", "79LQ9dC+9JLY5mLCkHXVZ/", "3dh5mrOMlEqZ5HgerRZ5U1", "dbv2rTXBpAuLm3xwJl0sIB", "c5t364ReNAJ4VnBA4OypXR", "5bnGXjFUJCwKkXsBv9B+7s", "1ceDDw4cBIGo5/5dNyW+aw", "73OtDaPBNK05MxhfHXG/f7", "b9cL1vIWJM5pptDsfnnjbU", "98o0laL3xA04lZe3tt+yhX", "2e00CX59pMPYJH4/pMoMiv", "089KJgBetIep/UZzU54vNA", "d0jxIU8c5OY7xr3Fir6MDd", "ccDHb15zJNGoos6UsEcnu3", "fesducK3JPrqJILCYe68up", "e5G0D7aslCCoxb8e47NfGw", "4bWxY8UidCz7MVd3VVfjhU", "91mwpGJfNDnJr3IxgVpNBe", "b6sarjaCpJ2oT2nY7QbnnK", "acvsWge75Lboog/AeN/FF5", "90/+Mdcj9NPLv7GT7X3O6K", "83z2yOsnJPr4ovwF+m5dnP", "d3PUD4Q1NO8Yworffw4jSx", "118PxBSTxIZKXm6pESDNYE", "fdI04Ro4pHiI11rJcGVOaW", "4dAHxvy1tDGbDPy5Iyu0qR", "137Gw5xIJEN7nuLxKNcS2e", "f8E1S8BaNKwZUxz92z8fch", "7a/QZLET9IDreTiBfRn2PD", "48rNiLsKJE9JxNBJsPpDup", "deznjldl9OCqvTVWbhwSbm", "53QF0OyZ1D55fgjwVxpBcs", "a33UZXc/5Pzo5TFjSuAxtE", "ccN4ASiitC95aD9Lut28Yz", "f0jwjWQBZMJoR/9qG8ndYb", "abpLQmo1tJV6dbSUq6/UgU", "78UVQRb29DxIV8cks+51nR", "236+Bac+lJe75IHPQ5FQhY", "0aJpZ46WtPBbaMMGug+Ham", "6cH6A0iI5NobNHapuiYy9w", "8bk4OB12VHrqJItHKofG9f", "44F5RqzeBAOYwVwVHHVTHG", "6265JGQBhAA4xZCkfma9xo", "dbrfKp8QRLXo+LAovF6R1r", "f8zmoIsllG8ozQ9xZTcydw", "02EpDKtlpDCaBTEg+Y4cu2", "5cqfMMaphGDY1i7mqBlPy+", "74KjI2RXFEFJxQh8wshuC1", "14PdXA1n9JS5s6r75Cyugw", "8fSA+XMDRMgog5+JKqHoFc", "7dFN9M5VhLBbPjJLZpEK6L", "f26343FIdChJbsteFZN2Jb", "bcIDAFuvtPTLWKfoXs/CUK", "29FYIk+N1GYaeWH/q1NxQO", "f8ikgKlIRA8KZ+2oAI42zr", "c8t1YBC29NgJrJYWfpuNWe", "e0Fiii8DxOCpc7pOK8LcgF", "d7QBcY3VdKu4mh9/D7FA4P", "74LaZozrlOqbgrJ9hy8OI3", "4foKF4BbdPyrHUBYZyB/Gx", "22I0XgqUhH2ovm86fLxprG", "74d1cjZntOor3CxJaoTaHV", "c2kuBa9sRIbLRPrnMxmWyS", "c4bpAu1T5Deph3IUgJp6c4", "22KnN1v/FOU6IoaxJPvrZA", "9dA5aXjl9Bbq+WnYky+D+2", "2dDCcXf0xJBIT02sTgjvVN", "cbH7ylu+BOYYbsRArX1ne2", "d5dabkL4BMXqH917S5ijOR"], ["node", "_spriteFrame", "_N$file", "_N$target", "_textureSetter", "_parent", "_N$skeletonData", "_normalMaterial", "_N$barSprite", "_defaultClip", "_N$font", "_clip", "lbTimeNan", "<PERSON><PERSON><PERSON>", "fatThirdCard", "fatSecondCard", "fatFirstCard", "fatBackCard", "lbSID", "lbMessage", "root", "popUpWin", "lbTotalFeedChicken", "dealer", "lbPlayerStatus", "lbInfo", "lbTableId", "lbRoomID", "nodeParentChat", "nodeMainRoomView", "nodeChooseRoomView", "nodeRegisterLeave", "spriteBack", "spriteSound", "nodeBuyResult", "nodeSell", "nodeBuy", "popupAsk", "nodeChickenFet", "groupBien", "groupBet", "lbCurrentBet", "nodeBtnReceivSide", "nodeBtnFedChicken", "nodeBtnBetSide2", "nodeBtnBetSide1", "nodeBtnBet", "sliderBetValue", "btnSendChat", "editBoxChat", "chatListView", "lbTotalWin", "lbNickName", "lbRank", "rtAdmin", "lbName", "chipBet", "moveCard", "musicBackground", "_N$content", "BCTopListView", "data", "spriteCardBack", "prefabChat", "sfAvatarDef", "bmfWin", "bmfLose", "prefab", "prefabHelp", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs", "_anchorPoint", "_color", "_eulerAngles"], 0, 4, 1, 9, 5, 2, 7, 5, 5, 5], "cc.SpriteFrame", ["cc.Label", ["_isSystemFontUsed", "_fontSize", "_N$verticalAlign", "_N$horizontalAlign", "_string", "_N$overflow", "_lineHeight", "_enableWrapText", "_spacingX", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_anchorPoint", "_color", "_children"], 1, 2, 4, 5, 1, 7, 5, 5, 2], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_srcBlendFactor", "_fillType", "_fillStart", "_fillRange", "node", "_materials", "_spriteFrame", "_fillCenter"], -5, 1, 3, 6, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_enabled", "_N$spacingX", "_N$paddingLeft", "_N$paddingRight", "_N$spacingY", "_N$paddingTop", "_N$paddingBottom", "node", "_layoutSize"], -6, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_N$enableAutoGrayEffect", "_N$interactable", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_normalMaterial"], -1, 1, 9, 1, 5, 5, 5, 6], ["cc.Node", ["_name", "_active", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_children", "_color"], 1, 4, 1, 12, 5, 7, 12, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$handleTouchEvent", "node", "_N$font"], -3, 1, 6], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs", "_parent"], 2, 2, 12, 4, 5, 7, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.ProgressBar", ["_N$mode", "_N$totalLength", "_N$progress", "node", "_N$barSprite"], 0, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.AudioSource", ["preload", "_volume", "_loop", "node"], 0, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_N$skeletonData", "_materials"], -1, 1, 6, 3], ["cc.PrivateNode", ["_name", "_active", "_zIndex", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_position", "_scale", "_color"], 0, 1, 9, 4, 5, 5, 8, 8, 5], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 1, 11], ["cc.AnimationClip", ["_name", "_duration", "events", "curveData"], -1], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["270b2V+1hxGr7jm4iAk2tii", ["node", "lbSID", "lbRoomID", "lbTableId", "lbInfo", "lbPlayerStatus", "BCPlayers", "dealer", "lbTotalFeedChicken", "fatBackCard", "fatFirstCard", "fatSecondCard", "fatThirdCard", "<PERSON><PERSON><PERSON>", "lbTimeNan", "popUpWin", "spriteCardBack"], 3, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6], ["847ffXbIbxBvLyl+Mj81BuJ", ["node", "spriteSound", "sfSounds", "spriteBack", "nodeRegisterLeave", "nodeChooseRoomView", "nodeMainRoomView", "nodeParentChat", "prefabChat"], 3, 1, 1, 3, 1, 1, 1, 1, 1, 6], ["4fb66Zk0vFI5a3A3R+uuuFw", ["node", "sfBacks", "sfBiens", "sfAvatarDef", "bmfWin", "bmfLose"], 3, 1, 3, 3, 6, 6, 6], ["8b1d6iQkZFPBawrgTt4ojie", ["node", "popupAsk", "nodeBuy", "nodeSell", "nodeBuyResult"], 3, 1, 1, 1, 1, 1], ["b53604HHtVJVozVjSo1sI5d", ["node", "prefab"], 3, 1, 6], ["24ed6LRdrlC7YXd2HOPZQ2C", ["node", "nodeCards", "groupCardFirst", "groupCardSecond", "groupCardThird", "fatBackCard", "fatFirstCard", "fatSecondCard", "fatThirdCard", "<PERSON><PERSON><PERSON>", "lbTimeNan", "nodeChickenFet"], 3, 1, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1], ["56bd4y7a3hNp5RXp+tVhS56", ["node", "sliderBetValue", "nodeBtnBet", "nodeBtnBetSide1", "nodeBtnBetSide2", "nodeBtnFedChicken", "nodeBtnReceivSide", "lbCurrentBet", "groupBet", "groupBien"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["59d80V9xtxJYrNAwGrKLCoS", ["node", "prefabHelp"], 3, 1, 6], ["c9ea2HJ+4FBwJf8JdBQBbUQ", ["channelId", "node", "chatListView", "editBoxChat", "btnSendChat"], 2, 1, 1, 1, 1], ["24599EH74xGALpp/as8ka/r", ["node", "lbRank", "lbSID", "lbNickName", "lbTotalWin"], 3, 1, 1, 1, 1, 1], ["0189d13ogtDM7awTFcGZjeU", ["node"], 3, 1], ["8be9fmDgjRGuJDpCIp+Bx7T", ["node", "nodeUser", "lbName", "lbMessage", "rtAdmin"], 3, 1, 1, 1, 1, 1], ["e1d47OmuNVDDrDGxrmilkwx", ["node", "musicBackground", "moveCard", "chipBet"], 3, 1, 1, 1, 1], ["8457bzXi+RCFrWEyccPy/PF", ["touchParent", "node"], 2, 1], ["9f6b2nZ+DxPyKKB6bv1s1Zt", ["node"], 3, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["832cdd6gThBUZ3q0q4hjRy5", ["messWinPosY", "node", "nodeMessage", "lbMessage"], 2, 1, 1, 1], ["fde5fYTdUdLFaHQ7QSWDYdb", ["node"], 3, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["3a139t42a5OfqWFdRGvxQXQ", ["node", "BCTopListView"], 3, 1, 1], ["6e37d/myClEaKavwWnoFb9e", ["node", "card<PERSON>irs<PERSON>", "cardSecond", "cardThird", "nodeBetInfo"], 3, 1, 1, 1, 1, 1], ["cc.Slider", ["direction", "_N$progress", "node", "slideEvents", "_N$handle"], 1, 1, 9, 1], ["87b0b6j4kBLKKRiRXRQjf/0", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "editingReturn", "_N$textLabel", "_N$placeholderLabel", "_N$background"], 1, 1, 9, 1, 1, 1], ["39ffdxvVYlPd5jJUTozwX7F", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1]], [[21, 0, 1, 2], [0, 0, 4, 5, 3, 6, 8, 2], [0, 0, 4, 7, 5, 3, 6, 8, 2], [4, 8, 9, 10, 1], [0, 0, 4, 5, 3, 6, 2], [0, 0, 1, 4, 5, 3, 6, 8, 3], [2, 4, 1, 0, 3, 2, 9, 10, 11, 6], [8, 1, 2, 1], [11, 0, 1, 3, 3], [4, 0, 8, 9, 10, 2], [4, 8, 10, 1], [4, 0, 8, 10, 2], [6, 0, 4, 5, 7, 6, 2], [4, 1, 0, 8, 9, 10, 3], [32, 0, 1], [2, 4, 1, 0, 3, 2, 9, 11, 6], [0, 0, 4, 5, 3, 10, 6, 8, 2], [16, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 4, 5, 3, 6, 8, 11, 3], [0, 0, 4, 5, 3, 10, 6, 2], [4, 1, 0, 8, 10, 3], [0, 0, 1, 4, 7, 3, 3], [6, 0, 4, 5, 7, 6, 10, 2], [11, 0, 1, 2, 3, 4], [2, 4, 1, 6, 0, 3, 2, 5, 9, 11, 8], [4, 1, 0, 2, 8, 9, 10, 4], [40, 0, 1], [41, 0, 1], [0, 0, 1, 4, 7, 5, 3, 6, 8, 3], [7, 0, 3, 4, 2, 5, 2], [10, 0, 1, 2, 3, 4, 5, 2], [4, 1, 0, 5, 6, 7, 8, 9, 11, 6], [12, 0, 3, 4, 2], [43, 0, 1, 2, 3, 4, 1], [0, 0, 2, 4, 7, 5, 3, 6, 8, 3], [0, 0, 4, 7, 5, 3, 6, 2], [3, 0, 5, 2, 3, 4, 7, 6, 2], [8, 1, 2, 3, 1], [2, 4, 1, 0, 3, 2, 9, 10, 6], [2, 1, 0, 3, 2, 5, 9, 10, 11, 6], [2, 4, 1, 0, 3, 2, 5, 9, 10, 11, 7], [2, 4, 1, 0, 2, 9, 11, 5], [0, 0, 4, 5, 3, 6, 9, 8, 2], [7, 0, 3, 4, 2, 5, 6, 2], [4, 0, 8, 2], [37, 0, 1], [35, 0, 1, 2], [38, 0, 1], [17, 0, 1, 2, 3, 4, 5, 10, 6, 7, 8, 9, 4], [0, 0, 4, 7, 5, 3, 8, 2], [0, 0, 1, 4, 5, 3, 6, 9, 8, 3], [3, 0, 5, 2, 3, 2], [3, 0, 5, 2, 3, 4, 6, 2], [5, 2, 0, 1, 6, 9, 10, 5], [4, 1, 0, 8, 9, 3], [6, 1, 0, 4, 5, 7, 8, 9, 6, 3], [2, 4, 1, 0, 3, 2, 5, 9, 11, 7], [0, 0, 7, 5, 3, 6, 8, 2], [0, 0, 4, 7, 5, 3, 2], [0, 0, 2, 4, 5, 3, 6, 3], [0, 0, 4, 7, 3, 2], [0, 0, 4, 7, 3, 8, 2], [3, 0, 9, 2, 3, 4, 6, 2], [3, 0, 5, 9, 2, 3, 4, 6, 2], [3, 0, 5, 2, 3, 8, 4, 6, 2], [3, 0, 5, 2, 3, 4, 2], [3, 0, 1, 5, 2, 3, 4, 7, 6, 3], [8, 0, 1, 2, 3, 2], [5, 2, 0, 1, 9, 10, 4], [5, 0, 1, 9, 10, 3], [5, 2, 0, 1, 3, 9, 10, 5], [4, 0, 2, 8, 9, 3], [9, 0, 1, 2, 6, 7, 4], [13, 0, 1, 2, 3, 4, 5, 6, 6], [14, 0, 1, 2, 2], [15, 0, 3, 2], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 4], [18, 0, 1, 2, 3], [19, 0, 1, 2, 3, 5], [20, 0, 2], [0, 0, 7, 3, 2], [0, 0, 1, 4, 3, 8, 3], [0, 0, 4, 3, 8, 2], [0, 0, 7, 5, 3, 10, 6, 8, 2], [0, 0, 1, 7, 5, 3, 6, 3], [0, 0, 7, 5, 3, 6, 9, 2], [0, 0, 1, 2, 4, 7, 5, 3, 4], [0, 0, 7, 5, 3, 6, 9, 8, 2], [0, 0, 1, 4, 7, 5, 3, 8, 3], [0, 0, 7, 5, 3, 6, 2], [0, 0, 1, 2, 7, 5, 3, 6, 4], [0, 0, 1, 4, 7, 5, 3, 6, 3], [0, 0, 2, 4, 5, 3, 6, 8, 3], [0, 0, 4, 7, 5, 3, 6, 9, 8, 2], [0, 0, 4, 7, 5, 3, 6, 9, 2], [0, 0, 1, 4, 5, 3, 6, 3], [0, 0, 4, 3, 2], [0, 0, 1, 4, 5, 3, 10, 6, 9, 3], [0, 0, 4, 3, 6, 9, 2], [7, 0, 7, 2, 2], [7, 0, 3, 7, 2, 2], [7, 0, 3, 4, 2, 8, 5, 6, 2], [7, 0, 1, 3, 4, 2, 5, 6, 3], [10, 0, 6, 1, 2, 3, 4, 5, 2], [3, 0, 9, 2, 3, 4, 7, 6, 2], [3, 0, 5, 9, 2, 3, 8, 4, 7, 6, 2], [3, 0, 5, 2, 3, 8, 4, 2], [3, 0, 5, 2, 3, 8, 4, 7, 2], [3, 0, 5, 2, 3, 8, 4, 7, 6, 2], [3, 0, 1, 5, 2, 3, 8, 4, 7, 6, 3], [22, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 1], [23, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [24, 0, 1, 2, 3, 4, 5, 1], [25, 0, 1, 2, 3, 4, 1], [26, 0, 1, 1], [27, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [5, 2, 0, 1, 4, 5, 3, 9, 10, 7], [5, 2, 0, 1, 4, 5, 7, 8, 3, 6, 9, 10, 10], [5, 0, 1, 3, 9, 10, 4], [28, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [29, 0, 1, 1], [30, 0, 1, 2, 3, 4, 2], [31, 0, 1, 2, 3, 4, 1], [4, 3, 4, 0, 2, 8, 5], [4, 3, 8, 9, 2], [4, 0, 2, 8, 9, 10, 3], [6, 4, 5, 7, 6, 1], [6, 4, 5, 1], [6, 1, 2, 0, 4, 8, 9, 6, 4], [6, 1, 3, 0, 4, 5, 8, 9, 6, 4], [33, 0, 1, 2, 3, 4, 1], [34, 0, 1, 2, 3, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 7], [9, 0, 1, 2, 3, 4, 5, 6, 7], [36, 0, 1], [12, 1, 0, 2, 3, 4, 4], [39, 0, 1, 2, 3, 2], [13, 0, 5, 6, 2], [2, 4, 1, 0, 8, 3, 2, 5, 9, 10, 11, 8], [2, 4, 0, 9, 11, 3], [2, 4, 0, 3, 2, 5], [2, 1, 0, 3, 2, 9, 10, 11, 5], [2, 1, 6, 0, 3, 2, 5, 9, 10, 7], [2, 1, 0, 3, 2, 9, 5], [2, 4, 1, 6, 7, 0, 3, 2, 5, 9, 9], [2, 4, 1, 6, 7, 0, 3, 2, 9, 11, 8], [2, 1, 6, 7, 0, 3, 2, 9, 10, 7], [2, 1, 6, 7, 0, 2, 5, 9, 10, 7], [2, 1, 6, 7, 0, 2, 5, 9, 7], [2, 4, 1, 6, 7, 0, 2, 5, 9, 10, 8], [2, 4, 1, 0, 3, 2, 9, 6], [2, 4, 1, 6, 7, 0, 5, 9, 10, 7], [14, 0, 1, 2], [42, 0, 1, 1], [15, 1, 2, 0, 3, 4], [16, 0, 1, 2, 3, 4, 6, 5], [44, 0, 1, 2, 3, 4, 3], [45, 0, 1, 2, 3, 4, 3], [46, 0, 1, 2, 3, 4, 5, 6, 3], [47, 0, 1, 2, 3, 4, 3]], [[[{"name": "slice_money", "rect": [0, 0, 101, 51], "offset": [0, 0], "originalSize": [101, 51], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [45]], [[{"name": "btn_gopGa", "rect": [0, 0, 150, 63], "offset": [0, 0], "originalSize": [150, 63], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [46]], [[{"name": "bien_off", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [47]], [[{"name": "btn_back", "rect": [0, 0, 60, 50], "offset": [0, 0], "originalSize": [60, 50], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [48]], [[{"name": "icon_Chuog", "rect": [0, 0, 33, 33], "offset": [0, 0], "originalSize": [33, 33], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [49]], [[[77, "showMoney", 2.5, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 200}, {"frame": 2.4166666666666665, "value": 255}, {"frame": 2.5, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0.5, 0.5]], [{"frame": 0.23333333333333334}, "value", 8, [0, 1, 1]], [{"frame": 2.5}, "value", 8, [0, 1, 1]]], 11, 11, 11]]]]], 0, 0, [], [], []], [[{"name": "mask", "rect": [0, 0, 41, 37], "offset": [0, 0], "originalSize": [41, 37], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [50]], [[{"name": "nameBC", "rect": [0, 1, 301, 64], "offset": [0, -0.5], "originalSize": [301, 65], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [51]], [[{"name": "ga", "rect": [0, 0, 24, 32], "offset": [0, 0], "originalSize": [24, 32], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [52]], [[{"name": "btn_cuoc", "rect": [0, 0, 150, 63], "offset": [0, 0], "originalSize": [150, 63], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [53]], [[{"name": "tb1", "rect": [0, 0, 1118, 490], "offset": [0, 0], "originalSize": [1118, 490], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [54]], [[{"name": "btn_bien", "rect": [0, 0, 150, 63], "offset": [0, 0], "originalSize": [150, 63], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [55]], [[[79, "3<PERSON><PERSON><PERSON>"], [80, "3<PERSON><PERSON><PERSON>", [-2, -3], [0, "53253gSgJPFpMlB0EBeLo0", -1]], [49, "3CView", 1, [-40, -41, -42, -43, -44, -45, -46, -47, -48], [[110, -25, -24, -23, -22, -21, -20, [-13, -14, -15, -16, -17, -18, -19], -12, -11, -10, -9, -8, -7, -6, -5, -4, 499], [111, -32, -31, [500, 501], -30, -29, -28, -27, -26, 502], [112, -33, [504, 505], [508, 509], 503, 506, 507], [113, -38, -37, -36, -35, -34], [114, -39, 510]], [0, "79fTdgJ1VJ05DsZmaRyGzs", 1], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [99, "cardGroups", [[-49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61, -62, -63, -64, -65, -66, -67, -68, -69, [81, "nodeCardStart", false, -70, [0, "f6yLZ93+hKqa1cD+K2F5F1", 1], [-10, 112, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], [0, "3aS3jJk9xLerU7RVDcLZSa", 1]], [30, "btnPos2", [-73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88], [[[7, -71, [182, 183]], -72], 4, 1], [0, "e3881PtQpBTI+7ROP/3ZhX", 1], [5, 100, 100], [530.3, -229, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btnPos3", [-91, -92, -93, -94, -95, -96, -97, -98, -99, -100, -101, -102, -103, -104, -105, -106], [[[7, -89, [224, 225]], -90], 4, 1], [0, "e03BpZ2lZB0osbUuFMz/h6", 1], [5, 100, 100], [550, -48, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btnPos4", [-109, -110, -111, -112, -113, -114, -115, -116, -117, -118, -119, -120, -121, -122, -123, -124], [[[7, -107, [266, 267]], -108], 4, 1], [0, "56BSXN971G+KsqFmmwxFLs", 1], [5, 100, 100], [531, 131, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btnPos7", [-127, -128, -129, -130, -131, -132, -133, -134, -135, -136, -137, -138, -139, -140, -141, -142], [[[7, -125, [308, 309]], -126], 4, 1], [0, "5a2UlgD45BAaBD9hXFTLNq", 1], [5, 100, 100], [-496, 130, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btnPos6", [-145, -146, -147, -148, -149, -150, -151, -152, -153, -154, -155, -156, -157, -158, -159, -160], [[[7, -143, [350, 351]], -144], 4, 1], [0, "68vTesGSxBWLXG8rw3xgMo", 1], [5, 100, 100], [-555, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btnPos5", [-163, -164, -165, -166, -167, -168, -169, -170, -171, -172, -173, -174, -175, -176, -177, -178], [[[7, -161, [392, 393]], -162], 4, 1], [0, "45o2D7keFKw5yJU1J9Eo2J", 1], [5, 100, 100], [-480, -232, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "btnPos1", [-181, -182, -183, -184, -185, -186, -187, -188, -189, -190, -191, -192, -193, -194, -195, -196], [[[7, -179, [57, 58]], -180], 4, 1], [0, "92UH9GHK1APrdPdsKguX6n", 1], [5, 120, 120], [-119, -307, 0, 0, 0, 0, 1, 1, 1, 1]], [100, "game", 2, [[-197, -198, -199, -200, -201, -202, -203, -204, -205, [82, "layout-chip", -206, [0, "37+cx5QOdKCYHkTJ4fUQWW", 1], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], -207, -208, -209], 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1], [0, "62dYSf17NBeqG62ckIWwyx", 1]], [49, "user-pos", 11, [10, 4, 5, 6, 7, 8, 9, 3, -239], [[115, -238, [402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438], [-231, -232, -233, -234, -235, -236, -237], [-224, -225, -226, -227, -228, -229, -230], [-217, -218, -219, -220, -221, -222, -223], -216, -215, -214, -213, -212, -211, -210]], [0, "277e3u5dhOaYUlK2vxsPDN", 1], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "layout-bet", 11, [-251, -252], [[116, false, 1, 1, 10, 10, 20, -240, [5, 677.82, 80]], [119, -250, -249, -248, -247, -246, -245, -244, -243, -242, -241]], [0, "0cwCqqVZJENbeWKxEVLLo5", 1], [5, 677.82, 80], [154, -307, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "layoutRoom", [-254, -255, -256, -257, -258, -259, -260, -261, -262], [[117, false, 1, 3, 20, 20, 20, 15, 60, 20, -253, [5, 580, 483]]], [0, "83UG0IEVRKF4mxbTlUK7O7", 1], [5, 580, 483], [33, -63.2, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "3CRoom", 1, [-264, -265, 14, -266, -267, -268], [[120, -263, 603]], [0, "c4SZbLfhRFi6zIVt+mQl4r", 1]], [21, "<PERSON><PERSON><PERSON>", false, 2, [-269, -270, -271, -272, -273, -274, -275], [0, "eamQPsqbZAmJ+4IPVgkwj8", 1]], [2, "layout-totalBet", 11, [-277, -278, -279, -280, -281, -282, -283], [[68, false, 1, 1, -276, [5, 676, 218]]], [0, "45kbl0iRJCdq6uWBAwNWcg", 1], [5, 676, 218], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "chatView", 15, [-288, -289, -290, -291, -292], [[121, "bai", -287, -286, -285, -284]], [0, "b1opN9kp1LOYZrp+Sk8H9b", 1], [5, 358, 495], [-451, -58, 0, 0, 0, 0, 1, 1, 1, 1]], [83, "item", [-298, -299, -300, -301, -302, -303], [[122, -297, -296, -295, -294, -293]], [0, "daD4XCEVBLsYxTtFV0KUxn", 1], [4, 4278190080], [5, 240, 60], [-2, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bgSID", 11, [-305, -306, -307, -308, -309, -310], [[13, 1, 0, -304, [22], 23]], [0, "03uHVoDnFOYJkqN2MMdAiW", 1], [5, 170, 66], [-409.7, 324, 0, 0, 0, 0, 1, 1, 1, 1]], [84, "nodeBuyOwner", false, [-312, -313, -314, -315], [[53, false, 1, 2, 20, -311, [5, 300, 300]]], [0, "1dvKR14ltOs6tvPk3ndQtA", 1], [5, 300, 300]], [85, "item-horizontal", [-317, -318, -319, -320], [[69, 1, 1, -316, [5, 279.39, 33]]], [0, "adlH4DhW1GqIYnBABZM2q5", 1], [5, 279.39, 33], [0, 0, 0.5]], [18, "card_1_1", false, 3, [[11, 0, -321, 99], [7, -322, [100]], [14, -323]], [0, "50m85K4lNDdroJwOpovnRL", 1], [5, 81, 104], [10.6, -339.6, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, 15]], [18, "card_6_1", false, 3, [[11, 0, -324, 59], [7, -325, [60]], [14, -326]], [0, "ffxeFUl/FEcY1yYm6isoj5", 1], [5, 57, 80], [-430, -46, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, 15]], [5, "card_6_2", false, 3, [[11, 0, -327, 61], [7, -328, [62]], [14, -329]], [0, "28BEvOXJ1JibZEExZiDQrh", 1], [5, 57, 80], [-408.9, -43.4, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "card_6_3", false, 3, [[11, 0, -330, 63], [7, -331, [64]], [14, -332]], [0, "95k46bdB5LhIxNqV6jolhX", 1], [5, 57, 80], [-387.9, -47.4, 0, 0, 0, -0.17364817766693033, 0.984807753012208, 1, 1, 1], [1, 0, 0, -20]], [18, "card_4_1", false, 3, [[11, 0, -333, 65], [7, -334, [66]], [14, -335]], [0, "2fj9ZJWJ9P87mpypB3URq/", 1], [5, 57, 80], [327, 87, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, 15]], [5, "card_4_2", false, 3, [[11, 0, -336, 67], [7, -337, [68]], [14, -338]], [0, "8ahT8E4vRGPJs501B5h+Cu", 1], [5, 57, 80], [348, 91, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "card_4_3", false, 3, [[11, 0, -339, 69], [7, -340, [70]], [14, -341]], [0, "2fUksG9+hKVY1Hei7uEm14", 1], [5, 57, 80], [367, 85, 0, 0, 0, -0.17364817766693033, 0.984807753012208, 1, 1, 1], [1, 0, 0, -20]], [18, "card_3_1", false, 3, [[11, 0, -342, 71], [7, -343, [72]], [14, -344]], [0, "6bsgE2UhJDIbVI8U0tKxzl", 1], [5, 57, 80], [381, -61, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, 15]], [5, "card_3_2", false, 3, [[11, 0, -345, 73], [7, -346, [74]], [14, -347]], [0, "feOv2wlGpPRKTq9dMDbrAx", 1], [5, 57, 80], [403, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "card_3_3", false, 3, [[11, 0, -348, 75], [7, -349, [76]], [14, -350]], [0, "a1LkMmLxVIYogciwgqBbRa", 1], [5, 57, 80], [424, -63, 0, 0, 0, -0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, -15]], [18, "card_2_1", false, 3, [[11, 0, -351, 77], [7, -352, [78]], [14, -353]], [0, "a9aj97xeVAgZRynudvhVla", 1], [5, 57, 80], [321, -187, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, 15]], [5, "card_2_2", false, 3, [[11, 0, -354, 79], [7, -355, [80]], [14, -356]], [0, "e5Weu/8rpH4Yx7A49H07sn", 1], [5, 57, 80], [343, -183, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "card_2_3", false, 3, [[11, 0, -357, 81], [7, -358, [82]], [14, -359]], [0, "3bM0v47s5EGZWPNLWXv3MF", 1], [5, 57, 80], [368.6, -189.4, 0, 0, 0, -0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, -15]], [5, "card_1_2", false, 3, [[11, 0, -360, 83], [7, -361, [84]], [14, -362]], [0, "40A9tgyt5ELLF5JeJIJWFE", 1], [5, 76, 102], [42.7, -335, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "card_1_3", false, 3, [[11, 0, -363, 85], [7, -364, [86]], [14, -365]], [0, "ba2Rg4f95F+4L/ahBhs/Ig", 1], [5, 76, 102], [75, -341, 0, 0, 0, -0.11320321376790672, 0.9935718556765875, 1, 1, 1], [1, 0, 0, -13]], [18, "card_5_1", false, 3, [[11, 0, -366, 87], [7, -367, [88]], [14, -368]], [0, "79pkT6225OqoZrKGbKx2vq", 1], [5, 57, 80], [-365.7, -192.2, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, 15]], [5, "card_5_2", false, 3, [[11, 0, -369, 89], [7, -370, [90]], [14, -371]], [0, "d1/Zxrg61PLLOv77DBjBYi", 1], [5, 57, 80], [-344.2, -188.6, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "card_5_3", false, 3, [[11, 0, -372, 91], [7, -373, [92]], [14, -374]], [0, "92O2Bw+Z9EVYlJMNa9WA8i", 1], [5, 57, 80], [-321, -192.8, 0, 0, 0, -0.17364817766693033, 0.984807753012208, 1, 1, 1], [1, 0, 0, -20]], [18, "card_7_1", false, 3, [[11, 0, -375, 93], [7, -376, [94]], [14, -377]], [0, "cf/5ZQp25GArvfQ6ds3lN9", 1], [5, 57, 80], [-371, 102, 0, 0, 0, 0.13052619222005157, 0.9914448613738104, 1, 1, 1], [1, 0, 0, 15]], [5, "card_7_2", false, 3, [[11, 0, -378, 95], [7, -379, [96]], [14, -380]], [0, "0dLPxmv0ZIj6oNHuF4lz5D", 1], [5, 57, 80], [-349, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "card_7_3", false, 3, [[11, 0, -381, 97], [7, -382, [98]], [14, -383]], [0, "cen9uilnREdq7cQ6UGgbKZ", 1], [5, 57, 80], [-328, 102, 0, 0, 0, -0.17364817766693033, 0.984807753012208, 1, 1, 1], [1, 0, 0, -20]], [2, "bet_total", 12, [-385, -386, -387], [[13, 1, 0, -384, [400], 401]], [0, "5cSOPyKD1HVL1fqdta2s7p", 1], [5, 234, 40], [0, 45, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "sliderBetValue", [-389, -390, -391, -392], [-388], [0, "56n1FrErhKr5Vr435qIKPw", 1], [5, 60, 350], [387, 246.2, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "groupBien", false, 13, [-393, -394, -395, -396], [0, "f2x5hSTnpOT4OL9UWFfnNw", 1]], [86, "popup", false, 0, 2, [-398, -399, -400], [[7, -397, [496, 497]]], [0, "e4lAruwpJIVqhZE9Ct7Y3S", 1]], [2, "1k", 14, [-404, -405], [[3, -401, [519], 520], [22, 3, -403, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "1000", 2]], [4, **********], -402, 521]], [0, "dcCVmroKFAIbkwPNwSs/zh", 1], [5, 136, 136], [-202, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "2k", 200, 14, [-409, -410], [[3, -406, [526], 527], [22, 3, -408, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "2000", 2]], [4, **********], -407, 528]], [0, "fbS4TkPJxGCph49lt0CXEY", 1], [5, 136, 136], [-6, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "5k", 14, [-414, -415], [[3, -411, [533], 534], [22, 3, -413, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "5000", 2]], [4, **********], -412, 535]], [0, "21doN6kDBKi5+nwRZ5uhS1", 1], [5, 136, 136], [190, 153.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "10k", 14, [-419, -420], [[3, -416, [540], 541], [22, 3, -418, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "10000", 2]], [4, **********], -417, 542]], [0, "59Ovn2hAdDzac3sq/Q5qaX", 1], [5, 136, 136], [-202, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "20K", 200, 14, [-424, -425], [[3, -421, [547], 548], [22, 3, -423, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "20000", 2]], [4, **********], -422, 549]], [0, "55HBZJtsZAgq/mnbbfE3MU", 1], [5, 136, 136], [-6, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "50K", 200, 14, [-429, -430], [[3, -426, [554], 555], [22, 3, -428, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "50000", 2]], [4, **********], -427, 556]], [0, "ff6LVQlxJO87mUU/aN+yfT", 1], [5, 136, 136], [190, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "100K", 200, 14, [-434, -435], [[3, -431, [561], 562], [22, 3, -433, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "100000", 2]], [4, **********], -432, 563]], [0, "3dLqao96tBYao1tjjmeF4S", 1], [5, 136, 136], [-202, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "200k", 200, 14, [-439, -440], [[3, -436, [568], 569], [22, 3, -438, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "200000", 2]], [4, **********], -437, 570]], [0, "f2XWrZ8/ZOOa6ylwww18Y6", 1], [5, 136, 136], [-6, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "500K", 200, 14, [-444, -445], [[3, -441, [575], 576], [22, 3, -443, [[23, "847ffXbIbxBvLyl+Mj81BuJ", "setBetRoom", "500000", 2]], [4, **********], -442, 577]], [0, "b1u/Q2mdtDdaj9RJa3ljfB", 1], [5, 136, 136], [190, -158.5, 0, 0, 0, 0, 1, 1, 1, 1]], [87, "item", [-451, 22], [[118, 1, 2, 5, -446, [5, -5, 33]], [130, -450, 22, -449, -448, -447]], [0, "22jIGQPfFOjbr9Jvs9V9+6", 1], [5, -5, 33], [0, 0, 0.5], [-170, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [58, "audioPool", 2, [-456, -457, -458], [[131, -455, -454, -453, -452]], [0, "a5axxQbxNJ/ZYUeIS5Am8x", 1]], [2, "bet_acc1", 17, [-460, -461], [[13, 1, 0, -459, [141], 142]], [0, "afirFXo/VC6JRvfMaAAu7U", 1], [5, 150, 40], [46, -225, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bet_acc2", 17, [-463, -464], [[13, 1, 0, -462, [105], 106]], [0, "6dW3nawMVOg5ZDteI+MuDt", 1], [5, 150, 40], [347.9, -225.6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bet_acc3", 17, [-466, -467], [[13, 1, 0, -465, [111], 112]], [0, "0eVLHPD3ZNoKo8Ihfi3Ax5", 1], [5, 150, 40], [392.6, -90.1, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bet_acc4", 17, [-469, -470], [[13, 1, 0, -468, [117], 118]], [0, "42+fUfEGNNza7Z74h0UIhj", 1], [5, 150, 40], [350, 56.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bet_acc5", 17, [-472, -473], [[13, 1, 0, -471, [123], 124]], [0, "b5JwgbznVAk5yeNIiGP9ZQ", 1], [5, 150, 40], [-339.7, -227.6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bet_acc6", 17, [-475, -476], [[13, 1, 0, -474, [129], 130]], [0, "8cWqYe9aBIUZRYOUbIZfEL", 1], [5, 150, 40], [-408.3, -88.3, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bet_acc7", 17, [-478, -479], [[13, 1, 0, -477, [135], 136]], [0, "03trE7J6dOQpPJ2vXu2Aaw", 1], [5, 150, 40], [-351.9, 59.1, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien1", 4, [-483], [[13, 1, 0, -480, [166], 167], [12, 3, -482, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide1", 4]], [4, **********], -481]], [0, "88qx/1SX1Asp8LyHNrXVbD", 1], [5, 80, 80], [-192, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien2", 4, [-487], [[13, 1, 0, -484, [170], 171], [12, 3, -486, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide2", 4]], [4, **********], -485]], [0, "d0tUW8hhNPGbGJWPq8vEsu", 1], [5, 80, 80], [-92, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien1", 5, [-491], [[13, 1, 0, -488, [207], 208], [126, -490, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide1", 5]], [4, **********], -489]], [0, "e3kPQW2ZdOGZpw4U8ju8fD", 1], [5, 80, 80], [-205, 33, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien2", 5, [-495], [[13, 1, 0, -492, [211], 212], [12, 3, -494, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide2", 5]], [4, **********], -493]], [0, "8fguhmBMpMkZYpROVp0fwx", 1], [5, 80, 80], [-104.2, 26.4, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien1", 6, [-499], [[13, 1, 0, -496, [249], 250], [12, 3, -498, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide1", 6]], [4, **********], -497]], [0, "bccGO+1rVCeZFFF9wADTxX", 1], [5, 80, 80], [-223, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien2", 6, [-503], [[13, 1, 0, -500, [253], 254], [12, 3, -502, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide2", 6]], [4, **********], -501]], [0, "12fRgcendPqoQ5a2Yd6SBE", 1], [5, 80, 80], [-123.8, -5.6, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien1", 7, [-507], [[13, 1, 0, -504, [291], 292], [12, 3, -506, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide1", 7]], [4, **********], -505]], [0, "e58bz0kGNA/5CCcJbbpijT", 1], [5, 80, 80], [108, -3, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien2", 7, [-511], [[13, 1, 0, -508, [295], 296], [12, 3, -510, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide2", 7]], [4, **********], -509]], [0, "b3MYDZZk1CrKHC8ODHJI/G", 1], [5, 80, 80], [207, -3, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien1", 8, [-515], [[13, 1, 0, -512, [333], 334], [12, 3, -514, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide1", 8]], [4, **********], -513]], [0, "9aHvMaAxFNWYxfyGGDxz9N", 1], [5, 80, 80], [109, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien2", 8, [-519], [[13, 1, 0, -516, [337], 338], [12, 3, -518, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide2", 8]], [4, **********], -517]], [0, "73a47ZU6hCko95A2N1mY2Y", 1], [5, 80, 80], [205, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien1", 9, [-523], [[13, 1, 0, -520, [375], 376], [12, 3, -522, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide1", 9]], [4, **********], -521]], [0, "474BmXIGVH+K4Ch6qpp8wX", 1], [5, 80, 80], [91, 72, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien2", 9, [-527], [[13, 1, 0, -524, [379], 380], [12, 3, -526, [[8, "6e37d/myClEaKavwWnoFb9e", "onBetSide2", 9]], [4, **********], -525]], [0, "1fLaUt1XRF44waq+1Vb4LA", 1], [5, 80, 80], [189, 72, 0, 0, 0, 0, 1, 1, 1, 1]], [103, "<PERSON><PERSON>", 45, [-530], [[[3, -528, [458], 459], -529], 4, 1], [0, "517xESv15FxK4Ur8RhZySv", 1], [5, 86, 54], [-1.1368683772161603e-13, -175.00000000000003, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "btnCuoc", [-533], [[12, 3, -532, [[8, "56bd4y7a3hNp5RXp+tVhS56", "onBet", 13]], [4, **********], -531]], [0, "ffO9JPvYlIq6ZrphKQruW2", 1], [5, 101.89, 40], [389.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnBien1", 46, [-536], [[12, 3, -535, [[8, "56bd4y7a3hNp5RXp+tVhS56", "onBetSide1", 13]], [4, **********], -534]], [0, "82VTSMiQhBhbCLYwBeq76d", 1], [5, 101.89, 40], [53, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnBien2", 46, [-539], [[12, 3, -538, [[8, "56bd4y7a3hNp5RXp+tVhS56", "onBetSide2", 13]], [4, **********], -537]], [0, "ddk9W9/IRL3pX3DV9od4p7", 1], [5, 101.89, 40], [222, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnGopGa", 46, [-542], [[12, 3, -541, [[8, "56bd4y7a3hNp5RXp+tVhS56", "onFeedChicken", 13]], [4, **********], -540]], [0, "8bbUZv2sJPMILfDxRySja1", 1], [5, 101.89, 40], [389.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnNhanBien", 46, [-545], [[12, 3, -544, [[8, "56bd4y7a3hNp5RXp+tVhS56", "onActiveRecievBetSide", 13]], [4, **********], -543]], [0, "77L1BOLZhPZZoe7Y8V7t0H", 1], [5, 101.89, 40], [-494.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [88, "popupWin", false, 2, [-547, -548], [[7, -546, [476, 477]]], [0, "424OeYW0FDUrKEnHS9wRRv", 1], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "bgPopup", 47, [21, -550, -551], [[10, -549, 494]], [0, "faBA/qHkFKE7VqhRfiVERJ", 1], [5, 735, 447]], [2, "lbAsk", 21, [-553, -554, -555], [[72, false, "<color=#05A71F>[TQ] </c><color=#FCFF00>alita </c>mu<PERSON><PERSON> b<PERSON>\n", 30, -552, 482]], [0, "84T1qqW/FPSYWf0/48A8GU", 1], [5, 374.34000000000003, 80], [0, 79, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "nodeSellOwner", false, 85, [-557, -558], [[53, false, 1, 2, 20, -556, [5, 300, 80]]], [0, "6ebLO1qBJOc7LYBJRQ58/n", 1], [5, 300, 80], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [89, "lbResult", [-560, -561, -562], [[72, false, "<color=#05A71F>[TQ] </c><color=#FCFF00>alita </c>mu<PERSON><PERSON> b<PERSON>\n", 30, -559, 493]], [0, "97DMVGYXxDn4ow9vAqsim/", 1], [5, 374.34000000000003, 80]], [90, "offset-message", false, 0, [-564, -565], [[123, false, 768, 2, false, -563]], [0, "71DzE4X9JL3bWXD0DC1eMc", 1], [5, 451, 80]], [63, "scrollview", 18, [-568, -569], [-566, -567], [0, "55JkrMEfhCM66AvhJwTo71", 1], [5, 360, 270], [-7, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [62, "editbox-chat", [-571, -572, -573], [-570], [0, "d7fHM/sghLtaBIIAojleIZ", 1], [5, 235, 30], [-43, 4.5, 0, 0, 0, 0, 1, 1, 1, 1]], [104, "scrollviewRanks", [19, -576], [-574, -575], [0, "d77M/t1u1EKJNjHmHu9NLu", 1], [5, 240, 350], [0, 0.5, 1], [0, 166, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 11, [-579], [[55, 0.9, 3, -578, [[8, "847ffXbIbxBvLyl+Mj81BuJ", "backClicked", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -577]], [0, "40P3Z39uhB/biCGKJRlGtT", 1], [5, 100, 96], [-589, 312, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnSound", 11, [-582], [[55, 0.9, 3, -581, [[8, "847ffXbIbxBvLyl+Mj81BuJ", "soundClicked", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -580]], [0, "b8KGqS2glKy5eC3J7ARtPJ", 1], [5, 80, 70], [588, 306, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 10, [-584, -585], [[3, -583, [33], 34]], [0, "560FzT/wlL+7jqK/vlqzGg", 1], [5, 121, 51], [-4.1, -84.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 4, [-587, -588], [[3, -586, [152], 153]], [0, "55g9wMWABBLJWGfeyKjb4F", 1], [5, 121, 51], [0, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 5, [-590, -591], [[3, -589, [193], 194]], [0, "f9JCxbPcRP2rvBy9xKNOpt", 1], [5, 121, 51], [0, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 6, [-593, -594], [[3, -592, [235], 236]], [0, "8ali0pPB5OgpY7M7s6sUpO", 1], [5, 121, 51], [0, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 7, [-596, -597], [[3, -595, [277], 278]], [0, "ffZ0MnOP9F95A+rrV+X16d", 1], [5, 121, 51], [0, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 8, [-599, -600], [[3, -598, [319], 320]], [0, "44zBbOBTlG+Y084xoCci58", 1], [5, 121, 51], [0, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_money", 9, [-602, -603], [[3, -601, [361], 362]], [0, "a8QxbOlEhHKoTeYaAhYdEC", 1], [5, 121, 51], [0, -69, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "back", 16, [[-604, [46, null, -605], [134, -606]], 1, 4, 4], [0, "7e2rYHlhVE4aPi4ybK9qQ3", 1], [5, 76, 102], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [59, "bg", 200, 16, [[20, 1, 0, -607, 439], [45, -608], [47, -609]], [0, "aeTE9X6nhKKLysRFF4dz9E", 1], [5, 3000, 3000]], [1, "btnOpenCard", 16, [[10, -610, 441], [12, 3, -612, [[8, "270b2V+1hxGr7jm4iAk2tii", "onOpenCard", 2]], [4, **********], -611]], [0, "69wDyFQLJPzY2qZktEMWA5", 1], [5, 150, 63], [545, -309, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "bgLeaveRoom", false, 11, [-614], [[20, 1, 0, -613, 443]], [0, "52K385v+BMMbfmnaGJ/ufL", 1], [5, 200, 40], [-538.7, 267, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "bgInfo", false, 11, [-617], [[20, 1, 0, -615, 445], [37, -616, [447], 446]], [0, "eco5zkn7ZOgoMhos5koWEE", 1], [5, 200, 40], [-158, 316, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnChat", 11, [-620], [[55, 0.9, 3, -619, [[8, "847ffXbIbxBvLyl+Mj81BuJ", "chatClicked", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -618]], [0, "90F7TEYHVHWpin/tBYNVBp", 1], [5, 80, 70], [-588, -315, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "groupCuoc", 13, [45, 79], [0, "6fG8VtNgBAFrwkfuI88rl2", 1]], [35, "progressBar", 45, [-624], [[54, 1, 0, -621, [454]], [135, 400, 1, 0, -623, -622]], [0, "5dV3S9CaNOPr/DSeeLXg5L", 1], [5, 60, 400]], [61, "nodeStatus", 2, [-625, -626, -627], [0, "7bm0anEstIUIb0JWuyIoFF", 1], [0, 21, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "bg", 200, 47, [[20, 1, 0, -628, 478], [45, -629], [47, -630]], [0, "aao+qctsROE6+j6kCWnupZ", 1], [5, 100000, 100000]], [2, "nodeButton", 21, [-632, -633], [[70, false, 1, 1, 200, -631, [5, 500, 200]]], [0, "997pt3j1lDVJBY3EewJ3Te", 1], [5, 500, 200], [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnOk", 112, [[10, -634, 484], [12, 3, -636, [[8, "8b1d6iQkZFPBawrgTt4ojie", "onBuyOwnerClick", 2]], [4, **********], -635]], [0, "2ety3pVPtAVrQjcjBILsDI", 1], [5, 150, 63], [-175, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCancel", 112, [[10, -637, 485], [12, 3, -639, [[8, "8b1d6iQkZFPBawrgTt4ojie", "onClosePopup", 2]], [4, **********], -638]], [0, "c9Se5rj6lAnLuwKfl5NHBE", 1], [5, 150, 63], [175, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeButton", 87, [-641, -642], [[70, false, 1, 1, 200, -640, [5, 500, 200]]], [0, "f6VW2BFhVL4oHb3WnAXTtl", 1], [5, 500, 200], [0, -137, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnOk", 115, [[10, -643, 488], [12, 3, -645, [[8, "8b1d6iQkZFPBawrgTt4ojie", "onSellOwnerClick", 2]], [4, **********], -644]], [0, "39Mc0lCUtBar13aLtWubSE", 1], [5, 150, 63], [-175, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnCancel", 115, [[10, -646, 489], [12, 3, -648, [[8, "8b1d6iQkZFPBawrgTt4ojie", "onCancelSellOwnerClick", 2]], [4, **********], -647]], [0, "d19k+E7RdHv79I0GYZTvg5", 1], [5, 150, 63], [175, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [91, "nodeBuyResult", false, 85, [88], [[53, false, 1, 2, 20, -649, [5, 300, 80]]], [0, "84e/r5C0BHrZtX2bzbtw3w", 1], [5, 300, 80]], [1, "btnClose", 47, [[10, -650, 495], [12, 3, -652, [[8, "8b1d6iQkZFPBawrgTt4ojie", "onClosePopup", 2]], [4, **********], -651]], [0, "04JxlDtJRFUJ00gcG9FHw+", 1], [5, 74, 74], [340, 204, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "popupSlotsView", 2, [89], [[7, -653, [498]], [136, 0, -655, 89, -654]], [0, "9cjNAMYo9GDr73TPlxfg9I", 1], [5, 444, 220]], [28, "scrollview", false, 18, [-659], [[73, false, 0.75, 0.23, null, null, -657, -656], [45, -658]], [0, "15cl+8CsFDH78mZL1iqOMt", 1], [5, 360, 270], [0, 18, 0, 0, 0, 0, 1, 1, 1, 1]], [63, "btnSend", 18, [-661], [-660], [0, "70Os/IdBJJaLHP77PW+7eR", 1], [5, 80, 70], [108, -200.7, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 15, [[9, 2, -662, [601], 602], [12, 3, -664, [[8, "59d80V9xtxJYrNAwGrKLCoS", "createHelpView", 15]], [4, **********], -663]], [0, "68We4xIn9FOrXn2Z1b5SyC", 1], [5, 51, 51], [326, 199, 0, 0, 0, 0, 1, 1, 1, 1]], [60, "bg", 15, [-665, -666], [0, "321xg/eahDUJolGrc6//6e", 1]], [1, "bg", 124, [[9, 0, -667, [0], 1], [47, -668]], [0, "21VO45joxGXYRAWZb5f4wy", 1], [5, 1561, 723], [0, 5.684341886080802e-14, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "icoBack@2x", 124, [[3, -669, [2], 3], [127, -670, [[8, "847ffXbIbxBvLyl+Mj81BuJ", "<PERSON><PERSON><PERSON><PERSON>", 2]]]], [0, "f27pHRxAVEBrlsBR9geWxd", 1], [5, 36, 53], [-600.355, 235.964, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg", 11, [[9, 0, -671, [4], 5], [47, -672]], [0, "a1Zp4Ct1VL97p65xTE1ffI", 1], [5, 1560, 723]], [2, "table", 11, [-674], [[3, -673, [8], 9]], [0, "e5OvWuGmlHyaHYRvtDIfEB", 1], [5, 1118, 490], [0, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "table", 11, [-676], [[124, false, -675, [10]]], [0, "13J2uhlZxCAI6APC7faBJc", 1], [5, 967, 573], [0, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Avatar", 10, [[25, 1, 0, false, -677, [24], 25], [26, -678]], [0, "34rcmtjRhEPrN5JmzwJSjk", 1], [4, 4293322470], [5, 120, 120]], [29, "timeprogress", 10, [[-679, [32, 2, -681, -680]], 1, 4], [0, "6fOSZrtmFM67qqEHBCJ4Mw", 1], [5, 100, 100]], [1, "lbChip", 95, [[6, "10.M", 18, false, 1, 1, -682, [31], 32], [27, -683]], [0, "e8kDvLbrBIzLcIWBHRkX+k", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "nodeBien", false, 10, [-685], [[11, 0, -684, 40]], [0, "70O6gmmCVGFoudAV+vJZbQ", 1], [5, 35, 35], [-56, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "nodeBien1", false, 10, [-687], [[11, 0, -686, 42]], [0, "47S0lXh7dNvbIQejXtUP/B", 1], [5, 80, 80], [59.1, 126, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "nodeBien2", false, 10, [-689], [[11, 0, -688, 44]], [0, "d3MyuMgTpIwKmuhxECkUEd", 1], [5, 80, 80], [139, 126, 0, 0, 0, 0, 1, 1, 1, 1]], [50, "lbWin", false, 10, [[15, "+100.000", 20, false, 1, 1, -690, 51], [67, true, -691, [53], 52]], [0, "36A4K3LrxEjbSqU6xMVrCb", 1], [5, 153.13, 25], [0, 0.5, 0.500000000000002], [0, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 10, [-692, -693], [0, "014BC/nA1O67hA9DM+VobO", 1]], [2, "bubble", 137, [-695], [[20, 1, 0, -694, 56]], [0, "16iOuUyM5DbZRg2mM1Jzyg", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Avatar", 4, [[25, 1, 0, false, -696, [143], 144], [26, -697]], [0, "c3g/qsCmRLS5bwXDLb/YjL", 1], [4, 4293322470], [5, 86, 86]], [29, "timeprogress", 4, [[-698, [32, 2, -700, -699]], 1, 4], [0, "ff/csYcQ5IiqDlEaQPybvz", 1], [5, 70, 70]], [1, "lbChip", 96, [[6, "10.M", 18, false, 1, 1, -701, [150], 151], [27, -702]], [0, "2eFDPa2FlM2oeSqH6hD0Jt", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien", 4, [-704], [[9, 0, -703, [160], 161]], [0, "d7L70/JDZNN5UbWBnsCi6K", 1], [5, 40, 40], [-41, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", false, 4, [[15, "+100.000", 20, false, 1, 1, -705, 176], [67, true, -706, [178], 177]], [0, "7dRKxrkOhH/K4NtR8Ha9F4", 1], [5, 153.13, 25], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 4, [-707, -708], [0, "7a0fwzr3RI37yKN/Z2v0K4", 1]], [2, "bubble", 144, [-710], [[20, 1, 0, -709, 181]], [0, "ea3z3/I2tLO6Mq+XhLjA6T", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Avatar", 5, [[25, 1, 0, false, -711, [184], 185], [26, -712]], [0, "f0UI+6sa5Aa5zd0r2US61f", 1], [4, 4293322470], [5, 86, 86]], [29, "timeprogress", 5, [[-713, [32, 2, -715, -714]], 1, 4], [0, "aaKe4bK7BLYaSfmRkYnxOm", 1], [5, 70, 70]], [1, "lbChip", 97, [[6, "10.M", 18, false, 1, 1, -716, [191], 192], [27, -717]], [0, "d7WffCbwxK6KD5zw8Par8i", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien", 5, [-719], [[9, 0, -718, [201], 202]], [0, "ddU9rOmq5Mx5NdjqvH86c0", 1], [5, 40, 40], [-41, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", false, 5, [[15, "+100K", 20, false, 1, 1, -720, 217], [37, -721, [219, 220], 218]], [0, "5577R5uXlBhKo7s8e6dOEf", 1], [5, 100.63, 25], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 5, [-722, -723], [0, "12ZxRBTztOurg2e+ph8l9n", 1]], [2, "bubble", 151, [-725], [[20, 1, 0, -724, 223]], [0, "07O0FyrtlIApYBw9bw4oDg", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Avatar", 6, [[25, 1, 0, false, -726, [226], 227], [26, -727]], [0, "ff5TDymF1EqpXiCEjVo4SJ", 1], [4, 4293322470], [5, 86, 86]], [29, "timeprogress", 6, [[-728, [32, 2, -730, -729]], 1, 4], [0, "16To+3985B+JsC1a7+wlwH", 1], [5, 70, 70]], [1, "lbChip", 98, [[6, "10.M", 18, false, 1, 1, -731, [233], 234], [27, -732]], [0, "590VqK/zhJrYFS5HEZDc1H", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien", 6, [-734], [[9, 0, -733, [243], 244]], [0, "f442gRbrpClLppG6xDtw5e", 1], [5, 40, 40], [-41, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", false, 6, [[15, "+100K", 20, false, 1, 1, -735, 259], [37, -736, [261, 262], 260]], [0, "a3yp2OM0lCHZWq54Qd4Tbw", 1], [5, 100.63, 25], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 6, [-737, -738], [0, "7ac2M3gshPPo2bg165uA77", 1]], [2, "bubble", 158, [-740], [[20, 1, 0, -739, 265]], [0, "bbEFJ7qApCJL3UoizrhBcD", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Avatar", 7, [[25, 1, 0, false, -741, [268], 269], [26, -742]], [0, "8asxcrOFpGrr7Km4AAl8Nc", 1], [4, 4293322470], [5, 86, 86]], [29, "timeprogress", 7, [[-743, [32, 2, -745, -744]], 1, 4], [0, "555jik7wFHAby9i6nGyxWx", 1], [5, 70, 70]], [1, "lbChip", 99, [[6, "10.M", 18, false, 1, 1, -746, [275], 276], [27, -747]], [0, "6eFBECVJdIt7HkuPoaYklp", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien", 7, [-749], [[9, 0, -748, [285], 286]], [0, "f48+LYmQpEXJIj+4cVotLF", 1], [5, 40, 40], [-41, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", false, 7, [[15, "+100K", 20, false, 1, 1, -750, 301], [37, -751, [303, 304], 302]], [0, "1cbQjSCJJEGrcTuME2Z6qM", 1], [5, 80.63, 37.5], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 7, [-752, -753], [0, "ebm/ac+6NDNLklZNn5BBdZ", 1]], [2, "bubble", 165, [-755], [[20, 1, 0, -754, 307]], [0, "e9/29FFElGUp5PL/mKHcOq", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Avatar", 8, [[25, 1, 0, false, -756, [310], 311], [26, -757]], [0, "b6vqSFjUZHirFgBWKcxZ3s", 1], [4, 4293322470], [5, 86, 86]], [29, "timeprogress", 8, [[-758, [32, 2, -760, -759]], 1, 4], [0, "66iXce6q5EU7j8dyBgoH4a", 1], [5, 70, 70]], [1, "lbChip", 100, [[6, "10.M", 18, false, 1, 1, -761, [317], 318], [27, -762]], [0, "02q89fIopK3aK3HbT0LNNh", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien", 8, [-764], [[9, 0, -763, [327], 328]], [0, "ca4b4yUd1IpLtIE09B5XDO", 1], [5, 40, 40], [-41, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", false, 8, [[15, "+100K", 20, false, 1, 1, -765, 343], [37, -766, [345, 346], 344]], [0, "ebE6hncupLpKlCih/zhynt", 1], [5, 80.63, 37.5], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 8, [-767, -768], [0, "1fEZJktoFFZ7je28M/VRWP", 1]], [2, "bubble", 172, [-770], [[20, 1, 0, -769, 349]], [0, "31J8qEi0FKp6heWuUjDUmU", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "Avatar", 9, [[25, 1, 0, false, -771, [352], 353], [26, -772]], [0, "4drEaYLjRPFotdOSeSkmqj", 1], [4, 4293322470], [5, 86, 86]], [29, "timeprogress", 9, [[-773, [32, 2, -775, -774]], 1, 4], [0, "75VBARJfhNIqbbFb1GxYVg", 1], [5, 70, 70]], [1, "lbChip", 101, [[6, "10.M", 18, false, 1, 1, -776, [359], 360], [27, -777]], [0, "b3B7hFpKtG65Kd22CmGzwS", 1], [5, 41.85, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "nodeBien", 9, [-779], [[9, 0, -778, [369], 370]], [0, "f5fTRk3BVOA6F65MudrCaw", 1], [5, 40, 40], [-41, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbWin", false, 9, [[15, "100k", 20, false, 1, 1, -780, 385], [37, -781, [387, 388], 386]], [0, "f05VQkuNROmaYMzhZ2PmAh", 1], [5, 57.5, 25], [0, 60, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 9, [-782, -783], [0, "7bKTk0941OarfPabY/Nl8G", 1]], [2, "bubble", 179, [-785], [[20, 1, 0, -784, 391]], [0, "b6IkSboYhAupQD9/rAp1/n", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [101, "lbTotalFeedChicken", 44, [[[138, "0", 16, false, 1, 1, 1, 2, -786, [396], 397], -787], 4, 1], [0, "e7EXqRQiNNfovO69h6k+/O", 1], [4, 4278249468], [5, 146.5, 20], [2, 7.8, 0, 0, 0, 0, 1, 1, 1, 1]], [44, 0, 102], [2, "lbNan", 16, [-789], [[139, "Nặn Bài", false, -788, 440]], [0, "34WcsonkBOBrQcxxgWcZir", 1], [5, 143, 40], [0, 181, 0, 0, 0, 0, 1, 1, 1, 1]], [140, "5s", false, 1, 1], [43, "fatThirdCard", 16, [[-790, [46, null, -791]], 1, 4], [0, "00bZcOOmBC77K9KtF3UoCP", 1], [5, 76, 102], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [44, 0, 185], [43, "fatSecondCard", 16, [[-792, [46, null, -793]], 1, 4], [0, "4b6QVLmQRLP6nHaiqy9hpb", 1], [5, 76, 102], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [44, 0, 187], [43, "fatFirstCard", 16, [[-794, [46, null, -795]], 1, 4], [0, "885J9qhL5N/qLNjSvVz976", 1], [5, 76, 102], [0, 0, 0, 0, 0, 0, 1, 2, 2, 1]], [44, 0, 189], [2, "slice_money", 78, [-797], [[3, -796, [456], 457]], [0, "01Xc3wfgRCzZIDM2EX6bgj", 1], [5, 101, 51], [-74, 33, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "btn_bien", 80, [-799], [[10, -798, 463]], [0, "0fNpy9t81D5JoKiMlAAV2J", 1], [5, 150, 63]], [35, "btn_bien", 81, [-801], [[10, -800, 465]], [0, "f2M7Jgh8hCKoTI51Vj5Maa", 1], [5, 150, 63]], [43, "lbStatus", 110, [[-802, [7, -803, [472]]], 1, 4], [0, "e4f/b6eJxKF74oYpKyvIel", 1], [5, 178.2, 40], [-2, 34.1, 0, 0, 0, 0, 1, 1, 1, 1]], [102, "lbPlayerStatus", false, 110, [[-804, [7, -805, [473]]], 1, 4], [0, "fbEdg4YehDFo4U0lS4IFk5", 1], [5, 0, 18], [-2, -115, 0, 0, 0, 0, 1, 1, 1, 1]], [92, "mask", 150, 84, [[11, 0, -806, 474], [45, -807]], [0, "857MO1+4ZGR4D8MFiQ4dKf", 1], [5, 300000, 300000], [0, 62, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "gameName", 15, [-809], [[9, 0, -808, [513], 514]], [0, "eaOu5t2FNOOJBkLX2t4ei7", 1], [5, 403, 40], [39, 198, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_popup_chat", 18, [[25, 1, 0, false, -810, [578], 579], [45, -811]], [0, "53RGNCEsJMDZw6BWwfIZKJ", 1], [5, 358, 495], [-9.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "view", 90, [-813], [[74, 0, -812, [584]]], [0, "8aXpf9XZtGtZJVKnLJUB4i", 1], [5, 340, 380], [-6, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "content", 199, [[69, 1, 2, -814, [5, 340, 0]]], [0, "63gDPdc0VAcJidsPwY4HSv", 1], [5, 340, 0], [0, 0.5, 1], [0, 131, 0, 0, 0, 0, 1, 1, 1, 1]], [93, "view", 121, [-816], [[152, 0, -815]], [0, "8eSdBKrndAEIEIMhWUDhzs", 1], [5, 360, 270], [0, 0.5, 1], [0, 134, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "rtChat", 201, [[132, false, "", 22, 345, 33, false, -817, 585]], [0, "43CcqIY/pA5p2a5jSwWQs1", 1], [5, 345, 33], [0, 0, 1], [-173, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "textbox_chat", 18, [91], [[13, 1, 0, -818, [588], 589]], [0, "d6OKRIqyhOSYKb3c+Tl6Da", 1], [5, 340, 60], [-12, -200.7, 0, 0, 0, 0, 1, 1, 1, 1]], [49, "nodeRanks", 15, [-821], [[153, -820, -819]], [0, "ebCRuCUYdDXLN0PBArjLkB", 1], [490, -52, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "bg_bxh", 204, [92], [[3, -822, [599], 600]], [0, "0bhFtTvidEjZ/90UKcgM8+", 1], [5, 287, 459]], [5, "avatar", false, 19, [[20, 1, 0, -823, 593], [26, -824]], [0, "356t7zDVVK84XH35BrfvqL", 1], [5, 37, 37], [-44.2, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [105, "lbMoney", 19, [-826], [-825], [0, "afSRbLSCNJuaVRBXaup+uF", 1], [4, 4278255612], [5, 104.5, 40], [0, 0, 0.5], [-85.8, -8.5, 0, 0, 0, 0, 1, 1, 1, 1]], [94, "view", 92, [-828], [[74, 0, -827, [598]]], [0, "67d/kTsF1B9oR6CNbZ4yAi", 1], [5, 240, 350], [0, 0.5, 1]], [51, "musicBackground", 58, [-829], [0, "6c8rDh+fREn62ea/uOQm4x", 1]], [154, 0.2, true, true, 209], [51, "chipBet", 58, [-830], [0, "d9UDKtVVZMc6KhIUQSDYhN", 1]], [75, true, 211], [51, "moveCard", 58, [-831], [0, "37VcvejzFHNJ8nU78BvBs6", 1]], [75, true, 213], [1, "nameBC", 128, [[3, -832, [6], 7]], [0, "15lFP90CZATaqTAc4uY3rs", 1], [5, 301, 64], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-input", 129, [[68, false, 1, 1, -833, [5, 676, 218]]], [0, "9eGxo1W9tJfanG6F9Rnz8I", 1], [5, 676, 218], [0, 98, 0, 0, 0, 0, 1, 1, 1, 1]], [64, "bgBack", 93, [-834], [0, "61SHUzy65H+o53uXGBttb9", 1], [4, 4293322470], [5, 36, 53], [0, 11.6, 0, 0, 0, 0, 1, 1, 1, 1]], [71, 2, false, 217, [11]], [106, "bgSound", 94, [-835], [0, "48pb+eWuhP/62pwCkIsUnT", 1], [4, 4293322470], [5, 81, 82]], [71, 2, false, 219, [12]], [42, "lbTable", 20, [[6, "Bàn", 17, false, 1, 1, -836, [13], 14]], [0, "b5HZTuYZ9M1oMCB8mPOHPE", 1], [5, 31.02, 40], [0, 0, 0.5], [-64.9, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "lbTableValue", 20, [-837], [0, "1e2h3E28VEfa9s+73AP2dd", 1], [5, 39.1, 40], [0, 0, 0.5], [-9, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [38, ": 012", 17, false, 1, 1, 222, [15]], [42, "lbRoomID", 20, [[6, "Phòng", 17, false, 1, 1, -838, [16], 17]], [0, "cb9b38syVB5J5Vc4iME/aC", 1], [5, 50.58, 40], [0, 0, 0.5], [-64.2, 2.9, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "lbRoomIDValue", 20, [-839], [0, "614+Sf+JJAfJL26ad4tgt8", 1], [5, 53.98, 40], [0, 0, 0.5], [-9.1, 3.3, 0, 0, 0, 0, 1, 1, 1, 1]], [38, ": 1.000", 17, false, 1, 1, 225, [18]], [42, "lbSID", 20, [[6, "<PERSON><PERSON><PERSON>", 17, false, 1, 1, -840, [19], 20]], [0, "1b2nFaxpVEsIIyAuipeMDr", 1], [5, 45.05, 40], [0, 0, 0.5], [-64.8, -19, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "lbSIDValue", 20, [-841], [0, "2dWZDTa+JKm6ATt85t7BH8", 1], [5, 78.62, 40], [0, 0, 0.5], [-8.7, -17.8, 0, 0, 0, 0, 1, 1, 1, 1]], [38, ": #123456", 17, false, 1, 1, 228, [21]], [31, 3, 0, 2, 0.5, 1, 131, [26], [0, 0.5, 0.5]], [4, "ava_sheld", 10, [[9, 0, -842, [27], 28]], [0, "38QO5R0UtDT6BXoUsZM8nP", 1], [5, 120, 120]], [16, "lbName", 95, [[6, "Academy...", 18, false, 1, 1, -843, [29], 30]], [0, "80ty1SPLJBkYqDAXTiIh1I", 1], [4, 4281915391], [5, 89.8, 40], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeChuong", 10, [[3, -844, [35], 36]], [0, "17HAdTT91DW5uUl+mtVJml", 1], [5, 33, 33], [-52, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeGa", 10, [[3, -845, [37], 38]], [0, "ec1h5uIhNKR5pkUIjOMWkI", 1], [5, 33, 33], [4, 53, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 133, [[56, "500K", 15, false, 1, 1, 2, -846, 39]], [0, "ff6dwXNutNJ5Zb7RK+1bA0", 1], [5, 36.75, 15], [-0.8, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBien", 134, [[56, "500K", 20, false, 1, 1, 2, -847, 41]], [0, "94rZVxgzhDkbmXslMpTLKu", 1], [5, 50, 20]], [4, "lbBien", 135, [[56, "500K", 20, false, 1, 1, 2, -848, 43]], [0, "17/AOJCvJPxr1RJQ2UDXCc", 1], [5, 50, 20]], [1, "nodeOut", 10, [[3, -849, [45], 46]], [0, "c6zSJgHp1OOLz5jUtpit7+", 1], [5, 36, 39], [54.4, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lbDiem", false, 10, [[15, "0", 33, false, 1, 1, -850, 47]], [0, "666ua3UelNuboltjrYc4O8", 1], [5, 58.78, 41.25], [246, 54, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lose", false, 10, [[10, -851, 48]], [0, "25rpoGGblMAoWhqI6CAV8U", 1], [5, 144, 42], [-2.842170943040401e-14, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "win", false, 10, [[17, "animation", 0, false, "animation", -852, 49]], [0, "69BhqAkkZGurFu0zC+nOAQ", 1], [5, 408.34, 328], [0, -30, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "chickenWin", false, 10, [[10, -853, 50]], [0, "23+XOGiZtA84WoPU/YZ9ew", 1], [5, 164, 164], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "emotion", 137, [[17, "1-waaaht", 0, false, "1-waaaht", -854, 54]], [0, "4dzuzd6cNGloFwwoeS56NW", 1], [5, 123, 110]], [19, "lbChat", 138, [[24, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -855, 55]], [0, "93nvLYQ/FCYqjmwfqWVmPR", 1], [4, 4278190080], [5, 172.4, 55]], [33, 10, 23, 36, 37, 59], [1, "chip_demo", 60, [[9, 0, -856, [101], 102]], [0, "2ewI/zUQ1Gg4hTGEtcOwXd", 1], [5, 35, 35], [-56, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalGate3", 60, [[39, 13, false, 1, 1, 2, -857, [103], 104]], [0, "38A/VIuaZEF4oDRAS5dfco", 1], [4, 4278249468], [5, 107.25, 16.25], [14.3, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 61, [[9, 0, -858, [107], 108]], [0, "48aetHgphC5qLprIJSS8vv", 1], [5, 35, 35], [-56.1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalGate2", 61, [[39, 13, false, 1, 1, 2, -859, [109], 110]], [0, "f1Q4u3wNNPL4P79mlkSKQ/", 1], [4, 4278249468], [5, 107.25, 16.25], [13.5, 5.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 62, [[9, 0, -860, [113], 114]], [0, "8dEpHJIWVHraEWIkm9kXez", 1], [5, 35, 35], [-55.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalGate1", 62, [[39, 13, false, 1, 1, 2, -861, [115], 116]], [0, "50PBtwm3BE5oiWAv9aeKhZ", 1], [4, 4278249468], [5, 107.25, 16.25], [13.3, 6.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 63, [[9, 0, -862, [119], 120]], [0, "6dQuEqfmhNA5fB2SvkE/nr", 1], [5, 35, 35], [-55.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalGate5", 63, [[39, 13, false, 1, 1, 2, -863, [121], 122]], [0, "11rFIbtgVN94KUSwSsWtyp", 1], [4, 4278249468], [5, 107.25, 16.25], [14.5, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 64, [[9, 0, -864, [125], 126]], [0, "8dDK+qyG1En5XKmhckwiVF", 1], [5, 35, 35], [-55.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalGate6", 64, [[141, 13, false, 1, 1, -865, [127], 128]], [0, "ad1VjsDldCOpIogRA9MdFD", 1], [4, 4278249468], [5, 0, 40], [15.6, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 65, [[9, 0, -866, [131], 132]], [0, "ba3AFP7khIcreZZy4rlG+g", 1], [5, 35, 35], [-55.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalGate6", 65, [[39, 13, false, 1, 1, 2, -867, [133], 134]], [0, "b9KNuG/85NxZaPnT3AefbS", 1], [4, 4278249468], [5, 107.25, 16.25], [15.1, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 59, [[9, 0, -868, [137], 138]], [0, "4dJI6O1wBACZNJz4eqGU0p", 1], [5, 35, 35], [-56, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTotalGate4", 59, [[39, 13, false, 1, 1, 2, -869, [139], 140]], [0, "c1rvj39OhNjb1JdoNTi1Kp", 1], [4, 4278249468], [5, 107.25, 16.25], [15.1, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [31, 3, 0, 2, 0.5, 1, 140, [145], [0, 0.5, 0.5]], [4, "ava_sheld", 4, [[3, -870, [146], 147]], [0, "5ff3MvVfxLnbkR5GxvPN6J", 1], [5, 87, 87]], [16, "lbName", 96, [[6, "Academy...", 18, false, 1, 1, -871, [148], 149]], [0, "dfHI7xBZxD2JBXtTOgiG48", 1], [4, 4281915391], [5, 89.8, 40], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeChuong", 4, [[3, -872, [154], 155]], [0, "c9C462hvhEm6IhAnJwxGM1", 1], [5, 33, 33], [-35, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeGa", 4, [[3, -873, [156], 157]], [0, "c73bjbVu5Huasy5Tbnrmfe", 1], [5, 33, 33], [4, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 142, [[40, "500K", 15, false, 1, 1, 2, -874, [158], 159]], [0, "0ab4Bop6JA3LBDQ+Ck2G5X", 1], [5, 36.75, 15], [-0.8, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeOut", 4, [[3, -875, [162], 163]], [0, "d6Otss+GZD2a/iLBNaRtX5", 1], [5, 36, 39], [40, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBien", 66, [[6, "500K", 25, false, 1, 1, -876, [164], 165]], [0, "5e36fLDHJBTLXaXSh2f0C0", 1], [5, 61.25, 40]], [4, "lbBien", 67, [[6, "500K", 25, false, 1, 1, -877, [168], 169]], [0, "7anguuQbBFupHA8sXqtJnm", 1], [5, 61.25, 40]], [5, "lbDiem", false, 4, [[15, "10", 33, false, 1, 1, -878, 172]], [0, "85bZV3MyxIMp+Sg69Q1XAd", 1], [5, 58.78, 41.25], [-279, 66, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lose", false, 4, [[10, -879, 173]], [0, "b9z8h7GGpPcqx8h4ocOjTy", 1], [5, 144, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "win", false, 4, [[17, "animation", 0, false, "animation", -880, 174]], [0, "f9UTztbsBB4pYnLT8jEJvE", 1], [5, 408.34, 328], [0, -24, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "chickenWin", false, 4, [[10, -881, 175]], [0, "c1F2irWI9FIoecC57kE1fN", 1], [5, 164, 164], [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "emotion", 144, [[17, "1-waaaht", 0, false, "1-waaaht", -882, 179]], [0, "18O+FNhO5Gh5wBDM22hkx+", 1], [5, 123, 110]], [19, "lbChat", 145, [[24, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -883, 180]], [0, "9elU7DMj1EM6VeUllCWjCT", 1], [4, 4278190080], [5, 172.4, 55]], [33, 4, 33, 34, 35, 60], [31, 3, 0, 2, 0.5, 1, 147, [186], [0, 0.5, 0.5]], [4, "ava_sheld", 5, [[3, -884, [187], 188]], [0, "e5cb+EucVECp7WOAca1n0Q", 1], [5, 87, 87]], [16, "lbName", 97, [[6, "Academy...", 18, false, 1, 1, -885, [189], 190]], [0, "92W44LJNtCloQJ0WgJrl7U", 1], [4, 4281915391], [5, 89.8, 40], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeChuong", 5, [[3, -886, [195], 196]], [0, "32pmG3Cn5FMpT5X+ZMeb6b", 1], [5, 33, 33], [-35, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeGa", 5, [[3, -887, [197], 198]], [0, "46Yu8Xz4lEuZrNA7A06WXs", 1], [5, 33, 33], [4, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 149, [[40, "500K", 15, false, 1, 1, 2, -888, [199], 200]], [0, "59+BPpjBJHB5H+ULgZq/w0", 1], [5, 36.75, 15], [-0.8, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeOut", 5, [[3, -889, [203], 204]], [0, "ffD5FuPg5BEpOdBPBwe7uB", 1], [5, 36, 39], [40, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBien", 68, [[6, "500K", 25, false, 1, 1, -890, [205], 206]], [0, "0cXhO2OiNFVb02s97zyekS", 1], [5, 61.25, 40]], [4, "lbBien", 69, [[6, "500K", 25, false, 1, 1, -891, [209], 210]], [0, "e71dg+2FJFSrN3lYudcmIS", 1], [5, 61.25, 40]], [5, "lbDiem", false, 5, [[15, "0", 33, false, 1, 1, -892, 213]], [0, "8dxq1mh/BFwYRRpZYbn/Fp", 1], [5, 34.03, 41.25], [-235.6, 19.5, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lose", false, 5, [[10, -893, 214]], [0, "6eGM36+rBDNbhLIt7WFMt1", 1], [5, 144, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "win", false, 5, [[17, "animation", 0, false, "animation", -894, 215]], [0, "dcMfhE7RZHGp18DHmmVWbG", 1], [5, 408.34, 328], [0, -24, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "chickenWin", false, 5, [[10, -895, 216]], [0, "c1voymONFD9bZRvLHDqcGr", 1], [5, 164, 164], [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "emotion", 151, [[17, "1-waaaht", 0, false, "1-waaaht", -896, 221]], [0, "caZEmfI7pOf74LnXqCo+O5", 1], [5, 123, 110]], [19, "lbChat", 152, [[24, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -897, 222]], [0, "9etPs804pBa73oKrzDY4dH", 1], [4, 4278190080], [5, 172.4, 55]], [33, 5, 30, 31, 32, 61], [31, 3, 0, 2, 0.5, 1, 154, [228], [0, 0.5, 0.5]], [4, "ava_sheld", 6, [[3, -898, [229], 230]], [0, "e0HP/Ql4VGoIuKaoxTvqH8", 1], [5, 87, 87]], [16, "lbName", 98, [[6, "Academy...", 18, false, 1, 1, -899, [231], 232]], [0, "17U/ioSr5LJ5FzSdFGfbxo", 1], [4, 4281915391], [5, 89.8, 40], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeChuong", 6, [[3, -900, [237], 238]], [0, "60Z8IDiahLkLUqkoVrttpq", 1], [5, 33, 33], [-35, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeGa", 6, [[3, -901, [239], 240]], [0, "8aLs4O2PZNdZImc9RyDDL6", 1], [5, 33, 33], [4, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 156, [[40, "500K", 15, false, 1, 1, 2, -902, [241], 242]], [0, "4cEVTlWlBPbabOuGlThEEw", 1], [5, 36.75, 15], [-0.8, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeOut", 6, [[3, -903, [245], 246]], [0, "b7XVMZK6JPhLUB+AwJcC6Q", 1], [5, 36, 39], [40, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBien", 70, [[6, "500K", 25, false, 1, 1, -904, [247], 248]], [0, "6eSlXQ1i9EyrMOABR/d8uT", 1], [5, 61.25, 40]], [4, "lbBien", 71, [[6, "500K", 25, false, 1, 1, -905, [251], 252]], [0, "8djZFlKsBHiLJYKKlb05aX", 1], [5, 61.25, 40]], [5, "lbDiem", false, 6, [[15, "0", 33, false, 1, 1, -906, 255]], [0, "6dSb0p0yBEb6yVLqllBycq", 1], [5, 58.78, 41.25], [-286.4, -29.3, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lose", false, 6, [[10, -907, 256]], [0, "eery+XIs5Ox7V6HXaZGPlr", 1], [5, 144, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "win", false, 6, [[17, "animation", 0, false, "animation", -908, 257]], [0, "9dQowUTApPQrj/rpq3osay", 1], [5, 408.34, 328], [0, -24, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "chickenWin", false, 6, [[10, -909, 258]], [0, "30w5Eu+gBLYYOYb8OjmsCO", 1], [5, 164, 164], [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "emotion", 158, [[17, "1-waaaht", 0, false, "1-waaaht", -910, 263]], [0, "663DqSvyhOPZLjdK9GBZ+W", 1], [5, 123, 110]], [19, "lbChat", 159, [[24, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -911, 264]], [0, "67R0d28LFE+ovvBDaY0Cuz", 1], [4, 4278190080], [5, 172.4, 55]], [33, 6, 27, 28, 29, 62], [31, 3, 0, 2, 0.5, 1, 161, [270], [0, 0.5, 0.5]], [4, "ava_sheld", 7, [[3, -912, [271], 272]], [0, "48M0sfZMFJOrYAfIKP7DsN", 1], [5, 87, 87]], [16, "lbName", 99, [[6, "Academy...", 18, false, 1, 1, -913, [273], 274]], [0, "65imkRS35NvaIt7cUm/KfA", 1], [4, 4281915391], [5, 89.8, 40], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeChuong", 7, [[3, -914, [279], 280]], [0, "08joPywrNJxZTr90Xi6Ydz", 1], [5, 33, 33], [-35, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeGa", 7, [[3, -915, [281], 282]], [0, "9088GebxFNKq4/SuOcbixd", 1], [5, 33, 33], [4, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 163, [[40, "500K", 15, false, 1, 1, 2, -916, [283], 284]], [0, "2a8KHHMxRO8oAU3vaeHrzD", 1], [5, 36.75, 15], [-0.8, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeOut", 7, [[3, -917, [287], 288]], [0, "8bsn624OhKG6m0GYngL41q", 1], [5, 36, 39], [40, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBien", 72, [[6, "500K", 25, false, 1, 1, -918, [289], 290]], [0, "adl5o75rhA1rd7Zxy2bMya", 1], [5, 61.25, 40]], [4, "lbBien", 73, [[6, "500K", 25, false, 1, 1, -919, [293], 294]], [0, "4chxrnEH5A37cq+Pr+uN/8", 1], [5, 61.25, 40]], [5, "lbDiem", false, 7, [[15, "0", 33, false, 1, 1, -920, 297]], [0, "96ZQjDxGRL5a6S/30SCpNr", 1], [5, 58.78, 41.25], [238, -28, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lose", false, 7, [[10, -921, 298]], [0, "ebOpExkf9PB4MSwRAeGX3N", 1], [5, 144, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "win", false, 7, [[17, "animation", 0, false, "animation", -922, 299]], [0, "0ayu4wz59DG7MfjwE6koxy", 1], [5, 408.34, 328], [0, -24, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "chickenWin", false, 7, [[10, -923, 300]], [0, "1bvRr5GVVOs4+BmpN43myp", 1], [5, 164, 164], [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "emotion", 165, [[17, "1-waaaht", 0, false, "1-waaaht", -924, 305]], [0, "c5sl0xeqlIdLpJNvX7UxQq", 1], [5, 123, 110]], [19, "lbChat", 166, [[24, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -925, 306]], [0, "adJpJH8BVBCbf2KURQsa0w", 1], [4, 4278190080], [5, 172.4, 55]], [33, 7, 41, 42, 43, 65], [31, 3, 0, 2, 0.5, 1, 168, [312], [0, 0.5, 0.5]], [4, "ava_sheld", 8, [[3, -926, [313], 314]], [0, "6eny6kQ2hAXK2o8scgo9N3", 1], [5, 87, 87]], [16, "lbName", 100, [[6, "Academy...", 18, false, 1, 1, -927, [315], 316]], [0, "7cWFsdMv1IvaJXzGLr9ZhW", 1], [4, 4281915391], [5, 89.8, 40], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeChuong", 8, [[3, -928, [321], 322]], [0, "50B9HQ4RhDe6ZSY+Z2AK05", 1], [5, 33, 33], [-35, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeGa", 8, [[3, -929, [323], 324]], [0, "6409DXpIhKUo9LjgP00fGV", 1], [5, 33, 33], [4, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 170, [[40, "500K", 15, false, 1, 1, 2, -930, [325], 326]], [0, "b9hgqByOxJwoM35Os5t6mI", 1], [5, 36.75, 15], [-0.8, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeOut", 8, [[3, -931, [329], 330]], [0, "17s5Oi7ttMJYlaWKJp+Lym", 1], [5, 36, 39], [40, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBien", 74, [[6, "500K", 25, false, 1, 1, -932, [331], 332]], [0, "aaKKD38E5F55/ABHqKcWIm", 1], [5, 61.25, 40]], [4, "lbBien", 75, [[6, "500K", 25, false, 1, 1, -933, [335], 336]], [0, "3fNQd754pPWKMzZI0lQnD0", 1], [5, 61.25, 40]], [5, "lbDiem", false, 8, [[15, "0", 33, false, 1, 1, -934, 339]], [0, "1fZZNw4FpJ3aj+iNEFxNdl", 1], [5, 58.78, 41.25], [240, 16, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lose", false, 8, [[10, -935, 340]], [0, "f3Jz2lpfxLub0HMN0f58MU", 1], [5, 144, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "win", false, 8, [[17, "animation", 0, false, "animation", -936, 341]], [0, "1eMDZG0B5Ck4XI1Tz8sgD4", 1], [5, 408.34, 328], [0, -24, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "chickenWin", false, 8, [[10, -937, 342]], [0, "6aONyWFq1BPISLGDttK24x", 1], [5, 164, 164], [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "emotion", 172, [[17, "1-waaaht", 0, false, "1-waaaht", -938, 347]], [0, "15LhogHWJN26naDFbv/2Aa", 1], [5, 123, 110]], [19, "lbChat", 173, [[24, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -939, 348]], [0, "5arlA5Oc5NV4KPIcRArag0", 1], [4, 4278190080], [5, 172.4, 55]], [33, 8, 24, 25, 26, 64], [31, 3, 0, 2, 0.5, 1, 175, [354], [0, 0.5, 0.5]], [4, "ava_sheld", 9, [[3, -940, [355], 356]], [0, "c8o50Yb8xMy4To8XteR89L", 1], [5, 87, 87]], [16, "lbName", 101, [[6, "Academy...", 18, false, 1, 1, -941, [357], 358]], [0, "e35VxvFxpMgaoyUuVNlUQt", 1], [4, 4281915391], [5, 89.8, 40], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeChuong", 9, [[3, -942, [363], 364]], [0, "4cdAPb715Ln5mSjaQVbGsS", 1], [5, 33, 33], [-35, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeGa", 9, [[3, -943, [365], 366]], [0, "3cZZfxbmVGorXwGHYOqIZJ", 1], [5, 33, 33], [4, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 177, [[40, "500K", 15, false, 1, 1, 2, -944, [367], 368]], [0, "2dii3h73hMhJ8f4XzRI6ej", 1], [5, 36.75, 15], [-0.8, 1.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nodeOut", 9, [[3, -945, [371], 372]], [0, "8aI98zozNAbrI62P4Neul4", 1], [5, 36, 39], [40, 10, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBien", 76, [[6, "500K", 25, false, 1, 1, -946, [373], 374]], [0, "26LaCwsx5Fja22CQnZbvCt", 1], [5, 61.25, 40]], [4, "lbBien", 77, [[6, "500K", 25, false, 1, 1, -947, [377], 378]], [0, "3fkxScrDpAdo7DH2fsi98R", 1], [5, 61.25, 40]], [5, "lbDiem", false, 9, [[15, "0", 33, false, 1, 1, -948, 381]], [0, "d0doe0aGREvrSXTWlwLDm5", 1], [5, 58.78, 41.25], [218, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "lose", false, 9, [[10, -949, 382]], [0, "bdHHeNodVP6rVF+k7ddb4R", 1], [5, 144, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "win", false, 9, [[17, "animation", 0, false, "animation", -950, 383]], [0, "30o3jg9yFIm6GoWibpqYpF", 1], [5, 408.34, 328], [0, -24, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "chickenWin", false, 9, [[10, -951, 384]], [0, "27oGL+6aVMUr+g18AowpOF", 1], [5, 164, 164], [0, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "emotion", 179, [[17, "1-waaaht", 0, false, "1-waaaht", -952, 389]], [0, "fciB5GPIpHtI67FOz1dE1H", 1], [5, 123, 110]], [19, "lbChat", 180, [[24, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -953, 390]], [0, "5e9loL35RON4o8yR7YNcGv", 1], [4, 4278190080], [5, 172.4, 55]], [33, 9, 38, 39, 40, 63], [1, "chip_demo", 44, [[9, 0, -954, [394], 395]], [0, "aeNQXgCSpDk654B7AeewU/", 1], [5, 35, 35], [-95, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [27, 181], [1, "ga", 44, [[3, -955, [398], 399]], [0, "c4l84Y70ZISradA3r2ixP/", 1], [5, 24, 32], [87.1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [64, "lbTimeNan", 183, [184], [0, "44bNDrdEFI2r80VGxS0/gD", 1], [4, 4278255612], [5, 47, 40], [0, -47, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbSID", 105, [[15, "<PERSON><PERSON><PERSON> ký rời bàn", 20, false, 1, 1, -956, 442]], [0, "b8rN2vNxVIwa/v/lo8ypsm", 1], [5, 144, 20]], [1, "lbInfo", 106, [[15, "Đặt cửa", 24, false, 1, 1, -957, 444]], [0, "d7t2JH8hlL27STrwrIRbLd", 1], [5, 84.6, 24], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Background", 107, [[125, 2, false, -958, [448], 449]], [0, "94yQ3t2AhFlbpCEuZwD//Q", 1], [5, 60, 61]], [4, "slice_bg", 45, [[3, -959, [450], 451]], [0, "e192nrR3BPiK7po8W6l9G8", 1], [5, 148, 431]], [95, "Background", false, 45, [[20, 1, 0, -960, 452]], [0, "2bp+VgYHdJ2IOpM+z/mexl", 1], [5, 60, 400]], [36, "bar", 109, [-961], [0, "faNmWgr8NJZ5gpGO8ZeK7x", 1], [5, 60, 0], [0, 0.5, 0], [0, -199.7, 0, 0, 0, 0, 1, 1, 1, 1]], [54, 1, 0, 365, [453]], [52, "lbCurrentBet", 191, [-962], [0, "c5eAzsbE1D4JjEWR6JXGtT", 1], [5, 95, 15.63], [-0.5, 12.3, 0, 0, 0, 0, 1, 1, 1, 1]], [142, 25, 25, false, 1, 1, 2, 367, [455]], [128, 1.1, true, 3, 78, [4, 4294967295], [4, 4294967295], 78], [156, 1, 0, 45, [[8, "56bd4y7a3hNp5RXp+tVhS56", "onSetBetValue", 13]], 369], [4, "btn_cuoc", 79, [[3, -963, [460], 461]], [0, "4aQB7alvVCHKGfGQf5g1cK", 1], [5, 150, 63]], [1, "lbBien", 192, [[24, "500K", 18, 32, false, 1, 1, 2, -964, 462]], [0, "fe+BbLW1ZK94vWeU+fC7gs", 1], [5, 79.88, 18], [33, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBien", 193, [[24, "500K", 18, 32, false, 1, 1, 2, -965, 464]], [0, "b6NE0wyTtP17wgwHeHqVRO", 1], [5, 79.88, 18], [31, 12, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "btn_gopGa", 82, [[10, -966, 466]], [0, "a5J85lZ4xKuJHs4+D/kgcM", 1], [5, 150, 63]], [4, "btn_nhanB", 83, [[10, -967, 467]], [0, "00vDcGfLlM37/tyYp7r4y8", 1], [5, 150, 63]], [52, "dealer_xd", 2, [-968], [0, "b5mJVU9PZNSaqtFuDF0zxh", 1], [5, 151.57, 182.34], [0, 185, 0, 0, 0, 0, 1, 0.85, 0.85, 1]], [155, "animation", 0, false, "animation", 376, [468]], [1, "bg_noti", 110, [[3, -969, [469], 470]], [0, "78dTrFIUdBy5pq/FIm1ikG", 1], [5, 274, 49], [0, 32.8, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "CHO PHIEN MOI", 24, false, 1, 1, 194, [471]], [143, 18, false, 1, 1, 195], [4, "animAntat", 84, [[17, "animation", 0, false, "animation", -970, 475]], [0, "92DESat8pFrqS3gA93VkFn", 1], [5, 408.34, 328]], [48, "RICHTEXT_CHILD", false, -32768, 86, [[41, "[TQ] ", 30, false, 1, -971, 479]], [0, "a1SiRkx0FMC6hqx6SfIV86", 1], [4, 4280264453], [5, 62.08, 40], [0, 0, 0], [1, -187.17000000000002, 0, 0], [1, 1, 1, 1]], [48, "RICHTEXT_CHILD", false, -32768, 86, [[41, "alita ", 30, false, 1, -972, 480]], [0, "d9ey97LV1Bc4uULk0/tBeS", 1], [4, 4278255612], [5, 66.17, 40], [0, 0, 0], [1, -125.09000000000002, 0, 0], [1, 1, 1, 1]], [76, "RICHTEXT_CHILD", false, -32768, 86, [[41, "mu<PERSON><PERSON> b<PERSON>", 30, false, 1, -973, 481]], [0, "98sj0DDGtIYKJ4wGwV1MHE", 1], [5, 246.09, 40], [0, 0, 0], [1, -58.920000000000016, 0, 0], [1, 1, 1, 1]], [1, "label", 21, [[15, "Bạn có muốn <PERSON> không?", 30, false, 1, 1, -974, 483]], [0, "bbB/pBUJVLJodpv+K+7whq", 1], [5, 471, 30], [0, 49, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbTime", 21, [[15, "1s", 30, false, 1, 1, -975, 486]], [0, "35XAefU7JIe5JX1wu0UJKM", 1], [4, 4278255612], [5, 35.25, 30], [0, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "label", 87, [[15, "Bạn có muốn Bán <PERSON> không?", 30, false, 1, 1, -976, 487]], [0, "72NwWftNVH36a7ZAREkGms", 1], [5, 464.25, 30], [0, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "RICHTEXT_CHILD", false, -32768, 88, [[41, "[TQ] ", 30, false, 1, -977, 490]], [0, "bf4nhvcWxPfbzSNDDEcpPD", 1], [4, 4280264453], [5, 62.08, 40], [0, 0, 0], [1, -187.17000000000002, 0, 0], [1, 1, 1, 1]], [48, "RICHTEXT_CHILD", false, -32768, 88, [[41, "alita ", 30, false, 1, -978, 491]], [0, "cd8zXXvjFBRKWJMMU78Vsw", 1], [4, 4278255612], [5, 66.17, 40], [0, 0, 0], [1, -125.09000000000002, 0, 0], [1, 1, 1, 1]], [76, "RICHTEXT_CHILD", false, -32768, 88, [[41, "mu<PERSON><PERSON> b<PERSON>", 30, false, 1, -979, 492]], [0, "45rfykIZFKX4C2lsQKMv27", 1], [5, 246.09, 40], [0, 0, 0], [1, -58.920000000000016, 0, 0], [1, 1, 1, 1]], [96, "\bchat<PERSON>arent", 2, [0, "b1xAF98edOc5Z2t+I4sTla", 1]], [4, "bg_tex1", 89, [[44, 0, -980]], [0, "45J/idg7RL+J61VAv2g157", 1], [5, 800, 80]], [65, "lbMessage", 89, [-981], [0, "67OcaCb9ZClK23d38CIZfm", 1], [5, 700, 45]], [144, "Dat cuoc thanh cong ", 36, 50, false, false, 1, 1, 2, 393], [1, "name", 197, [[6, "BA CÂY", 34, false, 1, 1, -982, [511], 512]], [0, "b9ILd/rBBBGouGQJ1J3ab+", 1], [5, 121.55, 40], [-12, 4.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chip_demo", 48, [[9, 0, -983, [515], 516]], [0, "2fEqn8bdBCj6OkNk9P6cfc", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 48, [[6, "1K", 25, false, 1, 1, -984, [517], 518]], [0, "75U8w2uAZEmqlLjRc3v4kF", 1], [5, 46.09, 40]], [1, "chip_demo", 49, [[9, 0, -985, [522], 523]], [0, "5d2Zg3rQ9OIaF8x+ncPeAo", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 49, [[6, "2K", 25, false, 1, 1, -986, [524], 525]], [0, "6clD7DXmdHGYLzlOmmqvhK", 1], [5, 54.69, 40]], [1, "chip_demo", 50, [[9, 0, -987, [529], 530]], [0, "cdig3nMElPWInteLqMP+Dt", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 50, [[6, "5K", 25, false, 1, 1, -988, [531], 532]], [0, "95d3zlWCBGgpzPlOALZJ+q", 1], [5, 56.25, 40]], [1, "chip_demo", 51, [[9, 0, -989, [536], 537]], [0, "7bFozo70VNua6NkSe8VKUA", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 51, [[6, "10K", 25, false, 1, 1, -990, [538], 539]], [0, "b2RHBeC1hOtIaD3wz4HLoz", 1], [5, 73.44, 40]], [1, "chip_demo", 52, [[9, 0, -991, [543], 544]], [0, "9bxTYY3JJC6pnWgapxwqje", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 52, [[6, "20K", 25, false, 1, 1, -992, [545], 546]], [0, "3d5Dhvqi1FCYNAX4iSecJF", 1], [5, 82.03, 40]], [1, "chip_demo", 53, [[9, 0, -993, [550], 551]], [0, "a5rIinQLZDnKeOqLeHZb8P", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 53, [[6, "50K", 25, false, 1, 1, -994, [552], 553]], [0, "caKCnTkdNHJJKTHsy4n/gL", 1], [5, 83.59, 40]], [1, "chip_demo", 54, [[9, 0, -995, [557], 558]], [0, "7bmqptp61EdZolWHMCEBmK", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 54, [[6, "100K", 25, false, 1, 1, -996, [559], 560]], [0, "47BLmdPX5DMK1u+3YHSdEf", 1], [5, 100.78, 40]], [1, "chip_demo", 55, [[9, 0, -997, [564], 565]], [0, "cd032DCBJLvaJ8oxHLvrTI", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 55, [[6, "200K", 25, false, 1, 1, -998, [566], 567]], [0, "edIDOYFNxBwZP+2ykUWtSY", 1], [5, 109.38, 40]], [1, "chip_demo", 56, [[9, 0, -999, [571], 572]], [0, "7fHd1xEKVFS6viXDy6IUxi", 1], [5, 40, 40], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbRoomValue", 56, [[6, "500K", 25, false, 1, 1, -1000, [573], 574]], [0, "98bW7iqFBLX6ZPUM+cdLJc", 1], [5, 110.94, 40]], [61, "temp", 90, [57], [0, "1ewjmB+/9H/Y4hWRyZSoY8", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [66, "rtChat", false, 57, [-1001], [0, "2dvJ9vwblG+IGfQ/n6irgu", 1], [5, 345, 33], [0, 0, 0.5], [0, 16.5, 0, 0, 0, 0, 1, 1, 1, 1]], [133, false, "", 20, 345, 33, false, 415], [97, "lbSID", false, 22, [[145, "[TQ]", 18, 48, false, false, 1, 1, -1002, 580]], [0, "6ac/MIImZI3oW/0EyRusNb", 1], [4, 4279026733], [5, 33.3, 21.6], [0, 0, 0.5]], [107, "lbNickName", 22, [-1003], [0, "43oVwM30xCV4afGLm5/J4/", 1], [4, 4281523194], [5, 95.39, 60.48], [0, 0, 0.5]], [146, 19, 48, false, false, 1, 1, 418, [581]], [5, "V1", false, 22, [[10, -1004, 582]], [0, "6cglfqmM5AXZiU/Rf9/Jos", 1], [5, 30, 28], [133.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "lbMessage", 22, [-1005], [0, "b4kLZzM89JXomSLZz2HIg6", 1], [5, 184, 24], [0, 0, 0.5], [95.39, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [147, 19, 48, false, false, 1, 1, 421, [583]], [137, false, 90, 200], [157, 15, 400, 90, 57, 423], [65, "BACKGROUND_SPRITE", 91, [-1006], [0, "74mI42kYxDPpVAjK6EDcdb", 1], [5, 235, 30]], [54, 1, 0, 425, [586]], [66, "TEXT_LABEL", false, 91, [-1007], [0, "5fTjdUCOVB9qOpZ2an5U/O", 1], [5, 233, 30], [0, 0, 1], [-115.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [148, 20, 30, false, false, 1, 1, 427], [108, "PLACEHOLDER_LABEL", 91, [-1008], [0, "867ysh6s9C5YAm5qKuwMGH", 1], [4, 4290493371], [5, 233, 30], [0, 0, 1], [-115.5, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [149, "<PERSON><PERSON>i dung tin nhắn ...", 20, 30, false, false, 1, 1, 429, [587]], [158, 255, 6, 91, [[8, "c9ea2HJ+4FBwJf8JdBQBbUQ", "editingReturn", 18]], 428, 430, 426], [1, "sprite", 122, [[9, 2, -1009, [590], 591]], [0, "10EoJZOqtKE546v2/G7dW+", 1], [5, 82, 46], [-2.6, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [129, 1.1, false, 3, 122, [[8, "c9ea2HJ+4FBwJf8JdBQBbUQ", "sendChatClicked", 18]], [4, 4294967295], [4, 4294967295], 122], [50, "iconRank", false, 19, [[10, -1010, 592]], [0, "7emMBnSWJLVIwDdwOvyqJL", 1], [5, 36, 29], [0, 0, 0.5], [-113, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "lbRank", 19, [-1011], [0, "e7sxwcmIdJqIR+82fmxTne", 1], [5, 15, 40], [-105.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "0", 25, false, 1, 1, 435, [594]], [109, "lbSID", false, 19, [-1012], [0, "37Il/TKkZK7I1LCePneb/w", 1], [4, 4280264453], [5, 32.73, 17], [0, 0, 0.5], [-86.9, 13.4, 0, 0, 0, 0, 1, 1, 1, 1]], [150, "[TQ] ", 17, false, 1, 1, 437], [36, "lbNickName", 19, [-1013], [0, "87MbNS25lCAq1Xo+dNS4We", 1], [5, 170, 17], [0, 0, 0.5], [-50.8, 13.4, 0, 0, 0, 0, 1, 1, 1, 1]], [151, "9900MMMM99009009", 17, 17, false, false, 2, 439, [595]], [50, "chip_demo", false, 207, [[11, 0, -1014, 596]], [0, "5eQT3lX3xIVIWAVj0ZxWuN", 1], [5, 25, 25], [0, 1, 0.5], [-0.9, -3.8, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "90.000.000", 20, false, 1, 1, 207, [597]], [98, "content", 208, [0, "1aVCeHTPJOK4ll7CbogzVR", 1], [5, 240, 350], [0, 0.5, 1]], [73, false, 0.75, 0.23, null, null, 92, 443], [159, 5, 100, 92, 19, 444]], 0, [0, 20, 1, 0, -1, 15, 0, -2, 2, 0, 21, 84, 0, 12, 184, 0, 13, 16, 0, 14, 186, 0, 15, 188, 0, 16, 190, 0, 17, 182, 0, 22, 357, 0, 23, 377, 0, -1, 245, 0, -2, 275, 0, -3, 291, 0, -4, 307, 0, -5, 355, 0, -6, 339, 0, -7, 323, 0, 24, 380, 0, 25, 379, 0, 26, 223, 0, 27, 226, 0, 18, 229, 0, 0, 2, 0, 28, 391, 0, 29, 2, 0, 30, 15, 0, 31, 105, 0, 32, 218, 0, 33, 220, 0, 0, 2, 0, 0, 2, 0, 34, 118, 0, 35, 87, 0, 36, 21, 0, 37, 47, 0, 0, 2, 0, 0, 2, 0, -1, 58, 0, -2, 11, 0, -3, 376, 0, -4, 110, 0, -5, 16, 0, -6, 84, 0, -7, 47, 0, -8, 391, 0, -9, 120, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, -4, 27, 0, -5, 28, 0, -6, 29, 0, -7, 30, 0, -8, 31, 0, -9, 32, 0, -10, 33, 0, -11, 34, 0, -12, 35, 0, -13, 23, 0, -14, 36, 0, -15, 37, 0, -16, 38, 0, -17, 39, 0, -18, 40, 0, -19, 41, 0, -20, 42, 0, -21, 43, 0, 5, 3, 0, 0, 4, 0, -2, 275, 0, -1, 139, 0, -2, 140, 0, -3, 261, 0, -4, 96, 0, -5, 263, 0, -6, 264, 0, -7, 142, 0, -8, 266, 0, -9, 66, 0, -10, 67, 0, -11, 269, 0, -12, 270, 0, -13, 271, 0, -14, 272, 0, -15, 143, 0, -16, 144, 0, 0, 5, 0, -2, 291, 0, -1, 146, 0, -2, 147, 0, -3, 277, 0, -4, 97, 0, -5, 279, 0, -6, 280, 0, -7, 149, 0, -8, 282, 0, -9, 68, 0, -10, 69, 0, -11, 285, 0, -12, 286, 0, -13, 287, 0, -14, 288, 0, -15, 150, 0, -16, 151, 0, 0, 6, 0, -2, 307, 0, -1, 153, 0, -2, 154, 0, -3, 293, 0, -4, 98, 0, -5, 295, 0, -6, 296, 0, -7, 156, 0, -8, 298, 0, -9, 70, 0, -10, 71, 0, -11, 301, 0, -12, 302, 0, -13, 303, 0, -14, 304, 0, -15, 157, 0, -16, 158, 0, 0, 7, 0, -2, 323, 0, -1, 160, 0, -2, 161, 0, -3, 309, 0, -4, 99, 0, -5, 311, 0, -6, 312, 0, -7, 163, 0, -8, 314, 0, -9, 72, 0, -10, 73, 0, -11, 317, 0, -12, 318, 0, -13, 319, 0, -14, 320, 0, -15, 164, 0, -16, 165, 0, 0, 8, 0, -2, 339, 0, -1, 167, 0, -2, 168, 0, -3, 325, 0, -4, 100, 0, -5, 327, 0, -6, 328, 0, -7, 170, 0, -8, 330, 0, -9, 74, 0, -10, 75, 0, -11, 333, 0, -12, 334, 0, -13, 335, 0, -14, 336, 0, -15, 171, 0, -16, 172, 0, 0, 9, 0, -2, 355, 0, -1, 174, 0, -2, 175, 0, -3, 341, 0, -4, 101, 0, -5, 343, 0, -6, 344, 0, -7, 177, 0, -8, 346, 0, -9, 76, 0, -10, 77, 0, -11, 349, 0, -12, 350, 0, -13, 351, 0, -14, 352, 0, -15, 178, 0, -16, 179, 0, 0, 10, 0, -2, 245, 0, -1, 130, 0, -2, 131, 0, -3, 231, 0, -4, 95, 0, -5, 233, 0, -6, 234, 0, -7, 133, 0, -8, 134, 0, -9, 135, 0, -10, 238, 0, -11, 239, 0, -12, 240, 0, -13, 241, 0, -14, 242, 0, -15, 136, 0, -16, 137, 0, -1, 127, 0, -2, 128, 0, -3, 129, 0, -4, 93, 0, -5, 94, 0, -6, 20, 0, -7, 12, 0, -8, 105, 0, -9, 106, 0, 5, 11, 0, -11, 17, 0, -12, 107, 0, -13, 13, 0, 38, 44, 0, 12, 184, 0, 13, 16, 0, 14, 186, 0, 15, 188, 0, 16, 190, 0, 17, 182, 0, -1, 29, 0, -2, 32, 0, -3, 35, 0, -4, 37, 0, -5, 40, 0, -6, 26, 0, -7, 43, 0, -1, 28, 0, -2, 31, 0, -3, 34, 0, -4, 36, 0, -5, 39, 0, -6, 25, 0, -7, 42, 0, -1, 27, 0, -2, 30, 0, -3, 33, 0, -4, 23, 0, -5, 38, 0, -6, 24, 0, -7, 41, 0, 0, 12, 0, -9, 44, 0, 0, 13, 0, 39, 46, 0, 40, 108, 0, 41, 368, 0, 42, 83, 0, 43, 82, 0, 44, 81, 0, 45, 80, 0, 46, 79, 0, 47, 370, 0, 0, 13, 0, -1, 108, 0, -2, 46, 0, 0, 14, 0, -1, 48, 0, -2, 49, 0, -3, 50, 0, -4, 51, 0, -5, 52, 0, -6, 53, 0, -7, 54, 0, -8, 55, 0, -9, 56, 0, 0, 15, 0, -1, 124, 0, -2, 197, 0, -4, 18, 0, -5, 204, 0, -6, 123, 0, -1, 103, 0, -2, 183, 0, -3, 185, 0, -4, 187, 0, -5, 189, 0, -6, 102, 0, -7, 104, 0, 0, 17, 0, -1, 59, 0, -2, 60, 0, -3, 61, 0, -4, 62, 0, -5, 63, 0, -6, 64, 0, -7, 65, 0, 48, 433, 0, 49, 431, 0, 50, 424, 0, 0, 18, 0, -1, 198, 0, -2, 90, 0, -3, 121, 0, -4, 203, 0, -5, 122, 0, 51, 442, 0, 52, 440, 0, 18, 438, 0, 53, 436, 0, 0, 19, 0, -1, 434, 0, -2, 206, 0, -3, 435, 0, -4, 437, 0, -5, 439, 0, -6, 207, 0, 0, 20, 0, -1, 221, 0, -2, 222, 0, -3, 224, 0, -4, 225, 0, -5, 227, 0, -6, 228, 0, 0, 21, 0, -1, 86, 0, -2, 385, 0, -3, 112, 0, -4, 386, 0, 0, 22, 0, -1, 417, 0, -2, 418, 0, -3, 420, 0, -4, 421, 0, 0, 23, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, -1, 356, 0, -2, 181, 0, -3, 358, 0, -1, 370, 0, -1, 363, 0, -2, 364, 0, -3, 109, 0, -4, 78, 0, -1, 80, 0, -2, 81, 0, -3, 82, 0, -4, 83, 0, 0, 47, 0, -1, 111, 0, -2, 85, 0, -3, 119, 0, 0, 48, 0, 3, 48, 0, 0, 48, 0, -1, 396, 0, -2, 397, 0, 0, 49, 0, 3, 49, 0, 0, 49, 0, -1, 398, 0, -2, 399, 0, 0, 50, 0, 3, 50, 0, 0, 50, 0, -1, 400, 0, -2, 401, 0, 0, 51, 0, 3, 51, 0, 0, 51, 0, -1, 402, 0, -2, 403, 0, 0, 52, 0, 3, 52, 0, 0, 52, 0, -1, 404, 0, -2, 405, 0, 0, 53, 0, 3, 53, 0, 0, 53, 0, -1, 406, 0, -2, 407, 0, 0, 54, 0, 3, 54, 0, 0, 54, 0, -1, 408, 0, -2, 409, 0, 0, 55, 0, 3, 55, 0, 0, 55, 0, -1, 410, 0, -2, 411, 0, 0, 56, 0, 3, 56, 0, 0, 56, 0, -1, 412, 0, -2, 413, 0, 0, 57, 0, 54, 416, 0, 19, 422, 0, 55, 419, 0, 0, 57, 0, -1, 415, 0, 56, 212, 0, 57, 214, 0, 58, 210, 0, 0, 58, 0, -1, 209, 0, -2, 211, 0, -3, 213, 0, 0, 59, 0, -1, 258, 0, -2, 259, 0, 0, 60, 0, -1, 246, 0, -2, 247, 0, 0, 61, 0, -1, 248, 0, -2, 249, 0, 0, 62, 0, -1, 250, 0, -2, 251, 0, 0, 63, 0, -1, 252, 0, -2, 253, 0, 0, 64, 0, -1, 254, 0, -2, 255, 0, 0, 65, 0, -1, 256, 0, -2, 257, 0, 0, 66, 0, 3, 66, 0, 0, 66, 0, -1, 267, 0, 0, 67, 0, 3, 67, 0, 0, 67, 0, -1, 268, 0, 0, 68, 0, 3, 68, 0, 0, 68, 0, -1, 283, 0, 0, 69, 0, 3, 69, 0, 0, 69, 0, -1, 284, 0, 0, 70, 0, 3, 70, 0, 0, 70, 0, -1, 299, 0, 0, 71, 0, 3, 71, 0, 0, 71, 0, -1, 300, 0, 0, 72, 0, 3, 72, 0, 0, 72, 0, -1, 315, 0, 0, 73, 0, 3, 73, 0, 0, 73, 0, -1, 316, 0, 0, 74, 0, 3, 74, 0, 0, 74, 0, -1, 331, 0, 0, 75, 0, 3, 75, 0, 0, 75, 0, -1, 332, 0, 0, 76, 0, 3, 76, 0, 0, 76, 0, -1, 347, 0, 0, 77, 0, 3, 77, 0, 0, 77, 0, -1, 348, 0, 0, 78, 0, -2, 369, 0, -1, 191, 0, 3, 79, 0, 0, 79, 0, -1, 371, 0, 3, 80, 0, 0, 80, 0, -1, 192, 0, 3, 81, 0, 0, 81, 0, -1, 193, 0, 3, 82, 0, 0, 82, 0, -1, 374, 0, 3, 83, 0, 0, 83, 0, -1, 375, 0, 0, 84, 0, -1, 196, 0, -2, 381, 0, 0, 85, 0, -2, 87, 0, -3, 118, 0, 0, 86, 0, -1, 382, 0, -2, 383, 0, -3, 384, 0, 0, 87, 0, -1, 387, 0, -2, 115, 0, 0, 88, 0, -1, 388, 0, -2, 389, 0, -3, 390, 0, 0, 89, 0, -1, 392, 0, -2, 393, 0, -1, 423, 0, -2, 424, 0, -1, 414, 0, -2, 199, 0, -1, 431, 0, -1, 425, 0, -2, 427, 0, -3, 429, 0, -1, 444, 0, -2, 445, 0, -2, 208, 0, 3, 93, 0, 0, 93, 0, -1, 217, 0, 3, 94, 0, 0, 94, 0, -1, 219, 0, 0, 95, 0, -1, 232, 0, -2, 132, 0, 0, 96, 0, -1, 262, 0, -2, 141, 0, 0, 97, 0, -1, 278, 0, -2, 148, 0, 0, 98, 0, -1, 294, 0, -2, 155, 0, 0, 99, 0, -1, 310, 0, -2, 162, 0, 0, 100, 0, -1, 326, 0, -2, 169, 0, 0, 101, 0, -1, 342, 0, -2, 176, 0, -1, 182, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, 0, 103, 0, 0, 104, 0, 3, 104, 0, 0, 104, 0, 0, 105, 0, -1, 360, 0, 0, 106, 0, 0, 106, 0, -1, 361, 0, 3, 107, 0, 0, 107, 0, -1, 362, 0, 0, 109, 0, 8, 366, 0, 0, 109, 0, -1, 365, 0, -1, 378, 0, -2, 194, 0, -3, 195, 0, 0, 111, 0, 0, 111, 0, 0, 111, 0, 0, 112, 0, -1, 113, 0, -2, 114, 0, 0, 113, 0, 3, 113, 0, 0, 113, 0, 0, 114, 0, 3, 114, 0, 0, 114, 0, 0, 115, 0, -1, 116, 0, -2, 117, 0, 0, 116, 0, 3, 116, 0, 0, 116, 0, 0, 117, 0, 3, 117, 0, 0, 117, 0, 0, 118, 0, 0, 119, 0, 3, 119, 0, 0, 119, 0, 0, 120, 0, 19, 394, 0, 0, 120, 0, 59, 202, 0, 0, 121, 0, 0, 121, 0, -1, 201, 0, -1, 433, 0, -1, 432, 0, 0, 123, 0, 3, 123, 0, 0, 123, 0, -1, 125, 0, -2, 126, 0, 0, 125, 0, 0, 125, 0, 0, 126, 0, 0, 126, 0, 0, 127, 0, 0, 127, 0, 0, 128, 0, -1, 215, 0, 0, 129, 0, -1, 216, 0, 0, 130, 0, 0, 130, 0, -1, 230, 0, 8, 230, 0, 0, 131, 0, 0, 132, 0, 0, 132, 0, 0, 133, 0, -1, 235, 0, 0, 134, 0, -1, 236, 0, 0, 135, 0, -1, 237, 0, 0, 136, 0, 0, 136, 0, -1, 243, 0, -2, 138, 0, 0, 138, 0, -1, 244, 0, 0, 139, 0, 0, 139, 0, -1, 260, 0, 8, 260, 0, 0, 140, 0, 0, 141, 0, 0, 141, 0, 0, 142, 0, -1, 265, 0, 0, 143, 0, 0, 143, 0, -1, 273, 0, -2, 145, 0, 0, 145, 0, -1, 274, 0, 0, 146, 0, 0, 146, 0, -1, 276, 0, 8, 276, 0, 0, 147, 0, 0, 148, 0, 0, 148, 0, 0, 149, 0, -1, 281, 0, 0, 150, 0, 0, 150, 0, -1, 289, 0, -2, 152, 0, 0, 152, 0, -1, 290, 0, 0, 153, 0, 0, 153, 0, -1, 292, 0, 8, 292, 0, 0, 154, 0, 0, 155, 0, 0, 155, 0, 0, 156, 0, -1, 297, 0, 0, 157, 0, 0, 157, 0, -1, 305, 0, -2, 159, 0, 0, 159, 0, -1, 306, 0, 0, 160, 0, 0, 160, 0, -1, 308, 0, 8, 308, 0, 0, 161, 0, 0, 162, 0, 0, 162, 0, 0, 163, 0, -1, 313, 0, 0, 164, 0, 0, 164, 0, -1, 321, 0, -2, 166, 0, 0, 166, 0, -1, 322, 0, 0, 167, 0, 0, 167, 0, -1, 324, 0, 8, 324, 0, 0, 168, 0, 0, 169, 0, 0, 169, 0, 0, 170, 0, -1, 329, 0, 0, 171, 0, 0, 171, 0, -1, 337, 0, -2, 173, 0, 0, 173, 0, -1, 338, 0, 0, 174, 0, 0, 174, 0, -1, 340, 0, 8, 340, 0, 0, 175, 0, 0, 176, 0, 0, 176, 0, 0, 177, 0, -1, 345, 0, 0, 178, 0, 0, 178, 0, -1, 353, 0, -2, 180, 0, 0, 180, 0, -1, 354, 0, 0, 181, 0, -2, 357, 0, 0, 183, 0, -1, 359, 0, -1, 186, 0, 0, 185, 0, -1, 188, 0, 0, 187, 0, -1, 190, 0, 0, 189, 0, 0, 191, 0, -1, 367, 0, 0, 192, 0, -1, 372, 0, 0, 193, 0, -1, 373, 0, -1, 379, 0, 0, 194, 0, -1, 380, 0, 0, 195, 0, 0, 196, 0, 0, 196, 0, 0, 197, 0, -1, 395, 0, 0, 198, 0, 0, 198, 0, 0, 199, 0, -1, 200, 0, 0, 200, 0, 0, 201, 0, -1, 202, 0, 0, 202, 0, 0, 203, 0, 60, 445, 0, 0, 204, 0, -1, 205, 0, 0, 205, 0, 0, 206, 0, 0, 206, 0, -1, 442, 0, -1, 441, 0, 0, 208, 0, -1, 443, 0, -1, 210, 0, -1, 212, 0, -1, 214, 0, 0, 215, 0, 0, 216, 0, -1, 218, 0, -1, 220, 0, 0, 221, 0, -1, 223, 0, 0, 224, 0, -1, 226, 0, 0, 227, 0, -1, 229, 0, 0, 231, 0, 0, 232, 0, 0, 233, 0, 0, 234, 0, 0, 235, 0, 0, 236, 0, 0, 237, 0, 0, 238, 0, 0, 239, 0, 0, 240, 0, 0, 241, 0, 0, 242, 0, 0, 243, 0, 0, 244, 0, 0, 246, 0, 0, 247, 0, 0, 248, 0, 0, 249, 0, 0, 250, 0, 0, 251, 0, 0, 252, 0, 0, 253, 0, 0, 254, 0, 0, 255, 0, 0, 256, 0, 0, 257, 0, 0, 258, 0, 0, 259, 0, 0, 261, 0, 0, 262, 0, 0, 263, 0, 0, 264, 0, 0, 265, 0, 0, 266, 0, 0, 267, 0, 0, 268, 0, 0, 269, 0, 0, 270, 0, 0, 271, 0, 0, 272, 0, 0, 273, 0, 0, 274, 0, 0, 277, 0, 0, 278, 0, 0, 279, 0, 0, 280, 0, 0, 281, 0, 0, 282, 0, 0, 283, 0, 0, 284, 0, 0, 285, 0, 0, 286, 0, 0, 287, 0, 0, 288, 0, 0, 289, 0, 0, 290, 0, 0, 293, 0, 0, 294, 0, 0, 295, 0, 0, 296, 0, 0, 297, 0, 0, 298, 0, 0, 299, 0, 0, 300, 0, 0, 301, 0, 0, 302, 0, 0, 303, 0, 0, 304, 0, 0, 305, 0, 0, 306, 0, 0, 309, 0, 0, 310, 0, 0, 311, 0, 0, 312, 0, 0, 313, 0, 0, 314, 0, 0, 315, 0, 0, 316, 0, 0, 317, 0, 0, 318, 0, 0, 319, 0, 0, 320, 0, 0, 321, 0, 0, 322, 0, 0, 325, 0, 0, 326, 0, 0, 327, 0, 0, 328, 0, 0, 329, 0, 0, 330, 0, 0, 331, 0, 0, 332, 0, 0, 333, 0, 0, 334, 0, 0, 335, 0, 0, 336, 0, 0, 337, 0, 0, 338, 0, 0, 341, 0, 0, 342, 0, 0, 343, 0, 0, 344, 0, 0, 345, 0, 0, 346, 0, 0, 347, 0, 0, 348, 0, 0, 349, 0, 0, 350, 0, 0, 351, 0, 0, 352, 0, 0, 353, 0, 0, 354, 0, 0, 356, 0, 0, 358, 0, 0, 360, 0, 0, 361, 0, 0, 362, 0, 0, 363, 0, 0, 364, 0, -1, 366, 0, -1, 368, 0, 0, 371, 0, 0, 372, 0, 0, 373, 0, 0, 374, 0, 0, 375, 0, -1, 377, 0, 0, 378, 0, 0, 381, 0, 0, 382, 0, 0, 383, 0, 0, 384, 0, 0, 385, 0, 0, 386, 0, 0, 387, 0, 0, 388, 0, 0, 389, 0, 0, 390, 0, 0, 392, 0, -1, 394, 0, 0, 395, 0, 0, 396, 0, 0, 397, 0, 0, 398, 0, 0, 399, 0, 0, 400, 0, 0, 401, 0, 0, 402, 0, 0, 403, 0, 0, 404, 0, 0, 405, 0, 0, 406, 0, 0, 407, 0, 0, 408, 0, 0, 409, 0, 0, 410, 0, 0, 411, 0, 0, 412, 0, 0, 413, 0, -1, 416, 0, 0, 417, 0, -1, 419, 0, 0, 420, 0, -1, 422, 0, -1, 426, 0, -1, 428, 0, -1, 430, 0, 0, 432, 0, 0, 434, 0, -1, 436, 0, -1, 438, 0, -1, 440, 0, 0, 441, 0, 61, 1, 3, 5, 12, 4, 5, 12, 5, 5, 12, 6, 5, 12, 7, 5, 12, 8, 5, 12, 9, 5, 12, 10, 5, 12, 14, 5, 15, 19, 5, 92, 21, 5, 85, 22, 5, 57, 45, 5, 108, 57, 5, 414, 79, 5, 108, 88, 5, 118, 89, 5, 120, 91, 5, 203, 92, 5, 205, 184, 0, 359, 1014], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 210, 212, 214, 218, 220, 223, 226, 229, 230, 260, 276, 292, 308, 324, 340, 184, 186, 188, 190, 182, 366, 368, 369, 369, 369, 369, 377, 379, 380, 394, 416, 419, 422, 428, 430, 436, 438, 440, 442], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, -1, 2, -1, -1, 2, -1, -1, 1, -1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, 2, 1, 2, 1, 2, 1, -1, 1, 2, 1, 6, 1, 2, 9, -1, 6, 2, 1, -1, -2, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 2, 1, 6, 1, 2, 9, -1, 6, 2, 1, -1, -2, -1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 2, 1, 6, 1, 2, 9, -1, -2, 6, 2, 1, -1, -2, -1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 2, 1, 6, 1, 2, 9, -1, -2, 6, 2, 1, -1, -2, -1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 2, 1, 6, 1, 2, 9, -1, -2, 6, 2, 1, -1, -2, -1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 2, 1, 6, 1, 2, 9, -1, -2, 6, 2, 1, -1, -2, -1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1, -1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, -1, 2, -1, 1, 2, 1, 6, 1, 2, 9, -1, -2, 6, 2, 1, -1, -2, -1, 1, -1, 2, -1, 1, -1, 1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, 1, 2, 1, 2, 1, 2, 1, 9, -1, -1, 1, -1, 1, 1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 2, 1, 2, 1, 1, 1, -1, -1, 1, -1, -1, -1, 1, 6, -1, -2, 1, 2, 2, 2, 10, 2, 1, 1, 2, 2, 1, 1, 2, 2, 2, 10, 1, 1, -1, -2, -1, 62, -1, -2, 63, 64, -1, -2, 65, 66, -1, -2, 67, -1, 2, -1, 1, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, -1, 2, -1, 1, 7, -1, 1, 2, -1, 1, -1, -1, 10, -1, -1, -1, 1, -1, 1, 1, 1, -1, -1, 1, -1, -1, -1, 1, -1, 1, 68, 11, 11, 11, 1, 1, 2, 2, 2, 1, 1, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 2, 69, 70, 71, 72, 6, 2, 2, 2, 10, 2, 2, 2, 2, 2, 2, 2, 2], [0, 56, 0, 32, 0, 57, 0, 58, 0, 59, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 29, 0, 60, 0, 0, 61, 0, 1, 0, 1, 0, 14, 0, 15, 0, 16, 1, 10, 1, 6, 1, 6, 0, 17, 4, 18, 11, 19, 4, 7, 7, 20, 1, 21, 22, 23, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 2, 3, 0, 5, 0, 4, 0, 12, 0, 5, 0, 4, 0, 12, 0, 5, 0, 4, 0, 12, 0, 5, 0, 4, 0, 12, 0, 5, 0, 4, 0, 12, 0, 5, 0, 4, 0, 12, 0, 5, 0, 4, 0, 12, 0, 25, 0, 0, 26, 0, 1, 0, 1, 0, 14, 0, 15, 0, 16, 0, 1, 0, 10, 0, 17, 0, 1, 0, 6, 0, 1, 0, 6, 24, 18, 11, 19, 4, 7, 7, 20, 1, 21, 22, 23, 0, 25, 0, 0, 26, 0, 1, 0, 1, 0, 14, 0, 15, 0, 16, 0, 1, 0, 10, 0, 17, 0, 1, 0, 6, 0, 1, 0, 6, 24, 18, 11, 19, 4, 7, 28, 7, 20, 1, 21, 22, 23, 0, 25, 0, 0, 26, 0, 1, 0, 1, 0, 14, 0, 15, 0, 16, 0, 1, 0, 10, 0, 17, 0, 1, 0, 6, 0, 1, 0, 6, 24, 18, 11, 19, 4, 7, 28, 7, 20, 1, 21, 22, 23, 0, 25, 0, 0, 26, 0, 1, 0, 1, 0, 14, 0, 15, 0, 16, 0, 1, 0, 10, 0, 17, 0, 1, 0, 6, 0, 1, 0, 6, 24, 18, 11, 19, 4, 7, 28, 7, 20, 1, 21, 22, 23, 0, 25, 0, 0, 26, 0, 1, 0, 1, 0, 14, 0, 15, 0, 16, 0, 1, 0, 10, 0, 17, 0, 1, 0, 6, 0, 1, 0, 6, 24, 18, 11, 19, 4, 7, 28, 7, 20, 1, 21, 22, 23, 0, 25, 0, 0, 26, 0, 1, 0, 1, 0, 14, 0, 15, 0, 16, 0, 1, 0, 10, 0, 17, 0, 1, 0, 6, 0, 1, 0, 6, 24, 18, 11, 19, 4, 7, 28, 7, 20, 1, 21, 22, 23, 0, 5, 0, 4, 0, 62, 0, 12, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 2, 30, 1, 99, 1, 29, 1, 29, 33, 33, 0, 100, 0, 101, 34, 0, 0, 0, 0, 102, 0, 103, 0, 104, 4, 35, 4, 35, 105, 106, 107, 0, 108, 0, 36, 36, 30, 11, 37, 38, 30, 13, 13, 13, 13, 1, 39, 40, 1, 1, 39, 40, 13, 13, 13, 13, 109, 110, 37, 38, 111, 2, 41, 112, 113, 114, 115, 116, 4, 24, 6, 10, 117, 0, 1, 0, 118, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 5, 0, 8, 0, 9, 0, 0, 119, 42, 0, 120, 0, 0, 43, 0, 0, 0, 121, 0, 122, 123, 124, 0, 0, 5, 0, 0, 0, 125, 0, 126, 127, 128, 129, 130, 32, 41, 1, 1, 1, 27, 27, 27, 27, 27, 27, 131, 1, 2, 2, 2, 2, 34, 1, 31, 31, 31, 132, 133, 1, 1, 1, 43, 44, 44, 42, 1, 1, 1, 1, 1]], [[{"name": "btn_back", "rect": [0, 0, 60, 50], "offset": [0, 0], "originalSize": [60, 50], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [134]], [[{"name": "btn_mo", "rect": [0, 0, 150, 63], "offset": [0, 0], "originalSize": [150, 63], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [135]], [[{"name": "btn_tc", "rect": [0, 0, 150, 63], "offset": [0, 0], "originalSize": [150, 63], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [136]], [[{"name": "anGa", "rect": [0, 0, 164, 164], "offset": [0, 0], "originalSize": [164, 164], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [137]], [[{"name": "tb_money", "rect": [0, 0, 157, 40], "offset": [0, 0], "originalSize": [157, 40], "capInsets": [18, 0, 18, 0]}], [1], 0, [0], [4], [138]], [[[78, "mo_bai", 0.3333333333333333, [{"frame": 0.16666666666666666, "func": "onOpenCard", "params": []}], {"props": {"scaleX": [{"frame": 0, "value": 1}, {"frame": 0.16666666666666666, "value": 0.1}, {"frame": 0.3333333333333333, "value": 1}]}}]], 0, 0, [], [], []], [[{"name": "time_progress", "rect": [0, 0, 74, 74], "offset": [0, 0], "originalSize": [74, 74], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [139]], [[{"name": "icon_Ga", "rect": [0, 0, 33, 33], "offset": [0, 0], "originalSize": [33, 33], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [140]], [[{"name": "btn_dy", "rect": [0, 0, 150, 63], "offset": [0, 0], "originalSize": [150, 63], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [141]], [[{"name": "slice_bg", "rect": [1, 0, 148, 431], "offset": [0, 0], "originalSize": [150, 431], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [142]], [[{"name": "PhatLuong", "rect": [0, 0, 144, 42], "offset": [0, 0], "originalSize": [144, 42], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [143]], [[{"name": "bien_on", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [144]], [[{"name": "btn_nhanB", "rect": [0, 0, 150, 63], "offset": [0, 0], "originalSize": [150, 63], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [145]], [[{"name": "slice_full", "rect": [0, 0, 57, 392], "offset": [0, 0], "originalSize": [57, 392], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [146]], [[{"name": "slice_btn", "rect": [0, 0, 86, 54], "offset": [-0.5, 0], "originalSize": [87, 54], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [4], [147]]]]