[1, ["3dmjiU7fdPKLYzbkyDlUM6", "47de+dPOVJ/o5WRLR5Uv8q"], ["_textureSetter", "spriteFrame"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[{"name": "so_Tr@export", "rect": [0, 0, 92, 104], "offset": [-0.5, 0.5], "originalSize": [93, 105], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "so_Tr@export", 32, {"commonHeight": 40, "fontSize": 32, "atlasName": "<EMAIL>", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 80, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 10, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "35": {"xOffset": 0, "yOffset": 18, "xAdvance": 16, "rect": {"x": 0, "y": 83, "width": 17, "height": 20}}, "46": {"xOffset": 0, "yOffset": 32, "xAdvance": 6, "rect": {"x": 35, "y": 84, "width": 7, "height": 8}}, "48": {"xOffset": 0, "yOffset": 14, "xAdvance": 19, "rect": {"x": 0, "y": 27, "width": 20, "height": 27}}, "49": {"xOffset": 0, "yOffset": 14, "xAdvance": 9, "rect": {"x": 78, "y": 0, "width": 10, "height": 26}}, "50": {"xOffset": 0, "yOffset": 14, "xAdvance": 15, "rect": {"x": 61, "y": 0, "width": 16, "height": 26}}, "51": {"xOffset": 0, "yOffset": 14, "xAdvance": 15, "rect": {"x": 76, "y": 55, "width": 16, "height": 27}}, "52": {"xOffset": 0, "yOffset": 14, "xAdvance": 18, "rect": {"x": 21, "y": 27, "width": 19, "height": 26}}, "53": {"xOffset": 0, "yOffset": 14, "xAdvance": 15, "rect": {"x": 59, "y": 55, "width": 16, "height": 27}}, "54": {"xOffset": 0, "yOffset": 14, "xAdvance": 18, "rect": {"x": 21, "y": 54, "width": 19, "height": 27}}, "55": {"xOffset": 0, "yOffset": 14, "xAdvance": 15, "rect": {"x": 60, "y": 28, "width": 16, "height": 26}}, "56": {"xOffset": 0, "yOffset": 14, "xAdvance": 18, "rect": {"x": 41, "y": 0, "width": 19, "height": 27}}, "57": {"xOffset": 0, "yOffset": 14, "xAdvance": 19, "rect": {"x": 0, "y": 55, "width": 20, "height": 27}}, "75": {"xOffset": 0, "yOffset": 14, "xAdvance": 17, "rect": {"x": 41, "y": 28, "width": 18, "height": 26}}, "77": {"xOffset": 0, "yOffset": 14, "xAdvance": 25, "rect": {"x": 0, "y": 0, "width": 26, "height": 26}}, "99": {"xOffset": 0, "yOffset": 19, "xAdvance": 14, "rect": {"x": 59, "y": 83, "width": 15, "height": 21}}, "104": {"xOffset": 0, "yOffset": 19, "xAdvance": 14, "rect": {"x": 75, "y": 83, "width": 15, "height": 21}}, "105": {"xOffset": 0, "yOffset": 19, "xAdvance": 8, "rect": {"x": 27, "y": 0, "width": 9, "height": 21}}, "111": {"xOffset": 0, "yOffset": 16, "xAdvance": 14, "rect": {"x": 77, "y": 27, "width": 15, "height": 24}}, "116": {"xOffset": 0, "yOffset": 19, "xAdvance": 15, "rect": {"x": 18, "y": 83, "width": 16, "height": 21}}, "117": {"xOffset": 0, "yOffset": 12, "xAdvance": 16, "rect": {"x": 41, "y": 55, "width": 17, "height": 28}}}, "kerningDict": {}}]], 0, 0, [0], [1], [1]]]]