[1, ["ecpdLyjvZBwrvm+cedCcQy", "60lCKF0BFLRJpfGf89TIRK", "3eDfLdztNFJKpGkXf2HbFB", "7a/QZLET9IDreTiBfRn2PD", "0ei89u+IhLvIN0g/FOLKqw", "0b3tMc1rhGyowdqiGNw/Lm", "9ejj4WhqRNUq9/8QOg8gzV", "39Gb8g9UxMeI7Bk9UiHqx7", "b5PzgflLtKtrLSnuLgi+5w", "daZDXxL+pKJLXY/BEY1m59", "d82n49/IVAvIEqsa0xvvk0", "96WjhgjUhBKKEwkJ5u+S5t", "44/Bx9BqhLGIm1AurAtWyF", "d3gGyDqD9KOLwHoxdM+IWa", "8dk9hXLkFHe72iDBHQIVEZ", "aaDrfLDr9PpIie4YDgTW4w"], ["node", "_textureSetter", "_spriteFrame", "_N$file", "_parent", "star", "updateProgressBar", "retryButtonNode", "LabelVersion", "messageLabel", "scene", "_N$skeletonData", "manifestUrl"], [["cc.Node", ["_name", "_id", "_active", "_components", "_parent", "_contentSize", "_trs", "_children"], 0, 9, 1, 5, 7, 12], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "node", "_materials", "_N$file"], -3, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_contentSize", "_trs", "_anchorPoint", "_children"], 2, 1, 2, 5, 7, 5, 2], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_children", "_components", "_contentSize", "_trs"], 1, 1, 2, 9, 5, 7], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["b75b2OsXb1HZ5pzqqHFkeOM", ["node", "messageLabel", "LabelVersion", "retryButtonNode", "updateProgressBar", "star", "manifestUrl"], 3, 1, 1, 1, 1, 1, 1, 6], ["cc.<PERSON><PERSON>", ["node"], 3, 1], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials", "_N$skeletonData"], -1, 1, 3, 6], ["cc.ProgressBar", ["_N$mode", "_N$progress", "node", "_N$barSprite"], 1, 1, 1]], [[4, 2, 3, 4, 1], [0, 0, 4, 3, 6, 2], [5, 0, 2, 3, 4, 5, 6, 2], [3, 0, 1, 2, 3, 4, 2], [7, 0, 1, 3], [0, 0, 1, 7, 3, 5, 6, 3], [0, 0, 4, 2], [0, 0, 2, 4, 3, 5, 6, 3], [0, 0, 4, 3, 5, 6, 2], [0, 0, 4, 3, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 1, 6, 2, 3, 5, 4, 2], [3, 0, 1, 2, 3, 5, 4, 2], [8, 0, 1, 1], [6, 0, 3, 2], [6, 0, 1, 2, 3, 4], [9, 0, 1, 2, 3, 4, 5, 6, 1], [4, 0, 2, 3, 4, 2], [4, 1, 0, 2, 3, 3], [10, 0, 1], [2, 0, 1, 2, 3, 4, 6, 7, 8, 6], [2, 0, 1, 2, 3, 4, 6, 7, 6], [2, 0, 1, 5, 2, 3, 4, 6, 7, 7], [2, 0, 1, 5, 2, 3, 4, 6, 7, 8, 7], [11, 0, 1, 2, 3, 4, 3], [12, 0, 1, 2, 3], [13, 0, 1, 2, 3, 4, 5, 6, 5], [14, 0, 1, 2, 3, 3]], [[[{"name": "bo79 icon", "rect": [5, 9, 494, 491], "offset": [2, -4.5], "originalSize": [500, 500], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [2]], [[[4, "update", null], [5, "<PERSON><PERSON>", "11lapc49lPWa4miCo1FdE3", [[-9, -10, -11, -12, -13, -14, [6, "star", -15]], 1, 1, 1, 1, 1, 1, 4], [[13, -1, [5, 1561, 732]], [14, 45, -2], [16, -8, -7, -6, -5, -4, -3, 17]], [5, 1561, 732], [780.5, 366, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "background-3", 1, [-19, -20, -21], [[17, 0, -16, [5], 6], [19, -17], [15, 45, 1561, 732, -18]], [5, 1040.6666666666667, 488], [0, 0, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [10, "thulai", false, 1, [-23], [[0, -22, [15], 16]], [5, 206, 67], [0, -160, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "version", false, 1, [[20, "0%", 20, false, 1, 1, -24, [8], 9]], [5, 29.95, 50.4], [550, -290, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "loadingBar", 1, [-26], [[0, -25, [11], 12]], [5, 811, 61], [27.959, -255.261, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "progressBar", 5, [-28], [-27], [5, 0, 8], [0, 0, 0.5], [-367.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "New Node", false, [1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Main Camera", 1, [[25, 7, -1, -29]], [0, 0, 568.2858805349479, 0, 0, 0, 1, 1, 1, 1]], [1, "logoone6789", 2, [[26, "effect phao hoa", 0, false, "effect phao hoa", -30, [0], 1]], [-30.09, -280.924, 0, 0, 0, 0, 1, 0.65, 0.65, 1]], [3, "New Label", 2, [-31], [5, 108.88, 40], [-638.343, -303.221, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "2.59.21", 26, false, 1, 1, 10, [2]], [8, "logo-loading", 2, [[0, -32, [3], 4]], [5, 494, 491], [0, 10, 0, 0, 0, 0, 1, 0.36, 0.36, 2.398]], [3, "message", 1, [-33], [5, 38.94, 32.76], [0, -290, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "0%", 26, 26, false, 1, 1, 13, [7]], [12, "bar", 6, [-34], [5, 719, 17.4], [0, 0, 0.5], [-22.4, 6.7, 0, 0, 0, 0, 1, 1, 1, 1]], [18, 3, 0, 15, [10]], [27, 2, 0.5, 6, 16], [9, "label", 3, [[23, "<PERSON><PERSON><PERSON>", 26, 26, false, 1, 1, -35, [13], 14]], [5, 96.05, 32.76]]], 0, [0, 0, 1, 0, 0, 1, 0, 5, 4, 0, 6, 17, 0, 7, 3, 0, 8, 11, 0, 9, 14, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 13, 0, -4, 4, 0, -5, 5, 0, -6, 3, 0, 4, 1, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 10, 0, -3, 12, 0, 0, 3, 0, -1, 18, 0, 0, 4, 0, 0, 5, 0, -1, 6, 0, -1, 17, 0, -1, 15, 0, 0, 8, 0, 0, 9, 0, -1, 11, 0, 0, 12, 0, -1, 14, 0, -1, 16, 0, 0, 18, 0, 10, 7, 1, 4, 7, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 14, 16], [-1, 11, -1, -1, 2, -1, 2, -1, -1, 3, -1, -1, 2, -1, 3, -1, 2, 12, 3, 3, 2], [3, 4, 0, 0, 5, 0, 6, 0, 0, 1, 0, 0, 7, 0, 1, 0, 8, 9, 10, 1, 11]], [[{"name": "vien@2x", "rect": [0, 0, 811, 61], "offset": [0, 0], "originalSize": [811, 61], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [12]], [[{"name": "loading", "rect": [2, 2, 723, 18], "offset": [0.5, -0.5], "originalSize": [726, 21], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [13]], [[{"name": "bg_login", "rect": [0, 0, 1560, 725], "offset": [0, 0], "originalSize": [1560, 725], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [14]], [[{"name": "boderDangNhap", "rect": [0, 0, 206, 67], "offset": [0, 0], "originalSize": [206, 67], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [15]]]]