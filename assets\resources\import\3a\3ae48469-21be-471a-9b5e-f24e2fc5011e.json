[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "62TTC6G+hHCamgiYceJ0w6", "e8zRISlSpJaY0/oMNgi0gY", "7a/QZLET9IDreTiBfRn2PD", "e1NJ8c5BZHI5JcP4RUdG0s", "a4LbrG+m1FiJQK9CcpaToD", "2cWB/vWPRHja3uQTinHH30"], ["node", "_N$file", "_spriteFrame", "_parent", "_defaultClip", "root", "groupUserListView", "lbBet", "lbTime", "lbSTT", "lbBalance", "lbNickName", "lbSId", "avatar", "_N$target", "data"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_enableWrapText", "_N$horizontalAlign", "_N$overflow", "_N$verticalAlign", "_spacingX", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 2, 1, 12, 4, 5, 7, 2], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 2, 1, 5, 9, 5, 5, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_enabled", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -2, 1, 3], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 5, 5], ["fde5fYTdUdLFaHQ7QSWDYdb", ["node"], 3, 1], ["387f5EIxBBIDIXmETs3cA+V", ["node", "avatar", "lbSId", "lbNickName", "lbBalance", "lbSTT"], 3, 1, 1, 1, 1, 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["db826cyWwhIqL5byWshkSo6", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["9617bUH1AJAaoUdxbUXn7QN", ["node", "groupUserListView"], 3, 1, 1]], [[6, 0, 1, 2], [0, 0, 5, 3, 2, 4, 7, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [2, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 3, 4, 2, 5, 6, 9, 10, 8], [3, 0, 1, 3, 4, 2, 7, 9, 10, 7], [0, 0, 5, 3, 2, 4, 2], [1, 2, 0, 1, 3, 4, 5, 4], [5, 1, 2, 1], [8, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [0, 0, 5, 6, 2, 7, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 6, 3, 2, 4, 7, 2], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 8, 7, 2], [2, 0, 1, 2, 3, 6, 4, 7, 5, 2], [2, 0, 1, 2, 3, 4, 7, 5, 2], [2, 0, 1, 2, 3, 6, 4, 5, 2], [4, 0, 1, 6, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [1, 0, 3, 4, 5, 2], [1, 0, 3, 4, 2], [1, 0, 1, 3, 4, 5, 3], [1, 2, 0, 1, 3, 4, 4], [5, 0, 1, 3, 4, 5, 6, 2], [6, 1, 1], [3, 0, 1, 2, 8, 9, 10, 11, 5], [3, 0, 1, 3, 4, 2, 5, 7, 6, 9, 10, 9], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2, 3], [11, 0, 1, 2, 3, 4, 1], [12, 0, 1], [7, 2, 0, 1, 3, 5, 6, 5], [7, 0, 1, 4, 5, 6, 4], [13, 0, 1, 2, 3, 4, 5, 1], [14, 0, 1, 2, 2], [15, 0, 1, 2, 3, 4, 5, 6, 6], [16, 0, 1], [17, 0, 1, 2, 3, 4, 5, 4], [18, 0, 1, 2, 1], [19, 0, 1, 1]], [[9, "dragonTigerGroupUserView"], [10, "dragonTigerGroupUserView", [-5, -6, -7, -8, -9, -10, -11], [[41, -2, [22, 23], 21], [42, -4, -3]], [27, -1]], [2, "title", 1, [-16, -17, -18], [[25, 1, 0, false, -12, [13]], [32, -15, -14, -13, [4, 4278246399], [4, 4294829568]]], [0, "52Ear1Oc9OuLPwqnZKmXiO", 1], [5, 1000, 50], [0, 156.888, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "scrollview", 1, [-22, -23], [[-19, [39, -20], -21], 1, 4, 1], [0, "357bVKchZE6or4/UK5sm+b", 1], [5, 1050, 440], [0, -57, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "layout-nick<PERSON><PERSON>", [-25, -26, -27, -28], [[34, false, 1, 1, 5, -24, [5, 327.4, 50]]], [0, "d8BgRisrhKZpQWfFhG0fBT", 1], [5, 327.4, 50], [-239, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "<PERSON><PERSON>", [4, -35], [[36, -34, -33, -32, -31, -30, -29]], [0, "f9/CLsDX9BkaDV5XoLdbMD", 1], [5, 994, 50]], [2, "btnClose", 1, [-38], [[26, 3, -37, [[31, "9617bUH1AJAaoUdxbUXn7QN", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -36]], [0, "70/jNbXfBII44/QOpCcnc2", 1], [5, 80, 80], [510.115, 276.125, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "black", 100, 1, [[22, 0, -39, [0], 1], [8, -40, [4, 4292269782]]], [0, "55RFaYDwBGObs2OWvk29ht", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "nen popup", 1, [[7, 1, 0, false, -41, [2], 3], [8, -42, [4, 4292269782]]], [0, "c2swagYzRJKazAjCUlfGNQ", 1], [5, 1084, 618]], [2, "title_BXH", 1, [-44], [[30, "default", "animation", 0, false, "animation", -43, [7]]], [0, "08BdTZpAFLmbp956v2dFLB", 1], [5, 270, 89.04], [0, 274.851, 0, 0, 0, 0, 1, 1.5, 1, 1]], [21, "avatar", 4, [[-45, [7, 1, 0, false, -46, [15], 16]], 1, 4], [0, "9cN503P+5J/pJVHo+v4osn", 1], [5, 40, 40], [152, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "view", 3, [-48], [[37, 0, -47, [20]]], [0, "2eNzw2H/ZAL5iULcTbN/zB", 1], [5, 1050, 440]], [16, "content", 11, [[35, 1, 2, 10, -49, [5, 1000, 0]]], [0, "a93vKl9nJGBqwejlpmSXrH", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_content", 1, [[23, 0, -50, [4]]], [0, "5cvrKagatE559KMBDT6ktx", 1], [5, 1020, 520], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Node", 9, [[28, "THÔNG TIN NGƯỜI CHƠI", 18, false, 2, -51, [5], 6]], [0, "1fHV9es2NPyabwuN5PIFxI", 1], [5, 387.88, 22.5], [0, 39.036, 0, 0, 0, 0, 1, 0.7, 1, 1]], [6, "sprite", 6, [[24, 0, false, -52, [8], 9]], [0, "a1S+tiIo5FtpxAeWnuYdk2", 1], [5, 70, 70]], [1, "lbSTT", 2, [[4, "SỐ THỨ TỰ", 15, 48, false, false, 1, 1, -53, [10]]], [0, "baXvwsQsRLQYm5ImUMXEQv", 1], [5, 200, 50], [-360, 18.74, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbNickname", 2, [-54], [0, "0eobPQ2tVCDbC9c7P1U8kr", 1], [5, 200, 50], [12, 18.74, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "TÊN NHÂN VẬT", 15, 48, false, false, 1, 1, 17, [11]], [3, "lbBet", 2, [-55], [0, "c42srZHeBP34xbbfwTR0wI", 1], [5, 200, 50], [330, 18.74, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "SỐ DƯ", 15, 48, false, false, 1, 1, 19, [12]], [12, "temp", 3, [5], [0, "71KBhvOyNCxYNVkL+cqRcu", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lbSTT", 4, [-56], [0, "68kZXIeABM442dKy/bNi/X", 1], [5, 10.8, 28.8], [-123, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "1", 24, 48, false, false, 1, 22, [14]], [33, 10], [17, "lbSID", 4, [-57], [0, "f5f65539lNNKrZlnOse30Y", 1], [4, 4279026733], [5, 44.4, 28.8], [0, 0, 0.5], [177, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "[TQ]", 24, 48, false, false, 1, 25, [17]], [18, "lbNickName", 4, [-58], [0, "4exz2QuwFAIo+Q67RVlL2y", 1], [5, 114, 28.8], [0, 0, 0.5], [226, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "<PERSON><PERSON><PERSON>", 24, 48, false, false, 1, 27, [18]], [19, "lbBalance", 5, [-59], [0, "e7PSYukpBA9ot6SihYdzF2", 1], [4, 4284344318], [5, 200, 30], [333, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "350.000.000", 24, 48, false, false, 1, 1, 1, 29, [19]], [38, false, 0.75, 0.23, null, null, 3, 12], [40, 20, 10, 400, 3, 5, 31]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 32, 0, 0, 1, 0, -1, 7, 0, -2, 8, 0, -3, 13, 0, -4, 9, 0, -5, 6, 0, -6, 2, 0, -7, 3, 0, 0, 2, 0, 7, 20, 0, 8, 18, 0, 0, 2, 0, -1, 16, 0, -2, 17, 0, -3, 19, 0, -1, 31, 0, 0, 3, 0, -3, 32, 0, -1, 21, 0, -2, 11, 0, 0, 4, 0, -1, 22, 0, -2, 10, 0, -3, 25, 0, -4, 27, 0, 9, 23, 0, 10, 30, 0, 11, 28, 0, 12, 26, 0, 13, 24, 0, 0, 5, 0, -2, 29, 0, 14, 6, 0, 0, 6, 0, -1, 15, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 14, 0, -1, 24, 0, 0, 10, 0, 0, 11, 0, -1, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, -1, 18, 0, -1, 20, 0, -1, 23, 0, -1, 26, 0, -1, 28, 0, -1, 30, 0, 15, 1, 4, 3, 5, 5, 3, 21, 59], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 26, 28, 30], [-1, 2, -1, 2, -1, -1, 1, -1, -1, 2, -1, -1, -1, -1, -1, -1, 2, -1, -1, -1, -1, 4, -1, -2, 1, 1, 1, 1], [0, 3, 0, 4, 0, 0, 5, 6, 0, 7, 0, 0, 0, 0, 0, 0, 8, 0, 0, 0, 0, 2, 2, 9, 1, 1, 1, 1]]