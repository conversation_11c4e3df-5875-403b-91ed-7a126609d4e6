[1, ["ecpdLyjvZBwrvm+cedCcQy", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "42O/VjejNCtZatJjR2ydi1", "3eH6k56UpDyqQV8Lnm+mYi", "76kAn0cdRK8ZSsvVqDUwuF", "c1y3UL3AVHoqWPxPdQzt/K", "2cWB/vWPRHja3uQTinHH30", "16GlJC2KtKebuzZ/Gz50oS", "3c1tE1yKFNgqjyjoNg4M7I"], ["node", "_spriteFrame", "_textureSetter", "root", "_N$target", "data", "_defaultClip"], [["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children"], 0, 9, 4, 1, 5, 7, 2], ["cc.Sprite", ["_sizeMode", "_enabled", "_type", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["de56dweua5OT5S1skwvV36R", ["node"], 3, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[7, 0, 1, 2], [0, 0, 5, 3, 4, 6, 2], [4, 0, 2], [0, 0, 8, 3, 4, 2], [0, 0, 5, 8, 3, 4, 6, 7, 2], [0, 0, 2, 5, 3, 4, 6, 7, 3], [0, 0, 1, 5, 8, 3, 4, 6, 7, 3], [0, 0, 5, 3, 4, 6, 7, 2], [0, 0, 1, 5, 3, 4, 6, 7, 3], [5, 0, 1, 2, 1], [6, 0, 1], [3, 0, 1, 2, 3, 4, 5, 2], [3, 1, 6, 1], [8, 0, 1, 2, 3], [1, 0, 4, 5, 6, 2], [1, 1, 2, 0, 4, 5, 6, 4], [1, 2, 0, 3, 4, 5, 6, 4], [1, 4, 5, 1], [1, 1, 4, 5, 6, 2], [1, 3, 4, 5, 6, 2]], [[[[2, "minipokerHelpView"], [3, "minipokerHelpView", [-4, -5, -6, -7, -8], [[9, -2, [12, 13], 11], [10, -3]], [0, "a8upMNcAJHprvEkGP9etRM", -1]], [4, "btnClose", 1, [-11], [[11, 3, -10, [[13, "de56dweua5OT5S1skwvV36R", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -9]], [0, "b5kyTUcvdLOL66HyWEcbKZ", 1], [5, 80, 80], [507.4, 289.1, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "black", 100, 1, [[14, 0, -12, [0], 1], [12, -13, [4, 4292269782]]], [0, "61BKXCcxJAR454umZjDtzR", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "tit_popup", false, 1, [-15], [[15, false, 1, 0, -14, [5], 6]], [0, "8aM9xcPVNJHYmWbEywZx6g", 1], [5, 401, 48], [0, 272, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[16, 1, 0, false, -16, [2], 3]], [0, "42Ol2URaJLvYybiDH7L5TE", 1], [5, 1084, 618]], [7, "hd", 4, [[17, -17, [4]]], [0, "48Pna+cwFCmZYmWHmft+Tf", 1], [5, 213, 38], [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "hd_pic", false, 1, [[18, false, -18, [7], 8]], [0, "5dPhvR68xLV4vzkqxvXGUh", 1], [5, 994, 500], [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 2, [[19, false, -19, [9], 10]], [0, "01r1R1rUlIh5tGaVOEzW6a", 1], [5, 69, 36]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 5, 0, -3, 4, 0, -4, 7, 0, -5, 2, 0, 4, 2, 0, 0, 2, 0, -1, 8, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -1, 6, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 5, 1, 19], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, 6, -1, -2], [0, 2, 0, 3, 0, 0, 4, 0, 5, 0, 6, 1, 1, 7]], [[{"name": "luatCHoiMiniPoker", "rect": [0, 0, 1195, 671], "offset": [0, 0], "originalSize": [1195, 671], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [8]], [[{"name": "huong dan_all", "rect": [0, 0, 994, 500], "offset": [0, 0], "originalSize": [994, 500], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [9]]]]