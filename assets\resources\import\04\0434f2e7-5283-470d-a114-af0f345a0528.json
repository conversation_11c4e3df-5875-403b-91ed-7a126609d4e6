[1, ["082tnR0PxL3oAF2m3CFcRt"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "p1k_ani", "\np1k_ani.png\nsize: 595,595\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nlight3\n  rotate: false\n  xy: 2, 318\n  size: 364, 275\n  orig: 364, 275\n  offset: 0, 0\n  index: -1\np_2\n  rotate: true\n  xy: 2, 2\n  size: 314, 324\n  orig: 314, 324\n  offset: 0, 0\n  index: -1\n", ["p1k_ani.png"], {"skeleton": {"hash": "8s6aJPxvQClOjKEAh6OfUZVDuHo", "spine": "3.6.53", "width": 364, "height": 374.5}, "bones": [{"name": "root"}, {"name": "light3", "parent": "root", "length": 105.48, "rotation": 132.31, "x": 23.5, "y": 250}, {"name": "p_2", "parent": "root", "x": -14.5, "y": 108}, {"name": "p_3", "parent": "p_2", "length": 168.61, "rotation": 99.9, "x": -47.39, "y": 18.43}, {"name": "p_4", "parent": "p_2", "length": 72, "rotation": 86.63, "x": 34.15, "y": 122.32}, {"name": "p_5", "parent": "p_2", "length": 77.58, "rotation": 65.63, "x": 122.33, "y": 93.93}, {"name": "p_6", "parent": "p_2", "length": 44.43, "rotation": -133.9, "x": -26.85, "y": -48.01}, {"name": "p_7", "parent": "p_2", "length": 37.84, "rotation": -65.48, "x": 22.07, "y": -56.47}], "slots": [{"name": "light3", "bone": "light3", "attachment": "light3"}, {"name": "p_2", "bone": "p_2", "attachment": "p_2"}], "skins": {"default": {"light3": {"light3": {"x": -15, "y": 2.83, "rotation": -132.31, "width": 364, "height": 275}}, "p_2": {"p_2": {"type": "mesh", "hull": 4, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [3, 2, 196, -108, 0.05393, 5, -153.54, -150.42, 0.30567, 7, 119.07, 136.86, 0.6404, 2, 3, -112.4, 91.3, 0.06455, 6, 106.42, -24.08, 0.93545, 1, 3, 206.77, 35.59, 1, 2, 4, 103.03, -156.07, 0.00205, 5, 141.59, -16.73, 0.99795]}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "p_2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "light3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1, "x": 0.866, "y": 0.866}, {"time": 2, "x": 1.086, "y": 1.086}, {"time": 3, "x": 1, "y": 1}]}, "p_3": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": 5}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "p_4": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "p_5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": 4.87, "y": 10.76}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "p_6": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": -1.98}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}, "p_7": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5, "angle": 5.22}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 1.5, "x": 32.38, "y": -6.82}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]