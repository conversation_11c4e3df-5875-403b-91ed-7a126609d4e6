[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "6fgBCSDDdPMInvyNlggls2", "24xd2Xl+xHVZeWwPN10Wzf", "08iyQNo3JBW5bLGk07dtfI", "30SFO9f3xCKLc2nqMMpbAg", "ccyx5BnPNOdqUU1Ffe2FNF", "0bH0vFZ/tA4aBA017o6pgg", "e7hGggnrBElK+iKLz5LXN6", "05LgaxFeBAeLJ8ACj0ZZ+S", "76idLiVk9CWaSmqgVAAxwY", "13mvzB/HJFfoaL+PVlmDhi", "d68OxEfjlPQpqnGXWBUW9H", "7aNc4tYvpM+a0EdyqtDpWU", "80uDVB/o5CSJ3RSpZOJCbe", "3dWbidMjtMJbw5JxGzAh8z", "31VgVlLlFDyLAYrTgVJkEN", "2es/UAgjhOPrHDMeO6nEFC", "b9OTpbyadKlK6AYXZAKBCS", "453apgR3dB07HonQvOjD00", "f9LXxCHNxC4aeUB3e5nqWL", "61fWfnAr5BnLdxBSFjSIvb", "02GEcjhIlFMK5TZ24P1Brx", "fdNoodJKVLj4dF1TLppv2g", "b1KRr+snVI4ILgmkzw/fOX", "18B/2e6V1HoqgAAAQCXnhK", "a9VpD0DP5LJYQPXITZq+uj", "1ewsTTeZRBbL9DYfWsJfc7", "2a27zPk91Bf7iAxuxCbBAq", "78syxAT1pPY7sxIWu/LEol", "c1y3UL3AVHoqWPxPdQzt/K", "43FgFyEmhMwp0gdhTuTevT", "a4gZnE1bZKGbBiv0z3g3yu", "a856OrGNNLtL4gTmWx+vyx", "5aFaVAKepOVoF397CY8Xyr", "b8EW9snbRCFZGQOZaztrs7", "0fUrWdkHVBl4gRYvzgcqao", "d9csZTfLtEZIOWofc/YBA5", "e8urEIeU1K1rVEmds9rCjZ", "15Qd8NgURNPK4zsPfg2aPF", "2cWB/vWPRHja3uQTinHH30", "b439qzgolDFJ3h/DI0FWJ8", "cdbLn3EjlPNaJD6cQIOF1X", "d1Gcgc3T5KL4aM9uLgQTgf"], ["node", "_spriteFrame", "_N$file", "_parent", "checkMark", "_textureSetter", "root", "lbTotalXiu", "lbTotalTai", "btnBack", "btnNext", "pageView", "TaiXiuSicboGraphDice3View", "TaiXiuSicboGraphDiceSumView", "TaiXiuSicboGraphCatCauView", "TaiXiuSicboGraph100View", "_N$target", "_N$content", "data", "lbResult", "lbSessionID", "toggleDiceSum", "toggleDice3", "toggleDice2", "toggleDice1", "_defaultClip"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_components", "_trs", "_children", "_parent", "_anchorPoint", "_color"], 0, 4, 5, 9, 7, 2, 1, 5, 5], ["cc.Sprite", ["_isTrimmedMode", "_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint", "_color"], 1, 1, 2, 4, 5, 7, 2, 5, 5], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_enableWrapText", "_lineHeight", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingY", "_enabled", "_N$spacingX", "_N$startAxis", "_N$paddingBottom", "_N$verticalDirection", "_N$horizontalDirection", "node", "_layoutSize"], -6, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["<PERSON><PERSON>", ["vertical", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "_enabled", "node", "_N$content"], -4, 1, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["19451Z/jLpAC7sEEmxz3UU4", ["node", "TaiXiuSicboGraph100View", "TaiXiuSicboGraphCatCauView", "TaiXiuSicboGraphDiceSumView", "TaiXiuSicboGraphDice3View", "pageView", "btnNext", "btnBack", "lbTotalTai", "lbTotalXiu"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Graphics", ["_lineWidth", "node", "_materials", "_strokeColor"], 2, 1, 3, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["7aaf38Tf4RI26cxcU9VSL2q", ["node", "nodeParent", "nodeTaiTemp", "nodeXiuTemp"], 3, 1, 1, 1, 1], ["94529qlD85DTYblej6/iPVU", ["node", "nodeParent", "nodeTaiTemp", "nodeXiuTemp", "sfTaiXiu"], 3, 1, 1, 1, 1, 3], ["454cecb3SVGYKGnFawI7fcS", ["node", "nodeGraphics", "nodeParent", "nodeTaiTemp", "nodeXiuTemp", "sfTaiXiu"], 3, 1, 1, 1, 1, 1, 3], ["cc.Toggle", ["_N$transition", "node", "_N$normalColor", "_N$target", "checkEvents"], 2, 1, 5, 1, 9], ["0aa65a0ZgVP56DBP/Kdrg6N", ["node", "nodeGraphics1", "nodeGraphics2", "nodeGraphics3", "nodeDice1Temp", "nodeDice2Temp", "nodeDice3Temp", "colorDice1", "colorDice2", "colorDice3", "spriteDice"], 3, 1, 1, 1, 1, 1, 1, 1, 5, 5, 5, 3], ["<PERSON>.<PERSON>", ["bounceDuration", "node", "_N$content", "pageEvents"], 2, 1, 1, 9]], [[6, 0, 1, 2], [0, 0, 8, 5, 3, 4, 6, 2], [1, 0, 3, 4, 5, 2], [14, 0, 1, 2, 3], [0, 0, 8, 7, 5, 3, 4, 6, 2], [0, 0, 8, 5, 3, 4, 2], [0, 0, 8, 5, 3, 4, 9, 6, 2], [10, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 6, 2, 3, 4, 7, 9, 7], [2, 0, 2, 7, 3, 4, 5, 6, 2], [2, 0, 2, 3, 4, 5, 2], [13, 0, 1, 2, 3, 2], [1, 3, 4, 5, 1], [1, 1, 3, 4, 5, 2], [1, 0, 3, 4, 2], [16, 0, 1, 1], [20, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 7, 8, 6], [0, 0, 7, 3, 4, 6, 2], [0, 0, 7, 5, 3, 4, 9, 6, 2], [0, 0, 8, 5, 3, 4, 9, 2], [2, 0, 2, 3, 4, 5, 6, 2], [1, 1, 0, 3, 4, 5, 3], [1, 1, 0, 3, 4, 3], [5, 2, 7, 1], [5, 1, 0, 2, 3, 4, 5, 6, 3], [7, 0, 1, 2, 3, 4, 5, 7, 8, 7], [9, 0, 2], [0, 0, 7, 5, 3, 2], [0, 0, 7, 3, 6, 2], [0, 0, 1, 8, 7, 5, 3, 4, 6, 3], [0, 0, 5, 3, 4, 9, 6, 2], [0, 0, 2, 8, 5, 3, 4, 6, 3], [0, 0, 3, 4, 9, 6, 2], [0, 0, 8, 7, 5, 3, 4, 2], [0, 0, 7, 5, 3, 4, 2], [0, 0, 7, 5, 3, 4, 6, 2], [0, 0, 8, 7, 3, 9, 6, 2], [0, 0, 8, 5, 3, 10, 4, 6, 2], [2, 0, 1, 2, 7, 3, 4, 5, 6, 3], [2, 0, 2, 3, 4, 5, 8, 6, 2], [2, 0, 2, 3, 4, 9, 5, 6, 2], [11, 0, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [6, 1, 1], [4, 3, 1, 0, 2, 9, 10, 5], [4, 1, 0, 2, 9, 10, 4], [4, 3, 1, 0, 4, 9, 10, 5], [4, 0, 5, 6, 4, 2, 7, 8, 9, 10, 8], [1, 2, 1, 0, 3, 4, 5, 4], [1, 2, 3, 4, 5, 2], [5, 0, 2, 3, 4, 5, 6, 2], [7, 6, 0, 1, 2, 3, 4, 5, 7, 8, 8], [15, 0, 1], [3, 0, 1, 2, 3, 4, 7, 8, 9, 6], [3, 0, 1, 5, 2, 4, 7, 8, 6], [3, 0, 1, 6, 5, 2, 3, 8, 7], [3, 0, 1, 6, 5, 2, 3, 4, 7, 8, 8], [17, 0, 1, 2, 3, 1], [18, 0, 1, 2, 3, 4, 1], [19, 0, 1, 2, 3, 4, 5, 1], [21, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [22, 0, 1, 2, 3, 2]], [[[{"name": "18x", "rect": [0, 0, 23, 23], "offset": [0, 0], "originalSize": [23, 23], "capInsets": [0, 0, 0, 0]}], [8], 0, [0], [5], [25]], [[[27, "SicboGraphView"], [28, "taiXiuGraphView", [-13, -14, -15, -16, -17, -18, -19], [[42, -2, [104, 105], 103], [43, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3]], [44, -1]], [18, "page_2", [-20, -21, -22, -23, -24, -25, -26, -27, -28, -29], [0, "180if/3X9I26r2zvXfL42M", 1], [5, 1050, 600], [1575, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "temp", [-30, -31, -32, -33, -34, -35, -36], [0, "30IMlsJH1EyITqqnRU+nU3", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "layout-numberBot", false, 2, [-38, -39, -40, -41, -42, -43], [[45, false, 1, 2, 9, -37, [5, 50, 201]]], [0, "f0yymOci9Fhr6RPRL5BGH+", 1], [5, 50, 201], [-365, -118, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "page_1", [-44, -45, -46, -47, -48, -49], [0, "3bmetvDZZCJYF7Pzous+gQ", 1], [5, 1050, 600], [525, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "layout-parentDice1", [-51, -52], [[11, 1, -50, [57], [4, 4294967295]]], [0, "236vpd2ytI4q9cgtfXteHL", 1], [5, 950, 210], [0, 1, 0.5], [474, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraphDice3View", 2, [[[12, -53, [80], 81], -54], 4, 1], [0, "6e6it3wRBIFZ+x5XMsHW35", 1], [5, 1039, 163], [0, -71.833, 0, 0, 0, 0, 1, 0.8, 1, 1]], [19, "content", [5, 2], [[46, 1, 1, 15, -55, [5, 2100, 600]]], [0, "8bQGt5xtNPOqg+pqO6p4Qw", 1], [5, 2100, 600], [0, 0, 0.5], [-524, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "layout-parentDiceSum", [[11, 1, -56, [60], [4, 4294967295]]], [0, "c7cWr+aKRL/7qzm0/lCGAO", 1], [5, 950, 210], [0, 1, 0.5], [474, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnBackSession", 1, [[[22, 0, false, -57, [6], 7], -58], 4, 1], [0, "f7pIk7HpdEqqgZ/du9yeAd", 1], [5, 50, 62], [-535, -13, 0, 0, 0, 0, 1, 1, 1, 0]], [7, "btnNextSession", 1, [[[22, 0, false, -59, [8], 9], -60], 4, 1], [0, "6b8SKSAMlJyavOP2ZdI7IT", 1], [5, 50, 62], [535, -13, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "btnClose", 1, [[13, 0, -61, [10], 11], [51, 3, -63, [[3, "19451Z/jLpAC7sEEmxz3UU4", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -62]], [0, "d555BWhqdOwqXaJVVgCSqx", 1], [5, 100, 50], [538.3, 328.1, 0, 0, 0, 0, 1, 1, 1, 0]], [9, "pageview", 1, [3, -65], [-64], [0, "f141bbdD5Berpy4/Chtz9K", 1], [5, 954, 500], [0, -49, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "dot_Tai", 3, [[2, false, -66, [12], 13]], [0, "d6NlaknBdGvIXf4PumChbu", 1], [5, 21, 21]], [1, "dot_Xiu", 3, [[2, false, -67, [14], 15]], [0, "0bIVe6rz1MRIi7AYH0jVPv", 1], [5, 21, 21], [0, -38, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "scrollview", 5, [-71], [[52, false, false, 0.1, false, 0.23, null, null, -69, -68], [53, -70]], [0, "40Q0dqGURD3aWi7Q2DZHUV", 1], [5, 930, 163], [0, -126, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "TotalTai", 5, [-73, -74], [[13, 0, -72, [47], 48]], [0, "5bVc82IM9IiI3aHHrKzpV7", 1], [5, 110, 33], [-33, 268, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "TotalXiu", 5, [-76, -77], [[13, 0, -75, [51], 52]], [0, "71ZoiEirJF058lF2LZVJM/", 1], [5, 110, 39], [93, 268, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraphDiceSumView", 2, [[[12, -78, [53], 54], -79], 4, 1], [0, "52C5dSde1PjLa67i8fXz5u", 1], [5, 1039, 163], [0, 137.76, 0, 0, 0, 0, 1, 0.8, 1, 1]], [39, "toggle-sumDice", false, 2, [-81, -82], [-80], [0, "15I+Jqkw5KaakFUGXsx9tz", 1], [5, 28, 28], [-259, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "layout-session", 2, [-84, -85], [[47, false, 1, 1, 5, -83, [5, 835.3900000000001, 50]]], [0, "6ewYJJEjlK+Z0fgmwahsUl", 1], [5, 835.3900000000001, 50], [0, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "toggle-dice1", 2, [-87, -88], [-86], [0, "2c5l/9HBZBfrBYEiUF+SL+", 1], [5, 161, 65], [-196, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "toggle-dice2", 2, [-90, -91], [-89], [0, "7bANuUrAxLw6oOf9yjq64X", 1], [5, 161, 65], [10, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "toggle-dice3", 2, [-93, -94], [-92], [0, "7bMxkmc4ZCAJjpN+3DY8Tt", 1], [5, 161, 65], [216, -249, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "black", 100, 1, [[13, 0, -95, [0], 1], [24, -96, [4, 4292269782]]], [0, "6fMEV9KgxOi6M7CBjegh34", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "nen popup", 1, [[49, 1, 0, false, -97, [2], 3], [24, -98, [4, 4292269782]]], [0, "49EMnkmKhL6py+EaO6hM+w", 1], [5, 1180, 710]], [1, "dot_Tai_number", 3, [[2, false, -99, [16], 17]], [0, "27q+0FAatOfaeIdON0l8gr", 1], [5, 23, 23], [0, 74, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "dot_Xiu_number", 3, [[14, false, -100, [18]]], [0, "f0x0xEp8tKiIkzrf7+vwBv", 1], [5, 23, 23], [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "Dice1", 3, [[2, false, -101, [19], 20]], [0, "b0rFsQOIlF35r6VEwVNhCU", 1], [5, 27, 27]], [5, "Dice2", 3, [[2, false, -102, [21], 22]], [0, "95RokRFStHUr+29MzmMdxw", 1], [5, 27, 27]], [5, "Dice3", 3, [[2, false, -103, [23], 24]], [0, "31jBDaiFZBk6KT1DUq+jm9", 1], [5, 27, 27]], [4, "view", 13, [8], [[15, -104, [102]]], [0, "fcP4Uw5XFF7oY+K8DBI+PP", 1], [5, 1050, 600], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraph100View", 5, [[[12, -105, [25], 26], -106], 4, 1], [0, "2dlu4wPfJDgbYzCpyHfc7Y", 1], [5, 954, 151], [0, 126, 0, 0, 0, 0, 1, 1, 1.25, 1]], [1, "layout-parent100", 5, [[48, 3, 1, 6, 25.5, 20, 0, 1, -107, [5, 895, 197]]], [0, "8aXleEb1NNQ4u6gAdLeQeL", 1], [5, 895, 197], [-5, 125, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "txGraphCatCauView", 5, [[[50, 2, -108, [27], 28], -109], 4, 1], [0, "01P4RtrWxIwKordymiIfba", 1], [5, 1039, 163], [0, -126, 0, 0, 0, 0, 1, 0.9, 1, 1]], [33, "layout-parentCatCau", [0, "7ahe1YbOBCqb5ydqyynyTp", 1], [5, 930, 163], [0, 1, 0.5], [472, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "view", 16, [36], [[15, -110, [29]]], [0, "6b0POiuihHHr6aC5qXeG3x", 1], [5, 930, 163]], [35, "view", [9], [[15, -111, [59]]], [0, "555H2o6atNto04Z/Z6Ojzt", 1], [5, 960, 210]], [36, "scrollview-diceSum", [38], [[26, false, 0.1, false, 0.23, null, null, -112, 9]], [0, "83Aq3p1uRJD7O0ZOeuXswJ", 1], [5, 960, 210], [0, 434, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "offset-graph", 2, [39, -113], [0, "8c9uMQpw1GUrJ9bHtNfGyJ", 1], [0, 0.5, 1], [0, -243, 0, 0, 0, 0, 1, 0.88, 0.88, 1]], [4, "scrollview-dice3", 40, [-115], [[26, false, 0.1, false, 0.23, null, null, -114, 6]], [0, "b9703nnnVPJZGlBUBleRCp", 1], [5, 960, 210], [0, 196, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "view", 41, [6], [[15, -116, [58]]], [0, "02MWGQi+dFuI5F+CiNKuig", 1], [5, 960, 210], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "layout-parentDice2", 6, [[11, 1, -117, [55], [4, 4294967295]]], [0, "63neosd+BKIYIZTOQOBmQW", 1], [5, 950, 210], [0, 1, 0.5]], [20, "layout-parentDice3", 6, [[11, 1, -118, [56], [4, 4294967295]]], [0, "31UUgVtB5DvLEMkooBsjEj", 1], [5, 950, 210], [0, 1, 0.5]], [5, "Background", 20, [[23, 0, false, -119, [61]]], [0, "32CzNmTxdMxaFOGf1+BDDi", 1], [5, 36, 36]], [1, "Background", 22, [[2, false, -120, [82], 83]], [0, "f4AdEbn3VCcKZCCZ2ZKtY1", 1], [5, 161, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Background", 23, [[2, false, -121, [85], 86]], [0, "107DiTmYZBZqQYVo5fNaQA", 1], [5, 161, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "Background", 24, [[2, false, -122, [88], 89]], [0, "2b1GW3CXJHHaUVrwGIVrBw", 1], [5, 161, 65], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "title", 1, [[54, "GIẢM 1 SỐ Ở SOI CẦU !", 30, false, 1, 1, -123, [4], 5]], [0, "481KT6MTVEMpD9loqG2lDT", 1], [5, 578.44, 40], [0, 343, 0, 0, 0, 0, 1, 1, 1, 1]], [25, 1.1, 3, 10, [[3, "19451Z/jLpAC7sEEmxz3UU4", "backPageClicked", 1]], [4, 4294967295], [4, 2533359615], 10], [25, 1.1, 3, 11, [[3, "19451Z/jLpAC7sEEmxz3UU4", "nextPageClicked", 1]], [4, 4294967295], [4, 2533359615], 11], [58, 33, 34, 14, 15], [59, 35, 36, 27, 28, [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44]], [1, "lbTitleTai", 17, [[17, "Tài:", 20, false, 1, 1, -124, [45]]], [0, "54R37vjBlHW6DwdlPb9ReW", 1], [5, 33.34, 50.4], [-22, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "lbTotalTai", 17, [-125], [0, "f3XHXBihVGU4DY4rhh4NAC", 1], [5, 19.77, 50.4], [0, 0, 0.5], [8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "23", 16, false, false, 1, 55, [46]], [38, "lbTitleXiu", 18, [[17, "Xỉu:", 20, false, 1, 1, -126, [49]]], [0, "d9czs+S9ZHYbka9YUBg3Sq", 1], [4, 4278190080], [5, 34.46, 50.4], [-14, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "lbTotalXiu", 18, [-127], [0, "35t1sYUgJDopg4qHbGRi1u", 1], [4, 4278190080], [5, 19.77, 50.4], [20, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "34", 16, false, 1, 1, 58, [50]], [60, 19, 9, 9, 14, 15, [65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79]], [16, 3, 20, [4, 4292269782], 45, [[3, "454cecb3SVGYKGnFawI7fcS", "toggleDrawDiceSumClicked", 19]]], [10, "checkmark", 20, [-128], [0, "d4V50o7h9II5nlNdCecoMk", 1], [5, 35, 35]], [23, 0, false, 62, [62]], [56, "<PERSON><PERSON><PERSON> gần đ<PERSON><PERSON> nhất: #123456", 18, 50, false, false, 1, [64]], [21, "lbSession", 21, [64], [0, "93aCZmwGZMta2g7HSTK3Pl", 1], [5, 443.81, 50], [0, 43, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "lbResult", 21, [-129], [0, "41oK/qPLFL9ZGzEvWfKISS", 1], [5, 192.5, 50], [0, 8, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "Tài 14: (0-0-0)", 16, 50, false, false, 1, 1, 66, [63]], [61, 7, 6, 43, 44, 29, 30, 31, [4, 4294901984], [4, 4278585011], [4, 4288282368], [91, 92, 93]], [16, 3, 22, [4, 4292269782], 46, [[3, "0aa65a0ZgVP56DBP/Kdrg6N", "toggleDrawDice1Clicked", 7]]], [10, "checkmark", 22, [-130], [0, "37jBKn3GVHxKlxJwAq8Mwu", 1], [5, 161, 65]], [14, false, 70, [84]], [16, 3, 23, [4, 4292269782], 47, [[3, "0aa65a0ZgVP56DBP/Kdrg6N", "toggleDrawDice2Clicked", 7]]], [10, "checkmark", 23, [-131], [0, "73puLOXvdGGrcqFNUYS702", 1], [5, 161, 65]], [14, false, 73, [87]], [16, 3, 24, [4, 4292269782], 48, [[3, "0aa65a0ZgVP56DBP/Kdrg6N", "toggleDrawDice3Clicked", 7]]], [10, "checkmark", 24, [-132], [0, "78wAT0KYVJiZahTQE4lms5", 1], [5, 161, 65]], [14, false, 76, [90]], [1, "layout-numberTop", 2, [[12, -133, [94], 95]], [0, "f4fe2olC9Nz6Tnk9lGuubE", 1], [5, 21, 386], [-440, 37, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 4, [[8, "6", 20, 46, false, 2, 1, -134, 96]], [0, "6dWd1Yf4BISbd4MjGRb0I5", 1], [5, 12, 23], [0, 1, 0.5], [0, 87.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 4, [[8, "5", 20, 46, false, 2, 1, -135, 97]], [0, "aaIaVtZTlBwIKdxGHYd4uk", 1], [5, 12.5, 23], [0, 1, 0.5], [0, 52.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 4, [[8, "4", 20, 46, false, 2, 1, -136, 98]], [0, "62ABrQRkpKX40bAeyrVvQa", 1], [5, 13, 23], [0, 1, 0.5], [0, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 4, [[8, "3", 20, 46, false, 2, 1, -137, 99]], [0, "29TWkn3M5C3YZgr7ou9yD2", 1], [5, 12, 23], [0, 1, 0.5], [0, -17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 4, [[8, "2", 20, 46, false, 2, 1, -138, 100]], [0, "99nYClREZAZ4AEYPHJCtC/", 1], [5, 12, 23], [0, 1, 0.5], [0, -52.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "label", 4, [[8, "1", 20, 46, false, 2, 1, -139, 101]], [0, "96LBjH1PdOLI/vG3iAfRpI", 1], [5, 9, 23], [0, 1, 0.5], [0, -87.5, 0, 0, 0, 0, 1, 1, 1, 1]], [62, 0.5, 13, 8, [[3, "19451Z/jLpAC7sEEmxz3UU4", "pageEvent", 1]]]], 0, [0, 6, 1, 0, 0, 1, 0, 7, 59, 0, 8, 56, 0, 9, 50, 0, 10, 51, 0, 11, 85, 0, 12, 68, 0, 13, 60, 0, 14, 53, 0, 15, 52, 0, 0, 1, 0, -1, 25, 0, -2, 26, 0, -3, 49, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, -7, 13, 0, -1, 19, 0, -2, 7, 0, -3, 20, 0, -4, 22, 0, -5, 23, 0, -6, 24, 0, -7, 78, 0, -8, 4, 0, -9, 21, 0, -10, 40, 0, -1, 14, 0, -2, 15, 0, -3, 27, 0, -4, 28, 0, -5, 29, 0, -6, 30, 0, -7, 31, 0, 0, 4, 0, -1, 79, 0, -2, 80, 0, -3, 81, 0, -4, 82, 0, -5, 83, 0, -6, 84, 0, -1, 33, 0, -2, 35, 0, -3, 34, 0, -4, 16, 0, -5, 17, 0, -6, 18, 0, 0, 6, 0, -1, 43, 0, -2, 44, 0, 0, 7, 0, -2, 68, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, -2, 50, 0, 0, 11, 0, -2, 51, 0, 0, 12, 0, 16, 12, 0, 0, 12, 0, -1, 85, 0, -2, 32, 0, 0, 14, 0, 0, 15, 0, 17, 36, 0, 0, 16, 0, 0, 16, 0, -1, 37, 0, 0, 17, 0, -1, 54, 0, -2, 55, 0, 0, 18, 0, -1, 57, 0, -2, 58, 0, 0, 19, 0, -2, 60, 0, -1, 61, 0, -1, 45, 0, -2, 62, 0, 0, 21, 0, -1, 65, 0, -2, 66, 0, -1, 69, 0, -1, 46, 0, -2, 70, 0, -1, 72, 0, -1, 47, 0, -2, 73, 0, -1, 75, 0, -1, 48, 0, -2, 76, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, -2, 52, 0, 0, 34, 0, 0, 35, 0, -2, 53, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, -2, 41, 0, 0, 41, 0, -1, 42, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 54, 0, -1, 56, 0, 0, 57, 0, -1, 59, 0, -1, 63, 0, -1, 67, 0, -1, 71, 0, -1, 74, 0, -1, 77, 0, 0, 78, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 18, 1, 2, 3, 8, 3, 3, 13, 5, 3, 8, 6, 3, 42, 8, 3, 32, 9, 3, 38, 36, 3, 37, 38, 3, 39, 39, 3, 40, 60, 19, 67, 60, 20, 64, 60, 21, 61, 61, 4, 63, 64, 0, 65, 68, 22, 75, 68, 23, 72, 68, 24, 69, 69, 4, 71, 72, 4, 74, 75, 4, 77, 139], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 56, 59, 67, 64, 71, 74, 77], [-1, 1, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -1, -1, -1, 1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -2, -3, -1, 1, 2, 2, 2, 2, 2, 2, -1, 25, -1, -2, 2, 2, 2, 2, 1, 1, 1], [0, 26, 0, 27, 0, 3, 0, 28, 0, 29, 0, 30, 0, 31, 0, 32, 0, 4, 0, 0, 6, 0, 7, 0, 8, 0, 33, 0, 5, 0, 9, 10, 11, 12, 13, 14, 15, 4, 16, 17, 18, 19, 20, 21, 22, 0, 0, 0, 34, 0, 0, 0, 35, 0, 5, 2, 2, 2, 0, 0, 2, 0, 0, 0, 0, 9, 10, 11, 12, 13, 14, 15, 4, 16, 17, 18, 19, 20, 21, 22, 0, 5, 0, 36, 0, 0, 37, 0, 0, 38, 0, 6, 7, 8, 0, 39, 1, 1, 1, 1, 1, 1, 0, 23, 23, 40, 24, 24, 3, 3, 41, 42, 43]]]]