[1, ["ecpdLyjvZBwrvm+cedCcQy", "33rUXoynpNhotX2utXZJwA", "23quAc+lZNu7js5xT3Drq9", "8djwU3+UxH6Kphl+zyXBCb", "eeSE8YRL5Mx5TLaBFagdXk", "6f2FhYpUtADrpN4aXnhdqV", "9awKuJe0ZAdYuaoLZYOaoE", "e8oJav7WpL8bY60FRQe+Vq", "99p94ZGIlGMI0qqphFIyH5", "a9VpD0DP5LJYQPXITZq+uj", "adw94Z+hpN57wutNivq8Q5", "2d0GWtSg5FrJDfRT138Wm2", "3dIShOHnVI1op+mDNW9du2", "ecZ2WxCZNK0L/qkvMpMRVd", "a0GXxO5aZA5IqYgdmUGwYZ", "95iGvZP9xCTaRtHJAyQgPU", "b8HhmjhHRKY58r2150z/wE", "b0Iu3tmihP/6oH9RGtybSL", "c3XDXPRD1Os6SFWZfK9QvU", "cfz/yQZBJJiYf4wM94u6vh", "91WWbV6g9BG40SRBIXEexz", "67nszg0uRKLqYL+NbU/8h6", "93fW0Ir+dIuK37nU/SbeuD", "edFJtQzONIdqcgRWPJ8aHt", "06WDwLuiFJf6TJ2i1kuBO0", "9af4YxTO5OFpcNdJvfIU55", "0b6qoyR6RCV5hSKupBJVpG", "c1AgGk9utBvKbav2uV78dH", "daQ9bEiAdKOpC5djJ5NyvM", "3ePZ1j0iJBObSD46vwWe9C", "a2fjjyZbNM9rQOEOHG7luv", "466mV8M/dHhJjwkUQPnf6m", "7bLSy+22pNc5ZsGJNkqU2X", "87UaQBILRCXJ3devflmdzQ", "4bi2cg2UtHZ4jy1BRfAjtg", "63g/cx5QtLsKMTvbWRLRh8", "c0t00ThHRP/LtVdvnns90Z", "f5+uSWs1VKK7Eopoog0m8j", "6a5h44kzdEZLpsX5Jh8O+o", "536EenOihJpYGtUbFbD4ip", "79LpLtKgdDaquFOTOvJzYd", "44cldFLgxHcpKCwxrttz6H", "37XeeBq+JFZb9Y7LQ4Xm6e", "92r5dGZ1FOxaZ95O7P/bmo", "506GqVIk5OHJ8RlzbgZqdx", "37bghh7bBMHIppBECcNyqZ", "ecFICBm9VBZYoW76+uC5NR", "53Kz9aj+9G8JgMhulaLlaZ", "213k6SBvJC9okQXCX5W+li", "efOQ9FWONHzaVmEZMBmnRl", "a7FG+jH3xGZ44mR7VTXzL4", "31BmvZn6lEHp34yH4X9bXK"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "_parent", "_defaultClip", "root", "btnQuickPlay", "lbiWin", "lbRemaining", "lbMultiplier", "lbTime", "nodeResult", "nodePick", "nodeBonus", "nodeStart", "lbResult", "data", "_N$normalSprite", "spriteFrame"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 9, 4, 5, 1, 7, 2, 5], ["cc.Label", ["_string", "_isSystemFontUsed", "_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_enableWrapText", "_lineHeight", "_N$overflow", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_enabled", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 2, 4, 5, 7, 2], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "_N$normalColor", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 5, 9, 5, 5, 1], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["060a6/zuN5GgayVIQsDGq4s", ["node", "nodeStart", "nodeBonus", "nodePick", "nodeResult", "lbTime", "lbMultiplier", "lbRemaining", "lbiWin", "btnQuickPlay"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["bbb57j8bZFCnJBmJ+EWCoBg", ["node", "btnPicks", "lbiPrizes", "lbMultipliers", "spriteBGPrizes", "nodeEffects", "animationPicks", "sfPicks"], 3, 1, 2, 2, 2, 2, 2, 2, 3], ["09052Zq0oVJl59gzr1ZolUq", ["node"], 3, 1], ["7269ege3OpEV4lKd6fET93f", ["node", "lbResult"], 3, 1, 1], ["cc.Layout", ["_enabled", "_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[11, 0, 1, 2], [3, 3, 4, 5, 1], [17, 0, 1], [3, 0, 1, 3, 4, 5, 3], [1, 0, 6, 3, 4, 5, 7, 2], [1, 0, 6, 8, 3, 4, 5, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 3], [1, 0, 1, 6, 3, 4, 5, 7, 3], [6, 0, 1, 2, 4, 5, 6, 7, 3], [7, 1, 2, 1], [1, 0, 1, 3, 4, 5, 9, 7, 3], [4, 0, 2, 7, 3, 4, 5, 6, 2], [4, 0, 1, 2, 7, 3, 4, 5, 6, 3], [5, 0, 2, 3, 4, 5, 6, 2], [3, 0, 3, 4, 5, 2], [3, 3, 4, 1], [2, 0, 3, 1, 4, 2, 8, 6], [7, 0, 1, 2, 3, 2], [8, 0, 1, 2, 3, 4], [1, 0, 2, 6, 3, 4, 5, 3], [2, 0, 3, 1, 4, 2, 8, 10, 6], [8, 0, 1, 3, 3], [1, 0, 1, 6, 8, 3, 4, 5, 7, 3], [1, 0, 6, 3, 4, 5, 9, 7, 2], [6, 2, 3, 1], [6, 2, 4, 3, 1], [2, 0, 5, 1, 2, 8, 9, 10, 5], [2, 0, 5, 1, 2, 8, 9, 5], [9, 0, 2], [1, 0, 8, 3, 4, 5, 2], [1, 0, 1, 6, 8, 3, 4, 5, 3], [1, 0, 6, 3, 4, 5, 2], [1, 0, 6, 8, 3, 4, 5, 9, 7, 2], [4, 0, 2, 3, 4, 5, 6, 2], [5, 0, 2, 3, 4, 5, 7, 6, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 1], [13, 0, 1], [14, 0, 1, 1], [15, 0, 1, 2, 3, 4, 5, 6, 6], [3, 2, 0, 1, 3, 4, 5, 4], [16, 0, 1], [2, 0, 3, 5, 1, 2, 8, 9, 10, 6], [2, 0, 3, 6, 5, 1, 4, 2, 8, 9, 10, 8], [2, 0, 3, 1, 4, 2, 8, 9, 10, 6], [2, 0, 5, 1, 4, 2, 8, 9, 6], [2, 0, 3, 6, 1, 4, 2, 7, 8, 9, 8], [18, 0, 1, 2, 4]], [[[{"name": "Tex1", "rect": [0, 0, 681, 38], "offset": [0, 0], "originalSize": [681, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [14]], [[{"name": "bg_<PERSON>us", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [15]], [[{"name": "fonttienthang1_font", "rect": [0, 0, 130, 144], "offset": [-5, 5], "originalSize": [140, 154], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [16]], [[{"name": "khung_jackpot", "rect": [0, 0, 273, 167], "offset": [0, 0], "originalSize": [273, 167], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [17]], [[{"name": "boxthongbao0", "rect": [0, 0, 400, 208], "offset": [0, 0], "originalSize": [400, 208], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [18]], [[{"name": "name-board-01@2x", "rect": [1, 1, 367, 75], "offset": [0, 0], "originalSize": [369, 77], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [19]], [[{"name": "off_ngoctrai", "rect": [9, 182, 304, 172], "offset": [0.5, -89.5], "originalSize": [321, 357], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [20]], [[{"name": "tex_<PERSON><PERSON>", "rect": [0, 1, 206, 50], "offset": [-0.5, -0.5], "originalSize": [207, 51], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [21]], [[[28, "aquariumBonusGameView"], [29, "aquariumBonusGameView", [-12, -13, -14, -15, -16], [[35, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2]], [0, "6fl0ppGaVK6J1jykS3QEm+", -1], [5, 1280, 720]], [30, "bonusGamePickView", false, 1, [-42, -43, -44, -45, -46, -47, -48, -49], [[36, -41, [-37, -38, -39, -40], [-33, -34, -35, -36], [-29, -30, -31, -32], [-25, -26, -27, -28], [-21, -22, -23, -24], [-17, -18, -19, -20], [87, 88, 89]]], [0, "b1mPc2fbFJzKwTgDGESQWe", 1], [5, 1280, 720]], [22, "bonusGameStartView", false, 1, [-51, -52, -53, -54, -55, -56, -57, -58, -59, -60], [[37, -50]], [0, "a2FzyZJ4FI5YmozDBBjBT9", 1], [5, 1174, 452], [0, 55, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "bonusGameResultView", false, 1, [-63, -64, -65, -66, -67, -68], [[38, -62, -61]], [0, "e3+hEREDhArZp9kohFmFcX", 1], [5, 1174, 452], [0, 3.349, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "layout-pick", 2, [-70, -71, -72, -73], [[39, false, 1, 1, 40, 25, -69, [5, 1240, 200]]], [0, "3dL1S9XpZHLZGaMSaUpIk2", 1], [5, 1240, 200], [52.028, -6.343, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [11, "button", 5, [-75, -76], [-74], [0, "b0ecocl8VIHrr0/R9zRB88", 1], [5, 280, 280], [-480, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg-coin", 0, 6, [-79, -80], [-77, -78], [0, "1d4Y3V599DMbB7NHvtBAZc", 1], [5, 287, 86], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "button", 5, [-82, -83], [-81], [0, "23PBEYnudHeJjo6LVtsQCF", 1], [5, 280, 280], [-160, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg-coin", 0, 8, [-86, -87], [-84, -85], [0, "19sUyGyVlBB5xl6GtX+d1Z", 1], [5, 287, 86], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "button", 5, [-89, -90], [-88], [0, "4fnz5XC/5Hx6M+VZ6CxSiH", 1], [5, 280, 280], [160, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg-coin", 0, 10, [-93, -94], [-91, -92], [0, "4fnGFGvOVL84WUaHzPlzZ2", 1], [5, 287, 86], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "button", 5, [-96, -97], [-95], [0, "03N+RSzTJEFLH0JKy9xXs5", 1], [5, 280, 280], [480, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg-coin", 0, 12, [-100, -101], [-98, -99], [0, "f2EqV35ZlPyL7WnfwUieXQ", 1], [5, 287, 86], [0, -15, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "bacground", 2, [[40, false, 2, false, -102, [24], 25], [24, -103, [4, 4292269782]], [41, -104]], [0, "c6uXavV/VOmra5SDrwcMSF", 1], [5, 1280, 720]], [5, "multiplier", 2, [-106, -107], [[3, 2, false, -105, [30], 31]], [0, "5fF1r5GuFJPbeRBTt0sZdf", 1], [5, 273, 167], [-405, 282, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "timer", 2, [-109, -110], [[3, 2, false, -108, [37], 38]], [0, "dd/svggx5JwLwULYF20bKG", 1], [5, 273, 167], [405, 282, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "remaining", 2, [-113], [[42, "<PERSON><PERSON> lượt mở còn lại:  ", 36, false, false, 1, -111, [40], 41], [2, -112]], [0, "c2u9a5sdFNw6uRQE76WHs4", 1], [5, 316.8, 40], [0, 0, 0.5], [-185, 180, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "balance", 2, [-115, -116], [[14, 2, -114, [46], 47]], [0, "5cJg26mMREa4yt12hQmchT", 1], [5, 369, 77], [-67, -267, 0, 0, 0, 0, 1, 1.5, 1.5, 1]], [10, "bg_effect", false, [[1, -117, [48], 49], [17, true, -118, [51], 50]], [0, "c1aotIyTVAGo2TlaPJobvp", 1], [5, 308, 315], [0, 0.535, 0.48], [-72.865, 1.774, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_effect", false, [[1, -119, [58], 59], [17, true, -120, [61], 60]], [0, "36BZ8nXcBKRamrdzRLeVQE", 1], [5, 308, 315], [0, 0.535, 0.48], [-67.121, -4.537, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_effect", false, [[1, -121, [67], 68], [17, true, -122, [70], 69]], [0, "dbbTvZYHRE2JFNVR/LFR6r", 1], [5, 308, 315], [0, 0.535, 0.48], [-68.576, -8.903, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_effect", false, [[1, -123, [76], 77], [17, true, -124, [79], 78]], [0, "f5+i1r0OxP/qdyhTbhtpTt", 1], [5, 308, 315], [0, 0.535, 0.48], [-70.032, -0.171, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "btnQuickPlay", 2, [[[3, 2, false, -125, [85], 86], -126], 4, 1], [0, "feufIO/VRBAJTZEWrQTlcY", 1], [5, 103, 103], [410.958, -285.912, 0, 0, 0, 0, 1, 2.385, 2.385, 1]], [19, "black", 0, 1, [[14, 0, -127, [0], 1], [24, -128, [4, 4292269782]]], [0, "e19Argo6RMbovbQbLsAnw0", 1], [5, 3000, 3000]], [19, "black", 0, 3, [[14, 0, -129, [4], 5], [25, -130, [[21, "09052Zq0oVJl59gzr1ZolUq", "startClicked", 3]], [4, 4292269782]]], [0, "8drcUoHlBOip3lw/vSAc0g", 1], [5, 3000, 3000]], [23, "lbMultiplier", 15, [[26, "<PERSON><PERSON> số: ", false, false, 1, -131, [26], 27], [2, -132]], [0, "1ds47po4pM/ZWALBAvfisL", 1], [5, 116, 40], [0, 0, 0.5], [-79.6, -25.579, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lbMultiplierVal", 15, [[-133, [9, -134, [29]]], 1, 4], [0, "f5jYWPFSZBU6tmzGgcObYR", 1], [5, 37.5, 40], [67.2, -26.579, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "lbMultiplier", 16, [[26, "<PERSON><PERSON> chơi:  ", false, false, 1, -135, [34], 35], [2, -136]], [0, "09+9qLIaRMZrjsh5YNEIkO", 1], [5, 157, 40], [0, 0, 0.5], [-93.826, -26.837, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lbTime", 16, [[-137, [2, -138]], 1, 4], [0, "7ehCRtzI9CCp+CO3mr3bGe", 1], [5, 37.5, 40], [80.074, -27.837, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "lbRemaining", 17, [[-139, [2, -140]], 1, 4], [0, "02SfAShBlESIZZhlUKiO5F", 1], [5, 37.5, 40], [0, 0, 0.5], [322, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lbWin", 18, [[[43, "1,000,00,000", 22, 90, false, false, 1, 1, -141, [44], 45], -142], 4, 1], [0, "bcuN+OZLlGiZ3CRrxCxHUA", 1], [5, 144.22, 90], [0, 2, 0, 0, 0, 0, 1, 1.906, 1.906, 1.906]], [5, "ngoctrai", 6, [19], [[3, 2, false, -143, [52], 53]], [0, "484b46mOBD9Yv8RSYivp5c", 1], [5, 300, 143], [0, 0, 0, 0, 0, 0, 1, 1.426, 1.426, 1.426]], [6, "labelVal", false, 7, [[[44, "5.000", 50, false, 1, 1, -144, [54], 55], -145], 4, 1], [0, "49wEOxfOdFdaZDowVivNz4", 1], [5, 150, 40], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "labelMultiplier", false, 7, [[-146, [2, -147]], 1, 4], [0, "25Ldu9CRhOToxD04O6UYvB", 1], [5, 67, 40], [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ngoctrai", 8, [20], [[3, 2, false, -148, [62], 63]], [0, "dbR0DK12lNG6279ILxQHdo", 1], [5, 300, 143], [0, 0, 0, 0, 0, 0, 1, 1.456, 1.456, 1.456]], [6, "labelVal", false, 9, [[[20, "5.000", 50, false, 1, 1, -149, 64], -150], 4, 1], [0, "5daQBz5H1MgbD0EI0HzKLK", 1], [5, 150, 62.5], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "labelMultiplier", false, 9, [[-151, [2, -152]], 1, 4], [0, "0cj4pZw/hLIpAaHEh2a7eP", 1], [5, 67, 40], [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ngoctrai", 10, [21], [[3, 2, false, -153, [71], 72]], [0, "72UMHC2FNAlYtxS8uUXV5N", 1], [5, 300, 143], [0, 0, 0, 0, 0, 0, 1, 1.456, 1.456, 1.456]], [6, "labelVal", false, 11, [[[20, "5.000", 50, false, 1, 1, -154, 73], -155], 4, 1], [0, "5eR6H+CoZD8pSnLG2E6SAU", 1], [5, 150, 62.5], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "labelMultiplier", false, 11, [[-156, [2, -157]], 1, 4], [0, "9eN9dkuhxKd66DRFJGKNJ7", 1], [5, 67, 40], [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "ngoctrai", 12, [22], [[3, 2, false, -158, [80], 81]], [0, "d52DANhYhMvJkPBVJRZXs/", 1], [5, 300, 143], [0, 0, 0, 0, 0, 0, 1, 1.456, 1.456, 1.456]], [6, "labelVal", false, 13, [[[20, "5.000", 50, false, 1, 1, -159, 82], -160], 4, 1], [0, "09EwHWl05Cka9HlycZ+Cvv", 1], [5, 150, 62.5], [0, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "labelMultiplier", false, 13, [[-161, [2, -162]], 1, 4], [0, "76q5CSbIVPtJHu/jWIZ0/Y", 1], [5, 67, 40], [-8.8, 22.2, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "black", 100, 4, [[14, 0, -163, [90], 91], [25, -164, [[21, "7269ege3OpEV4lKd6fET93f", "closeClicked", 4]], [4, 4292269782]]], [0, "a9IrEj7vRBEKPyBbxzzRMj", 1], [5, 3000, 3000]], [4, "tayduky_bonusgame_BG0", 1, [[1, -165, [2], 3]], [0, "18I9g25i9Paq9fw9kpnnxX", 1], [5, 800, 369], [0, 0, 0, 0, 0, 0, 1, 2.744, 2.744, 2.744]], [4, "nen popup", 3, [[3, 2, false, -166, [6], 7]], [0, "e8HsmY83RM9pTi4SlnlRad", 1], [5, 400, 208], [0, -24, 0, 0, 0, 0, 1, 3.06, 3.06, 3.06]], [7, "tex_<PERSON><PERSON>", false, 3, [[1, -167, [8], 9]], [0, "1ec77Fv35DN5cj913IJUij", 1], [5, 400, 208], [0, 237, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bg_popup", false, 3, [[3, 2, false, -168, [10], 11]], [0, "7c5IEsyEdMN7Dw8olARDoW", 1], [5, 840, 473], [0, -72, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "Tex1", false, 3, [[1, -169, [12], 13]], [0, "b4M8vZ1KxCj6D68rv/+rv6", 1], [5, 681, 38], [0, 93, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "tex_SanNgoc", false, 3, [[1, -170, [14], 15]], [0, "e8bz33ak5NyJTM63tR2tyM", 1], [5, 321, 90], [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "iteam_Ngoc", 3, [[3, 2, false, -171, [16], 17]], [0, "77lUtBojNHirpMdqwLuxH7", 1], [5, 729, 729], [-108.398, 226.139, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "quat", 3, [[1, -172, [18], 19]], [0, "29L8Bbmu5FCp8v3tOTL4EX", 1], [5, 250, 251], [-114.49, -10.065, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "quat", 3, [[1, -173, [20], 21]], [0, "a8MTCRbO1AB4ZCgoBBg2mK", 1], [5, 250, 251], [349.762, -7.549, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "quat", 3, [[1, -174, [22], 23]], [0, "195jCb3VJEkqG1muqzZjHX", 1], [5, 250, 251], [113.232, -10.065, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "x15", false, false, 1, 27, [28]], [7, "bg_SanNgoc", false, 2, [[1, -175, [32], 33]], [0, "64nqgyAK5KOYBq94dTaIAb", 1], [5, 273, 167], [-3.814, 312.046, 0, 0, 0, 0, 1, 1, 1, 1]], [45, "15", false, false, 1, 1, 29, [36]], [27, "15", false, false, 1, 30, [39]], [4, "textTotalWin", 18, [[3, 2, false, -176, [42], 43]], [0, "78z+FsAhNGUqWrHgj15HOp", 1], [5, 306, 88], [0, 62, 0, 0, 0, 0, 1, 0.66667, 0.66667, 0.66667]], [2, 31], [2, 33], [16, "4", 32, false, 1, 1, 34], [15, 7, [56]], [9, 7, [57]], [8, 1.05, 3, 6, [[18, "bbb57j8bZFCnJBmJ+EWCoBg", "pickClicked", "1", 2]], [4, 4294967295], [4, 4294967295], 6], [2, 36], [16, "4", 32, false, 1, 1, 37], [15, 9, [65]], [9, 9, [66]], [8, 1.05, 3, 8, [[18, "bbb57j8bZFCnJBmJ+EWCoBg", "pickClicked", "2", 2]], [4, 4294967295], [4, 4294967295], 8], [2, 39], [16, "4", 32, false, 1, 1, 40], [15, 11, [74]], [9, 11, [75]], [8, 1.05, 3, 10, [[18, "bbb57j8bZFCnJBmJ+EWCoBg", "pickClicked", "3", 2]], [4, 4294967295], [4, 4294967295], 10], [2, 42], [16, "4", 32, false, 1, 1, 43], [15, 13, [83]], [9, 13, [84]], [8, 1.05, 3, 12, [[18, "bbb57j8bZFCnJBmJ+EWCoBg", "pickClicked", "4", 2]], [4, 4294967295], [4, 4294967295], 12], [8, 1.05, 3, 23, [[21, "060a6/zuN5GgayVIQsDGq4s", "quickPlayClicked", 1]], [4, 4294967295], [4, 4294967295], 23], [7, "nen popup", false, 4, [[3, 2, false, -177, [92], 93]], [0, "5f33d4pCRCZ4slKiIpifBs", 1], [5, 955, 594], [0, -24, 0, 0, 0, 0, 1, 1.255, 1.255, 1.255]], [4, "tex_<PERSON><PERSON>", 4, [[1, -178, [94], 95]], [0, "ebXXCktn9GsrVMoc7vVuAR", 1], [5, 206, 50], [0, 112.569, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg_popup", 4, [[3, 2, false, -179, [96], 97]], [0, "55MLuSgf9FjIhkHWiY0HP4", 1], [5, 299, 145], [0, 14.867, 0, 0, 0, 0, 1, 3.874, 3.874, 3.874]], [4, "lbWin", 4, [[1, -180, [98], 99]], [0, "ddyD8CiylJIqFZXoQRmdbB", 1], [5, 549, 38], [0, 31.393, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "lbWinVal", 4, [-181], [0, "860VtqvRhICIfmeghkR+1b", 1], [5, 700, 156.25], [0, -95, 0, 0, 0, 0, 1, 1.521, 1.521, 1.521]], [46, "1,000,000,000", 50, 100, false, 1, 1, 2, 86, [100]]], 0, [0, 6, 1, 0, 7, 81, 0, 8, 60, 0, 9, 58, 0, 10, 55, 0, 11, 57, 0, 12, 4, 0, 13, 5, 0, 14, 2, 0, 15, 3, 0, 0, 1, 0, -1, 24, 0, -2, 45, 0, -3, 3, 0, -4, 2, 0, -5, 4, 0, -1, 64, 0, -2, 69, 0, -3, 74, 0, -4, 79, 0, -1, 19, 0, -2, 20, 0, -3, 21, 0, -4, 22, 0, -1, 63, 0, -2, 68, 0, -3, 73, 0, -4, 78, 0, -1, 62, 0, -2, 67, 0, -3, 72, 0, -4, 77, 0, -1, 61, 0, -2, 66, 0, -3, 71, 0, -4, 76, 0, -1, 65, 0, -2, 70, 0, -3, 75, 0, -4, 80, 0, 0, 2, 0, -1, 14, 0, -2, 15, 0, -3, 56, 0, -4, 16, 0, -5, 17, 0, -6, 18, 0, -7, 5, 0, -8, 23, 0, 0, 3, 0, -1, 25, 0, -2, 46, 0, -3, 47, 0, -4, 48, 0, -5, 49, 0, -6, 50, 0, -7, 51, 0, -8, 52, 0, -9, 53, 0, -10, 54, 0, 16, 87, 0, 0, 4, 0, -1, 44, 0, -2, 82, 0, -3, 83, 0, -4, 84, 0, -5, 85, 0, -6, 86, 0, 0, 5, 0, -1, 6, 0, -2, 8, 0, -3, 10, 0, -4, 12, 0, -1, 65, 0, -1, 32, 0, -2, 7, 0, -1, 63, 0, -2, 64, 0, -1, 33, 0, -2, 34, 0, -1, 70, 0, -1, 35, 0, -2, 9, 0, -1, 68, 0, -2, 69, 0, -1, 36, 0, -2, 37, 0, -1, 75, 0, -1, 38, 0, -2, 11, 0, -1, 73, 0, -2, 74, 0, -1, 39, 0, -2, 40, 0, -1, 80, 0, -1, 41, 0, -2, 13, 0, -1, 78, 0, -2, 79, 0, -1, 42, 0, -2, 43, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 26, 0, -2, 27, 0, 0, 16, 0, -1, 28, 0, -2, 29, 0, 0, 17, 0, 0, 17, 0, -1, 30, 0, 0, 18, 0, -1, 59, 0, -2, 31, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, -2, 81, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, -1, 55, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, -1, 57, 0, 0, 29, 0, -1, 58, 0, 0, 30, 0, 0, 31, 0, -2, 60, 0, 0, 32, 0, 0, 33, 0, -2, 61, 0, -1, 62, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, -2, 66, 0, -1, 67, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, -2, 71, 0, -1, 72, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, -2, 76, 0, -1, 77, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 56, 0, 0, 59, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, -1, 87, 0, 17, 1, 19, 4, 32, 20, 4, 35, 21, 4, 38, 22, 4, 41, 181], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 57, 58, 62, 63, 67, 68, 72, 73, 77, 78, 81, 87], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 3, -1, -1, -1, 1, -1, 1, -1, 3, -1, -1, 1, -1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 1, 5, -1, -1, 1, -1, 3, -1, -1, -1, 1, 5, -1, -1, 1, 3, -1, -1, -1, 1, 5, -1, -1, 1, 3, -1, -1, -1, 1, 5, -1, -1, 1, 3, -1, -1, -1, 1, -1, -2, -3, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 3, 3, 3, 3, 1, 3, 1, 3, 1, 3, 1, 18, 3], [0, 9, 0, 22, 0, 9, 0, 12, 0, 12, 0, 23, 0, 24, 0, 25, 0, 3, 0, 3, 0, 3, 0, 3, 0, 26, 0, 10, 0, 27, 0, 11, 0, 11, 0, 10, 0, 0, 11, 0, 0, 10, 0, 28, 0, 13, 0, 29, 0, 4, 1, 1, 0, 5, 0, 2, 0, 6, 0, 4, 1, 1, 0, 5, 2, 0, 6, 0, 4, 1, 1, 0, 5, 2, 0, 6, 0, 4, 1, 1, 0, 5, 2, 0, 6, 0, 30, 31, 32, 33, 0, 9, 0, 34, 0, 35, 0, 36, 0, 37, 0, 2, 2, 2, 7, 8, 7, 8, 7, 8, 7, 8, 38, 13]], [[{"name": "ani_bonus", "rect": [7, 10, 288, 123], "offset": [1, 0], "originalSize": [300, 143], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [39]], [[{"name": "bn_NgocTrai", "rect": [0, 0, 321, 357], "offset": [0, 0], "originalSize": [321, 357], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [40]], [[{"name": "bn_he<PERSON><PERSON><PERSON><PERSON>", "rect": [4, 3, 313, 354], "offset": [0, -1.5], "originalSize": [321, 357], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [41]], [[{"name": "quat", "rect": [2, 476, 250, 251], "offset": [-237.5, -237], "originalSize": [729, 729], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [42]], [[{"name": "tayduky_bonusgame_BG0", "rect": [0, 0, 800, 369], "offset": [0, 0], "originalSize": [800, 369], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [43]], [[{"name": "tex_SanNgoc", "rect": [0, 0, 321, 90], "offset": [0, 0.5], "originalSize": [321, 91], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [44]], [[{"name": "buttonsieutoc", "rect": [0, 0, 103, 103], "offset": [0, 0], "originalSize": [103, 103], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [45]], [[{"name": "o<PERSON><PERSON>g", "rect": [0, 0, 299, 145], "offset": [0, 0], "originalSize": [299, 145], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [46]], [[{"name": "textTotalWin", "rect": [0, 0, 306, 88], "offset": [0, 0], "originalSize": [306, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [47]], [[[47, "fonttienthang1_font", 45, {"commonHeight": 45, "fontSize": 45, "atlasName": "fonttienthang1_font.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 80, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 8, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "44": {"xOffset": 0, "yOffset": 31, "xAdvance": 11, "rect": {"x": 0, "y": 104, "width": 10, "height": 17}}, "46": {"xOffset": 0, "yOffset": 30, "xAdvance": 11, "rect": {"x": 20, "y": 104, "width": 10, "height": 15}}, "48": {"xOffset": 0, "yOffset": 3, "xAdvance": 31, "rect": {"x": 0, "y": 0, "width": 30, "height": 42}}, "49": {"xOffset": 0, "yOffset": 4, "xAdvance": 15, "rect": {"x": 75, "y": 0, "width": 14, "height": 41}}, "50": {"xOffset": 0, "yOffset": 4, "xAdvance": 24, "rect": {"x": 106, "y": 52, "width": 23, "height": 41}}, "51": {"xOffset": 0, "yOffset": 4, "xAdvance": 26, "rect": {"x": 40, "y": 0, "width": 25, "height": 41}}, "52": {"xOffset": 0, "yOffset": 3, "xAdvance": 25, "rect": {"x": 106, "y": 0, "width": 24, "height": 42}}, "53": {"xOffset": 0, "yOffset": 4, "xAdvance": 24, "rect": {"x": 104, "y": 103, "width": 23, "height": 41}}, "54": {"xOffset": 0, "yOffset": 3, "xAdvance": 27, "rect": {"x": 0, "y": 52, "width": 26, "height": 42}}, "55": {"xOffset": 0, "yOffset": 4, "xAdvance": 24, "rect": {"x": 71, "y": 103, "width": 23, "height": 41}}, "56": {"xOffset": 0, "yOffset": 3, "xAdvance": 26, "rect": {"x": 71, "y": 51, "width": 25, "height": 42}}, "57": {"xOffset": 0, "yOffset": 3, "xAdvance": 26, "rect": {"x": 36, "y": 52, "width": 25, "height": 42}}}, "kerningDict": {}}]], 0, 0, [0], [19], [48]], [[{"name": "bg_popup", "rect": [0, 0, 840, 473], "offset": [0, 0], "originalSize": [840, 473], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [49]], [[{"name": "bg_effect", "rect": [0, 3, 308, 315], "offset": [0, 0], "originalSize": [308, 321], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [50]], [[{"name": "lbWin", "rect": [0, 0, 549, 38], "offset": [0, 0], "originalSize": [549, 38], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [51]]]]