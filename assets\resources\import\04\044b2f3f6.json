[1, ["825TQ2kU9Ktq1Ncj5HdPmn", "fdNoodJKVLj4dF1TLppv2g", "23seKNKSNBh7R8c+Ob7J7i", "30GKt/CLxOSpqjERAGCtyN", "96cZ/HnUdF3oYw72gclSrl", "98J5/p4CREt44GmKS0OZgt", "978D3gK/RIoJMzQjzClPIO", "8bXWcR/edN15C/fIJtP/eU", "97SZsQitVKi4ztOr2qfjQO", "3ccZe9QfdLUbEpUtiLmEU5", "2eS24sSfxIi5OaBWP4I11p", "b9E1jKhaJIIoX9x4qTX8r8", "dbI35ksvBJO4g1/F33Y6Cd", "02IRn/snxCU6QLv/VfYj+I", "c1u9jtUoFDlbQp3jrsWXnw", "5dOKeytXNLy72xK0l6zooK", "19a83K+uVJYq7M32h6xAhL", "c8wnj0outP7pBlfmf8v+3X", "ee9D6PtApNOoetxzcRfIRn", "38iZ7RsR1BqrQxKl/CykHr", "9fwXzQuMFIk52NGl1dtphk", "17x+Ea0tFLxJ7tYzZmRZKr", "acSlomxKFGB6T1gjNds59P", "f1EftrRcZBoJt/0oTk5wsW", "fccTJvrjxHZptZjxFGQHbW", "a9VpD0DP5LJYQPXITZq+uj", "60DYNDgK1HUah563euLEwQ", "3bQFBKqvJLUqdWikadkbdl", "beMCiu5NlO4phUjVH7CUGD", "ceu6pbEM9En4PxVfs1s8AN", "5dn6XUa7NKHaRBWMTPHiaQ", "c29ikaQr9P9LxbsLnUakcs", "2a8OfT8P9IGqseZ7nBgyAW", "23COJUPDJKq7Rn2yTV1Uw4", "0cb0OOh5JNOYSVHUhhoSCZ", "f31teMD3lJ1asoM8vJmb/+", "0a0SmpDrVJnrs2dshv6pCi", "36FNOspxNH76AmK2KNjJvD", "8e2xS+8lRJDZcz4ndMbdj9", "2eu4BP2KZF6oduChLaLoID", "cf0D3WOD9KVKZ9UJ32lglG", "b7lir85kBFdaGFS4fHWt3e", "74NAZWILdLMoFNUrbQH/iP", "63GK8OvVpCwo0cdSeDs7Sy", "818AjWYppPWK0R97Ff0rpq", "4bUjY6zTROKJrMqCjvSoV/", "efr2VPEttC3p4GrDt0F3Qi", "72wz70p05DMLk5pCS9IpiD", "bdz+rVkQlFC4qZS5v6aFgq", "1aYX5OkzZOfaULfsoM01sF", "de5yMD9cxM4rx/ADjF09l8", "36dZlAxRRLa5Ee6rbXDWPT", "23JUig23RLm7/HopqFiX5+", "67XbKVEiNNS6FSUE1Ry2HT", "a2b4nn1gBC77sYyV5SX1E2", "e9kKBJbDJEQIV8wa08TB/N", "03Gcd799hFLI/1dlNdkv2P", "b02yj+wgNNZqebJOM+zbMr", "c5H1zNoG5FNLT97wLAo1z6", "e0bBlLRahCzL/OtoJ4LI/p", "2cWB/vWPRHja3uQTinHH30", "a6cmKQRV9Mp5CutMWLTKxy", "80pNqeJ2BDjpDrx6v23WXV", "a210TsVhRENo1wOhT9Fxko", "48DzEYxI9Gv6h7I/HTOFSt", "acdG1r8WRApbXSeEWe/D8X", "c2fw7tOyFLV5aY04G37GNG"], ["node", "_spriteFrame", "_N$target", "_textureSetter", "_N$normalSprite", "root", "lbTotalLines", "data", "_defaultClip", "_N$file"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_parent", "_contentSize", "_trs", "_children"], 0, 9, 4, 1, 5, 7, 2], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "_N$pressedColor", "_N$disabledColor", "_N$target", "clickEvents", "_N$normalSprite", "_N$normalColor"], 1, 1, 5, 5, 1, 9, 6, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_spriteFrame"], 1, 1, 6], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_N$spacingY", "_resize", "node", "_layoutSize"], -1, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["a4c84JoE69IlpundA0+AU+n", ["node", "nodeLines", "lbTotalLines"], 3, 1, 2, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "node"], -3, 1]], [[10, 0, 1, 2], [1, 0, 5, 3, 4, 6, 7, 2], [2, 0, 1, 2, 6, 3, 4, 5, 3], [1, 0, 5, 8, 3, 4, 6, 7, 2], [3, 0, 1, 2, 3, 3], [5, 0, 1, 2, 3, 4], [3, 2, 3, 1], [5, 0, 1, 3, 3], [1, 0, 1, 5, 8, 3, 4, 6, 7, 3], [1, 0, 5, 3, 4, 6, 2], [2, 0, 1, 2, 6, 3, 4, 5, 7, 3], [6, 0, 2], [1, 0, 8, 3, 4, 2], [1, 0, 2, 5, 3, 4, 6, 7, 3], [7, 0, 1, 2, 3, 4, 5, 6, 2], [8, 0, 1, 2, 1], [9, 0, 1, 2, 1], [4, 0, 1, 2, 4, 5, 4], [4, 3, 0, 1, 4, 5, 4], [3, 0, 2, 3, 2], [2, 0, 1, 2, 3, 4, 5, 7, 3], [2, 2, 8, 1], [11, 0, 1, 2, 3, 4, 5, 6, 7]], [[[{"name": "9", "rect": [0, 0, 89, 58], "offset": [0, 0], "originalSize": [89, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [2]], [[{"name": "7", "rect": [0, 0, 91, 58], "offset": [0, 0], "originalSize": [91, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [3]], [[{"name": "22", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [4]], [[{"name": "6", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [5]], [[{"name": "25", "rect": [0, 0, 89, 57], "offset": [0, 0], "originalSize": [89, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [6]], [[{"name": "4", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [7]], [[{"name": "12", "rect": [0, 0, 89, 58], "offset": [0, 0], "originalSize": [89, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [8]], [[{"name": "10", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [9]], [[{"name": "24", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [10]], [[{"name": "chon dong", "rect": [0, 0, 573, 330], "offset": [0, 0], "originalSize": [573, 330], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [11]], [[{"name": "18", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [12]], [[{"name": "3", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [13]], [[{"name": "Rounded Rectangle 4", "rect": [0, 0, 620, 418], "offset": [0, 0], "originalSize": [620, 418], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [14]], [[{"name": "16", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [15]], [[{"name": "20", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [16]], [[{"name": "15", "rect": [0, 0, 89, 58], "offset": [0, 0], "originalSize": [89, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [17]], [[{"name": "17", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [18]], [[{"name": "11", "rect": [0, 0, 89, 58], "offset": [0, 0], "originalSize": [89, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [19]], [[{"name": "closs", "rect": [0, 0, 66, 67], "offset": [0, 0], "originalSize": [66, 67], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [20]], [[{"name": "14", "rect": [0, 0, 89, 58], "offset": [0, 0], "originalSize": [89, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [21]], [[{"name": "21", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [22]], [[{"name": "1", "rect": [0, 0, 90, 59], "offset": [0, 0], "originalSize": [90, 59], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [23]], [[{"name": "5", "rect": [0, 0, 89, 58], "offset": [0, 0], "originalSize": [89, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [24]], [[[11, "betLinesView"], [12, "betLinesView", [-30, -31, -32, -33, -34, -35, -36, -37], [[15, -2, [39, 40], 38], [16, -29, [-4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28], -3]], [0, "c9/+Gkff1PAZ1JPvmc2nvX", -1]], [3, "layout-lines", 1, [-39, -40, -41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61, -62, -63], [[17, 3, 25, 7, -38, [5, 570, 350]]], [0, "a03t6+HDpHv5IXmk/JjwRc", 1], [5, 570, 350], [6, -4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "layout-button", 1, [-65, -66, -67, -68], [[18, 1, 1, 3, -64, [5, 549, 60]]], [0, "efIoU8tI1PS5odgPo9NoEn", 1], [5, 549, 60], [-4, -194, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnLine", false, 1, [-72], [[4, 2, false, -69, 36], [10, 1.1, 3, -71, [[7, "a4c84JoE69IlpundA0+AU+n", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -70, 37]], [0, "ecsiiuvthGcZXo6LXg2Uyn", 1], [5, 165, 94], [-435, -313, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-75], [[2, 1.05, 3, -74, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "0", 1]], [4, 4294967295], [4, 4294967295], -73]], [0, "fa/6W51N9INIIoqlm4F/f1", 1], [5, 90, 60], [-240, 145, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-78], [[2, 1.05, 3, -77, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "1", 1]], [4, 4294967295], [4, 4294967295], -76]], [0, "bfdMKBmzxKxZfu4yWnlZka", 1], [5, 90, 60], [-125, 145, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-81], [[2, 1.05, 3, -80, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "2", 1]], [4, 4294967295], [4, 4294967295], -79]], [0, "73PjanN71OXolvusx2whzC", 1], [5, 90, 60], [-10, 145, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-84], [[2, 1.05, 3, -83, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "3", 1]], [4, 4294967295], [4, 4294967295], -82]], [0, "fagmTaEmtIgLYzjP+KTTbe", 1], [5, 90, 60], [105, 145, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-87], [[2, 1.05, 3, -86, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "4", 1]], [4, 4294967295], [4, 4294967295], -85]], [0, "f3SlEwRIVItZTxKd8b2zCu", 1], [5, 90, 60], [220, 145, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-90], [[2, 1.05, 3, -89, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "5", 1]], [4, 4294967295], [4, 4294967295], -88]], [0, "23vo6k10dAnL18HBjQBXxC", 1], [5, 90, 60], [-240, 78, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-93], [[2, 1.05, 3, -92, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "6", 1]], [4, 4294967295], [4, 4294967295], -91]], [0, "5e9cMIV0lCGJZP/PJt2jYX", 1], [5, 90, 60], [-125, 78, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-96], [[2, 1.05, 3, -95, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "7", 1]], [4, 4294967295], [4, 4294967295], -94]], [0, "2fnLGDVvVGjpNao1+hGagi", 1], [5, 90, 60], [-10, 78, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-99], [[2, 1.05, 3, -98, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "8", 1]], [4, 4294967295], [4, 4294967295], -97]], [0, "a6IYQUrRJKv4WcctUonEq2", 1], [5, 90, 60], [105, 78, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-102], [[2, 1.05, 3, -101, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "9", 1]], [4, 4294967295], [4, 4294967295], -100]], [0, "24hLfLzqtL1oDfKi6P1jCZ", 1], [5, 90, 60], [220, 78, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-105], [[2, 1.05, 3, -104, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "10", 1]], [4, 4294967295], [4, 4294967295], -103]], [0, "46U8HrtdxB6LYwPSSHpiMQ", 1], [5, 90, 60], [-240, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-108], [[2, 1.05, 3, -107, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "11", 1]], [4, 4294967295], [4, 4294967295], -106]], [0, "42arRJZM1KibdSMcl8LKHW", 1], [5, 90, 60], [-125, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-111], [[2, 1.05, 3, -110, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "12", 1]], [4, 4294967295], [4, 4294967295], -109]], [0, "f54gAhpHRDgoHelislziL7", 1], [5, 90, 60], [-10, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-114], [[2, 1.05, 3, -113, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "13", 1]], [4, 4294967295], [4, 4294967295], -112]], [0, "ddKrfn6KhB55D5kuSftHn7", 1], [5, 90, 60], [105, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-117], [[2, 1.05, 3, -116, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "14", 1]], [4, 4294967295], [4, 4294967295], -115]], [0, "ddAAKoUW5JbJgNPdCb+9H+", 1], [5, 90, 60], [220, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-120], [[2, 1.05, 3, -119, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "15", 1]], [4, 4294967295], [4, 4294967295], -118]], [0, "f1wnFg++ZLH4iOH4CYKdzV", 1], [5, 90, 60], [-240, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-123], [[2, 1.05, 3, -122, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "16", 1]], [4, 4294967295], [4, 4294967295], -121]], [0, "81PdzHy+RKRaDiXArtKRbg", 1], [5, 90, 60], [-125, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-126], [[2, 1.05, 3, -125, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "17", 1]], [4, 4294967295], [4, 4294967295], -124]], [0, "f5eeh0P+pAubJqaANN/ogd", 1], [5, 90, 60], [-10, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-129], [[2, 1.05, 3, -128, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "18", 1]], [4, 4294967295], [4, 4294967295], -127]], [0, "e1LqtwodlBPa2/Nk0WteYL", 1], [5, 90, 60], [105, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-132], [[2, 1.05, 3, -131, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "19", 1]], [4, 4294967295], [4, 4294967295], -130]], [0, "3bVvVoXFBA95kzTjNVfme6", 1], [5, 90, 60], [220, -56, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-135], [[2, 1.05, 3, -134, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "20", 1]], [4, 4294967295], [4, 4294967295], -133]], [0, "3eKUI/6tpHubUYXPuLZTha", 1], [5, 90, 60], [-240, -123, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-138], [[2, 1.05, 3, -137, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "21", 1]], [4, 4294967295], [4, 4294967295], -136]], [0, "2ecZc/03tLJ7AuLixZ1B2O", 1], [5, 90, 60], [-125, -123, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-141], [[2, 1.05, 3, -140, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "22", 1]], [4, 4294967295], [4, 4294967295], -139]], [0, "c3bhDA07lJZY/Z2vjrP/Rv", 1], [5, 90, 60], [-10, -123, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-144], [[2, 1.05, 3, -143, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "23", 1]], [4, 4294967295], [4, 4294967295], -142]], [0, "2dr0XuRzNBA47A5sRT8E1L", 1], [5, 90, 60], [105, -123, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "button", 2, [-147], [[2, 1.05, 3, -146, [[5, "a4c84JoE69IlpundA0+AU+n", "selectLineClicked", "24", 1]], [4, 4294967295], [4, 4294967295], -145]], [0, "ef9t8g+ZZBaqW4SFZ5OMQB", 1], [5, 90, 60], [220, -123, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnEven", 3, [[6, -148, 28], [2, 1.05, 3, -150, [[7, "a4c84JoE69IlpundA0+AU+n", "selectEvenClicked", 1]], [4, 4294967295], [4, 4294967295], -149]], [0, "c3qwyHHhZC6ZTSTxn8VsCY", 1], [5, 135, 59], [-207, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnOdd", 3, [[6, -151, 29], [2, 1.05, 3, -153, [[7, "a4c84JoE69IlpundA0+AU+n", "selectOddClicked", 1]], [4, 4294967295], [4, 4294967295], -152]], [0, "7cME7wrHdERo647cpiYe54", 1], [5, 135, 59], [-69, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnNone", 3, [[6, -154, 30], [2, 1.05, 3, -156, [[7, "a4c84JoE69IlpundA0+AU+n", "selectNoneClicked", 1]], [4, 4294967295], [4, 4294967295], -155]], [0, "47sP5ks6BAuIFbYhPsC1Po", 1], [5, 135, 59], [69, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnAll", 3, [[6, -157, 31], [2, 1.05, 3, -159, [[7, "a4c84JoE69IlpundA0+AU+n", "selectAllClicked", 1]], [4, 4294967295], [4, 4294967295], -158]], [0, "9cwJnsbK9IBadv5C+YrYlF", 1], [5, 135, 59], [207, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "btnClose", 1, [-162], [[10, 1.1, 3, -161, [[7, "a4c84JoE69IlpundA0+AU+n", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -160, 33]], [0, "47s/c/ptdOK5mTawXikTS0", 1], [5, 80, 80], [318, 187, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "btnContinue", false, 1, [-165], [[20, 1.1, 3, -164, [4, 4294967295], [4, 4294967295], -163, 35]], [0, "aduBPBY4dOfpg7N/Ij6Upv", 1], [5, 180, 80], [0, -281, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[19, 0, -166, 0], [21, -167, [4, 4292269782]]], [0, "5fd79+y/BAAqpeRcMCn7Ut", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 5, [[4, 2, false, -168, 3]], [0, "7ehM4PmBhGj4tJTJTTfM0h", 1], [5, 90, 59], [1.5, -2.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 6, [[4, 2, false, -169, 4]], [0, "b2ps92NeVE9YpnAiIQUgIl", 1], [5, 91, 59], [1, -2.6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 7, [[4, 2, false, -170, 5]], [0, "2dqcOph2tKBbjeqflNFu42", 1], [5, 90, 58], [3.5, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 8, [[4, 2, false, -171, 6]], [0, "d0cewHb3NKW4jREQ23YN16", 1], [5, 89, 58], [4, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 9, [[4, 2, false, -172, 7]], [0, "34pfp5AWJJTbSL9eNCZp23", 1], [5, 90, 58], [1.5, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 10, [[4, 2, false, -173, 8]], [0, "f0Wbum+UJAwZjyEHTioNAX", 1], [5, 90, 58], [1.5, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 11, [[4, 2, false, -174, 9]], [0, "10kd2Z329DCYuoIG6/VtpE", 1], [5, 91, 58], [1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 12, [[4, 2, false, -175, 10]], [0, "6bdRwWtD9KGJytwyzzZUnb", 1], [5, 90, 58], [3.5, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 13, [[4, 2, false, -176, 11]], [0, "32/fHvh15IeqnGDHj/yci7", 1], [5, 89, 58], [2, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 14, [[4, 2, false, -177, 12]], [0, "beRRphqzFO9KXPYawDCGIo", 1], [5, 90, 58], [1.5, 0.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 15, [[4, 2, false, -178, 13]], [0, "fdKFktLxtLiYIhMV9JqRTk", 1], [5, 89, 58], [1, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 16, [[4, 2, false, -179, 14]], [0, "97z+n0aDNGB6j9881YH4We", 1], [5, 89, 58], [1, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 17, [[4, 2, false, -180, 15]], [0, "b0UqIye29KdJP0/TN/R4K9", 1], [5, 90, 58], [3.5, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 18, [[4, 2, false, -181, 16]], [0, "7az8XsA+dKPIc4KnrntSN2", 1], [5, 89, 58], [2, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 19, [[4, 2, false, -182, 17]], [0, "cakkykMUVKe4aFdyxJ12Be", 1], [5, 89, 58], [2, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 20, [[4, 2, false, -183, 18]], [0, "c4ARPSOqdFsKVEbXpf+Mpl", 1], [5, 90, 58], [1.5, -0.1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 21, [[4, 2, false, -184, 19]], [0, "2fkIOt9mBD/bEsA0Hns/39", 1], [5, 90, 58], [1.5, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 22, [[4, 2, false, -185, 20]], [0, "deeGuO/CRAKYhDI17XT6YM", 1], [5, 90, 58], [3.5, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 23, [[4, 2, false, -186, 21]], [0, "15DPhUfyJHErisb3hQ71RK", 1], [5, 90, 58], [1.5, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 24, [[4, 2, false, -187, 22]], [0, "ecskSwCBdDM6Gjc3HS0kK+", 1], [5, 90, 58], [1.5, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 25, [[4, 2, false, -188, 23]], [0, "85jxNhLmlLbZtjcw9ubrNU", 1], [5, 90, 58], [1.5, 0.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 26, [[4, 2, false, -189, 24]], [0, "7awrlHJu5Kt4tVuCBS5KFP", 1], [5, 90, 58], [1.5, 1.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 27, [[4, 2, false, -190, 25]], [0, "1b48W3OHpOJJBP15PE/M73", 1], [5, 90, 58], [3.5, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 28, [[4, 2, false, -191, 26]], [0, "eaP2qRhBFMUIYi0r5L5DPB", 1], [5, 90, 58], [1.5, 1.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 29, [[4, 2, false, -192, 27]], [0, "9aDilUg8VDLo+5ta/lz8to", 1], [5, 89, 57], [2, 1.4, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "nen popup", 1, [[6, -193, 1]], [0, "1fwY/bPplBdIiIvdTWcC5a", 1], [5, 620, 418], [0, -22, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chon dong", 1, [[6, -194, 2]], [0, "4f+a+UCuNBz7daO4ODzAOO", 1], [5, 573, 330], [9, 9.91, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "sprite", 34, [[4, 2, false, -195, 32]], [0, "d2atDKs51BU6oEkSWClC/y", 1], [5, 66, 67]], [9, "sprite", 35, [[4, 2, false, -196, 34]], [0, "5aIesm2gFIyZd1Gedt372g", 1], [5, 186, 73]], [14, "label", 4, [-197], [0, "8f/bk5Y65Me6Gj2HLvm7Zg", 1], [5, 33, 17], [0, 0, 0.5], [22, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "25", 16, 34, false, 1, 1, 66]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 67, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 40, 0, -5, 41, 0, -6, 42, 0, -7, 43, 0, -8, 44, 0, -9, 45, 0, -10, 46, 0, -11, 47, 0, -12, 48, 0, -13, 49, 0, -14, 50, 0, -15, 51, 0, -16, 52, 0, -17, 53, 0, -18, 54, 0, -19, 55, 0, -20, 56, 0, -21, 57, 0, -22, 58, 0, -23, 59, 0, -24, 60, 0, -25, 61, 0, 0, 1, 0, -1, 36, 0, -2, 62, 0, -3, 63, 0, -4, 2, 0, -5, 3, 0, -6, 34, 0, -7, 35, 0, -8, 4, 0, 0, 2, 0, -1, 5, 0, -2, 6, 0, -3, 7, 0, -4, 8, 0, -5, 9, 0, -6, 10, 0, -7, 11, 0, -8, 12, 0, -9, 13, 0, -10, 14, 0, -11, 15, 0, -12, 16, 0, -13, 17, 0, -14, 18, 0, -15, 19, 0, -16, 20, 0, -17, 21, 0, -18, 22, 0, -19, 23, 0, -20, 24, 0, -21, 25, 0, -22, 26, 0, -23, 27, 0, -24, 28, 0, -25, 29, 0, 0, 3, 0, -1, 30, 0, -2, 31, 0, -3, 32, 0, -4, 33, 0, 0, 4, 0, 2, 4, 0, 0, 4, 0, -1, 66, 0, 2, 5, 0, 0, 5, 0, -1, 37, 0, 2, 6, 0, 0, 6, 0, -1, 38, 0, 2, 7, 0, 0, 7, 0, -1, 39, 0, 2, 8, 0, 0, 8, 0, -1, 40, 0, 2, 9, 0, 0, 9, 0, -1, 41, 0, 2, 10, 0, 0, 10, 0, -1, 42, 0, 2, 11, 0, 0, 11, 0, -1, 43, 0, 2, 12, 0, 0, 12, 0, -1, 44, 0, 2, 13, 0, 0, 13, 0, -1, 45, 0, 2, 14, 0, 0, 14, 0, -1, 46, 0, 2, 15, 0, 0, 15, 0, -1, 47, 0, 2, 16, 0, 0, 16, 0, -1, 48, 0, 2, 17, 0, 0, 17, 0, -1, 49, 0, 2, 18, 0, 0, 18, 0, -1, 50, 0, 2, 19, 0, 0, 19, 0, -1, 51, 0, 2, 20, 0, 0, 20, 0, -1, 52, 0, 2, 21, 0, 0, 21, 0, -1, 53, 0, 2, 22, 0, 0, 22, 0, -1, 54, 0, 2, 23, 0, 0, 23, 0, -1, 55, 0, 2, 24, 0, 0, 24, 0, -1, 56, 0, 2, 25, 0, 0, 25, 0, -1, 57, 0, 2, 26, 0, 0, 26, 0, -1, 58, 0, 2, 27, 0, 0, 27, 0, -1, 59, 0, 2, 28, 0, 0, 28, 0, -1, 60, 0, 2, 29, 0, 0, 29, 0, -1, 61, 0, 0, 30, 0, 2, 30, 0, 0, 30, 0, 0, 31, 0, 2, 31, 0, 0, 31, 0, 0, 32, 0, 2, 32, 0, 0, 32, 0, 0, 33, 0, 2, 33, 0, 0, 33, 0, 2, 34, 0, 0, 34, 0, -1, 64, 0, 2, 35, 0, 0, 35, 0, -1, 65, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, -1, 67, 0, 7, 1, 197], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 67], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 4, 1, 4, 8, -1, -2, 9], [25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 0, 58, 0, 59, 0, 1, 1, 60, 61]], [[{"name": "2", "rect": [0, 0, 91, 59], "offset": [0, 0], "originalSize": [91, 59], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [62]], [[{"name": "13", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [63]], [[{"name": "23", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [64]], [[{"name": "19", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [65]], [[{"name": "8", "rect": [0, 0, 90, 58], "offset": [0, 0], "originalSize": [90, 58], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [3], [66]]]]