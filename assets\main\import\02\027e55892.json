[1, ["ecpdLyjvZBwrvm+cedCcQy", "a2tBXzjmRHWIetS1zkxuiC", "7a/QZLET9IDreTiBfRn2PD", "b8HUDj6L9D2Yv7ct+XmEBs", "44/Bx9BqhLGIm1AurAtWyF", "ee6B4NmmBFYLKRKk2CJ59y", "3d3L34eQJDD4KQftTH1iYm", "41cHi73qlJFq3Vl8NGLYlB", "39Gb8g9UxMeI7Bk9UiHqx7", "b1KRr+snVI4ILgmkzw/fOX", "9dIABYtxNGX7bIdVvi+P6f", "23S18zM7tLrJCQcG3K0X6D", "beq11ThlxPXYrw1QMZ2gUQ", "7emhmTnMdFFb9AgbU8BRcy", "caQiZ7+X5DbJ33gYZSfopw", "65zxsnJPlBRI8RL3wJhb51"], ["node", "_N$file", "_textureSetter", "_spriteFrame", "_N$target", "rocketNode", "loadingLabel", "progressBar", "scene", "_parent", "_N$skeletonData"], [["cc.Node", ["_name", "_active", "_id", "_opacity", "_components", "_contentSize", "_parent", "_trs", "_children", "_color"], -1, 9, 5, 1, 7, 2, 5], "cc.SpriteFrame", ["cc.Sprite", ["_isTrimmedMode", "_sizeMode", "_type", "_fillRange", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_enableWrapText", "_lineHeight", "node", "_materials", "_N$file"], -5, 1, 3, 6], ["cc.Node", ["_name", "_parent", "_components", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 2, 5, 7, 5, 5], ["sp.Skeleton", ["_preCacheMode", "premultipliedAlpha", "_animationName", "defaultSkin", "defaultAnimation", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.SceneAsset", ["_name", "asyncLoadAssets"], 1], ["cc.Node", ["_name", "_parent", "_children", "_components", "_contentSize", "_trs"], 2, 1, 2, 12, 5, 7], ["cc.<PERSON>", ["_fitWidth", "node", "_designResolution"], 2, 1, 5], ["2a33dcxF+NBW6IzkrkZ/0RB", ["node", "designResolution"], 3, 1, 5], ["cc.Widget", ["_alignFlags", "node"], 2, 1], ["b81161waG5HGLuSkMbTMgXi", ["node", "progressBar", "loadingLabel", "rocketNode"], 3, 1, 1, 1, 1], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target"], 1, 1, 9, 5, 5, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["cc.LabelOutline", ["node", "_color"], 3, 1, 5], ["cc.Scene", ["_name", "_active", "_children", "_anchorPoint", "_trs"], 1, 2, 5, 7], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["cc.ProgressBar", ["_N$mode", "_N$progress", "node", "_N$barSprite"], 1, 1, 1]], [[3, 0, 1, 6, 2, 3, 4, 8, 10, 7], [0, 0, 6, 4, 5, 7, 2], [0, 0, 1, 6, 8, 4, 5, 7, 3], [0, 0, 3, 6, 4, 9, 5, 7, 3], [0, 0, 6, 4, 5, 2], [2, 1, 0, 4, 6, 3], [2, 4, 5, 1], [12, 0, 1, 2, 3, 4, 5, 6, 3], [13, 0, 1, 2, 3], [6, 0, 1, 3], [0, 0, 2, 8, 4, 5, 7, 3], [0, 0, 6, 8, 4, 5, 2], [0, 0, 6, 8, 4, 5, 7, 2], [0, 0, 1, 6, 4, 5, 7, 3], [0, 0, 6, 4, 7, 2], [7, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 5, 3, 4, 2], [4, 0, 1, 2, 3, 6, 4, 2], [8, 0, 1, 2, 2], [9, 0, 1, 1], [10, 0, 1, 2], [11, 0, 1, 2, 3, 1], [2, 0, 4, 5, 6, 2], [2, 2, 1, 0, 4, 5, 6, 4], [2, 2, 3, 4, 5, 3], [14, 0, 1], [3, 0, 1, 2, 3, 4, 5, 8, 9, 10, 7], [3, 0, 1, 7, 2, 3, 4, 8, 9, 7], [15, 0, 1, 1], [16, 0, 1, 2, 3, 4, 3], [17, 0, 1, 2, 3], [5, 0, 1, 2, 5, 6, 4], [5, 3, 4, 0, 1, 2, 5, 6, 7, 6], [18, 0, 1, 2, 3, 3]], [[[{"name": "vang-btn", "rect": [0, 0, 268, 78], "offset": [0, 0], "originalSize": [268, 78], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [3]], [[{"name": "vien@2x", "rect": [0, 0, 811, 61], "offset": [0, 0], "originalSize": [811, 61], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [4]], [[{"name": "BGcasino", "rect": [0, 0, 735, 397], "offset": [0, 0], "originalSize": [735, 397], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [5]], [[{"name": "loading_bg02", "rect": [0, 0, 761, 23], "offset": [0, 0], "originalSize": [761, 23], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [6]], [[[9, "loading", null], [10, "<PERSON><PERSON>", "f9zJkgZT5EU7EZN1zP7Lyo", [-8, -9, -10, -11, -12, -13, -14, -15], [[18, true, -1, [5, 1398, 786]], [19, -2, [5, 1398, 786]], [20, 45, -3], [21, -7, -6, -5, -4]], [5, 1398, 786], [699, 393, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn-retry", false, 1, [-19, -20], [[5, 2, false, -16, 14], [7, 1.1, 3, -18, [[8, "23d18uKhIlKJaRUKfhtzpsz", "retryClicked", 1]], [4, 4294967295], [4, 4294967295], -17]], [5, 268, 78], [0, -156, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btn-retryCheckV", false, 1, [-24, -25], [[5, 2, false, -21, 17], [7, 1.1, 3, -23, [[8, "23d18uKhIlKJaRUKfhtzpsz", "retryCheckVersionClicked", 1]], [4, 4294967295], [4, 4294967295], -22]], [5, 203, 60], [0, -156, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "progressBar", 1, [-28, -29], [[[22, false, -26, [6], 7], -27], 4, 1], [5, 811, 61], [0, -243, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "bg_lb", 1, [-32], [[23, 1, 0, false, -30, [1], 2], [25, -31]], [5, 1398, 786]], [12, "rocket", 4, [-34], [[6, -33, [5]]], [5, 50, 17], [-280.353, -9.924, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lbProgress", false, 1, [[26, "0%", 44, false, 1, 1, 2, -35, [8], 9], [28, -36, [4, 4278190080]]], [5, 132.93, 68], [0, -287, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "New Node", false, [1], [0, 0, 0], [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Main Camera", 1, [[30, 7, -1, -37]], [0, 0, 568.2858805349479, 0, 0, 0, 1, 1, 1, 1]], [1, "logo", 5, [[31, 0, false, "animation", -38, [0]]], [5, 228.51, 151.93], [0, 0, 0, 0, 0, 0, 1, 1.242, 1.242, 1.242]], [16, "bar", 4, [-39], [4, 4293709824], [5, 761, 23], [-28.806, 7.001000000000001, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 3, 0.2, 11, [3]], [1, "duoi", 6, [[6, -40, [4]]], [5, 98, 66], [-59.943, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [33, 2, 0.2, 4, 12], [1, "loading 2_cut", 1, [[32, "default", "animation", 0, false, "animation", -41, [10], 11]], [5, 228.51, 145.37], [-15.792, 40.178, 0, 0, 0, 0, 1, 3, 3, 1]], [3, "Label", 100, 2, [[0, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -42, 12]], [4, 4278190080], [5, 84.78, 40], [-1, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label", 2, [[0, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -43, 13]], [5, 84.78, 40]], [3, "Label", 100, 3, [[0, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -44, 15]], [4, 4278190080], [5, 84.78, 40], [-1, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Label", 3, [[0, "<PERSON><PERSON><PERSON> lại", 28, false, false, 1, 1, -45, 16]], [5, 84.78, 40]], [17, "lbMessage", 1, [-46], [5, 183, 30], [0, 0.5, 1], [-4.827, -267.616, 0, 0, 0, 0, 1, 1.766, 1.766, 1.766]], [27, "Đang tải game ...", 24, 30, false, 1, 1, 20, [18]]], 0, [0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 6, 0, 6, 21, 0, 7, 14, 0, 0, 1, 0, -1, 9, 0, -2, 5, 0, -3, 4, 0, -4, 7, 0, -5, 15, 0, -6, 2, 0, -7, 3, 0, -8, 20, 0, 0, 2, 0, 4, 2, 0, 0, 2, 0, -1, 16, 0, -2, 17, 0, 0, 3, 0, 4, 3, 0, 0, 3, 0, -1, 18, 0, -2, 19, 0, 0, 4, 0, -2, 14, 0, -1, 11, 0, -2, 6, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, -1, 13, 0, 0, 7, 0, 0, 7, 0, 0, 9, 0, 0, 10, 0, -1, 12, 0, 0, 13, 0, 0, 15, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, -1, 21, 0, 8, 8, 1, 9, 8, 46], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 21], [-1, -1, 3, -1, -1, -1, -1, 3, -1, 1, -1, 10, 1, 1, 3, 1, 1, 3, -1, 3, 1], [2, 0, 7, 0, 0, 0, 0, 8, 0, 9, 2, 10, 1, 1, 11, 1, 1, 12, 0, 13, 14]], [[{"name": "button2", "rect": [5, 2, 192, 58], "offset": [-0.5, -1], "originalSize": [203, 60], "capInsets": [89, 0, 88, 0]}], [1], 0, [0], [2], [15]]]]