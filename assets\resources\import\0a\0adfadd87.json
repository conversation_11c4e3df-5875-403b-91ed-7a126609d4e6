[1, ["e5lYdmO+VHUoLHloqGA9wM", "7cYmLiw+1JSaPd1nlTlgaW"], ["spriteFrame", "_textureSetter"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[[0, "font_T@export", 32, {"commonHeight": 50, "fontSize": 32, "atlasName": "<EMAIL>", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 400, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 20, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "46": {"xOffset": 0, "yOffset": 34, "xAdvance": 8, "rect": {"x": 114, "y": 154, "width": 11, "height": 16}}, "48": {"xOffset": 0, "yOffset": 8, "xAdvance": 26, "rect": {"x": 175, "y": 84, "width": 29, "height": 42}}, "49": {"xOffset": 0, "yOffset": 9, "xAdvance": 14, "rect": {"x": 281, "y": 0, "width": 17, "height": 41}}, "50": {"xOffset": 0, "yOffset": 8, "xAdvance": 25, "rect": {"x": 176, "y": 162, "width": 28, "height": 42}}, "51": {"xOffset": 0, "yOffset": 8, "xAdvance": 25, "rect": {"x": 176, "y": 252, "width": 28, "height": 42}}, "52": {"xOffset": 0, "yOffset": 9, "xAdvance": 27, "rect": {"x": 145, "y": 42, "width": 30, "height": 41}}, "53": {"xOffset": 0, "yOffset": 9, "xAdvance": 26, "rect": {"x": 147, "y": 210, "width": 29, "height": 41}}, "54": {"xOffset": 0, "yOffset": 9, "xAdvance": 26, "rect": {"x": 145, "y": 84, "width": 29, "height": 41}}, "55": {"xOffset": 0, "yOffset": 9, "xAdvance": 23, "rect": {"x": 206, "y": 194, "width": 26, "height": 41}}, "56": {"xOffset": 0, "yOffset": 8, "xAdvance": 25, "rect": {"x": 177, "y": 205, "width": 28, "height": 42}}, "57": {"xOffset": 0, "yOffset": 8, "xAdvance": 26, "rect": {"x": 146, "y": 256, "width": 29, "height": 42}}, "65": {"xOffset": 0, "yOffset": 9, "xAdvance": 35, "rect": {"x": 42, "y": 154, "width": 38, "height": 41}}, "66": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 205, "y": 118, "width": 27, "height": 41}}, "67": {"xOffset": 0, "yOffset": 8, "xAdvance": 36, "rect": {"x": 41, "y": 240, "width": 39, "height": 42}}, "68": {"xOffset": 0, "yOffset": 9, "xAdvance": 31, "rect": {"x": 43, "y": 42, "width": 34, "height": 41}}, "69": {"xOffset": 0, "yOffset": 9, "xAdvance": 22, "rect": {"x": 233, "y": 118, "width": 25, "height": 41}}, "70": {"xOffset": 0, "yOffset": 9, "xAdvance": 20, "rect": {"x": 258, "y": 202, "width": 23, "height": 41}}, "71": {"xOffset": 0, "yOffset": 8, "xAdvance": 38, "rect": {"x": 0, "y": 154, "width": 41, "height": 42}}, "72": {"xOffset": 0, "yOffset": 9, "xAdvance": 27, "rect": {"x": 114, "y": 112, "width": 30, "height": 41}}, "73": {"xOffset": 0, "yOffset": 9, "xAdvance": 8, "rect": {"x": 281, "y": 244, "width": 11, "height": 41}}, "74": {"xOffset": 0, "yOffset": 9, "xAdvance": 22, "rect": {"x": 232, "y": 236, "width": 25, "height": 41}}, "75": {"xOffset": 0, "yOffset": 9, "xAdvance": 26, "rect": {"x": 145, "y": 126, "width": 29, "height": 41}}, "76": {"xOffset": 0, "yOffset": 9, "xAdvance": 21, "rect": {"x": 234, "y": 0, "width": 24, "height": 41}}, "77": {"xOffset": 0, "yOffset": 9, "xAdvance": 37, "rect": {"x": 0, "y": 240, "width": 40, "height": 41}}, "78": {"xOffset": 0, "yOffset": 9, "xAdvance": 29, "rect": {"x": 113, "y": 171, "width": 32, "height": 41}}, "79": {"xOffset": 0, "yOffset": 8, "xAdvance": 38, "rect": {"x": 0, "y": 197, "width": 41, "height": 42}}, "80": {"xOffset": 0, "yOffset": 9, "xAdvance": 25, "rect": {"x": 176, "y": 42, "width": 28, "height": 41}}, "81": {"xOffset": 0, "yOffset": 8, "xAdvance": 39, "rect": {"x": 0, "y": 77, "width": 42, "height": 42}}, "82": {"xOffset": 0, "yOffset": 9, "xAdvance": 26, "rect": {"x": 146, "y": 168, "width": 29, "height": 41}}, "83": {"xOffset": 0, "yOffset": 8, "xAdvance": 23, "rect": {"x": 205, "y": 248, "width": 26, "height": 42}}, "84": {"xOffset": 0, "yOffset": 9, "xAdvance": 22, "rect": {"x": 233, "y": 76, "width": 25, "height": 41}}, "85": {"xOffset": 0, "yOffset": 9, "xAdvance": 26, "rect": {"x": 147, "y": 0, "width": 29, "height": 41}}, "86": {"xOffset": 0, "yOffset": 9, "xAdvance": 34, "rect": {"x": 42, "y": 196, "width": 37, "height": 41}}, "87": {"xOffset": 0, "yOffset": 9, "xAdvance": 44, "rect": {"x": 0, "y": 0, "width": 47, "height": 41}}, "88": {"xOffset": 0, "yOffset": 9, "xAdvance": 30, "rect": {"x": 82, "y": 0, "width": 33, "height": 41}}, "89": {"xOffset": 0, "yOffset": 9, "xAdvance": 29, "rect": {"x": 80, "y": 196, "width": 32, "height": 41}}, "90": {"xOffset": 0, "yOffset": 9, "xAdvance": 24, "rect": {"x": 205, "y": 76, "width": 27, "height": 41}}, "91": {"xOffset": 0, "yOffset": 8, "xAdvance": 14, "rect": {"x": 277, "y": 120, "width": 17, "height": 49}}, "93": {"xOffset": 0, "yOffset": 8, "xAdvance": 14, "rect": {"x": 259, "y": 120, "width": 17, "height": 49}}, "97": {"xOffset": 0, "yOffset": 16, "xAdvance": 30, "rect": {"x": 78, "y": 42, "width": 33, "height": 34}}, "98": {"xOffset": 0, "yOffset": 9, "xAdvance": 30, "rect": {"x": 48, "y": 0, "width": 33, "height": 41}}, "99": {"xOffset": 0, "yOffset": 16, "xAdvance": 29, "rect": {"x": 112, "y": 42, "width": 32, "height": 34}}, "100": {"xOffset": 0, "yOffset": 9, "xAdvance": 30, "rect": {"x": 43, "y": 84, "width": 33, "height": 41}}, "101": {"xOffset": 0, "yOffset": 16, "xAdvance": 29, "rect": {"x": 81, "y": 238, "width": 32, "height": 34}}, "102": {"xOffset": 0, "yOffset": 8, "xAdvance": 18, "rect": {"x": 259, "y": 42, "width": 21, "height": 42}}, "103": {"xOffset": 0, "yOffset": 16, "xAdvance": 29, "rect": {"x": 81, "y": 127, "width": 32, "height": 43}}, "104": {"xOffset": 0, "yOffset": 9, "xAdvance": 25, "rect": {"x": 205, "y": 34, "width": 28, "height": 41}}, "105": {"xOffset": 0, "yOffset": 9, "xAdvance": 8, "rect": {"x": 282, "y": 170, "width": 11, "height": 41}}, "106": {"xOffset": 0, "yOffset": 9, "xAdvance": 12, "rect": {"x": 281, "y": 42, "width": 15, "height": 49}}, "107": {"xOffset": 0, "yOffset": 9, "xAdvance": 22, "rect": {"x": 233, "y": 160, "width": 25, "height": 41}}, "108": {"xOffset": 0, "yOffset": 9, "xAdvance": 8, "rect": {"x": 293, "y": 212, "width": 11, "height": 41}}, "109": {"xOffset": 0, "yOffset": 16, "xAdvance": 39, "rect": {"x": 0, "y": 42, "width": 42, "height": 34}}, "110": {"xOffset": 0, "yOffset": 16, "xAdvance": 26, "rect": {"x": 175, "y": 127, "width": 29, "height": 34}}, "111": {"xOffset": 0, "yOffset": 16, "xAdvance": 29, "rect": {"x": 111, "y": 77, "width": 32, "height": 34}}, "112": {"xOffset": 0, "yOffset": 16, "xAdvance": 30, "rect": {"x": 77, "y": 84, "width": 33, "height": 42}}, "113": {"xOffset": 0, "yOffset": 16, "xAdvance": 29, "rect": {"x": 114, "y": 213, "width": 32, "height": 42}}, "114": {"xOffset": 0, "yOffset": 16, "xAdvance": 15, "rect": {"x": 259, "y": 85, "width": 18, "height": 34}}, "115": {"xOffset": 0, "yOffset": 16, "xAdvance": 19, "rect": {"x": 258, "y": 244, "width": 22, "height": 34}}, "116": {"xOffset": 0, "yOffset": 9, "xAdvance": 18, "rect": {"x": 259, "y": 0, "width": 21, "height": 41}}, "117": {"xOffset": 0, "yOffset": 17, "xAdvance": 25, "rect": {"x": 177, "y": 0, "width": 28, "height": 33}}, "118": {"xOffset": 0, "yOffset": 17, "xAdvance": 28, "rect": {"x": 114, "y": 256, "width": 31, "height": 33}}, "119": {"xOffset": 0, "yOffset": 17, "xAdvance": 39, "rect": {"x": 0, "y": 120, "width": 42, "height": 33}}, "120": {"xOffset": 0, "yOffset": 17, "xAdvance": 24, "rect": {"x": 205, "y": 160, "width": 27, "height": 33}}, "121": {"xOffset": 0, "yOffset": 17, "xAdvance": 27, "rect": {"x": 116, "y": 0, "width": 30, "height": 41}}, "122": {"xOffset": 0, "yOffset": 17, "xAdvance": 22, "rect": {"x": 206, "y": 0, "width": 25, "height": 33}}}, "kerningDict": {}}]], 0, 0, [0], [0], [0]], [[{"name": "font_T@export", "rect": [0, 0, 304, 298], "offset": [-0.5, 0.5], "originalSize": [305, 299], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [1]]]]