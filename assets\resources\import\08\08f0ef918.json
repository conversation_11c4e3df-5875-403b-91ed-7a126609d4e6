[1, ["dbkdZt9VdEzYma8bsGlorI", "48HO0b/ABAWb/bHd84lEu/", "a2igG1YptHs5ZbTy5qSy7X", "98R9YhzctB1aInFJ50vMnv", "ffT852tP1HEKBgTURfbnmx", "6dLfwxYIRO4bY7xceFzLOF", "9fsZYWMbhPDbl0gJfQDbz6", "8592FjhdxLxpGbbmafr/g8", "72faeWHsRKw6VbuFlr3BKj", "55mPnss5tIDbuWkBAOhSNQ", "eeGpL/QIZE4Kqhy65qdOra", "58hG1oSF9MZq1NQcn5eQ0u", "05Wo7uJqtPlL8wC4f4xjsp", "4ebDRhVh5NWIcV4CXKdy8I", "80PoidjShEvYd3jbEbRW8g", "33dphTDk1Lf4R0N9vJnGmK", "34Tdh0UypCY6s4RbdxJ0Fx", "abfudYt6ZJ6rxHlVLRlIpZ", "a6IS4ke0xAfIdP/KakZMEE", "00VlFmL4tPC7W0mM+sCDJQ", "ceA/axcHlA/r0iLXRAIjR+", "2eHnjxB9VAaYQR28+5AF/C", "4fzL/H3BlIW58q3Aidenpk", "faJ73tyW1BdJklkjfWtWEm", "11d/8yZjZJMKnZFyxuN9gz", "4cBE186hZGQL5lW81/6nPY", "cevNljnWJMyKSZ4EMY8llH", "18/rUPNolH4Z2x6roWY1ps", "6aWge4Vs1DSbUsMJi0DYHB", "16qkgkWLhFop/AtltIhLsG", "99Tm/wEUtOq69zsYudgteE", "4crKH31hpH7KHVDqNMPJgH", "28Cc1cCcZOaqwCB7zjPtNc", "6e4V2043lA0JoBaxoZjN3F", "c6fla9nqJIrq6znt/jBHFf", "09iakVrJVHF40qqzmz4teA", "6448AhxfpMQYYkmEe5cdTD", "5as6cPpbZJWYVwXmL20/Cq", "1cpQrJ2/1LHLZQtanVHhMs", "9eK+84OVVJeZVEhwKSiaGy", "69/0P/L2tBDrLXkOJmeEYH", "6cI0f7lXBFpb2sSIKUzTtQ", "ccKL7TXD9BBqbbYYU9PN5X", "d8K9BuleJFiLrdaxRFWQ5L", "e3Tv9xdgxI7ZoSwSNJLjZq", "23PB9HLbJCu4q9vy9jvsOO", "12cPOCAIdHJY3aY+76ojgU", "cf01jUdddC1o54OxmxNML9", "5e0whbWz1CpoVcJhIkOlaB", "e1jcByDN1CxaS+hXS46HOw", "ddkZq5+oROabUi9Pd8hKgq", "33/FRxjr9Cl4kVHg82Gme3", "e1YfJ1yv9NX4kuVnHdeKPL", "44o1g1FB1IuIRL75YKJK4i", "30A0GP1v9AQYP73prqIeEZ"], ["_textureSetter", "BG_check", "borderDiamondBoard", "borderMoneyJackpot", "borderTable", "btn100", "btn100Sellect", "btn10K", "btn10KSellect", "btn1K", "btn1KSellect", "btnChan", "btnChanSellect", "btnChonDong", "btnHuyChonDong", "btnLe", "btnLeSellect", "btnQuay", "btnSet10Dong", "btnSet20Dong", "btnSieutoc", "btnSieutocSellect", "btnTatCa", "btnTatCaSellect", "btnTuquay", "btnTuquaySellect", "button_sieutoc_normal", "button_tuquay_normal", "<PERSON><PERSON><PERSON>", "icBXH", "icBxh", "icClose", "icHoidap", "icI", "icLine1", "icLine10", "icLine11", "icLine12", "icLine13", "icLine14", "icLine15", "icLine16", "icLine17", "icLine18", "icLine19", "icLine2", "icLine20", "icLine3", "icLine4", "icLine5", "icLine6", "icLine7", "icLine8", "icLine9", "txtNumber5"], ["cc.SpriteFrame", ["cc.SpriteAtlas", ["_name", "_spriteFrames"], 2, 11]], [[1, 0, 1, 2]], [[[{"name": "btnSet20Dong", "rect": [381, 708, 145, 59], "offset": [0, 0], "originalSize": [145, 59], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "button_tuquay_normal", "rect": [749, 162, 145, 78], "offset": [0, 0], "originalSize": [145, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "txtNumber5", "rect": [624, 490, 32, 38], "offset": [0, 0], "originalSize": [32, 39], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "btnLe", "rect": [682, 748, 151, 52], "offset": [0, 0], "originalSize": [151, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "BG_check", "rect": [658, 490, 34, 34], "offset": [0, 0], "originalSize": [34, 34], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "btnTatCa", "rect": [681, 802, 151, 52], "offset": [0, 0], "originalSize": [151, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "btnChan", "rect": [528, 802, 151, 52], "offset": [0, 0], "originalSize": [151, 52], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [694, 490, 31, 27], "offset": [0, 0], "originalSize": [31, 27], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "btnSet10Dong", "rect": [528, 626, 145, 59], "offset": [0, 0], "originalSize": [145, 59], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "diamond.plist", [{}, "BG_check", 6, 0, "borderDiamondBoard", 6, 1, "borderMoneyJackpot", 6, 2, "borderTable", 6, 3, "btn100", 6, 4, "btn100Sellect", 6, 5, "btn10K", 6, 6, "btn10KSellect", 6, 7, "btn1K", 6, 8, "btn1KSellect", 6, 9, "btnChan", 6, 10, "btnChanSellect", 6, 11, "btnChonDong", 6, 12, "btnHuyChonDong", 6, 13, "btnLe", 6, 14, "btnLeSellect", 6, 15, "btnQuay", 6, 16, "btnSet10Dong", 6, 17, "btnSet20Dong", 6, 18, "btnSieutoc", 6, 19, "btnSieutocSellect", 6, 20, "btnTatCa", 6, 21, "btnTatCaSellect", 6, 22, "btnTuquay", 6, 23, "btnTuquaySellect", 6, 24, "button_sieutoc_normal", 6, 25, "button_tuquay_normal", 6, 26, "<PERSON><PERSON><PERSON>", 6, 27, "icBXH", 6, 28, "icBxh", 6, 29, "icClose", 6, 30, "icHoidap", 6, 31, "icI", 6, 32, "icLine1", 6, 33, "icLine10", 6, 34, "icLine11", 6, 35, "icLine12", 6, 36, "icLine13", 6, 37, "icLine14", 6, 38, "icLine15", 6, 39, "icLine16", 6, 40, "icLine17", 6, 41, "icLine18", 6, 42, "icLine19", 6, 43, "icLine2", 6, 44, "icLine20", 6, 45, "icLine3", 6, 46, "icLine4", 6, 47, "icLine5", 6, 48, "icLine6", 6, 49, "icLine7", 6, 50, "icLine8", 6, 51, "icLine9", 6, 52, "txtNumber5", 6, 53]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54], [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54]], [[{"name": "button_sieutoc_normal", "rect": [381, 546, 145, 79], "offset": [0, 0], "originalSize": [145, 79], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]]]]