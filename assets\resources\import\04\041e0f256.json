[1, ["ecpdLyjvZBwrvm+cedCcQy", "017Jn3Zv1Ft7hygdjpaSoK", "e6tW9cddpJzJGEf+rPpeFE", "fdNoodJKVLj4dF1TLppv2g", "a9VpD0DP5LJYQPXITZq+uj", "86pE5dBbtEPbVMhKgcg/2S", "50gpb+3PhB2bLW/IqzgUYr", "e1NJ8c5BZHI5JcP4RUdG0s", "9d+PxZkqRKJ7i9c7u2FTVj", "2fJXNQqKlFTahBD/6BNnad", "65rjRcORdDtLdCkQC3pBBz", "2cWB/vWPRHja3uQTinHH30", "38AFSCk3FPqKL++UVYXwUJ", "e0zMyzsQpOR6t7JSb3evTf"], ["node", "_spriteFrame", "_N$file", "_textureSetter", "root", "topList<PERSON>iew", "spriteTop", "lbTotalWin", "lbNickName", "lbSID", "lbRank", "lbBet", "lbTime", "lbSessionID", "_N$target", "data", "_parent", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_active", "_prefab", "_components", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 0, 4, 9, 5, 1, 7, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 1, 1, 2, 4, 5, 7, 5, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$verticalAlign", "_N$horizontalAlign", "_N$overflow", "_spacingX", "node", "_materials"], -6, 1, 3], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["95ea4ir9rFI2LGvFsTPVF8V", ["node", "topList<PERSON>iew"], 3, 1, 1], ["ddfa3kmvaBFpJ/3BBqFUFbs", ["node", "lbRank", "lbSID", "lbNickName", "lbTotalWin", "spriteTop", "spTop"], 3, 1, 1, 1, 1, 1, 1, 3], ["98b467lg1tGSIOSS7iaiI4s", ["node", "lbSessionID", "lbTime", "lbBet", "jackpotColor", "bigWinColor"], 3, 1, 1, 1, 1, 5, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["236d6E11AdCC7qwHzVYZojt", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1]], [[5, 0, 1, 2], [2, 0, 2, 3, 4, 5, 6, 2], [0, 0, 6, 8, 4, 3, 5, 7, 2], [3, 0, 1, 2, 3, 4, 8, 6, 7, 9, 10, 9], [0, 0, 6, 4, 3, 5, 2], [2, 0, 2, 3, 4, 7, 5, 6, 2], [1, 0, 4, 5, 2], [6, 1, 6, 1], [3, 0, 1, 2, 3, 4, 6, 5, 7, 9, 10, 9], [8, 0, 2], [0, 0, 8, 4, 3, 2], [0, 0, 8, 4, 3, 5, 2], [0, 0, 1, 6, 4, 3, 5, 7, 3], [0, 0, 6, 8, 4, 3, 5, 2], [0, 0, 6, 4, 3, 5, 9, 7, 2], [0, 0, 6, 4, 3, 5, 7, 2], [0, 0, 2, 6, 4, 3, 5, 7, 3], [0, 0, 6, 8, 3, 7, 2], [9, 0, 1, 2, 3, 4, 5, 6, 2], [2, 0, 1, 2, 3, 4, 7, 5, 8, 6, 3], [2, 0, 2, 3, 4, 5, 8, 6, 2], [10, 0, 1, 2, 1], [11, 0, 1, 1], [5, 1, 1], [12, 0, 1, 2, 3, 4, 5, 6, 1], [1, 3, 2, 0, 1, 4, 5, 6, 5], [1, 0, 4, 5, 6, 2], [1, 2, 0, 1, 4, 5, 6, 4], [1, 4, 5, 6, 1], [1, 0, 1, 4, 5, 6, 3], [13, 0, 1, 2, 3, 4, 5, 1], [14, 0, 1], [6, 0, 1, 2, 3, 4, 5, 2], [15, 0, 1, 2, 3], [7, 0, 1, 2, 4, 5, 4], [7, 0, 1, 3, 4, 5, 4], [16, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 5, 9, 7], [3, 0, 1, 2, 3, 4, 5, 9, 10, 7], [17, 0, 1, 2, 3, 4, 5, 6, 6], [18, 0, 1, 2, 3, 4, 5, 4]], [[[[9, "BacaratTopView"], [10, "BacaratTopView", [-5, -6, -7, -8, -9, -10, -11], [[21, -2, [23, 24], 22], [22, -4, -3]], [23, -1]], [11, "<PERSON><PERSON>", [-18, -19, -20, -21], [[24, -17, -16, -15, -14, -13, -12, [18, 19, 20]]], [0, "a8xomprhBABKvV+eqPnibc", 1], [5, 994, 50]], [2, "title", 1, [-27, -28, -29], [[25, false, 1, 0, false, -22, [12], 13], [30, -26, -25, -24, -23, [4, 4278246399], [4, 4294829568]]], [0, "9eUtnPLYxHaoGUYljSwjQc", 1], [5, 994, 50], [0, 169.014, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "scrollview", 1, [-33, -34], [[-30, [31, -31], -32], 1, 4, 1], [0, "acIXck5wtM/obzh86GfgBE", 1], [5, 1000, 410], [0, -67.795, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 1, [-37], [[32, 3, -36, [[33, "95ea4ir9rFI2LGvFsTPVF8V", "closeClicked", 1]], [4, 4294967295], [4, 4294967295], -35]], [0, "81GtArI+5PnpAbR4oxBGPx", 1], [5, 80, 80], [508.516, 214.473, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "layout-nick<PERSON><PERSON>", 2, [-39, -40], [[34, 1, 1, 5, -38, [5, 114, 50]]], [0, "b7ejrcRPtL8ohkwzngqc4x", 1], [5, 114, 50], [-63, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "black", 100, 1, [[26, 0, -41, [0], 1], [7, -42, [4, 4292269782]]], [0, "caVETEmgNI846X5onhQhSi", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nen popup", 1, [[27, 1, 0, false, -43, [2], 3], [7, -44, [4, 4292269782]]], [0, "6czwmkiiVMsqUOxYfCqRx3", 1], [5, 1084, 618]], [13, "view", 4, [-46], [[36, 0, -45, [21]]], [0, "052UBULRxM6aaoCbZuFJOc", 1], [5, 1000, 410]], [14, "content", 9, [[35, 1, 2, 10, -47, [5, 1000, 0]]], [0, "01RRJEADxKG58CbubhSW7U", 1], [5, 1000, 0], [0, 0.5, 1], [0, 220, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bg_content", 1, [[6, 0, -48, [4]]], [0, "6cPTk4Zu9IAK8+CIkpKm9T", 1], [5, 1020, 520], [0, -26, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "title_BXH", false, 1, [[28, -49, [5], 6]], [0, "2301ShdlREVrkKF+fKIgru", 1], [5, 395, 100], [0, 296, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "sprite", 5, [[29, 0, false, -50, [7], 8]], [0, "dfPSOEypZANpsuwOACPiU6", 1], [5, 70, 70]], [1, "lbRank", 3, [-51], [0, "67GghXvkVAUbAmeDwRFY7a", 1], [5, 150, 50], [-367, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "HẠNG", 20, 48, false, false, 1, 1, 1, 14, [9]], [1, "lbNickname", 3, [-52], [0, "22z734HHpLQ588/0Wv58lP", 1], [5, 200, 50], [-63, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "TÊN NHÂN VẬT", 20, 48, false, false, 1, 1, 1, 16, [10]], [1, "lbBet", 3, [-53], [0, "43VmczygtDGbc7Zz7llrxE", 1], [5, 200, 50], [265, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "TỔNG THẮNG", 20, 48, false, false, 1, 1, 1, 18, [11]], [17, "temp", 4, [2], [0, "16ryVMvoFKkpqLnOO+c6IU", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "top", 2, [-54], [0, "fcZyNOcQBLC54xF2m6Bxpf", 1], [5, 53, 43], [-364, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 2, 21, [14]], [5, "lbRank", 2, [-55], [0, "7aSWt0yG9K3KA1ckIFf6R7", 1], [4, 4284344318], [5, 150, 30], [-367, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "1", 24, 48, false, false, 1, 1, 1, 23, [15]], [19, "lbSID", false, 6, [-56], [0, "a5YE1q7YxFF7d7HW3Y8ytW", 1], [4, 4279026733], [5, 44.4, 28.8], [0, 0, 0.5], [-81.7, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "[TQ]", 24, 48, false, false, 1, 25], [20, "lbNickName", 6, [-57], [0, "93ZRF99w9F7rF8Ta4sEiWa", 1], [5, 114, 28.8], [0, 0, 0.5], [-57, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "<PERSON><PERSON><PERSON>", 24, 48, false, false, 1, 27, [16]], [5, "lbTotalWin", 2, [-58], [0, "da8WNw1cdJubaVnKZPQmQd", 1], [4, 4284344318], [5, 200, 30], [265, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "350.000.000", 24, 48, false, false, 1, 1, 1, 29, [17]], [39, false, 0.75, 0.23, null, null, 4, 10], [40, 20, 10, 400, 4, 2, 31]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 32, 0, 0, 1, 0, -1, 7, 0, -2, 8, 0, -3, 11, 0, -4, 12, 0, -5, 5, 0, -6, 3, 0, -7, 4, 0, 6, 22, 0, 7, 30, 0, 8, 28, 0, 9, 26, 0, 10, 24, 0, 0, 2, 0, -1, 21, 0, -2, 23, 0, -3, 6, 0, -4, 29, 0, 0, 3, 0, 11, 19, 0, 12, 17, 0, 13, 15, 0, 0, 3, 0, -1, 14, 0, -2, 16, 0, -3, 18, 0, -1, 31, 0, 0, 4, 0, -3, 32, 0, -1, 20, 0, -2, 9, 0, 14, 5, 0, 0, 5, 0, -1, 13, 0, 0, 6, 0, -1, 25, 0, -2, 27, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 10, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, -1, 15, 0, -1, 17, 0, -1, 19, 0, -1, 22, 0, -1, 24, 0, -1, 26, 0, -1, 28, 0, -1, 30, 0, 15, 1, 2, 16, 20, 58], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 24, 26, 28, 30], [-1, 1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, -1, -2, -3, -1, 17, -1, -2, 1, 2, 2, 2, 2], [0, 4, 0, 5, 0, 0, 6, 0, 7, 0, 0, 0, 0, 8, 0, 0, 0, 0, 2, 9, 10, 0, 3, 3, 11, 2, 1, 1, 1, 1]], [[{"name": "title_BXH", "rect": [0, 5, 395, 100], "offset": [0, -2.5], "originalSize": [395, 105], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [3], [12]], [[{"name": "popup_ranking", "rect": [0, 0, 1156, 642], "offset": [0, 0], "originalSize": [1156, 642], "capInsets": [0, 0, 0, 0]}], [4], 0, [0], [3], [13]]]]