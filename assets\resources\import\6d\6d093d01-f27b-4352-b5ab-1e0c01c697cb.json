[1, ["d701BQvUZEgY4P7ApQQ/nm"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "thangs<PERSON>lon", "\nthangsieulon.png\nsize: 1024,1024\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nGroup 3\n  rotate: false\n  xy: 461, 752\n  size: 318, 99\n  orig: 318, 99\n  offset: 0, 0\n  index: -1\nGroup 4\n  rotate: false\n  xy: 2, 2\n  size: 267, 120\n  orig: 267, 120\n  offset: 0, 0\n  index: -1\nLayer 588 copy\n  rotate: false\n  xy: 2, 386\n  size: 457, 465\n  orig: 457, 465\n  offset: 0, 0\n  index: -1\nLayer 8\n  rotate: false\n  xy: 364, 299\n  size: 86, 85\n  orig: 86, 85\n  offset: 0, 0\n  index: -1\nbb\n  rotate: false\n  xy: 461, 591\n  size: 101, 159\n  orig: 101, 159\n  offset: 0, 0\n  index: -1\nca\n  rotate: false\n  xy: 2, 124\n  size: 360, 260\n  orig: 360, 260\n  offset: 0, 0\n  index: -1\n", ["thangsieulon.png"], {"skeleton": {"hash": "awcRl89DCcIUAnE4qcCKqUatfws", "spine": "3.7.94", "width": 457, "height": 465, "images": "./images/", "audio": "C:/Users/<USER>/Desktop/ca 2m/thang sieu lon"}, "bones": [{"name": "root", "x": -6.04, "y": 13.6}, {"name": "Layer 588 copy", "parent": "root", "x": 4.72, "y": 6.1}, {"name": "Layer 8", "parent": "root", "x": 146.71, "y": -13.57}, {"name": "bb", "parent": "root", "length": 104.36, "rotation": 91.22, "x": 122.29, "y": -28.37}, {"name": "Group 4", "parent": "root", "x": -27.93, "y": -12.09}, {"name": "Group 3", "parent": "root", "x": -10.17, "y": -83.13}, {"name": "ca", "parent": "root", "length": 105.09, "rotation": 89.67, "x": -36.61, "y": 3.96}], "slots": [{"name": "Layer 588 copy", "bone": "Layer 588 copy", "attachment": "Layer 588 copy"}, {"name": "ca", "bone": "ca", "attachment": "ca"}, {"name": "Group 4", "bone": "Group 4", "attachment": "Group 4"}, {"name": "Group 3", "bone": "Group 3", "attachment": "Group 3"}, {"name": "Layer 8", "bone": "Layer 8", "attachment": "Layer 8"}, {"name": "bb", "bone": "bb", "attachment": "bb", "blend": "screen"}], "skins": {"default": {"Group 3": {"Group 3": {"x": 3.85, "y": 6.99, "width": 318, "height": 99}}, "Group 4": {"Group 4": {"x": 0.11, "y": 15.45, "width": 267, "height": 120}}, "Layer 588 copy": {"Layer 588 copy": {"x": -3.53, "y": -1.24, "width": 457, "height": 465}}, "Layer 8": {"Layer 8": {"x": 0.97, "y": 7.43, "width": 86, "height": 85}}, "bb": {"bb": {"x": 62.05, "y": -9.21, "rotation": -91.22, "width": 101, "height": 159}}, "ca": {"ca": {"type": "mesh", "hull": 50, "width": 360, "height": 260, "uvs": [0.56947, 0.85952, 0.58654, 0.92056, 0.64342, 0.98555, 0.70316, 1, 0.90085, 0.97964, 0.962, 0.90875, 0.97765, 0.83392, 0.99898, 0.73742, 0.99045, 0.62518, 0.96769, 0.51687, 0.92645, 0.42826, 0.98618, 0.39478, 1, 0.3357, 0.97622, 0.27466, 0.99471, 0.21558, 1, 0.06001, 0.96485, 0.06001, 0.91365, 0.07773, 0.85818, 0.12893, 0.81693, 0.19982, 0.78849, 0.20573, 0.75151, 0.19786, 0.73018, 0.22149, 0.67756, 0.15847, 0.71311, 0.07576, 0.7572, 0.01669, 0.66902, 0.0029, 0.61214, 0, 0.51969, 0, 0.46707, 0.05016, 0.36467, 0.0541, 0.21249, 0.10333, 0.18974, 0.18013, 0.15702, 0.27859, 0.15987, 0.35933, 0.15987, 0.44795, 0.08023, 0.52672, 0.002, 0.58776, 0, 0.71576, 0, 0.8221, 0, 0.88512, 0.02476, 0.96389, 0.12005, 0.95995, 0.23951, 0.8989, 0.27365, 0.93829, 0.34334, 0.91269, 0.36609, 0.87133, 0.38885, 0.85361, 0.44431, 0.85361, 0.55382, 0.89693], "triangles": [24, 26, 25, 24, 27, 26, 14, 16, 15, 17, 16, 14, 18, 17, 14, 19, 18, 14, 13, 19, 14, 34, 33, 32, 12, 10, 13, 10, 19, 13, 11, 10, 12, 22, 35, 34, 43, 39, 38, 29, 28, 27, 27, 23, 29, 24, 23, 27, 30, 29, 23, 22, 31, 30, 8, 0, 9, 7, 0, 8, 31, 22, 32, 23, 22, 30, 32, 22, 34, 48, 47, 35, 36, 35, 47, 22, 21, 20, 22, 48, 35, 48, 22, 0, 20, 19, 10, 10, 22, 20, 10, 0, 22, 0, 10, 9, 7, 6, 0, 47, 43, 36, 39, 42, 40, 49, 48, 0, 46, 43, 47, 38, 37, 36, 38, 36, 43, 6, 4, 0, 45, 43, 46, 1, 3, 2, 44, 43, 45, 43, 42, 39, 41, 40, 42, 6, 5, 4, 1, 0, 3, 4, 3, 0], "vertices": [-29.88, -34.48, -45.71, -40.71, -62.49, -61.29, -66.12, -82.82, -60.42, -153.95, -41.86, -175.86, -22.37, -181.38, 2.76, -188.91, 31.93, -185.67, 60.04, -177.32, 82.99, -162.34, 91.82, -183.79, 107.21, -188.67, 123.03, -180.02, 138.43, -186.59, 178.89, -188.26, 178.82, -175.6, 174.1, -157.2, 160.67, -137.31, 142.16, -122.57, 140.56, -112.34, 142.53, -99.01, 136.34, -91.37, 152.62, -72.33, 174.19, -85, 189.65, -100.79, 193.05, -69.02, 193.68, -48.54, 193.49, -15.26, 180.34, 3.61, 179.1, 40.47, 165.98, 95.17, 145.96, 103.25, 120.3, 114.88, 99.31, 113.73, 76.27, 113.6, 55.62, 142.15, 39.59, 170.22, 6.3, 170.74, -21.34, 170.58, -37.73, 170.49, -58.15, 161.46, -56.93, 127.16, -40.81, 84.24, -50.98, 71.9, -44.18, 46.85, -33.38, 38.72, -28.72, 30.55, -28.6, 10.59, -39.64, -28.9], "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 0, 98]}}}}, "animations": {"animation": {"slots": {"Group 3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6667, "color": "ffffff00"}, {"time": 1, "color": "ffffffff"}]}, "Group 4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff"}]}}, "bones": {"Group 3": {"scale": [{"time": 0, "x": 5, "y": 5, "curve": "stepped"}, {"time": 0.6667, "x": 5, "y": 5}, {"time": 1, "x": 0.9, "y": 0.9}, {"time": 1.3667, "x": 1, "y": 1}, {"time": 1.6667, "x": 0.9, "y": 0.9}, {"time": 2, "x": 1, "y": 1}, {"time": 2.3333, "x": 0.9, "y": 0.9}, {"time": 2.7, "x": 1, "y": 1}, {"time": 3, "x": 0.9, "y": 0.9}, {"time": 3.3333, "x": 1, "y": 1}]}, "Group 4": {"scale": [{"time": 0, "x": 5, "y": 5, "curve": "stepped"}, {"time": 0.3333, "x": 5, "y": 5}, {"time": 0.6667, "x": 0.9, "y": 0.9}, {"time": 1.0333, "x": 1, "y": 1}, {"time": 1.3333, "x": 0.9, "y": 0.9}, {"time": 1.6667, "x": 1, "y": 1}, {"time": 2, "x": 0.9, "y": 0.9}, {"time": 2.3667, "x": 1, "y": 1}, {"time": 2.6667, "x": 0.9, "y": 0.9}, {"time": 3, "x": 1, "y": 1}, {"time": 3.3333, "x": 0.9, "y": 0.9}]}, "ca": {"scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.5, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.9, "y": 0.9}, {"time": 1.1667, "x": 1, "y": 1}, {"time": 1.6667, "x": 0.9, "y": 0.9}, {"time": 2.1667, "x": 1, "y": 1}, {"time": 2.8333, "x": 0.9, "y": 0.9}, {"time": 3.3333, "x": 1, "y": 1}]}, "Layer 8": {"translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.5, "x": 0, "y": 29.54}, {"time": 1.0667, "x": 0, "y": 0}, {"time": 1.6333, "x": 0, "y": 29.54}, {"time": 2.2, "x": 0, "y": 0}, {"time": 2.7667, "x": 0, "y": 29.54}, {"time": 3.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 1, "y": 1}]}, "bb": {"scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.7667, "x": 1, "y": 1}, {"time": 1.5667, "x": 0.8, "y": 0.8}, {"time": 2.5, "x": 1, "y": 1}, {"time": 3.3333, "x": 0.8, "y": 0.8}]}, "Layer 588 copy": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.6667, "angle": 180}, {"time": 3.3333, "angle": 0}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 1.1, "y": 1.1}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 1, "x": 1.1, "y": 1.1}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.6667, "x": 1.1, "y": 1.1}, {"time": 2, "x": 1, "y": 1}, {"time": 2.3333, "x": 1.1, "y": 1.1}, {"time": 2.6667, "x": 1, "y": 1}, {"time": 3, "x": 1.1, "y": 1.1}, {"time": 3.3333, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]