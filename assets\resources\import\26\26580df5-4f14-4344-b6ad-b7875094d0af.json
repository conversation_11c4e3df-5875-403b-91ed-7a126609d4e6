[1, ["7aP3LRJhpKRKeY+8kddy0y"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "Spin_x", "\nSpin_x.png\nsize: 1024,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nQ_c\n  rotate: false\n  xy: 771, 421\n  size: 18, 57\n  orig: 18, 57\n  offset: 0, 0\n  index: -1\nQ_g\n  rotate: true\n  xy: 2, 2\n  size: 48, 89\n  orig: 48, 89\n  offset: 0, 0\n  index: -1\nQ_spin\n  rotate: false\n  xy: 791, 427\n  size: 51, 51\n  orig: 51, 51\n  offset: 0, 0\n  index: -1\nX1_demo\n  rotate: false\n  xy: 2, 52\n  size: 767, 426\n  orig: 767, 426\n  offset: 0, 0\n  index: -1\n", ["Spin_x.png"], {"skeleton": {"hash": "K97GXoELE3bkGiHoakufFeE++9Y", "spine": "3.6.53", "width": 57.56, "height": 169.26, "images": "./anh/"}, "bones": [{"name": "root"}, {"name": "Q_c", "parent": "root", "length": 47.36, "rotation": 88.85, "x": 8.19, "y": 24.68}, {"name": "Q_g", "parent": "root", "length": 38.84, "rotation": 1.4, "x": -20.22, "y": 0.06}, {"name": "Q_spin", "parent": "root", "rotation": 88.85, "x": 7.24, "y": 101.39}], "slots": [{"name": "X1_demo", "bone": "root"}, {"name": "Q_g", "bone": "Q_g", "attachment": "Q_g"}, {"name": "Q_c", "bone": "Q_c", "attachment": "Q_c"}, {"name": "Q_spin", "bone": "Q_spin", "attachment": "Q_spin"}], "skins": {"default": {"Q_c": {"Q_c": {"x": 24.45, "y": 0.14, "rotation": -88.85, "width": 18, "height": 57}}, "Q_g": {"Q_g": {"x": 20.21, "y": -0.56, "rotation": -1.4, "width": 48, "height": 89}}, "Q_spin": {"Q_spin": {"x": -2.11, "y": -0.86, "rotation": -88.85, "width": 51, "height": 51}}, "X1_demo": {"X1_demo": {"x": -369.44, "y": 30.92, "width": 767, "height": 426}}}}, "animations": {"animation": {"bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "Q_g": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "Q_c": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}, {"time": 0.4667, "angle": 2.55}, {"time": 0.5333, "angle": 0}, {"time": 0.6, "angle": 2.55}, {"time": 0.6667, "angle": 0}, {"time": 0.7333, "angle": 2.55}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": 0, "y": -47.77}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3, "x": -1.258, "y": 1}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}, "Q_spin": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}, {"time": 0.4667, "angle": 2.55}, {"time": 0.5333, "angle": 0}, {"time": 0.6, "angle": 2.55}, {"time": 0.6667, "angle": 0}, {"time": 0.7333, "angle": 2.55}, {"time": 0.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": 0, "y": -201.31}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.4667, "x": -4.26, "y": 0}, {"time": 0.5333, "x": 0, "y": 0}, {"time": 0.6, "x": -4.26, "y": 0}, {"time": 0.6667, "x": 0, "y": 0}, {"time": 0.7333, "x": -4.26, "y": 0}, {"time": 0.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8333, "x": 1, "y": 1}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]