[1, ["cee4idYE5Do6G4/8TuO0wI", "1cL5l/O7hE64JmMoe8Dn87", "017Jn3Zv1Ft7hygdjpaSoK", "fdNoodJKVLj4dF1TLppv2g", "2aa7VZQSlFcpjITT9MxU4i", "46DA3E9bdDgIqPSbyYx4c8", "765SDY8gBNE4mLSNpFeYcj", "a9VpD0DP5LJYQPXITZq+uj", "c1RXLI8G1EHqaafp7phgIY", "25bZ6LNu1ETo4+3yJxsik2", "3eH6k56UpDyqQV8Lnm+mYi", "74qCZlsOtBcqB3v53xd5qm", "adJ2nysudCAaX1B0uKpoc0", "2cWB/vWPRHja3uQTinHH30"], ["node", "_spriteFrame", "_defaultClip", "_textureSetter", "_N$file", "root", "lbTotalWin", "lbSessionID", "_N$target", "data", "_parent"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_trs", "_parent", "_children"], 1, 4, 9, 5, 7, 1, 2], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_spriteFrame"], 0, 1, 6], "cc.SpriteFrame", ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_N$file"], -5, 1, 6], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 2, 1, 9, 5, 5, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["75fafXlU59BUbvJF1jo3XxQ", ["node", "lbSessionID", "lbTotalWin", "spriteIcons"], 3, 1, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 0]], [[9, 0, 1, 2], [0, 0, 6, 7, 3, 2, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 2], [7, 0, 1, 2, 1], [1, 3, 1], [0, 0, 6, 7, 2, 4, 5, 2], [0, 0, 6, 3, 2, 4, 5, 2], [0, 0, 6, 3, 2, 4, 2], [10, 0, 1, 2, 3, 4, 4], [1, 3, 4, 1], [6, 0, 2], [0, 0, 7, 3, 2, 2], [0, 0, 7, 3, 2, 4, 5, 2], [0, 0, 1, 6, 3, 2, 4, 5, 3], [4, 0, 1, 2, 3, 6, 4, 5, 2], [8, 0, 1, 2, 3, 1], [5, 0, 1, 2, 3, 4, 5, 2], [5, 1, 6, 1], [11, 0, 1, 2, 3], [1, 0, 3, 4, 2], [1, 1, 0, 3, 4, 3], [1, 1, 0, 2, 3, 4, 4], [1, 0, 2, 3, 4, 3], [12, 0, 1, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [3, 0, 1, 2, 3, 4, 5, 6, 8, 9, 8], [3, 0, 1, 2, 3, 4, 5, 6, 8, 8], [13, 0, 1, 2, 4]], [[[{"name": "candy5", "rect": [1, 1, 103, 102], "offset": [-0.5, 0], "originalSize": [106, 104], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [4]], [[{"name": "BG-KimCuong", "rect": [0, 0, 696, 508], "offset": [0, 0], "originalSize": [696, 508], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [3], [5]], [[{"name": "bg_popup", "rect": [0, 0, 114, 107], "offset": [0, 0], "originalSize": [114, 107], "capInsets": [54, 54, 54, 53]}], [2], 0, [0], [3], [6]], [[[10, "777SessionDetailView"], [11, "777SessionDetailView", [-15, -16, -17, -18, -19, -20, -21], [[3, -2, [26, 27], 25], [15, -14, -13, -12, [-3, -4, -5, -6, -7, -8, -9, -10, -11]]], [0, "51ql18VmNEZLNP97EMYsrR", -1]], [12, "slots", [-23, -24, -25], [[8, 1, 1, 22, -22, [5, 260, 310]]], [0, "b4Y71BBC5HGrDq01fSreYR", 1], [5, 260, 310], [0, 0, 0, 0, 0, 0, 1, 1.08, 1.08, 1]], [1, "btnClose", 1, [-28], [[16, 3, -27, [[18, "75fafXlU59BUbvJF1jo3XxQ", "backClicked", 1]], [4, 4294967295], [4, 4294967295], -26]], [0, "c0v6RlXhdO3b8d9vKRyGMW", 1], [5, 80, 80], [523.4, 297.1, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "1", 2, [-29, -30, -31], [0, "42xhC4AXtGbI/V1183PBm8", 1], [5, 72, 300], [-94, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "2", 2, [-32, -33, -34], [0, "4b3px5XdVPkaysVVdUULkc", 1], [5, 72, 300], [0, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "3", 2, [-35, -36, -37], [0, "eey74fIWREtKRz58Jx3Lmu", 1], [5, 72, 300], [94, -6, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-totalwin", 1, [-39, -40], [[8, 1, 1, 5, -38, [5, 292.2, 50]]], [0, "3aHEiwq0JOn53j4rnQr9pE", 1], [5, 292.2, 50], [0, -260, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "black", 100, 1, [[19, 0, -41, 0], [17, -42, [4, 4292269782]]], [0, "d7FV6OndpGe6dyvjB21KHy", 1], [5, 3000, 3000], [0, 23, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tit_popup", 1, [-44], [[20, 1, 0, -43, 3]], [0, "6cR7bkzzxG1rnS1K761nhU", 1], [5, 401, 48], [0, 272, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "spinView", 1, [-45, -46], [0, "fbNcqZiP1PKros6/LVHR4s", 1], [5, 550, 308], [0, -27, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [1, "mask", 10, [2], [[23, 1, -47]], [0, "1cAK+NVSlCUofO096YAO//", 1], [5, 310, 310], [-72, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "slot1", 4, [-49], [[3, -48, [7], 6]], [0, "1eA5o6+M1COaIV2EYDPqkS", 1], [5, 72, 96], [0, 95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot2", 4, [-51], [[3, -50, [9], 8]], [0, "b5k+2D4V5JkbTOE1QiN8Ln", 1], [5, 72, 96], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot3", 4, [-53], [[3, -52, [11], 10]], [0, "3etZnlXQBDqY5cmeRmrIIO", 1], [5, 72, 96], [0, -95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot1", 5, [-55], [[3, -54, [13], 12]], [0, "44mMzDyCJBDJ8gSyQ99+hq", 1], [5, 72, 96], [0, 95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot2", 5, [-57], [[3, -56, [15], 14]], [0, "c3rR4RG3pKA6dRloR7SJSN", 1], [5, 72, 96], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot3", 5, [-59], [[3, -58, [17], 16]], [0, "62Rxt7YVRHqr7MQeIcLyBy", 1], [5, 72, 96], [0, -95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot1", 6, [-61], [[3, -60, [19], 18]], [0, "7aj97OUA5HLag5bsx0O/8/", 1], [5, 72, 96], [0, 95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot2", 6, [-63], [[3, -62, [21], 20]], [0, "31Eby+PjROCrYb/GQEvkDb", 1], [5, 72, 96], [0, 0, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [1, "slot3", 6, [-65], [[3, -64, [23], 22]], [0, "63cOZx/sJNh6y7kq6Y4tJX", 1], [5, 72, 96], [0, -95, 0, 0, 0, 0, 1, 1.3, 1.3, 1]], [7, "nen popup", 1, [[21, 1, 0, false, -66, 1]], [0, "5dP03VgQBDvaANXu10PxY8", 1], [5, 1084, 618]], [6, "sprite", 9, [[9, -67, 2]], [0, "9aqtGC2ENGNoxtPxKdmzvW", 1], [5, 284, 38], [0, 2, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "sprite", 3, [[22, 2, false, -68, 4]], [0, "81EwmDdCVDoqK9SPnL5v7Q", 1], [5, 66, 67]], [6, "khungchinh1", 10, [[9, -69, 5]], [0, "c9MkX/lFpAAbQHWUsGDszg", 1], [5, 696, 508], [0, 0, 0, 0, 0, 0, 1, 0.93, 0.93, 1]], [2, "candy5", 12, [-70], [0, "bdZBLQ03dJhq3alc+UdcS4", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 25], [2, "candy5", 13, [-71], [0, "0en+t+VxRPdbzYX6SeTfyL", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 27], [2, "candy5", 14, [-72], [0, "56orojxCRDhYBzBmJuJHjJ", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 29], [2, "candy5", 15, [-73], [0, "74bmZMnRpP/ohNyfRJexjm", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 31], [2, "candy5", 16, [-74], [0, "faD05DVHxDqIzENmsHZGsX", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 33], [2, "candy5", 17, [-75], [0, "94UHH35cZHpIgLRYyiQz4b", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 35], [2, "candy5", 18, [-76], [0, "55j9pB86xNzrx0C3lwyXYK", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 37], [2, "candy5", 19, [-77], [0, "2eWqEAeOlOqar19RUEvCGk", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 39], [2, "candy5", 20, [-78], [0, "57QAxB+/BDErc2GTdK40sb", 1], [5, 103, 102], [0, 4.5, 0, 0, 0, 0, 1, 0.75, 0.75, 1]], [4, 41], [2, "lbSession", 1, [-79], [0, "79oLokgBBD/o0ifQpuSnp9", 1], [5, 405, 30], [0, 205, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "PHIÊN: #13423534234", 26, 50, false, false, 1, 1, 1, 43], [6, "lbTotalWin", 7, [[25, "Tổng thắng: ", 32, 50, false, false, 1, 1, -80, 24]], [0, "45HTfjKyZJvoFkuCUBcQ8z", 1], [5, 175.2, 40], [-58.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "lbTotalWinVal", 7, [-81], [0, "450Ghh3BNFnYTJveNnKKOs", 1], [4, 4278249468], [5, 112, 40], [90.1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "1.000 G", 32, 50, false, false, 1, 1, 46]], 0, [0, 5, 1, 0, 0, 1, 0, -1, 26, 0, -2, 32, 0, -3, 38, 0, -4, 28, 0, -5, 34, 0, -6, 40, 0, -7, 30, 0, -8, 36, 0, -9, 42, 0, 6, 47, 0, 7, 44, 0, 0, 1, 0, -1, 8, 0, -2, 21, 0, -3, 9, 0, -4, 3, 0, -5, 10, 0, -6, 43, 0, -7, 7, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, 8, 3, 0, 0, 3, 0, -1, 23, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -1, 15, 0, -2, 16, 0, -3, 17, 0, -1, 18, 0, -2, 19, 0, -3, 20, 0, 0, 7, 0, -1, 45, 0, -2, 46, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, -1, 22, 0, -1, 24, 0, -2, 11, 0, 0, 11, 0, 0, 12, 0, -1, 25, 0, 0, 13, 0, -1, 27, 0, 0, 14, 0, -1, 29, 0, 0, 15, 0, -1, 31, 0, 0, 16, 0, -1, 33, 0, 0, 17, 0, -1, 35, 0, 0, 18, 0, -1, 37, 0, 0, 19, 0, -1, 39, 0, 0, 20, 0, -1, 41, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, -1, 26, 0, -1, 28, 0, -1, 30, 0, -1, 32, 0, -1, 34, 0, -1, 36, 0, -1, 38, 0, -1, 40, 0, -1, 42, 0, -1, 44, 0, 0, 45, 0, -1, 47, 0, 9, 1, 2, 10, 11, 81], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 47], [1, 1, 1, 1, 1, 1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, 4, 2, -1, -2, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 4], [7, 8, 9, 10, 11, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 3, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2]], [[[27, "slotHighlight", 1.95, {"paths": {"skeleton": {"props": {"scaleX": [{"frame": 0, "value": 1}, {"frame": 0.31666666666666665, "value": 1.2}, {"frame": 0.3333333333333333, "value": 1.2}, {"frame": 1.7833333333333334, "value": 1.2}, {"frame": 1.95, "value": 1}], "scaleY": [{"frame": 0, "value": 1}, {"frame": 0.31666666666666665, "value": 1.2}, {"frame": 0.3333333333333333, "value": 1.2}, {"frame": 1.7833333333333334, "value": 1.2}, {"frame": 1.95, "value": 1}], "angle": [{"frame": 0.3333333333333333, "value": 0}, {"frame": 0.4166666666666667, "value": -5}, {"frame": 0.48333333333333334, "value": 0}, {"frame": 0.55, "value": 5}, {"frame": 0.6166666666666667, "value": 0}, {"frame": 0.6833333333333333, "value": -5}, {"frame": 0.75, "value": 0}, {"frame": 0.8166666666666667, "value": 5}, {"frame": 0.8833333333333333, "value": 0}, {"frame": 0.95, "value": -5}, {"frame": 1.0166666666666666, "value": 0}, {"frame": 1.0833333333333333, "value": 5}, {"frame": 1.15, "value": 0}, {"frame": 1.2166666666666666, "value": -5}, {"frame": 1.2833333333333334, "value": 0}, {"frame": 1.35, "value": 5}, {"frame": 1.4166666666666667, "value": 0}, {"frame": 1.4833333333333334, "value": -5}, {"frame": 1.55, "value": 0}, {"frame": 1.6166666666666667, "value": 5}, {"frame": 1.6833333333333333, "value": 0}]}}}}]], 0, 0, [], [], []]]]