[1, ["13zkjzDH1CMZ0DblMLAz+n", "31qa6CkoxKBqo+jQsqJC5O"], ["_textureSetter", "spriteFrame"], ["cc.SpriteFrame", ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0]], [[1, 0, 1, 2, 4]], [[[{"name": "myriadpro", "rect": [0, 0, 509, 344], "offset": [-1.5, 84], "originalSize": [512, 512], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[[0, "myriadpro", 40, {"commonHeight": 48, "fontSize": 40, "atlasName": "myriadpro.png", "fontDefDictionary": {"32": {"xOffset": 1, "yOffset": 29, "xAdvance": 8, "rect": {"x": 24, "y": 340, "width": 2, "height": 2}}, "33": {"xOffset": 3, "yOffset": 2, "xAdvance": 9, "rect": {"x": 455, "y": 226, "width": 10, "height": 30}}, "34": {"xOffset": 3, "yOffset": 1, "xAdvance": 13, "rect": {"x": 416, "y": 316, "width": 16, "height": 13}}, "35": {"xOffset": 2, "yOffset": 3, "xAdvance": 20, "rect": {"x": 364, "y": 287, "width": 22, "height": 28}}, "36": {"xOffset": 4, "yOffset": 0, "xAdvance": 21, "rect": {"x": 206, "y": 158, "width": 20, "height": 35}}, "37": {"xOffset": 2, "yOffset": 3, "xAdvance": 32, "rect": {"x": 92, "y": 287, "width": 35, "height": 29}}, "38": {"xOffset": 2, "yOffset": 2, "xAdvance": 24, "rect": {"x": 0, "y": 257, "width": 28, "height": 30}}, "39": {"xOffset": 3, "yOffset": 1, "xAdvance": 8, "rect": {"x": 432, "y": 316, "width": 8, "height": 13}}, "40": {"xOffset": 4, "yOffset": 1, "xAdvance": 11, "rect": {"x": 247, "y": 158, "width": 11, "height": 35}}, "41": {"xOffset": 2, "yOffset": 1, "xAdvance": 11, "rect": {"x": 258, "y": 158, "width": 11, "height": 35}}, "42": {"xOffset": 2, "yOffset": 2, "xAdvance": 17, "rect": {"x": 363, "y": 316, "width": 18, "height": 18}}, "43": {"xOffset": 3, "yOffset": 8, "xAdvance": 24, "rect": {"x": 22, "y": 316, "width": 23, "height": 24}}, "44": {"xOffset": 2, "yOffset": 24, "xAdvance": 8, "rect": {"x": 381, "y": 316, "width": 12, "height": 14}}, "45": {"xOffset": 2, "yOffset": 17, "xAdvance": 12, "rect": {"x": 485, "y": 316, "width": 13, "height": 7}}, "46": {"xOffset": 3, "yOffset": 24, "xAdvance": 8, "rect": {"x": 463, "y": 316, "width": 10, "height": 10}}, "47": {"xOffset": 1, "yOffset": 2, "xAdvance": 14, "rect": {"x": 21, "y": 193, "width": 15, "height": 32}}, "48": {"xOffset": 2, "yOffset": 3, "xAdvance": 21, "rect": {"x": 0, "y": 287, "width": 22, "height": 29}}, "49": {"xOffset": 5, "yOffset": 3, "xAdvance": 21, "rect": {"x": 386, "y": 287, "width": 14, "height": 28}}, "50": {"xOffset": 3, "yOffset": 3, "xAdvance": 21, "rect": {"x": 385, "y": 257, "width": 21, "height": 29}}, "51": {"xOffset": 3, "yOffset": 3, "xAdvance": 21, "rect": {"x": 327, "y": 257, "width": 21, "height": 29}}, "52": {"xOffset": 2, "yOffset": 3, "xAdvance": 21, "rect": {"x": 421, "y": 287, "width": 23, "height": 28}}, "53": {"xOffset": 3, "yOffset": 3, "xAdvance": 21, "rect": {"x": 306, "y": 257, "width": 21, "height": 29}}, "54": {"xOffset": 2, "yOffset": 3, "xAdvance": 21, "rect": {"x": 127, "y": 287, "width": 22, "height": 29}}, "55": {"xOffset": 3, "yOffset": 3, "xAdvance": 21, "rect": {"x": 400, "y": 287, "width": 21, "height": 28}}, "56": {"xOffset": 2, "yOffset": 3, "xAdvance": 21, "rect": {"x": 284, "y": 257, "width": 22, "height": 29}}, "57": {"xOffset": 3, "yOffset": 3, "xAdvance": 21, "rect": {"x": 22, "y": 287, "width": 22, "height": 29}}, "58": {"xOffset": 3, "yOffset": 10, "xAdvance": 8, "rect": {"x": 144, "y": 316, "width": 10, "height": 23}}, "59": {"xOffset": 2, "yOffset": 10, "xAdvance": 8, "rect": {"x": 444, "y": 287, "width": 12, "height": 27}}, "60": {"xOffset": 4, "yOffset": 8, "xAdvance": 24, "rect": {"x": 0, "y": 316, "width": 22, "height": 24}}, "61": {"xOffset": 3, "yOffset": 13, "xAdvance": 24, "rect": {"x": 393, "y": 316, "width": 23, "height": 14}}, "62": {"xOffset": 4, "yOffset": 8, "xAdvance": 24, "rect": {"x": 481, "y": 287, "width": 22, "height": 24}}, "63": {"xOffset": 3, "yOffset": 2, "xAdvance": 16, "rect": {"x": 244, "y": 257, "width": 17, "height": 30}}, "64": {"xOffset": 3, "yOffset": 5, "xAdvance": 29, "rect": {"x": 214, "y": 257, "width": 30, "height": 30}}, "65": {"xOffset": 2, "yOffset": 2, "xAdvance": 24, "rect": {"x": 450, "y": 257, "width": 27, "height": 29}}, "66": {"xOffset": 4, "yOffset": 2, "xAdvance": 22, "rect": {"x": 97, "y": 257, "width": 22, "height": 30}}, "67": {"xOffset": 2, "yOffset": 2, "xAdvance": 23, "rect": {"x": 28, "y": 257, "width": 24, "height": 30}}, "68": {"xOffset": 4, "yOffset": 2, "xAdvance": 27, "rect": {"x": 188, "y": 257, "width": 26, "height": 30}}, "69": {"xOffset": 4, "yOffset": 2, "xAdvance": 20, "rect": {"x": 221, "y": 287, "width": 20, "height": 29}}, "70": {"xOffset": 4, "yOffset": 2, "xAdvance": 19, "rect": {"x": 431, "y": 257, "width": 19, "height": 29}}, "71": {"xOffset": 2, "yOffset": 2, "xAdvance": 26, "rect": {"x": 148, "y": 257, "width": 26, "height": 30}}, "72": {"xOffset": 4, "yOffset": 2, "xAdvance": 26, "rect": {"x": 267, "y": 287, "width": 25, "height": 29}}, "73": {"xOffset": 4, "yOffset": 2, "xAdvance": 10, "rect": {"x": 500, "y": 257, "width": 9, "height": 29}}, "74": {"xOffset": 1, "yOffset": 2, "xAdvance": 15, "rect": {"x": 438, "y": 226, "width": 17, "height": 30}}, "75": {"xOffset": 4, "yOffset": 2, "xAdvance": 22, "rect": {"x": 406, "y": 257, "width": 25, "height": 29}}, "76": {"xOffset": 4, "yOffset": 2, "xAdvance": 19, "rect": {"x": 176, "y": 287, "width": 19, "height": 29}}, "77": {"xOffset": 3, "yOffset": 2, "xAdvance": 32, "rect": {"x": 292, "y": 287, "width": 32, "height": 29}}, "78": {"xOffset": 4, "yOffset": 2, "xAdvance": 26, "rect": {"x": 67, "y": 287, "width": 25, "height": 29}}, "79": {"xOffset": 2, "yOffset": 2, "xAdvance": 28, "rect": {"x": 410, "y": 226, "width": 28, "height": 30}}, "80": {"xOffset": 4, "yOffset": 2, "xAdvance": 21, "rect": {"x": 52, "y": 257, "width": 22, "height": 30}}, "81": {"xOffset": 2, "yOffset": 2, "xAdvance": 28, "rect": {"x": 65, "y": 158, "width": 29, "height": 35}}, "82": {"xOffset": 4, "yOffset": 2, "xAdvance": 22, "rect": {"x": 366, "y": 226, "width": 23, "height": 30}}, "83": {"xOffset": 3, "yOffset": 2, "xAdvance": 20, "rect": {"x": 389, "y": 226, "width": 21, "height": 30}}, "84": {"xOffset": 1, "yOffset": 2, "xAdvance": 20, "rect": {"x": 44, "y": 287, "width": 23, "height": 29}}, "85": {"xOffset": 4, "yOffset": 2, "xAdvance": 26, "rect": {"x": 465, "y": 226, "width": 25, "height": 30}}, "86": {"xOffset": 1, "yOffset": 2, "xAdvance": 22, "rect": {"x": 149, "y": 287, "width": 27, "height": 29}}, "87": {"xOffset": 2, "yOffset": 2, "xAdvance": 34, "rect": {"x": 348, "y": 257, "width": 37, "height": 29}}, "88": {"xOffset": 2, "yOffset": 2, "xAdvance": 23, "rect": {"x": 241, "y": 287, "width": 26, "height": 29}}, "89": {"xOffset": 1, "yOffset": 2, "xAdvance": 22, "rect": {"x": 195, "y": 287, "width": 26, "height": 29}}, "90": {"xOffset": 2, "yOffset": 2, "xAdvance": 22, "rect": {"x": 477, "y": 257, "width": 23, "height": 29}}, "91": {"xOffset": 4, "yOffset": 2, "xAdvance": 11, "rect": {"x": 354, "y": 158, "width": 11, "height": 34}}, "93": {"xOffset": 2, "yOffset": 2, "xAdvance": 11, "rect": {"x": 343, "y": 158, "width": 11, "height": 34}}, "94": {"xOffset": 3, "yOffset": 3, "xAdvance": 24, "rect": {"x": 340, "y": 316, "width": 23, "height": 21}}, "95": {"xOffset": 1, "yOffset": 32, "xAdvance": 20, "rect": {"x": 0, "y": 340, "width": 22, "height": 4}}, "96": {"xOffset": 2, "yOffset": 1, "xAdvance": 12, "rect": {"x": 473, "y": 316, "width": 12, "height": 9}}, "97": {"xOffset": 2, "yOffset": 9, "xAdvance": 19, "rect": {"x": 45, "y": 316, "width": 20, "height": 23}}, "98": {"xOffset": 4, "yOffset": 1, "xAdvance": 23, "rect": {"x": 237, "y": 226, "width": 23, "height": 31}}, "99": {"xOffset": 3, "yOffset": 9, "xAdvance": 18, "rect": {"x": 86, "y": 316, "width": 18, "height": 23}}, "100": {"xOffset": 2, "yOffset": 1, "xAdvance": 23, "rect": {"x": 306, "y": 226, "width": 23, "height": 31}}, "101": {"xOffset": 3, "yOffset": 9, "xAdvance": 20, "rect": {"x": 65, "y": 316, "width": 21, "height": 23}}, "102": {"xOffset": 2, "yOffset": 0, "xAdvance": 12, "rect": {"x": 329, "y": 226, "width": 16, "height": 31}}, "103": {"xOffset": 3, "yOffset": 9, "xAdvance": 22, "rect": {"x": 189, "y": 226, "width": 22, "height": 31}}, "104": {"xOffset": 4, "yOffset": 1, "xAdvance": 22, "rect": {"x": 345, "y": 226, "width": 21, "height": 31}}, "105": {"xOffset": 3, "yOffset": 2, "xAdvance": 9, "rect": {"x": 462, "y": 193, "width": 9, "height": 31}}, "106": {"xOffset": -1, "yOffset": 2, "xAdvance": 10, "rect": {"x": 414, "y": 45, "width": 14, "height": 39}}, "107": {"xOffset": 4, "yOffset": 1, "xAdvance": 19, "rect": {"x": 144, "y": 193, "width": 22, "height": 31}}, "108": {"xOffset": 4, "yOffset": 1, "xAdvance": 9, "rect": {"x": 180, "y": 226, "width": 9, "height": 31}}, "109": {"xOffset": 4, "yOffset": 9, "xAdvance": 33, "rect": {"x": 154, "y": 316, "width": 32, "height": 22}}, "110": {"xOffset": 4, "yOffset": 9, "xAdvance": 22, "rect": {"x": 209, "y": 316, "width": 21, "height": 22}}, "111": {"xOffset": 3, "yOffset": 9, "xAdvance": 22, "rect": {"x": 104, "y": 316, "width": 23, "height": 23}}, "112": {"xOffset": 4, "yOffset": 9, "xAdvance": 23, "rect": {"x": 74, "y": 257, "width": 23, "height": 30}}, "113": {"xOffset": 3, "yOffset": 9, "xAdvance": 23, "rect": {"x": 261, "y": 257, "width": 23, "height": 30}}, "114": {"xOffset": 4, "yOffset": 9, "xAdvance": 13, "rect": {"x": 302, "y": 316, "width": 15, "height": 22}}, "115": {"xOffset": 3, "yOffset": 9, "xAdvance": 16, "rect": {"x": 127, "y": 316, "width": 17, "height": 23}}, "116": {"xOffset": 2, "yOffset": 5, "xAdvance": 13, "rect": {"x": 324, "y": 287, "width": 15, "height": 28}}, "117": {"xOffset": 4, "yOffset": 10, "xAdvance": 22, "rect": {"x": 281, "y": 316, "width": 21, "height": 22}}, "118": {"xOffset": 2, "yOffset": 10, "xAdvance": 19, "rect": {"x": 317, "y": 316, "width": 23, "height": 22}}, "119": {"xOffset": 2, "yOffset": 10, "xAdvance": 29, "rect": {"x": 249, "y": 316, "width": 32, "height": 22}}, "120": {"xOffset": 1, "yOffset": 10, "xAdvance": 19, "rect": {"x": 186, "y": 316, "width": 23, "height": 22}}, "121": {"xOffset": 1, "yOffset": 10, "xAdvance": 19, "rect": {"x": 268, "y": 193, "width": 23, "height": 31}}, "122": {"xOffset": 2, "yOffset": 10, "xAdvance": 17, "rect": {"x": 230, "y": 316, "width": 19, "height": 22}}, "123": {"xOffset": 2, "yOffset": 2, "xAdvance": 11, "rect": {"x": 317, "y": 158, "width": 13, "height": 34}}, "124": {"xOffset": 4, "yOffset": -1, "xAdvance": 10, "rect": {"x": 160, "y": 0, "width": 7, "height": 42}}, "125": {"xOffset": 2, "yOffset": 2, "xAdvance": 11, "rect": {"x": 330, "y": 158, "width": 13, "height": 34}}, "126": {"xOffset": 3, "yOffset": 15, "xAdvance": 24, "rect": {"x": 440, "y": 316, "width": 23, "height": 10}}, "127": {"xOffset": 1, "yOffset": 29, "xAdvance": 0, "rect": {"x": 22, "y": 340, "width": 2, "height": 2}}, "192": {"xOffset": 2, "yOffset": -4, "xAdvance": 24, "rect": {"x": 105, "y": 122, "width": 27, "height": 36}}, "193": {"xOffset": 2, "yOffset": -4, "xAdvance": 24, "rect": {"x": 239, "y": 122, "width": 27, "height": 36}}, "194": {"xOffset": 2, "yOffset": -4, "xAdvance": 24, "rect": {"x": 417, "y": 122, "width": 27, "height": 36}}, "195": {"xOffset": 2, "yOffset": -4, "xAdvance": 24, "rect": {"x": 311, "y": 122, "width": 27, "height": 36}}, "200": {"xOffset": 4, "yOffset": -4, "xAdvance": 20, "rect": {"x": 291, "y": 122, "width": 20, "height": 36}}, "201": {"xOffset": 4, "yOffset": -4, "xAdvance": 20, "rect": {"x": 85, "y": 122, "width": 20, "height": 36}}, "202": {"xOffset": 4, "yOffset": -4, "xAdvance": 20, "rect": {"x": 473, "y": 84, "width": 20, "height": 36}}, "204": {"xOffset": 0, "yOffset": -4, "xAdvance": 10, "rect": {"x": 227, "y": 122, "width": 12, "height": 36}}, "205": {"xOffset": 4, "yOffset": -4, "xAdvance": 10, "rect": {"x": 132, "y": 122, "width": 12, "height": 36}}, "210": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 174, "y": 122, "width": 28, "height": 36}}, "211": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 389, "y": 122, "width": 28, "height": 36}}, "212": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 361, "y": 122, "width": 28, "height": 36}}, "213": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 212, "y": 84, "width": 28, "height": 37}}, "217": {"xOffset": 4, "yOffset": -4, "xAdvance": 26, "rect": {"x": 0, "y": 122, "width": 25, "height": 36}}, "218": {"xOffset": 4, "yOffset": -4, "xAdvance": 26, "rect": {"x": 266, "y": 122, "width": 25, "height": 36}}, "221": {"xOffset": 1, "yOffset": -4, "xAdvance": 22, "rect": {"x": 300, "y": 84, "width": 26, "height": 36}}, "224": {"xOffset": 2, "yOffset": 1, "xAdvance": 19, "rect": {"x": 291, "y": 193, "width": 20, "height": 31}}, "225": {"xOffset": 2, "yOffset": 1, "xAdvance": 19, "rect": {"x": 311, "y": 193, "width": 20, "height": 31}}, "226": {"xOffset": 2, "yOffset": 1, "xAdvance": 19, "rect": {"x": 248, "y": 193, "width": 20, "height": 31}}, "227": {"xOffset": 2, "yOffset": 2, "xAdvance": 19, "rect": {"x": 47, "y": 193, "width": 20, "height": 31}}, "232": {"xOffset": 3, "yOffset": 1, "xAdvance": 20, "rect": {"x": 368, "y": 193, "width": 21, "height": 31}}, "233": {"xOffset": 3, "yOffset": 1, "xAdvance": 20, "rect": {"x": 123, "y": 193, "width": 21, "height": 31}}, "234": {"xOffset": 3, "yOffset": 1, "xAdvance": 20, "rect": {"x": 67, "y": 193, "width": 21, "height": 31}}, "236": {"xOffset": 0, "yOffset": 1, "xAdvance": 9, "rect": {"x": 356, "y": 193, "width": 12, "height": 31}}, "237": {"xOffset": 4, "yOffset": 1, "xAdvance": 9, "rect": {"x": 88, "y": 193, "width": 12, "height": 31}}, "242": {"xOffset": 3, "yOffset": 1, "xAdvance": 22, "rect": {"x": 439, "y": 193, "width": 23, "height": 31}}, "243": {"xOffset": 3, "yOffset": 1, "xAdvance": 22, "rect": {"x": 283, "y": 226, "width": 23, "height": 31}}, "244": {"xOffset": 3, "yOffset": 1, "xAdvance": 22, "rect": {"x": 260, "y": 226, "width": 23, "height": 31}}, "245": {"xOffset": 3, "yOffset": 2, "xAdvance": 22, "rect": {"x": 94, "y": 226, "width": 23, "height": 31}}, "249": {"xOffset": 4, "yOffset": 1, "xAdvance": 22, "rect": {"x": 117, "y": 226, "width": 21, "height": 31}}, "250": {"xOffset": 4, "yOffset": 1, "xAdvance": 22, "rect": {"x": 138, "y": 226, "width": 21, "height": 31}}, "253": {"xOffset": 1, "yOffset": 1, "xAdvance": 19, "rect": {"x": 117, "y": 45, "width": 23, "height": 39}}, "258": {"xOffset": 2, "yOffset": -4, "xAdvance": 24, "rect": {"x": 370, "y": 84, "width": 27, "height": 36}}, "259": {"xOffset": 2, "yOffset": 2, "xAdvance": 19, "rect": {"x": 187, "y": 193, "width": 20, "height": 31}}, "272": {"xOffset": 1, "yOffset": 2, "xAdvance": 27, "rect": {"x": 119, "y": 257, "width": 29, "height": 30}}, "273": {"xOffset": 2, "yOffset": 1, "xAdvance": 23, "rect": {"x": 211, "y": 226, "width": 26, "height": 31}}, "296": {"xOffset": 1, "yOffset": -4, "xAdvance": 10, "rect": {"x": 260, "y": 84, "width": 14, "height": 36}}, "297": {"xOffset": 1, "yOffset": 2, "xAdvance": 9, "rect": {"x": 174, "y": 257, "width": 14, "height": 30}}, "360": {"xOffset": 4, "yOffset": -4, "xAdvance": 26, "rect": {"x": 157, "y": 84, "width": 25, "height": 37}}, "361": {"xOffset": 4, "yOffset": 2, "xAdvance": 22, "rect": {"x": 159, "y": 226, "width": 21, "height": 31}}, "416": {"xOffset": 2, "yOffset": -1, "xAdvance": 28, "rect": {"x": 415, "y": 158, "width": 30, "height": 33}}, "417": {"xOffset": 3, "yOffset": 6, "xAdvance": 22, "rect": {"x": 456, "y": 287, "width": 25, "height": 26}}, "431": {"xOffset": 4, "yOffset": -2, "xAdvance": 27, "rect": {"x": 444, "y": 122, "width": 30, "height": 35}}, "432": {"xOffset": 4, "yOffset": 5, "xAdvance": 22, "rect": {"x": 339, "y": 287, "width": 25, "height": 28}}, "7840": {"xOffset": 2, "yOffset": 2, "xAdvance": 24, "rect": {"x": 46, "y": 84, "width": 27, "height": 38}}, "7841": {"xOffset": 2, "yOffset": 9, "xAdvance": 19, "rect": {"x": 228, "y": 193, "width": 20, "height": 31}}, "7842": {"xOffset": 2, "yOffset": -7, "xAdvance": 24, "rect": {"x": 166, "y": 45, "width": 27, "height": 39}}, "7843": {"xOffset": 2, "yOffset": 0, "xAdvance": 19, "rect": {"x": 466, "y": 158, "width": 20, "height": 33}}, "7844": {"xOffset": 2, "yOffset": -7, "xAdvance": 24, "rect": {"x": 0, "y": 45, "width": 27, "height": 39}}, "7845": {"xOffset": 2, "yOffset": -2, "xAdvance": 19, "rect": {"x": 182, "y": 158, "width": 24, "height": 35}}, "7846": {"xOffset": 2, "yOffset": -6, "xAdvance": 24, "rect": {"x": 478, "y": 45, "width": 27, "height": 39}}, "7847": {"xOffset": 2, "yOffset": -2, "xAdvance": 19, "rect": {"x": 226, "y": 158, "width": 21, "height": 35}}, "7848": {"xOffset": 2, "yOffset": -8, "xAdvance": 24, "rect": {"x": 398, "y": 0, "width": 27, "height": 40}}, "7849": {"xOffset": 2, "yOffset": -3, "xAdvance": 19, "rect": {"x": 348, "y": 84, "width": 22, "height": 36}}, "7850": {"xOffset": 2, "yOffset": -8, "xAdvance": 24, "rect": {"x": 274, "y": 0, "width": 27, "height": 41}}, "7851": {"xOffset": 2, "yOffset": -2, "xAdvance": 19, "rect": {"x": 162, "y": 158, "width": 20, "height": 35}}, "7852": {"xOffset": 2, "yOffset": -4, "xAdvance": 24, "rect": {"x": 75, "y": 0, "width": 27, "height": 44}}, "7853": {"xOffset": 2, "yOffset": 1, "xAdvance": 19, "rect": {"x": 425, "y": 0, "width": 20, "height": 39}}, "7854": {"xOffset": 2, "yOffset": -7, "xAdvance": 24, "rect": {"x": 428, "y": 45, "width": 27, "height": 39}}, "7855": {"xOffset": 2, "yOffset": -2, "xAdvance": 19, "rect": {"x": 119, "y": 158, "width": 20, "height": 35}}, "7856": {"xOffset": 2, "yOffset": -7, "xAdvance": 24, "rect": {"x": 47, "y": 45, "width": 27, "height": 39}}, "7857": {"xOffset": 2, "yOffset": -2, "xAdvance": 19, "rect": {"x": 45, "y": 158, "width": 20, "height": 35}}, "7858": {"xOffset": 2, "yOffset": -9, "xAdvance": 24, "rect": {"x": 167, "y": 0, "width": 27, "height": 42}}, "7859": {"xOffset": 2, "yOffset": -3, "xAdvance": 19, "rect": {"x": 107, "y": 84, "width": 20, "height": 37}}, "7860": {"xOffset": 2, "yOffset": -8, "xAdvance": 24, "rect": {"x": 247, "y": 0, "width": 27, "height": 41}}, "7861": {"xOffset": 2, "yOffset": -2, "xAdvance": 19, "rect": {"x": 240, "y": 84, "width": 20, "height": 36}}, "7862": {"xOffset": 2, "yOffset": -4, "xAdvance": 24, "rect": {"x": 0, "y": 0, "width": 27, "height": 45}}, "7863": {"xOffset": 2, "yOffset": 2, "xAdvance": 19, "rect": {"x": 27, "y": 45, "width": 20, "height": 39}}, "7864": {"xOffset": 4, "yOffset": 2, "xAdvance": 20, "rect": {"x": 26, "y": 84, "width": 20, "height": 38}}, "7865": {"xOffset": 3, "yOffset": 9, "xAdvance": 20, "rect": {"x": 207, "y": 193, "width": 21, "height": 31}}, "7866": {"xOffset": 4, "yOffset": -7, "xAdvance": 20, "rect": {"x": 97, "y": 45, "width": 20, "height": 39}}, "7867": {"xOffset": 3, "yOffset": 0, "xAdvance": 20, "rect": {"x": 0, "y": 193, "width": 21, "height": 33}}, "7868": {"xOffset": 4, "yOffset": -4, "xAdvance": 20, "rect": {"x": 397, "y": 84, "width": 20, "height": 36}}, "7869": {"xOffset": 3, "yOffset": 2, "xAdvance": 20, "rect": {"x": 166, "y": 193, "width": 21, "height": 31}}, "7870": {"xOffset": 4, "yOffset": -7, "xAdvance": 20, "rect": {"x": 74, "y": 45, "width": 23, "height": 39}}, "7871": {"xOffset": 3, "yOffset": -2, "xAdvance": 20, "rect": {"x": 21, "y": 158, "width": 24, "height": 35}}, "7872": {"xOffset": 4, "yOffset": -6, "xAdvance": 20, "rect": {"x": 351, "y": 45, "width": 20, "height": 39}}, "7873": {"xOffset": 3, "yOffset": -2, "xAdvance": 20, "rect": {"x": 474, "y": 122, "width": 21, "height": 35}}, "7874": {"xOffset": 4, "yOffset": -8, "xAdvance": 20, "rect": {"x": 377, "y": 0, "width": 21, "height": 40}}, "7875": {"xOffset": 3, "yOffset": -3, "xAdvance": 20, "rect": {"x": 326, "y": 84, "width": 22, "height": 36}}, "7876": {"xOffset": 4, "yOffset": -8, "xAdvance": 20, "rect": {"x": 301, "y": 0, "width": 20, "height": 41}}, "7877": {"xOffset": 3, "yOffset": -2, "xAdvance": 20, "rect": {"x": 0, "y": 158, "width": 21, "height": 35}}, "7878": {"xOffset": 4, "yOffset": -4, "xAdvance": 20, "rect": {"x": 27, "y": 0, "width": 20, "height": 44}}, "7879": {"xOffset": 3, "yOffset": 1, "xAdvance": 20, "rect": {"x": 193, "y": 45, "width": 21, "height": 39}}, "7880": {"xOffset": 2, "yOffset": -7, "xAdvance": 10, "rect": {"x": 403, "y": 45, "width": 11, "height": 39}}, "7881": {"xOffset": 2, "yOffset": 0, "xAdvance": 9, "rect": {"x": 36, "y": 193, "width": 11, "height": 32}}, "7882": {"xOffset": 4, "yOffset": 2, "xAdvance": 10, "rect": {"x": 98, "y": 84, "width": 9, "height": 38}}, "7883": {"xOffset": 3, "yOffset": 2, "xAdvance": 9, "rect": {"x": 394, "y": 45, "width": 9, "height": 39}}, "7884": {"xOffset": 2, "yOffset": 2, "xAdvance": 28, "rect": {"x": 300, "y": 45, "width": 28, "height": 39}}, "7885": {"xOffset": 3, "yOffset": 9, "xAdvance": 22, "rect": {"x": 100, "y": 193, "width": 23, "height": 31}}, "7886": {"xOffset": 2, "yOffset": -7, "xAdvance": 28, "rect": {"x": 272, "y": 45, "width": 28, "height": 39}}, "7887": {"xOffset": 3, "yOffset": 0, "xAdvance": 22, "rect": {"x": 486, "y": 158, "width": 23, "height": 33}}, "7888": {"xOffset": 2, "yOffset": -7, "xAdvance": 28, "rect": {"x": 349, "y": 0, "width": 28, "height": 40}}, "7889": {"xOffset": 3, "yOffset": -2, "xAdvance": 22, "rect": {"x": 94, "y": 158, "width": 25, "height": 35}}, "7890": {"xOffset": 2, "yOffset": -6, "xAdvance": 28, "rect": {"x": 244, "y": 45, "width": 28, "height": 39}}, "7891": {"xOffset": 3, "yOffset": -2, "xAdvance": 22, "rect": {"x": 139, "y": 158, "width": 23, "height": 35}}, "7892": {"xOffset": 2, "yOffset": -8, "xAdvance": 28, "rect": {"x": 321, "y": 0, "width": 28, "height": 41}}, "7893": {"xOffset": 3, "yOffset": -3, "xAdvance": 22, "rect": {"x": 338, "y": 122, "width": 23, "height": 36}}, "7894": {"xOffset": 2, "yOffset": -8, "xAdvance": 28, "rect": {"x": 132, "y": 0, "width": 28, "height": 42}}, "7895": {"xOffset": 3, "yOffset": -2, "xAdvance": 22, "rect": {"x": 269, "y": 158, "width": 23, "height": 35}}, "7896": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 47, "y": 0, "width": 28, "height": 44}}, "7897": {"xOffset": 3, "yOffset": 1, "xAdvance": 22, "rect": {"x": 455, "y": 45, "width": 23, "height": 39}}, "7898": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 443, "y": 84, "width": 30, "height": 36}}, "7899": {"xOffset": 3, "yOffset": 1, "xAdvance": 22, "rect": {"x": 414, "y": 193, "width": 25, "height": 31}}, "7900": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 144, "y": 122, "width": 30, "height": 36}}, "7901": {"xOffset": 3, "yOffset": 1, "xAdvance": 22, "rect": {"x": 389, "y": 193, "width": 25, "height": 31}}, "7902": {"xOffset": 2, "yOffset": -7, "xAdvance": 28, "rect": {"x": 214, "y": 45, "width": 30, "height": 39}}, "7903": {"xOffset": 3, "yOffset": 0, "xAdvance": 22, "rect": {"x": 390, "y": 158, "width": 25, "height": 33}}, "7904": {"xOffset": 2, "yOffset": -4, "xAdvance": 28, "rect": {"x": 127, "y": 84, "width": 30, "height": 37}}, "7905": {"xOffset": 3, "yOffset": 2, "xAdvance": 22, "rect": {"x": 331, "y": 193, "width": 25, "height": 31}}, "7906": {"xOffset": 2, "yOffset": -1, "xAdvance": 28, "rect": {"x": 194, "y": 0, "width": 30, "height": 42}}, "7907": {"xOffset": 3, "yOffset": 6, "xAdvance": 22, "rect": {"x": 292, "y": 158, "width": 25, "height": 35}}, "7908": {"xOffset": 4, "yOffset": 2, "xAdvance": 26, "rect": {"x": 73, "y": 84, "width": 25, "height": 38}}, "7909": {"xOffset": 4, "yOffset": 10, "xAdvance": 22, "rect": {"x": 48, "y": 226, "width": 21, "height": 31}}, "7910": {"xOffset": 4, "yOffset": -7, "xAdvance": 26, "rect": {"x": 475, "y": 0, "width": 25, "height": 39}}, "7911": {"xOffset": 4, "yOffset": 0, "xAdvance": 22, "rect": {"x": 445, "y": 158, "width": 21, "height": 33}}, "7912": {"xOffset": 4, "yOffset": -4, "xAdvance": 27, "rect": {"x": 55, "y": 122, "width": 30, "height": 36}}, "7913": {"xOffset": 4, "yOffset": 1, "xAdvance": 22, "rect": {"x": 23, "y": 226, "width": 25, "height": 31}}, "7914": {"xOffset": 4, "yOffset": -4, "xAdvance": 27, "rect": {"x": 25, "y": 122, "width": 30, "height": 36}}, "7915": {"xOffset": 4, "yOffset": 1, "xAdvance": 22, "rect": {"x": 471, "y": 193, "width": 25, "height": 31}}, "7916": {"xOffset": 4, "yOffset": -7, "xAdvance": 27, "rect": {"x": 445, "y": 0, "width": 30, "height": 39}}, "7917": {"xOffset": 4, "yOffset": 0, "xAdvance": 22, "rect": {"x": 365, "y": 158, "width": 25, "height": 33}}, "7918": {"xOffset": 4, "yOffset": -4, "xAdvance": 27, "rect": {"x": 182, "y": 84, "width": 30, "height": 37}}, "7919": {"xOffset": 4, "yOffset": 2, "xAdvance": 22, "rect": {"x": 69, "y": 226, "width": 25, "height": 31}}, "7920": {"xOffset": 4, "yOffset": -2, "xAdvance": 27, "rect": {"x": 102, "y": 0, "width": 30, "height": 43}}, "7921": {"xOffset": 4, "yOffset": 5, "xAdvance": 22, "rect": {"x": 202, "y": 122, "width": 25, "height": 36}}, "7922": {"xOffset": 1, "yOffset": -4, "xAdvance": 22, "rect": {"x": 274, "y": 84, "width": 26, "height": 36}}, "7923": {"xOffset": 1, "yOffset": 1, "xAdvance": 19, "rect": {"x": 371, "y": 45, "width": 23, "height": 39}}, "7924": {"xOffset": 1, "yOffset": 2, "xAdvance": 22, "rect": {"x": 0, "y": 84, "width": 26, "height": 38}}, "7925": {"xOffset": 1, "yOffset": 10, "xAdvance": 19, "rect": {"x": 0, "y": 226, "width": 23, "height": 31}}, "7926": {"xOffset": 1, "yOffset": -7, "xAdvance": 22, "rect": {"x": 140, "y": 45, "width": 26, "height": 39}}, "7927": {"xOffset": 1, "yOffset": 0, "xAdvance": 19, "rect": {"x": 224, "y": 0, "width": 23, "height": 41}}, "7928": {"xOffset": 1, "yOffset": -4, "xAdvance": 22, "rect": {"x": 417, "y": 84, "width": 26, "height": 36}}, "7929": {"xOffset": 1, "yOffset": 2, "xAdvance": 19, "rect": {"x": 328, "y": 45, "width": 23, "height": 39}}}, "kerningDict": {}}]], 0, 0, [0], [1], [1]]]]