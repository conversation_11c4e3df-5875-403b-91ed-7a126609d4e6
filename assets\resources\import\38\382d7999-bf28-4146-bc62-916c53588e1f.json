[1, ["89s9G35lNDUqiX8ZE1vopN"], 0, [["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3]], [[0, 0, 1, 2, 3, 4, 5]], [[0, "item2_p3", "\nitem2_p3.png\nsize: 179,179\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nitem2_p3\n  rotate: false\n  xy: 0, 30\n  size: 151, 149\n  orig: 151, 149\n  offset: 0, 0\n  index: -1\nsao\n  rotate: false\n  xy: 151, 151\n  size: 28, 28\n  orig: 30, 30\n  offset: 1, 1\n  index: -1\n", ["item2_p3.png"], {"skeleton": {"hash": "D8NAJuG4yclEGL8rFlg+8UCUBOc", "spine": "3.6.53", "width": 137, "height": 138}, "bones": [{"name": "root"}, {"name": "item", "parent": "root"}, {"name": "sao", "parent": "item", "x": -18.8, "y": 41}], "slots": [{"name": "item", "bone": "item", "attachment": "item2_p3"}, {"name": "sao", "bone": "sao", "color": "ffffff00", "blend": "additive"}], "skins": {"default": {"item": {"item2_p3": {"width": 151, "height": 149}}, "sao": {"sao": {"x": -0.85, "y": 0.31, "width": 30, "height": 30}}}}, "animations": {"animation": {"slots": {"sao": {"color": [{"time": 0, "color": "ffffff00"}], "attachment": [{"time": 0, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}, "sao": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}]}}}, "win": {"slots": {"sao": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "sao"}, {"time": 0.5, "name": "sao"}, {"time": 1, "name": "sao"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}]}, "item": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": 12.54}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.5, "x": 1.177, "y": 1.177}, {"time": 1, "x": 1, "y": 1}]}, "sao": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5, "angle": -88}, {"time": 1, "angle": -132.67}], "translate": [{"time": 0, "x": 39.19, "y": -32.13}, {"time": 0.5, "x": 48.06, "y": -32.75}, {"time": 1, "x": 51.07, "y": -31.81}], "scale": [{"time": 0, "x": 0.633, "y": 0.633}, {"time": 0.5, "x": 1.466, "y": 1.466}, {"time": 1, "x": 1.171, "y": 1.171}]}}}}}, [0]]], 0, 0, [0], [-1], [0]]