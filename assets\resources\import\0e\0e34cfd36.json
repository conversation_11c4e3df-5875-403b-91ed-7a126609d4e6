[1, ["ecpdLyjvZBwrvm+cedCcQy", "89ksg3sNlBNIcaQxAIMzu4", "7a/QZLET9IDreTiBfRn2PD", "adw94Z+hpN57wutNivq8Q5", "a4LbrG+m1FiJQK9CcpaToD", "7fivjktH1En6T0ECoCYrpY", "33HOamjjFPGoj/1rwHcNWy", "66fRxWeMpGa6p8Xl0JIsQT", "76XIlclNBJFY6dbyGQN/Jp", "b1FdnLn49AQY9AUBmOdLZD", "72hOUIvdtMLZ0oT88tRKh8", "5ckbco7cROAJ92hw6PR5eT", "49mwvGKDlEqbNnn0IdTrDT", "3aJeuZLTBALJSElFpk2E1O", "e2nTjevMRIZpJ2dl6Vj6Xz", "c6EQ14nVVHz7ZBi/CZauEk", "997uMAouxMLKv5xv9cD2/W", "37iT7iEaVF9p1SPPB3WsMl", "3blsj1R4hAmrXRnG4wwuKa", "00sEzLsXVFyK3JKnaKIcgf", "c7nDq4DoZHbpolEo6elFVN", "6d6O9domlJXZGGGI3ppw/1", "44nY6WoHJMjaMi/wNjaMpE", "30ALdNuPxBPoUkSzF+gJH+", "cfv0eRkQJC5b4n+SQEwiJN", "7cjssSIZJGZZX4I7RHGslO", "c9dXdaSSZG+Z02KOK4DkqW", "3fOJXEpOxNaKyQkTLMlCNf", "01xtkNTo1M6qkOD+J5h70z", "8cpi5YpNRMzqf5CV4gyTvo", "d95bvZH75CwoRMYVic+jmC", "f26343FIdChJbsteFZN2Jb", "3b0DFQrkRPZpAdhAHpGPrY", "a4MyH+SulBvIrr1tjhz/p3", "e43/4YI8JFzpujLjPzLBTm", "e1Q3BX/kNPCoNe5rsSOD0V", "3f6x9pyblFtrc7o1yxx2Gp", "6aiKTm6UBMWIqn5wFwMhsp", "a7KPRgHApB/bu1PtDYmNvc", "06dasMEAdCQY4sW3w/r/8h", "00ta/4M4ZD0IdpJTuhks/m", "7cJTLBFK9OuI73yN8QqVoe", "74HyseAK5EoZUJoxVzNBs6", "77wAbDnElABbQH4hahkycC", "7fECiI7IxLT6skbAkxc5oK", "9fjKrW7nhIvIA1HIPt5AZn", "05P7O8r95Ixo93+QYLSCf+", "d4pCckDi5DorBRkClQSCY+", "13Khd5zGRIfZTCYMYiCgfe", "8eeaiVinRKUK8/IUPYsL6j", "26zkpNznRDHq2V4ZpKYUSN", "c5QYCYtR5JjbviMPHQCspd", "9dOZW6G1pPoLc78vOToCvJ", "a8Xv6tDNVP+LPeN73Bx5Ep", "9aHYW2sBNIGJzzBeyAouiQ", "7dvxA6RF9J/aHJ3p+TFgpR", "b7iPCD4XVF8bWeD8E/408l", "d3yRBlJSBH0YCCnLEoCMZm", "cfCCkOSwZFhKUES1iBUmHI", "ee44TJJN5MpKNM7IBoC9WP", "fcxc9lC7ZDfZPDnGBL4d1B", "2flJsucyZD94lNNqhnd0Nw", "a5UJuxto5AtrayVrOEZwDp", "81oHDYPw1C8K0XKmTtZHGZ", "13i+DCrV5McoGCadgaEp9n", "46hWDhev5Bk5FssUoaezXs", "11wZlpR2JKcZgfS2vXreB0", "c5iKXIj0RKJajnXzk7ZS3J", "85Q7gXMnBO8JBGOvi5jp+k", "b1oDHnCRJJk69mj8A6S+J7", "84zX8gXEpO15sH3Ma4X7Y2", "02bTuDIB9Nw4zmfIf0svHw", "bbDGGeDppFhLwou5cukIMN", "3diJQjMthOppQvyvxdjUGy", "82CJEfNtZNxLXBiqsuKrA9", "e1t51ztH1M1pjX9HxeaXID", "52yj5QybhDsoX/aLA28GJK", "f4VBK8/UNIfYgm+XZHeJ72", "90zjGjYG5BbbeZySzzfxSI", "b9O5mBZy5BPoDSHVBkuZFF", "4dZLiS9YBADbmD6JzctCHA", "49zkABBnZPsIWSMAAiJ4w4", "30oZv4Nw9Kr7gUaHONCYxb", "f1xDt8cPdIXaHQFRjTfZaq", "f9BkCPX7xM2rtYFacHyy0P", "25rzJFoslOtJtYUkz1Bhky", "acbxJjdVxJ06mjwyk87HX5", "d1aszK8GhBhp0W9E5+x8AP", "e3eOK2jOFIq5Aan5nCveQC", "f8UjW2pt5BzbR0YBylwufK", "75V2KSCfVMJbwnDNFoBJtY", "a33UZXc/5Pzo5TFjSuAxtE", "eaMb4+jW9NsJHi2IajVb9+", "11ZMoHtZRAiJs3DsxWk5uE", "8c6MaBB4FNg5VRStDu13OA", "cdIa6sxYZBPbu+3lMSSrCP", "f0jwjWQBZMJoR/9qG8ndYb", "95mwlsYo9MjrKCOCHaLmeN", "88blqDQCFKBJSfTSpHhJOn", "aaigp18sdKOqQCISl7HwTo", "7fTgemWOtACL8rbWjtl/a8", "3a5IRpIb5HGpte8k4vxQEe", "acANKYuHhEVK0miaLGDfQA", "d9Ae7XVCBNJL7OO/FXlEGq", "80C5jYJdlGe7vIUufxNWo/", "41Tcd8V6VA+ZFhZ1nRNs1N", "51Gn1twE1F5ZuY8dqK9iPt", "8buGxcZLlIgKBdJEgbdpW/", "68afDrDJFNapCADDXrxJW7", "c7Cr5u1txGhqsLsa5OBsbc", "54WDSJgt9OmoAy4chYoACL", "84J6uh1otNBrvVaRTTFyVr", "7bAx6AMLVPRoY5+qSQxaKk", "36zvRTtmxK2KsrTn1llpQd", "69pAWPklpKsr2XiuIrpOpR", "99+3k8XEhB7bPyPw9yuYyO", "bdgczPFupIyJ9jzxCcip+K", "b729PNVmNG85YT5WG5BvOv", "79OcJuvCVAiYSfaCDTDFUx", "a1owtECS1LDaLVnnOHHkYE", "5fXBKtwBxCzIII00mW7nhQ", "eeSWR9dI1Dr42zuA3EqiGC", "20XZh1m+pPdrVSnKympaqW", "44wjLzlKZFgZX4Qc0aGSoy", "8eMUXh+4ZIpoSB6tQ0YEfd", "aaJgsiJPBEN4PslFD5zSbw", "557H0APixJ05RpB8DOSVjS", "93WB3XkHlHrLAXhYc12LQa", "c8np2ZJ35AArIklpByAnqn", "95Nw0ix6lIJ7GAbQBAIZTv", "7eER7eh+ZITaQD0F4CoYCF", "a2pzdOB8pJLr5DfvfUzx6R", "b4/FAuRl1POIuXU3ZBKQru", "5aNmDKAwRM6qI8htdVYAq7", "53An/o0EdMeIo2adbWuDcY", "7fDJ02s/BKe4wemc2C1Zwp", "5a4v0mQxFAU5yICd6OkdFi", "a0bo14Z9RAIL/aUe6iWown", "12iANgV85PA7Agyz1Ckd4B", "c2BjPqnTVBSrl1Nqflifah", "5dv1Ow6aVKRJ3ymLNLYU8Q", "03imSMmSVGP5AchfHlMals", "eftIAzzGRBEomtnvRP9A2T", "a8mEefBkNLMoOlue+Ske+Q", "6cysFjkBlNwJYQP6S/EQco", "0dmktWNGJFQIVIVOmj104x", "74pzzXm3NHFImIvfZoIs+I", "59P5qqQZRA7aMacGri5FDa"], ["node", "_spriteFrame", "_N$file", "_N$target", "_textureSetter", "_N$skeletonData", "_parent", "_clip", "label", "_defaultClip", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "btnNodeHo", "btnNodeHoa", "btnNodeRong", "nodeParentChat", "nodeBetSides", "nodeCardStart", "nodeCardBurn", "nodeCardHo", "nodeCardRong", "lbTotalBetHo", "lbTotalBetHoa", "lbTotalBetRong", "lbTotalUserWin", "lbTotalUser", "lbTimerPrepare", "lbTimer", "lbSID", "labelWin", "popUpWin", "pos<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodePosGroupUser", "nodeChipsStartMove", "nodeChipsHo", "nodeChipsHoa", "nodeChipsRong", "nodeBetAgain", "nodeBetX2", "lbTigerBet", "lbTideBet", "lbDragonBet", "layoutButtonBets", "animationCard", "skeletonBurn", "skeletonBox", "spriteMusic", "spriteSound", "nodeOffset", "animation", "getCoin", "selectChip", "cardOpen", "cardBurnMoveToBox", "cardsSlideHo", "cardsSlideRong", "cardsSlideBurn", "cardsSlide", "betSound", "backgroundSound", "resultAnimation", "bgWinHo", "bgWinHoa", "bgWinRong", "lbMessage", "data", "prefabCardBack", "spriteCardDefault", "prefabChat", "prefabHelp", "prefabHistory", "prefabTop", "prefabGraph", "prefabGroupUser"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_prefab", "_parent", "_contentSize", "_components", "_trs", "_children", "_color", "_eulerAngles"], 1, 4, 1, 5, 9, 7, 12, 5, 5], ["cc.Node", ["_name", "_active", "_opacity", "_children", "_prefab", "_parent", "_components", "_contentSize", "_trs", "_anchorPoint"], 0, 2, 4, 1, 9, 5, 7, 5], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_string", "_N$overflow", "_spacingX", "_enableWrapText", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "_enabled", "_srcBlendFactor", "node", "_materials", "_spriteFrame"], -2, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_color"], 1, 1, 2, 4, 5, 7, 5, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children"], 1, 1, 12, 4, 5, 7, 12], ["cc.Layout", ["_N$layoutType", "_N$spacingX", "_resize", "_N$paddingLeft", "_N$paddingRight", "_N$verticalDirection", "_N$affectedByScale", "_enabled", "_N$paddingTop", "_N$paddingBottom", "_N$spacingY", "node", "_layoutSize", "_N$cellSize"], -8, 1, 5, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "node", "_N$normalColor", "clickEvents", "_N$target", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite"], 1, 1, 5, 9, 1, 6, 6, 6, 6], ["sp.Skeleton", ["_preCacheMode", "defaultSkin", "defaultAnimation", "_animationName", "premultipliedAlpha", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "wrapMode", "speed", "curveData"], -1, 11], ["cc.Node", ["_name", "_active", "_children", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_anchorPoint"], 1, 2, 12, 4, 5, 7, 1, 5], ["cc.AudioSource", ["_volume", "_loop", "node"], 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node", "label"], 3, 1, 1], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.AnimationClip", ["_name", "_duration", "wrapMode", "curveData"], -1], ["cc.Prefab", ["_name"], 2], ["76024KUhhFBtabZIY7jMFca", ["node", "lbSID", "lbTimer", "lbTimerPrepare", "lbTotalUser", "lbTotalUserWin", "lbTotalBetRong", "lbTotalBetHoa", "lbTotalBetHo", "nodeCardRong", "nodeCardHo", "nodeCardBurn", "nodeCardStart", "nodeBetSides", "nodeParentChat", "prefabCardBack", "spriteCardDefault", "prefabChat"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6], ["da958BXEnRMGZF0W+LkzH8h", ["node", "popUpWin", "labelWin", "prefabHelp", "prefabHistory", "prefabTop", "prefabGraph", "prefabGroupUser"], 3, 1, 1, 1, 6, 6, 6, 6, 6], ["ad3c4mNFMlJNqQRWI3muDWb", ["node", "prefabsChip", "nodeChipsRong", "nodeChipsHoa", "nodeChipsHo", "nodeChipsStartMove", "lstPosChip", "nodePosGroupUser", "lstPosPlayer", "pos<PERSON><PERSON><PERSON><PERSON><PERSON>"], 3, 1, 3, 1, 1, 1, 1, 2, 1, 2, 1], ["589e1jMA7ZDxK+OuSO2vzVH", ["node", "lstPlayers"], 3, 1, 2], ["9045aSGcglIarpLEVHVnxFT", ["node", "layoutButtonBets", "lbDragonBet", "lbTideBet", "lbTigerBet", "nodeBetX2", "nodeBetAgain"], 3, 1, 1, 1, 1, 1, 1, 1], ["0b076F8GwNO86Hb3iQ1s6EH", ["node", "animationCard"], 3, 1, 1], ["cd406Zuov5IzoegwnkLOrFu", ["node", "skeletonBox", "skeletonBurn"], 3, 1, 1, 1], ["3301eErm8FOHK6xupffz1SU", ["node", "btnNodeRong", "btnNodeHoa", "btnNodeHo"], 3, 1, 1, 1, 1], ["55b94jcr75E0KW3Dy4NWZW8", ["node", "lstSession", "spSessions"], 3, 1, 2, 3], ["ad402R7yshJsYOag7Xuj6+9", ["node", "animation", "nodeOffset", "spriteSound", "spriteMusic", "sfSounds", "sfMusics"], 3, 1, 1, 1, 1, 1, 3, 3], ["2698dggu0FLCZ4tn8pq02NI", ["node", "backgroundSound", "betSound", "cardsSlide", "cardsSlideBurn", "cardsSlideRong", "cardsSlideHo", "cardBurnMoveToBox", "cardOpen", "selectChip", "getCoin"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["c27b9EGSHFI1psbUOsV9dXt", ["node", "bgWinRong", "bgWinHoa", "bgWinHo", "btnNodeRong", "btnNodeHoa", "btnNodeHo", "resultAnimation"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["f386fw8a7JDJaeLG5L+qtXR", ["node", "spriteButtons"], 3, 1, 3], ["832cdd6gThBUZ3q0q4hjRy5", ["messWinPosY", "node", "nodeMessage", "lbMessage"], 2, 1, 1, 1], ["b5964xPIH1BUbpO82T+GdIa", ["node"], 3, 1], ["cc.Mask", ["_enabled", "_segments", "_N$alphaThreshold", "node", "_materials"], 0, 1, 3], ["fde5fYTdUdLFaHQ7QSWDYdb", ["node"], 3, 1], ["00a651GqNBONKtApqdWrOwB", ["node", "lbSID", "lbName"], 3, 1, 1, 1], ["cc.AudioClip", ["_name", "_native", "duration"], 0]], [[14, 0, 1, 2], [2, 0, 5, 3, 6, 4, 7, 8, 2], [1, 0, 3, 5, 2, 4, 6, 2], [4, 5, 6, 7, 1], [7, 0, 2, 3, 4, 5, 6, 2], [1, 0, 3, 2, 6, 2], [6, 1, 2, 1], [9, 0, 2, 4, 3, 5, 2], [1, 0, 3, 5, 2, 4, 2], [4, 0, 5, 6, 7, 2], [1, 0, 1, 3, 5, 2, 4, 6, 3], [5, 0, 2, 3, 4, 5, 6, 2], [4, 0, 5, 6, 2], [9, 2, 3, 5, 1], [1, 0, 3, 5, 2, 8, 4, 2], [4, 1, 0, 5, 6, 7, 3], [15, 0, 1, 2, 3, 4], [10, 1, 2, 0, 3, 5, 6, 7, 5], [5, 0, 2, 3, 4, 2], [15, 0, 1, 3, 3], [3, 5, 0, 1, 2, 3, 9, 10, 11, 6], [2, 0, 1, 5, 3, 4, 3], [3, 5, 0, 4, 1, 2, 3, 6, 9, 10, 11, 8], [1, 0, 3, 2, 4, 6, 2], [5, 0, 1, 2, 3, 4, 8, 5, 6, 3], [8, 2, 0, 1, 11, 12, 4], [4, 1, 0, 2, 5, 6, 7, 4], [33, 0, 1, 1], [16, 0, 1], [3, 5, 0, 1, 2, 3, 9, 6], [3, 5, 0, 8, 1, 2, 3, 9, 10, 7], [37, 0, 1], [38, 0, 1, 2, 1], [13, 2, 1], [12, 0, 7, 2, 3, 4, 5, 6, 2], [10, 2, 0, 4, 3, 5, 6, 7, 5], [3, 0, 1, 2, 3, 9, 11, 5], [39, 0, 1, 2, 4], [17, 0, 1, 2, 3, 4, 5], [2, 0, 5, 3, 4, 8, 2], [1, 0, 1, 3, 5, 2, 4, 3], [2, 0, 5, 3, 6, 4, 7, 2], [16, 0, 1, 1], [3, 0, 4, 1, 2, 3, 6, 9, 10, 7], [11, 0, 1, 4, 3], [11, 0, 1, 3, 2, 4, 5], [4, 5, 6, 1], [9, 1, 0, 2, 4, 3, 5, 6, 7, 8, 9, 3], [32, 0, 1], [3, 0, 4, 1, 2, 3, 9, 10, 6], [11, 0, 1, 2, 4, 4], [1, 0, 3, 5, 2, 4, 6, 9, 2], [1, 0, 1, 3, 2, 4, 6, 3], [7, 0, 1, 2, 3, 4, 5, 6, 3], [2, 0, 5, 3, 6, 4, 7, 9, 8, 2], [2, 0, 5, 3, 6, 4, 2], [2, 0, 5, 3, 4, 2], [2, 0, 5, 3, 4, 7, 8, 2], [4, 3, 1, 0, 5, 6, 7, 4], [10, 1, 0, 4, 5, 6, 4], [10, 1, 2, 0, 4, 3, 5, 6, 7, 6], [3, 0, 4, 1, 2, 6, 9, 10, 6], [13, 0, 2, 2], [18, 0, 1, 2, 3, 5], [19, 0, 2], [1, 0, 7, 5, 2, 2], [1, 0, 3, 2, 4, 2], [1, 0, 3, 7, 2, 2], [1, 0, 3, 5, 2, 8, 4, 6, 2], [1, 0, 3, 2, 2], [7, 0, 2, 7, 3, 4, 5, 2], [7, 0, 2, 7, 3, 4, 6, 2], [2, 0, 3, 6, 4, 7, 2], [2, 0, 3, 6, 4, 7, 8, 2], [2, 0, 1, 2, 3, 6, 4, 7, 4], [2, 0, 2, 5, 3, 6, 4, 8, 3], [2, 0, 3, 6, 4, 7, 9, 8, 2], [12, 0, 2, 3, 4, 5, 8, 6, 2], [12, 0, 1, 7, 2, 3, 4, 5, 6, 3], [5, 0, 2, 3, 4, 5, 7, 6, 2], [5, 0, 2, 3, 4, 5, 2], [5, 0, 1, 2, 3, 4, 5, 6, 3], [14, 1, 1], [20, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 1], [21, 0, 1, 2, 3, 4, 5, 6, 7, 1], [22, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [23, 0, 1, 1], [24, 0, 1, 2, 3, 4, 5, 6, 1], [25, 0, 1, 1], [26, 0, 1, 2, 1], [27, 0, 1, 2, 3, 1], [8, 0, 3, 4, 1, 5, 6, 11, 12, 13, 7], [8, 7, 2, 0, 8, 9, 10, 11, 12, 7], [8, 2, 0, 1, 11, 12, 13, 4], [28, 0, 1, 2, 1], [29, 0, 1, 2, 3, 4, 5, 6, 1], [4, 4, 0, 2, 5, 6, 4], [30, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [31, 0, 1, 2, 3, 4, 5, 6, 7, 1], [6, 1, 2, 3, 1], [6, 0, 1, 2, 3, 2], [6, 1, 1], [6, 0, 1, 2, 2], [9, 0, 2, 4, 3, 2], [34, 0, 1, 2, 3, 2], [35, 0, 1], [3, 0, 1, 2, 3, 9, 10, 11, 5], [3, 0, 1, 2, 3, 9, 10, 5], [3, 0, 4, 1, 7, 2, 3, 9, 10, 7], [3, 5, 0, 1, 2, 3, 6, 9, 10, 7], [3, 5, 0, 4, 8, 1, 7, 2, 6, 9, 10, 9], [36, 0, 1, 2, 3, 4, 4], [13, 0, 1, 2, 3]], [[[{"name": "D_Hoa", "rect": [0, 0, 164, 193], "offset": [0, 0], "originalSize": [164, 193], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [32]], [[{"name": "bottom_bar", "rect": [0, 0, 1233, 91], "offset": [0, 0], "originalSize": [1233, 91], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [33]], [[[38, "Dragon", "\r\nDragon.png\r\nsize: 670, 876\r\nformat: RGBA8888\r\nfilter: Linear, Linear\r\nrepeat: none\r\nDragon\r\n  rotate: false\r\n  xy: 384, 9\r\n  size: 284, 387\r\n  orig: 284, 387\r\n  offset: 0, 0\r\n  index: -1\r\nLayer 5\r\n  rotate: false\r\n  xy: 503, 636\r\n  size: 98, 86\r\n  orig: 98, 86\r\n  offset: 0, 0\r\n  index: -1\r\nLayer 6\r\n  rotate: false\r\n  xy: 503, 724\r\n  size: 151, 150\r\n  orig: 151, 150\r\n  offset: 0, 0\r\n  index: -1\r\nclose_eyes\r\n  rotate: true\r\n  xy: 603, 618\r\n  size: 104, 58\r\n  orig: 104, 58\r\n  offset: 0, 0\r\n  index: -1\r\nlight1\r\n  rotate: true\r\n  xy: 2, 2\r\n  size: 394, 380\r\n  orig: 394, 380\r\n  offset: 0, 0\r\n  index: -1\r\nlight2\r\n  rotate: false\r\n  xy: 2, 398\r\n  size: 499, 476\r\n  orig: 499, 476\r\n  offset: 0, 0\r\n  index: -1\r\n", ["Dragon.png"], {"skins": {"default": {"close_eyes": {"close_eyes": {"rotation": -103.69, "x": 8.32, "width": 104, "y": -11.05, "height": 58}}, "light1": {"light1": {"x": 21.07, "width": 394, "y": 0.65, "height": 380}}, "Dragon": {"Dragon": {"width": 284, "type": "mesh", "hull": 232, "height": 387, "triangles": [86, 87, 85, 87, 88, 85, 85, 88, 84, 84, 88, 83, 83, 81, 82, 83, 88, 81, 88, 89, 81, 89, 90, 81, 90, 80, 81, 90, 91, 80, 91, 92, 80, 80, 115, 79, 80, 114, 115, 80, 92, 114, 79, 115, 116, 92, 93, 114, 98, 99, 97, 97, 99, 96, 93, 113, 114, 93, 102, 113, 102, 93, 95, 93, 94, 95, 95, 96, 100, 96, 99, 100, 95, 101, 102, 95, 100, 101, 113, 102, 112, 102, 103, 112, 109, 112, 103, 109, 103, 104, 111, 109, 110, 111, 112, 109, 106, 107, 105, 104, 105, 108, 105, 107, 108, 104, 108, 109, 74, 75, 73, 75, 77, 73, 73, 240, 72, 73, 77, 240, 72, 240, 71, 75, 76, 77, 240, 77, 78, 78, 241, 240, 233, 240, 241, 78, 79, 241, 79, 116, 241, 116, 117, 241, 233, 241, 117, 63, 64, 238, 64, 239, 238, 238, 237, 63, 62, 63, 61, 59, 61, 237, 237, 61, 63, 238, 249, 237, 57, 59, 54, 59, 237, 255, 54, 59, 255, 54, 255, 53, 59, 60, 61, 237, 236, 255, 59, 57, 58, 54, 55, 57, 57, 55, 56, 65, 239, 64, 65, 66, 239, 239, 232, 238, 232, 235, 238, 235, 249, 238, 235, 233, 245, 245, 233, 244, 237, 249, 250, 237, 250, 236, 245, 247, 235, 235, 248, 249, 235, 247, 248, 244, 246, 245, 245, 246, 247, 249, 248, 250, 250, 251, 236, 250, 248, 251, 251, 252, 236, 252, 251, 248, 41, 266, 267, 34, 41, 30, 30, 41, 267, 41, 34, 40, 34, 30, 31, 40, 35, 39, 40, 34, 35, 38, 35, 36, 38, 39, 35, 36, 37, 38, 34, 32, 33, 34, 31, 32, 255, 256, 53, 256, 51, 53, 256, 262, 51, 255, 254, 256, 257, 259, 258, 257, 254, 259, 256, 254, 257, 257, 260, 256, 256, 260, 262, 257, 258, 260, 53, 51, 52, 258, 259, 260, 259, 261, 260, 259, 285, 261, 51, 262, 50, 50, 262, 263, 49, 263, 48, 263, 49, 50, 260, 261, 262, 285, 286, 261, 261, 286, 262, 285, 281, 286, 285, 280, 281, 286, 266, 262, 262, 266, 263, 45, 46, 44, 46, 47, 44, 44, 47, 43, 263, 266, 48, 286, 281, 266, 47, 48, 43, 48, 42, 43, 48, 266, 42, 281, 268, 266, 42, 266, 41, 266, 268, 267, 30, 23, 24, 30, 267, 23, 267, 20, 23, 20, 21, 23, 268, 269, 267, 267, 269, 20, 268, 4, 269, 30, 25, 29, 30, 24, 25, 28, 26, 27, 26, 28, 25, 28, 29, 25, 4, 5, 269, 269, 5, 14, 269, 15, 20, 15, 269, 14, 8, 6, 7, 14, 5, 8, 23, 21, 22, 19, 20, 16, 18, 19, 17, 16, 20, 15, 17, 19, 16, 6, 8, 5, 14, 12, 13, 14, 8, 12, 11, 9, 10, 11, 12, 9, 12, 8, 9, 270, 268, 281, 169, 270, 280, 270, 169, 277, 169, 280, 158, 155, 156, 158, 156, 157, 158, 159, 168, 158, 158, 168, 169, 159, 160, 162, 160, 161, 162, 159, 162, 168, 168, 162, 167, 162, 163, 167, 163, 166, 167, 166, 164, 165, 166, 163, 164, 169, 170, 277, 172, 173, 175, 170, 183, 194, 277, 170, 197, 173, 174, 175, 172, 175, 171, 175, 183, 171, 170, 171, 183, 175, 176, 182, 170, 194, 197, 197, 194, 196, 194, 195, 196, 176, 177, 182, 175, 182, 183, 177, 178, 181, 177, 181, 182, 180, 181, 179, 181, 178, 179, 194, 184, 193, 194, 183, 184, 184, 185, 193, 197, 198, 277, 198, 201, 277, 193, 185, 192, 185, 186, 192, 199, 200, 198, 192, 186, 191, 186, 187, 191, 191, 187, 190, 190, 188, 189, 188, 190, 187, 281, 280, 270, 273, 276, 270, 268, 270, 274, 270, 276, 274, 268, 274, 4, 4, 274, 221, 226, 4, 221, 226, 221, 222, 4, 226, 3, 274, 220, 221, 3, 226, 2, 222, 223, 225, 222, 225, 226, 225, 223, 224, 226, 227, 2, 227, 1, 2, 0, 227, 228, 227, 0, 1, 0, 228, 231, 231, 228, 230, 228, 229, 230, 276, 208, 209, 276, 209, 275, 209, 210, 275, 274, 275, 219, 219, 275, 218, 275, 274, 276, 274, 219, 220, 210, 211, 275, 218, 275, 212, 275, 211, 212, 218, 212, 217, 216, 217, 213, 217, 212, 213, 216, 213, 215, 215, 213, 214, 277, 273, 270, 277, 201, 273, 198, 200, 201, 201, 202, 273, 202, 203, 273, 203, 208, 273, 276, 273, 208, 203, 204, 208, 204, 207, 208, 205, 206, 204, 204, 206, 207, 255, 236, 253, 255, 253, 254, 236, 252, 253, 247, 246, 265, 248, 247, 265, 265, 246, 264, 252, 248, 265, 264, 283, 265, 264, 282, 283, 123, 282, 122, 253, 252, 284, 252, 265, 284, 265, 283, 284, 253, 284, 259, 253, 259, 254, 259, 284, 285, 130, 272, 129, 129, 272, 128, 130, 131, 272, 125, 126, 124, 126, 127, 124, 128, 272, 127, 282, 123, 278, 278, 123, 124, 131, 141, 272, 141, 142, 272, 272, 142, 127, 131, 140, 141, 140, 132, 139, 140, 131, 132, 124, 127, 278, 282, 279, 283, 282, 278, 279, 142, 271, 127, 127, 271, 278, 283, 280, 284, 283, 279, 280, 284, 280, 285, 139, 133, 138, 139, 132, 133, 142, 143, 271, 271, 143, 144, 133, 137, 138, 133, 134, 137, 154, 279, 278, 144, 152, 271, 152, 153, 271, 278, 271, 153, 134, 136, 137, 134, 135, 136, 144, 151, 152, 151, 144, 145, 151, 145, 146, 146, 150, 151, 158, 280, 279, 154, 278, 153, 146, 147, 150, 149, 147, 148, 149, 150, 147, 154, 155, 158, 279, 154, 158, 244, 233, 234, 118, 233, 117, 243, 233, 118, 118, 119, 242, 119, 120, 242, 233, 243, 234, 243, 118, 242, 246, 244, 264, 234, 243, 244, 264, 244, 243, 243, 242, 264, 264, 242, 282, 242, 120, 282, 120, 121, 282, 282, 121, 122, 68, 69, 232, 70, 71, 69, 69, 71, 232, 232, 71, 233, 71, 240, 233, 233, 235, 232, 66, 67, 239, 239, 67, 232, 67, 68, 232], "uvs": [0.59032, 0.01476, 0.62581, 0.05246, 0.63587, 0.09599, 0.6311, 0.14691, 0.61945, 0.18655, 0.66129, 0.15974, 0.67507, 0.13136, 0.70155, 0.12475, 0.71479, 0.13175, 0.76405, 0.10415, 0.77676, 0.10415, 0.77888, 0.11504, 0.73809, 0.14924, 0.7418, 0.16012, 0.73047, 0.17378, 0.72934, 0.1902, 0.77579, 0.18334, 0.80014, 0.19124, 0.79958, 0.20163, 0.77239, 0.20163, 0.75865, 0.20751, 0.78899, 0.20652, 0.81461, 0.2085, 0.81393, 0.22186, 0.84158, 0.22334, 0.88135, 0.23521, 0.91438, 0.22334, 0.92652, 0.22482, 0.92787, 0.24165, 0.89146, 0.25896, 0.85843, 0.26391, 0.88068, 0.27776, 0.91438, 0.31388, 0.91934, 0.35016, 0.91042, 0.35708, 0.9481, 0.36944, 0.97933, 0.36107, 1, 0.3538, 1, 0.37672, 0.96793, 0.40546, 0.91686, 0.42511, 0.88166, 0.42875, 0.87819, 0.43639, 0.90149, 0.46295, 0.90447, 0.49824, 0.89188, 0.53158, 0.8665, 0.52835, 0.84348, 0.51469, 0.81234, 0.50004, 0.83164, 0.53084, 0.83367, 0.56114, 0.82805, 0.58071, 0.85002, 0.57682, 0.84471, 0.60073, 0.85305, 0.60962, 0.8682, 0.60128, 0.89622, 0.59906, 0.88486, 0.63074, 0.91061, 0.62908, 0.89698, 0.65742, 0.95076, 0.68855, 0.98465, 0.76107, 0.97137, 0.86381, 0.88041, 0.93581, 0.72405, 0.97856, 0.61183, 1, 0.53699, 1, 0.5003, 0.97949, 0.43847, 0.97401, 0.38275, 0.95706, 0.36904, 0.95887, 0.3049, 0.92068, 0.29159, 0.93267, 0.25286, 0.93844, 0.23834, 0.94865, 0.18328, 0.92334, 0.17783, 0.90958, 0.21051, 0.87627, 0.20325, 0.8443, 0.18873, 0.81677, 0.16755, 0.8181, 0.17118, 0.83187, 0.18631, 0.8412, 0.18994, 0.8634, 0.17844, 0.88027, 0.14697, 0.89892, 0.11611, 0.90381, 0.11006, 0.89448, 0.12822, 0.8785, 0.10643, 0.86562, 0.09021, 0.84683, 0.09021, 0.80885, 0.10207, 0.78591, 0.0929, 0.76692, 0.05139, 0.76454, 0.04924, 0.75347, 0.03468, 0.75703, 0.01312, 0.77602, 0, 0.77958, 0, 0.74832, 0.0377, 0.71803, 0.07997, 0.70873, 0.10673, 0.71975, 0.10486, 0.71355, 0.07903, 0.69528, 0.04005, 0.70011, 0.02455, 0.70114, 0.02502, 0.68357, 0.07386, 0.66634, 0.13679, 0.65669, 0.17717, 0.67151, 0.18046, 0.68357, 0.1598, 0.7101, 0.15651, 0.73044, 0.16966, 0.76249, 0.18751, 0.77317, 0.23494, 0.77455, 0.2401, 0.76731, 0.28049, 0.7232, 0.28688, 0.70256, 0.32839, 0.67561, 0.32232, 0.66834, 0.31754, 0.64538, 0.32807, 0.63014, 0.29434, 0.62928, 0.28168, 0.63732, 0.27347, 0.63381, 0.26764, 0.62175, 0.25567, 0.63129, 0.22452, 0.64535, 0.17354, 0.64435, 0.14102, 0.62426, 0.11819, 0.59438, 0.11037, 0.56935, 0.09219, 0.55601, 0.08884, 0.54144, 0.10617, 0.53796, 0.12714, 0.55047, 0.13944, 0.5675, 0.14392, 0.58227, 0.1551, 0.59212, 0.16963, 0.59233, 0.17914, 0.58494, 0.15733, 0.5716, 0.13693, 0.55416, 0.12602, 0.52872, 0.13586, 0.50346, 0.15628, 0.48521, 0.16297, 0.48557, 0.16419, 0.49141, 0.16188, 0.50266, 0.16419, 0.51985, 0.17287, 0.52027, 0.18531, 0.51433, 0.18242, 0.50266, 0.17143, 0.48801, 0.15494, 0.47634, 0.14711, 0.46822, 0.169, 0.46207, 0.15316, 0.4513, 0.12708, 0.45318, 0.12149, 0.44498, 0.13686, 0.43302, 0.1194, 0.4149, 0.1194, 0.3932, 0.12801, 0.39064, 0.1329, 0.39611, 0.14431, 0.40756, 0.16108, 0.40704, 0.17086, 0.40021, 0.15665, 0.39183, 0.1322, 0.38688, 0.11171, 0.39028, 0.08644, 0.39334, 0.08174, 0.38873, 0.09219, 0.37508, 0.04659, 0.37712, 0.01925, 0.37191, 0, 0.35823, 0, 0.331, 0.0111, 0.32131, 0.03386, 0.31603, 0.07071, 0.32533, 0.09484, 0.32437, 0.06535, 0.30275, 0.03789, 0.26391, 0.0332, 0.20442, 0.05195, 0.14985, 0.08277, 0.13461, 0.11895, 0.13706, 0.12297, 0.15722, 0.1156, 0.18328, 0.11292, 0.23245, 0.12297, 0.2762, 0.13235, 0.28702, 0.13637, 0.27522, 0.15781, 0.27276, 0.17791, 0.27571, 0.18059, 0.25457, 0.16183, 0.23294, 0.17188, 0.22802, 0.19935, 0.23736, 0.21141, 0.21376, 0.24434, 0.19435, 0.23982, 0.18067, 0.21869, 0.16296, 0.22674, 0.15336, 0.25893, 0.1589, 0.28408, 0.18474, 0.29263, 0.17182, 0.30169, 0.14266, 0.31678, 0.11349, 0.34746, 0.08802, 0.4166, 0.07462, 0.45526, 0.07617, 0.45473, 0.08589, 0.4219, 0.09444, 0.39541, 0.10843, 0.39382, 0.12048, 0.40918, 0.13097, 0.46003, 0.12126, 0.50134, 0.12786, 0.5077, 0.12165, 0.49022, 0.10921, 0.49128, 0.09988, 0.52306, 0.09755, 0.55748, 0.11154, 0.54848, 0.07695, 0.50134, 0.04274, 0.49075, 0.01787, 0.5114, 0, 0.55537, 0, 0.43857, 0.89905, 0.38241, 0.82177, 0.4298, 0.73161, 0.52808, 0.7329, 0.61584, 0.71745, 0.67726, 0.78056, 0.6562, 0.86943, 0.6211, 0.95701, 0.26657, 0.87458, 0.24726, 0.81404, 0.3655, 0.69785, 0.39862, 0.70791, 0.4696, 0.73214, 0.48285, 0.73231, 0.47869, 0.70976, 0.5002, 0.70851, 0.5421, 0.71558, 0.55547, 0.72808, 0.57504, 0.72463, 0.59079, 0.70145, 0.59135, 0.6732, 0.63268, 0.65575, 0.65702, 0.63664, 0.69269, 0.63041, 0.69382, 0.62126, 0.66382, 0.60755, 0.65872, 0.59924, 0.6474, 0.59384, 0.68307, 0.57972, 0.68193, 0.57016, 0.74787, 0.54916, 0.78477, 0.50697, 0.41784, 0.68913, 0.5188, 0.66633, 0.71224, 0.47315, 0.7583, 0.31261, 0.60697, 0.30175, 0.68921, 0.24623, 0.47539, 0.31865, 0.19413, 0.56006, 0.18755, 0.62644, 0.28624, 0.23899, 0.45894, 0.16656, 0.34216, 0.14242, 0.38822, 0.23657, 0.22045, 0.28002, 0.30826, 0.56844, 0.38742, 0.53842, 0.47185, 0.51422, 0.60774, 0.4997, 0.37291, 0.62362, 0.45866, 0.612, 0.52199, 0.59458, 0.57608, 0.55198, 0.64468, 0.50551], "vertices": [1, 5, 120.82, 20.14, 1, 1, 5, 114.27, 3.66, 1, 2, 5, 101.84, -8.06, 0.99985, 28, 19.11, 69.17, 0.00015, 3, 5, 84.69, -17.86, 0.93988, 27, 100.95, 33.08, 0.0002, 28, 9.09, 52.15, 0.05992, 3, 5, 70.09, -23.62, 0.57928, 27, 85.57, 29.94, 0.01524, 28, -0.73, 39.91, 0.40548, 2, 5, 85.32, -27.75, 0.18984, 28, 14.54, 43.88, 0.81016, 2, 5, 96.62, -24.92, 0.129, 28, 22.95, 51.95, 0.871, 2, 5, 102.92, -29.75, 0.11517, 28, 30.82, 50.87, 0.88483, 2, 5, 102.75, -34.39, 0.1062, 28, 32.97, 46.77, 0.8938, 2, 5, 119.4, -40.1, 0.07769, 28, 50.26, 50.07, 0.92231, 2, 5, 121.4, -43.1, 0.07722, 28, 53.49, 48.45, 0.92278, 2, 5, 118.23, -45.94, 0.07768, 28, 52.14, 44.41, 0.92232, 2, 5, 100.79, -43.65, 0.10102, 28, 35.86, 37.75, 0.89898, 2, 5, 97.87, -46.86, 0.10418, 28, 34.92, 33.52, 0.89582, 2, 5, 91.69, -47.11, 0.10681, 28, 29.68, 30.23, 0.89319, 2, 5, 86.22, -50.37, 0.07975, 28, 26.55, 24.69, 0.92025, 2, 5, 95.75, -59.87, 0.01689, 28, 39.54, 21.16, 0.98311, 2, 5, 97.04, -67.33, 0.01161, 28, 44.36, 15.34, 0.98839, 2, 5, 93.61, -69.42, 0.01146, 28, 42.41, 11.81, 0.98854, 2, 5, 89.33, -63, 0.01908, 28, 35.51, 15.26, 0.98092, 2, 5, 85.27, -61.01, 0.02225, 28, 31, 14.97, 0.97775, 2, 5, 90.37, -67.97, 0.00318, 28, 38.88, 11.46, 0.99682, 3, 5, 93.77, -74.45, 9e-05, 28, 45.04, 7.53, 0.99963, 30, -2.19, 52.41, 0.00028, 2, 5, 89.36, -77.16, 1e-05, 28, 42.56, 2.99, 0.99999, 2, 28, 49.33, -1.04, 0.9623, 30, 6.85, 49.25, 0.0377, 2, 28, 57.37, -10.2, 0.94485, 30, 19, 48.29, 0.05515, 2, 28, 67.82, -10.28, 0.99843, 30, 26.56, 55.5, 0.00157, 1, 28, 70.65, -12.34, 1, 2, 28, 68.08, -18.33, 0.99718, 30, 32.35, 49.91, 0.00282, 2, 28, 55.83, -19.7, 0.93158, 30, 24.52, 40.39, 0.06842, 2, 28, 46.59, -17.22, 0.70063, 30, 16.16, 35.73, 0.29937, 2, 28, 49.84, -24.84, 0.40901, 30, 23.8, 32.53, 0.59099, 2, 28, 52.15, -41.62, 0.12743, 30, 37.15, 22.1, 0.87257, 2, 28, 47.13, -54.81, 0.02921, 30, 42.73, 9.14, 0.97079, 2, 28, 43.67, -56.07, 0.01333, 30, 41.13, 5.83, 0.98667, 1, 30, 52.77, 4.5, 1, 1, 30, 60.25, 10.27, 1, 1, 30, 65, 14.72, 1, 1, 30, 67.68, 6.27, 1, 3, 29, 64.84, -64.39, 0.00338, 30, 62.36, -7.09, 0.99659, 31, 173.09, 90.85, 3e-05, 3, 29, 50.86, -55.87, 0.08585, 30, 50.83, -18.72, 0.91233, 31, 157.03, 94.05, 0.00182, 3, 29, 44.51, -48.02, 0.27625, 30, 41.73, -23.08, 0.71606, 31, 148.37, 99.23, 0.00768, 4, 29, 41.47, -48.69, 0.38335, 30, 41.68, -26.2, 0.60438, 26, 113.31, -161.43, 2e-05, 31, 145.74, 97.55, 0.01225, 3, 29, 36.06, -59.66, 0.53087, 30, 51.09, -34, 0.44837, 31, 144.43, 85.4, 0.02076, 4, 29, 24.78, -67.41, 0.58336, 30, 56.03, -46.76, 0.39133, 26, 88.83, -156.03, 0, 31, 136.51, 74.24, 0.02532, 4, 29, 11.87, -70.98, 0.60103, 30, 56.52, -60.14, 0.37148, 26, 79.39, -146.53, 2e-05, 31, 125.62, 66.45, 0.02747, 5, 4, 19.59, -126.28, 0.00039, 29, 9.24, -64.16, 0.60479, 30, 49.27, -61.13, 0.36547, 26, 84.06, -140.89, 0.00021, 31, 120.8, 71.95, 0.02914, 5, 4, 26.27, -121.18, 0.00277, 29, 10.41, -55.83, 0.61949, 30, 41.44, -58.06, 0.33941, 26, 91.89, -137.83, 0.00116, 31, 119.03, 80.17, 0.03716, 5, 4, 33.87, -113.93, 0.01645, 29, 10.72, -45.33, 0.68294, 30, 31.3, -55.33, 0.21747, 26, 101.2, -132.97, 0.00585, 31, 115.72, 90.14, 0.07729, 5, 4, 21, -116.43, 0.04886, 29, 3.32, -56.16, 0.66428, 30, 40.12, -65.04, 0.08344, 26, 88.13, -131.81, 0.0165, 31, 112.49, 77.43, 0.18692, 5, 4, 9.47, -114.22, 0.06718, 29, -6.44, -62.69, 0.59233, 30, 44.22, -76.04, 0.04443, 26, 77.67, -126.49, 0.02343, 31, 105.57, 67.94, 0.27263, 5, 4, 2.48, -110.87, 0.07737, 29, -13.76, -65.22, 0.50289, 30, 44.99, -83.74, 0.02334, 26, 71.88, -121.35, 0.02854, 31, 99.57, 63.05, 0.36785, 5, 4, 2.47, -117.29, 0.07676, 29, -9.25, -69.8, 0.46693, 30, 50.48, -80.42, 0.01745, 26, 70.09, -127.52, 0.02883, 31, 105.37, 60.3, 0.41004, 5, 4, -6.16, -113.64, 0.07515, 29, -17.96, -73.26, 0.41043, 30, 51.84, -89.7, 0.01177, 26, 62.81, -121.62, 0.02922, 31, 98.39, 54.05, 0.47343, 5, 4, -10.07, -115.12, 0.06802, 29, -19.7, -77.06, 0.34581, 30, 55.14, -92.26, 0.00637, 26, 58.65, -121.97, 0.02686, 31, 98.06, 49.89, 0.55293, 5, 4, -7.95, -120.07, 0.06097, 29, -14.72, -79.09, 0.31225, 30, 58.26, -87.89, 0.00379, 26, 59.31, -127.3, 0.02341, 31, 103.44, 49.69, 0.59958, 5, 4, -9, -128, 0.05931, 29, -9.88, -85.48, 0.30703, 30, 65.59, -84.66, 0.00324, 26, 56.11, -134.64, 0.02244, 31, 110.17, 45.36, 0.60798, 5, 4, -20.15, -121.96, 0.05356, 29, -22.06, -89.02, 0.26125, 30, 66.22, -97.32, 0.00205, 26, 47.07, -125.75, 0.02096, 31, 99.96, 37.85, 0.66219, 5, 4, -21.25, -129.22, 0.04863, 29, -17.74, -94.96, 0.23756, 30, 73, -94.5, 0.00127, 26, 44, -132.43, 0.01869, 31, 106.05, 33.76, 0.69384, 5, 4, -30.99, -122.86, 0.03845, 29, -29.14, -97.28, 0.17722, 30, 72.62, -106.13, 0.00058, 26, 36.39, -123.62, 0.0153, 31, 96.15, 27.65, 0.76846, 4, 4, -46.31, -134.85, 0.00762, 29, -31.61, -116.58, 0.04187, 26, 18.35, -130.9, 0.00182, 31, 100.46, 8.68, 0.9487, 1, 31, 90.31, -19.19, 1, 1, 31, 62.39, -47.76, 1, 3, 1, 120.98, 9.6, 0.00015, 26, -54.81, -66.08, 5e-05, 31, 24.79, -53.2, 0.9998, 3, 1, 76.57, -6.94, 0.07537, 26, -47.15, -19.32, 0.24343, 31, -20.15, -38.17, 0.6812, 3, 1, 44.7, -15.24, 0.33913, 26, -38.54, 12.47, 0.50944, 31, -50.16, -24.6, 0.15143, 3, 1, 23.45, -15.24, 0.56994, 26, -27.99, 30.92, 0.40566, 31, -66.69, -11.24, 0.0244, 3, 1, 13.03, -7.3, 0.74124, 26, -15.93, 36.03, 0.25544, 31, -69.81, 1.48, 0.00331, 2, 1, -4.53, -5.18, 0.78324, 2, -12.45, 4.93, 0.21676, 3, 1, -20.35, 1.38, 0.01468, 2, 0.71, 15.9, 0.95733, 24, -57.67, 33.6, 0.02799, 3, 1, -24.25, 0.68, 0.00112, 2, 1.89, 19.68, 0.95622, 24, -54.41, 35.83, 0.04266, 2, 2, 23.44, 28.96, 0.59548, 24, -31.73, 29.83, 0.40452, 2, 2, 21.08, 34.46, 0.46232, 24, -30.19, 35.62, 0.53768, 2, 2, 24.21, 45.24, 0.30889, 24, -21.08, 42.17, 0.69111, 2, 2, 22.62, 50.73, 0.27375, 24, -18.94, 47.46, 0.72625, 2, 2, 38.55, 60.03, 0.20209, 24, -0.66, 44.95, 0.79791, 2, 2, 43.99, 58.93, 0.19542, 24, 2.94, 40.72, 0.80458, 3, 2, 51.1, 44.73, 0.168, 24, -0.23, 25.16, 0.8311, 25, -23.69, 16.57, 0.00089, 3, 2, 63.02, 40.82, 0.02747, 24, 6.73, 14.73, 0.91548, 25, -13.76, 8.9, 0.05704, 2, 24, 14.86, 6.7, 0.30351, 25, -3.49, 3.89, 0.69649, 1, 25, 0.57, 8.36, 1, 1, 25, -3.81, 11.56, 1, 1, 25, -9.42, 11.29, 1, 1, 25, -16.01, 16.89, 1, 1, 25, -18.06, 23.89, 1, 1, 25, -16.42, 35.26, 1, 1, 25, -11.27, 42.61, 1, 1, 25, -7.56, 41.13, 1, 1, 25, -7.13, 33.09, 1, 1, 25, 0.79, 33.64, 1, 1, 25, 9.11, 31.44, 1, 1, 25, 19.11, 20.67, 1, 1, 25, 22.67, 11.86, 1, 1, 25, 29.58, 8.24, 1, 1, 25, 38.85, 15.59, 1, 1, 25, 42.21, 12.86, 1, 1, 25, 44.31, 16.68, 1, 1, 25, 43.8, 26.23, 1, 1, 25, 45.6, 29.78, 1, 1, 25, 53.82, 20.91, 1, 1, 25, 53.94, 5.03, 1, 1, 25, 47.59, -5.77, 1, 1, 25, 39.11, -7.81, 1, 1, 25, 41.13, -9.21, 1, 1, 25, 51.32, -9.4, 1, 1, 25, 58.17, -0.51, 1, 1, 25, 61.12, 2.78, 1, 1, 25, 65.65, -2.3, 1, 1, 25, 60.01, -16.62, 1, 1, 25, 49.45, -31.51, 1, 1, 25, 37.14, -35.1, 1, 1, 25, 33.28, -32.32, 1, 1, 25, 30.6, -20.8, 1, 1, 25, 25.93, -14.39, 1, 3, 3, -27.18, 39.1, 5e-05, 24, 28.42, -10.23, 0.02127, 25, 14.76, -7.84, 0.97868, 4, 2, 89.48, 32, 0.00496, 3, -27.34, 32.56, 0.00455, 24, 22.1, -8.54, 0.24489, 25, 8.23, -8.25, 0.7456, 4, 2, 82.75, 20.32, 0.13053, 3, -19.49, 21.6, 0.1074, 24, 9.6, -13.59, 0.72358, 25, -2.01, -17.02, 0.03849, 4, 2, 84.55, 17.72, 0.17287, 3, -16.37, 22.16, 0.19764, 24, 9.41, -16.74, 0.6176, 25, -1.18, -20.07, 0.01189, 4, 2, 94.35, -0.36, 0.04427, 3, 4.14, 23.59, 0.84459, 4, -14.28, 53.27, 0.00495, 24, 5.95, -37.02, 0.10619, 5, 2, 100.59, -5.68, 0.00717, 3, 11.56, 27.06, 0.9187, 4, -6.95, 49.61, 0.02937, 6, -110.75, 82.67, 9e-05, 24, 7.58, -45.04, 0.04467, 4, 3, 27.03, 24.16, 0.7482, 4, 0.4, 35.69, 0.24734, 6, -111.42, 66.94, 0.00231, 24, 1.11, -59.39, 0.00215, 4, 3, 28.19, 27.24, 0.65454, 4, 3.54, 36.7, 0.34071, 6, -108.2, 66.23, 0.00399, 24, 3.83, -61.25, 0.00075, 3, 3, 34.37, 33.77, 0.44036, 4, 12.49, 35.92, 0.54991, 6, -100.86, 61.04, 0.00973, 3, 3, 40.87, 35.03, 0.25396, 4, 17.51, 31.61, 0.72494, 6, -98.7, 54.79, 0.0211, 3, 3, 35.25, 42.8, 0.10195, 4, 20.11, 40.84, 0.84638, 6, -91.81, 61.46, 0.05167, 3, 3, 30.58, 43.73, 0.08378, 4, 17.93, 45.07, 0.85823, 6, -91.55, 66.2, 0.05799, 3, 3, 30.23, 46.4, 0.08107, 4, 19.81, 47.02, 0.85975, 6, -88.95, 66.94, 0.05918, 3, 3, 32.89, 50.57, 0.05947, 4, 24.73, 47.52, 0.87074, 6, -84.44, 64.89, 0.06979, 3, 3, 27.89, 50.99, 0.03918, 4, 21.95, 51.7, 0.88065, 6, -84.74, 69.9, 0.08018, 3, 3, 18.16, 54.63, 0.02121, 4, 18.76, 61.58, 0.88963, 6, -82.52, 80.05, 0.08916, 3, 3, 9.58, 66.3, 0.01109, 4, 22.56, 75.56, 0.89432, 6, -72.19, 90.2, 0.09459, 3, 3, 10.05, 78.36, 0.0065, 4, 32.3, 82.69, 0.89685, 6, -60.18, 91.45, 0.09665, 3, 3, 15.2, 90.58, 0.00263, 4, 45.07, 86.25, 0.90312, 6, -47.36, 88.09, 0.09424, 3, 3, 21.48, 98.28, 0.00059, 4, 55.01, 86.11, 0.9096, 6, -38.84, 82.97, 0.08981, 3, 3, 22.38, 105.52, 3e-05, 4, 61.24, 89.91, 0.91192, 6, -31.54, 83.1, 0.08805, 2, 4, 66.95, 89.5, 0.91222, 6, -26.83, 79.87, 0.08778, 2, 4, 67.09, 84.4, 0.91214, 6, -29.27, 75.39, 0.08786, 3, 3, 30.17, 99.01, 0.0002, 4, 60.98, 79.76, 0.91109, 6, -36.89, 74.47, 0.08871, 3, 3, 27.11, 92.2, 0.00094, 4, 53.75, 77.92, 0.90825, 6, -44.06, 76.53, 0.09081, 3, 3, 23.38, 87.69, 0.00246, 4, 47.89, 78.04, 0.90328, 6, -49.06, 79.58, 0.09426, 3, 3, 22.32, 82.84, 0.00507, 4, 43.44, 75.86, 0.8943, 6, -54, 79.94, 0.10063, 3, 3, 24.79, 79.53, 0.00727, 4, 42.38, 71.87, 0.88384, 6, -56.93, 77.03, 0.10889, 3, 3, 28.71, 79.16, 0.00758, 4, 44.52, 68.57, 0.86608, 6, -56.74, 73.1, 0.12634, 3, 3, 28.98, 87.22, 0.00343, 4, 51, 73.36, 0.83512, 6, -48.73, 73.97, 0.16145, 3, 3, 30.75, 95.93, 0.00113, 4, 58.93, 77.4, 0.81768, 6, -39.85, 73.46, 0.18119, 3, 3, 36.62, 104.42, 0.00015, 4, 69.23, 78.07, 0.80488, 6, -30.61, 68.85, 0.19497, 2, 4, 78.07, 73.04, 0.8, 6, -25.51, 60.06, 0.2, 2, 4, 83.56, 65.74, 0.79953, 6, -24.46, 50.98, 0.20047, 2, 4, 82.97, 63.93, 0.79953, 6, -25.87, 49.71, 0.20047, 2, 4, 80.69, 64.12, 0.79953, 6, -27.74, 51.03, 0.20047, 3, 3, 50.83, 102.58, 1e-05, 4, 76.62, 65.79, 0.79928, 6, -30.42, 54.52, 0.20071, 3, 3, 45.98, 97.97, 0.00041, 4, 70, 66.73, 0.79317, 6, -35.66, 58.67, 0.20642, 3, 3, 47.37, 95.93, 0.00071, 4, 69.26, 64.37, 0.78279, 6, -37.49, 57.01, 0.2165, 3, 3, 51.35, 94.55, 0.00071, 4, 70.66, 60.4, 0.73951, 6, -38.29, 52.87, 0.25978, 3, 3, 54.41, 97.97, 0.00022, 4, 75.24, 60.12, 0.66264, 6, -34.47, 50.32, 0.33714, 2, 4, 81.48, 61.82, 0.56, 6, -28.22, 48.64, 0.44, 2, 4, 86.98, 65.3, 0.49071, 6, -21.72, 48.87, 0.50929, 2, 4, 90.56, 66.71, 0.48329, 6, -17.91, 48.29, 0.51671, 2, 4, 91.4, 60.11, 0.44054, 6, -20.51, 42.16, 0.55946, 2, 4, 96.52, 63.49, 0.31604, 6, -14.39, 42.51, 0.68396, 2, 4, 97.56, 70.86, 0.26933, 6, -9.78, 48.34, 0.73067, 2, 4, 101.02, 71.65, 0.26814, 6, -6.39, 47.28, 0.73186, 2, 4, 104.49, 66.31, 0.25116, 6, -6.09, 40.93, 0.74884, 2, 4, 112.47, 69.47, 0.20235, 6, 2.4, 39.63, 0.79765, 2, 4, 120.63, 67.49, 0.19029, 6, 8.45, 33.8, 0.80971, 2, 4, 121.02, 64.87, 0.19038, 6, 7.46, 31.35, 0.80962, 2, 4, 118.63, 64.02, 0.19185, 6, 4.98, 31.82, 0.80815, 2, 4, 113.56, 61.93, 0.20418, 6, -0.46, 32.57, 0.79582, 2, 4, 112.63, 57.25, 0.19351, 6, -3.62, 29, 0.80649, 2, 4, 114.54, 53.93, 0.14308, 6, -3.65, 25.16, 0.85692, 2, 4, 118.64, 57.08, 0.05528, 6, 1.48, 25.82, 0.94472, 2, 4, 122.15, 63.37, 0.00887, 6, 7.68, 29.49, 0.99113, 2, 4, 122.25, 69.34, 0.00143, 6, 10.78, 34.59, 0.99857, 1, 6, 14.91, 40.58, 1, 1, 6, 17.12, 40.3, 1, 1, 6, 18.86, 34.5, 1, 1, 6, 27.28, 44.37, 1, 1, 6, 34.12, 48.56, 1, 1, 6, 41.73, 48.82, 1, 1, 6, 49.31, 41.5, 1, 1, 6, 49.82, 36.63, 1, 1, 6, 46.81, 30.56, 1, 1, 6, 36.95, 25.52, 1, 1, 6, 32.46, 20.33, 1, 1, 6, 44.3, 20.55, 1, 1, 6, 60.53, 15.74, 1, 1, 6, 78.03, 0.71, 1, 1, 6, 89.54, -17.78, 1, 1, 6, 87.71, -28.18, 1, 1, 6, 79.89, -34.91, 1, 1, 6, 73.48, -30.32, 1, 1, 6, 67.68, -21.81, 1, 1, 6, 54.51, -8.06, 1, 1, 6, 40.34, 1.64, 1, 1, 6, 35.47, 2.63, 1, 1, 6, 37.97, -1.36, 1, 3, 4, 163.34, 45.85, 0.00362, 7, 14.39, 48.25, 0.03523, 6, 34.43, -6.4, 0.96115, 3, 4, 160.87, 40.58, 0.02071, 7, 12.3, 42.82, 0.17498, 6, 29.64, -9.72, 0.8043, 4, 4, 168.64, 37.9, 0.02268, 7, 20.24, 40.69, 0.44089, 8, -31.56, 34.75, 0.00049, 6, 35, -15.95, 0.53594, 3, 4, 178.04, 41.09, 0.01211, 7, 29.38, 44.53, 0.5039, 6, 44.73, -17.92, 0.48398, 3, 4, 179.21, 37.87, 0.01241, 7, 30.78, 41.4, 0.50401, 6, 44.12, -21.3, 0.48357, 4, 4, 173.85, 31.15, 0.02023, 7, 25.9, 34.32, 0.55003, 8, -23.47, 32.05, 0.01213, 6, 36.1, -24.41, 0.41761, 4, 4, 181.92, 25.66, 0.00894, 7, 34.33, 29.41, 0.67501, 8, -13.71, 32, 0.07468, 6, 40.3, -33.21, 0.24137, 4, 4, 187, 14.79, 0.00089, 7, 40.16, 18.92, 0.62715, 8, -3.43, 25.82, 0.28569, 6, 39.21, -45.16, 0.08627, 3, 7, 45.6, 19.3, 0.56766, 8, 1.09, 28.85, 0.39442, 6, 43.92, -47.91, 0.03792, 3, 7, 53.36, 24.06, 0.56546, 8, 5.45, 36.85, 0.41419, 6, 53.02, -48.35, 0.02035, 3, 7, 56.64, 21.18, 0.56525, 8, 9.73, 35.99, 0.41462, 6, 54.1, -52.57, 0.02012, 3, 7, 52.99, 12.53, 0.55609, 8, 10.88, 26.67, 0.41884, 6, 46.21, -57.66, 0.02508, 3, 7, 41.93, 7.17, 0.39611, 8, 3.96, 16.51, 0.57771, 6, 34.06, -55.86, 0.02617, 3, 7, 46.45, 3.93, 0.14514, 8, 9.49, 15.96, 0.8482, 6, 35.97, -61.08, 0.00666, 2, 7, 57.14, -0.5, 0.00657, 8, 20.97, 17.45, 0.99343, 1, 8, 33.04, 17.33, 1, 1, 8, 45.31, 12.56, 1, 1, 8, 56.96, -4.07, 1, 1, 8, 60.19, -14.58, 1, 1, 8, 56.61, -15.74, 1, 1, 8, 50.29, -8.13, 1, 2, 8, 42.6, -2.95, 0.99731, 5, 59.95, 46.09, 0.00269, 2, 8, 38.07, -4.14, 0.96521, 5, 55.82, 43.88, 0.03479, 2, 8, 35.77, -9.63, 0.80011, 5, 54.86, 38, 0.19989, 2, 8, 44.29, -21.88, 0.38097, 5, 66, 28.07, 0.61903, 2, 8, 45.95, -33.78, 0.11546, 5, 70.38, 16.89, 0.88454, 2, 8, 48.83, -34.64, 0.05848, 5, 73.38, 16.72, 0.94152, 2, 8, 51.63, -28.32, 0.02595, 5, 74.64, 23.52, 0.97405, 2, 8, 55.12, -27.35, 0.02416, 5, 77.81, 25.27, 0.97584, 2, 8, 59.09, -35.51, 0.01929, 5, 83.56, 18.26, 0.98071, 2, 8, 57.39, -46.55, 0.00338, 5, 84.48, 7.12, 0.99662, 1, 5, 94.2, 16.68, 1, 1, 5, 97.79, 35.16, 1, 1, 5, 104.13, 43, 1, 1, 5, 113.14, 41.96, 1, 1, 5, 120.06, 31.57, 1, 3, 1, -4.5, 23.83, 0.02652, 2, 13.24, -8.55, 0.84208, 26, 19.8, 35.81, 0.1314, 3, 2, 47.13, -8.3, 0.81701, 3, -8.2, -22.68, 0.07676, 26, 53.68, 34.82, 0.10623, 6, 2, 71.79, -36.41, 0.00603, 3, 27.6, -11.88, 0.70767, 4, -27.48, 12.84, 0.02402, 29, -122.02, 1.72, 0.00037, 26, 77.29, 5.82, 0.26131, 31, -25.11, 88.69, 0.0006, 5, 3, 44.35, -34.22, 0.04682, 4, -34.57, -14.16, 0.20443, 29, -108.09, -22.47, 0.02585, 26, 63.01, -18.16, 0.65373, 31, -3.71, 70.76, 0.06918, 4, 4, -34.66, -39.79, 0.23889, 29, -90.14, -40.76, 0.0918, 26, 55.84, -42.77, 0.39885, 31, 19.44, 59.75, 0.27045, 4, 4, -62.52, -50.96, 0.06232, 29, -102.11, -68.29, 0.03382, 26, 25.98, -45.8, 0.28609, 31, 17.66, 29.79, 0.61777, 5, 1, 57.3, 35.3, 0.00031, 4, -94.52, -37, 0.00085, 29, -134.68, -80.86, 0.00024, 26, -0.92, -23.54, 0.31624, 31, -8.6, 6.79, 0.68236, 3, 1, 47.34, 1.4, 0.23113, 26, -25.4, 1.93, 0.55739, 31, -37.65, -13.31, 0.21148, 2, 2, 44.29, 30.32, 0.35328, 24, -14.48, 18.03, 0.64672, 3, 2, 67.59, 24.31, 0.01865, 3, -29.4, 9.45, 0.00312, 24, 0.13, -1.08, 0.97823, 4, 3, 26.71, 10.55, 0.91077, 4, -10.46, 27.49, 0.08824, 6, -124.94, 65.33, 0.00034, 24, -12.04, -55.87, 0.00066, 2, 3, 29.41, 0.74, 0.99999, 6, -134.27, 61.26, 1e-05, 6, 2, 66.37, -46.33, 0.00095, 3, 34.39, -20.93, 0.32693, 4, -30.35, 1.91, 0.1209, 29, -116.38, -8.08, 0.00479, 26, 71.51, -3.89, 0.53718, 31, -16.44, 81.43, 0.00925, 5, 3, 36.64, -23.94, 0.22698, 4, -31.31, -1.73, 0.14854, 29, -114.5, -11.34, 0.00805, 26, 69.58, -7.13, 0.59959, 31, -13.56, 79.01, 0.01683, 5, 3, 42.81, -17.65, 0.29018, 4, -22.55, -2.65, 0.30556, 29, -107.63, -5.84, 0.0117, 26, 77.75, -10.43, 0.37352, 31, -9, 86.54, 0.01903, 5, 3, 46.94, -22.18, 0.15796, 4, -23.53, -8.7, 0.35224, 29, -104.07, -10.83, 0.0229, 26, 75.13, -15.97, 0.42881, 31, -3.94, 83.08, 0.0381, 5, 3, 52.08, -33.25, 0.035, 4, -29, -19.61, 0.30746, 29, -100.29, -22.44, 0.04541, 26, 66.86, -24.95, 0.51545, 31, 3.6, 73.47, 0.09669, 5, 3, 50.6, -39.21, 0.01485, 4, -34.6, -22.16, 0.23686, 29, -102.49, -28.18, 0.0438, 26, 60.77, -25.84, 0.5817, 31, 3.51, 67.32, 0.12279, 5, 3, 55.06, -42.78, 0.00566, 4, -34.62, -27.88, 0.24532, 29, -98.48, -32.26, 0.05848, 26, 59.17, -31.33, 0.52278, 31, 8.68, 64.87, 0.16775, 5, 3, 64.89, -40.8, 0.00233, 4, -26.96, -34.34, 0.32565, 29, -88.49, -31.48, 0.09683, 26, 64.74, -39.67, 0.38203, 31, 17.79, 69.04, 0.19317, 5, 3, 73.62, -34.22, 0.00043, 4, -16.38, -37.09, 0.42357, 29, -79.03, -25.99, 0.14078, 26, 74.16, -45.23, 0.26363, 31, 24.79, 77.44, 0.17159, 5, 4, -12.59, -50.09, 0.35706, 27, -79.18, -46.49, 0.00013, 29, -67.2, -32.58, 0.22319, 26, 74.2, -58.77, 0.18787, 31, 38.16, 75.32, 0.23175, 6, 4, -7.04, -58.56, 0.31524, 27, -69.63, -49.85, 0.00024, 29, -57.3, -34.71, 0.30155, 30, 5.23, -119.06, 0.00017, 26, 77.19, -68.44, 0.13457, 31, 48.19, 76.73, 0.24824, 5, 4, -7.1, -68.97, 0.23993, 29, -50.02, -42.15, 0.35233, 30, 14.16, -113.69, 0.0012, 26, 74.26, -78.43, 0.10528, 31, 57.58, 72.24, 0.30126, 6, 4, -3.74, -70.12, 0.23644, 27, -59.98, -57.03, 1e-05, 29, -46.83, -40.61, 0.38408, 30, 13.39, -110.23, 0.00184, 26, 77.17, -80.47, 0.09393, 31, 60.05, 74.79, 0.28371, 6, 4, 3.43, -63.1, 0.29549, 27, -58.54, -47.09, 0.00126, 29, -46.66, -30.57, 0.40361, 30, 3.67, -107.74, 0.00116, 26, 86, -75.7, 0.08862, 31, 56.76, 84.28, 0.20986, 6, 4, 6.9, -62.45, 0.30181, 27, -56.18, -44.47, 0.00232, 29, -44.65, -27.67, 0.42653, 30, 1.31, -105.12, 0.00132, 26, 89.51, -76.04, 0.07959, 31, 57.65, 87.69, 0.18842, 6, 4, 9.69, -59.82, 0.32578, 27, -55.56, -40.69, 0.00448, 29, -44.51, -23.84, 0.42915, 30, -2.38, -104.1, 0.00096, 26, 92.92, -74.29, 0.07461, 31, 56.47, 91.33, 0.16501, 6, 4, 12.61, -70.96, 0.22469, 27, -46.48, -47.76, 0.00133, 29, -34.61, -29.71, 0.52885, 30, 5.62, -95.82, 0.0051, 26, 92.64, -85.79, 0.05506, 31, 67.78, 89.22, 0.18497, 6, 4, 16.27, -71.52, 0.21288, 27, -43.23, -45.98, 0.00188, 29, -31.6, -27.54, 0.56591, 30, 4.2, -92.39, 0.00628, 26, 96.01, -87.35, 0.0474, 31, 69.85, 92.3, 0.16564, 5, 4, 19.74, -91.64, 0.09179, 29, -15, -39.41, 0.66202, 30, 19.59, -78.99, 0.03494, 26, 93.78, -107.64, 0.02603, 31, 89.53, 86.86, 0.18522, 5, 4, 33.12, -105.69, 0.02968, 29, 4.39, -40, 0.72777, 30, 24.65, -60.26, 0.12853, 26, 102.76, -124.84, 0.00968, 31, 107.94, 92.97, 0.10435, 5, 3, 38.5, 0.89, 0.74171, 4, -10.7, 12.25, 0.24345, 6, -132.82, 52.29, 4e-05, 26, 93.25, 0.61, 0.01473, 31, -17.42, 103.61, 7e-05, 6, 3, 63.07, -16.32, 0.02256, 4, -8.92, -17.7, 0.66336, 27, -95.93, -18.52, 0.00017, 29, -87.35, -6.96, 0.06278, 26, 86.69, -28.66, 0.19603, 31, 10.43, 92.46, 0.05511, 5, 4, 50.71, -88.77, 0.01027, 29, 5.02, -15.6, 0.91212, 30, 1.05, -54, 0.04403, 26, 124.34, -113.45, 0.00336, 31, 100.14, 116.1, 0.03022, 4, 27, 56.75, -25.77, 0.05302, 28, 12.72, -21.36, 0.2616, 29, 65.02, 5.15, 0.11121, 30, -5.26, 9.17, 0.57418, 3, 5, 31.03, -45.4, 0.19067, 27, 43.33, 15.27, 0.7661, 28, -23.84, 1.62, 0.04323, 2, 5, 61.86, -52.92, 0.06546, 28, 6.66, 10.39, 0.93454, 4, 4, 124.73, -37.57, 0.05186, 5, 4.86, -17.94, 0.72773, 27, 22.32, 46.85, 0.22038, 28, -60.19, 12.48, 3e-05, 3, 3, 38.92, 81.71, 0.00536, 4, 52.87, 62.15, 0.8362, 6, -52.77, 63.35, 0.15844, 3, 3, 17.49, 67.41, 0.01226, 4, 28.35, 70.05, 0.89198, 6, -69.96, 82.53, 0.09575, 4, 4, 167.4, 7.32, 0.02323, 7, 21.13, 10.1, 0.86806, 8, -15.52, 8.68, 0.0209, 6, 18.52, -41.73, 0.0878, 2, 8, 27.73, -27.65, 0.33295, 5, 51.24, 18.6, 0.66705, 1, 8, 25.03, 6.7, 1, 3, 7, 17.18, -18.61, 0.25137, 8, -4.64, -18.17, 0.35071, 5, 17.55, 20.28, 0.39792, 3, 4, 156.39, 29.23, 0.09493, 7, 8.62, 31.19, 0.31611, 6, 20.06, -17.26, 0.58896, 6, 3, 56.26, 54.13, 0.07421, 4, 42.05, 31.43, 0.71365, 5, -101.9, -32.07, 0.10959, 6, -77.6, 42.27, 0.06935, 27, -85.27, 51.46, 0.03319, 28, -145.87, -52.75, 0, 10, 3, 79.23, 43.52, 0.05173, 4, 48.01, 6.83, 0.55863, 5, -79.77, -44.33, 0.17612, 6, -84.84, 18.02, 0.04834, 27, -65.6, 35.54, 0.05408, 28, -120.57, -52.42, 1e-05, 29, -64.11, 50.51, 0.07119, 30, -79.25, -105.97, 0.00016, 26, 148.18, -20.82, 0.01238, 31, 12.5, 154.41, 0.02737, 10, 3, 101.35, 30.34, 0.03693, 4, 51.43, -18.68, 0.40326, 5, -58.67, -59.08, 0.1518, 6, -94.75, -5.74, 0.03452, 27, -47.39, 17.35, 0.0465, 28, -94.93, -54.77, 1e-05, 29, -43.74, 34.76, 0.27907, 30, -59.22, -89.79, 0.01113, 26, 144.41, -46.29, 0.00968, 31, 37.04, 146.63, 0.02711, 10, 3, 129.48, 3.33, 0.01561, 4, 47.76, -57.51, 0.1956, 5, -32.59, -88.08, 0.06418, 6, -117.49, -37.42, 0.01459, 27, -26.73, -15.73, 0.01984, 28, -57.9, -67, 0, 29, -19.06, 4.56, 0.6117, 30, -24.13, -72.77, 0.02655, 26, 130.15, -82.58, 0.0102, 31, 70.6, 126.75, 0.04172, 10, 3, 50.68, 26.53, 0.35314, 4, 16.95, 18.64, 0.5263, 5, -109.49, -59.19, 0.04848, 6, -105.72, 43.87, 0.02191, 27, -97.45, 26.06, 0.01489, 28, -139, -80.07, 0, 29, -94.5, 37.08, 0.0196, 30, -73.21, -138.64, 4e-05, 26, 121.59, -0.88, 0.00808, 31, -11.42, 131.35, 0.00756, 10, 3, 69.18, 10.07, 0.13441, 4, 15.55, -6.08, 0.54953, 5, -92.24, -76.96, 0.05613, 6, -119.39, 23.22, 0.01632, 27, -83.54, 5.57, 0.01727, 28, -115.21, -86.94, 0, 29, -78.11, 18.51, 0.10607, 30, -51.35, -126.99, 0.00297, 26, 113.42, -24.26, 0.08515, 31, 10.35, 119.55, 0.03214, 10, 3, 85.54, 0.01, 0.07897, 4, 17.85, -25.15, 0.45912, 5, -76.65, -88.18, 0.04899, 6, -127.02, 5.59, 0.01312, 27, -70.14, -8.19, 0.01652, 28, -96.11, -88.95, 0, 29, -63.08, 6.55, 0.23214, 30, -36.25, -115.13, 0.00327, 26, 110.35, -43.22, 0.07284, 31, 28.58, 113.49, 0.07504, 10, 3, 107.99, -1.99, 0.04072, 4, 30.23, -43.98, 0.3244, 5, -54.41, -91.82, 0.04967, 6, -125.81, -16.91, 0.01214, 27, -48.87, -15.63, 0.0165, 28, -74.99, -81.07, 0, 29, -41.04, 1.86, 0.42681, 30, -26.59, -94.77, 0.01341, 26, 117.04, -64.74, 0.045, 31, 50.89, 116.66, 0.07136, 10, 3, 134.14, -6.33, 0.00932, 4, 43.09, -67.17, 0.15541, 5, -28.64, -98.05, 0.03833, 6, -126.39, -43.41, 0.00871, 27, -24.57, -26.24, 0.01217, 28, -49.52, -73.7, 0, 29, -15.59, -5.59, 0.67382, 30, -13.45, -71.74, 0.02717, 26, 122.99, -90.58, 0.01494, 31, 77.35, 118.41, 0.06013], "edges": [130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 462, 460, 462, 460, 458, 458, 456, 456, 454, 454, 452, 452, 450, 450, 448, 448, 446, 446, 444, 444, 442, 442, 440, 440, 438, 438, 436, 436, 434, 434, 432, 432, 430, 430, 428, 428, 426, 426, 424, 424, 422, 422, 420, 420, 418, 418, 416, 416, 414, 414, 412, 412, 410, 410, 408, 408, 406, 406, 404, 404, 402, 402, 400, 400, 398, 398, 396, 396, 394, 394, 392, 392, 390, 390, 388, 388, 386, 386, 384, 384, 382, 382, 380, 380, 378, 378, 376, 376, 374, 374, 372, 372, 370, 370, 368, 368, 366, 366, 364, 364, 362, 362, 360, 360, 358, 356, 358, 356, 354, 354, 352, 352, 350, 350, 348, 348, 346, 346, 344, 344, 342, 342, 340, 340, 338, 338, 336, 336, 334, 334, 332, 332, 330, 330, 328, 328, 326, 326, 324, 324, 322, 322, 320, 320, 318, 318, 316, 316, 314, 314, 312, 312, 310, 310, 308, 308, 306, 306, 304, 304, 302, 302, 300, 300, 298, 298, 296, 296, 294, 294, 292, 292, 290, 290, 288, 288, 286, 286, 284, 284, 282, 282, 280, 280, 278, 278, 276, 276, 274, 274, 272, 272, 270, 270, 268, 268, 266, 266, 264, 264, 262, 262, 260, 260, 258, 258, 256, 256, 254, 254, 252, 252, 250, 250, 248, 248, 246, 246, 244, 244, 242, 242, 240, 240, 238, 238, 236, 236, 234, 234, 232, 232, 230, 230, 228, 228, 226, 226, 224, 224, 222, 222, 220, 220, 218, 218, 216, 216, 214, 214, 212, 212, 210, 210, 208, 208, 206, 206, 204, 204, 202, 202, 200, 200, 198, 196, 198, 196, 194, 194, 192, 192, 190, 190, 188, 188, 186, 186, 184, 184, 182, 182, 180, 180, 178, 178, 176, 176, 174, 174, 172, 172, 170, 170, 168, 168, 166, 166, 164, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 154, 152, 152, 150, 150, 148, 148, 146, 146, 144, 144, 142, 142, 140, 140, 138, 138, 136, 136, 134, 130, 132, 134, 132, 134, 464, 464, 466, 466, 468, 472, 474, 474, 476, 476, 478, 478, 130, 142, 480, 480, 482, 482, 234, 240, 484, 484, 486, 468, 488, 486, 488, 470, 490, 490, 488, 490, 492, 492, 494, 494, 496, 470, 498, 496, 498, 472, 500, 500, 498, 500, 502, 502, 504, 504, 506, 506, 508, 508, 510, 510, 512, 512, 514, 514, 516, 516, 518, 518, 520, 520, 522, 522, 524, 524, 526, 526, 96, 254, 556, 556, 558, 558, 560, 560, 562, 246, 564, 564, 566, 566, 568, 568, 570, 570, 572, 572, 562]}}, "Layer 5": {"Layer 5": {"width": 98, "type": "mesh", "hull": 48, "height": 86, "triangles": [21, 22, 20, 20, 22, 19, 24, 6, 23, 23, 7, 22, 23, 6, 7, 24, 25, 6, 6, 25, 5, 22, 8, 19, 22, 7, 8, 19, 9, 18, 19, 8, 9, 5, 26, 4, 5, 25, 26, 9, 12, 18, 18, 12, 17, 12, 9, 11, 9, 10, 11, 13, 14, 12, 12, 15, 17, 12, 14, 15, 15, 16, 17, 4, 26, 3, 26, 27, 3, 3, 27, 2, 39, 35, 38, 38, 36, 37, 38, 35, 36, 40, 34, 39, 39, 34, 35, 40, 41, 34, 41, 33, 34, 41, 42, 33, 27, 1, 2, 27, 28, 1, 33, 42, 32, 42, 43, 32, 32, 43, 31, 28, 0, 1, 28, 29, 0, 43, 44, 31, 30, 44, 45, 30, 31, 44, 30, 46, 29, 29, 47, 0, 29, 46, 47, 30, 45, 46], "uvs": [0.3921, 0.06483, 0.44653, 0.17942, 0.45485, 0.31123, 0.43519, 0.47922, 0.42676, 0.65714, 0.46355, 0.78131, 0.551, 0.85329, 0.67038, 0.87385, 0.77044, 0.84964, 0.84678, 0.7889, 0.81453, 0.7829, 0.81124, 0.72591, 0.8935, 0.72141, 0.8731, 0.69142, 0.89218, 0.64343, 0.96522, 0.63968, 1, 0.63743, 1, 0.69086, 0.90638, 0.83201, 0.84101, 0.90782, 0.79169, 1, 0.72861, 1, 0.73778, 0.96271, 0.67011, 0.9797, 0.51918, 0.96812, 0.39784, 0.85831, 0.3322, 0.67978, 0.36386, 0.3168, 0.36711, 0.16183, 0.26358, 0.07431, 0.19024, 0.06656, 0.11464, 0.11222, 0.07079, 0.20353, 0.06777, 0.29055, 0.08894, 0.34138, 0.12749, 0.35947, 0.17739, 0.36895, 0.19176, 0.40255, 0.17588, 0.43614, 0.09725, 0.43442, 0.02997, 0.3991, 0, 0.34483, 0, 0.18545, 0.03224, 0.08723, 0.09196, 0.02176, 0.1721, 0, 0.2356, 0, 0.30591, 0], "vertices": [2, 22, 2.04, -5.97, 1, 23, -10.25, 24.68, 0, 2, 21, 35.56, -3.59, 0.92532, 22, -6.93, 0.74, 0.07468, 1, 21, 24.3, -5.17, 1, 1, 21, 9.76, -4.22, 1, 3, 20, 18.63, -4.63, 0.76937, 21, -5.56, -4.43, 0.23063, 23, 35.9, 46.51, 0, 1, 20, 7.57, -2.47, 1, 3, 19, 17.83, -4.96, 0.75837, 20, -2.06, -6.84, 0.24163, 23, 47.13, 64.02, 0, 2, 19, 6.18, -2.89, 1, 23, 44.49, 75.55, 0, 2, 18, 23.84, -3.33, 0.79514, 19, -3.68, -4.72, 0.20486, 1, 18, 14.95, -1.28, 1, 1, 18, 16.68, -3.97, 1, 1, 18, 13.26, -7.5, 1, 1, 18, 7.57, -1.78, 1, 1, 18, 7, -4.99, 1, 1, 18, 2.68, -6.37, 1, 1, 18, -2.36, -1.27, 1, 1, 18, -4.78, 1.13, 1, 1, 18, -1.37, 4.21, 1, 1, 18, 13.78, 5.54, 1, 3, 18, 22.92, 5.15, 0.98155, 19, -10.46, 0.46, 0.01845, 23, 41.08, 92.18, 0, 3, 18, 32.04, 6.88, 0.37387, 19, -5.43, 8.26, 0.62613, 23, 50.23, 90.59, 0, 3, 18, 36.18, 2.29, 0.1951, 19, 0.75, 8.1, 0.8049, 23, 52.49, 84.84, 0, 2, 18, 33.2, 0.81, 0.13296, 19, -0.23, 4.92, 0.86704, 1, 19, 6.44, 6.21, 1, 1, 19, 21.2, 4.83, 1, 1, 20, 5.01, 6.41, 1, 1, 20, 21.54, 4.38, 1, 1, 21, 23.22, 3.69, 1, 2, 21, 36.54, 4.28, 0.27928, 22, 0.77, 2.63, 0.72072, 2, 22, 13.13, 0.04, 1, 23, -4.87, 13.26, 0, 2, 22, 19.94, 2.45, 0.91152, 23, -2.86, 6.33, 0.08848, 2, 22, 25.02, 9.12, 0.00371, 23, 3.51, 0.87, 0.99629, 2, 22, 25.63, 18.05, 0, 23, 12.39, -0.25, 1, 1, 23, 19.46, 2.22, 1, 1, 23, 22.77, 5.75, 1, 1, 23, 22.83, 9.84, 1, 1, 23, 21.8, 14.69, 1, 1, 23, 23.97, 17.06, 1, 1, 23, 27.23, 16.67, 1, 1, 23, 29.92, 9.44, 1, 1, 23, 29.51, 2.19, 1, 1, 23, 26.24, -2.25, 1, 2, 22, 32.58, 19.55, 0, 23, 13.49, -7.27, 1, 1, 23, 4.47, -7.43, 1, 2, 22, 30.3, 2.99, 0.13644, 23, -2.91, -4.05, 0.86356, 1, 22, 23.95, -2, 1, 2, 22, 18.3, -4.61, 0.99999, 23, -9.81, 8.36, 1e-05, 2, 22, 12.05, -7.5, 1, 23, -12.34, 14.78, 0], "edges": [34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 34, 32, 30, 32]}}, "Layer 6": {"Layer 6": {"width": 151, "type": "mesh", "hull": 61, "height": 150, "triangles": [30, 31, 29, 31, 32, 29, 34, 35, 33, 35, 36, 33, 36, 37, 33, 28, 47, 27, 28, 29, 47, 32, 46, 29, 29, 46, 47, 33, 43, 32, 32, 43, 46, 27, 48, 26, 27, 47, 48, 37, 42, 33, 33, 42, 43, 37, 38, 42, 38, 41, 42, 38, 39, 41, 43, 44, 46, 46, 44, 45, 26, 49, 25, 26, 48, 49, 39, 40, 41, 49, 50, 25, 25, 50, 24, 50, 51, 24, 24, 51, 23, 51, 52, 23, 52, 22, 23, 52, 53, 22, 53, 21, 22, 53, 54, 21, 54, 20, 21, 54, 55, 20, 5, 10, 4, 4, 11, 3, 4, 10, 11, 5, 6, 10, 55, 19, 20, 10, 7, 8, 8, 9, 10, 10, 6, 7, 11, 12, 3, 3, 12, 2, 55, 56, 19, 19, 56, 18, 12, 13, 2, 13, 1, 2, 18, 57, 17, 18, 56, 57, 13, 14, 1, 17, 58, 16, 17, 57, 58, 14, 0, 1, 15, 60, 14, 14, 60, 0, 16, 59, 15, 16, 58, 59, 15, 59, 60], "uvs": [0.97308, 0.03802, 1, 0.08414, 1, 0.19467, 0.97412, 0.25319, 0.91378, 0.2932, 0.86153, 0.29246, 0.83651, 0.27023, 0.83357, 0.23838, 0.84387, 0.21023, 0.87257, 0.19986, 0.88655, 0.21319, 0.91673, 0.20801, 0.93733, 0.17837, 0.94395, 0.11541, 0.92114, 0.07244, 0.86889, 0.04355, 0.79751, 0.05244, 0.70847, 0.088, 0.63709, 0.14948, 0.60103, 0.2206, 0.60398, 0.28134, 0.6312, 0.39765, 0.67812, 0.49931, 0.69104, 0.55566, 0.67812, 0.68398, 0.61222, 0.80675, 0.51206, 0.92184, 0.40179, 0.97479, 0.30467, 0.97989, 0.23587, 0.97887, 0.22961, 1, 0.19569, 1, 0.17073, 0.97606, 0.09961, 0.96657, 0.1062, 0.98886, 0.08453, 1, 0.06334, 1, 0.03555, 0.95899, 0.02519, 0.9348, 0, 0.91252, 0, 0.8757, 0.04435, 0.87465, 0.07985, 0.90934, 0.13937, 0.90723, 0.1801, 0.86308, 0.2156, 0.89147, 0.18428, 0.92616, 0.32107, 0.92826, 0.43594, 0.90093, 0.52261, 0.82419, 0.57273, 0.74114, 0.62285, 0.64338, 0.62494, 0.54352, 0.58663, 0.43583, 0.54699, 0.33107, 0.54864, 0.23131, 0.58172, 0.13219, 0.64854, 0.05051, 0.75735, 0, 0.8404, 0, 0.90053, 0], "vertices": [1, 16, 26.16, 4.62, 1, 2, 16, 32.13, -0.74, 0.51029, 17, -2.71, 6.31, 0.48971, 1, 17, 13.68, 8.83, 1, 1, 17, 22.95, 6.3, 1, 1, 17, 30.26, -1.79, 1, 1, 17, 31.35, -9.61, 1, 1, 17, 28.63, -13.85, 1, 1, 17, 23.98, -15.02, 1, 1, 17, 19.57, -14.12, 1, 1, 17, 17.37, -10.07, 1, 1, 17, 19.03, -7.68, 1, 1, 17, 17.56, -3.3, 1, 1, 17, 12.7, -0.9, 1, 2, 16, 25.48, -7.77, 0.00336, 17, 3.21, -1.35, 0.99664, 2, 16, 20.25, -2.67, 0.85251, 17, -2.63, -5.73, 0.14749, 1, 16, 11.42, -0.94, 1, 2, 15, 36.29, -8.38, 0.23253, 16, 1.55, -5.48, 0.76747, 1, 15, 22.83, -3.1, 1, 1, 15, 8.65, -2.49, 1, 2, 14, 37.63, -3.97, 0.87281, 15, -2.6, -6.58, 0.12719, 1, 14, 28.7, -2.13, 1, 2, 13, 44.94, 3.08, 1e-05, 14, 10.78, -1.77, 0.99999, 1, 13, 30.25, -5.09, 1, 1, 13, 21.96, -7.65, 1, 1, 13, 2.62, -7.1, 1, 1, 12, 30.11, -5.98, 1, 1, 12, 7.16, -6.23, 1, 1, 11, 28.47, -4.77, 1, 1, 11, 13.79, -4.78, 1, 2, 10, 31.22, -0.83, 0.0259, 11, 3.42, -4.09, 0.9741, 2, 10, 31.22, -4.14, 0.1181, 11, 2.31, -7.2, 0.8819, 2, 10, 26.31, -5.6, 0.42975, 11, -2.8, -6.94, 0.57025, 1, 10, 21.67, -3.23, 1, 1, 10, 10.97, -4.93, 1, 1, 10, 12.88, -7.85, 1, 1, 10, 10.22, -10.39, 1, 1, 10, 7.15, -11.3, 1, 1, 10, 1.38, -6.6, 1, 1, 10, -1.16, -3.57, 1, 1, 10, -5.75, -1.45, 1, 1, 10, -7.33, 3.85, 1, 1, 10, -0.95, 5.91, 1, 1, 10, 5.67, 2.45, 1, 1, 10, 14.19, 5.31, 1, 1, 10, 18.2, 13.41, 1, 1, 10, 24.55, 10.86, 1, 2, 10, 21.5, 4.52, 0.8856, 11, -3.95, 4.21, 0.1144, 2, 10, 41.39, 10.11, 0, 11, 16.66, 2.83, 1, 2, 11, 34.19, 6.03, 0.28636, 12, 1.83, 4.42, 0.71364, 2, 11, 47.86, 16.85, 0, 12, 19.13, 2.35, 1, 2, 12, 33.46, 5.03, 0.95263, 13, -7.08, 8.15, 0.04737, 1, 13, 8.09, 1.66, 1, 2, 13, 23.05, 2.44, 0.99488, 14, -10.18, 4.6, 0.00512, 1, 14, 6.91, 6.18, 1, 1, 14, 23.61, 8.06, 1, 1, 14, 38.05, 4.09, 1, 1, 15, 4.31, 5.11, 1, 1, 15, 20.05, 7.18, 1, 2, 15, 37.23, 1.51, 0.98404, 16, -6.61, 0.18, 0.01596, 2, 15, 46.4, -7.05, 2e-05, 16, 5.33, 3.98, 0.99998, 2, 15, 53.03, -13.24, 0, 16, 13.99, 6.74, 1], "edges": [80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 78, 80, 76, 78]}}, "light2": {"light2": {"x": 3.96, "width": 499, "y": -13.94, "height": 476}}}}, "skeleton": {"images": "./images/", "x": -210.14, "width": 499, "y": -49.88, "spine": "3.7-from-3.8-from-4.0.61", "audio": "D:/job/Sang/anim-RongHo/Dragon", "hash": "nhYLjRGsP/w", "height": 476}, "slots": [{"attachment": "light2", "name": "light2", "bone": "light2"}, {"attachment": "light1", "name": "light1", "bone": "light1"}, {"attachment": "Dragon", "name": "Dragon", "bone": "Dragon17"}, {"attachment": "Layer 6", "name": "Layer 6", "bone": "Layer 14"}, {"attachment": "Layer 5", "name": "Layer 5", "bone": "Layer 19"}, {"attachment": "close_eyes", "name": "close_eyes", "bone": "close_eyes"}], "bones": [{"name": "root"}, {"parent": "root", "name": "Dragon", "x": 10.91, "y": 9.36}, {"parent": "Dragon", "rotation": 117.65, "name": "Dragon3", "length": 66.58, "x": -5.94, "y": 8.13}, {"parent": "Dragon3", "rotation": -65.52, "name": "Dragon2", "length": 32.02, "x": 71.17, "y": -6.37}, {"parent": "Dragon2", "rotation": 51.57, "name": "Dragon5", "length": 142.36, "x": 54.74, "y": 1.66}, {"parent": "Dragon5", "rotation": -47.38, "name": "Dragon4", "length": 93.98, "x": 134.64, "y": -21.85}, {"parent": "Dragon5", "rotation": 30.26, "name": "Dragon7", "length": 67.59, "x": 130.37, "y": 34.03}, {"parent": "Dragon5", "rotation": -4, "name": "Dragon8", "length": 24.09, "x": 145.61, "y": -1.28}, {"parent": "Dragon8", "rotation": -29.91, "name": "Dragon6", "length": 46.91, "x": 30.26, "y": -5.17}, {"parent": "Dragon5", "name": "close_eyes", "x": 85.75, "y": 2.58}, {"parent": "Dragon5", "rotation": -120.25, "name": "Layer 6", "length": 22.36, "x": 46.97, "y": -19.57}, {"parent": "Layer 6", "rotation": 19.52, "name": "Layer 8", "length": 33.64, "x": 26.63, "y": 1.88}, {"parent": "Layer 8", "rotation": 45.18, "name": "Layer 7", "length": 37.33, "x": 36.04, "y": 1.62}, {"parent": "Layer 7", "rotation": 37.7, "name": "Layer 10", "length": 31.94, "x": 44.05, "y": 2.91}, {"parent": "Layer 10", "rotation": 18.58, "name": "Layer 9", "length": 41.86, "x": 34.17, "y": 1.32}, {"parent": "Layer 9", "rotation": -61.4, "name": "Layer 12", "length": 35.65, "x": 44.65, "y": -3.11}, {"parent": "Layer 12", "rotation": -60.68, "name": "Layer 11", "length": 26.74, "x": 40.31, "y": -4.35}, {"parent": "Layer 11", "rotation": -81.1, "name": "Layer 14", "length": 22.85, "x": 26.32, "y": -4.39}, {"parent": "Dragon5", "rotation": 124.22, "name": "Layer 5", "length": 24.2, "x": 44.96, "y": 33.38}, {"parent": "Layer 5", "rotation": -46.44, "name": "Layer 15", "length": 22.6, "x": 29.8, "y": -2.75}, {"parent": "Layer 15", "rotation": -61.72, "name": "Layer 13", "length": 23.35, "x": 24.82, "y": -3.53}, {"parent": "Layer 13", "rotation": -33.62, "name": "Layer 17", "length": 34.25, "x": 25.72, "y": -4.02}, {"parent": "Layer 17", "rotation": 69.1, "name": "Layer 16", "length": 24.88, "x": 38.72, "y": 2.62}, {"parent": "Layer 16", "rotation": 93.27, "name": "Layer 19", "length": 20.54, "x": 26.09, "y": 5.66}, {"parent": "Dragon3", "rotation": 38.13, "name": "Dragon10", "length": 12.7, "x": 66.82, "y": 25.08}, {"parent": "Dragon10", "rotation": -18.61, "name": "Dragon9", "length": 43.44, "x": 16.93, "y": 1.9}, {"parent": "Dragon3", "rotation": 2.1, "name": "Dragon12", "length": 56.04, "x": -5.23, "y": -45.06}, {"parent": "Dragon5", "rotation": -37.4, "name": "Dragon13", "length": 51.87, "x": 78.54, "y": -61.24}, {"parent": "Dragon13", "rotation": -39.73, "name": "Dragon11", "length": 40.15, "x": 60.63, "y": -1.22}, {"parent": "Dragon5", "rotation": -44.66, "name": "Dragon15", "length": 54.04, "x": 58.11, "y": -74.15}, {"parent": "Dragon15", "rotation": -76.63, "name": "Dragon14", "length": 49.82, "x": 57.31, "y": -2.09}, {"parent": "Dragon3", "rotation": -78.72, "name": "Dragon17", "length": 77.43, "x": -10.27, "y": -78.39}, {"parent": "root", "name": "light2", "x": 35.4, "y": 202.06}, {"parent": "light2", "name": "light1", "x": -19.61, "y": -10.59}], "animations": {"animation": {"slots": {"close_eyes": {"attachment": [{"name": null, "time": 0}, {"name": "close_eyes", "time": 1.6}, {"name": null, "time": 1.6667}, {"name": "close_eyes", "time": 3.1667}, {"name": null, "time": 3.2333}]}, "light1": {"color": [{"color": "ffffff61", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.8333}, {"color": "ffffffff", "time": 2}, {"color": "ffffff5d", "time": 4}]}, "light2": {"color": [{"color": "ffffff61", "time": 0}, {"color": "ffffffff", "time": 0.8333}, {"color": "ffffff5d", "time": 4}]}}, "bones": {"Dragon17": {"rotate": [{"angle": 0, "time": 0}, {"angle": -2.9, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 13": {"rotate": [{"angle": 0, "time": 0}, {"angle": -12.48, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 12": {"rotate": [{"angle": 0, "time": 0}, {"angle": -7.63, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 19": {"rotate": [{"angle": 0, "time": 0}, {"angle": 3.31, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 17": {"rotate": [{"angle": 0, "time": 0}, {"angle": -17.02, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 16": {"rotate": [{"angle": 0, "time": 0}, {"angle": 5.91, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon2": {"rotate": [{"angle": 0, "time": 0}, {"angle": 4.12, "time": 0.5}, {"curve": "stepped", "angle": 4.87, "time": 0.7}, {"angle": 4.87, "time": 0.7333}, {"angle": 0, "time": 1.3667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1.177, "y": 1.177, "time": 0.7}, {"x": 1.177, "y": 1.177, "time": 0.7333}, {"x": 1, "y": 1, "time": 1.3667}]}, "Dragon11": {"rotate": [{"angle": 0, "time": 0}, {"angle": -5.46, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon10": {"rotate": [{"angle": 0, "time": 0}, {"angle": 12.68, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon15": {"rotate": [{"angle": 0, "time": 0}, {"angle": -6.47, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon14": {"rotate": [{"angle": 0, "time": 0}, {"angle": -18.24, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon12": {"rotate": [{"angle": 0, "time": 0}, {"angle": -13.47, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 7": {"rotate": [{"angle": 0, "time": 0}, {"angle": -5.55, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 8": {"rotate": [{"angle": 0, "time": 0}, {"angle": -12.27, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon9": {"rotate": [{"angle": 0, "time": 0}, {"angle": 9.88, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 9": {"rotate": [{"angle": 0, "time": 0}, {"angle": -12.82, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon6": {"rotate": [{"angle": 0, "time": 0}, {"angle": -16.25, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon5": {"rotate": [{"angle": 0, "time": 0}, {"angle": 4.23, "time": 0.5}, {"curve": "stepped", "angle": -2.77, "time": 0.7}, {"angle": -2.77, "time": 0.7333}, {"angle": 0, "time": 1.3667}]}, "Dragon4": {"rotate": [{"angle": 0, "time": 0}, {"angle": -3.18, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Dragon3": {"rotate": [{"angle": 0, "time": 0}, {"angle": 1.41, "time": 0.5}, {"curve": "stepped", "angle": 1.81, "time": 0.7}, {"angle": 1.81, "time": 0.7333}, {"angle": 0, "time": 1.3667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1.036, "y": 1.036, "time": 0.7}, {"x": 1.036, "y": 1.036, "time": 0.7333}, {"x": 1, "y": 1, "time": 1.3667}]}, "Layer 11": {"rotate": [{"angle": 0, "time": 0}, {"angle": -16.48, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "Layer 5": {"rotate": [{"angle": 0, "time": 0}, {"angle": 15.36, "time": 0.5}, {"angle": 0, "time": 1.3667}]}, "light2": {"rotate": [{"angle": 0, "time": 0}, {"angle": -360, "time": 4}], "scale": [{"x": 0.726, "y": 0.726, "time": 0}, {"x": 1, "y": 1, "time": 0.8333}, {"x": 0.726, "y": 0.726, "time": 4}]}}, "deform": {"default": {"Dragon": {"Dragon": [{"time": 0}, {"offset": 632, "time": 0.5, "vertices": [2.33125, -0.1076, 1.22152, -1.98861, 0.05264, -2.33308, 2.33125, -0.1076, 1.22152, -1.98861, 0.05264, -2.33308, 1.22152, -1.98861, 0.05264, -2.33308, 0.39734, -1.0059, -0.16385, -1.06898, 1.05523, -0.23685, 0.39734, -1.0059, -0.16385, -1.06898, 1.05523, -0.23685, 0.39734, -1.0059, -0.16385, -1.06898, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.75657, -0.06454, 1.49617, -2.31617, 0.12474, -2.75444, 2.49274, -0.00528, 1.3969, -2.06467, 0.16582, -2.4872, 2.33122, -0.10763, 1.22149, -1.98861, 0.05264, -2.33307, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.17088, 1.11728, 1.58254, -0.34044, 1.19499, -1.0912, 1.58254, -0.34044, 1.19499, -1.0912, 3.44745, -1.83471, 2.05249, -3.32158, 3.44745, -1.83471, 2.05249, -3.32158, 0.24209, -1.8349, -0.71576, -1.70671, 1.58241, -0.3404, 1.19505, -1.09131, 1.58241, -0.3404, 1.19505, -1.09131, 1.58241, -0.3404, 1.19505, -1.09131, 1.58241, -0.3404, 1.19505, -1.09131, 1.58241, -0.3404, 1.19505, -1.09131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.36848, -1.85561, -0.23801, -1.8768, 1.18863, -1.4718, -1.25372, -1.41675, -0.36848, -1.85561, -0.23801, -1.8768, -1.25372, -1.41675, -0.36848, -1.85561, -0.23801, -1.8768, -1.25372, -1.41675, -0.36848, -1.85561, -0.23801, -1.8768, 1.18863, -1.4718, -1.25372, -1.41675, -1.86646, -2.08157, -1.71658, -2.20683, 0.40266, -2.76692, -2.66141, -0.8569, -2.32582, -0.91851, -2.25604, -1.0787, -0.7845, -2.37447, -2.4718, 0.37894, -0.90887, -4.96056, 2.94835, -4.09149, -3.54453, -3.58742, -0.90887, -4.96056, 2.94835, -4.09149, -3.54453, -3.58742, -0.90887, -4.96056, 2.94835, -4.09149, -3.54453, -3.58742, -0.90887, -4.96056, 2.94835, -4.09149, -3.54453, -3.58742, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.83192, 0.88845, 2.32671, -1.84285, 1.08069, -2.76418, 0, 0, 0, 0, 0, 0, 1.1335, -6.27393, 1.56885, -6.17942, 5.54376, -3.14844, -2.18307, -5.98993, -0.78448, -2.37453, -0.76804, -2.37982, 0.79497, -1.57953, -0.04926, -2.79543, 1.98221, -1.97171, 1.9958, -1.95792, -0.35181, -0.81109, -0.29431, -0.83369, -0.71266, -0.52321, 0.91411, 4.0681, 3.87873, 1.53058, 1.28111, 3.96774, 4.12117, -0.63264, 2.15106, 3.57154, -1.01735, 4.04362, -0.03896, 2.90014, 2.37708, 1.66234, 0.2254, 2.89159, 2.89055, 0.23802, 0.87788, 2.76422, -1.3416, 2.5715, 0.19737, 2.89383, -2.89986, -0.04822, 2.47513, 2.13167, -1.16986, 3.05004, 1.2017, 2.53785, 2.77461, 0.43259, 1.42807, 2.41765, 2.61432, -1.0246, 1.94109, 2.02871, -0.07097, 2.80725, 1.40442, 2.43156, -2.54182, 1.19348, 3.05996, 0.79874, 0.28239, 3.1499, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.33568, -3.9587, -5.71098, 1.3608, -4.67899, -3.54665, -4.24695, 4.05379, -5.36415, -2.38736, -2.08582, -5.48788, -4.6438, -3.59221, 3.97336, -4.32208, -6.55371, 0.87822, -3.04176, -5.87114, -4.33568, -3.9587, -5.71098, 1.3608, -4.67899, -3.54665, -4.24695, 4.05379, -5.36415, -2.38736, -2.08582, -5.48788, -4.6438, -3.59221, 3.97336, -4.32208, -6.55371, 0.87822, -3.04176, -5.87114, -4.33568, -3.9587, -5.71098, 1.3608, -4.67899, -3.54665, -4.24695, 4.05379, -5.36415, -2.38736, -2.08582, -5.48788, -4.6438, -3.59221, 3.97336, -4.32208, -6.55371, 0.87822, -3.04176, -5.87114]}, {"time": 1.3667}]}}}}}}, [0]]], 0, 0, [0], [-1], [34]], [[{"name": "BG+table", "rect": [0, 0, 1560, 1170], "offset": [0, 0], "originalSize": [1560, 1170], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [35]], [[[44, "openPopupWin", 0.2833333333333333, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.2833333333333333, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0, 0]], [{"frame": 0.21666666666666667}, "value", 8, [0, 0.7, 0.7]], [{"frame": 0.2833333333333333}, "value", 8, [0, 0.5, 0.5]]], 11, 11, 11]]]]], 0, 0, [], [], []], [[{"name": "<PERSON>_<PERSON>_choose", "rect": [0, 0, 142, 147], "offset": [0, 0], "originalSize": [142, 147], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [36]], [[{"name": "D_Rong", "rect": [0, 0, 472, 239], "offset": [0, 0], "originalSize": [472, 239], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [37]], [[[64, "dragonTigerView"], [65, "dragonTigerView", [[[66, "border", -50, [0, "31F0HHvQhIx7gpwxXsgAEe", -49], [5, 1280, 720]], -51, -52, -53, -54, -55, -56, -57, -58], 4, 1, 1, 1, 1, 1, 1, 1, 1], [[83, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3, -2, 305, 306, 307], [84, -19, -18, -17, 308, 309, 310, 311, 312], [85, -40, [313, 314, 315, 316, 317, 318, 319, 320, 321], -39, -38, -37, -36, [-29, -30, -31, -32, -33, -34, -35], -28, [-21, -22, -23, -24, -25, -26, -27], -20], [86, -48, [-41, -42, -43, -44, -45, -46, -47]]], [82, -1]], [70, "dragonTigerMain", 1, [[-76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92, -93, -94, [5, "posChipMove", -95, [0, "54nr+v0ldELpSZUfMRnIyW", 1], [381.727, -227.651, 0, 0, 0, 0, 1, 1, 1, 1]], -96, -97, -98, -99, -100, -101, -102], 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 1, 1, 1, 1, 1], [[[87, -65, -64, -63, -62, -61, -60, -59], [88, -67, -66], -68, [89, -71, -70, -69], [90, -75, -74, -73, -72]], 4, 4, 1, 4, 4], [0, "8djiwrJV9AwYJvs9yPRAvf", 1], [5, 1280, 720]], [67, "layoutChip", 2, [[-103, -104, -105, -106, -107, -108, -109, [23, "nodeChipsRong", -110, [0, "88lmCJOZZCBLeirtIx+3Qn", 1], [5, 150, 130], [-225, -95.407, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "nodeChipsHo", -111, [0, "126qZx2V9CWr3+MHs5Sldd", 1], [5, 150, 130], [236.218, -95.407, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "nodeChipsHoa", -112, [0, "91WKmHKUJAWr5TEGJu2EfO", 1], [5, 140, 100], [3.587, -129.107, 0, 0, 0, 0, 1, 1, 1, 1]], -113, -114, -115, -116, -117, -118, -119, -120, -121], 1, 1, 1, 1, 1, 1, 1, 4, 4, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1], [0, "aenunlumRABYalvw+RGBZQ", 1]], [54, "layoutSessionHistoy", 2, [-139, -140, -141, -142, -143, -144, -145, -146, -147, -148, -149, -150, -151, -152, -153], [[91, 3, 10, 10, 21, 0, true, -122, [5, 870, 30], [5, 30, 30]], [94, -138, [-123, -124, -125, -126, -127, -128, -129, -130, -131, -132, -133, -134, -135, -136, -137], [23, 24, 25]]], [0, "f18WzrOFtBobEDJfh+iajv", 1], [5, 870, 30], [0, 0.5, 0], [9, 325.034, 0, 0, 0, 0, 1, 1, 1, 1]], [77, "popupMenu", [-162, -163, -164, -165, -166], [[-154, [95, -159, -158, -157, -156, -155, [144, 145], [146, 147]], [92, false, 1, 2, 25, 18, 10, -160, [5, 111, 488]], [58, false, 1, 0, -161, [148], 149]], 1, 4, 4, 4], [0, "1c+PhW8kpP0p596VXvGQry", 1], [5, 111, 488], [0, 0.5, 1], [-90.25, 515.669, 0, 0, 0, 0, 1, 1, 1, 1]], [55, "audioPool", 1, [-178, -179, -180, -181, -182, -183, -184, -185, -186, -187], [[97, -177, -176, -175, -174, -173, -172, -171, -170, -169, -168, -167]], [0, "4cllTJZ+xAr7B4mrCQ2SgG", 1]], [54, "layoutButtonBets", 2, [-189, -190, -191, -192, -193, -194, -195], [[93, 1, 3, 50, -188, [5, 850, 90], [5, 65, 68]]], [0, "99/+BHkOZNC5x7HFa+tfD4", 1], [5, 850, 90], [0, 0.5, 0.51], [47.771, -306.486, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [71, "nodeBetSides", 2, [[-205, -206, -207, [5, "nodeChipMove", -208, [0, "7dCN9N4MJL6qNp5N7A3myl", 1], [0, -300, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 1, 1, 4], [[-196, [98, -204, -203, -202, -201, -200, -199, -198, -197]], 1, 4], [0, "8cbsvHoOpFYYpbE/v2UuHc", 1], [25.727, 61.349, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "user-pos", 2, [-209, -210, -211, -212, -213, -214, -215], [0, "f0naoeQRlPK4x8ZwqFg0pp", 1], [0, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [78, "btnPos6", false, 9, [-218, -219, -220, -221, -222], [[[6, -216, [173, 174]], -217], 4, 1], [0, "37M8cE+5pLjIZhatPLwuE/", 1], [5, 100, 100], [694.067, 359.726, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnPos5", 9, [-225, -226, -227, -228, -229], [[-223, [6, -224, [192, 193]]], 1, 4], [0, "1dflFExnJBG7xtrJNP9RAj", 1], [5, 100, 100], [694.067, 249.726, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnPos4", 9, [-232, -233, -234, -235, -236], [[[6, -230, [211, 212]], -231], 4, 1], [0, "09FkXrx+FOLpJm+QGxyc9C", 1], [5, 100, 100], [694.067, 139.726, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnPos3", 9, [-239, -240, -241, -242, -243], [[-237, [6, -238, [230, 231]]], 1, 4], [0, "65vcNs1hFO0rgDwzKpL/MO", 1], [5, 100, 100], [694.067, 29.726, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnPos2", 9, [-246, -247, -248, -249, -250], [[-244, [6, -245, [250, 251]]], 1, 4], [0, "a5s+G9eANODq/AGBhozhSL", 1], [5, 100, 100], [694.067, -80.274, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnPos1", 9, [-253, -254, -255, -256, -257], [[-251, [6, -252, [269, 270]]], 1, 4], [0, "1cU+va9/ZD0ZUvCxzGEP6y", 1], [5, 100, 100], [694.067, -190.274, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnPos", 9, [-260, -261, -262, -263, -264], [[-258, [6, -259, [288, 289]]], 1, 4], [0, "d20uZCgkZJxaFSZVeelX1q", 1], [5, 100, 100], [-538.683, -263.84299999999996, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sideRong", 8, [-268], [[47, 1.05, 3, -266, [[16, "9045aSGcglIarpLEVHVnxFT", "setBetSide", "1", 2]], [4, **********], -265, 46, 47, 48, 49], [48, -267]], [0, "fcDHdSF19M9ojumpkchC7Q", 1], [5, 390, 252], [-225.243, -105.052, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "bgSide", 17, [-270, -271, -272, -273, -274], [[3, -269, [44], 45]], [0, "2cC6roCOpPE4fZE5D31qWc", 1], [5, 250, 257]], [1, "sideHo", 8, [-278], [[47, 1.05, 3, -276, [[16, "9045aSGcglIarpLEVHVnxFT", "setBetSide", "3", 2]], [4, **********], -275, 62, 63, 64, 65], [48, -277]], [0, "a5MaxbMhROt5WIfd4Bkmfi", 1], [5, 340, 200], [232.528, -105.052, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "bgSide", 19, [-280, -281, -282, -283, -284], [[3, -279, [60], 61]], [0, "14sPlTS3pCIbPwvAdSMPb+", 1], [5, 250, 257]], [1, "sideHoa", 8, [-288], [[47, 1.05, 3, -286, [[16, "9045aSGcglIarpLEVHVnxFT", "setBetSide", "2", 2]], [4, **********], -285, 76, 77, 78, 79], [48, -287]], [0, "2bLX+w/ZZIx6Bn3t4Igxxa", 1], [5, 122, 150], [4, -136, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "bgSide", 21, [-290, -291, -292, -293, -294], [[3, -289, [74], 75]], [0, "620bFEY7xNSr9TF2qhnh6k", 1], [5, 164, 193]], [72, "bgUser", [-299, -300], [[3, -295, [152], 153], [99, -296, [155], 154], [7, 3, -298, [[19, "da958BXEnRMGZF0W+LkzH8h", "createGroupUserView", 1]], [4, **********], -297]], [0, "f8BYwMOjJGG4PC4lAKU0yH", 1], [5, 115, 115]], [4, "session15", 4, [[-301, [100, true, -302, [22], 21], [13, -304, [4, **********], -303]], 1, 4, 4], [0, "983/8CXYVD8akjM5R67tQ1", 1], [5, 35, 35], [376.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "bg_time", [-306, -307, -308], [[15, 1, 0, -305, [85], 86]], [0, "d8ELD/PklFwZWGGld0ecgV", 1], [5, 80, 80], [-37, 34, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBet1k", 7, [-312], [[7, 3, -310, [[16, "9045aSGcglIarpLEVHVnxFT", "setBalanceBet", "1000", 2]], [4, **********], -309], [27, -311, [95, 96]]], [0, "99uLfgUttHWL2VFRTqHU6X", 1], [5, 69, 90], [-390.5, -0.8999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBet5k", 7, [-316], [[7, 3, -314, [[16, "9045aSGcglIarpLEVHVnxFT", "setBalanceBet", "5000", 2]], [4, **********], -313], [27, -315, [99, 100]]], [0, "8fALXr7qNHSqUuNFZaUROS", 1], [5, 69, 90], [-271.5, -0.8999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBet10k", 7, [-320], [[7, 3, -318, [[16, "9045aSGcglIarpLEVHVnxFT", "setBalanceBet", "10000", 2]], [4, **********], -317], [27, -319, [103, 104]]], [0, "b6dzq9lTtNSrJ5rELNaXjj", 1], [5, 69, 90], [-152.5, -0.8999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBet50k", 7, [-324], [[7, 3, -322, [[16, "9045aSGcglIarpLEVHVnxFT", "setBalanceBet", "50000", 2]], [4, **********], -321], [27, -323, [107, 108]]], [0, "e2cuPtup9CnLrtTmeZKpR4", 1], [5, 69, 90], [-33.5, -0.8999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBet100k", 7, [-328], [[7, 3, -326, [[16, "9045aSGcglIarpLEVHVnxFT", "setBalanceBet", "100000", 2]], [4, **********], -325], [27, -327, [111, 112]]], [0, "dd6Pf2FtlOVaM7KyURtzxg", 1], [5, 69, 90], [85.5, -0.8999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBet500k", 7, [-332], [[7, 3, -330, [[16, "9045aSGcglIarpLEVHVnxFT", "setBalanceBet", "500000", 2]], [4, **********], -329], [27, -331, [115, 116]]], [0, "0d4ttoFptAd469N+Sqcyd2", 1], [5, 69, 90], [204.5, -0.8999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnBet1M", 7, [-336], [[7, 3, -334, [[16, "9045aSGcglIarpLEVHVnxFT", "setBalanceBet", "1000000", 2]], [4, **********], -333], [27, -335, [119, 120]]], [0, "59RwEvBdtCQ5641r2EEw2b", 1], [5, 69, 90], [323.5, -0.8999999999999986, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnDatLai", false, 2, [[15, 1, 0, -337, [121], 122], [7, 3, -339, [[16, "9045aSGcglIarpLEVHVnxFT", "onBetAgain", "1", 2]], [4, **********], -338]], [0, "43SLfXgmZHX6BSEY+yGZ7h", 1], [5, 81, 82], [460.676, -253.651, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "btnX2", false, 2, [[15, 1, 0, -340, [123], 124], [7, 3, -342, [[16, "9045aSGcglIarpLEVHVnxFT", "onBetAgain", "2", 2]], [4, **********], -341]], [0, "a3c4I5f05BiaHwT9dJdBc+", 1], [5, 81, 82], [558.417, -254.252, 0, 0, 0, 0, 1, 1, 1, 0]], [56, "layoutButtonPopup", 2, [-343, -344, -345, -346], [0, "2dfJ7kKQNPXLWNBOtLfN+P", 1]], [74, "offset-message", false, 0, [-348, -349], [[96, 768, 2, false, -347, [299]]], [0, "57ZouYMXBEbIDj9VLBW5i8", 1], [5, 451, 80]], [75, "popupWin", 0, 1, [-351, -352], [[6, -350, [304]]], [0, "99yIn1sTZCk4ghQIZSqrIa", 1], [0, -98, 0, 0, 0, 0, 1, 0, 0, 1]], [4, "session1", 4, [[-353, [13, -355, [4, **********], -354]], 1, 4], [0, "03UdiONk1EjZAcylFazQXb", 1], [5, 35, 35], [-407.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session2", 4, [[-356, [13, -358, [4, **********], -357]], 1, 4], [0, "07xKrKveJLZJ9QAv5AI0QI", 1], [5, 35, 35], [-351.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session3", 4, [[-359, [13, -361, [4, **********], -360]], 1, 4], [0, "046lRlE9JIW4m5W3/tkENc", 1], [5, 35, 35], [-295.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session4", 4, [[-362, [13, -364, [4, **********], -363]], 1, 4], [0, "19M3nXZvRJ/YrqhZQT6tkV", 1], [5, 35, 35], [-239.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session5", 4, [[-365, [13, -367, [4, **********], -366]], 1, 4], [0, "62TlEzMyVPybdQ9cOEVv7l", 1], [5, 35, 35], [-183.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session6", 4, [[-368, [13, -370, [4, **********], -369]], 1, 4], [0, "472gptPZFL0auT5KEi9Tsc", 1], [5, 35, 35], [-127.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session7", 4, [[-371, [13, -373, [4, **********], -372]], 1, 4], [0, "87djpMx1JAFIZskCrHIqd8", 1], [5, 35, 35], [-71.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session8", 4, [[-374, [13, -376, [4, **********], -375]], 1, 4], [0, "30fXHYTJdAxq7YV1l19aFm", 1], [5, 35, 35], [-15.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session9", 4, [[-377, [13, -379, [4, **********], -378]], 1, 4], [0, "06K4rHyHZAiq7twssyCljJ", 1], [5, 35, 35], [40.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session10", 4, [[-380, [13, -382, [4, **********], -381]], 1, 4], [0, "1aOsG/XGJECZDn1ePDA2jt", 1], [5, 35, 35], [96.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session11", 4, [[-383, [13, -385, [4, **********], -384]], 1, 4], [0, "c8CuX5B/VL9IwFTLUx70nC", 1], [5, 35, 35], [152.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session12", 4, [[-386, [13, -388, [4, **********], -387]], 1, 4], [0, "f8Lnv5akFDfI+IbI5MKMvy", 1], [5, 35, 35], [208.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session13", 4, [[-389, [13, -391, [4, **********], -390]], 1, 4], [0, "d0n7VtqMFKAaXe0/inXJqs", 1], [5, 35, 35], [264.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "session14", 4, [[-392, [13, -394, [4, **********], -393]], 1, 4], [0, "bf3FqNzPBOvqpZON9rJvlQ", 1], [5, 35, 35], [320.5, 17.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnClose", 35, [[9, 0, -395, [125], 126], [7, 3, -397, [[19, "76024KUhhFBtabZIY7jMFca", "backClicked", 1]], [4, **********], -396]], [0, "93KEjPhpVAJ6sX5g06GVnE", 1], [5, 80, 80], [-710, 300, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnChat", 35, [[9, 0, -398, [127], 128], [7, 3, -400, [[19, "76024KUhhFBtabZIY7jMFca", "chatClicked", 1]], [4, **********], -399]], [0, "fa6LZeXvxJHZ/j/kIU3l69", 1], [5, 80, 80], [-711.979, -189.385, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnSoiCau", 35, [[9, 0, -401, [129], 130], [7, 3, -403, [[19, "76024KUhhFBtabZIY7jMFca", "graphClicked", 1]], [4, **********], -402]], [0, "20OlE2hX5MqqRK77ltlBPT", 1], [5, 80, 80], [-711.979, -89.385, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnMenu", false, 35, [[3, -404, [131], 132], [7, 3, -406, [[19, "ad402R7yshJsYOag7Xuj6+9", "openSettingClicked", 5]], [4, **********], -405]], [0, "41VqwxDTNG7aGSuY/vkQ7K", 1], [5, 81, 82], [-710, -120, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "btnAward", 5, [[9, 0, -407, [134], 135], [7, 3, -409, [[19, "76024KUhhFBtabZIY7jMFca", "topClicked", 1]], [4, **********], -408]], [0, "05E/wetuhGwbR5nsAuogpR", 1], [5, 80, 80], [0, -70, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnHistory", 5, [[9, 0, -410, [136], 137], [7, 3, -412, [[19, "76024KUhhFBtabZIY7jMFca", "historyClicked", 1]], [4, **********], -411]], [0, "4c7k4qYNNI3ohsn6rq1B0a", 1], [5, 80, 80], [0, -170, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnHelp", 5, [[9, 0, -413, [138], 139], [7, 3, -415, [[19, "76024KUhhFBtabZIY7jMFca", "helpClicked", 1]], [4, **********], -414]], [0, "feFjBOsFhBtL64bkbu35jV", 1], [5, 80, 80], [0, -270, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "music_on", false, 5, [[-416, [7, 3, -418, [[19, "ad402R7yshJsYOag7Xuj6+9", "musicClicked", 5]], [4, **********], -417]], 1, 4], [0, "71ADpC90lE0oLd03ME3FL/", 1], [5, 81, 81], [0, -429.5, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "ava_money", 10, [-420, -421], [[9, 0, -419, [165], 166]], [0, "27wxJp8/tEiqEWxfSoskAW", 1], [5, 140, 50], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-name copy", 60, [-423, -424], [[25, 1, 1, 2, -422, [5, 89.06, 50]]], [0, "7cWEZ42xJLQbQ8tAFYFJOr", 1], [5, 89.06, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 11, [-426, -427], [[9, 0, -425, [184], 185]], [0, "1fo95d6G9HfZL3eGbMCCzW", 1], [5, 140, 50], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-name copy", 62, [-429, -430], [[25, 1, 1, 2, -428, [5, 89.06, 50]]], [0, "c8GYgONcNFIYVpy9RrxtkJ", 1], [5, 89.06, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 12, [-432, -433], [[9, 0, -431, [203], 204]], [0, "99OUUmhudMUbeibfo/0LkF", 1], [5, 140, 50], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-name", 64, [-435, -436], [[25, 1, 1, 2, -434, [5, 89.06, 50]]], [0, "8ch2QgzElCQ5EiYKnHKZta", 1], [5, 89.06, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 13, [-438, -439], [[9, 0, -437, [222], 223]], [0, "e4mbuYFQNF+LfCh3j0fwpa", 1], [5, 140, 50], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-name copy", 66, [-441, -442], [[25, 1, 1, 2, -440, [5, 89.06, 50]]], [0, "c7Vgb4x1xLlYW+Dw6LnST3", 1], [5, 89.06, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 14, [-444, -445], [[9, 0, -443, [242], 243]], [0, "68LrptI4NIzbI61+2ybhQc", 1], [5, 140, 50], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-name copy", 68, [-447, -448], [[25, 1, 1, 2, -446, [5, 89.06, 50]]], [0, "66WfGg0u1JPIgT17eqeXWQ", 1], [5, 89.06, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 15, [-450, -451], [[9, 0, -449, [261], 262]], [0, "0f5VUJ7t5GaZU2VEvQ6cIP", 1], [5, 140, 50], [-120, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-name copy", 70, [-453, -454], [[25, 1, 1, 2, -452, [5, 89.06, 50]]], [0, "47+Gtt24ZN1L4CbY0cDREA", 1], [5, 89.06, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 16, [-456, -457], [[9, 0, -455, [280], 281]], [0, "a0GlPrxX1Acp6HwWf11M8V", 1], [5, 160, 60], [141.181, -38.461, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "layout-name copy", 72, [-459, -460], [[25, 1, 1, 2, -458, [5, 89.06, 50]]], [0, "a9BWnTaqlHOK1trcvGNsHN", 1], [5, 89.06, 50], [0, 15.93, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "popupSlotsView", 1, [36], [[6, -461, [300]], [104, 0, -463, 36, -462]], [0, "b6tndIPE5FkqrDo1WEdGAc", 1], [5, 444, 220]], [40, "background", false, 1, [[15, 1, 0, -464, [0], 1], [105, -465]], [0, "0cWv8/TYVJHJTEPpJiej46", 1], [5, 1561, 760]], [39, "nodeCardStart", 2, [-466], [0, "54qcWPSydPpJTEXWoY/0Su", 1], [191.103, 118.642, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "nodeCardBurn", 2, [-467], [0, "97o1i2NQFNsalODq6NxiOw", 1], [5, 48, 62], [-172.748, 137.609, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bgWin", false, 18, [[17, "default", "animation", 0, "animation", -468, [36], 37]], [0, "8e1/RspDJLYpSXdbB/fGxV", 1], [5, 499, 476], [-16.189, -125.692, 0, 0, 0, 0, 1, 0.67, 0.67, 0]], [4, "lbTotalBetDragon", 18, [[-469, [42, -471, -470]], 1, 4], [0, "7dccUdoR1L5Kr6RR6z4Y0k", 1], [5, 210, 30], [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 30, 50, false, 1, 1, 2, 79, [40]], [1, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 18, [-473], [[3, -472, [42], 43]], [0, "97l4r8hk9GQLHLHGWYRrdj", 1], [5, 227, 54], [6.81, -101.312, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bgWin", false, 20, [[17, "default", "animation", 0, "animation", -474, [52], 53]], [0, "b3X7oY2qNE6JSp5wxQ+19+", 1], [5, 499, 476], [-5, -100.848, 0, 0, 0, 0, 1, 0.67, 0.67, 1]], [4, "lbTotalBetTiger", 20, [[-475, [42, -477, -476]], 1, 4], [0, "afGBzlJXNBerSbTIIHaNqI", 1], [5, 210, 30], [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 30, 50, false, 1, 1, 2, 83, [56]], [1, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 20, [-479], [[3, -478, [58], 59]], [0, "16PKSuzCpO4YG+y7lUhJiM", 1], [5, 227, 54], [-5.487, -101.312, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bgWin", false, 22, [[17, "default", "animation", 0, "animation", -480, [68], 69]], [0, "38PONzdTFEvrnNKaMHI98n", 1], [5, 202, 193], [2.399, 22.591, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [4, "lbTotalBetTide", 22, [[-481, [42, -483, -482]], 1, 4], [0, "a7YDns2YVIPZ6MgIQCGjyl", 1], [5, 180, 30], [1, 87.017, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 30, 50, false, 1, 1, 2, 87, [72]], [55, "nodeTimeBetting", 25, [-485], [[6, -484, [81]]], [0, "81MVfwV/tPWqJmJ54UV2ti", 1]], [76, "mask", [5], [[111, false, 110, 0, -486, [133]]], [0, "10yBCrvYlIBakCtIXArazd", 1], [5, 170, 500], [0, 0.5, 1], [69.926, -380.432, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "sound_on", false, 5, [[-487, [103, 3, -488, [[19, "ad402R7yshJsYOag7Xuj6+9", "soundClicked", 5]], [4, **********]]], 1, 4], [0, "d4lpa1z+lCt4Qpk7K4loc4", 1], [5, 81, 81], [0, -338.5, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "Avatar", 10, [[26, 1, 0, false, -489, [156], 157], [31, -490]], [0, "23srub/RhHA5seNZyWsRuL", 1], [4, 4293322470], [5, 86, 86]], [10, "lbWin", false, 10, [[36, 15, false, 1, 1, -491, 160], [6, -492, [161]]], [0, "3fupj4eU5Eh6mZpZsClJ4r", 1], [5, 80.63, 37.5], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 60, [[20, "10.M", 12, false, 1, 1, -493, [163], 164], [28, -494]], [0, "58fZpfgKZC4pZtnjixK8V6", 1], [5, 23.25, 40], [0, -4.479, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 10, [-495, -496], [0, "bcOWjxLbpAMbDKvbkZu/53", 1]], [1, "bubble", 95, [-498], [[15, 1, 0, -497, [171], 172]], [0, "34cSIvdsNH/74h0BrGaNS4", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Avatar", 11, [[26, 1, 0, false, -499, [175], 176], [31, -500]], [0, "46WA8b5/FFd5t6CsPG8qLl", 1], [4, 4293322470], [5, 86, 86]], [10, "lbWin", false, 11, [[36, 15, false, 1, 1, -501, 179], [6, -502, [180]]], [0, "b6MaufCkxN64PKndrNmhyw", 1], [5, 80.63, 37.5], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 62, [[20, "10.M", 12, false, 1, 1, -503, [182], 183], [28, -504]], [0, "b0EdpobU9JP6r7A/Q/vLSN", 1], [5, 23.25, 40], [0, -4.479, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 11, [-505, -506], [0, "13CkS1ht5HCIxyph2OVCnB", 1]], [1, "bubble", 100, [-508], [[15, 1, 0, -507, [190], 191]], [0, "5bO/CGwOJFj7zOyqW4gGds", 1], [5, 192.4, 65], [45, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Avatar", 12, [[26, 1, 0, false, -509, [194], 195], [31, -510]], [0, "51W5SO1AdB47qzPMcdBpsW", 1], [4, 4293322470], [5, 86, 86]], [10, "lbWin", false, 12, [[36, 15, false, 1, 1, -511, 198], [6, -512, [199]]], [0, "78QJATjvtAMKZkw1q7VD1P", 1], [5, 80.63, 37.5], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 64, [[20, "10.M", 12, false, 1, 1, -513, [201], 202], [28, -514]], [0, "88NtU4KY9MBYeQgToSxOlt", 1], [5, 23.25, 40], [0, -4.479, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 12, [-515, -516], [0, "capF1Xe8dN2YrVRKpoxIF4", 1]], [1, "bubble", 105, [-518], [[15, 1, 0, -517, [209], 210]], [0, "4c8PvyhstD/JHixc+T/Ctg", 1], [5, 192.4, 65], [47, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Avatar", 13, [[26, 1, 0, false, -519, [213], 214], [31, -520]], [0, "f1pEcZuGxOHr9RR+9tTQsl", 1], [4, 4293322470], [5, 86, 86]], [10, "lbWin", false, 13, [[36, 15, false, 1, 1, -521, 217], [6, -522, [218]]], [0, "391dxz3htImYys/KLgdOX3", 1], [5, 80.63, 37.5], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 66, [[20, "10.M", 12, false, 1, 1, -523, [220], 221], [28, -524]], [0, "13BR/y3BxBJKeX3956nXEv", 1], [5, 23.25, 40], [0, -4.479, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 13, [-525, -526], [0, "441RWnOBFBp6RRmJ0JTKGc", 1]], [1, "bubble", 110, [-528], [[15, 1, 0, -527, [228], 229]], [0, "fbgE7NjTxI3YpJcvi38Uu6", 1], [5, 192.4, 65], [-24, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Avatar", 14, [[26, 1, 0, false, -529, [232], 233], [31, -530]], [0, "bfYldsiVdLmaaJ3leJw6FO", 1], [4, 4293322470], [5, 86, 86]], [10, "lbWin", false, 14, [[106, 15, false, 1, 1, -531, [236], 237], [6, -532, [238]]], [0, "71YaKsTJ9An4YLCAo0kacJ", 1], [5, 80.63, 40], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 68, [[20, "10.M", 12, false, 1, 1, -533, [240], 241], [28, -534]], [0, "cdIMDcWklMFYAYTSrpzNgU", 1], [5, 23.25, 40], [0, -4.479, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 14, [-535, -536], [0, "49Xe8///xLprRosfrIDdTn", 1]], [1, "bubble", 115, [-538], [[15, 1, 0, -537, [248], 249]], [0, "797WkI5V9Kp5oeoQuSF5mj", 1], [5, 192.4, 65], [-49, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "Avatar", 15, [[26, 1, 0, false, -539, [252], 253], [31, -540]], [0, "5cEI3uG+xHPY40emDnkdKK", 1], [4, 4293322470], [5, 86, 86]], [10, "lbWin", false, 15, [[36, 15, false, 1, 1, -541, 256], [6, -542, [257]]], [0, "d2EG7AVb5CJ7CMXSn17Nuj", 1], [5, 80.63, 37.5], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 70, [[20, "10.M", 12, false, 1, 1, -543, [259], 260], [28, -544]], [0, "7aPWR/fF1IgaT/as0tQTxt", 1], [5, 23.25, 40], [0, -4.479, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 15, [-545, -546], [0, "f6/NV4LsxM8Io1pvpAjRT9", 1]], [1, "bubble", 120, [-548], [[15, 1, 0, -547, [267], 268]], [0, "b87iO9Ae9DALvNAafffPMG", 1], [5, 192.4, 65], [-50, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [68, "Avatar", 16, [[26, 1, 0, false, -549, [271], 272], [31, -550]], [0, "bdzP+AxPdMvrXmDLZXHdMM", 1], [4, 4293322470], [5, 110, 110], [-16.356, -20.661, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "lbWin", false, 16, [[36, 15, false, 1, 1, -551, 275], [6, -552, [276]]], [0, "96RWlOHe9H05CdeZ7q7kco", 1], [5, 62.81, 37.5], [0, 58.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 72, [[22, "0", 15, 50, false, 1, 1, 2, -553, [278], 279], [28, -554]], [0, "60P8BAH11NN4zkqDHOaKpf", 1], [5, 150, 31], [0, -3.096, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "chat", false, 16, [-555, -556], [0, "85FEv1QmpFp4ppCAbUvMkZ", 1]], [1, "bubble", 125, [-558], [[15, 1, 0, -557, [286], 287]], [0, "aafbGCxaZIObcCB/V2F+sU", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbWin", 37, [[-559, [42, -561, -560]], 1, 4], [0, "5bLASJXWBJyp8seSmhZW5C", 1], [5, 0, 40], [0, 43, 0, 0, 0, 0, 1, 1, 1, 1]], [107, 15, false, 1, 1, 127, [303]], [8, "BG+table", 1, [[3, -562, [2], 3]], [0, "04ZdHCz9NCoZ6vkLIxzP/m", 1], [5, 1560, 1170]], [51, "bgSession", 2, [[58, false, 1, 0, -563, [4], 5]], [0, "64WGPwTM5HcYj9uIQwIXba", 1], [5, 900, 67], [-2, 336.034, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, 180]], [12, 0, 38, [6]], [12, 0, 39, [7]], [12, 0, 40, [8]], [12, 0, 41, [9]], [12, 0, 42, [10]], [12, 0, 43, [11]], [12, 0, 44, [12]], [12, 0, 45, [13]], [12, 0, 46, [14]], [12, 0, 47, [15]], [12, 0, 48, [16]], [12, 0, 49, [17]], [12, 0, 50, [18]], [12, 0, 51, [19]], [12, 0, 24, [20]], [21, "nodeTable", false, 2, [-564], [0, "9eTV9GEldOYILbVo3mzPxT", 1]], [10, "table", false, 146, [[3, -565, [26], 27]], [0, "c520LDXVtMcJB31f99v6R6", 1], [5, 1267, 720], [4, -67, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "dealer", 2, [-566], [0, "70YxiqTlZKTq7gbDyT8L+5", 1], [5, 154, 162], [0.612, 308.991, 0, 0, 0, 0, 1, 0.824, 0.824, 0.824]], [2, "dearlerG", 148, [[17, "default", "animation", 0, "animation", -567, [28], 29]], [0, "a1ufPh519DhKEEf0aeYQga", 1], [5, 221.48, 317], [4.956, -175.312, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [79, "lbSessionID", 2, [-568], [0, "547VJucVpKoqQWMS8B6Zms", 1], [5, 0, 25.2], [0, 0, 0.5], [-374.095, 207.036, 0, 0, 0, 0, 1, 1, 1, 1]], [108, 25, 20, false, 2, 1, 1, 150, [30]], [11, "box", 2, [-569], [0, "b7tif7s6REq7iVg8juxlXL", 1], [5, 90, 89], [188.761, 141.237, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "default", 0, false, 152, [31]], [51, "cardBack", 76, [[46, -570, [32]]], [0, "917dfGybBNg7bugvJ1uwjE", 1], [5, 48, 62], [0, 0, 0, 0, 0, 0.3826834323650898, 0.9238795325112867, 1, 1, 1], [1, 0, 0, 45]], [80, "fold", 77, [-571], [0, "f4vEei5FlCrJBMmRYDImn+", 1], [5, 51.69, 63.12]], [59, "default", 0, false, 155, [33]], [52, "nodeCardRong", false, 2, [0, "4dkPFPqgtIYZtAB7E0vwxM", 1], [5, 48, 62], [-74.273, 102.958, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [52, "nodeCardHo", false, 2, [0, "b8nhSmm15GLaEkU/zS8Srd", 1], [5, 48, 62], [125.727, 102.958, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [40, "disable", false, 18, [[3, -572, [34], 35]], [0, "5dZgGPI95EI4hsv6iRtN5F", 1], [5, 250, 257]], [2, "lbTile", 18, [[20, "1:2", 30, false, 1, 1, -573, [38], 39]], [0, "e2Wl25HqhB/KwQdJIcJEgU", 1], [5, 44.25, 40], [-140, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbDragonBet", 81, [-574], [0, "23qhLGfD5C+o82N9ecjRtF", 1], [5, 240, 40], [1.334, -4.713, 0, 0, 0, 0, 1, 1, 1, 1]], [61, 20, 50, false, 1, 2, 161, [41]], [40, "disable", false, 20, [[3, -575, [50], 51]], [0, "910v+29NtP1aiJGt2Y8ju8", 1], [5, 250, 257]], [2, "lbTile", 20, [[20, "1:2", 30, false, 1, 1, -576, [54], 55]], [0, "14hv3j9rRMKrygUIWMwtof", 1], [5, 44.25, 40], [140, -25, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbTigerBet", 85, [-577], [0, "ab51TFF1hHN5ODESdf5Nls", 1], [5, 230, 40], [0.808, -4.567, 0, 0, 0, 0, 1, 1, 1, 1]], [61, 20, 50, false, 1, 2, 165, [57]], [40, "disable", false, 22, [[3, -578, [66], 67]], [0, "7cFyr9mqBCpK4YYuDF2pRv", 1], [5, 164, 193]], [2, "lbTile", 22, [[20, "1:13", 30, false, 1, 1, -579, [70], 71]], [0, "eeq7W79fZEGYtW2xlCGgL8", 1], [5, 61.5, 40], [-3.793, 115.256, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbTideBet", 22, [-580], [0, "17jWaM0JRFLYe6bIm+fPon", 1], [5, 190, 40], [1.891, -62.77, 0, 0, 0, 0, 1, 1, 1, 1]], [43, 20, 50, false, 1, 1, 2, 169, [73]], [101, 8], [23, "nodeChipsRong", 2, [0, "0aRrSoJZ1OmpDavIINh2EG", 1], [5, 150, 130], [-199.273, -34.058, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "nodeChipsHo", 2, [0, "73b+xF/39ML5YAtJtsI2T/", 1], [5, 150, 130], [261.945, -34.058, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "nodeChipsHoa", 2, [0, "3dqxPkdqNGhK8HUUa6wStK", 1], [5, 140, 100], [29.314, -67.758, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "nodeChipsStartMove", 2, [0, "be/NR1/9hP9pkgT9IdkwKn", 1], [5, 50, 50], [25.727, 61.349, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "timeGroup", 2, [25], [0, "862JMMxXVNkJHZPvOx6opc", 1], [67.626, 75.183, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbTime", 89, [-581], [0, "baYSHJcvRLz57ZYVtwGObG", 1], [5, 0, 50], [0.548, 10.241, 0, 0, 0, 0, 1, 1, 1, 1]], [49, 30, 50, false, 1, 1, 177, [80]], [56, "nodeTimePrepare", 25, [-582], [0, "35747ALGtAHrm6VqAcPHZz", 1]], [11, "lbTimePrepare", 179, [-583], [0, "bfmrOLQhlC1ILAwziKiI2E", 1], [5, 0, 50], [0.548, 10.241, 0, 0, 0, 0, 1, 1, 1, 1]], [49, 30, 50, false, 1, 1, 180, [82]], [2, "New Node", 25, [[3, -584, [83], 84]], [0, "acfu3wPPtChKew6U5nNwtx", 1], [5, 152, 152], [0, 0, 0, 0, 0, 0, 1, 0.55, 0.55, 1]], [10, "bgBets", false, 2, [[15, 1, 0, -585, [87], 88]], [0, "6107wCCIJMvZ1IBuFBfTRt", 1], [5, 1110, 120], [23.727, -280.783, 0, 0, 0, 0, 1, 0.8, 0.6, 1]], [2, "exportbaucua10", 2, [[9, 0, -586, [89], 90]], [0, "82yGg3BGtP752P6O/PY4sq", 1], [5, 1560, 113], [-5, -299, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "exportbaucua7", 2, [[9, 0, -587, [91], 92]], [0, "24u6gpJxJFHIAjr0vuvFuq", 1], [5, 658.1, 99.6], [20.5, -305.7, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "1k", 26, [[3, -588, [93], 94]], [0, "07RoaIvt5N9qZwcLP+BbIF", 1], [5, 95, 100]], [8, "5k", 27, [[3, -589, [97], 98]], [0, "19asyvlmVA0Y8r87rwILM5", 1], [5, 142, 147]], [8, "10k", 28, [[3, -590, [101], 102]], [0, "2d1weAwxxBYpDf8m/r17ti", 1], [5, 95, 100]], [8, "50k", 29, [[3, -591, [105], 106]], [0, "36axtHdn1EG4TTngN/URMH", 1], [5, 95, 100]], [8, "100k", 30, [[3, -592, [109], 110]], [0, "79gKlKSKhL9KDTcVBWk+5O", 1], [5, 95, 100]], [8, "500k", 31, [[3, -593, [113], 114]], [0, "8013wU8MBPpoP1bDZ029kN", 1], [5, 95, 100]], [8, "1M", 32, [[3, -594, [117], 118]], [0, "e7Rs9QoSJBGLjIi+bYXu6s", 1], [5, 95, 100]], [39, "layoutSetting", 1, [90], [0, "9aaGYnilNJ+ovNK4z6lzYX", 1], [-689.994, 135.674, 0, 0, 0, 0, 1, 1, 1, 1]], [46, 91, [140]], [46, 59, [141]], [6, 5, [142, 143]], [39, "nodeTotalUser", 2, [23], [0, "87U6GFSUhK/ZVags4gJBfs", 1], [264.091, 239.812, 0, 0, 0, 0, 1, 0.747, 0.747, 0.747]], [11, "lbTotalUser", 23, [-595], [0, "43Re9jdnlG3qynjq+TJETH", 1], [5, 46.5, 20], [-0.7, -24.349, 0, 0, 0, 0, 1, 1, 1, 1]], [109, "0", 20, false, 1, 1, 2, 198, [150]], [81, "lbTotalUserWin", false, 23, [-596], [0, "cb6F7iBa9JQbY6O+kkzCvs", 1], [5, 57.5, 60], [0, -68.824, 0, 0, 0, 0, 1, 1, 1, 1]], [49, 20, 30, false, 1, 1, 200, [151]], [2, "ava_sheld", 10, [[17, "default", "cham", 0, "cham", -597, [158], 159]], [0, "eaVCndH0hJz60ZqUkSfoW/", 1], [5, 295, 294], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [24, "lbSID", false, 61, [-598], [0, "e5As7jk4hIraFM4TLw0MDG", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "[TQ]", 16, false, 1, 1, 203], [11, "lbName", 61, [-599], [0, "45nZ5woWBIlIqHn33tyu36", 1], [5, 89.06, 40], [0, 15.092, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "8888888..", 10, false, false, 1, 1, 205, [162]], [8, "emotion", 95, [[35, "1-waaaht", 0, false, "1-waaaht", -600, [167], 168]], [0, "ff4haBGyVH+KKF64iCgkbt", 1], [5, 123, 110]], [14, "lbChat", 96, [[22, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -601, [169], 170]], [0, "1asq9WLBdPAbqEf+aIfp55", 1], [4, 4278190080], [5, 172.4, 55]], [32, 10, 204, 206], [2, "ava_sheld", 11, [[17, "default", "cham", 0, "cham", -602, [177], 178]], [0, "88uJmcC4JHnr3AykIri7/g", 1], [5, 295, 294], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [24, "lbSID", false, 63, [-603], [0, "c3zStZegRFapr1o0wZD7ox", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "[TQ]", 16, false, 1, 1, 211], [11, "lbName", 63, [-604], [0, "a8HLnjh7ZAyq9asG4xWRfL", 1], [5, 89.06, 40], [0, 15.092, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "8888888..", 10, false, false, 1, 1, 213, [181]], [8, "emotion", 100, [[35, "1-waaaht", 0, false, "1-waaaht", -605, [186], 187]], [0, "0eDe3zR4xEg5CD6RXzQZ59", 1], [5, 123, 110]], [14, "lbChat", 101, [[22, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -606, [188], 189]], [0, "61O+y4KedOPoQtp4kpplsy", 1], [4, 4278190080], [5, 172.4, 55]], [32, 11, 212, 214], [2, "ava_sheld", 12, [[17, "default", "cham", 0, "cham", -607, [196], 197]], [0, "abK7QlEJRKToZIU7PnFl+U", 1], [5, 295, 294], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [24, "lbSID", false, 65, [-608], [0, "00lDmr0jRMQLB2z+Y6HlN0", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "[TQ]", 16, false, 1, 1, 219], [11, "lbName", 65, [-609], [0, "35hl7algFGA6P7Ecyo0G/D", 1], [5, 89.06, 40], [0, 15.092, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "8888888..", 10, false, false, 1, 1, 221, [200]], [8, "emotion", 105, [[35, "1-waaaht", 0, false, "1-waaaht", -610, [205], 206]], [0, "8fUe+IhjRISZgTfOzQ0tY6", 1], [5, 123, 110]], [14, "lbChat", 106, [[22, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -611, [207], 208]], [0, "08FCxY2zBB55yrJuXOiPhw", 1], [4, 4278190080], [5, 172.4, 55]], [32, 12, 220, 222], [2, "ava_sheld", 13, [[17, "default", "cham", 0, "cham", -612, [215], 216]], [0, "b9zwKfru5EwZNDc8VVSlHG", 1], [5, 295, 294], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [24, "lbSID", false, 67, [-613], [0, "47d0zer9pEc7RYCapZaOZJ", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "[TQ]", 16, false, 1, 1, 227], [11, "lbName", 67, [-614], [0, "7f6RjAPehLrIyieVvTAShp", 1], [5, 89.06, 40], [0, 15.092, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "8888888..", 10, false, false, 1, 1, 229, [219]], [8, "emotion", 110, [[60, "default", "1-waaaht", 0, false, "1-waaaht", -615, [224], 225]], [0, "b8ssUDXnhEbLUlgTqzndTD", 1], [5, 123, 110]], [14, "lbChat", 111, [[22, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -616, [226], 227]], [0, "18w1FsU9RHu7+0rNmBbZ8q", 1], [4, 4278190080], [5, 172.4, 55]], [32, 13, 228, 230], [2, "ava_sheld", 14, [[17, "default", "cham", 0, "cham", -617, [234], 235]], [0, "cb9jdNusNHtKGHLPDHrGja", 1], [5, 295, 294], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [24, "lbSID", false, 69, [-618], [0, "19OyvOLtdOdb22nlVtvSGW", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "[TQ]", 16, false, 1, 1, 235], [11, "lbName", 69, [-619], [0, "dfMhm+BDZD7rNbGsf+otHr", 1], [5, 89.06, 40], [0, 15.092, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "8888888..", 10, false, false, 1, 1, 237, [239]], [8, "emotion", 115, [[35, "1-waaaht", 0, false, "1-waaaht", -620, [244], 245]], [0, "efXNyQvDBGBIXg4oFT2sKM", 1], [5, 123, 110]], [14, "lbChat", 116, [[22, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -621, [246], 247]], [0, "84PJ6DsyVF8KAi+jNAI2x2", 1], [4, 4278190080], [5, 172.4, 55]], [32, 14, 236, 238], [2, "ava_sheld", 15, [[17, "default", "cham", 0, "cham", -622, [254], 255]], [0, "aeNiIOX31DdpekuSyb4tPE", 1], [5, 295, 294], [0, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [24, "lbSID", false, 71, [-623], [0, "f3Nhk+JxdHKJJLJYtPUgpk", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "[TQ]", 16, false, 1, 1, 243], [11, "lbName", 71, [-624], [0, "81VxXYBbZNjp7r1L/GiUYh", 1], [5, 89.06, 40], [0, 15.092, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "8888888..", 10, false, false, 1, 1, 245, [258]], [8, "emotion", 120, [[35, "1-waaaht", 0, false, "1-waaaht", -625, [263], 264]], [0, "d8gYLiDqNI9ZYPVeiIWUCJ", 1], [5, 123, 110]], [14, "lbChat", 121, [[22, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -626, [265], 266]], [0, "24ew2bnxpATIq3PKyagaP/", 1], [4, 4278190080], [5, 172.4, 55]], [32, 15, 244, 246], [2, "ava_sheld", 16, [[17, "default", "cham", 0, "cham", -627, [273], 274]], [0, "56eY2vr/ZPPoEop6rQghaR", 1], [5, 295, 294], [-16.356, -20.661, 0, 0, 0, 0, 1, 0.5, 0.5, 1]], [24, "lbSID", false, 73, [-628], [0, "c62I6leiZJvbUDeZB8L2js", 1], [4, 4279026733], [5, 57.75, 30], [-70, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "[TQ]", 30, false, 1, 1, 251], [11, "lbName", 73, [-629], [0, "a1JZnjG8ZKUrwigcmJHV4S", 1], [5, 89.06, 40], [0, 15.092, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "8888888..", 10, false, false, 1, 1, 253, [277]], [8, "emotion", 125, [[35, "1-waaaht", 0, false, "1-waaaht", -630, [282], 283]], [0, "73DqCvZVdDh7QAb6jJV3pa", 1], [5, 123, 110]], [14, "lbChat", 126, [[22, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -631, [284], 285]], [0, "e1i7gGp+NBZ5zmglAnpH7q", 1], [4, 4278190080], [5, 172.4, 55]], [32, 16, 252, 254], [5, "posChip1k", 3, [0, "18kbnlbl5Cy46Nn4AoS/lY", 1], [-288.719, -318.825, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posChip5k", 3, [0, "33dUztryVEu5AjsaFHw0dB", 1], [-196.568, -321.533, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posChip10k", 3, [0, "c8HUFwu9pHnpMOQyPlqSJT", 1], [-99.072, -320.472, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posChip50k", 3, [0, "44qgw6FxxGFavakuk92ikr", 1], [-5.838, -323.236, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posChip100k", 3, [0, "03qXeL8OlJX5phb4X10qZ5", 1], [91.624, -318.62, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posChip500k", 3, [0, "73vRPJTJdDfLjGZ4L+3RyK", 1], [187.214, -318.446, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posChip1m", 3, [0, "96UdH9W/RAQbQu/rlBQjI9", 1], [283.377, -318.319, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posUser1", 3, [0, "faBCOI/ClIt4PP9WuhxKvy", 1], [-538.815, -247.426, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posUser2", 3, [0, "4ahY3UGaNP14EvU73BRs3W", 1], [700, -301.037, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posUser3", 3, [0, "8cgYHHdoNAB7eN9vcPcncO", 1], [700, -190.945, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posUser4", 3, [0, "f3T1KiQ2hAFLsvPfXyFhq2", 1], [700, -81.313, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posUser5", 3, [0, "450EAgdstNt6s4Mj4aCw0z", 1], [700, 28.145, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posUser6", 3, [0, "0b5eTCdOpGyqTdng5vtm+7", 1], [700, 138.856, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posUser7", 3, [0, "e1KJ2CGXRAM7JPTtFbrUUl", 1], [700, 248.859, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "posGroupUser", 3, [0, "75KQPvMJJEJYaCqgELZYUz", 1], [258.961, 202.539, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pos<PERSON><PERSON><PERSON>", 3, [0, "d2N5ge101Fq5GVYR9wbrWL", 1], [0, 150, 0, 0, 0, 0, 1, 1, 1, 1]], [102, true, 2, [290, 291, 292, 293, 294, 295]], [69, "chat<PERSON>arent", 1, [0, "4b9sTvzcJHX5AUkUZ9DfX/", 1]], [2, "bg_tex1", 36, [[9, 0, -632, [296], 297]], [0, "43xjHbjfRH2JXYrfAs5W3t", 1], [5, 444, 50], [0, 3.598, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lbMessage", 36, [-633], [0, "a49U/lQRJGgbXzhD0ymryd", 1], [5, 600, 50], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [110, "Dat cuoc thanh cong ", 20, 50, false, false, 2, 1, 3, 277, [298]], [8, "win", 37, [[60, "default", "animation", 0, false, "animation", -634, [301], 302]], [0, "e8mu8LoRlHAJQQ07RbzRmF", 1], [5, 1280, 613]], [18, "betSound", 6, [-635], [0, "ffbiSXYwBCl7YyT39VqF89", 1]], [62, 0.7, 280], [18, "bgSound", 6, [-636], [0, "7fBYwn7ytENaiqaPCjuxdo", 1]], [112, 0.5, true, 282], [18, "openCardSound", 6, [-637], [0, "09Rw6BPUFO7ooGFd4hcTQq", 1]], [33, 284], [18, "cardSlideSound", 6, [-638], [0, "48mbej9EFBb4Wbbb5o4jb4", 1]], [33, 286], [18, "cardBurnMoveToBoxSound", 6, [-639], [0, "4dwOOt/q1FyqyresdOgQfh", 1]], [62, 0.7, 288], [18, "cardSlideBurn", 6, [-640], [0, "a9y+iBBt9A94HBUD5ljLjC", 1]], [33, 290], [18, "cardSlideRong", 6, [-641], [0, "34RJyR9ZVOOLJWfrkUa1Gf", 1]], [33, 292], [18, "cardSlideHo", 6, [-642], [0, "56JI34YLBP+Z/GDCW+i6DH", 1]], [33, 294], [18, "selectChip", 6, [-643], [0, "a5CLXQTKFJAZSmCeWkpN4a", 1]], [33, 296], [18, "getCoin", 6, [-644], [0, "a58gV2HzVGqIz3v5E/062p", 1]], [33, 298]], 0, [0, 14, 1, 0, 18, 275, 0, 19, 8, 0, 20, 76, 0, 21, 77, 0, 22, 158, 0, 23, 157, 0, 24, 84, 0, 25, 88, 0, 26, 80, 0, 27, 201, 0, 28, 199, 0, 29, 181, 0, 30, 178, 0, 31, 151, 0, 0, 1, 0, 32, 128, 0, 33, 37, 0, 0, 1, 0, 34, 273, 0, -1, 265, 0, -2, 266, 0, -3, 267, 0, -4, 268, 0, -5, 269, 0, -6, 270, 0, -7, 271, 0, 35, 272, 0, -1, 258, 0, -2, 259, 0, -3, 260, 0, -4, 261, 0, -5, 262, 0, -6, 263, 0, -7, 264, 0, 36, 175, 0, 37, 173, 0, 38, 174, 0, 39, 172, 0, 0, 1, 0, -1, 257, 0, -2, 249, 0, -3, 241, 0, -4, 233, 0, -5, 225, 0, -6, 217, 0, -7, 209, 0, 0, 1, 0, 14, 1, 0, 6, 1, 0, -2, 75, 0, -3, 129, 0, -4, 2, 0, -5, 193, 0, -6, 275, 0, -7, 74, 0, -8, 37, 0, -9, 6, 0, 40, 33, 0, 41, 34, 0, 42, 166, 0, 43, 170, 0, 44, 162, 0, 45, 7, 0, 0, 2, 0, 46, 274, 0, 0, 2, 0, -3, 274, 0, 47, 156, 0, 48, 153, 0, 0, 2, 0, 15, 19, 0, 16, 21, 0, 17, 17, 0, 0, 2, 0, -1, 130, 0, -2, 4, 0, -3, 146, 0, -4, 148, 0, -5, 150, 0, -6, 152, 0, -7, 76, 0, -8, 77, 0, -9, 157, 0, -10, 158, 0, -11, 8, 0, -12, 172, 0, -13, 173, 0, -14, 174, 0, -15, 175, 0, -16, 176, 0, -17, 183, 0, -18, 184, 0, -19, 185, 0, 6, 2, 0, -21, 7, 0, -22, 33, 0, -23, 34, 0, -24, 35, 0, -25, 197, 0, -26, 9, 0, -27, 3, 0, -1, 258, 0, -2, 259, 0, -3, 260, 0, -4, 261, 0, -5, 262, 0, -6, 263, 0, -7, 264, 0, 6, 3, 0, 6, 3, 0, 6, 3, 0, -11, 265, 0, -12, 266, 0, -13, 267, 0, -14, 268, 0, -15, 269, 0, -16, 270, 0, -17, 271, 0, -18, 272, 0, -19, 273, 0, 0, 4, 0, -1, 131, 0, -2, 132, 0, -3, 133, 0, -4, 134, 0, -5, 135, 0, -6, 136, 0, -7, 137, 0, -8, 138, 0, -9, 139, 0, -10, 140, 0, -11, 141, 0, -12, 142, 0, -13, 143, 0, -14, 144, 0, -15, 145, 0, 0, 4, 0, -1, 38, 0, -2, 39, 0, -3, 40, 0, -4, 41, 0, -5, 42, 0, -6, 43, 0, -7, 44, 0, -8, 45, 0, -9, 46, 0, -10, 47, 0, -11, 48, 0, -12, 49, 0, -13, 50, 0, -14, 51, 0, -15, 24, 0, -1, 196, 0, 49, 195, 0, 50, 194, 0, 51, 5, 0, 52, 196, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 56, 0, -2, 57, 0, -3, 58, 0, -4, 91, 0, -5, 59, 0, 53, 299, 0, 54, 297, 0, 55, 285, 0, 56, 289, 0, 57, 295, 0, 58, 293, 0, 59, 291, 0, 60, 287, 0, 61, 281, 0, 62, 283, 0, 0, 6, 0, -1, 280, 0, -2, 282, 0, -3, 284, 0, -4, 286, 0, -5, 288, 0, -6, 290, 0, -7, 292, 0, -8, 294, 0, -9, 296, 0, -10, 298, 0, 0, 7, 0, -1, 26, 0, -2, 27, 0, -3, 28, 0, -4, 29, 0, -5, 30, 0, -6, 31, 0, -7, 32, 0, -1, 171, 0, 63, 171, 0, 15, 19, 0, 16, 21, 0, 17, 17, 0, 64, 82, 0, 65, 86, 0, 66, 78, 0, 0, 8, 0, -1, 17, 0, -2, 19, 0, -3, 21, 0, 6, 8, 0, -1, 10, 0, -2, 11, 0, -3, 12, 0, -4, 13, 0, -5, 14, 0, -6, 15, 0, -7, 16, 0, 0, 10, 0, -2, 209, 0, -1, 92, 0, -2, 202, 0, -3, 93, 0, -4, 60, 0, -5, 95, 0, -1, 217, 0, 0, 11, 0, -1, 97, 0, -2, 210, 0, -3, 98, 0, -4, 62, 0, -5, 100, 0, 0, 12, 0, -2, 225, 0, -1, 102, 0, -2, 218, 0, -3, 103, 0, -4, 64, 0, -5, 105, 0, -1, 233, 0, 0, 13, 0, -1, 107, 0, -2, 226, 0, -3, 108, 0, -4, 66, 0, -5, 110, 0, -1, 241, 0, 0, 14, 0, -1, 112, 0, -2, 234, 0, -3, 113, 0, -4, 68, 0, -5, 115, 0, -1, 249, 0, 0, 15, 0, -1, 117, 0, -2, 242, 0, -3, 118, 0, -4, 70, 0, -5, 120, 0, -1, 257, 0, 0, 16, 0, -1, 122, 0, -2, 250, 0, -3, 123, 0, -4, 72, 0, -5, 125, 0, 3, 17, 0, 0, 17, 0, 0, 17, 0, -1, 18, 0, 0, 18, 0, -1, 159, 0, -2, 78, 0, -3, 160, 0, -4, 79, 0, -5, 81, 0, 3, 19, 0, 0, 19, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, -1, 163, 0, -2, 82, 0, -3, 164, 0, -4, 83, 0, -5, 85, 0, 3, 21, 0, 0, 21, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, -1, 167, 0, -2, 86, 0, -3, 168, 0, -4, 87, 0, -5, 169, 0, 0, 23, 0, 0, 23, 0, 3, 23, 0, 0, 23, 0, -1, 198, 0, -2, 200, 0, -1, 145, 0, 0, 24, 0, 3, 24, 0, 0, 24, 0, 0, 25, 0, -1, 89, 0, -2, 179, 0, -3, 182, 0, 3, 26, 0, 0, 26, 0, 0, 26, 0, -1, 186, 0, 3, 27, 0, 0, 27, 0, 0, 27, 0, -1, 187, 0, 3, 28, 0, 0, 28, 0, 0, 28, 0, -1, 188, 0, 3, 29, 0, 0, 29, 0, 0, 29, 0, -1, 189, 0, 3, 30, 0, 0, 30, 0, 0, 30, 0, -1, 190, 0, 3, 31, 0, 0, 31, 0, 0, 31, 0, -1, 191, 0, 3, 32, 0, 0, 32, 0, 0, 32, 0, -1, 192, 0, 0, 33, 0, 3, 33, 0, 0, 33, 0, 0, 34, 0, 3, 34, 0, 0, 34, 0, -1, 52, 0, -2, 53, 0, -3, 54, 0, -4, 55, 0, 0, 36, 0, -1, 276, 0, -2, 277, 0, 0, 37, 0, -1, 279, 0, -2, 127, 0, -1, 131, 0, 3, 38, 0, 0, 38, 0, -1, 132, 0, 3, 39, 0, 0, 39, 0, -1, 133, 0, 3, 40, 0, 0, 40, 0, -1, 134, 0, 3, 41, 0, 0, 41, 0, -1, 135, 0, 3, 42, 0, 0, 42, 0, -1, 136, 0, 3, 43, 0, 0, 43, 0, -1, 137, 0, 3, 44, 0, 0, 44, 0, -1, 138, 0, 3, 45, 0, 0, 45, 0, -1, 139, 0, 3, 46, 0, 0, 46, 0, -1, 140, 0, 3, 47, 0, 0, 47, 0, -1, 141, 0, 3, 48, 0, 0, 48, 0, -1, 142, 0, 3, 49, 0, 0, 49, 0, -1, 143, 0, 3, 50, 0, 0, 50, 0, -1, 144, 0, 3, 51, 0, 0, 51, 0, 0, 52, 0, 3, 52, 0, 0, 52, 0, 0, 53, 0, 3, 53, 0, 0, 53, 0, 0, 54, 0, 3, 54, 0, 0, 54, 0, 0, 55, 0, 3, 55, 0, 0, 55, 0, 0, 56, 0, 3, 56, 0, 0, 56, 0, 0, 57, 0, 3, 57, 0, 0, 57, 0, 0, 58, 0, 3, 58, 0, 0, 58, 0, -1, 195, 0, 3, 59, 0, 0, 59, 0, 0, 60, 0, -1, 61, 0, -2, 94, 0, 0, 61, 0, -1, 203, 0, -2, 205, 0, 0, 62, 0, -1, 63, 0, -2, 99, 0, 0, 63, 0, -1, 211, 0, -2, 213, 0, 0, 64, 0, -1, 65, 0, -2, 104, 0, 0, 65, 0, -1, 219, 0, -2, 221, 0, 0, 66, 0, -1, 67, 0, -2, 109, 0, 0, 67, 0, -1, 227, 0, -2, 229, 0, 0, 68, 0, -1, 69, 0, -2, 114, 0, 0, 69, 0, -1, 235, 0, -2, 237, 0, 0, 70, 0, -1, 71, 0, -2, 119, 0, 0, 71, 0, -1, 243, 0, -2, 245, 0, 0, 72, 0, -1, 73, 0, -2, 124, 0, 0, 73, 0, -1, 251, 0, -2, 253, 0, 0, 74, 0, 67, 278, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, -1, 154, 0, -1, 155, 0, 0, 78, 0, -1, 80, 0, 8, 80, 0, 0, 79, 0, 0, 81, 0, -1, 161, 0, 0, 82, 0, -1, 84, 0, 8, 84, 0, 0, 83, 0, 0, 85, 0, -1, 165, 0, 0, 86, 0, -1, 88, 0, 8, 88, 0, 0, 87, 0, 0, 89, 0, -1, 177, 0, 0, 90, 0, -1, 194, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 93, 0, 0, 93, 0, 0, 94, 0, 0, 94, 0, -1, 207, 0, -2, 96, 0, 0, 96, 0, -1, 208, 0, 0, 97, 0, 0, 97, 0, 0, 98, 0, 0, 98, 0, 0, 99, 0, 0, 99, 0, -1, 215, 0, -2, 101, 0, 0, 101, 0, -1, 216, 0, 0, 102, 0, 0, 102, 0, 0, 103, 0, 0, 103, 0, 0, 104, 0, 0, 104, 0, -1, 223, 0, -2, 106, 0, 0, 106, 0, -1, 224, 0, 0, 107, 0, 0, 107, 0, 0, 108, 0, 0, 108, 0, 0, 109, 0, 0, 109, 0, -1, 231, 0, -2, 111, 0, 0, 111, 0, -1, 232, 0, 0, 112, 0, 0, 112, 0, 0, 113, 0, 0, 113, 0, 0, 114, 0, 0, 114, 0, -1, 239, 0, -2, 116, 0, 0, 116, 0, -1, 240, 0, 0, 117, 0, 0, 117, 0, 0, 118, 0, 0, 118, 0, 0, 119, 0, 0, 119, 0, -1, 247, 0, -2, 121, 0, 0, 121, 0, -1, 248, 0, 0, 122, 0, 0, 122, 0, 0, 123, 0, 0, 123, 0, 0, 124, 0, 0, 124, 0, -1, 255, 0, -2, 126, 0, 0, 126, 0, -1, 256, 0, -1, 128, 0, 8, 128, 0, 0, 127, 0, 0, 129, 0, 0, 130, 0, -1, 147, 0, 0, 147, 0, -1, 149, 0, 0, 149, 0, -1, 151, 0, -1, 153, 0, 0, 154, 0, -1, 156, 0, 0, 159, 0, 0, 160, 0, -1, 162, 0, 0, 163, 0, 0, 164, 0, -1, 166, 0, 0, 167, 0, 0, 168, 0, -1, 170, 0, -1, 178, 0, -1, 180, 0, -1, 181, 0, 0, 182, 0, 0, 183, 0, 0, 184, 0, 0, 185, 0, 0, 186, 0, 0, 187, 0, 0, 188, 0, 0, 189, 0, 0, 190, 0, 0, 191, 0, 0, 192, 0, -1, 199, 0, -1, 201, 0, 0, 202, 0, -1, 204, 0, -1, 206, 0, 0, 207, 0, 0, 208, 0, 0, 210, 0, -1, 212, 0, -1, 214, 0, 0, 215, 0, 0, 216, 0, 0, 218, 0, -1, 220, 0, -1, 222, 0, 0, 223, 0, 0, 224, 0, 0, 226, 0, -1, 228, 0, -1, 230, 0, 0, 231, 0, 0, 232, 0, 0, 234, 0, -1, 236, 0, -1, 238, 0, 0, 239, 0, 0, 240, 0, 0, 242, 0, -1, 244, 0, -1, 246, 0, 0, 247, 0, 0, 248, 0, 0, 250, 0, -1, 252, 0, -1, 254, 0, 0, 255, 0, 0, 256, 0, 0, 276, 0, -1, 278, 0, 0, 279, 0, -1, 281, 0, -1, 283, 0, -1, 285, 0, -1, 287, 0, -1, 289, 0, -1, 291, 0, -1, 293, 0, -1, 295, 0, -1, 297, 0, -1, 299, 0, 68, 1, 5, 6, 90, 23, 6, 197, 25, 6, 176, 36, 6, 74, 90, 6, 193, 644], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 153, 156, 80, 162, 84, 166, 88, 170, 178, 181, 194, 195, 199, 201, 204, 206, 212, 214, 220, 222, 228, 230, 236, 238, 244, 246, 252, 254, 274, 128, 281, 283, 285, 287, 289, 291, 293, 295, 297, 299], [-1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 9, -1, -1, -2, -3, -1, 1, -1, 5, -1, -1, -1, -1, -1, 1, -1, 5, -1, 2, -1, -1, -1, 1, -1, 1, 10, 11, 12, 13, -1, 1, -1, 5, -1, 2, -1, -1, -1, 1, -1, 1, 10, 11, 12, 13, -1, 1, -1, 5, -1, 2, -1, -1, -1, 1, 10, 11, 12, 13, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -2, -1, 1, -1, -2, -1, 1, -1, -2, -1, 1, -1, -2, -1, 1, -1, -2, -1, 1, -1, -2, -1, 1, -1, -2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -2, -1, -2, -1, -2, -1, 1, -1, -1, -1, 1, 9, -1, -1, 1, -1, 5, 2, -1, -1, -1, 2, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 1, -1, 5, 2, -1, -1, -1, 2, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 1, -1, 5, 2, -1, -1, -1, 2, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 1, -1, 5, 2, -1, -1, -1, 2, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 1, -1, 5, -1, 2, -1, -1, -1, 2, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 1, -1, 5, 2, -1, -1, -1, 2, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 1, -1, 5, 2, -1, -1, -1, 2, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, -2, -3, -4, -5, -6, -1, 1, -1, -1, -1, -1, 5, -1, -1, 69, 70, 71, 72, 73, 74, 75, 76, -1, -2, -3, -4, -5, -6, -7, -8, -9, 5, 5, 2, 2, 2, 2, 2, 2, 2, 2, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 9, 2, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7], [0, 38, 0, 39, 0, 40, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 14, 41, 42, 43, 0, 44, 2, 45, 0, 2, 0, 2, 0, 15, 2, 46, 0, 3, 0, 0, 0, 47, 0, 15, 48, 16, 16, 49, 0, 17, 2, 50, 0, 3, 0, 0, 0, 51, 0, 17, 52, 18, 18, 53, 0, 19, 2, 54, 0, 3, 0, 0, 0, 19, 55, 20, 20, 56, 0, 57, 0, 0, 58, 0, 59, 0, 60, 0, 61, 0, 62, 0, 21, 63, 21, 0, 22, 22, 64, 0, 23, 65, 23, 0, 24, 66, 24, 0, 25, 67, 25, 0, 26, 68, 26, 0, 27, 69, 27, 0, 70, 0, 71, 0, 72, 0, 73, 0, 74, 0, 75, 0, 0, 76, 0, 77, 0, 78, 0, 0, 79, 80, 28, 81, 29, 82, 0, 83, 0, 0, 0, 84, 13, 13, 0, 4, 2, 5, 1, 6, 0, 0, 1, 0, 7, 2, 8, 0, 3, 0, 9, 10, 11, 0, 4, 2, 5, 1, 6, 0, 0, 1, 0, 7, 2, 8, 0, 3, 0, 9, 10, 11, 0, 4, 2, 5, 1, 6, 0, 0, 1, 0, 7, 2, 8, 0, 3, 0, 9, 10, 11, 0, 4, 2, 5, 1, 6, 0, 0, 1, 0, 7, 2, 8, 0, 3, 0, 9, 10, 11, 0, 4, 2, 5, 0, 1, 6, 0, 0, 1, 0, 7, 2, 8, 0, 3, 0, 9, 10, 11, 0, 4, 2, 5, 1, 6, 0, 0, 1, 0, 7, 2, 8, 0, 3, 0, 9, 10, 11, 0, 4, 2, 5, 1, 6, 0, 0, 1, 0, 7, 2, 8, 0, 3, 0, 9, 10, 11, 85, 86, 87, 88, 89, 13, 0, 90, 0, 0, 91, 2, 92, 0, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 1, 1, 1, 1, 1, 1, 1, 1, 28, 29, 1, 1, 3, 12, 3, 12, 3, 12, 3, 12, 3, 12, 3, 12, 3, 12, 13, 1, 113, 114, 115, 30, 116, 30, 31, 31, 117, 118]], [[[45, "card-rong", 0.16666666666666666, 0.2, "54", [{}, "paths", 11, [{}, "nodeCardRong", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]], [{"frame": 0.08333333333333333}, "value", 8, [0, 1.2, 1.2]], [{"frame": 0.16666666666666666}, "value", 8, [0, 1, 1]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[38, "Tiger", "\r\nTiger.png\r\nsize: 1173, 480\r\nformat: RGBA8888\r\nfilter: Linear, Linear\r\nrepeat: none\r\nTiger\r\n  rotate: false\r\n  xy: 885, 131\r\n  size: 286, 347\r\n  orig: 286, 347\r\n  offset: 0, 0\r\n  index: -1\r\nclose_eyes_l\r\n  rotate: false\r\n  xy: 885, 70\r\n  size: 58, 59\r\n  orig: 58, 59\r\n  offset: 0, 0\r\n  index: -1\r\nclose_eyes_r\r\n  rotate: true\r\n  xy: 945, 64\r\n  size: 65, 48\r\n  orig: 65, 48\r\n  offset: 0, 0\r\n  index: -1\r\nlight1\r\n  rotate: true\r\n  xy: 503, 84\r\n  size: 394, 380\r\n  orig: 394, 380\r\n  offset: 0, 0\r\n  index: -1\r\nlight2\r\n  rotate: false\r\n  xy: 2, 2\r\n  size: 499, 476\r\n  orig: 499, 476\r\n  offset: 0, 0\r\n  index: -1\r\n", ["Tiger.png"], {"skins": {"default": {"close_eyes_r": {"close_eyes_r": {"rotation": -94.71, "x": 2.17, "width": 65, "y": -8.49, "height": 48}}, "light1": {"light1": {"x": 1.21, "width": 394, "y": 1.94, "height": 380}}, "close_eyes_l": {"close_eyes_l": {"rotation": -94.71, "x": 6.27, "width": 58, "y": 0.51, "height": 59}}, "Tiger": {"Tiger": {"width": 286, "type": "mesh", "hull": 144, "height": 347, "triangles": [60, 164, 59, 58, 59, 144, 144, 59, 164, 144, 164, 166, 61, 163, 60, 60, 163, 164, 144, 166, 167, 144, 167, 169, 169, 170, 49, 170, 48, 49, 61, 162, 163, 58, 144, 57, 53, 144, 169, 53, 169, 49, 54, 57, 144, 52, 50, 51, 49, 50, 53, 57, 145, 56, 57, 54, 145, 54, 144, 53, 52, 53, 50, 145, 54, 56, 54, 55, 56, 164, 196, 165, 164, 165, 166, 166, 165, 167, 165, 194, 167, 167, 168, 169, 167, 194, 168, 169, 168, 170, 168, 194, 170, 194, 195, 170, 170, 47, 48, 170, 172, 47, 170, 171, 172, 170, 195, 171, 47, 172, 46, 172, 45, 46, 172, 171, 45, 171, 44, 45, 171, 195, 44, 195, 43, 44, 154, 65, 66, 66, 67, 154, 68, 69, 67, 77, 70, 71, 67, 152, 154, 152, 67, 79, 78, 69, 70, 149, 152, 79, 73, 77, 71, 70, 77, 78, 69, 78, 67, 67, 78, 79, 149, 79, 82, 156, 64, 65, 73, 71, 72, 77, 73, 76, 82, 147, 149, 147, 148, 149, 73, 74, 76, 147, 82, 90, 82, 87, 90, 74, 75, 76, 90, 92, 147, 92, 93, 147, 79, 80, 82, 82, 80, 81, 87, 82, 83, 87, 89, 90, 83, 86, 87, 149, 148, 150, 93, 146, 147, 147, 146, 148, 86, 84, 85, 86, 83, 84, 87, 88, 89, 90, 91, 92, 93, 95, 146, 93, 94, 95, 146, 96, 173, 146, 173, 175, 173, 96, 99, 96, 146, 95, 99, 96, 98, 96, 97, 98, 175, 174, 176, 175, 173, 174, 173, 99, 174, 99, 100, 102, 100, 101, 102, 206, 174, 99, 197, 99, 102, 104, 106, 103, 197, 102, 106, 102, 103, 106, 104, 105, 106, 106, 107, 117, 112, 116, 117, 198, 106, 117, 117, 107, 108, 109, 111, 108, 111, 112, 108, 117, 108, 112, 116, 112, 115, 109, 110, 111, 112, 113, 115, 113, 114, 115, 34, 35, 203, 34, 32, 33, 34, 203, 32, 203, 211, 202, 202, 211, 201, 203, 202, 32, 31, 32, 28, 32, 202, 28, 31, 28, 30, 28, 202, 25, 25, 202, 18, 202, 201, 18, 30, 28, 29, 27, 25, 26, 27, 28, 25, 24, 25, 19, 18, 19, 25, 24, 19, 21, 24, 21, 23, 21, 22, 23, 19, 20, 21, 7, 13, 3, 4, 5, 7, 4, 7, 3, 13, 7, 11, 12, 13, 11, 9, 11, 8, 7, 8, 11, 9, 10, 11, 5, 6, 7, 18, 3, 16, 3, 18, 201, 16, 3, 13, 18, 16, 17, 15, 16, 13, 15, 13, 14, 118, 198, 117, 119, 120, 118, 118, 120, 129, 121, 122, 120, 122, 126, 120, 129, 120, 128, 128, 120, 126, 126, 127, 128, 122, 123, 126, 123, 124, 126, 124, 125, 126, 183, 184, 195, 195, 184, 43, 38, 184, 185, 38, 185, 37, 43, 184, 38, 43, 39, 42, 43, 38, 39, 41, 42, 40, 183, 210, 184, 183, 209, 210, 40, 42, 39, 185, 210, 186, 186, 210, 204, 210, 185, 184, 178, 177, 205, 174, 205, 176, 205, 174, 206, 178, 205, 207, 185, 187, 37, 185, 186, 187, 206, 99, 197, 37, 35, 36, 37, 187, 35, 205, 206, 207, 186, 204, 187, 209, 214, 213, 209, 208, 214, 35, 187, 203, 206, 197, 215, 209, 212, 210, 209, 213, 212, 210, 211, 204, 203, 187, 204, 204, 211, 203, 210, 212, 211, 197, 198, 215, 215, 199, 214, 215, 198, 199, 197, 106, 198, 214, 199, 213, 211, 212, 201, 212, 213, 201, 199, 200, 213, 213, 200, 201, 199, 198, 118, 199, 118, 129, 199, 129, 131, 129, 130, 131, 199, 131, 200, 201, 200, 3, 131, 140, 200, 200, 140, 1, 3, 1, 2, 3, 200, 1, 1, 140, 0, 140, 131, 135, 131, 132, 135, 135, 139, 140, 135, 136, 139, 139, 136, 138, 136, 137, 138, 132, 133, 135, 133, 134, 135, 140, 141, 0, 141, 142, 0, 0, 142, 143, 156, 65, 154, 156, 154, 155, 152, 153, 154, 151, 152, 149, 149, 150, 151, 61, 161, 162, 159, 160, 161, 159, 156, 158, 159, 62, 156, 161, 160, 162, 162, 160, 163, 160, 196, 163, 163, 196, 164, 159, 158, 160, 196, 160, 191, 160, 158, 191, 192, 165, 196, 156, 157, 158, 158, 157, 191, 156, 155, 157, 196, 191, 192, 165, 192, 194, 157, 155, 189, 155, 154, 189, 157, 189, 191, 189, 190, 191, 192, 191, 193, 181, 193, 190, 190, 193, 191, 154, 153, 189, 190, 189, 188, 192, 193, 194, 194, 193, 195, 181, 182, 193, 152, 151, 153, 189, 153, 188, 188, 180, 190, 188, 179, 180, 153, 151, 188, 151, 150, 188, 190, 180, 181, 150, 148, 188, 193, 183, 195, 193, 182, 183, 148, 146, 176, 148, 176, 188, 176, 146, 175, 176, 177, 188, 179, 177, 178, 179, 188, 177, 183, 182, 209, 180, 208, 181, 182, 181, 209, 181, 208, 209, 179, 207, 180, 180, 207, 208, 179, 178, 207, 177, 176, 205, 206, 215, 207, 207, 214, 208, 207, 215, 214, 63, 64, 62, 64, 156, 62, 61, 62, 161, 62, 159, 161], "uvs": [0.62378, 0.02359, 0.64429, 0.04331, 0.6861, 0.05122, 0.7279, 0.07155, 0.74846, 0.05122, 0.79986, 0.03371, 0.82317, 0.03258, 0.82385, 0.0467, 0.88348, 0.03935, 0.90198, 0.03258, 0.91774, 0.03427, 0.92528, 0.0755, 0.9186, 0.13285, 0.90119, 0.15987, 0.91348, 0.16747, 0.89504, 0.18056, 0.87814, 0.20336, 0.89695, 0.20971, 0.87876, 0.22988, 0.90824, 0.23453, 0.94275, 0.22316, 0.94589, 0.23919, 0.96973, 0.22419, 0.98165, 0.24332, 0.97224, 0.29038, 0.94698, 0.3335, 0.96181, 0.33531, 0.96731, 0.34301, 0.94368, 0.35886, 0.98159, 0.35614, 0.98049, 0.37969, 0.95632, 0.40913, 0.91675, 0.4345, 0.92967, 0.4442, 0.89822, 0.47108, 0.85589, 0.49317, 0.86172, 0.50149, 0.84735, 0.51557, 0.86366, 0.5399, 0.88929, 0.56326, 0.91866, 0.56593, 0.91769, 0.57929, 0.90406, 0.58357, 0.86903, 0.58571, 0.88395, 0.59961, 0.90958, 0.63678, 0.91574, 0.66191, 0.90893, 0.66592, 0.91509, 0.69266, 0.92256, 0.70255, 0.94721, 0.69319, 0.96635, 0.66913, 0.97576, 0.67073, 0.97837, 0.76126, 0.98219, 0.82165, 1, 0.82566, 1, 0.8437, 0.97627, 0.90195, 0.94618, 0.96097, 0.91426, 0.97673, 0.7244, 0.9748, 0.70488, 0.96381, 0.57205, 0.96381, 0.55967, 0.97912, 0.48968, 1, 0.4104, 1, 0.31039, 1, 0.25271, 0.98495, 0.23206, 0.99023, 0.20144, 0.98025, 0.15943, 0.98319, 0.11029, 0.98788, 0.0462, 0.97849, 0.01487, 0.93741, 0, 0.90219, 0, 0.87134, 0.02042, 0.87876, 0.05387, 0.85119, 0.09633, 0.81089, 0.13686, 0.75044, 0.12335, 0.70644, 0.13171, 0.69902, 0.14908, 0.71174, 0.14874, 0.6224, 0.15495, 0.55627, 0.17084, 0.55691, 0.19138, 0.58502, 0.22278, 0.59812, 0.22084, 0.58758, 0.23441, 0.58406, 0.2627, 0.5978, 0.26929, 0.58885, 0.28015, 0.59492, 0.2879, 0.58885, 0.28518, 0.58119, 0.28945, 0.56905, 0.28867, 0.56074, 0.28131, 0.55914, 0.27976, 0.55084, 0.29517, 0.53905, 0.28069, 0.53905, 0.26122, 0.53411, 0.26322, 0.52218, 0.24674, 0.51724, 0.21928, 0.49667, 0.21579, 0.48514, 0.22927, 0.47733, 0.2098, 0.46169, 0.20829, 0.45304, 0.15014, 0.43643, 0.13457, 0.40538, 0.1726, 0.39449, 0.19891, 0.37163, 0.17625, 0.34692, 0.18429, 0.33487, 0.19964, 0.34632, 0.21865, 0.34572, 0.24644, 0.33126, 0.24644, 0.3186, 0.22596, 0.3168, 0.22889, 0.29992, 0.20403, 0.30053, 0.15943, 0.2722, 0.12726, 0.21073, 0.14627, 0.14204, 0.15943, 0.13119, 0.17917, 0.13843, 0.20476, 0.12336, 0.2691, 0.11793, 0.32321, 0.12637, 0.34131, 0.1084, 0.40488, 0.07436, 0.44814, 0.03925, 0.47894, 0.00954, 0.49074, 0.00846, 0.51105, 0.03007, 0.51433, 0.01657, 0.52744, 0, 0.54317, 0, 0.56414, 0.01765, 0.59494, 0.03061, 0.59363, 0.02089, 0.60805, 0, 0.62378, 0, 0.95074, 0.9369, 0.98767, 0.86361, 0.33747, 0.57306, 0.34591, 0.62551, 0.36462, 0.62551, 0.37585, 0.66252, 0.39082, 0.6579, 0.40766, 0.67949, 0.43769, 0.72234, 0.45232, 0.72158, 0.48615, 0.76078, 0.51999, 0.78037, 0.54102, 0.80299, 0.55748, 0.77962, 0.59864, 0.81128, 0.63979, 0.85047, 0.65294, 0.84967, 0.67047, 0.88089, 0.688, 0.87903, 0.70496, 0.86644, 0.75811, 0.82777, 0.76885, 0.78582, 0.78185, 0.79142, 0.81634, 0.76392, 0.82878, 0.73876, 0.84179, 0.74342, 0.87175, 0.70055, 0.8792, 0.65166, 0.89552, 0.6586, 0.3341, 0.54436, 0.36145, 0.5406, 0.35453, 0.55207, 0.39984, 0.55254, 0.45704, 0.5474, 0.50406, 0.53293, 0.53577, 0.55767, 0.57486, 0.57914, 0.63659, 0.59222, 0.68474, 0.60249, 0.73402, 0.58195, 0.77709, 0.55225, 0.80073, 0.52697, 0.80009, 0.50221, 0.82949, 0.49537, 0.46304, 0.61273, 0.51517, 0.7273, 0.57773, 0.65999, 0.63159, 0.77313, 0.6872, 0.72301, 0.70284, 0.65283, 0.7932, 0.72444, 0.82621, 0.61989, 0.69936, 0.83472, 0.49585, 0.4754, 0.51033, 0.424, 0.56601, 0.3983, 0.64063, 0.37168, 0.7275, 0.37443, 0.79431, 0.39555, 0.80768, 0.4396, 0.79431, 0.48274, 0.50142, 0.51028, 0.49894, 0.4947, 0.55376, 0.50202, 0.62281, 0.49927, 0.67849, 0.48458, 0.7442, 0.48183, 0.7687, 0.46622, 0.68963, 0.46439, 0.66402, 0.46071, 0.62058, 0.47724, 0.54931, 0.48091], "vertices": [3, 3, 177.13, -27.76, 0.83956, 7, -7.24, 64.3, 0.14664, 8, -19.1, 63.1, 0.0138, 3, 3, 169.83, -33.05, 0.73005, 7, -7.93, 55.31, 0.23432, 8, -19.39, 54.09, 0.03563, 3, 3, 166.11, -44.74, 0.52933, 7, -1.41, 44.92, 0.36938, 8, -12.43, 43.99, 0.10129, 3, 3, 158.1, -56.08, 0.25262, 7, 2.05, 31.47, 0.43045, 8, -8.39, 30.7, 0.31694, 3, 3, 164.65, -62.52, 0.11666, 7, 11.2, 32.31, 0.26654, 8, 0.71, 31.93, 0.6168, 3, 3, 169.5, -77.67, 0.02517, 7, 25.89, 26.21, 0.04919, 8, 15.66, 26.48, 0.92564, 3, 3, 169.34, -84.34, 0.01621, 7, 30.88, 21.77, 0.0245, 8, 20.83, 22.26, 0.95929, 3, 3, 164.44, -84.13, 0.01212, 7, 27.55, 18.17, 0.01714, 8, 17.67, 18.52, 0.97073, 1, 8, 31.96, 8.88, 1, 1, 8, 37.45, 7.03, 1, 1, 8, 40.38, 3.55, 1, 3, 7, 41, -9.41, 0.00796, 8, 32.3, -8.45, 0.99179, 12, 66.95, 43.65, 0.00025, 3, 7, 25.57, -22.13, 0.21935, 8, 17.44, -21.83, 0.75815, 12, 49.25, 34.35, 0.02251, 3, 7, 15.42, -25.24, 0.48347, 8, 7.43, -25.37, 0.43936, 12, 38.68, 33.39, 0.07717, 3, 7, 16.04, -29.59, 0.51708, 8, 8.24, -29.69, 0.39663, 12, 38.4, 29.01, 0.0863, 4, 3, 116.48, -100.61, 0.00184, 7, 9.1, -29.07, 0.58129, 8, 1.29, -29.48, 0.28631, 12, 31.71, 30.94, 0.13057, 4, 3, 108.99, -95.14, 0.01586, 7, 0.09, -31.25, 0.61337, 8, -7.62, -32.04, 0.08276, 12, 22.44, 30.65, 0.28802, 4, 3, 106.35, -100.33, 0.01846, 7, 2.34, -36.61, 0.56917, 8, -5.15, -37.31, 0.04602, 12, 23.54, 24.94, 0.36635, 4, 3, 99.8, -94.57, 0.02776, 7, -6.29, -37.88, 0.44088, 8, -13.71, -38.95, 0.01668, 12, 14.84, 25.47, 0.51468, 4, 3, 97.5, -102.84, 0.00963, 7, -1.47, -44.99, 0.17883, 8, -8.58, -45.84, 0.0017, 12, 18.1, 17.53, 0.80984, 3, 3, 100.62, -113, 0.00042, 7, 8.3, -49.17, 0.06813, 12, 26.81, 11.43, 0.93145, 3, 3, 95.01, -113.43, 0.00015, 7, 5, -53.74, 0.04051, 12, 22.64, 7.63, 0.95934, 3, 3, 99.63, -120.66, 0, 7, 13.5, -54.88, 0.00243, 12, 30.73, 4.77, 0.99757, 1, 12, 27.04, -1.71, 1, 1, 12, 11.9, -8.4, 1, 3, 3, 62.37, -111.06, 0.02115, 12, -4.58, -10.54, 0.97642, 10, 219.23, 18.23, 0.00243, 3, 3, 61.39, -115.24, 0.02651, 12, -2.78, -14.43, 0.9703, 10, 219.38, 13.95, 0.00319, 3, 3, 58.6, -116.58, 0.02641, 12, -4.16, -17.21, 0.97039, 10, 217.04, 11.92, 0.0032, 5, 2, 179.55, -104.5, 0.00021, 3, 53.67, -109.4, 0.06173, 7, -24.81, -82.66, 0.00084, 12, -12.46, -14.57, 0.9306, 10, 210.41, 17.57, 0.00663, 4, 3, 53.72, -120.28, 0.06781, 7, -16.47, -89.66, 1e-05, 12, -5.74, -23.13, 0.92409, 10, 213.29, 7.08, 0.00809, 4, 2, 172.51, -115.15, 0.0002, 3, 45.6, -119.3, 0.07494, 12, -12.75, -27.33, 0.91602, 10, 205.2, 5.91, 0.00884, 4, 2, 162.17, -108.42, 0.00219, 3, 35.99, -111.57, 0.11969, 12, -25.08, -27.14, 0.86425, 10, 193.9, 10.87, 0.01387, 5, 2, 153.17, -97.27, 0.01179, 3, 28.15, -99.57, 0.23439, 7, -48.81, -95.77, 0.00091, 12, -38.64, -22.48, 0.72262, 10, 183.2, 20.41, 0.03029, 4, 2, 149.86, -101.02, 0.01442, 3, 24.49, -102.97, 0.25399, 12, -39.44, -27.42, 0.69733, 10, 180.55, 16.17, 0.03426, 5, 2, 140.38, -92.19, 0.02717, 3, 15.93, -93.24, 0.31485, 7, -61.54, -101, 0.00056, 12, -52.17, -25, 0.60803, 10, 169.76, 23.33, 0.04939, 5, 2, 132.5, -80.23, 0.07321, 3, 9.28, -80.55, 0.40932, 7, -75.52, -97.86, 0.0018, 12, -65.21, -19.06, 0.41991, 10, 160.03, 33.86, 0.09576, 5, 2, 129.64, -81.94, 0.08591, 3, 6.27, -81.97, 0.41842, 7, -76.38, -101.08, 0.00136, 12, -66.71, -22.03, 0.38561, 10, 157.49, 31.7, 0.1087, 5, 2, 124.68, -77.92, 0.12452, 3, 1.74, -77.48, 0.42118, 7, -82.74, -101.63, 0.00065, 12, -73.05, -21.27, 0.30345, 10, 151.95, 34.86, 0.1502, 4, 2, 116.33, -82.74, 0.18288, 3, -7.06, -81.43, 0.37398, 12, -77.56, -29.79, 0.20729, 10, 144.49, 28.75, 0.23585, 4, 2, 108.35, -90.21, 0.20869, 3, -15.74, -88.07, 0.32961, 12, -80.34, -40.36, 0.16125, 10, 137.84, 20.07, 0.30045, 4, 2, 107.57, -98.63, 0.20886, 3, -17.35, -96.37, 0.32618, 12, -76.52, -47.9, 0.15771, 10, 138.44, 11.64, 0.30725, 4, 2, 102.94, -98.43, 0.20897, 3, -21.95, -95.71, 0.32602, 12, -80.55, -50.2, 0.15759, 10, 133.83, 11.08, 0.30743, 4, 2, 101.38, -94.56, 0.2096, 3, -23.11, -91.71, 0.32537, 12, -83.92, -47.75, 0.15719, 10, 131.67, 14.64, 0.30784, 4, 2, 100.46, -84.56, 0.22534, 3, -23.03, -81.66, 0.2974, 12, -90.03, -39.77, 0.137, 10, 129.13, 24.37, 0.34026, 4, 2, 95.71, -88.91, 0.2324, 3, -28.18, -85.52, 0.24347, 12, -91.73, -45.99, 0.10531, 10, 125.15, 19.3, 0.41881, 4, 2, 82.95, -96.47, 0.21366, 3, -41.64, -91.76, 0.16149, 12, -98.51, -59.18, 0.06447, 10, 113.79, 9.76, 0.56038, 4, 2, 74.26, -98.39, 0.19277, 3, -50.48, -92.8, 0.12842, 12, -104.85, -65.43, 0.05005, 10, 105.53, 6.45, 0.62876, 4, 2, 72.83, -96.46, 0.18669, 3, -51.7, -90.75, 0.1205, 12, -107.08, -64.56, 0.04673, 10, 103.81, 8.12, 0.64607, 4, 2, 63.59, -98.39, 0.11593, 3, -61.09, -91.74, 0.0617, 12, -113.88, -71.11, 0.02326, 10, 95, 4.71, 0.79912, 4, 2, 60.19, -100.59, 0.06579, 3, -64.69, -93.59, 0.03426, 12, -115.59, -74.78, 0.0128, 10, 92.01, 1.99, 0.88715, 4, 2, 63.57, -107.58, 0.01281, 3, -62.03, -100.88, 0.01067, 12, -109.01, -78.9, 0.00396, 10, 96.48, -4.36, 0.97255, 3, 3, -54.16, -107.02, 0.00514, 12, -99.02, -78.91, 0.0019, 10, 105.68, -8.24, 0.99296, 3, 3, -54.94, -109.66, 0.00513, 12, -98.02, -81.47, 0.00189, 10, 105.62, -10.98, 0.99298, 3, 3, -86.3, -107.82, 0.00041, 12, -123.9, -99.28, 0.00015, 10, 74.86, -17.39, 0.99944, 1, 10, 54.44, -22.24, 1, 1, 10, 53.99, -27.5, 1, 1, 10, 47.84, -28.63, 1, 1, 10, 26.73, -25.61, 1, 1, 10, 5.03, -20.84, 1, 1, 10, -1.99, -12.85, 1, 3, 1, -8.91, -45.3, 0.3241, 2, -35.27, -45.61, 0.13114, 10, -11.13, 40.68, 0.54475, 3, 1, -4.9, -39.85, 0.40393, 2, -31.56, -39.96, 0.1495, 10, -8.39, 46.86, 0.44657, 4, 1, -3.54, -1.89, 0.99163, 2, -32.24, -1.98, 0.00181, 9, -4.06, -59.55, 0.00188, 10, -15.25, 84.23, 0.00469, 2, 1, -8.72, 1.84, 0.95353, 9, -8.19, -54.69, 0.04647, 2, 1, -15.25, 22.1, 0.64958, 9, -9.66, -33.45, 0.35042, 2, 1, -14.44, 44.76, 0.18252, 9, -3.43, -11.65, 0.81748, 3, 1, -13.42, 73.35, 0.00046, 2, -46.13, 72.62, 0, 9, 4.42, 15.85, 0.99954, 3, 1, -7.61, 89.65, 0.00029, 2, -41.2, 89.21, 0, 9, 13.98, 30.28, 0.99971, 3, 1, -9.23, 95.61, 0.00027, 2, -43.14, 95.08, 0, 9, 13.84, 36.46, 0.99973, 3, 1, -5.46, 104.24, 0.00023, 2, -39.83, 103.9, 0, 9, 19.57, 43.93, 0.99977, 3, 1, -6.05, 116.29, 0.00021, 2, -41.07, 115.9, 0, 9, 21.9, 55.76, 0.99979, 3, 1, -7.18, 130.39, 0.0002, 2, -42.95, 129.92, 0, 9, 24.19, 69.72, 0.9998, 3, 1, -3.26, 148.59, 0.0002, 2, -40.02, 148.3, 0, 9, 32.36, 86.45, 0.9998, 2, 1, 11.3, 157.04, 0.00019, 9, 48.53, 91.15, 0.99981, 2, 1, 23.67, 160.85, 0.00018, 9, 61.45, 91.88, 0.99982, 2, 1, 34.37, 160.47, 0.00018, 9, 71.74, 88.94, 0.99982, 2, 1, 31.58, 154.72, 0.00017, 9, 67.66, 84.03, 0.99983, 3, 1, 40.8, 144.82, 0.00014, 2, 4.19, 146.9, 0, 9, 74.23, 72.21, 0.99986, 2, 1, 54.34, 132.19, 8e-05, 9, 84.34, 56.69, 0.99992, 3, 1, 74.89, 119.85, 2e-05, 11, 18.68, 115.8, 0.00369, 9, 101.32, 39.78, 0.99629, 4, 1, 90.29, 123.17, 1e-05, 2, 54.77, 127.93, 9e-05, 11, 23.46, 100.79, 0.00968, 9, 117.07, 39.3, 0.99022, 3, 1, 92.78, 120.69, 1e-05, 11, 21.23, 98.08, 0.00928, 9, 118.89, 36.29, 0.99071, 4, 1, 88.19, 115.88, 1e-05, 2, 53.06, 120.53, 0.00101, 11, 16, 102.18, 0.01247, 9, 113.28, 32.73, 0.98651, 4, 2, 84.05, 121.19, 0.02027, 3, -18.81, 124.69, 8e-05, 11, 17.98, 71.24, 0.05382, 9, 143.11, 24.31, 0.92583, 3, 2, 107.03, 119.82, 0.01689, 11, 17.59, 48.23, 0.05923, 9, 164.69, 16.3, 0.92388, 3, 2, 106.89, 115.27, 0.01715, 11, 13.04, 48.18, 0.05931, 9, 163.23, 11.99, 0.92354, 4, 2, 97.24, 109.22, 0.02912, 3, -6.88, 111.48, 0.00123, 11, 6.59, 57.56, 0.0638, 9, 152.23, 9.02, 0.90585, 4, 2, 92.85, 100.17, 0.06954, 3, -12.15, 102.9, 0.00734, 11, -2.65, 61.55, 0.08501, 9, 145.4, 1.63, 0.83812, 4, 2, 96.5, 100.78, 0.07978, 3, -8.46, 103.15, 0.00919, 11, -1.88, 57.94, 0.09193, 9, 149.06, 1.16, 0.81909, 4, 2, 97.79, 96.93, 0.08397, 3, -7.56, 99.18, 0.01002, 11, -5.67, 56.48, 0.0946, 9, 149.17, -2.9, 0.81142, 4, 2, 93.17, 88.75, 0.13668, 3, -12.98, 91.51, 0.02293, 11, -14.04, 60.75, 0.13157, 9, 142.36, -9.38, 0.70882, 4, 2, 96.31, 86.92, 0.15707, 3, -10.04, 89.38, 0.02994, 11, -15.73, 57.54, 0.15165, 9, 144.83, -12.04, 0.66134, 4, 2, 94.25, 83.78, 0.17949, 3, -12.39, 86.46, 0.03867, 11, -18.96, 59.46, 0.17531, 9, 141.95, -14.45, 0.60653, 4, 2, 96.4, 81.6, 0.20912, 3, -10.47, 84.07, 0.05643, 11, -21.05, 57.22, 0.22573, 9, 143.37, -17.16, 0.50873, 4, 2, 99.05, 82.43, 0.21874, 3, -7.76, 84.63, 0.06918, 11, -20.11, 54.61, 0.26615, 9, 146.14, -17.14, 0.44593, 4, 2, 103.28, 81.28, 0.22078, 3, -3.66, 83.07, 0.0866, 11, -21.07, 50.33, 0.33039, 9, 149.86, -19.47, 0.36223, 4, 2, 106.16, 81.55, 0.21264, 3, -0.77, 83.05, 0.09665, 11, -20.68, 47.47, 0.38084, 9, 152.69, -20.05, 0.30987, 4, 2, 106.67, 83.67, 0.20831, 3, -0.04, 85.11, 0.09993, 11, -18.54, 47.04, 0.40065, 9, 153.8, -18.18, 0.29112, 4, 2, 109.55, 84.16, 0.20733, 3, 2.87, 85.31, 0.10138, 11, -17.92, 44.19, 0.40683, 9, 156.69, -18.54, 0.28446, 5, 2, 113.72, 79.83, 0.17803, 3, 6.58, 80.58, 0.11196, 4, -65.81, 86.53, 0.00061, 11, -22.08, 39.84, 0.5022, 9, 159.41, -23.91, 0.20719, 5, 2, 113.64, 83.97, 0.14587, 3, 6.92, 84.71, 0.10312, 4, -62.36, 88.82, 0.0002, 11, -17.94, 40.09, 0.59116, 9, 160.55, -19.92, 0.15964, 4, 2, 115.26, 89.57, 0.12576, 3, 9.08, 90.12, 0.09565, 11, -12.28, 38.72, 0.64689, 9, 163.73, -15.04, 0.1317, 5, 2, 119.41, 89.07, 0.11251, 3, 13.16, 89.21, 0.08942, 4, -54.96, 86.72, 0.00018, 11, -12.6, 34.55, 0.68256, 9, 167.55, -16.73, 0.11533, 4, 2, 121.04, 93.81, 0.08802, 3, 15.26, 93.76, 0.07238, 11, -7.79, 33.13, 0.75211, 9, 170.5, -12.67, 0.08749, 4, 2, 128.03, 101.79, 0.05887, 3, 23.02, 101.01, 0.04829, 11, 0.48, 26.47, 0.83616, 9, 179.52, -7.08, 0.05669, 4, 2, 132.01, 102.86, 0.05526, 3, 27.09, 101.67, 0.04539, 11, 1.72, 22.54, 0.84632, 9, 183.64, -7.21, 0.05303, 5, 2, 134.8, 99.06, 0.04521, 3, 29.47, 97.61, 0.03945, 4, -38.25, 79.16, 0.00026, 11, -1.97, 19.6, 0.8723, 9, 185.19, -11.67, 0.04279, 4, 2, 140.12, 104.72, 0.01876, 3, 35.34, 102.71, 0.01373, 11, 3.92, 14.52, 0.94991, 9, 191.93, -7.8, 0.0176, 4, 2, 143.11, 105.21, 0.01131, 3, 38.36, 102.9, 0.0067, 11, 4.53, 11.55, 0.97137, 9, 194.94, -8.21, 0.01062, 1, 11, 21.48, 6.81, 1, 1, 11, 26.58, -3.68, 1, 2, 4, -8.82, 64.23, 0.01678, 11, 15.95, -8.11, 0.98322, 3, 3, 66.74, 103.25, 0.0032, 4, -10.68, 53.46, 0.12348, 11, 8.92, -16.48, 0.87331, 3, 3, 75.82, 109.01, 0.00046, 4, -0.53, 49.92, 0.16179, 11, 15.91, -24.65, 0.83776, 2, 4, -0.12, 45.17, 0.16005, 11, 13.86, -28.96, 0.83995, 3, 3, 75.47, 102.32, 0.00342, 4, -5.98, 46.04, 0.1707, 11, 9.24, -25.26, 0.82589, 3, 3, 75.23, 96.89, 0.01678, 4, -10.39, 42.85, 0.23593, 11, 3.83, -25.8, 0.74729, 4, 3, 79.58, 88.56, 0.04627, 6, -29.66, 31.32, 0.00026, 4, -14.22, 34.26, 0.47563, 11, -3.8, -31.29, 0.47785, 4, 3, 83.96, 88.2, 0.03927, 6, -26.65, 28.12, 0.00314, 4, -11.78, 30.61, 0.6486, 11, -3.54, -35.67, 0.30898, 4, 3, 85.07, 93.98, 0.02598, 6, -21.96, 31.67, 0.00876, 4, -6.56, 33.34, 0.73372, 11, 2.35, -35.94, 0.23155, 4, 3, 90.83, 92.66, 0.01635, 6, -18.56, 26.83, 0.03218, 4, -4.01, 28, 0.78213, 11, 1.87, -41.84, 0.16934, 4, 3, 91.21, 99.77, 0.0033, 6, -13.52, 31.85, 0.1094, 4, 1.79, 32.12, 0.79059, 11, 8.95, -41.2, 0.0967, 3, 6, 2.51, 33.42, 0.39047, 4, 17.86, 31.02, 0.57977, 11, 22.28, -50.24, 0.02976, 3, 6, 23.82, 24.17, 0.86383, 4, 37.34, 18.37, 0.1355, 11, 32.75, -70.97, 0.00067, 1, 6, 36.18, 3.08, 1, 1, 6, 36.01, -2.24, 1, 2, 3, 147.85, 102.24, 0.00071, 6, 30.18, -4.28, 0.99929, 3, 3, 152.46, 94.51, 0.0087, 6, 28.42, -13.1, 0.9913, 4, 35.72, -19.15, 0, 3, 3, 152.83, 76.02, 0.08651, 6, 16.3, -27.07, 0.87903, 4, 21.45, -30.92, 0.03446, 3, 3, 148.64, 60.84, 0.31228, 6, 3.02, -35.53, 0.52059, 4, 6.95, -37.07, 0.16713, 3, 3, 154.43, 55.17, 0.47084, 6, 3.51, -43.62, 0.36569, 4, 6.11, -45.13, 0.16347, 3, 3, 164.71, 36.08, 0.78495, 6, -1.65, -64.68, 0.13643, 4, -2.47, -65.04, 0.07862, 3, 3, 175.83, 22.75, 0.93875, 6, -2.33, -82.03, 0.0418, 4, -6.01, -82.04, 0.01945, 3, 3, 185.39, 13.12, 0.97836, 6, -1.69, -95.58, 0.017, 4, -7.62, -95.51, 0.00464, 3, 3, 185.48, 9.73, 0.97914, 6, -3.89, -98.16, 0.01646, 4, -10.22, -97.69, 0.0044, 4, 3, 177.53, 4.55, 0.98657, 6, -13.26, -96.67, 0.00799, 4, -19.21, -94.68, 0.00194, 7, -31.63, 85.51, 0.0035, 4, 3, 182.13, 3.23, 0.98898, 6, -10.74, -100.73, 0.00301, 4, -17.39, -99.1, 0.00047, 7, -27.65, 88.16, 0.00753, 3, 3, 187.55, -0.97, 0.98858, 6, -9.53, -107.49, 0.0004, 7, -20.93, 89.57, 0.01102, 4, 3, 187.18, -5.46, 0.98651, 6, -12.81, -110.57, 0.00022, 7, -17.75, 86.39, 0.01317, 8, -30.56, 84.71, 0.0001, 3, 3, 180.58, -10.93, 0.96372, 7, -17.84, 77.82, 0.03475, 8, -30.28, 76.14, 0.00153, 3, 3, 175.38, -19.34, 0.88195, 7, -14.79, 68.41, 0.10946, 8, -26.82, 66.87, 0.00859, 3, 3, 178.77, -19.25, 0.86579, 7, -12.67, 71.06, 0.12444, 8, -24.82, 69.61, 0.00977, 3, 3, 185.66, -23.95, 0.85983, 7, -4.63, 73.27, 0.13062, 8, -16.88, 72.17, 0.00955, 3, 3, 185.29, -28.43, 0.85951, 7, -1.45, 70.09, 0.13094, 8, -13.56, 69.13, 0.00954, 1, 10, 13.48, -20.61, 1, 1, 10, 40.4, -26.41, 1, 5, 2, 102.13, 67.52, 0.27725, 3, -6.18, 69.49, 0.10509, 4, -82.42, 89.63, 0.00017, 11, -34.87, 50.89, 0.29084, 9, 144.74, -32.29, 0.32665, 4, 2, 83.98, 64.79, 0.32316, 3, -24.51, 68.58, 0.042, 11, -38.38, 68.91, 0.14512, 9, 126.58, -29.61, 0.48972, 4, 2, 84.08, 59.44, 0.379, 3, -24.95, 63.25, 0.04615, 11, -43.72, 68.59, 0.13858, 9, 125.11, -34.76, 0.43627, 4, 2, 71.29, 56, 0.40905, 3, -38.01, 61.1, 0.01574, 11, -47.7, 81.21, 0.07746, 9, 111.88, -34.32, 0.49775, 4, 2, 72.97, 51.74, 0.46727, 3, -36.77, 56.7, 0.0182, 11, -51.88, 79.35, 0.07818, 9, 112.25, -38.88, 0.43635, 4, 2, 65.57, 46.79, 0.52081, 3, -44.63, 52.52, 0.00665, 11, -57.14, 86.54, 0.04976, 9, 103.72, -41.45, 0.42278, 3, 2, 50.85, 37.94, 0.60762, 11, -66.61, 100.86, 0.01626, 9, 87.06, -45.62, 0.37613, 3, 2, 51.19, 33.76, 0.67045, 11, -70.77, 100.35, 0.01425, 9, 86.17, -49.72, 0.31529, 4, 1, 67.74, 20.15, 0.00026, 2, 37.77, 23.84, 0.78299, 11, -81.25, 113.34, 0.00269, 9, 70.43, -55.29, 0.21405, 4, 1, 60.6, 10.72, 0.00025, 2, 31.14, 14.05, 0.89977, 11, -91.32, 119.54, 0.0004, 9, 61.23, -62.72, 0.09958, 2, 2, 23.4, 7.89, 0.95633, 9, 52.04, -66.35, 0.04367, 3, 2, 31.59, 3.33, 0.98659, 11, -102.01, 118.63, 3e-05, 9, 58.54, -73.11, 0.01338, 3, 1, 49.08, -11.38, 0.00164, 2, 20.82, -8.63, 0.96473, 10, 38.18, 86.3, 0.03362, 3, 1, 35.07, -22.65, 0.11699, 2, 7.43, -20.64, 0.74508, 10, 26.93, 72.27, 0.13793, 4, 1, 35.21, -26.42, 0.12503, 2, 7.78, -24.4, 0.6963, 12, -200.51, -38.17, 0, 10, 27.88, 68.62, 0.17866, 3, 1, 24.21, -31.04, 0.28326, 2, -2.97, -29.61, 0.46579, 10, 18.13, 61.74, 0.25095, 3, 1, 24.68, -36.08, 0.24898, 2, -2.23, -34.61, 0.43341, 10, 19.67, 56.92, 0.31761, 3, 1, 28.87, -41.08, 0.17962, 2, 2.22, -39.38, 0.44062, 10, 24.84, 52.94, 0.37976, 5, 1, 41.74, -56.75, 0.05641, 2, 15.91, -54.34, 0.36712, 3, -104.13, -43.15, 0.00148, 12, -177.69, -59.19, 0.00052, 10, 40.79, 40.41, 0.57447, 5, 1, 56.17, -60.34, 0.0164, 2, 30.52, -57.15, 0.39482, 3, -89.88, -47.4, 0.00967, 12, -163.83, -53.8, 0.00315, 10, 55.66, 40.01, 0.57597, 5, 1, 54.1, -63.99, 0.0172, 2, 28.65, -60.9, 0.338, 3, -92.12, -50.95, 0.0083, 12, -163.42, -57.97, 0.00271, 10, 54.42, 36.01, 0.63379, 5, 1, 63.28, -74.19, 0.00383, 2, 38.36, -70.59, 0.24684, 3, -83.42, -61.57, 0.01897, 12, -150.03, -61, 0.00629, 10, 65.58, 28.03, 0.72408, 5, 1, 71.88, -78.05, 0.0007, 2, 47.15, -74, 0.23385, 3, -75.01, -65.83, 0.03599, 12, -140.78, -59.2, 0.01221, 10, 74.81, 26.1, 0.71725, 5, 1, 70.13, -81.71, 0.00053, 2, 45.6, -77.74, 0.18924, 3, -76.93, -69.4, 0.02979, 12, -140.1, -63.2, 0.01021, 10, 73.9, 22.15, 0.77022, 4, 2, 60.63, -86.05, 0.17004, 3, -62.8, -79.16, 0.06997, 12, -122.95, -62.23, 0.02565, 10, 90.07, 16.41, 0.73434, 4, 2, 77.63, -87.87, 0.22002, 3, -46.07, -82.68, 0.14925, 12, -107.59, -54.73, 0.05824, 10, 107.14, 17.38, 0.57249, 4, 2, 75.31, -92.58, 0.20022, 3, -48.86, -87.13, 0.13313, 12, -107.05, -59.96, 0.05186, 10, 105.62, 12.35, 0.61479, 5, 2, 112.07, 68.67, 0.23095, 3, 3.83, 69.64, 0.14244, 4, -76.09, 81.88, 0.00187, 11, -33.3, 41.01, 0.3971, 9, 154.58, -34.1, 0.22764, 5, 2, 113.52, 60.87, 0.26112, 3, 4.49, 61.74, 0.18598, 4, -81.88, 76.46, 0.00386, 11, -41.03, 39.23, 0.3522, 9, 153.69, -41.98, 0.19685, 5, 2, 109.5, 62.78, 0.2749, 3, 0.68, 64.04, 0.15511, 4, -82.44, 80.87, 0.00181, 11, -39.29, 43.32, 0.33393, 9, 150.41, -38.99, 0.23425, 5, 2, 109.57, 49.82, 0.35606, 3, -0.54, 51.13, 0.21448, 4, -93.31, 73.81, 0.00264, 11, -52.24, 42.7, 0.24772, 9, 146.69, -51.4, 0.17909, 5, 2, 111.65, 33.49, 0.43208, 3, -0.11, 34.68, 0.32658, 4, -105.94, 63.26, 0.00246, 11, -68.46, 39.93, 0.14332, 9, 143.91, -67.62, 0.09556, 5, 2, 116.91, 20.14, 0.3412, 3, 3.79, 20.87, 0.55082, 4, -114.34, 51.62, 0.0017, 11, -81.58, 34.11, 0.07027, 9, 145.05, -81.93, 0.03601, 5, 2, 108.49, 10.91, 0.61965, 3, -5.51, 12.53, 0.33884, 4, -126.65, 53.73, 6e-05, 11, -91.15, 42.13, 0.02396, 9, 134.3, -88.29, 0.01748, 3, 2, 101.23, -0.4, 0.99984, 12, -134.16, 31.89, 1e-05, 10, 116.18, 107.53, 0.00014, 4, 2, 97.01, -18.13, 0.69948, 3, -19.82, -15.22, 0.23909, 12, -128.3, 14.63, 0.01973, 10, 114.91, 89.34, 0.0417, 4, 2, 93.7, -31.96, 0.56979, 3, -24.5, -28.65, 0.27458, 12, -123.75, 1.15, 0.0438, 10, 113.89, 75.16, 0.11183, 4, 2, 101.08, -45.93, 0.36545, 3, -18.56, -43.28, 0.37575, 12, -110.07, -6.74, 0.09302, 10, 123.44, 62.58, 0.16579, 5, 2, 111.6, -58.06, 0.22379, 3, -9.3, -56.41, 0.43385, 7, -105.95, -96.42, 0.00018, 12, -94.7, -11.41, 0.16582, 10, 135.8, 52.32, 0.17636, 5, 2, 120.49, -64.66, 0.15469, 3, -1.11, -63.86, 0.45447, 7, -94.97, -95, 0.00079, 12, -83.66, -12.27, 0.23672, 10, 145.65, 47.26, 0.15332, 5, 2, 129.08, -64.33, 0.10557, 3, 7.47, -64.39, 0.48315, 7, -89.02, -88.79, 0.00315, 12, -76.57, -7.42, 0.29559, 10, 154.07, 48.99, 0.11254, 5, 2, 131.6, -72.69, 0.08854, 3, 9.14, -72.96, 0.43976, 7, -81.4, -93.06, 0.00253, 12, -69.98, -13.15, 0.36137, 10, 157.92, 41.15, 0.1078, 4, 2, 89.01, 31.37, 0.71428, 3, -22.84, 34.83, 0.0573, 11, -71.55, 62.46, 0.07388, 9, 121.64, -63.04, 0.15454, 3, 2, 49.53, 15.75, 0.89727, 11, -88.83, 101.24, 0.00357, 9, 79.32, -66.46, 0.09916, 4, 2, 73.2, -1.72, 0.99687, 3, -41.88, 3.49, 0.00107, 12, -157.2, 15.85, 0.00021, 10, 88.74, 101.66, 0.00185, 5, 1, 61.97, -21.27, 0.00083, 2, 34.22, -17.82, 0.90415, 3, -82.27, -8.64, 0.00103, 12, -181.62, -18.53, 0.00049, 10, 52.9, 79.42, 0.0935, 4, 2, 51.9, -33.41, 0.74729, 3, -66.24, -25.92, 0.02584, 12, -158.36, -22.32, 0.00728, 10, 72.88, 66.92, 0.21959, 4, 2, 76.32, -37.45, 0.64974, 3, -42.34, -32.38, 0.12709, 12, -135.53, -12.74, 0.02908, 10, 97.64, 66.91, 0.19409, 5, 1, 77.21, -68.06, 0.00059, 2, 51.94, -63.73, 0.3641, 3, -69.22, -56.09, 0.05094, 12, -142.19, -47.97, 0.01654, 10, 77.86, 37.01, 0.56784, 4, 2, 88.38, -72.52, 0.28491, 3, -33.84, -68.48, 0.23332, 12, -106.66, -36.02, 0.09026, 10, 115.25, 34.27, 0.39151, 5, 1, 39.92, -39.87, 0.09043, 2, 13.2, -37.58, 0.56951, 3, -105.16, -26.21, 0.00034, 12, -188.9, -46.45, 0.00016, 10, 35.38, 56.5, 0.33956, 8, 2, 136.82, 22.84, 0.29879, 3, 23.88, 21.57, 0.57897, 6, -115.9, 18.93, 0.01461, 4, -101.31, 36.31, 0.00991, 7, -143.97, -20.69, 0.00404, 8, -152.01, -27.75, 0.00061, 11, -78.03, 14.32, 0.06154, 9, 164.88, -85.16, 0.03154, 8, 2, 154.73, 19.02, 0.26295, 3, 41.32, 15.98, 0.59786, 6, -106.7, 3.1, 0.01475, 4, -94.86, 19.17, 0.00981, 7, -128.43, -11, 0.0284, 8, -136.9, -17.4, 0.00432, 11, -81.08, -3.73, 0.05416, 9, 180.9, -94.04, 0.02775, 10, 2, 163.93, 3.25, 0.22399, 3, 48.9, -0.63, 0.51695, 6, -112.2, -14.31, 0.01247, 4, -103.17, 2.92, 0.0083, 7, -110.86, -15.96, 0.08523, 8, -119.13, -21.59, 0.00596, 12, -83.03, 68.35, 0.07607, 11, -96.44, -13.6, 0.04579, 9, 185.1, -111.8, 0.02346, 10, 177.45, 121.34, 0.00177, 10, 2, 173.55, -17.92, 0.17345, 3, 56.35, -22.65, 0.41984, 6, -121.43, -35.65, 0.00931, 4, -115.8, -16.6, 0.0062, 7, -89.24, -24.52, 0.14954, 8, -97.16, -29.2, 0.0077, 12, -63.62, 55.54, 0.1743, 11, -117.18, -24.11, 0.03419, 9, 188.12, -134.86, 0.01752, 10, 190.39, 102.02, 0.00796, 10, 2, 173.04, -42.77, 0.11964, 3, 53.36, -47.33, 0.34135, 6, -140.19, -51.96, 0.00627, 4, -137, -29.59, 0.00417, 7, -72.35, -42.76, 0.13919, 8, -79.49, -46.69, 0.00663, 12, -50.82, 34.23, 0.3353, 11, -142.04, -24.66, 0.02302, 9, 180.37, -158.48, 0.0118, 10, 193.93, 77.41, 0.01262, 10, 2, 166.05, -62.01, 0.08155, 3, 44.49, -65.78, 0.30348, 6, -159.14, -59.71, 0.00406, 4, -156.97, -34.09, 0.0027, 7, -64.02, -61.45, 0.09057, 8, -70.36, -65, 0.0043, 12, -46.49, 14.22, 0.47198, 11, -161.56, -18.5, 0.01489, 9, 168.08, -174.85, 0.00763, 10, 190.17, 57.29, 0.01885, 10, 2, 150.84, -66.11, 0.08576, 3, 28.94, -68.33, 0.36594, 6, -172.39, -51.18, 0.00228, 4, -168.63, -23.49, 0.00152, 7, -72.13, -74.97, 0.05216, 8, -77.87, -78.86, 0.00242, 12, -57.2, 2.66, 0.41921, 11, -166.3, -3.47, 0.00839, 9, 152.33, -174.32, 0.0043, 10, 175.83, 50.77, 0.05802, 10, 2, 135.8, -62.55, 0.10445, 3, 14.34, -63.29, 0.45571, 6, -179.85, -37.65, 0.00086, 4, -173.75, -8.92, 0.00057, 7, -85.41, -82.85, 0.02171, 8, -90.8, -87.31, 0.00091, 12, -71.82, -2.34, 0.31439, 11, -163.39, 11.7, 0.00315, 9, 138.98, -166.53, 0.00162, 10, 160.41, 51.83, 0.09664, 10, 2, 124.75, 21.03, 0.32437, 3, 11.69, 20.97, 0.56117, 6, -125.34, 26.66, 0.00559, 4, -109.35, 45.5, 0.00484, 7, -151.4, -30.37, 0.00214, 8, -159.01, -37.75, 0.00027, 12, -125.66, 62.54, 0.00056, 11, -80.35, 26.31, 0.0668, 9, 152.81, -83.37, 0.03423, 10, 135.9, 132.5, 1e-05, 10, 2, 130.14, 21.84, 0.31295, 3, 17.13, 21.24, 0.56912, 6, -121.12, 23.21, 0.00962, 4, -105.76, 41.4, 0.00711, 7, -148.08, -26.05, 0.00299, 8, -155.88, -33.29, 0.00043, 12, -121.52, 66.1, 0.00031, 11, -79.32, 20.96, 0.06445, 9, 158.2, -84.17, 0.03303, 10, 141.08, 134.17, 1e-05, 10, 2, 127.88, 6.11, 0.53777, 3, 13.32, 5.82, 0.34683, 6, -134.29, 14.33, 0.00464, 4, -120.22, 34.81, 0.00312, 7, -138.79, -38.93, 0.03174, 8, -146.04, -45.75, 0.00222, 12, -115.07, 51.58, 0.02833, 11, -95.12, 22.54, 0.02797, 9, 151.45, -98.55, 0.0167, 10, 141.42, 118.29, 0.00068, 10, 2, 129.19, -13.61, 0.43102, 3, 12.65, -13.94, 0.36551, 6, -148.03, 0.11, 0.00341, 4, -136.12, 23.06, 0.00229, 7, -124.15, -52.22, 0.02777, 8, -130.84, -58.39, 0.00177, 12, -103.46, 35.58, 0.10855, 11, -114.78, 20.39, 0.01993, 9, 146.95, -117.8, 0.01183, 10, 145.92, 99.04, 0.02791, 10, 2, 134.57, -29.45, 0.31589, 3, 16.42, -30.23, 0.38836, 6, -156.15, -14.51, 0.00294, 4, -146.54, 9.99, 0.00197, 7, -109.29, -59.88, 0.03516, 8, -115.65, -65.4, 0.00193, 12, -90.48, 25.04, 0.1836, 11, -130.36, 14.34, 0.01557, 9, 147.47, -134.51, 0.00902, 10, 153.81, 84.3, 0.04557, 10, 2, 135.86, -48.22, 0.19616, 3, 15.83, -49.04, 0.42268, 6, -169.19, -28.07, 0.00182, 4, -161.65, -1.23, 0.00122, 7, -95.32, -72.49, 0.02881, 8, -101.15, -77.39, 0.00141, 12, -79.4, 9.83, 0.26137, 11, -149.06, 12.25, 0.00879, 9, 143.23, -152.84, 0.00496, 10, 158.14, 65.99, 0.07277, 10, 2, 141.4, -55.13, 0.15346, 3, 20.65, -56.46, 0.40235, 6, -170.59, -36.82, 0.00198, 4, -164.47, -9.62, 0.00132, 7, -86.54, -73.62, 0.03731, 8, -92.33, -78.13, 0.00178, 12, -71.03, 6.93, 0.32084, 11, -155.73, 6.42, 0.00853, 9, 146.51, -161.07, 0.00465, 10, 164.73, 60.07, 0.06779, 10, 2, 141.64, -32.5, 0.27608, 3, 23.14, -33.98, 0.38093, 6, -153.67, -21.8, 0.00351, 4, -145.31, 2.39, 0.00235, 7, -102.08, -57.17, 0.05393, 8, -108.57, -62.38, 0.00277, 12, -82.87, 26.21, 0.21388, 11, -133.12, 7.16, 0.0167, 9, 153.33, -139.5, 0.00939, 10, 161.28, 82.43, 0.04047, 10, 2, 142.78, -25.16, 0.29127, 3, 25.01, -26.78, 0.39649, 6, -147.46, -17.71, 0.00466, 4, -138.5, 5.39, 0.00311, 7, -106.36, -51.09, 0.0543, 8, -113.11, -56.49, 0.00304, 12, -85.81, 33.04, 0.18095, 11, -125.73, 6.33, 0.02096, 9, 156.57, -132.8, 0.01157, 10, 161.21, 89.86, 0.03364, 10, 2, 136.82, -12.84, 0.37604, 3, 20.32, -13.93, 0.39422, 6, -142.33, -5.03, 0.00503, 4, -131.35, 17.05, 0.00336, 7, -119.2, -46.36, 0.04105, 8, -126.14, -52.33, 0.00263, 12, -97.41, 40.3, 0.11517, 11, -113.68, 12.8, 0.02415, 9, 154.47, -119.28, 0.01361, 10, 153.33, 101.05, 0.02475, 10, 2, 135.19, 7.52, 0.46878, 3, 20.72, 6.49, 0.3977, 6, -128.35, 9.86, 0.00679, 4, -115.09, 29.42, 0.00454, 7, -134.51, -32.85, 0.03688, 8, -142.03, -39.49, 0.00289, 12, -109.63, 56.66, 0.02957, 11, -93.41, 15.31, 0.03328, 9, 158.85, -99.33, 0.01886, 10, 148.39, 120.87, 0.00071], "edges": [128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 288, 288, 114, 114, 290, 290, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 90, 88, 88, 86, 86, 84, 84, 82, 82, 80, 80, 78, 78, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 286, 284, 286, 284, 282, 282, 280, 280, 278, 278, 276, 274, 276, 274, 272, 272, 270, 270, 268, 268, 266, 266, 264, 264, 262, 262, 260, 260, 258, 258, 256, 256, 254, 254, 252, 252, 250, 250, 248, 248, 246, 246, 244, 244, 242, 242, 240, 240, 238, 238, 236, 236, 234, 234, 232, 232, 230, 230, 228, 228, 226, 226, 224, 224, 222, 222, 220, 220, 218, 218, 216, 216, 214, 214, 212, 212, 210, 210, 208, 208, 206, 206, 204, 204, 202, 202, 200, 200, 198, 198, 196, 196, 194, 194, 192, 192, 190, 190, 188, 188, 186, 186, 184, 184, 182, 182, 180, 180, 178, 178, 176, 176, 174, 174, 172, 172, 170, 170, 168, 168, 166, 166, 164, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 154, 152, 152, 150, 148, 150, 148, 146, 146, 144, 144, 142, 142, 140, 140, 138, 138, 136, 136, 134, 134, 132, 128, 130, 130, 132, 112, 114, 114, 116, 186, 292, 292, 294, 294, 296, 296, 298, 298, 300, 300, 302, 302, 304, 304, 306, 306, 308, 308, 310, 310, 312, 312, 314, 314, 316, 316, 318, 318, 320, 320, 322, 322, 324, 324, 326, 326, 328, 328, 330, 330, 332, 332, 334, 334, 336, 336, 338, 338, 340, 340, 342, 342, 344, 344, 94, 198, 346, 346, 348, 348, 350, 350, 352, 352, 354, 354, 356, 356, 358, 358, 360, 360, 362, 362, 364, 364, 366, 366, 368, 368, 370, 370, 372, 372, 374, 374, 70, 394, 396, 396, 398, 398, 400, 400, 402, 402, 404, 404, 406, 406, 408, 408, 372, 410, 356, 394, 412, 412, 410, 412, 414, 414, 416, 416, 418, 418, 420, 420, 408, 406, 422, 422, 424, 424, 426, 426, 428, 428, 430, 430, 394]}}, "light2": {"light2": {"x": 4.4, "width": 499, "y": -8.99, "height": 476}}}}, "skeleton": {"images": "./images/", "x": -243.63, "width": 499, "y": -79.34, "spine": "3.7-from-3.8-from-4.0.61", "audio": "D:/job/Sang/anim-<PERSON>gHo/Tiger", "hash": "SFqrNYjaf+Q", "height": 476}, "slots": [{"attachment": "light2", "name": "light2", "bone": "light2"}, {"attachment": "light1", "name": "light1", "bone": "light1"}, {"attachment": "Tiger", "name": "Tiger", "bone": "Tiger13"}, {"attachment": "close_eyes_r", "name": "close_eyes_r", "bone": "close_eyes_r"}, {"attachment": "close_eyes_l", "name": "close_eyes_l", "bone": "close_eyes_l"}], "bones": [{"name": "root"}, {"parent": "root", "rotation": 92.05, "name": "Tiger", "length": 17.17, "x": 7.97, "y": 13.69}, {"parent": "Tiger", "rotation": -3.07, "name": "Tiger3", "length": 102.94, "x": 28.75, "y": -1.64}, {"parent": "Tiger3", "rotation": 5.73, "name": "Tiger2", "length": 156.76, "x": 115.22, "y": -1.01}, {"parent": "Tiger2", "rotation": 51.6, "name": "Tiger4", "length": 6.63, "x": 115.27, "y": 78.41}, {"parent": "Tiger2", "name": "Tiger6", "x": 115.27, "y": 78.41}, {"parent": "Tiger6", "rotation": 42.08, "name": "Tiger5", "length": 27.74, "x": 7.32, "y": 6.77}, {"parent": "Tiger2", "rotation": -49.71, "name": "Tiger8", "length": 6.07, "x": 132.76, "y": -74.87}, {"parent": "Tiger8", "rotation": -2.49, "name": "Tiger7", "length": 29.92, "x": 9.1, "y": 0.43}, {"parent": "Tiger", "rotation": 13.9, "name": "Tiger10", "length": 89.2, "x": -13.91, "y": 56.9}, {"parent": "Tiger", "rotation": -12.44, "name": "Tiger11", "length": 67.9, "x": -6.8, "y": -87.42}, {"parent": "Tiger2", "rotation": 81.82, "name": "Tiger12", "length": 20.25, "x": 49.16, "y": 96.77}, {"parent": "Tiger2", "rotation": -37.89, "name": "Tiger13", "length": 19.03, "x": 72.45, "y": -105.55}, {"parent": "Tiger2", "name": "Tiger9", "x": 83.41, "y": -17.32}, {"parent": "Tiger9", "name": "close_eyes_l", "x": 3, "y": -45.74}, {"parent": "Tiger9", "name": "close_eyes_r", "x": -6.16, "y": 52.14}, {"parent": "root", "name": "light2", "x": 1.48, "y": 167.65}, {"parent": "light2", "name": "light1", "x": 0.69, "y": -6.93}], "animations": {"animation": {"slots": {"close_eyes_r": {"attachment": [{"name": null, "time": 0}, {"name": "close_eyes_r", "time": 2}, {"name": null, "time": 2.0667}, {"name": "close_eyes_r", "time": 3.6}, {"name": null, "time": 3.6667}]}, "light1": {"color": [{"color": "ffffff6c", "time": 0}, {"color": "ffffffff", "curve": "stepped", "time": 0.6667}, {"color": "ffffffff", "time": 1.3333}, {"color": "ffffff71", "time": 4}]}, "close_eyes_l": {"attachment": [{"name": null, "time": 0}, {"name": "close_eyes_l", "time": 2}, {"name": null, "time": 2.0667}, {"name": "close_eyes_l", "time": 3.6}, {"name": null, "time": 3.6667}]}, "light2": {"color": [{"color": "ffffff6c", "time": 0}, {"color": "ffffffff", "time": 0.6667}, {"color": "ffffff71", "time": 4}]}}, "bones": {"Tiger13": {"rotate": [{"angle": 0, "time": 0}, {"angle": 12.46, "time": 0.6667}, {"angle": 0, "time": 1.3333}]}, "Tiger12": {"rotate": [{"angle": 0, "time": 0}, {"angle": 4.12, "time": 0.6667}, {"angle": 0, "time": 1.3333}]}, "Tiger7": {"rotate": [{"angle": 0, "time": 0}, {"angle": 28.82, "time": 0.6667}, {"angle": 0, "time": 1.3333}]}, "Tiger6": {"rotate": [{"angle": 0, "time": 0}, {"angle": -3.18, "time": 0.6667}, {"angle": 0, "time": 1.3333}]}, "Tiger5": {"rotate": [{"angle": 0, "time": 0}, {"angle": 15.75, "time": 0.6667}, {"angle": 0, "time": 1.3333}]}, "Tiger3": {"rotate": [{"angle": 0, "time": 0}, {"angle": -1.2, "time": 0.3333}, {"curve": "stepped", "angle": -3.81, "time": 0.6667}, {"angle": -3.81, "time": 0.7333}, {"angle": 0, "time": 1.3333}], "scale": [{"x": 1, "y": 1, "time": 0}, {"curve": "stepped", "x": 1.09, "y": 1.09, "time": 0.6667}, {"x": 1.09, "y": 1.09, "time": 0.7333}, {"x": 1, "y": 1, "time": 1.3333}]}, "Tiger2": {"rotate": [{"angle": 0, "time": 0}, {"angle": -0.98, "time": 0.3333}, {"curve": "stepped", "angle": -4.65, "time": 0.6667}, {"angle": -4.65, "time": 0.7333}, {"angle": 0, "time": 1.3333}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.027, "y": 1, "time": 0.6667}, {"x": 1, "y": 1, "time": 1.3333}]}, "Tiger11": {"rotate": [{"angle": 0, "time": 0}, {"angle": -7.45, "time": 0.6667}, {"angle": -6.04, "time": 0.7333}, {"angle": 0, "time": 1.3333}]}, "Tiger10": {"rotate": [{"angle": 0, "time": 0}, {"curve": "stepped", "angle": 3.54, "time": 0.6667}, {"angle": 3.54, "time": 0.7333}, {"angle": 0, "time": 1.3333}]}, "light2": {"rotate": [{"angle": 0, "time": 0}, {"angle": -180, "time": 2}, {"angle": -360, "time": 4}], "scale": [{"x": 0.599, "y": 0.599, "time": 0}, {"x": 1, "y": 1, "time": 0.6667}, {"x": 0.599, "y": 0.599, "time": 4}]}}, "deform": {"default": {"Tiger": {"Tiger": [{"time": 0}, {"offset": 942, "time": 0.6667, "vertices": [2.42142, 5.49565, 2.45888, 5.44892, 5.60917, -2.06693, 4.87158, 4.37119, -0.52485, 6.20857, -0.39626, 6.21744, 6.17355, 0.83572, 2.26549, 6.40163, -4.22436, 4.45285, -4.03058, 4.53197, 4.23512, 4.3416, -2.19677, 6.31843, -4.22436, 4.45285, -4.03058, 4.53197, 4.23512, 4.3416, -2.19677, 6.31843, -4.22436, 4.45285, -4.03058, 4.53197, 4.23512, 4.3416, -2.19677, 6.31843, -1.1974, 2.78477, 2.72095, 1.31041, 0.06075, 3.30315, -1.1974, 2.78477, 2.72095, 1.31041, 0.06075, 3.30315, -0.39152, 3.67283, -0.76017, 3.30268, 3.25999, 0.91234, 0.72745, 3.62126, -0.39152, 3.67283, -0.76017, 3.30268, 3.25999, 0.91234, 0.72745, 3.62126, -0.76017, 3.30268, 0.72745, 3.62126, -0.76017, 3.30268, 3.25999, 0.91234, 0.72745, 3.62126, 0, 0, 0, 0, 0, 0, 1.28639, 6.74915, 0.4303, 6.28935, -0.91992, 6.80897, 1.28639, 6.74915, 0.4303, 6.28935, -3.06454, 5.50768, -0.91992, 6.80897, 1.28639, 6.74915, 0.4303, 6.28935, -0.91992, 6.80897, 1.28639, 6.74915, 0.4303, 6.28935, -0.91992, 6.80897, 1.28639, 6.74915, 0.4303, 6.28935, -0.91992, 6.80897, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.57751, 2.3864, 0.60643, 2.3751, 2.23778, 0.99997, 2.41187, -0.43657, 1.6447, 2.11083, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.62856, -0.54472, 0.60217, -0.55648, 0.81383, 0.09952, 1.11545, 0.01973, 0.79052, -0.44357, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.55144, 4.85654, 0.6264, 4.84526, 4.87739, -0.28181, 2.72752, 4.5758, -0.44408, 5.25334, 5.22381, 0.70712, 1.91692, 5.41673, -0.32299, 3.8207, -0.24402, 3.82614, -2.33974, 3.0372, -1.18274, 4.00816, 0.0932, 2.61012, -0.20187, 2.38788, -0.15245, 2.39128, -1.46236, 1.89817, -0.73919, 2.50505, -0.20187, 2.38788, -0.15245, 2.39128, -1.46236, 1.89817, -0.73919, 2.50505, -0.20187, 2.38788, -0.15245, 2.39128, -1.46236, 1.89817, -0.73919, 2.50505, 0.03729, 1.04409, -0.08075, 0.95519, -0.06097, 0.95655, -0.58498, 0.75928, -0.29568, 1.00206, -4.26472, 4.93043, -4.06131, 5.01018, -6.16753, 1.88703, -5.63425, 4.32849, 1.28639, 6.74915, 0.4303, 6.28935, 0.53497, 6.28012, -3.06454, 5.50768, -0.91992, 6.80897, 0.57751, 2.3864, 0.60643, 2.3751, 2.21327, 1.05344, 2.23778, 0.99997, -1.41949, 1.99858, -1.04849, 2.21589, 2.41187, -0.43657, 1.6447, 2.11083, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.88014, 1.04266, 0.87633, 1.02587, 1.34367, -0.12099, 1.34811, -0.04959, -0.21577, 1.33194, 0.39725, 1.28924, 0.35078, 1.30265, 1.08518, -0.80133, 1.34213, 0.64045, 0.71096, 1.30625, 0.67841, -0.72562, 0.64763, -0.73825, -0.12613, -1.1106, -0.17667, -0.96599, 0.98186, 0.01675, 1.49533, -0.11031, 1.22598, -0.23608, -0.69084, -0.69775, 0.34811, -1.02522, 0.88342, -0.62562, -0.01669, -0.13883, -0.01854, -0.13844, -0.02231, -0.22038, -0.12019, -0.07138, 0.0936, -0.10353, 0.64603, 0.17607, 0.367, 0.01969, -0.1396, 0.00899, -0.07887, -0.13041, 0.01265, -0.1517, 0.57751, 2.3864, 0.60643, 2.3751, 2.21327, 1.05344, 2.23778, 0.99997, -1.41949, 1.99858, -1.04849, 2.21589, -0.82404, 2.30846, 2.41187, -0.43657, 1.6447, 2.11083, 0.09318, 2.67451, 0.57751, 2.3864, 0.60643, 2.3751, 2.21327, 1.05344, 2.23778, 0.99997, -1.41949, 1.99858, -1.04849, 2.21589, -0.82404, 2.30846, 2.41187, -0.43657, 1.6447, 2.11083, 0.09318, 2.67451, 0.03988, 2.60068, 0.08699, 2.59952, 2.05342, 1.59634, 2.09094, 1.54646, -1.92661, 1.74754, -1.59171, 2.05733, -1.38037, 2.20437, 2.59891, 0.09753, 1.20694, 2.56495, -0.52777, 2.78545, -0.53763, 0.21428, -0.51944, 0.22442, -0.15985, 0.54291, -0.14684, 0.54649, -0.50711, -0.25104, -0.54321, -0.15856, -0.55634, -0.10409, 0.18704, 0.5341, -0.43776, 0.45412, -0.62095, 0.11094, -0.53763, 0.21428, -0.51944, 0.22442, -0.15985, 0.54291, -0.14684, 0.54649, -0.50711, -0.25104, -0.54321, -0.15856, -0.55634, -0.10409, 0.18704, 0.5341, -0.43776, 0.45412, -0.62095, 0.11094, 0.09093, -0.33044, 0.08273, -0.33206, -0.10637, -0.42206, -0.20909, -0.27101, 0.30672, -0.15152, 0.84753, 0.09163, 0.55911, -0.08436, -0.32556, -0.10573, -0.05798, -0.36903, 0.16956, -0.33263, 2.89019, 0.35496, 2.82091, 0.30033, 2.18013, -2.17799, 1.98714, -2.02412, 1.5952, 2.34605, 2.7757, 2.48281, 2.59674, 2.04834, 0.49889, -2.79216, 3.02978, -0.94477, 3.00768, 1.01311, 1.07523, -0.4286, 1.03909, -0.44883, 0.31958, -1.08565, 0.29353, -1.09312, 1.01436, 0.50237, 1.08649, 0.31741, 1.11235, 0.20795, -0.37426, -1.06813, 0.87552, -0.9083, 1.24189, -0.22164, 1.07523, -0.4286, 1.03909, -0.44883, 0.31958, -1.08565, 0.29353, -1.09312, 1.01436, 0.50237, 1.08649, 0.31741, 1.11235, 0.20795, -0.37426, -1.06813, 0.87552, -0.9083, 1.24189, -0.22164, 1.07523, -0.4286, 1.03909, -0.44883, 0.31958, -1.08565, 0.29353, -1.09312, 1.01436, 0.50237, 1.08649, 0.31741, 1.11235, 0.20795, -0.37426, -1.06813, 0.87552, -0.9083, 1.24189, -0.22164, 1.65274, 1.95779, 1.64552, 1.92627, 2.53285, -0.03221, 2.53131, -0.09315, -0.40514, 2.50095, 0.03799, 2.5333, 0.28831, 2.51641, 2.03761, -1.5047, 2.52022, 1.20253, 1.33507, 2.45287]}, {"time": 1.3333}]}}}}}}, [0]]], 0, 0, [0], [-1], [119]], [[[37, "bet", ".mp3", 0.684], -1], 0, 0, [], [], []], [[{"name": "D_<PERSON>", "rect": [0, 0, 250, 257], "offset": [0, 0], "originalSize": [250, 257], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [120]], [[[44, "total-money-animation", 2.6333333333333333, [{}, "paths", 11, [{}, "lbTotalUserWin", 11, [{}, "props", 11, [{"active": [{"frame": 0, "value": true}], "y": [{"frame": 0, "value": -69}, {"frame": 0.23333333333333334, "value": 68}, {"frame": 2.6333333333333333, "value": 109}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.23333333333333334, "value": 255}, {"frame": 2.6333333333333333, "value": 0}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0, 0]], [{"frame": 0.23333333333333334}, "value", 8, [0, 1, 1]]], 11, 11]]]]]]], 0, 0, [], [], []], [[{"name": "<PERSON>_<PERSON>_win", "rect": [0, 0, 474, 239], "offset": [0, 0], "originalSize": [474, 239], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [121]], [[{"name": "ic_chat", "rect": [0, 0, 88, 88], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [122]], [[{"name": "Chip_Orange", "rect": [0, 0, 95, 100], "offset": [0, 0], "originalSize": [95, 100], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [123]], [[{"name": "k<PERSON><PERSON><PERSON><PERSON>", "rect": [0, 0, 295, 108], "offset": [0, 0], "originalSize": [295, 108], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [124]], [[[37, "bgm2", ".mp3", 121.248], -1], 0, 0, [], [], []], [[{"name": "bg_tex1", "rect": [0, 0, 444, 80], "offset": [-3.5, 0], "originalSize": [451, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [125]], [[[37, "get-coin", ".mp3", 1.776], -1], 0, 0, [], [], []], [[{"name": "D_Hoa", "rect": [0, 0, 192, 219], "offset": [0, 0], "originalSize": [192, 219], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [126]], [[{"name": "table-bc", "rect": [0, 0, 1267, 720], "offset": [0, 0], "originalSize": [1267, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [127]], [[[38, "<PERSON><PERSON><PERSON><PERSON><PERSON>", "\r\nvongxoay.png\r\nsize: 512,512\r\nformat: RGBA8888\r\nfilter: Linear,Linear\r\nrepeat: none\r\nvongxoay\r\n  rotate: false\r\n  xy: 2, 2\r\n  size: 295, 294\r\n  orig: 295, 294\r\n  offset: 0, 0\r\n  index: -1\r\n", ["vongxoay.png"], {"skins": {"default": {"vongxoay": {"vongxoay": {"width": 295, "height": 294}}}}, "skeleton": {"images": "./images/", "width": 295, "spine": "3.6.53", "hash": "RADJfWPSSVc3ktJh5u7j/Kn36SQ", "height": 294}, "slots": [{"attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bone": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "bones": [{"name": "root"}, {"parent": "root", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "animations": {"cham": {"bones": {"vongxoay": {"rotate": [{"angle": 0, "time": 0}, {"angle": -120, "time": 0.6}, {"angle": 120, "time": 1.1667}, {"angle": 0, "time": 1.7667}]}}}, "nhanh": {"bones": {"vongxoay": {"rotate": [{"angle": 0, "time": 0}, {"angle": -120, "time": 0.1667}, {"angle": 120, "time": 0.3333}, {"angle": 0, "time": 0.5}]}}}}}, [0]]], 0, 0, [0], [-1], [128]], [[{"name": "ic_soicau", "rect": [0, 0, 88, 88], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [129]], [[{"name": "D_Rong_Disable", "rect": [0, 0, 489, 252], "offset": [0, 0], "originalSize": [489, 252], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [130]], [[{"name": "<PERSON>_<PERSON>g_win", "rect": [0, 0, 472, 239], "offset": [0, 0], "originalSize": [472, 239], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [131]], [[[37, "open-card", ".mp3", 2.74275], -1], 0, 0, [], [], []], [[[38, "anim_hoa", "\r\nanim_hoa.png\r\nsize: 355, 238\r\nformat: RGBA8888\r\nfilter: Linear, Linear\r\nrepeat: none\r\nHoa\r\n  rotate: false\r\n  xy: 197, 2\r\n  size: 140, 81\r\n  orig: 140, 81\r\n  offset: 0, 0\r\n  index: -1\r\nlight1\r\n  rotate: false\r\n  xy: 197, 85\r\n  size: 156, 151\r\n  orig: 156, 151\r\n  offset: 0, 0\r\n  index: -1\r\nlight2\r\n  rotate: true\r\n  xy: 2, 34\r\n  size: 202, 193\r\n  orig: 202, 193\r\n  offset: 0, 0\r\n  index: -1\r\n", ["anim_hoa.png"], {"skins": {"default": {"light1": {"light1": {"x": 1.28, "width": 156, "y": 0.76, "height": 151}}, "Hoa": {"Hoa": {"x": -2.43, "width": 140, "y": 5.71, "height": 81}}, "light2": {"light2": {"x": 0.21, "width": 202, "y": -1, "height": 193}}}}, "skeleton": {"images": "./images/", "x": -100.94, "width": 202, "y": -92.66, "spine": "3.7-from-3.8-from-4.0.61", "audio": "D:/job/Sang/anim-RongHo/anim_hoa", "hash": "Bl0TYthTEXA", "height": 193}, "slots": [{"attachment": "light2", "name": "light2", "bone": "light2"}, {"attachment": "light1", "name": "light1", "bone": "light1"}, {"attachment": "Hoa", "name": "Hoa", "bone": "Hoa"}], "bones": [{"name": "root"}, {"parent": "root", "name": "bone"}, {"parent": "bone", "name": "Hoa", "x": 2.49, "y": -1.87}, {"parent": "bone", "name": "light1", "x": -1.22, "y": 3.07}, {"parent": "bone", "name": "light2", "x": -0.15, "y": 4.83}], "animations": {"animation": {"slots": {"light1": {"color": [{"color": "ffffffff", "time": 0}, {"color": "ffffffa1", "time": 1.1}, {"color": "ffffffff", "time": 2.1667}]}, "light2": {"color": [{"color": "ffffff60", "time": 0}, {"color": "ffffffff", "time": 1}, {"color": "ffffff60", "time": 2.1667}]}}, "bones": {"light1": {"rotate": [{"angle": 0, "time": 0}, {"angle": -180, "time": 1.1}, {"angle": -360, "time": 2.1667}], "scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.664, "y": 0.664, "time": 1.1}, {"x": 1, "y": 1, "time": 2.1667}]}, "Hoa": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.111, "y": 1.111, "time": 0.1667}, {"x": 0.836, "y": 0.836, "time": 0.4}, {"x": 1.067, "y": 1.067, "time": 0.6667}, {"x": 0.93, "y": 0.93, "time": 1}, {"x": 1.049, "y": 1.049, "time": 1.3333}, {"x": 0.882, "y": 0.882, "time": 1.7333}, {"x": 1, "y": 1, "time": 2.1667}]}, "light2": {"scale": [{"x": 0.666, "y": 0.666, "time": 0}, {"x": 1, "y": 1, "time": 1}, {"x": 0.666, "y": 0.666, "time": 2.1667}]}}}}}, [0]]], 0, 0, [0], [-1], [132]], [[{"name": "D_<PERSON>", "rect": [0, 0, 474, 239], "offset": [0, 0], "originalSize": [474, 239], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [133]], [[[38, "girl", "\r\ngirl.png\r\nsize: 349, 255\r\nformat: RGBA8888\r\nfilter: Linear, Linear\r\nrepeat: none\r\nclose_eyes\r\n  rotate: true\r\n  xy: 321, 183\r\n  size: 70, 26\r\n  orig: 70, 26\r\n  offset: 0, 0\r\n  index: -1\r\nearrings_l\r\n  rotate: false\r\n  xy: 321, 53\r\n  size: 11, 47\r\n  orig: 11, 47\r\n  offset: 0, 0\r\n  index: -1\r\nearrings_r\r\n  rotate: false\r\n  xy: 321, 135\r\n  size: 14, 46\r\n  orig: 14, 46\r\n  offset: 0, 0\r\n  index: -1\r\ngirl\r\n  rotate: true\r\n  xy: 2, 2\r\n  size: 251, 317\r\n  orig: 251, 317\r\n  offset: 0, 0\r\n  index: -1\r\nmouth\r\n  rotate: true\r\n  xy: 321, 102\r\n  size: 31, 13\r\n  orig: 31, 13\r\n  offset: 0, 0\r\n  index: -1\r\n", ["girl.png"], {"skins": {"default": {"close_eyes": {"close_eyes": {"rotation": -91.86, "x": 0.98, "width": 70, "y": 2.54, "height": 26}}, "mouth": {"mouth": {"width": 31, "type": "mesh", "hull": 17, "height": 13, "triangles": [21, 15, 16, 9, 10, 11, 0, 21, 16, 17, 8, 9, 21, 0, 1, 11, 17, 9, 20, 14, 15, 20, 15, 21, 18, 12, 13, 11, 12, 18, 17, 11, 18, 19, 13, 14, 19, 14, 20, 18, 13, 19, 7, 8, 17, 2, 20, 21, 2, 21, 1, 6, 17, 18, 7, 17, 6, 4, 5, 18, 6, 18, 5, 19, 4, 18, 3, 19, 20, 3, 20, 2, 4, 19, 3], "uvs": [1, 0.17448, 1, 0.36269, 0.86084, 0.76996, 0.68019, 1, 0.48969, 1, 0.38513, 1, 0.25639, 1, 0.09172, 0.66151, 0, 0.36165, 0, 0.21172, 0, 0.09391, 0.1995, 0.07963, 0.36118, 0, 0.49442, 0.08677, 0.59322, 0, 0.7549, 0.08677, 1, 0, 0.19352, 0.40805, 0.36135, 0.4658, 0.54751, 0.50048, 0.75403, 0.41724, 0.92273, 0.32708], "vertices": [1, 7, -1.69, 15.82, 1, 1, 7, 0.75, 15.77, 1, 1, 7, 5.97, 11.37, 1, 1, 7, 8.86, 5.71, 1, 1, 7, 8.76, -0.19, 1, 1, 7, 8.7, -3.43, 1, 1, 7, 8.63, -7.42, 1, 1, 7, 4.14, -12.45, 1, 1, 7, 0.19, -15.22, 1, 1, 7, -1.76, -15.19, 1, 1, 7, -3.29, -15.16, 1, 1, 7, -3.37, -8.97, 1, 1, 7, -4.31, -3.94, 1, 1, 7, -3.11, 0.17, 1, 1, 7, -4.18, 3.25, 1, 1, 7, -2.97, 8.24, 1, 1, 7, -3.96, 15.86, 1, 1, 7, 0.9, -9.23, 1, 1, 7, 1.74, -4.05, 1, 1, 7, 2.3, 1.72, 1, 1, 7, 1.33, 8.14, 1, 1, 7, 0.25, 13.39, 1], "edges": [6, 8, 6, 4, 4, 2, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 16, 14, 14, 12, 8, 10, 10, 12, 16, 18, 18, 20, 18, 34, 34, 36, 36, 38, 38, 40, 40, 42, 2, 0, 0, 32, 42, 0]}}, "earrings_l": {"earrings_l": {"width": 11, "type": "mesh", "hull": 11, "height": 47, "triangles": [4, 5, 3, 5, 6, 3, 3, 6, 2, 6, 7, 2, 2, 8, 1, 8, 2, 7, 1, 8, 10, 8, 9, 10, 10, 0, 1], "uvs": [0.75784, 0.03791, 0.75784, 0.13505, 0.88927, 0.26295, 1, 0.88949, 0.70942, 0.96234, 0.30821, 0.94615, 0.19061, 0.75997, 0, 0.26457, 0.13527, 0.13506, 0.20445, 0, 0.49498, 0], "vertices": [2, 8, -1.77, 3.73, 0.99702, 9, -6.7, 3.76, 0.00298, 2, 8, 2.8, 3.73, 0.64339, 9, -2.14, 3.61, 0.35661, 2, 8, 8.81, 5.17, 0.00334, 9, 3.91, 4.86, 0.99666, 1, 10, 9.39, 5.28, 1, 1, 10, 12.94, 2.23, 1, 1, 10, 12.36, -2.21, 1, 2, 9, 27.01, -3.59, 0.16428, 10, 3.67, -3.86, 0.83572, 2, 8, 8.88, -4.61, 0.0132, 9, 3.67, -4.92, 0.9868, 2, 8, 2.8, -3.12, 0.79605, 9, -2.37, -3.23, 0.20395, 1, 8, -3.55, -2.36, 1, 1, 8, -3.55, 0.84, 1], "edges": [18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 18, 20, 0, 20]}}, "girl": {"girl": {"width": 251, "type": "mesh", "hull": 93, "height": 317, "triangles": [90, 92, 0, 92, 90, 91, 1, 90, 0, 6, 1, 2, 5, 6, 2, 2, 3, 5, 4, 5, 3, 8, 6, 7, 89, 90, 1, 88, 89, 1, 87, 88, 1, 1, 8, 87, 1, 6, 8, 84, 85, 86, 83, 84, 86, 83, 86, 87, 81, 78, 79, 81, 79, 80, 77, 78, 81, 75, 76, 77, 82, 83, 87, 87, 81, 82, 87, 8, 81, 81, 75, 77, 8, 75, 81, 119, 33, 34, 119, 120, 33, 118, 119, 34, 118, 34, 35, 118, 35, 36, 118, 36, 37, 117, 118, 37, 130, 119, 118, 130, 118, 117, 110, 117, 37, 130, 117, 110, 111, 130, 110, 110, 37, 38, 109, 111, 110, 38, 109, 110, 112, 130, 111, 112, 111, 109, 108, 109, 38, 39, 93, 38, 108, 38, 93, 107, 112, 109, 107, 109, 108, 106, 112, 107, 106, 105, 112, 93, 41, 108, 40, 93, 39, 40, 41, 93, 107, 108, 41, 42, 105, 106, 41, 42, 106, 41, 106, 107, 43, 105, 42, 113, 130, 112, 104, 103, 113, 112, 105, 113, 104, 113, 105, 43, 104, 105, 103, 104, 43, 100, 114, 116, 98, 115, 114, 99, 98, 114, 100, 99, 114, 99, 45, 98, 44, 116, 102, 100, 116, 44, 45, 99, 100, 44, 45, 100, 44, 102, 43, 96, 95, 115, 97, 96, 115, 97, 115, 98, 49, 50, 96, 47, 48, 49, 96, 47, 49, 97, 47, 96, 46, 97, 98, 46, 47, 97, 45, 46, 98, 123, 54, 55, 53, 54, 123, 124, 52, 53, 123, 124, 53, 51, 52, 124, 125, 51, 124, 126, 124, 123, 125, 124, 126, 94, 125, 126, 51, 94, 50, 94, 51, 125, 95, 50, 94, 115, 94, 126, 95, 94, 115, 115, 126, 114, 96, 50, 95, 71, 73, 74, 72, 73, 71, 11, 9, 10, 67, 69, 70, 68, 69, 67, 66, 67, 70, 64, 65, 66, 12, 14, 11, 14, 12, 13, 14, 15, 11, 66, 62, 64, 63, 64, 62, 16, 18, 19, 18, 16, 17, 20, 16, 19, 75, 71, 74, 75, 66, 71, 66, 70, 71, 62, 66, 8, 16, 20, 23, 21, 22, 20, 9, 11, 8, 11, 15, 60, 16, 23, 15, 22, 23, 20, 8, 61, 62, 66, 75, 8, 25, 23, 24, 61, 8, 11, 15, 23, 60, 11, 60, 61, 60, 23, 28, 26, 23, 25, 26, 27, 23, 28, 59, 60, 23, 27, 28, 29, 59, 28, 58, 59, 29, 58, 29, 30, 58, 31, 57, 31, 58, 30, 31, 122, 57, 31, 32, 33, 33, 120, 31, 55, 56, 57, 120, 122, 31, 122, 55, 57, 123, 55, 122, 121, 122, 120, 127, 122, 121, 126, 123, 122, 127, 126, 122, 130, 120, 119, 128, 127, 121, 129, 121, 120, 129, 120, 130, 128, 121, 129, 114, 126, 127, 113, 129, 130, 128, 129, 113, 114, 127, 128, 113, 114, 128, 113, 103, 114, 101, 114, 103, 116, 114, 101, 102, 101, 103, 116, 101, 102, 102, 103, 43], "uvs": [0.5905, 0.02276, 0.58356, 0.03407, 0.62601, 0.03892, 0.64928, 0.04958, 0.64601, 0.06316, 0.63295, 0.06316, 0.61662, 0.05669, 0.62071, 0.0638, 0.61726, 0.07123, 0.66737, 0.08852, 0.70849, 0.11498, 0.70849, 0.14346, 0.72455, 0.15313, 0.74126, 0.16992, 0.74447, 0.20349, 0.73332, 0.23566, 0.73332, 0.24983, 0.74155, 0.24926, 0.75622, 0.25889, 0.75765, 0.28921, 0.74692, 0.30309, 0.7462, 0.31045, 0.73439, 0.32235, 0.70828, 0.32915, 0.71865, 0.34586, 0.71747, 0.36043, 0.68666, 0.38436, 0.64491, 0.39941, 0.61382, 0.40192, 0.60576, 0.41104, 0.60767, 0.43317, 0.61859, 0.4591, 0.63096, 0.4568, 0.64989, 0.47581, 0.75398, 0.50406, 0.82313, 0.53979, 0.85239, 0.58668, 0.87138, 0.65631, 0.93334, 0.80191, 0.96232, 0.90795, 0.94233, 0.93169, 0.80542, 0.9578, 0.64152, 0.97284, 0.57156, 1, 0.44803, 1, 0.36674, 0.97365, 0.28162, 0.95543, 0.16505, 0.93539, 0.09373, 0.9111, 0.07993, 0.87649, 0.09757, 0.80145, 0.15605, 0.66161, 0.18793, 0.56347, 0.22605, 0.52059, 0.31809, 0.49477, 0.38457, 0.46997, 0.39032, 0.45277, 0.41525, 0.44011, 0.42164, 0.42999, 0.42228, 0.40114, 0.3948, 0.37027, 0.36881, 0.34878, 0.34776, 0.30347, 0.32066, 0.28693, 0.31125, 0.23478, 0.3056, 0.21005, 0.31426, 0.1865, 0.30846, 0.15753, 0.30071, 0.15623, 0.30602, 0.13781, 0.32969, 0.12844, 0.33581, 0.12198, 0.32112, 0.1084, 0.32724, 0.10194, 0.34683, 0.10194, 0.37255, 0.09127, 0.36928, 0.07091, 0.3901, 0.0596, 0.40683, 0.03633, 0.42397, 0.03052, 0.43377, 0.03504, 0.42316, 0.04861, 0.4403, 0.03924, 0.45418, 0.02438, 0.45091, 0.01856, 0.45008, 0.00531, 0.4705, 0.00789, 0.48642, 0.02632, 0.50193, 0.015, 0.53173, 0.00692, 0.5603, 0.00596, 0.56315, 0, 0.58479, 0, 0.94933, 0.88421, 0.2675, 0.75229, 0.25379, 0.78962, 0.23525, 0.83111, 0.29088, 0.85409, 0.36262, 0.86271, 0.38882, 0.86526, 0.42751, 0.85824, 0.47467, 0.85632, 0.48877, 0.86558, 0.52827, 0.85313, 0.56722, 0.85016, 0.61502, 0.86447, 0.70129, 0.86355, 0.76017, 0.85109, 0.7934, 0.84278, 0.77999, 0.80585, 0.75434, 0.732, 0.73977, 0.76893, 0.71353, 0.8197, 0.57538, 0.8317, 0.42673, 0.82985, 0.30431, 0.82062, 0.44121, 0.85768, 0.74665, 0.68992, 0.74348, 0.64055, 0.66104, 0.60372, 0.58599, 0.60121, 0.50988, 0.62548, 0.43906, 0.60456, 0.35556, 0.59702, 0.27205, 0.6213, 0.25831, 0.69244, 0.3319, 0.71102, 0.44561, 0.7242, 0.49554, 0.7264, 0.57412, 0.7264, 0.65547, 0.7242], "vertices": [2, 4, 129.24, -23.59, 0.02194, 6, 19.05, -2.27, 0.97806, 2, 4, 125.71, -21.73, 0.1266, 6, 16.18, -5.03, 0.8734, 2, 4, 123.83, -32.33, 0.2859, 6, 25.64, -10.17, 0.7141, 2, 4, 120.26, -38.06, 0.27873, 6, 29.94, -15.36, 0.72127, 2, 4, 115.99, -37.1, 0.27848, 6, 27.68, -19.11, 0.72152, 2, 4, 116.09, -33.82, 0.28377, 6, 24.61, -17.98, 0.71623, 2, 4, 118.27, -29.79, 0.33607, 6, 21.47, -14.63, 0.66393, 2, 4, 115.99, -30.75, 0.41799, 6, 21.65, -17.1, 0.58201, 2, 4, 113.66, -29.8, 0.50259, 6, 20.03, -19.01, 0.49741, 2, 4, 107.78, -42.2, 0.72073, 6, 29.93, -28.51, 0.27927, 2, 4, 99.06, -52.24, 0.7952, 6, 36.7, -39.96, 0.2048, 2, 4, 90.03, -51.95, 0.8399, 6, 33.57, -48.43, 0.1601, 2, 4, 86.84, -55.88, 0.86483, 6, 36.29, -52.7, 0.13517, 2, 4, 81.39, -59.9, 0.88556, 6, 38.38, -59.15, 0.11444, 2, 4, 70.72, -60.36, 0.9113, 6, 35.44, -69.41, 0.0887, 2, 4, 60.62, -57.23, 0.94293, 6, 29.28, -78, 0.05707, 2, 4, 56.13, -57.08, 0.95816, 6, 27.73, -82.22, 0.04184, 2, 4, 56.24, -59.15, 0.96226, 6, 29.73, -82.76, 0.03774, 2, 4, 53.07, -62.73, 0.96745, 6, 32.12, -86.9, 0.03255, 2, 4, 43.46, -62.78, 0.97529, 6, 29.13, -96.04, 0.02471, 2, 4, 39.15, -59.95, 0.97933, 6, 25.07, -99.23, 0.02067, 2, 4, 36.82, -59.69, 0.98096, 6, 24.1, -101.36, 0.01904, 2, 4, 33.15, -56.61, 0.98351, 6, 20.01, -103.87, 0.01649, 2, 4, 31.2, -49.98, 0.98984, 6, 13.11, -103.62, 0.01016, 2, 4, 25.82, -52.42, 0.99436, 6, 13.72, -109.5, 0.00564, 2, 4, 21.22, -51.97, 0.9958, 6, 11.84, -113.72, 0.0042, 3, 4, 13.89, -43.99, 0.99817, 17, -53.21, -5.29, 5e-05, 6, 1.96, -118.16, 0.00178, 4, 3, 43.33, -33.12, 0.00782, 4, 9.46, -33.37, 0.98979, 17, -51.57, -16.69, 0.00208, 6, -9.52, -119, 0.00031, 4, 3, 42.23, -25.36, 0.05351, 4, 8.92, -25.54, 0.93655, 17, -53, -24.41, 0.00993, 6, -17.12, -117.04, 2e-05, 3, 3, 39.26, -23.45, 0.13047, 4, 6.09, -23.42, 0.84748, 17, -50.79, -27.16, 0.02205, 4, 2, 109.83, -25.25, 0.00046, 3, 32.27, -24.21, 0.36138, 4, -0.93, -23.68, 0.57178, 17, -43.92, -28.67, 0.06638, 4, 2, 101.72, -28.3, 0.01266, 3, 24.16, -27.28, 0.56818, 4, -9.24, -26.15, 0.23457, 17, -35.26, -28.34, 0.18459, 4, 2, 102.57, -31.37, 0.0181, 3, 25.02, -30.35, 0.56932, 4, -8.61, -29.28, 0.18398, 17, -35.09, -25.15, 0.2286, 4, 2, 96.73, -36.35, 0.03651, 3, 19.18, -35.34, 0.50821, 4, -14.79, -33.83, 0.09381, 17, -27.97, -22.29, 0.36147, 4, 2, 88.78, -62.8, 0.00692, 3, 11.28, -61.8, 0.06239, 4, -24.58, -59.65, 0.00154, 17, -12.05, 0.28, 0.92915, 1, 17, 3.69, 13.76, 1, 1, 17, 20.02, 16.64, 1, 1, 17, 42.54, 15.02, 1, 1, 17, 91.21, 17, 1, 2, 18, -17.24, 10.65, 0.65806, 17, 125.51, 14.56, 0.34194, 2, 18, -11.2, 17.38, 0.86433, 17, 131.33, 7.63, 0.13567, 1, 18, 23.99, 20.67, 1, 3, 16, 73.85, 0.54, 0.12484, 19, -0.29, 21.97, 0.07212, 18, 65.39, 19.52, 0.80304, 3, 16, 59.92, -13.18, 0.62101, 19, 18.78, 26.28, 0.13955, 18, 84, 25.53, 0.23945, 3, 15, 85.22, -18.7, 0.06461, 16, 30.5, -22.98, 0.93516, 18, 114.69, 21.11, 0.00023, 2, 15, 63.17, -18.67, 0.50962, 16, 8.5, -21.51, 0.49038, 2, 15, 41.21, -21.39, 0.99802, 16, -13.59, -22.79, 0.00198, 1, 15, 11.72, -26.56, 1, 2, 14, 126.21, -7.71, 0.0187, 15, -7.77, -26.19, 0.9813, 2, 14, 116.52, -13.91, 0.1312, 15, -15.12, -17.34, 0.8688, 2, 14, 92.4, -15.83, 0.95978, 15, -20, 6.36, 0.04022, 1, 14, 45.77, -13.22, 1, 1, 14, 13.65, -13.6, 1, 1, 14, -1.97, -7.91, 1, 3, 2, 87.55, 46.64, 0.23999, 3, 9.85, 47.64, 0.12947, 14, -15.89, 12.26, 0.63054, 4, 2, 96.04, 30.27, 0.3016, 3, 18.37, 31.28, 0.43733, 4, -10.78, 32.67, 0.03679, 14, -27.83, 26.32, 0.22428, 4, 2, 101.55, 29.03, 0.24093, 3, 23.88, 30.05, 0.51962, 4, -5.37, 31.05, 0.0905, 14, -33.47, 26.29, 0.14896, 4, 2, 105.79, 22.93, 0.15299, 3, 28.14, 23.96, 0.52727, 4, -1.57, 24.67, 0.23804, 14, -38.97, 31.29, 0.0817, 4, 2, 109.06, 21.45, 0.09814, 3, 31.41, 22.49, 0.44686, 4, 1.59, 22.96, 0.40568, 14, -42.49, 32, 0.04932, 4, 2, 118.21, 21.64, 0.02376, 3, 40.55, 22.69, 0.16777, 4, 10.72, 22.5, 0.79727, 14, -51.36, 29.77, 0.0112, 5, 2, 127.72, 28.9, 0.00206, 3, 50.05, 29.98, 0.02739, 4, 20.73, 29.08, 0.96929, 14, -59.01, 20.56, 0.00088, 5, -91.79, 37.7, 0.00037, 4, 2, 134.28, 35.68, 1e-05, 3, 56.6, 36.77, 0.0037, 4, 27.75, 35.38, 0.99376, 5, -84.12, 43.19, 0.00254, 2, 4, 42.28, 40.19, 0.9829, 5, -69.16, 46.39, 0.0171, 2, 4, 47.74, 46.82, 0.97015, 5, -63, 52.39, 0.02985, 2, 4, 64.34, 48.65, 0.92569, 5, -46.3, 52.39, 0.07431, 2, 4, 72.22, 49.81, 0.89294, 5, -38.34, 52.68, 0.10706, 2, 4, 79.61, 47.4, 0.84569, 5, -31.26, 49.47, 0.15431, 2, 4, 88.84, 48.55, 0.7721, 5, -21.96, 49.61, 0.2279, 2, 4, 89.31, 50.48, 0.76986, 5, -21.28, 51.48, 0.23014, 2, 4, 95.1, 48.96, 0.74936, 5, -15.69, 49.34, 0.25064, 2, 4, 97.88, 42.93, 0.70571, 5, -13.59, 43.04, 0.29429, 2, 4, 99.88, 41.32, 0.66832, 5, -11.78, 41.23, 0.33168, 2, 4, 104.3, 44.87, 0.63032, 5, -7, 44.27, 0.36968, 2, 4, 106.3, 43.27, 0.63374, 5, -5.18, 42.46, 0.36626, 2, 4, 106.14, 38.35, 0.60962, 5, -5.88, 37.59, 0.39038, 2, 4, 109.31, 31.79, 0.46816, 5, -3.45, 30.72, 0.53184, 2, 4, 115.78, 32.4, 0.3346, 5, 3.06, 30.62, 0.6654, 2, 4, 119.2, 27.06, 0.23509, 5, 5.87, 24.94, 0.76491, 2, 4, 126.43, 22.63, 0.09776, 5, 12.58, 19.74, 0.90224, 2, 4, 128.14, 18.27, 0.07863, 5, 13.8, 15.22, 0.92137, 2, 4, 126.62, 15.86, 0.07534, 5, 12.03, 12.99, 0.92466, 2, 4, 122.41, 18.66, 0.09726, 5, 8.14, 16.23, 0.90274, 2, 4, 125.24, 14.26, 0.02608, 5, 10.48, 11.55, 0.97392, 2, 4, 129.84, 10.63, 0.00134, 5, 14.65, 7.44, 0.99866, 2, 4, 131.71, 11.39, 0, 5, 16.59, 7.99, 1, 1, 5, 20.78, 7.6, 1, 1, 5, 19.24, 2.64, 1, 2, 5, 12.9, -0.49, 0.82773, 6, -5.84, 5.73, 0.17227, 2, 5, 15.9, -4.85, 0.2534, 6, -0.95, 7.74, 0.7466, 2, 5, 17.37, -12.61, 0.01381, 6, 6.96, 7.55, 0.98619, 1, 6, 13.79, 5.35, 1, 1, 6, 15.12, 6.88, 1, 1, 6, 20.21, 4.99, 1, 2, 18, -15.09, 2.74, 0.32117, 17, 117.37, 13.54, 0.67883, 5, 1, 21.57, 56.33, 0.01646, 2, 5.5, 56.22, 0.14587, 14, 66.23, 21.28, 0.65285, 15, 13.6, 36.9, 0.12383, 16, -37.32, 37.18, 0.061, 5, 1, 9.73, 59.77, 0.01838, 2, -6.46, 59.21, 0.08189, 14, 78.56, 21.04, 0.52926, 15, 14.89, 24.64, 0.30389, 16, -36.84, 24.87, 0.06658, 5, 1, -3.42, 64.42, 0.00706, 2, -19.78, 63.36, 0.02201, 14, 92.47, 19.98, 0.23142, 15, 15.55, 10.7, 0.71069, 16, -37.09, 10.92, 0.02882, 5, 1, -10.71, 50.46, 0.02402, 2, -26.53, 49.13, 0.03216, 14, 95.86, 35.36, 0.06482, 15, 31.23, 9.23, 0.7644, 16, -21.55, 8.42, 0.11459, 5, 1, -13.44, 32.46, 0.11338, 2, -28.57, 31.03, 0.04624, 14, 93.8, 53.45, 0.02774, 15, 48.93, 13.5, 0.15172, 16, -3.6, 11.53, 0.66092, 6, 1, -14.25, 25.88, 0.17603, 2, -29.13, 24.43, 0.03785, 14, 92.87, 60.01, 0.01678, 15, 55.33, 15.24, 0.00995, 16, 2.9, 12.84, 0.75924, 19, 53.51, -25.89, 0.00014, 6, 1, -12.02, 16.17, 0.36549, 2, -26.54, 14.81, 0.03097, 14, 88.19, 68.81, 0.00957, 15, 63.48, 20.97, 0, 16, 11.41, 18.02, 0.57914, 19, 43.55, -25.81, 0.01483, 5, 1, -11.42, 4.33, 0.58482, 2, -25.48, 3.01, 0.00327, 14, 84.52, 80.08, 0.00149, 16, 22.44, 22.34, 0.29464, 19, 31.89, -23.66, 0.11578, 5, 1, -14.35, 0.79, 0.51105, 14, 86.43, 84.26, 0.00024, 16, 26.73, 20.68, 0.27281, 19, 29.13, -19.98, 0.21584, 17, 79.27, -95.76, 5e-05, 5, 1, -10.4, -9.12, 0.53931, 2, -23.96, -10.4, 0.0106, 16, 34.89, 27.55, 0.04341, 19, 18.57, -21.52, 0.40208, 17, 78.27, -85.14, 0.0046, 5, 1, -9.46, -18.9, 0.36255, 2, -22.64, -20.13, 0.04674, 19, 8.84, -20.18, 0.56692, 18, 78.3, -21.64, 0.00632, 17, 80.11, -75.49, 0.01747, 5, 1, -14, -30.9, 0.14098, 2, -26.72, -32.29, 0.04137, 19, -1.78, -12.98, 0.70418, 18, 67.07, -15.43, 0.08549, 17, 87.83, -65.25, 0.02799, 5, 1, -13.71, -52.55, 0.0319, 2, -25.6, -53.92, 0.03152, 19, -22.91, -8.25, 0.13943, 18, 45.6, -12.63, 0.71608, 17, 93.62, -44.38, 0.08107, 5, 1, -9.76, -67.33, 0.012, 2, -21.09, -68.54, 0.02013, 19, -38.2, -8.67, 0.04428, 18, 30.4, -14.43, 0.70415, 17, 93.98, -29.09, 0.21944, 5, 1, -7.12, -75.67, 0.00594, 2, -18.14, -76.77, 0.01233, 19, -46.93, -9.3, 0.02163, 18, 21.77, -15.85, 0.56847, 17, 93.79, -20.35, 0.39162, 5, 1, 4.58, -72.3, 0.01459, 2, -6.58, -72.96, 0.03644, 19, -46.36, -21.47, 0.04745, 18, 23.43, -27.91, 0.3015, 17, 81.61, -20.29, 0.60002, 5, 1, 28, -65.87, 0.01752, 2, 16.57, -65.64, 0.10728, 19, -45.52, -45.74, 0.04952, 18, 26.46, -52, 0.06444, 17, 57.33, -19.9, 0.76124, 5, 1, 16.29, -62.21, 0.03389, 2, 4.74, -62.43, 0.11538, 19, -39.25, -35.2, 0.09238, 18, 31.76, -40.94, 0.16959, 17, 67.54, -26.7, 0.58875, 5, 1, 0.19, -55.62, 0.05326, 2, -11.6, -56.46, 0.0856, 19, -29.12, -21.06, 0.16462, 18, 40.57, -25.95, 0.41785, 17, 81.14, -37.53, 0.27866, 5, 1, -3.61, -20.95, 0.37924, 2, -16.72, -21.96, 0.10853, 19, 5.5, -25.4, 0.45804, 18, 75.44, -27.14, 0.02007, 17, 75.06, -71.89, 0.03413, 6, 1, -3.02, 16.37, 0.47764, 2, -17.55, 15.35, 0.11707, 14, 79.55, 66.27, 0.02543, 15, 59.9, 29.22, 0.00859, 16, 8.37, 26.49, 0.37073, 19, 41.66, -34.61, 0.00054, 5, 1, -0.1, 47.09, 0.05935, 2, -15.8, 46.17, 0.10917, 14, 84.74, 35.85, 0.19454, 15, 30.34, 20.33, 0.43885, 16, -21.7, 19.55, 0.1981, 5, 1, -11.85, 12.73, 0.43148, 2, -26.23, 11.38, 0.02268, 14, 87.12, 72.08, 0.00653, 16, 14.61, 19.28, 0.50672, 19, 40.16, -25.18, 0.03259, 6, 1, 41.33, -63.94, 0.0089, 2, 29.83, -63.2, 0.12477, 3, -47.67, -62.32, 0.00093, 19, -46.74, -59.16, 0.02809, 18, 26.47, -65.48, 0.01968, 17, 43.99, -18.01, 0.81762, 6, 1, 56.99, -63.14, 0.00162, 2, 45.44, -61.81, 0.10536, 3, -32.06, -60.9, 0.00869, 19, -49.59, -74.57, 0.00866, 18, 25.02, -81.09, 0.00224, 17, 28.74, -14.39, 0.87342, 6, 1, 68.66, -42.45, 0.00026, 2, 56.32, -40.69, 0.33407, 3, -21.22, -39.75, 0.09951, 19, -32.16, -90.72, 0.00646, 18, 43.84, -95.6, 0.0003, 17, 11.73, -30.98, 0.55939, 4, 2, 56.4, -21.84, 0.65953, 3, -21.18, -20.9, 0.11578, 19, -14.02, -95.86, 0.00332, 17, 5.68, -48.83, 0.22137, 4, 2, 47.98, -3.04, 0.98688, 3, -29.63, -2.12, 0.00227, 19, 6.35, -92.8, 0.00034, 17, 7.71, -69.33, 0.0105, 3, 2, 53.93, 14.98, 0.86343, 14, 9.8, 50.65, 0.13625, 16, -11.27, 95.23, 0.00032, 4, 2, 55.52, 36.01, 0.48436, 3, -22.16, 36.94, 0.0086, 14, 12.95, 29.79, 0.50583, 16, -31.91, 90.87, 0.00121, 4, 2, 47.03, 56.66, 0.11969, 14, 25.85, 11.56, 0.87843, 15, -1.02, 75.78, 5e-05, 16, -49.36, 76.94, 0.00183, 5, 1, 40.54, 58.64, 0.00195, 2, 24.37, 59.25, 0.12345, 14, 48.52, 14.11, 0.84344, 15, 4.3, 53.59, 0.01465, 16, -45.5, 54.45, 0.0165, 5, 1, 34.64, 40.17, 0.01211, 2, 19.18, 40.57, 0.41861, 14, 49.39, 33.48, 0.48006, 15, 23.63, 55.11, 0.03552, 16, -26.12, 54.7, 0.0537, 5, 1, 30.47, 11.62, 0.00246, 2, 16.09, 11.89, 0.90193, 14, 45.98, 62.12, 0.06775, 15, 51.64, 62.02, 0.00457, 16, 2.28, 59.77, 0.02329, 4, 2, 15.88, -0.66, 0.99842, 19, 17.26, -62.51, 0.00065, 18, 90.51, -63.04, 5e-05, 17, 37.4, -81.76, 0.00088, 5, 1, 29.77, -20.63, 0.0482, 2, 16.63, -20.37, 0.73808, 19, -1.93, -57.94, 0.07311, 18, 70.98, -60.22, 0.01378, 17, 42.94, -62.82, 0.12683, 6, 1, 30.47, -41.05, 0.05073, 2, 18.1, -40.75, 0.40876, 3, -59.44, -39.89, 0.0007, 19, -21.96, -53.89, 0.10531, 18, 50.67, -57.99, 0.04993, 17, 48, -43.03, 0.38457], "edges": [86, 84, 84, 82, 82, 80, 80, 78, 78, 186, 186, 76, 76, 74, 74, 72, 72, 70, 70, 68, 68, 66, 66, 64, 64, 62, 62, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 184, 182, 184, 182, 180, 180, 178, 178, 176, 176, 174, 174, 172, 172, 170, 170, 168, 168, 166, 166, 164, 164, 162, 162, 160, 160, 158, 158, 156, 156, 154, 154, 152, 152, 150, 150, 148, 148, 146, 146, 144, 144, 142, 142, 140, 140, 138, 138, 136, 136, 134, 134, 132, 132, 130, 130, 128, 128, 126, 126, 124, 124, 122, 122, 120, 120, 118, 118, 116, 116, 114, 114, 112, 112, 110, 110, 108, 108, 106, 106, 104, 104, 102, 102, 100, 100, 98, 98, 96, 96, 94, 94, 92, 92, 90, 86, 88, 90, 88, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 202, 204, 204, 206, 206, 208, 208, 210, 210, 212, 212, 214, 214, 216, 216, 218, 218, 220, 220, 222, 222, 224, 224, 226, 226, 228, 228, 230, 230, 188, 76, 78, 200, 232, 232, 202, 220, 234, 234, 236, 236, 238, 238, 240, 240, 242, 242, 244, 244, 246, 246, 248, 248, 250, 250, 188, 250, 252, 252, 254, 254, 256, 256, 258, 258, 260, 260, 234]}}, "earrings_r": {"earrings_r": {"width": 14, "type": "mesh", "hull": 13, "height": 46, "triangles": [3, 4, 5, 5, 6, 7, 5, 7, 3, 7, 2, 3, 7, 8, 2, 9, 2, 8, 2, 9, 1, 1, 9, 0, 0, 10, 11, 11, 12, 0, 0, 9, 10], "uvs": [0.68511, 0.09775, 0.71965, 0.18393, 0.75057, 0.75622, 0.76185, 0.79568, 0.79566, 0.90719, 0.56456, 0.94321, 0.31655, 0.92606, 0.27146, 0.81112, 0.21304, 0.76567, 0.19833, 0.20832, 0.17191, 0.07062, 0.2908, 0, 0.60914, 0], "vertices": [2, 11, 2.05, 3.22, 0.88006, 12, -3.15, 3.2, 0.11994, 2, 11, 5.99, 3.89, 0.14179, 12, 0.82, 3.58, 0.85821, 2, 12, 27.15, 3.38, 0.00235, 13, 2.42, 3.03, 0.99765, 1, 13, 4.24, 3.14, 1, 1, 13, 9.38, 3.47, 1, 1, 13, 10.95, 0.19, 1, 1, 13, 10.06, -3.26, 1, 2, 12, 29.51, -3.39, 0.0306, 13, 4.76, -3.74, 0.9694, 2, 12, 27.4, -4.15, 0.15872, 13, 2.65, -4.5, 0.84128, 2, 11, 7.45, -3.34, 0.12942, 12, 1.77, -3.74, 0.87058, 2, 11, 1.14, -4.01, 0.9976, 12, -4.58, -3.96, 0.0024, 1, 11, -2.18, -2.51, 1, 1, 11, -2.39, 1.94, 1], "edges": [22, 20, 20, 18, 18, 16, 16, 14, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 22, 24, 0, 24]}}}}, "skeleton": {"images": "./images/", "x": -106.87, "width": 221.48, "y": -4.21, "spine": "3.7-from-3.8-from-4.0.61", "audio": "D:/job/Sang/anim-RongHo/girl", "hash": "6YS31BZlY4Y", "height": 317}, "slots": [{"attachment": "girl", "name": "girl", "bone": "girl13"}, {"attachment": "mouth", "name": "mouth", "bone": "mouth"}, {"attachment": "close_eyes", "name": "close_eyes", "bone": "close_eyes"}, {"attachment": "earrings_r", "name": "earrings_r", "bone": "earrings_r2"}, {"attachment": "earrings_l", "name": "earrings_l", "bone": "earrings_l2"}], "bones": [{"name": "root"}, {"parent": "root", "rotation": 90, "name": "girl", "length": 6.79, "x": -3.46, "y": 52.75}, {"parent": "girl", "rotation": -2.18, "name": "girl3", "length": 75.07, "x": 13.93, "y": 0.36}, {"parent": "girl3", "rotation": -0.11, "name": "girl2", "length": 26.81, "x": 77.61, "y": -0.98}, {"parent": "girl2", "rotation": 4.15, "name": "girl5", "length": 110.09, "x": 31.48, "y": -0.53}, {"parent": "girl5", "rotation": 6.27, "name": "girl4", "length": 8.66, "x": 116.09, "y": 1.63}, {"parent": "girl4", "rotation": -77.85, "name": "girl7", "length": 15.01, "x": 8.53, "y": -7.4}, {"parent": "girl5", "rotation": 179.16, "name": "mouth", "length": 4.82, "x": 14.86, "y": -1.87}, {"parent": "girl5", "rotation": 178.14, "name": "earrings_l", "length": 3.18, "x": 32.6, "y": 30.18}, {"parent": "earrings_l", "rotation": 1.89, "name": "earrings_l3", "length": 22.67, "x": 5.06, "y": 0.19}, {"parent": "earrings_l3", "rotation": -4.23, "name": "earrings_l2", "length": 9.18, "x": 23.63, "y": 0.53}, {"parent": "girl5", "rotation": 175.41, "name": "earrings_r", "length": 3.94, "x": 26.68, "y": -36.52}, {"parent": "earrings_r", "rotation": 4.11, "name": "earrings_r3", "length": 23.23, "x": 5.43, "y": 0.26}, {"parent": "earrings_r3", "rotation": 0.21, "name": "earrings_r2", "length": 6.75, "x": 24.74, "y": 0.34}, {"parent": "girl3", "rotation": 167.07, "name": "girl8", "length": 95.07, "x": 74.81, "y": 62.15}, {"parent": "girl8", "rotation": 82.92, "name": "girl6", "length": 50.22, "x": 101.17, "y": 3.23}, {"parent": "girl6", "rotation": 3.76, "name": "girl10", "length": 40.63, "x": 53.28, "y": 2.24}, {"parent": "girl3", "rotation": -161.53, "name": "girl11", "length": 111.24, "x": 77.26, "y": -66.35}, {"parent": "girl11", "rotation": -98.08, "name": "girl9", "length": 64.3, "x": 112.54, "y": -1.02}, {"parent": "girl9", "rotation": 5.19, "name": "girl13", "length": 19.81, "x": 67.67, "y": -2.34}, {"parent": "girl5", "name": "close_eyes", "x": 43.14, "y": -4.14}], "animations": {"animation": {"slots": {"close_eyes": {"attachment": [{"name": null, "time": 0}, {"name": "close_eyes", "time": 1.0667}, {"name": null, "time": 1.1333}]}}, "bones": {"earrings_r3": {"rotate": [{"angle": 0, "time": 0}, {"angle": -3.81, "time": 1}, {"angle": 0, "time": 2}, {"angle": -3.81, "time": 3}, {"angle": 0, "time": 4}]}, "mouth": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 0.864, "y": 1.109, "time": 1}, {"x": 1, "y": 1, "time": 2}, {"x": 0.864, "y": 1.109, "time": 3}, {"x": 1, "y": 1, "time": 4}], "translate": [{"time": 0}, {"x": 0.57, "time": 1}, {"time": 2}, {"x": 0.57, "time": 3}, {"time": 4}]}, "girl4": {"rotate": [{"angle": 0, "time": 0}, {"angle": -2.17, "time": 1}, {"angle": 0, "time": 2}, {"angle": -2.17, "time": 3}, {"angle": 0, "time": 4}]}, "earrings_l": {"rotate": [{"angle": 0, "time": 0}, {"angle": -3.14, "time": 1.1667}, {"angle": 0, "time": 2}, {"angle": -3.14, "time": 3.1667}, {"angle": 0, "time": 4}]}, "girl7": {"rotate": [{"angle": 0, "time": 0}, {"angle": -1.25, "time": 1}, {"angle": 0, "time": 2}, {"angle": -1.25, "time": 3}, {"angle": 0, "time": 4}]}, "girl3": {"scale": [{"x": 1, "y": 1, "time": 0}, {"x": 1.028, "y": 1, "time": 0.9}, {"x": 1, "y": 1, "time": 2}, {"x": 1.028, "y": 1, "time": 2.9}, {"x": 1, "y": 1, "time": 4}]}, "earrings_l3": {"rotate": [{"angle": 0, "time": 0}, {"angle": -8.32, "time": 1.1667}, {"angle": 0, "time": 2}, {"angle": -8.32, "time": 3.1667}, {"angle": 0, "time": 4}]}, "earrings_r": {"rotate": [{"angle": 0, "time": 0}, {"angle": -5.69, "time": 1}, {"angle": 0, "time": 2}, {"angle": -5.69, "time": 3}, {"angle": 0, "time": 4}]}}, "deform": {"default": {"mouth": {"mouth": [{"time": 0, "vertices": [-1.36305, -0.09862, -1.36305, -0.09862, -0.49388, -0.11415, 0, 0, 0, 0, 0, 0, 0, 0, -0.36882, 0.00659, -1.02797, 0.17114, -1.02797, 0.17114, -1.02797, 0.17114, -0.36882, 0.00659, 0, 0, 0, 0, 0, 0, -0.49388, -0.11415, -1.36305, -0.09862, -0.52245, -0.04159, 0, 0, 0, 0, -0.59839, -0.26506, -1.36305, -0.09862]}, {"time": 1, "vertices": [-1.4622, -0.05644, -1.4622, -0.05644, 0, 0, 1.29256, -0.088, 1.4715, -0.00709, 1.47144, -0.00708, 0.37856, 0, -1.01117, 0.31563, -2.09436, 0.54804, -2.09436, 0.54804, -2.09436, 0.54804, -1.44621, 0.4596, -0.61928, 0.08073, 0, 0, 0, 0, -1.37349, -0.05761, -1.4622, -0.05644, -0.45145, -0.11705, 1.21826, 0.00235, 1.29701, -0.00119, -0.07812, -0.20378, -1.4622, -0.05644]}, {"time": 2, "vertices": [-1.36305, -0.09862, -1.36305, -0.09862, -0.49388, -0.11415, 0, 0, 0, 0, 0, 0, 0, 0, -0.36882, 0.00659, -1.02797, 0.17114, -1.02797, 0.17114, -1.02797, 0.17114, -0.36882, 0.00659, 0, 0, 0, 0, 0, 0, -0.49388, -0.11415, -1.36305, -0.09862, -0.52245, -0.04159, 0, 0, 0, 0, -0.59839, -0.26506, -1.36305, -0.09862]}, {"time": 3, "vertices": [-1.4622, -0.05644, -1.4622, -0.05644, 0, 0, 1.29256, -0.088, 1.4715, -0.00709, 1.47144, -0.00708, 0.37856, 0, -1.01117, 0.31563, -2.09436, 0.54804, -2.09436, 0.54804, -2.09436, 0.54804, -1.44621, 0.4596, -0.61928, 0.08073, 0, 0, 0, 0, -1.37349, -0.05761, -1.4622, -0.05644, -0.45145, -0.11705, 1.21826, 0.00235, 1.29701, -0.00119, -0.07812, -0.20378, -1.4622, -0.05644]}, {"time": 4, "vertices": [-1.36305, -0.09862, -1.36305, -0.09862, -0.49388, -0.11415, 0, 0, 0, 0, 0, 0, 0, 0, -0.36882, 0.00659, -1.02797, 0.17114, -1.02797, 0.17114, -1.02797, 0.17114, -0.36882, 0.00659, 0, 0, 0, 0, 0, 0, -0.49388, -0.11415, -1.36305, -0.09862, -0.52245, -0.04159, 0, 0, 0, 0, -0.59839, -0.26506, -1.36305, -0.09862]}]}, "girl": {"girl": [{"time": 0}, {"time": 1, "vertices": [-1.22612, -0.4293, 0.09707, -1.29501, -1.80508, 0.36844, -0.82346, -1.64737, -1.87712, 1.94832, -2.36835, -1.30789, -1.77304, 0.5418, -0.98276, -1.57184, -1.47574, 0.37657, -0.74613, -1.32764, -1.47574, 0.37657, -0.74613, -1.32764, -0.41121, 0.49912, -0.58892, -0.26733, -1.47574, 0.37657, -0.74613, -1.32764, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.48508, -0.92028, -0.54993, -0.88317, -2.59781, -0.69813, -2.64203, -0.51024, -2.43628, -0.39137, -2.45853, -0.21588, -2.43628, -0.39137, -2.45853, -0.21588, -0.3024, 0.00948, -0.30103, 0.03114, 0.11502, -1.09501, 0.03607, -1.10036, -4.98286, 0.46802, -4.93671, 0.82353, -4.98286, 0.46802, -4.93671, 0.82353, -0.16681, -0.46251, -0.19965, -0.44934, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.26257, -1.51958, -1.36829, -1.42527, -1.26257, -1.51958, -1.36829, -1.42527, -1.26257, -1.51958, -1.36829, -1.42527, 0, 0, 0, 0, 0, 0, 0, 0, -1.63811, 0.83095, -1.57462, 0.94612, -1.63811, 0.83095, -1.57462, 0.94612, -3.03488, 2.97706, -1.39471, 2.65089, 0, 0, 0, 0, 0, 0, 0, 0, -1.25394, -0.34039, 0.09707, -1.29501, 0.59768, -2.8714, 1.63303, -4.29382, 2.02834, -5.68005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.20388, -0.36608, 1.21702, -0.31999, 1.21759, -0.31767, -0.63493, -1.0863, -0.53438, -1.13941, -1.05264, 0.68903, 4.62304, -0.25547, 4.57776, -0.07931, 4.57808, -0.07059, -1.30544, -4.38842, -0.90359, -4.48853, -4.31679, 1.5257, 6.09021, -0.61635, 6.0572, -0.38407, 6.05811, -0.37254, -1.99621, -5.73173, -1.4701, -5.88876, -5.6234, 2.28352, 2.82626, -0.148, 2.82666, -0.14262, -0.90136, -2.68279, -2.63374, 1.03592, 2.82626, -0.148, 2.82666, -0.14262, -0.90136, -2.68279, -2.63374, 1.03592, 2.82626, -0.148, -2.78761, -0.48804, -0.64902, 2.75479, 4.3057, -0.45278, 4.30672, -0.44458, -4.29771, -0.52197, -0.77055, 4.26039, 3.42959, -0.12186, -3.36969, -0.64848, -1.05875, 3.26425, -0.84306, 3.32665, 0.83709, -1.46645, 0.89238, -1.43352, -1.19018, 1.19763, 1.04191, 1.32894, 1.12642, 1.25785, 7.43212, -2.37077, 7.39162, -2.08613, -7.67076, 0.37964, -0.56872, 7.65918, -0.06611, 7.68001, 6.71037, -2.73168, 6.70438, -2.47426, -7.08773, 0.91168, 0.03113, 7.1462, 0.49883, 7.12886, 3.13466, -0.80855, -1.62042, -2.8024, -1.36067, -2.93751, -2.71672, 1.76006, 6.34948, 1.23799, 6.20727, 1.4788, -0.24194, -6.37634, 0.33532, -6.37227, -6.35577, 0.56393, 11.04094, -0.20552, 10.81454, 0.21494, 10.81421, 0.23549, -2.69637, -10.47513, -1.73862, -10.67609, -10.32521, 3.22251]}, {"time": 2}, {"time": 3, "vertices": [-1.22612, -0.4293, 0.09707, -1.29501, -1.80508, 0.36844, -0.82346, -1.64737, -1.87712, 1.94832, -2.36835, -1.30789, -1.77304, 0.5418, -0.98276, -1.57184, -1.47574, 0.37657, -0.74613, -1.32764, -1.47574, 0.37657, -0.74613, -1.32764, -0.41121, 0.49912, -0.58892, -0.26733, -1.47574, 0.37657, -0.74613, -1.32764, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, -1.32361, 0.18245, -0.51905, -1.2308, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -0.48508, -0.92028, -0.54993, -0.88317, -2.59781, -0.69813, -2.64203, -0.51024, -2.43628, -0.39137, -2.45853, -0.21588, -2.43628, -0.39137, -2.45853, -0.21588, -0.3024, 0.00948, -0.30103, 0.03114, 0.11502, -1.09501, 0.03607, -1.10036, -4.98286, 0.46802, -4.93671, 0.82353, -4.98286, 0.46802, -4.93671, 0.82353, -0.16681, -0.46251, -0.19965, -0.44934, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -1.26257, -1.51958, -1.36829, -1.42527, -1.26257, -1.51958, -1.36829, -1.42527, -1.26257, -1.51958, -1.36829, -1.42527, 0, 0, 0, 0, 0, 0, 0, 0, -1.63811, 0.83095, -1.57462, 0.94612, -1.63811, 0.83095, -1.57462, 0.94612, -3.03488, 2.97706, -1.39471, 2.65089, 0, 0, 0, 0, 0, 0, 0, 0, -1.25394, -0.34039, 0.09707, -1.29501, 0.59768, -2.8714, 1.63303, -4.29382, 2.02834, -5.68005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.20388, -0.36608, 1.21702, -0.31999, 1.21759, -0.31767, -0.63493, -1.0863, -0.53438, -1.13941, -1.05264, 0.68903, 4.62304, -0.25547, 4.57776, -0.07931, 4.57808, -0.07059, -1.30544, -4.38842, -0.90359, -4.48853, -4.31679, 1.5257, 6.09021, -0.61635, 6.0572, -0.38407, 6.05811, -0.37254, -1.99621, -5.73173, -1.4701, -5.88876, -5.6234, 2.28352, 2.82626, -0.148, 2.82666, -0.14262, -0.90136, -2.68279, -2.63374, 1.03592, 2.82626, -0.148, 2.82666, -0.14262, -0.90136, -2.68279, -2.63374, 1.03592, 2.82626, -0.148, -2.78761, -0.48804, -0.64902, 2.75479, 4.3057, -0.45278, 4.30672, -0.44458, -4.29771, -0.52197, -0.77055, 4.26039, 3.42959, -0.12186, -3.36969, -0.64848, -1.05875, 3.26425, -0.84306, 3.32665, 0.83709, -1.46645, 0.89238, -1.43352, -1.19018, 1.19763, 1.04191, 1.32894, 1.12642, 1.25785, 7.43212, -2.37077, 7.39162, -2.08613, -7.67076, 0.37964, -0.56872, 7.65918, -0.06611, 7.68001, 6.71037, -2.73168, 6.70438, -2.47426, -7.08773, 0.91168, 0.03113, 7.1462, 0.49883, 7.12886, 3.13466, -0.80855, -1.62042, -2.8024, -1.36067, -2.93751, -2.71672, 1.76006, 6.34948, 1.23799, 6.20727, 1.4788, -0.24194, -6.37634, 0.33532, -6.37227, -6.35577, 0.56393, 11.04094, -0.20552, 10.81454, 0.21494, 10.81421, 0.23549, -2.69637, -10.47513, -1.73862, -10.67609, -10.32521, 3.22251]}, {"time": 4}]}}}}}}, [0]]], 0, 0, [0], [-1], [134]], [[{"name": "BG", "rect": [0, 0, 1280, 768], "offset": [0, 0], "originalSize": [1280, 768], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [135]], [[{"name": "D_Ho_Disable", "rect": [0, 0, 490, 252], "offset": [0, 0], "originalSize": [490, 252], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [136]], [[[45, "card-ho", 0.16666666666666666, 0.2, "54", [{}, "paths", 11, [{}, "nodeCardHo", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]], [{"frame": 0.08333333333333333}, "value", 8, [0, 1.2, 1.2]], [{"frame": 0.16666666666666666}, "value", 8, [0, 1, 1]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[{"name": "<PERSON>_<PERSON>_choose", "rect": [0, 0, 142, 146], "offset": [0, 0.5], "originalSize": [142, 147], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [137]], [[{"name": "D_Hoa_Disable", "rect": [0, 0, 198, 231], "offset": [0, 0], "originalSize": [198, 231], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [138]], [[[37, "cardburn-move-box", ".mp3", 1.48898], -1], 0, 0, [], [], []], [[{"name": "ho_money", "rect": [0, 0, 227, 54], "offset": [0, 0], "originalSize": [227, 54], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [139]], [[{"name": "D_Rong", "rect": [0, 0, 250, 257], "offset": [0, 0], "originalSize": [250, 257], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [140]], [[{"name": "D_Hoa_win", "rect": [0, 0, 192, 219], "offset": [0, 0], "originalSize": [192, 219], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [141]], [[{"name": "fold", "rect": [0, 0, 49, 62], "offset": [0, 0], "originalSize": [49, 62], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [142]], [[{"name": "ava", "rect": [0, 0, 152, 152], "offset": [0, 0], "originalSize": [152, 152], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [143]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [0, 0, 95, 100], "offset": [0, 0], "originalSize": [95, 100], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [144]], [[[45, "card-hoa", 0.16666666666666666, 0.2, "54", [{}, "paths", 11, [{}, "nodeCardHo", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]], [{"frame": 0.08333333333333333}, "value", 8, [0, 1.2, 1.2]], [{"frame": 0.16666666666666666}, "value", 8, [0, 1, 1]]], 11, 11, 11]]], "nodeCardRong", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]], [{"frame": 0.08333333333333333}, "value", 8, [0, 1.2, 1.2]], [{"frame": 0.16666666666666666}, "value", 8, [0, 1, 1]]], 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[50, "countTime-animation", 0.16666666666666666, "1", [{}, "paths", 11, [{}, "lbTime", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 5, 5]], [{"frame": 0.06666666666666667}, "value", 8, [0, 1, 1]]], 11, 11]]]]]]], 0, 0, [], [], []], [[{"name": "rong_money", "rect": [0, 0, 227, 54], "offset": [0, 0], "originalSize": [227, 54], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [145]], [[[37, "card-slide-burn", ".mp3", 0.470125], -1], 0, 0, [], [], []], [[{"name": "btn_menu", "rect": [0, 0, 81, 82], "offset": [0, 0], "originalSize": [81, 82], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [146]], [[[50, "session-history", 0.6166666666666667, "2", [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 1, 1]], [{"frame": 0.3333333333333333}, "value", 8, [0, 1.1, 1.1]], [{"frame": 0.6166666666666667}, "value", 8, [0, 1, 1]]], 11, 11, 11]]]]], 0, 0, [], [], []], [[[63, "coin", 0.3333333333333333, "0", {"paths": {"nodeMoney/coin": {"props": {"y": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 10}, {"frame": 0.3333333333333333, "value": 0}]}}}}]], 0, 0, [], [], []], [[[44, "time-animation", 0.25, [{}, "paths", 11, [{}, "timeGroup/bg_time/nodeTimeBetting/lbTime", 11, [{}, "props", 11, [{"opacity": [{"frame": 0, "value": 0}, {"frame": 0.25, "value": 255}]}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 5, 5]], [{"frame": 0.08333333333333333}, "value", 8, [0, 1, 1]]], 11, 11]]]]]]], 0, 0, [], [], []], [[{"name": "barChuaChip", "rect": [0, 0, 735, 74], "offset": [0, 0], "originalSize": [735, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [4], [147]]]]