[1, ["76XIlclNBJFY6dbyGQN/Jp", "017Jn3Zv1Ft7hygdjpaSoK", "ec6DsHDq5J8b7LSwoBiKOV", "adw94Z+hpN57wutNivq8Q5", "eaB9O5GzlIa69oqw8Eg2yN", "694v/Gi1RLJ44dz2e07uzg", "d8slDZK7xIXajoS5Ut36UN", "deewEDvg1HJZMQDCQZnyvr", "cabx6nr5tGhZhywbWZ9lb8", "8fEAcrlmNMfqybOVsE//K0", "b4Mpyyq49CyIL58JvOtPVa", "58fGnqgQ9PS49TiZwpjN0h", "a9VpD0DP5LJYQPXITZq+uj", "53aGdrE3BHrImFrErAB8gp", "a0q5iYU5VEwYKuFitrZXRU", "6b+3cJWI9Ac4EQcctYA/NO", "293EMboV5EXpNloJcordjA", "09pgvO7T5Nsoc7v9Hhg6Np", "beKaWy57FOe6/pttXvlyq8", "44F5RqzeBAOYwVwVHHVTHG"], ["node", "_N$skeletonData", "_spriteFrame", "_N$file", "_N$target", "_textureSetter", "_parent", "root", "nodeNormalChat", "nodeEmotion", "btnSendChat", "editBoxChat", "chatListView", "spriteVIP", "lbMessage", "lbName", "lbSID", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_components", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint", "_color"], 0, 4, 9, 5, 7, 1, 2, 5, 5], "cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_anchorPoint", "_children", "_color"], 1, 2, 4, 5, 7, 1, 5, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "_spriteFrame"], 0, 1, 6], ["cc.Label", ["_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$verticalAlign", "_string", "_N$overflow", "_N$horizontalAlign", "node", "_N$file"], -5, 1, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.<PERSON><PERSON>", ["zoomScale", "_N$transition", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor"], 1, 1, 9, 1, 5, 5, 5], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 0], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["<PERSON><PERSON>", ["vertical", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "horizontal", "node", "_N$content"], -3, 1, 1], ["sp.SkeletonData", ["_name", "_atlasText", "textureNames", "_skeleton<PERSON><PERSON>", "textures"], -1, 3], ["cc.Prefab", ["_name"], 2], ["4ffb0Clea9C6rf5rVISctZ3", ["node", "chatListView", "editBoxChat", "btnSendChat", "nodeEmotion", "nodeNormalChat"], 3, 1, 1, 1, 1, 1, 1], ["cc.Animation", ["node", "_clips"], 3, 1, 3], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_N$skeletonData"], -1, 1, 6], ["ecc4coyMxJC/JyRtGdKkYJu", ["node", "lbSID", "lbName", "lbMessage", "spriteVIP"], 3, 1, 1, 1, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1], ["f1a89My30pBc6s/NqdlZI8u", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["cc.EditBox", ["_N$backgroundImage", "_N$returnType", "_N$fontSize", "_N$lineHeight", "_N$placeholder", "_N$placeholderFontSize", "_N$maxLength", "_N$inputMode", "node", "_N$fontColor", "_N$placeholderFontColor", "editingReturn"], -5, 1, 8, 8, 9]], [[8, 0, 1, 2], [0, 0, 7, 4, 3, 5, 6, 2], [6, 0, 1, 2, 3, 5, 4, 3], [9, 0, 1, 2, 3, 4], [15, 0, 1, 2, 3, 4, 5, 5], [3, 1, 0, 3, 4, 3], [0, 0, 7, 4, 3, 10, 5, 6, 2], [4, 5, 0, 1, 2, 3, 7, 4, 8, 9, 8], [9, 0, 1, 3, 3], [7, 0, 1, 2, 4], [0, 0, 7, 8, 4, 3, 5, 6, 2], [0, 0, 8, 4, 3, 5, 9, 6, 2], [0, 0, 7, 8, 3, 6, 2], [0, 0, 7, 8, 4, 3, 5, 2], [2, 0, 8, 2, 3, 4, 5, 2], [5, 1, 0, 2, 5, 6, 4], [6, 0, 1, 2, 3, 6, 7, 4, 3], [3, 0, 3, 4, 2], [3, 0, 2, 3, 4, 3], [18, 0, 1, 2], [4, 5, 0, 1, 2, 3, 7, 4, 8, 8], [7, 0, 2], [11, 0, 1, 2, 3, 4, 5], [12, 0, 2], [0, 0, 8, 4, 3, 9, 6, 2], [0, 0, 1, 8, 4, 3, 5, 6, 3], [0, 0, 1, 2, 7, 8, 3, 5, 9, 6, 4], [0, 0, 8, 4, 3, 5, 9, 2], [0, 0, 2, 7, 4, 3, 5, 6, 3], [0, 0, 7, 4, 3, 5, 9, 2], [0, 0, 7, 4, 3, 5, 9, 6, 2], [0, 0, 7, 4, 3, 5, 2], [0, 0, 1, 7, 4, 3, 5, 9, 6, 3], [0, 0, 7, 4, 3, 10, 5, 9, 6, 2], [2, 0, 6, 8, 2, 3, 4, 5, 2], [2, 0, 6, 2, 3, 9, 4, 7, 2], [2, 0, 6, 2, 3, 9, 4, 7, 5, 2], [2, 0, 1, 6, 2, 3, 4, 5, 3], [2, 0, 6, 2, 3, 4, 7, 5, 2], [13, 0, 1, 2, 3, 4, 5, 1], [14, 0, 1, 1], [8, 1, 1], [5, 3, 1, 0, 2, 4, 5, 6, 6], [5, 3, 0, 2, 4, 5, 6, 5], [5, 1, 0, 5, 6, 3], [6, 2, 3, 5, 4, 1], [16, 0, 1, 2, 3, 4, 1], [3, 3, 1], [3, 1, 0, 3, 3], [17, 0, 1], [10, 0, 1, 2, 3, 4, 6, 7, 6], [10, 5, 6, 7, 2], [4, 5, 0, 1, 2, 3, 4, 6, 8, 8], [4, 0, 1, 2, 3, 4, 6, 8, 9, 7], [4, 5, 0, 1, 2, 3, 4, 6, 8, 9, 8], [19, 0, 1, 2, 3, 4, 3], [20, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 9]], [[[[9, "showChatRoom", 0.16666666666666666, {"paths": {"offset-chat": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 0.16666666666666666, "value": true}], "x": [{"frame": 0, "value": -535}, {"frame": 0.16666666666666666, "value": 0}], "opacity": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 255}]}}}}]], 0, 0, [], [], []], [[{"name": "btn_send", "rect": [0, 0, 82, 46], "offset": [0, 0], "originalSize": [82, 46], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [4]], [[{"name": "V1", "rect": [0, 0, 30, 28], "offset": [0, 0], "originalSize": [30, 28], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [5]], [[{"name": "bgchat", "rect": [0, 0, 535, 720], "offset": [0, 0], "originalSize": [535, 720], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [6]], [[[21, "hideBubbleChat"]], 0, 0, [], [], []], [[{"name": "btn_emo", "rect": [0, 0, 50, 50], "offset": [0, 0], "originalSize": [50, 50], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [5], [7]], [[[9, "showBubbleChat", 2.683333333333333, {"paths": {"chat": {"props": {"active": [{"frame": 0, "value": true}, {"frame": 2.5166666666666666, "value": true}, {"frame": 2.683333333333333, "value": false}], "opacity": [{"frame": 0, "value": 255}, {"frame": 2.5166666666666666, "value": 255}, {"frame": 2.683333333333333, "value": 0}], "y": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": 10}, {"frame": 0.3333333333333333, "value": 0}], "scaleX": [{"frame": 0, "value": 1}, {"frame": 0.08333333333333333, "value": 1.2}, {"frame": 0.16666666666666666, "value": 1}], "scaleY": [{"frame": 0, "value": 1}, {"frame": 0.16666666666666666, "value": 1.2}, {"frame": 0.25, "value": 1}]}}}}]], 0, 0, [], [], []], [[[22, "Emotion-1", "\nEmotion-1.png\nsize: 1024,512\nformat: RGBA8888\nfilter: Linear,Linear\nrepeat: none\nEmotion/1-Buon/1\n  rotate: false\n  xy: 2, 275\n  size: 123, 110\n  orig: 123, 110\n  offset: 0, 0\n  index: -1\nEmotion/2-Ngac<PERSON>hien/1\n  rotate: false\n  xy: 2, 275\n  size: 123, 110\n  orig: 123, 110\n  offset: 0, 0\n  index: -1\nEmotion/1-Buon/2\n  rotate: true\n  xy: 360, 397\n  size: 43, 20\n  orig: 43, 20\n  offset: 0, 0\n  index: -1\nEmotion/1-Buon/3\n  rotate: true\n  xy: 221, 108\n  size: 32, 14\n  orig: 32, 14\n  offset: 0, 0\n  index: -1\nEmotion/1-Buon/4\n  rotate: false\n  xy: 441, 327\n  size: 30, 17\n  orig: 30, 17\n  offset: 0, 0\n  index: -1\nEmotion/1-Buon/5\n  rotate: false\n  xy: 110, 109\n  size: 30, 18\n  orig: 30, 18\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/2\n  rotate: false\n  xy: 426, 346\n  size: 45, 45\n  orig: 45, 45\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/3\n  rotate: false\n  xy: 377, 245\n  size: 42, 44\n  orig: 42, 44\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/4\n  rotate: false\n  xy: 800, 403\n  size: 45, 45\n  orig: 45, 45\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/5\n  rotate: false\n  xy: 330, 244\n  size: 45, 45\n  orig: 45, 45\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/6\n  rotate: false\n  xy: 217, 87\n  size: 18, 19\n  orig: 18, 19\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/7\n  rotate: false\n  xy: 779, 388\n  size: 19, 18\n  orig: 19, 18\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/8\n  rotate: true\n  xy: 171, 80\n  size: 24, 16\n  orig: 24, 16\n  offset: 0, 0\n  index: -1\nEmotion/10-Bom/9\n  rotate: true\n  xy: 473, 339\n  size: 24, 16\n  orig: 24, 16\n  offset: 0, 0\n  index: -1\nEmotion/11-Dep Trai/2\n  rotate: false\n  xy: 135, 422\n  size: 125, 88\n  orig: 125, 88\n  offset: 0, 0\n  index: -1\nEmotion/11-Dep Trai/3\n  rotate: true\n  xy: 88, 109\n  size: 90, 20\n  orig: 90, 20\n  offset: 0, 0\n  index: -1\nEmotion/11-Khoc/2\n  rotate: false\n  xy: 345, 291\n  size: 27, 41\n  orig: 27, 41\n  offset: 0, 0\n  index: -1\nEmotion/11-Khoc/3\n  rotate: false\n  xy: 413, 291\n  size: 24, 36\n  orig: 24, 36\n  offset: 0, 0\n  index: -1\nEmotion/11-Khoc/4\n  rotate: false\n  xy: 206, 308\n  size: 55, 80\n  orig: 55, 80\n  offset: 0, 0\n  index: -1\nEmotion/11-Khoc/5\n  rotate: false\n  xy: 701, 399\n  size: 26, 10\n  orig: 26, 10\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/1\n  rotate: false\n  xy: 2, 387\n  size: 131, 123\n  orig: 131, 123\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/2\n  rotate: true\n  xy: 435, 393\n  size: 48, 45\n  orig: 48, 45\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/3\n  rotate: false\n  xy: 263, 333\n  size: 68, 62\n  orig: 68, 62\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/4-1\n  rotate: false\n  xy: 382, 395\n  size: 51, 46\n  orig: 51, 46\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/4-2\n  rotate: false\n  xy: 238, 150\n  size: 39, 39\n  orig: 39, 39\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/5-1\n  rotate: false\n  xy: 374, 291\n  size: 37, 36\n  orig: 37, 36\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/5-2\n  rotate: false\n  xy: 344, 153\n  size: 34, 34\n  orig: 34, 34\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/6\n  rotate: false\n  xy: 892, 411\n  size: 48, 12\n  orig: 48, 12\n  offset: 0, 0\n  index: -1\nEmotion/13-Boc Khoi/7\n  rotate: false\n  xy: 101, 2\n  size: 34, 31\n  orig: 34, 31\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/1\n  rotate: false\n  xy: 474, 444\n  size: 112, 20\n  orig: 112, 20\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/3\n  rotate: false\n  xy: 677, 402\n  size: 22, 22\n  orig: 22, 22\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/4\n  rotate: false\n  xy: 163, 56\n  size: 22, 22\n  orig: 22, 22\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/5-1\n  rotate: true\n  xy: 588, 428\n  size: 34, 87\n  orig: 34, 87\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/5-2\n  rotate: false\n  xy: 82, 35\n  size: 46, 72\n  orig: 46, 72\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/6-2\n  rotate: true\n  xy: 263, 291\n  size: 40, 80\n  orig: 40, 80\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/7\n  rotate: false\n  xy: 126, 133\n  size: 55, 6\n  orig: 55, 6\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/8\n  rotate: false\n  xy: 288, 132\n  size: 14, 16\n  orig: 14, 16\n  offset: 0, 0\n  index: -1\nEmotion/14-Khoc Thet/9\n  rotate: true\n  xy: 473, 322\n  size: 15, 16\n  orig: 15, 16\n  offset: 0, 0\n  index: -1\nEmotion/15-Choang/2\n  rotate: false\n  xy: 262, 442\n  size: 108, 68\n  orig: 108, 68\n  offset: 0, 0\n  index: -1\nEmotion/15-Choang/3\n  rotate: false\n  xy: 189, 195\n  size: 53, 51\n  orig: 53, 51\n  offset: 0, 0\n  index: -1\nEmotion/15-Choang/4\n  rotate: false\n  xy: 185, 108\n  size: 34, 32\n  orig: 34, 32\n  offset: 0, 0\n  index: -1\nEmotion/15-Choang/5\n  rotate: false\n  xy: 183, 142\n  size: 53, 51\n  orig: 53, 51\n  offset: 0, 0\n  index: -1\nEmotion/15-Choang/6\n  rotate: true\n  xy: 729, 387\n  size: 22, 21\n  orig: 22, 21\n  offset: 0, 0\n  index: -1\nEmotion/15-Choang/7\n  rotate: false\n  xy: 261, 191\n  size: 42, 40\n  orig: 42, 40\n  offset: 0, 0\n  index: -1\nEmotion/15-Choang/8\n  rotate: false\n  xy: 828, 385\n  size: 17, 16\n  orig: 17, 16\n  offset: 0, 0\n  index: -1\nEmotion/16-Nhin Deu/3\n  rotate: false\n  xy: 391, 185\n  size: 40, 16\n  orig: 40, 16\n  offset: 0, 0\n  index: -1\nEmotion/16-Nhin Deu/4\n  rotate: true\n  xy: 439, 291\n  size: 34, 14\n  orig: 34, 14\n  offset: 0, 0\n  index: -1\nEmotion/16-Nhin Deu/5\n  rotate: true\n  xy: 127, 297\n  size: 88, 6\n  orig: 88, 6\n  offset: 0, 0\n  index: -1\nEmotion/16-Nhin Deu/6\n  rotate: false\n  xy: 482, 395\n  size: 46, 47\n  orig: 46, 47\n  offset: 0, 0\n  index: -1\nEmotion/17-WTH/10\n  rotate: false\n  xy: 2, 115\n  size: 84, 84\n  orig: 84, 84\n  offset: 0, 0\n  index: -1\nEmotion/17-WTH/2\n  rotate: false\n  xy: 372, 443\n  size: 100, 67\n  orig: 100, 67\n  offset: 0, 0\n  index: -1\nEmotion/17-WTH/3\n  rotate: false\n  xy: 135, 390\n  size: 107, 30\n  orig: 107, 30\n  offset: 0, 0\n  index: -1\nEmotion/17-WTH/6\n  rotate: true\n  xy: 640, 398\n  size: 28, 35\n  orig: 28, 35\n  offset: 0, 0\n  index: -1\nEmotion/17-WTH/7\n  rotate: true\n  xy: 421, 247\n  size: 42, 20\n  orig: 42, 20\n  offset: 0, 0\n  index: -1\nEmotion/17-WTH/8\n  rotate: true\n  xy: 752, 388\n  size: 18, 25\n  orig: 18, 25\n  offset: 0, 0\n  index: -1\nEmotion/17-WTH/9\n  rotate: false\n  xy: 514, 372\n  size: 19, 21\n  orig: 19, 21\n  offset: 0, 0\n  index: -1\nEmotion/2-NgacNhien/2\n  rotate: false\n  xy: 279, 150\n  size: 23, 39\n  orig: 23, 39\n  offset: 0, 0\n  index: -1\nEmotion/2-NgacNhien/3\n  rotate: false\n  xy: 237, 121\n  size: 23, 14\n  orig: 23, 14\n  offset: 0, 0\n  index: -1\nEmotion/2-NgacNhien/4\n  rotate: false\n  xy: 264, 134\n  size: 22, 14\n  orig: 22, 14\n  offset: 0, 0\n  index: -1\nEmotion/2-NgacNhien/5\n  rotate: true\n  xy: 455, 292\n  size: 33, 14\n  orig: 33, 14\n  offset: 0, 0\n  index: -1\nEmotion/2-NgacNhien/6\n  rotate: true\n  xy: 443, 254\n  size: 35, 13\n  orig: 35, 13\n  offset: 0, 0\n  index: -1\nEmotion/20-burn_joss_stick/2\n  rotate: false\n  xy: 458, 246\n  size: 16, 15\n  orig: 16, 15\n  offset: 0, 0\n  index: -1\nEmotion/20-burn_joss_stick/3\n  rotate: false\n  xy: 323, 133\n  size: 16, 17\n  orig: 16, 17\n  offset: 0, 0\n  index: -1\nEmotion/20-burn_joss_stick/4\n  rotate: false\n  xy: 990, 459\n  size: 28, 6\n  orig: 28, 6\n  offset: 0, 0\n  index: -1\nEmotion/20-burn_joss_stick/5\n  rotate: false\n  xy: 847, 404\n  size: 28, 6\n  orig: 28, 6\n  offset: 0, 0\n  index: -1\nEmotion/20-burn_joss_stick/6\n  rotate: true\n  xy: 376, 329\n  size: 15, 63\n  orig: 15, 63\n  offset: 0, 0\n  index: -1\nEmotion/20-burn_joss_stick/7\n  rotate: true\n  xy: 330, 233\n  size: 9, 53\n  orig: 9, 53\n  offset: 0, 0\n  index: -1\nEmotion/21-baffle/2\n  rotate: true\n  xy: 458, 263\n  size: 27, 21\n  orig: 27, 21\n  offset: 0, 0\n  index: -1\nEmotion/21-baffle/3\n  rotate: true\n  xy: 435, 157\n  size: 26, 19\n  orig: 26, 19\n  offset: 0, 0\n  index: -1\nEmotion/21-baffle/4\n  rotate: false\n  xy: 2, 2\n  size: 52, 31\n  orig: 52, 31\n  offset: 0, 0\n  index: -1\nEmotion/22-cooll/10\n  rotate: true\n  xy: 135, 251\n  size: 63, 65\n  orig: 63, 65\n  offset: 0, 0\n  index: -1\nEmotion/22-cooll/2\n  rotate: true\n  xy: 244, 391\n  size: 29, 16\n  orig: 29, 16\n  offset: 0, 0\n  index: -1\nEmotion/22-cooll/3\n  rotate: true\n  xy: 430, 204\n  size: 41, 21\n  orig: 41, 21\n  offset: 0, 0\n  index: -1\nEmotion/22-cooll/4\n  rotate: true\n  xy: 137, 3\n  size: 30, 17\n  orig: 30, 17\n  offset: 0, 0\n  index: -1\nEmotion/22-cooll/5\n  rotate: true\n  xy: 677, 426\n  size: 36, 22\n  orig: 36, 22\n  offset: 0, 0\n  index: -1\nEmotion/22-cooll/6\n  rotate: false\n  xy: 579, 399\n  size: 59, 27\n  orig: 59, 27\n  offset: 0, 0\n  index: -1\nEmotion/22-cooll/9\n  rotate: false\n  xy: 202, 248\n  size: 59, 58\n  orig: 59, 58\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/10\n  rotate: false\n  xy: 2, 35\n  size: 78, 78\n  orig: 78, 78\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/2\n  rotate: true\n  xy: 847, 412\n  size: 36, 43\n  orig: 36, 43\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/3\n  rotate: false\n  xy: 135, 316\n  size: 69, 72\n  orig: 69, 72\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/5\n  rotate: true\n  xy: 130, 35\n  size: 12, 24\n  orig: 12, 24\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/6\n  rotate: true\n  xy: 800, 388\n  size: 13, 26\n  orig: 13, 26\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/7\n  rotate: false\n  xy: 262, 397\n  size: 96, 43\n  orig: 96, 43\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/8\n  rotate: false\n  xy: 892, 425\n  size: 87, 30\n  orig: 87, 30\n  offset: 0, 0\n  index: -1\nEmotion/27-dribble/9\n  rotate: false\n  xy: 305, 189\n  size: 41, 42\n  orig: 41, 42\n  offset: 0, 0\n  index: -1\nEmotion/3-Chao/2\n  rotate: false\n  xy: 238, 137\n  size: 24, 11\n  orig: 24, 11\n  offset: 0, 0\n  index: -1\nEmotion/3-Chao/3\n  rotate: false\n  xy: 262, 121\n  size: 22, 11\n  orig: 22, 11\n  offset: 0, 0\n  index: -1\nEmotion/3-Chao/4\n  rotate: false\n  xy: 800, 450\n  size: 90, 60\n  orig: 90, 60\n  offset: 0, 0\n  index: -1\nEmotion/3-Chao/5\n  rotate: true\n  xy: 433, 185\n  size: 17, 21\n  orig: 17, 21\n  offset: 0, 0\n  index: -1\nEmotion/3-Chao/6\n  rotate: false\n  xy: 535, 374\n  size: 18, 21\n  orig: 18, 21\n  offset: 0, 0\n  index: -1\nEmotion/3-Chao/7\n  rotate: false\n  xy: 333, 334\n  size: 41, 61\n  orig: 41, 61\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/2\n  rotate: false\n  xy: 237, 81\n  size: 13, 12\n  orig: 13, 12\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/3\n  rotate: false\n  xy: 142, 106\n  size: 41, 25\n  orig: 41, 25\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/4\n  rotate: false\n  xy: 252, 105\n  size: 21, 14\n  orig: 21, 14\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/5\n  rotate: true\n  xy: 473, 365\n  size: 26, 17\n  orig: 26, 17\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/6\n  rotate: false\n  xy: 752, 408\n  size: 46, 41\n  orig: 46, 41\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/7\n  rotate: true\n  xy: 391, 203\n  size: 40, 37\n  orig: 40, 37\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/8\n  rotate: true\n  xy: 942, 382\n  size: 41, 37\n  orig: 41, 37\n  offset: 0, 0\n  index: -1\nEmotion/33-tire/9\n  rotate: false\n  xy: 304, 152\n  size: 38, 35\n  orig: 38, 35\n  offset: 0, 0\n  index: -1\nEmotion/4-Hut Thuoc/2\n  rotate: false\n  xy: 244, 198\n  size: 15, 48\n  orig: 15, 48\n  offset: 0, 0\n  index: -1\nEmotion/4-Hut Thuoc/3\n  rotate: false\n  xy: 56, 2\n  size: 43, 31\n  orig: 43, 31\n  offset: 0, 0\n  index: -1\nEmotion/4-Hut Thuoc/4\n  rotate: false\n  xy: 189, 87\n  size: 26, 19\n  orig: 26, 19\n  offset: 0, 0\n  index: -1\nEmotion/4-Hut Thuoc/5\n  rotate: false\n  xy: 474, 466\n  size: 123, 44\n  orig: 123, 44\n  offset: 0, 0\n  index: -1\nEmotion/4-Hut Thuoc/6\n  rotate: true\n  xy: 275, 99\n  size: 20, 14\n  orig: 20, 14\n  offset: 0, 0\n  index: -1\nEmotion/4-Hut Thuoc/7\n  rotate: false\n  xy: 252, 90\n  size: 21, 13\n  orig: 21, 13\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/2\n  rotate: false\n  xy: 130, 81\n  size: 39, 23\n  orig: 39, 23\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/3\n  rotate: false\n  xy: 599, 464\n  size: 100, 46\n  orig: 100, 46\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/4\n  rotate: false\n  xy: 304, 133\n  size: 17, 17\n  orig: 17, 17\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/5-1\n  rotate: false\n  xy: 126, 141\n  size: 55, 54\n  orig: 55, 54\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/5-2\n  rotate: false\n  xy: 492, 372\n  size: 20, 21\n  orig: 20, 21\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/6\n  rotate: false\n  xy: 471, 304\n  size: 16, 16\n  orig: 16, 16\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/7\n  rotate: false\n  xy: 701, 411\n  size: 49, 38\n  orig: 49, 38\n  offset: 0, 0\n  index: -1\nEmotion/5-Dang Yeu/8\n  rotate: true\n  xy: 990, 467\n  size: 43, 32\n  orig: 43, 32\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/10\n  rotate: true\n  xy: 110, 129\n  size: 70, 14\n  orig: 70, 14\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/11\n  rotate: true\n  xy: 109, 201\n  size: 72, 17\n  orig: 72, 17\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/2\n  rotate: false\n  xy: 2, 201\n  size: 105, 72\n  orig: 105, 72\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/3\n  rotate: true\n  xy: 981, 370\n  size: 85, 41\n  orig: 85, 41\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/4\n  rotate: false\n  xy: 130, 49\n  size: 31, 30\n  orig: 31, 30\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/5\n  rotate: false\n  xy: 453, 216\n  size: 12, 11\n  orig: 12, 11\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/6\n  rotate: true\n  xy: 348, 189\n  size: 42, 41\n  orig: 42, 41\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/7\n  rotate: false\n  xy: 453, 229\n  size: 16, 15\n  orig: 16, 15\n  offset: 0, 0\n  index: -1\nEmotion/6-Soc/9\n  rotate: false\n  xy: 237, 95\n  size: 13, 24\n  orig: 13, 24\n  offset: 0, 0\n  index: -1\nEmotion/7-Phan No/2\n  rotate: false\n  xy: 530, 397\n  size: 47, 45\n  orig: 47, 45\n  offset: 0, 0\n  index: -1\nEmotion/7-Phan No/3\n  rotate: false\n  xy: 376, 346\n  size: 48, 47\n  orig: 48, 47\n  offset: 0, 0\n  index: -1\nEmotion/7-Phan No/4\n  rotate: false\n  xy: 555, 377\n  size: 20, 18\n  orig: 20, 18\n  offset: 0, 0\n  index: -1\nEmotion/7-Phan No/5\n  rotate: false\n  xy: 128, 197\n  size: 59, 52\n  orig: 59, 52\n  offset: 0, 0\n  index: -1\nEmotion/7-Phan No/6\n  rotate: false\n  xy: 263, 233\n  size: 65, 56\n  orig: 65, 56\n  offset: 0, 0\n  index: -1\nEmotion/9-Gao Thet/2-1\n  rotate: false\n  xy: 380, 159\n  size: 25, 24\n  orig: 25, 24\n  offset: 0, 0\n  index: -1\nEmotion/9-Gao Thet/2-2\n  rotate: false\n  xy: 407, 160\n  size: 26, 23\n  orig: 26, 23\n  offset: 0, 0\n  index: -1\nEmotion/9-Gao Thet/3\n  rotate: false\n  xy: 892, 457\n  size: 96, 53\n  orig: 96, 53\n  offset: 0, 0\n  index: -1\nEmotion/9-Gao Thet/4\n  rotate: true\n  xy: 847, 386\n  size: 16, 23\n  orig: 16, 23\n  offset: 0, 0\n  index: -1\nEmotion/9-Gao Thet/5\n  rotate: false\n  xy: 701, 451\n  size: 97, 59\n  orig: 97, 59\n  offset: 0, 0\n  index: -1\n", ["Emotion-1.png"], {"skeleton": {"hash": "62AmgvIIQuKNX/ykxhDI/5hAiW4", "spine": "3.6.53", "width": 123, "height": 110, "images": ""}, "bones": [{"name": "root"}, {"name": "X-Tong", "parent": "root", "length": 36.19, "x": -0.08, "y": -54.81}, {"name": "bone", "parent": "X-Tong", "length": 27.02, "x": -0.92, "y": 44.25}, {"name": "1-1", "parent": "bone", "length": 12.09, "x": -1.15, "y": 16.49}, {"name": "1-2", "parent": "bone", "length": 12.09, "x": 12.02, "y": -32.8}, {"name": "1-3", "parent": "bone", "length": 12.09, "x": -2.44, "y": 48.72}, {"name": "bone2", "parent": "bone", "length": 18.35, "x": -11.07, "y": -22.82}, {"name": "bone3", "parent": "bone", "length": 16.46, "rotation": 174.71, "x": -19.86, "y": 48.3}, {"name": "bone4", "parent": "bone", "length": 9.82, "rotation": 122.66, "x": -22.19, "y": 59.34}, {"name": "bone5", "parent": "bone", "length": 7.82, "rotation": 77.47, "x": 0.49, "y": 60.61}, {"name": "bone6", "parent": "bone", "length": 41.07, "rotation": 64.96, "x": 46.16, "y": 31.86}, {"name": "bone7", "parent": "bone", "length": 24.11, "x": -2.33, "y": -35.24}, {"name": "bone8", "parent": "root", "length": 26.71, "x": 46.86, "y": -49.37}, {"name": "bone9", "parent": "bone", "length": 34.82, "rotation": -22.65, "x": 10.96, "y": -28.22}, {"name": "bone10", "parent": "bone8", "length": 36.44, "rotation": 88.81, "x": 0.04, "y": -0.03}, {"name": "bone11", "parent": "bone", "length": 59.77, "rotation": 166.79, "x": 57.51, "y": 13.02}, {"name": "bone12", "parent": "bone", "length": 12.02, "rotation": 30.34, "x": -39.89, "y": -0.64}, {"name": "bone13", "parent": "bone", "length": 12.84, "rotation": 16.7, "x": 11.35, "y": 54.71}, {"name": "bone14", "parent": "bone8", "length": 36.44, "rotation": 88.81, "x": 0.04, "y": -0.03}, {"name": "bone15", "parent": "bone8", "length": 36.44, "rotation": 88.81, "x": 0.04, "y": -0.03}, {"name": "bone16", "parent": "bone8", "length": 36.44, "rotation": 88.81, "x": 0.04, "y": -0.03}, {"name": "bone17", "parent": "bone8", "length": 36.44, "rotation": 88.81, "x": 0.04, "y": -0.03}, {"name": "bone18", "parent": "bone8", "length": 36.44, "rotation": 146.73, "x": 3.24, "y": 4.77}, {"name": "bone19", "parent": "bone8", "length": 36.44, "rotation": 29.89, "x": -2.2, "y": 7.97}, {"name": "bone20", "parent": "bone8", "length": 36.44, "rotation": 45.13, "x": 1.32, "y": 14.05}, {"name": "bone21", "parent": "bone8", "length": 36.44, "rotation": 124.27, "x": 2.28, "y": 10.53, "scaleX": 0.758}, {"name": "bone22", "parent": "bone3", "length": 12.27, "rotation": -174.71, "x": 0.09, "y": 0.1}, {"name": "bone23", "parent": "bone5", "length": 9.16, "rotation": -77.47, "x": -0.03, "y": 0.14}], "slots": [{"name": "Emotion/1-Buon/1", "bone": "bone", "attachment": "Emotion/1-Buon/1"}, {"name": "Emotion/1-<PERSON>uon/2", "bone": "1-1"}, {"name": "Emotion/1-Buon/3", "bone": "1-2"}, {"name": "Emotion/1-Buon/4", "bone": "1-3"}, {"name": "Emotion/1-Buon/5", "bone": "1-3"}, {"name": "Emotion/2-Ngac<PERSON><PERSON><PERSON>/1", "bone": "bone"}, {"name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/2", "bone": "bone2"}, {"name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/3", "bone": "bone5"}, {"name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/4", "bone": "bone4"}, {"name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/5", "bone": "bone3"}, {"name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/6", "bone": "1-3"}, {"name": "Emotion/3-<PERSON>/2", "bone": "1-3"}, {"name": "Emotion/3-<PERSON>/3", "bone": "1-3"}, {"name": "Emotion/3-<PERSON>/4", "bone": "bone7"}, {"name": "Emotion/3-<PERSON>/5", "bone": "bone6"}, {"name": "Emotion/3-<PERSON>/6", "bone": "bone6"}, {"name": "Emotion/3-<PERSON>/7", "bone": "bone6"}, {"name": "Emotion/4-<PERSON><PERSON>/3", "bone": "bone9"}, {"name": "Emotion/4-<PERSON><PERSON>/4", "bone": "bone12"}, {"name": "Emotion/4-<PERSON><PERSON>/5", "bone": "bone11"}, {"name": "Emotion/4-<PERSON><PERSON>/6", "bone": "bone4"}, {"name": "Emotion/4-<PERSON><PERSON>/7", "bone": "bone13"}, {"name": "Emotion/4-<PERSON><PERSON>/2", "bone": "bone10"}, {"name": "Emotion/4-<PERSON><PERSON>/12", "bone": "bone18"}, {"name": "Emotion/4-<PERSON><PERSON>/8", "bone": "bone14"}, {"name": "Emotion/4-<PERSON><PERSON>/13", "bone": "bone19"}, {"name": "Emotion/4-<PERSON><PERSON>/9", "bone": "bone15"}, {"name": "Emotion/4-<PERSON><PERSON>/14", "bone": "bone20"}, {"name": "Emotion/4-<PERSON><PERSON>/10", "bone": "bone16"}, {"name": "Emotion/4-<PERSON><PERSON>/11", "bone": "bone17"}, {"name": "Emotion/4-<PERSON><PERSON>/15", "bone": "bone21"}, {"name": "Emotion/5-<PERSON><PERSON>/2", "bone": "bone7"}, {"name": "Emotion/5-<PERSON><PERSON>/3", "bone": "bone2"}, {"name": "Emotion/5-<PERSON><PERSON>/4", "bone": "bone3"}, {"name": "Emotion/5-<PERSON><PERSON>/5-1", "bone": "1-1"}, {"name": "Emotion/5-<PERSON><PERSON>/5-2", "bone": "bone12"}, {"name": "Emotion/5-<PERSON><PERSON>/6", "bone": "bone3"}, {"name": "Emotion/5-<PERSON><PERSON>/7", "bone": "1-3"}, {"name": "Emotion/5-<PERSON><PERSON>/8", "bone": "bone6"}, {"name": "Emotion/6-Soc/2", "bone": "1-1"}, {"name": "Emotion/6-Soc/3", "bone": "bone"}, {"name": "Emotion/6-Soc/4", "bone": "bone5"}, {"name": "Emotion/6-Soc/5", "bone": "bone23"}, {"name": "Emotion/6-Soc/6", "bone": "bone3"}, {"name": "Emotion/6-Soc/7", "bone": "bone22"}, {"name": "Emotion/6-Soc/9", "bone": "1-1"}, {"name": "Emotion/6-Soc/10", "bone": "1-1"}, {"name": "Emotion/6-Soc/11", "bone": "1-1"}, {"name": "Emotion/7-<PERSON><PERSON> No/2", "bone": "bone12"}, {"name": "Emotion/7-<PERSON><PERSON> No/3", "bone": "1-3"}, {"name": "Emotion/7-<PERSON><PERSON> No/4", "bone": "bone7"}, {"name": "Emotion/7-<PERSON><PERSON> No/5", "bone": "bone3"}, {"name": "Emotion/7-<PERSON><PERSON> No/6", "bone": "bone9"}, {"name": "Emotion/9-<PERSON>/2-1", "bone": "bone4"}, {"name": "Emotion/9-<PERSON>/2-2", "bone": "bone13"}, {"name": "Emotion/9-<PERSON>/3", "bone": "1-1"}, {"name": "Emotion/9-<PERSON>/4", "bone": "1-1"}, {"name": "Emotion/9-<PERSON>/5", "bone": "1-1"}, {"name": "Emotion/10-Bom/2", "bone": "bone23"}, {"name": "Emotion/10-Bom/3", "bone": "bone22"}, {"name": "Emotion/10-Bom/5", "bone": "1-3"}, {"name": "Emotion/10-Bom/4", "bone": "1-1"}, {"name": "Emotion/10-Bom/6", "bone": "bone2"}, {"name": "Emotion/10-Bom/7", "bone": "bone12"}, {"name": "Emotion/10-Bom/8", "bone": "bone4"}, {"name": "Emotion/10-Bom/9", "bone": "bone13"}, {"name": "Emotion/11-<PERSON><PERSON> Trai/2", "bone": "bone2"}, {"name": "Emotion/11-<PERSON><PERSON> Trai/3", "bone": "1-3"}, {"name": "Emotion/11-Khoc/2", "bone": "1-1"}, {"name": "Emotion/11-Khoc/3", "bone": "1-3"}, {"name": "Emotion/11-Khoc/6", "bone": "bone13"}, {"name": "Emotion/11-Khoc/7", "bone": "bone4"}, {"name": "Emotion/11-Khoc/4", "bone": "bone2"}, {"name": "Emotion/11-Khoc/5", "bone": "bone22"}, {"name": "Emotion/13-<PERSON><PERSON>/1", "bone": "bone"}, {"name": "Emotion/13-<PERSON><PERSON>/2", "bone": "bone"}, {"name": "Emotion/13-<PERSON><PERSON>/3", "bone": "bone"}, {"name": "Emotion/13-<PERSON><PERSON>/4-1", "bone": "bone5"}, {"name": "Emotion/13-<PERSON><PERSON>/4-2", "bone": "bone23"}, {"name": "Emotion/13-<PERSON><PERSON>/5-1", "bone": "bone3"}, {"name": "Emotion/13-<PERSON><PERSON>/5-2", "bone": "bone22"}, {"name": "Emotion/13-<PERSON><PERSON>/6", "bone": "bone2"}, {"name": "Emotion/13-<PERSON><PERSON>/7", "bone": "bone14"}, {"name": "Emotion/13-<PERSON><PERSON>/9", "bone": "bone16"}, {"name": "Emotion/13-<PERSON><PERSON>/10", "bone": "bone17"}, {"name": "Emotion/13-<PERSON><PERSON>/11", "bone": "bone18"}, {"name": "Emotion/13-<PERSON><PERSON>/12", "bone": "bone19"}, {"name": "Emotion/13-<PERSON><PERSON>/13", "bone": "bone20"}, {"name": "Emotion/13-<PERSON><PERSON>/14", "bone": "bone21"}, {"name": "Emotion/13-<PERSON><PERSON>/8", "bone": "bone15"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/1", "bone": "bone7"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/3", "bone": "bone23"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/4", "bone": "1-1"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/7", "bone": "1-3"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/8", "bone": "bone13"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/9", "bone": "bone4"}, {"name": "Emotion/15-<PERSON><PERSON>/2", "bone": "bone7"}, {"name": "Emotion/15-<PERSON><PERSON>/3", "bone": "bone2"}, {"name": "Emotion/15-<PERSON><PERSON>/4", "bone": "1-1"}, {"name": "Emotion/15-<PERSON><PERSON>/5", "bone": "bone3"}, {"name": "Emotion/15-<PERSON><PERSON>/6", "bone": "bone22"}, {"name": "Emotion/15-<PERSON><PERSON>/7", "bone": "bone5"}, {"name": "Emotion/15-<PERSON><PERSON>/8", "bone": "bone23"}, {"name": "Emotion/16-<PERSON><PERSON>/3", "bone": "bone22"}, {"name": "Emotion/16-<PERSON><PERSON>/4", "bone": "bone22"}, {"name": "Emotion/16-<PERSON><PERSON>/5", "bone": "bone3"}, {"name": "Emotion/16-<PERSON><PERSON>/6", "bone": "1-1"}, {"name": "Emotion/17-WTH/2", "bone": "1-1"}, {"name": "Emotion/17-WTH/3", "bone": "bone2"}, {"name": "Emotion/17-WTH/6", "bone": "bone7"}, {"name": "Emotion/17-WTH/7", "bone": "bone12"}, {"name": "Emotion/17-WTH/11", "bone": "bone9"}, {"name": "Emotion/17-WTH/12", "bone": "bone23"}, {"name": "Emotion/17-WTH/8", "bone": "bone13"}, {"name": "Emotion/17-WTH/9", "bone": "bone4"}, {"name": "Emotion/17-WTH/10", "bone": "bone3"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/5-1", "bone": "bone16"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/5-3", "bone": "bone17"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/5-4", "bone": "bone18"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/5-2", "bone": "bone10"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/5-5", "bone": "bone14"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/5-6", "bone": "bone15"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/6-2", "bone": "bone19"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/6-3", "bone": "bone20"}, {"name": "Emotion/14-<PERSON><PERSON><PERSON>/6-4", "bone": "bone21"}, {"name": "Emotion/20-burn_joss_stick/2", "bone": "bone12"}, {"name": "Emotion/20-burn_joss_stick/3", "bone": "bone6"}, {"name": "Emotion/20-burn_joss_stick/4", "bone": "bone22"}, {"name": "Emotion/20-burn_joss_stick/5", "bone": "bone22"}, {"name": "Emotion/20-burn_joss_stick/6", "bone": "bone10"}, {"name": "Emotion/20-burn_joss_stick/8", "bone": "bone15"}, {"name": "Emotion/20-burn_joss_stick/9", "bone": "bone14"}, {"name": "Emotion/20-burn_joss_stick/10", "bone": "bone19"}, {"name": "Emotion/20-burn_joss_stick/14", "bone": "bone20"}, {"name": "Emotion/20-burn_joss_stick/15", "bone": "bone21"}, {"name": "Emotion/20-burn_joss_stick/11", "bone": "bone18"}, {"name": "Emotion/20-burn_joss_stick/12", "bone": "bone17"}, {"name": "Emotion/20-burn_joss_stick/13", "bone": "bone16"}, {"name": "Emotion/20-burn_joss_stick/7", "bone": "bone2"}, {"name": "Emotion/21-baffle/2", "bone": "bone4"}, {"name": "Emotion/21-baffle/3", "bone": "bone13"}, {"name": "Emotion/21-baffle/4", "bone": "bone12"}, {"name": "Emotion/22-cooll/2", "bone": "bone4"}, {"name": "Emotion/22-cooll/3", "bone": "bone12"}, {"name": "Emotion/22-cooll/4", "bone": "bone6"}, {"name": "Emotion/22-cooll/5", "bone": "bone7"}, {"name": "Emotion/22-cooll/6", "bone": "bone2"}, {"name": "Emotion/22-cooll/9", "bone": "bone15"}, {"name": "Emotion/22-cooll/10", "bone": "bone17"}, {"name": "Emotion/22-cooll/11", "bone": "bone16"}, {"name": "Emotion/22-cooll/12", "bone": "bone14"}, {"name": "Emotion/27-dribble/2", "bone": "bone"}, {"name": "Emotion/27-dribble/3", "bone": "bone"}, {"name": "Emotion/27-dribble/5", "bone": "bone14"}, {"name": "Emotion/27-dribble/12", "bone": "bone16"}, {"name": "Emotion/27-dribble/6", "bone": "bone15"}, {"name": "Emotion/27-dribble/11", "bone": "bone10"}, {"name": "Emotion/27-dribble/7", "bone": "bone7"}, {"name": "Emotion/27-dribble/8", "bone": "bone2"}, {"name": "Emotion/27-dribble/9", "bone": "bone13"}, {"name": "Emotion/27-dribble/10", "bone": "bone22"}, {"name": "Emotion/33-tire/2", "bone": "bone10"}, {"name": "Emotion/33-tire/3", "bone": "bone2"}, {"name": "Emotion/33-tire/4", "bone": "bone16"}, {"name": "Emotion/33-tire/5", "bone": "bone15"}, {"name": "Emotion/33-tire/6", "bone": "bone12"}, {"name": "Emotion/33-tire/7", "bone": "bone13"}, {"name": "Emotion/33-tire/8", "bone": "1-1"}, {"name": "Emotion/33-tire/9", "bone": "bone4"}], "skins": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": {"type": "mesh", "hull": 17, "width": 123, "height": 110, "uvs": [0.79557, 0.07956, 0.91567, 0.19671, 1, 0.39089, 1, 0.61905, 0.94461, 0.82294, 0.76156, 0.96075, 0.56734, 1, 0.36865, 1, 0.16346, 0.92123, 0.04159, 0.80676, 0, 0.59478, 0, 0.40222, 0.06619, 0.20156, 0.19643, 0.05916, 0.35707, 0, 0.48485, 0, 0.62624, 0, 0.38746, 0.14331, 0.38746, 0.86825, 0.25722, 0.81, 0.15736, 0.72747, 0.12118, 0.58022, 0.13175, 0.42002, 0.18963, 0.28733, 0.2837, 0.19671, 0.48485, 0.12713, 0.59049, 0.13198, 0.71205, 0.187, 0.80322, 0.28085, 0.87558, 0.42973, 0.87703, 0.5964, 0.70771, 0.81485, 0.55431, 0.86987, 0.81335, 0.71452, 0.53839, 0.73394, 0.64114, 0.68702, 0.70482, 0.62714, 0.75113, 0.55271, 0.75836, 0.46209, 0.70771, 0.34234, 0.64404, 0.27438, 0.57602, 0.24525, 0.34592, 0.29056, 0.27357, 0.36662, 0.23594, 0.45238, 0.23449, 0.54623, 0.26054, 0.65142, 0.48964, 0.24756, 0.40678, 0.2645, 0.32903, 0.71043, 0.41976, 0.74643, 0.65838, 0.47545, 0.62491, 0.41036, 0.5798, 0.37456, 0.55069, 0.36317, 0.50412, 0.35829, 0.43718, 0.37293, 0.39934, 0.3892, 0.36587, 0.425, 0.34841, 0.47708, 0.34841, 0.54054, 0.3615, 0.58448, 0.4008, 0.6219, 0.443, 0.63167, 0.52013, 0.62516, 0.57543, 0.59424, 0.62782, 0.55844, 0.6511, 0.5145, 0.49248, 0.48847], "triangles": [15, 17, 14, 26, 15, 16, 27, 26, 16, 25, 15, 26, 25, 17, 15, 13, 14, 17, 0, 27, 16, 24, 13, 17, 28, 27, 0, 12, 13, 24, 41, 25, 26, 41, 26, 27, 47, 25, 41, 17, 25, 47, 48, 17, 47, 24, 17, 48, 40, 41, 27, 1, 28, 0, 39, 40, 27, 23, 12, 24, 42, 24, 48, 23, 24, 42, 28, 39, 27, 54, 55, 47, 48, 47, 55, 41, 54, 47, 53, 54, 41, 40, 53, 41, 43, 23, 42, 56, 48, 55, 42, 48, 56, 39, 53, 40, 57, 42, 56, 43, 42, 57, 28, 1, 2, 11, 12, 23, 52, 53, 39, 22, 11, 23, 22, 23, 43, 58, 43, 57, 29, 28, 2, 38, 39, 28, 44, 22, 43, 44, 43, 58, 29, 38, 28, 51, 52, 39, 38, 51, 39, 59, 44, 58, 68, 56, 55, 68, 55, 54, 68, 54, 53, 68, 53, 52, 68, 52, 51, 57, 56, 68, 58, 57, 68, 59, 58, 68, 67, 68, 51, 60, 59, 68, 45, 44, 59, 45, 22, 44, 60, 45, 59, 37, 51, 38, 67, 51, 37, 66, 68, 67, 21, 11, 22, 21, 22, 45, 61, 60, 68, 65, 68, 66, 10, 11, 21, 3, 30, 29, 38, 29, 30, 37, 38, 30, 3, 29, 2, 62, 61, 68, 68, 63, 62, 65, 64, 68, 37, 66, 67, 36, 66, 37, 64, 63, 68, 61, 46, 45, 61, 45, 60, 21, 45, 46, 35, 66, 36, 65, 66, 35, 49, 46, 61, 49, 61, 62, 33, 37, 30, 36, 37, 33, 20, 21, 46, 35, 34, 64, 35, 64, 65, 50, 62, 63, 49, 62, 50, 63, 64, 34, 50, 63, 34, 20, 9, 10, 20, 10, 21, 19, 20, 46, 19, 46, 49, 33, 35, 36, 31, 35, 33, 3, 33, 30, 4, 33, 3, 18, 49, 50, 19, 49, 18, 31, 32, 34, 31, 34, 35, 50, 34, 32, 18, 50, 32, 8, 20, 19, 9, 20, 8, 4, 5, 31, 4, 31, 33, 7, 19, 18, 8, 19, 7, 5, 6, 32, 5, 32, 31, 18, 32, 6, 7, 18, 6], "vertices": [36.63, 57.05, 51.41, 44.17, 61.78, 22.81, 61.78, -2.29, 54.97, -24.72, 32.45, -39.88, 8.56, -44.19, -15.88, -44.19, -41.11, -35.53, -56.11, -22.94, -61.22, 0.38, -61.22, 21.56, -53.08, 43.63, -37.06, 59.3, -17.3, 65.81, -1.58, 65.81, 15.81, 65.81, -13.56, 50.04, -13.56, -29.7, -29.58, -23.29, -41.87, -14.22, -46.32, 1.98, -45.02, 19.6, -37.9, 34.2, -26.33, 44.17, -1.58, 51.82, 11.41, 51.29, 26.36, 45.24, 37.58, 34.91, 46.48, 18.54, 46.65, 0.2, 25.83, -23.83, 6.96, -29.88, 38.82, -12.79, 5, -14.93, 17.64, -9.77, 25.47, -3.18, 31.17, 5.01, 32.06, 14.98, 25.83, 28.15, 18, 35.62, 9.63, 38.83, -18.67, 33.84, -27.57, 25.48, -32.2, 16.04, -32.38, 5.72, -29.17, -5.85, -1, 38.57, -11.19, 36.71, -20.75, -12.34, -9.59, -16.3, 19.76, 13.51, 15.64, 20.67, 10.09, 24.6, 6.51, 25.86, 0.79, 26.39, -7.45, 24.78, -12.1, 22.99, -16.22, 19.05, -18.37, 13.33, -18.37, 6.35, -16.76, 1.51, -11.92, -2.6, -6.73, -3.68, 2.76, -2.96, 9.56, 0.44, 16, 4.38, 18.87, 9.21, -0.65, 12.07], "edges": [24, 26, 26, 28, 28, 34, 14, 36, 16, 18, 16, 38, 18, 40, 20, 42, 18, 20, 14, 16, 20, 22, 24, 22, 22, 44, 24, 46, 26, 48, 28, 30, 30, 32, 30, 50, 32, 52, 0, 2, 32, 0, 0, 54, 2, 56, 2, 4, 4, 58, 4, 6, 8, 6, 6, 60, 12, 14, 12, 64, 64, 62, 10, 12, 62, 10, 8, 10, 8, 66, 62, 66, 66, 60, 60, 58, 58, 56, 56, 54, 54, 52, 52, 50, 50, 34, 34, 48, 48, 46, 46, 44, 44, 42, 42, 40, 40, 38, 38, 36, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 84, 86, 86, 88, 88, 90, 90, 92, 62, 70, 66, 72, 74, 60, 76, 58, 78, 56, 80, 54, 52, 82, 50, 94, 94, 82, 96, 84, 96, 34, 84, 48, 46, 86, 96, 94, 44, 88, 42, 90, 40, 92, 92, 98, 98, 100, 100, 36, 68, 100, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 102, 76, 102, 78, 104, 80, 106, 82, 108, 94, 110, 96, 112, 84, 114, 86, 116, 88, 118, 90, 120, 92, 122, 98, 124, 100, 126, 68, 128, 70, 130, 72, 132, 74, 134, 128, 136, 110, 136, 136, 126, 122, 136, 136, 106, 136, 102, 136, 132, 136, 112, 136, 114, 136, 118, 136, 116, 136, 120, 136, 124, 136, 104, 136, 134, 136, 108, 64, 36, 68, 64, 98, 38]}}, "Emotion/1-Buon/2": {"Emotion/1-Buon/2": {"x": 0.2, "y": 0.5, "width": 43, "height": 20}}, "Emotion/1-Buon/3": {"Emotion/1-Buon/3": {"x": 0.22, "y": 0.77, "width": 32, "height": 14}}, "Emotion/1-Buon/4": {"Emotion/1-Buon/4": {"x": -15.42, "y": 0.71, "width": 30, "height": 17}}, "Emotion/1-Buon/5": {"Emotion/1-Buon/5": {"x": 18.57, "y": 0.21, "width": 30, "height": 18}}, "Emotion/10-Bom/2": {"Emotion/10-Bom/2": {"x": -0.18, "y": -0.26, "width": 45, "height": 45}}, "Emotion/10-Bom/3": {"Emotion/10-Bom/3": {"x": -0.19, "y": -0.33, "width": 42, "height": 44}}, "Emotion/10-Bom/4": {"Emotion/10-Bom/4": {"x": -0.34, "y": 0.02, "width": 45, "height": 45}}, "Emotion/10-Bom/5": {"Emotion/10-Bom/5": {"x": -0.19, "y": -0.27, "width": 45, "height": 45}}, "Emotion/10-Bom/6": {"Emotion/10-Bom/6": {"x": 2, "y": -0.01, "rotation": -45.51, "width": 18, "height": 19}}, "Emotion/10-Bom/7": {"Emotion/10-Bom/7": {"x": 1.63, "y": 0.26, "rotation": -128.83, "width": 19, "height": 18}}, "Emotion/10-Bom/8": {"Emotion/10-Bom/8": {"x": 3.35, "y": 0.41, "rotation": -111.85, "width": 24, "height": 16}}, "Emotion/10-Bom/9": {"Emotion/10-Bom/9": {"x": 3.24, "y": -0.41, "rotation": -58.6, "width": 24, "height": 16}}, "Emotion/11-Dep Trai/2": {"Emotion/11-Dep Trai/2": {"type": "mesh", "hull": 14, "width": 125, "height": 88, "uvs": [1, 0.37365, 1, 0.67136, 0.91486, 0.88436, 0.63881, 1, 0.30313, 1, 0, 1, 0, 0.72703, 0, 0.48741, 0, 0.16537, 0.08501, 0, 0.32016, 0.17505, 0.47913, 0.10729, 0.73083, 0, 1, 0, 0.11398, 0.93035, 0.81686, 0.21457, 0.52542, 0.3661, 0.31558, 0.39723, 0.10376, 0.27925, 0.10911, 0.61988, 0.30876, 0.72705, 0.587, 0.71038, 0.86759, 0.5613], "triangles": [18, 8, 9, 15, 12, 13, 11, 12, 15, 10, 18, 9, 16, 11, 15, 10, 11, 16, 15, 13, 0, 17, 18, 10, 17, 10, 16, 7, 8, 18, 19, 7, 18, 22, 15, 0, 16, 15, 22, 17, 19, 18, 22, 0, 1, 21, 16, 22, 6, 7, 19, 20, 19, 17, 17, 16, 21, 20, 17, 21, 2, 22, 1, 21, 22, 2, 14, 19, 20, 6, 19, 14, 5, 6, 14, 4, 14, 20, 5, 14, 4, 3, 21, 2, 4, 20, 21, 3, 4, 21], "vertices": [62.48, 10.84, 62.48, -15.35, 51.83, -34.1, 17.33, -44.27, -24.63, -44.27, -62.52, -44.27, -62.52, -20.25, -62.52, 0.83, -62.52, 29.17, -51.9, 43.73, -22.5, 28.32, -2.63, 34.28, 28.83, 43.73, 62.48, 43.73, -48.28, -38.15, 39.58, 24.84, 3.15, 11.51, -23.08, 8.77, -49.55, 19.15, -48.88, -10.82, -23.93, -20.26, 10.85, -18.79, 45.93, -5.67], "edges": [16, 18, 18, 20, 24, 26, 10, 12, 12, 28, 8, 10, 28, 8, 4, 2, 6, 8, 4, 6, 20, 22, 22, 24, 30, 24, 26, 30, 32, 22, 30, 32, 34, 20, 32, 34, 34, 36, 36, 16, 18, 36, 12, 14, 14, 16, 28, 38, 38, 36, 14, 38, 8, 40, 40, 34, 38, 40, 6, 42, 42, 32, 40, 42, 4, 44, 44, 30, 42, 44, 2, 0, 0, 26, 44, 0]}}, "Emotion/11-Dep Trai/3": {"Emotion/11-Dep Trai/3": {"x": -1.94, "y": -1.01, "width": 90, "height": 20}}, "Emotion/11-Khoc/2": {"Emotion/11-Khoc/2": {"x": 8.53, "y": -0.49, "rotation": -85.56, "width": 27, "height": 41}}, "Emotion/11-Khoc/3": {"Emotion/11-Khoc/3": {"x": 7.83, "y": -0.25, "rotation": -87.62, "width": 24, "height": 36}}, "Emotion/11-Khoc/4": {"Emotion/11-Khoc/4": {"x": 19.43, "y": -1.72, "rotation": -89.11, "width": 55, "height": 80}}, "Emotion/11-Khoc/5": {"Emotion/11-Khoc/5": {"x": 1.77, "y": -1.96, "width": 26, "height": 10}}, "Emotion/11-Khoc/6": {"Emotion/11-Khoc/3": {"x": 5.99, "y": -0.48, "rotation": -89.85, "width": 24, "height": 36}}, "Emotion/11-Khoc/7": {"Emotion/11-Khoc/3": {"x": 7.04, "y": 0.08, "rotation": -89.37, "width": 24, "height": 36}}, "Emotion/13-Boc Khoi/1": {"Emotion/13-Boc Khoi/1": {"x": 2.34, "y": 11.32, "scaleX": 1.02, "scaleY": 1.02, "width": 131, "height": 123}}, "Emotion/13-Boc Khoi/10": {"Emotion/13-Boc Khoi/7": {"x": 22.95, "y": 4.23, "rotation": -24.94, "width": 34, "height": 31}}, "Emotion/13-Boc Khoi/11": {"Emotion/13-Boc Khoi/7": {"x": 25.09, "y": -0.57, "rotation": -47.86, "width": 34, "height": 31}}, "Emotion/13-Boc Khoi/12": {"Emotion/13-Boc Khoi/7": {"x": 17.51, "y": 3.16, "rotation": -39.35, "width": 34, "height": 31}}, "Emotion/13-Boc Khoi/13": {"Emotion/13-Boc Khoi/7": {"x": 11.82, "y": 0.07, "rotation": -38.34, "width": 34, "height": 31}}, "Emotion/13-Boc Khoi/14": {"Emotion/13-Boc Khoi/7": {"x": 14.17, "y": 2.48, "rotation": -41.63, "width": 34, "height": 31}}, "Emotion/13-Boc Khoi/2": {"Emotion/13-Boc Khoi/2": {"x": -27.3, "y": 24.08, "width": 48, "height": 45}}, "Emotion/13-Boc Khoi/3": {"Emotion/13-Boc Khoi/3": {"x": 27.32, "y": 27.23, "width": 68, "height": 62}}, "Emotion/13-Boc Khoi/4-1": {"Emotion/13-Boc Khoi/4-1": {"x": -0.11, "y": 0.12, "rotation": -77.47, "width": 51, "height": 46}}, "Emotion/13-Boc Khoi/4-2": {"Emotion/13-Boc Khoi/4-2": {"x": -0.07, "y": -0.01, "width": 39, "height": 39}}, "Emotion/13-Boc Khoi/5-1": {"Emotion/13-Boc Khoi/5-1": {"x": 0.19, "y": 0.1, "rotation": -174.71, "width": 37, "height": 36}}, "Emotion/13-Boc Khoi/5-2": {"Emotion/13-Boc Khoi/5-2": {"x": 0.2, "y": 0.09, "width": 34, "height": 34}}, "Emotion/13-Boc Khoi/6": {"Emotion/13-Boc Khoi/6": {"x": 1.18, "y": -0.71, "width": 48, "height": 12}}, "Emotion/13-Boc Khoi/7": {"Emotion/13-Boc Khoi/7": {"x": 22.95, "y": 4.23, "rotation": -24.94, "width": 34, "height": 31}}, "Emotion/13-Boc Khoi/8": {"Emotion/13-Boc Khoi/7": {"x": 22.95, "y": 4.23, "rotation": -24.94, "width": 34, "height": 31}}, "Emotion/13-Boc Khoi/9": {"Emotion/13-Boc Khoi/7": {"x": 22.95, "y": 4.23, "rotation": -24.94, "width": 34, "height": 31}}, "Emotion/14-Khoc Thet/1": {"Emotion/14-Khoc Thet/1": {"x": 0.7, "y": -4.79, "width": 112, "height": 20}}, "Emotion/14-Khoc Thet/3": {"Emotion/14-Khoc Thet/3": {"x": -0.23, "y": 0.01, "width": 22, "height": 22}}, "Emotion/14-Khoc Thet/4": {"Emotion/14-Khoc Thet/4": {"x": -0.11, "y": -0.03, "width": 22, "height": 22}}, "Emotion/14-Khoc Thet/5-1": {"Emotion/14-Khoc Thet/5-1": {"x": 39.58, "y": 4.54, "rotation": 91.53, "width": 34, "height": 87}}, "Emotion/14-Khoc Thet/5-2": {"Emotion/14-Khoc Thet/5-2": {"x": 39, "y": 3.61, "rotation": 124.14, "width": 46, "height": 72}}, "Emotion/14-Khoc Thet/5-3": {"Emotion/14-Khoc Thet/5-1": {"x": 40.68, "y": 5.33, "rotation": 92.57, "width": 34, "height": 87}}, "Emotion/14-Khoc Thet/5-4": {"Emotion/14-Khoc Thet/5-1": {"x": 44.01, "y": 4.12, "rotation": 93.16, "width": 34, "height": 87}}, "Emotion/14-Khoc Thet/5-5": {"Emotion/14-Khoc Thet/5-2": {"x": 38.73, "y": 4.62, "rotation": 121.8, "width": 46, "height": 72}}, "Emotion/14-Khoc Thet/5-6": {"Emotion/14-Khoc Thet/5-2": {"x": 38.94, "y": 4.8, "rotation": 121.8, "width": 46, "height": 72}}, "Emotion/14-Khoc Thet/6-2": {"Emotion/14-Khoc Thet/6-2": {"x": 36.09, "y": -2.24, "rotation": 67.79, "width": 40, "height": 80}}, "Emotion/14-Khoc Thet/6-3": {"Emotion/14-Khoc Thet/6-2": {"x": 37.4, "y": -1.86, "rotation": 65.08, "width": 40, "height": 80}}, "Emotion/14-Khoc Thet/6-4": {"Emotion/14-Khoc Thet/6-2": {"x": 44.62, "y": -3.38, "rotation": 61.53, "width": 40, "height": 80}}, "Emotion/14-Khoc Thet/7": {"Emotion/14-Khoc Thet/7": {"x": 0.04, "y": 0.05, "width": 55, "height": 6}}, "Emotion/14-Khoc Thet/8": {"Emotion/14-Khoc Thet/8": {"x": 1.53, "y": -0.79, "rotation": -30.69, "width": 14, "height": 16}}, "Emotion/14-Khoc Thet/9": {"Emotion/14-Khoc Thet/9": {"x": 1.74, "y": 1.06, "rotation": -155.07, "width": 15, "height": 16}}, "Emotion/15-Choang/2": {"Emotion/15-Choang/2": {"x": -0.79, "y": 14.07, "rotation": 5.88, "width": 108, "height": 68}}, "Emotion/15-Choang/3": {"Emotion/15-Choang/3": {"x": -0.32, "y": -0.19, "width": 53, "height": 51}}, "Emotion/15-Choang/4": {"Emotion/15-Choang/4": {"x": -0.32, "y": 0.05, "width": 34, "height": 32}}, "Emotion/15-Choang/5": {"Emotion/15-Choang/5": {"x": 0.23, "y": 0.1, "rotation": -174.71, "width": 53, "height": 51}}, "Emotion/15-Choang/6": {"Emotion/15-Choang/6": {"x": 0.47, "y": 0.04, "width": 22, "height": 21}}, "Emotion/15-Choang/7": {"Emotion/15-Choang/7": {"x": -0.04, "y": 0.45, "rotation": -77.47, "width": 42, "height": 40}}, "Emotion/15-Choang/8": {"Emotion/15-Choang/8": {"x": -0.09, "y": -0.54, "width": 17, "height": 16}}, "Emotion/16-Nhin Deu/3": {"Emotion/16-Nhin Deu/3": {"x": 11.21, "y": 0.71, "width": 40, "height": 16}}, "Emotion/16-Nhin Deu/4": {"Emotion/16-Nhin Deu/4": {"x": -14.28, "y": -0.04, "width": 34, "height": 14}}, "Emotion/16-Nhin Deu/5": {"Emotion/16-Nhin Deu/5": {"x": -4.7, "y": -4.33, "rotation": -174.71, "width": 88, "height": 6}}, "Emotion/16-Nhin Deu/6": {"Emotion/16-Nhin Deu/6": {"x": -0.16, "y": 0.02, "width": 46, "height": 47}}, "Emotion/17-WTH/10": {"Emotion/17-WTH/10": {"x": 0.18, "y": -0.02, "rotation": -174.71, "width": 84, "height": 84}}, "Emotion/17-WTH/11": {"Emotion/17-WTH/7": {"x": 14.8, "y": -1.72, "rotation": -12.66, "width": 42, "height": 20}}, "Emotion/17-WTH/12": {"Emotion/17-WTH/7": {"x": 9.58, "y": -1.77, "rotation": -13.95, "width": 42, "height": 20}}, "Emotion/17-WTH/2": {"Emotion/17-WTH/2": {"x": 2.82, "y": 1.87, "rotation": -13.77, "width": 100, "height": 67}}, "Emotion/17-WTH/3": {"Emotion/17-WTH/3": {"x": 3.67, "y": -1.2, "width": 107, "height": 30}}, "Emotion/17-WTH/6": {"Emotion/17-WTH/6": {"x": -0.51, "y": -0.32, "rotation": -55.98, "width": 28, "height": 35}}, "Emotion/17-WTH/7": {"Emotion/17-WTH/7": {"x": 3.56, "y": -2.51, "rotation": -23.41, "width": 42, "height": 20}}, "Emotion/17-WTH/8": {"Emotion/17-WTH/8": {"x": -0.23, "y": -2.1, "rotation": -19.81, "width": 18, "height": 25}}, "Emotion/17-WTH/9": {"Emotion/17-WTH/9": {"x": 1.28, "y": -0.1, "rotation": -137.83, "width": 19, "height": 21}}, "Emotion/2-NgacNhien/1": {"Emotion/2-NgacNhien/1": {"x": 0.28, "y": 10.52, "width": 123, "height": 110}}, "Emotion/2-NgacNhien/2": {"Emotion/2-NgacNhien/2": {"x": 0.35, "y": 1.08, "width": 23, "height": 39}}, "Emotion/2-NgacNhien/3": {"Emotion/2-NgacNhien/3": {"x": 7.12, "y": -7.33, "rotation": -77.47, "width": 23, "height": 14}}, "Emotion/2-NgacNhien/4": {"Emotion/2-NgacNhien/4": {"x": 11, "y": 5.08, "rotation": -122.66, "width": 22, "height": 14}}, "Emotion/2-NgacNhien/5": {"Emotion/2-NgacNhien/5": {"x": 11.74, "y": 1.63, "rotation": -174.71, "width": 33, "height": 14}}, "Emotion/2-NgacNhien/6": {"Emotion/2-NgacNhien/6": {"x": 12.81, "y": -0.55, "width": 35, "height": 13}}, "Emotion/20-burn_joss_stick/10": {"Emotion/20-burn_joss_stick/6": {"x": 26.03, "y": -3.09, "rotation": -91.49, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/11": {"Emotion/20-burn_joss_stick/6": {"x": 25.9, "y": 0.33, "rotation": -97.55, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/12": {"Emotion/20-burn_joss_stick/6": {"x": 27.8, "y": -0.83, "rotation": -94.23, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/13": {"Emotion/20-burn_joss_stick/6": {"x": 27.8, "y": -0.83, "rotation": -94.23, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/14": {"Emotion/20-burn_joss_stick/6": {"x": 17.83, "y": -0.54, "rotation": -97.37, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/15": {"Emotion/20-burn_joss_stick/6": {"x": 19.17, "y": -0.17, "rotation": -94.24, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/2": {"Emotion/20-burn_joss_stick/2": {"x": 0.55, "y": 0.18, "rotation": -130.93, "width": 16, "height": 15}}, "Emotion/20-burn_joss_stick/3": {"Emotion/20-burn_joss_stick/3": {"x": -2.01, "y": 0.37, "rotation": -50.79, "width": 16, "height": 17}}, "Emotion/20-burn_joss_stick/4": {"Emotion/20-burn_joss_stick/4": {"x": -26.73, "y": 0.69, "width": 28, "height": 6}}, "Emotion/20-burn_joss_stick/5": {"Emotion/20-burn_joss_stick/5": {"x": 25.17, "y": 0.69, "width": 28, "height": 6}}, "Emotion/20-burn_joss_stick/6": {"Emotion/20-burn_joss_stick/6": {"x": 27.8, "y": -0.83, "rotation": -94.23, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/7": {"Emotion/20-burn_joss_stick/7": {"x": 0.78, "y": 24.61, "width": 9, "height": 53}}, "Emotion/20-burn_joss_stick/8": {"Emotion/20-burn_joss_stick/6": {"x": 27.8, "y": -0.83, "rotation": -94.23, "width": 15, "height": 63}}, "Emotion/20-burn_joss_stick/9": {"Emotion/20-burn_joss_stick/6": {"x": 27.8, "y": -0.83, "rotation": -94.23, "width": 15, "height": 63}}, "Emotion/21-baffle/2": {"Emotion/21-baffle/2": {"x": 6.2, "y": 1.14, "rotation": -121.93, "width": 27, "height": 21}}, "Emotion/21-baffle/3": {"Emotion/21-baffle/3": {"x": 8.8, "y": -2.84, "rotation": -54.16, "width": 26, "height": 19}}, "Emotion/21-baffle/4": {"Emotion/21-baffle/4": {"x": 5.71, "y": -2.11, "rotation": -55.9, "width": 52, "height": 31}}, "Emotion/22-cooll/10": {"Emotion/22-cooll/10": {"x": 24.48, "y": 1.24, "rotation": -82.33, "width": 63, "height": 65}}, "Emotion/22-cooll/11": {"Emotion/22-cooll/9": {"x": 26, "y": 0.13, "rotation": -98.37, "width": 59, "height": 58}}, "Emotion/22-cooll/12": {"Emotion/22-cooll/10": {"x": 24.48, "y": 1.24, "rotation": -82.33, "width": 63, "height": 65}}, "Emotion/22-cooll/2": {"Emotion/22-cooll/2": {"x": 4.52, "y": 0.63, "rotation": -91.9, "width": 29, "height": 16}}, "Emotion/22-cooll/3": {"Emotion/22-cooll/3": {"x": 2.55, "y": 0.26, "rotation": -67.86, "width": 41, "height": 21}}, "Emotion/22-cooll/4": {"Emotion/22-cooll/4": {"x": 4.45, "y": -0.37, "rotation": -88, "width": 30, "height": 17}}, "Emotion/22-cooll/5": {"Emotion/22-cooll/5": {"x": -0.08, "y": 0.53, "rotation": -119.31, "width": 36, "height": 22}}, "Emotion/22-cooll/6": {"Emotion/22-cooll/6": {"x": -0.13, "y": 0.03, "width": 59, "height": 27}}, "Emotion/22-cooll/9": {"Emotion/22-cooll/9": {"x": 26, "y": 0.13, "rotation": -98.37, "width": 59, "height": 58}}, "Emotion/27-dribble/10": {"Emotion/27-dribble/10": {"x": -0.37, "y": 0.09, "width": 78, "height": 78}}, "Emotion/27-dribble/11": {"Emotion/27-dribble/6": {"x": 12.25, "y": -1.08, "rotation": -88.21, "width": 13, "height": 26}}, "Emotion/27-dribble/12": {"Emotion/27-dribble/5": {"x": 11.64, "y": -0.95, "rotation": -109.07, "width": 12, "height": 24}}, "Emotion/27-dribble/2": {"Emotion/27-dribble/2": {"x": 43.97, "y": 32.37, "width": 36, "height": 43}}, "Emotion/27-dribble/3": {"Emotion/27-dribble/3": {"x": -31.4, "y": 34.87, "width": 69, "height": 72}}, "Emotion/27-dribble/5": {"Emotion/27-dribble/5": {"x": 11.64, "y": -0.95, "rotation": -109.07, "width": 12, "height": 24}}, "Emotion/27-dribble/6": {"Emotion/27-dribble/6": {"x": 12.25, "y": -1.08, "rotation": -88.21, "width": 13, "height": 26}}, "Emotion/27-dribble/7": {"Emotion/27-dribble/7": {"x": 1.51, "y": -0.76, "rotation": -14.52, "width": 96, "height": 43}}, "Emotion/27-dribble/8": {"Emotion/27-dribble/8": {"x": 4.4, "y": 5.48, "rotation": -11.08, "width": 87, "height": 30}}, "Emotion/27-dribble/9": {"Emotion/27-dribble/9": {"x": -0.36, "y": 0.09, "rotation": -16.7, "width": 41, "height": 42}}, "Emotion/3-Chao/2": {"Emotion/3-Chao/2": {"x": -16.34, "y": 5.61, "width": 24, "height": 11}}, "Emotion/3-Chao/3": {"Emotion/3-Chao/3": {"x": 19.7, "y": 5.61, "width": 22, "height": 11}}, "Emotion/3-Chao/4": {"Emotion/3-Chao/4": {"x": 3.55, "y": 13, "width": 90, "height": 60}}, "Emotion/3-Chao/5": {"Emotion/3-Chao/5": {"x": 28.25, "y": 25.52, "rotation": -64.96, "width": 17, "height": 21}}, "Emotion/3-Chao/6": {"Emotion/3-Chao/6": {"x": 21.34, "y": -18.72, "rotation": -64.96, "width": 18, "height": 21}}, "Emotion/3-Chao/7": {"Emotion/3-Chao/7": {"x": 24.72, "y": -2.24, "rotation": -64.96, "width": 41, "height": 61}}, "Emotion/33-tire/2": {"Emotion/33-tire/2": {"x": 5.07, "y": -0.98, "rotation": 38.7, "width": 13, "height": 12}}, "Emotion/33-tire/3": {"Emotion/33-tire/3": {"x": -0.87, "y": -0.59, "width": 41, "height": 25}}, "Emotion/33-tire/4": {"Emotion/33-tire/4": {"x": 3.98, "y": -0.28, "rotation": -68.83, "width": 21, "height": 14}}, "Emotion/33-tire/5": {"Emotion/33-tire/5": {"x": 2.83, "y": -0.16, "rotation": -107.85, "width": 26, "height": 17}}, "Emotion/33-tire/6": {"Emotion/33-tire/6": {"x": -0.76, "y": -0.84, "rotation": -20.83, "width": 46, "height": 41}}, "Emotion/33-tire/7": {"Emotion/33-tire/7": {"x": 3.97, "y": -14.91, "rotation": -21.06, "width": 40, "height": 37}}, "Emotion/33-tire/8": {"Emotion/33-tire/8": {"x": -0.28, "y": -1.04, "rotation": 15.92, "width": 41, "height": 37}}, "Emotion/33-tire/9": {"Emotion/33-tire/9": {"x": -0.7, "y": 13.7, "rotation": -142.76, "width": 38, "height": 35}}, "Emotion/4-Hut Thuoc/10": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/11": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/12": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/13": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/14": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/15": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/2": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/3": {"Emotion/4-Hut Thuoc/3": {"x": 19.51, "y": 1.39, "rotation": 22.65, "width": 43, "height": 31}}, "Emotion/4-Hut Thuoc/4": {"Emotion/4-Hut Thuoc/4": {"x": 1.64, "y": -1.09, "rotation": -30.34, "width": 26, "height": 19}}, "Emotion/4-Hut Thuoc/5": {"Emotion/4-Hut Thuoc/5": {"x": 55.9, "y": 2.03, "rotation": -166.79, "width": 123, "height": 44}}, "Emotion/4-Hut Thuoc/6": {"Emotion/4-Hut Thuoc/6": {"x": -0.04, "y": -0.59, "rotation": -122.66, "width": 20, "height": 14}}, "Emotion/4-Hut Thuoc/7": {"Emotion/4-Hut Thuoc/7": {"x": 6.28, "y": -0.23, "rotation": -16.7, "width": 21, "height": 13}}, "Emotion/4-Hut Thuoc/8": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/4-Hut Thuoc/9": {"Emotion/4-Hut Thuoc/2": {"x": 16.55, "y": 0.46, "rotation": -88.81, "width": 15, "height": 48}}, "Emotion/5-Dang Yeu/2": {"Emotion/5-Dang Yeu/2": {"x": 8.14, "y": -1.63, "width": 39, "height": 23}}, "Emotion/5-Dang Yeu/3": {"Emotion/5-Dang Yeu/3": {"x": 13.3, "y": 9.48, "width": 100, "height": 46}}, "Emotion/5-Dang Yeu/4": {"Emotion/5-Dang Yeu/4": {"x": 11.96, "y": 1.53, "rotation": -174.71, "width": 17, "height": 17}}, "Emotion/5-Dang Yeu/5-1": {"Emotion/5-Dang Yeu/5-1": {"x": 28.77, "y": -3.75, "rotation": 130.14, "width": 55, "height": 54}}, "Emotion/5-Dang Yeu/5-2": {"Emotion/5-Dang Yeu/5-2": {"x": 2.76, "y": -0.38, "rotation": 136.56, "width": 20, "height": 21}}, "Emotion/5-Dang Yeu/6": {"Emotion/5-Dang Yeu/6": {"x": -9.59, "y": -3.51, "rotation": -174.71, "width": 16, "height": 16}}, "Emotion/5-Dang Yeu/7": {"Emotion/5-Dang Yeu/7": {"x": 0.43, "y": -2.03, "rotation": -3.48, "width": 49, "height": 38}}, "Emotion/5-Dang Yeu/8": {"Emotion/5-Dang Yeu/8": {"x": -0.81, "y": -0.12, "rotation": -2.28, "width": 43, "height": 32}}, "Emotion/6-Soc/10": {"Emotion/6-Soc/10": {"x": 1.07, "y": -17.38, "width": 70, "height": 14}}, "Emotion/6-Soc/11": {"Emotion/6-Soc/11": {"x": 1.08, "y": 20.79, "width": 72, "height": 17}}, "Emotion/6-Soc/2": {"Emotion/6-Soc/2": {"x": 5.07, "y": -3.85, "width": 105, "height": 72}}, "Emotion/6-Soc/3": {"Emotion/6-Soc/3": {"x": -7.69, "y": 51.73, "width": 85, "height": 41}}, "Emotion/6-Soc/4": {"Emotion/6-Soc/4": {"x": -0.07, "y": 0.12, "rotation": -77.47, "width": 31, "height": 30}}, "Emotion/6-Soc/5": {"Emotion/6-Soc/5": {"x": -0.17, "y": 0.17, "width": 12, "height": 11}}, "Emotion/6-Soc/6": {"Emotion/6-Soc/6": {"x": 0.19, "y": -0.01, "rotation": -174.71, "width": 42, "height": 41}}, "Emotion/6-Soc/7": {"Emotion/6-Soc/7": {"x": 0.04, "y": -0.2, "width": 16, "height": 15}}, "Emotion/6-Soc/9": {"Emotion/6-Soc/9": {"x": 0.9, "y": -7.52, "width": 13, "height": 24}}, "Emotion/7-Phan No/2": {"Emotion/7-Phan No/2": {"x": 8.52, "y": -0.42, "rotation": 132.51, "width": 47, "height": 45}}, "Emotion/7-Phan No/3": {"Emotion/7-Phan No/3": {"x": 9.57, "y": -1.08, "rotation": 53.23, "width": 48, "height": 47}}, "Emotion/7-Phan No/4": {"Emotion/7-Phan No/4": {"x": -0.15, "y": -0.16, "width": 20, "height": 18}}, "Emotion/7-Phan No/5": {"Emotion/7-Phan No/5": {"x": 9.54, "y": -3.44, "rotation": 129.51, "width": 59, "height": 52}}, "Emotion/7-Phan No/6": {"Emotion/7-Phan No/6": {"x": 8.71, "y": 0.38, "rotation": 51.62, "width": 65, "height": 56}}, "Emotion/9-Gao Thet/2-1": {"Emotion/9-Gao Thet/2-1": {"x": 11.51, "y": 0.31, "rotation": -130.09, "width": 25, "height": 24}}, "Emotion/9-Gao Thet/2-2": {"Emotion/9-Gao Thet/2-2": {"x": 12.33, "y": -0.59, "rotation": -39.76, "width": 26, "height": 23}}, "Emotion/9-Gao Thet/3": {"Emotion/9-Gao Thet/3": {"x": 2.59, "y": -14.45, "width": 96, "height": 53}}, "Emotion/9-Gao Thet/4": {"Emotion/9-Gao Thet/4": {"x": 0.43, "y": -17.25, "width": 16, "height": 23}}, "Emotion/9-Gao Thet/5": {"Emotion/9-Gao Thet/5": {"x": 2.08, "y": 12.46, "width": 97, "height": 59}}}}, "animations": {"1-waaaht": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/2": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/2"}, {"time": 1.4, "name": "Emotion/6-Soc/2"}]}, "Emotion/6-Soc/4": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/4"}, {"time": 1.4, "name": "Emotion/6-Soc/4"}]}, "Emotion/6-Soc/5": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/5"}]}, "Emotion/6-Soc/6": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/6"}, {"time": 1.4, "name": "Emotion/6-Soc/6"}]}, "Emotion/6-Soc/7": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/7"}]}, "Emotion/6-Soc/9": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/9"}, {"time": 1.4, "name": "Emotion/6-Soc/9"}]}, "Emotion/6-Soc/10": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/10"}, {"time": 1.4, "name": "Emotion/6-Soc/10"}]}, "Emotion/6-Soc/11": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/11"}, {"time": 1.4, "name": "Emotion/6-Soc/11"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -15.33, "y": -27.55, "curve": "stepped"}, {"time": 1.4, "x": -15.33, "y": -27.55}], "scale": [{"time": 0, "x": 0.552, "y": 0.356, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 1, "y": 0.258}, {"time": 0.3333, "x": 1, "y": 0.194}, {"time": 0.3667, "x": 0.544, "y": 0.362}, {"time": 0.4, "x": 1, "y": 1.138}, {"time": 0.4667, "x": 1, "y": 1.272}, {"time": 0.5667, "x": 1, "y": 1.138}, {"time": 0.6667, "x": 1, "y": 1.05}, {"time": 0.7333, "x": 1, "y": 1.414}, {"time": 0.8, "x": 1, "y": 1.05}, {"time": 0.8667, "x": 1, "y": 1.314}, {"time": 0.9333, "x": 1, "y": 1.05}, {"time": 1, "x": 1, "y": 1.327}, {"time": 1.0667, "x": 1, "y": 1.05}, {"time": 1.1333, "x": 1, "y": 1.327}, {"time": 1.2, "x": 1, "y": 1.05}, {"time": 1.2667, "x": 1, "y": 1.327}, {"time": 1.3333, "x": 1, "y": 1.05}, {"time": 1.4, "x": 0.552, "y": 0.356}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -24.14, "y": 1.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 13.95, "y": -8.32, "curve": "stepped"}, {"time": 0.1667, "x": 13.95, "y": -8.32}, {"time": 0.3, "x": 14.01, "y": -7.3, "curve": "stepped"}, {"time": 0.6667, "x": 14.01, "y": -7.3}, {"time": 1.4, "x": 13.95, "y": -8.32}], "scale": [{"time": 0, "x": 0.294, "y": 0.294, "curve": "stepped"}, {"time": 0.1667, "x": 0.294, "y": 0.294}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 0.7667, "x": 1.156, "y": 1.156}, {"time": 0.9667, "x": 0.983, "y": 0.983}, {"time": 1.3333, "x": 1.087, "y": 1.087}, {"time": 1.4, "x": 0.294, "y": 0.294}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0}], "translate": [{"time": 0, "x": 2.98, "y": 1.04, "curve": "stepped"}, {"time": 0.3, "x": 2.98, "y": 1.04}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -2.78, "y": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -5.21, "y": -2.51, "curve": "stepped"}, {"time": 0.3, "x": -5.21, "y": -2.51, "curve": "stepped"}, {"time": 1.3333, "x": -5.21, "y": -2.51}, {"time": 1.4, "x": -2.78, "y": -9.33}], "scale": [{"time": 0, "x": 0.475, "y": 0.475, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 0.575, "y": 0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0.372, "y": 0.536, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.231, "y": 0.231, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.4, "x": 0.475, "y": 0.475}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": 0.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 1.8}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 0, "y": -25.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0, "y": 28.28, "curve": [0.09, 0, 0.348, 0.94]}, {"time": 0.2667, "x": 0, "y": 95.17, "curve": [0.566, -0.01, 0.914, 0.99]}, {"time": 0.4, "x": 0, "y": -26.23, "curve": [0.304, 0.01, 0.645, 0.41]}, {"time": 0.4333, "x": 0, "y": -9.03, "curve": [0.363, 0.34, 0.768, 1]}, {"time": 0.5333, "x": 0, "y": 8.93, "curve": [0.484, 0.04, 0.859, 1]}, {"time": 0.6333, "x": 0, "y": 8.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 2.73}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 1.481, "y": 0.416, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0.62, "y": 1.633, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.053, "y": 1.496, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.963, "y": 1.847, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.249, "y": 1.069, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0.916, "y": 1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.006, "y": 1.471, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1.035, "y": 1.317, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 1.199, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 7.29, "y": 2.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": -3.32, "y": 7.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -7.43, "y": -2.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 3.39, "y": -7.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 7.32, "y": 2.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 3.34, "y": -12.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 12.71, "y": -0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -4, "y": 9.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": -13, "y": -3.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.57, "y": -10.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}}}, "10-beated": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 2.6667, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/15-Choang/2": {"attachment": [{"time": 0, "name": "Emotion/15-<PERSON><PERSON>/2"}]}, "Emotion/15-Choang/3": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "color": "f89999ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "color": "f89999ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "color": "f89999ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "color": "f89999ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "color": "f89999ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "color": "f89999ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/15-<PERSON><PERSON>/3"}]}, "Emotion/15-Choang/4": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "color": "ffffffa3", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffa6", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/15-<PERSON><PERSON>/4"}, {"time": 2.6667, "name": "Emotion/15-<PERSON><PERSON>/4"}]}, "Emotion/15-Choang/5": {"attachment": [{"time": 0, "name": "Emotion/15-<PERSON><PERSON>/5"}, {"time": 2.6667, "name": "Emotion/15-<PERSON><PERSON>/5"}]}, "Emotion/15-Choang/6": {"attachment": [{"time": 0, "name": "Emotion/15-<PERSON><PERSON>/6"}, {"time": 2.1667, "name": "Emotion/15-<PERSON><PERSON>/6"}, {"time": 2.6667, "name": "Emotion/15-<PERSON><PERSON>/6"}]}, "Emotion/15-Choang/7": {"attachment": [{"time": 0, "name": "Emotion/15-<PERSON><PERSON>/7"}]}, "Emotion/15-Choang/8": {"attachment": [{"time": 0, "name": "Emotion/15-<PERSON><PERSON>/8"}, {"time": 2.2, "name": "Emotion/15-<PERSON><PERSON>/8"}, {"time": 2.6667, "name": "Emotion/15-<PERSON><PERSON>/8"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": -1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.247, 0, 0.729, 0.91]}, {"time": 1.2667, "x": 1.07, "y": 0.972, "curve": [0.348, 0.66, 0.682, 1]}, {"time": 1.3333, "x": 1, "y": 0.972, "curve": [0.263, 0, 0.618, 0.43]}, {"time": 1.8, "x": 1.07, "y": 0.98, "curve": [0.354, 0.41, 0.756, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 34.96, "y": -29.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 36.19, "y": -28.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 40.34, "y": -29.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 34.33, "y": -28.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 38.12, "y": -29.08}, {"time": 2.6667, "x": 34.96, "y": -29.41}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "x": 0.931, "y": 0.931, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1.117, "y": 1.117, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0.883, "y": 0.883, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 1.051, "y": 1.051, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -21.78, "y": 7.04, "curve": "stepped"}, {"time": 2.6667, "x": -21.78, "y": 7.04}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0.983, "y": 1.166, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "x": 0.928, "y": 0.884, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0.935, "y": 1.118, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.964, "y": 0.979, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.905, "y": 1.107, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0.926, "y": 0.932, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0.895, "y": 1.111, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0.926, "y": 0.932, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.895, "y": 1.111, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.926, "y": 0.932, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0.895, "y": 1.111, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0.853, "y": 0.886, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 1.3, "angle": 0, "curve": "stepped"}, {"time": 1.9667, "angle": 0}], "translate": [{"time": 0, "x": 27.75, "y": -34.41}, {"time": 1.3, "x": 27.75, "y": -37.77, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 1.3667, "x": 27.75, "y": -27.5, "curve": [0.285, 0.16, 0.638, 0.56]}, {"time": 1.9, "x": 27.75, "y": -34.99, "curve": [0.37, 0.47, 0.753, 1]}, {"time": 1.9667, "x": 27.75, "y": -34.41}], "scale": [{"time": 1.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1.148, "y": 1.148, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.982, "y": 0.982, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1.013, "y": 1.013, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 0.985, "y": 0.985, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1}], "shear": [{"time": 1.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -0.93, "y": -17.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": -2.8, "y": -20.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": -0.93, "y": -10.57, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 1.6333, "x": -2.17, "y": -13.9, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.9, "x": -0.93, "y": -17.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": -0.93, "y": -17.39}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1.165, "y": 1.165, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.984, "y": 0.984, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "x": 1.016, "y": 1.016, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 0.994, "y": 0.994, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 51.57}, {"time": 0.6, "angle": 320.87}, {"time": 1.3667, "angle": 192.79}, {"time": 1.5333, "angle": 105.68}, {"time": 1.7, "angle": 311.18}, {"time": 2.3667, "angle": 134.1}, {"time": 2.6667, "angle": 51.57}], "translate": [{"time": 0, "x": 8.84, "y": -9.21, "curve": [0.46, 0.03, 0.77, 0.41]}, {"time": 0.5667, "x": 11.81, "y": -6.96, "curve": [0.474, 0.26, 0.713, 0.63]}, {"time": 1.3667, "x": 7.11, "y": 9.54, "curve": [0.098, 0.02, 0.543, 0.97]}, {"time": 1.5333, "x": 11.49, "y": -37.82, "curve": [0.539, 0.02, 0.859, 0.99]}, {"time": 1.7333, "x": 9.05, "y": -11.46, "curve": [0.344, 0, 0.707, 0.98]}, {"time": 1.9667, "x": 9.12, "y": -12.25, "curve": [0.504, 0, 0.832, 1]}, {"time": 2.2, "x": 8.84, "y": -9.21, "curve": "stepped"}, {"time": 2.6667, "x": 8.84, "y": -9.21}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.6333, "angle": 18.27}, {"time": 1.3667, "angle": 56.85}, {"time": 1.7, "angle": 156.82}, {"time": 2.1, "angle": -96.18}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 11.47, "y": -2.77, "curve": [0.25, 0, 0.625, 0.5]}, {"time": 0.7, "x": 5.19, "y": -6.77, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 1.3667, "x": -2.2, "y": -5.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 20.59, "y": -0.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "x": 9.59, "y": -3.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 10.47, "y": -2.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "x": 11.47, "y": -2.77, "curve": "stepped"}, {"time": 2.6667, "x": 11.47, "y": -2.77}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": -5.97, "curve": "stepped"}, {"time": 2.6667, "angle": -5.97}], "translate": [{"time": 0, "x": -8.6, "y": 7.45, "curve": "stepped"}, {"time": 2.6667, "x": -8.6, "y": 7.45}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 1.037, "y": 1.446, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "x": 1, "y": 0.885, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.037, "y": 1.446, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1, "y": 0.965, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.037, "y": 1.446, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1, "y": 0.965, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1.037, "y": 1.446, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 0.965, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.037, "y": 1.446, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 0.965, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1.097, "y": 1.446, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.057, "y": 0.986, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0.932, "y": 0.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "x": 1.016, "y": 0.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 35, "vertices": [-1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734, 0, -1.38734], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "offset": 35, "vertices": [0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481, 0, 0.86481], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "vertices": [3.5495, 0, 4.97654, 0, 5.97855, 0, 5.97855, 0, 5.32043, 0, 3.14545, 0, 0.83774, 0, -1.52307, 0, -3.961, 0, -5.40908, 0, -5.90325, 0, -5.90325, 0, -5.11678, 0, -3.56925, 0, -1.66063, 0, -0.14236, 0, 1.53759, 0, -1.29954, -3.90007, -1.29954, -3.90007, -2.84707, -3.90008, -4.0335, -3.90007, -4.46337, -3.90007, -4.33787, -3.90007, -3.65008, -3.90007, -2.53243, -3.90007, -0.14236, -3.90007, 1.11286, -3.90007, 2.55722, -3.90007, 3.64049, -3.90007, 4.50022, -3.90007, 4.51742, -3.90007, 2.50563, -3.90007, 0.68299, -3.90007, 3.76085, -3.90007, 0.49385, -8.02968, 1.71467, -8.02968, 2.47124, -8.02968, 3.02148, -8.02968, 3.10745, -8.02968, 2.50563, -8.02968, 1.74906, -8.02968, 0.94091, -8.02968, -1.79305, -8.02968, -2.65279, -8.02968, -3.09985, -8.02968, -3.11704, -8.02968, -2.80754, -8.02968, -0.08549, -8.02968, -1.07, -8.02968, -1.99381, -8.02968, -0.91575, -8.02968, 1.91951, -8.02968, 1.52181, -8.02968, 0.98577, -8.02968, 0.63995, -8.02968, 0.08662, -8.02968, -0.70878, -8.02968, -1.15836, -8.02968, -1.55606, -8.02968, -1.76356, -8.02968, -1.76356, -8.02968, -1.60794, -8.02968, -1.14107, -8.02968, -0.63962, -8.02968, 0.27683, -8.02968, 0.9339, -8.02968, 1.55639, -8.02968, 1.83305, -8.02968, -0.05171, -8.02968], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "vertices": [-1.3522, 0, -1.89583, 0, -2.27755, 0, -2.27755, 0, -2.02684, 0, -1.19827, 0, -0.31914, 0, 0.58022, 0, 1.50896, 0, 2.06061, 0, 2.24886, 0, 2.24886, 0, 1.94925, 0, 1.35972, 0, 0.63262, 0, 0.05423, 0, -0.58575, 0, 0.49507, 7.59863, 0.49507, 7.59863, 1.08461, 7.59863, 1.53659, 7.59863, 1.70035, 7.59863, 1.65254, 7.59863, 1.39052, 7.59863, 0.96474, 7.59863, 0.05424, 7.59863, -0.42394, 7.59863, -0.97417, 7.59863, -1.38685, 7.59863, -1.71437, 7.59863, -1.72092, 7.59863, -0.95452, 7.59863, -0.26018, 7.59863, -1.4327, 7.59863, -0.18812, 12.64597, -0.6532, 12.64597, -0.94142, 12.64597, -1.15103, 12.64597, -1.18378, 12.64597, -0.95452, 12.64597, -0.6663, 12.64597, -0.35843, 12.64597, 0.68308, 12.64597, 1.0106, 12.64597, 1.18091, 12.64597, 1.18746, 12.64597, 1.06955, 12.64597, 0.03258, 12.64597, 0.40763, 12.64597, 0.75956, 12.64597, 0.34887, 12.64597, -0.73123, 12.64597, -0.57973, 12.64597, -0.37552, 12.64597, -0.24378, 12.64597, -0.03299, 12.64597, 0.27002, 12.64597, 0.44129, 12.64597, 0.5928, 12.64597, 0.67184, 12.64597, 0.67184, 12.64597, 0.61256, 12.64597, 0.4347, 12.64597, 0.24368, 12.64597, -0.10545, 12.64597, -0.35576, 12.64597, -0.5929, 12.64597, -0.6983, 12.64597, 0.01971, 12.64597], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "vertices": [0.67609, 0, 0.94791, 0, 1.13877, 0, 1.13877, 0, 1.01341, 0, 0.59914, 0, 0.15957, 0, -0.29011, 0, -0.75447, 0, -1.0303, 0, -1.12443, 0, -1.12443, 0, -0.97462, 0, -0.67986, 0, -0.31631, 0, -0.02712, 0, 0.29287, 0, -0.24753, -3.9296, -0.24753, -3.9296, -0.5423, -3.9296, -0.76828, -3.9296, -0.85016, -3.9296, -0.82626, -3.9296, -0.69525, -3.9296, -0.48237, -3.9296, -0.02712, -3.9296, 0.21197, -3.9296, 0.48709, -3.9296, 0.69342, -3.9296, 0.85718, -3.9296, 0.86046, -3.9296, 0.47726, -3.9296, 0.13009, -3.9296, 0.71635, -3.9296, 0.09406, -3.92959, 0.3266, -3.92959, 0.47071, -3.92959, 0.57552, -3.92959, 0.59189, -3.92959, 0.47726, -3.92959, 0.33315, -3.92959, 0.17922, -3.92959, -0.34154, -3.92959, -0.5053, -3.92959, -0.59045, -3.92959, -0.59373, -3.92959, -0.53477, -3.92959, -0.01629, -3.92959, -0.20381, -3.92959, -0.37977, -3.92959, -0.17443, -3.92959, 0.36562, -3.92959, 0.28986, -3.92959, 0.18776, -3.92959, 0.12189, -3.92959, 0.0165, -3.92959, -0.13501, -3.92959, -0.22064, -3.92959, -0.2964, -3.92959, -0.33592, -3.92959, -0.33592, -3.92959, -0.30628, -3.92959, -0.21735, -3.92959, -0.12183, -3.92959, 0.05273, -3.92959, 0.17788, -3.92959, 0.29645, -3.92959, 0.34915, -3.92959, -0.00985, -3.92959], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "vertices": [-0.50706, 0, -0.71092, 0, -0.85406, 0, -0.85406, 0, -0.76005, 0, -0.44934, 0, -0.11967, 0, 0.21758, 0, 0.56585, 0, 0.77271, 0, 0.8433, 0, 0.8433, 0, 0.73095, 0, 0.50988, 0, 0.23723, 0, 0.02034, 0, -0.21965, 0, 0.18564, 0.46155, 0.18564, 0.46155, 0.40672, 0.46155, 0.5762, 0.46155, 0.63761, 0.46155, 0.61968, 0.46155, 0.52143, 0.46155, 0.36177, 0.46155, 0.02034, 0.46155, -0.15898, 0.46155, -0.36531, 0.46155, -0.52006, 0.46155, -0.64288, 0.46155, -0.64533, 0.46155, -0.35794, 0.46155, -0.09757, 0.46155, -0.53726, 0.46155, -0.07055, 1.58835, -0.24495, 1.58835, -0.35303, 1.58835, -0.43163, 1.58835, -0.44392, 1.58835, -0.35794, 1.58835, -0.24986, 1.58835, -0.13441, 1.58835, 0.25614, 1.58835, 0.37896, 1.58835, 0.44282, 1.58835, 0.44528, 1.58835, 0.40107, 1.58835, 0.01221, 1.58835, 0.15285, 1.58835, 0.28482, 1.58835, 0.13082, 1.58835, -0.27421, 1.58835, -0.2174, 1.58835, -0.14082, 1.58835, -0.09142, 1.58835, -0.01237, 1.58835, 0.10125, 1.58835, 0.16548, 1.58835, 0.22229, 1.58835, 0.25193, 1.58835, 0.25193, 1.58835, 0.2297, 1.58835, 0.16301, 1.58835, 0.09137, 1.58835, -0.03955, 1.58835, -0.13341, 1.58835, -0.22234, 1.58835, -0.26186, 1.58835, 0.00739, 1.58835], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667}]}}}}, "11-angry": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": "Emotion/1-<PERSON>uon/2"}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": "Emotion/1-Buon/3"}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": "Emotion/1-Buon/4"}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": "Emotion/1-Buon/5"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -10.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 7.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -10.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 4.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.37, "y": -2.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 7.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 19.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -8.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 5.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 4.02, "y": 4.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 4.27, "y": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 4.85, "y": -1.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 5, "y": 1.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 14.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 31.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -2.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 7.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.6, "y": 19.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 7.49, "y": 18.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": -5.05, "y": 16.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 1.1, "y": 17.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 15.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -6.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 3.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 3.62, "y": -2.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": -4.94, "y": -6.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 9.57, "y": -6.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "x": 5.32, "y": -3.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1667, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "offset": 34, "vertices": [4.07678, -6.08625, -0.96568, -5.92667, -0.52841, -4.92652, 0.07021, -4.16803, 1.10333, -3.91906, 2.21504, -4.03647, 3.12376, -4.5159, 3.73089, -5.26746, 4.16536, -6.84729, 4.10559, -7.66787, 3.69298, -8.60122, 3.01773, -9.28965, 1.96441, -9.81965, 0.80474, -9.79423, -0.67307, -8.42929, -1.018, -7.22405, -0.00124, -9.27298, -0.06862, -7.13017, 0.2325, -7.93965, 0.63328, -8.44806, 1.13961, -8.82461, 1.76815, -8.90084, 2.61352, -8.53326, 3.10193, -8.05298, 3.32127, -7.53037, 3.06276, -5.73078, 2.55156, -5.15127, 1.96429, -4.83975, 1.31184, -4.80783, 0.5738, -4.98729, 3.3265, -6.85799, 3.22904, -6.20981, 0.14647, -5.50696, -0.12626, -6.20471, 1.69983, -8.12028, 2.16082, -7.87428, 2.42095, -7.53127, 2.50734, -7.3074, 2.55276, -6.9463, 2.46736, -6.42239, 2.36349, -6.12452, 2.12272, -5.85632, 1.76482, -5.70904, 1.32339, -5.69506, 1.01457, -5.78726, 0.74455, -6.08463, 0.66625, -6.41073, 0.69255, -7.01206, 0.89399, -7.44898, 1.13009, -7.86432, 1.42997, -8.0551, 1.65012, -6.82708], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "offset": 34, "vertices": [-0.20367, -3.70501, 2.00495, -3.64682, 1.83915, -4.09521, 1.59668, -4.44201, 1.15129, -4.57703, 0.66228, -4.55391, 0.25285, -4.36738, -0.03167, -4.05421, -0.26169, -3.37452, -0.25639, -3.01426, -0.09968, -2.59571, 0.17806, -2.27764, 0.62513, -2.01915, 1.13277, -2.00085, 1.81353, -2.56014, 1.9949, -3.07831, 1.49838, -2.20829, 1.58221, -3.14344, 1.43005, -2.79719, 1.24191, -2.58504, 1.0109, -2.43326, 0.73419, -2.41587, 0.37399, -2.59805, 0.17264, -2.82043, 0.09, -3.05447, 0.2487, -3.83468, 0.48688, -4.07506, 0.75171, -4.19637, 1.03768, -4.19375, 1.35574, -4.09657, 0.10479, -3.34858, 0.16384, -3.62947, 1.52936, -3.85855, 1.6309, -3.54659, 0.78392, -2.75541, 0.58861, -2.87464, 0.48361, -3.03121, 0.45152, -3.13129, 0.44082, -3.29034, 0.49144, -3.51719, 0.54442, -3.64477, 0.65647, -3.75594, 0.8167, -3.81125, 1.01005, -3.80616, 1.14272, -3.75801, 1.25323, -3.62116, 1.27919, -3.47659, 1.25243, -3.21437, 1.15328, -3.02846, 1.03948, -2.85286, 0.90356, -2.77706, 0.83848, -3.31952], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "offset": 34, "vertices": [-0.86333, -2.91875, -2.06247, -2.84036, -1.95035, -2.6058, -1.8017, -2.4301, -1.55382, -2.37898, -1.29011, -2.41581, -1.07761, -2.53723, -0.93911, -2.72101, -0.84832, -3.10061, -0.86913, -3.29551, -0.97487, -3.51431, -1.14113, -3.67288, -1.39615, -3.7906, -1.67191, -3.77527, -2.01284, -3.43844, -2.0853, -3.14878, -1.8597, -3.64474, -1.85854, -3.13404, -1.79333, -3.32922, -1.702, -3.45344, -1.58452, -3.54711, -1.43552, -3.57028, -1.23124, -3.48955, -1.11115, -3.37917, -1.05477, -3.25642, -1.10191, -2.82595, -1.21891, -2.68393, -1.35613, -2.60506, -1.51125, -2.59225, -1.68841, -2.62909, -1.04812, -3.09646, -1.06614, -2.94133, -1.79434, -2.7494, -1.86483, -2.91327, -1.44546, -3.38392, -1.33378, -3.32903, -1.26909, -3.24944, -1.24671, -3.19684, -1.23302, -3.11127, -1.24915, -2.98585, -1.27148, -2.91408, -1.32668, -2.84831, -1.41068, -2.81041, -1.51566, -2.80355, -1.58992, -2.82302, -1.65658, -2.89167, -1.67782, -2.96869, -1.67639, -3.11205, -1.63193, -3.21765, -1.57906, -3.31842, -1.50918, -3.36623, -1.44694, -3.07564], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "offset": 34, "vertices": [3.20892, -1.57631, 1.66057, -1.33347, 1.83379, -1.04191, 2.04745, -0.83108, 2.37553, -0.794, 2.71371, -0.8729, 2.97544, -1.05562, 3.13374, -1.3106, 3.207, -1.81432, 3.15705, -2.06504, 2.994, -2.33688, 2.75938, -2.52318, 2.41429, -2.64611, 2.05778, -2.59372, 1.65466, -2.11617, 1.59461, -1.73137, 1.82937, -2.40207, 1.89089, -1.73889, 1.95263, -2.00002, 2.05665, -2.17213, 2.19824, -2.30768, 2.3891, -2.35531, 2.66384, -2.27447, 2.83284, -2.14519, 2.92055, -1.99248, 2.90998, -1.42777, 2.77466, -1.2295, 2.60562, -1.11093, 2.4057, -1.076, 2.17126, -1.10297, 2.94798, -1.78546, 2.94283, -1.58187, 2.01953, -1.24675, 1.90866, -1.45139, 2.39803, -2.11206, 2.54958, -2.05394, 2.64296, -1.95818, 2.67819, -1.89249, 2.70607, -1.78295, 2.69987, -1.61812, 2.67929, -1.52232, 2.61536, -1.43037, 2.51068, -1.37123, 2.37514, -1.34997, 2.27641, -1.36652, 2.18173, -1.44781, 2.14508, -1.54535, 2.13007, -1.73173, 2.17539, -1.87416, 2.23221, -2.01128, 2.31735, -2.08161, 2.43239, -1.7115], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667}]}}}}, "12-ah": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/6-Soc/2": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/2"}, {"time": 1.4, "name": "Emotion/6-Soc/2"}]}, "Emotion/6-Soc/4": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/6-Soc/5": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/6-Soc/6": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/6-Soc/7": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/6-Soc/9": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/9"}, {"time": 1.4, "name": "Emotion/6-Soc/9"}]}, "Emotion/6-Soc/10": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/10"}, {"time": 1.4, "name": "Emotion/6-Soc/10"}]}, "Emotion/6-Soc/11": {"attachment": [{"time": 0, "name": "Emotion/6-Soc/11"}, {"time": 1.4, "name": "Emotion/6-Soc/11"}]}, "Emotion/9-Gao Thet/3": {"attachment": [{"time": 0, "name": "Emotion/9-<PERSON>/3"}, {"time": 1.4, "name": "Emotion/9-<PERSON>/3"}]}, "Emotion/9-Gao Thet/4": {"attachment": [{"time": 0, "name": "Emotion/9-<PERSON>/4"}, {"time": 1.4, "name": "Emotion/9-<PERSON>/4"}]}, "Emotion/9-Gao Thet/5": {"attachment": [{"time": 0, "name": "Emotion/9-<PERSON>/5"}, {"time": 1.4, "name": "Emotion/9-<PERSON>/5"}]}, "Emotion/9-Gao Thet/2-1": {"attachment": [{"time": 0, "name": "Emotion/9-<PERSON>/2-1"}, {"time": 1.4, "name": "Emotion/9-<PERSON>/2-1"}]}, "Emotion/9-Gao Thet/2-2": {"attachment": [{"time": 0, "name": "Emotion/9-<PERSON>/2-2"}, {"time": 1.4, "name": "Emotion/9-<PERSON>/2-2"}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -0.25, "y": -23.57, "curve": "stepped"}, {"time": 1.4, "x": -0.25, "y": -23.57}], "scale": [{"time": 0, "x": 0.552, "y": 0.356, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 1, "y": 0.258}, {"time": 0.2, "x": 1, "y": 0.194}, {"time": 0.2333, "x": 0.544, "y": 0.362}, {"time": 0.2667, "x": 1, "y": 1.138}, {"time": 0.3333, "x": 1, "y": 1.272}, {"time": 0.4333, "x": 1.161, "y": 0.764}, {"time": 0.5333, "x": 1, "y": 1.05}, {"time": 0.6, "x": 1, "y": 1.414}, {"time": 0.6667, "x": 1, "y": 1.05}, {"time": 0.7333, "x": 1, "y": 1.314}, {"time": 0.8, "x": 1, "y": 1.05}, {"time": 0.8667, "x": 1, "y": 1.327}, {"time": 0.9333, "x": 1, "y": 1.05}, {"time": 1, "x": 1, "y": 1.327}, {"time": 1.0667, "x": 1, "y": 1.05}, {"time": 1.1333, "x": 1, "y": 1.327}, {"time": 1.2, "x": 1, "y": 1.05}, {"time": 1.2667, "x": 1, "y": 1.327}, {"time": 1.3333, "x": 1, "y": 1.05}, {"time": 1.4, "x": 0.552, "y": 0.356}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 1.4, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -24.14, "y": 1.94, "curve": "stepped"}, {"time": 1.4, "x": -24.14, "y": 1.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 13.95, "y": -8.32, "curve": "stepped"}, {"time": 0.1667, "x": 13.95, "y": -8.32}, {"time": 0.3, "x": 14.01, "y": -7.3, "curve": "stepped"}, {"time": 0.6667, "x": 14.01, "y": -7.3}, {"time": 1.4, "x": 13.95, "y": -8.32}], "scale": [{"time": 0, "x": 0.294, "y": 0.294, "curve": "stepped"}, {"time": 0.1667, "x": 0.294, "y": 0.294}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 0.7667, "x": 1.156, "y": 1.156}, {"time": 0.9667, "x": 0.983, "y": 0.983}, {"time": 1.3333, "x": 1.087, "y": 1.087}, {"time": 1.4, "x": 0.294, "y": 0.294}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": -1.69}, {"time": 0.6667, "angle": 20.53, "curve": "stepped"}, {"time": 1.2667, "angle": 20.53}, {"time": 1.4, "angle": -1.69}], "translate": [{"time": 0, "x": 3.39, "y": -4.27}, {"time": 0.6667, "x": -6.41, "y": -9.97, "curve": "stepped"}, {"time": 1.2667, "x": -6.41, "y": -9.97}, {"time": 1.4, "x": 3.39, "y": -4.27}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -2.78, "y": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -5.21, "y": -2.51, "curve": "stepped"}, {"time": 0.3, "x": -5.21, "y": -2.51, "curve": "stepped"}, {"time": 1.3333, "x": -5.21, "y": -2.51}, {"time": 1.4, "x": -2.78, "y": -9.33}], "scale": [{"time": 0, "x": 0.475, "y": 0.475, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 0.575, "y": 0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0.372, "y": 0.536, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.231, "y": 0.231, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.4, "x": 0.475, "y": 0.475}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": 0.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 1.8}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 0, "y": -25.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0, "y": 28.28, "curve": [0.09, 0, 0.348, 0.94]}, {"time": 0.2667, "x": 0, "y": 95.17, "curve": [0.566, -0.01, 0.914, 0.99]}, {"time": 0.4, "x": 0, "y": -26.23, "curve": [0.304, 0.01, 0.645, 0.41]}, {"time": 0.4333, "x": 0, "y": -9.03, "curve": [0.363, 0.34, 0.768, 1]}, {"time": 0.5333, "x": 0, "y": 8.93, "curve": [0.484, 0.04, 0.859, 1]}, {"time": 0.6333, "x": 0, "y": 8.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 2.73}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 1.481, "y": 0.416, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0.62, "y": 1.633, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.053, "y": 1.496, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.963, "y": 1.847, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.249, "y": 1.069, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0.916, "y": 1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.006, "y": 1.471, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1.035, "y": 1.317, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 1.199, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 36.76}, {"time": 0.6667, "angle": 5.66, "curve": "stepped"}, {"time": 1.2667, "angle": 5.66}, {"time": 1.4, "angle": 36.76}], "translate": [{"time": 0, "x": 8.18, "y": -8.59}, {"time": 0.6667, "x": 21.25, "y": -14.46, "curve": "stepped"}, {"time": 1.2667, "x": 21.25, "y": -14.46}, {"time": 1.4, "x": 8.18, "y": -8.59}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 7.29, "y": 2.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": -3.32, "y": 7.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -7.43, "y": -2.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 3.39, "y": -7.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 7.32, "y": 2.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 3.34, "y": -12.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 12.71, "y": -0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -4, "y": 9.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": -13, "y": -3.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.57, "y": -10.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}}}, "13-beat": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 3, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/16-Nhin Deu/3": {"attachment": [{"time": 0, "name": "Emotion/16-<PERSON><PERSON>/3"}]}, "Emotion/16-Nhin Deu/4": {"attachment": [{"time": 0, "name": "Emotion/16-<PERSON><PERSON>/4"}]}, "Emotion/16-Nhin Deu/5": {"attachment": [{"time": 0, "name": "Emotion/16-<PERSON><PERSON>/5"}, {"time": 3, "name": "Emotion/16-<PERSON><PERSON>/5"}]}, "Emotion/16-Nhin Deu/6": {"attachment": [{"time": 0, "name": "Emotion/16-<PERSON><PERSON>/6"}, {"time": 3, "name": "Emotion/16-<PERSON><PERSON>/6"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": -1.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": -1.34}], "translate": [{"time": 0, "x": 28.25, "y": 16.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 43.25, "y": 19.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 28.25, "y": 16.5}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.645, "y": 0.645, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 7.2, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -27.14, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 7.2, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": -22.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -20.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": -22.07}], "translate": [{"time": 0, "x": -20.55, "y": -48.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 2.11, "y": -42.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": -20.55, "y": -48.04}], "scale": [{"time": 0, "x": 0.674, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0.674, "y": 1}], "shear": [{"time": 0, "x": 0, "y": -0.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 0, "y": -0.69}]}, "bone22": {"rotate": [{"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5667, "angle": 0}], "translate": [{"time": 1.3333, "x": 0, "y": 0}, {"time": 1.4, "x": 0.06, "y": 2.91}, {"time": 1.4667, "x": -0.12, "y": 3.79}, {"time": 1.5667, "x": 0, "y": 0}], "scale": [{"time": 1.3333, "x": 1, "y": 1}, {"time": 1.4, "x": 1, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 1, "y": 0}, {"time": 1.5667, "x": 1, "y": 1}], "shear": [{"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5667, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 16.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -11.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "angle": 16.01}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 1, "y": 0.882, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "x": 1, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "offset": 34, "vertices": [-6.30141, 2.99434, -2.9314, 3.06561, -3.18789, 2.38288, -3.56059, 1.85572, -4.24115, 1.65319, -4.98701, 1.69235, -5.61021, 1.98022, -6.04177, 2.46022, -6.38734, 3.49901, -6.37637, 4.04859, -6.13398, 4.68588, -5.70772, 5.16904, -5.02362, 5.55978, -4.24899, 5.58369, -3.21483, 4.72504, -2.94222, 3.93309, -3.69285, 5.26432, -3.57235, 3.83698, -3.80179, 4.36645, -4.08711, 4.69155, -4.43822, 4.92495, -4.86026, 4.95366, -5.41136, 4.67862, -5.7203, 4.34095, -5.84822, 3.98454, -5.61231, 2.79294, -5.25083, 2.4243, -4.84802, 2.23715, -4.41157, 2.23885, -3.92547, 2.38457, -5.82801, 3.53574, -5.74015, 3.1067, -3.65865, 2.74637, -3.50128, 3.22154, -4.78717, 4.43527, -5.08608, 4.25489, -5.24755, 4.01686, -5.2973, 3.86446, -5.31488, 3.62191, -5.23944, 3.27538, -5.15964, 3.0803, -4.98954, 2.90982, -4.74554, 2.82417, -4.45053, 2.83041, -4.24772, 2.90281, -4.07806, 3.11073, -4.0373, 3.33106, -4.07604, 3.73135, -4.22584, 4.01576, -4.39802, 4.28457, -4.60483, 4.40129, -4.70843, 3.57418], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "offset": 34, "vertices": [9.04694, 4.0474, 0.60509, 4.49548, 1.37348, 6.15539, 2.4035, 7.40458, 4.14325, 7.78463, 6.00146, 7.54808, 7.50661, 6.71233, 8.49682, 5.43149, 9.16806, 2.76925, 9.03851, 1.39669, 8.31381, -0.15217, 7.15789, -1.28127, 5.37429, -2.13143, 3.43241, -2.04725, 1.0056, 0.29245, 0.47093, 2.32386, 2.10086, -1.14511, 2.06478, 2.44712, 2.54022, 1.08024, 3.19343, 0.21413, 4.02819, -0.43488, 5.07844, -0.58511, 6.50786, 0.00039, 7.34331, 0.78749, 7.72947, 1.65514, 7.3609, 4.67924, 6.52526, 5.66842, 5.55258, 6.21136, 4.46068, 6.28821, 3.21783, 6.01404, 7.76233, 2.78138, 7.6223, 3.87076, 2.4833, 5.15878, 2.00139, 3.9996, 4.99198, 0.72499, 5.77306, 1.1206, 6.22114, 1.6859, 6.3739, 2.05784, 6.46293, 2.6612, 6.33866, 3.54192, 6.17531, 4.04466, 5.78156, 4.50262, 5.18726, 4.76221, 4.44823, 4.80143, 3.92755, 4.65805, 3.46455, 4.16955, 3.32169, 3.62605, 3.34418, 2.61772, 3.66599, 1.87853, 4.04667, 1.17423, 4.54223, 0.84388, 4.95503, 2.89326], "curve": [0.25, 0, 0.75, 1]}, {"time": 3, "offset": 34, "vertices": [-6.30141, 2.99434, -2.9314, 3.06561, -3.18789, 2.38288, -3.56059, 1.85572, -4.24115, 1.65319, -4.98701, 1.69235, -5.61021, 1.98022, -6.04177, 2.46022, -6.38734, 3.49901, -6.37637, 4.04859, -6.13398, 4.68588, -5.70772, 5.16904, -5.02362, 5.55978, -4.24899, 5.58369, -3.21483, 4.72504, -2.94222, 3.93309, -3.69285, 5.26432, -3.57235, 3.83698, -3.80179, 4.36645, -4.08711, 4.69155, -4.43822, 4.92495, -4.86026, 4.95366, -5.41136, 4.67862, -5.7203, 4.34095, -5.84822, 3.98454, -5.61231, 2.79294, -5.25083, 2.4243, -4.84802, 2.23715, -4.41157, 2.23885, -3.92547, 2.38457, -5.82801, 3.53574, -5.74015, 3.1067, -3.65865, 2.74637, -3.50128, 3.22154, -4.78717, 4.43527, -5.08608, 4.25489, -5.24755, 4.01686, -5.2973, 3.86446, -5.31488, 3.62191, -5.23944, 3.27538, -5.15964, 3.0803, -4.98954, 2.90982, -4.74554, 2.82417, -4.45053, 2.83041, -4.24772, 2.90281, -4.07806, 3.11073, -4.0373, 3.33106, -4.07604, 3.73135, -4.22584, 4.01576, -4.39802, 4.28457, -4.60483, 4.40129, -4.70843, 3.57418]}]}}}}, "14-adore": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/10-Bom/2": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/2"}]}, "Emotion/10-Bom/3": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/3"}]}, "Emotion/10-Bom/4": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/4"}]}, "Emotion/10-Bom/5": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/5"}]}, "Emotion/10-Bom/6": {"color": [{"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/10-Bom/6"}]}, "Emotion/10-Bom/7": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}, {"time": 1.3667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/10-Bom/7"}]}, "Emotion/10-Bom/8": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/10-Bom/8"}]}, "Emotion/10-Bom/9": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/10-Bom/9"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": -5.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": 22.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -15.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 22.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -15.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 22.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -15.53, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.7, "angle": -0.31, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 0.8667, "angle": 0}, {"time": 1.2667, "angle": -5.54}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": -11.72, "y": -27.78, "curve": "stepped"}, {"time": 1, "x": -11.72, "y": -27.78, "curve": "stepped"}, {"time": 1.6667, "x": -11.72, "y": -27.78, "curve": "stepped"}, {"time": 2.3333, "x": -11.72, "y": -27.78, "curve": "stepped"}, {"time": 3, "x": -11.72, "y": -27.78}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": [0.327, 0.31, 0.662, 0.65]}, {"time": 1.3333, "x": 1.178, "y": 1.178, "curve": [0.332, 0.33, 0.668, 0.67]}, {"time": 1.6667, "x": 1, "y": 1, "curve": [0.338, 0.35, 0.673, 0.69]}, {"time": 2, "x": 1.165, "y": 1.165, "curve": [0.344, 0.37, 0.679, 0.71]}, {"time": 2.3333, "x": 1, "y": 1, "curve": [0.352, 0.42, 0.687, 0.76]}, {"time": 2.6667, "x": 1.126, "y": 1.126, "curve": [0.362, 0.64, 0.698, 1]}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0, "x": 26.47, "y": -56.64, "curve": "stepped"}, {"time": 1, "x": 26.47, "y": -56.64, "curve": "stepped"}, {"time": 1.6667, "x": 26.47, "y": -56.64, "curve": "stepped"}, {"time": 2.3333, "x": 26.47, "y": -56.64, "curve": "stepped"}, {"time": 3, "x": 26.47, "y": -56.64}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1, "curve": [0.327, 0.31, 0.662, 0.65]}, {"time": 1.3333, "x": 1.178, "y": 1.178, "curve": [0.332, 0.33, 0.668, 0.67]}, {"time": 1.6667, "x": 1, "y": 1, "curve": [0.338, 0.35, 0.673, 0.69]}, {"time": 2, "x": 1.165, "y": 1.165, "curve": [0.344, 0.37, 0.679, 0.71]}, {"time": 2.3333, "x": 1, "y": 1, "curve": [0.352, 0.42, 0.687, 0.76]}, {"time": 2.6667, "x": 1.126, "y": 1.126, "curve": [0.362, 0.64, 0.698, 1]}, {"time": 3, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone3": {"translate": [{"time": 0, "x": 47, "y": -9.57}]}, "bone23": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 4.21}, {"time": 0.2, "angle": -10.84}, {"time": 0.3, "angle": 4.21}, {"time": 0.4, "angle": -10.84}, {"time": 0.5, "angle": 4.21}, {"time": 0.6, "angle": -10.84}, {"time": 0.9333, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 1.7667, "angle": 0, "curve": "stepped"}, {"time": 2.0333, "angle": 0, "curve": "stepped"}, {"time": 2.1, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.4333, "angle": 0, "curve": "stepped"}, {"time": 2.7, "angle": 0, "curve": "stepped"}, {"time": 2.7667, "angle": 0}], "translate": [{"time": 0, "x": -26.49, "y": 14.82}, {"time": 0.1, "x": -28.03, "y": 23.35}, {"time": 0.2, "x": -22.18, "y": 16.82}, {"time": 0.3, "x": -28.03, "y": 23.35}, {"time": 0.4, "x": -22.18, "y": 16.82}, {"time": 0.5, "x": -28.03, "y": 23.35}, {"time": 0.6, "x": -22.18, "y": 16.82}, {"time": 0.9333, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 1, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 1.3333, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 1.4, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 1.7, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 1.7667, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 2.0333, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 2.1, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 2.3667, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 2.4333, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 2.7, "x": -26.49, "y": 14.82, "curve": "stepped"}, {"time": 2.7667, "x": -26.49, "y": 14.82}], "scale": [{"time": 0, "x": 0.679, "y": 0.679}, {"time": 0.9333, "x": 0.674, "y": 0.674}, {"time": 1, "x": 1, "y": 1}, {"time": 1.1, "x": 0.766, "y": 0.766}, {"time": 1.3333, "x": 0.674, "y": 0.674}, {"time": 1.4, "x": 1, "y": 1}, {"time": 1.5, "x": 0.766, "y": 0.766}, {"time": 1.7, "x": 0.674, "y": 0.674}, {"time": 1.7667, "x": 1, "y": 1}, {"time": 1.8667, "x": 0.766, "y": 0.766}, {"time": 2.0333, "x": 0.674, "y": 0.674}, {"time": 2.1, "x": 1, "y": 1}, {"time": 2.2, "x": 0.766, "y": 0.766}, {"time": 2.3667, "x": 0.674, "y": 0.674}, {"time": 2.4333, "x": 1, "y": 1}, {"time": 2.5333, "x": 0.766, "y": 0.766}, {"time": 2.7, "x": 0.674, "y": 0.674}, {"time": 2.7667, "x": 1, "y": 1}, {"time": 2.8667, "x": 0.766, "y": 0.766}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7667, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 4.21}, {"time": 0.2, "angle": 343.45}, {"time": 0.3, "angle": 4.21}, {"time": 0.4, "angle": 343.45}, {"time": 0.5, "angle": 4.21}, {"time": 0.6, "angle": 343.45}, {"time": 0.9333, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 1.7, "angle": 0, "curve": "stepped"}, {"time": 1.7667, "angle": 0, "curve": "stepped"}, {"time": 2.0333, "angle": 0, "curve": "stepped"}, {"time": 2.1, "angle": 0, "curve": "stepped"}, {"time": 2.3667, "angle": 0, "curve": "stepped"}, {"time": 2.4333, "angle": 0, "curve": "stepped"}, {"time": 2.7, "angle": 0, "curve": "stepped"}, {"time": 2.7667, "angle": 0}], "translate": [{"time": 0, "x": 0.45, "y": 0.04}, {"time": 0.1, "x": 10.26, "y": -4.91}, {"time": 0.2, "x": 1.48, "y": 2.13}, {"time": 0.3, "x": 10.26, "y": -4.91}, {"time": 0.4, "x": 1.48, "y": 2.13}, {"time": 0.5, "x": 10.26, "y": -4.91}, {"time": 0.6, "x": 1.48, "y": 2.13}, {"time": 0.9333, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 1, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 1.3333, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 1.4, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 1.7, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 1.7667, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 2.0333, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 2.1, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 2.3667, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 2.4333, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 2.7, "x": 0.45, "y": 0.04, "curve": "stepped"}, {"time": 2.7667, "x": 0.45, "y": 0.04}], "scale": [{"time": 0, "x": 0.679, "y": 0.679}, {"time": 0.9333, "x": 0.674, "y": 0.674}, {"time": 1, "x": 1, "y": 1}, {"time": 1.1, "x": 0.766, "y": 0.766}, {"time": 1.3333, "x": 0.674, "y": 0.674}, {"time": 1.4, "x": 1, "y": 1}, {"time": 1.5, "x": 0.766, "y": 0.766}, {"time": 1.7, "x": 0.674, "y": 0.674}, {"time": 1.7667, "x": 1, "y": 1}, {"time": 1.8667, "x": 0.766, "y": 0.766}, {"time": 2.0333, "x": 0.674, "y": 0.674}, {"time": 2.1, "x": 1, "y": 1}, {"time": 2.2, "x": 0.766, "y": 0.766}, {"time": 2.3667, "x": 0.674, "y": 0.674}, {"time": 2.4333, "x": 1, "y": 1}, {"time": 2.5333, "x": 0.766, "y": 0.766}, {"time": 2.7, "x": 0.674, "y": 0.674}, {"time": 2.7667, "x": 1, "y": 1}, {"time": 2.8667, "x": 0.766, "y": 0.766}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7667, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": -10.31}], "translate": [{"time": 0, "x": -8.66, "y": 0.76}]}, "bone13": {"rotate": [{"time": 0, "angle": 40.96}], "translate": [{"time": 0, "x": 19.91, "y": 6.08}]}, "bone12": {"rotate": [{"time": 0, "angle": 8.14}], "translate": [{"time": 0, "x": -3.6, "y": -33.75}]}, "bone2": {"rotate": [{"time": 0, "angle": 136.73}], "translate": [{"time": 0, "x": 60.6, "y": -8.25}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0}]}}}}, "15-beat_shot": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 0.1667, "name": null}]}, "Emotion/1-Buon/3": {"color": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/1-Buon/3"}, {"time": 0.1333, "name": "Emotion/1-Buon/3"}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/17-WTH/2": {"attachment": [{"time": 0, "name": "Emotion/17-WTH/2"}, {"time": 0.1667, "name": "Emotion/17-WTH/2"}]}, "Emotion/17-WTH/3": {"attachment": [{"time": 0, "name": "Emotion/17-WTH/3"}, {"time": 0.2, "name": "Emotion/17-WTH/3"}]}, "Emotion/17-WTH/6": {"attachment": [{"time": 0, "name": "Emotion/17-WTH/6"}, {"time": 0.3, "name": "Emotion/17-WTH/6"}]}, "Emotion/17-WTH/7": {"attachment": [{"time": 0, "name": "Emotion/17-WTH/7"}, {"time": 0.1667, "name": "Emotion/17-WTH/7"}]}, "Emotion/17-WTH/8": {"attachment": [{"time": 0, "name": "Emotion/17-WTH/8"}, {"time": 0.1667, "name": "Emotion/17-WTH/8"}]}, "Emotion/17-WTH/9": {"attachment": [{"time": 0, "name": "Emotion/17-WTH/9"}]}, "Emotion/17-WTH/10": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/17-WTH/10"}, {"time": 0.1, "name": "Emotion/17-WTH/10"}]}, "Emotion/17-WTH/11": {"color": [{"time": 1.4667, "color": "ffffffff"}, {"time": 1.9333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/17-WTH/7"}]}, "Emotion/17-WTH/12": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2333, "color": "ffffffff"}, {"time": 1.5, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/17-WTH/7"}]}, "Emotion/2-NgacNhien/5": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/5"}]}, "Emotion/2-NgacNhien/6": {"color": [{"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/6"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.313, 0, 0.647, 0.35]}, {"time": 0.1333, "x": 0, "y": 1.1, "curve": [0.325, 0.27, 0.659, 0.61]}, {"time": 0.2, "x": 0, "y": -6.39, "curve": [0.323, 0.28, 0.656, 0.61]}, {"time": 0.3, "x": 0, "y": 3.64, "curve": [0.322, 0.29, 0.656, 0.62]}, {"time": 0.4333, "x": 0, "y": 2.28, "curve": [0.323, 0.3, 0.658, 0.63]}, {"time": 0.6, "x": 0, "y": 3.11, "curve": [0.347, 0.38, 0.757, 1]}, {"time": 1.9333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.313, 0, 0.647, 0.35]}, {"time": 0.1333, "x": 1.024, "y": 1.027, "curve": [0.325, 0.27, 0.659, 0.61]}, {"time": 0.2, "x": 1.266, "y": 0.852, "curve": [0.323, 0.28, 0.656, 0.61]}, {"time": 0.3, "x": 1.05, "y": 1.086, "curve": [0.322, 0.29, 0.656, 0.62]}, {"time": 0.4333, "x": 1.116, "y": 1.042, "curve": [0.323, 0.3, 0.658, 0.63]}, {"time": 0.6, "x": 1.061, "y": 1.069, "curve": [0.325, 0.31, 0.661, 0.65]}, {"time": 0.8333, "x": 1.108, "y": 1.055, "curve": [0.332, 0.33, 0.669, 0.67]}, {"time": 1.1333, "x": 1.053, "y": 1.036, "curve": [0.381, 0.54, 0.744, 1]}, {"time": 1.9333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 13.58, "curve": "stepped"}, {"time": 0.1667, "angle": 13.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 13.5, "curve": "stepped"}, {"time": 0.3333, "angle": 13.5, "curve": "stepped"}, {"time": 0.6333, "angle": 13.5}], "translate": [{"time": 0, "x": -12.4, "y": 1.2, "curve": "stepped"}, {"time": 0.1667, "x": -12.4, "y": 1.2, "curve": "stepped"}, {"time": 0.2333, "x": -12.4, "y": 1.2, "curve": "stepped"}, {"time": 0.3333, "x": -12.4, "y": 1.2, "curve": "stepped"}, {"time": 0.6333, "x": -12.4, "y": 1.2}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 1.125, "y": 1.125}, {"time": 0.3333, "x": 1.012, "y": 1.012}, {"time": 0.4333, "x": 1.097, "y": 1.097}, {"time": 0.6333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 15.04}], "translate": [{"time": 0, "x": -19.45, "y": 8.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": -10.97, "y": 11.78}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 13.08}], "translate": [{"time": 0, "x": -18.41, "y": -7.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": -25.6, "y": -10.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 1, "y": 1.993}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 1.1667, "angle": 0}], "translate": [{"time": 0, "x": -3.58, "y": -6.94, "curve": "stepped"}, {"time": 0.2, "x": -3.58, "y": -6.94, "curve": "stepped"}, {"time": 0.3, "x": -3.58, "y": -6.94, "curve": "stepped"}, {"time": 1.1667, "x": -3.58, "y": -6.94}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.3, "x": 1.03, "y": 2.403}, {"time": 0.6333, "x": 0.964, "y": 1.035}, {"time": 0.8667, "x": 0.98, "y": 1.047}, {"time": 1.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 63.73, "curve": "stepped"}, {"time": 0.3, "angle": 63.73, "curve": "stepped"}, {"time": 1.2667, "angle": 63.73}], "translate": [{"time": 0, "x": -16.2, "y": 34.41, "curve": "stepped"}, {"time": 0.3, "x": -16.2, "y": 34.41}, {"time": 1.2667, "x": -30.28, "y": 12.38}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0}, {"time": 1.2667, "x": 1, "y": 1}], "shear": [{"time": 1.2667, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": -8.04, "curve": "stepped"}, {"time": 0.1667, "angle": -8.04}, {"time": 0.2667, "angle": -17.75}, {"time": 1.0333, "angle": 15.51}], "translate": [{"time": 0, "x": 6.4, "y": 6.18, "curve": "stepped"}, {"time": 0.1667, "x": 6.4, "y": 6.18}, {"time": 0.2667, "x": -15.16, "y": 3.07}, {"time": 1.0333, "x": -18.46, "y": -8.07}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.0333, "x": 1, "y": 1}], "shear": [{"time": 0.2667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": -329.25, "curve": "stepped"}, {"time": 0.1, "angle": -329.25}, {"time": 0.1667, "angle": -336.88}, {"time": 0.2667, "angle": 2.62}, {"time": 0.3667, "angle": 0}, {"time": 0.4667, "angle": -6.62}, {"time": 0.5667, "angle": -3.5}], "translate": [{"time": 0, "x": -35.13, "y": 203.08, "curve": [0.25, 0, 0.793, 0.1]}, {"time": 0.1, "x": 10.97, "y": 203.08, "curve": [0.25, 0, 0.793, 0.1]}, {"time": 0.1667, "x": 6.39, "y": -13.86, "curve": [0.102, 0.01, 0.566, 1.01]}, {"time": 0.2667, "x": -3.37, "y": 2.88, "curve": [0.574, 0, 0.883, 0.98]}, {"time": 0.3667, "x": 6.39, "y": -13.86, "curve": [0.117, -0.01, 0.578, 0.97]}, {"time": 0.4667, "x": 6.5, "y": -14.47, "curve": [0.391, 0, 0.844, 1.01]}, {"time": 0.5667, "x": 6.39, "y": -13.86}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0}, {"time": 0.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 15.18, "curve": "stepped"}, {"time": 0.3333, "angle": 15.18, "curve": "stepped"}, {"time": 0.5333, "angle": 15.18}], "translate": [{"time": 0, "x": -27.48, "y": 2.57, "curve": "stepped"}, {"time": 0.3333, "x": -27.48, "y": 2.57, "curve": "stepped"}, {"time": 0.5333, "x": -27.48, "y": 2.57}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.5333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 2.41, "curve": "stepped"}, {"time": 0.1667, "angle": 2.41, "curve": "stepped"}, {"time": 0.4667, "angle": 2.41}], "translate": [{"time": 0, "x": 16.2, "y": 0.13, "curve": "stepped"}, {"time": 0.1667, "x": 16.2, "y": 0.13, "curve": "stepped"}, {"time": 0.4667, "x": 16.2, "y": 0.13}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4667, "x": 1, "y": 1}], "shear": [{"time": 0.4667, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 140.27, "curve": "stepped"}, {"time": 0.4, "angle": 140.27}, {"time": 0.7, "angle": 155.6}, {"time": 1.9333, "angle": 122.66}], "translate": [{"time": 0, "x": 5.08, "y": 32.99, "curve": "stepped"}, {"time": 0.4, "x": 5.08, "y": 32.99}, {"time": 0.7, "x": 38.51, "y": 12.23}, {"time": 1.9333, "x": 41.94, "y": -80.31}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.7, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9333, "x": 1, "y": 1}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0}, {"time": 1, "angle": 91.99}, {"time": 1.4, "angle": 80.46}], "translate": [{"time": 0, "x": -49.96, "y": -10.7, "curve": "stepped"}, {"time": 0.4667, "x": -49.96, "y": -10.7}, {"time": 1, "x": -73.42, "y": -18.57}, {"time": 1.4, "x": -118.56, "y": -17.98}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "offset": 34, "vertices": [0.6003, 3.21966, 0.6003, -3.26192, 1.33273, -2.74108, 1.89426, -2.00321, 2.09771, -0.68665, 2.03832, 0.74566, 1.71279, 1.93202, 1.18381, 2.74222, 0.05261, 3.36433, -0.54147, 3.32093, -1.22507, 2.82902, -1.73777, 1.98989, -2.14468, 0.65886, -2.15282, -0.83132, -1.20065, -2.78447, -0.33802, -3.27638, 0, 0, -0.2485, -2.06109, -0.8263, -1.64152, -1.18438, -1.10621, -1.4448, -0.4407, -1.48549, 0.3695, -1.20066, 1.44012, -0.84258, 2.04776, -0.46009, 2.30818, 0.83387, 1.90308, 1.24078, 1.2231, 1.45237, 0.45631, 1.4605, -0.38282, 1.31402, -1.32323, 0.0257, 2.28757, 0.49166, 2.13608, 0.92889, -1.85084, 0.41866, -2.17271, -0.92325, 0.25005, -0.73502, 0.83201, -0.48132, 1.1521, -0.31764, 1.25394, -0.05576, 1.29758, 0.3207, 1.16664, 0.53348, 1.02115, 0.72171, 0.70107, 0.81991, 0.2355, 0.81991, -0.33191, 0.74626, -0.72474, 0.52529, -1.05937, 0.28796, -1.14666, -0.14578, -1.08847, -0.45677, -0.81203, -0.75139, -0.49195, -0.88233, -0.09913, 0.00971, 0.13366], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "offset": 34, "vertices": [-1.03902, -3.57699, -1.03902, -3.12625, -2.30674, -3.16247, -3.27865, -3.21378, -3.6308, -3.30534, -3.52799, -3.40494, -2.96457, -3.48744, -2.04899, -3.54379, -0.09107, -3.58705, 0.9372, -3.58403, 2.1204, -3.54982, 3.00781, -3.49147, 3.71209, -3.39891, 3.72617, -3.29528, 2.07814, -3.15945, 0.58505, -3.12523, 3.07021, -3.37266, 0.43011, -3.20976, 1.4302, -3.23893, 2.04997, -3.27616, 2.50072, -3.32244, 2.57114, -3.37878, 2.07814, -3.45323, 1.45837, -3.49549, 0.79634, -3.5136, -1.4433, -3.48544, -2.14758, -3.43814, -2.51381, -3.38482, -2.5279, -3.32647, -2.27435, -3.26107, -0.04448, -3.51217, -0.85098, -3.50163, -1.60776, -3.22438, -0.72463, -3.20199, 1.59799, -3.37048, 1.2722, -3.41095, 0.83309, -3.4332, 0.54979, -3.44029, 0.09651, -3.44332, -0.55508, -3.43422, -0.92336, -3.4241, -1.24915, -3.40184, -1.41914, -3.36947, -1.41914, -3.33, -1.29165, -3.30269, -0.9092, -3.27942, -0.49842, -3.27335, 0.25233, -3.27739, 0.79059, -3.29662, 1.30053, -3.31888, 1.52717, -3.34619, -0.01681, -3.36238], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "offset": 34, "vertices": [0.45097, 3.38876, 0.45097, -4.23837, 1.0012, -3.62546, 1.42304, -2.7572, 1.57588, -1.20794, 1.53127, 0.47751, 1.28671, 1.87355, 0.88932, 2.82693, 0.03952, 3.559, -0.40677, 3.50794, -0.92032, 2.92909, -1.30549, 1.94164, -1.61118, 0.37536, -1.61729, -1.37819, -0.90198, -3.67655, -0.25393, -4.25538, 0, 0, -0.18668, -2.8253, -0.62075, -2.33158, -0.88976, -1.70166, -1.0854, -0.91852, -1.11597, 0.03487, -0.90199, 1.2947, -0.63298, 2.00975, -0.34564, 2.31619, 0.62644, 1.83949, 0.93212, 1.03933, 1.09108, 0.13701, 1.09719, -0.85042, 0.98714, -1.95704, 0.0193, 2.29192, 0.36935, 2.11368, 0.69781, -2.5779, 0.31451, -2.95665, -0.69358, -0.1057, -0.55218, 0.57912, -0.36159, 0.95578, -0.23863, 1.07562, -0.04189, 1.12698, 0.24092, 0.97289, 0.40077, 0.80169, 0.54217, 0.42504, 0.61596, -0.12282, 0.61595, -0.79051, 0.56062, -1.25277, 0.39462, -1.64654, 0.21633, -1.74927, -0.10952, -1.68078, -0.34315, -1.35549, -0.56447, -0.97884, -0.66284, -0.51659, 0.00729, -0.24266], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "offset": 34, "vertices": [-1.19619, 2.04712, -1.19619, -2.82062, -2.65568, -2.42945, -3.77462, -1.87531, -4.18003, -0.88656, -4.06166, 0.18913, -3.41301, 1.0801, -2.35895, 1.68855, -0.10484, 2.15577, 1.07897, 2.12318, 2.44115, 1.75376, 3.46279, 1.12355, 4.27361, 0.12393, 4.28982, -0.99521, 2.3925, -2.46205, 0.67355, -2.83147, 3.33293, -2.59466, 0.49517, -1.91878, 1.64654, -1.60368, 2.36006, -1.20165, 2.87899, -0.70184, 2.96007, -0.09338, 2.3925, 0.71067, 1.67897, 1.16702, 0.9168, 1.36259, -1.66162, 1.05836, -2.47245, 0.54769, -2.89409, -0.02819, -2.9103, -0.65838, -2.6184, -1.36464, -0.05121, 1.3471, -0.97971, 1.23335, -1.85096, -1.76088, -0.83424, -2.0026, 1.83972, -0.18309, 1.46464, 0.25397, 0.95911, 0.49436, 0.63295, 0.57084, 0.11111, 0.60363, -0.63904, 0.50528, -1.06304, 0.39602, -1.43812, 0.15564, -1.6338, -0.19402, -1.63381, -0.62015, -1.48704, -0.91516, -1.04674, -1.16647, -0.57381, -1.23204, 0.29049, -1.18833, 0.91018, -0.98073, 1.49726, -0.74034, 1.75818, -0.44532, -0.01935, -0.2705], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "offset": 34, "vertices": [-1.05882, 5.84195, -1.05882, -6.57953, -2.35071, -5.58135, -3.34115, -4.1673, -3.7, -1.6442, -3.59523, 1.10073, -3.02107, 3.3743, -2.08805, 4.92697, -0.0928, 6.11921, 0.95506, 6.03604, 2.16082, 5.09333, 3.06513, 3.48519, 3.78284, 0.93437, 3.79719, -1.92146, 2.11775, -5.66453, 0.5962, -6.60723, 3.82363, -2.5294, 0.4383, -4.27822, 1.45745, -3.47414, 2.08903, -2.44826, 2.54837, -1.17285, 2.62015, 0.37984, 2.11775, 2.43159, 1.48616, 3.5961, 0.81152, 4.09518, -1.47081, 3.31884, -2.18852, 2.0157, -2.56174, 0.54619, -2.57609, -1.06194, -2.31771, -2.86416, -0.04533, 4.05566, -0.8672, 3.76537, -1.6384, -3.8753, -0.73844, -4.49213, 1.62845, 0.15092, 1.29645, 1.26621, 0.84897, 1.87962, 0.56027, 2.0748, 0.09835, 2.15845, -0.56566, 1.9075, -0.94096, 1.62868, -1.27297, 1.01527, -1.44618, 0.12303, -1.44618, -0.96438, -1.31627, -1.7172, -0.92653, -2.35849, -0.50792, -2.52579, 0.25713, -2.41426, 0.80566, -1.8845, 1.32532, -1.27108, 1.55627, -0.51826, -0.01713, -0.07214], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "offset": 34, "vertices": [-1.87166, 2.72572, -1.87166, -3.28735, -4.15529, -2.80413, -5.90607, -2.11962, -6.54041, -0.89821, -6.35521, 0.43057, -5.34027, 1.53117, -3.69099, 2.28279, -0.16405, 2.85994, 1.68823, 2.81968, 3.81963, 2.36333, 5.41816, 1.58485, 6.68683, 0.35003, 6.71221, -1.03244, 3.7435, -2.84441, 1.05389, -3.30075, 5.33945, -3.84486, 0.77478, -2.17331, 2.57631, -1.78407, 3.69275, -1.28745, 4.5047, -0.67004, 4.63157, 0.08159, 3.7435, 1.07482, 2.62706, 1.63853, 1.4345, 1.88014, -2.59991, 1.50432, -3.8686, 0.87349, -4.52832, 0.16212, -4.55369, -0.61636, -4.09696, -1.48879, -0.08012, 1.86101, -1.53293, 1.72048, -2.89616, -1.97826, -1.30532, -2.27685, 2.87857, -0.02923, 2.2917, 0.51067, 1.5007, 0.80762, 0.99037, 0.9021, 0.17385, 0.94259, -0.9999, 0.82111, -1.66332, 0.68614, -2.25019, 0.3892, -2.55639, -0.04272, -2.55639, -0.56912, -2.32675, -0.93356, -1.6378, -1.244, -0.89783, -1.32498, 0.45453, -1.27099, 1.42414, -1.01454, 2.34273, -0.7176, 2.75099, -0.35317, -0.03028, -0.13721], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "offset": 34, "vertices": [-1.34049, 4.92153, -1.3405, -5.36275, -2.97605, -4.53631, -4.22997, -3.36556, -4.68429, -1.27657, -4.55165, 0.99607, -3.82474, 2.87846, -2.64351, 4.16397, -0.11749, 5.15108, 1.20913, 5.08222, 2.73565, 4.30172, 3.88053, 2.97027, 4.78916, 0.85833, 4.80734, -1.50613, 2.68113, -4.60518, 0.75481, -5.38568, 4.35851, -2.94849, 0.5549, -3.45739, 1.84517, -2.79166, 2.64477, -1.94229, 3.22631, -0.88632, 3.31717, 0.39921, 2.68112, 2.09795, 1.88152, 3.06208, 1.0274, 3.4753, -1.86208, 2.83254, -2.77072, 1.75361, -3.24322, 0.53694, -3.26139, -0.7945, -2.93428, -2.28663, -0.05738, 3.44259, -1.09789, 3.20223, -2.07426, -3.1238, -0.93488, -3.63449, 2.06165, 0.20968, 1.64134, 1.13307, 1.07481, 1.64095, 0.70931, 1.80254, 0.12451, 1.8718, -0.71613, 1.66403, -1.19128, 1.43318, -1.61161, 0.92531, -1.8309, 0.18659, -1.83091, -0.71372, -1.66643, -1.33701, -1.17301, -1.86797, -0.64303, -2.00648, 0.32554, -1.91414, 1.01998, -1.47552, 1.67789, -0.96765, 1.97028, -0.34436, -0.02169, 0.025], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "offset": 34, "vertices": [-1.43516, 1.95641, -1.43517, -2.1318, -3.18622, -1.80327, -4.5287, -1.33788, -5.0151, -0.50746, -4.8731, 0.39596, -4.09485, 1.14425, -2.8302, 1.65526, -0.12579, 2.04766, 1.29452, 2.02029, 2.92884, 1.71002, 4.15458, 1.18074, 5.12739, 0.3412, 5.14685, -0.59872, 2.87047, -1.83065, 0.80811, -2.14092, 5.48859, -3.51078, 0.59409, -1.37438, 1.97548, -1.10974, 2.83156, -0.7721, 3.45416, -0.35233, 3.55144, 0.15869, 2.87047, 0.83398, 2.0144, 1.21724, 1.09996, 1.3815, -1.99358, 1.12599, -2.96639, 0.6971, -3.47225, 0.21345, -3.49171, -0.31583, -3.1415, -0.90898, -0.06143, 1.3685, -1.17543, 1.27295, -2.22074, -1.24177, -1.0009, -1.44478, 2.20726, 0.08335, 1.75725, 0.45042, 1.15072, 0.65231, 0.75941, 0.71654, 0.13331, 0.74408, -0.76671, 0.66148, -1.27541, 0.56972, -1.72542, 0.36783, -1.96021, 0.07417, -1.96021, -0.28372, -1.78412, -0.53149, -1.25585, -0.74255, -0.68845, -0.79762, 0.34853, -0.76091, 1.09202, -0.58655, 1.79638, -0.38466, 2.10943, -0.13689, -0.02322, 0.00994], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333}]}}}}, "16-extreme-sexy-girl": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 1, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/14-Khoc Thet/1": {"attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/1"}]}, "Emotion/14-Khoc Thet/3": {"attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/3"}, {"time": 1, "name": "Emotion/14-<PERSON><PERSON><PERSON>/3"}]}, "Emotion/14-Khoc Thet/4": {"attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/4"}, {"time": 1, "name": "Emotion/14-<PERSON><PERSON><PERSON>/4"}]}, "Emotion/14-Khoc Thet/7": {"attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/7"}]}, "Emotion/14-Khoc Thet/8": {"attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/8"}]}, "Emotion/14-Khoc Thet/9": {"attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/9"}]}, "Emotion/14-Khoc Thet/5-1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2, "color": "ffffffff"}, {"time": 0.5, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5333, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-1"}]}, "Emotion/14-Khoc Thet/5-2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-2"}]}, "Emotion/14-Khoc Thet/5-3": {"color": [{"time": 0, "color": "ffffff73"}, {"time": 0.1333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.1667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8333, "color": "ffffffff"}, {"time": 1, "color": "ffffff73"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-1"}, {"time": 1, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-1"}]}, "Emotion/14-Khoc Thet/5-4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-1"}, {"time": 1, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-1"}]}, "Emotion/14-Khoc Thet/5-5": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2333, "color": "ffffffff"}, {"time": 0.4, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-2"}]}, "Emotion/14-Khoc Thet/5-6": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3667, "color": "ffffff00"}, {"time": 0.5667, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/5-2"}]}, "Emotion/14-Khoc Thet/6-2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/6-2"}, {"time": 1, "name": "Emotion/14-<PERSON><PERSON><PERSON>/6-2"}]}, "Emotion/14-Khoc Thet/6-3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/6-2"}]}, "Emotion/14-Khoc Thet/6-4": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 1, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/14-<PERSON><PERSON><PERSON>/6-2"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": -1.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 3.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -1.31}], "scale": [{"time": 0, "x": 1.125, "y": 0.974, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.967, "y": 1.069, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.125, "y": 0.974}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 16.83, "y": 15.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 8.34, "y": 21.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 16.83, "y": 15.32}], "scale": [{"time": 0, "x": 1.247, "y": 1.247, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.773, "y": 0.553, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.247, "y": 1.247}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 12.34, "curve": "stepped"}, {"time": 1, "x": 0, "y": 12.34}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.765, "y": 0.403, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": -166.22, "curve": "stepped"}, {"time": 0.5, "angle": -166.22, "curve": "stepped"}, {"time": 1, "angle": -166.22}], "translate": [{"time": 0, "x": 251.31, "y": 69.16}, {"time": 0.5, "x": 263.97, "y": 8.14}, {"time": 0.5333, "x": 240.92, "y": 84.07}, {"time": 1, "x": 251.31, "y": 69.16}], "scale": [{"time": 0, "x": 0.822, "y": 0.822, "curve": "stepped"}, {"time": 0.5, "x": 0.822, "y": 0.822}, {"time": 0.5333, "x": 0.179, "y": 0.179}, {"time": 1, "x": 0.822, "y": 0.822}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": -179.34}, {"time": 0.1333, "angle": -179.65}, {"time": 0.4333, "angle": -177.34}, {"time": 1, "angle": -179.34}], "translate": [{"time": 0, "x": 246.64, "y": 20.65}, {"time": 0.1333, "x": 243.07, "y": 2.53}, {"time": 0.1667, "x": 243.78, "y": 76.09}, {"time": 0.4333, "x": 241.06, "y": 67.52}, {"time": 1, "x": 246.64, "y": 20.65}], "scale": [{"time": 0, "x": 0.789, "y": 0.789}, {"time": 0.1333, "x": 1, "y": 1}, {"time": 0.1667, "x": 0, "y": 0}, {"time": 0.4333, "x": 0.632, "y": 0.632}, {"time": 1, "x": 0.789, "y": 0.789}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": -245.71}, {"time": 0.6, "angle": 114.29}, {"time": 1, "angle": -245.71}], "translate": [{"time": 0, "x": 211.62, "y": 60.41}, {"time": 0.6, "x": 211.93, "y": 3.34}, {"time": 0.6333, "x": 218.54, "y": 75.87}, {"time": 1, "x": 211.62, "y": 60.41}], "scale": [{"time": 0, "x": 0.79, "y": 0.329}, {"time": 0.6, "x": 1, "y": 0.416}, {"time": 0.6333, "x": 0, "y": 0}, {"time": 1, "x": 0.79, "y": 0.329}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": -205.76}, {"time": 0.4, "angle": -203.95}, {"time": 1, "angle": -205.76}], "translate": [{"time": 0, "x": 197.59, "y": 54.71}, {"time": 0.4, "x": 180.08, "y": 6.21}, {"time": 0.7, "x": 219.47, "y": 79.76}, {"time": 1, "x": 197.59, "y": 54.71}], "scale": [{"time": 0, "x": 0.878, "y": 0.878}, {"time": 0.4, "x": 1, "y": 1}, {"time": 0.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0}, {"time": 1, "x": 0.878, "y": 0.878}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": -159.64}, {"time": 0.3333, "angle": -162.58, "curve": "stepped"}, {"time": 0.3667, "angle": -162.58}, {"time": 1, "angle": -159.64}], "translate": [{"time": 0, "x": 263.7, "y": 27.15}, {"time": 0.3333, "x": 273.68, "y": -27.7}, {"time": 0.3667, "x": 241.54, "y": 81.99}, {"time": 0.7, "x": 248.24, "y": 62.62}, {"time": 1, "x": 263.7, "y": 27.15}], "scale": [{"time": 0, "x": 0.773, "y": 0.773}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.3667, "x": 0, "y": 0}, {"time": 0.7, "x": 0.787, "y": 0.787}, {"time": 1, "x": 0.773, "y": 0.773}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3667, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": -159.86}, {"time": 0.3, "angle": -154.65}, {"time": 0.5667, "angle": -151.3}, {"time": 1, "angle": -159.86}], "translate": [{"time": 0, "x": 213.29, "y": 59.83}, {"time": 0.3, "x": 213.84, "y": 54.85}, {"time": 0.5667, "x": 190.58, "y": 6.87}, {"time": 0.6, "x": 220.7, "y": 70.67}, {"time": 1, "x": 213.29, "y": 59.83}], "scale": [{"time": 0, "x": 0.35, "y": 0.141}, {"time": 0.3, "x": 1, "y": 0.404, "curve": "stepped"}, {"time": 0.5667, "x": 1, "y": 0.404}, {"time": 0.6, "x": 0, "y": 0}, {"time": 1, "x": 0.35, "y": 0.141}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": -115.84}, {"time": 0.8667, "angle": -120.53}], "translate": [{"time": 0, "x": 247.99, "y": 51.98}, {"time": 0.8667, "x": 273.73, "y": -18.31}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 1, "y": 0.452}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 149.64}, {"time": 0.3333, "angle": -213.59}, {"time": 0.7, "angle": -210.36}, {"time": 1, "angle": 149.64}], "translate": [{"time": 0, "x": 211.84, "y": 59.38}, {"time": 0.3, "x": 216.05, "y": 15.69}, {"time": 0.3333, "x": 218.61, "y": 70.72}, {"time": 0.7, "x": 219.26, "y": 75.37}, {"time": 1, "x": 211.84, "y": 59.38}], "scale": [{"time": 0, "x": 1.319, "y": 0.397, "curve": "stepped"}, {"time": 0.3, "x": 1.319, "y": 0.397}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7, "x": 0.876, "y": 0.263}, {"time": 1, "x": 1.319, "y": 0.397}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.188, "y": 1.338, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0}], "translate": [{"time": 0, "x": -8.5, "y": 1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -1.29, "y": -3.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -8.5, "y": 1.55}], "scale": [{"time": 0, "x": 1.247, "y": 1.247, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0.773, "y": 0.553, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.247, "y": 1.247}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 14.91, "curve": "stepped"}, {"time": 1, "angle": 14.91}], "translate": [{"time": 0, "x": 40, "y": -12.93}, {"time": 0.4667, "x": 38.53, "y": -13.83}, {"time": 0.5333, "x": 48.04, "y": -9.92}, {"time": 0.6333, "x": 39.48, "y": -14.12}, {"time": 1, "x": 40, "y": -12.93}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4667, "x": 0.683, "y": 1}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 31.79, "curve": "stepped"}, {"time": 1, "angle": 31.79}], "translate": [{"time": 0, "x": -29.05, "y": -17.11}, {"time": 0.4667, "x": -27.7, "y": -17.52}, {"time": 0.5333, "x": -39.41, "y": -12.82}, {"time": 0.6333, "x": -28.18, "y": -17.41}, {"time": 1, "x": -29.05, "y": -17.11}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4667, "x": 0.621, "y": 1}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -281.1, "y": -2.15}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -13.53, "y": -20.5}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": -179.01}, {"time": 0.3, "angle": -195.3}, {"time": 1, "angle": 164.7}], "translate": [{"time": 0, "x": 215.49, "y": 75.76}, {"time": 0.3, "x": 218.59, "y": 77.62}, {"time": 1, "x": 212.38, "y": 23.57}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "offset": 34, "vertices": [-1.6248, 0, -1.6248, 0, -3.56929, 0, -5.06008, 0, -5.60021, 0, -5.44252, 0, -4.5783, 0, -3.17394, 0, -0.17077, 0, 1.40643, 0, 3.2213, 0, 4.58245, 0, 5.66272, 0, 5.68432, 0, 3.15648, 0, 0.86629, 0, 4.73368, 0, 0.62863, 0, 2.16262, 0, 3.11327, 0, 3.80464, 0, 3.91267, 0, 3.15648, 0, 2.20584, 0, 1.19038, 0, -2.2449, 0, -3.32518, 0, -3.88693, 0, -3.90852, 0, -3.51963, 0, -0.09931, 0, -1.33637, 0, -2.49716, 0, -1.14256, 0, 2.42, 0, 1.92028, 0, 1.24675, 0, 0.81221, 0, 0.11694, 0, -0.8825, 0, -1.4474, 0, -1.94712, 0, -2.20784, 0, -2.20784, 0, -2.0123, 0, -1.42567, 0, -0.79559, 0, 0.35594, 0, 1.18156, 0, 1.96373, 0, 2.31136, 0, -0.05687], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 34, "vertices": [-0.28508, 4.53919, -0.28508, -7.13288, -0.62625, -6.19494, -0.88782, -4.8662, -0.98258, -2.49531, -0.95492, 0.08401, -0.80329, 2.22041, -0.55689, 3.67943, -0.02996, 4.79973, 0.24676, 4.72157, 0.56519, 3.83574, 0.80401, 2.32463, 0.99355, -0.07231, 0.99734, -2.75585, 0.55382, -6.27311, 0.152, -7.15893, 0.83055, -4.65777, 0.1103, -4.97041, 0.37944, -4.21486, 0.54624, -3.25087, 0.66754, -2.0524, 0.6865, -0.59339, 0.55382, 1.3346, 0.38702, 2.42884, 0.20886, 2.89781, -0.39388, 2.16831, -0.58342, 0.94379, -0.68198, -0.43706, -0.68576, -1.94818, -0.61753, -3.64167, -0.01743, 2.86068, -0.23447, 2.58789, -0.43814, -4.5918, -0.20047, -5.17143, 0.4246, -0.8085, 0.33692, 0.23951, 0.21875, 0.81592, 0.1425, 0.99932, 0.02052, 1.07792, -0.15484, 0.84212, -0.25395, 0.58012, -0.34163, 0.00371, -0.38738, -0.8347, -0.38738, -1.8565, -0.35307, -2.56391, -0.25014, -3.16651, -0.13959, -3.32371, 0.06245, -3.21891, 0.20731, -2.72111, 0.34455, -2.1447, 0.40554, -1.4373, -0.00998, -1.0181], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 34, "vertices": [-1.6248, 0, -1.6248, 0, -3.56929, 0, -5.06008, 0, -5.60021, 0, -5.44252, 0, -4.5783, 0, -3.17394, 0, -0.17077, 0, 1.40643, 0, 3.2213, 0, 4.58245, 0, 5.66272, 0, 5.68432, 0, 3.15648, 0, 0.86629, 0, 4.73368, 0, 0.62863, 0, 2.16262, 0, 3.11327, 0, 3.80464, 0, 3.91267, 0, 3.15648, 0, 2.20584, 0, 1.19038, 0, -2.2449, 0, -3.32518, 0, -3.88693, 0, -3.90852, 0, -3.51963, 0, -0.09931, 0, -1.33637, 0, -2.49716, 0, -1.14256, 0, 2.42, 0, 1.92028, 0, 1.24675, 0, 0.81221, 0, 0.11694, 0, -0.8825, 0, -1.4474, 0, -1.94712, 0, -2.20784, 0, -2.20784, 0, -2.0123, 0, -1.42567, 0, -0.79559, 0, 0.35594, 0, 1.18156, 0, 1.96373, 0, 2.31136, 0, -0.05687]}]}}}}, "2-misdoubt": {"slots": {"Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/2-NgacNhien/2": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/2"}]}, "Emotion/2-NgacNhien/3": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/3"}]}, "Emotion/2-NgacNhien/4": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/4"}]}, "Emotion/2-NgacNhien/5": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/5"}]}, "Emotion/2-NgacNhien/6": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/6"}]}}, "bones": {"bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0, "y": -5.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0, "y": 16.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0, "y": 4.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0, "y": 5.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": -0.49, "y": 1.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1.849, "y": 0.589, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 1.13, "y": 0.244, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.476, "y": 0.106, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.801, "y": 1.801, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1.376, "y": 1.376, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1.816, "y": 1.467, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1.667, "y": 1.287, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1.668, "y": 1.334, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.849, "y": 0.589}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.53, "y": 5.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0.49, "y": -9.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0.36, "y": -1.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.21, "y": -8.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0.14, "y": -6.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0.03, "y": -6.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.83, "y": 0.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.835, "y": 0.835, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.476, "y": 0.476, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0.971, "y": 5.118, "curve": [0.271, 0, 0.619, 0.41]}, {"time": 0.5333, "x": 0.897, "y": 4.29, "curve": [0.342, 0.36, 0.757, 1]}, {"time": 0.7, "x": 0.884, "y": 2.658, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.936, "y": 2.257, "curve": "stepped"}, {"time": 1.4333, "x": 0.936, "y": 2.257, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0.83, "y": 0.83}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.59, "y": 5.89, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.23, "y": -9.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0.96, "y": -2.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.65, "y": -9.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0.48, "y": -8.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 0.2, "y": -8.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.651, "y": 0.651, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.787, "y": 0.787, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 0.334, "y": 0.334, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.035, "y": 3.661, "curve": [0.271, 0, 0.619, 0.41]}, {"time": 0.5333, "x": 0.892, "y": 3.015, "curve": [0.342, 0.36, 0.757, 1]}, {"time": 0.7, "x": 0.843, "y": 2.299, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0.927, "y": 1.726, "curve": "stepped"}, {"time": 1.4333, "x": 0.927, "y": 1.726, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0.651, "y": 0.651}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": [0.326, 0.31, 0.66, 0.64]}, {"time": 0.4667, "x": 0, "y": 5.88, "curve": [0.322, 0.3, 0.659, 0.64]}, {"time": 0.7, "x": 0, "y": 10.34, "curve": [0.331, 0.33, 0.667, 0.67]}, {"time": 0.9, "x": 0, "y": 3.84, "curve": [0.378, 0.52, 0.747, 1]}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": -22.48}, {"time": 0.3667, "angle": -56.54}, {"time": 0.4, "angle": 46.28}, {"time": 0.7333, "angle": 49.77}, {"time": 1.4, "angle": 43.6}, {"time": 1.6667, "angle": -22.48}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.914, "y": 0.914, "curve": "stepped"}, {"time": 0.3667, "x": 0.914, "y": 0.914}, {"time": 0.4, "x": 1.438, "y": 1.438}, {"time": 1.4, "x": 1.408, "y": 1.408}, {"time": 1.5, "x": 1, "y": 1}, {"time": 1.6667, "x": 0.914, "y": 0.914}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 27.36}, {"time": 0.3667, "angle": -291.67}, {"time": 0.4, "angle": -22.47}, {"time": 0.7333, "angle": -27.72}, {"time": 1.4, "angle": -23.12}, {"time": 1.6667, "angle": 27.36}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.912, "y": 0.912, "curve": "stepped"}, {"time": 0.3667, "x": 0.912, "y": 0.912}, {"time": 0.4, "x": 1.438, "y": 1.438}, {"time": 1.4, "x": 1.408, "y": 1.408}, {"time": 1.5, "x": 1, "y": 1}, {"time": 1.6667, "x": 0.912, "y": 0.912}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0, "y": -1.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0, "y": 20.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0, "y": -8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 4.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 0, "y": -1.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0, "y": -0.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 1.077, "y": 0.993, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.002, "y": 1.153, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.196, "y": 0.904, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1.017, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 1.017, "y": 0.976, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "offset": 34, "vertices": [-0.88014, -3.70735, -0.88014, -3.70735, -1.93345, -3.70735, -2.74098, -3.70735, -3.03357, -3.70735, -2.94815, -3.70735, -2.48002, -3.70735, -1.71929, -3.70735, -0.09251, -3.70735, 0.76185, -3.70735, 1.74494, -3.70735, 2.48225, -3.70735, 3.06743, -3.70735, 3.07914, -3.70735, 1.70983, -3.70735, 0.46926, -3.70735, 2.56418, -3.70735, 0.34052, -3.70735, 1.17147, -3.70735, 1.68642, -3.70735, 2.06093, -3.70735, 2.11945, -3.70735, 1.70983, -3.70735, 1.19487, -3.70735, 0.64481, -3.70735, -1.21604, -3.70735, -1.80122, -3.70735, -2.1055, -3.70735, -2.1172, -3.70735, -1.90654, -3.70735, -0.0538, -3.70735, -0.7239, -3.70735, -1.35268, -3.70735, -0.61891, -3.70735, 1.31089, -3.70735, 1.04019, -3.70735, 0.67534, -3.70735, 0.43996, -3.70735, 0.06335, -3.70734, -0.47804, -3.70735, -0.78404, -3.70735, -1.05474, -3.70735, -1.19596, -3.70735, -1.19596, -3.70735, -1.09004, -3.70735, -0.77227, -3.70735, -0.43096, -3.70735, 0.19281, -3.70735, 0.64004, -3.70735, 1.06373, -3.70735, 1.25204, -3.70735, -0.03081, -3.70735], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "offset": 34, "vertices": [0.59446, -4.90101, 0.59446, -4.901, 1.3059, -4.90099, 1.85133, -4.90099, 2.04894, -4.90099, 1.99125, -4.90099, 1.67506, -4.90099, 1.16125, -4.90099, 0.06248, -4.90099, -0.51458, -4.90099, -1.17858, -4.901, -1.67659, -4.90099, -2.07183, -4.90099, -2.07973, -4.90099, -1.15487, -4.90099, -0.31695, -4.90099, -1.73192, -4.90099, -0.23, -4.90099, -0.79124, -4.90099, -1.13906, -4.90099, -1.39201, -4.90099, -1.43153, -4.90099, -1.15487, -4.90099, -0.80705, -4.90099, -0.43553, -4.90099, 0.82134, -4.90099, 1.21658, -4.90099, 1.42211, -4.90099, 1.43001, -4.90099, 1.28772, -4.901, 0.03633, -4.90099, 0.48893, -4.90099, 0.91363, -4.90099, 0.41803, -4.90099, -0.88541, -4.90099, -0.70258, -4.90099, -0.45615, -4.90099, -0.29717, -4.90099, -0.04279, -4.90099, 0.32288, -4.90099, 0.52956, -4.90099, 0.71239, -4.90099, 0.80778, -4.90099, 0.80778, -4.90099, 0.73624, -4.90099, 0.52161, -4.90099, 0.29108, -4.90099, -0.13023, -4.90099, -0.4323, -4.90099, -0.71848, -4.90099, -0.84566, -4.90099, 0.0208, -4.90099], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "offset": 34, "vertices": [-1.10199, 2.38628, -1.10199, 2.38629, -2.42081, 2.38629, -3.43191, 2.38629, -3.79826, 2.38629, -3.69131, 2.38629, -3.10515, 2.38629, -2.15268, 2.38629, -0.11583, 2.38629, 0.95389, 2.38629, 2.18479, 2.38629, 3.10795, 2.38629, 3.84064, 2.38629, 3.8553, 2.38629, 2.14083, 2.38629, 0.58755, 2.38629, 3.21054, 2.38629, 0.42636, 2.38629, 1.46676, 2.38629, 2.11152, 2.38629, 2.58044, 2.38629, 2.6537, 2.38629, 2.14083, 2.38629, 1.49607, 2.38629, 0.80735, 2.38629, -1.52257, 2.38629, -2.25525, 2.38629, -2.63624, 2.38629, -2.6509, 2.38629, -2.38714, 2.38629, -0.06736, 2.38629, -0.90637, 2.38629, -1.69365, 2.38629, -0.77493, 2.38629, 1.64133, 2.38629, 1.3024, 2.38629, 0.84558, 2.38629, 0.55086, 2.38629, 0.07931, 2.38629, -0.59854, 2.38629, -0.98168, 2.38629, -1.32061, 2.38629, -1.49744, 2.38629, -1.49744, 2.38629, -1.36481, 2.38629, -0.96694, 2.38629, -0.53959, 2.38629, 0.24141, 2.38629, 0.80137, 2.38629, 1.33187, 2.38629, 1.56764, 2.38629, -0.03857, 2.38629], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "offset": 34, "vertices": [0.85939, 5.40989, 0.85939, 1.96497, 1.88788, 2.2418, 2.67639, 2.63397, 2.96207, 3.33372, 2.87867, 4.09499, 2.42157, 4.72554, 1.67877, 5.15615, 0.09033, 5.4868, -0.7439, 5.46373, -1.70382, 5.20228, -2.42377, 4.75629, -2.99514, 4.04885, -3.00657, 3.25682, -1.66954, 2.21873, -0.4582, 1.95728, -2.50375, 2.69548, -0.3325, 2.60321, -1.14387, 2.8262, -1.64668, 3.11072, -2.01237, 3.46444, -2.06951, 3.89506, -1.66954, 4.46409, -1.16672, 4.78705, -0.62962, 4.92546, 1.18738, 4.71015, 1.75876, 4.34874, 2.05589, 3.94119, 2.06731, 3.4952, 1.86161, 2.99538, 0.05253, 4.9145, 0.70684, 4.83399, 1.32081, 2.71495, 0.60433, 2.54388, -1.27999, 3.83157, -1.01568, 4.14088, -0.65943, 4.311, -0.4296, 4.36513, -0.06185, 4.38833, 0.46677, 4.31874, 0.76556, 4.24141, 1.02987, 4.07129, 1.16778, 3.82384, 1.16778, 3.52226, 1.06435, 3.31347, 0.75407, 3.13562, 0.42081, 3.08922, -0.18827, 3.12015, -0.62496, 3.26708, -1.03866, 3.4372, -1.22254, 3.64598, 0.03008, 3.76971], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "offset": 34, "vertices": [-1.42227, 2.80999, -1.42227, 3.04694, -3.12439, 3.0279, -4.42935, 3.00092, -4.90217, 2.9528, -4.76414, 2.90044, -4.00762, 2.85707, -2.77832, 2.82745, -0.14949, 2.80471, 1.23112, 2.80629, 2.81977, 2.82428, 4.01124, 2.85495, 4.95688, 2.90361, 4.97579, 2.95808, 2.76303, 3.02948, 0.75831, 3.04746, 4.14364, 2.99669, 0.55027, 3.00304, 1.89306, 2.9877, 2.7252, 2.96813, 3.33041, 2.9438, 3.42496, 2.91419, 2.76303, 2.87505, 1.93088, 2.85283, 1.042, 2.84331, -1.96508, 2.85812, -2.91071, 2.88299, -3.40243, 2.91101, -3.42134, 2.94169, -3.08092, 2.97607, -0.08693, 2.84406, -1.1698, 2.84961, -2.18589, 2.99535, -1.00015, 3.00712, 2.11835, 2.91856, 1.68092, 2.89728, 1.09134, 2.88558, 0.71097, 2.88186, 0.10237, 2.88026, -0.7725, 2.88505, -1.26699, 2.89037, -1.70442, 2.90207, -1.93264, 2.91909, -1.93264, 2.93983, -1.76147, 2.95419, -1.24796, 2.96642, -0.69642, 2.96961, 0.31157, 2.96748, 1.03428, 2.95738, 1.71896, 2.94568, 2.02326, 2.93132, -0.04978, 2.92281], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "offset": 34, "vertices": [-0.27163, 3.64822, -0.27163, 0.67244, -0.59671, 0.91157, -0.84593, 1.25033, -0.93623, 1.85478, -0.90987, 2.51238, -0.76538, 3.05706, -0.53061, 3.42902, -0.02855, 3.71464, 0.23512, 3.69472, 0.53853, 3.46888, 0.76607, 3.08362, 0.94668, 2.47252, 0.95029, 1.78836, 0.52769, 0.89164, 0.14482, 0.6658, 0.79136, 1.30346, 0.10509, 1.22376, 0.36154, 1.41639, 0.52046, 1.66216, 0.63606, 1.9677, 0.65411, 2.33968, 0.52769, 2.83121, 0.36877, 3.11019, 0.19901, 3.22975, -0.3753, 3.04377, -0.5559, 2.73158, -0.64981, 2.37953, -0.65342, 1.99427, -0.5884, 1.56252, -0.0166, 3.22028, -0.22341, 3.15074, -0.41747, 1.32028, -0.19101, 1.17251, 0.40457, 2.28484, 0.32103, 2.55202, 0.20843, 2.69898, 0.13578, 2.74574, 0.01955, 2.76577, -0.14753, 2.70566, -0.24197, 2.63886, -0.32552, 2.49191, -0.3691, 2.27816, -0.3691, 2.01765, -0.33641, 1.83729, -0.23834, 1.68366, -0.133, 1.64359, 0.0595, 1.6703, 0.19753, 1.79722, 0.32829, 1.94417, 0.38641, 2.12452, -0.00951, 2.2314], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "offset": 34, "vertices": [-0.79214, 3.39185, -0.79214, -1.62282, -1.74015, -1.21985, -2.46695, -0.64899, -2.73029, 0.36962, -2.65341, 1.47777, -2.23207, 2.39563, -1.5474, 3.02246, -0.08326, 3.50378, 0.68568, 3.4702, 1.57049, 3.08963, 2.23409, 2.4404, 2.76077, 1.41061, 2.7713, 0.25768, 1.53889, -1.25344, 0.42235, -1.63402, 2.30783, -0.55944, 0.30648, -0.69376, 1.05435, -0.36915, 1.51782, 0.04501, 1.85489, 0.5599, 1.90756, 1.18674, 1.53889, 2.01505, 1.07542, 2.48518, 0.58035, 2.68666, -1.09446, 2.37325, -1.62114, 1.84715, -1.89501, 1.2539, -1.90554, 0.60468, -1.71593, -0.1229, -0.04842, 2.67071, -0.65153, 2.55351, -1.21745, -0.5311, -0.55704, -0.78013, 1.17983, 1.09432, 0.9362, 1.54458, 0.60783, 1.79221, 0.39598, 1.87101, 0.05701, 1.90478, -0.43025, 1.80347, -0.70566, 1.69091, -0.94929, 1.44327, -1.0764, 1.08306, -1.0764, 0.64407, -0.98106, 0.34014, -0.69506, 0.08125, -0.38788, 0.01371, 0.17353, 0.05873, 0.57605, 0.27261, 0.95739, 0.52025, 1.12687, 0.82417, -0.02773, 1.00427], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667}]}}}}, "20-burn_joss_stick": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/10-Bom/4": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/4"}, {"time": 2, "name": "Emotion/10-Bom/4"}]}, "Emotion/10-Bom/5": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/5"}]}, "Emotion/20-burn_joss_stick/2": {"attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/2"}]}, "Emotion/20-burn_joss_stick/3": {"attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/3"}]}, "Emotion/20-burn_joss_stick/4": {"attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/4"}]}, "Emotion/20-burn_joss_stick/5": {"attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/5"}]}, "Emotion/20-burn_joss_stick/6": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 1.9667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/7": {"attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/7"}, {"time": 2, "name": "Emotion/20-burn_joss_stick/7"}]}, "Emotion/20-burn_joss_stick/8": {"color": [{"time": 0.3667, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 0.6333, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/9": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3, "color": "ffffff00"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 0.3333, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 2, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/11": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.6, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6333, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 0.6333, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/12": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3667, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00", "curve": "stepped"}, {"time": 1.8333, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 1.8333, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/13": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1333, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 2, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 1.6667, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 2, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/14": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1333, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 1.1333, "name": "Emotion/20-burn_joss_stick/6"}]}, "Emotion/20-burn_joss_stick/15": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 1.3, "name": "Emotion/20-burn_joss_stick/6"}, {"time": 2, "name": "Emotion/20-burn_joss_stick/6"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -22.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": -38.03, "y": -44.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -36.4, "y": -3.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -38.03, "y": -44.59}], "scale": [{"time": 0, "x": 1.072, "y": 1.072, "curve": "stepped"}, {"time": 2, "x": 1.072, "y": 1.072}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -27.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 1.93, "y": -75.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 3.44, "y": -48.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1.93, "y": -75.9}], "scale": [{"time": 0, "x": 1.098, "y": 1.098, "curve": "stepped"}, {"time": 2, "x": 1.098, "y": 1.098}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 98.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 71.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 98.74}], "translate": [{"time": 0, "x": -13.86, "y": -5.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -10.11, "y": 36.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -13.86, "y": -5.15}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": -17.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -27.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -17.03}], "translate": [{"time": 0, "x": -24.03, "y": -40.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -17.21, "y": -17.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -24.03, "y": -40.76}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.9667, "angle": 0}], "translate": [{"time": 0, "x": -87.14, "y": 53.83}, {"time": 1.9667, "x": -47.93, "y": 203.95}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 1.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.9667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": -86.79, "y": 141.86}, {"time": 0.6, "x": -89.97, "y": 181.24}, {"time": 0.6333, "x": -79.56, "y": 52.16}, {"time": 2, "x": -86.79, "y": 141.86}], "scale": [{"time": 0, "x": 0.876, "y": 0.876}, {"time": 0.6, "x": 1, "y": 1}, {"time": 0.6333, "x": 0.594, "y": 0.594}, {"time": 2, "x": 0.876, "y": 0.876}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}, {"time": 1.5333, "angle": 3.29}], "translate": [{"time": 0, "x": -91.34, "y": 54.06}, {"time": 1.5333, "x": -74.06, "y": 197.82}], "scale": [{"time": 0, "x": 0.502, "y": 0.502}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 49.44}, {"time": 0.3, "angle": 47.3}, {"time": 0.3333, "angle": 61.31}, {"time": 2, "angle": 49.44}], "translate": [{"time": 0, "x": -73.64, "y": 153.06}, {"time": 0.3, "x": -70.18, "y": 172.69}, {"time": 0.3333, "x": -92.88, "y": 44.03}, {"time": 2, "x": -73.64, "y": 153.06}], "scale": [{"time": 0, "x": 0.979, "y": 0.979}, {"time": 0.3, "x": 1.043, "y": 1.043}, {"time": 0.3333, "x": 0.624, "y": 0.624}, {"time": 2, "x": 0.979, "y": 0.979}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 48.2, "curve": "stepped"}, {"time": 1.1333, "angle": 48.2, "curve": "stepped"}, {"time": 2, "angle": 48.2}], "translate": [{"time": 0, "x": -91.21, "y": 90.92}, {"time": 1.1, "x": -95.32, "y": 148.33}, {"time": 1.1333, "x": -87.98, "y": 45.68}, {"time": 2, "x": -91.21, "y": 90.92}], "scale": [{"time": 0, "x": 0.788, "y": 0.788}, {"time": 1.1, "x": 0.987, "y": 0.987}, {"time": 1.1333, "x": 0.632, "y": 0.632}, {"time": 2, "x": 0.788, "y": 0.788}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": -35.21}, {"time": 1.2667, "angle": -40.41}, {"time": 1.3, "angle": -32.33}, {"time": 2, "angle": -35.21}], "translate": [{"time": 0, "x": -86.38, "y": 80.29}, {"time": 1.2667, "x": -83.47, "y": 144.95}, {"time": 1.3, "x": -87.98, "y": 44.56}, {"time": 2, "x": -86.38, "y": 80.29}], "scale": [{"time": 0, "x": 0.868, "y": 0.868}, {"time": 1.2667, "x": 1.168, "y": 1.168}, {"time": 1.3, "x": 0.702, "y": 0.702}, {"time": 2, "x": 0.868, "y": 0.868}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": -47.55}, {"time": 0.6, "angle": -44.59}, {"time": 0.6333, "angle": -54.29}, {"time": 2, "angle": -47.55}], "translate": [{"time": 0, "x": -103.96, "y": 118.22}, {"time": 0.6, "x": -109.98, "y": 148.33}, {"time": 0.6333, "x": -90.24, "y": 49.63}, {"time": 2, "x": -103.96, "y": 118.22}], "scale": [{"time": 0, "x": 0.649, "y": 0.649}, {"time": 0.6, "x": 0.818, "y": 0.818}, {"time": 0.6333, "x": 0.264, "y": 0.264}, {"time": 2, "x": 0.649, "y": 0.649}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0.62}, {"time": 1.8, "angle": 7.42}, {"time": 1.8333, "angle": 0}, {"time": 2, "angle": 0.62}], "translate": [{"time": 0, "x": -86.75, "y": 64.5}, {"time": 1.8, "x": -102.51, "y": 176.13}, {"time": 1.8333, "x": -85.31, "y": 54.35}, {"time": 2, "x": -86.75, "y": 64.5}], "scale": [{"time": 0, "x": 0.54, "y": 0.54}, {"time": 1.8, "x": 0.845, "y": 0.845}, {"time": 1.8333, "x": 0.513, "y": 0.513}, {"time": 2, "x": 0.54, "y": 0.54}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 14.28}, {"time": 1.6333, "angle": 22.79}, {"time": 1.6667, "angle": 12.54}, {"time": 2, "angle": 14.28}], "translate": [{"time": 0, "x": -93.65, "y": 74.92}, {"time": 1.6333, "x": -127.03, "y": 171.52}, {"time": 1.6667, "x": -86.83, "y": 55.21}, {"time": 2, "x": -93.65, "y": 74.92}], "scale": [{"time": 0, "x": 0.547, "y": 0.547}, {"time": 1.6333, "x": 0.656, "y": 0.656}, {"time": 1.6667, "x": 0.525, "y": 0.525}, {"time": 2, "x": 0.547, "y": 0.547}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 12.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -18.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 12.91}], "translate": [{"time": 0, "x": -8.21, "y": -5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -9.08, "y": 30.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -8.21, "y": -5.46}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 341.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": -11.38, "y": 40.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -14.98, "y": 4.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -11.38, "y": 40.57}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 11.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -14.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 11.64}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.188, 0.23, 0.75, 1]}, {"time": 0.5, "angle": 2.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.82, "curve": [0.25, 0, 0.805, 0.74]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 10.63, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "offset": 34, "vertices": [-2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, 0, 0, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 34, "vertices": [3.43516, 7.1253, -3.13454, 7.39631, -2.55216, 8.69433, 1.50423, 5.3928, -0.41291, 9.98687, 1.03446, 9.81994, 2.21274, 9.18374, 2.99462, 8.19669, 3.54109, 6.13235, 3.45292, 5.06367, 2.90351, 3.85239, 2.01483, 2.96357, 0.63546, 2.28601, -0.87559, 2.33367, -2.7845, 4.1311, -3.21896, 5.70613, -1.91946, 3.02308, -1.98049, 5.81659, -1.59818, 4.75787, -1.08222, 4.09024, -0.42702, 3.59314, 0.39116, 3.48594, 1.49753, 3.95442, 2.14006, 4.57425, 2.43245, 5.25259, 2.11805, 7.60117, 1.45908, 8.36283, 0.69761, 8.77619, -0.15233, 8.82595, -1.11642, 8.60132, 2.44768, 6.12882, 2.32878, 6.97477, -1.67985, 7.9294, -2.04404, 7.02345, 0.3119, 4.50408, 0.91576, 4.81891, 1.25907, 5.26268, 1.37447, 5.55335, 1.43818, 6.02343, 1.33345, 6.70726, 1.2018, 7.09676, 0.89137, 7.44934, 0.42676, 7.64577, -0.14837, 7.6695, -0.55201, 7.55321, -0.90762, 7.16903, -1.01375, 6.74503, -0.98701, 5.961, -0.72994, 5.38907, -0.42742, 4.84478, -0.03899, 4.59241, 0.26329, 6.19008], "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "offset": 34, "vertices": [-2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, 0, 0, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849, -2.049, -3.00849]}]}}}}, "21-baffle": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 3.5667, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 3.5667, "name": null}]}, "Emotion/2-NgacNhien/2": {"color": [{"time": 0.6333, "color": "ffffffff"}, {"time": 0.7, "color": "ffffff00", "curve": "stepped"}, {"time": 3.0667, "color": "ffffff00"}, {"time": 3.3333, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/2"}, {"time": 3.5667, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/2"}]}, "Emotion/2-NgacNhien/5": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/5"}, {"time": 3.5667, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/5"}]}, "Emotion/2-NgacNhien/6": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/6"}, {"time": 3.5667, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/6"}]}, "Emotion/21-baffle/2": {"attachment": [{"time": 0, "name": null}, {"time": 0.6333, "name": "Emotion/21-baffle/2"}, {"time": 3.0667, "name": "Emotion/21-baffle/2"}, {"time": 3.3333, "name": "Emotion/21-baffle/2"}, {"time": 3.5667, "name": null}]}, "Emotion/21-baffle/3": {"attachment": [{"time": 0, "name": "Emotion/21-baffle/3"}, {"time": 0.6333, "name": "Emotion/21-baffle/3"}, {"time": 3.0667, "name": "Emotion/21-baffle/3"}, {"time": 3.3333, "name": "Emotion/21-baffle/3"}, {"time": 3.5667, "name": "Emotion/21-baffle/3"}]}, "Emotion/21-baffle/4": {"attachment": [{"time": 0, "name": null}, {"time": 0.6333, "name": "Emotion/21-baffle/4"}, {"time": 3.0667, "name": "Emotion/21-baffle/4"}, {"time": 3.3333, "name": "Emotion/21-baffle/4"}, {"time": 3.5667, "name": null}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 28.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -19.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "angle": 21.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7667, "angle": -7.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.243, 0, 0.685, 0.73]}, {"time": 0.5667, "angle": -1.02, "curve": [0.373, 0.62, 0.713, 1]}, {"time": 0.6333, "angle": -24.92}, {"time": 0.7, "angle": -35.76, "curve": "stepped"}, {"time": 3.0667, "angle": -35.76}, {"time": 3.3333, "angle": -24.92}, {"time": 3.5667, "angle": 0}], "translate": [{"time": 0, "x": 13.23, "y": -14.19, "curve": "stepped"}, {"time": 0.3333, "x": 13.23, "y": -14.19, "curve": "stepped"}, {"time": 0.5667, "x": 13.23, "y": -14.19, "curve": "stepped"}, {"time": 0.6333, "x": 13.23, "y": -14.19}, {"time": 0.7, "x": 14.79, "y": -12.69, "curve": "stepped"}, {"time": 3.0667, "x": 14.79, "y": -12.69}, {"time": 3.3333, "x": 13.23, "y": -14.19, "curve": "stepped"}, {"time": 3.5667, "x": 13.23, "y": -14.19}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.885, "y": 1.022, "curve": "stepped"}, {"time": 0.6333, "x": 0.885, "y": 1.022}, {"time": 0.7, "x": 0.885, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0.885, "y": 0}, {"time": 3.3333, "x": 0.885, "y": 1.022}, {"time": 3.5667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 37.7, "curve": "stepped"}, {"time": 0.6333, "angle": 37.7, "curve": "stepped"}, {"time": 0.7, "angle": 37.7, "curve": "stepped"}, {"time": 1.1333, "angle": 37.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 49.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 24.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "angle": 37.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 37.7, "curve": "stepped"}, {"time": 3.0667, "angle": 37.7, "curve": "stepped"}, {"time": 3.3333, "angle": 37.7, "curve": "stepped"}, {"time": 3.5667, "angle": 37.7}], "translate": [{"time": 0, "x": 2.41, "y": -29.75, "curve": "stepped"}, {"time": 0.6333, "x": 2.41, "y": -29.75, "curve": "stepped"}, {"time": 0.7, "x": 2.41, "y": -29.75, "curve": "stepped"}, {"time": 1.1333, "x": 2.41, "y": -29.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": -5.65, "y": -29.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 7.56, "y": -31.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "x": -1.05, "y": -29.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 2.41, "y": -29.75, "curve": "stepped"}, {"time": 3.0667, "x": 2.41, "y": -29.75, "curve": "stepped"}, {"time": 3.3333, "x": 2.41, "y": -29.75, "curve": "stepped"}, {"time": 3.5667, "x": 2.41, "y": -29.75}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 1, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": -0.5, "curve": "stepped"}, {"time": 0.6333, "angle": -0.5, "curve": "stepped"}, {"time": 0.7, "angle": -0.5, "curve": "stepped"}, {"time": 1.1333, "angle": -0.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 10.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -13.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "angle": -0.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": -0.5, "curve": "stepped"}, {"time": 3.0667, "angle": -0.5, "curve": "stepped"}, {"time": 3.3333, "angle": -0.5, "curve": "stepped"}, {"time": 3.5667, "angle": -0.5}], "translate": [{"time": 0, "x": 13.31, "y": -33.64, "curve": "stepped"}, {"time": 0.6333, "x": 13.31, "y": -33.64, "curve": "stepped"}, {"time": 0.7, "x": 13.31, "y": -33.64, "curve": "stepped"}, {"time": 1.1333, "x": 13.31, "y": -33.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 5.26, "y": -33.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 18.47, "y": -35.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "x": 9.85, "y": -33.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 13.31, "y": -33.64, "curve": "stepped"}, {"time": 3.0667, "x": 13.31, "y": -33.64, "curve": "stepped"}, {"time": 3.3333, "x": 13.31, "y": -33.64, "curve": "stepped"}, {"time": 3.5667, "x": 13.31, "y": -33.64}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 1, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 25.76, "curve": "stepped"}, {"time": 0.6333, "angle": 25.76, "curve": "stepped"}, {"time": 0.7, "angle": 25.76, "curve": "stepped"}, {"time": 1.1333, "angle": 25.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 36.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 18.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "angle": 39.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "angle": 25.76, "curve": "stepped"}, {"time": 3.0667, "angle": 25.76, "curve": "stepped"}, {"time": 3.3333, "angle": 25.76, "curve": "stepped"}, {"time": 3.5667, "angle": 25.76}], "translate": [{"time": 0, "x": 21.29, "y": -23.55, "curve": "stepped"}, {"time": 0.6333, "x": 21.29, "y": -23.55, "curve": "stepped"}, {"time": 0.7, "x": 21.29, "y": -23.55, "curve": "stepped"}, {"time": 1.1333, "x": 21.29, "y": -23.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 29.64, "y": -25.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 21, "y": -21.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "x": 29.01, "y": -23.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 21.29, "y": -23.55, "curve": "stepped"}, {"time": 3.0667, "x": 21.29, "y": -23.55, "curve": "stepped"}, {"time": 3.3333, "x": 21.29, "y": -23.55, "curve": "stepped"}, {"time": 3.5667, "x": 21.29, "y": -23.55}], "scale": [{"time": 0, "x": 0, "y": 1, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.8333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.0667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 0, "y": 1, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 5.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -14.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "angle": -11.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.983, "y": 1.045, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1.146, "y": 0.884, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0.955, "y": 1.205, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 0.986, "y": 1.166, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.0667, "x": 0.955, "y": 1.155, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.5667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6333, "angle": 0}, {"time": 0.7, "angle": -27.87, "curve": "stepped"}, {"time": 3.0667, "angle": -27.87}, {"time": 3.2, "angle": -27.15}, {"time": 3.3333, "angle": 0, "curve": "stepped"}, {"time": 3.5667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0}, {"time": 0.7, "x": -4.28, "y": -0.31}, {"time": 3.0667, "x": -10.43, "y": -1.18}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0.133}, {"time": 0.3333, "x": 1.229, "y": 0.104}, {"time": 0.6333, "x": 0.693, "y": 0.354}, {"time": 0.7, "x": 2.488, "y": 0.074, "curve": "stepped"}, {"time": 3.0667, "x": 2.488, "y": 0.074}, {"time": 3.2, "x": 2.278, "y": 0.214}, {"time": 3.3333, "x": 0.693, "y": 0.354}, {"time": 3.5667, "x": 1, "y": 0.133}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.5667, "angle": -1.17}, {"time": 0.6333, "angle": -335.94}, {"time": 3.0667, "angle": 24.06}, {"time": 3.3333, "angle": -335.94}, {"time": 3.5667, "angle": 0}], "translate": [{"time": 0, "x": 11.69, "y": -16.72, "curve": "stepped"}, {"time": 0.3333, "x": 11.69, "y": -16.72, "curve": "stepped"}, {"time": 0.5667, "x": 11.69, "y": -16.72}, {"time": 0.6333, "x": 18.01, "y": -13.57, "curve": "stepped"}, {"time": 3.0667, "x": 18.01, "y": -13.57, "curve": "stepped"}, {"time": 3.3333, "x": 18.01, "y": -13.57}, {"time": 3.5667, "x": 11.69, "y": -16.72}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3333, "x": 0.885, "y": 1.022, "curve": "stepped"}, {"time": 0.6333, "x": 0.885, "y": 1.022}, {"time": 0.7, "x": 0.885, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0.885, "y": 0}, {"time": 3.3333, "x": 0.885, "y": 1.022}, {"time": 3.5667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.5667, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0}]}}}}, "22-cool": {"slots": {"Emotion/1-Buon/1": {"color": [{"time": 0, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "f7d3d3ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "color": "f7d3d3ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "f7d3d3ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "color": "f7d3d3ff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "color": "ffffffff"}]}, "Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/10-Bom/4": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/4"}, {"time": 1.3333, "name": "Emotion/10-Bom/4"}]}, "Emotion/10-Bom/5": {"attachment": [{"time": 0, "name": "Emotion/10-Bom/5"}, {"time": 1.3333, "name": "Emotion/10-Bom/5"}]}, "Emotion/21-baffle/2": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}, {"time": 2.6667, "name": null}]}, "Emotion/21-baffle/3": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/21-baffle/4": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}, {"time": 2.6667, "name": null}]}, "Emotion/22-cooll/2": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3333, "color": "ff1313ff"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ff1313ff"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ff1313ff"}, {"time": 2, "color": "ffffffff"}, {"time": 2.3333, "color": "ff1313ff"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 1.3333, "name": "Emotion/22-cooll/2"}]}, "Emotion/22-cooll/3": {"attachment": [{"time": 0, "name": "Emotion/22-cooll/3"}, {"time": 1.3333, "name": "Emotion/22-cooll/3"}, {"time": 2.6667, "name": "Emotion/22-cooll/3"}]}, "Emotion/22-cooll/4": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.3333, "color": "ff1313ff"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1, "color": "ff1313ff"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ff1313ff"}, {"time": 2, "color": "ffffffff"}, {"time": 2.3333, "color": "ff1313ff"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/22-cooll/4"}, {"time": 1.3333, "name": "Emotion/22-cooll/4"}, {"time": 2.6667, "name": "Emotion/22-cooll/4"}]}, "Emotion/22-cooll/5": {"attachment": [{"time": 0, "name": "Emotion/22-cooll/5"}, {"time": 1.3333, "name": "Emotion/22-cooll/5"}, {"time": 2.6667, "name": "Emotion/22-cooll/5"}]}, "Emotion/22-cooll/6": {"attachment": [{"time": 0, "name": "Emotion/22-cooll/6"}, {"time": 0.6667, "name": "Emotion/22-cooll/6"}, {"time": 1.3333, "name": "Emotion/22-cooll/6"}, {"time": 2, "name": "Emotion/22-cooll/6"}, {"time": 2.6667, "name": "Emotion/22-cooll/6"}]}, "Emotion/22-cooll/9": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.3333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.9, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/22-cooll/9"}]}, "Emotion/22-cooll/10": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/22-cooll/10"}, {"time": 0.8, "name": "Emotion/22-cooll/10"}]}, "Emotion/22-cooll/11": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6, "color": "ffffffff"}, {"time": 1.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4, "color": "ffffff00"}, {"time": 2.0667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/22-cooll/9"}, {"time": 1.4, "name": "Emotion/22-cooll/9"}]}, "Emotion/22-cooll/12": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6333, "color": "ffffff00"}, {"time": 2.1667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/22-cooll/10"}, {"time": 1.6, "name": "Emotion/22-cooll/10"}, {"time": 1.6333, "name": "Emotion/22-cooll/10"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": -43.88, "curve": "stepped"}, {"time": 1.3333, "angle": -43.88}], "translate": [{"time": 0, "x": -38.7, "y": 12.99, "curve": "stepped"}, {"time": 1.3333, "x": -38.7, "y": 12.99}], "scale": [{"time": 0, "x": 1.084, "y": 1.084, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.943, "y": 0.943, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1.197, "y": 1.197, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.973, "y": 0.973, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.096, "y": 1.096, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1.084, "y": 1.084}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 1.3333, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 55.69, "curve": "stepped"}, {"time": 1.3333, "angle": 55.69}], "translate": [{"time": 0, "x": 43.1, "y": -18.17, "curve": "stepped"}, {"time": 1.3333, "x": 43.1, "y": -18.17}], "scale": [{"time": 0, "x": 1.076, "y": 1.076, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.963, "y": 0.963, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1.187, "y": 1.187, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1.015, "y": 1.015, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.07, "y": 1.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1.076, "y": 1.076}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": -31.21, "curve": "stepped"}, {"time": 1.3333, "angle": -31.21, "curve": "stepped"}, {"time": 2.6667, "angle": -31.21}], "translate": [{"time": 0, "x": 0.36, "y": -71.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.36, "y": -67.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.36, "y": -71.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.36, "y": -67.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0.36, "y": -71.47}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.794, "y": 0.794, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.794, "y": 0.794, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 38.96, "curve": "stepped"}, {"time": 1.3333, "angle": 38.96, "curve": "stepped"}, {"time": 2.6667, "angle": 38.96}], "translate": [{"time": 0, "x": 13.39, "y": 2.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 13.39, "y": 5.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 13.39, "y": 2.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 13.39, "y": 4.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 13.39, "y": 2.91}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 0.3333, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 0.6667, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 1, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 1.3333, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 1.6667, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 2, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 2.3333, "x": 8.92, "y": -5.63, "curve": "stepped"}, {"time": 2.6667, "x": 8.92, "y": -5.63}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "x": 1.131, "y": 0.853, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 26.65, "curve": "stepped"}, {"time": 1.3333, "angle": 26.65, "curve": "stepped"}, {"time": 2.6667, "angle": 26.65}], "translate": [{"time": 0, "x": -27.54, "y": -44.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -27.54, "y": -40.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": -27.54, "y": -44.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -27.54, "y": -40.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": -27.54, "y": -44.25}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.794, "y": 0.794, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.794, "y": 0.794, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 119.83, "curve": "stepped"}, {"time": 1.3333, "angle": 119.83, "curve": "stepped"}, {"time": 2.6667, "angle": 119.83}], "translate": [{"time": 0, "x": 22.31, "y": 38.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 22.31, "y": 41.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 22.31, "y": 38.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 22.31, "y": 40.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 22.31, "y": 38.99}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": -71.14, "y": 24.02, "curve": "stepped"}, {"time": 1.3333, "x": -71.14, "y": 24.02}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -89.81, "y": 91.53}, {"time": 1.6, "x": -89.81, "y": 138.6}, {"time": 1.6333, "x": -89.81, "y": 61.76}, {"time": 2.6667, "x": -89.81, "y": 91.53}], "scale": [{"time": 0, "x": 0.844, "y": 0.844}, {"time": 1.6, "x": 1, "y": 1}, {"time": 1.6333, "x": 0, "y": 0}, {"time": 2.6667, "x": 0.844, "y": 0.844}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.896, "y": 1.039, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.896, "y": 1.039, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 10.49, "curve": "stepped"}, {"time": 0.3333, "angle": 10.49}, {"time": 2.6667, "angle": 4.15}], "translate": [{"time": 0, "x": -0.79, "y": 63.34, "curve": "stepped"}, {"time": 0.3333, "x": -0.79, "y": 63.34}, {"time": 2.6667, "x": -6.37, "y": 130.86}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.7667, "x": 1.109, "y": 1.109}, {"time": 0.9667, "x": 0.934, "y": 0.934}, {"time": 1.2667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3667, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0, "curve": "stepped"}, {"time": 2.0667, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": -5.81, "y": 97.49}, {"time": 1.3667, "x": -4.57, "y": 154.89}, {"time": 1.4, "x": -6.51, "y": 65.46}, {"time": 2.0667, "x": -6.35, "y": 72.89}, {"time": 2.6667, "x": -5.81, "y": 97.49}], "scale": [{"time": 0, "x": 0.912, "y": 0.912}, {"time": 1.3667, "x": 1, "y": 1}, {"time": 1.4, "x": 0, "y": 0}, {"time": 2.0667, "x": 0.874, "y": 0.874}, {"time": 2.6667, "x": 0.912, "y": 0.912}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0.33, "curve": "stepped"}, {"time": 0.7667, "angle": 0.33, "curve": "stepped"}, {"time": 0.8, "angle": 0.33, "curve": "stepped"}, {"time": 2.6667, "angle": 0.33}], "translate": [{"time": 0, "x": -86.56, "y": 122.59}, {"time": 0.7667, "x": -86.56, "y": 134.81}, {"time": 0.8, "x": -86.56, "y": 69.6}, {"time": 2.6667, "x": -86.56, "y": 122.59}], "scale": [{"time": 0, "x": 0.966, "y": 0.966}, {"time": 0.7667, "x": 1, "y": 1}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.7667, "x": 0.864, "y": 0.864}, {"time": 2.6667, "x": 0.966, "y": 0.966}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": "stepped"}, {"time": 1.3333}]}}}}, "23-dribble": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/27-dribble/2": {"attachment": [{"time": 0, "name": "Emotion/27-dribble/2"}]}, "Emotion/27-dribble/3": {"attachment": [{"time": 0, "name": "Emotion/27-dribble/3"}]}, "Emotion/27-dribble/5": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/27-dribble/5"}]}, "Emotion/27-dribble/6": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7667, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/27-dribble/6"}, {"time": 1.2667, "name": "Emotion/27-dribble/6"}]}, "Emotion/27-dribble/7": {"attachment": [{"time": 0, "name": "Emotion/27-dribble/7"}]}, "Emotion/27-dribble/8": {"attachment": [{"time": 0, "name": "Emotion/27-dribble/8"}]}, "Emotion/27-dribble/9": {"attachment": [{"time": 0, "name": "Emotion/27-dribble/9"}]}, "Emotion/27-dribble/10": {"attachment": [{"time": 0, "name": "Emotion/27-dribble/10"}]}, "Emotion/27-dribble/11": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 1.2667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3, "color": "ffffff00"}, {"time": 1.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/27-dribble/6"}, {"time": 1.3, "name": "Emotion/27-dribble/6"}]}, "Emotion/27-dribble/12": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.5333, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 1, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/27-dribble/5"}, {"time": 1, "name": "Emotion/27-dribble/5"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 0.9333, "angle": 0}], "translate": [{"time": 0, "x": 23.43, "y": -26.96, "curve": "stepped"}, {"time": 0.6, "x": 23.43, "y": -26.96, "curve": "stepped"}, {"time": 0.6667, "x": 23.43, "y": -26.96, "curve": "stepped"}, {"time": 0.7333, "x": 23.43, "y": -26.96, "curve": "stepped"}, {"time": 0.8, "x": 23.43, "y": -26.96, "curve": "stepped"}, {"time": 0.8667, "x": 23.43, "y": -26.96, "curve": "stepped"}, {"time": 0.9333, "x": 23.43, "y": -26.96}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1}, {"time": 0.6333, "x": 0.924, "y": 0.924}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 0.7, "x": 0.924, "y": 0.924}, {"time": 0.7333, "x": 1, "y": 1}, {"time": 0.7667, "x": 0.924, "y": 0.924}, {"time": 0.8, "x": 1, "y": 1}, {"time": 0.8333, "x": 0.924, "y": 0.924}, {"time": 0.8667, "x": 1, "y": 1}, {"time": 0.9, "x": 0.924, "y": 0.924}, {"time": 0.9333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 0.8, "angle": 0, "curve": "stepped"}, {"time": 0.8667, "angle": 0, "curve": "stepped"}, {"time": 0.9333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 0.4, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 0.6, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 0.6667, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 0.7333, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 0.8, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 0.8667, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 0.9333, "x": 1.17, "y": 20.97, "curve": "stepped"}, {"time": 1.6667, "x": 1.17, "y": 20.97}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.4, "x": 0.945, "y": 0.945}, {"time": 0.6, "x": 0.954, "y": 0.954}, {"time": 0.6333, "x": 0.903, "y": 0.903}, {"time": 0.6667, "x": 0.954, "y": 0.954}, {"time": 0.7, "x": 0.903, "y": 0.903}, {"time": 0.7333, "x": 0.954, "y": 0.954}, {"time": 0.7667, "x": 0.903, "y": 0.903}, {"time": 0.8, "x": 0.954, "y": 0.954}, {"time": 0.8333, "x": 0.903, "y": 0.903}, {"time": 0.8667, "x": 0.954, "y": 0.954}, {"time": 0.9, "x": 0.903, "y": 0.903}, {"time": 0.9333, "x": 0.956, "y": 0.956}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 9.26}], "translate": [{"time": 0, "x": -68.53, "y": 2.36}, {"time": 1.6667, "x": -72.07, "y": -70.77}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": -57.82, "y": -2.07}, {"time": 0.9667, "x": -57.82, "y": -18.61}, {"time": 1, "x": -57.82, "y": 8.96}, {"time": 1.6667, "x": -57.82, "y": -2.07}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1, "x": 0, "y": 0}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.2333, "angle": 0, "curve": "stepped"}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": -3.98, "y": 1.86}, {"time": 1.2333, "x": -4.17, "y": -39.1}, {"time": 1.2667, "x": -3.92, "y": 14.79}, {"time": 1.6667, "x": -3.98, "y": 1.86}], "scale": [{"time": 0, "x": 0.444, "y": 0.444}, {"time": 0.4667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2333, "x": 1, "y": 1}, {"time": 1.2667, "x": 0, "y": 0}, {"time": 1.6667, "x": 0.444, "y": 0.444}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.2667, "angle": 0, "curve": "stepped"}, {"time": 1.3, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": -28.99, "y": -16.96}, {"time": 1.2667, "x": -32.35, "y": -59.19}, {"time": 1.3, "x": -28.04, "y": -5.05}, {"time": 1.6667, "x": -28.99, "y": -16.96}], "scale": [{"time": 0, "x": 0.832, "y": 0.832}, {"time": 1.2667, "x": 1, "y": 1}, {"time": 1.3, "x": 0, "y": 0}, {"time": 1.6667, "x": 0.832, "y": 0.832}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 15.18}], "translate": [{"time": 0, "x": 11.49, "y": 17.24}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 10.25, "curve": "stepped"}, {"time": 1.6667, "angle": 10.25}], "translate": [{"time": 0, "x": 18.78, "y": -7.73, "curve": "stepped"}, {"time": 1.6667, "x": 18.78, "y": -7.73}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1.036, "y": 1.525, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.936, "y": 1.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.114, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0.936, "y": 1.04}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0}]}}}}, "24-tire": {"slots": {"Emotion/1-Buon/1": {"attachment": [{"time": 0, "name": "Emotion/1-Buon/1"}, {"time": 1.3333, "name": "Emotion/1-Buon/1"}]}, "Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/33-tire/2": {"color": [{"time": 0, "color": "ffffff00"}, {"time": 0.2, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2333, "color": "ffffffff"}, {"time": 2.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/33-tire/2"}, {"time": 1.3333, "name": "Emotion/33-tire/2"}]}, "Emotion/33-tire/3": {"attachment": [{"time": 0, "name": "Emotion/33-tire/3"}, {"time": 1.3333, "name": "Emotion/33-tire/3"}]}, "Emotion/33-tire/4": {"attachment": [{"time": 0, "name": "Emotion/33-tire/4"}, {"time": 1.3333, "name": "Emotion/33-tire/4"}]}, "Emotion/33-tire/5": {"attachment": [{"time": 0, "name": "Emotion/33-tire/5"}, {"time": 1.3333, "name": "Emotion/33-tire/5"}]}, "Emotion/33-tire/6": {"attachment": [{"time": 0, "name": "Emotion/33-tire/6"}, {"time": 1.3333, "name": "Emotion/33-tire/6"}]}, "Emotion/33-tire/7": {"attachment": [{"time": 0, "name": "Emotion/33-tire/7"}, {"time": 1.3333, "name": "Emotion/33-tire/7"}]}, "Emotion/33-tire/8": {"attachment": [{"time": 0, "name": "Emotion/33-tire/8"}, {"time": 1.3333, "name": "Emotion/33-tire/8"}]}, "Emotion/33-tire/9": {"attachment": [{"time": 0, "name": "Emotion/33-tire/9"}, {"time": 1.3333, "name": null}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.489, 0.98]}, {"time": 0.6667, "x": 0, "y": 2.57, "curve": [0.173, 0.31, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.489, 0.98]}, {"time": 2, "x": 0, "y": 2.57, "curve": [0.173, 0.31, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.452, 1.01]}, {"time": 0.6667, "x": 0.923, "y": 1.036, "curve": [0.199, 0.94, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.452, 1.01]}, {"time": 2, "x": 0.923, "y": 1.036, "curve": [0.199, 0.94, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": -16.39, "curve": "stepped"}, {"time": 1.3333, "angle": -16.39}], "translate": [{"time": 0, "x": 24.31, "y": -15.83, "curve": "stepped"}, {"time": 1.3333, "x": 24.31, "y": -15.83}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 1.3333, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": -19.04, "curve": "stepped"}, {"time": 1.3333, "angle": -19.04, "curve": "stepped"}, {"time": 2.6667, "angle": -19.04}], "translate": [{"time": 0, "x": -21.25, "y": 60.38, "curve": [0.25, 0, 0.511, 0.99]}, {"time": 0.6667, "x": -21.02, "y": 63.58, "curve": [0.199, 0.28, 0.75, 1]}, {"time": 1.3333, "x": -21.25, "y": 60.38, "curve": [0.25, 0, 0.511, 0.99]}, {"time": 2, "x": -21.02, "y": 63.58, "curve": [0.199, 0.28, 0.75, 1]}, {"time": 2.6667, "x": -21.25, "y": 60.38}], "scale": [{"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 17.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 15.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 17.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 15.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 17.96}], "translate": [{"time": 0, "x": -82.76, "y": 70.19, "curve": [0.25, 0, 0.507, 0.98]}, {"time": 0.6667, "x": -82.81, "y": 73.32, "curve": [0.202, 0.33, 0.75, 1]}, {"time": 1.3333, "x": -82.76, "y": 70.19, "curve": [0.25, 0, 0.507, 0.98]}, {"time": 2, "x": -82.81, "y": 73.32, "curve": [0.202, 0.33, 0.75, 1]}, {"time": 2.6667, "x": -82.76, "y": 70.19}], "scale": [{"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": -9.48, "curve": "stepped"}, {"time": 1.3333, "angle": -9.48}], "translate": [{"time": 0, "x": 13.43, "y": 3.53, "curve": "stepped"}, {"time": 1.3333, "x": 13.43, "y": 3.53}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 21.56, "y": -12.66, "curve": "stepped"}, {"time": 1.3333, "x": 21.56, "y": -12.66, "curve": "stepped"}, {"time": 2.6667, "x": 21.56, "y": -12.66}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "x": 0.844, "y": 0.844, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.007, "y": 1.007, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0.862, "y": 0.862, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0.986, "y": 0.986, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0.865, "y": 0.865, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 0.987, "y": 0.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.783, "y": 0.783, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1.027, "y": 1.027, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 0.844, "y": 0.844, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.007, "y": 1.007, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 0.862, "y": 0.862, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9333, "x": 0.986, "y": 0.986, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.0667, "x": 0.865, "y": 0.865, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "x": 0.987, "y": 0.987, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0.783, "y": 0.783, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4667, "x": 1.027, "y": 1.027, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone13": {"translate": [{"time": 0, "x": -48.56, "y": -40.53, "curve": "stepped"}, {"time": 1.3333, "x": -48.56, "y": -40.53}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 23.22, "curve": "stepped"}, {"time": 1.3333, "angle": 23.22}, {"time": 1.4333, "angle": 29.23}, {"time": 1.4667, "angle": 42.3}, {"time": 1.5, "angle": 23.22}], "translate": [{"time": 0, "x": 51.3, "y": -47.2, "curve": "stepped"}, {"time": 1.3333, "x": 51.3, "y": -47.2}, {"time": 1.4333, "x": 49.95, "y": -46.86}, {"time": 1.4667, "x": 46.79, "y": -45.87}, {"time": 1.5, "x": 51.3, "y": -47.2}], "scale": [{"time": 0, "x": 1, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 0}, {"time": 1.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1}, {"time": 1.4667, "x": 1, "y": 0.686}, {"time": 1.5, "x": 1, "y": 0}], "shear": [{"time": 1.5, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": -130.31, "curve": "stepped"}, {"time": 1.3333, "angle": -130.31}], "translate": [{"time": 0, "x": -33.07, "y": 7.52, "curve": "stepped"}, {"time": 1.3333, "x": -33.07, "y": 7.52}, {"time": 2.6667, "x": 3.58, "y": -22.88}], "scale": [{"time": 0, "x": 0.833, "y": 0.833, "curve": "stepped"}, {"time": 1.3333, "x": 0.833, "y": 0.833}, {"time": 2.6667, "x": 1.747, "y": 1.747}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone3": {"translate": [{"time": 1.1, "x": -0.45, "y": -50.62, "curve": "stepped"}, {"time": 2.4333, "x": -0.45, "y": -50.62}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": "stepped"}, {"time": 1.3333}]}}}}, "25-BigSmile": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}, {"time": 1.4, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/3-Chao/2": {"attachment": [{"time": 0, "name": "Emotion/3-<PERSON>/2"}, {"time": 2, "name": "Emotion/3-<PERSON>/2"}]}, "Emotion/3-Chao/3": {"attachment": [{"time": 0, "name": "Emotion/3-<PERSON>/3"}, {"time": 2, "name": "Emotion/3-<PERSON>/3"}]}, "Emotion/3-Chao/4": {"attachment": [{"time": 0, "name": "Emotion/3-<PERSON>/4"}]}, "Emotion/3-Chao/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/3-Chao/6": {"attachment": [{"time": 0, "name": null}]}, "Emotion/3-Chao/7": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/6": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/7": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/9": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/10": {"attachment": [{"time": 0, "name": null}]}, "Emotion/6-Soc/11": {"attachment": [{"time": 0, "name": null}]}, "Emotion/9-Gao Thet/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/9-Gao Thet/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/9-Gao Thet/5": {"attachment": [{"time": 0, "name": null}]}}, "bones": {"root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -0.25, "y": -23.57, "curve": "stepped"}, {"time": 1.4, "x": -0.25, "y": -23.57}], "scale": [{"time": 0, "x": 0.552, "y": 0.356, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 1, "y": 0.258}, {"time": 0.2, "x": 1, "y": 0.194}, {"time": 0.2333, "x": 0.544, "y": 0.362}, {"time": 0.2667, "x": 1, "y": 1.138}, {"time": 0.3333, "x": 1, "y": 1.272}, {"time": 0.4333, "x": 1.161, "y": 0.764}, {"time": 0.5333, "x": 1, "y": 1.05}, {"time": 0.6, "x": 1, "y": 1.414}, {"time": 0.6667, "x": 1, "y": 1.05}, {"time": 0.7333, "x": 1, "y": 1.314}, {"time": 0.8, "x": 1, "y": 1.05}, {"time": 0.8667, "x": 1, "y": 1.327}, {"time": 0.9333, "x": 1, "y": 1.05}, {"time": 1, "x": 1, "y": 1.327}, {"time": 1.0667, "x": 1, "y": 1.05}, {"time": 1.1333, "x": 1, "y": 1.327}, {"time": 1.2, "x": 1, "y": 1.05}, {"time": 1.2667, "x": 1, "y": 1.327}, {"time": 1.3333, "x": 1, "y": 1.05}, {"time": 1.4, "x": 0.552, "y": 0.356}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 1.4, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 1.11, "y": -13.21, "curve": "stepped"}, {"time": 0.0667, "x": 1.11, "y": -13.21, "curve": "stepped"}, {"time": 0.2333, "x": 1.11, "y": -13.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.17, "y": -8.16, "curve": [0.242, 0, 0.669, 0.67]}, {"time": 1.9333, "x": 1.12, "y": -9.61, "curve": [0.379, 0.6, 0.723, 1]}, {"time": 2, "x": 1.11, "y": -13.21}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 1.269, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "x": 0.939, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.754, "y": 1.698, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 1.239, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 13.95, "y": -8.32, "curve": "stepped"}, {"time": 0.1667, "x": 13.95, "y": -8.32}, {"time": 0.3, "x": 14.01, "y": -7.3, "curve": "stepped"}, {"time": 0.6667, "x": 14.01, "y": -7.3}, {"time": 1.4, "x": 13.95, "y": -8.32}], "scale": [{"time": 0, "x": 0.294, "y": 0.294, "curve": "stepped"}, {"time": 0.1667, "x": 0.294, "y": 0.294}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1}, {"time": 0.7667, "x": 1.156, "y": 1.156}, {"time": 0.9667, "x": 0.983, "y": 0.983}, {"time": 1.3333, "x": 1.087, "y": 1.087}, {"time": 1.4, "x": 0.294, "y": 0.294}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.1667, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": -2.78, "y": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -5.21, "y": -2.51, "curve": "stepped"}, {"time": 0.3, "x": -5.21, "y": -2.51, "curve": "stepped"}, {"time": 1.3333, "x": -5.21, "y": -2.51}, {"time": 1.4, "x": -2.78, "y": -9.33}], "scale": [{"time": 0, "x": 0.475, "y": 0.475, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 0.575, "y": 0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0.372, "y": 0.536, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.231, "y": 0.231, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}, {"time": 1.4, "x": 0.475, "y": 0.475}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": 0.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 1.8}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 0, "y": -25.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0, "y": 28.28, "curve": [0.09, 0, 0.348, 0.94]}, {"time": 0.2667, "x": 0, "y": 95.17, "curve": [0.566, -0.01, 0.914, 0.99]}, {"time": 0.4, "x": 0, "y": -26.23, "curve": [0.304, 0.01, 0.645, 0.41]}, {"time": 0.4333, "x": 0, "y": -9.03, "curve": [0.363, 0.34, 0.768, 1]}, {"time": 0.5333, "x": 0, "y": 8.93, "curve": [0.484, 0.04, 0.859, 1]}, {"time": 0.6333, "x": 0, "y": 8.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 0, "y": 2.73}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "x": 1.481, "y": 0.416, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 0.62, "y": 1.633, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.053, "y": 1.496, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.963, "y": 1.847, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1.249, "y": 1.069, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0.916, "y": 1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.006, "y": 1.471, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 1.035, "y": 1.317, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 1, "y": 1.199, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.3333, "x": 2.86, "y": 22.73}, {"time": 0.4333, "x": -1.36, "y": 4.83}, {"time": 0.5, "x": 0.14, "y": 13.72}, {"time": 0.6, "x": -0.18, "y": 18.08}, {"time": 0.7, "x": 0.14, "y": 13.72}, {"time": 0.8, "x": -0.18, "y": 18.08}, {"time": 0.9, "x": 0.14, "y": 13.72}, {"time": 1, "x": -0.18, "y": 18.08}, {"time": 1.1, "x": 0.14, "y": 13.72}, {"time": 1.2, "x": -0.18, "y": 18.08}, {"time": 1.3, "x": 0.14, "y": 13.72}, {"time": 1.4, "x": -0.18, "y": 18.08}, {"time": 1.5, "x": 0.14, "y": 13.72}, {"time": 1.6, "x": -0.18, "y": 18.08}, {"time": 1.7, "x": 0.14, "y": 13.72}, {"time": 1.8, "x": -0.18, "y": 18.08}, {"time": 1.9, "x": 0.14, "y": 13.72}, {"time": 2, "x": 0.03, "y": 27.8}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.3333, "x": 0.69, "y": 1.017}, {"time": 0.4333, "x": 1.356, "y": 1.015}, {"time": 0.5, "x": 1.003, "y": 0.958}, {"time": 0.6, "x": 1.186, "y": 0.625}, {"time": 0.7, "x": 1.003, "y": 0.958}, {"time": 0.8, "x": 1.186, "y": 0.625}, {"time": 0.9, "x": 1.003, "y": 0.958}, {"time": 1, "x": 1.186, "y": 0.625}, {"time": 1.1, "x": 1.003, "y": 0.958}, {"time": 1.2, "x": 1.186, "y": 0.625}, {"time": 1.3, "x": 1.003, "y": 0.958}, {"time": 1.4, "x": 1.186, "y": 0.625}, {"time": 1.5, "x": 1.003, "y": 0.958}, {"time": 1.6, "x": 1.186, "y": 0.625}, {"time": 1.7, "x": 1.003, "y": 0.958}, {"time": 1.8, "x": 1.186, "y": 0.625}, {"time": 1.9, "x": 1.003, "y": 0.958}, {"time": 2, "x": 0.936, "y": 0.768}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4667, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 7.29, "y": 2.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": -3.32, "y": 7.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -7.43, "y": -2.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 3.39, "y": -7.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 7.32, "y": 2.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5333, "angle": 0, "curve": "stepped"}, {"time": 1.4, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 3.34, "y": -12.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 12.71, "y": -0.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": -4, "y": 9.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": -13, "y": -3.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": 1.57, "y": -10.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 0, "y": 0}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4, "x": 0, "y": 0}]}}}, "3-boss": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/2-NgacNhien/2": {"attachment": [{"time": 0, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/2"}, {"time": 1.3333, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/2"}, {"time": 3.1667, "name": "Emotion/2-<PERSON><PERSON><PERSON><PERSON><PERSON>/2"}]}, "Emotion/3-Chao/2": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/3-Chao/3": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/3-Chao/4": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/3-Chao/5": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/3-Chao/7": {"attachment": [{"time": 0, "name": null}, {"time": 1.3333, "name": null}]}, "Emotion/4-Hut Thuoc/2": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff"}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 3, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/3": {"attachment": [{"time": 0, "name": "Emotion/4-<PERSON><PERSON>/3"}, {"time": 1.3667, "name": "Emotion/4-<PERSON><PERSON>/3"}, {"time": 3.1667, "name": "Emotion/4-<PERSON><PERSON>/3"}]}, "Emotion/4-Hut Thuoc/4": {"attachment": [{"time": 0, "name": "Emotion/4-<PERSON><PERSON>/4"}, {"time": 1.3333, "name": "Emotion/4-<PERSON><PERSON>/4"}, {"time": 3.1667, "name": "Emotion/4-<PERSON><PERSON>/4"}]}, "Emotion/4-Hut Thuoc/5": {"attachment": [{"time": 0, "name": "Emotion/4-<PERSON><PERSON>/5"}, {"time": 0.2333, "name": "Emotion/4-<PERSON><PERSON>/5"}, {"time": 1.3333, "name": "Emotion/4-<PERSON><PERSON>/5"}, {"time": 2.5667, "name": "Emotion/4-<PERSON><PERSON>/5"}]}, "Emotion/4-Hut Thuoc/6": {"attachment": [{"time": 0, "name": "Emotion/4-<PERSON><PERSON>/6"}, {"time": 1.3333, "name": "Emotion/4-<PERSON><PERSON>/6"}, {"time": 3.1667, "name": "Emotion/4-<PERSON><PERSON>/6"}]}, "Emotion/4-Hut Thuoc/7": {"attachment": [{"time": 0, "name": "Emotion/4-<PERSON><PERSON>/7"}, {"time": 1.3333, "name": "Emotion/4-<PERSON><PERSON>/7"}, {"time": 3.1667, "name": "Emotion/4-<PERSON><PERSON>/7"}]}, "Emotion/4-Hut Thuoc/8": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.6667, "color": "ffffffff"}, {"time": 0.8333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8667, "color": "ffffff00"}, {"time": 1.0333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.2, "color": "ffffff00"}, {"time": 2.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 0.8667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 2.2, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/9": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8667, "color": "ffffffff"}, {"time": 1.1667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 2.5, "color": "ffffff00", "curve": "stepped"}, {"time": 2.5333, "color": "ffffff00"}, {"time": 2.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.2, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 2.5333, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/10": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1, "color": "ffffffff"}, {"time": 1.4, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4333, "color": "ffffff00"}, {"time": 1.6, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4333, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.7667, "color": "ffffff00"}, {"time": 2.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.4333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 2.7667, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/11": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.3, "color": "ffffff00", "curve": "stepped"}, {"time": 1.3333, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.3667, "color": "ffffffff"}, {"time": 2.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6667, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 2.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/12": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 1.6333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.6667, "color": "ffffff00"}, {"time": 1.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 2.7667, "color": "ffffffff"}, {"time": 2.9667, "color": "ffffff00", "curve": "stepped"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 3, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/13": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}, {"time": 0.8667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00", "curve": "stepped"}, {"time": 2.0333, "color": "ffffff00"}, {"time": 2.2, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 0.7, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 2.0333, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/14": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2667, "color": "ffffff00"}, {"time": 1.4, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2667, "color": "ffffffff"}, {"time": 2.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.6, "color": "ffffff00"}, {"time": 2.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.2667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 2.6, "name": "Emotion/4-<PERSON><PERSON>/2"}]}, "Emotion/4-Hut Thuoc/15": {"color": [{"time": 0.3333, "color": "ffffff00"}, {"time": 0.5, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00", "curve": "stepped"}, {"time": 1.4667, "color": "ffffff00"}, {"time": 1.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.5, "color": "ffffffff"}, {"time": 2.7667, "color": "ffffff00", "curve": "stepped"}, {"time": 2.8, "color": "ffffff00"}, {"time": 3, "color": "ffffffff"}, {"time": 3.1667, "color": "ffffff00"}], "attachment": [{"time": 0.3333, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.4667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 1.6667, "name": "Emotion/4-<PERSON><PERSON>/2"}, {"time": 2.8, "name": "Emotion/4-<PERSON><PERSON>/2"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -1.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 6.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 3.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -1.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4, "angle": -1.58}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 1.73, "y": -2.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 1.73, "y": 3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 1.73, "y": -0.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 3.89, "y": -0.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4, "x": 3.89, "y": -0.43}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 1, "y": 0.961, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4, "x": 1, "y": 0.961}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 1.3333, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0.252, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 0.252}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0.615, "y": 0.284, "curve": "stepped"}, {"time": 1.3333, "x": 0.615, "y": 0.284}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.5, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 1.046, "y": 0.947, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0.991, "y": 1.072, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1.067, "y": 1.085}, {"time": 2.2667, "x": 1.16, "y": 1.097}, {"time": 2.5667, "x": 1.085, "y": 1.028}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "bone10": {"rotate": [{"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 1.6333, "x": 0, "y": 90.69}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2.9667, "x": 0, "y": 90.69}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.9667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": 9.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 15.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -5.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 20.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 9.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 9.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 9.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 6.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "angle": 9.82}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": -0.26, "y": -0.58, "curve": "stepped"}, {"time": 0.7, "x": -0.26, "y": -0.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": -8.1, "y": 4.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -9.08, "y": 5.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": -2.38, "y": 1.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": -3.5, "y": 2.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -7.24, "y": 6.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4, "x": 0.21, "y": 0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.1667, "x": 1, "y": 1, "curve": [0.243, 0, 0.691, 0.76]}, {"time": 0.8667, "x": 0.95, "y": 1.604, "curve": [0.37, 0.63, 0.708, 1]}, {"time": 1, "x": 0.945, "y": 1.627, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1.038, "y": 1.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1.083, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "x": 1.028, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 0.937, "y": 1.108, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.806, "y": 1.339, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.4, "x": 1.015, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2, "angle": 0}, {"time": 0.3333, "angle": 19.07}, {"time": 0.5333, "angle": 7.37}, {"time": 0.7333, "angle": 3.26}, {"time": 1.0333, "angle": 14.89}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": 8.55}, {"time": 2.2, "angle": -5.04}, {"time": 2.6667, "angle": 9.79}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.3333, "x": 3.46, "y": -3.51}, {"time": 0.5333, "x": 0.73, "y": 0.79}, {"time": 0.7333, "x": -0.76, "y": 0.17}, {"time": 1.0333, "x": -0.14, "y": -3.38}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2, "x": -1.64, "y": -3.42}, {"time": 2.2, "x": -2.47, "y": -6.47}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "bone11": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2333, "angle": 0}, {"time": 0.3333, "angle": 9.14}, {"time": 0.4, "angle": 2.1, "curve": "stepped"}, {"time": 0.5, "angle": 2.1}, {"time": 0.6, "angle": -3.82}, {"time": 0.7333, "angle": 2.1}, {"time": 1.3333, "angle": 0}, {"time": 2, "angle": 2.1}, {"time": 2.1, "angle": 7.04}, {"time": 2.2333, "angle": 4.72}, {"time": 2.5667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.3333, "x": 2.61, "y": 23.34}, {"time": 0.4, "x": 1.17, "y": 6.34, "curve": "stepped"}, {"time": 0.5, "x": 1.17, "y": 6.34}, {"time": 0.6, "x": 1.84, "y": 9.44}, {"time": 0.7333, "x": 1.17, "y": 6.34}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 2, "x": 1.17, "y": 6.34}, {"time": 2.1, "x": 0.97, "y": 13.94}, {"time": 2.2333, "x": 1.22, "y": 4.64}, {"time": 2.5667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5667, "x": 0, "y": 0}]}, "bone4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -8.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 21.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 5.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 19.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 5.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "angle": 27.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "angle": 15.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.48, "y": 1.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -2.62, "y": -6.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -1.7, "y": -2.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": -1.6, "y": -6.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": -0.72, "y": -1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -0.17, "y": -2.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0.86, "y": -7.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7333, "x": 1.22, "y": -2.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 16.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 2.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -6.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 18.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -4.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 20.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "angle": -2.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7, "angle": 9.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.67, "y": -2.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -0.95, "y": 7.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -0.29, "y": 0.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": -0.27, "y": -0.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -0.38, "y": 3.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "x": -0.31, "y": 1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": -0.35, "y": 2.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2667, "x": -0.26, "y": -0.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.7, "x": -1.24, "y": 2.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0}, {"time": 1, "angle": 4.21}, {"time": 1.3333, "angle": 0}, {"time": 1.4, "angle": 4.21}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 27.22, "y": -6.05, "curve": "stepped"}, {"time": 0.6, "x": 27.22, "y": -6.05}, {"time": 0.8667, "x": 25.33, "y": -3.42}, {"time": 1, "x": 24.42, "y": -3.4}, {"time": 1.1333, "x": 25.34, "y": -3.86}, {"time": 1.3333, "x": 27.22, "y": -6.05}, {"time": 1.4, "x": 26.43, "y": -6.94}, {"time": 1.6333, "x": 25.31, "y": -1.83}, {"time": 2.0333, "x": 25.16, "y": 0.09}, {"time": 2.4, "x": 26.42, "y": -6.76}, {"time": 2.5, "x": 26.32, "y": -2.97}, {"time": 2.8333, "x": 26.35, "y": -3.79}, {"time": 3.1667, "x": 27.22, "y": -6.05}], "scale": [{"time": 0, "x": 0.742, "y": 0.37}, {"time": 0.1667, "x": 0.691, "y": 0.344, "curve": "stepped"}, {"time": 0.6, "x": 0.691, "y": 0.344}, {"time": 0.8667, "x": 1.614, "y": 1.036}, {"time": 1, "x": 1.658, "y": 1.146}, {"time": 1.1333, "x": 0.835, "y": 0.577}, {"time": 1.3333, "x": 0.742, "y": 0.37}, {"time": 1.4, "x": 0.57, "y": 0.394}, {"time": 1.6333, "x": 1.317, "y": 0.91}, {"time": 2.0333, "x": 1.544, "y": 1.067}, {"time": 2.4, "x": 0.52, "y": 0.36}, {"time": 2.5, "x": 0.868, "y": 0.6}, {"time": 2.8333, "x": 0.837, "y": 0.579}, {"time": 3.1667, "x": 0.742, "y": 0.37}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0.3333, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 1.6667, "angle": 0, "curve": "stepped"}, {"time": 2.9667, "angle": 0, "curve": "stepped"}, {"time": 3, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0}, {"time": 1.6333, "x": 0, "y": 90.69}, {"time": 1.6667, "x": 0, "y": 0}, {"time": 2.9667, "x": 0, "y": 90.69}, {"time": 3, "x": 0, "y": 0}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.9667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0.3333, "angle": -29.31}, {"time": 0.8333, "angle": -39.08}, {"time": 0.8667, "angle": 0}, {"time": 1.6667, "angle": -29.31}, {"time": 2.1667, "angle": -39.08}, {"time": 2.2, "angle": 0}, {"time": 3, "angle": -29.31}], "translate": [{"time": 0.3333, "x": -2.79, "y": 59.42}, {"time": 0.8333, "x": -3.72, "y": 79.22}, {"time": 0.8667, "x": 0, "y": 0}, {"time": 1.6667, "x": -2.79, "y": 59.42}, {"time": 2.1667, "x": -3.72, "y": 79.22}, {"time": 2.2, "x": 0, "y": 0}, {"time": 3, "x": -2.79, "y": 59.42}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 0.8667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.2, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone17": {"rotate": [{"time": 0.3333, "angle": 5.01}, {"time": 1.3, "angle": 33.42}, {"time": 1.3333, "angle": 0}, {"time": 1.6667, "angle": 5.01}, {"time": 2.6333, "angle": 33.42}, {"time": 2.6667, "angle": 0}, {"time": 3, "angle": 5.01}], "translate": [{"time": 0.3333, "x": 0.13, "y": 14.8}, {"time": 1.3, "x": 0.86, "y": 98.67}, {"time": 1.3333, "x": 0, "y": 0}, {"time": 1.6667, "x": 0.13, "y": 14.8}, {"time": 2.6333, "x": 0.86, "y": 98.67}, {"time": 2.6667, "x": 0, "y": 0}, {"time": 3, "x": 0.13, "y": 14.8}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0.3333, "angle": 5.01}, {"time": 1.4333, "angle": 33.42}, {"time": 1.4667, "angle": 0}, {"time": 1.6667, "angle": 5.01}, {"time": 2.7667, "angle": 33.42}, {"time": 2.8, "angle": 0}, {"time": 3, "angle": 5.01}], "translate": [{"time": 0.3333, "x": 0.13, "y": 14.8}, {"time": 1.4333, "x": 0.86, "y": 98.67}, {"time": 1.4667, "x": 0, "y": 0}, {"time": 1.6667, "x": 0.13, "y": 14.8}, {"time": 2.7667, "x": 0.86, "y": 98.67}, {"time": 2.8, "x": 0, "y": 0}, {"time": 3, "x": 0.13, "y": 14.8}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.4667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.8, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0.3333, "angle": -29.31}, {"time": 0.6667, "angle": -39.08}, {"time": 0.7, "angle": 0}, {"time": 1.6667, "angle": -29.31}, {"time": 2, "angle": -39.08}, {"time": 2.0333, "angle": 0}, {"time": 3, "angle": -29.31}], "translate": [{"time": 0.3333, "x": -2.79, "y": 59.42}, {"time": 0.6667, "x": -3.72, "y": 79.22}, {"time": 0.7, "x": 0, "y": 0}, {"time": 1.6667, "x": -2.79, "y": 59.42}, {"time": 2, "x": -3.72, "y": 79.22}, {"time": 2.0333, "x": 0, "y": 0}, {"time": 3, "x": -2.79, "y": 59.42}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 0.7, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.0333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.0333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone16": {"rotate": [{"time": 0.3333, "angle": -8.3}, {"time": 1.4, "angle": -33.22}, {"time": 1.4333, "angle": 0}, {"time": 1.6667, "angle": -8.3}, {"time": 2.7333, "angle": -33.22}, {"time": 2.7667, "angle": 0}, {"time": 3, "angle": -8.3}], "translate": [{"time": 0.3333, "x": -1.64, "y": 20.73}, {"time": 1.4, "x": -6.58, "y": 82.94}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 1.6667, "x": -1.64, "y": 20.73}, {"time": 2.7333, "x": -6.58, "y": 82.94}, {"time": 2.7667, "x": 0, "y": 0}, {"time": 3, "x": -1.64, "y": 20.73}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.4333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.7667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone15": {"rotate": [{"time": 0.3333, "angle": 10.56}, {"time": 1.1667, "angle": 21.11}, {"time": 1.2, "angle": 0}, {"time": 1.6667, "angle": 10.56}, {"time": 2.5, "angle": 21.11}, {"time": 2.5333, "angle": 0}, {"time": 3, "angle": 10.56}], "translate": [{"time": 0.3333, "x": -1.57, "y": 41.76}, {"time": 1.1667, "x": -3.15, "y": 83.51}, {"time": 1.2, "x": 0, "y": 0}, {"time": 1.6667, "x": -1.57, "y": 41.76}, {"time": 2.5, "x": -3.15, "y": 83.51}, {"time": 2.5333, "x": 0, "y": 0}, {"time": 3, "x": -1.57, "y": 41.76}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.2, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.5333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0.3333, "angle": 10.56}, {"time": 1.2333, "angle": 21.11}, {"time": 1.2667, "angle": 0}, {"time": 1.6667, "angle": 10.56}, {"time": 2.5667, "angle": 21.11}, {"time": 2.6, "angle": 0}, {"time": 3, "angle": 10.56}], "translate": [{"time": 0.3333, "x": -1.57, "y": 41.76}, {"time": 1.2333, "x": -3.15, "y": 83.51}, {"time": 1.2667, "x": 0, "y": 0}, {"time": 1.6667, "x": -1.57, "y": 41.76}, {"time": 2.5667, "x": -3.15, "y": 83.51}, {"time": 2.6, "x": 0, "y": 0}, {"time": 3, "x": -1.57, "y": 41.76}], "scale": [{"time": 0.3333, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.2667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 1.6667, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 2.6, "x": 0.661, "y": 0.661, "curve": "stepped"}, {"time": 3, "x": 0.661, "y": 0.661}], "shear": [{"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3, "x": 0, "y": 0}]}, "bone8": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.2667, "angle": 0, "curve": "stepped"}, {"time": 3.1667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.2667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.1667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "offset": 34, "vertices": [1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495, 1.5315, -4.37495], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "offset": 34, "vertices": [-0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869, -0.45073, 6.53869], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 34, "vertices": [0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642, 0.53977, 1.60642], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "offset": 34, "vertices": [-0.81607, 6.42284, -0.96247, 6.41918, -2.12872, 6.39006, -3.0152, 6.36793, -3.31269, 6.3605, -3.1848, 6.3637, -2.63445, 6.37743, -1.76536, 6.39914, 0.06807, 6.44491, 1.02259, 6.46875, 2.11096, 6.49591, 2.91662, 6.51603, 3.541, 6.53161, 3.52044, 6.5311, 1.9449, 6.49177, 0.54635, 6.45685, 2.92067, 6.51612, 0.42982, 6.45394, 1.36862, 6.47738, 1.95663, 6.49206, 2.39052, 6.50289, 2.47426, 6.50498, 2.04032, 6.49415, 1.47813, 6.48011, 0.86883, 6.46491, -1.22148, 6.41272, -1.8913, 6.39599, -2.24892, 6.38706, -2.28098, 6.38626, -2.0666, 6.39161, 0.08704, 6.44538, -0.66581, 6.42658, -1.45909, 6.40678, -0.64572, 6.42709, 1.56727, 6.48234, 1.27767, 6.4751, 0.87686, 6.4651, 0.61591, 6.45859, 0.19569, 6.4481, -0.41274, 6.4329, -0.75826, 6.42428, -1.06823, 6.41654, -1.2367, 6.41233, -1.24951, 6.41201, -1.13993, 6.41475, -0.79209, 6.42343, -0.41234, 6.43291, 0.28659, 6.45036, 0.79301, 6.46301, 1.2741, 6.47502, 1.49357, 6.4805, 0.0641, 6.44481], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "offset": 34, "vertices": [1.13577, 3.31058, 1.35279, -0.51898, 1.91702, -0.16703, 2.33825, 0.30282, 2.45576, 1.09298, 2.36062, 1.93566, 2.06239, 2.61696, 1.61517, 3.06371, 0.69598, 3.363, 0.22563, 3.30149, -0.30079, 2.96957, -0.67985, 2.44285, -0.95845, 1.63186, -0.91502, 0.75091, -0.09345, -0.34561, 0.6081, -0.58418, -0.59528, 0.14851, 0.6385, 0.13928, 0.16558, 0.3523, -0.13672, 0.64695, -0.36581, 1.02445, -0.42525, 1.50069, -0.2349, 2.15044, 0.02913, 2.53107, 0.32417, 2.70805, 1.36535, 2.5468, 1.71127, 2.1696, 1.90499, 1.72932, 1.93954, 1.23402, 1.85469, 0.66956, 0.71065, 2.72517, 1.08577, 2.66382, 1.5665, 0.33456, 1.17207, 0.1136, 0.02526, 1.46405, 0.15525, 1.81925, 0.34602, 2.02369, 0.4726, 2.09374, 0.67911, 2.13534, 0.98247, 2.0807, 1.15632, 2.00758, 1.31652, 1.82983, 1.4101, 1.56068, 1.4291, 1.22543, 1.38375, 0.98889, 1.21948, 0.77784, 1.03392, 0.71194, 0.68751, 0.72014, 0.43128, 0.86469, 0.18659, 1.03602, 0.06944, 1.26021, 0.77008, 1.4516], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "offset": 34, "vertices": [-0.73998, 6.77336, -0.48989, -5.59521, -1.62071, -4.55039, -2.50074, -3.10334, -2.86007, -0.57684, -2.82528, 2.15226, -2.3774, 4.39353, -1.60646, 5.90283, 0.085, 7.01136, 0.9876, 6.88725, 2.04326, 5.90106, 2.85316, 4.26413, 3.52158, 1.69589, 3.59142, -1.14834, 2.22284, -4.8093, 0.93363, -5.68803, 3.08915, -3.13886, 0.75098, -3.3627, 1.61103, -2.60221, 2.1334, -1.60559, 2.50266, -0.35371, 2.5331, 1.18953, 2.05983, 3.25235, 1.49336, 4.43678, 0.90327, 4.96032, -1.0434, 4.27723, -1.63423, 3.00791, -1.92551, 1.55937, -1.90548, -0.04135, -1.64705, -1.84608, 0.16737, 4.95474, -0.53342, 4.69806, -1.04263, -2.87967, -0.25645, -3.52935, 1.68506, 1.00066, 1.37715, 2.12429, 0.98007, 2.75272, 0.72793, 2.95844, 0.3291, 3.05993, -0.23675, 2.83623, -0.55382, 2.57337, -0.82691, 1.97567, -0.95788, 1.09405, -0.93598, 0.01128, -0.80913, -0.74346, -0.46113, -1.39738, -0.09784, -1.58045, 0.55768, -1.49955, 1.01863, -0.99366, 1.45307, -0.40333, 1.63648, 0.33718, 0.27472, 0.8434], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2333, "offset": 34, "vertices": [-1.79, 2.9152, -1.86893, -2.15452, -4.31901, -1.76455, -6.19326, -1.20079, -6.85957, -0.17584, -6.64293, 0.94589, -5.53675, 1.88156, -3.7528, 2.52787, 0.04858, 3.0414, 2.04049, 3.02156, 4.32715, 2.65309, 6.03643, 2.00894, 7.38489, 0.97752, 7.39403, -0.18787, 4.17691, -1.73823, 1.2778, -2.14352, 6.18025, -1.02249, 0.99237, -1.19508, 2.93532, -0.85314, 4.14276, -0.42592, 5.02426, 0.10082, 5.1706, 0.73551, 4.22835, 1.56613, 3.03484, 2.0329, 1.75522, 2.22749, -2.58939, 1.87986, -3.96235, 1.3383, -4.68132, 0.7335, -4.71883, 0.07696, -4.23901, -0.65512, 0.12574, 2.19981, -1.43884, 2.07024, -2.95376, -1.05864, -1.24648, -1.29826, 3.28349, 0.6287, 2.6593, 1.0794, 1.81234, 1.32373, 1.26465, 1.39951, 0.38687, 1.42741, -0.87728, 1.31604, -1.59267, 1.19717, -2.22785, 0.94234, -2.56288, 0.57584, -2.5698, 0.13202, -2.32756, -0.17348, -1.59056, -0.42996, -0.79566, -0.49259, 0.65973, -0.43675, 1.70609, -0.21314, 2.69807, 0.04423, 3.14201, 0.3546, 0.15313, 0.51546], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6333, "offset": 34, "vertices": [-0.81815, 6.40167, -0.65973, -6.0576, -1.66489, -5.02475, -2.44378, -3.58214, -2.75163, -1.04256, -2.70616, 1.70815, -2.29408, 3.97458, -1.59713, 5.50912, -0.07959, 6.65612, 0.72644, 6.54701, 1.66473, 5.5719, 2.37994, 3.93673, 2.96383, 1.36055, 3.01127, -1.50432, 1.76885, -5.21765, 0.61202, -6.12594, 2.55189, -3.51905, 0.46102, -3.78597, 1.23368, -3.00441, 1.70578, -1.99089, 2.04237, -0.72284, 2.07771, 0.83281, 1.66559, 2.90312, 1.16556, 4.08665, 0.64093, 4.60377, -1.10246, 3.88098, -1.63719, 2.59145, -1.90515, 1.12662, -1.89567, -0.48606, -1.67419, -2.3001, -0.0168, 4.58512, -0.64446, 4.31407, -1.13945, -3.33094, -0.44023, -3.97171, 1.3188, 0.62749, 1.04953, 1.7543, 0.69795, 2.38054, 0.47368, 2.58339, 0.11777, 2.67861, -0.38912, 2.44317, -0.67388, 2.17269, -0.9211, 1.56555, -1.04278, 0.67484, -1.02892, -0.41588, -0.91952, -1.17418, -0.61193, -1.82697, -0.28822, -2.00502, 0.29807, -1.9119, 0.71269, -1.39396, 1.10407, -0.79141, 1.27189, -0.04196, 0.05751, 0.44406], "curve": [0.25, 0, 0.75, 1]}, {"time": 3.1667}]}}}}, "4-beauty": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/2-NgacNhien/2": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/2-NgacNhien/6": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/3-Chao/2": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/3-Chao/3": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/3-Chao/5": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/3-Chao/6": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/3-Chao/7": {"attachment": [{"time": 0, "name": null}, {"time": 2, "name": null}]}, "Emotion/5-Dang Yeu/2": {"attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/2"}, {"time": 2, "name": "Emotion/5-<PERSON><PERSON>/2"}]}, "Emotion/5-Dang Yeu/3": {"attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/3"}, {"time": 2, "name": "Emotion/5-<PERSON><PERSON>/3"}]}, "Emotion/5-Dang Yeu/4": {"attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/4"}]}, "Emotion/5-Dang Yeu/6": {"attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/6"}]}, "Emotion/5-Dang Yeu/7": {"attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/7"}, {"time": 2, "name": "Emotion/5-<PERSON><PERSON>/7"}]}, "Emotion/5-Dang Yeu/8": {"attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/8"}, {"time": 2, "name": "Emotion/5-<PERSON><PERSON>/8"}]}, "Emotion/5-Dang Yeu/5-1": {"color": [{"time": 1.4333, "color": "ffffffff"}, {"time": 1.4667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/5-1"}]}, "Emotion/5-Dang Yeu/5-2": {"color": [{"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.4667, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.5667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/5-<PERSON><PERSON>/5-2"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 1, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": -2.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": -2.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1.046, "y": 0.937, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.046, "y": 0.937, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": -130.5, "curve": "stepped"}, {"time": 0.4, "angle": -130.5}, {"time": 0.7333, "angle": -123.37}, {"time": 1, "angle": -130.64}, {"time": 1.4667, "angle": -120}], "translate": [{"time": 0, "x": -13.94, "y": -18.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": -10.14, "y": -20.25}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}, {"time": 0.5333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 10.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": 3.62}, {"time": 1, "angle": -14.5}, {"time": 2, "angle": 10.67}], "translate": [{"time": 0, "x": -27.83, "y": -26.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -21.62, "y": -11.34}, {"time": 2, "x": -27.83, "y": -26.25}], "scale": [{"time": 0, "x": 0.469, "y": 0.469, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "x": 1.409, "y": 1.409}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.4333, "x": 1.448, "y": 1.448}, {"time": 0.8, "x": 0.953, "y": 0.953}, {"time": 0.9, "x": 1.499, "y": 1.499}, {"time": 1.3, "x": 1.143, "y": 1.143}, {"time": 1.4, "x": 1.546, "y": 1.546}, {"time": 1.6667, "x": 1.204, "y": 1.204}, {"time": 1.7667, "x": 1.478, "y": 1.478}, {"time": 2, "x": 0.469, "y": 0.469}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": -39.37}, {"time": 0.4333, "angle": -62.56}, {"time": 1, "angle": -68.52}, {"time": 2, "angle": -39.37}], "translate": [{"time": 0, "x": -30.93, "y": 5.89}, {"time": 0.4333, "x": -22.66, "y": -4.23}, {"time": 1, "x": -14.31, "y": 1.42}, {"time": 2, "x": -30.93, "y": 5.89}], "scale": [{"time": 0, "x": 0.574, "y": 0.574}, {"time": 0.1, "x": 1.485, "y": 1.485}, {"time": 0.3333, "x": 0.972, "y": 0.972}, {"time": 0.4333, "x": 1.46, "y": 1.46}, {"time": 0.8, "x": 0.954, "y": 0.954}, {"time": 0.9, "x": 1.135, "y": 1.135}, {"time": 1.3, "x": 1.144, "y": 1.144}, {"time": 1.4, "x": 1.547, "y": 1.547}, {"time": 1.6667, "x": 1.205, "y": 1.205}, {"time": 1.7667, "x": 1.485, "y": 1.485}, {"time": 2, "x": 0.574, "y": 0.574}], "shear": [{"time": 0.4333, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": -351.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -9.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 8.86}], "translate": [{"time": 0, "x": 23.89, "y": -47.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 22.48, "y": -51.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 22.45, "y": -49.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 24.03, "y": -55.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 23.89, "y": -47.74}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 6.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -6.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 6.51}], "translate": [{"time": 0, "x": 5.84, "y": 0.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -4.54, "y": 3.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.7, "y": 0.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 5.84, "y": 0.22}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 1, "y": 0.833, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 0.805, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": -165, "curve": "stepped"}, {"time": 0.2667, "angle": -165, "curve": "stepped"}, {"time": 0.3333, "angle": -165, "curve": "stepped"}, {"time": 0.5667, "angle": -165, "curve": "stepped"}, {"time": 0.7, "angle": -165}], "translate": [{"time": 0, "x": 28.93, "y": -3.42, "curve": "stepped"}, {"time": 0.2667, "x": 28.93, "y": -3.42}, {"time": 0.3333, "x": 27.08, "y": -6}, {"time": 0.5333, "x": 16.11, "y": -18.23}, {"time": 0.5667, "x": 28.93, "y": -3.42, "curve": "stepped"}, {"time": 0.7, "x": 28.93, "y": -3.42}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0}, {"time": 0.3333, "x": 1, "y": 1}, {"time": 0.5333, "x": 2.135, "y": 1.237}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0}], "shear": [{"time": 0.2667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.5667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 5.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -10.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 5.46}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 9.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -4.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 9.63}], "translate": [{"time": 0, "x": 11.22, "y": -0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": -6.76, "y": 1.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 11.22, "y": -0.3}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "offset": 34, "vertices": [-6.27469, 2.2358, -3.1694, 2.29628, -3.40679, 1.66761, -3.75097, 1.18243, -4.37835, 0.99687, -5.06555, 1.03409, -5.63933, 1.30027, -6.03627, 1.74327, -6.35308, 2.70093, -6.34214, 3.20732, -6.11782, 3.79415, -5.72429, 4.23867, -5.09336, 4.59767, -4.37956, 4.61851, -3.42801, 3.82576, -3.17802, 3.09561, -3.8676, 4.32337, -3.75878, 3.00802, -3.96938, 3.49622, -4.23179, 3.79622, -4.55495, 4.01182, -4.94379, 4.03892, -5.45199, 3.78635, -5.73717, 3.47566, -5.85559, 3.14744, -5.64004, 2.04913, -5.30753, 1.70892, -4.93663, 1.53585, -4.53447, 1.53676, -4.08638, 1.67029, -5.83765, 2.73389, -5.75736, 2.33843, -3.83998, 2.00325, -3.69424, 2.44082, -4.87723, 3.56116, -5.15293, 3.39541, -5.30207, 3.17634, -5.34814, 3.03598, -5.36471, 2.81252, -5.29573, 2.49311, -5.22251, 2.31323, -5.06602, 2.15589, -4.84135, 2.07659, -4.56949, 2.08189, -4.38252, 2.14829, -4.22587, 2.33961, -4.18798, 2.54257, -4.22306, 2.91146, -4.36066, 3.17375, -4.51889, 3.4217, -4.70926, 3.52956, -4.80599, 2.76762], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 34, "vertices": [0.49011, -4.4144, -3.13485, -0.60676, -4.4897, -0.57318, -5.33908, -0.74634, -5.06002, -1.42544, -4.12547, -2.2944, -2.73035, -3.14227, -1.08836, -3.86343, 1.80195, -4.75329, 3.11289, -5.00321, 4.37419, -5.03113, 5.05718, -4.77585, 5.22729, -4.18254, 4.41214, -3.31089, 1.17985, -1.72208, -1.03407, -1.03322, 3.01672, -2.52445, -0.55559, -1.70565, 0.97771, -2.22, 2.08187, -2.70046, 3.03939, -3.21215, 3.58393, -3.70697, 3.54255, -4.20386, 3.07759, -4.39485, 2.36358, -4.37053, -0.77118, -3.53269, -2.06599, -2.9446, -2.97036, -2.39605, -3.45798, -1.89932, -3.65471, -1.41478, 1.26024, -4.13322, 0.12827, -3.82823, -3.0842, -1.28337, -2.11744, -1.33082, 2.25348, -3.37616, 2.15591, -3.63078, 1.76473, -3.70121, 1.45383, -3.68516, 0.88965, -3.5894, -0.02967, -3.33796, -0.58927, -3.15385, -1.19133, -2.87856, -1.67244, -2.55953, -1.98978, -2.22619, -2.04394, -2.02957, -1.73445, -1.93542, -1.24987, -1.99416, -0.24248, -2.22941, 0.61106, -2.53597, 1.45225, -2.86059, 1.96623, -3.15206, 0.09156, -2.87528], "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 34, "vertices": [6.18254, 3.83352, -0.38022, 4.10401, 0.2015, 5.40069, 0.99025, 6.38068, 2.33841, 6.69197, 3.78426, 6.52527, 4.96133, 5.88978, 5.74241, 4.90378, 6.2884, 2.84162, 6.20037, 1.77404, 5.6516, 0.56404, 4.7639, -0.32382, 3.386, -1.00071, 1.87654, -0.95316, -0.0304, 0.8423, -0.46449, 2.41561, 0.83373, -0.26453, 0.77267, 2.52603, 1.15465, 1.46844, 1.6701, 0.80155, 2.32464, 0.305, 3.14192, 0.19794, 4.24712, 0.66598, 4.88892, 1.28518, 5.18098, 1.9628, 4.86679, 4.30892, 4.2085, 5.06972, 3.44782, 5.48261, 2.59876, 5.53228, 1.63571, 5.30785, 5.19615, 2.83809, 5.07734, 3.68318, 1.0729, 4.63661, 0.70916, 3.73159, 3.0627, 1.21499, 3.66592, 1.52952, 4.00882, 1.97283, 4.12409, 2.26321, 4.18772, 2.73279, 4.08307, 3.4159, 3.95155, 3.80499, 3.64142, 4.15716, 3.17729, 4.35337, 2.60276, 4.37706, 2.19955, 4.26087, 1.84435, 3.87709, 1.73836, 3.45352, 1.7651, 2.67033, 2.02192, 2.09901, 2.32416, 1.55532, 2.71217, 1.30322, 3.01408, 2.89922], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "offset": 34, "vertices": [0.13475, -3.63987, -0.01059, -3.58338, -1.47097, -3.67476, -2.58303, -3.74777, -2.9624, -3.78336, -2.81089, -3.78881, -2.13003, -3.76056, -1.04873, -3.70491, 1.23875, -3.57621, 2.43179, -3.50541, 3.7947, -3.42007, 4.80632, -3.35198, 5.5943, -3.29213, 5.57723, -3.27817, 3.61976, -3.37403, 1.87494, -3.47202, 4.83386, -3.31142, 1.72228, -3.49321, 2.893, -3.42838, 3.62467, -3.39059, 4.16302, -3.36552, 4.26294, -3.36776, 3.7145, -3.41086, 3.00844, -3.4586, 2.24552, -3.50623, -0.36423, -3.6561, -1.19728, -3.69842, -1.63973, -3.71682, -1.67494, -3.71047, -1.40158, -3.6849, 1.26871, -3.56364, 0.3288, -3.61756, -0.63938, -3.63464, 0.37891, -3.57134, 3.13025, -3.43337, 2.76499, -3.46076, 2.26227, -3.49363, 1.93559, -3.51392, 1.41023, -3.54536, 0.65067, -3.58884, 0.21975, -3.61279, -0.16574, -3.63233, -0.37356, -3.63991, -0.3863, -3.63496, -0.24707, -3.6228, 0.18954, -3.59369, 0.66458, -3.56479, 1.53765, -3.51388, 2.16888, -3.47942, 2.7682, -3.44728, 3.04017, -3.43518, 1.25254, -3.54296], "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "offset": 34, "vertices": [-6.27469, 2.2358, -3.1694, 2.29628, -3.40679, 1.66761, -3.75097, 1.18243, -4.37835, 0.99687, -5.06555, 1.03409, -5.63933, 1.30027, -6.03627, 1.74327, -6.35308, 2.70093, -6.34214, 3.20732, -6.11782, 3.79415, -5.72429, 4.23867, -5.09336, 4.59767, -4.37956, 4.61851, -3.42801, 3.82576, -3.17802, 3.09561, -3.8676, 4.32337, -3.75878, 3.00802, -3.96938, 3.49622, -4.23179, 3.79622, -4.55495, 4.01182, -4.94379, 4.03892, -5.45199, 3.78635, -5.73717, 3.47566, -5.85559, 3.14744, -5.64004, 2.04913, -5.30753, 1.70892, -4.93663, 1.53585, -4.53447, 1.53676, -4.08638, 1.67029, -5.83765, 2.73389, -5.75736, 2.33843, -3.83998, 2.00325, -3.69424, 2.44082, -4.87723, 3.56116, -5.15293, 3.39541, -5.30207, 3.17634, -5.34814, 3.03598, -5.36471, 2.81252, -5.29573, 2.49311, -5.22251, 2.31323, -5.06602, 2.15589, -4.84135, 2.07659, -4.56949, 2.08189, -4.38252, 2.14829, -4.22587, 2.33961, -4.18798, 2.54257, -4.22306, 2.91146, -4.36066, 3.17375, -4.51889, 3.4217, -4.70926, 3.52956, -4.80599, 2.76762]}]}}}}, "5-byebye": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 2.8, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 2.8, "name": null}]}, "Emotion/3-Chao/2": {"attachment": [{"time": 0, "name": "Emotion/3-<PERSON>/2"}, {"time": 2.8, "name": "Emotion/3-<PERSON>/2"}]}, "Emotion/3-Chao/3": {"attachment": [{"time": 0, "name": "Emotion/3-<PERSON>/3"}, {"time": 2.8, "name": "Emotion/3-<PERSON>/3"}]}, "Emotion/3-Chao/4": {"attachment": [{"time": 0, "name": "Emotion/3-<PERSON>/4"}]}, "Emotion/3-Chao/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/3-Chao/7": {"attachment": [{"time": 0, "name": "Emotion/3-<PERSON>/7"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0, "y": -8.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0, "y": 8.06, "curve": [0.109, 0, 0.453, 0.94]}, {"time": 0.2667, "x": 0, "y": 46.87, "curve": [0.512, 0, 0.918, 0.98]}, {"time": 0.3667, "x": 0, "y": 9.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "x": 0, "y": -12.45, "curve": [0.247, 0, 0.631, 0.53]}, {"time": 0.5667, "x": 0, "y": 9.89, "curve": [0.379, 0.53, 0.746, 1]}, {"time": 0.7333, "x": 0, "y": 3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 0, "y": 1.01}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 1.295, "y": 0.831, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.613, "y": 1.153, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 1.053, "y": 0.736, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.841, "y": 1.116, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.303, "y": 0.744, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "x": 0.901, "y": 1.137, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "x": 1.087, "y": 1.015, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4, "angle": 0}, {"time": 0.7667, "angle": 4.21}, {"time": 1.0333, "angle": -3.84}, {"time": 1.2667, "angle": 2.27}, {"time": 1.5667, "angle": -4.51}, {"time": 1.8333, "angle": 5.55}, {"time": 2.2, "angle": -3.18}, {"time": 2.8, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.265, 0, 0.618, 0.43]}, {"time": 0.1333, "x": -0.09, "y": 6.28, "curve": [0.324, 0.3, 0.667, 0.67]}, {"time": 0.2333, "x": -0.47, "y": -12.21, "curve": [0.381, 0.54, 0.744, 1]}, {"time": 0.4, "x": -0.05, "y": 3.44}, {"time": 0.5333, "x": 0.09, "y": -3.6}, {"time": 0.7667, "x": -4.1, "y": -2.79}, {"time": 1.0333, "x": 2.59, "y": -4.6}, {"time": 1.2667, "x": -3.48, "y": -6.53}, {"time": 1.5667, "x": 1.83, "y": -8.33}, {"time": 1.8333, "x": -3.08, "y": -7.77}, {"time": 2.2, "x": 1.61, "y": -5.49}, {"time": 2.8, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 0.252, "curve": [0.271, 0, 0.619, 0.41]}, {"time": 0.1333, "x": 1, "y": 0.333, "curve": [0.319, 0.29, 0.66, 0.64]}, {"time": 0.2333, "x": 0.853, "y": 1.353, "curve": [0.375, 0.5, 0.75, 1]}, {"time": 0.4, "x": 1, "y": 1}, {"time": 0.5333, "x": 1.129, "y": 0.651}, {"time": 0.6667, "x": 1.129, "y": 1.349}, {"time": 0.8667, "x": 1, "y": 1}, {"time": 2.2667, "x": 1, "y": 1.136}, {"time": 2.3333, "x": 1, "y": 0, "curve": "stepped"}, {"time": 2.4, "x": 1, "y": 0}, {"time": 2.5, "x": 1, "y": 1.136}, {"time": 2.7, "x": 1, "y": 1.127}, {"time": 2.8, "x": 1, "y": 0.252}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.8, "x": 0, "y": 0}]}, "bone6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -107.84, "curve": [0.246, 0, 0.633, 0.54]}, {"time": 0.3, "angle": -4.9, "curve": [0.38, 0.53, 0.745, 1]}, {"time": 0.4, "angle": 5.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -49.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 30.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -52.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 32.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": -46.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1333, "angle": 30.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": -52.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "angle": -3.69}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.5, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.292, "y": 0.292, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.5, "x": 1, "y": 1}, {"time": 2.6667, "x": 1.468, "y": 1.468}, {"time": 2.8, "x": 0, "y": 0}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0.6667, "angle": 1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -2.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "angle": 2.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "angle": -2.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "angle": 0.21}], "translate": [{"time": 0.1333, "x": 0.02, "y": -1.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 2.28, "y": -0.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 0.07, "y": 4.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1.1, "y": 1.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": -0.47, "y": 4.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 2.22, "y": 0.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "x": -1.54, "y": 2.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "x": 1.5, "y": 1.11}], "scale": [{"time": 0, "x": 0.615, "y": 0.284, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "x": 0.401, "y": 0.108, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "x": 0.294, "y": 0.162, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": 0.779, "y": 0.604, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 0.864, "y": 0.962, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.935, "y": 1.136, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 0.92, "y": 0.693, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "x": 1.014, "y": 1.139, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "x": 1, "y": 0.734, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.9, "x": 1, "y": 1.109, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.2, "x": 1, "y": 0.842, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5333, "x": 1, "y": 1.005, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "x": 0.615, "y": 0.284}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -1.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 2.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -2.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 2.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 6.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -0.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 3.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 0.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 6.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.1667, "angle": 0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.5, "angle": 4.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8, "angle": -3.07}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "offset": 34, "vertices": [-0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519, -0.08881, 6.35519], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "offset": 34, "vertices": [1.23222, -1.10165, 1.19412, -1.10147, 2.81837, -1.10914, 4.0656, -1.11502, 4.52366, -1.11719, 4.40062, -1.1166, 3.68707, -1.11323, 2.52097, -1.10773, 0.02081, -1.09593, -1.29441, -1.08971, -2.81039, -1.08256, -3.95016, -1.07718, -4.85863, -1.07289, -4.88541, -1.07276, -2.78935, -1.08265, -0.88286, -1.09166, -4.09904, -1.07647, -0.67757, -1.09263, -1.95403, -1.0866, -2.74346, -1.08287, -3.31597, -1.08017, -3.40128, -1.07977, -2.76452, -1.08277, -1.96837, -1.08654, -1.12023, -1.09054, 1.74148, -1.10405, 2.63814, -1.10828, 3.10197, -1.11047, 3.11505, -1.11054, 2.78529, -1.10898, -0.0451, -1.09561, 0.98538, -1.10048, 1.92972, -1.10494, 0.79847, -1.0996, -2.1575, -1.08564, -1.73744, -1.08762, -1.17402, -1.09028, -0.81113, -1.092, -0.23122, -1.09473, 0.60128, -1.09867, 1.07139, -1.10088, 1.48614, -1.10284, 1.70078, -1.10386, 1.69745, -1.10384, 1.5321, -1.10306, 1.04105, -1.10074, 0.51522, -1.09826, -0.4445, -1.09373, -1.13122, -1.09049, -1.78145, -1.08742, -2.06897, -1.08606, -0.09314, -1.09539], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "offset": 34, "vertices": [-1.35492, 6.07473, -1.17955, 6.07212, -2.69538, 6.10484, -3.86668, 6.13006, -4.31947, 6.13961, -4.23643, 6.13755, -3.60109, 6.12358, -2.53842, 6.10044, -0.23589, 6.05049, 0.98337, 6.0241, 2.39831, 5.99357, 3.47224, 5.97047, 4.34254, 5.95189, 4.39956, 5.95093, 2.50014, 5.99241, 0.74473, 6.03049, 3.69394, 5.9664, 0.5283, 6.03495, 1.70165, 6.00948, 2.42136, 5.9938, 2.93731, 5.98251, 2.99881, 5.98103, 2.38583, 5.9941, 1.63521, 6.01022, 0.84392, 6.0273, -1.79821, 6.08457, -2.61411, 6.10236, -3.0272, 6.11144, -3.02118, 6.11146, -2.69539, 6.10459, -0.15156, 6.04885, -1.10285, 6.06947, -1.89146, 6.08728, -0.83658, 6.06451, 1.84925, 6.00593, 1.44756, 6.01452, 0.91873, 6.02591, 0.58037, 6.03321, 0.04224, 6.04485, -0.72609, 6.06149, -1.15843, 6.07089, -1.5357, 6.07911, -1.72447, 6.08328, -1.70911, 6.08305, -1.54747, 6.07963, -1.08536, 6.06969, -0.59638, 6.05912, 0.29138, 6.0399, 0.92153, 6.02621, 1.51694, 6.01326, 1.77479, 6.0076, -0.06051, 6.04728], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 34, "vertices": [-1.53867, -4.17944, -1.4981, -4.18728, -3.62445, -4.18482, -5.25679, -4.18253, -5.8548, -4.18043, -5.69159, -4.17885, -4.75541, -4.17822, -3.22713, -4.17856, 0.048, -4.18063, 1.77035, -4.18217, 3.755, -4.18446, 5.24643, -4.18676, 6.43425, -4.18938, 6.46718, -4.1912, 3.71938, -4.19119, 1.22191, -4.18963, 5.43584, -4.19158, 0.95481, -4.18794, 2.62708, -4.18887, 3.66169, -4.18912, 4.41241, -4.18897, 4.52528, -4.18809, 3.69292, -4.18607, 2.65116, -4.18446, 1.54079, -4.1832, -2.2075, -4.18045, -3.38275, -4.18025, -3.99129, -4.18065, -4.00962, -4.18165, -3.57911, -4.18316, 0.13277, -4.18201, -1.21697, -4.18103, -2.45942, -4.18476, -0.97838, -4.18642, 2.89625, -4.18682, 2.34699, -4.18565, 1.60957, -4.18463, 1.13448, -4.1841, 0.37508, -4.1834, -0.71534, -4.18263, -1.33122, -4.18226, -1.87483, -4.18219, -2.1566, -4.1825, -2.15304, -4.18319, -1.93708, -4.18385, -1.29447, -4.1848, -0.60596, -4.1855, 0.65097, -4.18651, 1.5507, -4.18695, 2.40272, -4.1873, 2.77982, -4.18715, 0.19259, -4.18465], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "offset": 34, "vertices": [0.18983, 6.11313, -0.16835, 1.55947, -0.8544, 1.8384, -1.36166, 2.29012, -1.48749, 3.19092, -1.35035, 4.20425, -0.96709, 5.07639, -0.40605, 5.7084, 0.73235, 6.27981, 1.30976, 6.31985, 1.94976, 6.05545, 2.40376, 5.52678, 2.72734, 4.63997, 2.65293, 3.594, 1.61572, 2.10875, 0.74662, 1.66073, 2.24509, 2.80949, 0.72641, 2.5039, 1.31353, 2.86728, 1.69258, 3.28588, 1.98353, 3.78436, 2.06801, 4.35841, 1.84917, 5.07676, 1.53328, 5.46113, 1.17437, 5.59867, -0.11089, 5.16042, -0.5456, 4.63438, -0.79448, 4.07055, -0.8488, 3.48003, -0.7578, 2.83674, 0.69912, 5.52651, 0.23598, 5.36476, -0.41107, 2.5118, 0.06912, 2.34625, 1.51268, 4.20773, 1.36113, 4.59425, 1.13121, 4.78898, 0.97709, 4.8411, 0.72391, 4.84067, 0.34926, 4.70396, 0.13355, 4.57649, -0.06784, 4.32926, -0.18942, 3.99051, -0.22077, 3.59187, -0.1706, 3.32464, 0.02657, 3.11578, 0.25337, 3.08263, 0.67991, 3.17502, 0.99871, 3.40616, 1.30394, 3.66602, 1.45344, 3.95755, 0.59569, 4.01517], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "offset": 34, "vertices": [-1.14388, -0.0395, -1.13443, -4.17317, -2.65399, -3.85004, -3.81949, -3.38636, -4.24331, -2.54922, -4.12223, -1.63501, -3.44892, -0.8744, -2.35319, -0.35119, -0.00839, 0.05951, 1.2236, 0.03913, 2.64188, -0.26615, 3.70626, -0.79502, 4.55198, -1.63889, 4.57103, -2.58916, 2.59944, -3.84651, 0.81134, -4.17084, 3.83004, -3.26712, 0.62393, -3.39689, 1.82149, -3.1222, 2.56325, -2.7764, 3.1023, -2.34875, 3.18549, -1.83154, 2.59327, -1.15224, 1.84985, -0.76913, 1.05632, -0.60776, -1.62632, -0.88204, -2.46911, -1.32071, -2.90676, -1.81234, -2.9224, -2.34761, -2.61728, -2.94555, 0.049, -0.62688, -0.91701, -0.72923, -1.81787, -3.27731, -0.75935, -3.4763, 2.01977, -1.91464, 1.6286, -1.54579, 1.10204, -1.34479, 0.76248, -1.28185, 0.21936, -1.25724, -0.5611, -1.34539, -1.00212, -1.44079, -1.39197, -1.64725, -1.59494, -1.94538, -1.5941, -2.30725, -1.4408, -2.55687, -0.98211, -2.76756, -0.48984, -2.82032, 0.40952, -2.77786, 1.05399, -2.59774, 1.66446, -2.38997, 1.93542, -2.13784, 0.08529, -2.00036], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8}]}}}}, "6-after_boom": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/13-Boc Khoi/1": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/1"}, {"time": 1.2667, "name": "Emotion/13-<PERSON><PERSON>/1"}]}, "Emotion/13-Boc Khoi/2": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/2"}, {"time": 1.2667, "name": "Emotion/13-<PERSON><PERSON>/2"}]}, "Emotion/13-Boc Khoi/3": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/3"}, {"time": 1.2667, "name": "Emotion/13-<PERSON><PERSON>/3"}]}, "Emotion/13-Boc Khoi/6": {"attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/6"}, {"time": 0.7333, "name": "Emotion/13-<PERSON><PERSON>/6"}]}, "Emotion/13-Boc Khoi/7": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7667, "color": "ffffff00"}, {"time": 0.8333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4333, "color": "ffffffff"}, {"time": 2.1667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/8": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.9333, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4667, "color": "ffffffff"}, {"time": 2.0667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/9": {"color": [{"time": 1.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.2, "color": "ffffffff"}, {"time": 3.3333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/10": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 2.4, "color": "ffffffff"}, {"time": 3.1, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/11": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}, {"time": 0.6, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/12": {"color": [{"time": 0.4, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 0.7333, "color": "ffffff00"}, {"time": 0.8, "color": "ffffffff", "curve": "stepped"}, {"time": 1.8333, "color": "ffffffff"}, {"time": 2.2333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}, {"time": 0.1333, "name": "Emotion/13-<PERSON><PERSON>/7"}, {"time": 0.7333, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/13": {"color": [{"time": 0.4667, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffffd1"}, {"time": 1.1, "color": "ffffff00", "curve": "stepped"}, {"time": 1.1667, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 2.0667, "color": "ffffffff"}, {"time": 2.7333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/14": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.3333, "color": "ffffffff"}, {"time": 0.5, "color": "ffffffe3"}, {"time": 0.7333, "color": "ffffff00", "curve": "stepped"}, {"time": 0.8, "color": "ffffff00"}, {"time": 1.1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.5667, "color": "ffffffff"}, {"time": 2.4333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/7"}]}, "Emotion/13-Boc Khoi/4-1": {"attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/4-1"}]}, "Emotion/13-Boc Khoi/4-2": {"attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/4-2"}]}, "Emotion/13-Boc Khoi/5-1": {"attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/5-1"}]}, "Emotion/13-Boc Khoi/5-2": {"attachment": [{"time": 0, "name": "Emotion/13-<PERSON><PERSON>/5-2"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": -5.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": 22.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -15.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 22.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -15.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 22.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -15.53, "curve": [0.26, 0, 0.618, 0.44]}, {"time": 0.7, "angle": -0.31, "curve": [0.359, 0.43, 0.756, 1]}, {"time": 0.8667, "angle": 0}, {"time": 1.2667, "angle": -5.54}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.8667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.2667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 18.56}, {"time": 0.2, "angle": -0.89}, {"time": 0.7333, "angle": 0, "curve": "stepped"}, {"time": 2.3, "angle": 0}], "translate": [{"time": 0, "x": 9.22, "y": -6.46}, {"time": 0.1, "x": 19.77, "y": -3.43}, {"time": 0.2, "x": 7.72, "y": -4.57}, {"time": 0.3, "x": 19.77, "y": -3.43}, {"time": 0.4, "x": 7.72, "y": -4.57}, {"time": 0.5, "x": 19.77, "y": -3.43}, {"time": 0.6, "x": 7.72, "y": -4.57}, {"time": 0.7333, "x": 9.22, "y": -6.46}, {"time": 0.8, "x": 9.12, "y": 1.58, "curve": "stepped"}, {"time": 2.2, "x": 9.12, "y": 1.58}, {"time": 2.3, "x": 9.22, "y": -6.46}], "scale": [{"time": 0, "x": 1.016, "y": 1.016}, {"time": 0.6333, "x": 0.333, "y": 0.333}, {"time": 0.7333, "x": 0.073, "y": 0.073}, {"time": 0.8, "x": 0.565, "y": 3.787}, {"time": 0.9667, "x": 0.565, "y": 3.402, "curve": "stepped"}, {"time": 2.2, "x": 0.565, "y": 3.402}, {"time": 2.3, "x": 1.016, "y": 1.016}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3, "x": 0, "y": 0}]}, "bone22": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 1.3667, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 1.7667, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0, "curve": "stepped"}, {"time": 3.1667, "angle": 0, "curve": "stepped"}, {"time": 3.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.7667, "x": -1.22, "y": -12.32}, {"time": 0.8667, "x": 0.61, "y": -13.91, "curve": "stepped"}, {"time": 0.9333, "x": 0.61, "y": -13.91, "curve": "stepped"}, {"time": 1.3, "x": 0.61, "y": -13.91}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 1.5667, "x": 0.61, "y": -13.91, "curve": "stepped"}, {"time": 1.6333, "x": 0.61, "y": -13.91, "curve": "stepped"}, {"time": 1.7, "x": 0.61, "y": -13.91}, {"time": 1.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.9667, "x": 0.61, "y": -13.91, "curve": "stepped"}, {"time": 3.1, "x": 0.61, "y": -13.91}, {"time": 3.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1}, {"time": 0.7667, "x": 0.423, "y": 0.09}, {"time": 0.8667, "x": 0.345, "y": 0.031, "curve": "stepped"}, {"time": 0.9333, "x": 0.345, "y": 0.031, "curve": "stepped"}, {"time": 1.3, "x": 0.345, "y": 0.031}, {"time": 1.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1}, {"time": 1.5667, "x": 0.345, "y": 0.031, "curve": "stepped"}, {"time": 1.6333, "x": 0.345, "y": 0.031, "curve": "stepped"}, {"time": 1.7, "x": 0.345, "y": 0.031}, {"time": 1.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 1}, {"time": 1.9667, "x": 0.345, "y": 0.031, "curve": "stepped"}, {"time": 3.1, "x": 0.345, "y": 0.031}, {"time": 3.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2333, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": -349.37}, {"time": 0.2, "angle": -6.33}], "translate": [{"time": 0, "x": -8.35, "y": -24.21}, {"time": 0.1, "x": -9.9, "y": -30.08}, {"time": 0.2, "x": -10.19, "y": -22.33}], "scale": [{"time": 0, "x": 1.015, "y": 1.015}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone23": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6, "angle": 0, "curve": "stepped"}, {"time": 1.3667, "angle": 0, "curve": "stepped"}, {"time": 1.4333, "angle": 0, "curve": "stepped"}, {"time": 1.7667, "angle": 0, "curve": "stepped"}, {"time": 1.8333, "angle": 0, "curve": "stepped"}, {"time": 3.1667, "angle": 0, "curve": "stepped"}, {"time": 3.2333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.7667, "x": 16.67, "y": 3.9, "curve": "stepped"}, {"time": 0.8667, "x": 16.67, "y": 3.9, "curve": "stepped"}, {"time": 0.9333, "x": 16.67, "y": 3.9, "curve": "stepped"}, {"time": 1.3, "x": 16.67, "y": 3.9}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0}, {"time": 1.5667, "x": 16.67, "y": 3.9, "curve": "stepped"}, {"time": 1.6333, "x": 16.67, "y": 3.9, "curve": "stepped"}, {"time": 1.7, "x": 16.67, "y": 3.9}, {"time": 1.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0}, {"time": 1.9667, "x": 16.67, "y": 3.9, "curve": "stepped"}, {"time": 3.1, "x": 16.67, "y": 3.9}, {"time": 3.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6, "x": 1, "y": 1}, {"time": 0.7667, "x": 0.826, "y": 0.075, "curve": "stepped"}, {"time": 0.8667, "x": 0.826, "y": 0.075, "curve": "stepped"}, {"time": 0.9333, "x": 0.826, "y": 0.075, "curve": "stepped"}, {"time": 1.3, "x": 0.826, "y": 0.075}, {"time": 1.3667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.4333, "x": 1, "y": 1}, {"time": 1.5667, "x": 0.826, "y": 0.075, "curve": "stepped"}, {"time": 1.6333, "x": 0.826, "y": 0.075, "curve": "stepped"}, {"time": 1.7, "x": 0.826, "y": 0.075}, {"time": 1.7667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.8333, "x": 1, "y": 1}, {"time": 1.9667, "x": 0.826, "y": 0.075, "curve": "stepped"}, {"time": 3.1, "x": 0.826, "y": 0.075}, {"time": 3.1667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 3.2333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.8333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.2333, "x": 0, "y": 0}]}, "bone5": {"rotate": [{"time": 0, "angle": 0}, {"time": 0.1, "angle": 10.86}, {"time": 0.2, "angle": -6.93}], "translate": [{"time": 0, "x": 21.04, "y": -34.4}, {"time": 0.1, "x": 16.74, "y": -26.99}, {"time": 0.2, "x": 20.68, "y": -32.5}], "scale": [{"time": 0, "x": 1.016, "y": 1.016}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone14": {"rotate": [{"time": 0, "angle": -59.27}, {"time": 0.8333, "angle": -47}, {"time": 2.1667, "angle": 16.22}], "translate": [{"time": 0, "x": -28.03, "y": 13.12}, {"time": 0.7667, "x": -44.12, "y": 17.89, "curve": "stepped"}, {"time": 0.8333, "x": -44.12, "y": 17.89}, {"time": 2.1667, "x": 28.4, "y": 186.07}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7667, "x": 0, "y": 0}, {"time": 0.8333, "x": 1, "y": 1}, {"time": 2.1667, "x": 2.127, "y": 2.127}]}, "bone16": {"rotate": [{"time": 0, "angle": -59.27}, {"time": 1.7667, "angle": -3.92}, {"time": 3.3333, "angle": 46.45}], "translate": [{"time": 0, "x": -28.03, "y": 13.12}, {"time": 1.2333, "x": -44.12, "y": 17.89, "curve": "stepped"}, {"time": 1.7667, "x": -44.12, "y": 17.89}, {"time": 3.3333, "x": -16.54, "y": 127.01}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.2333, "x": 0, "y": 0}, {"time": 1.7667, "x": 1, "y": 1}, {"time": 3.3333, "x": 2.127, "y": 2.127}]}, "bone17": {"rotate": [{"time": 0, "angle": -59.27}, {"time": 2.2333, "angle": -3.92}, {"time": 3.1, "angle": -42.93}], "translate": [{"time": 0, "x": -28.03, "y": 13.12}, {"time": 1.7667, "x": -44.12, "y": 17.89, "curve": "stepped"}, {"time": 2.2333, "x": -44.12, "y": 17.89}, {"time": 3.1, "x": -13.62, "y": 104.61}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7667, "x": 0, "y": 0}, {"time": 2.2333, "x": 0.634, "y": 0.634}, {"time": 3.1, "x": 1.22, "y": 1.22}]}, "bone15": {"rotate": [{"time": 0, "angle": -59.27}, {"time": 1.1, "angle": -3.92}, {"time": 2.0667, "angle": 46.45}], "translate": [{"time": 0, "x": -28.03, "y": 13.12}, {"time": 0.9333, "x": -44.12, "y": 17.89, "curve": "stepped"}, {"time": 1.1, "x": -44.12, "y": 17.89}, {"time": 2.0667, "x": -20.17, "y": 173.77}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0}, {"time": 1.1, "x": 1, "y": 1}, {"time": 2.0667, "x": 2.127, "y": 2.127}]}, "X-Tong": {"rotate": [{"time": 0, "angle": -0.64, "curve": "stepped"}, {"time": 1.3333, "angle": -0.64, "curve": "stepped"}, {"time": 2.3333, "angle": -0.64, "curve": "stepped"}, {"time": 3.3333, "angle": -0.64}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1.108, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.8333, "x": 1.12, "y": 0.924, "curve": [0.25, 0, 0.75, 1]}, {"time": 3.3333, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 3.3333, "x": 0, "y": 0}]}, "bone18": {"rotate": [{"time": 0, "angle": -4.85}, {"time": 0.0667, "angle": 17.95}, {"time": 0.5333, "angle": -104.03}, {"time": 0.6, "angle": -4.85}, {"time": 0.6667, "angle": 17.95}, {"time": 1.5667, "angle": 16.29}], "translate": [{"time": 0, "x": -102.86, "y": 18.49}, {"time": 0.0667, "x": -103.62, "y": 14.79}, {"time": 0.5333, "x": -124.66, "y": 137.93}, {"time": 0.6, "x": -102.86, "y": 18.49}, {"time": 0.6667, "x": -103.62, "y": 14.79}, {"time": 1.5667, "x": -103.83, "y": 170.23}], "scale": [{"time": 0, "x": 0, "y": 0}, {"time": 0.0667, "x": 1, "y": 0.416, "curve": "stepped"}, {"time": 0.5333, "x": 1, "y": 0.416}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.6667, "x": 1, "y": 0.416}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.0667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0}]}, "bone19": {"rotate": [{"time": 0, "angle": -4.85, "curve": "stepped"}, {"time": 0.1333, "angle": -4.85}, {"time": 0.2, "angle": 29.37}, {"time": 0.6667, "angle": 35.2}, {"time": 0.7333, "angle": -4.85}, {"time": 0.8, "angle": 17.95}, {"time": 2.3, "angle": 125.15}], "translate": [{"time": 0, "x": 8.99, "y": 9.67, "curve": "stepped"}, {"time": 0.1333, "x": 8.99, "y": 9.67}, {"time": 0.2, "x": 17.05, "y": 22.5}, {"time": 0.6667, "x": 13.09, "y": 137.93}, {"time": 0.7333, "x": 8.99, "y": 9.67}, {"time": 0.8, "x": 13.75, "y": 14.79}, {"time": 2.3, "x": 13.53, "y": 146.54}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0}, {"time": 0.2, "x": 1, "y": 0.709}, {"time": 0.6667, "x": 1, "y": 0.598}, {"time": 0.7333, "x": 0, "y": 0}, {"time": 0.8, "x": 1, "y": 0.404, "curve": "stepped"}, {"time": 2.3, "x": 1, "y": 0.404}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0}]}, "bone20": {"rotate": [{"time": 0, "angle": -4.85, "curve": "stepped"}, {"time": 0.3333, "angle": -4.85}, {"time": 0.4667, "angle": 29.37}, {"time": 1.1, "angle": 35.2}, {"time": 1.1667, "angle": -4.85}, {"time": 1.3667, "angle": 17.95}, {"time": 2.7333, "angle": 125.15}], "translate": [{"time": 0, "x": 8.99, "y": 9.67, "curve": "stepped"}, {"time": 0.3333, "x": 8.99, "y": 9.67}, {"time": 0.4667, "x": 17.05, "y": 22.5}, {"time": 1.1, "x": 13.09, "y": 137.93}, {"time": 1.1667, "x": 8.99, "y": 9.67}, {"time": 1.3667, "x": 13.75, "y": 14.79}, {"time": 2.7333, "x": 13.53, "y": 146.54}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0}, {"time": 0.4667, "x": 1, "y": 0.404, "curve": "stepped"}, {"time": 1.1, "x": 1, "y": 0.404}, {"time": 1.1667, "x": 0, "y": 0}, {"time": 1.3667, "x": 1, "y": 0.404, "curve": "stepped"}, {"time": 2.7333, "x": 1, "y": 0.404}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3667, "x": 0, "y": 0}]}, "bone21": {"rotate": [{"time": 0, "angle": -4.85, "curve": "stepped"}, {"time": 0.2, "angle": -4.85}, {"time": 0.3333, "angle": 17.95}, {"time": 0.7333, "angle": -104.03}, {"time": 0.8, "angle": -4.85}, {"time": 1.1, "angle": 17.95}, {"time": 2.4333, "angle": 16.29}], "translate": [{"time": 0, "x": -102.86, "y": 18.49, "curve": "stepped"}, {"time": 0.2, "x": -102.86, "y": 18.49}, {"time": 0.3333, "x": -103.62, "y": 14.79}, {"time": 0.7333, "x": -124.66, "y": 137.93}, {"time": 0.8, "x": -102.86, "y": 18.49}, {"time": 1.1, "x": -103.62, "y": 14.79}, {"time": 2.4333, "x": -103.83, "y": 170.23}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0}, {"time": 0.3333, "x": 1, "y": 0.416, "curve": "stepped"}, {"time": 0.7333, "x": 1, "y": 0.416}, {"time": 0.8, "x": 0, "y": 0}, {"time": 1.1, "x": 1, "y": 0.416}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.8, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": "stepped"}, {"time": 1.2667}]}}}}, "7-matrix": {"slots": {"Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": null}, {"time": 1.3333, "name": null}, {"time": 2, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": null}, {"time": 1.3333, "name": null}, {"time": 2, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": null}, {"time": 1.3333, "name": null}, {"time": 2, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": null}, {"time": 1.3333, "name": null}, {"time": 2, "name": null}]}, "Emotion/11-Dep Trai/2": {"attachment": [{"time": 0, "name": "Emotion/11-<PERSON><PERSON> Trai/2"}, {"time": 0.6667, "name": "Emotion/11-<PERSON><PERSON> Trai/2"}, {"time": 1.3333, "name": "Emotion/11-<PERSON><PERSON> Trai/2"}, {"time": 2, "name": "Emotion/11-<PERSON><PERSON> Trai/2"}]}, "Emotion/11-Dep Trai/3": {"attachment": [{"time": 0, "name": "Emotion/11-<PERSON><PERSON> Trai/3"}, {"time": 0.6667, "name": "Emotion/11-<PERSON><PERSON> Trai/3"}, {"time": 1.3333, "name": "Emotion/11-<PERSON><PERSON> Trai/3"}, {"time": 2, "name": "Emotion/11-<PERSON><PERSON> Trai/3"}]}, "Emotion/3-Chao/4": {"attachment": [{"time": 0, "name": null}, {"time": 0.6667, "name": null}, {"time": 1.3333, "name": null}, {"time": 1.5333, "name": null}, {"time": 1.7333, "name": null}, {"time": 1.9333, "name": null}]}, "Emotion/7-Phan No/4": {"attachment": [{"time": 0, "name": "Emotion/7-<PERSON><PERSON> No/4"}, {"time": 0.6667, "name": "Emotion/7-<PERSON><PERSON> No/4"}, {"time": 1.3333, "name": "Emotion/7-<PERSON><PERSON> No/4"}, {"time": 1.5333, "name": "Emotion/7-<PERSON><PERSON> No/4"}, {"time": 1.7333, "name": "Emotion/7-<PERSON><PERSON> No/4"}, {"time": 1.9333, "name": "Emotion/7-<PERSON><PERSON> No/4"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0, "curve": "stepped"}, {"time": 2.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": -2.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 0, "y": -2.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 0, "y": -2.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 0, "y": -2.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.056, "y": 0.974, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.056, "y": 0.974, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.056, "y": 0.974, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "x": 1.056, "y": 0.974, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": "stepped"}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 0.6667, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 1.3333, "x": -7.36, "y": 15.94, "curve": "stepped"}, {"time": 2, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.6667, "angle": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 0.7333, "angle": -9.3, "curve": [0.306, 0.21, 0.643, 0.56]}, {"time": 0.9, "angle": 3.24, "curve": [0.319, 0.29, 0.656, 0.63]}, {"time": 1, "angle": 0.28, "curve": [0.331, 0.33, 0.666, 0.66]}, {"time": 1.1333, "angle": 0.85, "curve": [0.377, 0.51, 0.749, 1]}, {"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": [0.305, 0, 0.64, 0.36]}, {"time": 0.7333, "x": -0.01, "y": 9.26, "curve": [0.306, 0.21, 0.643, 0.56]}, {"time": 0.9, "x": 0.62, "y": -0.09, "curve": [0.314, 0.27, 0.658, 0.64]}, {"time": 1.1333, "x": 0.32, "y": 0.53, "curve": [0.377, 0.51, 0.749, 1]}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.1333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone2": {"translate": [{"time": 0, "x": 11.52, "y": 22.59, "curve": "stepped"}, {"time": 0.6667, "x": 11.52, "y": 22.59, "curve": "stepped"}, {"time": 1.3333, "x": 11.52, "y": 22.59, "curve": "stepped"}, {"time": 2, "x": 11.52, "y": 22.59}], "scale": [{"time": 0, "x": 1.006, "y": 1.006, "curve": "stepped"}, {"time": 0.6667, "x": 1.006, "y": 1.006, "curve": "stepped"}, {"time": 1.3333, "x": 1.006, "y": 1.006, "curve": "stepped"}, {"time": 2, "x": 1.006, "y": 1.006}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 1.3333, "angle": 0, "curve": "stepped"}, {"time": 1.5333, "angle": 0, "curve": "stepped"}, {"time": 1.7333, "angle": 0, "curve": "stepped"}, {"time": 1.9333, "angle": 0, "curve": "stepped"}, {"time": 2.1333, "angle": 0}], "translate": [{"time": 0, "x": -20.23, "y": 60.73, "curve": "stepped"}, {"time": 0.6667, "x": -20.23, "y": 60.73, "curve": "stepped"}, {"time": 1.3333, "x": -20.23, "y": 60.73, "curve": "stepped"}, {"time": 1.5333, "x": -20.23, "y": 60.73, "curve": "stepped"}, {"time": 1.7333, "x": -20.23, "y": 60.73, "curve": "stepped"}, {"time": 1.9333, "x": -20.23, "y": 60.73, "curve": "stepped"}, {"time": 2.1333, "x": -20.23, "y": 60.73}], "scale": [{"time": 0, "x": 0.255, "y": 0.255, "curve": "stepped"}, {"time": 0.6667, "x": 0.255, "y": 0.255, "curve": "stepped"}, {"time": 1.3333, "x": 0.255, "y": 0.255}, {"time": 1.4333, "x": 0.526, "y": 0.526}, {"time": 1.5333, "x": 0.255, "y": 0.255}, {"time": 1.6333, "x": 0.526, "y": 0.526}, {"time": 1.7333, "x": 0.255, "y": 0.255}, {"time": 1.8333, "x": 0.526, "y": 0.526}, {"time": 1.9333, "x": 0.255, "y": 0.255}, {"time": 2.0333, "x": 0.526, "y": 0.526}, {"time": 2.1333, "x": 0.255, "y": 0.255}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.5333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.7333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.1333, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 10.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -10.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667, "angle": 10.43}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2.6667, "x": 0, "y": 0}]}, "bone3": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone4": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone5": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone6": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone9": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone8": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone10": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone11": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone12": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone13": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone14": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone15": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone16": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone17": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone18": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone19": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone20": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone21": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone22": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}, "bone23": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.6667, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.3333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 2, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 2, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": "stepped"}, {"time": 0.6667, "curve": "stepped"}, {"time": 1.3333, "curve": "stepped"}, {"time": 2}]}, "Emotion/11-Dep Trai/2": {"Emotion/11-Dep Trai/2": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 30, "vertices": [5.87923, -4.76605, 0.93853, -3.24676, -2.51813, -2.60144, -5.81404, -2.8096, -6.16753, -0.86183, -3.05564, -0.68885, 1.49584, -1.40297, 6.25673, -2.88399], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "offset": 30, "vertices": [5.87923, -4.76605, 0.93853, -3.24676, -2.51813, -2.60144, -5.81404, -2.8096, -6.16753, -0.86183, -3.05564, -0.68885, 1.49584, -1.40297, 6.25673, -2.88399], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "offset": 30, "vertices": [5.87923, -4.76605, 0.93853, -3.24676, -2.51813, -2.60144, -5.81404, -2.8096, -6.16753, -0.86183, -3.05564, -0.68885, 1.49584, -1.40297, 6.25673, -2.88399], "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "curve": [0.25, 0, 0.75, 1]}, {"time": 2.3333, "offset": 30, "vertices": [5.87923, -4.76605, 0.93853, -3.24676, -2.51813, -2.60144, -5.81404, -2.8096, -6.16753, -0.86183, -3.05564, -0.68885, 1.49584, -1.40297, 6.25673, -2.88399], "curve": [0.25, 0, 0.75, 1]}, {"time": 2.6667}]}}}}, "8-sweat": {"slots": {"Emotion/1-Buon/1": {"color": [{"time": 1.6667, "color": "ffffffff"}]}, "Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}, {"time": 0.2667, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/11-Khoc/2": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.2667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.6, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/11-Khoc/2"}, {"time": 0.2667, "name": "Emotion/11-Khoc/2"}]}, "Emotion/11-Khoc/3": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 0.1333, "color": "ffffffff"}, {"time": 0.6667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.2333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6667, "color": "ffffffff"}], "attachment": [{"time": 0, "name": "Emotion/11-Khoc/3"}]}, "Emotion/11-Khoc/4": {"color": [{"time": 0, "color": "ffffff00", "curve": "stepped"}, {"time": 0.6, "color": "ffffff00"}, {"time": 0.7, "color": "ffffffff", "curve": "stepped"}, {"time": 1.3333, "color": "ffffffff"}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/11-Khoc/4"}, {"time": 0.6, "name": "Emotion/11-Khoc/4"}]}, "Emotion/11-Khoc/5": {"attachment": [{"time": 0, "name": "Emotion/11-Khoc/5"}]}, "Emotion/11-Khoc/6": {"color": [{"time": 0, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0, "name": "Emotion/11-Khoc/3"}, {"time": 0.2333, "name": "Emotion/11-Khoc/3"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 108.49, "curve": "stepped"}, {"time": 0.2667, "angle": 108.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 85.76}, {"time": 0.7333, "angle": 119.33}, {"time": 1.3, "angle": 105.92}, {"time": 1.6, "angle": 90.47}], "translate": [{"time": 0, "x": 34.55, "y": 65.94, "curve": "stepped"}, {"time": 0.2667, "x": 34.55, "y": 65.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 31.9, "y": 18.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 47.3, "y": -0.44}, {"time": 1.3, "x": 54.73, "y": -17.43}, {"time": 1.6, "x": 58.45, "y": -96.02}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.828, "y": 0.828}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2667, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": 120.09}, {"time": 0.6667, "angle": 101.26, "curve": "stepped"}, {"time": 1.2, "angle": 101.26}, {"time": 1.2333, "angle": 87.62, "curve": "stepped"}, {"time": 1.4333, "angle": 87.62}, {"time": 1.6667, "angle": 120.09}], "translate": [{"time": 0, "x": 20.02, "y": -38.23}, {"time": 0.6667, "x": 42.36, "y": -113.83, "curve": "stepped"}, {"time": 1.2, "x": 42.36, "y": -113.83}, {"time": 1.2333, "x": 3.54, "y": 83.31, "curve": [0.371, 0.62, 0.71, 1]}, {"time": 1.4333, "x": 3.54, "y": -11.45}, {"time": 1.6667, "x": 20.02, "y": -38.23}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 1.2333, "x": 0, "y": 0, "curve": [0.371, 0.62, 0.71, 1]}, {"time": 1.4333, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone2": {"rotate": [{"time": 0, "angle": 89.12, "curve": "stepped"}, {"time": 0.6, "angle": 89.12}, {"time": 0.7, "angle": 115.85}, {"time": 1.0333, "angle": 142.82}, {"time": 1.3333, "angle": 114.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 103.73}], "translate": [{"time": 0, "x": 8.01, "y": 149.55}, {"time": 0.6, "x": 7, "y": 149.55}, {"time": 0.7, "x": 21.85, "y": 62.69}, {"time": 1.0333, "x": 39.77, "y": 48.19}, {"time": 1.3333, "x": 55.26, "y": 7.36, "curve": [0.25, 0, 0.832, 0.15]}, {"time": 1.6, "x": 67.34, "y": -20.43}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6, "x": 0, "y": 0}, {"time": 0.7, "x": 0.445, "y": 0.445}, {"time": 1.3333, "x": 1, "y": 1}], "shear": [{"time": 1.6667, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 2.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 5.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 4.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -353.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 5.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 2.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": -26.2, "y": -18.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": -24.23, "y": -18.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": -29.05, "y": -19.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": -27.17, "y": -18.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": -28.19, "y": -19.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": -27.76, "y": -19.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": -27.08, "y": -18.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": -26.2, "y": -18.29}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": 0.944, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1.126, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "x": 1.1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "x": 1.093, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "x": 1.086, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "x": 1.078, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 1.044, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.1333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.4333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}, "bone13": {"rotate": [{"time": 0, "angle": 75.51, "curve": "stepped"}, {"time": 0.2333, "angle": 75.51}, {"time": 0.3333, "angle": 80.59}, {"time": 0.7, "angle": 114.82}, {"time": 1.4333, "angle": 91.83}], "translate": [{"time": 0, "x": 0, "y": 52.96, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 52.96}, {"time": 0.3333, "x": 7.37, "y": -6.55}, {"time": 0.7, "x": 18.56, "y": -13.92}, {"time": 1.4333, "x": 28.99, "y": -109.82}], "scale": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}, {"time": 0.3333, "x": 0.72, "y": 0.72}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.2333, "x": 0, "y": 0}]}, "X-Tong": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -2.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 0.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -1.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 1.6667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6667, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "vertices": [-1.30301, 0, -1.82686, 0, -2.19469, 0, -2.19469, 0, -1.9531, 0, -1.15468, 0, -0.30753, 0, 0.55911, 0, 1.45406, 0, 1.98565, 0, 2.16705, 0, 2.16705, 0, 1.87834, 0, 1.31025, 0, 0.60961, 0, 0.05226, 0, -0.56444, 0, 0.47706, 0, 0.47706, 0, 1.04515, 0, 1.48068, 0, 1.63848, 0, 1.59241, 0, 1.33993, 0, 0.92964, 0, 0.05226, 0, -0.40852, 0, -0.93874, 0, -1.3364, 0, -1.65201, 0, -1.65832, 0, -0.9198, 0, -0.25072, 0, -1.38059, 0, -0.18129, 0, -0.62945, 0, -0.90718, 0, -1.10917, 0, -1.14073, 0, -0.91981, 0, -0.64207, 0, -0.3454, 0, 0.65822, 0, 0.97383, 0, 1.13794, 0, 1.14425, 0, 1.03063, 0, 0.03138, 0, 0.39279, 0, 0.73192, 0, 0.33617, 0, -0.70464, 0, -0.55865, 0, -0.36187, 0, -0.23492, 0, -0.0318, 0, 0.26019, 0, 0.42523, 0, 0.57122, 0, 0.64739, 0, 0.64739, 0, 0.59027, 0, 0.41888, 0, 0.2348, 0, -0.10162, 0, -0.34283, 0, -0.57134, 0, -0.6729, 0, 0.01898], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "vertices": [2.1667, -3.23743, 3.0378, -2.83602, 3.64946, -2.17066, 3.64946, -1.38886, 3.24773, -0.69024, 1.92005, -0.21804, 0.51138, -0.08355, -0.92973, -0.08355, -2.41789, -0.35344, -3.30184, -0.74568, -3.60348, -1.47203, -3.60349, -2.13184, -3.12341, -2.81938, -2.17876, -3.3073, -1.0137, -3.51003, -0.0869, -3.51003, 0.93858, -3.51003, -0.79327, -3.01898, -0.79328, -0.53498, -1.73792, -0.73459, -2.46215, -1.01737, -2.72455, -1.52193, -2.64795, -2.07085, -2.2281, -2.52552, -1.54586, -2.83601, -0.0869, -3.07443, 0.67932, -3.0578, 1.56099, -2.86928, 2.22224, -2.54769, 2.74705, -2.03758, 2.75754, -1.46649, 1.5295, -0.71796, 0.41691, -0.52944, 2.29571, -1.06173, 0.30146, -0.99519, 1.04668, -1.15599, 1.50851, -1.36114, 1.84438, -1.61619, 1.89686, -1.92669, 1.5295, -2.33699, 1.06767, -2.56987, 0.57436, -2.66967, -1.09453, -2.51442, -1.61933, -2.25383, -1.89223, -1.95996, -1.90272, -1.63837, -1.7138, -1.27797, -0.05218, -2.66177, -0.65315, -2.60372, -1.21707, -1.07577, -0.559, -0.95241, 1.17171, -1.88091, 0.92895, -2.10394, 0.60174, -2.22661, 0.39064, -2.26564, 0.05287, -2.28237, -0.43266, -2.23219, -0.70709, -2.17643, -0.94986, -2.05376, -1.07652, -1.87534, -1.07652, -1.65788, -0.98153, -1.50733, -0.69654, -1.37909, -0.39044, -1.34563, 0.16898, -1.36794, 0.57007, -1.47388, 0.95005, -1.59655, 1.11894, -1.74709, -0.03157, -1.83631], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "vertices": [0.95462, -0.04538, 1.33842, -0.03289, 1.60791, -0.01217, 1.60791, 0.01218, 1.43091, 0.03393, 0.84595, 0.04864, 0.22531, 0.05283, -0.40963, 0.05283, -1.06529, 0.04443, -1.45474, 0.03221, -1.58766, 0.00959, -1.58766, -0.01096, -1.37614, -0.03237, -0.95993, -0.04756, -0.44662, -0.05387, -0.03829, -0.05387, 0.41353, -0.05387, -0.34951, -0.03858, -0.34951, 0.03877, -0.7657, 0.03256, -1.08479, 0.02375, -1.2004, 0.00804, -1.16665, -0.00906, -0.98167, -0.02322, -0.68109, -0.03289, -0.03829, -0.04031, 0.2993, -0.03979, 0.68775, -0.03392, 0.97909, -0.02391, 1.21032, -0.00802, 1.21494, 0.00976, 0.67388, 0.03307, 0.18369, 0.03894, 1.01146, 0.02237, 0.13282, 0.02444, 0.46115, 0.01943, 0.66463, 0.01304, 0.81261, 0.0051, 0.83574, -0.00457, 0.67388, -0.01735, 0.4704, -0.0246, 0.25305, -0.02771, -0.48224, -0.02287, -0.71346, -0.01476, -0.8337, -0.0056, -0.83832, 0.00441, -0.75508, 0.01563, -0.02299, -0.02746, -0.28777, -0.02565, -0.53623, 0.02193, -0.24629, 0.02577, 0.51624, -0.00314, 0.40928, -0.01009, 0.26512, -0.01391, 0.17211, -0.01512, 0.0233, -0.01564, -0.19063, -0.01408, -0.31154, -0.01235, -0.4185, -0.00853, -0.4743, -0.00297, -0.4743, 0.0038, -0.43245, 0.00849, -0.30689, 0.01248, -0.17202, 0.01353, 0.07445, 0.01283, 0.25117, 0.00953, 0.41858, 0.00571, 0.49299, 0.00102, -0.01391], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "vertices": [2.2967, -2.21239, 3.22007, -1.97073, 3.86842, -1.57018, 3.86841, -1.09952, 3.44258, -0.67894, 2.03526, -0.39468, 0.54206, -0.31371, -0.98551, -0.31371, -2.56297, -0.47618, -3.49994, -0.71232, -3.8197, -1.14959, -3.81971, -1.54681, -3.31081, -1.96072, -2.30948, -2.25445, -1.07451, -2.3765, -0.09211, -2.3765, 0.9949, -2.3765, -0.84087, -2.08088, -0.84087, -0.58548, -1.8422, -0.70564, -2.60988, -0.87588, -2.88802, -1.17963, -2.80682, -1.51009, -2.36179, -1.78381, -1.63861, -1.97073, -0.09211, -2.11426, 0.72007, -2.10425, 1.65465, -1.99076, 2.35558, -1.79716, 2.91187, -1.49007, 2.923, -1.14625, 1.62127, -0.69563, 0.44193, -0.58214, 2.43345, -0.90258, 0.31954, -0.86253, 1.10947, -0.95933, 1.59902, -1.08283, 1.95504, -1.23638, 2.01068, -1.42331, 1.62127, -1.67032, 1.13173, -1.81051, 0.60882, -1.87059, -1.1602, -1.77713, -1.71649, -1.62024, -2.00577, -1.44333, -2.01689, -1.24973, -1.81662, -1.03276, -0.05532, -1.86584, -0.69234, -1.83088, -1.2901, -0.91104, -0.59254, -0.83677, 1.24201, -1.39575, 0.98468, -1.53001, 0.63784, -1.60386, 0.41407, -1.62736, 0.05605, -1.63743, -0.45862, -1.60722, -0.74952, -1.57365, -1.00685, -1.49981, -1.14111, -1.39239, -1.14111, -1.26148, -1.04041, -1.17085, -0.73833, -1.09364, -0.41387, -1.0735, 0.17912, -1.08693, 0.60427, -1.15071, 1.00706, -1.22455, 1.18608, -1.31519, -0.03346, -1.36889], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "vertices": [1.43114, -0.4688, 2.00652, -0.33968, 2.41052, -0.12565, 2.41051, 0.12581, 2.14517, 0.35053, 1.26823, 0.50241, 0.33777, 0.54567, -0.6141, 0.54567, -1.59706, 0.45885, -2.18091, 0.3327, -2.38016, 0.09906, -2.38017, -0.11317, -2.06306, -0.33432, -1.4391, -0.49126, -0.66956, -0.55647, -0.0574, -0.55647, 0.61995, -0.55647, -0.52397, -0.39852, -0.52397, 0.40048, -1.14792, 0.33627, -1.62628, 0.2453, -1.79961, 0.08301, -1.749, -0.09355, -1.47169, -0.2398, -1.02106, -0.33968, -0.0574, -0.41637, 0.4487, -0.41101, 1.03105, -0.35037, 1.46783, -0.24693, 1.81447, -0.08285, 1.8214, 0.10085, 1.01026, 0.34162, 0.27538, 0.40226, 1.51635, 0.23104, 0.19912, 0.25244, 0.69134, 0.20072, 0.99639, 0.13473, 1.21825, 0.05269, 1.25291, -0.04718, 1.01025, -0.17916, 0.70521, -0.25406, 0.37937, -0.28616, -0.72295, -0.23623, -1.06959, -0.15241, -1.24985, -0.05788, -1.25677, 0.04556, -1.13199, 0.16148, -0.03447, -0.28363, -0.43142, -0.26495, -0.80389, 0.22652, -0.36923, 0.2662, 0.77393, -0.03246, 0.61358, -0.10419, 0.39746, -0.14365, 0.25802, -0.15621, 0.03492, -0.16159, -0.28578, -0.14545, -0.46705, -0.12751, -0.6274, -0.08805, -0.71106, -0.03066, -0.71106, 0.03928, -0.64831, 0.08771, -0.46007, 0.12896, -0.25789, 0.13972, 0.11161, 0.13254, 0.37654, 0.09847, 0.62752, 0.05901, 0.73908, 0.01059, -0.02085, -0.01811], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "vertices": [3.47881, -1.29327, 4.87744, -0.93707, 5.85948, -0.34664, 5.85948, 0.34709, 5.21448, 0.96702, 3.08281, 1.38601, 0.82105, 1.50536, -1.49275, 1.50536, -3.88213, 1.26587, -5.30136, 0.91781, -5.78569, 0.27329, -5.78571, -0.31221, -5.01488, -0.92229, -3.49817, -1.35526, -1.62756, -1.53515, -0.13952, -1.53515, 1.50697, -1.53515, -1.27366, -1.09941, -1.27367, 1.10478, -2.79037, 0.92766, -3.95318, 0.67673, -4.37449, 0.229, -4.25149, -0.25808, -3.57739, -0.66153, -2.482, -0.93706, -0.13953, -1.14863, 1.0907, -1.13386, 2.50629, -0.96658, 3.56799, -0.68121, 4.4106, -0.22857, 4.42746, 0.27821, 2.45573, 0.94242, 0.66939, 1.1097, 3.68595, 0.63737, 0.48401, 0.69641, 1.68052, 0.55373, 2.42203, 0.37169, 2.96131, 0.14536, 3.04558, -0.13016, 2.45573, -0.49425, 1.71423, -0.70089, 0.92217, -0.78945, -1.75735, -0.65169, -2.59996, -0.42045, -3.03813, -0.15968, -3.05498, 0.12568, -2.75163, 0.44549, -0.08379, -0.78244, -1.04869, -0.73093, -1.95411, 0.62491, -0.89752, 0.73437, 1.88127, -0.08954, 1.4915, -0.28745, 0.96614, -0.3963, 0.6272, -0.43093, 0.08489, -0.44578, -0.69467, -0.40125, -1.1353, -0.35177, -1.52507, -0.24292, -1.72844, -0.08459, -1.72844, 0.10837, -1.57592, 0.24196, -1.11835, 0.35576, -0.62688, 0.38544, 0.27131, 0.36565, 0.91529, 0.27165, 1.52539, 0.16279, 1.79654, 0.02921, -0.05068, -0.04996], "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667}]}}}}, "9-choler": {"slots": {"Emotion/1-Buon/1": {"color": [{"time": 0, "color": "ffffffff"}, {"time": 0.1333, "color": "f78888ff"}, {"time": 0.3, "color": "ffffffff"}, {"time": 0.4667, "color": "f78888ff"}, {"time": 0.6333, "color": "ffffffff"}, {"time": 0.8, "color": "f78888ff"}, {"time": 0.9667, "color": "ffffffff"}, {"time": 1.1333, "color": "f78888ff"}, {"time": 1.3, "color": "ffffffff"}, {"time": 1.4667, "color": "f78888ff"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 1.8, "color": "f78888ff"}, {"time": 1.9667, "color": "ffffffff"}]}, "Emotion/1-Buon/2": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/3": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/4": {"attachment": [{"time": 0, "name": null}]}, "Emotion/1-Buon/5": {"attachment": [{"time": 0, "name": null}]}, "Emotion/7-Phan No/2": {"attachment": [{"time": 0, "name": "Emotion/7-<PERSON><PERSON> No/2"}]}, "Emotion/7-Phan No/3": {"attachment": [{"time": 0, "name": "Emotion/7-<PERSON><PERSON> No/3"}]}, "Emotion/7-Phan No/4": {"attachment": [{"time": 0, "name": "Emotion/7-<PERSON><PERSON> No/4"}]}, "Emotion/7-Phan No/5": {"attachment": [{"time": 0, "name": "Emotion/7-<PERSON><PERSON> No/5"}]}, "Emotion/7-Phan No/6": {"attachment": [{"time": 0, "name": "Emotion/7-<PERSON><PERSON> No/6"}]}}, "bones": {"bone": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.3, "angle": 0, "curve": "stepped"}, {"time": 0.6333, "angle": 0, "curve": "stepped"}, {"time": 0.9667, "angle": 0, "curve": "stepped"}, {"time": 1.3, "angle": 0, "curve": "stepped"}, {"time": 1.6333, "angle": 0, "curve": "stepped"}, {"time": 1.9667, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1333, "x": 1.059, "y": 1.059}, {"time": 0.3, "x": 1, "y": 1}, {"time": 0.4667, "x": 1.059, "y": 1.059}, {"time": 0.6333, "x": 1, "y": 1}, {"time": 0.8, "x": 1.059, "y": 1.059}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.1333, "x": 1.059, "y": 1.059}, {"time": 1.3, "x": 1, "y": 1}, {"time": 1.4667, "x": 1.059, "y": 1.059}, {"time": 1.6333, "x": 1, "y": 1}, {"time": 1.8, "x": 1.059, "y": 1.059}, {"time": 1.9667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.9667, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.3, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.6333, "x": 0, "y": 0, "curve": "stepped"}, {"time": 1.9667, "x": 0, "y": 0}]}, "root": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-1": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-2": {"rotate": [{"time": 0, "angle": 0}], "translate": [{"time": 0, "x": -7.36, "y": 15.94}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "1-3": {"rotate": [{"time": 0, "angle": -52.92}], "translate": [{"time": 0, "x": 22.7, "y": -44.53}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1333, "x": 1.177, "y": 1.177}, {"time": 0.3, "x": 1, "y": 1}, {"time": 0.4667, "x": 1.157, "y": 1.157}, {"time": 0.6333, "x": 1, "y": 1}, {"time": 0.8, "x": 1.167, "y": 1.167}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.1333, "x": 1.167, "y": 1.167}, {"time": 1.3, "x": 1, "y": 1}, {"time": 1.4667, "x": 1.167, "y": 1.167}, {"time": 1.6333, "x": 1, "y": 1}, {"time": 1.8, "x": 1.167, "y": 1.167}, {"time": 1.9667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone12": {"rotate": [{"time": 0, "angle": -163.14}], "translate": [{"time": 0, "x": 25.68, "y": 1.5}], "scale": [{"time": 0, "x": 1, "y": 1}, {"time": 0.1333, "x": 1.197, "y": 1.197}, {"time": 0.3, "x": 1, "y": 1}, {"time": 0.4667, "x": 1.176, "y": 1.176}, {"time": 0.6333, "x": 1, "y": 1}, {"time": 0.8, "x": 1.186, "y": 1.186}, {"time": 0.9667, "x": 1, "y": 1}, {"time": 1.1333, "x": 1.186, "y": 1.186}, {"time": 1.3, "x": 1, "y": 1}, {"time": 1.4667, "x": 1.186, "y": 1.186}, {"time": 1.6333, "x": 1, "y": 1}, {"time": 1.8, "x": 1.186, "y": 1.186}, {"time": 1.9667, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone3": {"rotate": [{"time": 0, "angle": 55.9}], "translate": [{"time": 0, "x": 3.24, "y": -28.5}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone9": {"rotate": [{"time": 0, "angle": -28.84}], "translate": [{"time": 0, "x": 15.61, "y": 50.96}], "scale": [{"time": 0, "x": 1, "y": 1}], "shear": [{"time": 0, "x": 0, "y": 0}]}, "bone7": {"rotate": [{"time": 0, "angle": 0, "curve": "stepped"}, {"time": 0.4333, "angle": 0}], "translate": [{"time": 0, "x": 16.8, "y": -3.44, "curve": "stepped"}, {"time": 0.4333, "x": 16.8, "y": -3.44}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": "stepped"}, {"time": 0.4333, "x": 1, "y": 1}, {"time": 0.6, "x": 1.148, "y": 1.148}, {"time": 0.8333, "x": 0.923, "y": 0.923}, {"time": 1.1, "x": 1.061, "y": 1.061}], "shear": [{"time": 0, "x": 0, "y": 0, "curve": "stepped"}, {"time": 0.4333, "x": 0, "y": 0}]}}, "deform": {"default": {"Emotion/1-Buon/1": {"Emotion/1-Buon/1": [{"time": 0}, {"time": 0.1333, "offset": 34, "vertices": [-1.75354, 5.20925, -1.75355, -5.23702, -3.85212, -4.39757, -5.46103, -3.20838, -6.04398, -1.08648, -5.87379, 1.22196, -4.94108, 3.13399, -3.42544, 4.43978, -0.18431, 5.44243, 1.51788, 5.37248, 3.47655, 4.57968, 4.94556, 3.22726, 6.11143, 1.08205, 6.13475, -1.31966, 3.4066, -4.46753, 0.93494, -5.26032, 5.10877, -3.02184, 1.34278, -6.08307, 4.33766, -4.85981, 6.19363, -3.2991, 7.54344, -1.35876, 4.2227, 0.61569, 6.27799, 4.12481, 4.42201, 5.89643, 2.4395, 6.65569, -4.26733, 5.47462, -6.37641, 3.4921, -7.47311, 1.25648, -7.5153, -1.19003, -6.75603, -3.93182, -0.07842, 6.59559, -2.49357, 6.15394, -4.75983, -5.47009, -2.11519, -6.40852, 4.84013, 0.65513, 3.86452, 2.35186, 2.54954, 3.28508, 1.70118, 3.582, 0.34379, 3.70926, -1.60746, 3.32749, -2.71034, 2.90331, -3.68596, 1.9701, -4.19498, 0.61271, -4.19498, -1.0416, -3.81322, -2.1869, -2.66792, -3.16252, -1.43779, -3.41704, 0.81039, -3.24736, 2.42229, -2.44141, 3.94935, -1.50821, 4.62804, -0.36291, 0.00444, 0.31578]}, {"time": 0.3}, {"time": 0.4667, "offset": 34, "vertices": [-1.75354, 5.20925, -1.75355, -5.23702, -3.85212, -4.39757, -5.46103, -3.20838, -6.04398, -1.08648, -5.87379, 1.22196, -4.94108, 3.13399, -3.42544, 4.43978, -0.18431, 5.44243, 1.51788, 5.37248, 3.47655, 4.57968, 4.94556, 3.22726, 6.11143, 1.08205, 6.13475, -1.31966, 3.4066, -4.46753, 0.93494, -5.26032, 5.10877, -3.02184, 1.34278, -6.08307, 4.33766, -4.85981, 6.19363, -3.2991, 7.54344, -1.35876, 4.2227, 0.61569, 6.27799, 4.12481, 4.42201, 5.89643, 2.4395, 6.65569, -4.26733, 5.47462, -6.37641, 3.4921, -7.47311, 1.25648, -7.5153, -1.19003, -6.75603, -3.93182, -0.07842, 6.59559, -2.49357, 6.15394, -4.75983, -5.47009, -2.11519, -6.40852, 4.84013, 0.65513, 3.86452, 2.35186, 2.54954, 3.28508, 1.70118, 3.582, 0.34379, 3.70926, -1.60746, 3.32749, -2.71034, 2.90331, -3.68596, 1.9701, -4.19498, 0.61271, -4.19498, -1.0416, -3.81322, -2.1869, -2.66792, -3.16252, -1.43779, -3.41704, 0.81039, -3.24736, 2.42229, -2.44141, 3.94935, -1.50821, 4.62804, -0.36291, 0.00444, 0.31578]}, {"time": 0.6333}, {"time": 0.8, "offset": 34, "vertices": [-1.75354, 5.20925, -1.75355, -5.23702, -3.85212, -4.39757, -5.46103, -3.20838, -6.04398, -1.08648, -5.87379, 1.22196, -4.94108, 3.13399, -3.42544, 4.43978, -0.18431, 5.44243, 1.51788, 5.37248, 3.47655, 4.57968, 4.94556, 3.22726, 6.11143, 1.08205, 6.13475, -1.31966, 3.4066, -4.46753, 0.93494, -5.26032, 5.10877, -3.02184, 1.34278, -6.08307, 4.33766, -4.85981, 6.19363, -3.2991, 7.54344, -1.35876, 4.2227, 0.61569, 6.27799, 4.12481, 4.42201, 5.89643, 2.4395, 6.65569, -4.26733, 5.47462, -6.37641, 3.4921, -7.47311, 1.25648, -7.5153, -1.19003, -6.75603, -3.93182, -0.07842, 6.59559, -2.49357, 6.15394, -4.75983, -5.47009, -2.11519, -6.40852, 4.84013, 0.65513, 3.86452, 2.35186, 2.54954, 3.28508, 1.70118, 3.582, 0.34379, 3.70926, -1.60746, 3.32749, -2.71034, 2.90331, -3.68596, 1.9701, -4.19498, 0.61271, -4.19498, -1.0416, -3.81322, -2.1869, -2.66792, -3.16252, -1.43779, -3.41704, 0.81039, -3.24736, 2.42229, -2.44141, 3.94935, -1.50821, 4.62804, -0.36291, 0.00444, 0.31578]}, {"time": 0.9667}, {"time": 1.1333, "offset": 34, "vertices": [-1.75354, 5.20925, -1.75355, -5.23702, -3.85212, -4.39757, -5.46103, -3.20838, -6.04398, -1.08648, -5.87379, 1.22196, -4.94108, 3.13399, -3.42544, 4.43978, -0.18431, 5.44243, 1.51788, 5.37248, 3.47655, 4.57968, 4.94556, 3.22726, 6.11143, 1.08205, 6.13475, -1.31966, 3.4066, -4.46753, 0.93494, -5.26032, 5.10877, -3.02184, 1.34278, -6.08307, 4.33766, -4.85981, 6.19363, -3.2991, 7.54344, -1.35876, 4.2227, 0.61569, 6.27799, 4.12481, 4.42201, 5.89643, 2.4395, 6.65569, -4.26733, 5.47462, -6.37641, 3.4921, -7.47311, 1.25648, -7.5153, -1.19003, -6.75603, -3.93182, -0.07842, 6.59559, -2.49357, 6.15394, -4.75983, -5.47009, -2.11519, -6.40852, 4.84013, 0.65513, 3.86452, 2.35186, 2.54954, 3.28508, 1.70118, 3.582, 0.34379, 3.70926, -1.60746, 3.32749, -2.71034, 2.90331, -3.68596, 1.9701, -4.19498, 0.61271, -4.19498, -1.0416, -3.81322, -2.1869, -2.66792, -3.16252, -1.43779, -3.41704, 0.81039, -3.24736, 2.42229, -2.44141, 3.94935, -1.50821, 4.62804, -0.36291, 0.00444, 0.31578]}, {"time": 1.3}, {"time": 1.4667, "offset": 34, "vertices": [-1.75354, 5.20925, -1.75355, -5.23702, -3.85212, -4.39757, -5.46103, -3.20838, -6.04398, -1.08648, -5.87379, 1.22196, -4.94108, 3.13399, -3.42544, 4.43978, -0.18431, 5.44243, 1.51788, 5.37248, 3.47655, 4.57968, 4.94556, 3.22726, 6.11143, 1.08205, 6.13475, -1.31966, 3.4066, -4.46753, 0.93494, -5.26032, 5.10877, -3.02184, 1.34278, -6.08307, 4.33766, -4.85981, 6.19363, -3.2991, 7.54344, -1.35876, 4.2227, 0.61569, 6.27799, 4.12481, 4.42201, 5.89643, 2.4395, 6.65569, -4.26733, 5.47462, -6.37641, 3.4921, -7.47311, 1.25648, -7.5153, -1.19003, -6.75603, -3.93182, -0.07842, 6.59559, -2.49357, 6.15394, -4.75983, -5.47009, -2.11519, -6.40852, 4.84013, 0.65513, 3.86452, 2.35186, 2.54954, 3.28508, 1.70118, 3.582, 0.34379, 3.70926, -1.60746, 3.32749, -2.71034, 2.90331, -3.68596, 1.9701, -4.19498, 0.61271, -4.19498, -1.0416, -3.81322, -2.1869, -2.66792, -3.16252, -1.43779, -3.41704, 0.81039, -3.24736, 2.42229, -2.44141, 3.94935, -1.50821, 4.62804, -0.36291, 0.00444, 0.31578]}, {"time": 1.6333}, {"time": 1.8, "offset": 34, "vertices": [-1.75354, 5.20925, -1.75355, -5.23702, -3.85212, -4.39757, -5.46103, -3.20838, -6.04398, -1.08648, -5.87379, 1.22196, -4.94108, 3.13399, -3.42544, 4.43978, -0.18431, 5.44243, 1.51788, 5.37248, 3.47655, 4.57968, 4.94556, 3.22726, 6.11143, 1.08205, 6.13475, -1.31966, 3.4066, -4.46753, 0.93494, -5.26032, 5.10877, -3.02184, 1.34278, -6.08307, 4.33766, -4.85981, 6.19363, -3.2991, 7.54344, -1.35876, 4.2227, 0.61569, 6.27799, 4.12481, 4.42201, 5.89643, 2.4395, 6.65569, -4.26733, 5.47462, -6.37641, 3.4921, -7.47311, 1.25648, -7.5153, -1.19003, -6.75603, -3.93182, -0.07842, 6.59559, -2.49357, 6.15394, -4.75983, -5.47009, -2.11519, -6.40852, 4.84013, 0.65513, 3.86452, 2.35186, 2.54954, 3.28508, 1.70118, 3.582, 0.34379, 3.70926, -1.60746, 3.32749, -2.71034, 2.90331, -3.68596, 1.9701, -4.19498, 0.61271, -4.19498, -1.0416, -3.81322, -2.1869, -2.66792, -3.16252, -1.43779, -3.41704, 0.81039, -3.24736, 2.42229, -2.44141, 3.94935, -1.50821, 4.62804, -0.36291, 0.00444, 0.31578]}, {"time": 1.9667}]}}}}}}, [0]]], 0, 0, [0], [-1], [8]], [[{"name": "textbox_chat", "rect": [0, 0, 285, 45], "offset": [0, 0], "originalSize": [285, 45], "capInsets": [30, 0, 30, 0]}], [1], 0, [0], [5], [9]], [[{"name": "bg_tool2", "rect": [0, 2, 47, 34], "offset": [-0.5, 0.5], "originalSize": [48, 39], "capInsets": [22, 16, 22, 16]}], [1], 0, [0], [5], [10]], [[[9, "hideChatRoom", 0.16666666666666666, {"paths": {"offset-chat": {"props": {"x": [{"frame": 0, "value": 0}, {"frame": 0.16666666666666666, "value": -535}], "opacity": [{"frame": 0, "value": 255}, {"frame": 0.16666666666666666, "value": 0}], "active": [{"frame": 0, "value": true}, {"frame": 0.16666666666666666, "value": false}]}}}}]], 0, 0, [], [], []], [[{"name": "bg_tn", "rect": [0, 0, 50, 50], "offset": [0, 0], "originalSize": [50, 50], "capInsets": [24, 24, 24, 24]}], [1], 0, [0], [5], [11]], [[[23, "ChatRoomPrefab"], [24, "ChatRoomPrefab", [-9], [[39, -7, -6, -5, -4, -3, -2], [40, -8, [41, 42]]], [41, -1], [0, 0, 0.5], [-640, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "emotion", false, [-11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32], [[42, false, 1, 3, 20, 30, -10, [5, 700, 670]]], [0, "021XxSJwtPmISIUEM6p/5c", 1], [5, 700, 670], [265, 97, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "1-waaaht", 2, [[4, "1-waaaht", 0, false, "1-waaaht", -33, 14], [2, 0.9, 3, -35, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "0", 1]], [4, 4292269782], -34]], [0, "9d2qtRF3VJTZJYWAE6zu58", 1], [5, 123, 110], [-288.5, 280, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "content", [-37, -38, -39, -40, -41, -42, -43, -44, -45, -46, -47, -48], [[43, false, 3, 15, 10, -36, [5, 637, 140]]], [0, "66CXvBx9dKNqTRLsqzs8C8", 1], [5, 637, 140], [0, 0, 0.5], [-250, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "offset-chat", false, 0, 1, [-49, -50, -51, 2, -52, -53, -54], [0, "74nvatl9FH67pS4Ls5hZ+R", 1], [5, 380, 415], [0, 0, 0.5], [-535, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "item-horizontal", [-56, -57, -58, -59], [[15, 1, 1, 5, -55, [5, 503, 33]]], [0, "3dq/zgR4tH2aNo0sUTCcku", 1], [5, 503, 33], [0, 0, 0.5]], [14, "scrollview-view", [-62, -63], [-60, -61], [0, "83maxVaA1NjpwD4wqxxbUb", 1], [5, 500, 480], [0, 112, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "item", [6], [[15, 1, 2, 5, -64, [5, -5, 33]], [46, -69, -68, -67, -66, -65]], [0, "daWZfyAhtEcKTJaxT6udbi", 1], [5, -5, 33], [0, 0, 0.5], [-248, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "editbox-chat", [-71, -72, -73], [-70], [0, "369BdhlsJKuIh6JCc78Vu1", 1], [5, 310, 30], [-79.9, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "black", 150, 5, [[17, 0, -74, 0], [45, -76, [[8, "4ffb0Clea9C6rf5rVISctZ3", "hideChatClicked", 1]], [4, 4292269782], -75]], [0, "d5XKVBQv9KgLAhCe5pbeLG", 1], [5, 3000, 2000], [460, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "normalChat", 5, [7, -77], [0, "21zzcxTK5EJb4xHZiN2dKV", 1], [264, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnQChat", 4, [[5, 1, 0, -78, 2], [2, 0.9, 3, -80, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatShortcutClicked", "0", 1]], [4, 4292269782], -79]], [0, "f9sngG9DNNUaK+qJAX77/P", 1], [4, 4293322470], [5, 180, 60], [90, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnQChat copy", 4, [[5, 1, 0, -81, 3], [2, 0.9, 3, -83, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatShortcutClicked", "1", 1]], [4, 4292269782], -82]], [0, "3a4MA/0dtOyKsbuW5yrFVM", 1], [4, 4293322470], [5, 180, 60], [285, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnQChat copy", 4, [[5, 1, 0, -84, 4], [2, 0.9, 3, -86, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatShortcutClicked", "2", 1]], [4, 4292269782], -85]], [0, "376G8vlWdGYJhTURkq09J+", 1], [4, 4293322470], [5, 180, 60], [480, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnQChat copy", 4, [[5, 1, 0, -87, 5], [2, 0.9, 3, -89, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatShortcutClicked", "3", 1]], [4, 4292269782], -88]], [0, "efKivKTwRDArIK5ij7fB2R", 1], [4, 4293322470], [5, 180, 60], [90, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnQChat copy", 4, [[5, 1, 0, -90, 6], [2, 0.9, 3, -92, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatShortcutClicked", "4", 1]], [4, 4292269782], -91]], [0, "f7g4AQ5xBBC4vsS4PUmZqA", 1], [4, 4293322470], [5, 180, 60], [285, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnQChat copy", 4, [[5, 1, 0, -93, 7], [2, 0.9, 3, -95, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatShortcutClicked", "5", 1]], [4, 4292269782], -94]], [0, "24AaElWmRBQoGbnFLcW4aj", 1], [4, 4293322470], [5, 180, 60], [480, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnEmotion", 5, [-98], [[16, 0.9, 3, -97, [[8, "4ffb0Clea9C6rf5rVISctZ3", "showEmotionClicked", 1]], [4, 4294967295], [4, 4294967295], -96]], [0, "46i61P9GJIrpZcxBi8U+5Y", 1], [5, 80, 70], [376, -319, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "btnSend", 5, [-100], [-99], [0, "65E+kV+aJMX57LVlnBy82r", 1], [5, 80, 70], [457, -319, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "bgchat", 5, [[17, 0, -101, 1], [49, -102]], [0, "1dePhv9BxBpqI9nhpBCsDi", 1], [5, 535, 1500], [0, 0, 0.5]], [13, "view", 7, [-104], [[19, 0, -103]], [0, "f1Veg7LipI8rYrSE80XuR8", 1], [5, 500, 480]], [30, "content", 21, [[44, 1, 2, -105, [5, 500, 0]]], [0, "36kcmFzipHE6l/Lr3PL/gr", 1], [5, 500, 0], [0, 0.5, 1], [0, 232, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "scrollview-shortcut", 11, [-107], [[50, false, 0.75, 0.23, null, null, -106, 4]], [0, "28tryaxnxNQLOuYry2HaSc", 1], [5, 500, 130], [0, -209.1, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "view", 23, [4], [[19, 0, -108]], [0, "82gX2BtopMS6MB5Nd2jHIZ", 1], [5, 500, 150]], [1, "2-mis<PERSON><PERSON><PERSON>", 2, [[4, "2-mis<PERSON><PERSON><PERSON>", 0, false, "2-mis<PERSON><PERSON><PERSON>", -109, 15], [2, 0.9, 3, -110, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "1", 1]], [4, 4292269782], 3]], [0, "08/Db25wJFg7xq8BXt0VJ2", 1], [5, 123, 110], [-145.5, 280, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "3-boss", 2, [[4, "3-boss", 0, false, "3-boss", -111, 16], [2, 0.9, 3, -112, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "2", 1]], [4, 4292269782], 3]], [0, "87vQjuqUtHebadVM2QzNv3", 1], [5, 123, 110], [-2.5, 280, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "4-beauty", 2, [[4, "4-beauty", 0, false, "4-beauty", -113, 17], [2, 0.9, 3, -114, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "3", 1]], [4, 4292269782], 3]], [0, "987KwrfYtGF77OqR1yQbfD", 1], [5, 123, 110], [140.5, 280, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "5-byebye", 2, [[4, "5-byebye", 0, false, "5-byebye", -115, 18], [2, 0.9, 3, -116, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "4", 1]], [4, 4292269782], 3]], [0, "f0ib2PVbNDcq4xGkq06SUy", 1], [5, 123, 110], [283.5, 280, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "6-after_boom", 2, [[4, "6-after_boom", 0, false, "6-after_boom", -117, 19], [2, 0.9, 3, -118, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "5", 1]], [4, 4292269782], 3]], [0, "251BH4ueBGe6smmLAXmrOR", 1], [5, 123, 110], [-288.5, 140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "7-matrix", 2, [[4, "7-matrix", 0, false, "7-matrix", -119, 20], [2, 0.9, 3, -120, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "6", 1]], [4, 4292269782], 3]], [0, "acIKiDrPdBabXObxVETUfR", 1], [5, 123, 110], [-145.5, 140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "8-sweat", 2, [[4, "8-sweat", 0, false, "8-sweat", -121, 21], [2, 0.9, 3, -122, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "7", 1]], [4, 4292269782], 3]], [0, "d2JZnWUOBMJK/c/03Gslsm", 1], [5, 123, 110], [-2.5, 140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "9-choler", 2, [[4, "9-choler", 0, false, "9-choler", -123, 22], [2, 0.9, 3, -124, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "8", 1]], [4, 4292269782], 3]], [0, "921NNfSiRBqLjj53CQ7eSe", 1], [5, 123, 110], [140.5, 140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "10-beated", 2, [[4, "10-beated", 0, false, "10-beated", -125, 23], [2, 0.9, 3, -126, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "9", 1]], [4, 4292269782], 3]], [0, "8cwIsVBRxHqJo2j+WRNTBL", 1], [5, 123, 110], [283.5, 140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "11-angry", 2, [[4, "11-angry", 0, false, "11-angry", -127, 24], [2, 0.9, 3, -128, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "10", 1]], [4, 4292269782], 3]], [0, "d0jGyJdplBIbnalHQtdW/+", 1], [5, 123, 110], [-288.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "12-ah", 2, [[4, "12-ah", 0, false, "12-ah", -129, 25], [2, 0.9, 3, -130, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "11", 1]], [4, 4292269782], 3]], [0, "c6cSS0UpFPga7epvie0EHj", 1], [5, 123, 110], [-145.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "13-beat", 2, [[4, "13-beat", 0, false, "13-beat", -131, 26], [2, 0.9, 3, -132, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "12", 1]], [4, 4292269782], 3]], [0, "c10Bma1GhNUb7L6QLqG5v1", 1], [5, 123, 110], [-2.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "14-adore", 2, [[4, "14-adore", 0, false, "14-adore", -133, 27], [2, 0.9, 3, -134, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "13", 1]], [4, 4292269782], 3]], [0, "8dwM4sreRMjrlDpgoAQPzh", 1], [5, 123, 110], [140.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "15-beat_shot", 2, [[4, "15-beat_shot", 0, false, "15-beat_shot", -135, 28], [2, 0.9, 3, -136, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "14", 1]], [4, 4292269782], 3]], [0, "63CibJlShDz58c2me6ipEa", 1], [5, 123, 110], [283.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "16-extreme-sexy-girl", 2, [[4, "16-extreme-sexy-girl", 0, false, "16-extreme-sexy-girl", -137, 29], [2, 0.9, 3, -138, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "15", 1]], [4, 4292269782], 3]], [0, "6ar00g6zxAwrHmQLMAS5Cb", 1], [5, 123, 110], [-288.5, -140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "20-burn_joss_stick", 2, [[4, "20-burn_joss_stick", 0, false, "20-burn_joss_stick", -139, 30], [2, 0.9, 3, -140, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "16", 1]], [4, 4292269782], 3]], [0, "e60BMD2aJPp6rzVD1XWQnZ", 1], [5, 123, 110], [-145.5, -140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "21-baffle", 2, [[4, "21-baffle", 0, false, "21-baffle", -141, 31], [2, 0.9, 3, -142, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "17", 1]], [4, 4292269782], 3]], [0, "bbnKCOBvlHp4DQjEREIZip", 1], [5, 123, 110], [-2.5, -140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "22-cool", 2, [[4, "22-cool", 0, false, "22-cool", -143, 32], [2, 0.9, 3, -144, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "18", 1]], [4, 4292269782], 3]], [0, "9cDQWCpLtAN7Ehgz76VJZ3", 1], [5, 123, 110], [140.5, -140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "23-dribble", 2, [[4, "23-dribble", 0, false, "23-dribble", -145, 33], [2, 0.9, 3, -146, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "19", 1]], [4, 4292269782], 3]], [0, "acNLmUn1ZL3L8snXRCPBat", 1], [5, 123, 110], [283.5, -140, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "24-tire", 2, [[4, "24-tire", 0, false, "24-tire", -147, 34], [2, 0.9, 3, -148, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "20", 1]], [4, 4292269782], 3]], [0, "b6hfTg9g9C4bMlov3vEXdZ", 1], [5, 123, 110], [-288.5, -280, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "25-BigSmile", 2, [[4, "25-BigSmile", 0, false, "25-BigSmile", -149, 35], [2, 0.9, 3, -150, [[3, "4ffb0Clea9C6rf5rVISctZ3", "chatEmotionClicked", "21", 1]], [4, 4292269782], 3]], [0, "39E8hWX5xGmK9wdBbxFPJK", 1], [5, 123, 110], [-145.5, -280, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "textbox_chat", 5, [9], [[5, 1, 0, -151, 38]], [0, "54Eza4YVRB0JIdHKZG0taP", 1], [5, 510, 72], [264, -316, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "temp", 7, [8], [0, "8dJ7AnPp5F6ZqA/5lr5n5r", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "lbSID", 6, [-152], [0, "04a4XrOM5HeqNFaI0ud2BC", 1], [4, 4279026733], [5, 37, 24], [0, 0, 0.5]], [20, "[BB]", 20, 48, false, false, 1, 1, 48], [36, "lbNickName", 6, [-153], [0, "af1br/a+xAnKJBar8oeZlW", 1], [4, 4281523194], [5, 95, 24], [0, 0, 0.5], [42, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "<PERSON><PERSON><PERSON>", 20, 48, false, false, 1, 1, 50], [37, "V1", false, 6, [-154], [0, "79gOCbS2FFjYcN/dsq2DiI", 1], [5, 30, 28], [157, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [47, 52], [38, "lbMessage", 6, [-155], [0, "02gfGIDwxBkoeyzInwDNEA", 1], [5, 326, 24], [0, 0, 0.5], [177, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "12345678901234560", 20, 48, false, false, 1, 1, 54], [51, false, 7, 22], [55, 15, 400, 7, 8, 56], [1, "Label", 4, [[7, "<PERSON><PERSON>i nhiều quá\nđánh đi!", 20, 33, false, false, 1, 1, -156, 8]], [0, "b1o/LRcS5AMafdScPoN5bh", 1], [5, 126.5, 48], [90, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[7, "<PERSON><PERSON>i gì vết bẩn?", 20, 48, false, false, 1, 1, -157, 9]], [0, "51OC6WL0xMwLRR/NPEtnVr", 1], [5, 147.5, 24], [285, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[7, "<PERSON><PERSON>i thì chơi \nko chơi thì té", 20, 33, false, false, 1, 1, -158, 10]], [0, "4fhI+lSLlBqYN7kjOQTQc8", 1], [5, 118, 48], [480, 40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[7, "<PERSON><PERSON><PERSON>!", 20, 48, false, false, 1, 1, -159, 11]], [0, "e0kutY1FhC0bV1d8ZlEkya", 1], [5, 52, 24], [90, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[7, "Max nhọ !!!", 20, 48, false, false, 1, 1, -160, 12]], [0, "4bel+7YYRD27BCMOeNnYOn", 1], [5, 99.5, 24], [285, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Label", 4, [[7, "Ngon quá hehe!", 20, 48, false, false, 1, 1, -161, 13]], [0, "eftGv+EyNK2JyljzDVFF7U", 1], [5, 142, 24], [480, -30, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "BACKGROUND_SPRITE", 9, [[48, 1, 0, -162]], [0, "82HT0tp8JLSIvNJ/t430cT", 1], [5, 310, 30]], [32, "TEXT_LABEL", false, 9, [[53, 24, 30, false, false, 1, 1, -163, 36]], [0, "b86jjq2zVEkZPE2ZA8QXSS", 1], [5, 308, 30], [0, 0, 1], [-153, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "PLACEHOLDER_LABEL", 9, [[54, "<PERSON><PERSON>i dung tin nhắn ...", 24, 30, false, false, 1, 1, -164, 37]], [0, "34l/zWJmZCBLhe3kbqeVGm", 1], [4, 4290493371], [5, 308, 30], [0, 0, 1], [-153, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [56, null, 0, 24, 30, "<PERSON><PERSON>i dung tin nhắn ...", 24, 255, 6, 9, [4, 4294967295], [4, 4290493371], [[8, "4ffb0Clea9C6rf5rVISctZ3", "editingReturn", 1]]], [1, "sprite", 18, [[18, 2, false, -165, 39]], [0, "ea+hmexCZKR71nK0w/V5jY", 1], [5, 50, 50], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 19, [[18, 2, false, -166, 40]], [0, "31MsejLAdFAITBaO2A/gl5", 1], [5, 82, 46], [0, 3, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 0.9, 3, 19, [[8, "4ffb0Clea9C6rf5rVISctZ3", "sendChatClicked", 1]], [4, 4294967295], [4, 4294967295], 19]], 0, [0, 7, 1, 0, 8, 11, 0, 9, 2, 0, 10, 70, 0, 11, 67, 0, 12, 57, 0, 0, 1, 0, 0, 1, 0, -1, 5, 0, 0, 2, 0, -1, 3, 0, -2, 25, 0, -3, 26, 0, -4, 27, 0, -5, 28, 0, -6, 29, 0, -7, 30, 0, -8, 31, 0, -9, 32, 0, -10, 33, 0, -11, 34, 0, -12, 35, 0, -13, 36, 0, -14, 37, 0, -15, 38, 0, -16, 39, 0, -17, 40, 0, -18, 41, 0, -19, 42, 0, -20, 43, 0, -21, 44, 0, -22, 45, 0, 0, 3, 0, 4, 3, 0, 0, 3, 0, 0, 4, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, -6, 17, 0, -7, 58, 0, -8, 59, 0, -9, 60, 0, -10, 61, 0, -11, 62, 0, -12, 63, 0, -1, 10, 0, -2, 20, 0, -3, 11, 0, -5, 46, 0, -6, 18, 0, -7, 19, 0, 0, 6, 0, -1, 48, 0, -2, 50, 0, -3, 52, 0, -4, 54, 0, -1, 56, 0, -2, 57, 0, -1, 47, 0, -2, 21, 0, 0, 8, 0, 13, 53, 0, 14, 55, 0, 15, 51, 0, 16, 49, 0, 0, 8, 0, -1, 67, 0, -1, 64, 0, -2, 65, 0, -3, 66, 0, 0, 10, 0, 4, 10, 0, 0, 10, 0, -2, 23, 0, 0, 12, 0, 4, 12, 0, 0, 12, 0, 0, 13, 0, 4, 13, 0, 0, 13, 0, 0, 14, 0, 4, 14, 0, 0, 14, 0, 0, 15, 0, 4, 15, 0, 0, 15, 0, 0, 16, 0, 4, 16, 0, 0, 16, 0, 0, 17, 0, 4, 17, 0, 0, 17, 0, 4, 18, 0, 0, 18, 0, -1, 68, 0, -1, 70, 0, -1, 69, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, -1, 22, 0, 0, 22, 0, 0, 23, 0, -1, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, -1, 49, 0, -1, 51, 0, -1, 53, 0, -1, 55, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 68, 0, 0, 69, 0, 17, 1, 2, 6, 5, 4, 6, 24, 6, 6, 8, 7, 6, 11, 8, 6, 47, 9, 6, 46, 166], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 51, 53, 55], [2, 2, 2, 2, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 3, 2, 2, 2, -1, -2, 3, 3, 2, 3], [12, 13, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 14, 15, 16, 17, 18, 1, 1, 19, 1]]]]