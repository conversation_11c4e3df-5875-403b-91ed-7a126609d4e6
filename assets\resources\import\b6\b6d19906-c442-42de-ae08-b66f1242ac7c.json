[1, ["ecpdLyjvZBwrvm+cedCcQy", "adw94Z+hpN57wutNivq8Q5", "7a/QZLET9IDreTiBfRn2PD", "d79u0ic5lA35QmzTl8BuFb", "c7NEvMzpdJLL2QqEd7wquk", "33HOamjjFPGoj/1rwHcNWy", "70yw8sSdpPmbxzUePlq3nV", "8djKvpJ/RFI511Fu754gdp", "25lXFGdhVNoY+tJlNNcovK", "4794hjQyZIRqe0TkzLY0Dm", "76XIlclNBJFY6dbyGQN/Jp", "b1FdnLn49AQY9AUBmOdLZD", "72hOUIvdtMLZ0oT88tRKh8", "5ckbco7cROAJ92hw6PR5eT", "cfHorq5BdJ/rBYfn1QuE8B", "c7q48Cmx1IdrgrV1t+Nv9P", "017Jn3Zv1Ft7hygdjpaSoK", "3az61Xs/BLiL9VslJe/sh/", "4dLE+3eBVDo4ek5WOPA6Nd", "acFdLSqs5JlJ1TNSJFYKrF", "18NdumZWRPqZ53dh/xvJNT", "e9n5EDcM5PyLtL/+6RE0ug", "0dK2GZQftMAJSflgT3a1uS", "dcv+qk7VJNqoCYld2H4Dl+", "adLyWIcE1J25XKD9KY15qP", "e8pNQ7hsFIb6dnOQEaht94", "a2y8HlDuBFprXqOjAISrIL", "65STJRaOhGZJTO2AQIt2zc", "b8zQpy881JmI1jvkWlf/wI", "95WbbJ1s1OVZEDJYugKIYU", "9cttih6ptF1YNT70ex7JDf", "f9tHm/pONLTaY6oZhd6Kkf", "04RG9FNyhJC4qjXfPNPrFd", "9eGy9DOGtPnbUtHULUTdHs", "f3xHKu65lDfYJHatY5gJPi", "a9N7vh9ElPmoznLYYiAor8", "f34FfYuqxEpb6/rUJofQ79", "05ccdA/lFHJ51QgL8m0jUr", "84jtU4t1hB57hyTgi5AiE9", "4avT+OMIZKv7Owwsjab01U", "3f5W4Ja0BCtZza3INwiXIi", "3eR9OaKR1HSqQaMWBJ5p46", "63FD8oR9dI5YgChkGQbxRy", "1aivrxy6lMSaMpVgci4MIu", "88mZGpAzlPt6jFrB9epHti", "01xtkNTo1M6qkOD+J5h70z", "8cpi5YpNRMzqf5CV4gyTvo", "bbrDBQOvlCBYBb6UsTyg3Y", "eekOD+scxFU6BVQHjDvDg3", "30oCoT79NPlaArbZriOeDd", "0cd7+nM/1MtbO/ulXB1C0+", "2bR9PhMKVHr5zpJyC/Epr+", "0djVAmtcRJVrJ4IDb9Z0ap", "04zbv1Do5ChbWJ9RZSU2Kl", "27gFHRJAVKHLzLr1LffIBb", "84tz15totHH7QcyvSgUEvp", "10Mj8lTdFMw7gojN+ocjP3", "8eN2mEk+pNh5fLuHFlP9HD", "1dYmPwNBhGmIMTegWgi0Jr", "3eqL4b0z1MBKN9IGCdvwdy", "6dKSD8Ho9BJ7/YrGmIf4lV", "26/AMdqIFF7b89VrUbHoDR", "d3IQKeoVxErroOr7kB9j35", "820NVNJgdPhYRO06x43C4a", "19I2CcCehLdKRsxFW+q4X3", "01+EGJXJZBwpQrTtZokQv0", "63HZc7yaRCe7kN2Rz6iOYh", "9akGI5o0hGcqQCdwXR8AV8", "fbEroGkVVI17RIFFQtv5yw", "a2h2r7bVBN1oSsWBZ+3Bd/", "47N8HFfRtNBqMgZs6zs35V", "16J8WgNMNN/qJXnSL5cP93", "423SiwKMFM7ZkBRQdMsV69", "cbfaQ80SROvrmr4Qp1wzGi", "b1rganKHZJ7ZkdX/bEUNVD", "14x1ukcCZMaZQ1b5maeZwq", "07GG4UZnlGq4JsQQFgTH8D", "89VhpQ9B5O/ogrFe3ru5BE", "86GU5dZQRMSrSR/RQv4OJ1", "c8RJIZXZpN+Yd4oTOGPN8L", "daKpvpUuVPL4eaxgZ5E8Pd", "edJerO6hxOfrVNIRg0SQyB", "c38QEVCEVBGKMFwYZ6HPv9", "9fRZB/kK5HgplLeaf5QRHg", "2dLXmF9tlJjbSrVDltiTRe", "29vP9xnQ9JNqs18Rarvm5f", "aaZSndG4ZKgq3gcgFB1+7/", "f9BkCPX7xM2rtYFacHyy0P", "11uYTZYC9NbqJaTRjhRD6o", "a33UZXc/5Pzo5TFjSuAxtE", "34/8X7mWVP1Z4A/127Rizt", "365vES89JFXKJeJPD0RZg1", "fdNoodJKVLj4dF1TLppv2g", "2cWB/vWPRHja3uQTinHH30", "a6BLW8ML9DWqqQhW7u/S55", "49zkABBnZPsIWSMAAiJ4w4", "30oZv4Nw9Kr7gUaHONCYxb", "ccN4ASiitC95aD9Lut28Yz", "f0jwjWQBZMJoR/9qG8ndYb", "d54i3DyU5PaKB23nOxWT+K", "62amWQo4dC4aw9mkVDyd3b", "12vhjkHjJKgbFkKU0/P8so", "46AAUWYcVLx5fYyHyU6Iqw", "3cLHlXsR9FRYI3aYzY5SU+", "e9QEAWXitMJo1Kq9StqWcl", "d9BVwzrUJHhJTltdQR6JUg", "78A4GnHoBPvajYliBao8lw", "d8AtT/UAREqJKjDQvui2Rt", "7dFN9M5VhLBbPjJLZpEK6L", "dcHQkkUypAiJx24P65zHZM", "a2tBXzjmRHWIetS1zkxuiC", "01D9NMEaVMr4GwWXkSOq5a", "9bv1VQif1ExpC7CCx72qDT"], ["node", "_spriteFrame", "_N$file", "_parent", "_N$target", "_N$skeletonData", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_file", "_clip", "_defaultClip", "_normalMaterial", "spriteSound", "animation", "prefabChat", "sfAvatarDef", "bmfWin", "bmfLose", "prefab", "prefabHelp", "prefabHistory", "prefabTop", "prefabGroupUser", "root", "nodeLe3", "nodeLe2", "nodeLe1", "nodeChan3", "nodeChan2", "nodeChan1", "nodeLe", "nodeChan", "animR<PERSON>ult", "animationBat", "nodeDia", "nodeBatNan", "lbTotalUserWin", "lbTotalUser", "progressTimer", "lbInfo", "lbTimer", "lbSID", "nodeParentChat", "nodeRegisterLeave", "spriteBack", "spriteNan", "btnRepeat", "btnX2", "nodeParentChip", "spriteMusic", "nodeOffset", "lbThreeDown", "lbThreeUp", "lbOdd", "lbFourDown", "lbFourUp", "lbEven", "nodeOddTemp", "nodeEvenTemp", "nodeParent", "chipBet", "chipSelect", "musicBackground", "lbMessage", "data"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_parent", "_components", "_contentSize", "_children", "_trs", "_anchorPoint", "_eulerAngles", "_color"], 0, 4, 1, 9, 5, 2, 7, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_enabled", "_isTrimmedMode", "_type", "_fillRange", "_srcBlendFactor", "node", "_materials", "_spriteFrame", "_fillCenter"], -3, 1, 3, 6, 5], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_N$overflow", "_lineHeight", "_enableWrapText", "_spacingX", "node", "_materials", "_N$file"], -6, 1, 3, 6], ["cc.Node", ["_name", "_active", "_parent", "_prefab", "_components", "_contentSize", "_trs", "_color", "_children"], 1, 1, 4, 2, 5, 7, 5, 9], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_N$interactable", "node", "clickEvents", "_N$target", "_N$normalColor", "_N$pressedColor", "_N$disabledColor", "_normalMaterial"], 0, 1, 9, 1, 5, 5, 5, 6], ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_anchorPoint"], 0, 12, 4, 5, 7, 1, 2, 5], ["sp.Skeleton", ["defaultSkin", "defaultAnimation", "_preCacheMode", "_animationName", "premultipliedAlpha", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_enabled", "_N$spacingX", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_trs", "_contentSize"], 2, 1, 2, 2, 4, 7, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.AudioSource", ["preload", "_loop", "node"], 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Camera", ["_clearFlags", "_depth", "node"], 1, 1], ["8e9baQKokRGs7BtgwmkMF5b", ["node", "musicBackground", "chipSelect", "chipBet"], 3, 1, 1, 1, 1], ["bd7bcGYD4dKrYuCQu3t/0Cl", ["node"], 3, 1], ["fde5fYTdUdLFaHQ7QSWDYdb", ["node"], 3, 1], ["f92cbvNs3pBuIDcZJI7cvrJ", ["node"], 3, 1], ["8eea9Z1+FZF679kKAC0dEw+", ["node", "lbSID", "lbName"], 3, 1, 1, 1], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1], ["<PERSON><PERSON>", ["vertical", "brake", "elastic", "bounceDuration", "_N$content", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node"], -4, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["98e8czi68lDZ4efW5MOpHZI", ["node", "animation", "nodeParent", "nodeEvenTemp", "nodeOddTemp", "lbEven", "lbFourUp", "lbFourDown", "lbOdd", "lbThreeUp", "lbThreeDown", "sfDots"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3], ["cc.ParticleSystem", ["_enabled", "_dstBlendFactor", "totalParticles", "emissionRate", "life", "lifeVar", "angleVar", "startSize", "startSizeVar", "endSize", "startSpin", "endSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "radialAccel", "node", "_materials", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file", "_spriteFrame"], -14, 1, 3, 8, 8, 8, 8, 5, 5, 6, 6], ["8457bzXi+RCFrWEyccPy/PF", ["touchParent", "node"], 2, 1], ["<PERSON>.<PERSON>", ["node", "_N$content"], 3, 1, 1], ["34c4fFXj/hAgrtv/y80/0lh", ["node"], 3, 1], ["832cdd6gThBUZ3q0q4hjRy5", ["messWinPosY", "node", "nodeMessage", "lbMessage"], 2, 1, 1, 1], ["ad402R7yshJsYOag7Xuj6+9", ["node", "animation", "nodeOffset", "spriteSound", "spriteMusic", "sfSounds", "sfMusics"], 3, 1, 1, 1, 1, 1, 3, 3], ["bffa5sppmhGT6sKdEShtvSs", ["node", "nodeBatNan", "nodeDia", "animationBat", "spriteVis", "sfVis", "animR<PERSON>ult", "nodeChan", "nodeLe", "nodeChan1", "nodeChan2", "nodeChan3", "nodeLe1", "nodeLe2", "nodeLe3"], 3, 1, 1, 1, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["6ce4al5+RxNhbewnDp3Aio6", ["node", "lbSID", "lbTimer", "lbInfo", "progressTimer", "lbTotalUser", "lbTotalUserWin", "xxPlayers"], 3, 1, 1, 1, 1, 1, 1, 1, 2], ["7fd86ILtVNML7Ljmj3x5HMC", ["node", "spriteSound", "sfSounds", "spriteBack", "nodeRegisterLeave", "nodeParentChat", "prefabChat"], 3, 1, 1, 3, 1, 1, 1, 6], ["47fe6sINhNMZZqSErpdFUwf", ["node", "nodeParentChip", "btnBetVals", "btnChips", "btnX2", "btnRepeat", "spriteNan", "lbTotalBets", "lbTotalUserBets"], 3, 1, 1, 2, 2, 1, 1, 1, 2, 2], ["a9e52Aa5P9MIq9DZ/P5xyek", ["node", "sfChips", "sfNans", "sfBacks", "sfDots", "sfAvatarDef", "bmfWin", "bmfLose"], 3, 1, 3, 3, 3, 3, 6, 6, 6], ["24b98EDPEhGSIEI/3GsBrAd", ["node", "prefab"], 3, 1, 6], ["180660VlHxDu5IXInXbxCct", ["node", "prefabHelp", "prefabHistory", "prefabTop", "prefabGroupUser"], 3, 1, 6, 6, 6, 6], ["cc.<PERSON>", ["node", "_designResolution"], 3, 1, 5], ["2a33dcxF+NBW6IzkrkZ/0RB", ["node", "designResolution"], 3, 1, 5]], [[10, 0, 1, 2], [0, 0, 4, 7, 5, 3, 6, 8, 2], [0, 0, 4, 5, 3, 6, 8, 2], [1, 6, 7, 8, 1], [1, 0, 2, 6, 7, 8, 3], [3, 0, 2, 4, 3, 5, 6, 2], [0, 0, 4, 5, 3, 11, 6, 2], [0, 0, 1, 4, 5, 3, 6, 8, 3], [2, 4, 0, 1, 2, 3, 9, 10, 6], [12, 0, 1, 1], [7, 0, 1, 3, 3], [6, 0, 1, 2, 4, 3, 5, 6, 7, 6], [4, 1, 0, 3, 4, 6, 7, 8, 5, 3], [7, 0, 1, 2, 3, 4], [0, 0, 1, 4, 5, 3, 6, 3], [0, 0, 1, 4, 7, 5, 3, 6, 8, 3], [1, 0, 6, 7, 8, 2], [1, 3, 0, 6, 7, 8, 3], [0, 0, 1, 4, 7, 3, 3], [5, 0, 8, 3, 4, 5, 6, 2], [1, 6, 7, 1], [8, 0, 1, 3, 5, 6, 4], [3, 0, 2, 4, 3, 7, 5, 2], [3, 0, 1, 2, 4, 3, 7, 5, 6, 3], [1, 0, 2, 6, 7, 3], [1, 3, 0, 2, 6, 7, 8, 4], [2, 4, 0, 1, 2, 3, 9, 6], [2, 4, 0, 7, 1, 2, 3, 5, 9, 10, 8], [2, 4, 0, 1, 2, 3, 5, 9, 10, 11, 7], [2, 4, 0, 6, 1, 2, 3, 5, 9, 10, 11, 8], [8, 2, 0, 1, 5, 6, 4], [18, 0, 1], [19, 0, 1], [20, 0, 1, 2, 1], [0, 0, 4, 3, 6, 8, 2], [0, 0, 1, 4, 5, 3, 3], [5, 0, 7, 3, 4, 5, 6, 2], [4, 2, 0, 3, 4, 6, 7, 8, 5, 3], [4, 1, 0, 3, 6, 5, 3], [2, 0, 6, 1, 2, 3, 9, 10, 6], [25, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 18], [0, 0, 4, 5, 3, 6, 2], [9, 0, 1, 2, 3, 4, 6, 5, 2], [0, 0, 4, 7, 3, 8, 2], [5, 0, 3, 4, 5, 6, 2], [1, 1, 6, 8, 2], [4, 0, 3, 4, 6, 5, 2], [0, 0, 7, 5, 3, 6, 8, 2], [0, 0, 4, 7, 5, 3, 8, 2], [3, 0, 2, 4, 3, 2], [1, 1, 6, 7, 8, 2], [4, 3, 4, 1], [0, 0, 4, 7, 3, 2], [0, 0, 4, 7, 5, 3, 6, 2], [0, 0, 4, 3, 2], [3, 0, 2, 4, 3, 7, 5, 6, 2], [3, 0, 1, 2, 4, 3, 5, 6, 3], [5, 0, 1, 7, 3, 4, 5, 6, 3], [11, 0, 2, 2], [4, 0, 3, 4, 5, 9, 2], [13, 0, 1, 2, 2], [14, 0, 2], [0, 0, 1, 4, 5, 3, 8, 3], [0, 0, 4, 7, 5, 3, 2], [0, 0, 4, 5, 3, 6, 8, 10, 2], [0, 0, 4, 7, 5, 3, 6, 9, 2], [0, 0, 7, 5, 3, 6, 9, 2], [0, 0, 4, 3, 6, 9, 8, 2], [0, 0, 1, 4, 7, 5, 3, 6, 8, 10, 3], [0, 0, 7, 5, 3, 6, 9, 8, 2], [0, 0, 2, 7, 5, 3, 6, 8, 3], [3, 0, 2, 8, 3, 2], [3, 0, 1, 2, 4, 3, 5, 3], [3, 0, 2, 4, 3, 5, 2], [5, 0, 7, 8, 3, 4, 5, 9, 6, 2], [5, 0, 2, 8, 3, 4, 5, 3], [9, 0, 1, 2, 3, 4, 5, 2], [15, 0, 1, 2, 3], [10, 1, 1], [11, 1, 0, 2, 3], [16, 0, 1, 2, 3, 1], [1, 1, 3, 0, 6, 7, 8, 4], [1, 3, 0, 4, 6, 7, 9, 4], [1, 1, 6, 7, 2], [1, 1, 0, 2, 6, 7, 4], [1, 0, 6, 7, 2], [1, 1, 5, 0, 2, 6, 7, 5], [6, 0, 1, 2, 3, 5, 6, 7, 5], [6, 0, 1, 2, 4, 3, 5, 6, 6], [4, 0, 3, 2], [4, 0, 3, 4, 5, 2], [4, 0, 3, 4, 6, 2], [2, 4, 0, 1, 8, 2, 3, 5, 9, 10, 11, 8], [2, 4, 0, 1, 2, 3, 9, 10, 11, 6], [2, 4, 0, 6, 1, 2, 3, 9, 10, 7], [2, 0, 1, 2, 3, 9, 10, 5], [2, 4, 0, 6, 7, 1, 2, 3, 5, 9, 10, 9], [7, 0, 1, 3], [8, 0, 1, 4, 5, 6, 4], [12, 0, 1, 2, 1], [17, 0, 1], [21, 0, 1, 2, 2], [13, 1, 2, 1], [22, 0, 1, 2, 3, 4, 5, 6, 7, 8], [23, 0, 1], [24, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [26, 0, 1, 2], [27, 0, 1, 1], [28, 0, 1], [29, 0, 1, 2, 3, 2], [30, 0, 1, 2, 3, 4, 5, 6, 1], [31, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 1], [32, 0, 1, 2, 3, 4, 5, 6, 7, 1], [33, 0, 1, 2, 3, 4, 5, 6, 1], [34, 0, 1, 2, 3, 4, 5, 6, 7, 8, 1], [35, 0, 1, 2, 3, 4, 5, 6, 7, 1], [36, 0, 1, 1], [37, 0, 1, 2, 3, 4, 1], [38, 0, 1, 1], [39, 0, 1, 1]], [[61, "xocxocView"], [47, "xocxocView", [-71, -72, -73, -74, -75, -76], [[111, -18, -17, -16, -15, [-11, -12, -13, -14], [364, 365], -10, -9, -8, -7, -6, -5, -4, -3, -2], [112, -32, -31, -30, -29, -28, -27, -26, [-19, -20, -21, -22, -23, -24, -25]], [113, -37, -36, [366, 367], -35, -34, -33, 368], [114, -65, -64, [-58, -59, -60, -61, -62, -63], [-53, -54, -55, -56, -57], -52, -51, -50, [-44, -45, -46, -47, -48, -49], [-38, -39, -40, -41, -42, -43]], [115, -66, [369, 370, 371, 372, 373], [374, 375], [377, 378], [379, 380, 381, 382, 383], 376, 384, 385], [116, -67, 386], [117, -68, 387, 388, 389, 390], [118, -69, [5, 1560, 730]], [119, -70, [5, 1560, 730]]], [78, -1], [5, 1560, 730], [780, 365, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "game", 1, [-77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92, -93, -94, -95, -96, -97, -98, -99, -100], [0, "c6aSec7WxF34NuVtLU85eM", 1]], [1, "layout-totalBet", 2, [-102, -103, -104, -105, -106, -107, -108, -109, -110, -111, -112, -113], [[30, false, 1, 1, -101, [5, 676, 218]]], [0, "33X5vkyOFAhquRBvrglODz", 1], [5, 676, 218], [0, -45.384, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "popup", [-117, -118, -119, -120, -121], [[9, -114, [35, 36]], [16, 2, -115, [37], 38], [100, -116]], [0, "6d/nMuMJ1IX7R7y3opcOJY", 1], [5, 330, 127], [-1019.57, 247.361, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "bg<PERSON><PERSON>", 0, [-129, -130, -131, -132], [[-122, [3, -123, [358], 359], [110, -128, -127, -126, -125, -124, [360, 361], [362, 363]]], 1, 4, 4], [0, "06EbRWBaRJYIxy2MR2/TF2", 1], [5, 672, 415]], [19, "btnPos copy", [-135, -136, -137, -138, -139, -140, -141], [[-133, [9, -134, [61, 62]]], 1, 4], [0, "cdKS2387lJhKenrpD8O/BN", 1], [5, 100, 100], [-460.193, 113.887, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnPos copy", [-144, -145, -146, -147, -148, -149, -150], [[-142, [9, -143, [83, 84]]], 1, 4], [0, "79k5C66FlOA5qQ6owtVC3T", 1], [5, 100, 100], [-537.571, -16.698, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnPos copy", [-153, -154, -155, -156, -157, -158, -159], [[-151, [9, -152, [105, 106]]], 1, 4], [0, "370XFqG+JFVIx1ALFVJTBx", 1], [5, 100, 100], [-596.324, -183.39, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnPos copy", [-162, -163, -164, -165, -166, -167, -168], [[-160, [9, -161, [128, 129]]], 1, 4], [0, "39Gck5yOZKTpGr+rW76bkG", 1], [5, 100, 100], [462.707, 113.314, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnPos copy", [-171, -172, -173, -174, -175, -176, -177], [[-169, [9, -170, [150, 151]]], 1, 4], [0, "9a8E5v6sxFO48rlEWgch4V", 1], [5, 100, 100], [546.901, -11.326, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnPos copy", [-180, -181, -182, -183, -184, -185, -186], [[-178, [9, -179, [173, 174]]], 1, 4], [0, "56QOoeSItAaY+wYKAk5h+3", 1], [5, 100, 100], [609.719, -154.431, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "btnPos", [-189, -190, -191, -192, -193, -194, -195], [[-187, [9, -188, [195, 196]]], 1, 4], [0, "7fDTFamXBJkKLpJJgTAX9E", 1], [5, 100, 100], [-385.7, -329.791, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "table", 2, [-197, -198, -199, -200, -201, -202], [[3, -196, [24], 25]], [0, "2fPzKgNhxA7KoZwiL98PeJ", 1], [5, 1560, 1170]], [43, "user-pos", 2, [6, 7, 8, 9, 10, 11, 12], [0, "75GugQbFhM4KCeFvnrBkos", 1], [0, 67, 0, 0, 0, 0, 1, 1, 1, 1]], [66, "bg", [-204, -205, -206, -207, -208, -209], [[3, -203, [229], 230]], [0, "35+ATcVrtCZYtdIgUfoeiT", 1], [5, 318, 121], [0, 0.5, 1]], [1, "layout-count", 15, [-211, -212, -213, -214, -215, -216], [[98, 1, 2, 3, -210, [5, 30, 327]]], [0, "79ynJXePtPGrF6UsTqS6wI", 1], [5, 30, 327], [-58, -59.5, 0, 0, 0, 0, 1, 1, 1, 1]], [69, "content", [-218, -219, -220, -221, -222], [[21, 1, 1, 10, -217, [5, 540, 120]]], [0, "afdflZ3PdCPp6+4gmWoS+y", 1], [5, 540, 120], [0, 0, 0.5], [-215, 3.522, 0, 0, 0, 0, 1, 1, 1, 1]], [71, "vt_xeng", 13, [[34, "chan", -223, [0, "73R9KtIXhEVLWETqHADoXj", 1], [5, 80, 50], [-224.194, 59.822, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "lẻ", -224, [0, "9aQr7ksT9KUaXCJ0BbzZ3W", 1], [5, 80, 50], [237, 88, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "4_do", -225, [0, "0brWpO8X9FWpicb4laf6xR", 1], [5, 80, 40], [-277, -78.434, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "4_trang", -226, [0, "83Uv2XqpxHTrv8uXAaS/9L", 1], [5, 80, 40], [-84, -78.434, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "3_do", -227, [0, "a8steAaPNGxZC22oIPrZxo", 1], [5, 80, 40], [286, -78.434, 0, 0, 0, 0, 1, 1, 1, 0]], [34, "3_trang", -228, [0, "192l6C/5JK/72ltPS3OP2S", 1], [5, 80, 40], [98, -78.434, 0, 0, 0, 0, 1, 1, 1, 0]]], [0, "beoIxpUjNErZLNXb0rXz4x", 1]], [48, "layout-input", 13, [-230, -231, -232, -233], [[30, false, 1, 1, -229, [5, 676, 218]]], [0, "a32CAdqN9IyIm+6PZfaJpt", 1], [0, 49.968, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "btnChan1", [[[4, 2, false, -234, [12], 13], -235, [38, 0.95, 3, -237, [4, 4292269782], -236]], 4, 1, 4], [0, "4a0qGVj/VJcJvVroEQTlB1", 1], [5, 175, 138], [48.452, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "btnChan2", [[[4, 2, false, -238, [14], 15], -239, [38, 0.95, 3, -241, [4, 4292269782], -240]], 4, 1, 4], [0, "3fVduFq4lHtr/IAoDWq6cS", 1], [5, 162, 138], [239.537, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "btnChan", 19, [[[4, 2, false, -242, [16], 17], -243, [38, 0.97, 3, -245, [4, 4292269782], -244]], 4, 1, 4], [0, "b8Gd/Nx6BG4pI6x6sCbL6Q", 1], [5, 188, 178], [-233.463, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "btnLe", 19, [[[4, 2, false, -246, [18], 19], -247, [38, 0.97, 3, -249, [4, 4292269782], -248]], 4, 1, 4], [0, "eevLBlCSpMh7dZnC/6iVL2", 1], [5, 188, 176], [239.057, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "btnLe1", [[[4, 2, false, -250, [20], 21], -251, [38, 0.95, 3, -253, [4, 4292269782], -252]], 4, 1, 4], [0, "4676M7R4JMWImH0iLKPzyU", 1], [5, 175, 138], [-42, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "btnLe2", [[[4, 2, false, -254, [22], 23], -255, [38, 0.95, 3, -257, [4, 4292269782], -256]], 4, 1, 4], [0, "fdJPUxXztK4J3P/bGHG24p", 1], [5, 162, 138], [-230.449, -103, 0, 0, 0, 0, 1, 1, 1, 1]], [74, "soicauView", 2, [-270], [[[105, -268, -267, -266, -265, -264, -263, -262, -261, -260, -259, -258, [234, 235, 236, 237, 238]], -269], 4, 1], [0, "5frndXmlhLAYkUHBooS7L2", 1], [5, 318, 160], [0, 0.5, 1], [463.293, 360.742, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "scrollview", [-273], [[107, -271, 17], [108, -272]], [0, "c6VgFs6opOF4zNnKyrJv4u", 1], [5, 460, 150], [0, 24.569, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "groupUser", 2, [-277, -278], [[3, -274, [342], 343], [46, 3, -276, [[10, "180660VlHxDu5IXInXbxCct", "createGroupUserView", 1]], [4, 4292269782], -275]], [0, "9d/P3YxRtCyasWlerVqfpv", 1], [5, 115, 115], [680.373, -226.243, 0, 0, 0, 0, 1, 1, 1, 1]], [63, "audioPool", 1, [-283, -284, -285], [[80, -282, -281, -280, -279]], [0, "bbdZEwpotFu7sezaume5SO", 1]], [19, "progressBar", [-288, -289], [[-286, [16, 0, -287, [209], 210]], 1, 4], [0, "67AbOFzD1P4p+o1TxGzON6", 1], [5, 258, 33], [0, 1.163, 0, 0, 0, 0, 1, 1, 1, 1]], [76, "result", 2, [-291, -292, -293], [-290], [0, "3bH5Jr+KJPK57MqofTI9tr", 1], [0, -25.727, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "le_win", 31, [-295, -296], [[83, false, -294, [254]]], [0, "8dDxAMzgZOE7VOm2+i97K8", 1], [5, 61, 61], [99, -90, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chan_win", 31, [-298, -299], [[84, false, 2, false, -297, [270]]], [0, "45pb1Cgl1A+bqEqnEoYRcJ", 1], [5, 163, 66], [-93, -89, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "vi", 2, [-300, -301, -302, -303], [0, "51h9DPDHNEEpu+VYy8V9ze", 1], [2.766, 161.751, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "chipcuoc", 2, [-304, 27, -305, -306], [0, "f8Z3reYlhAnaD5D1G46tbp", 1], [0, -324.143, 0, 0, 0, 0, 1, 1, 1, 1]], [70, "offset-message", 0, [-308, -309], [[86, false, 768, 2, false, -307, [348]]], [0, "61Z3liUFRPw7JAu75ZIPpp", 1], [5, 451, 80], [0, -49, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnMoney1", 13, [-311, -312], [[3, -310, [10], 11]], [0, "7aNG+NrlNNe6PE7kxbj5Fw", 1], [5, 170, 40], [210.754, 227.49, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "chan", 19, [20, 21], [[30, false, 1, 2, -313, [5, 150, 214]]], [0, "b7+Na494xPEJt6i19CCmyQ", 1], [-330, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [48, "le", 19, [24, 25], [[30, false, 1, 2, -314, [5, 150, 214]]], [0, "8fO+OxAv5Cwa01Ne5Bym2O", 1], [330, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 2, [-317], [[12, 0.9, 3, -316, [[10, "bd7bcGYD4dKrYuCQu3t/0Cl", "menuOpenClicked", 4]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -315]], [0, "0bLvHyvCtMGYdP8KT0Ssd1", 1], [5, 80, 80], [-686.003, 268.167, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHelp", 4, [-320], [[12, 0.9, 3, -319, [[10, "7fd86ILtVNML7Ljmj3x5HMC", "historyClicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -318]], [0, "40BG4Ux6xEOr7cveO7LIaV", 1], [5, 80, 70], [-23.059, 18.79, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "New Node copy", false, 4, [-323], [[90, 3, -322, [[97, "9cd33CjIU9EX5dsxAdMzOYE", "addComponent"]], -321]], [0, "2bfO/nX4tGc6y+aXa9hQNS", 1], [5, 80, 80], [-23.58, 20.211, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "btnSound", false, 2, [-326], [[12, 0.9, 3, -325, [[10, "7fd86ILtVNML7Ljmj3x5HMC", "soundClicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -324]], [0, "447jGM7nBCoqnIHt33BR1b", 1], [5, 80, 70], [588, 306, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 6, [-328, -329], [[3, -327, [51], 52]], [0, "0aMQzDJqtFMrKBxJSPoEe2", 1], [5, 153, 55], [-0.268, -47.446, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "layout-name copy", 44, [-331, -332], [[21, 1, 1, 2, -330, [5, 130, 50]]], [0, "1dOYeB76NDmLtY7LZ4jy+N", 1], [5, 130, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 7, [-334, -335], [[3, -333, [74], 75]], [0, "24VqZkEkJDWKvAecxk1pWT", 1], [5, 153, 55], [0, -44.708, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "layout-name copy", 46, [-337, -338], [[21, 1, 1, 2, -336, [5, 130, 50]]], [0, "faYORbaV5Hxa04pfuR3LRu", 1], [5, 130, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 8, [-340, -341], [[3, -339, [96], 97]], [0, "bdUqq7xPlEtYo7zz62otjl", 1], [5, 153, 55], [0, -45.9, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "layout-name", 48, [-343, -344], [[21, 1, 1, 2, -342, [5, 130, 50]]], [0, "a9ukAyWodJ9qN0XlmPmoME", 1], [5, 130, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 9, [-346, -347], [[3, -345, [118], 119]], [0, "08q6N49ylPb7IewhhcTcMk", 1], [5, 153, 55], [0, -46.521, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "layout-name copy", 50, [-349, -350], [[21, 1, 1, 2, -348, [5, 130, 50]]], [0, "97lzyqXTFG7J4YYQs3r7Bu", 1], [5, 130, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 10, [-352, -353], [[3, -351, [141], 142]], [0, "8f2xvzcj1BdLteWQ5TzMZ1", 1], [5, 153, 55], [0, -44.218, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "layout-name copy", 52, [-355, -356], [[21, 1, 1, 2, -354, [5, 130, 50]]], [0, "baZjChcadMO7AMzuRVdv/p", 1], [5, 130, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 11, [-358, -359], [[3, -357, [163], 164]], [0, "5aHcu8XPVAhqOeoLPpE/Co", 1], [5, 153, 55], [0, -45.947, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [1, "layout-name copy", 54, [-361, -362], [[21, 1, 1, 2, -360, [5, 130, 50]]], [0, "faNu1WLI1BPa7MOy8IXOFs", 1], [5, 130, 50], [0, 11.8, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ava_money", 12, [-364, -365], [[3, -363, [186], 187]], [0, "72vvZF10lFv45Pj5Qpcg/K", 1], [5, 153, 55], [-1.382, -58.428, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [1, "layout-name copy", 56, [-367, -368], [[21, 1, 1, 2, -366, [5, 130, 50]]], [0, "cbIcErRExL3pyDyEdSfm9q", 1], [5, 130, 50], [0, 50, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bgLeaveRoom", false, 2, [-370], [[17, 1, 0, -369, [199], 200]], [0, "4cj1YpogxDU6hk9oDwCTZP", 1], [5, 200, 40], [-525, 264.774, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "bgInfo", false, 2, [-373], [[81, false, 1, 0, -371, [202], 203], [99, -372, [205], 204]], [0, "68UdHEP+VBeZuQbe57H9Hf", 1], [5, 200, 40], [0, -61.095, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_time", 2, [30, -375], [[3, -374, [212], 213]], [0, "51+CeH7zNPmbDn4j+6XTJc", 1], [5, 274, 41], [-3.546, 3.982, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "scrollview", 15, [-378], [[103, false, 0.1, false, 0.23, null, null, null, -376], [104, -377]], [0, "bdyGIBMFZMfLOGCWorDJNg", 1], [5, 191, 114], [52.9, -59, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Le1", false, 32, [-380], [[4, 2, false, -379, [242], 243]], [0, "69zkvgOelAHYTHNqYGH5Bp", 1], [5, 213, 168], [140.235, 181.528, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "le", 32, [-382, -383], [[30, false, 1, 2, -381, [5, 150, 0]]], [0, "a3Gq9wUilAXIHOi/LC02Ey", 1], [5, 150, 0], [231, 28, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Le2", false, 63, [-385], [[4, 2, false, -384, [247], 248]], [0, "1cXa526OFP56kyMZSgXXgJ", 1], [5, 202, 165], [-42.027, -10.943, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Le3", false, 63, [-387], [[4, 2, false, -386, [252], 253]], [0, "25qn+R/otLCLFme3Cy/gDn", 1], [5, 187, 165], [-231.237, -9.284, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "chan", 33, [-389, -390], [[30, false, 1, 2, -388, [5, 150, 0]]], [0, "86TMOT3JBNvLY2VFDNCSDO", 1], [5, 150, 0], [-237, 27, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Chan2", false, 66, [-392], [[4, 2, false, -391, [258], 259]], [0, "4fnKGvdTpDe7xVcUm7Cg/X", 1], [5, 202, 165], [48.988, -11.666, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Chan3", false, 66, [-394], [[4, 2, false, -393, [263], 264]], [0, "8elDpY5VxHUrAMn2jYAFG3", 1], [5, 187, 165], [240.359, -10.17, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "Chan1", false, 33, [-396], [[4, 2, false, -395, [268], 269]], [0, "12wMPCnV9AMKW9Za3i432I", 1], [5, 213, 168], [-141.414, 177.839, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bat<PERSON><PERSON>", false, 2, [[4, 2, false, -397, [298], 299], [106, null, -398]], [0, "a0+0+mcjVPLqFpBL+vsUJW", 1], [5, 132, 113], [0.194, 120.633, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "btnChat", 2, [-401], [[12, 0.9, 3, -400, [[10, "7fd86ILtVNML7Ljmj3x5HMC", "chatClicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -399]], [0, "d9GT+DkqNCHoYj0XRxvNrz", 1], [5, 80, 70], [-541.455, -309.167, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "btnChip1", 17, [-403, -404], [-402], [0, "82Ytrv+lRKvrPwKRvp6VF3", 1], [5, 100, 100], [50, -0.532, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "btnChip2", 17, [-406, -407], [-405], [0, "526YaaHK1DmIP/+MtXi7BN", 1], [5, 100, 100], [160, -0.532, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "btnChip3", 17, [-409, -410], [-408], [0, "84JJb9HVFDzrZqim+5VeTN", 1], [5, 100, 100], [270, -0.532, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "btnChip4", 17, [-412, -413], [-411], [0, "ab/5o0ChhPJYbCcNqjbVfe", 1], [5, 100, 100], [380, -0.532, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "btnChip5", 17, [-415, -416], [-414], [0, "e5IIi+PStKOIRElZffz1lY", 1], [5, 100, 100], [490, -0.532, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnnextchip", 35, [[16, 2, -417, [328], 329], [59, 3, -419, [[13, "34c4fFXj/hAgrtv/y80/0lh", "switchPageClicked", "1", 27]], -418, 330]], [0, "46I1OPpghEN7MLwCexZ1DQ", 1], [5, 54, 80], [258.638, 21.847, 0, 0, 0, 0, 1, -1, 1, 1]], [2, "btn_prevchip", 35, [[16, 2, -420, [331], 332], [59, 3, -422, [[13, "34c4fFXj/hAgrtv/y80/0lh", "switchPageClicked", "-1", 27]], -421, 333]], [0, "3eNIMIrmVLrbO8lqOoks0W", 1], [5, 54, 80], [-258.362, 21.847, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "btnNan", false, 2, [[-423, [12, 0.9, 3, -425, [[10, "47fe6sINhNMZZqSErpdFUwf", "nanClicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -424]], 1, 4], [0, "57YfmIYBRMnK+za78R4pL7", 1], [5, 81, 83], [575.404, -303.711, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "btnX2", 2, [[[4, 2, false, -426, [335], 336], -427], 4, 1], [0, "0fwrgjBiFGKr0uw51zh1qZ", 1], [5, 186, 69], [503.31, -263.183, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [36, "btnRepeat", 2, [[[4, 2, false, -428, [337], 338], -429], 4, 1], [0, "7ctf4GjdxCNaL7iNDAxPD5", 1], [5, 188, 69], [503.31, -323.944, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [53, "popupSlotsView", 1, [36], [[9, -430, [349]], [109, 0, -432, 36, -431]], [0, "78NgYtLABKcLGlm8vuScHJ", 1], [5, 444, 220]], [2, "btnHistory", 5, [[3, -433, [350], 351], [46, 3, -435, [[10, "180660VlHxDu5IXInXbxCct", "createHistoryView", 1]], [4, 4292269782], -434]], [0, "0dwBnRs/REtb7E49Fuj0sT", 1], [5, 63, 64], [-3, 92, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "btnAward", 5, [[3, -436, [352], 353], [46, 3, -438, [[10, "180660VlHxDu5IXInXbxCct", "createTopView", 1]], [4, 4292269782], -437]], [0, "8cPGEWwoxMuJIpvSmPa267", 1], [5, 63, 63], [-3, 8.5, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "music_on", 5, [[-439, [46, 3, -441, [[10, "ad402R7yshJsYOag7Xuj6+9", "musicClicked", 5]], [4, 4292269782], -440]], 1, 4], [0, "1eYE2rIJBOeY+KH44EpvFu", 1], [5, 81, 81], [-3, -83.5, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [1, "New Node", 13, [-443], [[89, 3, -442]], [0, "57EQcy8yFNf6y1L4/VURj7", 1], [5, 200, 200], [217.701, 278.421, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Node", 4, [-445], [[51, -444, [[10, "7fd86ILtVNML7Ljmj3x5HMC", "backClicked", 1]]]], [0, "9dFByLzG5KDot3de5R4Gl4", 1], [5, 80, 80], [-116.728, 22.72, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Node copy", 4, [-447], [[51, -446, [[10, "7fd86ILtVNML7Ljmj3x5HMC", "helpClicked", 1]]]], [0, "8fdyhdpz5MxIABGWnfeiCt", 1], [5, 80, 80], [81.416, 21.048, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Avatar", 6, [[25, 1, 0, false, -448, [42], 43], [31, -449]], [0, "e5LqpQI7ZM9Z+4YWMErcrT", 1], [4, 4293322470], [5, 86, 86]], [7, "lbWin", false, 6, [[8, "+100K", 30, false, 1, 1, -450, [46]], [9, -451, [47]]], [0, "f4p2arSDVCGJPE/G/RgcRa", 1], [5, 109.69, 40], [1.261, 47.982, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 44, [[28, "10.M", 18, false, 1, 1, 2, -452, [49], 50], [32, -453]], [0, "1bGu0nhMJGLpSulvn3ptjV", 1], [5, 130, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "chat", false, 6, [-454, -455], [0, "97IqWXQZBK04uBkXeGB7xV", 1]], [1, "bubble", 92, [-457], [[17, 1, 0, -456, [59], 60]], [0, "0adcouyTRIl5fmL9J1ylJt", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Avatar", 7, [[25, 1, 0, false, -458, [65], 66], [31, -459]], [0, "34Asr+uHhGrpMGOqSemGzV", 1], [4, 4293322470], [5, 86, 86]], [7, "lbWin", false, 7, [[8, "+100K", 30, false, 1, 1, -460, [69]], [9, -461, [70]]], [0, "c3tEOA9UBJsKsyyKg3rNzx", 1], [5, 109.69, 40], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 46, [[28, "10.M", 18, false, 1, 1, 2, -462, [72], 73], [32, -463]], [0, "9bJlqOu81M+KmlCH8m/cUd", 1], [5, 130, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "chat", false, 7, [-464, -465], [0, "81qgpkEgFPmJT6qNKIqvX1", 1]], [1, "bubble", 97, [-467], [[17, 1, 0, -466, [81], 82]], [0, "cc4Jw1zklBOIS15XbUEbGw", 1], [5, 192.4, 65], [46, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Avatar", 8, [[25, 1, 0, false, -468, [87], 88], [31, -469]], [0, "02vb3kOrdLFKU+jzZHGirY", 1], [4, 4293322470], [5, 86, 86]], [7, "lbWin", false, 8, [[8, "+100K", 30, false, 1, 1, -470, [91]], [9, -471, [92]]], [0, "f1YLti42BJzqRzk62ehEpd", 1], [5, 109.69, 40], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 48, [[28, "10.M", 18, false, 1, 1, 2, -472, [94], 95], [32, -473]], [0, "3eQRNCH3FCtJLeKWHh4WOw", 1], [5, 130, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "chat", false, 8, [-474, -475], [0, "c07HErns1HcpEh5KZ2ydvm", 1]], [1, "bubble", 102, [-477], [[17, 1, 0, -476, [103], 104]], [0, "adr79qBzlCt7sQ+7jHxlkU", 1], [5, 192.4, 65], [46, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Avatar", 9, [[25, 1, 0, false, -478, [109], 110], [31, -479]], [0, "b90uNxzflP76RPryxm30wv", 1], [4, 4293322470], [5, 86, 86]], [7, "lbWin", false, 9, [[8, "+100K", 30, false, 1, 1, -480, [113]], [9, -481, [114]]], [0, "e62MWX349Py5Ww8mhYsGsS", 1], [5, 109.69, 40], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 50, [[28, "10.M", 18, false, 1, 1, 2, -482, [116], 117], [32, -483]], [0, "cdF6Pk5kFH3o3bW+cZniWa", 1], [5, 130, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "chat", false, 9, [-484, -485], [0, "b2ehGGMDFDJ4EIK7kWJa3z", 1]], [1, "bubble", 107, [-487], [[17, 1, 0, -486, [126], 127]], [0, "a2c8Z1y7hKBoJxZgt1QnwT", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Avatar", 10, [[25, 1, 0, false, -488, [132], 133], [31, -489]], [0, "41Y8Rz0MRHT4wBfKm77+Cl", 1], [4, 4293322470], [5, 86, 86]], [7, "lbWin", false, 10, [[8, "+100K", 30, false, 1, 1, -490, [136]], [9, -491, [137]]], [0, "49tGDswuFB+b3WTI1TkiP/", 1], [5, 109.69, 40], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 52, [[28, "10.M", 18, false, 1, 1, 2, -492, [139], 140], [32, -493]], [0, "059Qon+llGoIOK11moZsuj", 1], [5, 130, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "chat", false, 10, [-494, -495], [0, "87TDtJ6QtCGYdsZouMtsuH", 1]], [1, "bubble", 112, [-497], [[17, 1, 0, -496, [148], 149]], [0, "8dk2lFuXtH3r2oH7jQfKD4", 1], [5, 192.4, 65], [-46, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Avatar", 11, [[25, 1, 0, false, -498, [154], 155], [31, -499]], [0, "07OyjWJkFDjZXLw6+XnGES", 1], [4, 4293322470], [5, 86, 86]], [7, "lbWin", false, 11, [[8, "+100K", 30, false, 1, 1, -500, [158]], [9, -501, [159]]], [0, "17pdF7JPhEuoSOtQ9tlWwF", 1], [5, 109.69, 40], [0, 44.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 54, [[28, "10.M", 18, false, 1, 1, 2, -502, [161], 162], [32, -503]], [0, "e1QECAJHZKS647VyxtW/dN", 1], [5, 130, 40], [0, -9.5, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "chat", false, 11, [-504, -505], [0, "0a1s9IC8BIeI6woWsmAY2B", 1]], [1, "bubble", 117, [-507], [[17, 1, 0, -506, [171], 172]], [0, "d3hzZbBENI8aduQEo2U9T3", 1], [5, 192.4, 65], [-50, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Avatar", 12, [[25, 1, 0, false, -508, [177], 178], [31, -509]], [0, "eafXy6J2lB4rYf4jp7rvbA", 1], [4, 4293322470], [5, 110, 110]], [7, "lbWin", false, 12, [[8, "+100", 30, false, 1, 1, -510, [181]], [9, -511, [182]]], [0, "2dRlqmpppLyoAMY1u59YMT", 1], [5, 86.25, 40], [0, 58.2, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "lbChip", 56, [[28, "0", 20, false, 1, 1, 2, -512, [184], 185], [32, -513]], [0, "8dQFdtswBNbpK8njaioC43", 1], [5, 130, 31], [0, -7.486, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "chat", false, 12, [-514, -515], [0, "750g1E2MZEC6UcrVtvgZGZ", 1]], [1, "bubble", 122, [-517], [[17, 1, 0, -516, [193], 194]], [0, "89xeJu+HVBBK2V0Ty+EWb+", 1], [5, 192.4, 65], [0, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [65, "mask", 26, [15], [[60, 0, -518, [231]]], [0, "72c+tATANIL4b4AlAeILh0", 1], [5, 318, 160], [0, 0.5, 1]], [43, "temp", 15, [-519, -520], [0, "6eDmsVWa5DQbuW9NOBv74L", 1], [139, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "dot_Even_number", 125, [[4, 2, false, -521, [220], 221]], [0, "72vQpIIZlF84fsC85Ka3ge", 1], [5, 14, 14], [-0.01, 18.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "dot_Odd_number", 125, [[4, 2, false, -522, [222], 223]], [0, "e20uvCN29HaY7qIqHdGhlh", 1], [5, 15, 15], [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHide", 15, [-525], [[12, 0.9, 3, -524, [[10, "98e8czi68lDZ4efW5MOpHZI", "hideClicked", 26]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -523]], [0, "69pgNZJbZDGa2q3SNouYwJ", 1], [5, 50, 50], [142.6, -131, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Background", 128, [[4, 2, false, -526, [225], 226]], [0, "7axZvOiPpCJbqn3Ri7usUl", 1], [4, 4293322470], [5, 18, 17]], [68, "btnShow", false, 15, [-529], [[12, 0.9, 3, -528, [[10, "98e8czi68lDZ4efW5MOpHZI", "showClicked", 26]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -527]], [0, "fccdGbn7tMKIg3iBHjrcQ6", 1], [5, 50, 50], [142.6, -131, 0, 0, 0, -1, 6.123233995736766e-17, 1, 1, 1], [1, 0, 0, -180]], [6, "Background", 130, [[4, 2, false, -530, [227], 228]], [0, "54LxUOtgBN5rFNnlp/fo3B", 1], [4, 4293322470], [5, 18, 17]], [2, "dia", 2, [[3, -531, [273], 274]], [0, "0ccfFou0JPooZa/PcCmfzU", 1], [5, 186, 135], [0.452, 108.668, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [1, "bet_acc1", 3, [-533], [[20, -532, [287]]], [0, "c7UKEzz75BfJ/ygflgsKvs", 1], [5, 124, 38], [268.986, -11.538, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bet_acc4", 3, [-535], [[20, -534, [289]]], [0, "63f32XymRH16EwB2y8bEO7", 1], [5, 124, 38], [-238.36, -11.538, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bet_acc2", 3, [-537], [[20, -536, [291]]], [0, "cboKaJ0tNA/o5QIqt1CnoS", 1], [5, 124, 38], [299.693, -151.403, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bet_acc5", 3, [-539], [[20, -538, [293]]], [0, "83Ax/mKmNG66ynQqvJ8YaN", 1], [5, 124, 38], [-287.399, -151.403, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bet_acc3", 3, [-541], [[20, -540, [295]]], [0, "94ZMZj3nlCq7QTpivGPjUy", 1], [5, 124, 38], [100.602, -151.403, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bet_acc6", 3, [-543], [[20, -542, [297]]], [0, "dcpBb6fQBEIYzZCUEmkhAp", 1], [5, 124, 38], [-88.255, -151.403, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgSID", 2, [-545], [[17, 1, 0, -544, [301], 302]], [0, "bezn66EEtAdJf0kUInoTGR", 1], [5, 200, 40], [-205.327, 265.281, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "view", 27, [17], [[102, -546, [327]]], [0, "d9RZcsJiRF1pX3XJAan7UX", 1], [5, 460, 150], [0, -0.24, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "Background", 72, [[4, 0, false, -547, [307], 308]], [0, "dfFQTGzidIdJWiWievK2u5", 1], [4, 4293322470], [5, 95, 100]], [6, "Background", 73, [[4, 0, false, -548, [311], 312]], [0, "daFmKvKSVLpLgHGzYMRyT1", 1], [4, 4293322470], [5, 95, 100]], [6, "Background", 74, [[4, 0, false, -549, [315], 316]], [0, "2duG1/Ro9P4ozdWwy90eS3", 1], [4, 4293322470], [5, 95, 100]], [6, "Background", 75, [[4, 0, false, -550, [319], 320]], [0, "acknKcmSFBM5gu40ahn9vR", 1], [4, 4293322470], [5, 95, 100]], [6, "Background", 76, [[4, 0, false, -551, [323], 324]], [0, "fbbSztiLVKeJg712B2hMRE", 1], [4, 4293322470], [5, 95, 100]], [57, "lbWin", false, 28, [[-552, [9, -553, [341]]], 1, 4], [0, "a97b6bPu1EaKcClwINARuf", 1], [5, 86.25, 40], [-2.707, 36.946, 0, 0, 0, 0, 1, 1, 1, 1]], [36, "sound_on", 5, [[-554, [91, 3, -555, [[10, "ad402R7yshJsYOag7Xuj6+9", "soundClicked", 5]], [4, 4292269782]]], 1, 4], [0, "657tK5DMxMLISBu4prC//z", 1], [5, 81, 81], [-3, -176, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [62, "Main Camera", false, 1, [[77, 7, -1, -556]], [0, "efftHpgudHWKmBOwQAnqMN", 1], [0, 0, 527.4094709047232, 0, 0, 0, 1, 1, 1, 1]], [49, "musicBackground", 29, [-557], [0, "aaqtaxZhROvpgFU1eYrjCv", 1]], [79, true, true, 149], [49, "chipSelect", 29, [-558], [0, "f4qbaBNxBHbrNMY5OjV801", 1]], [58, true, 151], [49, "chipBet", 29, [-559], [0, "a9FSN/XXVHTpz21ZCc5r7a", 1]], [58, true, 153], [2, "huxocdiago2", 13, [[3, -560, [0], 1]], [0, "69Ef8NFOtDQKds8OkkVb4T", 1], [5, 329, 234], [12.891, 8.453, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "2Conchicken", 13, [[3, -561, [2], 3]], [0, "92mtN2RSRFXoqAPbqvz0wq", 1], [5, 644, 38], [0, 223.097, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "NoHu_baucua", 86, [[87, "default", "xocdia_medium", 0, "xocdia_medium", -562, [4], 5]], [0, "a9x5mmo75Ap5gz8QsnIaJr", 1], [5, 351, 279], [-225.705, -280.814, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "icMoney", 37, [[3, -563, [6], 7]], [0, "f3Tkvg1nVO3KeeJ8cgBV3N", 1], [5, 24, 32], [-68.034, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Label", 37, [[92, "1,256,895,158", 13, false, -2, 1, 1, 2, -564, [8], 9]], [0, "0d9kqeqf5B+5GN/9tMaFHI", 1], [5, 303.91, 40], [9.987, 22.962, 0, 0, 0, 0, 1, 1, 1, 1]], [37, false, 2, 20, [[13, "47fe6sINhNMZZqSErpdFUwf", "betClicked", "4", 1]], [4, 16777215], [4, 4294967295], [4, 16777215], 20], [37, false, 2, 21, [[13, "47fe6sINhNMZZqSErpdFUwf", "betClicked", "5", 1]], [4, 16777215], [4, 4294967295], [4, 16777215], 21], [37, false, 2, 22, [[13, "47fe6sINhNMZZqSErpdFUwf", "betClicked", "3", 1]], [4, 16777215], [4, 4294967295], [4, 16777215], 22], [37, false, 2, 23, [[13, "47fe6sINhNMZZqSErpdFUwf", "betClicked", "0", 1]], [4, 16777215], [4, 4294967295], [4, 16777215], 23], [37, false, 2, 24, [[13, "47fe6sINhNMZZqSErpdFUwf", "betClicked", "1", 1]], [4, 16777215], [4, 4294967295], [4, 16777215], 24], [37, false, 2, 25, [[13, "47fe6sINhNMZZqSErpdFUwf", "betClicked", "2", 1]], [4, 16777215], [4, 4294967295], [4, 16777215], 25], [55, "bgBack", 40, [-565], [0, "b83D9OinFGYbVZtJgqS4Tx", 1], [4, 4293322470], [5, 68, 65], [3.675, -0.94, 0, 0, 0, 0, 1, -1, 1, 1]], [24, 2, false, 166, [26]], [52, "seting", 2, [4], [0, "d0tILi3jxBBqUFrk+/38Vi", 1]], [6, "bgSound", 41, [[4, 2, false, -566, [27], 28]], [0, "72U1fj0RBBR4fapcTuSWNl", 1], [4, 4293322470], [5, 81, 82]], [2, "Icon_Exit", 87, [[3, -567, [29], 30]], [0, "b7eWFmZQ5Cxo0SkoWDZf7I", 1], [5, 45, 48], [-7.63, -3.876, 0, 0, 0, 0, 1, 1, 1, 1]], [64, "Icon_Setting", 42, [[3, -568, [31], 32]], [0, "fdkUrQ/JFA/ZRr194EH7Qt", 1], [5, 54, 52], [0, -0.033, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "Icon_Info", 88, [[16, 2, -569, [33], 34]], [0, "59s45VMxlNO6I/fvopvcCz", 1], [5, 48, 48], [0, -1.222, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Node", 4, [[51, -570, [[10, "bd7bcGYD4dKrYuCQu3t/0Cl", "menuCloseClicked", 4]]]], [0, "c8zUcnTIhGLbZhaO3CjU1I", 1], [5, 40, 80], [144.351, 22.164, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "bgSound", 43, [-571], [0, "0aTLd5ycRHRY7prX21EUwp", 1], [4, 4293322470], [5, 81, 82]], [24, 2, false, 174, [39]], [7, "win", false, 6, [[11, "default", "animation", 0, false, "animation", -572, [40], 41]], [0, "719i6zTeVICYoBKJPiJ229", 1], [5, 281, 269], [0, -3.195, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_sheld", 6, [[3, -573, [44], 45]], [0, "41Gt043zJOhr5FO5BWFVtG", 1], [5, 146, 146], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [23, "lbSID", false, 45, [-574], [0, "a9XASF2JBEh6B4/4pYS6sQ", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "[TQ]", 16, false, 1, 1, 178], [22, "lbName", 45, [-575], [0, "7ak04ZiEtBWbJaXR0MOS8q", 1], [4, 4282372607], [5, 130, 40]], [27, "8888888..", 20, false, false, 1, 1, 2, 180, [48]], [7, "lose", false, 6, [[50, false, -576, [53], 54]], [0, "9ft9vRMIJExrAbfNhfaQXK", 1], [5, 139, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "emotion", false, 92, [[11, "default", "1-waaaht", 0, false, "1-waaaht", -577, [55], 56]], [0, "c5XOtJCPpCa7K4JvKHo7P3", 1], [5, 123, 110]], [6, "lbChat", 93, [[29, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -578, [57], 58]], [0, "eagrzR2q9Af6ZvmNwcEeXL", 1], [4, 4278190080], [5, 172.4, 55]], [33, 6, 179, 181], [7, "win", false, 7, [[11, "default", "animation", 0, false, "animation", -579, [63], 64]], [0, "13NZ7WkdxCXo0tvpRJ3l+t", 1], [5, 281, 269], [4.112, -3.506, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_sheld", 7, [[3, -580, [67], 68]], [0, "bdgDm4NKtAHZiL7jysNw5g", 1], [5, 146, 146], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [23, "lbSID", false, 47, [-581], [0, "0asl44fPpMmrjPEytvhgWI", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "[TQ]", 16, false, 1, 1, 188], [22, "lbName", 47, [-582], [0, "11UIs0kYhIXaA1uhAVe8GO", 1], [4, 4282372607], [5, 130, 40]], [27, "8888888..", 20, false, false, 1, 1, 2, 190, [71]], [7, "lose", false, 7, [[45, false, -583, 76]], [0, "251dXLsdxAY7bHokuEpHhC", 1], [5, 139, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "emotion", false, 97, [[11, "default", "1-waaaht", 0, false, "1-waaaht", -584, [77], 78]], [0, "42UUHgoVJMEY9sma4m/AmX", 1], [5, 123, 110]], [6, "lbChat", 98, [[29, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -585, [79], 80]], [0, "59LygxQC5J54fn3KM/da6M", 1], [4, 4278190080], [5, 172.4, 55]], [33, 7, 189, 191], [7, "win", false, 8, [[11, "default", "animation", 0, false, "animation", -586, [85], 86]], [0, "42bYRQuQZKlJaWuGuEy01v", 1], [5, 281, 269], [8.514, -8.542, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_sheld", 8, [[3, -587, [89], 90]], [0, "02Y00CvItCKYtvIWMTgEQI", 1], [5, 146, 146], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [23, "lbSID", false, 49, [-588], [0, "8cS2EcmJVGQYJKJsh+vX+l", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "[TQ]", 16, false, 1, 1, 198], [22, "lbName", 49, [-589], [0, "31W2Fhc0VEGbS5B5pzhdXm", 1], [4, 4282372607], [5, 130, 40]], [27, "8888888..", 20, false, false, 1, 1, 2, 200, [93]], [7, "lose", false, 8, [[45, false, -590, 98]], [0, "62SN12VA1Ouotl+uLw+CTm", 1], [5, 139, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "emotion", false, 102, [[11, "default", "1-waaaht", 0, false, "1-waaaht", -591, [99], 100]], [0, "c1Ag0ajPZC5rb1h3SbtWk2", 1], [5, 123, 110]], [6, "lbChat", 103, [[29, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -592, [101], 102]], [0, "fc4z4gNzNCDZSgV8ubtbeV", 1], [4, 4278190080], [5, 172.4, 55]], [33, 8, 199, 201], [7, "win", false, 9, [[11, "default", "animation", 0, false, "animation", -593, [107], 108]], [0, "53OZhxTzREs6uwu0cVZg65", 1], [5, 281, 269], [-2.185, -4.027, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_sheld", 9, [[3, -594, [111], 112]], [0, "ca+UfSyZlCCKJrre+jgh8h", 1], [5, 146, 146], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [23, "lbSID", false, 51, [-595], [0, "1donTRJL5A0Ig6a7TQ2bmd", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "[TQ]", 16, false, 1, 1, 208], [22, "lbName", 51, [-596], [0, "17SUHwd8RIJq/XsQAnSCL2", 1], [4, 4282372607], [5, 130, 40]], [27, "8888888..", 20, false, false, 1, 1, 2, 210, [115]], [7, "lose", false, 9, [[50, false, -597, [120], 121]], [0, "35AO3s/DNJFqrl4Ro7ijlK", 1], [5, 139, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "emotion", false, 107, [[11, "default", "1-waaaht", 0, false, "1-waaaht", -598, [122], 123]], [0, "9b+AsCB/1JtJCtLD+FwlUd", 1], [5, 123, 110]], [6, "lbChat", 108, [[29, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -599, [124], 125]], [0, "52LoJRueJJFbuYuGWqZO8y", 1], [4, 4278190080], [5, 172.4, 55]], [33, 9, 209, 211], [7, "win", false, 10, [[11, "default", "animation", 0, false, "animation", -600, [130], 131]], [0, "3fTckY3fZLL5iNx6Xi6MVF", 1], [5, 281, 269], [-3.598, -7.55, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_sheld", 10, [[3, -601, [134], 135]], [0, "d25WBHW1FDPIBu0ojqNxzu", 1], [5, 146, 146], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [23, "lbSID", false, 53, [-602], [0, "457fq5xz9Hzo/YQ9twVjWP", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "[TQ]", 16, false, 1, 1, 218], [22, "lbName", 53, [-603], [0, "361gCPyPhDJ5EpDmUuV+pQ", 1], [4, 4282372607], [5, 130, 40]], [27, "8888888..", 20, false, false, 1, 1, 2, 220, [138]], [7, "lose", false, 10, [[45, false, -604, 143]], [0, "fbo+hWFPdJXpih6YmEfrCL", 1], [5, 139, 42], [-2.842170943040401e-14, -24.000000000000114, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "emotion", false, 112, [[11, "default", "1-waaaht", 0, false, "1-waaaht", -605, [144], 145]], [0, "bftLNahqRNgoADhZ6/mNtG", 1], [5, 123, 110]], [6, "lbChat", 113, [[29, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -606, [146], 147]], [0, "aaxcSWpOVCG60vq3SBPkeZ", 1], [4, 4278190080], [5, 172.4, 55]], [33, 10, 219, 221], [7, "win", false, 11, [[11, "default", "animation", 0, false, "animation", -607, [152], 153]], [0, "26sYgq8thMlLiSXUzyJvRL", 1], [5, 281, 269], [-2.612, -3.76, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_sheld", 11, [[3, -608, [156], 157]], [0, "23nvVTfbFEA6dRDB7cOkHZ", 1], [5, 146, 146], [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 1]], [23, "lbSID", false, 55, [-609], [0, "57OHJOxcFEX58wc94tKc0c", 1], [4, 4279026733], [5, 30.8, 16], [-37.8, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "[TQ]", 16, false, 1, 1, 228], [22, "lbName", 55, [-610], [0, "caQcP1RvRG76XPACAA27oe", 1], [4, 4282372607], [5, 130, 40]], [27, "8888888..", 20, false, false, 1, 1, 2, 230, [160]], [7, "lose", false, 11, [[50, false, -611, [165], 166]], [0, "4b/WCOzr1Cl5oN5Ecwo6fN", 1], [5, 139, 42], [0, -12.901, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "emotion", false, 117, [[11, "default", "1-waaaht", 0, false, "1-waaaht", -612, [167], 168]], [0, "02kM0o60JG1r44wYW0Up6Q", 1], [5, 123, 110]], [6, "lbChat", 118, [[29, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -613, [169], 170]], [0, "2bNv++SRhK1Iv2+tWi8zUt", 1], [4, 4278190080], [5, 172.4, 55]], [33, 11, 229, 231], [7, "win", false, 12, [[11, "default", "animation", 0, false, "animation", -614, [175], 176]], [0, "b96LUxWi5APLLby0/LlU5z", 1], [5, 281, 269], [-2.868, -6.474, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "ava_sheld", 12, [[3, -615, [179], 180]], [0, "d0Iz7/BmhMWKk5dI3oxBdg", 1], [5, 146, 146], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [23, "lbSID", false, 57, [-616], [0, "cav15w9mZLIrR5oi6OuRVG", 1], [4, 4279026733], [5, 57.75, 30], [-70, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "[TQ]", 30, false, 1, 1, 238], [55, "lbName", 57, [-617], [0, "b4tuORQnpPIbULXT169jJq", 1], [4, 4282372607], [5, 130, 40], [0, -35.821, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "8888888..", 20, false, false, 1, 1, 2, 240, [183]], [7, "lose", false, 12, [[45, false, -618, 188]], [0, "0aQr+wG0RLgoYIzy4sBXBV", 1], [5, 139, 42], [0, -28.7, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "emotion", false, 122, [[11, "default", "1-waaaht", 0, false, "1-waaaht", -619, [189], 190]], [0, "c27UBDJwFHrp88shcActdH", 1], [5, 123, 110]], [6, "lbChat", 123, [[29, "<PERSON><PERSON><PERSON>", 30, 50, false, 1, 1, 2, -620, [191], 192]], [0, "88HNrzq3BGtbYqf+/BcWWq", 1], [4, 4278190080], [5, 172.4, 55]], [33, 12, 239, 241], [14, "lbSID", false, 58, [[93, "<PERSON><PERSON><PERSON> ký rời bàn", 20, false, 1, 1, -621, [197], 198]], [0, "37ku2L8RlLD4VShCj+uCtS", 1], [5, 144, 40]], [56, "lbInfo", false, 59, [-622], [0, "e03rQKHONJtoYtgOho0ooA", 1], [5, 117.75, 40], [0, 4, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "Đặt cửa", 24, false, 1, 1, 247, [201]], [5, "bar", 30, [-623], [0, "8dW9tNtqxIwbi797QimNxm", 1], [5, 263, 34], [0, 0.437, 0, 0, 0, 0, 1, 1, 1, 1]], [82, 3, 0, 1, 249, [206], [0, 0.5, 0.5]], [41, "Text_cho dat cuoc", 30, [[3, -624, [207], 208]], [0, "a0BJGv065D65f9qOP+n0ts", 1], [5, 154, 24]], [101, 2, 30, 250], [56, "lbTime", false, 60, [-625], [0, "8b+HDyEXJK/qNnLaEym1/S", 1], [5, 57.37, 83.16], [0, 0.618, 0, 0, 0, 0, 1, 1, 1, 1]], [94, "20", 50, 66, false, 1, 1, 253, [211]], [5, "lbEven", 16, [-626], [0, "2eROeQMHRAu6JWGkX+pUvj", 1], [5, 0, 52], [0, 137.5, 0, 0, 0, 0, 1, 1, 1, 1]], [39, 12, 52, false, 1, 1, 255, [214]], [5, "lbFourDown", 16, [-627], [0, "253Tx5BvdABIYzxN21OWgu", 1], [5, 0, 52], [0, 82.5, 0, 0, 0, 0, 1, 1, 1, 1]], [39, 12, 52, false, 1, 1, 257, [215]], [5, "lbFourUp", 16, [-628], [0, "742Kf9q8pB4JB3dm8nKM3Q", 1], [5, 0, 52], [0, 27.5, 0, 0, 0, 0, 1, 1, 1, 1]], [39, 12, 52, false, 1, 1, 259, [216]], [5, "lbOdd", 16, [-629], [0, "80cboF/GlHuoC4hApeS/IJ", 1], [5, 0, 52], [0, -27.5, 0, 0, 0, 0, 1, 1, 1, 1]], [39, 12, 52, false, 1, 1, 261, [217]], [5, "lbThreeUp", 16, [-630], [0, "faTpLL9P5AgI+cDArgKLQY", 1], [5, 0, 52], [0, -82.5, 0, 0, 0, 0, 1, 1, 1, 1]], [39, 12, 52, false, 1, 1, 263, [218]], [5, "lbThreeDown", 16, [-631], [0, "b19To30l1A16YmTWmTU+PD", 1], [5, 0, 52], [0, -137.5, 0, 0, 0, 0, 1, 1, 1, 1]], [39, 12, 52, false, 1, 1, 265, [219]], [41, "view", 61, [[60, 0, -632, [224]]], [0, "9aecoTPhlHQb2QTpottnX3", 1], [5, 191, 191]], [67, "layout-parentCatCau", 15, [0, "05o22FQdZPaYOjl4m1viSa", 1], [5, 191, 114], [0, 1, 0.5], [148.59, -59, 0, 0, 0, 0, 1, 1, 1, 1]], [9, 26, [232, 233]], [2, "layout-fxResult", 31, [[30, false, 1, 1, -633, [5, 676, 218]]], [0, "bcfTiAUR5HPq7IObNZvAFJ", 1], [5, 676, 218], [0, -17, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "sao", false, 62, [[40, false, 1, 24, 48, 0.5, 0.05, 180, 0, 44, 17, -10, -9, 1, 118, 83, 0, 600, -634, [239], [4, 4281597951], [4, 2137417318], [4, 4281597951], [4, 0], [0, 10, 10], [0, 34, -314], 240, 241]], [0, "00zwnduORKwZuApw/TX+PK", 1]], [35, "sao", false, 64, [[40, false, 1, 24, 48, 0.5, 0.05, 180, 0, 44, 17, -10, -9, 1, 118, 83, 0, 600, -635, [244], [4, 4281597951], [4, 2137417318], [4, 4281597951], [4, 0], [0, 10, 10], [0, 34, -314], 245, 246]], [0, "d4AcSVlrZLJZQT0UTbSYbs", 1]], [35, "sao", false, 65, [[40, false, 1, 24, 48, 0.5, 0.05, 180, 0, 44, 17, -10, -9, 1, 118, 83, 0, 600, -636, [249], [4, 4281597951], [4, 2137417318], [4, 4281597951], [4, 0], [0, 10, 10], [0, 34, -314], 250, 251]], [0, "76q6HwtgxIL7IuIOPzZ5S8", 1]], [35, "sao", false, 67, [[40, false, 1, 24, 48, 0.5, 0.05, 180, 0, 44, 17, -10, -9, 1, 118, 83, 0, 600, -637, [255], [4, 4281597951], [4, 2137417318], [4, 4281597951], [4, 0], [0, 10, 10], [0, 34, -314], 256, 257]], [0, "b6nlTM1VJBepT/e6mBT7AN", 1]], [35, "sao", false, 68, [[40, false, 1, 24, 48, 0.5, 0.05, 180, 0, 44, 17, -10, -9, 1, 118, 83, 0, 600, -638, [260], [4, 4281597951], [4, 2137417318], [4, 4281597951], [4, 0], [0, 10, 10], [0, 34, -314], 261, 262]], [0, "27d3Yi2GRNVr5ocU1VYF8m", 1]], [35, "sao", false, 69, [[40, false, 1, 24, 48, 0.5, 0.05, 180, 0, 44, 17, -10, -9, 1, 118, 83, 0, 600, -639, [265], [4, 4281597951], [4, 2137417318], [4, 4281597951], [4, 0], [0, 10, 10], [0, 34, -314], 266, 267]], [0, "f6Gj64FIpAI4ks1B8QG0nS", 1]], [9, 31, [271, 272]], [5, "bat_mo", 2, [-640], [0, "9cURNZFU1A3bce8ZTum9V6", 1], [5, 195.26, 147.49], [-2.279, 112.697, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [88, "default", "Waiting", 0, false, "Waiting", 278, [275]], [5, "vi1", 34, [-641], [0, "bfhWiTvNdKv5dOrZ17VpcH", 1], [5, 24, 24], [-21.954, -32.346, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [24, 2, false, 280, [276]], [5, "vi2", 34, [-642], [0, "e1ww0bvr5Am5NnqBtYL83e", 1], [5, 24, 24], [-22.854, -65.331, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [24, 2, false, 282, [277]], [5, "vi3", 34, [-643], [0, "5bmN7AKL5Feb1QY4P8jsAl", 1], [5, 24, 24], [16.177, -33.286, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [24, 2, false, 284, [278]], [5, "vi4", 34, [-644], [0, "abgjwUXrRDybqy6OXy1Zcj", 1], [5, 24, 24], [16.177, -65.331, 0, 0, 0, 0, 1, 1.1, 1.1, 1]], [24, 2, false, 286, [279]], [5, "lbTotalGate1", 3, [-645], [0, "55BBSD2JtCMq+JAuh5gK1e", 1], [5, 73.13, 40], [230.024, 157.527, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "100.000", 15, false, 1, 1, 288, [280]], [5, "lbTotalGate4", 3, [-646], [0, "1araCzXlNGVrijFnf6mr5t", 1], [5, 73.13, 40], [-221.156, 157.527, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "100.000", 15, false, 1, 1, 290, [281]], [5, "lbTotalGate2", 3, [-647], [0, "90u5lFckBMFK/qCsaQprdt", 1], [5, 73.13, 40], [277.977, -4.24, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "100.000", 15, false, 1, 1, 292, [282]], [5, "lbTotalGate5", 3, [-648], [0, "fcoljiPbtNi6//n6eyKLfS", 1], [5, 73.13, 40], [-269.192, -4.24, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "100.000", 15, false, 1, 1, 294, [283]], [5, "lbTotalGate3", 3, [-649], [0, "119AJxcGdAzZNzn7WvF8Mi", 1], [5, 73.13, 40], [93.808, -4.24, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "100.000", 15, false, 1, 1, 296, [284]], [5, "lbTotalGate6", 3, [-650], [0, "3a1I1rdVNJApMgQhH+8NcZ", 1], [5, 73.13, 40], [-80.192, -4.24, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "100.000", 15, false, 1, 1, 298, [285]], [5, "lbTotalGate1", 133, [-651], [0, "c5KeYUweVGEaCGZwCwloLw", 1], [5, 73.13, 40], [-24.154, 69.331, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "100.000", 15, false, 1, 1, 300, [286]], [5, "lbTotalGate4", 134, [-652], [0, "9fg/X9m1NHDrDQlc0qRDD6", 1], [5, 73.13, 40], [0, 69.331, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "100.000", 15, false, 1, 1, 302, [288]], [5, "lbTotalGate2", 135, [-653], [0, "2aOy91nqFFQIZqSfTHrPV+", 1], [5, 73.13, 40], [0, 49.943, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "100.000", 15, false, 1, 1, 304, [290]], [5, "lbTotalGate5", 136, [-654], [0, "7d9gjkHv5IUaRIfCe/Eaea", 1], [5, 73.13, 40], [0, 49.943, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "100.000", 15, false, 1, 1, 306, [292]], [5, "lbTotalGate3", 137, [-655], [0, "fbhVQyq79FIZEZmePl8Izp", 1], [5, 73.13, 40], [0, 49.943, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "100.000", 15, false, 1, 1, 308, [294]], [5, "lbTotalGate6", 138, [-656], [0, "08uBf1x6FHr5II+x6zs1EL", 1], [5, 73.13, 40], [0, 49.943, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "100.000", 15, false, 1, 1, 310, [296]], [54, "layout-chip", 2, [0, "87+bV9CYdJgZT6+MShnZ6z", 1]], [54, "chat<PERSON>arent", 2, [0, "408YxEYldEkJRwiK+XmhEH", 1]], [72, "lbSID", false, 139, [-657], [0, "6ef/G8rMxJLZAuMm7i9j6F", 1], [5, 0, 40]], [95, 20, false, 1, 1, 314, [300]], [2, "Background", 71, [[4, 2, false, -658, [303], 304]], [0, "d9/NaFHGJDvoGCWGkAm8XS", 1], [5, 57, 60], [4.51, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "<PERSON>_<PERSON> cuoc", 35, [[3, -659, [305], 306]], [0, "dfAzJlZS9D56X4OVjr9qJy", 1], [5, 593, 78]], [14, "chip_press", false, 72, [[16, 0, -660, [309], 310]], [0, "cfjgSjmfZJvaxsb4PeWBR0", 1], [5, 142, 147]], [12, 0.9, 3, 72, [[13, "47fe6sINhNMZZqSErpdFUwf", "betValueClicked", "0", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 141], [41, "chip_press", 73, [[16, 0, -661, [313], 314]], [0, "d1ad5NWcxJ8KdDdGOl4Wzw", 1], [5, 142, 147]], [12, 0.9, 3, 73, [[13, "47fe6sINhNMZZqSErpdFUwf", "betValueClicked", "1", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 142], [14, "chip_press", false, 74, [[16, 0, -662, [317], 318]], [0, "d3Hq1/L2ZDeoqST0K+4Juy", 1], [5, 142, 147]], [12, 0.9, 3, 74, [[13, "47fe6sINhNMZZqSErpdFUwf", "betValueClicked", "2", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 143], [14, "chip_press", false, 75, [[16, 0, -663, [321], 322]], [0, "934MV7qJ9OV4baPKbrXEkB", 1], [5, 142, 147]], [12, 0.9, 3, 75, [[13, "47fe6sINhNMZZqSErpdFUwf", "betValueClicked", "3", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 144], [14, "chip_press", false, 76, [[16, 0, -664, [325], 326]], [0, "03EjcMKRNDSaztKAwFpRJJ", 1], [5, 142, 147]], [12, 0.9, 3, 76, [[13, "47fe6sINhNMZZqSErpdFUwf", "betValueClicked", "4", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 145], [24, 2, false, 79, [334]], [12, 0.9, 2, 80, [[10, "47fe6sINhNMZZqSErpdFUwf", "x2Clicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 80], [12, 0.9, 2, 81, [[10, "47fe6sINhNMZZqSErpdFUwf", "repeatClicked", 1]], [4, 4293322470], [4, 4291348680], [4, 3363338360], 81], [5, "lbTotalUser", 28, [-665], [0, "6dPeKeQsJHHIxHdPJn9RDQ", 1], [5, 44.06, 40], [1.957, -22.537, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "20", 30, false, 1, 1, 331, [339]], [8, "+100", 30, false, 1, 1, 146, [340]], [2, "dealer_xd", 1, [[11, "default", "Chop mat", 0, false, "Chop mat", -666, [344], 345]], [0, "2cBxltEk9CHKylcGgljMXO", 1], [5, 364.13, 224.02], [0, 259.734, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [41, "bg_tex1", 36, [[85, 0, -667, [346]]], [0, "e7O0Zrmm1G142P0wjUPqns", 1], [5, 800, 80]], [73, "lbMessage", 36, [-668], [0, "58BWIsxtpHub5Ro1iZm+fh", 1], [5, 700, 45]], [96, "Dat cuoc thanh cong ", 36, 50, false, false, 1, 1, 2, 336, [347]], [18, "layoutSetting", false, 1, [5], [0, "31+td9CxlHHaLVaNqyc+Lg", 1]], [20, 147, [354]], [20, 85, [355]], [9, 5, [356, 357]]], 0, [0, 25, 1, 0, 26, 65, 0, 27, 64, 0, 28, 62, 0, 29, 68, 0, 30, 67, 0, 31, 69, 0, 32, 32, 0, 33, 33, 0, 34, 277, 0, -1, 281, 0, -2, 283, 0, -3, 285, 0, -4, 287, 0, 35, 279, 0, 36, 132, 0, 37, 70, 0, 0, 1, 0, -1, 245, 0, -2, 205, 0, -3, 195, 0, -4, 185, 0, -5, 215, 0, -6, 225, 0, -7, 235, 0, 38, 333, 0, 39, 332, 0, 40, 252, 0, 41, 248, 0, 42, 254, 0, 43, 315, 0, 0, 1, 0, 44, 313, 0, 45, 58, 0, 46, 167, 0, 14, 175, 0, 0, 1, 0, -1, 301, 0, -2, 305, 0, -3, 309, 0, -4, 303, 0, -5, 307, 0, -6, 311, 0, -1, 289, 0, -2, 293, 0, -3, 297, 0, -4, 291, 0, -5, 295, 0, -6, 299, 0, 47, 328, 0, 48, 330, 0, 49, 329, 0, -1, 319, 0, -2, 321, 0, -3, 323, 0, -4, 325, 0, -5, 327, 0, -1, 160, 0, -2, 161, 0, -3, 162, 0, -4, 163, 0, -5, 164, 0, -6, 165, 0, 50, 312, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 148, 0, -2, 29, 0, -3, 2, 0, -4, 334, 0, -5, 82, 0, -6, 338, 0, -1, 13, 0, -2, 40, 0, -3, 43, 0, -4, 14, 0, -5, 58, 0, -6, 59, 0, -7, 60, 0, -8, 26, 0, -9, 31, 0, -10, 132, 0, -11, 278, 0, -12, 34, 0, -13, 3, 0, -14, 312, 0, -15, 70, 0, -16, 313, 0, -17, 139, 0, -18, 71, 0, -19, 35, 0, -20, 79, 0, -21, 80, 0, -22, 81, 0, -23, 28, 0, -24, 168, 0, 0, 3, 0, -1, 288, 0, -2, 290, 0, -3, 292, 0, -4, 294, 0, -5, 296, 0, -6, 298, 0, -7, 133, 0, -8, 134, 0, -9, 135, 0, -10, 136, 0, -11, 137, 0, -12, 138, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 41, 0, -2, 87, 0, -3, 42, 0, -4, 88, 0, -5, 173, 0, -1, 341, 0, 0, 5, 0, 51, 340, 0, 14, 339, 0, 52, 5, 0, 15, 341, 0, 0, 5, 0, -1, 83, 0, -2, 84, 0, -3, 147, 0, -4, 85, 0, -1, 185, 0, 0, 6, 0, -1, 176, 0, -2, 89, 0, -3, 177, 0, -4, 90, 0, -5, 44, 0, -6, 182, 0, -7, 92, 0, -1, 195, 0, 0, 7, 0, -1, 186, 0, -2, 94, 0, -3, 187, 0, -4, 95, 0, -5, 46, 0, -6, 192, 0, -7, 97, 0, -1, 205, 0, 0, 8, 0, -1, 196, 0, -2, 99, 0, -3, 197, 0, -4, 100, 0, -5, 48, 0, -6, 202, 0, -7, 102, 0, -1, 215, 0, 0, 9, 0, -1, 206, 0, -2, 104, 0, -3, 207, 0, -4, 105, 0, -5, 50, 0, -6, 212, 0, -7, 107, 0, -1, 225, 0, 0, 10, 0, -1, 216, 0, -2, 109, 0, -3, 217, 0, -4, 110, 0, -5, 52, 0, -6, 222, 0, -7, 112, 0, -1, 235, 0, 0, 11, 0, -1, 226, 0, -2, 114, 0, -3, 227, 0, -4, 115, 0, -5, 54, 0, -6, 232, 0, -7, 117, 0, -1, 245, 0, 0, 12, 0, -1, 236, 0, -2, 119, 0, -3, 237, 0, -4, 120, 0, -5, 56, 0, -6, 242, 0, -7, 122, 0, 0, 13, 0, -1, 155, 0, -2, 156, 0, -3, 86, 0, -4, 37, 0, -5, 18, 0, -6, 19, 0, 0, 15, 0, -1, 16, 0, -2, 125, 0, -3, 61, 0, -4, 268, 0, -5, 128, 0, -6, 130, 0, 0, 16, 0, -1, 255, 0, -2, 257, 0, -3, 259, 0, -4, 261, 0, -5, 263, 0, -6, 265, 0, 0, 17, 0, -1, 72, 0, -2, 73, 0, -3, 74, 0, -4, 75, 0, -5, 76, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 3, 18, 0, 0, 19, 0, -1, 38, 0, -2, 22, 0, -3, 23, 0, -4, 39, 0, 0, 20, 0, -2, 160, 0, 4, 20, 0, 0, 20, 0, 0, 21, 0, -2, 161, 0, 4, 21, 0, 0, 21, 0, 0, 22, 0, -2, 162, 0, 4, 22, 0, 0, 22, 0, 0, 23, 0, -2, 163, 0, 4, 23, 0, 0, 23, 0, 0, 24, 0, -2, 164, 0, 4, 24, 0, 0, 24, 0, 0, 25, 0, -2, 165, 0, 4, 25, 0, 0, 25, 0, 53, 266, 0, 54, 264, 0, 55, 262, 0, 56, 258, 0, 57, 260, 0, 58, 256, 0, 59, 127, 0, 60, 126, 0, 61, 268, 0, 15, 269, 0, 0, 26, 0, -2, 269, 0, -1, 124, 0, 0, 27, 0, 0, 27, 0, -1, 140, 0, 0, 28, 0, 4, 28, 0, 0, 28, 0, -1, 331, 0, -2, 146, 0, 62, 154, 0, 63, 152, 0, 64, 150, 0, 0, 29, 0, -1, 149, 0, -2, 151, 0, -3, 153, 0, -1, 252, 0, 0, 30, 0, -1, 249, 0, -2, 251, 0, -1, 277, 0, -1, 270, 0, -2, 32, 0, -3, 33, 0, 0, 32, 0, -1, 62, 0, -2, 63, 0, 0, 33, 0, -1, 66, 0, -2, 69, 0, -1, 280, 0, -2, 282, 0, -3, 284, 0, -4, 286, 0, -1, 317, 0, -3, 77, 0, -4, 78, 0, 0, 36, 0, -1, 335, 0, -2, 336, 0, 0, 37, 0, -1, 158, 0, -2, 159, 0, 0, 38, 0, 0, 39, 0, 4, 40, 0, 0, 40, 0, -1, 166, 0, 4, 41, 0, 0, 41, 0, -1, 169, 0, 4, 42, 0, 0, 42, 0, -1, 171, 0, 4, 43, 0, 0, 43, 0, -1, 174, 0, 0, 44, 0, -1, 45, 0, -2, 91, 0, 0, 45, 0, -1, 178, 0, -2, 180, 0, 0, 46, 0, -1, 47, 0, -2, 96, 0, 0, 47, 0, -1, 188, 0, -2, 190, 0, 0, 48, 0, -1, 49, 0, -2, 101, 0, 0, 49, 0, -1, 198, 0, -2, 200, 0, 0, 50, 0, -1, 51, 0, -2, 106, 0, 0, 51, 0, -1, 208, 0, -2, 210, 0, 0, 52, 0, -1, 53, 0, -2, 111, 0, 0, 53, 0, -1, 218, 0, -2, 220, 0, 0, 54, 0, -1, 55, 0, -2, 116, 0, 0, 55, 0, -1, 228, 0, -2, 230, 0, 0, 56, 0, -1, 57, 0, -2, 121, 0, 0, 57, 0, -1, 238, 0, -2, 240, 0, 0, 58, 0, -1, 246, 0, 0, 59, 0, 0, 59, 0, -1, 247, 0, 0, 60, 0, -2, 253, 0, 0, 61, 0, 0, 61, 0, -1, 267, 0, 0, 62, 0, -1, 271, 0, 0, 63, 0, -1, 64, 0, -2, 65, 0, 0, 64, 0, -1, 272, 0, 0, 65, 0, -1, 273, 0, 0, 66, 0, -1, 67, 0, -2, 68, 0, 0, 67, 0, -1, 274, 0, 0, 68, 0, -1, 275, 0, 0, 69, 0, -1, 276, 0, 0, 70, 0, 0, 70, 0, 4, 71, 0, 0, 71, 0, -1, 316, 0, -1, 319, 0, -1, 141, 0, -2, 318, 0, -1, 321, 0, -1, 142, 0, -2, 320, 0, -1, 323, 0, -1, 143, 0, -2, 322, 0, -1, 325, 0, -1, 144, 0, -2, 324, 0, -1, 327, 0, -1, 145, 0, -2, 326, 0, 0, 77, 0, 4, 77, 0, 0, 77, 0, 0, 78, 0, 4, 78, 0, 0, 78, 0, -1, 328, 0, 4, 79, 0, 0, 79, 0, 0, 80, 0, -2, 329, 0, 0, 81, 0, -2, 330, 0, 0, 82, 0, 65, 337, 0, 0, 82, 0, 0, 83, 0, 4, 83, 0, 0, 83, 0, 0, 84, 0, 4, 84, 0, 0, 84, 0, -1, 340, 0, 4, 85, 0, 0, 85, 0, 0, 86, 0, -1, 157, 0, 0, 87, 0, -1, 170, 0, 0, 88, 0, -1, 172, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, -1, 183, 0, -2, 93, 0, 0, 93, 0, -1, 184, 0, 0, 94, 0, 0, 94, 0, 0, 95, 0, 0, 95, 0, 0, 96, 0, 0, 96, 0, -1, 193, 0, -2, 98, 0, 0, 98, 0, -1, 194, 0, 0, 99, 0, 0, 99, 0, 0, 100, 0, 0, 100, 0, 0, 101, 0, 0, 101, 0, -1, 203, 0, -2, 103, 0, 0, 103, 0, -1, 204, 0, 0, 104, 0, 0, 104, 0, 0, 105, 0, 0, 105, 0, 0, 106, 0, 0, 106, 0, -1, 213, 0, -2, 108, 0, 0, 108, 0, -1, 214, 0, 0, 109, 0, 0, 109, 0, 0, 110, 0, 0, 110, 0, 0, 111, 0, 0, 111, 0, -1, 223, 0, -2, 113, 0, 0, 113, 0, -1, 224, 0, 0, 114, 0, 0, 114, 0, 0, 115, 0, 0, 115, 0, 0, 116, 0, 0, 116, 0, -1, 233, 0, -2, 118, 0, 0, 118, 0, -1, 234, 0, 0, 119, 0, 0, 119, 0, 0, 120, 0, 0, 120, 0, 0, 121, 0, 0, 121, 0, -1, 243, 0, -2, 123, 0, 0, 123, 0, -1, 244, 0, 0, 124, 0, -1, 126, 0, -2, 127, 0, 0, 126, 0, 0, 127, 0, 4, 129, 0, 0, 128, 0, -1, 129, 0, 0, 129, 0, 4, 131, 0, 0, 130, 0, -1, 131, 0, 0, 131, 0, 0, 132, 0, 0, 133, 0, -1, 300, 0, 0, 134, 0, -1, 302, 0, 0, 135, 0, -1, 304, 0, 0, 136, 0, -1, 306, 0, 0, 137, 0, -1, 308, 0, 0, 138, 0, -1, 310, 0, 0, 139, 0, -1, 314, 0, 0, 140, 0, 0, 141, 0, 0, 142, 0, 0, 143, 0, 0, 144, 0, 0, 145, 0, -1, 333, 0, 0, 146, 0, -1, 339, 0, 0, 147, 0, 0, 148, 0, -1, 150, 0, -1, 152, 0, -1, 154, 0, 0, 155, 0, 0, 156, 0, 0, 157, 0, 0, 158, 0, 0, 159, 0, -1, 167, 0, 0, 169, 0, 0, 170, 0, 0, 171, 0, 0, 172, 0, 0, 173, 0, -1, 175, 0, 0, 176, 0, 0, 177, 0, -1, 179, 0, -1, 181, 0, 0, 182, 0, 0, 183, 0, 0, 184, 0, 0, 186, 0, 0, 187, 0, -1, 189, 0, -1, 191, 0, 0, 192, 0, 0, 193, 0, 0, 194, 0, 0, 196, 0, 0, 197, 0, -1, 199, 0, -1, 201, 0, 0, 202, 0, 0, 203, 0, 0, 204, 0, 0, 206, 0, 0, 207, 0, -1, 209, 0, -1, 211, 0, 0, 212, 0, 0, 213, 0, 0, 214, 0, 0, 216, 0, 0, 217, 0, -1, 219, 0, -1, 221, 0, 0, 222, 0, 0, 223, 0, 0, 224, 0, 0, 226, 0, 0, 227, 0, -1, 229, 0, -1, 231, 0, 0, 232, 0, 0, 233, 0, 0, 234, 0, 0, 236, 0, 0, 237, 0, -1, 239, 0, -1, 241, 0, 0, 242, 0, 0, 243, 0, 0, 244, 0, 0, 246, 0, -1, 248, 0, -1, 250, 0, 0, 251, 0, -1, 254, 0, -1, 256, 0, -1, 258, 0, -1, 260, 0, -1, 262, 0, -1, 264, 0, -1, 266, 0, 0, 267, 0, 0, 270, 0, 0, 271, 0, 0, 272, 0, 0, 273, 0, 0, 274, 0, 0, 275, 0, 0, 276, 0, -1, 279, 0, -1, 281, 0, -1, 283, 0, -1, 285, 0, -1, 287, 0, -1, 289, 0, -1, 291, 0, -1, 293, 0, -1, 295, 0, -1, 297, 0, -1, 299, 0, -1, 301, 0, -1, 303, 0, -1, 305, 0, -1, 307, 0, -1, 309, 0, -1, 311, 0, -1, 315, 0, 0, 316, 0, 0, 317, 0, 0, 318, 0, 0, 320, 0, 0, 322, 0, 0, 324, 0, 0, 326, 0, -1, 332, 0, 0, 334, 0, 0, 335, 0, -1, 337, 0, 66, 1, 4, 3, 168, 5, 3, 338, 6, 3, 14, 7, 3, 14, 8, 3, 14, 9, 3, 14, 10, 3, 14, 11, 3, 14, 12, 3, 14, 15, 3, 124, 17, 3, 140, 20, 3, 38, 21, 3, 38, 24, 3, 39, 25, 3, 39, 27, 3, 35, 30, 3, 60, 36, 3, 82, 668], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 150, 152, 154, 160, 160, 160, 160, 161, 161, 161, 161, 162, 162, 162, 162, 163, 163, 163, 163, 164, 164, 164, 164, 165, 165, 165, 165, 167, 175, 179, 181, 189, 191, 199, 201, 209, 211, 219, 221, 229, 231, 239, 241, 248, 250, 254, 256, 258, 260, 262, 264, 266, 277, 279, 281, 283, 285, 287, 289, 291, 293, 295, 297, 299, 301, 303, 305, 307, 309, 311, 315, 328, 329, 329, 329, 329, 330, 330, 330, 330, 332, 337, 339, 340], [-1, 1, -1, 1, -1, 5, -1, 1, -1, 2, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -2, -1, 1, -1, -1, 5, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, 1, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 5, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, 1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 5, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, 1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 5, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, 1, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 5, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, 1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 5, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, 1, -1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 5, -1, 1, -1, 1, -1, -1, -1, -1, 2, -1, 1, 1, -1, 5, -1, 2, -1, 1, -1, -2, -1, 2, -1, 1, -1, -1, 1, 12, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -2, -1, -2, -3, -4, -5, -1, 10, 1, -1, 1, -1, 10, 1, -1, 1, -1, 10, 1, -1, 1, -1, -1, 10, 1, -1, 1, -1, 10, 1, -1, 1, -1, 10, 1, -1, 1, -1, -1, -2, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, 13, -1, 1, 13, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, 5, -1, -1, -1, -1, -1, 1, -1, 1, -1, -1, -1, -2, -1, 1, -1, -2, -1, -2, -1, -2, -1, -2, 16, -1, -2, -3, -4, -5, -1, -2, 17, -1, -2, -1, -2, -3, -4, -5, 18, 19, 20, 21, 22, 23, 24, 11, 11, 11, 6, 7, 8, 9, 6, 7, 8, 9, 6, 7, 8, 9, 6, 7, 8, 9, 6, 7, 8, 9, 6, 7, 8, 9, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 2, 2, 2, 2, 2, 2, 2, 12, 5, 1, 1, 1, 1, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 1, 6, 7, 8, 9, 6, 7, 8, 9, 2, 2, 1, 1], [0, 48, 0, 49, 2, 50, 0, 51, 0, 52, 0, 53, 0, 17, 0, 18, 0, 19, 0, 20, 0, 21, 0, 22, 0, 54, 0, 0, 55, 0, 56, 0, 57, 0, 58, 59, 60, 0, 61, 0, 2, 6, 0, 32, 0, 7, 0, 5, 0, 0, 4, 0, 8, 0, 9, 2, 10, 0, 1, 0, 11, 12, 13, 2, 6, 0, 62, 0, 7, 0, 5, 0, 0, 4, 0, 8, 9, 2, 10, 0, 1, 0, 11, 12, 13, 2, 6, 0, 63, 0, 7, 0, 5, 0, 0, 4, 0, 8, 9, 2, 10, 0, 1, 0, 11, 12, 13, 2, 6, 0, 26, 0, 7, 0, 5, 0, 0, 4, 0, 8, 0, 9, 2, 10, 0, 1, 0, 11, 12, 13, 2, 6, 0, 64, 0, 7, 0, 5, 0, 0, 4, 0, 8, 9, 2, 10, 0, 1, 0, 11, 12, 13, 2, 6, 0, 32, 0, 7, 0, 5, 0, 0, 4, 0, 8, 0, 9, 2, 10, 0, 1, 0, 11, 12, 13, 2, 6, 0, 26, 0, 7, 0, 5, 0, 0, 4, 0, 8, 9, 2, 10, 0, 1, 0, 11, 12, 13, 0, 1, 0, 27, 0, 0, 27, 33, 33, 0, 0, 65, 0, 66, 0, 0, 67, 0, 0, 0, 0, 0, 0, 0, 28, 0, 29, 0, 0, 34, 0, 34, 0, 68, 0, 69, 70, 28, 29, 35, 36, 37, 0, 14, 15, 0, 71, 0, 14, 15, 0, 72, 0, 14, 15, 0, 73, 0, 0, 14, 15, 0, 74, 0, 14, 15, 0, 75, 0, 14, 15, 0, 76, 0, 38, 77, 0, 78, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 0, 0, 27, 0, 80, 0, 81, 0, 39, 0, 82, 0, 40, 0, 83, 0, 41, 0, 84, 0, 42, 0, 85, 0, 43, 0, 86, 0, 0, 44, 0, 0, 44, 0, 0, 0, 23, 0, 24, 0, 0, 5, 0, 87, 2, 88, 0, 0, 0, 89, 0, 90, 0, 91, 0, 0, 92, 93, 0, 94, 45, 95, 46, 96, 30, 31, 47, 97, 98, 39, 40, 41, 42, 43, 25, 25, 26, 25, 25, 28, 29, 35, 36, 37, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 17, 17, 17, 17, 18, 18, 18, 18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 22, 22, 22, 22, 25, 47, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 109, 110, 16, 16, 16, 16, 16, 16, 38, 111, 31, 31, 30, 30, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 1, 112, 23, 23, 23, 23, 24, 24, 24, 24, 4, 4, 45, 46]]