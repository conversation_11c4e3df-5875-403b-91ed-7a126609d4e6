[1, ["017Jn3Zv1Ft7hygdjpaSoK", "adw94Z+hpN57wutNivq8Q5", "d98uhai11C7aIhe1ooITz0", "0c4QLBqD5EhYkHOfeQ6OD3", "fdNoodJKVLj4dF1TLppv2g", "edFBOUvzVH7bYniEYx74+R", "a9VpD0DP5LJYQPXITZq+uj", "32QZqIYVxO64WRfop4Olmy", "e18aj4b2NI1pmuqHawka9u", "18s14wsJ1Nh7EeEYJxncZC", "83esreDA5PPoUmAk6McrcF", "c1iNYlndJPkbHWUAVaO4IP", "2cWB/vWPRHja3uQTinHH30", "0agcTHxc9JNroMZv/Zs7Lf", "210GpTy+tAbLm8T2HOBsI8"], ["node", "_N$file", "_spriteFrame", "_parent", "_textureSetter", "lbDesc", "lbWin", "lbRoom", "lbNickName", "lbSID", "lbTime", "lbSessionID", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "root", "jackpotView", "bigWinView", "nodeBigWin", "nodeJackpot", "btnBigWin", "btnJackpot", "_N$target", "data", "_defaultClip"], [["cc.Node", ["_name", "_opacity", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_trs", "_contentSize", "_parent", "_children", "_color"], 1, 2, 4, 7, 5, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "node", "_spriteFrame"], 0, 1, 6], "cc.SpriteFrame", ["cc.<PERSON><PERSON>", ["_N$transition", "_N$interactable", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor"], 1, 1, 9, 5, 5, 1, 5], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_enableWrapText", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "node", "_N$file"], -5, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_enabled", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["3c6a1IcT7pG8b7cqDRMjmCv", ["node", "btnJackpot", "btnBigWin", "nodeJackpot", "nodeBigWin", "bigWinView", "jackpotView"], 3, 1, 1, 1, 1, 1, 1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["6c6718pco1KcbJmkeGVCmhO", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["3566aJpBw5KOJAxbKKG26cU", ["node", "lbSessionID", "lbTime", "lbSID", "lbNickName", "lbRoom", "lbWin", "lbDesc"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.LabelOutline", ["node", "_color"], 3, 1, 5], ["cc.Mask", ["_N$alphaThreshold", "node"], 2, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["088def4kH5OCoVfmOqRAxDh", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["5f63a+KMdxKmINJ7inujvgg", ["node", "slotsLBJackpotListView"], 3, 1, 1], ["7843dJ1OI1CC4ZbqgEYhC5i", ["spawnCount", "spacing", "bufferZone", "node", "itemTemplate", "scrollView"], 0, 1, 1, 1], ["3f026hcsZBBlIzyApf7sacO", ["node", "slotsLBBigWinListView"], 3, 1, 1]], [[11, 0, 1, 2], [0, 0, 5, 3, 2, 4, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [1, 0, 6, 2, 3, 5, 4, 2], [1, 0, 6, 2, 3, 8, 5, 4, 2], [0, 0, 5, 6, 3, 2, 4, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 8, 8], [2, 3, 4, 1], [2, 0, 1, 3, 4, 3], [14, 0, 1, 2, 3], [0, 0, 5, 6, 3, 2, 4, 2], [0, 0, 5, 3, 2, 4, 8, 7, 2], [0, 0, 5, 3, 2, 4, 2], [0, 0, 5, 6, 2, 7, 2], [1, 0, 7, 2, 3, 5, 4, 2], [1, 0, 1, 6, 7, 2, 3, 4, 3], [1, 0, 1, 6, 2, 3, 8, 5, 4, 3], [8, 0, 1, 2, 3, 4, 5, 2], [4, 0, 2, 3, 4, 5, 6, 2], [6, 0, 1, 2, 5, 6, 4], [6, 3, 0, 1, 4, 5, 6, 5], [15, 0, 1, 1], [16, 0, 1, 2], [17, 0, 1, 2, 3, 4, 5, 6, 6], [7, 0, 2], [0, 0, 6, 3, 2, 2], [0, 0, 6, 3, 2, 4, 7, 2], [0, 0, 6, 3, 2, 4, 2], [0, 0, 1, 5, 3, 2, 4, 7, 3], [9, 0, 1, 2, 1], [10, 0, 1, 2, 3, 4, 5, 6, 1], [12, 0, 1, 2, 3, 4, 5, 6, 7, 1], [13, 0, 1, 2, 3, 4, 5, 6, 7, 1], [2, 0, 3, 4, 2], [2, 2, 0, 1, 3, 4, 4], [4, 2, 7, 1], [4, 1, 0, 2, 3, 4, 5, 6, 3], [18, 0, 1, 2, 3, 4, 5, 4], [19, 0, 1, 1], [20, 0, 1, 2, 3, 4, 5, 4], [21, 0, 1, 1]], [[[{"name": "button cuoc", "rect": [0, 0, 137, 69], "offset": [0, 0], "originalSize": [137, 69], "capInsets": [64, 0, 64, 0]}], [3], 0, [0], [4], [5]], [[[25, "tkLeaderboardView"], [26, "tkLeaderboardView", [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21], [[30, -2, [17, 18], 16], [31, -9, -8, -7, -6, -5, -4, -3]], [0, "9b2uVQ1o9PI7Ah0a6twS7F", -1]], [27, "<PERSON><PERSON>", [-30, -31, -32, -33, -34, -35], [[32, -29, -28, -27, -26, -25, -24, -23, -22]], [0, "2aQkVz+1lBZa95AsTy6icl", 1], [5, 799, 37], [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "<PERSON><PERSON>", [-44, -45, -46, -47, -48, -49], [[33, -43, -42, -41, -40, -39, -38, -37, -36]], [0, "f3SaKIqs5IqYkU8l4UIxfc", 1], [5, 799, 37]], [6, "spriteBGTitle", 1, [-51, -52, -53, -54, -55, -56], [[8, -50, 13]], [0, "b9X4+nvG1BEoz7Q0w5nvBR", 1], [5, 803, 38], [0, 140, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", [-59, -60], [-57, -58], [0, "4dlwMy6RZNA599H3yu4WLz", 1], [5, 803, 355], [0, -90, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "scrollview", [-63, -64], [-61, -62], [0, "1axnNhYJNHcJfsJ4O/ZvoX", 1], [5, 803, 355], [0, -90, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "btnClose", 1, [-67], [[19, 3, -66, [[10, "3c6a1IcT7pG8b7cqDRMjmCv", "backClicked", 1]], [4, **********], [4, **********], -65]], [0, "66f7OR0/BJ7rZ/PFYHk8rj", 1], [5, 80, 80], [468, 270, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnJackpot", 1, [[[9, 0, false, -68, 5], -69], 4, 1], [0, "87kYQ0a7tCIrTph4cDoVQz", 1], [5, 150, 56], [-85, 195, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "btnBigWin", 1, [[[9, 0, false, -70, 6], -71], 4, 1], [0, "0ftZVSULZJT5R3/wQW/5Zb", 1], [5, 150, 56], [85, 195, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbJackpotView", false, 1, [5], [-72], [0, "24HSkW/SZDXpPc435hcYxn", 1], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "layout-nick<PERSON><PERSON>", 2, [-74, -75], [[20, 1, 1, 5, -73, [5, 137, 50]]], [0, "e4F0oCyDxL1IQ0x+wXTsG9", 1], [5, 137, 50], [-5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "lbBig<PERSON>in<PERSON>iew", false, 1, [6], [-76], [0, "9djQCWVb1IVo8i+p/oh5EU", 1], [0, 22, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "layout-nick<PERSON><PERSON>", 3, [-78, -79], [[20, 1, 1, 5, -77, [5, 137, 50]]], [0, "0fWGYfBsJEFaf+LASWPJh/", 1], [5, 137, 50], [-5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "black", 100, 1, [[34, 0, -80, 0], [36, -81, [4, 4292269782]]], [0, "d6pzTHVSpLlKnpljVD1+D0", 1], [5, 3000, 3000], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbJackpot", 1, [[3, "NỔ HŨ", 20, 50, false, false, 1, 1, 1, -82, 14], [22, -83, [4, 4278190080]]], [0, "f2p/TnxopKJ65LUW0bZ/Er", 1], [5, 100, 30], [-85, 193, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbBigWin", 1, [[3, "THẮNG LỚN", 20, 50, false, false, 1, 1, 1, -84, 15], [22, -85, [4, 4278190080]]], [0, "97lc0MEHJBb7ujteobrCQH", 1], [5, 123, 30], [85, 193, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "view", 5, [-87], [[23, 0, -86]], [0, "35ioq84xNFML0flzCCsZbZ", 1], [5, 803, 348]], [12, "content", 17, [[21, false, 1, 2, 10, -88, [5, 1190, 75]]], [0, "08+YvcNelMNpPmvFKQe30i", 1], [5, 803, 75], [0, 0.5, 1], [0, 170, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "view", 6, [-90], [[23, 0, -89]], [0, "46nJMNkH9AZrn3TO3OHwcC", 1], [5, 803, 348]], [12, "content", 19, [[21, false, 1, 2, 10, -91, [5, 1190, 75]]], [0, "71l0k8at9Jday9watt+HcL", 1], [5, 803, 75], [0, 0.5, 1], [0, 170, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "nen popup", 1, [[8, -92, 1]], [0, "9fuIFmJwpO1aFyCc+T5H1H", 1], [5, 959, 559]], [1, "title", 1, [[8, -93, 2]], [0, "d0tXncjrVE3q43Xl15Nt7r", 1], [5, 236, 35], [0, 252, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg<PERSON><PERSON>nt", 1, [[35, 1, 0, false, -94, 3]], [0, "84R5EfG8BCf7wPVrQnNrRg", 1], [5, 803, 355], [0, -68, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "sprite", 7, [[9, 2, false, -95, 4]], [0, "5caFsvtdlGMrhYGC533jZH", 1], [5, 81, 81]], [37, false, 2, 8, [[10, "3c6a1IcT7pG8b7cqDRMjmCv", "jackpotTabClicked", 1]], [4, **********], [4, **********], 8], [19, 2, 9, [[10, "3c6a1IcT7pG8b7cqDRMjmCv", "bigWinTabClicked", 1]], [4, **********], [4, **********], 9], [1, "lb\bSessionID", 4, [[3, "PHIÊN", 20, 50, false, false, 1, 1, 1, -96, 7]], [0, "f2mKQTOf1Oia44WXbMcOBS", 1], [5, 100, 30], [-342, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbTime", 4, [[3, "THỜI GIAN", 20, 50, false, false, 1, 1, 1, -97, 8]], [0, "829FRTdFBO9rHYS0De0pP3", 1], [5, 100, 30], [-201, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbAccount", 4, [[3, "TÀI KHOẢN", 20, 50, false, false, 1, 1, 1, -98, 9]], [0, "c4ked/Xr5MCqSNghmTAcl3", 1], [5, 100, 30], [-5, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbRoom", 4, [[3, "PHÒNG", 20, 50, false, false, 1, 1, 1, -99, 10]], [0, "85nRQ9QthEbY4Ju49lR4Bn", 1], [5, 100, 30], [137, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbWin", 4, [[3, "THẮNG", 20, 50, false, false, 1, 1, 1, -100, 11]], [0, "e6VGQsT35J5JRZyEZYbJZ/", 1], [5, 100, 30], [226.6, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "lbDesc", 4, [[3, "MÔ TẢ", 20, 50, false, false, 1, 1, 1, -101, 12]], [0, "f8JiMagRBJGYrvis6s2tXB", 1], [5, 100, 30], [339.6, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "temp", 5, [2], [0, "2aCWwxrUZEEJsvHAdxfIbT", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lb\bSessionID", 2, [-102], [0, "924Vqb/vRGd5o9KwOcXhkC", 1], [5, 200, 30], [-342, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "#********", 20, 50, false, false, 1, 1, 1, 34], [4, "lbTime", 2, [-103], [0, "897CBpFVNC8Z7jc4cbznV9", 1], [5, 220, 30], [-201, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "20:45 21-08-2018", 20, 50, false, false, 1, 1, 1, 36], [17, "lbSID", false, 11, [-104], [0, "76uQqP+KtEkqSE1NUvcp68", 1], [4, 4279026733], [5, 37, 25], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "[TQ]", 20, 50, false, false, 1, 1, 38], [4, "lbNickName", 11, [-105], [0, "b6LehMatZOxoalUiGo4CC6", 1], [5, 95, 25], [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "<PERSON><PERSON><PERSON>", 20, 50, false, false, 1, 1, 40], [5, "lbRoom", 2, [-106], [0, "2249R8h3lBdKVyjpYYpE2J", 1], [4, 4278255615], [5, 100, 30], [137, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "10.000", 20, 50, false, false, 1, 1, 1, 42], [5, "lbWin", 2, [-107], [0, "1cw1c30c9FLYnGu9xjL9Ez", 1], [4, 4278255615], [5, 200, 30], [226.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "50.000.000", 20, 50, false, false, 1, 1, 1, 44], [5, "lbDesc", 2, [-108], [0, "366yA/mLRO0KXn3pwhFRF1", 1], [4, 4278246399], [5, 200, 30], [339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THẮNG LỚN", 20, 50, false, false, 1, 1, 1, 46], [24, false, 0.75, 0.23, null, null, 5, 18], [38, 15, 10, 400, 5, 2, 48], [39, 10, 49], [14, "temp", 6, [3], [0, "a5bHLsqVFAtr64atDFbKbJ", 1], [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lb\bSessionID", 3, [-109], [0, "b7JhBtkaRJOZ6sFaeIhuMl", 1], [5, 200, 30], [-342, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHIÊN", 20, 50, false, false, 1, 1, 1, 52], [4, "lbTime", 3, [-110], [0, "b4ScWkhF9A25013ndze+ee", 1], [5, 220, 30], [-201, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THỜI GIAN", 20, 50, false, false, 1, 1, 1, 54], [17, "lbSID", false, 13, [-111], [0, "57UMzdzIxIh4pqLPw0xWwN", 1], [4, 4279026733], [5, 37, 25], [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "[TQ]", 20, 50, false, false, 1, 1, 56], [4, "lbNickName", 13, [-112], [0, "28yhp6nNZONK7fyEooXlbV", 1], [5, 95, 25], [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "<PERSON><PERSON><PERSON>", 20, 50, false, false, 1, 1, 58], [5, "lbRoom", 3, [-113], [0, "53mUO4ShxC97zQUwxSyTng", 1], [4, 4278255615], [5, 100, 30], [137, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "PHÒNG", 20, 50, false, false, 1, 1, 1, 60], [5, "lbWin", 3, [-114], [0, "500G9shEhIBJV7Zi5E7x5M", 1], [4, 4278255615], [5, 200, 30], [226.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "THẮNG", 20, 50, false, false, 1, 1, 1, 62], [5, "lbDesc", 3, [-115], [0, "acCF0NRftDHoIy0zvFqvae", 1], [4, 4294829568], [5, 200, 30], [339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "MÔ TẢ", 20, 50, false, false, 1, 1, 1, 64], [24, false, 0.75, 0.23, null, null, 6, 20], [40, 15, 10, 400, 6, 3, 66], [41, 12, 67]], 0, [0, 16, 1, 0, 0, 1, 0, 17, 50, 0, 18, 68, 0, 19, 12, 0, 20, 10, 0, 21, 26, 0, 22, 25, 0, 0, 1, 0, -1, 14, 0, -2, 21, 0, -3, 22, 0, -4, 23, 0, -5, 7, 0, -6, 8, 0, -7, 9, 0, -8, 4, 0, -9, 15, 0, -10, 16, 0, -11, 10, 0, -12, 12, 0, 5, 47, 0, 6, 45, 0, 7, 43, 0, 8, 41, 0, 9, 39, 0, 10, 37, 0, 11, 35, 0, 0, 2, 0, -1, 34, 0, -2, 36, 0, -3, 11, 0, -4, 42, 0, -5, 44, 0, -6, 46, 0, 5, 65, 0, 6, 63, 0, 7, 61, 0, 8, 59, 0, 9, 57, 0, 10, 55, 0, 11, 53, 0, 0, 3, 0, -1, 52, 0, -2, 54, 0, -3, 13, 0, -4, 60, 0, -5, 62, 0, -6, 64, 0, 0, 4, 0, -1, 27, 0, -2, 28, 0, -3, 29, 0, -4, 30, 0, -5, 31, 0, -6, 32, 0, -1, 48, 0, -2, 49, 0, -1, 33, 0, -2, 17, 0, -1, 66, 0, -2, 67, 0, -1, 51, 0, -2, 19, 0, 23, 7, 0, 0, 7, 0, -1, 24, 0, 0, 8, 0, -2, 25, 0, 0, 9, 0, -2, 26, 0, -1, 50, 0, 0, 11, 0, -1, 38, 0, -2, 40, 0, -1, 68, 0, 0, 13, 0, -1, 56, 0, -2, 58, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, -1, 18, 0, 0, 18, 0, 0, 19, 0, -1, 20, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, 0, 23, 0, 0, 24, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, -1, 35, 0, -1, 37, 0, -1, 39, 0, -1, 41, 0, -1, 43, 0, -1, 45, 0, -1, 47, 0, -1, 53, 0, -1, 55, 0, -1, 57, 0, -1, 59, 0, -1, 61, 0, -1, 63, 0, -1, 65, 0, 24, 1, 2, 3, 33, 3, 3, 51, 5, 3, 10, 6, 3, 12, 115], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 25, 25, 25, 26, 26, 26, 26, 35, 37, 39, 41, 43, 45, 47, 53, 55, 57, 59, 61, 63, 65], [2, 2, 2, 2, 2, 2, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 25, -1, -2, 12, 13, 14, 15, 12, 13, 14, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [6, 7, 8, 9, 10, 3, 2, 1, 1, 1, 1, 1, 1, 11, 1, 1, 4, 4, 12, 2, 2, 2, 3, 2, 2, 2, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]], [[{"name": "button off", "rect": [0, 0, 134, 71], "offset": [0, 0], "originalSize": [134, 71], "capInsets": [64, 0, 64, 0]}], [3], 0, [0], [4], [13]], [[{"name": "bxh", "rect": [0, 0, 236, 35], "offset": [0, 0], "originalSize": [236, 35], "capInsets": [0, 0, 0, 0]}], [3], 0, [0], [4], [14]]]]