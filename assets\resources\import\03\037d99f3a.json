[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "2bmZoWOI5CIqLSDqlgvIl7", "11ewsaO/lFzIkmSDPO+x2z", "6a4G4+RbhHyKqqOaqA4xwk", "43FgFyEmhMwp0gdhTuTevT", "15TqJIGghPRbUeUWSV2ffV", "dfQbBV8dpK0JRBWZR3zGfQ", "fa0JEQ585BFoq9kN30BSTe", "94/Z4szM5GnqVf6Ola0ppg", "4dXKxeZRdDHI1FesD3NALY", "97VdwFZ6dLdLWR9bX/Uo9i", "caMgThy1VMjaOc0JGk7H66", "a9VpD0DP5LJYQPXITZq+uj", "efY5X/fElAQ6uwXSaQ3Hds", "80NC2FR4dIM6qbuBKEpntK", "a4gZnE1bZKGbBiv0z3g3yu", "4cY7WEyolDNqraCVorY1vv", "b5IIEOIkdG9J8MO7H2PUes", "cbrbNpHxREtbMFr5OPdplY", "8eM0CVAvBH0obn8+EJpC8f", "a9hGP4m91HFqsLguJSeuCq", "eaqnIplqNErbdgYnwQx8GG", "eaU9euwJhIG45j5EP7fm6o", "d82n49/IVAvIEqsa0xvvk0", "fdNoodJKVLj4dF1TLppv2g", "60lCKF0BFLRJpfGf89TIRK", "bcbgFB+epIlKdGw1JroyQo", "84cbWRHshDY6cM42UYQNGn", "7a0l5GchdKwLgNH7EcMBkX", "24xd2Xl+xHVZeWwPN10Wzf", "6bMjFJEvJA8ZL6dZ7FFttz", "31Ocv9XW1AHKRIfdD5ZtoA", "ceozhxr4hDj7CIj3sFCoLE", "5eOaT1ozxJx5zIkTz18ogo", "f9TGEUO+ZB47z2jMM914+v", "f6ufQ6SdlM6JTU2BLJkcQa", "e5+rF8SI1DwKHlhuFGdy34", "3bk98SWbZBt7S68tKRjRo9", "b9ET1wCKVPgZy/IO5rG/V+", "2b5ExhYTNCxpIRXfAM0mL8", "14npym4UJN9KckWATh8o9J", "82sBTn0flK2KexTYdrOeji", "6cVqyF8gNDZrU7F085iDXT", "52vK6RrnlNZoXCWd76966P", "5cABgYCgBL6KjDtstKJ0fR", "3cfXmnlfJKU7Wb1UyZLedO", "9bww5s945HD42MJITTh6A6", "79S0qWPxNDnL09whSvxwXy", "b2pSzkn7RAhrAlu98L+fOe", "a3JTCykGNPBbVHrJGDdMpn", "8azDlpsXNK7az+dC0w4Agc", "4aFssfXlxKrLf6I+5E7ock", "73/PebF5ZBH6UNm+EBKBXI", "d1X2jV5upG6LexaG1LxyAM", "064bYDkbhP5LjQlrZYuGcA", "d9pBF3jZlAHaC8NSgrQKk6", "f2fCUuJWVCHKUtUXlJUbIm", "1b8xr2lRZBQ4n5tVAIsnnO", "b9sAPrSq9Bhq4NL0/rodI/", "9f2frFzWdCQpr+BjBS3wo+", "840Jl5rg1M9a6NAk0zwPHW", "78UsjaW+5Fj4PspIkMGsSL", "eeMYFIQ5xAAq1fqe71h/NR", "5b72Hj52pDm5ABBNDz1VGJ", "94bJoFmGpJma8nhzMjeIFA", "10iuQcxhxJ+IJHaM4cFvry", "582ts3JXhGs4Za0tVdw8AX", "84vbGj7+FNepG526cyvFRo", "0bqSDyYyVCEovDWnR1jQwP", "69+APD4OZCM4gpmctdnSNx", "007c9lo/NBGropDQCGjSj5", "d0ybIPJudLDpsLN8RmlLS8", "70wMkb7DBOS6HgeIx/DbMC", "902coHzjNB9KqpRTVsjH5X", "27uXxD+4NBtYInHIMkYgBl", "eaHGQy859GyazeVkE7Psyi", "aeiquu0ilM06X9fpQHUwJP", "28dsIXRrVNSpvhtVylosEf", "c8b+IZbOBLK4quBdDEfTbC", "77yI1jd+tPBKaxcybmHv8D", "e97GVMl6JHh5Ml5qEDdSGa", "f0BIwQ8D5Ml7nTNQbh1YlS", "29FYIk+N1GYaeWH/q1NxQO", "2fZIjzaBNLCLTBaIrkS0Wg", "f3vZglI69NEb33zEjXByeH", "10paj1R+RLIZxdDGbCK2kj", "cbmJ50bIhGead/YYUqNPmO", "05/k69cl1FpKZs3PPmGgqu", "f7Mj46m8tNkosfZTddJM+8", "e8oN4BXXxAXZOQPuh6jx+c", "cctuGsS4dOsav5wItCdzqL", "50FwycUB1Ncp0Mf8X1Wu6R", "f8qhKxquVFe5WPlVGTXPhI", "e8yPidfkFJ/Kr6rcBteued", "f9nBVbB69E7Jhl0LsbbfWP", "4482gLDRBB/qaawwh4zld6", "82ujlz+8ZL6Y8aT1JgaUN+", "762zLkBgJKQYMSVAMqDp1k", "2cWB/vWPRHja3uQTinHH30", "8fT/Bi5TRNlLeQqIXuzoyX", "125w58/9BP3KtF7tynnppU", "96h9+CfgdOFJeXLyN1FSzn", "5ccfR3nmxKzqbX/9bMwX+g", "bdXMx37QxPY7YX7lKQS2Xp", "d7UfaWJOVIRJE77kcYU9XG", "c0JzyHUexHBYz0GbAR5UQ0", "9bYDnu5NdJH7u79ZpCFpEO", "6dpqW883VHj5A+voQVzzib", "e4Ay7XgMVL+4end6+f7K00", "aeY5rQWbpKLqinwNskoJCm", "75XrxsWQ1LtrrCauLPArA2", "17AVOJWK1DF4sfIarOsL+y", "bfCSL/EGxNdac4DdARYnr3", "28K80YK2tNZ7rAdLZTYfOM", "eciFMHlpZD9pDJVpTnx9q/", "bbNjnPne1N54LbVvSQOkOt", "e1EEK3RudBHYnOZx0rRrBz", "00vqYevzZE2r4lG27Sq/71", "67s8Cm3YtNhKtx5wVcutPr", "7bY0ifBhhCkIlYOu9eR3LE", "e4rKJcPf9N2aVKhGuwKAY8", "09f45Hb7ND5b1pl5zOpaEX", "dcN0BpsExCwJNxf6hjqQkH", "b9kTvmB1tGN6bb8rUObGya", "fe+6M66XFKs52stdmjXMGt", "26DJyS+PhG+bUV+evripOp", "96TLzI5qVD1qwNrhlY68O0", "7bt3VOVYBJkJQB/hNqzGk+", "33s2l+7fhGhaLDRYAx3/Q1"], ["node", "_spriteFrame", "_textureSetter", "_N$file", "_N$skeletonData", "_defaultClip", "_file", "_N$target", "_parent", "_clip", "lblTextNotiNewGame", "nodeBGTimer", "touchParent", "spriteFrame", "root", "nodeMain", "lbUserBetXiu", "lbUserBetTai", "lbTotalBetXiu", "lbTotalBetTai", "lbTimer", "lbBigTimer", "lbSessionID", "nodeTornado", "lbBetXiu", "lbBetTai", "nodeLBBetResult", "animationBetResult", "nodeLBBetXiu", "nodeLBBetTai", "rollDice", "nodeXiu", "nodeTai", "winSound", "lbTotalDice", "nodeBgTotalDice", "xnEffect", "xnAnimationplay", "nodeBowl", "nodeResultDice", "nodeResult", "editBoxDatXiu", "editBoxDatTai", "audioChonSo", "lbBetXiuTemp", "lbBetTaiTemp", "nodeinputxiu", "nodeinputtai", "nodeInputFree", "nodeInputValue", "nodeInput", "spriteChat", "spriteNan", "nodeChat", "nodeLightOff", "btnSendChat", "editBoxChat", "chatListView", "rtAdmin", "spriteVIP", "lbMessage", "lbName", "nodeUser", "lbXiuWin", "lbTaiWin", "data", "btnHandle", "_normalMaterial", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail", "prefabGraph", "_N$font"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint", "_color", "_eulerAngles"], 0, 9, 4, 5, 1, 2, 7, 5, 5, 5], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "_type", "_enabled", "node", "_materials", "_spriteFrame"], -1, 1, 3, 6], ["cc.Label", ["_fontSize", "_isSystemFontUsed", "_N$verticalAlign", "_lineHeight", "_string", "_N$horizontalAlign", "_enableWrapText", "_N$overflow", "_spacingX", "_enabled", "_materials", "node", "_N$file"], -7, 3, 1, 6], ["cc.Node", ["_name", "_active", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_anchorPoint", "_color", "_children"], 0, 1, 2, 4, 5, 7, 5, 5, 2], ["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_children", "_parent", "_contentSize", "_trs", "_components"], 0, 4, 2, 1, 5, 7, 12], ["cc.<PERSON><PERSON>", ["_N$transition", "zoomScale", "_enabled", "_N$interactable", "node", "clickEvents", "_N$pressedColor", "_N$disabledColor", "_N$target", "_N$normalColor", "_N$pressedSprite", "_N$hoverSprite", "_N$disabledSprite", "_normalMaterial"], -1, 1, 9, 5, 5, 1, 5, 6, 6, 6, 6], ["cc.Layout", ["_N$layoutType", "_resize", "_N$spacingX", "_N$spacingY", "_enabled", "node", "_layoutSize"], -2, 1, 5], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["sp.Skeleton", ["defaultAnimation", "_preCacheMode", "_animationName", "defaultSkin", "premultipliedAlpha", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.AudioSource", ["node", "_clip"], 3, 1, 6], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.EditBox", ["returnType", "max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "editingReturn", "_N$textLabel", "_N$placeholderLabel", "_N$background", "textChanged"], 0, 1, 9, 1, 1, 1, 9], ["cc.ParticleSystem", ["_dstBlendFactor", "totalParticles", "emissionRate", "life", "lifeVar", "angleVar", "startSize", "startSizeVar", "startSpin", "_positionType", "speed", "speedVar", "tangentialAccel", "tangentialAccelVar", "radialAccel", "_enabled", "node", "_startColor", "_startColorVar", "_endColor", "_endColorVar", "posVar", "gravity", "_file"], -13, 1, 8, 8, 8, 8, 5, 5, 6], ["cc.BitmapFont", ["_name", "fontSize", "_fntConfig"], 0], ["cc.Prefab", ["_name"], 2], ["7cda1ZhTI9BX6LhZIPVztfk", ["node", "nodeTornado", "nodeBGTimer", "lbSessionID", "lbBigTimer", "lbTimer", "lbTotalBetTai", "lbTotalBetXiu", "lbUserBetTai", "lbUserBetXiu", "lblTextNotiNewGame", "nodeMain"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["b60dadO3BxK3oytZ2PXwoJo", ["node", "lbBetTai", "lbBetXiu"], 3, 1, 1, 1], ["9af0f6dzcxFzq4WUD4OZ1wU", ["node", "nodeResult", "nodeResultDice", "nodeBowl", "nodeBGTimer", "xnAnimation", "xnAnimationplay", "xnEffect", "nodeBgTotalDice", "lbTotalDice", "nodeTaiWins", "nodeXiuWins", "winSound", "nodeTai", "nodeXiu", "rollDice", "nodeLBBetTai", "nodeLBBetXiu", "animationBetResult", "nodeLBBetResult"], 3, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 2, 2, 1, 1, 1, 1, 1, 1, 1, 1], ["be2cdzw0clHA4Jg+GcALu/j", ["node"], 3, 1], ["f74d33qIsFLnqhF+m+kFzXV", ["node"], 3, 1], ["02bb1kRpk5FVZfaD1w3AxlF", ["node", "prefabHelp", "prefabHistory", "prefabTop", "prefabSessionDetail", "prefabGraph"], 3, 1, 6, 6, 6, 6, 6], ["f5a4bA3poJGYZQ88SPc6hs+", ["node", "nodeInput", "nodeInputValue", "nodeInputFree", "nodeinputtai", "nodeinputxiu", "lbBetTaiTemp", "lbBetXiuTemp", "lblTextNotiNewGame", "audioChonSo", "editBoxDatTai", "editBoxDatXiu"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["9e4ba74Za1FM7/o26LjTmND", ["node", "spriteSides", "sfSides"], 3, 1, 2, 3], ["79a43qxc5JLRZdcLtAklZTY", ["node", "spriteSides", "sfSides"], 3, 1, 2, 3], ["83045zvn81L3ZYULR1eENoJ", ["node", "nodeLightOff", "nodeChat", "spriteNan", "sfNans", "spriteChat", "sfChats"], 3, 1, 1, 1, 1, 3, 1, 3], ["c9ea2HJ+4FBwJf8JdBQBbUQ", ["channelId", "node", "chatListView", "editBoxChat", "btnSendChat"], 2, 1, 1, 1, 1], ["8457bzXi+RCFrWEyccPy/PF", ["node", "touchParent"], 3, 1, 1], ["8457bzXi+RCFrWEyccPy/PF", ["touchParent", "node"], 2, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["8be9fmDgjRGuJDpCIp+Bx7T", ["node", "nodeUser", "lbName", "lbMessage", "spriteVIP", "rtAdmin"], 3, 1, 1, 1, 1, 1, 1], ["cbf0avnl01NgI8Jid2gb8ms", ["node", "lbTaiWin", "lbXiuWin"], 3, 1, 1, 1], ["9d1bbBbrYlCd6GxBqWZU2YQ", ["node"], 3, 1], ["cc.Widget", ["alignMode", "_alignFlags", "_left", "_right", "_top", "_bottom", "_originalWidth", "_originalHeight", "node"], -5, 1], ["cc.Mask", ["node", "_materials"], 3, 1, 3], ["cc.RichText", ["_isSystemFontUsed", "_N$string", "_N$fontSize", "_N$maxWidth", "_N$lineHeight", "_N$handleTouchEvent", "node"], -3, 1], ["<PERSON><PERSON>", ["horizontal", "node", "_N$content"], 2, 1, 1], ["87b0b6j4kBLKKRiRXRQjf/0", ["spawnCount", "bufferZone", "node", "itemTemplate", "scrollView"], 1, 1, 1, 1], ["cc.WebView", ["_url", "node"], 2, 1]], [[10, 0, 1, 2], [1, 0, 6, 3, 4, 5, 8, 2], [6, 1, 0, 4, 5, 6, 7, 3], [2, 4, 5, 6, 1], [4, 0, 3, 4, 5, 6, 7, 2], [12, 0, 1, 3, 3], [12, 0, 1, 2, 3, 4], [1, 0, 1, 6, 3, 4, 5, 8, 3], [2, 0, 1, 5, 3], [2, 0, 4, 5, 2], [1, 0, 6, 7, 3, 4, 5, 8, 2], [2, 1, 4, 5, 6, 2], [1, 0, 1, 6, 7, 3, 4, 5, 8, 3], [1, 0, 1, 6, 3, 4, 8, 11, 3], [4, 0, 1, 3, 4, 5, 6, 7, 3], [9, 3, 0, 1, 4, 2, 5, 6, 7, 6], [1, 0, 6, 3, 4, 5, 2], [2, 0, 4, 5, 6, 2], [14, 15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 17], [3, 4, 0, 3, 6, 1, 5, 2, 11, 10, 8], [5, 0, 5, 8, 3, 6, 7, 2], [1, 0, 1, 6, 3, 4, 5, 3], [4, 0, 3, 4, 5, 6, 2], [4, 0, 3, 4, 5, 9, 6, 8, 7, 2], [4, 0, 3, 4, 5, 2], [8, 1, 2, 3, 1], [8, 1, 2, 1], [8, 0, 1, 2, 3, 2], [11, 0, 1], [7, 1, 0, 2, 5, 6, 4], [2, 3, 0, 4, 5, 3], [3, 4, 0, 3, 1, 8, 5, 2, 11, 10, 8], [5, 0, 5, 4, 3, 6, 7, 2], [5, 0, 5, 4, 3, 2], [5, 0, 5, 4, 8, 3, 6, 7, 2], [5, 0, 1, 5, 4, 3, 7, 3], [1, 0, 1, 7, 3, 4, 5, 8, 3], [1, 0, 2, 6, 3, 4, 5, 8, 3], [4, 0, 3, 10, 4, 5, 6, 7, 2], [4, 0, 1, 2, 3, 10, 4, 5, 6, 7, 4], [4, 0, 1, 2, 3, 4, 5, 9, 6, 8, 7, 4], [4, 0, 2, 3, 4, 5, 9, 6, 8, 7, 3], [11, 0, 1, 1], [2, 0, 1, 4, 5, 3], [2, 0, 1, 4, 5, 6, 3], [2, 3, 4, 5, 6, 2], [2, 4, 5, 1], [2, 1, 4, 5, 2], [2, 2, 0, 4, 3], [6, 4, 9, 8, 1], [6, 4, 5, 6, 7, 8, 1], [28, 0, 1, 1], [30, 0, 1], [9, 0, 1, 4, 2, 5, 6, 7, 5], [9, 3, 0, 1, 2, 5, 6, 5], [9, 3, 0, 1, 4, 2, 5, 6, 6], [3, 0, 3, 6, 1, 5, 2, 10, 7], [3, 4, 0, 3, 6, 1, 2, 7, 11, 10, 8], [3, 0, 6, 2, 7, 11, 5], [3, 4, 0, 3, 1, 5, 2, 11, 10, 7], [3, 4, 0, 1, 5, 11, 10, 12, 5], [13, 0, 1, 2, 3, 8, 4, 5, 6, 7, 4], [15, 0, 1, 2, 4], [16, 0, 2], [5, 0, 4, 8, 3, 6, 7, 2], [5, 0, 5, 4, 3, 6, 2], [5, 0, 2, 5, 4, 8, 3, 6, 7, 3], [5, 0, 1, 5, 4, 3, 3], [1, 0, 7, 3, 4, 5, 9, 8, 2], [1, 0, 6, 7, 3, 4, 2], [1, 0, 7, 3, 4, 5, 8, 2], [1, 0, 7, 3, 4, 5, 2], [1, 0, 1, 6, 7, 3, 4, 5, 3], [1, 0, 6, 7, 3, 4, 5, 9, 8, 2], [1, 0, 2, 6, 7, 3, 4, 3], [1, 0, 1, 6, 3, 4, 10, 5, 3], [1, 0, 2, 6, 7, 3, 4, 5, 8, 3], [1, 0, 6, 3, 4, 2], [4, 0, 1, 3, 4, 5, 6, 8, 7, 3], [4, 0, 1, 3, 4, 5, 9, 6, 8, 7, 3], [4, 0, 3, 4, 5, 7, 2], [4, 0, 3, 4, 5, 6, 8, 7, 2], [4, 0, 1, 3, 4, 5, 9, 6, 7, 3], [8, 0, 1, 2], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [18, 0, 1, 2, 1], [19, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 1], [20, 0, 1], [21, 0, 1], [22, 0, 1, 2, 3, 4, 5, 1], [10, 1, 1], [23, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [7, 4, 1, 0, 2, 3, 5, 6, 6], [7, 1, 0, 2, 3, 5, 6, 5], [7, 0, 2, 5, 6, 3], [7, 1, 0, 5, 6, 3], [24, 0, 1, 2, 1], [25, 0, 1, 2, 1], [26, 0, 1, 2, 3, 4, 5, 6, 1], [2, 2, 0, 1, 4, 5, 6, 4], [2, 2, 4, 5, 6, 2], [2, 0, 4, 2], [2, 2, 0, 4, 5, 3], [2, 3, 4, 2], [27, 0, 1, 2, 3, 4, 2], [6, 4, 1], [6, 0, 4, 5, 9, 6, 7, 8, 10, 11, 12, 2], [6, 1, 0, 4, 5, 13, 3], [6, 2, 1, 0, 4, 5, 6, 7, 4], [6, 1, 3, 0, 4, 5, 6, 7, 8, 4], [29, 0, 1, 2], [31, 0, 1, 2, 3, 4, 5, 1], [32, 0, 1, 2, 1], [33, 0, 1], [34, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9], [35, 0, 1, 1], [3, 4, 0, 3, 1, 8, 5, 2, 10, 8], [3, 4, 1, 5, 2, 11, 10, 12, 5], [3, 0, 3, 6, 1, 2, 7, 11, 7], [3, 4, 0, 3, 6, 1, 2, 11, 10, 7], [3, 4, 0, 6, 2, 7, 11, 10, 6], [3, 4, 0, 3, 6, 2, 7, 11, 10, 7], [3, 0, 3, 1, 5, 11, 10, 5], [3, 4, 0, 3, 6, 1, 7, 11, 10, 7], [3, 9, 0, 1, 5, 2, 11, 10, 6], [3, 0, 3, 1, 5, 2, 11, 10, 6], [13, 0, 1, 2, 3, 4, 5, 6, 7, 4], [36, 0, 1, 2, 3, 4, 5, 6, 7], [37, 0, 1, 2, 2], [38, 0, 1, 2, 3, 4, 3], [39, 0, 1, 2], [14, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 19, 20, 21, 22, 23, 16]], [[[{"name": "1KOff", "rect": [0, 0, 99, 57], "offset": [0, 0], "originalSize": [99, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [31]], [[{"name": "banChoi", "rect": [0, 0, 852, 389], "offset": [0, 0], "originalSize": [852, 389], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [32]], [[{"name": "500KOff", "rect": [0, 0, 99, 56], "offset": [0, 0.5], "originalSize": [99, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [33]], [[{"name": "50MOff", "rect": [0, 0, 99, 57], "offset": [0, 0], "originalSize": [99, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [34]], [[{"name": "taiden", "rect": [0, 0, 34, 28], "offset": [0, 0.5], "originalSize": [34, 29], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [35]], [[{"name": "da<PERSON><PERSON><PERSON><PERSON>", "rect": [0, 0, 262, 80], "offset": [0, 0], "originalSize": [262, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [36]], [[{"name": "icon_nan", "rect": [0, 0, 98, 99], "offset": [0, 0], "originalSize": [98, 99], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [37]], [[{"name": "btnChatOff", "rect": [0, 0, 53, 54], "offset": [0, 0], "originalSize": [53, 54], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [38]], [[{"name": "xiu", "rect": [0, 0, 164, 104], "offset": [0, 0], "originalSize": [164, 104], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [39]], [[{"name": "ic_nan", "rect": [0, 0, 98, 99], "offset": [0, 0], "originalSize": [98, 99], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [40]], [[{"name": "btnCloseOff", "rect": [0, 0, 53, 54], "offset": [0, 0], "originalSize": [53, 54], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [41]], [[{"name": "khungLive", "rect": [0, 0, 444, 320], "offset": [0, 0], "originalSize": [444, 320], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [42]], [[{"name": "xiu<PERSON><PERSON>", "rect": [0, 0, 34, 28], "offset": [0, 0.5], "originalSize": [34, 29], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [43]], [[{"name": "50KOn", "rect": [0, 0, 106, 63], "offset": [0, 0], "originalSize": [106, 63], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [44]], [[{"name": "allIn", "rect": [0, 0, 210, 90], "offset": [0, 0], "originalSize": [210, 90], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [45]], [[{"name": "ic_vin<PERSON><PERSON>h", "rect": [0, 0, 88, 89], "offset": [0, 0], "originalSize": [88, 89], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [46]], [[[62, "fontTotalMoney-export", 32, {"commonHeight": 35, "fontSize": 32, "atlasName": "fontTotalMoney-export.png", "fontDefDictionary": {"9": {"xOffset": 0, "yOffset": 0, "xAdvance": 160, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "32": {"xOffset": 0, "yOffset": 0, "xAdvance": 20, "rect": {"x": 0, "y": 0, "width": 0, "height": 0}}, "44": {"xOffset": 0, "yOffset": 25, "xAdvance": 11, "rect": {"x": 240, "y": 11, "width": 10, "height": 15}}, "46": {"xOffset": 0, "yOffset": 25, "xAdvance": 11, "rect": {"x": 240, "y": 0, "width": 10, "height": 10}}, "48": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 124, "y": 0, "width": 23, "height": 30}}, "49": {"xOffset": 0, "yOffset": 5, "xAdvance": 21, "rect": {"x": 219, "y": 0, "width": 20, "height": 30}}, "50": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 172, "y": 0, "width": 23, "height": 30}}, "51": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 0, "y": 0, "width": 24, "height": 30}}, "52": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 75, "y": 0, "width": 24, "height": 30}}, "53": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 148, "y": 0, "width": 23, "height": 30}}, "54": {"xOffset": 0, "yOffset": 5, "xAdvance": 24, "rect": {"x": 100, "y": 0, "width": 23, "height": 30}}, "55": {"xOffset": 0, "yOffset": 5, "xAdvance": 23, "rect": {"x": 196, "y": 0, "width": 22, "height": 30}}, "56": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 25, "y": 0, "width": 24, "height": 30}}, "57": {"xOffset": 0, "yOffset": 5, "xAdvance": 25, "rect": {"x": 50, "y": 0, "width": 24, "height": 30}}}, "kerningDict": {}}]], 0, 0, [0], [13], [47]], [[{"name": "dangBaoTri", "rect": [0, 0, 302, 36], "offset": [0, 0.5], "originalSize": [302, 37], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [48]], [[{"name": "input", "rect": [0, 0, 248, 70], "offset": [0, 0], "originalSize": [248, 70], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [49]], [[[63, "taiXiuSieuTocView"], [64, "taixiuSieuT<PERSON>iew", [-44, -45, -46], [[[25, -2, [221, 222], 220], [84, -14, -13, -12, -11, -10, -9, -8, -7, -6, -5, -4, -3], [85, -17, -16, -15], [86, -39, -38, -37, -36, -35, [-32, -33, -34], -31, -30, -29, -28, [-27], [-26], -25, -24, -23, -22, -21, -20, -19, -18], [87, -40], [88, -41], -42, [89, -43, 223, 224, 225, 226, 227]], 4, 4, 4, 4, 4, 4, 1, 4], [90, -1], [5, 860, 500], [-128, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [68, "betInputView", [-61, -62], [[26, -47, [127, 128]], [42, -48, 129], [91, -60, -59, -58, -57, -56, -55, -54, -53, -52, -51, -50, -49]], [0, "f1/Ciwkr9DrYMuBCRoQX+r", 1], [5, 680, 230], [0, 0.5, 1], [24, -259.096, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "taiXiuMain", 1, [-63, -64, -65, -66, -67, -68, -69, 2, -70, -71, -72, -73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88], [0, "f2PA3AISdOeICO/OiRMTCK", 1], [5, 900, 400], [1.673, -56.018, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "session<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", 3, [-124, -125, -126, -127, -128, -129, -130, -131, -132, -133, -134, -135, -136, -137, -138, -139], [[29, 1, 1, 18.1, -89, [5, 607.2, 40]], [96, -106, [-90, -91, -92, -93, -94, -95, -96, -97, -98, -99, -100, -101, -102, -103, -104, -105], [62, 63]], [97, -123, [-107, -108, -109, -110, -111, -112, -113, -114, -115, -116, -117, -118, -119, -120, -121, -122], [64, 65]]], [0, "84zLbYIDROxZQyE4m7nBO6", 1], [5, 607.2, 40], [0, -143.258, 0, 0, 0, 0, 1, 0.7, 0.5, 1]], [69, "buttonView", 3, [-145, -146, -147, -148, -149, -150, -151, -152, -153], [[98, -144, -143, -142, -141, [66, 67], -140, [68, 69]]], [0, "96LGW41hVLrYfxg1hiEyaV", 1]], [36, "layout-free", false, [-155, -156, -157, -158, -159, -160, -161, -162, -163, -164, -165, -166], [[92, false, 1, 3, 25.9, 15, -154, [5, 596, 107]]], [0, "86cL5cJoFDLYbbHeMalb7J", 1], [5, 596, 107], [2, -125, 0, 0, 0, 0, 1, 1, 1, 1]], [70, "layout-value", [-168, -169, -170, -171, -172, -173, -174, -175, -176, -177], [[93, 1, 1, 18, 15, -167, [5, 925, 86]]], [0, "d1JnSaiIJGoaZceGkpwCXM", 1], [5, 925, 86], [0, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "layout-infoBet", 3, [-178, -179, -180, -181, -182, -183, -184, -185, -186, -187], [0, "5d3NAB5TxG6LVDHjiwA47D", 1], [5, 200, 150], [0, -32, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "tornado", false, 3, [-190, -191, -192, -193, -194, -195], [[43, 2, false, -188, [156]], [25, -189, [158, 159, 160], 157]], [0, "91eBbots5A2IiMmRs3W6lP", 1], [5, 144, 147], [-5.257, 17.628, 0, 0, 0, 0, 1, 2.07, 2.07, 1]], [36, "chatView", false, [-200, -201, -202, -203], [[104, "", -199, -198, -197, -196]], [0, "59rGikyehL15ZugGeK2QZt", 1], [5, 360, 420], [-1154.3, 25.589, 0, 0, 0, 0, 1, 1, 1, 1]], [65, "layout-info", 3, [-204, -205, -206, -207, -208, -209, -210, -211], [0, "f7utxE0mtBeY7DdIIgeNtM", 1], [5, 200, 150]], [33, "result-sprite", 3, [-212, -213, -214, -215, -216], [0, "69OILfZQJMUIpeEueV6mFO", 1]], [10, "bat", 12, [-222], [[44, 2, false, -217, [198], 199], [49, -219, [4, 4292269782], -218], [51, -221, -220]], [0, "1eoeJVUctL5bEH5s8XGDrp", 1], [5, 279, 275], [-2.718, 20.874, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "scrollview", 10, [-226, -227], [[-223, [52, -224], -225], 1, 4, 1], [0, "feJr3qVLpBQ5ldigMpP7n0", 1], [5, 322, 300], [0, 7, 0, 0, 0, 0, 1, 1, 1, 1]], [71, "item", [-235, -236], [[29, 1, 2, 5, -228, [5, 321, 0]], [111, -234, -233, -232, -231, -230, -229]], [0, "f87v/ZvNdK6JU2FRvPLhmN", 1], [5, 321, 0]], [72, "item-horizontal", false, 15, [-238, -239, -240], [[94, 1, 5, -237, [5, 295, 33]]], [0, "e09E6Vr0BFpY3SDMXL/QxO", 1], [5, 295, 33]], [10, "bat", 3, [-246], [[44, 2, false, -241, [81], 82], [49, -243, [4, 4292269782], -242], [51, -245, -244]], [0, "ffXgTjfBhOmZ3NxpiMzrzp", 1], [5, 279, 275], [-2.718, 20.874, 0, 0, 0, 0, 1, 1, 1, 1]], [73, "bg-input", 2, [7, 6, -248], [[105, -247]], [0, "66VWM5sqRKZYPtSGsZZWdM", 1], [5, 880, 190], [0, 0.5, 1], [-13, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "layout-button", 18, [-250, -251, -252, -253], [[29, 1, 1, -30, -249, [5, 622, 100]]], [0, "dfYO7d9A5CMouMLksv74zK", 1], [5, 622, 100], [0, -135, 0, 0, 0, 0, 1, 1, 1, 1]], [35, "result-<PERSON><PERSON>", false, 12, [-254, -255, -256, -257], [0, "692uQjG7tDlrb9DKjUf2ko", 1], [0, 6, 0, 0, 0, 0, 1, 1, 1, 1]], [74, "resultEffectView", 0, 3, [-261, -262, -263, -264], [[112, -260, -259, -258]], [0, "e1VOoT8LdIVqhSNlGMfrsT", 1]], [37, "light_off", 160, 1, [[17, 0, -265, [0], 1], [26, -266, [2, 3]], [113, -267]], [0, "ffU/DJmmJLVYXV76BG1uXo", 1], [5, 4000, 4000], [-1.1368683772161603e-13, 3.410605131648481e-13, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg-main", false, 3, [-269, -270, -271], [[15, "default", "animation_table_dragon", 0, false, "animation_table_dragon", -268, [12], 13]], [0, "515YzdXMNISIVz9dU7CcI4", 1], [5, 1170.95, 841.72], [0, 0, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [10, "bg_popup_chat", 10, [-274, -275], [[99, 1, 0, false, -272, [70], 71], [52, -273]], [0, "70kDG7KvpFJIwiIR89/vwM", 1], [5, 374, 468], [0, -15, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [38, "editbox-chat", 10, [-277, -278, -279], [-276], [0, "d3G70mk45McYI1QLXe0l84", 1], [5, 200, 47.5], [-55.3, -176.3, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnOpenInputTai", 8, [[3, -280, [142], 143], [50, -282, [[5, "f5a4bA3poJGYZQ88SPc6hs+", "openInputBetTaiClicked", 2]], [4, 4294967295], [4, 4294967295], -281]], [0, "384IYjVFZJ778nnfcCWUAT", 1], [5, 184, 57], [-268.344, 28.914, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "editboxDatXiu", false, 0, 8, [-284, -285, -286], [-283], [0, "f4fCNOM+dM/bdIaVDF/hyn", 1], [5, 160, 40], [316, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "editboxDatTai", false, 0, 8, [-288, -289, -290], [-287], [0, "bfmT2f9jJI8LNedES0L1FN", 1], [5, 174, 64], [-250, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnOpenInputXiu", 8, [[3, -291, [140], 141], [50, -293, [[5, "f5a4bA3poJGYZQ88SPc6hs+", "openInputBetXiuClicked", 2]], [4, 4294967295], [4, 4294967295], -292]], [0, "19CYvRd4JLc5h9AOE8ho4u", 1], [5, 184, 57], [265.05, 28.359, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "bg-timer", false, 3, [-295], [[11, false, -294, [202], 203]], [0, "0f6Nbu03pAqKC0bjfSTv3Y", 1], [5, 51, 51], [-10.161, 174.724, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [12, "btnTop", false, 5, [-298], [[11, false, -296, [31], 32], [2, 1.1, 3, -297, [[5, "83045zvn81L3ZYULR1eENoJ", "topClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "46+Yc5MblJ86YaTNxGAdIs", 1], [5, 88, 89], [424.351, 101.904, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "dot", 4, [-301], [[-299, [83, true, -300]], 1, 4], [0, "a1X8s7Z7xMyq3IXDO9qK39", 1], [5, 30, 38], [288.59999999999997, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [38, "btnSend", 10, [-303], [-302], [0, "177h3AgtBNyZrjOAqB5i8a", 1], [5, 80, 44], [108.4, -182.7, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "btnAllin", 19, [-307], [[3, -304, [118], 119], [106, 3, -306, [[5, "f5a4bA3poJGYZQ88SPc6hs+", "allInClick", 2]], [4, 4293322470], [4, 4291348680], [4, 3363338360], -305, 120, 121, 122]], [0, "90ZgdbrpBMirFThZHyolZS", 1], [5, 210, 90], [-206, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [75, "Background", false, 34, [[100, 1, -308, [116], 117], [114, 0, 45, -53, -53, -0.5, -0.5, 100, 40, -309]], [0, "f3FRrARztHGqOXh9GOEQcW", 1], [4, 4293322470], [5, 256, 61]], [76, "bgNotify", 0, 3, [-312], [[17, 0, -310, [144], 145], [25, -311, [147, 148], 146]], [0, "90z6E5B15GGoEniPkdWcZP", 1], [5, 560, 50], [0, 44, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bg_timew", false, 12, [[30, false, 0, -313, [191]]], [0, "226d3UbMRMy46bsEli4ix9", 1], [5, 48, 48], [101, 82, 0, 0, 0, 0, 1, 1, 1, 1]], [66, "bgresult", 0, 3, [-316], [[-314, [17, 0, -315, [218], 219]], 1, 4], [0, "6fWnomkaNLZYAGgmbHenLa", 1], [5, 560, 50], [0, 44, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "logo", 3, [-318], [[3, -317, [20], 21]], [0, "2e8rREw71KBZkXPTVpAmJg", 1], [5, 363, 108], [20.602, 274.009, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "close", 24, [[3, -319, [24], 25], [107, 1.1, 3, -320, [[5, "83045zvn81L3ZYULR1eENoJ", "chatClicked", 5]], 26]], [0, "5bWaRypTJB97nj51DuP77i", 1], [5, 96, 50], [156, 212, 0, 0, 0, 0, 1, 0.7, 0.7, 1]], [7, "btnHelp", false, 5, [[11, false, -321, [27], 28], [2, 1.1, 3, -322, [[5, "83045zvn81L3ZYULR1eENoJ", "helpClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "30/RiPZ2RAXJY+GyzdbF4q", 1], [5, 89, 89], [-423.048, 101.904, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnHistory", 5, [[11, false, -323, [33], 34], [2, 1.1, 3, -324, [[5, "83045zvn81L3ZYULR1eENoJ", "historyClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "5cEqbomeZC2LKYwCJOzqiA", 1], [5, 53, 54], [-335.936, 208.107, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "btnNan", false, 5, [-325, -326], [0, "75qOvftIFOW4kS5SL8dbj1", 1], [5, 98, 99], [-404.838, -128.679, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnClose", 5, [[11, false, -327, [36], 37], [2, 1.1, 3, -328, [[5, "be2cdzw0clHA4Jg+GcALu/j", "closeClicked", 1]], [4, 4294967295], [4, 4294967295]]], [0, "60ZBR65J1Gwp2EFOIOuc+p", 1], [5, 53, 54], [291.309, 221.139, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "btnChat", 5, [[-329, [2, 1.1, 3, -330, [[5, "83045zvn81L3ZYULR1eENoJ", "chatClicked", 5]], [4, 4294967295], [4, 4294967295]]], 1, 4], [0, "8bkmx9EdZPZbtY/qQZFREU", 1], [5, 53, 54], [398.716, 134.047, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnGraph", 5, [[11, false, -331, [39], 40], [2, 1.1, 3, -332, [[5, "83045zvn81L3ZYULR1eENoJ", "graphClicked", 5]], [4, 4294967295], [4, 4294967295]]], [0, "55dZGZPxxMAZGh2sNO4TtR", 1], [5, 53, 54], [-420.515, 101.904, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [7, "btnEvent", false, 5, [[11, false, -333, [41], 42], [2, 1.1, 3, -334, [[5, "79a43qxc5JLRZdcLtAklZTY", "sessionDetailClicked", 4]], [4, 4294967295], [4, 4294967295]]], [0, "90wpAWK3BNfZsBllWpFoqg", 1], [5, 65, 65], [-420.515, -83.111, 0, 0, 0, 0, 1, 1, 1, 1]], [8, 0, false, [43]], [8, 0, false, [44]], [8, 0, false, [45]], [8, 0, false, [46]], [8, 0, false, [47]], [8, 0, false, [48]], [8, 0, false, [49]], [8, 0, false, [50]], [8, 0, false, [51]], [8, 0, false, [52]], [8, 0, false, [53]], [8, 0, false, [54]], [8, 0, false, [55]], [8, 0, false, [56]], [8, 0, false, [57]], [21, "dot-outline", false, 32, [[9, 0, -335, [58]], [27, true, -336, [60], 59]], [0, "55luKeYlpDlp2x5srtni7u", 1], [5, 36, 44]], [43, 0, false, 32, [61]], [10, "view", 14, [-338], [[115, -337, [78]]], [0, "ec/6ZA3WFGJ766Dm4h76bM", 1], [5, 325, 300], [0, -1, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "content", 65, [[95, 1, 2, -339, [5, 300, 0]]], [0, "2aRhuuAc5GTZZjgTJQiRVc", 1], [5, 300, 0], [0, 141, 0, 0, 0, 0, 1, 1, 1, 1]], [110, null, 1], [10, "khungLive", 3, [-341], [[3, -340, [83], 84]], [0, "ecO1WwUWVBJbEBH6dRq1lb", 1], [5, 444, 320], [661.371, 165.942, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[3, -342, [85], 86], [2, 1.1, 3, -343, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "1000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "78bkHfEhRIGK1LxcEEEkQ2", 1], [5, 99, 57], [-413, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "btnValue", false, 7, [[9, 0, -344, [87]], [2, 1.1, 3, -345, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "5000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "bbqrDXsAFEPIcYHdzcn4fT", 1], [5, 118, 54]], [1, "btnValue", 7, [[3, -346, [88], 89], [2, 1.1, 3, -347, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "10000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "1b4w/c4oFME6Db63LqkXr0", 1], [5, 99, 56], [-296, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[3, -348, [90], 91], [2, 1.1, 3, -349, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "50000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "39Bq5L749I/rqCKwljCB3M", 1], [5, 106, 63], [-175.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[3, -350, [92], 93], [2, 1.1, 3, -351, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "100000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "c3/hrjI2xFMaFCUiGow2Z9", 1], [5, 99, 57], [-55, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "btnValue", false, 7, [[30, false, 0, -352, [94]], [2, 1.1, 3, -353, [[6, "347b1xdyqpC24YIVRrVdPlw", "betValueClicked", "200000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "d55TPF3ChAY4HV8HosJ6lk", 1], [5, 118, 54]], [1, "btnValue", 7, [[3, -354, [95], 96], [2, 1.1, 3, -355, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "500000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "e5tAnMvflDQ6Zokl451Q7L", 1], [5, 99, 56], [62, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[3, -356, [97], 98], [2, 1.1, 3, -357, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "1000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "d36DLZwWJLBbEOWDAjjtGw", 1], [5, 99, 57], [179, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[3, -358, [99], 100], [2, 1.1, 3, -359, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "10000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "b5pUIbFQlDgKOjH4A4xEBJ", 1], [5, 99, 56], [296, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 7, [[3, -360, [101], 102], [2, 1.1, 3, -361, [[6, "f5a4bA3poJGYZQ88SPc6hs+", "betValueClicked", "50000000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "dakYzp9blBErbuGOp461U2", 1], [5, 99, 57], [413, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -362, [103]], [2, 1.1, 3, -363, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "1", 2]], [4, 4294967295], [4, 4294967295]]], [0, "eeiBwe0RhPUrfHJoIuKOXp", 1], [5, 78, 47], [-259.5, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -364, [104]], [2, 1.1, 3, -365, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "2", 2]], [4, 4294967295], [4, 4294967295]]], [0, "9cudEVK5pNY5k7Ymr8cKxe", 1], [5, 78, 47], [-156.6, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -366, [105]], [2, 1.1, 3, -367, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "3", 2]], [4, 4294967295], [4, 4294967295]]], [0, "60piKM4pJAkZVJRczHb46G", 1], [5, 78, 47], [-53.699999999999996, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -368, [106]], [2, 1.1, 3, -369, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "4", 2]], [4, 4294967295], [4, 4294967295]]], [0, "78IzivSFVDdatAgURbUzpj", 1], [5, 78, 47], [49.2, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -370, [107]], [2, 1.1, 3, -371, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "5", 2]], [4, 4294967295], [4, 4294967295]]], [0, "41SCJX7+9Oy7lgsNsyzJ8q", 1], [5, 78, 47], [152.1, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue000", 6, [[9, 0, -372, [108]], [2, 1.1, 3, -373, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "000", 2]], [4, 4294967295], [4, 4294967295]]], [0, "30FsS6HuxI142OfsKY2zi6", 1], [5, 78, 47], [255, 30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -374, [109]], [2, 1.1, 3, -375, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "6", 2]], [4, 4294967295], [4, 4294967295]]], [0, "4avwFYr4hJVYYWuaxGnmFa", 1], [5, 78, 47], [-259.5, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -376, [110]], [2, 1.1, 3, -377, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "7", 2]], [4, 4294967295], [4, 4294967295]]], [0, "30Q5t8+iNBZ68WnUOjdyuJ", 1], [5, 78, 47], [-156.6, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -378, [111]], [2, 1.1, 3, -379, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "8", 2]], [4, 4294967295], [4, 4294967295]]], [0, "6fXg09XO5PIb/m6i3MYk4e", 1], [5, 78, 47], [-53.699999999999996, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -380, [112]], [2, 1.1, 3, -381, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "9", 2]], [4, 4294967295], [4, 4294967295]]], [0, "a88b8I7FdN7J37O8MtoVrB", 1], [5, 78, 47], [49.2, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnValue", 6, [[9, 0, -382, [113]], [2, 1.1, 3, -383, [[6, "347b1xdyqpC24YIVRrVdPlw", "addValueClicked", "0", 2]], [4, 4294967295], [4, 4294967295]]], [0, "a2trlw+IBNlK1tiwFhfr21", 1], [5, 78, 47], [152.1, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btnDelete", 6, [[9, 0, -384, [114]], [2, 1.1, 3, -385, [[5, "347b1xdyqpC24YIVRrVdPlw", "deleteClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "90xqiS7H5C6o43XYGl93sP", 1], [5, 78, 47], [255, -30.5, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "btnOther", false, 19, [[30, false, 0, -386, [115]], [108, false, 1.1, 3, -387, [[5, "347b1xdyqpC24YIVRrVdPlw", "otherClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "4dy4pAjzBHoLEKyn8rWyPs", 1], [5, 150, 60], [-240, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "btnConfirm", 19, [[3, -388, [123], 124], [2, 1.1, 3, -389, [[5, "f5a4bA3poJGYZQ88SPc6hs+", "confirmClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "ecOle4eUxO363N/tVHUaPN", 1], [5, 262, 80]], [1, "btnCancel", 19, [[3, -390, [125], 126], [2, 1.1, 3, -391, [[5, "f5a4bA3poJGYZQ88SPc6hs+", "cancelClicked", 2]], [4, 4294967295], [4, 4294967295]]], [0, "93Itmh0PNAu7XVEy69Ofev", 1], [5, 210, 90], [206, 5, 0, 0, 0, 0, 1, 1, 1, 1]], [56, 21, 0, false, false, 1, 1, [138]], [56, 21, 0, false, false, 1, 1, [139]], [116, "<PERSON>n mời nặn", 18, 0, false, -1, 1, 1, [149]], [7, "taiWin", false, 3, [[53, "<PERSON><PERSON><PERSON>", 0, false, "<PERSON><PERSON><PERSON>", -392, [161], 162]], [0, "c3gFqZcRVHrKfLpm8Bv2Kj", 1], [5, 311, 301], [-272.289, 84.2, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "x<PERSON><PERSON><PERSON>", false, 3, [[53, "<PERSON><PERSON><PERSON>", 0, false, "<PERSON><PERSON><PERSON>", -393, [163], 164]], [0, "abgsXJJ0JLLbdIAtRNcNRB", 1], [5, 311, 301], [250.866, 84.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "tai", 3, [[3, -394, [167], 168]], [0, "f610OHwRJA3IX3e8khoJSC", 1], [5, 146, 101], [-277.786, 140.567, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "xiu", 3, [[3, -395, [169], 170]], [0, "feQb5yjUlCuYpX3Evlc7C0", 1], [5, 164, 104], [257.706, 145.587, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sprite", 11, [-397], [[45, false, -396, [179], 180]], [0, "cb7hSH+PRPkZBNPoox6rEh", 1], [5, 74, 29], [-161.72, -95.663, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "sprite", 11, [-399], [[45, false, -398, [182], 183]], [0, "5cs0mruTpLVqyl9sGCh+MJ", 1], [5, 74, 28], [155.11, -98.545, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "cancua", false, 11, [-401], [[46, -400, [187]]], [0, "3eLcGYTWNKlrn1k47Sdvco", 1], [5, 496, 45], [35, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "newSesion", false, 11, [-403], [[46, -402, [190]]], [0, "5aAL6eo35Ee6zzy9zFVhwV", 1], [5, 496, 45], [35, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "lbTaiWin", 21, [[-404, [27, true, -405, [210], 209]], 1, 4], [0, "be5kRNiA9Pp4rj60rKQK8D", 1], [5, 267.88, 0], [0, -78, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "lbXiuWin", 21, [[-406, [27, true, -407, [213], 212]], 1, 4], [0, "64equav59L8q+SS/YxUzG1", 1], [5, 267.88, 0], [0, -78, 0, 0, 0, 0, 1, 1, 1, 1]], [37, "light_off copy", 150, 1, [[17, 0, -408, [4], 5]], [0, "fd8UAxPlBHWbpF4M8BG7Db", 1], [5, 4000, 4000], [-1.1368683772161603e-13, 3.410605131648481e-13, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "sieutoc", false, 23, [[15, "default", "animation", 0, false, "animation", -409, [6], 7]], [0, "7aSiZJf2xFGKvAv5pHLixY", 1], [5, 573.9, 72], [0, 291.827, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "rectangle5", 23, [[3, -410, [8], 9]], [0, "10GagSVehMw54Q4NGIqo+U", 1], [5, 326, 39], [-341.961, 9.362, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "rectangle5", 23, [[3, -411, [10], 11]], [0, "dfNdEKdWBFpJia75z+74AZ", 1], [5, 326, 39], [335.492, 9.362, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "time_bg", false, 3, [[3, -412, [14], 15]], [0, "239dvsbx5ODZrSlwPIiUVI", 1], [5, 252, 252], [0, 9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "banChoi", 3, [[3, -413, [16], 17]], [0, "23u9thjhRHA5Sb3Nl+SNF/", 1], [5, 852, 389], [-2.06, 39.144, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 39, [[117, "<PERSON><PERSON><PERSON>:", false, 1, 1, -414, [18], 19]], [0, "93Bk55/KFMtaGtQICIALyl", 1], [5, 198.75, 40], [-95.044, -36.543, 0, 0, 0, 0, 1, 0.484, 0.484, 0.484]], [35, "offset-chat", false, 3, [10], [0, "d1Thc7nc1OL6ve2EbJVZ1G", 1], [694.639, -50.626, 0, 0, 0, 0, 1, 1.2, 1.35, 1]], [1, "boder", 24, [[3, -415, [22], 23]], [0, "82g7MaEBdI24AYTaSIv8bk", 1], [5, 404, 494], [0, 24, 0, 0, 0, 0, 1, 0.91, 0.68, 1]], [7, "Gift", false, 31, [[3, -416, [29], 30]], [0, "ffmijmlq9JBZS3PfDoVlUs", 1], [5, 49, 49], [35, 20, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [47, false, 43, [35]], [2, 1.1, 3, 43, [[5, "83045zvn81L3ZYULR1eENoJ", "nanClicked", 5]], [4, 4294967295], [4, 4294967295]], [47, false, 45, [38]], [14, "dot", false, 4, [48], [0, "b7dQZYwm9HYoqpwFp86Xk1", 1], [5, 30, 38], [-337.5, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "dot", false, 4, [49], [0, "3bvI7x+YRJH5DcDv/3AVs7", 1], [5, 30, 38], [-336.70000000000005, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "dot", false, 4, [50], [0, "28hSBOqF5MhqqFe8gDY3vM", 1], [5, 30, 38], [-288.6, -8, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "dot", 4, [51], [0, "93vmqdFt9FEIuCMd9Sr0s/", 1], [5, 30, 38], [-288.6, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [52], [0, "b8KABM67hD45uhCjuGDPP8", 1], [5, 30, 38], [-240.50000000000003, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [53], [0, "9b3eXFoUlAv6y1/FPJSoJt", 1], [5, 30, 38], [-192.40000000000003, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [54], [0, "ddu25zBJlAdYqgKvASDRqL", 1], [5, 30, 38], [-144.30000000000004, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [55], [0, "3cYkRedvNC4JNOfJSey88c", 1], [5, 30, 38], [-96.20000000000005, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [56], [0, "c3dJ6j8y1DWKadY+vEf9U0", 1], [5, 30, 38], [-48.100000000000044, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [57], [0, "bf2IH0oI5MAYHmOgxtSF8l", 1], [5, 30, 38], [-4.263256414560601e-14, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [58], [0, "98EfLMCU1ACauJTPPOE8Hf", 1], [5, 30, 38], [48.09999999999996, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [59], [0, "e7ISrWTwtBnoXTIDjhGfv0", 1], [5, 30, 38], [96.19999999999996, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [60], [0, "60lAZWl+RKbr3nA8v+0KV/", 1], [5, 30, 38], [144.29999999999995, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [61], [0, "20U2Ra1ElP5baMw/yCvaWP", 1], [5, 30, 38], [192.39999999999995, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [4, "dot", 4, [62], [0, "7cxq3f2iJBBb1CK0TJnxD0", 1], [5, 30, 38], [240.49999999999994, -13.916, 0, 0, 0, 0, 1, 1.396, 1.396, 1.396]], [7, "bt_big", false, 5, [[101, 0, -417]], [0, "d3ysmLEMVLOq3lx4aKH0dJ", 1], [5, 75, 75], [300, 195, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "sprite", 33, [[3, -418, [72], 73]], [0, "f5QxZPRJdC/JlXuLIQfFp7", 1], [5, 93, 53], [4, 5.5, 0, 0, 0, 0, 1, 1, 1, 1]], [109, 1.1, false, 3, 33, [[5, "c9ea2HJ+4FBwJf8JdBQBbUQ", "sendChatClicked", 10]], [4, 4294967295], [4, 4294967295], 33], [22, "BACKGROUND_SPRITE", 25, [-419], [0, "2dsk/T0mhFTKNbhUUz6/AO", 1], [5, 220, 47.5]], [102, 1, 0, 138, [74]], [78, "TEXT_LABEL", false, 25, [-420], [0, "e01KBllTZHTYQdkrqZ9I5U", 1], [5, 197.3, 47.5], [0, 0, 1], [-97.65, 23.75, 0, 0, 0, 0, 1, 1, 1, 1]], [118, 18, 42, false, false, 1, 1, 140], [23, "PLACEHOLDER_LABEL", 25, [-421], [0, "64paPc+ZhOdqychdYt0s3x", 1], [4, 4290493371], [5, 197.3, 47.5], [0, 0, 1], [-97.65, 23.75, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "<PERSON><PERSON><PERSON><PERSON> nội dung chat", 18, 47.5, false, false, 1, 1, 142, [75]], [126, 2, 250, 6, 25, [[5, "c9ea2HJ+4FBwJf8JdBQBbUQ", "editingReturn", 10]], 141, 143, 139], [33, "temp", 14, [15], [0, "85WIWEROlOp7NmX1iBHQRc", 1]], [79, "rtChat", false, 15, [-422], [0, "10qEssX8VOCLfN0JCbuHk5", 1], [4, 4291743438], [5, 321, 27.72], [0, 0, 0.5], [-161, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [127, false, "", 17, 321, 22, false, 146], [23, "lbNickName", 16, [-423], [0, "0c+S06bqNPzZcyGrLqJOtU", 1], [4, 4278224383], [5, 85.92, 25.2], [0, 0, 0.5], [-147.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [119, "Nick<PERSON>ame:", 16, 20, false, false, 1, 148, [76]], [23, "lbMessage", 16, [-424], [0, "5aW9StTIxP6ZkyDmBzNdTd", 1], [4, 4294440951], [5, 226, 23], [0, 0, 0.5], [-56.58, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [57, "1234567890123456789 ", 16, 20, false, false, 1, 1, 150, [77]], [14, "V10", false, 16, [-425], [0, "7dm8qGN0FB87Uhip0mGutQ", 1], [5, 42, 28], [-54.620000000000005, 1, 0, 0, 0, 0, 1, 0.9, 0.9, 1]], [103, false, 152], [128, false, 14, 66], [129, 15, 400, 14, 15, 154], [16, "Go_Livestrem_Loading", 17, [[15, "default", "animation", 0, false, "animation", -426, [79], 80]], [0, "7fFPaiO5FBYqRcgnYpiEpq", 1], [5, 219, 118]], [1, "New WebView", 68, [[130, "https://gosun.win/livetxgo88/", -427]], [0, "47++bDNjVOSYBb37CQnD5+", 1], [5, 300, 213.2], [0, 0.5, 0, 0, 0, 0, 1, 1.445, 1.445, 1.445]], [24, "audioChonSo", 2, [-428], [0, "1cUvVpNzBO+aOpgd7T9Yd2", 1]], [28, 158], [22, "BACKGROUND_SPRITE", 27, [-429], [0, "15iWADZV5NcL5xtXczQumv", 1], [5, 160, 40]], [48, 1, 0, 160], [40, "TEXT_LABEL", false, 0, 27, [-430], [0, "83BdZnRe1EnpJu4zMZqsnH", 1], [4, 16777215], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [58, 20, false, 1, 1, 162], [41, "PLACEHOLDER_LABEL", 0, 27, [-431], [0, "baP0ryPclNartQqiXPDp7z", 1], [4, 12303291], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [120, "Enter text here...", 20, false, 1, 1, 164, [130]], [61, 1, 9, 5, 27, [[5, "347b1xdyqpC24YIVRrVdPlw", "onTextXiuChange", 2]], [[5, "347b1xdyqpC24YIVRrVdPlw", "confirmClicked", 2]], 163, 165, 161], [22, "BACKGROUND_SPRITE", 28, [-432], [0, "8bnrzkqqdJGJY+3RAcbpi4", 1], [5, 174, 64]], [48, 1, 0, 167], [40, "TEXT_LABEL", false, 0, 28, [-433], [0, "bfiUqwD21DwaNDuCveWnhb", 1], [4, 16777215], [5, 172, 64], [0, 0, 1], [-85, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [58, 20, false, 1, 1, 169], [41, "PLACEHOLDER_LABEL", 0, 28, [-434], [0, "11F9nilixI0L6T1H0Ksgum", 1], [4, 12303291], [5, 172, 64], [0, 0, 1], [-85, 32, 0, 0, 0, 0, 1, 1, 1, 1]], [121, "Enter text here...", 20, 64, false, 1, 1, 171, [131]], [61, 1, 9, 5, 28, [[5, "347b1xdyqpC24YIVRrVdPlw", "onTextTaiChange", 2]], [[5, "347b1xdyqpC24YIVRrVdPlw", "confirmClicked", 2]], 170, 172, 168], [7, "bgLbTai", false, 8, [[3, -435, [132], 133]], [0, "48Y7nvAqhM4qV0GTiUYMK+", 1], [5, 248, 70], [-257.35, -12.69, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [7, "bgLbTai copy", false, 8, [[3, -436, [134], 135]], [0, "efxjyd7pxOGpP55bS/WtNF", 1], [5, 248, 70], [263.593, -12.345, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [4, "lbInputBetTai", 8, [-437], [0, "a3h3SsRYtCYZaRR4GzllwJ", 1], [5, 13.5, 30], [-268.344, 37.914, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 24, 30, false, false, 1, 1, 176, [136]], [4, "lbInputBetXiu", 8, [-438], [0, "9ephdnlrNMI7arxshZ5jDT", 1], [5, 13.5, 30], [267.05, 38.359, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "0", 24, 30, false, false, 1, 1, 178, [137]], [4, "lbBetTai", 8, [94], [0, "00VLJ6PTRN563H1yM8HKn/", 1], [5, 0, 26.46], [-255.35, -66.39, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "lbBetXiu", 8, [95], [0, "75ku7w8N5KEJ44oQg/IIgz", 1], [5, 0, 26.46], [266.593, -65.345, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "txtNotify", 36, [96], [0, "d2N3JF/flAXq/mYOoXBWoG", 1], [5, 164.38, 0], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "lua1", false, 9, [[131, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -439, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 150]], [0, "1f6OBAh1ZBYqZUU7xCjkxi", 1], [21.4, 77.6, 0, 0, 0, 0.6946583704589973, 0.7193398003386512, 0.3, 0.2, 1], [1, 0, 0, 88]], [13, "lua1 copy", false, 9, [[18, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -440, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 151]], [0, "8c/9i5SJlK2ounokFfwrbV", 1], [-18.1, 78.2, 0, 0, 0, 0.8290375725550417, 0.5591929034707468, 0.2, 0.2, 1], [1, 0, 0, 112]], [13, "lua1 copy", false, 9, [[18, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -441, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 152]], [0, "cewttrDrRNgKBkEjDoiCcl", 1], [-53.7, 57.1, 0, 0, 0, 0.9426414910921784, 0.3338068592337709, 0.2, 0.2, 1], [1, 0, 0, 141]], [13, "lua1 copy", false, 9, [[18, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -442, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 153]], [0, "08yipo9GdNu4TINT9VT4Ts", 1], [-20.9, -75.8, 0, 0, 0, -0.7431448254773942, 0.6691306063588582, 0.3, 0.2, 1], [1, 0, 0, -96]], [13, "lua1 copy", false, 9, [[18, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -443, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 154]], [0, "b54NYssnxEpoXhnovh05zu", 1], [20.4, -75.3, 0, 0, 0, -0.5591929034707469, 0.8290375725550416, 0.2, 0.2, 1], [1, 0, 0, -68]], [13, "lua1 copy", false, 9, [[18, false, 1, 15, 100, 0.15, 0.25, 11, 56, 3, -85, 1, 328, 291, 800, 194, 167, -444, [4, 4294914867], [4, 0], [4, 4294940979], [4, 0], [0, 0, 5], [0, 519, 0], 155]], [0, "e4F/EbckZCAqiN+k8UKtJx", 1], [52.2, -58.4, 0, 0, 0, -0.3826834323650898, 0.9238795325112867, 0.2, 0.2, 1], [1, 0, 0, -45]], [7, "tai", false, 3, [[15, "default", "<PERSON><PERSON><PERSON>", 0, false, "<PERSON><PERSON><PERSON>", -445, [165], 166]], [0, "0aZUoukDlEo7mReWGR3Ryk", 1], [5, 311, 301], [-272.289, 84.2, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "xiu", false, 3, [[15, "default", "<PERSON><PERSON><PERSON>", 0, false, "<PERSON><PERSON><PERSON>", -446, [171], 172]], [0, "08HEJaNexPCa/mbvum/mUp", 1], [5, 311, 301], [250.866, 84.9, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg_soicau", 3, [[3, -447, [173], 174]], [0, "d8gM0bCopGt7Bv0Vcelw1E", 1], [5, 552, 41], [0, -148.858, 0, 0, 0, 0, 1, 0.8, 0.8, 1]], [80, "lbBigTimer", 11, [-448], [0, "aaVxHxrMlISLsP7U0bQf+c", 1], [-6.624, 11, 0, 0, 0, 0, 1, 1, 1, 1]], [122, 36, 0, false, 1, 192, [175]], [4, "lbTotalBetTai", 11, [-449], [0, "aafQ7FPg5C0KnRPaCPXtbL", 1], [5, 100.88, 0], [-268.182, 60.577, 0, 0, 0, 0, 1, 2.376, 2.376, 2.376]], [19, "1,000,000,000", 12, 0, false, false, 1, 1, 194, [176]], [4, "lbTotalBetXiu", 11, [-450], [0, "92n0z7S3ZEsb4k749KcWSf", 1], [5, 100.88, 0], [269.06, 64.112, 0, 0, 0, 0, 1, 2.376, 2.376, 2.376]], [19, "1,000,000,000", 12, 0, false, false, 1, 1, 196, [177]], [4, "lbUserTai", 101, [-451], [0, "27Ux5EnV1MpI75T6A3tG7K", 1], [5, 10.13, 30], [0, 3.832, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "0", 18, 30, false, 1, 1, 198, [178]], [4, "lbUserXiu", 102, [-452], [0, "f2nlLCI5VNRJPSrDs+roGU", 1], [5, 10.13, 33], [0, 3.798, 0, 0, 0, 0, 1, 1, 1, 1]], [59, "0", 18, 33, false, 1, 1, 200, [181]], [81, "lbSessionID", 11, [-453], [0, "48ttkS3jJLJZwcwl7CO7E/", 1], [5, 150, 0], [0, 0, 0.5], [-23.687, 219.77, 0, 0, 0, 0, 1, 1, 1, 1]], [123, "#1234567", 22, 0, false, false, 1, 202, [184]], [16, "Label", 103, [[60, "<PERSON><PERSON><PERSON> ti<PERSON>n cân cửa", 20, false, 1, -454, [185], 186]], [0, "15ADNjiVdJjoYNeVImdLVv", 1], [5, 199.38, 40]], [16, "Label", 104, [[60, "<PERSON><PERSON><PERSON> đ<PERSON>u phiên mới", 20, false, 1, -455, [188], 189]], [0, "53lAFKhUdIrZlF1tceutla", 1], [5, 218.75, 25]], [4, "Dice1", 20, [-456], [0, "99RL4o1dFBs6oWJnpNYU1S", 1], [5, 322.37, 372.36], [-52.378, -45.566, 0, 0, 0, 0, 1, 0.31, 0.31, 1]], [54, "default", 1, 0, 1, 206, [192]], [4, "Dice2", 20, [-457], [0, "3fUqgMiQFJ/aLZLHa/McrJ", 1], [5, 322.37, 372.36], [49.879, -47.434, 0, 0, 0, 0, 1, 0.31, 0.31, 1]], [54, "default", 2, 0, 2, 208, [193]], [4, "Dice3", 20, [-458], [0, "adxqF4E0lLsb9UxqIP4i+r", 1], [5, 322.37, 372.36], [-1, -112.5, 0, 0, 0, 0, 1, 0.31, 0.31, 1]], [55, "default", 3, 0, false, 3, 210, [194]], [4, "effect", 20, [-459], [0, "2cl2CEdzZECrSQv0uK1AW7", 1], [5, 1347.04, 1545.94], [0, -91, 0, 0, 0, 0, 1, 0.25, 0.3, 1]], [55, "default", "effect", 0, false, "effect", 212, [195]], [16, "Go_Livestrem_Loading", 13, [[15, "default", "animation", 0, false, "animation", -460, [196], 197]], [0, "24W105pSBIea1hcfiRG+kV", 1], [5, 219, 118]], [24, "rollDice", 12, [-461], [0, "edFQYe3spBboDvXVA3lhWx", 1]], [28, 215], [24, "winSound", 12, [-462], [0, "77znS6BGBImKz+a5Ni5GJP", 1]], [28, 217], [67, "result-label", false, 3, [-463], [0, "d8ZAAuaadHK4V5IEuqFD9n", 1]], [14, "lbTotalDice", false, 219, [-464], [0, "741Pr92TBKt7OXr1HLV33b", 1], [5, 0, 50.4], [115.8, 99.4, 0, 0, 0, 0, 1, 1, 1, 1]], [124, false, 28, false, 1, 1, 220, [200]], [82, "lbTimer", false, 30, [-465], [0, "b0QOgdpgRCTYViVi21Yemz", 1], [4, 4294769916], [5, 41.44, 48], [-1, -3, 0, 0, 0, 0, 1, 1, 1, 1]], [125, 12.5, 48, false, 1, 1, 222, [201]], [7, "dangBaoTri", false, 3, [[3, -466, [204], 205]], [0, "359svAQjRKE5kmhWhKD6Wt", 1], [5, 302, 36], [-16.357, 136.305, 0, 0, 0, 0, 1, 1.662, 1.662, 1.662]], [1, "Popup", 21, [[17, 0, -467, [206], 207]], [0, "7f8dbEwepNhYylj6vJB2YH", 1], [5, 656, 50], [4, -110, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "+5,500,000,000", 21, 0, false, -1, 1, 1, 105, [208]], [31, "+5,500,000,000", 21, 0, false, -1, 1, 1, 106, [211]], [77, "Win", 21, [[42, -468, 214]], [0, "cfcXXFPLVOQJ2bQ+Sw/rEk", 1]], [4, "txtresult", 38, [-469], [0, "5cMB6OMhVBuJGYji1q/RjK", 1], [5, 164.38, 0], [0, 30, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "<PERSON>n mời nặn", 18, 0, false, -1, 1, 1, 229, [215]], [26, 38, [216, 217]]], 0, [0, 14, 1, 0, 0, 1, 0, 15, 3, 0, 10, 96, 0, 16, 201, 0, 17, 199, 0, 18, 197, 0, 19, 195, 0, 20, 223, 0, 21, 193, 0, 22, 203, 0, 11, 30, 0, 23, 9, 0, 0, 1, 0, 24, 95, 0, 25, 94, 0, 0, 1, 0, 26, 230, 0, 27, 231, 0, 28, 95, 0, 29, 94, 0, 30, 216, 0, 31, 100, 0, 32, 99, 0, 33, 218, 0, -1, 98, 0, -1, 97, 0, 34, 221, 0, 35, 37, 0, 36, 213, 0, 37, 12, 0, -1, 207, 0, -2, 209, 0, -3, 211, 0, 11, 30, 0, 38, 13, 0, 39, 37, 0, 40, 20, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -7, 67, 0, 0, 1, 0, -1, 22, 0, -2, 107, 0, -3, 3, 0, 0, 2, 0, 0, 2, 0, 41, 166, 0, 42, 173, 0, 43, 159, 0, 10, 96, 0, 44, 179, 0, 45, 177, 0, 46, 29, 0, 47, 26, 0, 48, 6, 0, 49, 7, 0, 50, 18, 0, 0, 2, 0, -1, 158, 0, -2, 18, 0, -1, 23, 0, -2, 111, 0, -3, 112, 0, -4, 39, 0, -5, 114, 0, -6, 17, 0, -7, 68, 0, -9, 9, 0, -10, 97, 0, -11, 98, 0, -12, 189, 0, -13, 99, 0, -14, 100, 0, -15, 190, 0, -16, 191, 0, -17, 4, 0, -18, 5, 0, -19, 11, 0, -20, 8, 0, -21, 12, 0, -22, 219, 0, -23, 30, 0, -24, 224, 0, -25, 21, 0, -26, 36, 0, -27, 38, 0, 0, 4, 0, -1, 64, 0, -2, 62, 0, -3, 61, 0, -4, 60, 0, -5, 59, 0, -6, 58, 0, -7, 57, 0, -8, 56, 0, -9, 55, 0, -10, 54, 0, -11, 53, 0, -12, 52, 0, -13, 51, 0, -14, 50, 0, -15, 49, 0, -16, 48, 0, 0, 4, 0, -1, 64, 0, -2, 62, 0, -3, 61, 0, -4, 60, 0, -5, 59, 0, -6, 58, 0, -7, 57, 0, -8, 56, 0, -9, 55, 0, -10, 54, 0, -11, 53, 0, -12, 52, 0, -13, 51, 0, -14, 50, 0, -15, 49, 0, -16, 48, 0, 0, 4, 0, -1, 120, 0, -2, 121, 0, -3, 122, 0, -4, 123, 0, -5, 124, 0, -6, 125, 0, -7, 126, 0, -8, 127, 0, -9, 128, 0, -10, 129, 0, -11, 130, 0, -12, 131, 0, -13, 132, 0, -14, 133, 0, -15, 134, 0, -16, 32, 0, 51, 119, 0, 52, 117, 0, 53, 10, 0, 54, 22, 0, 0, 5, 0, -1, 41, 0, -2, 31, 0, -3, 42, 0, -4, 43, 0, -5, 44, 0, -6, 45, 0, -7, 46, 0, -8, 47, 0, -9, 135, 0, 0, 6, 0, -1, 79, 0, -2, 80, 0, -3, 81, 0, -4, 82, 0, -5, 83, 0, -6, 84, 0, -7, 85, 0, -8, 86, 0, -9, 87, 0, -10, 88, 0, -11, 89, 0, -12, 90, 0, 0, 7, 0, -1, 69, 0, -2, 70, 0, -3, 71, 0, -4, 72, 0, -5, 73, 0, -6, 74, 0, -7, 75, 0, -8, 76, 0, -9, 77, 0, -10, 78, 0, -1, 27, 0, -2, 28, 0, -3, 174, 0, -4, 175, 0, -5, 176, 0, -6, 178, 0, -7, 180, 0, -8, 181, 0, -9, 26, 0, -10, 29, 0, 0, 9, 0, 0, 9, 0, -1, 183, 0, -2, 184, 0, -3, 185, 0, -4, 186, 0, -5, 187, 0, -6, 188, 0, 55, 137, 0, 56, 144, 0, 57, 155, 0, 0, 10, 0, -1, 24, 0, -2, 33, 0, -3, 25, 0, -4, 14, 0, -1, 192, 0, -2, 194, 0, -3, 196, 0, -4, 101, 0, -5, 102, 0, -6, 202, 0, -7, 103, 0, -8, 104, 0, -1, 37, 0, -2, 20, 0, -3, 13, 0, -4, 215, 0, -5, 217, 0, 0, 13, 0, 7, 13, 0, 0, 13, 0, 12, 67, 0, 0, 13, 0, -1, 214, 0, -1, 154, 0, 0, 14, 0, -3, 155, 0, -1, 145, 0, -2, 65, 0, 0, 15, 0, 58, 147, 0, 59, 153, 0, 60, 151, 0, 61, 149, 0, 62, 16, 0, 0, 15, 0, -1, 146, 0, -2, 16, 0, 0, 16, 0, -1, 148, 0, -2, 150, 0, -3, 152, 0, 0, 17, 0, 7, 17, 0, 0, 17, 0, 12, 67, 0, 0, 17, 0, -1, 156, 0, 0, 18, 0, -3, 19, 0, 0, 19, 0, -1, 91, 0, -2, 34, 0, -3, 92, 0, -4, 93, 0, -1, 206, 0, -2, 208, 0, -3, 210, 0, -4, 212, 0, 63, 227, 0, 64, 226, 0, 0, 21, 0, -1, 225, 0, -2, 105, 0, -3, 106, 0, -4, 228, 0, 0, 22, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, -1, 108, 0, -2, 109, 0, -3, 110, 0, 0, 24, 0, 0, 24, 0, -1, 115, 0, -2, 40, 0, -1, 144, 0, -1, 138, 0, -2, 140, 0, -3, 142, 0, 0, 26, 0, 7, 26, 0, 0, 26, 0, -1, 166, 0, -1, 160, 0, -2, 162, 0, -3, 164, 0, -1, 173, 0, -1, 167, 0, -2, 169, 0, -3, 171, 0, 0, 29, 0, 7, 29, 0, 0, 29, 0, 0, 30, 0, -1, 222, 0, 0, 31, 0, 0, 31, 0, -1, 116, 0, -1, 64, 0, 0, 32, 0, -1, 63, 0, -1, 137, 0, -1, 136, 0, 0, 34, 0, 7, 35, 0, 0, 34, 0, -1, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, -1, 182, 0, 0, 37, 0, -1, 231, 0, 0, 38, 0, -1, 229, 0, 0, 39, 0, -1, 113, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, -1, 117, 0, -2, 118, 0, 0, 44, 0, 0, 44, 0, -1, 119, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 63, 0, 0, 63, 0, 0, 65, 0, -1, 66, 0, 0, 66, 0, 0, 68, 0, -1, 157, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, 0, 88, 0, 0, 89, 0, 0, 89, 0, 0, 90, 0, 0, 90, 0, 0, 91, 0, 0, 91, 0, 0, 92, 0, 0, 92, 0, 0, 93, 0, 0, 93, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, -1, 198, 0, 0, 102, 0, -1, 200, 0, 0, 103, 0, -1, 204, 0, 0, 104, 0, -1, 205, 0, -1, 226, 0, 0, 105, 0, -1, 227, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, 0, 109, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, 0, 113, 0, 0, 115, 0, 0, 116, 0, 0, 135, 0, 0, 136, 0, -1, 139, 0, -1, 141, 0, -1, 143, 0, -1, 147, 0, -1, 149, 0, -1, 151, 0, -1, 153, 0, 0, 156, 0, 0, 157, 0, -1, 159, 0, -1, 161, 0, -1, 163, 0, -1, 165, 0, -1, 168, 0, -1, 170, 0, -1, 172, 0, 0, 174, 0, 0, 175, 0, -1, 177, 0, -1, 179, 0, 0, 183, 0, 0, 184, 0, 0, 185, 0, 0, 186, 0, 0, 187, 0, 0, 188, 0, 0, 189, 0, 0, 190, 0, 0, 191, 0, -1, 193, 0, -1, 195, 0, -1, 197, 0, -1, 199, 0, -1, 201, 0, -1, 203, 0, 0, 204, 0, 0, 205, 0, -1, 207, 0, -1, 209, 0, -1, 211, 0, -1, 213, 0, 0, 214, 0, -1, 216, 0, -1, 218, 0, -1, 220, 0, -1, 221, 0, -1, 223, 0, 0, 224, 0, 0, 225, 0, 0, 228, 0, -1, 230, 0, 65, 1, 2, 8, 3, 6, 8, 18, 7, 8, 18, 10, 8, 114, 15, 8, 145, 48, 0, 120, 49, 0, 121, 50, 0, 122, 51, 0, 123, 52, 0, 124, 53, 0, 125, 54, 0, 126, 55, 0, 127, 56, 0, 128, 57, 0, 129, 58, 0, 130, 59, 0, 131, 60, 0, 132, 61, 0, 133, 62, 0, 134, 67, 66, 118, 94, 0, 180, 95, 0, 181, 96, 0, 182, 469], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 117, 119, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 139, 147, 149, 151, 153, 159, 177, 179, 94, 95, 96, 193, 195, 197, 199, 201, 203, 207, 209, 211, 213, 216, 218, 221, 223, 226, 227, 230, 231], [-1, 1, -1, -2, -1, 1, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 1, 67, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 5, -1, -1, -1, -2, -1, -2, -1, -2, -1, -2, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, 4, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, 68, 69, 70, -1, 1, -1, 1, -1, -2, 9, -1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, 5, -1, -2, -1, 6, 6, 6, 6, 6, 6, -1, 5, -1, -2, -3, -1, 4, -1, 4, -1, 4, -1, 1, -1, 1, -1, 4, -1, 1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 3, -1, -1, 3, -1, -1, -1, -1, -1, -1, -1, 4, -1, 1, -1, -1, -1, 1, -1, 1, -1, 1, -1, 5, -1, -1, 5, -1, 9, -1, -1, -2, -1, 1, 5, -1, -2, 71, 72, 73, 74, 75, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 76, 3, 3, 1, 9, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 4, 4, 4, 4, 9, 9, 3, 3, 3, 3, 3, 5], [0, 13, 50, 51, 0, 13, 1, 52, 0, 14, 0, 14, 1, 53, 0, 54, 0, 55, 0, 56, 0, 57, 0, 58, 0, 59, 0, 0, 60, 0, 61, 0, 62, 0, 63, 0, 0, 64, 0, 0, 65, 0, 66, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 15, 0, 5, 16, 5, 16, 67, 17, 10, 10, 0, 68, 0, 69, 0, 0, 0, 0, 0, 1, 18, 0, 19, 0, 70, 0, 71, 0, 0, 72, 0, 73, 0, 74, 0, 0, 75, 0, 76, 0, 77, 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 79, 0, 80, 81, 82, 83, 0, 84, 0, 85, 86, 87, 88, 0, 0, 0, 20, 0, 20, 0, 0, 0, 0, 0, 21, 0, 21, 0, 11, 6, 6, 22, 0, 3, 3, 3, 3, 3, 3, 0, 23, 23, 89, 90, 1, 7, 1, 7, 1, 7, 0, 91, 0, 92, 1, 7, 0, 93, 0, 0, 0, 0, 0, 94, 0, 0, 95, 0, 0, 24, 0, 0, 24, 0, 0, 1, 1, 1, 1, 1, 18, 0, 19, 0, 0, 0, 96, 0, 97, 0, 11, 0, 8, 8, 0, 8, 8, 98, 0, 6, 22, 0, 11, 25, 25, 99, 100, 101, 102, 103, 104, 17, 10, 5, 5, 5, 2, 2, 2, 4, 4, 4, 4, 4, 4, 2, 2, 2, 2, 105, 106, 107, 108, 109, 110, 9, 9, 26, 26, 27, 28, 29, 29, 9, 9, 111, 12, 12, 12, 112, 113, 114, 115, 28, 30, 30, 27, 6]], [[{"name": "100KOff", "rect": [0, 0, 99, 57], "offset": [0, 0], "originalSize": [99, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [116]], [[{"name": "btnSoiOff", "rect": [0, 0, 53, 54], "offset": [0, 0], "originalSize": [53, 54], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [117]], [[{"name": "fontTotalMoney-export", "rect": [0, 0, 250, 30], "offset": [-3, 1], "originalSize": [256, 32], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [118]], [[{"name": "ic_huo<PERSON><PERSON>", "rect": [0, 0, 89, 89], "offset": [0, 0], "originalSize": [89, 89], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [119]], [[{"name": "cuoc", "rect": [0, 0, 184, 57], "offset": [0, 0], "originalSize": [184, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [120]], [[{"name": "10MOff", "rect": [0, 0, 99, 56], "offset": [0, 0.5], "originalSize": [99, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [121]], [[{"name": "chen", "rect": [0, 0, 279, 274], "offset": [0, 0.5], "originalSize": [279, 275], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [122]], [[{"name": "tai", "rect": [0, 0, 146, 101], "offset": [0, 0], "originalSize": [146, 101], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [123]], [[{"name": "10KOff", "rect": [0, 0, 99, 56], "offset": [0, 0.5], "originalSize": [99, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [124]], [[{"name": "1MOff", "rect": [0, 0, 99, 57], "offset": [0, 0], "originalSize": [99, 57], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [125]], [[{"name": "btnThongTinOff", "rect": [0, 0, 53, 54], "offset": [0, 0], "originalSize": [53, 54], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [126]], [[{"name": "logo", "rect": [0, 0, 363, 108], "offset": [0, 0], "originalSize": [363, 108], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [127]], [[{"name": "huy", "rect": [0, 0, 210, 90], "offset": [0, 0], "originalSize": [210, 90], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [128]], [[{"name": "bpderSoiCau", "rect": [0, 0, 552, 41], "offset": [0, 0], "originalSize": [552, 41], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [129]]]]